#!/usr/bin/env python3
"""
测试新的任务ID格式的脚本
"""

import uuid
import re

def test_task_id_generation():
    """测试任务ID生成逻辑"""
    print("🔍 测试任务ID生成逻辑...")
    print("=" * 50)
    
    def create_task_id(prefix=None):
        """模拟任务ID生成逻辑"""
        base_id = str(uuid.uuid4())
        if prefix:
            # 移除文件扩展名
            filename_without_ext = prefix.replace('.csv', '').replace('.CSV', '')

            # 提取第一个下划线前的内容
            if '_' in filename_without_ext:
                clean_prefix = filename_without_ext.split('_')[0]
            else:
                clean_prefix = filename_without_ext

            # 只保留字母、数字、下划线和连字符
            clean_prefix = ''.join(c for c in clean_prefix if c.isalnum() or c in '_-')

            # 限制长度并确保不为空
            clean_prefix = clean_prefix[:15]
            if clean_prefix:
                task_id = f"{clean_prefix}_{base_id}"
            else:
                task_id = base_id
        else:
            task_id = base_id
        return task_id
    
    # 测试不同的CSV文件名
    test_cases = [
        "traffic_data_2024.csv",           # 提取: traffic
        "network_analysis_tcp.csv",        # 提取: network
        "test_file_123.csv",               # 提取: test
        "singlename.csv",                  # 提取: singlename
        "data_processing_final.csv",       # 提取: data
        "log_2024_01_15.csv",             # 提取: log
        "数据文件_测试.csv",                # 中文文件名，提取: 数据文件
        "file with spaces_data.csv",       # 包含空格，提取: file with spaces
        "special@#$%_chars.csv",           # 特殊字符，提取: special@#$%
        "very_long_filename_that_exceeds_limit.csv",  # 超长文件名，提取: very
        "no_underscore.csv",               # 无下划线，提取: no
        None  # 无前缀
    ]
    
    print("📋 测试用例:")
    print("-" * 50)
    
    for i, csv_filename in enumerate(test_cases, 1):
        task_id = create_task_id(csv_filename)
        print(f"\n🔸 测试 {i}:")
        print(f"   输入文件名: {csv_filename}")
        print(f"   生成任务ID: {task_id}")
        
        if csv_filename:
            # 分析任务ID结构
            if '_' in task_id:
                prefix_part = task_id.split('_')[0]
                uuid_part = '_'.join(task_id.split('_')[1:])
                print(f"   前缀部分: {prefix_part}")
                print(f"   UUID部分: {uuid_part}")
                print(f"   前缀长度: {len(prefix_part)}")
                
                # 验证UUID格式
                try:
                    uuid.UUID(uuid_part)
                    print(f"   ✅ UUID格式有效")
                except ValueError:
                    print(f"   ❌ UUID格式无效")
            else:
                print(f"   📝 无前缀格式")

def test_frontend_display():
    """测试前端显示逻辑"""
    print("\n🔍 测试前端显示逻辑...")
    print("=" * 50)
    
    def format_task_id_display(task_id):
        """模拟前端任务ID显示逻辑"""
        if '_' in task_id:
            parts = task_id.split('_')
            prefix = parts[0]
            uuid_part = parts[-1]
            return f"{prefix}...{uuid_part[:8]}"
        else:
            return f"{task_id[:8]}..."
    
    def format_task_selector_display(task_id, timestamp):
        """模拟任务选择器显示逻辑"""
        if '_' in task_id:
            prefix = task_id.split('_')[0]
            return f"{prefix} ({timestamp})"
        else:
            return f"任务 {task_id[:8]}... ({timestamp})"
    
    # 测试任务ID
    test_task_ids = [
        "traffic_data_12345678-1234-5678-9abc-123456789abc",
        "network-analysis_87654321-4321-8765-cba9-987654321cba",
        "test123_11111111-**************-************",
        "12345678-1234-5678-9abc-123456789abc"  # 无前缀
    ]
    
    timestamp = "2024-01-15 14:30:25"
    
    print("📋 显示效果测试:")
    print("-" * 50)
    
    for i, task_id in enumerate(test_task_ids, 1):
        display_id = format_task_id_display(task_id)
        selector_display = format_task_selector_display(task_id, timestamp)
        
        print(f"\n🔸 任务 {i}:")
        print(f"   完整任务ID: {task_id}")
        print(f"   任务管理页显示: {display_id}")
        print(f"   选择器显示: {selector_display}")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    print("=" * 50)
    
    def create_task_id(prefix=None):
        """模拟任务ID生成逻辑"""
        base_id = str(uuid.uuid4())
        if prefix:
            filename_without_ext = prefix.replace('.csv', '').replace('.CSV', '')
            if '_' in filename_without_ext:
                clean_prefix = filename_without_ext.split('_')[0]
            else:
                clean_prefix = filename_without_ext
            clean_prefix = ''.join(c for c in clean_prefix if c.isalnum() or c in '_-')
            clean_prefix = clean_prefix[:15]
            if clean_prefix:
                task_id = f"{clean_prefix}_{base_id}"
            else:
                task_id = base_id
        else:
            task_id = base_id
        return task_id
    
    edge_cases = [
        "",  # 空字符串
        ".csv",  # 只有扩展名
        "a_test.csv",  # 单字符前缀
        "123_data.csv",  # 纯数字前缀
        "___---_test.csv",  # 只有特殊字符前缀
        "中文_测试.csv",  # 中文字符前缀
        "file@#$%_data.csv",  # 特殊字符前缀
        "no_underscore.csv",  # 无下划线
        "_starts_with_underscore.csv",  # 以下划线开头
    ]
    
    print("📋 边界情况测试:")
    print("-" * 50)
    
    for i, filename in enumerate(edge_cases, 1):
        task_id = create_task_id(filename)
        print(f"\n🔸 边界测试 {i}:")
        print(f"   输入: '{filename}'")
        print(f"   输出: {task_id}")
        
        # 检查是否有有效前缀
        if '_' in task_id:
            prefix = task_id.split('_')[0]
            if prefix:
                print(f"   ✅ 有效前缀: '{prefix}'")
            else:
                print(f"   ⚠️  空前缀")
        else:
            print(f"   📝 回退到纯UUID格式")

def test_uniqueness():
    """测试唯一性"""
    print("\n🔍 测试任务ID唯一性...")
    print("=" * 50)
    
    def create_task_id(prefix=None):
        """模拟任务ID生成逻辑"""
        base_id = str(uuid.uuid4())
        if prefix:
            filename_without_ext = prefix.replace('.csv', '').replace('.CSV', '')
            if '_' in filename_without_ext:
                clean_prefix = filename_without_ext.split('_')[0]
            else:
                clean_prefix = filename_without_ext
            clean_prefix = ''.join(c for c in clean_prefix if c.isalnum() or c in '_-')
            clean_prefix = clean_prefix[:15]
            if clean_prefix:
                task_id = f"{clean_prefix}_{base_id}"
            else:
                task_id = base_id
        else:
            task_id = base_id
        return task_id

    # 生成多个相同前缀的任务ID
    filename = "traffic_data_2024.csv"
    task_ids = [create_task_id(filename) for _ in range(5)]
    
    print(f"📋 相同文件名 '{filename}' 的多个任务ID:")
    print("-" * 50)
    
    for i, task_id in enumerate(task_ids, 1):
        print(f"   任务 {i}: {task_id}")
    
    # 检查唯一性
    unique_ids = set(task_ids)
    print(f"\n📊 唯一性检查:")
    print(f"   生成数量: {len(task_ids)}")
    print(f"   唯一数量: {len(unique_ids)}")
    print(f"   ✅ 唯一性: {'通过' if len(task_ids) == len(unique_ids) else '失败'}")

if __name__ == "__main__":
    print("🔍 任务ID格式测试")
    print("=" * 60)
    
    test_task_id_generation()
    test_frontend_display()
    test_edge_cases()
    test_uniqueness()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("\n💡 新任务ID格式特点:")
    print("1. ✅ 提取CSV文件名第一个下划线前的内容作为前缀")
    print("2. ✅ 自动清理特殊字符和扩展名")
    print("3. ✅ 限制前缀长度为15字符")
    print("4. ✅ 保持UUID唯一性")
    print("5. ✅ 向后兼容无前缀格式")
    print("\n🎯 提取规则:")
    print("• 'traffic_data_2024.csv' → 前缀: 'traffic'")
    print("• 'network_analysis.csv' → 前缀: 'network'")
    print("• 'singlename.csv' → 前缀: 'singlename'")
    print("\n🎯 显示效果:")
    print("• 任务管理页: 'traffic...12345678'")
    print("• 任务选择器: 'traffic (2024-01-15 14:30:25)'")
    print("• 完整ID可复制: 'traffic_12345678-1234-5678-9abc-123456789abc'")
