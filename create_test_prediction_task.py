#!/usr/bin/env python3
"""
创建测试预测任务的脚本
"""

import json
import os
from datetime import datetime, timedelta

def create_test_prediction_task():
    """创建测试预测任务"""
    print("🔍 创建测试预测任务")
    print("=" * 60)
    
    base_time = datetime.now()
    task_id = "test_prediction_12345678-1234-5678-9abc-123456789abc"
    
    # 创建测试任务
    test_task = {
        task_id: {
            "task_id": task_id,
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "test_traffic_data.csv",
                "model_names": ["TCP_spt_sip_dip"]
            },
            "created_at": (base_time - timedelta(minutes=5)).isoformat(),
            "started_at": (base_time - timedelta(minutes=4)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=1)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=1)).isoformat(),
            "current_step": "预测完成",
            "error": None,
            "total_steps": None
        }
    }
    
    # 创建测试结果 - 格式与同步预测完全一致
    test_result = {
        task_id: {
            "predictions": [
                {
                    "timestamp": "2025-07-24 10:00:00",
                    "packets_per_sec": 1500,
                    "packets_per_sec_smooth": 1480,
                    "pred_smooth": 1520,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:01:00",
                    "packets_per_sec": 2800,
                    "packets_per_sec_smooth": 2750,
                    "pred_smooth": 1530,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:02:00",
                    "packets_per_sec": 1200,
                    "packets_per_sec_smooth": 1250,
                    "pred_smooth": 1540,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:03:00",
                    "packets_per_sec": 3200,
                    "packets_per_sec_smooth": 3150,
                    "pred_smooth": 1550,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:04:00",
                    "packets_per_sec": 1800,
                    "packets_per_sec_smooth": 1820,
                    "pred_smooth": 1560,
                    "threshold": 2000,
                    "is_anomaly": False
                }
            ],
            "anomaly_count": 2,
            "suggested_threshold": 2000,
            "model_name": "TCP_spt_sip_dip",
            "message": "预测成功",
            "duration_seconds": 180.5,
            "cpu_percent": 15.6,
            "memory_mb": 256.4,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        }
    }
    
    # 备份现有文件
    tasks_file = "task_storage.json"
    results_file = "task_results.json"
    
    backup_tasks = {}
    backup_results = {}
    
    try:
        if os.path.exists(tasks_file):
            with open(tasks_file, 'r', encoding='utf-8') as f:
                backup_tasks = json.load(f)
        
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                backup_results = json.load(f)
    except json.JSONDecodeError as e:
        print(f"⚠️  JSON文件格式错误: {e}")
        print("将创建新的文件...")
        backup_tasks = {}
        backup_results = {}
    
    # 合并测试数据
    backup_tasks.update(test_task)
    backup_results.update(test_result)
    
    # 保存更新后的文件
    try:
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump(backup_tasks, f, ensure_ascii=False, indent=2)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(backup_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功创建测试预测任务:")
        print(f"   任务ID: {task_id}")
        print(f"   任务类型: prediction")
        print(f"   状态: completed")
        print(f"   预测数据点: {len(test_result[task_id]['predictions'])}")
        print(f"   异常数量: {test_result[task_id]['anomaly_count']}")
        print(f"   模型名称: {test_result[task_id]['model_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

def verify_task_creation():
    """验证任务创建"""
    print(f"\n🔍 验证任务创建...")
    print("=" * 60)
    
    try:
        # 检查任务文件
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        # 检查结果文件
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 查找预测任务
        prediction_tasks = [task for task in tasks.values() if task.get('task_type') == 'prediction' and task.get('status') == 'completed']
        
        print(f"📊 验证结果:")
        print(f"   总任务数: {len(tasks)}")
        print(f"   总结果数: {len(results)}")
        print(f"   已完成预测任务数: {len(prediction_tasks)}")
        
        if prediction_tasks:
            latest_task = prediction_tasks[-1]
            task_id = latest_task['task_id']
            print(f"\n📋 最新预测任务:")
            print(f"   ID: {task_id}")
            print(f"   状态: {latest_task['status']}")
            print(f"   创建时间: {latest_task['created_at']}")
            print(f"   完成时间: {latest_task['completed_at']}")
            
            if task_id in results:
                result = results[task_id]
                print(f"\n📊 任务结果:")
                print(f"   预测数据点: {len(result.get('predictions', []))}")
                print(f"   异常数量: {result.get('anomaly_count', 0)}")
                print(f"   模型名称: {result.get('model_name', 'N/A')}")
                print(f"   建议阈值: {result.get('suggested_threshold', 0)}")
                print(f"   执行时长: {result.get('duration_seconds', 0)}秒")
                
                # 检查数据格式
                if result.get('predictions'):
                    first_prediction = result['predictions'][0]
                    print(f"\n📋 预测数据格式:")
                    print(f"   字段: {list(first_prediction.keys())}")
                    print(f"   示例: {first_prediction}")
                
                return True
            else:
                print(f"❌ 找不到任务结果")
                return False
        else:
            print(f"❌ 没有找到已完成的预测任务")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_api_response():
    """测试API响应"""
    print(f"\n🌐 测试API响应...")
    print("=" * 60)
    
    import requests
    
    try:
        # 测试获取已完成任务
        response = requests.get("http://localhost:8000/tasks/completed")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tasks = data.get('tasks', [])
                prediction_tasks = [task for task in tasks if task.get('task_type') == 'prediction']
                
                print(f"✅ API响应成功:")
                print(f"   总任务数: {len(tasks)}")
                print(f"   预测任务数: {len(prediction_tasks)}")
                
                if prediction_tasks:
                    latest_task = prediction_tasks[-1]
                    print(f"\n📋 最新预测任务:")
                    print(f"   ID: {latest_task.get('task_id', 'N/A')}")
                    print(f"   状态: {latest_task.get('status', 'N/A')}")
                    print(f"   有结果: {'是' if latest_task.get('result') else '否'}")
                    
                    if latest_task.get('result'):
                        result = latest_task['result']
                        print(f"   预测数据点: {len(result.get('predictions', []))}")
                        print(f"   异常数量: {result.get('anomaly_count', 0)}")
                        print(f"   模型名称: {result.get('model_name', 'N/A')}")
                
                return True
            else:
                print(f"❌ API返回失败: {data}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def provide_next_steps():
    """提供下一步操作"""
    print(f"\n🎯 下一步操作:")
    print("=" * 60)
    
    print(f"1. 打开浏览器，访问模型预测页面")
    print(f"2. 切换到'异步预测结果'标签")
    print(f"3. 查看是否显示了测试预测任务")
    print(f"4. 选择任务查看预测结果")
    print(f"5. 检查浏览器控制台的调试信息")
    
    print(f"\n🔍 调试信息:")
    print(f"   打开浏览器开发者工具 (F12)")
    print(f"   查看Console标签页")
    print(f"   寻找以下调试信息:")
    print(f"     - 🔍 ModelPredictionPage: 获取已完成任务...")
    print(f"     - 📋 已完成预测任务: [...]")
    print(f"     - 🎯 选择异步预测任务: ...")
    print(f"     - 📊 任务结果: {{...}}")
    print(f"     - ✅ 转换后的异步预测结果: {{...}}")
    print(f"     - 🎨 异步预测结果状态更新: [...]")

if __name__ == "__main__":
    print("🔍 创建测试预测任务")
    print("=" * 60)
    
    # 创建测试任务
    if create_test_prediction_task():
        # 验证创建结果
        if verify_task_creation():
            # 测试API响应
            test_api_response()
        
        # 提供下一步操作
        provide_next_steps()
    
    print(f"\n" + "=" * 60)
    print("✅ 测试任务创建完成")
    print(f"\n💡 如果异步预测结果仍然不显示，请:")
    print("1. 检查浏览器控制台的调试信息")
    print("2. 确认前端正确获取了预测任务")
    print("3. 验证数据转换逻辑是否正确")
    print("4. 检查PredictionResultDisplay组件是否正常渲染")
