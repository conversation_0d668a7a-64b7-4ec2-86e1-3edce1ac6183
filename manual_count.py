#!/usr/bin/env python3
import os

def count_lines_manual():
    """手动统计项目代码行数"""
    
    # 后端Python文件
    backend_files = [
        'backend/main.py',
        'backend/auth.py', 
        'backend/clean_template.py',
        'backend/data_cleaning.py',
        'backend/data_query.py',
        'backend/model_prediction.py',
        'backend/model_registry.py',
        'backend/model_training.py',
        'backend/task_manager.py'
    ]
    
    # 前端TypeScript/React文件
    frontend_files = [
        'frontend-react-stable/src/App.tsx',
        'frontend-react-stable/src/index.tsx',
        'frontend-react-stable/src/components/TaskStatusIndicator.tsx',
        'frontend-react-stable/src/hooks/useTaskManager.ts',
        'frontend-react-stable/src/pages/CleanTemplatePage.tsx',
        'frontend-react-stable/src/pages/DataCleaningPage.tsx',
        'frontend-react-stable/src/pages/DataQueryPage.tsx',
        'frontend-react-stable/src/pages/LoginPage.tsx',
        'frontend-react-stable/src/pages/ModelPredictionPage.tsx',
        'frontend-react-stable/src/pages/ModelRegistryPage.tsx',
        'frontend-react-stable/src/pages/ModelTrainingPage.tsx',
        'frontend-react-stable/src/pages/TaskManagerPage.tsx',
        'frontend-react-stable/src/pages/UserManagementPage.tsx',
        'frontend-react-stable/src/services/api.ts',
        'frontend-react-stable/src/services/taskApi.ts',
        'frontend-react-stable/src/store/store.ts'
    ]
    
    # CSS文件
    css_files = [
        'frontend-react-stable/src/App.css',
        'frontend-react-stable/src/index.css'
    ]
    
    # 配置文件
    config_files = [
        'frontend-react-stable/package.json',
        'frontend-react-stable/tsconfig.json'
    ]
    
    # 文档文件
    doc_files = [
        'API文档.md',
        'ASYNC_FEATURES_README.md',
        'ASYNC_TASK_GUIDE.md',
        'HOW_TO_VIEW_ASYNC_RESULTS.md',
        '前端监控问题修复总结.md',
        '批量分析UI优化总结.md',
        '批量分析功能实现总结.md',
        '批量分析错误修复总结.md',
        '批量分析问题解决总结.md',
        '批量分析问题解决方案.md',
        '数据源选择修改总结.md'
    ]
    
    def count_file_lines(file_path):
        """统计单个文件行数"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return len(f.readlines())
            else:
                return 0
        except:
            return 0
    
    def count_category(files, category_name):
        """统计某个类别的文件行数"""
        total = 0
        existing_files = []
        
        for file_path in files:
            lines = count_file_lines(file_path)
            if lines > 0:
                total += lines
                existing_files.append((file_path, lines))
        
        print(f"\n📁 {category_name}:")
        print(f"   总行数: {total:,} 行")
        print(f"   文件数: {len(existing_files)} 个")
        
        for file_path, lines in sorted(existing_files, key=lambda x: x[1], reverse=True):
            print(f"   - {file_path}: {lines:,} 行")
        
        return total, existing_files
    
    print("=" * 60)
    print("AI智能清洗策略系统 - 代码行数统计")
    print("=" * 60)
    
    # 统计各个类别
    backend_total, backend_list = count_category(backend_files, "Python后端")
    frontend_total, frontend_list = count_category(frontend_files, "TypeScript/React前端")
    css_total, css_list = count_category(css_files, "CSS样式")
    config_total, config_list = count_category(config_files, "配置文件")
    doc_total, doc_list = count_category(doc_files, "文档")
    
    # 总计
    total_lines = backend_total + frontend_total + css_total + config_total + doc_total
    
    print("\n" + "=" * 60)
    print(f"🎯 项目总计: {total_lines:,} 行")
    print("=" * 60)
    
    # 详细统计
    print(f"\n📊 详细统计:")
    categories = [
        ("Python后端", backend_total),
        ("TypeScript/React前端", frontend_total),
        ("CSS样式", css_total),
        ("配置文件", config_total),
        ("文档", doc_total)
    ]
    
    for name, lines in sorted(categories, key=lambda x: x[1], reverse=True):
        if lines > 0:
            percentage = (lines / total_lines) * 100
            print(f"   {name}: {lines:,} 行 ({percentage:.1f}%)")
    
    return total_lines

if __name__ == "__main__":
    count_lines_manual()
