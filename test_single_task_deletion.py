#!/usr/bin/env python3
"""
测试单个任务删除功能的脚本
"""

import json
import os
import requests
import time
from datetime import datetime

def test_single_task_deletion():
    """测试单个任务删除功能"""
    print("🔍 测试单个任务删除功能")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 创建测试数据
    test_tasks = {
        "test_task_1": {
            "task_id": "test_task_1",
            "task_type": "training",
            "status": "completed",
            "params": {"csv_filename": "test1.csv"},
            "created_at": "2025-07-24T10:00:00.000Z",
            "completed_at": "2025-07-24T10:02:00.000Z",
            "progress": 100
        },
        "test_task_2": {
            "task_id": "test_task_2",
            "task_type": "prediction",
            "status": "completed",
            "params": {"csv_filename": "test2.csv"},
            "created_at": "2025-07-24T11:00:00.000Z",
            "completed_at": "2025-07-24T11:01:00.000Z",
            "progress": 100
        },
        "test_task_3": {
            "task_id": "test_task_3",
            "task_type": "training",
            "status": "running",
            "params": {"csv_filename": "test3.csv"},
            "created_at": "2025-07-24T12:00:00.000Z",
            "completed_at": None,
            "progress": 50
        }
    }
    
    test_results = {
        "test_task_1": {
            "results": {
                "TCP_spt_sip_dip": {
                    "r2_score": 0.85,
                    "model_path": "/test/model1.pth"
                }
            },
            "duration_seconds": 120
        },
        "test_task_2": {
            "predictions": [1.0, 2.0, 3.0],
            "anomaly_count": 1,
            "duration_seconds": 60
        }
    }
    
    # 保存测试数据到JSON文件
    tasks_file = "task_storage.json"
    results_file = "task_results.json"
    
    # 备份现有文件
    backup_tasks = None
    backup_results = None
    
    if os.path.exists(tasks_file):
        with open(tasks_file, 'r', encoding='utf-8') as f:
            backup_tasks = json.load(f)
    
    if os.path.exists(results_file):
        with open(results_file, 'r', encoding='utf-8') as f:
            backup_results = json.load(f)
    
    try:
        # 写入测试数据
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump(test_tasks, f, ensure_ascii=False, indent=2)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        print("✅ 测试数据已创建")
        print(f"   任务数: {len(test_tasks)}")
        print(f"   结果数: {len(test_results)}")
        
        # 测试API端点
        print(f"\n🧪 测试API端点...")
        
        # 1. 获取所有任务
        print(f"\n1. 获取所有任务:")
        try:
            response = requests.get(f"{base_url}/tasks/all")
            if response.status_code == 200:
                tasks = response.json()
                print(f"   ✅ 成功获取 {len(tasks)} 个任务")
                for task in tasks:
                    print(f"     - {task['task_id']}: {task['status']}")
            else:
                print(f"   ❌ 获取任务失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 2. 尝试删除运行中的任务（应该失败）
        print(f"\n2. 尝试删除运行中的任务:")
        try:
            response = requests.delete(f"{base_url}/tasks/task/test_task_3")
            if response.status_code == 400:
                print(f"   ✅ 正确拒绝删除运行中的任务")
                print(f"   错误信息: {response.json().get('detail', '')}")
            else:
                print(f"   ❌ 意外的响应: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 3. 删除已完成的任务
        print(f"\n3. 删除已完成的任务:")
        try:
            response = requests.delete(f"{base_url}/tasks/task/test_task_1")
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 成功删除任务: {result.get('message', '')}")
                print(f"   删除的任务ID: {result.get('deleted_task_id', '')}")
            else:
                print(f"   ❌ 删除失败: {response.status_code}")
                print(f"   错误信息: {response.json().get('detail', '')}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 4. 验证任务已被删除
        print(f"\n4. 验证任务已被删除:")
        try:
            # 检查JSON文件
            with open(tasks_file, 'r', encoding='utf-8') as f:
                remaining_tasks = json.load(f)
            
            with open(results_file, 'r', encoding='utf-8') as f:
                remaining_results = json.load(f)
            
            print(f"   剩余任务数: {len(remaining_tasks)}")
            print(f"   剩余结果数: {len(remaining_results)}")
            
            if "test_task_1" not in remaining_tasks:
                print(f"   ✅ 任务已从task_storage.json中删除")
            else:
                print(f"   ❌ 任务仍在task_storage.json中")
            
            if "test_task_1" not in remaining_results:
                print(f"   ✅ 结果已从task_results.json中删除")
            else:
                print(f"   ❌ 结果仍在task_results.json中")
            
            # 通过API验证
            response = requests.get(f"{base_url}/tasks/all")
            if response.status_code == 200:
                api_tasks = response.json()
                api_task_ids = [task['task_id'] for task in api_tasks]
                if "test_task_1" not in api_task_ids:
                    print(f"   ✅ 任务已从API响应中删除")
                else:
                    print(f"   ❌ 任务仍在API响应中")
            
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
        
        # 5. 测试删除不存在的任务
        print(f"\n5. 测试删除不存在的任务:")
        try:
            response = requests.delete(f"{base_url}/tasks/task/nonexistent_task")
            if response.status_code == 404:
                print(f"   ✅ 正确返回404错误")
                print(f"   错误信息: {response.json().get('detail', '')}")
            else:
                print(f"   ❌ 意外的响应: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 6. 测试清空所有已完成任务
        print(f"\n6. 测试清空所有已完成任务:")
        try:
            response = requests.delete(f"{base_url}/tasks/completed")
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 成功清空任务: {result.get('message', '')}")
                print(f"   清空数量: {result.get('cleared_count', 0)}")
                print(f"   清空的任务ID: {result.get('cleared_task_ids', [])}")
            else:
                print(f"   ❌ 清空失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
        
        # 7. 最终验证
        print(f"\n7. 最终验证:")
        try:
            with open(tasks_file, 'r', encoding='utf-8') as f:
                final_tasks = json.load(f)
            
            with open(results_file, 'r', encoding='utf-8') as f:
                final_results = json.load(f)
            
            completed_tasks = [t for t in final_tasks.values() if t['status'] == 'completed']
            
            print(f"   最终任务数: {len(final_tasks)}")
            print(f"   最终结果数: {len(final_results)}")
            print(f"   剩余已完成任务数: {len(completed_tasks)}")
            
            if len(completed_tasks) == 0:
                print(f"   ✅ 所有已完成任务已清空")
            else:
                print(f"   ⚠️  仍有已完成任务未清空")
            
        except Exception as e:
            print(f"   ❌ 最终验证失败: {e}")
        
    finally:
        # 恢复原始数据
        print(f"\n🔄 恢复原始数据...")
        try:
            if backup_tasks is not None:
                with open(tasks_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_tasks, f, ensure_ascii=False, indent=2)
                print(f"   ✅ 已恢复 {tasks_file}")
            elif os.path.exists(tasks_file):
                os.remove(tasks_file)
                print(f"   ✅ 已删除测试文件 {tasks_file}")
            
            if backup_results is not None:
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_results, f, ensure_ascii=False, indent=2)
                print(f"   ✅ 已恢复 {results_file}")
            elif os.path.exists(results_file):
                os.remove(results_file)
                print(f"   ✅ 已删除测试文件 {results_file}")
                
        except Exception as e:
            print(f"   ❌ 恢复数据失败: {e}")

def test_frontend_integration():
    """测试前端集成"""
    print(f"\n🖥️  前端集成测试指南:")
    print("=" * 60)
    
    print(f"📋 测试步骤:")
    print(f"1. 打开任务管理页面")
    print(f"2. 查看已完成任务列表")
    print(f"3. 点击单个任务的'删除'按钮")
    print(f"4. 确认删除对话框")
    print(f"5. 验证任务从列表中消失")
    print(f"6. 点击'清空全部'按钮")
    print(f"7. 确认清空对话框")
    print(f"8. 验证所有已完成任务被清空")
    
    print(f"\n🎯 预期行为:")
    print(f"✅ 单个删除: 只删除选中的任务")
    print(f"✅ 批量清空: 删除所有已完成任务")
    print(f"✅ 确认对话框: 防止误操作")
    print(f"✅ 实时更新: 删除后立即刷新列表")
    print(f"✅ 错误处理: 显示适当的错误信息")
    print(f"✅ 状态检查: 只能删除已完成的任务")

if __name__ == "__main__":
    print("🔍 单个任务删除功能测试")
    print("=" * 60)
    
    test_single_task_deletion()
    test_frontend_integration()
    
    print(f"\n" + "=" * 60)
    print("✅ 测试完成")
    print(f"\n🎯 功能总结:")
    print("1. ✅ 添加了单个任务删除API端点")
    print("2. ✅ 添加了前端删除按钮和确认对话框")
    print("3. ✅ 保留了批量清空功能")
    print("4. ✅ 增强了用户体验和安全性")
    print("5. ✅ 支持JSON文件存储的数据同步")
    print(f"\n💡 使用建议:")
    print("• 单个删除: 用于清理特定的失败或不需要的任务")
    print("• 批量清空: 用于定期清理所有已完成任务")
    print("• 确认对话框: 防止误删重要任务数据")
