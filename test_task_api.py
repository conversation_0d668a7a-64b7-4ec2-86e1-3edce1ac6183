#!/usr/bin/env python3
"""
测试任务管理API的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_task_apis():
    """测试任务管理相关的API"""
    
    print("🔍 测试任务管理API...")
    
    # 测试获取所有任务
    print("\n1. 测试获取所有任务:")
    try:
        response = requests.get(f"{BASE_URL}/tasks/tasks")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success', False)}")
            print(f"任务总数: {len(data.get('tasks', []))}")
            for task in data.get('tasks', []):
                print(f"  - 任务 {task['task_id'][:8]}: {task['status']} ({task.get('task_type', 'unknown')})")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试获取运行中任务
    print("\n2. 测试获取运行中任务:")
    try:
        response = requests.get(f"{BASE_URL}/tasks/tasks/running")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success', False)}")
            print(f"运行中任务数: {len(data.get('tasks', []))}")
            for task in data.get('tasks', []):
                print(f"  - 任务 {task['task_id'][:8]}: {task['status']} ({task.get('task_type', 'unknown')})")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

    # 测试获取已完成任务
    print("\n3. 测试获取已完成任务:")
    try:
        response = requests.get(f"{BASE_URL}/tasks/completed")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success', False)}")
            print(f"已完成任务数: {len(data.get('tasks', []))}")
            for task in data.get('tasks', []):
                print(f"  - 任务 {task['task_id'][:8]}: {task['status']} ({task.get('task_type', 'unknown')})")
                if 'completed_at' in task:
                    print(f"    完成时间: {task['completed_at']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_task_status_flow():
    """测试任务状态流转"""
    print("\n🔄 测试任务状态流转...")
    
    # 这里可以添加创建测试任务的逻辑
    # 但由于需要认证，我们先跳过
    print("需要认证才能创建测试任务，跳过此测试")

if __name__ == "__main__":
    print("=" * 50)
    print("任务管理API测试")
    print("=" * 50)
    
    test_task_apis()
    test_task_status_flow()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
