# 异步任务使用指南

## 🚀 快速开始

### 1. 启动异步任务

#### 模型训练
1. 进入 **模型训练** 页面
2. 配置训练参数
3. 在 **训练模式** 部分选择 **"异步训练（推荐）"**
4. 点击 **"开始训练"** 按钮
5. 系统提示任务已启动，您可以继续使用其他功能

#### 模型预测
1. 进入 **模型预测** 页面
2. 选择数据源和模型文件
3. 在 **预测模式** 部分选择 **"异步预测（推荐）"**
4. 点击 **"开始预测与检测"** 按钮
5. 系统提示任务已启动，您可以继续使用其他功能

### 2. 查看任务状态和结果

**访问路径**: 侧边栏菜单 → **任务管理**

## 📊 任务管理页面功能

### 实时监控面板
- **运行中任务**: 显示当前正在执行的任务数量
- **已完成任务**: 显示成功完成的任务数量  
- **失败任务**: 显示执行失败的任务数量
- **总任务数**: 显示历史总任务数量

### 任务列表
#### 运行中任务
- 🔄 **自动刷新**: 每5秒自动更新状态
- 📊 **进度显示**: 实时进度条和百分比
- ⏹️ **任务控制**: 可以取消正在运行的任务
- 👁️ **详情查看**: 查看任务的详细信息

#### 已完成任务
- ✅ **结果查看**: 查看训练或预测的完整结果
- 📋 **历史记录**: 保存所有已完成的任务
- 🗑️ **批量清理**: 一键清空所有已完成任务
- 📄 **详细信息**: 包含错误信息（如果失败）

## 🔍 查看任务结果

### 训练任务结果
点击已完成的训练任务 → **详情** 按钮，可以查看：

```json
{
  "train_shape": [数据集大小],
  "test_shape": [测试集大小], 
  "train_losses": [训练损失曲线],
  "val_losses": [验证损失曲线],
  "r2_score": 0.85,
  "model_save_path": "模型文件路径",
  "params_save_path": "参数文件路径",
  "scaler_y_save_path": "标准化器路径",
  "static_anomaly_threshold": 异常检测阈值
}
```

### 预测任务结果
点击已完成的预测任务 → **详情** 按钮，可以查看：

```json
{
  "predictions": [
    {
      "timestamp": "2025-01-01T00:00:00",
      "packets_per_sec": 1000,
      "packets_per_sec_smooth": 950,
      "pred_smooth": 980,
      "threshold": 1200,
      "is_anomaly": false
    }
  ],
  "anomaly_count": 5,
  "suggested_threshold": 1500,
  "model_name": "TCP_spt_sip_dip",
  "message": "预测成功"
}
```

## 💡 使用技巧

### 1. 任务监控
- ✅ 任务会自动在后台运行，无需等待
- ✅ 可以同时运行多个不同类型的任务
- ✅ 页面会自动刷新任务状态，无需手动刷新
- ⚠️ 避免同时运行过多大型任务以免影响性能

### 2. 结果管理
- 📥 **及时查看**: 任务完成后及时查看和下载结果
- 💾 **备份重要结果**: 重要的模型文件建议及时备份
- 🗑️ **定期清理**: 定期清理不需要的任务记录释放空间
- 📋 **复制任务ID**: 可以复制任务ID用于问题排查

### 3. 错误处理
如果任务失败：
1. 查看任务详情中的错误信息
2. 检查数据格式和参数配置
3. 根据错误提示调整参数
4. 重新提交任务

### 4. 性能优化
- 🎯 **合理参数**: 根据数据量调整批次大小和训练轮数
- ⚡ **资源管理**: 避免同时运行过多CPU密集型任务
- 📊 **监控进度**: 通过进度条判断任务是否正常运行

## 🔔 任务通知

### 完成通知
- 任务完成时会在页面显示通知消息
- 通知包含任务类型、状态和简要结果
- 失败任务会显示错误提示

### 状态指示
- 🔵 **运行中**: 蓝色旋转图标
- ✅ **已完成**: 绿色对勾图标  
- ❌ **失败**: 红色错误图标
- ⚠️ **已取消**: 黄色停止图标

## ❓ 常见问题

### Q: 任务提交后在哪里查看？
**A**: 侧边栏菜单 → 任务管理，所有任务都在这里显示

### Q: 可以同时运行多个任务吗？
**A**: 可以，系统支持多任务并行，但建议控制数量避免资源竞争

### Q: 任务失败了怎么办？
**A**: 点击失败任务的"详情"查看错误信息，根据提示调整参数后重新提交

### Q: 如何下载训练好的模型？
**A**: 模型文件保存在服务器指定目录，可通过文件管理功能下载

### Q: 任务可以取消吗？
**A**: 运行中的任务可以在任务管理页面点击"取消"按钮停止

### Q: 历史任务会保存多久？
**A**: 已完成的任务会持续保存，建议定期清理不需要的记录

## 🎯 最佳实践

1. **任务规划**: 
   - 先用小数据集测试参数
   - 确认无误后再用完整数据集训练

2. **资源管理**:
   - 合理安排任务执行时间
   - 避免在系统繁忙时提交大型任务

3. **结果备份**:
   - 重要模型及时下载备份
   - 记录有效的参数配置

4. **问题排查**:
   - 保存任务ID用于技术支持
   - 截图保存错误信息

---

**提示**: 异步任务让您可以充分利用系统资源，在训练或预测进行的同时继续其他工作，大大提高工作效率！
