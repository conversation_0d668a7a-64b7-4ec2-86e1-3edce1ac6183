#!/usr/bin/env python3
"""
验证图表移除效果的脚本
"""

import os
import re

def check_removed_chart_components():
    """检查已移除的图表组件"""
    print("🔍 检查已移除的图表组件")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查已移除的图表相关内容
        removed_items = [
            ("recharts库导入", r'import.*recharts'),
            ("LineChart导入", r'LineChart'),
            ("ResponsiveContainer导入", r'ResponsiveContainer'),
            ("XAxis导入", r'XAxis'),
            ("YAxis导入", r'YAxis'),
            ("CartesianGrid导入", r'CartesianGrid'),
            ("Tooltip导入", r'Tooltip'),
            ("Legend导入", r'Legend'),
            ("Line导入", r'Line'),
            ("图表容器", r'<ResponsiveContainer'),
            ("LineChart组件", r'<LineChart'),
            ("图表说明Alert", r'下图展示了真实流量'),
            ("预测结果图表标题", r'预测结果图表'),
            ("图表高度样式", r'height:\s*400')
        ]
        
        print(f"📊 图表组件移除检查:")
        all_removed = True
        
        for item_name, pattern in removed_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ❌ {item_name}: 仍然存在")
                all_removed = False
            else:
                print(f"   ✅ {item_name}: 已移除")
        
        return all_removed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_remaining_components():
    """检查保留的组件"""
    print(f"\n🔍 检查保留的组件")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查应该保留的内容
        retained_items = [
            ("PredictionResultDisplay组件", r'const PredictionResultDisplay'),
            ("建议阈值统计", r'建议的流量清洗阈值'),
            ("异常点数量统计", r'检测到的异常点数量'),
            ("阈值保存提示", r'此阈值已自动保存'),
            ("资源监控信息", r'资源使用情况'),
            ("预测耗时统计", r'预测耗时'),
            ("CPU使用率统计", r'CPU使用率'),
            ("内存使用统计", r'内存使用'),
            ("同步预测功能", r'同步预测'),
            ("异步预测结果", r'异步预测结果'),
            ("文件上传功能", r'<Upload'),
            ("模型选择功能", r'选择模型')
        ]
        
        print(f"📊 保留组件检查:")
        all_retained = True
        
        for item_name, pattern in retained_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ✅ {item_name}: 已保留")
            else:
                print(f"   ❌ {item_name}: 可能被误删")
                all_retained = False
        
        return all_retained
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_text_updates():
    """检查文本更新"""
    print(f"\n🔍 检查文本更新")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文本更新
        text_checks = [
            ("移除图表引用", r'基于图中的.*红色虚线', False),  # 应该不存在
            ("更新后的描述", r'基于动态阈值检测出的', True),   # 应该存在
        ]
        
        print(f"📝 文本更新检查:")
        text_ok = True
        
        for check_name, pattern, should_exist in text_checks:
            matches = re.findall(pattern, content, re.IGNORECASE)
            found = len(matches) > 0
            
            if should_exist and found:
                print(f"   ✅ {check_name}: 正确更新")
            elif not should_exist and not found:
                print(f"   ✅ {check_name}: 正确移除")
            else:
                print(f"   ❌ {check_name}: {'未找到' if should_exist else '仍然存在'}")
                text_ok = False
        
        return text_ok
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_file_size_reduction():
    """分析文件大小减少"""
    print(f"\n📊 分析文件大小变化")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计文件信息
        total_lines = len(content.split('\n'))
        total_chars = len(content)
        total_size = os.path.getsize(file_path)
        
        print(f"📋 当前文件状态:")
        print(f"   总行数: {total_lines}")
        print(f"   总字符数: {total_chars:,}")
        print(f"   文件大小: {total_size:,} 字节 ({total_size/1024:.1f} KB)")
        
        # 估算移除的内容
        estimated_removed_lines = 60  # 大约移除了60行（图表导入+图表组件）
        estimated_removed_chars = 2500  # 大约移除了2500字符
        
        print(f"\n📉 估算移除内容:")
        print(f"   移除行数: ~{estimated_removed_lines}")
        print(f"   移除字符数: ~{estimated_removed_chars:,}")
        print(f"   减少比例: ~{(estimated_removed_chars/(total_chars+estimated_removed_chars))*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_component_structure():
    """检查组件结构完整性"""
    print(f"\n🏗️  检查组件结构完整性")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查组件结构
        structure_checks = [
            ("PredictionResultDisplay函数", r'const PredictionResultDisplay.*=.*\{'),
            ("统计信息Row", r'<Row gutter.*<Statistic'),
            ("建议阈值Col", r'建议的流量清洗阈值'),
            ("异常点数量Col", r'检测到的异常点数量'),
            ("资源监控条件渲染", r'\(result\.duration_seconds.*\&\&'),
            ("资源监控Row", r'资源使用情况.*<Row'),
            ("组件返回语句", r'return\s*\(\s*<div>'),
            ("组件闭合", r'</div>\s*\)\s*;\s*\}')
        ]
        
        print(f"🔧 组件结构检查:")
        structure_ok = True
        
        for check_name, pattern in structure_checks:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ✅ {check_name}: 结构完整")
            else:
                print(f"   ⚠️  {check_name}: 可能有问题")
                structure_ok = False
        
        # 检查括号平衡
        open_braces = content.count('{')
        close_braces = content.count('}')
        open_parens = content.count('(')
        close_parens = content.count(')')
        
        print(f"\n🔍 语法检查:")
        print(f"   大括号: {open_braces} 开 / {close_braces} 闭 {'✅' if open_braces == close_braces else '❌'}")
        print(f"   小括号: {open_parens} 开 / {close_parens} 闭 {'✅' if open_parens == close_parens else '❌'}")
        
        syntax_ok = (open_braces == close_braces) and (open_parens == close_parens)
        
        return structure_ok and syntax_ok
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def provide_testing_instructions():
    """提供测试说明"""
    print(f"\n🧪 测试说明")
    print("=" * 60)
    
    print(f"📋 前端测试步骤:")
    print(f"1. 启动前端开发服务器")
    print(f"   cd frontend-react-stable")
    print(f"   npm start 或 yarn start")
    print(f"")
    print(f"2. 打开模型预测页面")
    print(f"   访问: http://localhost:3000/prediction")
    print(f"")
    print(f"3. 验证修改效果")
    print(f"   ✅ 应该看到: 统计信息（阈值、异常数量、资源使用）")
    print(f"   ❌ 不应该看到: 预测结果图表、时间序列图")
    print(f"")
    print(f"4. 测试功能完整性")
    print(f"   • 同步预测功能正常")
    print(f"   • 异步预测结果显示正常")
    print(f"   • 统计信息准确显示")
    print(f"   • 阈值保存提示正常")
    
    print(f"\n🎯 预期效果:")
    print(f"   ✅ 页面加载更快（无图表渲染）")
    print(f"   ✅ 界面更简洁清爽")
    print(f"   ✅ 关键信息突出显示")
    print(f"   ✅ 减少视觉干扰")

def create_performance_benefits():
    """创建性能优势说明"""
    print(f"\n⚡ 性能优势")
    print("=" * 60)
    
    print(f"📈 移除图表后的性能提升:")
    print(f"   ✅ 减少JavaScript包大小")
    print(f"   ✅ 降低内存使用（无图表DOM元素）")
    print(f"   ✅ 提高页面渲染速度")
    print(f"   ✅ 减少CPU使用（无图表动画）")
    print(f"   ✅ 改善移动端体验")
    
    print(f"\n📱 用户体验改善:")
    print(f"   ✅ 信息更聚焦（只显示关键数据）")
    print(f"   ✅ 页面响应更快")
    print(f"   ✅ 减少滚动需求")
    print(f"   ✅ 降低认知负担")

if __name__ == "__main__":
    print("🔍 图表移除效果验证")
    print("=" * 60)
    
    # 检查移除的图表组件
    removed_ok = check_removed_chart_components()
    
    # 检查保留的组件
    retained_ok = check_remaining_components()
    
    # 检查文本更新
    text_ok = check_text_updates()
    
    # 分析文件大小变化
    size_analyzed = analyze_file_size_reduction()
    
    # 检查组件结构
    structure_ok = check_component_structure()
    
    # 提供测试说明
    provide_testing_instructions()
    
    # 性能优势说明
    create_performance_benefits()
    
    print(f"\n" + "=" * 60)
    print("✅ 验证完成")
    
    print(f"\n🎯 修改总结:")
    print(f"   图表移除: {'✅ 成功' if removed_ok else '❌ 有问题'}")
    print(f"   组件保留: {'✅ 成功' if retained_ok else '❌ 有问题'}")
    print(f"   文本更新: {'✅ 成功' if text_ok else '❌ 有问题'}")
    print(f"   结构完整: {'✅ 正常' if structure_ok else '⚠️ 需要检查'}")
    
    if removed_ok and retained_ok and text_ok and structure_ok:
        print(f"\n🎉 修改成功!")
        print("   ✅ 预测结果图表已完全移除")
        print("   ✅ 核心统计信息保持完整")
        print("   ✅ 页面结构正常")
        print("   ✅ 性能得到优化")
        print("   ✅ 可以进行前端测试")
    else:
        print(f"\n⚠️  需要进一步检查:")
        if not removed_ok:
            print("   - 某些图表组件可能未完全移除")
        if not retained_ok:
            print("   - 某些重要组件可能被误删")
        if not text_ok:
            print("   - 文本描述可能需要调整")
        if not structure_ok:
            print("   - 组件结构可能有问题")
    
    print(f"\n💡 下一步:")
    print("   1. 启动前端开发服务器")
    print("   2. 测试模型预测页面")
    print("   3. 验证统计信息显示")
    print("   4. 确认性能改善效果")
