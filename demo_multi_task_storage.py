#!/usr/bin/env python3
"""
演示多次异步任务的JSON存储结构
"""

import json
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_demo_multi_task_storage():
    """创建多任务存储演示"""
    print("🔍 多次异步任务JSON存储结构演示")
    print("=" * 60)
    
    # 模拟多个异步任务的存储结构
    
    # ========== task_storage.json 结构 ==========
    tasks_data = {
        # 第一个训练任务
        "traffic_12345678-1234-5678-9abc-123456789abc": {
            "task_id": "traffic_12345678-1234-5678-9abc-123456789abc",
            "task_type": "training",
            "status": "completed",
            "params": {
                "csv_filename": "traffic_data.csv",
                "selected_prots": ["TCP", "UDP"],
                "selected_datatypes": {
                    "TCP": ["spt_sip_dip", "dpt_sip_dip"],
                    "UDP": ["spt_sip_dip"]
                },
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 100
            },
            "created_at": "2025-07-24T10:00:00.000Z",
            "started_at": "2025-07-24T10:00:05.000Z",
            "completed_at": "2025-07-24T10:02:30.000Z",
            "updated_at": "2025-07-24T10:02:30.000Z",
            "progress": 100,
            "current_step": "训练完成",
            "total_steps": 100,
            "error": None
        },
        
        # 第二个训练任务（不同数据）
        "network_87654321-4321-8765-cba9-987654321cba": {
            "task_id": "network_87654321-4321-8765-cba9-987654321cba",
            "task_type": "training",
            "status": "completed",
            "params": {
                "csv_filename": "network_logs.csv",
                "selected_prots": ["ICMP"],
                "selected_datatypes": {
                    "ICMP": ["dip", "sip"]
                },
                "learning_rate": 0.002,
                "batch_size": 64,
                "epochs": 150
            },
            "created_at": "2025-07-24T11:00:00.000Z",
            "started_at": "2025-07-24T11:00:03.000Z",
            "completed_at": "2025-07-24T11:03:45.000Z",
            "updated_at": "2025-07-24T11:03:45.000Z",
            "progress": 100,
            "current_step": "训练完成",
            "total_steps": 150,
            "error": None
        },
        
        # 第三个预测任务
        "predict_11111111-2222-3333-4444-555555555555": {
            "task_id": "predict_11111111-2222-3333-4444-555555555555",
            "task_type": "prediction",
            "status": "completed",
            "params": {
                "csv_filename": "test_data.csv",
                "model_names": ["TCP_spt_sip_dip", "UDP_spt_sip_dip"]
            },
            "created_at": "2025-07-24T12:00:00.000Z",
            "started_at": "2025-07-24T12:00:02.000Z",
            "completed_at": "2025-07-24T12:01:15.000Z",
            "updated_at": "2025-07-24T12:01:15.000Z",
            "progress": 100,
            "current_step": "预测完成",
            "total_steps": 50,
            "error": None
        },
        
        # 第四个正在运行的训练任务
        "running_22222222-3333-4444-5555-666666666666": {
            "task_id": "running_22222222-3333-4444-5555-666666666666",
            "task_type": "training",
            "status": "running",
            "params": {
                "csv_filename": "large_dataset.csv",
                "selected_prots": ["TCP", "UDP", "ICMP"],
                "selected_datatypes": {
                    "TCP": ["spt_sip_dip", "dpt_sip_dip"],
                    "UDP": ["spt_sip_dip", "dpt_sip_dip"],
                    "ICMP": ["dip", "sip"]
                },
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 200
            },
            "created_at": "2025-07-24T13:00:00.000Z",
            "started_at": "2025-07-24T13:00:05.000Z",
            "completed_at": None,
            "updated_at": "2025-07-24T13:05:30.000Z",
            "progress": 45,
            "current_step": "训练中 - Epoch 90/200",
            "total_steps": 200,
            "error": None
        }
    }
    
    # ========== task_results.json 结构 ==========
    results_data = {
        # 第一个训练任务的结果
        "traffic_12345678-1234-5678-9abc-123456789abc": {
            "results": {
                "TCP_spt_sip_dip": {
                    "r2_score": 0.8567,
                    "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_model_best.pth",
                    "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_params.json",
                    "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_test.csv",
                    "predictions": [1.23, 2.45, 3.67, 4.89, 5.12],
                    "train_shape": [800, 5],
                    "test_shape": [200, 5]
                },
                "TCP_dpt_sip_dip": {
                    "r2_score": 0.8234,
                    "model_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_model_best.pth",
                    "params_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_params.json",
                    "scaler_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_test.csv",
                    "predictions": [2.34, 3.45, 4.56, 5.67, 6.78],
                    "train_shape": [800, 5],
                    "test_shape": [200, 5]
                },
                "UDP_spt_sip_dip": {
                    "r2_score": 0.7891,
                    "model_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_model_best.pth",
                    "params_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_params.json",
                    "scaler_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_test.csv",
                    "predictions": [3.45, 4.56, 5.67, 6.78, 7.89],
                    "train_shape": [750, 5],
                    "test_shape": [180, 5]
                }
            },
            "duration_seconds": 145.5,
            "cpu_percent": 28.7,
            "memory_mb": 1024.3,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        },
        
        # 第二个训练任务的结果
        "network_87654321-4321-8765-cba9-987654321cba": {
            "results": {
                "ICMP_dip": {
                    "r2_score": 0.7234,
                    "model_path": "/data/output/network_logs_ICMP_dip_20250724_110000_model_best.pth",
                    "params_path": "/data/output/network_logs_ICMP_dip_20250724_110000_params.json",
                    "scaler_path": "/data/output/network_logs_ICMP_dip_20250724_110000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/network_logs_ICMP_dip_20250724_110000_test.csv",
                    "predictions": [0.12, 0.34, 0.56, 0.78, 0.91],
                    "train_shape": [600, 4],
                    "test_shape": [150, 4]
                },
                "ICMP_sip": {
                    "r2_score": 0.6987,
                    "model_path": "/data/output/network_logs_ICMP_sip_20250724_110000_model_best.pth",
                    "params_path": "/data/output/network_logs_ICMP_sip_20250724_110000_params.json",
                    "scaler_path": "/data/output/network_logs_ICMP_sip_20250724_110000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/network_logs_ICMP_sip_20250724_110000_test.csv",
                    "predictions": [1.45, 2.67, 3.89, 4.12, 5.34],
                    "train_shape": [600, 4],
                    "test_shape": [150, 4]
                }
            },
            "duration_seconds": 225.8,
            "cpu_percent": 32.1,
            "memory_mb": 768.9,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        },
        
        # 第三个预测任务的结果
        "predict_11111111-2222-3333-4444-555555555555": {
            "predictions": [
                {"timestamp": "2025-07-24T12:00:00", "value": 1.23, "is_anomaly": False},
                {"timestamp": "2025-07-24T12:02:00", "value": 8.45, "is_anomaly": True},
                {"timestamp": "2025-07-24T12:04:00", "value": 2.67, "is_anomaly": False},
                {"timestamp": "2025-07-24T12:06:00", "value": 9.12, "is_anomaly": True},
                {"timestamp": "2025-07-24T12:08:00", "value": 3.89, "is_anomaly": False}
            ],
            "anomaly_count": 2,
            "suggested_threshold": 7.5,
            "model_name": "TCP_spt_sip_dip",
            "duration_seconds": 75.2,
            "cpu_percent": 15.6,
            "memory_mb": 256.4,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        }
        
        # 注意：正在运行的任务没有结果数据
    }
    
    # 保存演示文件
    demo_tasks_file = "demo_task_storage.json"
    demo_results_file = "demo_task_results.json"
    
    try:
        with open(demo_tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks_data, f, ensure_ascii=False, indent=2)
        
        with open(demo_results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        print("✅ 演示文件创建成功:")
        print(f"   - {demo_tasks_file}")
        print(f"   - {demo_results_file}")
        
        return tasks_data, results_data
        
    except Exception as e:
        print(f"❌ 创建演示文件失败: {e}")
        return None, None

def analyze_storage_structure(tasks_data, results_data):
    """分析存储结构"""
    print(f"\n📊 存储结构分析:")
    print("=" * 60)
    
    print(f"📋 任务存储 (task_storage.json):")
    print(f"   总任务数: {len(tasks_data)}")
    
    # 按状态统计
    status_count = {}
    type_count = {}
    for task in tasks_data.values():
        status = task['status']
        task_type = task['task_type']
        status_count[status] = status_count.get(status, 0) + 1
        type_count[task_type] = type_count.get(task_type, 0) + 1
    
    print(f"   按状态分布:")
    for status, count in status_count.items():
        print(f"     - {status}: {count}个")
    
    print(f"   按类型分布:")
    for task_type, count in type_count.items():
        print(f"     - {task_type}: {count}个")
    
    print(f"\n📋 结果存储 (task_results.json):")
    print(f"   有结果的任务数: {len(results_data)}")
    
    # 分析结果类型
    training_results = 0
    prediction_results = 0
    total_models = 0
    
    for task_id, result in results_data.items():
        if 'results' in result:  # 训练结果
            training_results += 1
            total_models += len(result['results'])
        elif 'predictions' in result:  # 预测结果
            prediction_results += 1
    
    print(f"   训练结果: {training_results}个")
    print(f"   预测结果: {prediction_results}个")
    print(f"   总模型数: {total_models}个")

def demonstrate_file_operations():
    """演示文件操作"""
    print(f"\n🔧 文件操作演示:")
    print("=" * 60)
    
    demo_tasks_file = "demo_task_storage.json"
    demo_results_file = "demo_task_results.json"
    
    # 演示读取操作
    print(f"📖 读取操作:")
    try:
        with open(demo_tasks_file, 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open(demo_results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"   ✅ 成功读取任务数据: {len(tasks)}个任务")
        print(f"   ✅ 成功读取结果数据: {len(results)}个结果")
        
    except Exception as e:
        print(f"   ❌ 读取失败: {e}")
        return
    
    # 演示添加新任务
    print(f"\n📝 添加新任务演示:")
    new_task_id = "new_33333333-4444-5555-6666-777777777777"
    new_task = {
        "task_id": new_task_id,
        "task_type": "training",
        "status": "pending",
        "params": {
            "csv_filename": "new_data.csv",
            "selected_prots": ["TCP"],
            "selected_datatypes": {"TCP": ["spt_sip_dip"]}
        },
        "created_at": datetime.now().isoformat(),
        "started_at": None,
        "completed_at": None,
        "updated_at": datetime.now().isoformat(),
        "progress": 0,
        "current_step": "等待开始",
        "total_steps": 100,
        "error": None
    }
    
    # 添加到现有数据
    tasks[new_task_id] = new_task
    
    # 保存更新后的数据
    try:
        with open(demo_tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 成功添加新任务: {new_task_id}")
        print(f"   ✅ 文件已更新，现有 {len(tasks)} 个任务")
        
    except Exception as e:
        print(f"   ❌ 添加任务失败: {e}")
    
    # 演示添加结果
    print(f"\n📊 添加结果演示:")
    new_result = {
        "results": {
            "TCP_spt_sip_dip": {
                "r2_score": 0.9123,
                "model_path": "/data/output/new_data_TCP_spt_sip_dip_20250724_140000_model_best.pth",
                "predictions": [1.1, 2.2, 3.3, 4.4, 5.5],
                "train_shape": [900, 5],
                "test_shape": [225, 5]
            }
        },
        "duration_seconds": 180.3,
        "cpu_percent": 30.5,
        "memory_mb": 896.7
    }
    
    results[new_task_id] = new_result
    
    try:
        with open(demo_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 成功添加新结果: {new_task_id}")
        print(f"   ✅ 文件已更新，现有 {len(results)} 个结果")
        
    except Exception as e:
        print(f"   ❌ 添加结果失败: {e}")

def show_file_sizes():
    """显示文件大小"""
    print(f"\n📏 文件大小分析:")
    print("=" * 60)
    
    files = ["demo_task_storage.json", "demo_task_results.json"]
    
    for filename in files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"📁 {filename}:")
            print(f"   文件大小: {size:,} 字节 ({size/1024:.1f} KB)")
            
            # 分析内容
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   数据条目: {len(data)}")
                
                if len(data) > 0:
                    # 估算单个条目大小
                    avg_size = size / len(data)
                    print(f"   平均条目大小: {avg_size:.0f} 字节")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")

def cleanup_demo_files():
    """清理演示文件"""
    demo_files = [
        "demo_task_storage.json",
        "demo_task_results.json"
    ]
    
    print(f"\n🗑️  清理演示文件:")
    for filename in demo_files:
        if os.path.exists(filename):
            os.remove(filename)
            print(f"   ✅ 已删除: {filename}")

if __name__ == "__main__":
    print("🔍 多次异步任务JSON存储演示")
    print("=" * 60)
    
    # 创建演示数据
    tasks_data, results_data = create_demo_multi_task_storage()
    
    if tasks_data and results_data:
        # 分析存储结构
        analyze_storage_structure(tasks_data, results_data)
        
        # 演示文件操作
        demonstrate_file_operations()
        
        # 显示文件大小
        show_file_sizes()
        
        # 清理演示文件
        cleanup_demo_files()
    
    print(f"\n" + "=" * 60)
    print("✅ 演示完成")
    print(f"\n🎯 多任务存储要点:")
    print("1. 每个任务都有唯一的task_id作为键")
    print("2. 任务信息和结果分别存储在两个JSON文件中")
    print("3. 支持同时存储多个不同类型的任务")
    print("4. 文件会随着任务增加而增长")
    print("5. 可以通过task_id快速查找特定任务的信息和结果")
