#!/usr/bin/env python3
"""
测试资源监控修复的脚本
"""

import json
import os
import psutil
import time

def test_cpu_monitoring():
    """测试CPU监控逻辑"""
    print("🔍 测试CPU监控逻辑...")
    print("=" * 50)
    
    process = psutil.Process()
    cpu_cores = psutil.cpu_count()
    
    print(f"CPU核心数: {cpu_cores}")
    
    # 测试不同的interval参数
    print("\n测试不同的interval参数:")
    
    # interval=None (可能返回0)
    start_time = time.time()
    cpu_usage_none = process.cpu_percent(interval=None)
    print(f"interval=None: {cpu_usage_none}% (可能为0，因为没有间隔)")
    
    # interval=0.1 (推荐)
    cpu_usage_01 = process.cpu_percent(interval=0.1)
    print(f"interval=0.1: {cpu_usage_01}% (推荐使用)")
    
    # interval=1.0 (更准确但更慢)
    cpu_usage_1 = process.cpu_percent(interval=1.0)
    print(f"interval=1.0: {cpu_usage_1}% (更准确但更慢)")
    
    # 标准化CPU使用率
    cpu_normalized_01 = cpu_usage_01 / cpu_cores if cpu_cores else cpu_usage_01
    cpu_normalized_1 = cpu_usage_1 / cpu_cores if cpu_cores else cpu_usage_1
    
    print(f"\n标准化CPU使用率:")
    print(f"interval=0.1 标准化: {cpu_normalized_01:.2f}%")
    print(f"interval=1.0 标准化: {cpu_normalized_1:.2f}%")

def test_gpu_monitoring():
    """测试GPU监控逻辑"""
    print("\n🔍 测试GPU监控逻辑...")
    print("=" * 50)
    
    import torch
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"PyTorch设备: {device}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        print(f"当前CUDA设备: {torch.cuda.current_device()}")
        print(f"设备名称: {torch.cuda.get_device_name()}")
    
    # 测试GPU监控代码
    gpu_memory_mb = 0
    gpu_utilization_percent = 0
    
    try:
        import pynvml
        print("\npynvml模块可用")
        
        if device.type == 'cuda':
            print("检测到CUDA设备，尝试获取GPU信息...")
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            util_rates = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_memory_mb = mem_info.used / (1024 * 1024)
            gpu_utilization_percent = util_rates.gpu
            pynvml.nvmlShutdown()
            
            print(f"✅ GPU内存使用: {gpu_memory_mb:.2f} MB")
            print(f"✅ GPU利用率: {gpu_utilization_percent}%")
        else:
            print("❌ 使用CPU设备，GPU监控值应为0")
            print(f"GPU内存使用: {gpu_memory_mb} MB")
            print(f"GPU利用率: {gpu_utilization_percent}%")
            
    except ImportError:
        print("❌ pynvml模块不可用")
        print(f"GPU内存使用: {gpu_memory_mb} MB (默认值)")
        print(f"GPU利用率: {gpu_utilization_percent}% (默认值)")
    except Exception as e:
        print(f"❌ GPU监控失败: {e}")
        print(f"GPU内存使用: {gpu_memory_mb} MB (默认值)")
        print(f"GPU利用率: {gpu_utilization_percent}% (默认值)")

def check_model_registry():
    """检查模型仓库中的资源信息"""
    print("\n🔍 检查模型仓库中的资源信息...")
    print("=" * 50)
    
    registry_file = "backend/model_registry.json"
    
    if not os.path.exists(registry_file):
        print("❌ 模型仓库文件不存在")
        return
    
    try:
        with open(registry_file, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        models = registry.get("models", [])
        print(f"📊 模型总数: {len(models)}")
        
        if len(models) == 0:
            print("ℹ️  暂无模型数据")
            return
        
        print("\n📋 最近5个模型的资源信息:")
        print("-" * 70)
        
        recent_models = models[-5:] if len(models) >= 5 else models
        
        for i, model in enumerate(recent_models, 1):
            print(f"\n🔸 模型 {i}:")
            print(f"   ID: {model.get('model_id', 'N/A')}")
            print(f"   训练时间: {model.get('training_time', 'N/A')}")
            print(f"   训练时长: {model.get('training_duration', 'N/A')} 秒")
            print(f"   CPU使用率: {model.get('cpu_usage', 'N/A')}%")
            print(f"   内存使用: {model.get('memory_usage', 'N/A')} MB")
            print(f"   GPU内存: {model.get('gpu_memory', 'N/A')} MB")
            print(f"   GPU利用率: {model.get('gpu_utilization', 'N/A')}%")
            
            # 检查问题
            issues = []
            if not model.get('training_time'):
                issues.append("缺少训练时间")
            if model.get('cpu_usage') == 0 or model.get('cpu_usage') is None:
                issues.append("CPU使用率为0或空")
            if not model.get('training_duration'):
                issues.append("缺少训练时长")
                
            if issues:
                print(f"   ⚠️  问题: {', '.join(issues)}")
            else:
                print(f"   ✅ 资源信息完整")
        
    except Exception as e:
        print(f"❌ 读取模型仓库失败: {e}")

def simulate_training_monitoring():
    """模拟训练过程的资源监控"""
    print("\n🔍 模拟训练过程的资源监控...")
    print("=" * 50)
    
    from datetime import datetime
    import time
    import psutil
    
    # 模拟训练开始
    start_time = time.time()
    training_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    process = psutil.Process()
    cpu_cores = psutil.cpu_count()
    
    print(f"训练开始时间: {training_start_time}")
    print(f"CPU核心数: {cpu_cores}")
    
    # 模拟一些计算负载
    print("模拟计算负载...")
    dummy_calc = sum(i * i for i in range(100000))
    
    # 模拟训练结束
    time.sleep(0.2)  # 短暂等待以获取CPU使用率
    
    duration = time.time() - start_time
    cpu_usage = process.cpu_percent(interval=0.1)
    cpu_usage_normalized = cpu_usage / cpu_cores if cpu_cores else cpu_usage
    memory_mb = process.memory_info().rss / (1024 * 1024)
    
    print(f"\n📊 监控结果:")
    print(f"   训练时长: {duration:.2f} 秒")
    print(f"   CPU使用率: {cpu_usage:.2f}%")
    print(f"   CPU标准化: {cpu_usage_normalized:.2f}%")
    print(f"   内存使用: {memory_mb:.2f} MB")
    
    # 验证数据有效性
    print(f"\n✅ 数据验证:")
    print(f"   训练时间非空: {'是' if training_start_time else '否'}")
    print(f"   CPU使用率>0: {'是' if cpu_usage > 0 else '否'}")
    print(f"   训练时长>0: {'是' if duration > 0 else '否'}")
    print(f"   内存使用>0: {'是' if memory_mb > 0 else '否'}")

if __name__ == "__main__":
    print("🔍 资源监控修复验证")
    print("=" * 60)
    
    test_cpu_monitoring()
    test_gpu_monitoring()
    check_model_registry()
    simulate_training_monitoring()
    
    print("\n" + "=" * 60)
    print("✅ 验证完成")
    print("\n💡 修复总结:")
    print("1. ✅ 修复了CPU使用率监控 (interval=None → interval=0.1)")
    print("2. ✅ 添加了训练开始时间记录")
    print("3. ✅ GPU监控逻辑正确 (CPU训练时为0)")
    print("4. ✅ 添加了cleaning_threshold字段")
    print("\n🧪 测试建议:")
    print("1. 提交一个新的异步训练任务")
    print("2. 在模型仓库中检查新模型的资源信息")
    print("3. 确认CPU使用率和训练时间不再为空")
