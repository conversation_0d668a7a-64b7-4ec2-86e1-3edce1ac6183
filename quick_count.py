#!/usr/bin/env python3

# 手动记录主要文件的行数（通过view工具查看得到）
file_lines = {
    # 后端Python文件
    'backend/main.py': 47,
    'backend/auth.py': 170,  # 从view结果看到
    'backend/clean_template.py': 0,  # 待查看
    'backend/data_cleaning.py': 0,  # 待查看
    'backend/data_query.py': 48,  # 从之前的搜索结果看到
    'backend/model_prediction.py': 0,  # 待查看
    'backend/model_registry.py': 0,  # 待查看
    'backend/model_training.py': 0,  # 待查看
    'backend/task_manager.py': 0,  # 待查看
}

print("正在统计项目代码行数...")
print("已知文件行数:")
for file, lines in file_lines.items():
    if lines > 0:
        print(f"  {file}: {lines} 行")

print(f"\n当前已统计: {sum(file_lines.values())} 行")
print("需要继续查看其他文件...")
