{"training_12345678-1234-5678-9abc-123456789abc": {"results": {"TCP_spt_sip_dip": {"r2_score": 0.8567, "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_model_best.pth", "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_params.json", "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_scaler_y_best.pkl", "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_test.csv", "predictions": [1.23, 2.45, 3.67, 4.89, 5.12], "train_shape": [800, 5], "test_shape": [200, 5]}, "TCP_dpt_sip_dip": {"r2_score": 0.8234, "model_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_model_best.pth", "predictions": [2.34, 3.45, 4.56, 5.67, 6.78], "train_shape": [800, 5], "test_shape": [200, 5]}, "UDP_spt_sip_dip": {"r2_score": 0.7891, "model_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_model_best.pth", "predictions": [3.45, 4.56, 5.67, 6.78, 7.89], "train_shape": [750, 5], "test_shape": [180, 5]}}, "duration_seconds": 420.5, "cpu_percent": 28.7, "memory_mb": 1024.3, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}, "prediction_87654321-4321-8765-cba9-987654321cba": {"predictions": [{"timestamp": "2025-07-24 10:00:00", "packets_per_sec": 1500, "packets_per_sec_smooth": 1480, "pred_smooth": 1520, "threshold": 2000, "is_anomaly": false}, {"timestamp": "2025-07-24 10:01:00", "packets_per_sec": 2800, "packets_per_sec_smooth": 2750, "pred_smooth": 1530, "threshold": 2000, "is_anomaly": true}, {"timestamp": "2025-07-24 10:02:00", "packets_per_sec": 1200, "packets_per_sec_smooth": 1250, "pred_smooth": 1540, "threshold": 2000, "is_anomaly": false}], "anomaly_count": 1, "suggested_threshold": 2000, "model_name": "TCP_spt_sip_dip", "message": "预测成功", "duration_seconds": 180.5, "cpu_percent": 15.6, "memory_mb": 256.4, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}, "training_11111111-2222-3333-4444-555555555555": {"results": {"ICMP_dip": {"r2_score": 0.7234, "model_path": "/data/output/network_logs_ICMP_dip_20250724_110000_model_best.pth", "params_path": "/data/output/network_logs_ICMP_dip_20250724_110000_params.json", "scaler_path": "/data/output/network_logs_ICMP_dip_20250724_110000_scaler_y_best.pkl", "test_data_path": "/data/output/network_logs_ICMP_dip_20250724_110000_test.csv", "predictions": [0.12, 0.34, 0.56, 0.78, 0.91], "train_shape": [600, 4], "test_shape": [150, 4]}, "ICMP_sip": {"r2_score": 0.6987, "model_path": "/data/output/network_logs_ICMP_sip_20250724_110000_model_best.pth", "predictions": [1.45, 2.67, 3.89, 4.12, 5.34], "train_shape": [600, 4], "test_shape": [150, 4]}}, "duration_seconds": 360.8, "cpu_percent": 32.1, "memory_mb": 768.9, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}, "prediction_22222222-3333-4444-5555-666666666666": {"predictions": [{"timestamp": "2025-07-24 11:00:00", "packets_per_sec": 800, "packets_per_sec_smooth": 820, "pred_smooth": 850, "threshold": 1200, "is_anomaly": false}, {"timestamp": "2025-07-24 11:01:00", "packets_per_sec": 1500, "packets_per_sec_smooth": 1480, "pred_smooth": 860, "threshold": 1200, "is_anomaly": true}], "anomaly_count": 1, "suggested_threshold": 1200, "model_name": "ICMP_dip", "message": "预测成功", "duration_seconds": 120.3, "cpu_percent": 12.4, "memory_mb": 180.7, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}, "C20230330-00031_3f14a67d-1fa7-4ecd-aa29-c902393b78e8": {"results": {"ICMP_dip": {"train_shape": [4140, 5], "test_shape": [1035, 5], "train_losses": [0.006088850241894761, 0.006486190246278056, 0.005951576018674132, 0.005914454133971958, 0.005887720036810151, 0.005865816326945605, 0.005852393165696412, 0.005855640756522007, 0.005838831137282341, 0.006333653442482046, 0.005815372006338748, 0.005808748960532739, 0.005787539831544564, 0.006290169294459247, 0.00577596776437872, 0.005770370924324197, 0.005765476711928841, 0.005758133987995961, 0.0057637375658998735, 0.005744206738484081, 0.0057364538924673525, 0.0057308052235782725, 0.0057198318328914866, 0.005717680041072981, 0.005724527223122777, 0.00572567631618881, 0.005700044931018056, 0.00569017209434719, 0.005712772020928014, 0.005685985557050799, 0.005703583944409729, 0.005656314407531227, 0.005675816230706232, 0.005676684164455992, 0.0056620041030441826, 0.005667907392336247, 0.0057016644289262786, 0.00566620091308323, 0.005673138645391873, 0.0056625032080148685, 0.005638417871779211, 0.006156549849379189, 0.005678618061267571, 0.005677463421875473, 0.00568776151885686, 0.005646491375983942, 0.005705814057790803, 0.005667062617874308, 0.005667690002184145, 0.006174193819163065, 0.0056708573816024455, 0.005650285535438115, 0.005687792299795547, 0.005686692271524407, 0.006120376771434621, 0.0066207168313212605, 0.005667965182738044, 0.005643676703044775, 0.005647296717029349, 0.0056373937066211965, 0.00563912137752709, 0.005662021275639972, 0.005654928314811514, 0.0056754517424053574, 0.005642153902414293, 0.005643886226351924, 0.005645551829474016, 0.005652038237656143, 0.005679708449008558, 0.005667752640903245, 0.0056731326574777086, 0.00611173249083663, 0.005651230028405507, 0.0056583077627010425, 0.005650726604386591, 0.005647950531397328, 0.005646853981943423, 0.0056653193981901495, 0.005635924722696473, 0.005662835490454654, 0.005659177595077836, 0.005658098144692758, 0.0056373178506703425, 0.006176181050619789, 0.005641970640454019, 0.005658107774820564, 0.005674568511864209, 0.005647961100379462, 0.0056315683008563155, 0.005639662908350829, 0.005684380699733538, 0.005659403713877964, 0.00565128412151239, 0.005642619789256287, 0.005640258712105606, 0.005678752335001036, 0.005643407855643545, 0.005671027918566551, 0.005669381034493881, 0.00567433428884101], "val_losses": [0.020814547315239906, 0.020808694884181023, 0.020792672410607338, 0.020786035805940628, 0.02077103964984417, 0.020765773952007294, 0.020750895142555237, 0.020740747451782227, 0.020734364166855812, 0.02071884460747242, 0.020709220319986343, 0.020705673843622208, 0.020678086206316948, 0.020667560398578644, 0.02064419351518154, 0.02063465304672718, 0.020609533414244652, 0.020585542544722557, 0.02057403326034546, 0.020554207265377045, 0.020537128672003746, 0.0205222200602293, 0.020475070923566818, 0.02045256644487381, 0.020430492237210274, 0.020421363413333893, 0.020386531949043274, 0.02038520574569702, 0.020338980481028557, 0.0203304011374712, 0.020321562886238098, 0.020318394526839256, 0.02032046765089035, 0.02031654492020607, 0.020314104855060577, 0.020312774926424026, 0.02030828408896923, 0.020306287333369255, 0.02031063660979271, 0.020304180681705475, 0.020302819088101387, 0.0202985517680645, 0.02029798924922943, 0.020298447459936142, 0.020291833207011223, 0.0202905535697937, 0.020290914922952652, 0.020290479063987732, 0.02028956450521946, 0.02029341086745262, 0.02028775028884411, 0.02028450183570385, 0.020279720425605774, 0.0202812347561121, 0.020276417955756187, 0.020276308059692383, 0.020271629095077515, 0.020271720364689827, 0.020268555730581284, 0.020266994833946228, 0.020266849547624588, 0.020266640931367874, 0.020266512408852577, 0.020266177132725716, 0.020266296342015266, 0.020266074687242508, 0.020265864208340645, 0.020265638828277588, 0.020265601575374603, 0.020265312865376472, 0.020265163853764534, 0.0202649999409914, 0.020264897495508194, 0.020264914259314537, 0.020264822989702225, 0.020264379680156708, 0.020264342427253723, 0.02026425488293171, 0.02026413008570671, 0.020263496786355972, 0.02026337757706642, 0.020263096317648888, 0.0202629491686821, 0.020263034850358963, 0.020262461155653, 0.020262429490685463, 0.020262354984879494, 0.020262183621525764, 0.0202620979398489, 0.020262179896235466, 0.020262176170945168, 0.020262151956558228, 0.020262133330106735, 0.020262135192751884, 0.0202620942145586, 0.02026209607720375, 0.02026207186281681, 0.02026205323636532, 0.020262032747268677, 0.020262015983462334], "r2": 0.6163641665340588, "y_test_actual": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17], "y_pred": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 15, 14, 13, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 15, 14, 13, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 15, 14, 13, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 15, 14, 13, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "output_csv_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_predictions.csv", "predictions_filename": "C20230330-000318_2025-06_ICMP_dip_20250724_060643_predictions.csv", "weight_avg": 0.056601322614647905, "r2_score": 0.6163641665340588, "finished_time": "2025-07-24 06:16:43.063995", "model_save_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_model_best.pth", "params_save_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_params.json", "test_save_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_test.csv", "scaler_y_save_path": "/home/<USER>/C20230330-000318_2025-06_ICMP_dip_20250724_060643_scaler_y_best.pkl", "static_anomaly_threshold": 4.724246037733986, "duration_seconds": 600.7270414829254, "cpu_percent": 83.7975, "memory_mb": 543.265625, "gpu_memory_mb": 0, "gpu_utilization_percent": 0, "model_id": "model_20250724_061643"}}, "result_path": "/home/<USER>/C20230330-000318_2025-06_training_results.json", "total_models": 1}, "async_prediction_20250724_064928": {"predictions": [{"timestamp": "2025-07-24 10:00:00", "packets_per_sec": 1500, "packets_per_sec_smooth": 1480, "pred_smooth": 1520, "threshold": 2000, "is_anomaly": false}, {"timestamp": "2025-07-24 10:01:00", "packets_per_sec": 2800, "packets_per_sec_smooth": 2750, "pred_smooth": 1530, "threshold": 2000, "is_anomaly": true}, {"timestamp": "2025-07-24 10:02:00", "packets_per_sec": 1200, "packets_per_sec_smooth": 1250, "pred_smooth": 1540, "threshold": 2000, "is_anomaly": false}, {"timestamp": "2025-07-24 10:03:00", "packets_per_sec": 3200, "packets_per_sec_smooth": 3150, "pred_smooth": 1550, "threshold": 2000, "is_anomaly": true}, {"timestamp": "2025-07-24 10:04:00", "packets_per_sec": 1800, "packets_per_sec_smooth": 1820, "pred_smooth": 1560, "threshold": 2000, "is_anomaly": false}], "anomaly_count": 2, "suggested_threshold": 2000, "model_name": "TCP_spt_sip_dip", "message": "异步预测成功完成", "duration_seconds": 240.5, "cpu_percent": 18.3, "memory_mb": 312.7, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}, "prediction_20250724_065722": {"predictions": [{"timestamp": "2025-07-24 14:00:00", "packets_per_sec": 1200, "packets_per_sec_smooth": 1180, "pred_smooth": 1220, "threshold": 1800, "is_anomaly": false}, {"timestamp": "2025-07-24 14:01:00", "packets_per_sec": 2200, "packets_per_sec_smooth": 2180, "pred_smooth": 1230, "threshold": 1800, "is_anomaly": true}, {"timestamp": "2025-07-24 14:02:00", "packets_per_sec": 1100, "packets_per_sec_smooth": 1120, "pred_smooth": 1240, "threshold": 1800, "is_anomaly": false}], "anomaly_count": 1, "suggested_threshold": 1800, "model_name": "TCP_spt_sip_dip", "message": "修复测试预测成功", "duration_seconds": 120.0, "cpu_percent": 15.0, "memory_mb": 200.0, "gpu_memory_mb": 0, "gpu_utilization_percent": 0}}