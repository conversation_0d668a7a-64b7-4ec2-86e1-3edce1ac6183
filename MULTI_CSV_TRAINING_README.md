# 多CSV文件模型训练功能

## 功能概述

新增的多CSV文件训练功能允许用户在一次任务中选择多个CSV文件/目录，为每个文件配置不同的模型保存路径，实现批量训练多个模型。

## 主要特性

### 1. 训练模式选择
- **单文件训练**：保持原有功能，训练单个CSV文件
- **多文件批量训练**：新增功能，可同时训练多个CSV文件

### 2. 数据源配置
- **本地文件**：选择本地目录中的CSV文件
- **文件上传**：上传CSV文件进行训练
- **混合模式**：可以同时配置本地文件和上传文件

### 3. 独立配置
- 每个数据源可配置独立的模型保存路径
- 避免输出路径冲突的验证机制
- 支持为不同数据源设置不同的文件名标识

## 使用方法

### 前端操作步骤

1. **选择训练模式**
   - 在"训练模式"卡片中选择"多文件批量训练"

2. **配置数据源**
   - 点击"添加数据源"按钮添加新的数据源
   - 为每个数据源选择类型（本地文件或上传文件）
   - 配置相应的文件路径和模型保存路径

3. **本地文件配置**
   - 输入CSV文件目录路径
   - 点击"刷新"获取可用文件列表
   - 选择要训练的CSV文件
   - 设置模型保存路径

4. **文件上传配置**
   - 点击"选择CSV文件"上传文件
   - 设置模型保存路径

5. **协议和参数配置**
   - 选择要训练的协议（TCP、UDP、ICMP）
   - 为每个协议选择数据类型
   - 配置训练参数（学习率、批量大小、训练轮数等）

6. **开始训练**
   - 选择训练模式（同步或异步）
   - 点击"开始训练预测"按钮

### API接口

#### 同步多文件训练
```
POST /model_training/train_multi
```

#### 异步多文件训练
```
POST /model_training/train_multi_async
```

#### 请求参数
- `files`: 上传的文件列表（可选）
- `data_sources`: 数据源配置JSON字符串
- `selected_prots`: 选择的协议JSON字符串
- `selected_datatypes`: 选择的数据类型JSON字符串
- 其他训练参数（learning_rate, batch_size, epochs等）

#### 数据源配置格式
```json
[
  {
    "id": "1",
    "type": "local",
    "csvDir": "/path/to/csv/directory",
    "selectedFile": "data1.csv",
    "outputFolder": "/data/output/model1"
  },
  {
    "id": "2", 
    "type": "upload",
    "outputFolder": "/data/output/model2"
  }
]
```

## 结果展示

### 多文件训练结果
- 按数据源分组显示训练结果
- 每个数据源显示其所有协议/数据类型的训练结果
- 支持切换查看不同数据源和协议组合的详细结果

### 异步任务管理
- 支持异步多文件训练
- 可在任务管理器中查看进度和结果
- 训练完成后自动显示结果

## 技术实现

### 后端架构
- `train_multi`: 同步多文件训练接口
- `train_multi_async`: 异步多文件训练接口
- `train_model_multi_background`: 后台多文件训练执行函数
- `train_single_data_source_background`: 单数据源训练辅助函数
- `train_model_background_complete`: 完整训练逻辑函数

### 前端架构
- 新增训练模式选择组件
- 多数据源配置管理
- 动态数据源添加/删除
- 多文件训练结果展示组件

## 兼容性

- **向后兼容**：完全保留原有单文件训练功能
- **API兼容**：原有API接口保持不变
- **数据格式**：训练结果格式与原有格式兼容

## 注意事项

1. **路径唯一性**：确保每个数据源的输出路径不重复
2. **文件格式**：所有CSV文件必须包含必要的列（timestamp, packetssam, protocol等）
3. **资源管理**：多文件训练会消耗更多系统资源，建议使用异步模式
4. **错误隔离**：单个数据源训练失败不会影响其他数据源的训练

## 示例场景

### 场景1：批量训练不同客户的数据
```
数据源1: /data/customer_a/traffic.csv -> /models/customer_a/
数据源2: /data/customer_b/traffic.csv -> /models/customer_b/
数据源3: /data/customer_c/traffic.csv -> /models/customer_c/
```

### 场景2：训练不同时间段的数据
```
数据源1: /data/2024_q1/traffic.csv -> /models/2024_q1/
数据源2: /data/2024_q2/traffic.csv -> /models/2024_q2/
数据源3: /data/2024_q3/traffic.csv -> /models/2024_q3/
```

### 场景3：混合本地和上传文件
```
数据源1: 本地文件 /data/historical.csv -> /models/historical/
数据源2: 上传文件 new_data.csv -> /models/new_data/
```
