#!/usr/bin/env python3
"""
分析异步训练和预测结果的数据结构
"""

import json
from datetime import datetime, timed<PERSON>ta

def analyze_current_structure():
    """分析当前的存储结构"""
    print("🔍 分析当前异步任务结果存储结构")
    print("=" * 60)
    
    # 检查现有文件
    try:
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"📊 当前存储状态:")
        print(f"   任务数量: {len(tasks)}")
        print(f"   结果数量: {len(results)}")
        
        # 分析任务类型
        task_types = {}
        for task in tasks.values():
            task_type = task.get('task_type', 'unknown')
            task_types[task_type] = task_types.get(task_type, 0) + 1
        
        print(f"\n📋 任务类型分布:")
        for task_type, count in task_types.items():
            print(f"   {task_type}: {count}个")
        
        # 分析结果结构
        print(f"\n📊 结果数据结构:")
        for task_id, result in results.items():
            task_type = tasks.get(task_id, {}).get('task_type', 'unknown')
            print(f"   {task_id[:20]}... ({task_type})")
            print(f"     字段: {list(result.keys())}")
            if 'results' in result:
                print(f"     训练结果: {list(result['results'].keys())}")
            if 'predictions' in result:
                print(f"     预测数据点: {len(result['predictions'])}")
        
        return tasks, results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return {}, {}

def create_sample_data_structures():
    """创建示例数据结构"""
    print(f"\n📋 异步任务结果数据结构对比:")
    print("=" * 60)
    
    base_time = datetime.now()
    
    # 训练任务结果结构
    training_result = {
        "results": {
            "TCP_spt_sip_dip": {
                "r2_score": 0.8567,
                "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_model_best.pth",
                "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_params.json",
                "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_scaler_y_best.pkl",
                "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_test.csv",
                "predictions": [1.23, 2.45, 3.67, 4.89, 5.12],
                "train_shape": [800, 5],
                "test_shape": [200, 5]
            },
            "UDP_spt_sip_dip": {
                "r2_score": 0.7891,
                "model_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_model_best.pth",
                "predictions": [3.45, 4.56, 5.67, 6.78, 7.89],
                "train_shape": [750, 5],
                "test_shape": [180, 5]
            }
        },
        "duration_seconds": 420.5,
        "cpu_percent": 28.7,
        "memory_mb": 1024.3,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    # 预测任务结果结构
    prediction_result = {
        "predictions": [
            {
                "timestamp": "2025-07-24 10:00:00",
                "packets_per_sec": 1500,
                "packets_per_sec_smooth": 1480,
                "pred_smooth": 1520,
                "threshold": 2000,
                "is_anomaly": False
            },
            {
                "timestamp": "2025-07-24 10:01:00",
                "packets_per_sec": 2800,
                "packets_per_sec_smooth": 2750,
                "pred_smooth": 1530,
                "threshold": 2000,
                "is_anomaly": True
            }
        ],
        "anomaly_count": 2,
        "suggested_threshold": 2000,
        "model_name": "TCP_spt_sip_dip",
        "message": "预测成功",
        "duration_seconds": 180.5,
        "cpu_percent": 15.6,
        "memory_mb": 256.4,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    print(f"🎯 训练任务结果结构:")
    print(f"   顶级字段: {list(training_result.keys())}")
    print(f"   results 字段: {list(training_result['results'].keys())}")
    print(f"   单个模型字段: {list(training_result['results']['TCP_spt_sip_dip'].keys())}")
    print(f"   性能指标: duration_seconds, cpu_percent, memory_mb, gpu_*")
    
    print(f"\n🎯 预测任务结果结构:")
    print(f"   顶级字段: {list(prediction_result.keys())}")
    print(f"   predictions 数组长度: {len(prediction_result['predictions'])}")
    print(f"   单个预测字段: {list(prediction_result['predictions'][0].keys())}")
    print(f"   性能指标: duration_seconds, cpu_percent, memory_mb, gpu_*")
    
    return training_result, prediction_result

def analyze_compatibility():
    """分析兼容性"""
    print(f"\n🔍 数据结构兼容性分析:")
    print("=" * 60)
    
    print(f"✅ 相同字段:")
    print(f"   - duration_seconds: 执行时长")
    print(f"   - cpu_percent: CPU使用率")
    print(f"   - memory_mb: 内存使用量")
    print(f"   - gpu_memory_mb: GPU内存使用量")
    print(f"   - gpu_utilization_percent: GPU利用率")
    
    print(f"\n🎯 不同字段:")
    print(f"   训练任务特有:")
    print(f"     - results: 包含多个模型的训练结果")
    print(f"     - 每个模型: r2_score, model_path, predictions等")
    print(f"   预测任务特有:")
    print(f"     - predictions: 预测数据点数组")
    print(f"     - anomaly_count: 异常数量")
    print(f"     - suggested_threshold: 建议阈值")
    print(f"     - model_name: 使用的模型名称")
    print(f"     - message: 预测消息")
    
    print(f"\n📊 存储策略分析:")
    print(f"   方案1: 统一JSON文件 (当前方案)")
    print(f"     优点: 简单统一，易于管理")
    print(f"     缺点: 不同类型数据混合")
    print(f"   方案2: 分离JSON文件")
    print(f"     优点: 数据类型清晰分离")
    print(f"     缺点: 需要管理多个文件")

def demonstrate_unified_storage():
    """演示统一存储方案"""
    print(f"\n💡 统一存储方案演示:")
    print("=" * 60)
    
    # 创建统一的示例数据
    unified_results = {
        # 训练任务结果
        "training_12345678-1234-5678-9abc-123456789abc": {
            "task_type": "training",  # 添加任务类型标识
            "results": {
                "TCP_spt_sip_dip": {
                    "r2_score": 0.8567,
                    "model_path": "/data/output/model.pth",
                    "predictions": [1.23, 2.45, 3.67]
                }
            },
            "duration_seconds": 420.5,
            "cpu_percent": 28.7,
            "memory_mb": 1024.3
        },
        
        # 预测任务结果
        "prediction_87654321-4321-8765-cba9-987654321cba": {
            "task_type": "prediction",  # 添加任务类型标识
            "predictions": [
                {
                    "timestamp": "2025-07-24 10:00:00",
                    "packets_per_sec": 1500,
                    "is_anomaly": False
                }
            ],
            "anomaly_count": 2,
            "suggested_threshold": 2000,
            "model_name": "TCP_spt_sip_dip",
            "duration_seconds": 180.5,
            "cpu_percent": 15.6,
            "memory_mb": 256.4
        }
    }
    
    print(f"📋 统一存储结构:")
    print(f"   文件: task_results.json")
    print(f"   格式: {{task_id: {{task_type, ...result_data}}}}")
    print(f"   训练结果: 包含 results 字段")
    print(f"   预测结果: 包含 predictions 字段")
    print(f"   共同字段: duration_seconds, cpu_percent, memory_mb等")
    
    print(f"\n🔧 前端处理逻辑:")
    print(f"   1. 获取所有任务结果")
    print(f"   2. 根据 task_type 或字段存在性判断类型")
    print(f"   3. 训练页面: 过滤有 results 字段的任务")
    print(f"   4. 预测页面: 过滤有 predictions 字段的任务")
    
    return unified_results

def provide_implementation_plan():
    """提供实现方案"""
    print(f"\n🎯 实现方案:")
    print("=" * 60)
    
    print(f"✅ 推荐方案: 统一JSON存储")
    print(f"   理由:")
    print(f"     1. 当前已经是统一存储")
    print(f"     2. 数据结构兼容性好")
    print(f"     3. 管理简单")
    print(f"     4. 前端处理逻辑清晰")
    
    print(f"\n🔧 优化建议:")
    print(f"   1. 添加 task_type 字段到结果数据中")
    print(f"   2. 前端根据字段存在性自动识别类型")
    print(f"   3. 保持现有的文件结构")
    print(f"   4. 增强错误处理和数据验证")
    
    print(f"\n📋 前端识别逻辑:")
    print(f"   if (result.results) {{ // 训练任务结果")
    print(f"   }} else if (result.predictions) {{ // 预测任务结果")
    print(f"   }} else {{ // 未知类型")
    
    print(f"\n🚀 实施步骤:")
    print(f"   1. ✅ 当前已经统一存储")
    print(f"   2. ✅ 前端已经正确处理不同类型")
    print(f"   3. 可选: 添加 task_type 字段")
    print(f"   4. 可选: 增强数据验证")

if __name__ == "__main__":
    print("🔍 异步任务结果存储结构分析")
    print("=" * 60)
    
    # 分析当前结构
    tasks, results = analyze_current_structure()
    
    # 创建示例数据结构
    training_result, prediction_result = create_sample_data_structures()
    
    # 分析兼容性
    analyze_compatibility()
    
    # 演示统一存储
    unified_results = demonstrate_unified_storage()
    
    # 提供实现方案
    provide_implementation_plan()
    
    print(f"\n" + "=" * 60)
    print("✅ 分析完成")
    print(f"\n🎯 结论:")
    print("异步训练和预测结果 **已经** 存储在同一个JSON文件中！")
    print("当前的 task_results.json 文件可以同时存储:")
    print("  ✅ 训练任务结果 (包含 results 字段)")
    print("  ✅ 预测任务结果 (包含 predictions 字段)")
    print("  ✅ 共同的性能指标字段")
    print(f"\n💡 无需修改:")
    print("当前的存储结构已经是最优方案，支持两种类型的任务结果！")
