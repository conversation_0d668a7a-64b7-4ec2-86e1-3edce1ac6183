{"C20230330-00031_88310c06-2211-4d03-9d30-61da9f40fc20": {"task_id": "C20230330-00031_88310c06-2211-4d03-9d30-61da9f40fc20", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T03:09:34.016664", "started_at": "2025-07-29T03:09:34.019784", "completed_at": "2025-07-29T03:09:34.053586", "params": {"csv_filename": "C20230330-000318_2025-06", "selected_prots": ["TCP"], "selected_datatypes": {"TCP": ["spt"]}, "learning_rate": 0.001, "batch_size": 32, "epochs": 2, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 TCP/spt (1/1)", "total_steps": null, "updated_at": "2025-07-29T03:09:34.053596"}, "C20240702-00079_ca02bf0b-1f2e-4582-9456-74944035dca2": {"task_id": "C20240702-00079_ca02bf0b-1f2e-4582-9456-74944035dca2", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T03:12:23.294977", "started_at": "2025-07-29T03:12:23.297935", "completed_at": "2025-07-29T03:14:03.305983", "params": {"csv_filename": "C20240702-000792_2025-06", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 30, "hidden_size": 64, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 ICMP/dip (7/7)", "total_steps": null, "updated_at": "2025-07-29T03:14:03.305997"}, "C20240702-00079_eafbc1cf-8ae0-4d51-994c-6f7d2fa0d248": {"task_id": "C20240702-00079_eafbc1cf-8ae0-4d51-994c-6f7d2fa0d248", "task_type": "training", "status": "failed", "progress": 10, "created_at": "2025-07-29T03:19:16.633970", "started_at": "2025-07-29T03:19:16.638340", "completed_at": "2025-07-29T03:19:16.661081", "params": {"csv_filename": "C20240702-00079_2025-06", "selected_prots": ["UDP"], "selected_datatypes": {"UDP": ["dpt_sip_dip"]}, "learning_rate": 0.001, "batch_size": 32, "epochs": 2, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": "[Errno 2] No such file or directory: '/home/<USER>/C20240702-00079_2025-06.csv'", "current_step": "读取和预处理数据", "total_steps": null, "updated_at": "2025-07-29T03:19:16.661101"}, "C20230330-00031_f45ba2af-8f19-4bd2-835c-52d4fa84cab4": {"task_id": "C20230330-00031_f45ba2af-8f19-4bd2-835c-52d4fa84cab4", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T03:20:41.803246", "started_at": "2025-07-29T03:20:41.806452", "completed_at": "2025-07-29T03:20:42.001079", "params": {"csv_filename": "C20230330-000318_2025-06", "selected_prots": ["TCP"], "selected_datatypes": {"TCP": ["spt"]}, "learning_rate": 0.001, "batch_size": 32, "epochs": 3, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 TCP/spt (1/1)", "total_steps": null, "updated_at": "2025-07-29T03:20:42.001089"}, "C20240702-00079_a192f9be-ea4a-47b8-b383-933bebdd7ace": {"task_id": "C20240702-00079_a192f9be-ea4a-47b8-b383-933bebdd7ace", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T03:26:25.698135", "started_at": "2025-07-29T03:26:25.701334", "completed_at": "2025-07-29T03:31:18.473131", "params": {"csv_filename": "C20240702-000792_2025-06", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 30, "hidden_size": 64, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 ICMP/dip (7/7)", "total_steps": null, "updated_at": "2025-07-29T03:31:18.473147"}, "C20240702-00079_0c5fa109-ea47-4ea2-a670-e12bcf1b1964": {"task_id": "C20240702-00079_0c5fa109-ea47-4ea2-a670-e12bcf1b1964", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T03:43:44.588948", "started_at": "2025-07-29T03:43:44.600075", "completed_at": "2025-07-29T04:57:09.655886", "params": {"csv_filename": "C20240702-000792_2025-06", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 30, "hidden_size": 64, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 ICMP/dip (7/7)", "total_steps": null, "updated_at": "2025-07-29T04:57:09.655917"}, "C20240702-00079_aef961ac-c7e7-4bc9-ba4e-5019cd965ea7": {"task_id": "C20240702-00079_aef961ac-c7e7-4bc9-ba4e-5019cd965ea7", "task_type": "training", "status": "cancelled", "progress": 21, "created_at": "2025-07-29T04:59:45.193281", "started_at": "2025-07-29T04:59:45.203100", "completed_at": "2025-07-29T05:04:43.466464", "params": {"csv_filename": "C20240702-000792_2025-06", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 30, "hidden_size": 64, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 TCP/spt_sip_dip (1/7)", "total_steps": null, "updated_at": "2025-07-29T05:04:43.466477"}, "C20240702-00079_309fe79b-b84e-441a-881e-3603f2edf028": {"task_id": "C20240702-00079_309fe79b-b84e-441a-881e-3603f2edf028", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-07-29T05:04:31.463139", "started_at": "2025-07-29T05:04:31.473935", "completed_at": "2025-07-29T10:33:06.491109", "params": {"csv_filename": "C20240702-000792_2025-06", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 30, "hidden_size": 64, "num_layers": 2, "dropout": 0.2, "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "训练 ICMP/dip (7/7)", "total_steps": null, "updated_at": "2025-07-29T10:33:06.491169"}, "77ac548e-f38a-42a5-888b-bff0476e9550": {"task_id": "77ac548e-f38a-42a5-888b-bff0476e9550", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:00:15.046638", "started_at": "2025-07-29T23:00:15.049505", "completed_at": "2025-07-29T23:00:58.250880", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20241227-001035", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:00:58.250930"}, "b6e6d766-160a-41c2-85ef-c60297144cf3": {"task_id": "b6e6d766-160a-41c2-85ef-c60297144cf3", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:02:42.456654", "started_at": "2025-07-29T23:02:42.458525", "completed_at": "2025-07-29T23:03:21.422664", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20241227-001035", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:03:21.422701"}, "a1751d52-2f4b-42a9-a99f-b2e9ced9dc52": {"task_id": "a1751d52-2f4b-42a9-a99f-b2e9ced9dc52", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:05:42.272576", "started_at": "2025-07-29T23:05:42.275642", "completed_at": "2025-07-29T23:05:42.289799", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/tmp/batch_test_input", "output_dir": "/tmp/batch_test_output"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:05:42.289810"}, "191f509d-2644-4c5b-9a4c-9b499e65d930": {"task_id": "191f509d-2644-4c5b-9a4c-9b499e65d930", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:10:41.588688", "started_at": "2025-07-29T23:10:41.591003", "completed_at": "2025-07-29T23:10:56.685241", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:10:56.685267"}, "3ef33832-c247-4380-8ef7-e77680885f53": {"task_id": "3ef33832-c247-4380-8ef7-e77680885f53", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:14:56.185189", "started_at": "2025-07-29T23:14:56.187462", "completed_at": "2025-07-29T23:15:10.089494", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:15:10.089525"}, "2757b8e4-79d1-4440-8313-3fec4a023fd2": {"task_id": "2757b8e4-79d1-4440-8313-3fec4a023fd2", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:17:10.856936", "started_at": "2025-07-29T23:17:10.858632", "completed_at": "2025-07-29T23:17:24.401571", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:17:24.401598"}, "c253d519-ffbe-432a-b625-1b2e789e7ffa": {"task_id": "c253d519-ffbe-432a-b625-1b2e789e7ffa", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:21:22.289103", "started_at": "2025-07-29T23:21:22.291186", "completed_at": "2025-07-29T23:21:35.790988", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:21:35.791013"}, "cbcb6872-60f5-42ea-a388-774756b03ab0": {"task_id": "cbcb6872-60f5-42ea-a388-774756b03ab0", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:33:21.546145", "started_at": "2025-07-29T23:33:21.548316", "completed_at": "2025-07-29T23:33:35.424287", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:33:35.424312"}, "eeb01491-668d-4a85-8979-25028fa01a06": {"task_id": "eeb01491-668d-4a85-8979-25028fa01a06", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:35:44.936116", "started_at": "2025-07-29T23:35:44.938095", "completed_at": "2025-07-29T23:35:58.741123", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:35:58.741160"}, "62c763a8-5ece-4298-9987-039de974b3ef": {"task_id": "62c763a8-5ece-4298-9987-039de974b3ef", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:37:53.954602", "started_at": "2025-07-29T23:37:53.956620", "completed_at": "2025-07-29T23:40:07.397678", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:40:07.399872"}, "0b639d91-9123-4309-9cfe-a9d1d58fcbc1": {"task_id": "0b639d91-9123-4309-9cfe-a9d1d58fcbc1", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:48:04.942457", "started_at": "2025-07-29T23:48:04.946118", "completed_at": "2025-07-29T23:50:22.361327", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:50:22.361361"}, "ff80aec7-f84e-4563-9515-16f156804048": {"task_id": "ff80aec7-f84e-4563-9515-16f156804048", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-29T23:53:48.532885", "started_at": "2025-07-29T23:53:48.535194", "completed_at": "2025-07-29T23:56:18.322245", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result/C20230330-000318", "output_dir": "/data"}, {"customer": "任务2", "input_dir": "/home/<USER>/result/C20240528-000695", "output_dir": "/data"}, {"customer": "任务3", "input_dir": "/home/<USER>/result/C20240530-000713", "output_dir": "/data"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-29T23:56:18.322283"}, "C20230330-00031_c6499f24-6e6b-4ba0-add6-bb5ab5a699ab": {"task_id": "C20230330-00031_c6499f24-6e6b-4ba0-add6-bb5ab5a699ab", "task_type": "prediction", "status": "completed", "progress": 100, "created_at": "2025-07-30T03:16:43.448015", "started_at": "2025-07-30T03:16:43.450496", "completed_at": "2025-07-30T03:16:46.716413", "params": {"model_filename": "C20230330-000318_2025-06_ICMP_dip_20250724_034419_model_best.pth", "params_filename": "C20230330-000318_2025-06_ICMP_dip_20250724_034419_params.json", "scaler_filename": "C20230330-000318_2025-06_ICMP_dip_20250724_034419_scaler_y_best.pkl", "selected_prot": "ICMP", "selected_datatype": "dip", "csv_filename": "C20230330-000318_2025-06", "model_dir": "/home/<USER>", "output_folder": "/home/<USER>", "user": "admin"}, "error": null, "current_step": "预测完成", "total_steps": null, "updated_at": "2025-07-30T03:16:46.716430"}, "aizhinengqingxi_010715bd-2f23-4ef8-8b38-0a91ea60a67e": {"task_id": "aizhinengqingxi_010715bd-2f23-4ef8-8b38-0a91ea60a67e", "task_type": "training", "status": "completed", "progress": 95, "created_at": "2025-07-30T04:06:45.014022", "started_at": "2025-07-30T04:06:45.017429", "completed_at": "2025-07-30T05:28:48.421991", "params": {"csv_filename": "aizhinengqingxicepingdaliu_2025-07", "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "output_folder": "/data/730", "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "生成清洗模板", "total_steps": null, "updated_at": "2025-07-30T05:28:48.422001"}, "aizhinengqingxi_4e3043ed-efd7-4b54-96b5-fc2aafb5dd6f": {"task_id": "aizhinengqingxi_4e3043ed-efd7-4b54-96b5-fc2aafb5dd6f", "task_type": "prediction", "status": "completed", "progress": 90, "created_at": "2025-07-30T06:11:39.991940", "started_at": "2025-07-30T06:11:39.994859", "completed_at": "2025-07-30T06:11:41.719467", "params": {"model_filename": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_filename": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_filename": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "selected_prot": "ICMP", "selected_datatype": "dip", "csv_filename": "aizhinengqingxicepingdaliu_2025-07", "model_dir": "/data/730", "output_folder": "/data/730", "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "生成清洗模板", "total_steps": null, "updated_at": "2025-07-30T06:11:41.719479"}, "b400bee7-1e40-4ed8-baa1-60f108e9138b": {"task_id": "b400bee7-1e40-4ed8-baa1-60f108e9138b", "task_type": "data_cleaning", "status": "completed", "progress": 100, "created_at": "2025-07-30T07:43:04.467245", "started_at": "2025-07-30T07:43:04.471255", "completed_at": "2025-07-30T07:43:09.110659", "params": {"batch_tasks": [{"customer": "任务1", "input_dir": "/home/<USER>/result", "output_dir": "/data/730"}], "user": "admin"}, "error": null, "current_step": "批量分析完成", "total_steps": null, "updated_at": "2025-07-30T07:43:09.110677"}, "multi_1d254543-e4e4-4dd4-af45-1a7b4f69e8f3": {"task_id": "multi_1d254543-e4e4-4dd4-af45-1a7b4f69e8f3", "task_type": "prediction", "status": "completed", "progress": 100, "created_at": "2025-07-30T23:26:44.099809", "started_at": "2025-07-30T23:26:44.102384", "completed_at": "2025-07-30T23:26:48.990621", "params": {"models_config": [{"model_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "protocol": "ICMP", "datatype": "dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_scaler_y_best.pkl", "protocol": "TCP", "datatype": "dpt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_scaler_y_best.pkl", "protocol": "TCP", "datatype": "len_dpt_syn"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_scaler_y_best.pkl", "protocol": "TCP", "datatype": "seq_ack_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_scaler_y_best.pkl", "protocol": "TCP", "datatype": "spt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_scaler_y_best.pkl", "protocol": "UDP", "datatype": "dpt_sip_dip"}], "csv_filename": "aizhinengqingxicepingdaliu_2025-07", "model_dir": "/data/730", "output_folder": "/data/730", "auto_generate_template": true, "save_result_file": true, "user": "admin"}, "error": null, "current_step": "多模型预测完成", "total_steps": null, "updated_at": "2025-07-30T23:26:48.990632"}, "aizhinengqingxicepingdaliu_182030b3-7d02-4057-919f-94583313ac79": {"task_id": "aizhinengqingxicepingdaliu_182030b3-7d02-4057-919f-94583313ac79", "task_type": "prediction", "status": "completed", "progress": 100, "created_at": "2025-07-30T23:57:46.601896", "started_at": "2025-07-30T23:57:46.604943", "completed_at": "2025-07-30T23:57:50.999410", "params": {"models_config": [{"model_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "protocol": "ICMP", "datatype": "dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_scaler_y_best.pkl", "protocol": "TCP", "datatype": "dpt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_scaler_y_best.pkl", "protocol": "TCP", "datatype": "len_dpt_syn"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_scaler_y_best.pkl", "protocol": "TCP", "datatype": "seq_ack_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_scaler_y_best.pkl", "protocol": "TCP", "datatype": "spt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_scaler_y_best.pkl", "protocol": "UDP", "datatype": "dpt_sip_dip"}], "csv_filename": "aizhinengqingxicepingdaliu_2025-07", "model_dir": "/data/730", "output_folder": "/data/730", "auto_generate_template": true, "save_result_file": true, "user": "admin"}, "error": null, "current_step": "多模型预测完成", "total_steps": null, "updated_at": "2025-07-30T23:57:50.999420"}, "aizhinengqingxicepingdaliu_8e639fe1-0ae8-4a5b-ab08-ab42aac4b9fa": {"task_id": "aizhinengqingxicepingdaliu_8e639fe1-0ae8-4a5b-ab08-ab42aac4b9fa", "task_type": "prediction", "status": "completed", "progress": 100, "created_at": "2025-07-31T00:04:52.862343", "started_at": "2025-07-31T00:04:52.866229", "completed_at": "2025-07-31T00:04:58.199845", "params": {"models_config": [{"model_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "protocol": "ICMP", "datatype": "dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_scaler_y_best.pkl", "protocol": "TCP", "datatype": "dpt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_scaler_y_best.pkl", "protocol": "TCP", "datatype": "len_dpt_syn"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_scaler_y_best.pkl", "protocol": "TCP", "datatype": "seq_ack_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_scaler_y_best.pkl", "protocol": "TCP", "datatype": "spt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_scaler_y_best.pkl", "protocol": "UDP", "datatype": "dpt_sip_dip"}], "csv_filename": "aizhinengqingxicepingdaliu_2025-07", "model_dir": "/data/730", "output_folder": "/data/730", "auto_generate_template": true, "save_result_file": true, "user": "admin"}, "error": null, "current_step": "多模型预测完成", "total_steps": null, "updated_at": "2025-07-31T00:04:58.199854"}, "aizhinengqingxicepingdaliu_251db7df-3012-46ea-83f0-c7b841d247b6": {"task_id": "aizhinengqingxicepingdaliu_251db7df-3012-46ea-83f0-c7b841d247b6", "task_type": "prediction", "status": "completed", "progress": 100, "created_at": "2025-07-31T03:33:47.678499", "started_at": "2025-07-31T03:33:47.681754", "completed_at": "2025-07-31T03:33:52.024737", "params": {"models_config": [{"model_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_ICMP_dip_20250730_051533_scaler_y_best.pkl", "protocol": "ICMP", "datatype": "dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_dpt_sip_dip_20250730_041906_scaler_y_best.pkl", "protocol": "TCP", "datatype": "dpt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_len_dpt_syn_20250730_043121_scaler_y_best.pkl", "protocol": "TCP", "datatype": "len_dpt_syn"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_seq_ack_dip_20250730_044322_scaler_y_best.pkl", "protocol": "TCP", "datatype": "seq_ack_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_TCP_spt_sip_dip_20250730_040647_scaler_y_best.pkl", "protocol": "TCP", "datatype": "spt_sip_dip"}, {"model_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_model_best.pth", "params_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_params.json", "scaler_file": "aizhinengqingxicepingdaliu_2025-07_UDP_dpt_sip_dip_20250730_050351_scaler_y_best.pkl", "protocol": "UDP", "datatype": "dpt_sip_dip"}], "csv_filename": "aizhinengqingxicepingdaliu_2025-07", "model_dir": "/data/730", "output_folder": "/data/730", "auto_generate_template": true, "save_result_file": true, "user": "admin"}, "error": null, "current_step": "多模型预测完成", "total_steps": null, "updated_at": "2025-07-31T03:33:52.024746"}, "multi_9d6b1184-84cc-498b-b23f-b0d8f55242c5": {"task_id": "multi_9d6b1184-84cc-498b-b23f-b0d8f55242c5", "task_type": "training", "status": "cancelled", "progress": 45.0, "created_at": "2025-08-01T05:00:04.251534", "started_at": "2025-08-01T05:00:04.268139", "completed_at": "2025-08-01T06:29:27.737581", "params": {"data_sources": [{"id": "1", "type": "local", "outputFolder": "/data/73012", "enabled": true, "availableFiles": ["aizhinengqingxicepingdaliu_2025-07.csv"], "filesLoading": false, "csvDir": "/data/730", "selectedFile": "aizhinengqingxicepingdaliu_2025-07.csv"}, {"id": "1754038765154", "type": "local", "outputFolder": "/data/73013", "enabled": true, "availableFiles": ["C20230330-000318_2025-06.csv", "C20240528-000695_2025-06.csv", "C20240530-000713_2025-06.csv"], "filesLoading": false, "csvDir": "/data", "selectedFile": "C20240530-000713_2025-06.csv"}], "files_content": [], "selected_prots": ["TCP", "UDP", "ICMP"], "selected_datatypes": {"TCP": ["spt_sip_dip", "dpt_sip_dip", "len_dpt_syn", "seq_ack_dip"], "UDP": ["spt_sip_dip", "dpt_sip_dip"], "ICMP": ["dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "正在训练数据源 2/2: 1754038765154", "total_steps": null, "updated_at": "2025-08-01T06:29:27.737600"}, "multi_3c056915-aba2-4ae6-8809-e78d76468e6e": {"task_id": "multi_3c056915-aba2-4ae6-8809-e78d76468e6e", "task_type": "training", "status": "completed", "progress": 95, "created_at": "2025-08-01T06:28:12.954829", "started_at": "2025-08-01T06:28:12.959416", "completed_at": "2025-08-01T06:28:18.381855", "params": {"data_sources": [{"id": "1", "type": "local", "outputFolder": "/data/730233", "enabled": true, "availableFiles": ["C20230330-000318_2025-06.csv", "C20240528-000695_2025-06.csv", "C20240530-000713_2025-06.csv"], "filesLoading": false, "csvDir": "/data/", "selectedFile": "C20240530-000713_2025-06.csv"}], "files_content": [], "selected_prots": ["TCP"], "selected_datatypes": {"TCP": ["spt_sip_dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "多文件训练完成: 成功 1/1", "total_steps": null, "updated_at": "2025-08-01T06:28:18.381885", "result": {"total_sources": 1, "successful_sources": 1, "failed_sources": 0, "results": {"1": {"results": {}, "result_path": "/data/730233/C20240530-000713_2025-06_results.txt"}}}}, "C20240530-000713_6e34d501-a9b4-42f3-97ba-ec6199f0ac9e": {"task_id": "C20240530-000713_6e34d501-a9b4-42f3-97ba-ec6199f0ac9e", "task_type": "training", "status": "completed", "progress": 90, "created_at": "2025-08-01T06:29:02.409634", "started_at": "2025-08-01T06:29:02.413928", "completed_at": "2025-08-01T06:29:30.205492", "params": {"csv_filename": "C20240530-000713_2025-06", "selected_prots": ["TCP"], "selected_datatypes": {"TCP": ["spt_sip_dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "output_folder": "/data/555", "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "训练 TCP/spt_sip_dip (1/1)", "total_steps": null, "updated_at": "2025-08-01T06:29:30.205512"}, "multi_03dc42e1-d7f6-47c8-a002-a833dacc0985": {"task_id": "multi_03dc42e1-d7f6-47c8-a002-a833dacc0985", "task_type": "training", "status": "completed", "progress": 95, "created_at": "2025-08-01T06:49:36.510961", "started_at": "2025-08-01T06:49:36.523696", "completed_at": "2025-08-01T07:03:23.463297", "params": {"data_sources": [{"id": "1", "type": "local", "outputFolder": "/data/123456", "enabled": true, "availableFiles": ["C20230330-000318_2025-06.csv", "C20240528-000695_2025-06.csv", "C20240530-000713_2025-06.csv"], "filesLoading": false, "csvDir": "/data/", "selectedFile": "C20240530-000713_2025-06.csv"}], "files_content": [], "selected_prots": ["TCP"], "selected_datatypes": {"TCP": ["spt_sip_dip"]}, "learning_rate": 0.0001, "batch_size": 64, "epochs": 100, "sequence_length": 10, "hidden_size": 50, "num_layers": 2, "dropout": 0.2, "auto_generate_template": true, "user": "admin"}, "error": null, "current_step": "多文件训练完成: 成功 1/1", "total_steps": null, "updated_at": "2025-08-01T07:03:23.463311", "result": {"total_sources": 1, "successful_sources": 1, "failed_sources": 0, "results": {"1": {"results": {"TCP_spt_sip_dip": {"train_shape": [5636, 5], "test_shape": [1409, 5], "train_losses": [0.006071063957508953, 0.005875205921862896, 0.005744899339417274, 0.005557247983484432, 0.005400617335302134, 0.005348390508202258, 0.005173900388127685, 0.0050305821069773155, 0.004862056214806634, 0.00476371662372766, 0.004638417523579094, 0.004579424524235529, 0.004504469680284075, 0.0044678956565939575, 0.004486097252376421, 0.004459295099354938, 0.004466958730026609, 0.004405697695937693, 0.004430890114758506, 0.0043967407820318105, 0.004411476211964913, 0.004405502836760561, 0.004436629772823219, 0.004381400813353944, 0.004372650626966537, 0.004318746839162904, 0.004381732147417149, 0.004352539749655487, 0.004408111144459389, 0.004392600285783196, 0.004292098171714608, 0.004290852562561846, 0.004342316184192896, 0.004314368958692013, 0.004302215202104022, 0.004339199965811688, 0.00436443488124287, 0.004315656822592277, 0.004277618867532489, 0.004298421570324692, 0.004324459963231936, 0.0042996261895757336, 0.004319769929125691, 0.00431838364878284, 0.00432185041544231, 0.004339978297578235, 0.004348595334275829, 0.004276922411531553, 0.004308101484597522, 0.004310823107558441, 0.0043214003250388235, 0.0043555408456609, 0.004314877485572735, 0.004333273518359524, 0.004333700193895091, 0.0043288557060951385, 0.004319533998422304, 0.004290484436573568, 0.004331268591440989, 0.004340701497323148, 0.00432676975795967, 0.004303073765437022, 0.004300674034855275, 0.004305431912332003, 0.004314923525156303, 0.004349109921443822, 0.004298916841916012, 0.00429609889464303, 0.004275629034295314, 0.0043466378875804705, 0.004347486508442838, 0.004260145584736876, 0.00430473109677798, 0.00433407074352994, 0.004355835292793425, 0.00427843818585549, 0.004315403945430921, 0.004293504446723509, 0.004268843243059157, 0.00433239247348834, 0.0043620460324829725, 0.004289384170611999, 0.0042967088413478315, 0.004278794709116691, 0.004307430806896253, 0.004270143276374872, 0.004333536843126961, 0.004311075489620154, 0.004285036581541658, 0.004306473312283941, 0.00430373231620774, 0.0043472716653045135, 0.004271465619783795, 0.004314233605808781, 0.004282208030438586, 0.0043123248053297146, 0.00432578028626633, 0.004289585088738294, 0.0042960296007644, 0.004303059246054524], "val_losses": [0.00572205288335681, 0.005650950130075216, 0.005552368704229593, 0.005465712398290634, 0.005383866373449564, 0.005258176010102034, 0.005135166924446821, 0.0050303819589316845, 0.004879946354776621, 0.0047607445158064365, 0.004670415539294481, 0.004669510293751955, 0.004615789745002985, 0.004639632999897003, 0.004586776718497276, 0.00459122471511364, 0.004568150267004967, 0.004565122537314892, 0.004586437717080116, 0.00457553518936038, 0.004558885935693979, 0.0045661842450499535, 0.0045697446912527084, 0.004549503792077303, 0.004533699713647366, 0.004559572320431471, 0.004531019367277622, 0.004521234426647425, 0.004518277011811733, 0.0045232269912958145, 0.004513596184551716, 0.004513483960181475, 0.004518499597907066, 0.0045141070149838924, 0.004514956381171942, 0.0045156623236835, 0.004518001340329647, 0.0045152632519602776, 0.004515274427831173, 0.004514631815254688, 0.004514771047979593, 0.004513232037425041, 0.004516072571277618, 0.004513252060860395, 0.004514459054917097, 0.004512846004217863, 0.004512287676334381, 0.004513009451329708, 0.004511897452175617, 0.0045138169080019, 0.00451137637719512, 0.004511335399001837, 0.004511021543294191, 0.004510923288762569, 0.00451250234618783, 0.004511201288551092, 0.00451055308803916, 0.004509927239269018, 0.004510554950684309, 0.004509684629738331, 0.004509715363383293, 0.004509554710239172, 0.004509511403739452, 0.00450947554782033, 0.004509473219513893, 0.004509482067078352, 0.004509239923208952, 0.0045092664659023285, 0.004509266000241041, 0.004509211517870426, 0.004509135615080595, 0.0045090410858392715, 0.004509082064032555, 0.00450905179604888, 0.0045090182684361935, 0.004509087186306715, 0.004509009886533022, 0.004509015008807182, 0.004508924670517445, 0.004508962854743004, 0.004509001970291138, 0.004508971236646175, 0.004508921876549721, 0.00450888741761446, 0.004508885554969311, 0.004508872516453266, 0.0045089335180819035, 0.004508912097662687, 0.00450884411111474, 0.0045088366605341434, 0.004508831538259983, 0.004508837591856718, 0.004508823622018099, 0.004508822690695524, 0.004508822225034237, 0.004508819431066513, 0.004508822690695524, 0.0045088171027600765, 0.004508811514824629, 0.004508808720856905], "r2": 0.9333946777481276, "y_test_actual": [1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1108, 1270, 1448, 1577, 1577, 1577, 1415, 1237, 1108, 1108, 1108, 937, 1265, 2177, 3338, 3500, 3500, 3067, 2167, 800, 644, 822, 932, 927, 1132, 1121, 1094, 1066, 1039, 1017, 1000, 833, 850, 833, 717, 590, 620, 457, 317, 267, 560, 747, 1043, 1250, 1333, 1097, 990, 797, 717, 783, 803, 893, 907, 907, 893, 913, 850, 850, 850, 850, 850, 850, 863, 1310, 1690, 1690, 1690, 1677, 1230, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 913, 1110, 1290, 1187, 1187, 1123, 927, 747, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 850, 780, 827, 1207, 1053, 937, 927, 837, 450, 633, 930, 973, 995, 972, 910, 699, 705, 697, 697, 700, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 645, 805, 931, 1040, 1367, 1367, 1207, 1081, 971, 645, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 645, 971, 1047, 940, 940, 997, 671, 595, 702, 702, 702, 702, 702, 702, 702, 702, 702, 702, 628, 571, 731, 607, 583, 733, 1367, 1450, 1500, 1483, 1358, 737, 452, 482, 480, 455, 392, 391, 390, 389, 418, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 568, 822, 909, 909, 909, 822, 568, 481, 481, 481, 585, 672, 642, 642, 642, 539, 451, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 468, 572, 642, 680, 667, 617, 650, 783, 917, 983, 1050, 833, 675, 542, 529, 571, 700, 702, 711, 716, 716, 713, 711, 652, 1743, 3168, 3492, 3492, 3552, 2461, 1036, 712, 712, 712, 712, 712, 653, 761, 935, 809, 733, 767, 683, 583, 833, 883, 1067, 1092, 1054, 960, 1020, 897, 884, 884, 889, 892, 893, 893, 893, 893, 893, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 881, 969, 1174, 1062, 1062, 1074, 985, 781, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 931, 1169, 1357, 1357, 1357, 1319, 1081, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 814, 902, 990, 845, 845, 924, 835, 747, 892, 892, 892, 892, 892, 892, 892, 892, 892, 892, 797, 1069, 1374, 1570, 1658, 1733, 1333, 1083, 825, 958, 1117, 1250, 1186, 1225, 967, 778, 708, 639, 617, 525, 464, 433, 433, 717, 983, 1044, 1089, 1117, 828, 639, 639, 639, 694, 1450, 2106, 2106, 2106, 2050, 1294, 639, 639, 639, 639, 639, 639, 639, 639, 639, 639, 528, 1017, 1256, 1411, 1411, 1522, 1033, 794, 639, 639, 639, 639, 639, 544, 800, 1406, 1444, 1444, 1539, 1303, 736, 761, 837, 920, 986, 1032, 1057, 1619, 1726, 1945, 2283, 2433, 1933, 1908, 1767, 1508, 1433, 1458, 1458, 1458, 1458, 1458, 1458, 1458, 1333, 1308, 1133, 1158, 1083, 1033, 1046, 1202, 1155, 1207, 1358, 1346, 1341, 1340, 1340, 1340, 1340, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1089, 1188, 1420, 1501, 1633, 2017, 1667, 1476, 1455, 1395, 1340, 1671, 1708, 1724, 1729, 1729, 1727, 1726, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1725, 1980, 2868, 2973, 3056, 3117, 2900, 2028, 1917, 1806, 1617, 1467, 1422, 1250, 950, 967, 958, 867, 925, 1133, 1125, 1050, 1008, 1033, 925, 775, 658, 958, 1092, 1042], "y_pred": [1200, 1171, 1148, 1131, 1118, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1109, 1320, 1503, 1591, 1524, 1504, 1311, 1148, 1060, 1123, 1148, 932, 1432, 2666, 3830, 3337, 3042, 2500, 1691, 565, 607, 993, 1153, 1076, 1323, 1182, 1092, 1038, 1004, 985, 982, 793, 863, 853, 711, 578, 656, 446, 304, 276, 776, 915, 1191, 1259, 1208, 887, 822, 670, 655, 802, 846, 963, 942, 917, 885, 907, 823, 840, 848, 854, 856, 856, 872, 1483, 1818, 1616, 1545, 1515, 1036, 723, 846, 900, 918, 917, 904, 887, 873, 863, 855, 850, 850, 850, 932, 1173, 1335, 1123, 1125, 1054, 847, 692, 896, 892, 889, 881, 871, 862, 856, 853, 850, 849, 850, 850, 850, 850, 850, 762, 841, 1358, 1019, 874, 889, 797, 380, 713, 1125, 1049, 1006, 938, 851, 621, 689, 704, 714, 726, 726, 720, 713, 708, 705, 702, 702, 702, 702, 702, 702, 702, 702, 702, 630, 858, 978, 1061, 1426, 1294, 1072, 962, 889, 565, 729, 750, 755, 747, 737, 725, 715, 709, 705, 701, 630, 1094, 1079, 884, 890, 967, 582, 561, 752, 739, 731, 725, 716, 709, 705, 703, 700, 700, 610, 557, 795, 591, 579, 791, 1665, 1465, 1393, 1312, 1171, 566, 377, 510, 538, 510, 423, 428, 423, 414, 447, 514, 484, 474, 470, 469, 471, 474, 476, 478, 480, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 596, 914, 919, 853, 832, 732, 479, 443, 492, 512, 665, 746, 653, 637, 629, 501, 421, 492, 498, 499, 497, 493, 489, 486, 483, 482, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 481, 465, 606, 665, 678, 639, 575, 632, 805, 941, 971, 1016, 737, 601, 498, 536, 617, 794, 742, 727, 714, 705, 697, 697, 627, 2317, 3821, 3321, 2894, 2826, 1783, 698, 602, 764, 828, 845, 824, 710, 853, 1044, 790, 696, 757, 657, 558, 937, 928, 1126, 1075, 994, 881, 986, 842, 865, 885, 906, 909, 908, 903, 898, 895, 894, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 878, 996, 1241, 1023, 1029, 1047, 941, 722, 928, 920, 916, 911, 906, 899, 896, 894, 892, 891, 942, 1248, 1409, 1316, 1285, 1234, 973, 820, 895, 926, 937, 935, 927, 916, 907, 901, 896, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 893, 794, 930, 1027, 810, 836, 947, 817, 724, 945, 910, 896, 890, 887, 885, 887, 889, 889, 890, 773, 1164, 1492, 1606, 1596, 1621, 1138, 947, 742, 1019, 1241, 1371, 1208, 1238, 897, 723, 697, 648, 649, 540, 482, 459, 468, 890, 1139, 1051, 1020, 1010, 683, 543, 616, 653, 745, 1899, 2444, 2001, 1840, 1733, 985, 478, 632, 702, 726, 725, 707, 685, 668, 655, 646, 501, 1234, 1360, 1399, 1286, 1384, 832, 674, 585, 662, 690, 700, 691, 552, 930, 1724, 1447, 1321, 1385, 1103, 571, 729, 887, 1004, 1067, 1086, 1075, 1816, 1722, 1880, 2178, 2243, 1635, 1730, 1641, 1415, 1423, 1521, 1529, 1523, 1509, 1493, 1478, 1470, 1305, 1304, 1101, 1185, 1097, 1053, 1085, 1293, 1173, 1228, 1399, 1328, 1306, 1304, 1309, 1317, 1326, 1333, 1336, 1339, 1342, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1341, 1029, 1220, 1527, 1555, 1667, 2098, 1525, 1347, 1393, 1362, 1331, 1811, 1761, 1729, 1707, 1696, 1689, 1694, 1703, 1711, 1718, 1725, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 1726, 2060, 3191, 2959, 2903, 2900, 2631, 1723, 1818, 1806, 1643, 1517, 1512, 1294, 939, 1037, 1036, 906, 1001, 1267, 1163, 1030, 981, 1026, 890, 737, 638, 1107, 1198], "output_csv_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_predictions.csv", "predictions_filename": "C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_predictions.csv", "weight_avg": 791.8596490346567, "r2_score": 0.9333946777481276, "finished_time": "2025-08-01 07:03:23.334808", "model_save_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_model_best.pth", "params_save_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_params.json", "test_save_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_test.csv", "scaler_y_save_path": "/data/123456/C20240530-000713_2025-06_TCP_spt_sip_dip_20250801_064946_scaler_y_best.pkl", "static_anomaly_threshold": 1199.5590372242457, "duration_seconds": 823.1102826595306, "cpu_percent": 84.0825, "memory_mb": 1272.88671875, "gpu_memory_mb": 0, "gpu_utilization_percent": 0, "model_id": "C20240530-000713_model_20250801_070323"}}, "result_path": "/data/123456/C20240530-000713_2025-06_results.txt", "template_info": {"template_generated": true, "template_path": "/data/123456/C20240530-000713_20250801_cleantemplate.json", "updated_thresholds": 1}}}}}}