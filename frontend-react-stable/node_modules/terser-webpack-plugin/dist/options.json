{"definitions": {"Rule": {"description": "Filtering rule as regex or string.", "anyOf": [{"instanceof": "RegExp", "tsType": "RegExp"}, {"type": "string", "minLength": 1}]}, "Rules": {"description": "Filtering rules.", "anyOf": [{"type": "array", "items": {"description": "A rule condition.", "oneOf": [{"$ref": "#/definitions/Rule"}]}}, {"$ref": "#/definitions/Rule"}]}}, "title": "TerserPluginOptions", "type": "object", "additionalProperties": false, "properties": {"test": {"description": "Include all modules that pass test assertion.", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "include": {"description": "Include all modules matching any of these conditions.", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "exclude": {"description": "Exclude all modules matching any of these conditions.", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "cache": {"description": "Enable file caching. Ignored in webpack 5, for webpack 5 please use https://webpack.js.org/configuration/other-options/#cache.", "anyOf": [{"type": "boolean"}, {"type": "string"}]}, "cacheKeys": {"description": "Allows you to override default cache keys. Ignored in webpack 5, for webpack 5 please use https://webpack.js.org/configuration/other-options/#cache.", "instanceof": "Function"}, "parallel": {"description": "Use multi-process parallel running to improve the build speed.", "anyOf": [{"type": "boolean"}, {"type": "integer"}]}, "sourceMap": {"description": "Enables/Disables generation of source maps.", "type": "boolean"}, "minify": {"description": "Allows you to override default minify function.", "instanceof": "Function"}, "terserOptions": {"description": "Options for `terser`.", "additionalProperties": true, "type": "object"}, "extractComments": {"description": "Whether comments shall be extracted to a separate file.", "anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "RegExp"}, {"instanceof": "Function"}, {"additionalProperties": false, "properties": {"condition": {"anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "RegExp"}, {"instanceof": "Function"}]}, "filename": {"anyOf": [{"type": "string"}, {"instanceof": "Function"}]}, "banner": {"anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "Function"}]}}, "type": "object"}]}}}