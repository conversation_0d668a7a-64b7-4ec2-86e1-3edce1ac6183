{"name": "babel-plugin-jest-hoist", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/babel-plugin-jest-hoist"}, "engines": {"node": ">= 10.14.2"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-react": "^7.12.1", "@types/babel__template": "^7.0.2", "@types/node": "*", "@types/prettier": "^2.0.0", "babel-plugin-tester": "^10.0.0", "prettier": "^2.1.1"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}