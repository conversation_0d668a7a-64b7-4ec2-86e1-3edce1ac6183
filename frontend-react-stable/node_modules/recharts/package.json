{"name": "recharts", "version": "2.5.0", "description": "React charts", "main": "lib/index", "module": "es6/index", "jsnext:main": "es6/index", "types": "types/index.d.ts", "sideEffects": false, "files": ["*.md", "es6", "lib", "umd", "types"], "keywords": ["react", "reactjs", "chart", "react-component"], "scripts": {"prepare": "husky install", "build": "npm run build-types && npm run build-cjs && npm run build-es6 && npm run build-umd", "build-cjs": "rimraf lib && cross-env NODE_ENV=commonjs babel ./src -d lib --extensions '.js,.ts,.tsx'", "build-es6": "rimraf es6 && cross-env NODE_ENV=es6 babel ./src -d es6 --extensions '.js,.ts,.tsx'", "build-umd": "rimraf umd && cross-env NODE_ENV=production webpack --entry ./src/index.ts -o umd", "build-types": "rimraf types && npm run tsc", "demo": "webpack serve --config demo/webpack.config.js --port 3000 --host 127.0.0.1 --progress --profile --static demo/", "test": "cross-env NODE_ENV=test jest", "lint": "eslint \"./src/**/*.{ts,tsx}\"", "lint-storybook": "eslint \"./storybook/**/*.{ts,tsx}\"", "autofix": "eslint \"./src/**/*.{ts,tsx}\" --fix", "analyse": "cross-env NODE_ENV=analyse webpack ./src/index.ts -o umd/Recharts.js", "tsc": "tsc", "storybook": "npx storybook dev -p 6006 -c storybook", "build-storybook": "npx storybook build -o storybook/public -c storybook", "chromatic": "npx chromatic", "test-storybook": "test-storybook --config-dir storybook", "test-storybook:url": "test-storybook --url http://127.0.0.1:9009 --config-dir storybook", "test-storybook:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"npm run build-storybook --quiet && npx http-server storybook/public --port 9009 --silent\" \"wait-on tcp:127.0.0.1:9009 && npm run test-storybook:url --maxWorkers=2\""}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "repository": {"type": "git", "url": "git+https://github.com/recharts/recharts.git"}, "author": {"name": "recharts group"}, "bugs": {"url": "https://github.com/recharts/recharts/issues"}, "homepage": "https://github.com/recharts/recharts", "peerDependencies": {"prop-types": "^15.6.0", "react": "^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0"}, "dependencies": {"classnames": "^2.2.5", "eventemitter3": "^4.0.1", "lodash": "^4.17.19", "react-is": "^16.10.2", "react-resize-detector": "^8.0.4", "react-smooth": "^2.0.2", "recharts-scale": "^0.4.4", "reduce-css-calc": "^2.1.8", "victory-vendor": "^36.6.8"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.20.5", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.6.3", "@babel/preset-react": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@babel/runtime": "^7.6.3", "@storybook/addon-docs": "^7.0.0-beta.47", "@storybook/addon-essentials": "^7.0.0-beta.47", "@storybook/addon-interactions": "^7.0.0-beta.47", "@storybook/addon-links": "^7.0.0-beta.47", "@storybook/jest": "^0.0.11-next.0", "@storybook/react": "^7.0.0-beta.47", "@storybook/react-webpack5": "^7.0.0-beta.47", "@storybook/test-runner": "^0.9.4", "@storybook/testing-library": "^0.0.14-next.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^14.3.0", "@types/classnames": "^2.2.9", "@types/d3-interpolate": "^3.0.1", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-time-format": "^4.0.0", "@types/jest": "^27.5.1", "@types/lodash": "^4.14.144", "@types/node": "^14.18.34", "@types/react": "^16.0.0", "@types/react-dom": "^16.0.0", "@types/react-is": "^17.0.0", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "babel-loader": "^8.0.0", "babel-plugin-lodash": "^3.3.0", "browserslist": "^4.21.0", "chromatic": "^6.15.0", "concurrently": "^7.6.0", "cross-env": "^7.0.3", "d3-scale-chromatic": "^3.0.0", "d3-time": "^3.1.0", "d3-time-format": "^4.1.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.10", "husky": "^8.0.2", "jest": "^27.5.1", "jest-each": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "lint-staged": "^13.1.0", "prettier": "^2.2.1", "react": "^16.14.0", "react-dom": "^16.14.0", "react-router-dom": "^5.1.2", "rimraf": "^3.0.2", "storybook": "^7.0.0-beta.47", "terser-webpack-plugin": "^5.1.1", "ts-jest": "^27.1.5", "ts-loader": "^8.0.12", "typescript": "4.1.3", "update-browserslist-db": "^1.0.10", "webpack": "^5.65.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^4.9.0", "webpack-dev-server": "^4.6.0"}, "engines": {"node": ">=12"}, "license": "MIT"}