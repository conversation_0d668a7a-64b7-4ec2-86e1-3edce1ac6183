{"version": 3, "file": "index.js", "sources": ["src/browser.js"], "sourcesContent": ["const colorIndexRegExp = /((?:not )?all and )?(\\(color-index: *(22|48|70)\\))/i;\nconst prefersColorSchemeRegExp = /prefers-color-scheme:/i;\n\nconst prefersColorSchemeInit = initialColorScheme => {\n\tconst mediaQueryString = '(prefers-color-scheme: dark)';\n\tconst mediaQueryList = window.matchMedia && matchMedia(mediaQueryString);\n\tconst hasNativeSupport = mediaQueryList && mediaQueryList.media === mediaQueryString;\n\tconst mediaQueryListener = () => {\n\t\tset(mediaQueryList.matches ? 'dark' : 'light');\n\t};\n\tconst removeListener = () => {\n\t\tif (mediaQueryList) {\n\t\t\tmediaQueryList.removeListener(mediaQueryListener);\n\t\t}\n\t};\n\tconst set = colorScheme => {\n\t\tif (colorScheme !== currentColorScheme) {\n\t\t\tcurrentColorScheme = colorScheme;\n\n\t\t\tif (typeof result.onChange === 'function') {\n\t\t\t\tresult.onChange();\n\t\t\t}\n\t\t}\n\n\t\t[].forEach.call(document.styleSheets || [], styleSheet => {\n\t\t\t[].forEach.call(styleSheet.cssRules || [], cssRule => {\n\t\t\t\tconst colorSchemeMatch = prefersColorSchemeRegExp.test(Object(cssRule.media).mediaText);\n\n\t\t\t\tif (colorSchemeMatch) {\n\t\t\t\t\tconst index = [].indexOf.call(cssRule.parentStyleSheet.cssRules, cssRule);\n\n\t\t\t\t\tcssRule.parentStyleSheet.deleteRule(index);\n\t\t\t\t} else {\n\t\t\t\t\tconst colorIndexMatch = (Object(cssRule.media).mediaText || '').match(colorIndexRegExp);\n\n\t\t\t\t\tif (colorIndexMatch) {\n\t\t\t\t\t\tcssRule.media.mediaText = (\n\t\t\t\t\t\t\t(/^dark$/i.test(colorScheme)\n\t\t\t\t\t\t\t\t? colorIndexMatch[3] === '48'\n\t\t\t\t\t\t\t: /^light$/i.test(colorScheme)\n\t\t\t\t\t\t\t\t? colorIndexMatch[3] === '70'\n\t\t\t\t\t\t\t: colorIndexMatch[3] === '22')\n\t\t\t\t\t\t\t\t? 'not all and '\n\t\t\t\t\t\t\t: ''\n\t\t\t\t\t\t) + cssRule.media.mediaText.replace(colorIndexRegExp, '$2');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t};\n\tconst result = Object.defineProperty(\n\t\t{ hasNativeSupport, removeListener },\n\t\t'scheme',\n\t\t{ get: () => currentColorScheme, set }\n\t);\n\n\t// initialize the color scheme using the provided value, the system value, or light\n\tlet currentColorScheme = initialColorScheme || (mediaQueryList && mediaQueryList.matches ? 'dark' : 'light');\n\n\tset(currentColorScheme);\n\n\t// listen for system changes\n\tif (mediaQueryList) {\n\t\tmediaQueryList.addListener(mediaQueryListener);\n\t}\n\n\treturn result;\n};\n\nexport default prefersColorSchemeInit;\n"], "names": ["colorIndexRegExp", "prefersColorSchemeRegExp", "prefersColorSchemeInit", "initialColorScheme", "mediaQueryString", "mediaQueryList", "window", "matchMedia", "hasNativeSupport", "media", "mediaQueryListener", "set", "matches", "removeListener", "colorScheme", "currentColorScheme", "result", "onChange", "for<PERSON>ach", "call", "document", "styleSheets", "styleSheet", "cssRules", "cssRule", "colorSchemeMatch", "test", "Object", "mediaText", "index", "indexOf", "parentStyleSheet", "deleteRule", "colorIndexMatch", "match", "replace", "defineProperty", "get", "addListener"], "mappings": ";;AAAA,MAAMA,gBAAgB,GAAG,qDAAzB;AACA,MAAMC,wBAAwB,GAAG,wBAAjC;;AAEA,MAAMC,sBAAsB,GAAGC,kBAAkB,IAAI;QAC9CC,gBAAgB,GAAG,8BAAzB;QACMC,cAAc,GAAGC,MAAM,CAACC,UAAP,IAAqBA,UAAU,CAACH,gBAAD,CAAtD;QACMI,gBAAgB,GAAGH,cAAc,IAAIA,cAAc,CAACI,KAAf,KAAyBL,gBAApE;;QACMM,kBAAkB,GAAG,MAAM;IAChCC,GAAG,CAACN,cAAc,CAACO,OAAf,GAAyB,MAAzB,GAAkC,OAAnC,CAAH;GADD;;QAGMC,cAAc,GAAG,MAAM;QACxBR,cAAJ,EAAoB;MACnBA,cAAc,CAACQ,cAAf,CAA8BH,kBAA9B;;GAFF;;QAKMC,GAAG,GAAGG,WAAW,IAAI;QACtBA,WAAW,KAAKC,kBAApB,EAAwC;MACvCA,kBAAkB,GAAGD,WAArB;;UAEI,OAAOE,MAAM,CAACC,QAAd,KAA2B,UAA/B,EAA2C;QAC1CD,MAAM,CAACC,QAAP;;;;OAICC,OAAH,CAAWC,IAAX,CAAgBC,QAAQ,CAACC,WAAT,IAAwB,EAAxC,EAA4CC,UAAU,IAAI;SACtDJ,OAAH,CAAWC,IAAX,CAAgBG,UAAU,CAACC,QAAX,IAAuB,EAAvC,EAA2CC,OAAO,IAAI;cAC/CC,gBAAgB,GAAGxB,wBAAwB,CAACyB,IAAzB,CAA8BC,MAAM,CAACH,OAAO,CAACf,KAAT,CAAN,CAAsBmB,SAApD,CAAzB;;YAEIH,gBAAJ,EAAsB;gBACfI,KAAK,GAAG,GAAGC,OAAH,CAAWX,IAAX,CAAgBK,OAAO,CAACO,gBAAR,CAAyBR,QAAzC,EAAmDC,OAAnD,CAAd;UAEAA,OAAO,CAACO,gBAAR,CAAyBC,UAAzB,CAAoCH,KAApC;SAHD,MAIO;gBACAI,eAAe,GAAG,CAACN,MAAM,CAACH,OAAO,CAACf,KAAT,CAAN,CAAsBmB,SAAtB,IAAmC,EAApC,EAAwCM,KAAxC,CAA8ClC,gBAA9C,CAAxB;;cAEIiC,eAAJ,EAAqB;YACpBT,OAAO,CAACf,KAAR,CAAcmB,SAAd,GAA0B,CACzB,CAAC,UAAUF,IAAV,CAAeZ,WAAf,IACEmB,eAAe,CAAC,CAAD,CAAf,KAAuB,IADzB,GAEC,WAAWP,IAAX,CAAgBZ,WAAhB,IACCmB,eAAe,CAAC,CAAD,CAAf,KAAuB,IADxB,GAEAA,eAAe,CAAC,CAAD,CAAf,KAAuB,IAJzB,IAKG,cALH,GAME,EAPuB,IAQtBT,OAAO,CAACf,KAAR,CAAcmB,SAAd,CAAwBO,OAAxB,CAAgCnC,gBAAhC,EAAkD,IAAlD,CARJ;;;OAXH;KADD;GATD;;QAmCMgB,MAAM,GAAGW,MAAM,CAACS,cAAP,CACd;IAAE5B,gBAAF;IAAoBK;GADN,EAEd,QAFc,EAGd;IAAEwB,GAAG,EAAE,MAAMtB,kBAAb;IAAiCJ;GAHnB,CAAf,CA/CoD;;MAsDhDI,kBAAkB,GAAGZ,kBAAkB,KAAKE,cAAc,IAAIA,cAAc,CAACO,OAAjC,GAA2C,MAA3C,GAAoD,OAAzD,CAA3C;EAEAD,GAAG,CAACI,kBAAD,CAAH,CAxDoD;;MA2DhDV,cAAJ,EAAoB;IACnBA,cAAc,CAACiC,WAAf,CAA2B5B,kBAA3B;;;SAGMM,MAAP;CA/DD;;;;"}