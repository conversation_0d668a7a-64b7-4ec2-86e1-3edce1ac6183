var initPrefersColorScheme=function(){"use strict";var e=/((?:not )?all and )?(\(color-index: *(22|48|70)\))/i,t=/prefers-color-scheme:/i;return function(a){var r=window.matchMedia&&matchMedia("(prefers-color-scheme: dark)"),n=r&&"(prefers-color-scheme: dark)"===r.media,i=function(){c(r.matches?"dark":"light")},c=function(a){a!==s&&(s=a,"function"==typeof o.onChange&&o.onChange()),[].forEach.call(document.styleSheets||[],function(r){[].forEach.call(r.cssRules||[],function(r){if(t.test(Object(r.media).mediaText)){var n=[].indexOf.call(r.parentStyleSheet.cssRules,r);r.parentStyleSheet.deleteRule(n)}else{var i=(Object(r.media).mediaText||"").match(e);i&&(r.media.mediaText=((/^dark$/i.test(a)?"48"===i[3]:/^light$/i.test(a)?"70"===i[3]:"22"===i[3])?"not all and ":"")+r.media.mediaText.replace(e,"$2"))}})})},o=Object.defineProperty({hasNativeSupport:n,removeListener:function(){r&&r.removeListener(i)}},"scheme",{get:function(){return s},set:c}),s=a||(r&&r.matches?"dark":"light");return c(s),r&&r.addListener(i),o}}();
