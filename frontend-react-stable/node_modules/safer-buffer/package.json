{"name": "safer-buffer", "version": "2.1.2", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"browserify-test": "browserify --external tape tests.js > browserify-tests.js && tape browserify-tests.js", "test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"]}