{"SVGSymbolElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGTests"]}, "TVChannel": {"inherits": "EventTarget", "implements": []}, "SVGPolygonElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPoints"]}, "HTMLDataElement": {"inherits": "HTMLElement", "implements": []}, "MozCdmaIccInfo": {"inherits": "MozIccInfo", "implements": []}, "TreeBoxObject": {"inherits": "BoxObject", "implements": []}, "CSSStyleDeclaration": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "Selection": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "XULElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "TouchEventHandlers", "MozFrameLoaderOwner", "OnErrorEventHandlerForNodes"]}, "XMLHttpRequestUpload": {"inherits": "XMLHttpRequestEventTarget", "implements": ["LegacyQueryInterface"]}, "WindowRoot": {"inherits": "EventTarget", "implements": []}, "BrowserElement": {"inherits": null, "implements": ["Browser<PERSON><PERSON><PERSON><PERSON><PERSON>", "BrowserElementPrivileged"]}, "IDBTransaction": {"inherits": "EventTarget", "implements": []}, "TVTuner": {"inherits": "EventTarget", "implements": []}, "ScrollViewChangeEvent": {"inherits": "Event", "implements": []}, "SVGPathSegCurvetoCubicSmoothAbs": {"inherits": "SVGPathSeg", "implements": []}, "CameraControl": {"inherits": "MediaStream", "implements": []}, "SVGFEMorphologyElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "FetchEvent": {"inherits": "Event", "implements": []}, "DeviceOrientationEvent": {"inherits": "Event", "implements": []}, "HTMLBRElement": {"inherits": "HTMLElement", "implements": []}, "MozWifiConnectionInfoEvent": {"inherits": "Event", "implements": []}, "BroadcastChannel": {"inherits": "EventTarget", "implements": []}, "IDBDatabase": {"inherits": "EventTarget", "implements": []}, "HTMLFormControlsCollection": {"inherits": "HTMLCollection", "implements": []}, "WebSocket": {"inherits": "EventTarget", "implements": ["LegacyQueryInterface"]}, "SVGDescElement": {"inherits": "SVGElement", "implements": []}, "SVGCircleElement": {"inherits": "SVGGeometryElement", "implements": []}, "SVGCursorElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "DOMDownload": {"inherits": "EventTarget", "implements": []}, "SVGPathSegArcAbs": {"inherits": "SVGPathSeg", "implements": []}, "ArchiveRequest": {"inherits": "DOMRequest", "implements": []}, "SharedWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": []}, "Exception": {"inherits": null, "implements": ["ExceptionMembers"]}, "BluetoothAdapterEvent": {"inherits": "Event", "implements": []}, "HTMLParagraphElement": {"inherits": "HTMLElement", "implements": []}, "AudioDestinationNode": {"inherits": "AudioNode", "implements": []}, "ProcessingInstruction": {"inherits": "CharacterData", "implements": ["LegacyQueryInterface"]}, "BeforeUnloadEvent": {"inherits": "Event", "implements": []}, "PseudoElement": {"inherits": null, "implements": ["GeometryUtils"]}, "NetworkInformation": {"inherits": "EventTarget", "implements": []}, "MutationObserver": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "NodeList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "CloseEvent": {"inherits": "Event", "implements": []}, "HTMLAreaElement": {"inherits": "HTMLElement", "implements": ["HTMLHyperlinkElementUtils", "URLUtilsSearchParams"]}, "HTMLLegendElement": {"inherits": "HTMLElement", "implements": []}, "SVGMetadataElement": {"inherits": "SVGElement", "implements": []}, "SVGPathSegList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGForeignObjectElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGPatternElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGURIReference", "SVGUnitTypes"]}, "Performance": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "TouchEventHandlers", "OnErrorEventHandlerForNodes"]}, "HTMLHeadElement": {"inherits": "HTMLElement", "implements": []}, "MozIcc": {"inherits": "EventTarget", "implements": []}, "UDPSocket": {"inherits": "EventTarget", "implements": []}, "DocumentType": {"inherits": "Node", "implements": ["ChildNode", "LegacyQueryInterface"]}, "SVGStopElement": {"inherits": "SVGElement", "implements": []}, "ImageDocument": {"inherits": "HTMLDocument", "implements": []}, "SVGElement": {"inherits": "Element", "implements": ["GlobalEventHandlers", "TouchEventHandlers", "OnErrorEventHandlerForNodes"]}, "GamepadEvent": {"inherits": "Event", "implements": []}, "HTMLTableElement": {"inherits": "HTMLElement", "implements": []}, "PerformanceMark": {"inherits": "PerformanceEntry", "implements": []}, "InstallEvent": {"inherits": "ExtendableEvent", "implements": []}, "FocusEvent": {"inherits": "UIEvent", "implements": []}, "OscillatorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "PluginCrashedEvent": {"inherits": "Event", "implements": []}, "SVGPathSegLinetoVerticalRel": {"inherits": "SVGPathSeg", "implements": []}, "DocumentFragment": {"inherits": "Node", "implements": ["ParentNode", "LegacyQueryInterface"]}, "OfflineAudioCompletionEvent": {"inherits": "Event", "implements": []}, "SVGPoint": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLTitleElement": {"inherits": "HTMLElement", "implements": []}, "Window": {"inherits": null, "implements": ["LegacyQueryInterface", "GlobalEventHandlers", "WindowEventHandlers", "GlobalCrypto", "SpeechSynthesisGetter", "WindowModal", "TouchEventHandlers", "OnErrorEventHandlerForWindow", "ChromeWindow", "WindowOrWorkerGlobalScope"]}, "WindowClient": {"inherits": "Client", "implements": []}, "ErrorEvent": {"inherits": "Event", "implements": []}, "MessageEvent": {"inherits": "Event", "implements": []}, "SVGPathSegCurvetoQuadraticAbs": {"inherits": "SVGPathSeg", "implements": []}, "AudioNode": {"inherits": "EventTarget", "implements": []}, "MediaDevices": {"inherits": "EventTarget", "implements": []}, "CanvasCaptureMediaStream": {"inherits": "MediaStream", "implements": []}, "DynamicsCompressorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "SVGSVGElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGFitToViewBox", "SVGZoomAndPan"]}, "File": {"inherits": "Blob", "implements": []}, "SVGAnimationElement": {"inherits": "SVGElement", "implements": ["SVGTests"]}, "BluetoothPairingEvent": {"inherits": "Event", "implements": []}, "ShadowRoot": {"inherits": "DocumentFragment", "implements": []}, "ExtendableEvent": {"inherits": "Event", "implements": []}, "SVGFEOffsetElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "DesktopNotification": {"inherits": "EventTarget", "implements": []}, "SVGGradientElement": {"inherits": "SVGElement", "implements": ["SVGURIReference", "SVGUnitTypes"]}, "HTMLMarqueeElement": {"inherits": "HTMLElement", "implements": []}, "HTMLMetaElement": {"inherits": "HTMLElement", "implements": []}, "Text": {"inherits": "CharacterData", "implements": ["LegacyQueryInterface", "GeometryUtils"]}, "DOMApplication": {"inherits": "EventTarget", "implements": []}, "InstallTrigger": {"inherits": null, "implements": []}, "MutationEvent": {"inherits": "Event", "implements": []}, "HTMLLabelElement": {"inherits": "HTMLElement", "implements": []}, "SVGGeometryElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGPathElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPathData"]}, "HTMLTemplateElement": {"inherits": "HTMLElement", "implements": []}, "SVGAnimatedInteger": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGFEDisplacementMapElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "SVGPathSegMovetoAbs": {"inherits": "SVGPathSeg", "implements": []}, "XULCommandEvent": {"inherits": "UIEvent", "implements": []}, "Crypto": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "Response": {"inherits": null, "implements": ["Body"]}, "SVGZoomEvent": {"inherits": "UIEvent", "implements": []}, "SVGLinearGradientElement": {"inherits": "SVGGradientElement", "implements": []}, "WebGLContextEvent": {"inherits": "Event", "implements": []}, "WebGL2RenderingContext": {"inherits": "WebGLRenderingContext", "implements": []}, "SVGFEDistantLightElement": {"inherits": "SVGElement", "implements": []}, "MouseScrollEvent": {"inherits": "MouseEvent", "implements": []}, "DOMStringMap": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGAnimateMotionElement": {"inherits": "SVGAnimationElement", "implements": []}, "ChannelSplitterNode": {"inherits": "AudioNode", "implements": []}, "ListBoxObject": {"inherits": "BoxObject", "implements": []}, "MozGsmIccInfo": {"inherits": "MozIccInfo", "implements": []}, "TrackEvent": {"inherits": "Event", "implements": []}, "SVGStyleElement": {"inherits": "SVGElement", "implements": []}, "PresentationDeviceInfoManager": {"inherits": "EventTarget", "implements": []}, "NodeIterator": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGAltGlyphElement": {"inherits": "SVGTextPositioningElement", "implements": ["SVGURIReference"]}, "SVGFEGaussianBlurElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "MozMobileConnection": {"inherits": "EventTarget", "implements": []}, "SVGFEConvolveMatrixElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "FileList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "Document": {"inherits": "Node", "implements": ["XPathEvaluator", "GlobalEventHandlers", "TouchEventHandlers", "ParentNode", "OnErrorEventHandlerForNodes", "GeometryUtils", "FontFaceSource", "LegacyQueryInterface"]}, "SVGAnimatedEnumeration": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MozStkCommandEvent": {"inherits": "Event", "implements": []}, "HTMLFontElement": {"inherits": "HTMLElement", "implements": []}, "SharedWorker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "RecordErrorEvent": {"inherits": "Event", "implements": []}, "DelayNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "SVGPathSegCurvetoCubicAbs": {"inherits": "SVGPathSeg", "implements": []}, "BluetoothGatt": {"inherits": "EventTarget", "implements": []}, "CameraClosedEvent": {"inherits": "Event", "implements": []}, "SVGMaskElement": {"inherits": "SVGElement", "implements": ["SVGUnitTypes"]}, "ContactManager": {"inherits": "EventTarget", "implements": []}, "ProgressEvent": {"inherits": "Event", "implements": []}, "ServiceWorker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "SVGPathSegLinetoHorizontalAbs": {"inherits": "SVGPathSeg", "implements": []}, "CharacterData": {"inherits": "Node", "implements": ["ChildNode", "NonDocumentTypeChildNode"]}, "KeyboardEvent": {"inherits": "UIEvent", "implements": ["KeyEvent"]}, "TelephonyCall": {"inherits": "EventTarget", "implements": []}, "WorkerNavigator": {"inherits": null, "implements": ["NavigatorID", "NavigatorLanguage", "NavigatorOnLine", "NavigatorDataStore"]}, "PopupBlockedEvent": {"inherits": "Event", "implements": []}, "MediaElementAudioSourceNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "HTMLFrameSetElement": {"inherits": "HTMLElement", "implements": ["WindowEventHandlers"]}, "BluetoothManager": {"inherits": "EventTarget", "implements": []}, "SVGFilterElement": {"inherits": "SVGElement", "implements": ["SVGURIReference", "SVGUnitTypes"]}, "SVGClipPathElement": {"inherits": "SVGElement", "implements": ["SVGUnitTypes"]}, "SVGLineElement": {"inherits": "SVGGeometryElement", "implements": []}, "SpeechRecognitionEvent": {"inherits": "Event", "implements": []}, "SVGPointList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGFEDropShadowElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "HTMLQuoteElement": {"inherits": "HTMLElement", "implements": []}, "SVGFESpotLightElement": {"inherits": "SVGElement", "implements": []}, "ServiceWorkerContainer": {"inherits": "EventTarget", "implements": []}, "HTMLContentElement": {"inherits": "HTMLElement", "implements": []}, "HTMLOutputElement": {"inherits": "HTMLElement", "implements": []}, "DataStoreChangeEvent": {"inherits": "Event", "implements": []}, "Plugin": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGStringList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGRectElement": {"inherits": "SVGGeometryElement", "implements": []}, "HTMLUListElement": {"inherits": "HTMLElement", "implements": []}, "History": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SpeechRecognition": {"inherits": "EventTarget", "implements": []}, "AnimationEvent": {"inherits": "Event", "implements": []}, "TouchEvent": {"inherits": "UIEvent", "implements": []}, "IDBMutableFile": {"inherits": "EventTarget", "implements": []}, "Range": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "CameraStateChangeEvent": {"inherits": "Event", "implements": []}, "MediaStreamAudioDestinationNode": {"inherits": "AudioNode", "implements": []}, "HTMLMenuItemElement": {"inherits": "HTMLElement", "implements": []}, "MediaSource": {"inherits": "EventTarget", "implements": []}, "PannerNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MozNFCPeerEvent": {"inherits": "Event", "implements": []}, "GamepadButtonEvent": {"inherits": "GamepadEvent", "implements": []}, "IDBRequest": {"inherits": "EventTarget", "implements": []}, "SVGLengthList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLDataListElement": {"inherits": "HTMLElement", "implements": []}, "HTMLFieldSetElement": {"inherits": "HTMLElement", "implements": []}, "BluetoothDiscoveryHandle": {"inherits": "EventTarget", "implements": []}, "SVGDefsElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGTextElement": {"inherits": "SVGTextPositioningElement", "implements": []}, "SVGScriptElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "InputEvent": {"inherits": "UIEvent", "implements": []}, "HTMLShadowElement": {"inherits": "HTMLElement", "implements": []}, "XPathEvaluator": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "AudioBufferSourceNode": {"inherits": "AudioScheduledSourceNode", "implements": ["AudioNodePassThrough"]}, "MozNFCTagEvent": {"inherits": "Event", "implements": []}, "PageTransitionEvent": {"inherits": "Event", "implements": []}, "PopStateEvent": {"inherits": "Event", "implements": []}, "Element": {"inherits": "Node", "implements": ["ChildNode", "NonDocumentTypeChildNode", "ParentNode", "Animatable", "GeometryUtils", "LegacyQueryInterface"]}, "HTMLInputElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent", "MozPhonetic"]}, "ValidityState": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGAElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGURIReference"]}, "DedicatedWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": []}, "HTMLTimeElement": {"inherits": "HTMLElement", "implements": []}, "IDBFileHandle": {"inherits": "EventTarget", "implements": []}, "MediaKeyError": {"inherits": "Event", "implements": []}, "StyleSheet": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "URL": {"inherits": null, "implements": ["URLUtils", "URLUtilsSearchParams"]}, "XMLHttpRequest": {"inherits": "XMLHttpRequestEventTarget", "implements": ["LegacyQueryInterface"]}, "MozMessageDeletedEvent": {"inherits": "Event", "implements": []}, "SVGPathSegCurvetoQuadraticSmoothRel": {"inherits": "SVGPathSeg", "implements": []}, "RTCPeerConnectionIdentityErrorEvent": {"inherits": "Event", "implements": []}, "RTCTrackEvent": {"inherits": "Event", "implements": []}, "RTCDTMFSender": {"inherits": "EventTarget", "implements": []}, "RTCDTMFToneChangeEvent": {"inherits": "Event", "implements": []}, "AudioStreamTrack": {"inherits": "MediaStreamTrack", "implements": []}, "HTMLSelectElement": {"inherits": "HTMLElement", "implements": []}, "DOMCursor": {"inherits": "EventTarget", "implements": ["DOMRequestShared"]}, "TextTrackList": {"inherits": "EventTarget", "implements": []}, "MozIccManager": {"inherits": "EventTarget", "implements": []}, "ScrollBoxObject": {"inherits": "BoxObject", "implements": []}, "NamedNodeMap": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGFEFloodElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "DOMDownloadManager": {"inherits": "EventTarget", "implements": []}, "DataContainerEvent": {"inherits": "Event", "implements": []}, "IccCardLockError": {"inherits": "DOMError", "implements": []}, "TelephonyCallGroup": {"inherits": "EventTarget", "implements": []}, "TVManager": {"inherits": "EventTarget", "implements": []}, "SelectionStateChangedEvent": {"inherits": "Event", "implements": []}, "CSSValueList": {"inherits": "CSSValue", "implements": ["LegacyQueryInterface"]}, "VideoTrackList": {"inherits": "EventTarget", "implements": []}, "SettingsLock": {"inherits": "EventTarget", "implements": []}, "PerformanceMeasure": {"inherits": "PerformanceEntry", "implements": []}, "HTMLOptionsCollection": {"inherits": "HTMLCollection", "implements": []}, "TVCurrentChannelChangedEvent": {"inherits": "Event", "implements": []}, "WorkerGlobalScope": {"inherits": "EventTarget", "implements": ["GlobalCrypto", "WindowOrWorkerGlobalScope"]}, "MouseEvent": {"inherits": "UIEvent", "implements": []}, "SVGPathSegLinetoAbs": {"inherits": "SVGPathSeg", "implements": []}, "HTMLAppletElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent", "MozFrameLoaderOwner", "MozObjectLoadingContent"]}, "LocalMediaStream": {"inherits": "MediaStream", "implements": []}, "HTMLOptionElement": {"inherits": "HTMLElement", "implements": []}, "TVSource": {"inherits": "EventTarget", "implements": []}, "StyleRuleChangeEvent": {"inherits": "Event", "implements": []}, "HTMLMeterElement": {"inherits": "HTMLElement", "implements": []}, "AudioChannelManager": {"inherits": "EventTarget", "implements": []}, "MediaRecorder": {"inherits": "EventTarget", "implements": []}, "SVGPreserveAspectRatio": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "TransitionEvent": {"inherits": "Event", "implements": []}, "HTMLBodyElement": {"inherits": "HTMLElement", "implements": ["WindowEventHandlers"]}, "MozVoicemail": {"inherits": "EventTarget", "implements": []}, "HTMLDivElement": {"inherits": "HTMLElement", "implements": []}, "SVGPolylineElement": {"inherits": "SVGGeometryElement", "implements": ["SVGAnimatedPoints"]}, "IDBVersionChangeEvent": {"inherits": "Event", "implements": []}, "Clipboard": {"inherits": "EventTarget", "implements": []}, "ClipboardEvent": {"inherits": "Event", "implements": []}, "SVGFEMergeNodeElement": {"inherits": "SVGElement", "implements": []}, "MessagePort": {"inherits": "EventTarget", "implements": ["Transferable"]}, "MozVoicemailEvent": {"inherits": "Event", "implements": []}, "BoxObject": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "BluetoothAttributeEvent": {"inherits": "Event", "implements": []}, "CSSPrimitiveValue": {"inherits": "CSSValue", "implements": ["LegacyQueryInterface"]}, "PaintRequestList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MediaStreamAudioSourceNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "AudioScheduledSourceNode": {"inherits": "AudioNode", "implements": []}, "ConstantSourceNode": {"inherits": "AudioScheduledSourceNode", "implements": []}, "BaseAudioContext": {"inherits": "EventTarget", "implements": []}, "AudioProcessingEvent": {"inherits": "Event", "implements": []}, "Attr": {"inherits": "Node", "implements": ["LegacyQueryInterface"]}, "HTMLObjectElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent", "MozFrameLoaderOwner", "MozObjectLoadingContent"]}, "SVGFEPointLightElement": {"inherits": "SVGElement", "implements": []}, "SVGAnimatedString": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGGElement": {"inherits": "SVGGraphicsElement", "implements": []}, "VRFieldOfView": {"inherits": "VRFieldOfViewReadOnly", "implements": []}, "HTMLTableSectionElement": {"inherits": "HTMLElement", "implements": []}, "MutationRecord": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MediaKeySession": {"inherits": "EventTarget", "implements": []}, "DataErrorEvent": {"inherits": "Event", "implements": []}, "Worker": {"inherits": "EventTarget", "implements": ["AbstractWorker"]}, "HTMLTableColElement": {"inherits": "HTMLElement", "implements": []}, "IccChangeEvent": {"inherits": "Event", "implements": []}, "HTMLSpanElement": {"inherits": "HTMLElement", "implements": []}, "MozActivity": {"inherits": "DOMRequest", "implements": []}, "PerformanceResourceTiming": {"inherits": "PerformanceEntry", "implements": []}, "HTMLCollection": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "USSDReceivedEvent": {"inherits": "Event", "implements": []}, "HTMLMapElement": {"inherits": "HTMLElement", "implements": []}, "ImageCapture": {"inherits": "EventTarget", "implements": []}, "AudioTrackList": {"inherits": "EventTarget", "implements": []}, "HTMLSourceElement": {"inherits": "HTMLElement", "implements": []}, "Navigator": {"inherits": null, "implements": ["LegacyQueryInterface", "NavigatorID", "NavigatorLanguage", "NavigatorOnLine", "NavigatorC<PERSON>nt<PERSON><PERSON>s", "NavigatorStorageUtils", "NavigatorFeatures", "NavigatorGeolocation", "NavigatorBattery", "NavigatorDataStore", "NavigatorMobileId"]}, "OfflineAudioContext": {"inherits": "BaseAudioContext", "implements": []}, "SVGPathSegCurvetoCubicSmoothRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGAnimatedPreserveAspectRatio": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MozContactChangeEvent": {"inherits": "Event", "implements": []}, "HTMLMediaElement": {"inherits": "HTMLElement", "implements": []}, "DeviceStorage": {"inherits": "EventTarget", "implements": []}, "SVGImageElement": {"inherits": "SVGGraphicsElement", "implements": ["MozImageLoadingContent", "SVGURIReference"]}, "UIEvent": {"inherits": "Event", "implements": []}, "SVGTransformList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MozSpeakerManager": {"inherits": "EventTarget", "implements": []}, "MozCellBroadcast": {"inherits": "EventTarget", "implements": []}, "SVGAnimateTransformElement": {"inherits": "SVGAnimationElement", "implements": []}, "SVGFEBlendElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "CSSStyleSheet": {"inherits": "StyleSheet", "implements": []}, "SVGPathSegArcRel": {"inherits": "SVGPathSeg", "implements": []}, "MozCellBroadcastEvent": {"inherits": "Event", "implements": []}, "FMRadio": {"inherits": "EventTarget", "implements": []}, "HTMLTableCellElement": {"inherits": "HTMLElement", "implements": []}, "ChromeWorker": {"inherits": "Worker", "implements": []}, "Telephony": {"inherits": "EventTarget", "implements": []}, "SVGFECompositeElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "Event": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLButtonElement": {"inherits": "HTMLElement", "implements": []}, "ScrollAreaEvent": {"inherits": "UIEvent", "implements": []}, "IDBOpenDBRequest": {"inherits": "IDBRequest", "implements": []}, "SVGComponentTransferFunctionElement": {"inherits": "SVGElement", "implements": []}, "SVGPathSegLinetoVerticalAbs": {"inherits": "SVGPathSeg", "implements": []}, "SVGTSpanElement": {"inherits": "SVGTextPositioningElement", "implements": []}, "TVScanningStateChangedEvent": {"inherits": "Event", "implements": []}, "BluetoothDevice": {"inherits": "EventTarget", "implements": []}, "HTMLProgressElement": {"inherits": "HTMLElement", "implements": []}, "MozOtaStatusEvent": {"inherits": "Event", "implements": []}, "HTMLOptGroupElement": {"inherits": "HTMLElement", "implements": []}, "GamepadAxisMoveEvent": {"inherits": "GamepadEvent", "implements": []}, "XULDocument": {"inherits": "Document", "implements": []}, "Notification": {"inherits": "EventTarget", "implements": ["LegacyQueryInterface"]}, "DOMPoint": {"inherits": "DOMPointReadOnly", "implements": []}, "HMDVRDevice": {"inherits": "VRDevice", "implements": []}, "SVGFEFuncRElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "MediaStreamTrackEvent": {"inherits": "Event", "implements": []}, "RTCDataChannel": {"inherits": "EventTarget", "implements": []}, "AudioContext": {"inherits": "BaseAudioContext", "implements": []}, "RTCPeerConnectionIdentityEvent": {"inherits": "Event", "implements": []}, "VTTCue": {"inherits": "EventTarget", "implements": []}, "ServiceWorkerGlobalScope": {"inherits": "WorkerGlobalScope", "implements": ["GlobalFetch"]}, "PopupBoxObject": {"inherits": "BoxObject", "implements": []}, "SpeechSynthesisUtterance": {"inherits": "EventTarget", "implements": []}, "TreeColumns": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGDocument": {"inherits": "Document", "implements": []}, "MozMobileMessageManager": {"inherits": "EventTarget", "implements": []}, "SVGFEFuncBElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "HTMLDListElement": {"inherits": "HTMLElement", "implements": []}, "DOMRect": {"inherits": "DOMRectReadOnly", "implements": []}, "HTMLHtmlElement": {"inherits": "HTMLElement", "implements": []}, "SVGFEMergeElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "ContainerBoxObject": {"inherits": "BoxObject", "implements": []}, "CameraConfigurationEvent": {"inherits": "Event", "implements": []}, "MozAbortablePromise": {"inherits": "_Promise", "implements": []}, "RTCPeerConnection": {"inherits": "EventTarget", "implements": []}, "SVGFESpecularLightingElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "XMLDocument": {"inherits": "Document", "implements": []}, "DownloadEvent": {"inherits": "Event", "implements": []}, "WorkerLocation": {"inherits": null, "implements": ["URLUtilsReadOnly"]}, "PositionSensorVRDevice": {"inherits": "VRDevice", "implements": []}, "BeforeAfterKeyboardEvent": {"inherits": "KeyboardEvent", "implements": []}, "SVGFEColorMatrixElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "CallEvent": {"inherits": "Event", "implements": []}, "BlobEvent": {"inherits": "Event", "implements": []}, "HTMLUnknownElement": {"inherits": "HTMLElement", "implements": []}, "TouchList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "DOMTransactionEvent": {"inherits": "Event", "implements": []}, "SVGPathSegCurvetoQuadraticRel": {"inherits": "SVGPathSeg", "implements": []}, "SVGAnimateElement": {"inherits": "SVGAnimationElement", "implements": []}, "DOMParser": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGEllipseElement": {"inherits": "SVGGeometryElement", "implements": []}, "SimpleGestureEvent": {"inherits": "MouseEvent", "implements": []}, "NotifyPaintEvent": {"inherits": "Event", "implements": []}, "SVGTextPathElement": {"inherits": "SVGTextContentElement", "implements": ["SVGURIReference"]}, "HTMLDocument": {"inherits": "Document", "implements": []}, "CameraFacesDetectedEvent": {"inherits": "Event", "implements": []}, "CustomEvent": {"inherits": "Event", "implements": []}, "TimeEvent": {"inherits": "Event", "implements": []}, "DOMException": {"inherits": null, "implements": ["ExceptionMembers"]}, "IDBCursorWithValue": {"inherits": "IDBCursor", "implements": []}, "SVGMPathElement": {"inherits": "SVGElement", "implements": ["SVGURIReference"]}, "CommandEvent": {"inherits": "Event", "implements": []}, "MozInterAppMessageEvent": {"inherits": "Event", "implements": []}, "HTMLAudioElement": {"inherits": "HTMLMediaElement", "implements": []}, "SVGViewElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox", "SVGZoomAndPan"]}, "SVGTextPositioningElement": {"inherits": "SVGTextContentElement", "implements": []}, "SVGTextContentElement": {"inherits": "SVGGraphicsElement", "implements": []}, "Location": {"inherits": null, "implements": []}, "FontFaceSet": {"inherits": "EventTarget", "implements": []}, "TVCurrentSourceChangedEvent": {"inherits": "Event", "implements": []}, "Touch": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MozSettingsEvent": {"inherits": "Event", "implements": []}, "BluetoothAdapter": {"inherits": "EventTarget", "implements": []}, "SVGPathSegMovetoRel": {"inherits": "SVGPathSeg", "implements": []}, "RTCDataChannelEvent": {"inherits": "Event", "implements": []}, "HTMLModElement": {"inherits": "HTMLElement", "implements": []}, "BluetoothStatusChangedEvent": {"inherits": "Event", "implements": []}, "SpeechSynthesisEvent": {"inherits": "Event", "implements": []}, "SVGTitleElement": {"inherits": "SVGElement", "implements": []}, "DOMApplicationsManager": {"inherits": "EventTarget", "implements": []}, "SettingsManager": {"inherits": "EventTarget", "implements": []}, "MenuBoxObject": {"inherits": "BoxObject", "implements": []}, "Screen": {"inherits": "EventTarget", "implements": ["LegacyQueryInterface"]}, "MozClirModeEvent": {"inherits": "Event", "implements": []}, "HTMLEmbedElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent", "MozFrameLoaderOwner", "MozObjectLoadingContent"]}, "OfflineResourceList": {"inherits": "EventTarget", "implements": ["LegacyQueryInterface"]}, "SVGPathSegClosePath": {"inherits": "SVGPathSeg", "implements": []}, "ConvolverNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "BluetoothDiscoveryStateChangedEvent": {"inherits": "Event", "implements": []}, "PropertyNodeList": {"inherits": "NodeList", "implements": []}, "HTMLStyleElement": {"inherits": "HTMLElement", "implements": ["LinkStyle"]}, "DataStore": {"inherits": "EventTarget", "implements": []}, "CDATASection": {"inherits": "Text", "implements": []}, "SourceBufferList": {"inherits": "EventTarget", "implements": []}, "StorageEvent": {"inherits": "Event", "implements": []}, "MozEmergencyCbModeEvent": {"inherits": "Event", "implements": []}, "PluginArray": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGFETurbulenceElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "MozInterAppMessagePort": {"inherits": "EventTarget", "implements": []}, "SVGNumberList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MozWifiStatusChangeEvent": {"inherits": "Event", "implements": []}, "SVGFETileElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "CaretPosition": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGPathSegCurvetoCubicRel": {"inherits": "SVGPathSeg", "implements": []}, "Request": {"inherits": null, "implements": ["Body"]}, "SVGAnimatedNumber": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGFEDiffuseLightingElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "TreeWalker": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "BarProp": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLLinkElement": {"inherits": "HTMLElement", "implements": ["LinkStyle"]}, "SVGUseElement": {"inherits": "SVGGraphicsElement", "implements": ["SVGURIReference"]}, "HTMLCanvasElement": {"inherits": "HTMLElement", "implements": []}, "SVGPathSegLinetoHorizontalRel": {"inherits": "SVGPathSeg", "implements": []}, "HTMLParamElement": {"inherits": "HTMLElement", "implements": []}, "SourceBuffer": {"inherits": "EventTarget", "implements": []}, "HashChangeEvent": {"inherits": "Event", "implements": []}, "PointerEvent": {"inherits": "MouseEvent", "implements": []}, "FileReader": {"inherits": "EventTarget", "implements": []}, "Comment": {"inherits": "CharacterData", "implements": ["LegacyQueryInterface"]}, "MozMmsEvent": {"inherits": "Event", "implements": []}, "BatteryManager": {"inherits": "EventTarget", "implements": []}, "DOMMatrix": {"inherits": "DOMMatrixReadOnly", "implements": []}, "SVGSwitchElement": {"inherits": "SVGGraphicsElement", "implements": []}, "SVGFEImageElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes", "SVGURIReference"]}, "HTMLScriptElement": {"inherits": "HTMLElement", "implements": []}, "HTMLPictureElement": {"inherits": "HTMLElement", "implements": []}, "ServiceWorkerRegistration": {"inherits": "EventTarget", "implements": []}, "HTMLVideoElement": {"inherits": "HTMLMediaElement", "implements": []}, "IDBFileRequest": {"inherits": "DOMRequest", "implements": []}, "SVGAnimatedNumberList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "RTCPeerConnectionIceEvent": {"inherits": "Event", "implements": []}, "PaintRequest": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLMenuElement": {"inherits": "HTMLElement", "implements": []}, "EngineeringMode": {"inherits": "EventTarget", "implements": []}, "BluetoothDeviceEvent": {"inherits": "Event", "implements": []}, "DeviceLightEvent": {"inherits": "Event", "implements": []}, "GainNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MozApplicationEvent": {"inherits": "Event", "implements": []}, "SVGFEFuncAElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "StyleSheetApplicableStateChangeEvent": {"inherits": "Event", "implements": []}, "TVEITBroadcastedEvent": {"inherits": "Event", "implements": []}, "UndoManager": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "XMLSerializer": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "SVGPathSegCurvetoQuadraticSmoothAbs": {"inherits": "SVGPathSeg", "implements": []}, "WaveShaperNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "VideoStreamTrack": {"inherits": "MediaStreamTrack", "implements": []}, "DOMImplementation": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLTableCaptionElement": {"inherits": "HTMLElement", "implements": []}, "SVGMarkerElement": {"inherits": "SVGElement", "implements": ["SVGFitToViewBox"]}, "MozWifiManager": {"inherits": "EventTarget", "implements": []}, "HTMLPreElement": {"inherits": "HTMLElement", "implements": []}, "Rect": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "DeviceStorageChangeEvent": {"inherits": "Event", "implements": []}, "DOMMobileMessageError": {"inherits": "DOMError", "implements": []}, "CSSFontFaceLoadEvent": {"inherits": "Event", "implements": []}, "HTMLHeadingElement": {"inherits": "HTMLElement", "implements": []}, "SVGRadialGradientElement": {"inherits": "SVGGradientElement", "implements": []}, "DeviceProximityEvent": {"inherits": "Event", "implements": []}, "EventSource": {"inherits": "EventTarget", "implements": ["LegacyQueryInterface"]}, "StereoPannerNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "AutocompleteErrorEvent": {"inherits": "Event", "implements": []}, "HTMLFrameElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner"]}, "HTMLOListElement": {"inherits": "HTMLElement", "implements": []}, "RadioNodeList": {"inherits": "NodeList", "implements": []}, "StyleSheetChangeEvent": {"inherits": "Event", "implements": []}, "TextTrack": {"inherits": "EventTarget", "implements": []}, "HTMLBaseElement": {"inherits": "HTMLElement", "implements": []}, "AnalyserNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MediaStream": {"inherits": "EventTarget", "implements": []}, "HTMLTableRowElement": {"inherits": "HTMLElement", "implements": []}, "UserProximityEvent": {"inherits": "Event", "implements": []}, "DragEvent": {"inherits": "MouseEvent", "implements": []}, "HTMLHRElement": {"inherits": "HTMLElement", "implements": []}, "BiquadFilterNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "ScriptProcessorNode": {"inherits": "AudioNode", "implements": ["AudioNodePassThrough"]}, "MimeTypeArray": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "HTMLIFrameElement": {"inherits": "HTMLElement", "implements": ["MozFrameLoaderOwner", "BrowserElement"]}, "FormData": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "MediaKeyMessageEvent": {"inherits": "Event", "implements": []}, "MozNFC": {"inherits": "EventTarget", "implements": ["MozNFCManager"]}, "HTMLLIElement": {"inherits": "HTMLElement", "implements": []}, "DOMTokenList": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "XMLStylesheetProcessingInstruction": {"inherits": "ProcessingInstruction", "implements": []}, "MozSettingsTransactionEvent": {"inherits": "Event", "implements": []}, "SVGFEComponentTransferElement": {"inherits": "SVGElement", "implements": ["SVGFilterPrimitiveStandardAttributes"]}, "MozSmsEvent": {"inherits": "Event", "implements": []}, "HTMLDirectoryElement": {"inherits": "HTMLElement", "implements": []}, "MediaEncryptedEvent": {"inherits": "Event", "implements": []}, "CFStateChangeEvent": {"inherits": "Event", "implements": []}, "HTMLTrackElement": {"inherits": "HTMLElement", "implements": []}, "SVGPathSegLinetoRel": {"inherits": "SVGPathSeg", "implements": []}, "WheelEvent": {"inherits": "MouseEvent", "implements": []}, "Node": {"inherits": "EventTarget", "implements": []}, "MozWifiStationInfoEvent": {"inherits": "Event", "implements": []}, "SVGRect": {"inherits": null, "implements": ["LegacyQueryInterface"]}, "DOMSettableTokenList": {"inherits": "DOMTokenList", "implements": []}, "HTMLImageElement": {"inherits": "HTMLElement", "implements": ["MozImageLoadingContent"]}, "DOMRequest": {"inherits": "EventTarget", "implements": ["DOMRequestShared"]}, "HTMLFormElement": {"inherits": "HTMLElement", "implements": []}, "SVGGraphicsElement": {"inherits": "SVGElement", "implements": ["SVGTests"]}, "DeviceMotionEvent": {"inherits": "Event", "implements": []}, "CompositionEvent": {"inherits": "UIEvent", "implements": []}, "SpeechRecognitionError": {"inherits": "Event", "implements": []}, "CallGroupErrorEvent": {"inherits": "Event", "implements": []}, "MozInputMethod": {"inherits": "EventTarget", "implements": []}, "UDPMessageEvent": {"inherits": "Event", "implements": []}, "MediaStreamEvent": {"inherits": "Event", "implements": []}, "HTMLTextAreaElement": {"inherits": "HTMLElement", "implements": []}, "XMLHttpRequestEventTarget": {"inherits": "EventTarget", "implements": []}, "HTMLAnchorElement": {"inherits": "HTMLElement", "implements": ["HTMLHyperlinkElementUtils", "URLUtilsSearchParams"]}, "HTMLPropertiesCollection": {"inherits": "HTMLCollection", "implements": []}, "SVGFEFuncGElement": {"inherits": "SVGComponentTransferFunctionElement", "implements": []}, "ImageCaptureErrorEvent": {"inherits": "Event", "implements": []}, "SVGSetElement": {"inherits": "SVGAnimationElement", "implements": []}, "ChannelMergerNode": {"inherits": "AudioNode", "implements": []}, "SyncEvent": {"inherits": "ExtendableEvent", "implements": []}, "OffscreenCanvas": {"inherits": "EventTarget", "implements": []}, "PromiseRejectionEvent": {"inherits": "Event", "implements": []}, "CSSCounterStyleRule": {"inherits": "CSSRule", "implements": []}, "PerformanceLongTaskTiming": {"inherits": "PerformanceEntry", "implements": []}, "TaskAttributionTiming": {"inherits": "PerformanceEntry", "implements": []}, "BeforeInstallPromptEvent": {"inherits": "Event", "implements": []}, "PerformanceNavigationTiming": {"inherits": "PerformanceEntry", "implements": []}, "PerformancePaintTiming": {"inherits": "PerformanceEntry", "implements": []}, "CSSStyleValue": {"inherits": null, "implements": ["CSSImageValue", "CSSKeywordValue", "CSSNumericValue", "CSSPositionValue", "CSSTransformValue", "CSSUnitValue", "CSSUnparsedValue"]}, "CSSImageValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSKeywordValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSNumericValue": {"inherits": "CSSStyleValue", "implements": ["CSSMathValue", "CSSUnitValue"]}, "CSSMathValue": {"inherits": "CSSNumericValue", "implements": ["CSSMathInvert", "CSSMathMax", "CSSMathMin", "CSSMathNegate", "CSSMathProduct", "CSSMathSum"]}, "CSSMathInvert": {"inherits": "CSSMathValue", "implements": []}, "CSSMathMax": {"inherits": "CSSMathValue", "implements": []}, "CSSMathMin": {"inherits": "CSSMathValue", "implements": []}, "CSSMathNegate": {"inherits": "CSSMathValue", "implements": []}, "CSSMathProduct": {"inherits": "CSSMathValue", "implements": []}, "CSSMathSum": {"inherits": "CSSMathValue", "implements": []}, "CSSUnitValue": {"inherits": "CSSNumericValue", "implements": []}, "CSSPositionValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSTransformValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSUnparsedValue": {"inherits": "CSSStyleValue", "implements": []}, "CSSTransformComponent": {"inherits": null, "implements": ["CSSMatrixComponent", "CSSPerspective", "CSSRotate", "CSSScale", "CSSSkew", "CSSSkewX", "CSSSkewY", "CSSTranslate"]}, "CSSMatrixComponent": {"inherits": "CSSTransformComponent", "implements": []}, "CSSPerspective": {"inherits": "CSSTransformComponent", "implements": []}, "CSSRotate": {"inherits": "CSSTransformComponent", "implements": []}, "CSSScale": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkew": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkewX": {"inherits": "CSSTransformComponent", "implements": []}, "CSSSkewY": {"inherits": "CSSTransformComponent", "implements": []}, "CSSTranslate": {"inherits": "CSSTransformComponent", "implements": []}, "StylePropertyMapReadOnly": {"inherits": null, "implements": ["StylePropertyMap"]}, "StylePropertyMap": {"inherits": "StylePropertyMapReadOnly", "implements": []}}