{"version": 3, "file": "createEslinter.js", "sourceRoot": "./", "sources": ["createEslinter.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,yCAA6B;AAG7B,uCAA6D;AAE7D,SAAgB,cAAc,CAAC,aAA4B;IACzD,8DAA8D;IACtD,IAAA,uCAAS,CAAuB;IAExC,mEAAmE;IACnE,IAAM,QAAQ,GAAG,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC;IAE9C,SAAS,SAAS,CAAC,QAAgB;QACjC,IAAI;YACF,IACE,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE;oBACvD,WAAW,EAAE,QAAQ;iBACtB,CAAC,KAAK,CAAC,EACR;gBACA,OAAO,SAAS,CAAC;aAClB;YAED,IAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEvD,IAAI,aAAa,IAAI,aAAa,CAAC,GAAG,EAAE;gBACtC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;aACnC;YAED,OAAO,UAAU,CAAC;SACnB;QAAC,OAAO,CAAC,EAAE;YACV,0CAA+B,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC9C;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,EAAE,SAAS,WAAA,EAAE,CAAC;AACvB,CAAC;AAhCD,wCAgCC"}