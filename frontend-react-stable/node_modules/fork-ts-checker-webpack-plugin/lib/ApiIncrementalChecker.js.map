{"version": 3, "file": "ApiIncrementalChecker.js", "sourceRoot": "./", "sources": ["ApiIncrementalChecker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,+CAA8C;AAE9C,iCAGiB;AAGjB;IAUE,+BAAY,EASe;YARzB,0BAAU,EACV,wCAAiB,EACjB,oCAAe,EACf,sBAAQ,EACR,YAAG,EACH,4BAA4B,EAA5B,iDAA4B,EAC5B,wCAAiB,EACjB,gEAA6B;QAdvB,wBAAmB,GAAG,IAAI,GAAG,EAAsB,CAAC;QACpD,qBAAgB,GAAa,EAAE,CAAC;QAChC,qBAAgB,GAAa,EAAE,CAAC;QActC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,2BAAY,CAC3C,UAAU,EACV,GAAG,EACH,iBAAiB,EACjB,eAAe,EACf,oBAAoB,EACpB,iBAAiB,EACjB,6BAA6B,CAC9B,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAEM,2CAAW,GAAlB;QACE,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;IACrC,CAAC;IAEM,8CAAc,GAArB,UAAsB,QAAgB;QACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,6CAAa,GAApB;QACE,aAAa;IACf,CAAC;IAEY,mDAAmB,GAAhC;;;;;4BACwB,qBAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,EAAA;;wBAAjE,aAAa,GAAG,SAAiD;wBACvE,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC;wBACnD,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC;wBAEnD,sBAAO,qCAA6B,CAClC,aAAa,CAAC,OAAO,EACrB,IAAI,CAAC,UAAU,CAChB,EAAC;;;;KACH;IAEY,+CAAe,GAA5B,UAA6B,iBAAoC;;;;gBAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;iBACzD;gBAED,WAA+C,EAArB,KAAA,IAAI,CAAC,gBAAgB,EAArB,cAAqB,EAArB,IAAqB,EAAE;oBAAtC,WAAW;oBACpB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;iBAC9C;gBAED,WAA+C,EAArB,KAAA,IAAI,CAAC,gBAAgB,EAArB,cAAqB,EAArB,IAAqB,EAAE;oBAAtC,WAAW;oBACpB,iBAAiB,CAAC,4BAA4B,EAAE,CAAC;oBACjD,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;wBACpC,SAAS;qBACV;oBAEK,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBAEpD,IAAI,MAAM,KAAK,SAAS,EAAE;wBACxB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;qBACnD;yBAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;wBACpD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;qBAC9C;iBACF;gBAEK,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC9D,sBAAO,qCAA6B,CAAC,OAAO,CAAC,EAAC;;;KAC/C;IACH,4BAAC;AAAD,CAAC,AArFD,IAqFC;AArFY,sDAAqB"}