{"version": 3, "file": "InternalFormatter.js", "sourceRoot": "./", "sources": ["formatter/InternalFormatter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,gDAA0B;AAC1B,qCAAyB;AAEzB,kCAAuC;AAGvC;;GAEG;AACH,SAAS,uBAAuB;IAC9B,OAAO,SAAS,iBAAiB,CAAC,KAAK;QACrC,IAAM,KAAK,GAAG;YACZ,OAAO,EAAE,eAAK,CAAC,IAAI,CAAC,GAAG;YACvB,KAAK,EAAE,eAAK,CAAC,IAAI;SAClB,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAW,CAAC,QAAQ,EAAE;YACzC,IAAM,KAAK,GAAG;gBACT,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,UAC1D,KAAK,CAAC,OACN;aACH,CAAC;YACF,IAAI,KAAK,CAAC,KAAK,EAAE;gBACf,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;aACtD;YAED,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,qBAAkB,KAAK,CAAC,MAAM,qBAAiB,CAAC,CAAC;SAClE;IACH,CAAC,CAAC;AACJ,CAAC;AAEQ,0DAAuB"}