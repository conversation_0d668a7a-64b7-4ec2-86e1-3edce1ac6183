{"version": 3, "file": "DefaultFormatter.js", "sourceRoot": "./", "sources": ["formatter/DefaultFormatter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAyB;AACzB,gDAA0B;AAE1B,kCAAsD;AAEtD,yDAA8D;AAE9D,SAAS,sBAAsB;IAC7B,OAAO,SAAS,gBAAgB,CAAC,KAAK;QACpC,IAAM,KAAK,GAAG;YACZ,OAAO,EACL,KAAK,CAAC,QAAQ,KAAK,qBAAa,CAAC,OAAO;gBACtC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM;gBACnB,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,GAAG;YACpB,QAAQ,EAAE,eAAK,CAAC,IAAI,CAAC,IAAI;YACzB,IAAI,EAAE,eAAK,CAAC,IAAI;SACjB,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAW,CAAC,QAAQ,EAAE;YACzC,OAAO,2CAAuB,EAAE,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAM,IAAI,GACR,KAAK,CAAC,MAAM,KAAK,mBAAW,CAAC,UAAU,CAAC,CAAC,CAAC,OAAK,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;QAE3E,OAAO;YACL,KAAK,CAAC,OAAO,CAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAM,CAAC;gBAClD,KAAK,CAAC,QAAQ,CAAI,KAAK,CAAC,IAAI,SAAI,KAAK,CAAC,IAAI,SAAI,KAAK,CAAC,SAAS,MAAG,CAAC;gBACjE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;YACpB,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO;SACxC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC,CAAC;AACJ,CAAC;AAEQ,wDAAsB"}