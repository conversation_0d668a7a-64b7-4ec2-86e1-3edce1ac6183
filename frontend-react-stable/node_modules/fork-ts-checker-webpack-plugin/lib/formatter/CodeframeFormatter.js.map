{"version": 3, "file": "CodeframeFormatter.js", "sourceRoot": "./", "sources": ["formatter/CodeframeFormatter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAAyB;AACzB,qCAAyB;AACzB,gDAA0B;AAE1B,wCAA6C;AAC7C,kCAAsD;AAEtD,yDAA8D;AAC9D,gDAAqD;AAiBrD,SAAS,wBAAwB,CAC/B,OAAmC;IAEnC,OAAO,SAAS,kBAAkB,CAAC,KAAK;QACtC,IAAM,KAAK,GAAG;YACZ,OAAO,EACL,KAAK,CAAC,QAAQ,KAAK,qBAAa,CAAC,OAAO;gBACtC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM;gBACnB,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,GAAG;YACpB,QAAQ,EAAE,eAAK,CAAC,GAAG;SACpB,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAW,CAAC,QAAQ,EAAE;YACzC,OAAO,2CAAuB,EAAE,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAM,MAAM,GACV,IAAI,IAAI,yBAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,MAAM,EAAE;YACV,KAAK,GAAG,6BAAgB,CACtB,MAAM,EACN;gBACE,KAAK,EAAE;oBACL,oEAAoE;oBACpE,IAAI,EAAE,KAAK,CAAC,IAAK;oBACjB,MAAM,EAAE,KAAK,CAAC,SAAS;iBACxB;aACF,aAEC,aAAa,EAAE,IAAI,IAChB,CAAC,OAAO,IAAI,EAAE,CAAC,EAErB;iBACE,KAAK,CAAC,IAAI,CAAC;iBACX,GAAG,CAAC,UAAC,IAAY,IAAK,OAAA,IAAI,GAAG,IAAI,EAAX,CAAW,CAAC;iBAClC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;SACjB;QAED,IAAM,KAAK,GAAG;YACZ,KAAK,CAAC,OAAO,CACR,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,YAAO,KAAK,CAAC,IAAI,SAAI,KAAK,CAAC,IAAI,SAC5D,KAAK,CAAC,SAAS,OACb,CACL;YACD,KAAK,CAAC,QAAQ,CAAI,KAAK,CAAC,IAAI,SAAI,KAAK,CAAC,SAAS,SAAI,KAAK,CAAC,OAAS,CAAC;SACpE,CAAC;QACF,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnB;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC,CAAC;AACJ,CAAC;AAEQ,4DAAwB"}