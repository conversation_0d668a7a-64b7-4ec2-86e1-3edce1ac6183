"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var IncrementalChecker_1 = require("./IncrementalChecker");
var CancellationToken_1 = require("./CancellationToken");
var ApiIncrementalChecker_1 = require("./ApiIncrementalChecker");
var worker_rpc_1 = require("worker-rpc");
var RpcTypes_1 = require("./RpcTypes");
var patchTypescript_1 = require("./patchTypescript");
var createEslinter_1 = require("./createEslinter");
var issue_1 = require("./issue");
var rpc = new worker_rpc_1.RpcProvider(function (message) {
    try {
        if (process.send) {
            process.send(message, undefined, undefined, function (error) {
                if (error) {
                    process.exit();
                }
            });
        }
    }
    catch (e) {
        // channel closed...
        process.exit();
    }
});
process.on('message', function (message) { return rpc.dispatch(message); });
// eslint-disable-next-line @typescript-eslint/no-var-requires
var typescript = require(String(process.env.TYPESCRIPT_PATH));
var patchConfig = {
    skipGetSyntacticDiagnostics: process.env.USE_INCREMENTAL_API === 'true' &&
        process.env.CHECK_SYNTACTIC_ERRORS !== 'true'
};
patchTypescript_1.patchTypescript(typescript, patchConfig);
var resolveModuleName = process.env.RESOLVE_MODULE_NAME
    ? require(process.env.RESOLVE_MODULE_NAME).resolveModuleName
    : undefined;
var resolveTypeReferenceDirective = process.env
    .RESOLVE_TYPE_REFERENCE_DIRECTIVE
    ? require(process.env.RESOLVE_TYPE_REFERENCE_DIRECTIVE)
        .resolveTypeReferenceDirective
    : undefined;
var eslinter = process.env.ESLINT === 'true'
    ? createEslinter_1.createEslinter(JSON.parse(String(process.env.ESLINT_OPTIONS)))
    : undefined;
function createChecker(useIncrementalApi) {
    var incrementalCheckerParams = {
        typescript: typescript,
        context: String(process.env.CONTEXT),
        programConfigFile: String(process.env.TSCONFIG),
        compilerOptions: JSON.parse(String(process.env.COMPILER_OPTIONS)),
        eslinter: eslinter,
        checkSyntacticErrors: process.env.CHECK_SYNTACTIC_ERRORS === 'true',
        resolveModuleName: resolveModuleName,
        resolveTypeReferenceDirective: resolveTypeReferenceDirective,
        vue: JSON.parse(String(process.env.VUE))
    };
    return useIncrementalApi
        ? new ApiIncrementalChecker_1.ApiIncrementalChecker(incrementalCheckerParams)
        : new IncrementalChecker_1.IncrementalChecker(incrementalCheckerParams);
}
var checker = createChecker(process.env.USE_INCREMENTAL_API === 'true');
function run(cancellationToken) {
    return __awaiter(this, void 0, void 0, function () {
        var diagnostics, lints, _a, _b, _c, _d, _e, _f, error_1;
        return __generator(this, function (_g) {
            switch (_g.label) {
                case 0:
                    diagnostics = [];
                    lints = [];
                    _g.label = 1;
                case 1:
                    _g.trys.push([1, 5, , 6]);
                    checker.nextIteration();
                    _b = (_a = diagnostics.push).apply;
                    _c = [diagnostics];
                    return [4 /*yield*/, checker.getTypeScriptIssues(cancellationToken)];
                case 2:
                    _b.apply(_a, _c.concat([(_g.sent())]));
                    if (!checker.hasEsLinter()) return [3 /*break*/, 4];
                    _e = (_d = lints.push).apply;
                    _f = [lints];
                    return [4 /*yield*/, checker.getEsLintIssues(cancellationToken)];
                case 3:
                    _e.apply(_d, _f.concat([(_g.sent())]));
                    _g.label = 4;
                case 4: return [3 /*break*/, 6];
                case 5:
                    error_1 = _g.sent();
                    if (error_1 instanceof typescript.OperationCanceledException) {
                        return [2 /*return*/, undefined];
                    }
                    diagnostics.push(issue_1.createIssueFromInternalError(error_1));
                    return [3 /*break*/, 6];
                case 6:
                    if (cancellationToken.isCancellationRequested()) {
                        return [2 /*return*/, undefined];
                    }
                    return [2 /*return*/, {
                            diagnostics: diagnostics,
                            lints: lints
                        }];
            }
        });
    });
}
rpc.registerRpcHandler(RpcTypes_1.RUN, function (message) {
    return typeof message !== 'undefined'
        ? run(CancellationToken_1.CancellationToken.createFromJSON(typescript, message))
        : undefined;
});
process.on('SIGINT', function () {
    process.exit();
});
//# sourceMappingURL=service.js.map