{"version": 3, "file": "IncrementalChecker.js", "sourceRoot": "./", "sources": ["IncrementalChecker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAAyB;AACzB,yCAA6B;AAG7B,iDAAgD;AAEhD,2CAIsB;AACtB,2CAA0C;AAO1C,iCAIiB;AAGjB;IAsBE,4BAAY,EASe;YARzB,0BAAU,EACV,wCAAiB,EACjB,oCAAe,EACf,sBAAQ,EACR,YAAG,EACH,4BAA4B,EAA5B,iDAA4B,EAC5B,wCAAiB,EACjB,gEAA6B;QA7BvB,UAAK,GAAG,IAAI,6BAAa,CAAC,cAAM,OAAA,CAAC;YACvC,aAAa;YACb,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,EAAE;SACZ,CAAC,EALsC,CAKtC,CAAC,CAAC;QA0BF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;IACrE,CAAC;IAEa,oCAAiB,GAA/B,UACE,UAAqB,EACrB,UAAkB,EAClB,eAAuB;QAEvB,IAAM,QAAQ,GAAG,UAAU,CAAC,cAAc,CACxC,UAAU,EACV,UAAU,CAAC,GAAG,CAAC,QAAQ,CACxB,CAAC,MAAM,CAAC;QAET,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,IAAI,EAAE,CAAC;QAC1D,QAAQ,CAAC,eAAe,gBACnB,QAAQ,CAAC,eAAe,EACxB,eAAe,CACnB,CAAC;QAEF,IAAM,MAAM,GAAG,UAAU,CAAC,0BAA0B,CAClD,QAAQ,EACR,UAAU,CAAC,GAAG,EACd,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CACzB,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEc,gCAAa,GAA5B,UACE,UAAqB,EACrB,aAAmC,EACnC,KAAoB,EACpB,UAAkC,EAClC,qBAAoD,EACpD,iCAA4E;QAE5E,IAAM,IAAI,GAAG,UAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAClE,IAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvC,IAAA,mGAML,EALC,wCAAiB,EACjB,gEAID,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,UAAC,WAAW,EAAE,cAAc;YACpD,OAAO,WAAW,CAAC,GAAG,CAAC,UAAA,UAAU;gBAC/B,OAAO,iBAAiB,CACtB,UAAU,EACV,UAAU,EACV,cAAc,EACd,aAAa,CAAC,OAAO,EACrB,IAAI,CACL,CAAC,cAAc,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,8BAA8B,GAAG,UACpC,kBAAkB,EAClB,cAAc;YAEd,OAAO,kBAAkB,CAAC,GAAG,CAAC,UAAA,iBAAiB;gBAC7C,OAAO,6BAA6B,CAClC,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,aAAa,CAAC,OAAO,EACrB,IAAI,CACL,CAAC,8BAA8B,CAAC;YACnC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,UAAC,QAAQ,EAAE,eAAe,EAAE,OAAO;YACtD,IAAI;gBACF,IAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEpC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aACjD;YAAC,OAAO,CAAC,EAAE;gBACV,gCAAgC;gBAChC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACxB;YAED,+DAA+D;YAC/D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;gBAC3D,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAA,IAAI;oBAC7B,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;gBACtE,CAAC,CAAC,CAAC;aACJ;YAED,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QACxC,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC,aAAa,CAC7B,aAAa,CAAC,SAAS,EACvB,aAAa,CAAC,OAAO,EACrB,IAAI,EACJ,UAAU,CAAC,qBAAqB;SACjC,CAAC;IACJ,CAAC;IAEM,wCAAW,GAAlB;QACE,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;IACrC,CAAC;IAEa,iCAAc,GAA5B,UAA6B,QAAgB;QAC3C,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,0CAAa,GAApB;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO;YAC7B,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAChC,CAAC;IAEO,2CAAc,GAAtB,UAAuB,UAAsB;QAC3C,IAAI,CAAC,aAAa;YAChB,IAAI,CAAC,aAAa;gBAClB,uBAAU,CAAC,iBAAiB,CAC1B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,eAAe,CACrB,CAAC;QAEJ,OAAO,uBAAU,CAAC,aAAa,CAC7B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EACpC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,6BAA6B,EAClC,UAAU,CACX,CAAC;IACJ,CAAC;IAEO,+CAAkB,GAA1B;QACE,IAAI,CAAC,aAAa;YAChB,IAAI,CAAC,aAAa;gBAClB,kBAAkB,CAAC,iBAAiB,CAClC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,eAAe,CACrB,CAAC;QAEJ,OAAO,kBAAkB,CAAC,aAAa,CACrC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,6BAA6B,CACnC,CAAC;IACJ,CAAC;IAEY,gDAAmB,GAAhC,UACE,iBAAoC;;;;;gBAE5B,OAAO,GAAK,IAAI,QAAT,CAAU;gBACzB,IAAI,CAAC,OAAO,EAAE;oBACZ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;iBAC9D;gBACK,aAAa,GAAoB,EAAE,CAAC;gBAEpC,YAAY,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;gBAE9C,YAAY,CAAC,OAAO,CAAC,UAAA,UAAU;oBAC7B,IAAI,iBAAiB,EAAE;wBACrB,iBAAiB,CAAC,4BAA4B,EAAE,CAAC;qBAClD;oBAED,IAAM,uBAAuB,GAAiC,KAAI;yBAC/D,oBAAoB;wBACrB,CAAC,CAAC,OAAO;6BACJ,sBAAsB,CAAC,UAAU,EAAE,iBAAiB,CAAC;6BACrD,MAAM,CACL,OAAO,CAAC,uBAAuB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAC/D;wBACL,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;oBAElE,aAAa,CAAC,IAAI,OAAlB,aAAa,EAAS,uBAAuB,EAAE;gBACjD,CAAC,CAAC,CAAC;gBAEH,sBAAO,qCAA6B,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAC;;;KACtE;IAEY,4CAAe,GAA5B,UACE,iBAAoC;;;;;gBAG9B,WAAW,GAAG,IAAI,CAAC,KAAK;qBAC3B,IAAI,EAAE;qBACN,MAAM,CACL,UAAA,QAAQ;oBACN,OAAA,CAAC,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM;wBACpC,CAAC,kBAAkB,CAAC,cAAc,CAAC,QAAQ,CAAC;gBAD5C,CAC4C,CAC/C,CAAC;gBAEE,mBAAmB,GAAG,IAAI,GAAG,EAAsB,CAAC;gBAC1D,WAAW,CAAC,OAAO,CAAC,UAAA,QAAQ;oBAC1B,iBAAiB,CAAC,4BAA4B,EAAE,CAAC;oBAEjD,oEAAoE;oBACpE,IAAM,MAAM,GAAG,KAAI,CAAC,QAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAClD,IAAI,MAAM,KAAK,SAAS,EAAE;wBACxB,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;qBAC3C;gBACH,CAAC,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,mBAAmB,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,QAAQ;oBACzC,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAA,IAAI;wBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAA,QAAQ;oBAChC,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAA,IAAI;wBAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACrB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEG,OAAO,GAAG,IAAI,CAAC,KAAK;qBACvB,IAAI,EAAE;qBACN,MAAM,CACL,UAAC,UAAU,EAAE,QAAQ;oBACnB,OAAA,UAAU,CAAC,MAAM,CAAC,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBAAvD,CAAuD,EACzD,EAAE,CACH,CAAC;gBAEJ,sBAAO,qCAA6B,CAAC,OAAO,CAAC,EAAC;;;KAC/C;IACH,yBAAC;AAAD,CAAC,AAlRD,IAkRC;AAlRY,gDAAkB"}