{"version": 3, "file": "CompilerHost.js", "sourceRoot": "./", "sources": ["CompilerHost.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,2CAA0C;AAC1C,2CAA0C;AAc1C;IA0DE,sBACU,UAAqB,EACrB,UAAsB,EAC9B,iBAAyB,EACzB,eAAmC,EACnC,oBAA6B,EAC7B,qBAAyC,EACzC,iCAAiE;QAPnE,iBAkEC;QAjES,eAAU,GAAV,UAAU,CAAW;QACrB,eAAU,GAAV,UAAU,CAAY;QApChC,6FAA6F;QACrF,sBAAiB,GAAG,IAAI,uBAAU,EAA2B,CAAC;QAC9D,iBAAY,GAAG,IAAI,uBAAU,EAAsB,CAAC;QAEpD,eAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QAE/B,uBAAkB,GAAoB,EAAE,CAAC;QACzC,iBAAY,GAAG;YACrB,gBAAgB;QAClB,CAAC,CAAC;QAOM,uBAAkB,GAAG,KAAK,CAAC;QAuT5B,kBAAa,GAAG,IAAI,CAAC,UAAU;aACnC,8CAA8C,CAAC;QA7RhD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,uBAAuB,CAC9C,iBAAiB,EACjB,eAAe,EACf,UAAU,CAAC,GAAG,EACd,UAAU,CAAC,8CAA8C,EACzD,UAAC,IAAmB;YAClB,IACE,CAAC,oBAAoB;gBACrB,IAAI,CAAC,IAAI,IAAI,IAAI;gBACjB,IAAI,CAAC,IAAI,GAAG,IAAI;gBAChB,IAAI,CAAC,IAAI,CAAC,sGAAsG;cAChH;gBACA,OAAO;aACR;YACD,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,EACD;YACE,aAAa;QACf,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC;QAEzD,IAAI,qBAAqB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,UACxB,WAAqB,EACrB,cAAsB;gBAEtB,OAAO,WAAW,CAAC,GAAG,CAAC,UAAA,UAAU;oBAC/B,OAAO,qBAAqB,CAC1B,KAAI,CAAC,UAAU,EACf,UAAU,EACV,cAAc,EACd,KAAI,CAAC,eAAe,EACpB,KAAI,CACL,CAAC,cAAc,CAAC;gBACnB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;SACH;QAED,IAAI,iCAAiC,EAAE;YACrC,IAAI,CAAC,8BAA8B,GAAG,UACpC,kBAA4B,EAC5B,cAAsB;gBAEtB,OAAO,kBAAkB,CAAC,GAAG,CAAC,UAAA,iBAAiB;oBAC7C,OAAO,iCAAiC,CACtC,KAAI,CAAC,UAAU,EACf,iBAAiB,EACjB,cAAc,EACd,KAAI,CAAC,eAAe,EACpB,KAAI,CACL,CAAC,8BAA8B,CAAC;gBACnC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;SACH;IACH,CAAC;IAnHM,iCAAU,GAAjB;QACE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,CAAC;IAEM,uCAAgB,GAAvB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IA2GY,qCAAc,GAA3B;;;;;;;6BAKM,CAAC,IAAI,CAAC,cAAc,EAApB,wBAAoB;wBAChB,cAAc,GAAG,IAAI,OAAO,CAAkB,UAAA,OAAO;4BACzD,KAAI,CAAC,YAAY,GAAG;gCAClB,OAAO,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;gCACjC,KAAI,CAAC,YAAY,GAAG;oCAClB,gBAAgB;gCAClB,CAAC,CAAC;gCACF,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;4BAClC,CAAC,CAAC;wBACJ,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;wBACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;wBACzC,qBAAM,cAAc,EAAA;;wBAA7B,MAAM,GAAG,SAAoB;wBACnC,sBAAO;gCACL,OAAO,EAAE,MAAM;gCACf,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gCACzC,YAAY,EAAE,EAAE;6BACjB,EAAC;;oBAGJ,uEAAuE;oBACvE,oDAAoD;oBACpD,qBAAM,IAAI,CAAC,cAAc,EAAA;;wBAFzB,uEAAuE;wBACvE,oDAAoD;wBACpD,SAAyB,CAAC;wBAEpB,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBACnD,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;wBACvB,aAAa,GAAG,IAAI,OAAO,CAAkB,UAAA,OAAO;4BACxD,KAAI,CAAC,YAAY,GAAG;gCAClB,OAAO,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;gCACjC,KAAI,CAAC,YAAY,GAAG;oCAClB,gBAAgB;gCAClB,CAAC,CAAC;gCACF,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;4BAClC,CAAC,CAAC;wBACJ,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;wBAE9B,KAAK,GAAG,EAAE,CAAC;wBAEjB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,IAAI;4BACjC,KAAgB,UAAW,EAAX,KAAA,IAAI,CAAC,MAAM,EAAX,cAAW,EAAX,IAAW,EAAE;gCAAxB,IAAM,CAAC,SAAA;gCACV,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;6BAC3B;4BACD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC;wBAEG,YAAY,GAAa,EAAE,CAAC;wBAC5B,YAAY,GAAa,EAAE,CAAC;wBAClC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI;4BAC5B,KAAgB,UAAW,EAAX,KAAA,IAAI,CAAC,MAAM,EAAX,cAAW,EAAX,IAAW,EAAE;gCAAxB,IAAM,CAAC,SAAA;gCACV,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;gCACvC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gCACvB,IACE,CAAC,CAAC,SAAS,KAAK,KAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO;oCAC5D,CAAC,CAAC,SAAS,KAAK,KAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,EAC5D;oCACA,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;iCAC/B;qCAAM,IACL,CAAC,CAAC,SAAS,KAAK,KAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,EAC5D;oCACA,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;iCAC/B;6BACF;4BACD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC;wBAEH,2EAA2E;wBAC3E,+DAA+D;wBAC/D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;4BAC5B,oCAAoC;4BACpC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;4BAC7C,IAAI,CAAC,YAAY,EAAE,CAAC;4BACpB,sBAAO;oCACL,OAAO,EAAE,IAAI,CAAC,kBAAkB;oCAChC,YAAY,EAAE,EAAE;oCAChB,YAAY,EAAE,EAAE;iCACjB,EAAC;yBACH;wBAEe,qBAAM,aAAa,EAAA;;wBAA7B,OAAO,GAAG,SAAmB;wBACnC,sBAAO,EAAE,OAAO,SAAA,EAAE,YAAY,cAAA,EAAE,YAAY,cAAA,EAAE,EAAC;;;;KAChD;IAEM,iCAAU,GAAjB;IACE,8DAA8D;IAC9D,QAAkC,EAClC,EAAU;QACV,8DAA8D;QAC9D,cAAc;;QADd,8DAA8D;QAC9D,UAAc;QADd,8DAA8D;QAC9D,qBAAc;QADd,8DAA8D;QAC9D,IAAc;YADd,8DAA8D;YAC9D,6BAAc;;QAEd,0CAA0C;QAC1C,wEAAwE;QACxE,iEAAiE;QACjE,4DAA4D;QAC5D,4DAA4D;QAC5D,iEAAiE;QACjE,uEAAuE;QACvE,yEAAyE;QACzE,uEAAuE;QACvE,gCAAgC;QAChC,4EAA4E;QAC5E,+BAA+B;QAC/B,EAAE;QACF,wEAAwE;QACxE,+DAA+D;QAC/D,mEAAmE;QACnE,+DAA+D;QAC/D,sEAAsE;QACtE,iBAAiB;QACjB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC;IAED,8DAA8D;IACvD,mCAAY,GAAnB,UAAoB,SAAc;QAChC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,IAAI,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC;IAChE,CAAC;IAEM,0CAAmB,GAA1B;QACE,aAAa;IACf,CAAC;IAEM,qCAAc,GAArB,UACE,IAAY,EACZ,QAAqC,EACrC,SAAmB;QAEnB,IAAM,IAAI,GAA4B,EAAE,QAAQ,UAAA,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAC/D,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,cAAc,CACxB,IAAI,EACJ,UAAA,QAAQ;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;QACjC,CAAC,EACD,SAAS,CACV,CAAC;QACF,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC;SACF,CAAC;IACJ,CAAC;IAEM,gCAAS,GAAhB,UACE,IAAY,EACZ,QAAgC,EAChC,eAAwB;QAH1B,iBAyBC;QApBC,IAAM,IAAI,GAAuB,EAAE,QAAQ,UAAA,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAC1D,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,CACnB,IAAI,EACJ,UAAC,QAAQ,EAAE,SAAS;YAClB,IAAI,SAAS,KAAK,KAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,EAAE;gBAC9D,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;aAC/B;iBAAM,IAAI,SAAS,KAAK,KAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,EAAE;gBACrE,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;QAC5C,CAAC,EACD,eAAe,CAChB,CAAC;QACF,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC;SACF,CAAC;IACJ,CAAC;IAEM,iCAAU,GAAjB,UAAkB,IAAY;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,+BAAQ,GAAf,UAAgB,IAAY,EAAE,QAAiB;QAC7C,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAErD,wCAAwC;QACxC,IAAI,OAAO,IAAI,uBAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACrC,IAAM,QAAQ,GAAG,uBAAU,CAAC,kBAAkB,CAC5C,IAAI,CAAC,UAAU,EACf,OAAO,EACP,IAAI,CAAC,UAAU,CAAC,QAAQ,CACzB,CAAC;YACF,OAAO,QAAQ,CAAC,OAAO,CAAC;SACzB;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,sCAAe,GAAtB,UAAuB,IAAY;QACjC,OAAO,CACL,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClE,KAAK,CACN,CAAC;IACJ,CAAC;IAEM,qCAAc,GAArB,UAAsB,IAAY;QAChC,OAAO,CACL,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CACvE,CAAC;IACJ,CAAC;IAEM,oCAAa,GAApB,UACE,IAAY,EACZ,UAAkC,EAClC,OAA+B,EAC/B,OAA+B,EAC/B,KAAc;QAEd,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CACtC,IAAI,EACJ,UAAU,EACV,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC;IACJ,CAAC;IAKM,0CAAmB,GAA1B;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;IAC3C,CAAC;IAEM,4CAAqB,GAA5B,UAA6B,OAA2B;QACtD,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAEM,6CAAsB,GAA7B,UAA8B,IAAY;QACxC,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,sBAAsB;YAClC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CACzC,CAAC;IACJ,CAAC;IAEM,iCAAU,GAAjB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAEM,+BAAQ,GAAf,UAAgB,IAAY;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACzB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;SACH;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAEM,4BAAK,GAAZ,UAAa,CAAS;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACtB;IACH,CAAC;IAEM,gDAAyB,GAAhC;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAC;IACjD,CAAC;IAEM,0DAAmC,GAA1C;QACE,aAAa;IACf,CAAC;IAEM,yCAAkB,GAAzB,UACE,OAAoD;QAEpD,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,uHAAuH;IACvH,gBAAgB;IAChB,iCAAiC;IACjC,8BAA8B;IAC9B,4HAA4H;IACrH,sCAAe,GAAtB;QACE,4BAA4B;IAC9B,CAAC;IAEM,gCAAS,GAAhB;QACE,4BAA4B;IAC9B,CAAC;IAEM,2DAAoC,GAA3C;QACE,4BAA4B;IAC9B,CAAC;IACH,mBAAC;AAAD,CAAC,AAxaD,IAwaC;AAxaY,oCAAY"}