{"version": 3, "file": "EsLintIssueFactory.js", "sourceRoot": "./", "sources": ["issue/eslint/EsLintIssueFactory.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,kCAA2D;AAC3D,8CAA6C;AAC7C,kDAAiD;AAGjD,SAAS,4BAA4B,CAAC,OAA+B;IACnE,OAAO;QACL,MAAM,EAAE,yBAAW,CAAC,MAAM;QAC1B,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW;QAC3D,QAAQ,EACN,OAAO,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,6BAAa,CAAC,OAAO,CAAC,CAAC,CAAC,6BAAa,CAAC,KAAK;QACtE,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,IAAI,EAAE,OAAO,CAAC,QAAQ;QACtB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,SAAS,EAAE,OAAO,CAAC,MAAM;KAC1B,CAAC;AACJ,CAAC;AAED,SAAS,6CAA6C,CACpD,MAAkB;IAElB,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,OAAO,IAAI,OAAA,cACjC,OAAO,IACV,QAAQ,EAAE,MAAM,CAAC,QAAQ,IACzB,EAHoC,CAGpC,CAAC,CAAC;AACN,CAAC;AAED,SAAS,6CAA6C,CACpD,MAAkB;IAElB,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAC1B,UAAC,QAAQ,EAAE,MAAM,IAAK,OACjB,QAAQ,QACR,6CAA6C,CAAC,MAAM,CAAC,GAFpC,CAGrB,EACD,EAAE,CACH,CAAC;AACJ,CAAC;AAED,SAAS,8CAA8C,CACrD,OAAqB;IAErB,OAAO,OAAO,CAAC,MAAM,CACnB,UAAC,QAAQ,EAAE,MAAM,IAAK,OACjB,QAAQ,QACR,6CAA6C,CAAC,MAAM,CAAC,GAFpC,CAGrB,EACD,EAAE,CACH,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B,CACrC,QAAkC;IAElC,OAAO,gCAAwB,CAAC,QAAQ,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,6BAA6B,CAAC,OAAqB;IAC1D,OAAO,8BAA8B,CACnC,8CAA8C,CAAC,OAAO,CAAC,CACxD,CAAC;AACJ,CAAC;AAEQ,sEAA6B"}