{"version": 3, "file": "TypeScriptIssueFactory.js", "sourceRoot": "./", "sources": ["issue/typescript/TypeScriptIssueFactory.ts"], "names": [], "mappings": ";;AACA,kCAA2D;AAC3D,8CAA6C;AAC7C,kDAAiD;AAEjD,SAAS,2BAA2B,CAClC,UAAyB,EACzB,UAAqB;IAErB,IAAI,IAAwB,CAAC;IAC7B,IAAI,IAAwB,CAAC;IAC7B,IAAI,SAA6B,CAAC;IAElC,IAAI,UAAU,CAAC,IAAI,EAAE;QACnB,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEhC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,IAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,6BAA6B,CAC5D,UAAU,CAAC,KAAK,CACjB,CAAC;YACF,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;YACzB,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;SACpC;KACF;IAED,OAAO;QACL,MAAM,EAAE,yBAAW,CAAC,UAAU;QAC9B,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QAC7B,qDAAqD;QACrD,QAAQ,EACN,UAAU,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,6BAAa,CAAC,OAAO,CAAC,CAAC,CAAC,6BAAa,CAAC,KAAK;QACzE,OAAO,EAAE,UAAU,CAAC,4BAA4B,CAC9C,UAAU,CAAC,WAAW,EACtB,IAAI,CACL;QACD,IAAI,MAAA;QACJ,IAAI,MAAA;QACJ,SAAS,WAAA;KACV,CAAC;AACJ,CAAC;AAcQ,kEAA2B;AAZpC,SAAS,6BAA6B,CACpC,WAA4B,EAC5B,UAAqB;IAErB,SAAS,wCAAwC,CAAC,UAAyB;QACzE,OAAO,2BAA2B,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,gCAAwB,CAC7B,WAAW,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAC1D,CAAC;AACJ,CAAC;AAEqC,sEAA6B"}