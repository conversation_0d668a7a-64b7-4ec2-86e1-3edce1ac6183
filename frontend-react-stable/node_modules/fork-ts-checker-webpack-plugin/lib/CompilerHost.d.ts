import * as ts from 'typescript';
import { ResolveModuleName, ResolveTypeReferenceDirective } from './resolution';
import { VueOptions } from './types/vue-options';
export declare class CompilerHost implements ts.WatchCompilerHostOfConfigFile<ts.EmitAndSemanticDiagnosticsBuilderProgram> {
    private typescript;
    private vueOptions;
    private program?;
    getProgram(): ts.Program;
    getAllKnownFiles(): Set<string>;
    configFileName: string;
    optionsToExtend: ts.CompilerOptions;
    private directoryWatchers;
    private fileWatchers;
    private knownFiles;
    private gatheredDiagnostic;
    private afterCompile;
    private readonly tsHost;
    protected lastProcessing?: Promise<ts.Diagnostic[]>;
    private compilationStarted;
    resolveModuleNames: ((moduleNames: string[], containingFile: string, reusedNames?: string[] | undefined, redirectedReference?: ts.ResolvedProjectReference | undefined) => (ts.ResolvedModule | undefined)[]) | undefined;
    resolveTypeReferenceDirectives: ((typeReferenceDirectiveNames: string[], containingFile: string, redirectedReference?: ts.ResolvedProjectReference | undefined) => (ts.ResolvedTypeReferenceDirective | undefined)[]) | undefined;
    constructor(typescript: typeof ts, vueOptions: VueOptions, programConfigFile: string, compilerOptions: ts.CompilerOptions, checkSyntacticErrors: boolean, userResolveModuleName?: ResolveModuleName, userResolveTypeReferenceDirective?: ResolveTypeReferenceDirective);
    processChanges(): Promise<{
        results: ts.Diagnostic[];
        updatedFiles: string[];
        removedFiles: string[];
    }>;
    setTimeout(callback: (...args: any[]) => void, ms: number, ...args: any[]): any;
    clearTimeout(timeoutId: any): void;
    onWatchStatusChange(): void;
    watchDirectory(path: string, callback: ts.DirectoryWatcherCallback, recursive?: boolean): ts.FileWatcher;
    watchFile(path: string, callback: ts.FileWatcherCallback, pollingInterval?: number): ts.FileWatcher;
    fileExists(path: string): boolean;
    readFile(path: string, encoding?: string): string | undefined;
    directoryExists(path: string): boolean;
    getDirectories(path: string): string[];
    readDirectory(path: string, extensions?: ReadonlyArray<string>, exclude?: ReadonlyArray<string>, include?: ReadonlyArray<string>, depth?: number): string[];
    createProgram: typeof ts.createEmitAndSemanticDiagnosticsBuilderProgram;
    getCurrentDirectory(): string;
    getDefaultLibFileName(options: ts.CompilerOptions): string;
    getEnvironmentVariable(name: string): string | undefined;
    getNewLine(): string;
    realpath(path: string): string;
    trace(s: string): void;
    useCaseSensitiveFileNames(): boolean;
    onUnRecoverableConfigFileDiagnostic(): void;
    afterProgramCreate(program: ts.EmitAndSemanticDiagnosticsBuilderProgram): void;
    createDirectory(): void;
    writeFile(): void;
    onCachedDirectoryStructureHostCreate?(): void;
}
