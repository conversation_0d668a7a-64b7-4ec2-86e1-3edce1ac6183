{"version": 3, "file": "CancellationToken.js", "sourceRoot": "./", "sources": ["CancellationToken.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,kDAA4B;AAC5B,qCAAyB;AACzB,qCAAyB;AACzB,yCAA6B;AAG7B,uCAA4C;AAO5C;IAIE,2BACU,UAAqB,EAC7B,oBAA6B,EAC7B,WAAqB;QAFb,eAAU,GAAV,UAAU,CAAW;QAI7B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC;QACjC,IAAI,CAAC,oBAAoB;YACvB,oBAAoB,IAAI,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACjE,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC;IACrC,CAAC;IAEa,gCAAc,GAA5B,UACE,UAAqB,EACrB,IAA2B;QAE3B,OAAO,IAAI,iBAAiB,CAC1B,UAAU,EACV,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,WAAW,CACjB,CAAC;IACJ,CAAC;IAEM,kCAAM,GAAb;QACE,OAAO;YACL,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;IACJ,CAAC;IAEM,mDAAuB,GAA9B;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC3D,CAAC;IAEM,mDAAuB,GAA9B;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAEjE,IAAI,QAAQ,GAAG,EAAE,EAAE;YACjB,sCAAsC;YACtC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,WAAW,GAAG,yBAAc,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;SACnE;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,wDAA4B,GAAnC;QACE,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;YAClC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC;SACxD;IACH,CAAC;IAEM,+CAAmB,GAA1B;QACE,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEM,+CAAmB,GAA1B;QACE,IAAI,IAAI,CAAC,WAAW,IAAI,yBAAc,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,EAAE;YACtE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;YAC9C,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC1B;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AAvED,IAuEC;AAvEY,8CAAiB"}