{"version": 3, "file": "FilesRegister.js", "sourceRoot": "./", "sources": ["FilesRegister.ts"], "names": [], "mappings": ";;AASA;IAGE,uBAAoB,WAA6C;QAA7C,gBAAW,GAAX,WAAW,CAAkC;QAC/D,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAEM,4BAAI,GAAX;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAEM,2BAAG,GAAV,UAAW,QAAgB;QACzB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG;YACrB,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;SAClC,CAAC;IACJ,CAAC;IAEM,8BAAM,GAAb,UAAc,QAAgB;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;SAC7B;IACH,CAAC;IAEM,2BAAG,GAAV,UAAW,QAAgB;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAEM,2BAAG,GAAV,UAAW,QAAgB;QACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,QAAQ,GAAG,QAAQ,GAAG,0BAA0B,CAAC,CAAC;SACnE;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAEM,8BAAM,GAAb,UAAc,QAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACvB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpB;IACH,CAAC;IAEM,+BAAO,GAAd,UAAe,QAAgB;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;IACjC,CAAC;IAEM,kCAAU,GAAjB,UAAkB,QAAgB,EAAE,OAAkC;QACpE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEtB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAEM,gCAAQ,GAAf,UAAgB,QAAgB;QAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;IAClC,CAAC;IAEM,gCAAQ,GAAf,UAAgB,QAAgB,EAAE,KAAa;QAC7C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;YACxC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;YACnC,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;SACzE;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAlED,IAkEC;AAlEY,sCAAa"}