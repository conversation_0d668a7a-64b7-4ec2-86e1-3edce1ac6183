{"version": 3, "file": "hooks.js", "sourceRoot": "./", "sources": ["hooks.ts"], "names": [], "mappings": ";;AACA,mCAAoD;AAiBpD,IAAM,eAAe,GAAG,IAAI,OAAO,EAA0C,CAAC;AAE9E,SAAS,qCAAqC;IAC5C,OAAO;QACL,kBAAkB,EAAE,IAAI,yBAAe,CAAC,EAAE,CAAC;QAC3C,MAAM,EAAE,IAAI,kBAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAC3C,iBAAiB,EAAE,IAAI,kBAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,EAAE,IAAI,kBAAQ,CAAC,EAAE,CAAC;QACzB,YAAY,EAAE,IAAI,kBAAQ,CAAC,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;QAC3D,OAAO,EAAE,IAAI,kBAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC/C,kBAAkB,EAAE,IAAI,kBAAQ,CAAC,EAAE,CAAC;QACpC,IAAI,EAAE,IAAI,kBAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACvD,IAAI,EAAE,IAAI,kBAAQ,CAAC,CAAC,aAAa,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;KACxD,CAAC;AACJ,CAAC;AAED,SAAgB,kCAAkC,CAAC,QAA0B;IAC3E,IAAI,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,KAAK,GAAG,qCAAqC,EAAE,CAAC;QAChD,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KACtC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAPD,gFAOC"}