//! stable.js 0.1.8, https://github.com/Two-Screen/stable
//! © 2018 Angry Bytes and contributors. MIT licensed.
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):e.stable=n()}(this,function(){"use strict";var e=function(e,n){return t(e.slice(),n)};function t(e,n){"function"!=typeof n&&(n=function(e,n){return String(e).localeCompare(n)});var r=e.length;if(r<=1)return e;for(var t=new Array(r),f=1;f<r;f*=2){i(e,n,f,t);var o=e;e=t,t=o}return e}e.inplace=function(e,n){var r=t(e,n);return r!==e&&i(r,null,e.length,e),e};var i=function(e,n,r,t){var f,o,i,u,a,c=e.length,l=0,s=2*r;for(f=0;f<c;f+=s)for(i=(o=f+r)+r,c<o&&(o=c),c<i&&(i=c),u=f,a=o;;)if(u<o&&a<i)n(e[u],e[a])<=0?t[l++]=e[u++]:t[l++]=e[a++];else if(u<o)t[l++]=e[u++];else{if(!(a<i))break;t[l++]=e[a++]}};return e});