{"name": "connect-history-api-fallback", "version": "1.6.0", "description": "Provides a fallback for non-existing directories so that the HTML 5 history API can be used.", "keyswords": ["connect", "html5", "history api", "fallback", "spa"], "engines": {"node": ">=0.8"}, "main": "lib/index.js", "files": ["lib"], "scripts": {"test": "eslint lib/index.js test/index_test.js && nodeunit test/index_test.js"}, "repository": {"type": "git", "url": "http://github.com/bripkens/connect-history-api-fallback.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bripkens.de"}, "contributors": ["<PERSON> <<EMAIL>> (http://www.craigmyles.com)"], "license": "MIT", "devDependencies": {"eslint": "^0.18.0", "nodeunit": "^0.11.3", "sinon": "^1.14.1"}}