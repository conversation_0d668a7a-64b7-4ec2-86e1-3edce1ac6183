{"name": "react-scripts", "version": "4.0.3", "description": "Configuration and scripts for Create React App.", "repository": {"type": "git", "url": "https://github.com/facebook/create-react-app.git", "directory": "packages/react-scripts"}, "license": "MIT", "engines": {"node": "^10.12.0 || >=12.0.0"}, "bugs": {"url": "https://github.com/facebook/create-react-app/issues"}, "files": ["bin", "config", "lib", "scripts", "template", "template-typescript", "utils"], "bin": {"react-scripts": "./bin/react-scripts.js"}, "types": "./lib/react-app.d.ts", "dependencies": {"@babel/core": "7.12.3", "@pmmmwh/react-refresh-webpack-plugin": "0.4.3", "@svgr/webpack": "5.5.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.0", "babel-loader": "8.1.0", "babel-plugin-named-asset-import": "^0.3.7", "babel-preset-react-app": "^10.0.0", "bfj": "^7.0.2", "camelcase": "^6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "css-loader": "4.3.0", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "eslint": "^7.11.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "eslint-webpack-plugin": "^2.5.2", "file-loader": "6.1.1", "fs-extra": "^9.0.1", "html-webpack-plugin": "4.5.0", "identity-obj-proxy": "3.0.0", "jest": "26.6.0", "jest-circus": "26.6.0", "jest-resolve": "26.6.0", "jest-watch-typeahead": "0.6.1", "mini-css-extract-plugin": "0.11.3", "optimize-css-assets-webpack-plugin": "5.0.4", "pnp-webpack-plugin": "1.6.4", "postcss-flexbugs-fixes": "4.2.1", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "5.0.2", "prompts": "2.4.0", "react-app-polyfill": "^2.0.0", "react-dev-utils": "^11.0.3", "react-refresh": "^0.8.3", "resolve": "1.18.1", "resolve-url-loader": "^3.1.2", "sass-loader": "^10.0.5", "semver": "7.3.2", "style-loader": "1.3.0", "terser-webpack-plugin": "4.2.3", "ts-pnp": "1.2.0", "url-loader": "4.1.1", "webpack": "4.44.2", "webpack-dev-server": "3.11.1", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "5.1.4"}, "devDependencies": {"react": "^17.0.1", "react-dom": "^17.0.1"}, "optionalDependencies": {"fsevents": "^2.1.3"}, "peerDependencies": {"react": ">= 16", "typescript": "^3.2.1 || ^4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "gitHead": "f92c37a6e6636ed1650ffd976c6881f59b1be803"}