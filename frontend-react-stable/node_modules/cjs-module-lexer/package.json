{"name": "cjs-module-lexer", "version": "0.6.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "types": "lexer.d.ts", "scripts": {"test-js": "mocha -b -u tdd test/*.js", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4"}, "files": ["dist", "lexer.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/guybedford/cjs-module-lexer.git"}, "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "homepage": "https://github.com/guybedford/cjs-module-lexer#readme"}