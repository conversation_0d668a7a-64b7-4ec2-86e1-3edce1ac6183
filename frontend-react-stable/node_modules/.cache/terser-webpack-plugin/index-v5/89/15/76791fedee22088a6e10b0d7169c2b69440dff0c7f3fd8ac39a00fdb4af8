
a88944c1f6374693c48caf61dd549b3ad07fbc9a	{"key":"{\"terser\":\"5.43.1\",\"terser-webpack-plugin\":\"4.2.3\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.[cm]?js(\\\\?.*)?$\", \"i\"),\"extractComments\":true,\"sourceMap\":true,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"parse\":{\"ecma\":8},\"compress\":{\"ecma\":5,\"warnings\":false,\"comparisons\":false,\"inline\":2},\"mangle\":{\"safari10\":true},\"keep_classnames\":false,\"keep_fnames\":false,\"output\":{\"ecma\":5,\"comments\":false,\"ascii_only\":true},\"ecma\":5}},\"name\":\"static\\u002Fjs\\u002Fmain.5a98270a.chunk.js\",\"contentHash\":\"77059ca4de58672ca842\"}","integrity":"sha512-sd+QlG5EvLBck0ZzDyw63FGJ7KyV5r1a2bBDwL3U1sNNMthk2d3pJpCyTWLPR25y/b1TWI1uyUyCgC+3xTaHyg==","time":1753857488248,"size":1112870}