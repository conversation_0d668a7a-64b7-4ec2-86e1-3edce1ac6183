
2899271ebc438501491643eb53f05c8c18820c5f	{"key":"{\"terser\":\"5.43.1\",\"terser-webpack-plugin\":\"4.2.3\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.[cm]?js(\\\\?.*)?$\", \"i\"),\"extractComments\":true,\"sourceMap\":true,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"parse\":{\"ecma\":8},\"compress\":{\"ecma\":5,\"warnings\":false,\"comparisons\":false,\"inline\":2},\"mangle\":{\"safari10\":true},\"keep_classnames\":false,\"keep_fnames\":false,\"output\":{\"ecma\":5,\"comments\":false,\"ascii_only\":true},\"ecma\":5}},\"name\":\"static\\u002Fjs\\u002F2.ff16bfd0.chunk.js\",\"contentHash\":\"55ade0ffb39d8a4796a4\"}","integrity":"sha512-WSwnX8cUaBwzk6yOHGiqlAklVo1DZ4EVGRWClWgGEKJgavKe9RT0v1nXNDjWJxGlyCw/w9FBO9G6zz2Kv31qfQ==","time":1753255500479,"size":17167543}