{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * Cache `value` related LabeledValue & options.\n */\nexport default (function (labeledValues, valueOptions) {\n  var cacheRef = React.useRef({\n    values: new Map(),\n    options: new Map()\n  });\n  var filledLabeledValues = React.useMemo(function () {\n    var _cacheRef$current = cacheRef.current,\n      prevValueCache = _cacheRef$current.values,\n      prevOptionCache = _cacheRef$current.options;\n\n    // Fill label by cache\n    var patchedValues = labeledValues.map(function (item) {\n      if (item.label === undefined) {\n        var _prevValueCache$get;\n        return _objectSpread(_objectSpread({}, item), {}, {\n          label: (_prevValueCache$get = prevValueCache.get(item.value)) === null || _prevValueCache$get === void 0 ? void 0 : _prevValueCache$get.label\n        });\n      }\n      return item;\n    });\n\n    // Refresh cache\n    var valueCache = new Map();\n    var optionCache = new Map();\n    patchedValues.forEach(function (item) {\n      valueCache.set(item.value, item);\n      optionCache.set(item.value, valueOptions.get(item.value) || prevOptionCache.get(item.value));\n    });\n    cacheRef.current.values = valueCache;\n    cacheRef.current.options = optionCache;\n    return patchedValues;\n  }, [labeledValues, valueOptions]);\n  var getOption = React.useCallback(function (val) {\n    return valueOptions.get(val) || cacheRef.current.options.get(val);\n  }, [valueOptions]);\n  return [filledLabeledValues, getOption];\n});", "map": {"version": 3, "names": ["_objectSpread", "React", "labeledV<PERSON>ues", "valueOptions", "cacheRef", "useRef", "values", "Map", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMemo", "_cacheRef$current", "current", "prev<PERSON><PERSON><PERSON><PERSON><PERSON>", "prevOptionCache", "patchedValues", "map", "item", "label", "undefined", "_prevValueCache$get", "get", "value", "valueCache", "optionCache", "for<PERSON>ach", "set", "getOption", "useCallback", "val"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/hooks/useCache.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * Cache `value` related LabeledValue & options.\n */\nexport default (function (labeledValues, valueOptions) {\n  var cacheRef = React.useRef({\n    values: new Map(),\n    options: new Map()\n  });\n  var filledLabeledValues = React.useMemo(function () {\n    var _cacheRef$current = cacheRef.current,\n      prevValueCache = _cacheRef$current.values,\n      prevOptionCache = _cacheRef$current.options;\n\n    // Fill label by cache\n    var patchedValues = labeledValues.map(function (item) {\n      if (item.label === undefined) {\n        var _prevValueCache$get;\n        return _objectSpread(_objectSpread({}, item), {}, {\n          label: (_prevValueCache$get = prevValueCache.get(item.value)) === null || _prevValueCache$get === void 0 ? void 0 : _prevValueCache$get.label\n        });\n      }\n      return item;\n    });\n\n    // Refresh cache\n    var valueCache = new Map();\n    var optionCache = new Map();\n    patchedValues.forEach(function (item) {\n      valueCache.set(item.value, item);\n      optionCache.set(item.value, valueOptions.get(item.value) || prevOptionCache.get(item.value));\n    });\n    cacheRef.current.values = valueCache;\n    cacheRef.current.options = optionCache;\n    return patchedValues;\n  }, [labeledValues, valueOptions]);\n  var getOption = React.useCallback(function (val) {\n    return valueOptions.get(val) || cacheRef.current.options.get(val);\n  }, [valueOptions]);\n  return [filledLabeledValues, getOption];\n});"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,gBAAgB,UAAUC,aAAa,EAAEC,YAAY,EAAE;EACrD,IAAIC,QAAQ,GAAGH,KAAK,CAACI,MAAM,CAAC;IAC1BC,MAAM,EAAE,IAAIC,GAAG,CAAC,CAAC;IACjBC,OAAO,EAAE,IAAID,GAAG,CAAC;EACnB,CAAC,CAAC;EACF,IAAIE,mBAAmB,GAAGR,KAAK,CAACS,OAAO,CAAC,YAAY;IAClD,IAAIC,iBAAiB,GAAGP,QAAQ,CAACQ,OAAO;MACtCC,cAAc,GAAGF,iBAAiB,CAACL,MAAM;MACzCQ,eAAe,GAAGH,iBAAiB,CAACH,OAAO;;IAE7C;IACA,IAAIO,aAAa,GAAGb,aAAa,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;MACpD,IAAIA,IAAI,CAACC,KAAK,KAAKC,SAAS,EAAE;QAC5B,IAAIC,mBAAmB;QACvB,OAAOpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAChDC,KAAK,EAAE,CAACE,mBAAmB,GAAGP,cAAc,CAACQ,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,MAAM,IAAI,IAAIF,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACF;QAC1I,CAAC,CAAC;MACJ;MACA,OAAOD,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,IAAIM,UAAU,GAAG,IAAIhB,GAAG,CAAC,CAAC;IAC1B,IAAIiB,WAAW,GAAG,IAAIjB,GAAG,CAAC,CAAC;IAC3BQ,aAAa,CAACU,OAAO,CAAC,UAAUR,IAAI,EAAE;MACpCM,UAAU,CAACG,GAAG,CAACT,IAAI,CAACK,KAAK,EAAEL,IAAI,CAAC;MAChCO,WAAW,CAACE,GAAG,CAACT,IAAI,CAACK,KAAK,EAAEnB,YAAY,CAACkB,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,IAAIR,eAAe,CAACO,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,CAAC;IAC9F,CAAC,CAAC;IACFlB,QAAQ,CAACQ,OAAO,CAACN,MAAM,GAAGiB,UAAU;IACpCnB,QAAQ,CAACQ,OAAO,CAACJ,OAAO,GAAGgB,WAAW;IACtC,OAAOT,aAAa;EACtB,CAAC,EAAE,CAACb,aAAa,EAAEC,YAAY,CAAC,CAAC;EACjC,IAAIwB,SAAS,GAAG1B,KAAK,CAAC2B,WAAW,CAAC,UAAUC,GAAG,EAAE;IAC/C,OAAO1B,YAAY,CAACkB,GAAG,CAACQ,GAAG,CAAC,IAAIzB,QAAQ,CAACQ,OAAO,CAACJ,OAAO,CAACa,GAAG,CAACQ,GAAG,CAAC;EACnE,CAAC,EAAE,CAAC1B,YAAY,CAAC,CAAC;EAClB,OAAO,CAACM,mBAAmB,EAAEkB,SAAS,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}