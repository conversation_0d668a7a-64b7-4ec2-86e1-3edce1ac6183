{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"style\", \"render\", \"dragging\", \"onOffsetChange\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport SliderContext from '../context';\nimport { getDirectionStyle, getIndex } from '../util';\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _getIndex;\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    onOffsetChange = props.onOffsetChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\"); // ============================ Events ============================\n\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  }; // =========================== Keyboard ===========================\n\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled) {\n      var offset = null; // Change the value\n\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n        // Up is plus\n\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n        // Down is minus\n\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  }; // ============================ Offset ============================\n\n  var positionStyle = getDirectionStyle(direction, value, min, max); // ============================ Render ============================\n\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: classNames(handlePrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), range), _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-dragging\"), dragging), _classNames)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove,\n    onKeyDown: onKeyDown,\n    tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n    role: \"slider\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled,\n    \"aria-label\": getIndex(ariaLabelForHandle, valueIndex),\n    \"aria-labelledby\": getIndex(ariaLabelledByForHandle, valueIndex),\n    \"aria-valuetext\": (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value)\n  }, restProps)); // Customize\n\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_objectWithoutProperties", "_excluded", "React", "classNames", "KeyCode", "SliderContext", "getDirectionStyle", "getIndex", "<PERSON><PERSON>", "forwardRef", "props", "ref", "_classNames", "_getIndex", "prefixCls", "value", "valueIndex", "onStartMove", "style", "render", "dragging", "onOffsetChange", "restProps", "_React$useContext", "useContext", "min", "max", "direction", "disabled", "range", "tabIndex", "ariaLabelFor<PERSON>andle", "ariaLabelledByForHandle", "ariaValueTextFormatterForHandle", "handlePrefixCls", "concat", "onInternalStartMove", "e", "onKeyDown", "offset", "which", "keyCode", "LEFT", "RIGHT", "UP", "DOWN", "HOME", "END", "PAGE_UP", "PAGE_DOWN", "preventDefault", "positionStyle", "handleNode", "createElement", "className", "onMouseDown", "onTouchStart", "role", "index", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/Handles/Handle.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"style\", \"render\", \"dragging\", \"onOffsetChange\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport SliderContext from '../context';\nimport { getDirectionStyle, getIndex } from '../util';\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _getIndex;\n\n  var prefixCls = props.prefixCls,\n      value = props.value,\n      valueIndex = props.valueIndex,\n      onStartMove = props.onStartMove,\n      style = props.style,\n      render = props.render,\n      dragging = props.dragging,\n      onOffsetChange = props.onOffsetChange,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var _React$useContext = React.useContext(SliderContext),\n      min = _React$useContext.min,\n      max = _React$useContext.max,\n      direction = _React$useContext.direction,\n      disabled = _React$useContext.disabled,\n      range = _React$useContext.range,\n      tabIndex = _React$useContext.tabIndex,\n      ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n      ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n      ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle;\n\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\"); // ============================ Events ============================\n\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  }; // =========================== Keyboard ===========================\n\n\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled) {\n      var offset = null; // Change the value\n\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n        // Up is plus\n\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n        // Down is minus\n\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n\n        case KeyCode.END:\n          offset = 'max';\n          break;\n\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n      }\n\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  }; // ============================ Offset ============================\n\n\n  var positionStyle = getDirectionStyle(direction, value, min, max); // ============================ Render ============================\n\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: classNames(handlePrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), range), _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-dragging\"), dragging), _classNames)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove,\n    onKeyDown: onKeyDown,\n    tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n    role: \"slider\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled,\n    \"aria-label\": getIndex(ariaLabelForHandle, valueIndex),\n    \"aria-labelledby\": getIndex(ariaLabelledByForHandle, valueIndex),\n    \"aria-valuetext\": (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value)\n  }, restProps)); // Customize\n\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging\n    });\n  }\n\n  return handleNode;\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\n\nexport default Handle;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC;AACpH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,SAAS;AACrD,IAAIC,MAAM,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,WAAW,EAAEC,SAAS;EAE1B,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,MAAM,GAAGT,KAAK,CAACS,MAAM;IACrBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,cAAc,GAAGX,KAAK,CAACW,cAAc;IACrCC,SAAS,GAAGtB,wBAAwB,CAACU,KAAK,EAAET,SAAS,CAAC;EAE1D,IAAIsB,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,aAAa,CAAC;IACnDoB,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;IACvCC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,KAAK,GAAGN,iBAAiB,CAACM,KAAK;IAC/BC,QAAQ,GAAGP,iBAAiB,CAACO,QAAQ;IACrCC,kBAAkB,GAAGR,iBAAiB,CAACQ,kBAAkB;IACzDC,uBAAuB,GAAGT,iBAAiB,CAACS,uBAAuB;IACnEC,+BAA+B,GAAGV,iBAAiB,CAACU,+BAA+B;EAEvF,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACrB,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;;EAEvD,IAAIsB,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,CAAC,EAAE;IACxD,IAAI,CAACT,QAAQ,EAAE;MACbX,WAAW,CAACoB,CAAC,EAAErB,UAAU,CAAC;IAC5B;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIsB,SAAS,GAAG,SAASA,SAASA,CAACD,CAAC,EAAE;IACpC,IAAI,CAACT,QAAQ,EAAE;MACb,IAAIW,MAAM,GAAG,IAAI,CAAC,CAAC;;MAEnB,QAAQF,CAAC,CAACG,KAAK,IAAIH,CAAC,CAACI,OAAO;QAC1B,KAAKrC,OAAO,CAACsC,IAAI;UACfH,MAAM,GAAGZ,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;UAC5D;QAEF,KAAKvB,OAAO,CAACuC,KAAK;UAChBJ,MAAM,GAAGZ,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UAC5D;QACF;;QAEA,KAAKvB,OAAO,CAACwC,EAAE;UACbL,MAAM,GAAGZ,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UACrC;QACF;;QAEA,KAAKvB,OAAO,CAACyC,IAAI;UACfN,MAAM,GAAGZ,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;UACrC;QAEF,KAAKvB,OAAO,CAAC0C,IAAI;UACfP,MAAM,GAAG,KAAK;UACd;QAEF,KAAKnC,OAAO,CAAC2C,GAAG;UACdR,MAAM,GAAG,KAAK;UACd;QAEF,KAAKnC,OAAO,CAAC4C,OAAO;UAClBT,MAAM,GAAG,CAAC;UACV;QAEF,KAAKnC,OAAO,CAAC6C,SAAS;UACpBV,MAAM,GAAG,CAAC,CAAC;UACX;MACJ;MAEA,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnBF,CAAC,CAACa,cAAc,CAAC,CAAC;QAClB7B,cAAc,CAACkB,MAAM,EAAEvB,UAAU,CAAC;MACpC;IACF;EACF,CAAC,CAAC,CAAC;;EAGH,IAAImC,aAAa,GAAG7C,iBAAiB,CAACqB,SAAS,EAAEZ,KAAK,EAAEU,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;;EAEnE,IAAI0B,UAAU,GAAG,aAAalD,KAAK,CAACmD,aAAa,CAAC,KAAK,EAAExD,QAAQ,CAAC;IAChEc,GAAG,EAAEA,GAAG;IACR2C,SAAS,EAAEnD,UAAU,CAAC+B,eAAe,GAAGtB,WAAW,GAAG,CAAC,CAAC,EAAEb,eAAe,CAACa,WAAW,EAAE,EAAE,CAACuB,MAAM,CAACD,eAAe,EAAE,GAAG,CAAC,CAACC,MAAM,CAACnB,UAAU,GAAG,CAAC,CAAC,EAAEa,KAAK,CAAC,EAAE9B,eAAe,CAACa,WAAW,EAAE,EAAE,CAACuB,MAAM,CAACD,eAAe,EAAE,WAAW,CAAC,EAAEd,QAAQ,CAAC,EAAER,WAAW,CAAC,CAAC;IACrPM,KAAK,EAAEpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,aAAa,CAAC,EAAEjC,KAAK,CAAC;IAC7DqC,WAAW,EAAEnB,mBAAmB;IAChCoB,YAAY,EAAEpB,mBAAmB;IACjCE,SAAS,EAAEA,SAAS;IACpBR,QAAQ,EAAEF,QAAQ,GAAG,IAAI,GAAGrB,QAAQ,CAACuB,QAAQ,EAAEd,UAAU,CAAC;IAC1DyC,IAAI,EAAE,QAAQ;IACd,eAAe,EAAEhC,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAEX,KAAK;IACtB,eAAe,EAAEa,QAAQ;IACzB,YAAY,EAAErB,QAAQ,CAACwB,kBAAkB,EAAEf,UAAU,CAAC;IACtD,iBAAiB,EAAET,QAAQ,CAACyB,uBAAuB,EAAEhB,UAAU,CAAC;IAChE,gBAAgB,EAAE,CAACH,SAAS,GAAGN,QAAQ,CAAC0B,+BAA+B,EAAEjB,UAAU,CAAC,MAAM,IAAI,IAAIH,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK;EACnJ,CAAC,EAAEO,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAIH,MAAM,EAAE;IACViC,UAAU,GAAGjC,MAAM,CAACiC,UAAU,EAAE;MAC9BM,KAAK,EAAE1C,UAAU;MACjBF,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZK,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;EAEA,OAAOgC,UAAU;AACnB,CAAC,CAAC;AAEF,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrD,MAAM,CAACsD,WAAW,GAAG,QAAQ;AAC/B;AAEA,eAAetD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}