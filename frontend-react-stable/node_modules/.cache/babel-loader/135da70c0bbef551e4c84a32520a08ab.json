{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Radio,Upload,Input,Select,Button,Typography,Space,Divider,message,Spin,Alert,Statistic,Row,Col,Progress}from'antd';import{InboxOutlined,PlayCircleOutlined}from'@ant-design/icons';import{modelPredictionAPI}from'../services/api';import useTaskManager from'../hooks/useTaskManager';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Dragger}=Upload;const{Option}=Select;// 预测结果展示组件\nconst PredictionResultDisplay=_ref=>{let{result}=_ref;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Row,{gutter:16,style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",value:result.suggested_threshold,precision:2,valueStyle:{color:'#1890ff'}}),result.suggested_threshold&&/*#__PURE__*/_jsx(Alert,{message:\"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",type:\"success\",showIcon:true,style:{marginTop:8}})]})}),(result.duration_seconds!==undefined||result.cpu_percent!==undefined||result.memory_mb!==undefined||result.gpu_memory_mb!==undefined||result.gpu_utilization_percent!==undefined)&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Title,{level:5,children:\"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[result.duration_seconds!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9884\\u6D4B\\u8017\\u65F6\",value:result.duration_seconds,precision:2,suffix:\"\\u79D2\",valueStyle:{color:'#1890ff'}})}),result.cpu_percent!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"CPU\\u4F7F\\u7528\\u7387\",value:result.cpu_percent,precision:1,suffix:\"%\",valueStyle:{color:'#52c41a'}})}),result.memory_mb!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5185\\u5B58\\u4F7F\\u7528\",value:result.memory_mb,precision:1,suffix:\"MB\",valueStyle:{color:'#fa8c16'}})}),result.gpu_memory_mb!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u5185\\u5B58\",value:result.gpu_memory_mb,precision:1,suffix:\"MB\",valueStyle:{color:'#722ed1'}})}),result.gpu_utilization_percent!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u5229\\u7528\\u7387\",value:result.gpu_utilization_percent,precision:1,suffix:\"%\",valueStyle:{color:'#eb2f96'}})})]})]})]});};// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions=['TCP','UDP','ICMP'];const datatypeOptions={TCP:['spt_sip_dip','dpt_sip_dip','len_dpt_syn','seq_ack_dip'],UDP:['spt_sip_dip','dpt_sip_dip'],ICMP:['dip']};const ModelPredictionPage=()=>{const[dataSource,setDataSource]=useState('local');const[uploadedFile,setUploadedFile]=useState(null);const[csvDir,setCsvDir]=useState('');const[availableCsvFiles,setAvailableCsvFiles]=useState([]);const[selectedCsvFile,setSelectedCsvFile]=useState('');const[csvFilesLoading,setCsvFilesLoading]=useState(false);// 模型相关状态\nconst[modelDir,setModelDir]=useState('');const[availablePthFiles,setAvailablePthFiles]=useState([]);const[selectedModels,setSelectedModels]=useState([]);const[modelsLoading,setModelsLoading]=useState(false);const[predictionMode,setPredictionMode]=useState('single');// 单模型选择状态\nconst[selectedModelFile,setSelectedModelFile]=useState('');const[selectedParamsFile,setSelectedParamsFile]=useState('');const[selectedScalerFile,setSelectedScalerFile]=useState('');const[selectedProt,setSelectedProt]=useState('');const[selectedDatatype,setSelectedDatatype]=useState('');const[showManualSelection,setShowManualSelection]=useState(false);// 预测状态\nconst[predicting,setPredicting]=useState(false);const[progress,setProgress]=useState(0);const[results,setResults]=useState([]);const[selectedResultIndex,setSelectedResultIndex]=useState(0);const[matchingFilesLoading,setMatchingFilesLoading]=useState(false);// 任务管理\nconst{submitPredictionTask,getCompletedTasksByType,fetchCompletedTasks}=useTaskManager();const[useAsyncPrediction,setUseAsyncPrediction]=useState(true);// 默认使用异步预测\n// 异步任务结果状态\nconst[asyncPredictionResults,setAsyncPredictionResults]=useState([]);const[selectedAsyncTaskId,setSelectedAsyncTaskId]=useState('');// 获取已完成的预测任务\nconst completedPredictionTasks=getCompletedTasksByType('prediction');// 处理异步任务选择\nconst handleAsyncTaskSelect=taskId=>{setSelectedAsyncTaskId(taskId);const selectedTask=completedPredictionTasks.find(task=>task.task_id===taskId);if(selectedTask&&selectedTask.result){// 转换异步预测结果为简化格式\nconst asyncResult={suggested_threshold:selectedTask.result.suggested_threshold||0,model_name:selectedTask.result.model_name||'未知模型',message:selectedTask.result.message||'预测完成',duration_seconds:selectedTask.result.duration_seconds,cpu_percent:selectedTask.result.cpu_percent,memory_mb:selectedTask.result.memory_mb,gpu_memory_mb:selectedTask.result.gpu_memory_mb,gpu_utilization_percent:selectedTask.result.gpu_utilization_percent};setAsyncPredictionResults([asyncResult]);}};// 页面加载时获取已完成任务\nuseEffect(()=>{fetchCompletedTasks();},[fetchCompletedTasks]);// 自动选择最新的预测任务\nuseEffect(()=>{if(completedPredictionTasks.length>0&&!selectedAsyncTaskId){const latestTask=completedPredictionTasks[completedPredictionTasks.length-1];handleAsyncTaskSelect(latestTask.task_id);}},[completedPredictionTasks,selectedAsyncTaskId]);// 获取CSV文件列表\nconst fetchCsvFiles=async()=>{if(!csvDir)return;setCsvFilesLoading(true);try{const response=await modelPredictionAPI.listCsvFiles(csvDir);setAvailableCsvFiles(response.data.files||[]);}catch(error){var _error$response,_error$response$data;message.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'获取CSV文件列表失败');setAvailableCsvFiles([]);}finally{setCsvFilesLoading(false);}};// 获取模型文件列表\nconst fetchModelFiles=async()=>{if(!modelDir)return;setModelsLoading(true);try{const response=await modelPredictionAPI.listModelFiles(modelDir);setAvailablePthFiles(response.data.pth_files||[]);}catch(error){var _error$response2,_error$response2$data;message.error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||'获取模型文件列表失败');setAvailablePthFiles([]);}finally{setModelsLoading(false);}};// 自动匹配参数和标准化器文件（与Streamlit版本一致）\nconst autoMatchFiles=async modelFile=>{if(!modelFile||!modelDir)return;setMatchingFilesLoading(true);try{// 调用后端API获取匹配的文件和协议信息\nconst response=await modelPredictionAPI.getMatchingFiles(modelFile,modelDir);if(response.data){const matchingFiles=response.data;// 设置自动匹配的文件\nsetSelectedParamsFile(matchingFiles.params_filename||'');setSelectedScalerFile(matchingFiles.scaler_filename||'');setSelectedProt(matchingFiles.protocol||'');setSelectedDatatype(matchingFiles.datatype||'');// 显示匹配结果\nif(matchingFiles.params_filename&&matchingFiles.scaler_filename){message.success('✅ 已自动匹配相关文件');}if(matchingFiles.protocol&&matchingFiles.datatype){message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);setShowManualSelection(false);}else{message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');setShowManualSelection(true);}}}catch(error){var _error$response3,_error$response3$data;message.error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||'获取匹配文件失败');// 如果API调用失败，回退到简单的文件名解析\nconst baseNameWithoutExt=modelFile.replace('_model_best.pth','');setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);setShowManualSelection(true);}finally{setMatchingFilesLoading(false);}};// 处理模型文件选择\nconst handleModelFileChange=modelFile=>{setSelectedModelFile(modelFile);// 重置之前的选择\nsetSelectedParamsFile('');setSelectedScalerFile('');setSelectedProt('');setSelectedDatatype('');setShowManualSelection(false);// 异步调用自动匹配\nautoMatchFiles(modelFile);};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(dataSource==='local'&&csvDir&&csvDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchCsvFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[dataSource,csvDir]);useEffect(()=>{if(modelDir&&modelDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchModelFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[modelDir]);// 文件上传配置\nconst uploadProps={name:'file',multiple:false,accept:'.csv',beforeUpload:()=>false,onChange:info=>{if(info.fileList.length>0){setUploadedFile(info.fileList[0]);}else{setUploadedFile(null);}}};// 开始预测\nconst handleStartPrediction=async()=>{// 验证输入\nif(dataSource==='upload'&&!uploadedFile){message.error('请上传CSV文件');return;}if(dataSource==='local'&&(!csvDir||!selectedCsvFile)){message.error('请选择CSV文件');return;}// 验证模型选择\nlet modelsToPredict=[];if(predictionMode==='single'){if(!selectedModelFile||!selectedProt||!selectedDatatype){message.error('请选择模型文件并确保协议和数据类型已设置');return;}modelsToPredict=[{model_file:selectedModelFile,params_file:selectedParamsFile,scaler_file:selectedScalerFile,protocol:selectedProt,datatype:selectedDatatype}];}else{if(selectedModels.length===0){message.error('请至少选择一个模型');return;}// 为每个选中的模型获取匹配信息（与Streamlit版本一致）\nconst validModels=[];for(const modelFile of selectedModels){try{const response=await modelPredictionAPI.getMatchingFiles(modelFile,modelDir);if(response.data){const matchingFiles=response.data;const params_file=matchingFiles.params_filename;const scaler_file=matchingFiles.scaler_filename;const protocol=matchingFiles.protocol;const datatype=matchingFiles.datatype;// 只有当所有必要信息都可用时，才添加到选中模型列表\nif(params_file&&scaler_file&&protocol&&datatype){validModels.push({model_file:modelFile,params_file,scaler_file,protocol,datatype});message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);}else{message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);}}else{message.error(`获取模型 ${modelFile} 的匹配文件失败`);}}catch(error){var _error$response4,_error$response4$data;message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message}`);}}if(validModels.length===0){message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');return;}modelsToPredict=validModels;}setPredicting(true);setProgress(0);setResults([]);try{if(useAsyncPrediction&&predictionMode==='single'){// 只有单模型预测支持异步模式\nconst formData=new FormData();if(dataSource==='upload'&&uploadedFile){formData.append('file',uploadedFile.originFileObj);}else{formData.append('csv_dir',csvDir);formData.append('selected_file',selectedCsvFile);}formData.append('model_filename',selectedModelFile);formData.append('params_filename',selectedParamsFile);formData.append('scaler_filename',selectedScalerFile);formData.append('selected_prot',selectedProt);formData.append('selected_datatype',selectedDatatype);formData.append('model_dir',modelDir);formData.append('output_folder',modelDir);// 提交异步任务\nconst taskId=await submitPredictionTask(formData);if(taskId){message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');// 重置状态\nsetPredicting(false);setProgress(0);}return;// 异步模式下直接返回\n}// 同步预测模式（保留原有逻辑）\nconst allResults=[];for(let i=0;i<modelsToPredict.length;i++){const model=modelsToPredict[i];// 更新进度\nsetProgress(Math.round(i/modelsToPredict.length*90));if(modelsToPredict.length>1){message.info(`正在使用模型 ${i+1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);}const formData=new FormData();if(dataSource==='upload'&&uploadedFile){// 重新读取文件内容，因为文件内容只能读取一次\nformData.append('file',uploadedFile.originFileObj);}else{formData.append('csv_dir',csvDir);formData.append('selected_file',selectedCsvFile);}formData.append('model_filename',model.model_file);formData.append('params_filename',model.params_file);formData.append('scaler_filename',model.scaler_file);formData.append('selected_prot',model.protocol);formData.append('selected_datatype',model.datatype);formData.append('output_folder',modelDir);const response=await modelPredictionAPI.predict(formData);if(response.data){allResults.push({model_name:response.data.model_name||`${model.protocol}_${model.datatype}`,suggested_threshold:response.data.suggested_threshold||0,message:response.data.message||'预测完成',// 添加资源监控信息\nduration_seconds:response.data.duration_seconds,cpu_percent:response.data.cpu_percent,memory_mb:response.data.memory_mb,gpu_memory_mb:response.data.gpu_memory_mb,gpu_utilization_percent:response.data.gpu_utilization_percent});if(modelsToPredict.length>1){message.success(`✅ 模型 ${model.model_file} 预测成功`);}}}setProgress(100);setResults(allResults);message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);}catch(error){var _error$response5,_error$response5$data;message.error(((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||'预测失败');}finally{setPredicting(false);}};const isFormValid=()=>{const hasData=dataSource==='upload'?uploadedFile:csvDir&&selectedCsvFile;if(predictionMode==='single'){return hasData&&selectedModelFile&&selectedProt&&selectedDatatype;}else{return hasData&&selectedModels.length>0;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:dataSource,onChange:e=>setDataSource(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"local\",children:\"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Radio,{value:\"upload\",children:\"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"})]})]}),dataSource==='local'&&/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:csvDir,onChange:e=>setCsvDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchCsvFiles,loading:csvFilesLoading,disabled:!csvDir,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:csvFilesLoading,children:/*#__PURE__*/_jsx(Select,{value:selectedCsvFile,onChange:setSelectedCsvFile,placeholder:\"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:csvFilesLoading,children:availableCsvFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]})]}),dataSource==='upload'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsxs(Dragger,{...uploadProps,style:{marginTop:8},children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(InboxOutlined,{})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"})]})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6A21\\u578B\\u9009\\u62E9\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:modelDir,onChange:e=>setModelDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchModelFiles,loading:modelsLoading,disabled:!modelDir,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:predictionMode,onChange:e=>setPredictionMode(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"single\",children:\"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"}),/*#__PURE__*/_jsx(Radio,{value:\"multiple\",children:\"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"})]})]}),predictionMode==='single'?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:modelsLoading,children:/*#__PURE__*/_jsx(Select,{value:selectedModelFile,onChange:handleModelFileChange,placeholder:\"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:modelsLoading,children:availablePthFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})}),selectedModelFile&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:matchingFilesLoading,children:/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8,padding:12,backgroundColor:'#f5f5f5',borderRadius:4},children:matchingFilesLoading?/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u53C2\\u6570\\u6587\\u4EF6:\"}),\" \",selectedParamsFile||'未匹配']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"}),\" \",selectedScalerFile||'未匹配']}),!showManualSelection&&selectedProt&&selectedDatatype&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u534F\\u8BAE:\"}),\" \",selectedProt]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6570\\u636E\\u7C7B\\u578B:\"}),\" \",selectedDatatype]})]})]})})})]}),showManualSelection&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Select,{value:selectedProt,onChange:setSelectedProt,placeholder:\"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",style:{width:'100%'},children:protocolOptions.map(prot=>/*#__PURE__*/_jsx(Option,{value:prot,children:prot},prot))}),selectedProt&&/*#__PURE__*/_jsx(Select,{value:selectedDatatype,onChange:setSelectedDatatype,placeholder:`选择与模型对应的 ${selectedProt} 数据类型`,style:{width:'100%'},children:(datatypeOptions[selectedProt]||[]).map(datatype=>/*#__PURE__*/_jsx(Option,{value:datatype,children:datatype},datatype))})]})})]})]})})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:modelsLoading,children:/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedModels,onChange:setSelectedModels,placeholder:\"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",style:{width:'100%',marginTop:8},loading:modelsLoading,children:availablePthFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]}),availablePthFiles.length===0&&!modelsLoading&&/*#__PURE__*/_jsx(Alert,{message:\"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",description:\"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",type:\"warning\",showIcon:true})]})}),/*#__PURE__*/_jsx(Card,{className:\"function-card\",title:\"\\u9884\\u6D4B\\u6A21\\u5F0F\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:useAsyncPrediction,onChange:e=>setUseAsyncPrediction(e.target.value),style:{marginTop:8},disabled:predictionMode==='multiple'// 多模型预测暂不支持异步\n,children:[/*#__PURE__*/_jsx(Radio,{value:true,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"})]})}),/*#__PURE__*/_jsx(Radio,{value:false,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u540C\\u6B65\\u9884\\u6D4B\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"})]})})]})]}),useAsyncPrediction&&predictionMode==='single'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",description:/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \",/*#__PURE__*/_jsx(\"strong\",{children:\"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"}),\" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]}),type:\"info\",showIcon:true}),predictionMode==='multiple'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\",description:\"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\\u76EE\\u524D\\u4EC5\\u652F\\u6301\\u540C\\u6B65\\u6A21\\u5F0F\\uFF0C\\u9884\\u6D4B\\u8FC7\\u7A0B\\u4E2D\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",type:\"warning\",showIcon:true})]})}),/*#__PURE__*/_jsxs(Card,{className:\"function-card\",children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleStartPrediction,loading:predicting,disabled:!isFormValid(),className:\"action-button\",children:predicting?'正在预测...':'开始预测与检测'}),predicting&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsx(Text,{children:\"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:progress,status:\"active\"})]})]}),results.length>0&&/*#__PURE__*/_jsx(Card,{title:\"\\u9884\\u6D4B\\u7ED3\\u679C\",className:\"function-card\",children:results.length>1?/*#__PURE__*/// 多模型结果展示 - 与Streamlit版本一致\n_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Title,{level:4,children:\"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:{width:'100%',marginTop:8},placeholder:\"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",value:selectedResultIndex,onChange:value=>setSelectedResultIndex(value),children:results.map((result,index)=>/*#__PURE__*/_jsx(Option,{value:index,children:result.model_name},index))})]}),results[selectedResultIndex]&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Title,{level:5,children:[\"\\u6A21\\u578B: \",results[selectedResultIndex].model_name]}),/*#__PURE__*/_jsx(PredictionResultDisplay,{result:results[selectedResultIndex]})]})]}):/*#__PURE__*/// 单模型结果展示\n_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Title,{level:4,children:[\"\\u9884\\u6D4B\\u7ED3\\u679C - \",results[0].model_name]}),/*#__PURE__*/_jsx(PredictionResultDisplay,{result:results[0]})]})}),completedPredictionTasks.length>0&&/*#__PURE__*/_jsxs(Card,{title:\"\\u5F02\\u6B65\\u9884\\u6D4B\\u7ED3\\u679C\",className:\"function-card\",style:{marginTop:24},children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5F02\\u6B65\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\",description:\"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u9884\\u6D4B\\u6570\\u636E\\u548C\\u5F02\\u5E38\\u68C0\\u6D4B\\u62A5\\u544A\\u3002\",type:\"success\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u9884\\u6D4B\\u4EFB\\u52A1\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{value:selectedAsyncTaskId,onChange:handleAsyncTaskSelect,style:{width:'100%',marginTop:8},placeholder:\"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u9884\\u6D4B\\u4EFB\\u52A1\",children:completedPredictionTasks.map(task=>/*#__PURE__*/_jsx(Option,{value:task.task_id,children:task.task_id.includes('_')?`${task.task_id.split('_')[0]} (${new Date(task.updated_at||task.created_at).toLocaleString()})`:`任务 ${task.task_id.substring(0,8)}... (${new Date(task.updated_at||task.created_at).toLocaleString()})`},task.task_id))})]}),asyncPredictionResults.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Title,{level:4,children:[\"\\u9884\\u6D4B\\u7ED3\\u679C - \",asyncPredictionResults[0].model_name]}),/*#__PURE__*/_jsx(PredictionResultDisplay,{result:asyncPredictionResults[0]})]})]})]})]});};export default ModelPredictionPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "modelPredictionAPI", "useTaskManager", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "_ref", "result", "children", "gutter", "style", "marginBottom", "span", "title", "value", "suggested_threshold", "precision", "valueStyle", "color", "type", "showIcon", "marginTop", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "submitPredictionTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedPredictionTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "asyncResult", "model_name", "length", "latestTask", "fetchCsvFiles", "response", "listCsvFiles", "data", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "allResults", "i", "model", "Math", "round", "predict", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "fontSize", "fontWeight", "className", "direction", "size", "width", "strong", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "map", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status", "index", "includes", "split", "Date", "updated_at", "created_at", "toLocaleString", "substring"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\n\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n\n      </Row>\n\n\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  suggested_threshold: number;\n  message?: string;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const { submitPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为简化格式\n      const asyncResult: PredictionResult = {\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        message: selectedTask.result.message || '预测完成',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测数据和异常检测报告。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                <PredictionResultDisplay result={asyncPredictionResults[0]} />\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,KAAK,CACLC,SAAS,CACTC,GAAG,CACHC,GAAG,CACHC,QAAQ,KACH,MAAM,CACb,OAASC,aAAa,CAAEC,kBAAkB,KAAQ,mBAAmB,CAErE,OAASC,kBAAkB,KAAQ,iBAAiB,CACpD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGrB,UAAU,CAClC,KAAM,CAAEsB,OAAQ,CAAC,CAAG1B,MAAM,CAC1B,KAAM,CAAE2B,MAAO,CAAC,CAAGzB,MAAM,CAEzB;AACA,KAAM,CAAA0B,uBAA+D,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CAGjF,mBACER,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACR,GAAG,EAACqB,MAAM,CAAE,EAAG,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,cAC3CV,KAAA,CAACT,GAAG,EAACuB,IAAI,CAAE,EAAG,CAAAJ,QAAA,eACZZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,8DAAiB,CACvBC,KAAK,CAAEP,MAAM,CAACQ,mBAAoB,CAClCC,SAAS,CAAE,CAAE,CACbC,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACDX,MAAM,CAACQ,mBAAmB,eACzBnB,IAAA,CAACV,KAAK,EACJF,OAAO,CAAC,kNAAwC,CAChDmC,IAAI,CAAC,SAAS,CACdC,QAAQ,MACRV,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CACzB,CACF,EACE,CAAC,CAEH,CAAC,CAKL,CAACd,MAAM,CAACe,gBAAgB,GAAKC,SAAS,EAAIhB,MAAM,CAACiB,WAAW,GAAKD,SAAS,EAAIhB,MAAM,CAACkB,SAAS,GAAKF,SAAS,EAAIhB,MAAM,CAACmB,aAAa,GAAKH,SAAS,EAAIhB,MAAM,CAACoB,uBAAuB,GAAKJ,SAAS,gBACjMzB,KAAA,QAAKY,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eAC/BZ,IAAA,CAACK,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,sCAAM,CAAO,CAAC,cAC/BV,KAAA,CAACV,GAAG,EAACqB,MAAM,CAAE,EAAG,CAAAD,QAAA,EACbD,MAAM,CAACe,gBAAgB,GAAKC,SAAS,eACpC3B,IAAA,CAACP,GAAG,EAACwC,IAAI,CAAC,MAAM,CAAArB,QAAA,cACdZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAEP,MAAM,CAACe,gBAAiB,CAC/BN,SAAS,CAAE,CAAE,CACbc,MAAM,CAAC,QAAG,CACVb,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACiB,WAAW,GAAKD,SAAS,eAC/B3B,IAAA,CAACP,GAAG,EAACwC,IAAI,CAAC,MAAM,CAAArB,QAAA,cACdZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,uBAAQ,CACdC,KAAK,CAAEP,MAAM,CAACiB,WAAY,CAC1BR,SAAS,CAAE,CAAE,CACbc,MAAM,CAAC,GAAG,CACVb,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACkB,SAAS,GAAKF,SAAS,eAC7B3B,IAAA,CAACP,GAAG,EAACwC,IAAI,CAAC,MAAM,CAAArB,QAAA,cACdZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAEP,MAAM,CAACkB,SAAU,CACxBT,SAAS,CAAE,CAAE,CACbc,MAAM,CAAC,IAAI,CACXb,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACmB,aAAa,GAAKH,SAAS,eACjC3B,IAAA,CAACP,GAAG,EAACwC,IAAI,CAAC,MAAM,CAAArB,QAAA,cACdZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAEP,MAAM,CAACmB,aAAc,CAC5BV,SAAS,CAAE,CAAE,CACbc,MAAM,CAAC,IAAI,CACXb,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACoB,uBAAuB,GAAKJ,SAAS,eAC3C3B,IAAA,CAACP,GAAG,EAACwC,IAAI,CAAC,MAAM,CAAArB,QAAA,cACdZ,IAAA,CAACT,SAAS,EACR0B,KAAK,CAAC,uBAAQ,CACdC,KAAK,CAAEP,MAAM,CAACoB,uBAAwB,CACtCX,SAAS,CAAE,CAAE,CACbc,MAAM,CAAC,GAAG,CACVb,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,EACE,CAAC,EACH,CACN,EAGE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAa,eAAe,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CAC9C,KAAM,CAAAC,eAAe,CAAG,CACtBC,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CACjEC,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAC,CACnCC,IAAI,CAAE,CAAC,KAAK,CACd,CAAC,CAcD,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGjE,QAAQ,CAAqB,OAAO,CAAC,CACzE,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAM,IAAI,CAAC,CAC3D,KAAM,CAACoE,MAAM,CAAEC,SAAS,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACsE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvE,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAACwE,eAAe,CAAEC,kBAAkB,CAAC,CAAGzE,QAAQ,CAAS,EAAE,CAAC,CAClE,KAAM,CAAC0E,eAAe,CAAEC,kBAAkB,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC4E,QAAQ,CAAEC,WAAW,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC8E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/E,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAACgF,cAAc,CAAEC,iBAAiB,CAAC,CAAGjF,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAACkF,aAAa,CAAEC,gBAAgB,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoF,cAAc,CAAEC,iBAAiB,CAAC,CAAGrF,QAAQ,CAAwB,QAAQ,CAAC,CAErF;AACA,KAAM,CAACsF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvF,QAAQ,CAAS,EAAE,CAAC,CACtE,KAAM,CAACwF,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzF,QAAQ,CAAS,EAAE,CAAC,CACxE,KAAM,CAAC0F,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3F,QAAQ,CAAS,EAAE,CAAC,CACxE,KAAM,CAAC4F,YAAY,CAAEC,eAAe,CAAC,CAAG7F,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC8F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/F,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAACgG,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGjG,QAAQ,CAAC,KAAK,CAAC,CAErE;AACA,KAAM,CAACkG,UAAU,CAAEC,aAAa,CAAC,CAAGnG,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACoG,QAAQ,CAAEC,WAAW,CAAC,CAAGrG,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACsG,OAAO,CAAEC,UAAU,CAAC,CAAGvG,QAAQ,CAAqB,EAAE,CAAC,CAC9D,KAAM,CAACwG,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzG,QAAQ,CAAS,CAAC,CAAC,CACzE,KAAM,CAAC0G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3G,QAAQ,CAAC,KAAK,CAAC,CAEvE;AACA,KAAM,CAAE4G,oBAAoB,CAAEC,uBAAuB,CAAEC,mBAAoB,CAAC,CAAGzF,cAAc,CAAC,CAAC,CAC/F,KAAM,CAAC0F,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGhH,QAAQ,CAAC,IAAI,CAAC,CAAE;AAEpE;AACA,KAAM,CAACiH,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGlH,QAAQ,CAAqB,EAAE,CAAC,CAC5F,KAAM,CAACmH,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpH,QAAQ,CAAS,EAAE,CAAC,CAE1E;AACA,KAAM,CAAAqH,wBAAwB,CAAGR,uBAAuB,CAAC,YAAY,CAAC,CAEtE;AACA,KAAM,CAAAS,qBAAqB,CAAIC,MAAc,EAAK,CAChDH,sBAAsB,CAACG,MAAM,CAAC,CAC9B,KAAM,CAAAC,YAAY,CAAGH,wBAAwB,CAACI,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,OAAO,GAAKJ,MAAM,CAAC,CAEnF,GAAIC,YAAY,EAAIA,YAAY,CAACtF,MAAM,CAAE,CACvC;AACA,KAAM,CAAA0F,WAA6B,CAAG,CACpClF,mBAAmB,CAAE8E,YAAY,CAACtF,MAAM,CAACQ,mBAAmB,EAAI,CAAC,CACjEmF,UAAU,CAAEL,YAAY,CAACtF,MAAM,CAAC2F,UAAU,EAAI,MAAM,CACpDlH,OAAO,CAAE6G,YAAY,CAACtF,MAAM,CAACvB,OAAO,EAAI,MAAM,CAC9CsC,gBAAgB,CAAEuE,YAAY,CAACtF,MAAM,CAACe,gBAAgB,CACtDE,WAAW,CAAEqE,YAAY,CAACtF,MAAM,CAACiB,WAAW,CAC5CC,SAAS,CAAEoE,YAAY,CAACtF,MAAM,CAACkB,SAAS,CACxCC,aAAa,CAAEmE,YAAY,CAACtF,MAAM,CAACmB,aAAa,CAChDC,uBAAuB,CAAEkE,YAAY,CAACtF,MAAM,CAACoB,uBAC/C,CAAC,CAED4D,yBAAyB,CAAC,CAACU,WAAW,CAAC,CAAC,CAC1C,CACF,CAAC,CAED;AACA3H,SAAS,CAAC,IAAM,CACd6G,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACA,mBAAmB,CAAC,CAAC,CAEzB;AACA7G,SAAS,CAAC,IAAM,CACd,GAAIoH,wBAAwB,CAACS,MAAM,CAAG,CAAC,EAAI,CAACX,mBAAmB,CAAE,CAC/D,KAAM,CAAAY,UAAU,CAAGV,wBAAwB,CAACA,wBAAwB,CAACS,MAAM,CAAG,CAAC,CAAC,CAChFR,qBAAqB,CAACS,UAAU,CAACJ,OAAO,CAAC,CAC3C,CACF,CAAC,CAAE,CAACN,wBAAwB,CAAEF,mBAAmB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAa,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAAC5D,MAAM,CAAE,OAEbO,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAAsD,QAAQ,CAAG,KAAM,CAAA7G,kBAAkB,CAAC8G,YAAY,CAAC9D,MAAM,CAAC,CAC9DG,oBAAoB,CAAC0D,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAI,EAAE,CAAC,CACjD,CAAE,MAAOC,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnB5H,OAAO,CAAC0H,KAAK,CAAC,EAAAC,eAAA,CAAAD,KAAK,CAACJ,QAAQ,UAAAK,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBH,IAAI,UAAAI,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,aAAa,CAAC,CAC5DjE,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,OAAS,CACRI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAA8D,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAC7D,QAAQ,CAAE,OAEfO,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAI,CACF,KAAM,CAAA8C,QAAQ,CAAG,KAAM,CAAA7G,kBAAkB,CAACsH,cAAc,CAAC9D,QAAQ,CAAC,CAClEG,oBAAoB,CAACkD,QAAQ,CAACE,IAAI,CAACQ,SAAS,EAAI,EAAE,CAAC,CACrD,CAAE,MAAON,KAAU,CAAE,KAAAO,gBAAA,CAAAC,qBAAA,CACnBlI,OAAO,CAAC0H,KAAK,CAAC,EAAAO,gBAAA,CAAAP,KAAK,CAACJ,QAAQ,UAAAW,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBT,IAAI,UAAAU,qBAAA,iBAApBA,qBAAA,CAAsBL,MAAM,GAAI,YAAY,CAAC,CAC3DzD,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,OAAS,CACRI,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAA2D,cAAc,CAAG,KAAO,CAAAC,SAAiB,EAAK,CAClD,GAAI,CAACA,SAAS,EAAI,CAACnE,QAAQ,CAAE,OAE7B+B,uBAAuB,CAAC,IAAI,CAAC,CAC7B,GAAI,CACF;AACA,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAA7G,kBAAkB,CAAC4H,gBAAgB,CAACD,SAAS,CAAEnE,QAAQ,CAAC,CAE/E,GAAIqD,QAAQ,CAACE,IAAI,CAAE,CACjB,KAAM,CAAAc,aAAa,CAAGhB,QAAQ,CAACE,IAAI,CAEnC;AACA1C,qBAAqB,CAACwD,aAAa,CAACC,eAAe,EAAI,EAAE,CAAC,CAC1DvD,qBAAqB,CAACsD,aAAa,CAACE,eAAe,EAAI,EAAE,CAAC,CAC1DtD,eAAe,CAACoD,aAAa,CAACG,QAAQ,EAAI,EAAE,CAAC,CAC7CrD,mBAAmB,CAACkD,aAAa,CAACI,QAAQ,EAAI,EAAE,CAAC,CAEjD;AACA,GAAIJ,aAAa,CAACC,eAAe,EAAID,aAAa,CAACE,eAAe,CAAE,CAClExI,OAAO,CAAC2I,OAAO,CAAC,aAAa,CAAC,CAChC,CAEA,GAAIL,aAAa,CAACG,QAAQ,EAAIH,aAAa,CAACI,QAAQ,CAAE,CACpD1I,OAAO,CAAC2I,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC,CAC5FpD,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CAAC,IAAM,CACLtF,OAAO,CAAC4I,OAAO,CAAC,4BAA4B,CAAC,CAC7CtD,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CACF,CACF,CAAE,MAAOoC,KAAU,CAAE,KAAAmB,gBAAA,CAAAC,qBAAA,CACnB9I,OAAO,CAAC0H,KAAK,CAAC,EAAAmB,gBAAA,CAAAnB,KAAK,CAACJ,QAAQ,UAAAuB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBrB,IAAI,UAAAsB,qBAAA,iBAApBA,qBAAA,CAAsBjB,MAAM,GAAI,UAAU,CAAC,CACzD;AACA,KAAM,CAAAkB,kBAAkB,CAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,CAAE,EAAE,CAAC,CACnElE,qBAAqB,CAAC,GAAGiE,kBAAkB,cAAc,CAAC,CAC1D/D,qBAAqB,CAAC,GAAG+D,kBAAkB,eAAe,CAAC,CAC3DzD,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,OAAS,CACRU,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAiD,qBAAqB,CAAIb,SAAiB,EAAK,CACnDxD,oBAAoB,CAACwD,SAAS,CAAC,CAC/B;AACAtD,qBAAqB,CAAC,EAAE,CAAC,CACzBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,eAAe,CAAC,EAAE,CAAC,CACnBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,sBAAsB,CAAC,KAAK,CAAC,CAE7B;AACA6C,cAAc,CAACC,SAAS,CAAC,CAC3B,CAAC,CAED;AACA9I,SAAS,CAAC,IAAM,CACd,GAAI+D,UAAU,GAAK,OAAO,EAAII,MAAM,EAAIA,MAAM,CAAC0D,MAAM,CAAG,CAAC,CAAE,CAAE;AAC3D,KAAM,CAAA+B,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B9B,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAM+B,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAAC7F,UAAU,CAAEI,MAAM,CAAC,CAAC,CAExBnE,SAAS,CAAC,IAAM,CACd,GAAI2E,QAAQ,EAAIA,QAAQ,CAACkD,MAAM,CAAG,CAAC,CAAE,CAAE;AACrC,KAAM,CAAA+B,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BrB,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMsB,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAACjF,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAoF,WAAW,CAAG,CAClBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAEA,CAAA,GAAM,KAAK,CACzBC,QAAQ,CAAGC,IAAS,EAAK,CACvB,GAAIA,IAAI,CAACC,QAAQ,CAACzC,MAAM,CAAG,CAAC,CAAE,CAC5B3D,eAAe,CAACmG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC,IAAM,CACLpG,eAAe,CAAC,IAAI,CAAC,CACvB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAqG,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC;AACA,GAAIxG,UAAU,GAAK,QAAQ,EAAI,CAACE,YAAY,CAAE,CAC5CvD,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA,GAAIrE,UAAU,GAAK,OAAO,GAAK,CAACI,MAAM,EAAI,CAACI,eAAe,CAAC,CAAE,CAC3D7D,OAAO,CAAC0H,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA;AACA,GAAI,CAAAoC,eAMF,CAAG,EAAE,CAEP,GAAIrF,cAAc,GAAK,QAAQ,CAAE,CAC/B,GAAI,CAACE,iBAAiB,EAAI,CAACM,YAAY,EAAI,CAACE,gBAAgB,CAAE,CAC5DnF,OAAO,CAAC0H,KAAK,CAAC,sBAAsB,CAAC,CACrC,OACF,CACAoC,eAAe,CAAG,CAAC,CACjBC,UAAU,CAAEpF,iBAAiB,CAC7BqF,WAAW,CAAEnF,kBAAkB,CAC/BoF,WAAW,CAAElF,kBAAkB,CAC/B0D,QAAQ,CAAExD,YAAY,CACtByD,QAAQ,CAAEvD,gBACZ,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAId,cAAc,CAAC8C,MAAM,GAAK,CAAC,CAAE,CAC/BnH,OAAO,CAAC0H,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA;AACA,KAAM,CAAAwC,WAMJ,CAAG,EAAE,CAEP,IAAK,KAAM,CAAA9B,SAAS,GAAI,CAAA/D,cAAc,CAAE,CACtC,GAAI,CACF,KAAM,CAAAiD,QAAQ,CAAG,KAAM,CAAA7G,kBAAkB,CAAC4H,gBAAgB,CAACD,SAAS,CAAEnE,QAAQ,CAAC,CAC/E,GAAIqD,QAAQ,CAACE,IAAI,CAAE,CACjB,KAAM,CAAAc,aAAa,CAAGhB,QAAQ,CAACE,IAAI,CACnC,KAAM,CAAAwC,WAAW,CAAG1B,aAAa,CAACC,eAAe,CACjD,KAAM,CAAA0B,WAAW,CAAG3B,aAAa,CAACE,eAAe,CACjD,KAAM,CAAAC,QAAQ,CAAGH,aAAa,CAACG,QAAQ,CACvC,KAAM,CAAAC,QAAQ,CAAGJ,aAAa,CAACI,QAAQ,CAEvC;AACA,GAAIsB,WAAW,EAAIC,WAAW,EAAIxB,QAAQ,EAAIC,QAAQ,CAAE,CACtDwB,WAAW,CAACC,IAAI,CAAC,CACfJ,UAAU,CAAE3B,SAAS,CACrB4B,WAAW,CACXC,WAAW,CACXxB,QAAQ,CACRC,QACF,CAAC,CAAC,CACF1I,OAAO,CAAC2I,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC,CACrE,CAAC,IAAM,CACL1I,OAAO,CAAC4I,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC,CACxD,CACF,CAAC,IAAM,CACLpI,OAAO,CAAC0H,KAAK,CAAC,QAAQU,SAAS,UAAU,CAAC,CAC5C,CACF,CAAE,MAAOV,KAAU,CAAE,KAAA0C,gBAAA,CAAAC,qBAAA,CACnBrK,OAAO,CAAC0H,KAAK,CAAC,QAAQU,SAAS,SAAS,EAAAgC,gBAAA,CAAA1C,KAAK,CAACJ,QAAQ,UAAA8C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB5C,IAAI,UAAA6C,qBAAA,iBAApBA,qBAAA,CAAsBxC,MAAM,GAAIH,KAAK,CAAC1H,OAAO,EAAE,CAAC,CAC1F,CACF,CAEA,GAAIkK,WAAW,CAAC/C,MAAM,GAAK,CAAC,CAAE,CAC5BnH,OAAO,CAAC0H,KAAK,CAAC,wCAAwC,CAAC,CACvD,OACF,CAEAoC,eAAe,CAAGI,WAAW,CAC/B,CAEA1E,aAAa,CAAC,IAAI,CAAC,CACnBE,WAAW,CAAC,CAAC,CAAC,CACdE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,GAAIQ,kBAAkB,EAAI3B,cAAc,GAAK,QAAQ,CAAE,CACrD;AACA,KAAM,CAAA6F,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B,GAAIlH,UAAU,GAAK,QAAQ,EAAIE,YAAY,CAAE,CAC3C+G,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEjH,YAAY,CAACkH,aAAa,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAE/G,MAAM,CAAC,CAClC6G,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE3G,eAAe,CAAC,CACnD,CAEAyG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAE7F,iBAAiB,CAAC,CACpD2F,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAE3F,kBAAkB,CAAC,CACtDyF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEzF,kBAAkB,CAAC,CACtDuF,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEvF,YAAY,CAAC,CAC9CqF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAErF,gBAAgB,CAAC,CACtDmF,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAEvG,QAAQ,CAAC,CACtCqG,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEvG,QAAQ,CAAC,CAE1C;AACA,KAAM,CAAA2C,MAAM,CAAG,KAAM,CAAAX,oBAAoB,CAACqE,QAAQ,CAAC,CAEnD,GAAI1D,MAAM,CAAE,CACV5G,OAAO,CAAC2I,OAAO,CAAC,gCAAgC,CAAC,CACjD;AACAnD,aAAa,CAAC,KAAK,CAAC,CACpBE,WAAW,CAAC,CAAC,CAAC,CAChB,CAEA,OAAQ;AACV,CAEA;AACA,KAAM,CAAAgF,UAA8B,CAAG,EAAE,CAEzC,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGb,eAAe,CAAC3C,MAAM,CAAEwD,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,KAAK,CAAGd,eAAe,CAACa,CAAC,CAAC,CAEhC;AACAjF,WAAW,CAACmF,IAAI,CAACC,KAAK,CAAEH,CAAC,CAAGb,eAAe,CAAC3C,MAAM,CAAI,EAAE,CAAC,CAAC,CAE1D,GAAI2C,eAAe,CAAC3C,MAAM,CAAG,CAAC,CAAE,CAC9BnH,OAAO,CAAC2J,IAAI,CAAC,UAAUgB,CAAC,CAAG,CAAC,IAAIb,eAAe,CAAC3C,MAAM,KAAKyD,KAAK,CAACb,UAAU,UAAU,CAAC,CACxF,CAEA,KAAM,CAAAO,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B,GAAIlH,UAAU,GAAK,QAAQ,EAAIE,YAAY,CAAE,CAC3C;AACA+G,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEjH,YAAY,CAACkH,aAAa,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAE/G,MAAM,CAAC,CAClC6G,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE3G,eAAe,CAAC,CACnD,CAEAyG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEI,KAAK,CAACb,UAAU,CAAC,CACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEI,KAAK,CAACZ,WAAW,CAAC,CACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEI,KAAK,CAACX,WAAW,CAAC,CACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEI,KAAK,CAACnC,QAAQ,CAAC,CAChD6B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAEI,KAAK,CAAClC,QAAQ,CAAC,CACpD4B,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEvG,QAAQ,CAAC,CAE1C,KAAM,CAAAqD,QAAQ,CAAG,KAAM,CAAA7G,kBAAkB,CAACsK,OAAO,CAACT,QAAQ,CAAC,CAE3D,GAAIhD,QAAQ,CAACE,IAAI,CAAE,CACjBkD,UAAU,CAACP,IAAI,CAAC,CACdjD,UAAU,CAAEI,QAAQ,CAACE,IAAI,CAACN,UAAU,EAAI,GAAG0D,KAAK,CAACnC,QAAQ,IAAImC,KAAK,CAAClC,QAAQ,EAAE,CAC7E3G,mBAAmB,CAAEuF,QAAQ,CAACE,IAAI,CAACzF,mBAAmB,EAAI,CAAC,CAC3D/B,OAAO,CAAEsH,QAAQ,CAACE,IAAI,CAACxH,OAAO,EAAI,MAAM,CACxC;AACAsC,gBAAgB,CAAEgF,QAAQ,CAACE,IAAI,CAAClF,gBAAgB,CAChDE,WAAW,CAAE8E,QAAQ,CAACE,IAAI,CAAChF,WAAW,CACtCC,SAAS,CAAE6E,QAAQ,CAACE,IAAI,CAAC/E,SAAS,CAClCC,aAAa,CAAE4E,QAAQ,CAACE,IAAI,CAAC9E,aAAa,CAC1CC,uBAAuB,CAAE2E,QAAQ,CAACE,IAAI,CAAC7E,uBACzC,CAAC,CAAC,CAEF,GAAImH,eAAe,CAAC3C,MAAM,CAAG,CAAC,CAAE,CAC9BnH,OAAO,CAAC2I,OAAO,CAAC,QAAQiC,KAAK,CAACb,UAAU,OAAO,CAAC,CAClD,CACF,CACF,CAEArE,WAAW,CAAC,GAAG,CAAC,CAChBE,UAAU,CAAC8E,UAAU,CAAC,CACtB1K,OAAO,CAAC2I,OAAO,CAAC,SAAS+B,UAAU,CAACvD,MAAM,SAAS,CAAC,CAEtD,CAAE,MAAOO,KAAU,CAAE,KAAAsD,gBAAA,CAAAC,qBAAA,CACnBjL,OAAO,CAAC0H,KAAK,CAAC,EAAAsD,gBAAA,CAAAtD,KAAK,CAACJ,QAAQ,UAAA0D,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxD,IAAI,UAAAyD,qBAAA,iBAApBA,qBAAA,CAAsBpD,MAAM,GAAI,MAAM,CAAC,CACvD,CAAC,OAAS,CACRrC,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAA0F,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,OAAO,CAAG9H,UAAU,GAAK,QAAQ,CAAGE,YAAY,CAAIE,MAAM,EAAII,eAAgB,CAEpF,GAAIY,cAAc,GAAK,QAAQ,CAAE,CAC/B,MAAO,CAAA0G,OAAO,EAAIxG,iBAAiB,EAAIM,YAAY,EAAIE,gBAAgB,CACzE,CAAC,IAAM,CACL,MAAO,CAAAgG,OAAO,EAAI9G,cAAc,CAAC8C,MAAM,CAAG,CAAC,CAC7C,CACF,CAAC,CAID,mBACErG,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACK,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAClB,KAAK,CAAE,CAAE0J,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAE1J,YAAY,CAAE,KAAM,CAAE,CAAAH,QAAA,CAAC,oEAAW,CAAO,CAAC,cACvGZ,IAAA,CAACM,IAAI,EAACiB,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,8MAEvB,CAAM,CAAC,cAEPZ,IAAA,CAACb,OAAO,GAAE,CAAC,cAGXa,IAAA,CAACrB,IAAI,EAACsC,KAAK,CAAC,gCAAO,CAACyJ,SAAS,CAAC,eAAe,CAAA9J,QAAA,cAC3CV,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAAC9J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eAChEV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC1BV,KAAA,CAACtB,KAAK,CAACmM,KAAK,EACV7J,KAAK,CAAEuB,UAAW,CAClBqG,QAAQ,CAAGkC,CAAC,EAAKtI,aAAa,CAACsI,CAAC,CAACC,MAAM,CAAC/J,KAAK,CAAE,CAC/CJ,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAb,QAAA,eAExBZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAC,OAAO,CAAAN,QAAA,CAAC,yCAAS,CAAO,CAAC,cACtCZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAC,QAAQ,CAAAN,QAAA,CAAC,6BAAO,CAAO,CAAC,EAC1B,CAAC,EACX,CAAC,CAGL6B,UAAU,GAAK,OAAO,eACrBvC,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAAC7J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eACnDV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5BV,KAAA,CAACpB,KAAK,CAACiM,KAAK,EAACG,OAAO,MAACpK,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAE0J,OAAO,CAAE,MAAO,CAAE,CAAAvK,QAAA,eAC5DZ,IAAA,CAAClB,KAAK,EACJoC,KAAK,CAAE2B,MAAO,CACdiG,QAAQ,CAAGkC,CAAC,EAAKlI,SAAS,CAACkI,CAAC,CAACC,MAAM,CAAC/J,KAAK,CAAE,CAC3CkK,WAAW,CAAC,4BAAkB,CAC9BtK,KAAK,CAAE,CAAEmB,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACFjC,IAAA,CAAChB,MAAM,EACLuC,IAAI,CAAC,SAAS,CACd8J,OAAO,CAAE5E,aAAc,CACvB6E,OAAO,CAAEnI,eAAgB,CACzBoI,QAAQ,CAAE,CAAC1I,MAAO,CAClB/B,KAAK,CAAE,CAAE0K,UAAU,CAAE,CAAE,CAAE,CAAA5K,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5BZ,IAAA,CAACX,IAAI,EAACoM,QAAQ,CAAEtI,eAAgB,CAAAvC,QAAA,cAC9BZ,IAAA,CAACjB,MAAM,EACLmC,KAAK,CAAE+B,eAAgB,CACvB6F,QAAQ,CAAE5F,kBAAmB,CAC7BkI,WAAW,CAAC,mCAAU,CACtBtK,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAM,CAAEpJ,SAAS,CAAE,CAAE,CAAE,CACvC6J,OAAO,CAAEnI,eAAgB,CAAAvC,QAAA,CAExBmC,iBAAiB,CAAC2I,GAAG,CAAEC,IAAI,eAC1B3L,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAEyK,IAAK,CAAA/K,QAAA,CAC5B+K,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CAAC,EACD,CACR,CAGAlJ,UAAU,GAAK,QAAQ,eACtBvC,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,2DAAY,CAAM,CAAC,cAChCV,KAAA,CAACK,OAAO,KAAKkI,WAAW,CAAE3H,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAb,QAAA,eAChDZ,IAAA,MAAG0K,SAAS,CAAC,sBAAsB,CAAA9J,QAAA,cACjCZ,IAAA,CAACL,aAAa,GAAE,CAAC,CAChB,CAAC,cACJK,IAAA,MAAG0K,SAAS,CAAC,iBAAiB,CAAA9J,QAAA,CAAC,mFAAgB,CAAG,CAAC,cACnDZ,IAAA,MAAG0K,SAAS,CAAC,iBAAiB,CAAA9J,QAAA,CAAC,uEAE/B,CAAG,CAAC,EACG,CAAC,EACP,CACN,EACI,CAAC,CACJ,CAAC,cAGPZ,IAAA,CAACrB,IAAI,EAACsC,KAAK,CAAC,0BAAM,CAACyJ,SAAS,CAAC,eAAe,CAAA9J,QAAA,cAC1CV,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAAC9J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eAChEV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBV,KAAA,CAACpB,KAAK,CAACiM,KAAK,EAACG,OAAO,MAACpK,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAE0J,OAAO,CAAE,MAAO,CAAE,CAAAvK,QAAA,eAC5DZ,IAAA,CAAClB,KAAK,EACJoC,KAAK,CAAEmC,QAAS,CAChByF,QAAQ,CAAGkC,CAAC,EAAK1H,WAAW,CAAC0H,CAAC,CAACC,MAAM,CAAC/J,KAAK,CAAE,CAC7CkK,WAAW,CAAC,4BAAkB,CAC9BtK,KAAK,CAAE,CAAEmB,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACFjC,IAAA,CAAChB,MAAM,EACLuC,IAAI,CAAC,SAAS,CACd8J,OAAO,CAAEnE,eAAgB,CACzBoE,OAAO,CAAE3H,aAAc,CACvB4H,QAAQ,CAAE,CAAClI,QAAS,CACpBvC,KAAK,CAAE,CAAE0K,UAAU,CAAE,CAAE,CAAE,CAAA5K,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBV,KAAA,CAACtB,KAAK,CAACmM,KAAK,EACV7J,KAAK,CAAE2C,cAAe,CACtBiF,QAAQ,CAAGkC,CAAC,EAAKlH,iBAAiB,CAACkH,CAAC,CAACC,MAAM,CAAC/J,KAAK,CAAE,CACnDJ,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAb,QAAA,eAExBZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAC,QAAQ,CAAAN,QAAA,CAAC,sCAAM,CAAO,CAAC,cACpCZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAC,UAAU,CAAAN,QAAA,CAAC,kDAAQ,CAAO,CAAC,EAC7B,CAAC,EACX,CAAC,CAELiD,cAAc,GAAK,QAAQ,cAC1B3D,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BZ,IAAA,CAACX,IAAI,EAACoM,QAAQ,CAAE9H,aAAc,CAAA/C,QAAA,cAC5BZ,IAAA,CAACjB,MAAM,EACLmC,KAAK,CAAE6C,iBAAkB,CACzB+E,QAAQ,CAAET,qBAAsB,CAChC+C,WAAW,CAAC,kMAAkC,CAC9CtK,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAM,CAAEpJ,SAAS,CAAE,CAAE,CAAE,CACvC6J,OAAO,CAAE3H,aAAc,CAAA/C,QAAA,CAEtB2C,iBAAiB,CAACmI,GAAG,CAAEC,IAAI,eAC1B3L,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAEyK,IAAK,CAAA/K,QAAA,CAC5B+K,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,CAEN5H,iBAAiB,eAChB/D,IAAA,QAAKc,KAAK,CAAE,CAAEW,SAAS,CAAE,EAAG,CAAE,CAAAb,QAAA,cAC5BV,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAAC7J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eACnDV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACiB,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,kDAAQ,CAAM,CAAC,cACtCZ,IAAA,CAACX,IAAI,EAACoM,QAAQ,CAAEtG,oBAAqB,CAAAvE,QAAA,cACnCZ,IAAA,QAAKc,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAEmK,OAAO,CAAE,EAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAlL,QAAA,CACpFuE,oBAAoB,cACnBnF,IAAA,MAAAY,QAAA,CAAG,iEAAa,CAAG,CAAC,cAEpBV,KAAA,CAAAE,SAAA,EAAAQ,QAAA,eACEV,KAAA,MAAAU,QAAA,eAAGZ,IAAA,WAAAY,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACqD,kBAAkB,EAAI,KAAK,EAAI,CAAC,cAC3D/D,KAAA,MAAAU,QAAA,eAAGZ,IAAA,WAAAY,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACuD,kBAAkB,EAAI,KAAK,EAAI,CAAC,CAC5D,CAACM,mBAAmB,EAAIJ,YAAY,EAAIE,gBAAgB,eACvDrE,KAAA,CAAAE,SAAA,EAAAQ,QAAA,eACEV,KAAA,MAAAU,QAAA,eAAGZ,IAAA,WAAAY,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACyD,YAAY,EAAI,CAAC,cAC1CnE,KAAA,MAAAU,QAAA,eAAGZ,IAAA,WAAAY,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAAC2D,gBAAgB,EAAI,CAAC,EAChD,CACH,EACD,CACH,CACE,CAAC,CACF,CAAC,EACJ,CAAC,CAELE,mBAAmB,eAClBvE,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,gFAAa,CAAM,CAAC,cACjCZ,IAAA,QAAKc,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAb,QAAA,cAC3BV,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAAC7J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eACnDZ,IAAA,CAACjB,MAAM,EACLmC,KAAK,CAAEmD,YAAa,CACpByE,QAAQ,CAAExE,eAAgB,CAC1B8G,WAAW,CAAC,8DAAY,CACxBtK,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,CAExBuB,eAAe,CAACuJ,GAAG,CAAEK,IAAI,eACxB/L,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAE6K,IAAK,CAAAnL,QAAA,CAC5BmL,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CAER1H,YAAY,eACXrE,IAAA,CAACjB,MAAM,EACLmC,KAAK,CAAEqD,gBAAiB,CACxBuE,QAAQ,CAAEtE,mBAAoB,CAC9B4G,WAAW,CAAE,YAAY/G,YAAY,OAAQ,CAC7CvD,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,CAExB,CAACwB,eAAe,CAACiC,YAAY,CAAiC,EAAI,EAAE,EAAEqH,GAAG,CAAE5D,QAAQ,eAClF9H,IAAA,CAACQ,MAAM,EAAgBU,KAAK,CAAE4G,QAAS,CAAAlH,QAAA,CACpCkH,QAAQ,EADEA,QAEL,CACT,CAAC,CACI,CACT,EACI,CAAC,CACL,CAAC,EACH,CACN,EACI,CAAC,CACL,CACN,EACE,CAAC,cAEN5H,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,oEAAW,CAAM,CAAC,cAC/BZ,IAAA,CAACX,IAAI,EAACoM,QAAQ,CAAE9H,aAAc,CAAA/C,QAAA,cAC5BZ,IAAA,CAACjB,MAAM,EACLiN,IAAI,CAAC,UAAU,CACf9K,KAAK,CAAEuC,cAAe,CACtBqF,QAAQ,CAAEpF,iBAAkB,CAC5B0H,WAAW,CAAC,4FAAiB,CAC7BtK,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAM,CAAEpJ,SAAS,CAAE,CAAE,CAAE,CACvC6J,OAAO,CAAE3H,aAAc,CAAA/C,QAAA,CAEtB2C,iBAAiB,CAACmI,GAAG,CAAEC,IAAI,eAC1B3L,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAEyK,IAAK,CAAA/K,QAAA,CAC5B+K,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CACN,CAEApI,iBAAiB,CAACgD,MAAM,GAAK,CAAC,EAAI,CAAC5C,aAAa,eAC/C3D,IAAA,CAACV,KAAK,EACJF,OAAO,CAAC,4CAAS,CACjB6M,WAAW,CAAC,oOAA2C,CACvD1K,IAAI,CAAC,SAAS,CACdC,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,cAGPxB,IAAA,CAACrB,IAAI,EAAC+L,SAAS,CAAC,eAAe,CAACzJ,KAAK,CAAC,0BAAM,CAAAL,QAAA,cAC1CV,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,QAAQ,CAAC9J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eACjEV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BV,KAAA,CAACtB,KAAK,CAACmM,KAAK,EACV7J,KAAK,CAAEsE,kBAAmB,CAC1BsD,QAAQ,CAAGkC,CAAC,EAAKvF,qBAAqB,CAACuF,CAAC,CAACC,MAAM,CAAC/J,KAAK,CAAE,CACvDJ,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CACxB8J,QAAQ,CAAE1H,cAAc,GAAK,UAAY;AAAA,CAAAjD,QAAA,eAEzCZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAE,IAAK,CAAAN,QAAA,cACjBV,KAAA,CAAChB,KAAK,EAAA0B,QAAA,EAAC,kDAEL,cAAAZ,IAAA,CAACM,IAAI,EAACiB,IAAI,CAAC,WAAW,CAACT,KAAK,CAAE,CAAE0J,QAAQ,CAAE,EAAG,CAAE,CAAA5J,QAAA,CAAC,4EAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,cACRZ,IAAA,CAACpB,KAAK,EAACsC,KAAK,CAAE,KAAM,CAAAN,QAAA,cAClBV,KAAA,CAAChB,KAAK,EAAA0B,QAAA,EAAC,0BAEL,cAAAZ,IAAA,CAACM,IAAI,EAACiB,IAAI,CAAC,WAAW,CAACT,KAAK,CAAE,CAAE0J,QAAQ,CAAE,EAAG,CAAE,CAAA5J,QAAA,CAAC,0GAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,EACG,CAAC,EACX,CAAC,CAEL4E,kBAAkB,EAAI3B,cAAc,GAAK,QAAQ,eAChD7D,IAAA,CAACV,KAAK,EACJF,OAAO,CAAC,sCAAQ,CAChB6M,WAAW,cACT/L,KAAA,QAAAU,QAAA,EAAK,8JAEH,cAAAZ,IAAA,QAAK,CAAC,kFACQ,cAAAA,IAAA,WAAAY,QAAA,CAAQ,gEAAY,CAAQ,CAAC,0DAC7C,EAAK,CACN,CACDW,IAAI,CAAC,MAAM,CACXC,QAAQ,MACT,CACF,CAEAqC,cAAc,GAAK,UAAU,eAC5B7D,IAAA,CAACV,KAAK,EACJF,OAAO,CAAC,4CAAS,CACjB6M,WAAW,CAAC,0KAA8B,CAC1C1K,IAAI,CAAC,SAAS,CACdC,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,cAGPtB,KAAA,CAACvB,IAAI,EAAC+L,SAAS,CAAC,eAAe,CAAA9J,QAAA,eAC7BZ,IAAA,CAAChB,MAAM,EACLuC,IAAI,CAAC,SAAS,CACdqJ,IAAI,CAAC,OAAO,CACZsB,IAAI,cAAElM,IAAA,CAACJ,kBAAkB,GAAE,CAAE,CAC7ByL,OAAO,CAAEpC,qBAAsB,CAC/BqC,OAAO,CAAE3G,UAAW,CACpB4G,QAAQ,CAAE,CAACjB,WAAW,CAAC,CAAE,CACzBI,SAAS,CAAC,eAAe,CAAA9J,QAAA,CAExB+D,UAAU,CAAG,SAAS,CAAG,SAAS,CAC7B,CAAC,CAGRA,UAAU,eACTzE,KAAA,QAAKwK,SAAS,CAAC,kBAAkB,CAAA9J,QAAA,eAC/BZ,IAAA,CAACM,IAAI,EAAAM,QAAA,CAAC,gCAAK,CAAM,CAAC,cAClBZ,IAAA,CAACN,QAAQ,EAACyM,OAAO,CAAEtH,QAAS,CAACuH,MAAM,CAAC,QAAQ,CAAE,CAAC,EAC5C,CACN,EACG,CAAC,CAGNrH,OAAO,CAACwB,MAAM,CAAG,CAAC,eACjBvG,IAAA,CAACrB,IAAI,EAACsC,KAAK,CAAC,0BAAM,CAACyJ,SAAS,CAAC,eAAe,CAAA9J,QAAA,CACzCmE,OAAO,CAACwB,MAAM,CAAG,CAAC,cACjB;AACArG,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACb,OAAO,GAAE,CAAC,cACXa,IAAA,CAACK,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAApB,QAAA,CAAC,4CAAO,CAAO,CAAC,cAChCV,KAAA,QAAKY,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAAH,QAAA,eAC/BZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,oEAAW,CAAM,CAAC,cAC/BZ,IAAA,CAACjB,MAAM,EACL+B,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAM,CAAEpJ,SAAS,CAAE,CAAE,CAAE,CACvC2J,WAAW,CAAC,sCAAQ,CACpBlK,KAAK,CAAE+D,mBAAoB,CAC3B6D,QAAQ,CAAG5H,KAAK,EAAKgE,sBAAsB,CAAChE,KAAK,CAAE,CAAAN,QAAA,CAElDmE,OAAO,CAAC2G,GAAG,CAAC,CAAC/K,MAAM,CAAE0L,KAAK,gBACzBrM,IAAA,CAACQ,MAAM,EAAaU,KAAK,CAAEmL,KAAM,CAAAzL,QAAA,CAC9BD,MAAM,CAAC2F,UAAU,EADP+F,KAEL,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAGLtH,OAAO,CAACE,mBAAmB,CAAC,eAC3B/E,KAAA,QAAAU,QAAA,eACEV,KAAA,CAACG,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAApB,QAAA,EAAC,gBAAI,CAACmE,OAAO,CAACE,mBAAmB,CAAC,CAACqB,UAAU,EAAQ,CAAC,cACtEtG,IAAA,CAACS,uBAAuB,EAACE,MAAM,CAAEoE,OAAO,CAACE,mBAAmB,CAAE,CAAE,CAAC,EAC9D,CACN,EACE,CAAC,cAEN;AACA/E,KAAA,QAAAU,QAAA,eACEV,KAAA,CAACG,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAApB,QAAA,EAAC,6BAAO,CAACmE,OAAO,CAAC,CAAC,CAAC,CAACuB,UAAU,EAAQ,CAAC,cACvDtG,IAAA,CAACS,uBAAuB,EAACE,MAAM,CAAEoE,OAAO,CAAC,CAAC,CAAE,CAAE,CAAC,EAC5C,CACN,CACG,CACP,CAGAe,wBAAwB,CAACS,MAAM,CAAG,CAAC,eAClCrG,KAAA,CAACvB,IAAI,EAACsC,KAAK,CAAC,sCAAQ,CAACyJ,SAAS,CAAC,eAAe,CAAC5J,KAAK,CAAE,CAAEW,SAAS,CAAE,EAAG,CAAE,CAAAb,QAAA,eACtEZ,IAAA,CAACV,KAAK,EACJF,OAAO,CAAC,4CAAS,CACjB6M,WAAW,CAAC,sLAAgC,CAC5C1K,IAAI,CAAC,SAAS,CACdC,QAAQ,MACRV,KAAK,CAAE,CAAEC,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cACFb,KAAA,CAAChB,KAAK,EAACyL,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAAC9J,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAO,CAAE,CAAAjK,QAAA,eAEhEV,KAAA,QAAAU,QAAA,eACEZ,IAAA,CAACM,IAAI,EAACwK,MAAM,MAAAlK,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BZ,IAAA,CAACjB,MAAM,EACLmC,KAAK,CAAE0E,mBAAoB,CAC3BkD,QAAQ,CAAE/C,qBAAsB,CAChCjF,KAAK,CAAE,CAAE+J,KAAK,CAAE,MAAM,CAAEpJ,SAAS,CAAE,CAAE,CAAE,CACvC2J,WAAW,CAAC,oEAAa,CAAAxK,QAAA,CAExBkF,wBAAwB,CAAC4F,GAAG,CAAEvF,IAAI,eACjCnG,IAAA,CAACQ,MAAM,EAAoBU,KAAK,CAAEiF,IAAI,CAACC,OAAQ,CAAAxF,QAAA,CAC5CuF,IAAI,CAACC,OAAO,CAACkG,QAAQ,CAAC,GAAG,CAAC,CACzB,GAAGnG,IAAI,CAACC,OAAO,CAACmG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAI,CAAAC,IAAI,CAACrG,IAAI,CAACsG,UAAU,EAAItG,IAAI,CAACuG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,CAClG,MAAMxG,IAAI,CAACC,OAAO,CAACwG,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,QAAQ,GAAI,CAAAJ,IAAI,CAACrG,IAAI,CAACsG,UAAU,EAAItG,IAAI,CAACuG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,EAHjGxG,IAAI,CAACC,OAKV,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAGLV,sBAAsB,CAACa,MAAM,CAAG,CAAC,eAChCrG,KAAA,QAAAU,QAAA,eACEV,KAAA,CAACG,KAAK,EAAC2B,KAAK,CAAE,CAAE,CAAApB,QAAA,EAAC,6BAAO,CAAC8E,sBAAsB,CAAC,CAAC,CAAC,CAACY,UAAU,EAAQ,CAAC,cACtEtG,IAAA,CAACS,uBAAuB,EAACE,MAAM,CAAE+E,sBAAsB,CAAC,CAAC,CAAE,CAAE,CAAC,EAC3D,CACN,EACI,CAAC,EACJ,CACP,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}