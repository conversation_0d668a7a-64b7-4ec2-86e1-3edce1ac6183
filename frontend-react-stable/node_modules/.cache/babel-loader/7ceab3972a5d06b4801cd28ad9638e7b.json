{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { Divider, MenuItem, MenuItemGroup, SubMenu } from '..';\nexport function parseChildren(children, keyPath) {\n  return toArray(children).map(function (child, index) {\n    if (/*#__PURE__*/React.isValidElement(child)) {\n      var _child$props$eventKey, _child$props;\n      var key = child.key;\n      var eventKey = (_child$props$eventKey = (_child$props = child.props) === null || _child$props === void 0 ? void 0 : _child$props.eventKey) !== null && _child$props$eventKey !== void 0 ? _child$props$eventKey : key;\n      var emptyKey = eventKey === null || eventKey === undefined;\n      if (emptyKey) {\n        eventKey = \"tmp_key-\".concat([].concat(_toConsumableArray(keyPath), [index]).join('-'));\n      }\n      var cloneProps = {\n        key: eventKey,\n        eventKey: eventKey\n      };\n      if (process.env.NODE_ENV !== 'production' && emptyKey) {\n        cloneProps.warnKey = true;\n      }\n      return /*#__PURE__*/React.cloneElement(child, cloneProps);\n    }\n    return child;\n  });\n}\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var label = opt.label,\n        children = opt.children,\n        key = opt.key,\n        type = opt.type,\n        restProps = _objectWithoutProperties(opt, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index); // MenuItemGroup & SubMenuItem\n\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        } // Sub Menu\n\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      } // MenuItem & Divider\n\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(Divider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath) {\n  var childNodes = children;\n  if (items) {\n    childNodes = convertItemsToNodes(items);\n  }\n  return parseChildren(childNodes, keyPath);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_typeof", "_toConsumableArray", "_excluded", "React", "toArray", "Divider", "MenuItem", "MenuItemGroup", "SubMenu", "parse<PERSON><PERSON><PERSON>n", "children", "keyP<PERSON>", "map", "child", "index", "isValidElement", "_child$props$eventKey", "_child$props", "key", "eventKey", "props", "emptyKey", "undefined", "concat", "join", "cloneProps", "process", "env", "NODE_ENV", "<PERSON><PERSON><PERSON>", "cloneElement", "convertItemsToNodes", "list", "opt", "label", "type", "restProps", "mergedKey", "createElement", "title", "filter", "parseItems", "items", "childNodes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/utils/nodeUtil.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { Divider, MenuItem, MenuItemGroup, SubMenu } from '..';\nexport function parseChildren(children, keyPath) {\n  return toArray(children).map(function (child, index) {\n    if ( /*#__PURE__*/React.isValidElement(child)) {\n      var _child$props$eventKey, _child$props;\n\n      var key = child.key;\n      var eventKey = (_child$props$eventKey = (_child$props = child.props) === null || _child$props === void 0 ? void 0 : _child$props.eventKey) !== null && _child$props$eventKey !== void 0 ? _child$props$eventKey : key;\n      var emptyKey = eventKey === null || eventKey === undefined;\n\n      if (emptyKey) {\n        eventKey = \"tmp_key-\".concat([].concat(_toConsumableArray(keyPath), [index]).join('-'));\n      }\n\n      var cloneProps = {\n        key: eventKey,\n        eventKey: eventKey\n      };\n\n      if (process.env.NODE_ENV !== 'production' && emptyKey) {\n        cloneProps.warnKey = true;\n      }\n\n      return /*#__PURE__*/React.cloneElement(child, cloneProps);\n    }\n\n    return child;\n  });\n}\n\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var label = opt.label,\n          children = opt.children,\n          key = opt.key,\n          type = opt.type,\n          restProps = _objectWithoutProperties(opt, _excluded);\n\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index); // MenuItemGroup & SubMenuItem\n\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        } // Sub Menu\n\n\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      } // MenuItem & Divider\n\n\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(Divider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\n\nexport function parseItems(children, items, keyPath) {\n  var childNodes = children;\n\n  if (items) {\n    childNodes = convertItemsToNodes(items);\n  }\n\n  return parseChildren(childNodes, keyPath);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,OAAO,QAAQ,IAAI;AAC9D,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC/C,OAAOP,OAAO,CAACM,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACnD,IAAK,aAAaX,KAAK,CAACY,cAAc,CAACF,KAAK,CAAC,EAAE;MAC7C,IAAIG,qBAAqB,EAAEC,YAAY;MAEvC,IAAIC,GAAG,GAAGL,KAAK,CAACK,GAAG;MACnB,IAAIC,QAAQ,GAAG,CAACH,qBAAqB,GAAG,CAACC,YAAY,GAAGJ,KAAK,CAACO,KAAK,MAAM,IAAI,IAAIH,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,QAAQ,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGE,GAAG;MACrN,IAAIG,QAAQ,GAAGF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKG,SAAS;MAE1D,IAAID,QAAQ,EAAE;QACZF,QAAQ,GAAG,UAAU,CAACI,MAAM,CAAC,EAAE,CAACA,MAAM,CAACtB,kBAAkB,CAACU,OAAO,CAAC,EAAE,CAACG,KAAK,CAAC,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC,CAAC;MACzF;MAEA,IAAIC,UAAU,GAAG;QACfP,GAAG,EAAEC,QAAQ;QACbA,QAAQ,EAAEA;MACZ,CAAC;MAED,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIP,QAAQ,EAAE;QACrDI,UAAU,CAACI,OAAO,GAAG,IAAI;MAC3B;MAEA,OAAO,aAAa1B,KAAK,CAAC2B,YAAY,CAACjB,KAAK,EAAEY,UAAU,CAAC;IAC3D;IAEA,OAAOZ,KAAK;EACd,CAAC,CAAC;AACJ;AAEA,SAASkB,mBAAmBA,CAACC,IAAI,EAAE;EACjC,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEpB,GAAG,CAAC,UAAUqB,GAAG,EAAEnB,KAAK,EAAE;IAC5C,IAAImB,GAAG,IAAIjC,OAAO,CAACiC,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIC,KAAK,GAAGD,GAAG,CAACC,KAAK;QACjBxB,QAAQ,GAAGuB,GAAG,CAACvB,QAAQ;QACvBQ,GAAG,GAAGe,GAAG,CAACf,GAAG;QACbiB,IAAI,GAAGF,GAAG,CAACE,IAAI;QACfC,SAAS,GAAGrC,wBAAwB,CAACkC,GAAG,EAAE/B,SAAS,CAAC;MAExD,IAAImC,SAAS,GAAGnB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,MAAM,CAACK,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC;;MAE7E,IAAIJ,QAAQ,IAAIyB,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB;UACA,OAAO,aAAahC,KAAK,CAACmC,aAAa,CAAC/B,aAAa,EAAET,QAAQ,CAAC;YAC9DoB,GAAG,EAAEmB;UACP,CAAC,EAAED,SAAS,EAAE;YACZG,KAAK,EAAEL;UACT,CAAC,CAAC,EAAEH,mBAAmB,CAACrB,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;;QAGF,OAAO,aAAaP,KAAK,CAACmC,aAAa,CAAC9B,OAAO,EAAEV,QAAQ,CAAC;UACxDoB,GAAG,EAAEmB;QACP,CAAC,EAAED,SAAS,EAAE;UACZG,KAAK,EAAEL;QACT,CAAC,CAAC,EAAEH,mBAAmB,CAACrB,QAAQ,CAAC,CAAC;MACpC,CAAC,CAAC;;MAGF,IAAIyB,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,aAAahC,KAAK,CAACmC,aAAa,CAACjC,OAAO,EAAEP,QAAQ,CAAC;UACxDoB,GAAG,EAAEmB;QACP,CAAC,EAAED,SAAS,CAAC,CAAC;MAChB;MAEA,OAAO,aAAajC,KAAK,CAACmC,aAAa,CAAChC,QAAQ,EAAER,QAAQ,CAAC;QACzDoB,GAAG,EAAEmB;MACP,CAAC,EAAED,SAAS,CAAC,EAAEF,KAAK,CAAC;IACvB;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUP,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AAEA,OAAO,SAASQ,UAAUA,CAAC/B,QAAQ,EAAEgC,KAAK,EAAE/B,OAAO,EAAE;EACnD,IAAIgC,UAAU,GAAGjC,QAAQ;EAEzB,IAAIgC,KAAK,EAAE;IACTC,UAAU,GAAGZ,mBAAmB,CAACW,KAAK,CAAC;EACzC;EAEA,OAAOjC,aAAa,CAACkC,UAAU,EAAEhC,OAAO,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}