{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Button from '../button';\nimport { ConfigContext } from '../config-provider';\nimport { useCompactItemContext } from '../space/Compact';\nimport Dropdown from './dropdown';\nimport Space from '../space';\nvar DropdownButton = function DropdownButton(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getContextPopupContainer = _React$useContext.getPopupContainer,\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'default' : _props$type,\n    danger = props.danger,\n    disabled = props.disabled,\n    loading = props.loading,\n    onClick = props.onClick,\n    htmlType = props.htmlType,\n    children = props.children,\n    className = props.className,\n    menu = props.menu,\n    arrow = props.arrow,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    trigger = props.trigger,\n    align = props.align,\n    visible = props.visible,\n    open = props.open,\n    onVisibleChange = props.onVisibleChange,\n    onOpenChange = props.onOpenChange,\n    placement = props.placement,\n    getPopupContainer = props.getPopupContainer,\n    href = props.href,\n    _props$icon = props.icon,\n    icon = _props$icon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$icon,\n    title = props.title,\n    _props$buttonsRender = props.buttonsRender,\n    buttonsRender = _props$buttonsRender === void 0 ? function (buttons) {\n      return buttons;\n    } : _props$buttonsRender,\n    mouseEnterDelay = props.mouseEnterDelay,\n    mouseLeaveDelay = props.mouseLeaveDelay,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    destroyPopupOnHide = props.destroyPopupOnHide,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"danger\", \"disabled\", \"loading\", \"onClick\", \"htmlType\", \"children\", \"className\", \"menu\", \"arrow\", \"autoFocus\", \"overlay\", \"trigger\", \"align\", \"visible\", \"open\", \"onVisibleChange\", \"onOpenChange\", \"placement\", \"getPopupContainer\", \"href\", \"icon\", \"title\", \"buttonsRender\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayClassName\", \"overlayStyle\", \"destroyPopupOnHide\"]);\n  var prefixCls = getPrefixCls('dropdown-button', customizePrefixCls);\n  var dropdownProps = {\n    menu: menu,\n    arrow: arrow,\n    autoFocus: autoFocus,\n    align: align,\n    overlay: overlay,\n    disabled: disabled,\n    trigger: disabled ? [] : trigger,\n    onOpenChange: onOpenChange || onVisibleChange,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay,\n    overlayClassName: overlayClassName,\n    overlayStyle: overlayStyle,\n    destroyPopupOnHide: destroyPopupOnHide\n  };\n  var _useCompactItemContex = useCompactItemContext(prefixCls, direction),\n    compactSize = _useCompactItemContex.compactSize,\n    compactItemClassnames = _useCompactItemContex.compactItemClassnames;\n  var classes = classNames(prefixCls, compactItemClassnames, className);\n  if ('open' in props) {\n    dropdownProps.open = open;\n  } else if ('visible' in props) {\n    dropdownProps.open = visible;\n  }\n  if ('placement' in props) {\n    dropdownProps.placement = placement;\n  } else {\n    dropdownProps.placement = direction === 'rtl' ? 'bottomLeft' : 'bottomRight';\n  }\n  var leftButton = /*#__PURE__*/React.createElement(Button, {\n    type: type,\n    danger: danger,\n    disabled: disabled,\n    loading: loading,\n    onClick: onClick,\n    htmlType: htmlType,\n    href: href,\n    title: title\n  }, children);\n  var rightButton = /*#__PURE__*/React.createElement(Button, {\n    type: type,\n    danger: danger,\n    icon: icon\n  });\n  var _buttonsRender = buttonsRender([leftButton, rightButton]),\n    _buttonsRender2 = _slicedToArray(_buttonsRender, 2),\n    leftButtonToRender = _buttonsRender2[0],\n    rightButtonToRender = _buttonsRender2[1];\n  return /*#__PURE__*/React.createElement(Space.Compact, _extends({\n    className: classes,\n    size: compactSize,\n    block: true\n  }, restProps), leftButtonToRender, /*#__PURE__*/React.createElement(Dropdown, _extends({}, dropdownProps), rightButtonToRender));\n};\nDropdownButton.__ANT_BUTTON = true;\nexport default DropdownButton;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "EllipsisOutlined", "classNames", "React", "<PERSON><PERSON>", "ConfigContext", "useCompactItemContext", "Dropdown", "Space", "DropdownButton", "props", "_React$useContext", "useContext", "getContextPopupContainer", "getPopupContainer", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$type", "type", "danger", "disabled", "loading", "onClick", "htmlType", "children", "className", "menu", "arrow", "autoFocus", "overlay", "trigger", "align", "visible", "open", "onVisibleChange", "onOpenChange", "placement", "href", "_props$icon", "icon", "createElement", "title", "_props$buttonsRender", "buttonsRender", "buttons", "mouseEnterDelay", "mouseLeaveDelay", "overlayClassName", "overlayStyle", "destroyPopupOnHide", "restProps", "dropdownProps", "_useCompactItemContex", "compactSize", "compactItemClassnames", "classes", "leftButton", "rightB<PERSON>on", "_buttonsRender", "_buttonsRender2", "leftButtonToRender", "rightButtonToRender", "Compact", "size", "block", "__ANT_BUTTON"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/dropdown/dropdown-button.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Button from '../button';\nimport { ConfigContext } from '../config-provider';\nimport { useCompactItemContext } from '../space/Compact';\nimport Dropdown from './dropdown';\nimport Space from '../space';\nvar DropdownButton = function DropdownButton(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getContextPopupContainer = _React$useContext.getPopupContainer,\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'default' : _props$type,\n    danger = props.danger,\n    disabled = props.disabled,\n    loading = props.loading,\n    onClick = props.onClick,\n    htmlType = props.htmlType,\n    children = props.children,\n    className = props.className,\n    menu = props.menu,\n    arrow = props.arrow,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    trigger = props.trigger,\n    align = props.align,\n    visible = props.visible,\n    open = props.open,\n    onVisibleChange = props.onVisibleChange,\n    onOpenChange = props.onOpenChange,\n    placement = props.placement,\n    getPopupContainer = props.getPopupContainer,\n    href = props.href,\n    _props$icon = props.icon,\n    icon = _props$icon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$icon,\n    title = props.title,\n    _props$buttonsRender = props.buttonsRender,\n    buttonsRender = _props$buttonsRender === void 0 ? function (buttons) {\n      return buttons;\n    } : _props$buttonsRender,\n    mouseEnterDelay = props.mouseEnterDelay,\n    mouseLeaveDelay = props.mouseLeaveDelay,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    destroyPopupOnHide = props.destroyPopupOnHide,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"danger\", \"disabled\", \"loading\", \"onClick\", \"htmlType\", \"children\", \"className\", \"menu\", \"arrow\", \"autoFocus\", \"overlay\", \"trigger\", \"align\", \"visible\", \"open\", \"onVisibleChange\", \"onOpenChange\", \"placement\", \"getPopupContainer\", \"href\", \"icon\", \"title\", \"buttonsRender\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayClassName\", \"overlayStyle\", \"destroyPopupOnHide\"]);\n  var prefixCls = getPrefixCls('dropdown-button', customizePrefixCls);\n  var dropdownProps = {\n    menu: menu,\n    arrow: arrow,\n    autoFocus: autoFocus,\n    align: align,\n    overlay: overlay,\n    disabled: disabled,\n    trigger: disabled ? [] : trigger,\n    onOpenChange: onOpenChange || onVisibleChange,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay,\n    overlayClassName: overlayClassName,\n    overlayStyle: overlayStyle,\n    destroyPopupOnHide: destroyPopupOnHide\n  };\n  var _useCompactItemContex = useCompactItemContext(prefixCls, direction),\n    compactSize = _useCompactItemContex.compactSize,\n    compactItemClassnames = _useCompactItemContex.compactItemClassnames;\n  var classes = classNames(prefixCls, compactItemClassnames, className);\n  if ('open' in props) {\n    dropdownProps.open = open;\n  } else if ('visible' in props) {\n    dropdownProps.open = visible;\n  }\n  if ('placement' in props) {\n    dropdownProps.placement = placement;\n  } else {\n    dropdownProps.placement = direction === 'rtl' ? 'bottomLeft' : 'bottomRight';\n  }\n  var leftButton = /*#__PURE__*/React.createElement(Button, {\n    type: type,\n    danger: danger,\n    disabled: disabled,\n    loading: loading,\n    onClick: onClick,\n    htmlType: htmlType,\n    href: href,\n    title: title\n  }, children);\n  var rightButton = /*#__PURE__*/React.createElement(Button, {\n    type: type,\n    danger: danger,\n    icon: icon\n  });\n  var _buttonsRender = buttonsRender([leftButton, rightButton]),\n    _buttonsRender2 = _slicedToArray(_buttonsRender, 2),\n    leftButtonToRender = _buttonsRender2[0],\n    rightButtonToRender = _buttonsRender2[1];\n  return /*#__PURE__*/React.createElement(Space.Compact, _extends({\n    className: classes,\n    size: compactSize,\n    block: true\n  }, restProps), leftButtonToRender, /*#__PURE__*/React.createElement(Dropdown, _extends({}, dropdownProps), rightButtonToRender));\n};\nDropdownButton.__ANT_BUTTON = true;\nexport default DropdownButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,KAAK,MAAM,UAAU;AAC5B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,iBAAiB,GAAGR,KAAK,CAACS,UAAU,CAACP,aAAa,CAAC;IACrDQ,wBAAwB,GAAGF,iBAAiB,CAACG,iBAAiB;IAC9DC,YAAY,GAAGJ,iBAAiB,CAACI,YAAY;IAC7CC,SAAS,GAAGL,iBAAiB,CAACK,SAAS;EACzC,IAAIC,kBAAkB,GAAGP,KAAK,CAACQ,SAAS;IACtCC,WAAW,GAAGT,KAAK,CAACU,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,WAAW;IACvDE,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,IAAI,GAAGlB,KAAK,CAACkB,IAAI;IACjBC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IACnBC,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IAC3BC,OAAO,GAAGrB,KAAK,CAACqB,OAAO;IACvBC,OAAO,GAAGtB,KAAK,CAACsB,OAAO;IACvBC,KAAK,GAAGvB,KAAK,CAACuB,KAAK;IACnBC,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBC,IAAI,GAAGzB,KAAK,CAACyB,IAAI;IACjBC,eAAe,GAAG1B,KAAK,CAAC0B,eAAe;IACvCC,YAAY,GAAG3B,KAAK,CAAC2B,YAAY;IACjCC,SAAS,GAAG5B,KAAK,CAAC4B,SAAS;IAC3BxB,iBAAiB,GAAGJ,KAAK,CAACI,iBAAiB;IAC3CyB,IAAI,GAAG7B,KAAK,CAAC6B,IAAI;IACjBC,WAAW,GAAG9B,KAAK,CAAC+B,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,aAAarC,KAAK,CAACuC,aAAa,CAACzC,gBAAgB,EAAE,IAAI,CAAC,GAAGuC,WAAW;IACtGG,KAAK,GAAGjC,KAAK,CAACiC,KAAK;IACnBC,oBAAoB,GAAGlC,KAAK,CAACmC,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,UAAUE,OAAO,EAAE;MACnE,OAAOA,OAAO;IAChB,CAAC,GAAGF,oBAAoB;IACxBG,eAAe,GAAGrC,KAAK,CAACqC,eAAe;IACvCC,eAAe,GAAGtC,KAAK,CAACsC,eAAe;IACvCC,gBAAgB,GAAGvC,KAAK,CAACuC,gBAAgB;IACzCC,YAAY,GAAGxC,KAAK,CAACwC,YAAY;IACjCC,kBAAkB,GAAGzC,KAAK,CAACyC,kBAAkB;IAC7CC,SAAS,GAAGjE,MAAM,CAACuB,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;EACla,IAAIQ,SAAS,GAAGH,YAAY,CAAC,iBAAiB,EAAEE,kBAAkB,CAAC;EACnE,IAAIoC,aAAa,GAAG;IAClBzB,IAAI,EAAEA,IAAI;IACVC,KAAK,EAAEA,KAAK;IACZC,SAAS,EAAEA,SAAS;IACpBG,KAAK,EAAEA,KAAK;IACZF,OAAO,EAAEA,OAAO;IAChBT,QAAQ,EAAEA,QAAQ;IAClBU,OAAO,EAAEV,QAAQ,GAAG,EAAE,GAAGU,OAAO;IAChCK,YAAY,EAAEA,YAAY,IAAID,eAAe;IAC7CtB,iBAAiB,EAAEA,iBAAiB,IAAID,wBAAwB;IAChEkC,eAAe,EAAEA,eAAe;IAChCC,eAAe,EAAEA,eAAe;IAChCC,gBAAgB,EAAEA,gBAAgB;IAClCC,YAAY,EAAEA,YAAY;IAC1BC,kBAAkB,EAAEA;EACtB,CAAC;EACD,IAAIG,qBAAqB,GAAGhD,qBAAqB,CAACY,SAAS,EAAEF,SAAS,CAAC;IACrEuC,WAAW,GAAGD,qBAAqB,CAACC,WAAW;IAC/CC,qBAAqB,GAAGF,qBAAqB,CAACE,qBAAqB;EACrE,IAAIC,OAAO,GAAGvD,UAAU,CAACgB,SAAS,EAAEsC,qBAAqB,EAAE7B,SAAS,CAAC;EACrE,IAAI,MAAM,IAAIjB,KAAK,EAAE;IACnB2C,aAAa,CAAClB,IAAI,GAAGA,IAAI;EAC3B,CAAC,MAAM,IAAI,SAAS,IAAIzB,KAAK,EAAE;IAC7B2C,aAAa,CAAClB,IAAI,GAAGD,OAAO;EAC9B;EACA,IAAI,WAAW,IAAIxB,KAAK,EAAE;IACxB2C,aAAa,CAACf,SAAS,GAAGA,SAAS;EACrC,CAAC,MAAM;IACLe,aAAa,CAACf,SAAS,GAAGtB,SAAS,KAAK,KAAK,GAAG,YAAY,GAAG,aAAa;EAC9E;EACA,IAAI0C,UAAU,GAAG,aAAavD,KAAK,CAACuC,aAAa,CAACtC,MAAM,EAAE;IACxDgB,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBC,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ;IAClBc,IAAI,EAAEA,IAAI;IACVI,KAAK,EAAEA;EACT,CAAC,EAAEjB,QAAQ,CAAC;EACZ,IAAIiC,WAAW,GAAG,aAAaxD,KAAK,CAACuC,aAAa,CAACtC,MAAM,EAAE;IACzDgB,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdoB,IAAI,EAAEA;EACR,CAAC,CAAC;EACF,IAAImB,cAAc,GAAGf,aAAa,CAAC,CAACa,UAAU,EAAEC,WAAW,CAAC,CAAC;IAC3DE,eAAe,GAAG3E,cAAc,CAAC0E,cAAc,EAAE,CAAC,CAAC;IACnDE,kBAAkB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;EAC1C,OAAO,aAAa1D,KAAK,CAACuC,aAAa,CAAClC,KAAK,CAACwD,OAAO,EAAE/E,QAAQ,CAAC;IAC9D0C,SAAS,EAAE8B,OAAO;IAClBQ,IAAI,EAAEV,WAAW;IACjBW,KAAK,EAAE;EACT,CAAC,EAAEd,SAAS,CAAC,EAAEU,kBAAkB,EAAE,aAAa3D,KAAK,CAACuC,aAAa,CAACnC,QAAQ,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAEoE,aAAa,CAAC,EAAEU,mBAAmB,CAAC,CAAC;AAClI,CAAC;AACDtD,cAAc,CAAC0D,YAAY,GAAG,IAAI;AAClC,eAAe1D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}