{"ast": null, "code": "import rcWarning, { resetWarned } from \"rc-util/es/warning\";\nexport { resetWarned };\nexport function noop() {}\n// eslint-disable-next-line import/no-mutable-exports\nvar warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  warning = function warning(valid, component, message) {\n    rcWarning(valid, \"[antd: \".concat(component, \"] \").concat(message));\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nexport default warning;", "map": {"version": 3, "names": ["rc<PERSON><PERSON>ning", "resetWarned", "noop", "warning", "process", "env", "NODE_ENV", "valid", "component", "message", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/warning.js"], "sourcesContent": ["import rcWarning, { resetWarned } from \"rc-util/es/warning\";\nexport { resetWarned };\nexport function noop() {}\n// eslint-disable-next-line import/no-mutable-exports\nvar warning = noop;\nif (process.env.NODE_ENV !== 'production') {\n  warning = function warning(valid, component, message) {\n    rcWarning(valid, \"[antd: \".concat(component, \"] \").concat(message));\n    // StrictMode will inject console which will not throw warning in React 17.\n    if (process.env.NODE_ENV === 'test') {\n      resetWarned();\n    }\n  };\n}\nexport default warning;"], "mappings": "AAAA,OAAOA,SAAS,IAAIC,WAAW,QAAQ,oBAAoB;AAC3D,SAASA,WAAW;AACpB,OAAO,SAASC,IAAIA,CAAA,EAAG,CAAC;AACxB;AACA,IAAIC,OAAO,GAAGD,IAAI;AAClB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,OAAO,GAAG,SAASA,OAAOA,CAACI,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;IACpDT,SAAS,CAACO,KAAK,EAAE,SAAS,CAACG,MAAM,CAACF,SAAS,EAAE,IAAI,CAAC,CAACE,MAAM,CAACD,OAAO,CAAC,CAAC;IACnE;IACA,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCL,WAAW,CAAC,CAAC;IACf;EACF,CAAC;AACH;AACA,eAAeE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}