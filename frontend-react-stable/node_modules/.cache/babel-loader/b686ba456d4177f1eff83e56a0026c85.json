{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport TimeHeader from './TimeHeader';\nimport TimeBody from './TimeBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nvar countBoolean = function countBoolean(boolList) {\n  return boolList.filter(function (bool) {\n    return bool !== false;\n  }).length;\n};\nfunction TimePanel(props) {\n  var generateConfig = props.generateConfig,\n    _props$format = props.format,\n    format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,\n    prefixCls = props.prefixCls,\n    active = props.active,\n    operationRef = props.operationRef,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    _props$use12Hours = props.use12Hours,\n    use12Hours = _props$use12Hours === void 0 ? false : _props$use12Hours,\n    onSelect = props.onSelect,\n    value = props.value;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var bodyOperationRef = React.useRef();\n  // ======================= Keyboard =======================\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeColumnIndex = _React$useState2[0],\n    setActiveColumnIndex = _React$useState2[1];\n  var columnsCount = countBoolean([showHour, showMinute, showSecond, use12Hours]);\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          setActiveColumnIndex((activeColumnIndex + diff + columnsCount) % columnsCount);\n        },\n        onUpDown: function onUpDown(diff) {\n          if (activeColumnIndex === -1) {\n            setActiveColumnIndex(0);\n          } else if (bodyOperationRef.current) {\n            bodyOperationRef.current.onUpDown(diff);\n          }\n        },\n        onEnter: function onEnter() {\n          onSelect(value || generateConfig.getNow(), 'key');\n          setActiveColumnIndex(-1);\n        }\n      });\n    },\n    onBlur: function onBlur() {\n      setActiveColumnIndex(-1);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls, _defineProperty({}, \"\".concat(panelPrefixCls, \"-active\"), active))\n  }, /*#__PURE__*/React.createElement(TimeHeader, _extends({}, props, {\n    format: format,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(TimeBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    activeColumnIndex: activeColumnIndex,\n    operationRef: bodyOperationRef\n  })));\n}\nexport default TimePanel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "React", "classNames", "TimeHeader", "TimeBody", "createKeyDownHandler", "countBoolean", "boolList", "filter", "bool", "length", "TimePanel", "props", "generateConfig", "_props$format", "format", "prefixCls", "active", "operationRef", "showHour", "showMinute", "showSecond", "_props$use12Hours", "use12Hours", "onSelect", "value", "panelPrefixCls", "concat", "bodyOperationRef", "useRef", "_React$useState", "useState", "_React$useState2", "activeColumnIndex", "setActiveColumnIndex", "columnsCount", "current", "onKeyDown", "event", "onLeftRight", "diff", "onUpDown", "onEnter", "getNow", "onBlur", "createElement", "className"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/TimePanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport TimeHeader from './TimeHeader';\nimport TimeBody from './TimeBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nvar countBoolean = function countBoolean(boolList) {\n  return boolList.filter(function (bool) {\n    return bool !== false;\n  }).length;\n};\nfunction TimePanel(props) {\n  var generateConfig = props.generateConfig,\n    _props$format = props.format,\n    format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,\n    prefixCls = props.prefixCls,\n    active = props.active,\n    operationRef = props.operationRef,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    _props$use12Hours = props.use12Hours,\n    use12Hours = _props$use12Hours === void 0 ? false : _props$use12Hours,\n    onSelect = props.onSelect,\n    value = props.value;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var bodyOperationRef = React.useRef();\n  // ======================= Keyboard =======================\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeColumnIndex = _React$useState2[0],\n    setActiveColumnIndex = _React$useState2[1];\n  var columnsCount = countBoolean([showHour, showMinute, showSecond, use12Hours]);\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          setActiveColumnIndex((activeColumnIndex + diff + columnsCount) % columnsCount);\n        },\n        onUpDown: function onUpDown(diff) {\n          if (activeColumnIndex === -1) {\n            setActiveColumnIndex(0);\n          } else if (bodyOperationRef.current) {\n            bodyOperationRef.current.onUpDown(diff);\n          }\n        },\n        onEnter: function onEnter() {\n          onSelect(value || generateConfig.getNow(), 'key');\n          setActiveColumnIndex(-1);\n        }\n      });\n    },\n    onBlur: function onBlur() {\n      setActiveColumnIndex(-1);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls, _defineProperty({}, \"\".concat(panelPrefixCls, \"-active\"), active))\n  }, /*#__PURE__*/React.createElement(TimeHeader, _extends({}, props, {\n    format: format,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(TimeBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    activeColumnIndex: activeColumnIndex,\n    operationRef: bodyOperationRef\n  })));\n}\nexport default TimePanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,QAAQ,EAAE;EACjD,OAAOA,QAAQ,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IACrC,OAAOA,IAAI,KAAK,KAAK;EACvB,CAAC,CAAC,CAACC,MAAM;AACX,CAAC;AACD,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,cAAc,GAAGD,KAAK,CAACC,cAAc;IACvCC,aAAa,GAAGF,KAAK,CAACG,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,aAAa;IAC9DE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,UAAU,GAAGT,KAAK,CAACS,UAAU;IAC7BC,iBAAiB,GAAGV,KAAK,CAACW,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,iBAAiB;IACrEE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,KAAK,GAAGb,KAAK,CAACa,KAAK;EACrB,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACX,SAAS,EAAE,aAAa,CAAC;EACxD,IAAIY,gBAAgB,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EACrC;EACA,IAAIC,eAAe,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGhC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDG,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAIG,YAAY,GAAG7B,YAAY,CAAC,CAACa,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEE,UAAU,CAAC,CAAC;EAC/EL,YAAY,CAACkB,OAAO,GAAG;IACrBC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;MACnC,OAAOjC,oBAAoB,CAACiC,KAAK,EAAE;QACjCC,WAAW,EAAE,SAASA,WAAWA,CAACC,IAAI,EAAE;UACtCN,oBAAoB,CAAC,CAACD,iBAAiB,GAAGO,IAAI,GAAGL,YAAY,IAAIA,YAAY,CAAC;QAChF,CAAC;QACDM,QAAQ,EAAE,SAASA,QAAQA,CAACD,IAAI,EAAE;UAChC,IAAIP,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5BC,oBAAoB,CAAC,CAAC,CAAC;UACzB,CAAC,MAAM,IAAIN,gBAAgB,CAACQ,OAAO,EAAE;YACnCR,gBAAgB,CAACQ,OAAO,CAACK,QAAQ,CAACD,IAAI,CAAC;UACzC;QACF,CAAC;QACDE,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1BlB,QAAQ,CAACC,KAAK,IAAIZ,cAAc,CAAC8B,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC;UACjDT,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IACDU,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;MACxBV,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EACD,OAAO,aAAajC,KAAK,CAAC4C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE5C,UAAU,CAACwB,cAAc,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAACD,cAAc,EAAE,SAAS,CAAC,EAAET,MAAM,CAAC;EACzG,CAAC,EAAE,aAAahB,KAAK,CAAC4C,aAAa,CAAC1C,UAAU,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,EAAE;IAClEG,MAAM,EAAEA,MAAM;IACdC,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,EAAE,aAAaf,KAAK,CAAC4C,aAAa,CAACzC,QAAQ,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,EAAE;IAClEI,SAAS,EAAEA,SAAS;IACpBiB,iBAAiB,EAAEA,iBAAiB;IACpCf,YAAY,EAAEU;EAChB,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}