{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport ReadOutlinedSvg from \"@ant-design/icons-svg/es/asn/ReadOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar ReadOutlined = function ReadOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: ReadOutlinedSvg\n  }));\n};\nReadOutlined.displayName = 'ReadOutlined';\nexport default /*#__PURE__*/React.forwardRef(ReadOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "ReadOutlinedSvg", "AntdIcon", "ReadOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/ReadOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport ReadOutlinedSvg from \"@ant-design/icons-svg/es/asn/ReadOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar ReadOutlined = function ReadOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: ReadOutlinedSvg\n  }));\n};\nReadOutlined.displayName = 'ReadOutlined';\nexport default /*#__PURE__*/React.forwardRef(ReadOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,YAAY,CAACK,WAAW,GAAG,cAAc;AACzC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}