{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DescriptionsContext } from '.';\nimport Cell from './Cell';\nfunction renderCells(items, _ref, _ref2) {\n  var colon = _ref.colon,\n    prefixCls = _ref.prefixCls,\n    bordered = _ref.bordered;\n  var component = _ref2.component,\n    type = _ref2.type,\n    showLabel = _ref2.showLabel,\n    showContent = _ref2.showContent,\n    rootLabelStyle = _ref2.labelStyle,\n    rootContentStyle = _ref2.contentStyle;\n  return items.map(function (_ref3, index) {\n    var _ref3$props = _ref3.props,\n      label = _ref3$props.label,\n      children = _ref3$props.children,\n      _ref3$props$prefixCls = _ref3$props.prefixCls,\n      itemPrefixCls = _ref3$props$prefixCls === void 0 ? prefixCls : _ref3$props$prefixCls,\n      className = _ref3$props.className,\n      style = _ref3$props.style,\n      labelStyle = _ref3$props.labelStyle,\n      contentStyle = _ref3$props.contentStyle,\n      _ref3$props$span = _ref3$props.span,\n      span = _ref3$props$span === void 0 ? 1 : _ref3$props$span,\n      key = _ref3.key;\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: \"\".concat(type, \"-\").concat(key || index),\n        className: className,\n        style: style,\n        labelStyle: _extends(_extends({}, rootLabelStyle), labelStyle),\n        contentStyle: _extends(_extends({}, rootContentStyle), contentStyle),\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: \"label-\".concat(key || index),\n      className: className,\n      style: _extends(_extends(_extends({}, rootLabelStyle), style), labelStyle),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: \"content-\".concat(key || index),\n      className: className,\n      style: _extends(_extends(_extends({}, rootContentStyle), style), contentStyle),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children\n    })];\n  });\n}\nvar Row = function Row(props) {\n  var descContext = React.useContext(DescriptionsContext);\n  var prefixCls = props.prefixCls,\n    vertical = props.vertical,\n    row = props.row,\n    index = props.index,\n    bordered = props.bordered;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: \"label-\".concat(index),\n      className: \"\".concat(prefixCls, \"-row\")\n    }, renderCells(row, props, _extends({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: \"content-\".concat(index),\n      className: \"\".concat(prefixCls, \"-row\")\n    }, renderCells(row, props, _extends({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: \"\".concat(prefixCls, \"-row\")\n  }, renderCells(row, props, _extends({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;", "map": {"version": 3, "names": ["_extends", "React", "DescriptionsContext", "Cell", "renderCells", "items", "_ref", "_ref2", "colon", "prefixCls", "bordered", "component", "type", "showLabel", "showContent", "rootLabelStyle", "labelStyle", "rootContentStyle", "contentStyle", "map", "_ref3", "index", "_ref3$props", "props", "label", "children", "_ref3$props$prefixCls", "itemPrefixCls", "className", "style", "_ref3$props$span", "span", "key", "createElement", "concat", "content", "Row", "descContext", "useContext", "vertical", "row", "Fragment"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/descriptions/Row.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DescriptionsContext } from '.';\nimport Cell from './Cell';\nfunction renderCells(items, _ref, _ref2) {\n  var colon = _ref.colon,\n    prefixCls = _ref.prefixCls,\n    bordered = _ref.bordered;\n  var component = _ref2.component,\n    type = _ref2.type,\n    showLabel = _ref2.showLabel,\n    showContent = _ref2.showContent,\n    rootLabelStyle = _ref2.labelStyle,\n    rootContentStyle = _ref2.contentStyle;\n  return items.map(function (_ref3, index) {\n    var _ref3$props = _ref3.props,\n      label = _ref3$props.label,\n      children = _ref3$props.children,\n      _ref3$props$prefixCls = _ref3$props.prefixCls,\n      itemPrefixCls = _ref3$props$prefixCls === void 0 ? prefixCls : _ref3$props$prefixCls,\n      className = _ref3$props.className,\n      style = _ref3$props.style,\n      labelStyle = _ref3$props.labelStyle,\n      contentStyle = _ref3$props.contentStyle,\n      _ref3$props$span = _ref3$props.span,\n      span = _ref3$props$span === void 0 ? 1 : _ref3$props$span,\n      key = _ref3.key;\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: \"\".concat(type, \"-\").concat(key || index),\n        className: className,\n        style: style,\n        labelStyle: _extends(_extends({}, rootLabelStyle), labelStyle),\n        contentStyle: _extends(_extends({}, rootContentStyle), contentStyle),\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: \"label-\".concat(key || index),\n      className: className,\n      style: _extends(_extends(_extends({}, rootLabelStyle), style), labelStyle),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: \"content-\".concat(key || index),\n      className: className,\n      style: _extends(_extends(_extends({}, rootContentStyle), style), contentStyle),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children\n    })];\n  });\n}\nvar Row = function Row(props) {\n  var descContext = React.useContext(DescriptionsContext);\n  var prefixCls = props.prefixCls,\n    vertical = props.vertical,\n    row = props.row,\n    index = props.index,\n    bordered = props.bordered;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: \"label-\".concat(index),\n      className: \"\".concat(prefixCls, \"-row\")\n    }, renderCells(row, props, _extends({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: \"content-\".concat(index),\n      className: \"\".concat(prefixCls, \"-row\")\n    }, renderCells(row, props, _extends({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: \"\".concat(prefixCls, \"-row\")\n  }, renderCells(row, props, _extends({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,GAAG;AACvC,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACvC,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpBC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC1B,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,IAAI,GAAGL,KAAK,CAACK,IAAI;IACjBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,cAAc,GAAGR,KAAK,CAACS,UAAU;IACjCC,gBAAgB,GAAGV,KAAK,CAACW,YAAY;EACvC,OAAOb,KAAK,CAACc,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACvC,IAAIC,WAAW,GAAGF,KAAK,CAACG,KAAK;MAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;MACzBC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;MAC/BC,qBAAqB,GAAGJ,WAAW,CAACb,SAAS;MAC7CkB,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGjB,SAAS,GAAGiB,qBAAqB;MACpFE,SAAS,GAAGN,WAAW,CAACM,SAAS;MACjCC,KAAK,GAAGP,WAAW,CAACO,KAAK;MACzBb,UAAU,GAAGM,WAAW,CAACN,UAAU;MACnCE,YAAY,GAAGI,WAAW,CAACJ,YAAY;MACvCY,gBAAgB,GAAGR,WAAW,CAACS,IAAI;MACnCA,IAAI,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;MACzDE,GAAG,GAAGZ,KAAK,CAACY,GAAG;IACjB,IAAI,OAAOrB,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAO,aAAaV,KAAK,CAACgC,aAAa,CAAC9B,IAAI,EAAE;QAC5C6B,GAAG,EAAE,EAAE,CAACE,MAAM,CAACtB,IAAI,EAAE,GAAG,CAAC,CAACsB,MAAM,CAACF,GAAG,IAAIX,KAAK,CAAC;QAC9CO,SAAS,EAAEA,SAAS;QACpBC,KAAK,EAAEA,KAAK;QACZb,UAAU,EAAEhB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEe,cAAc,CAAC,EAAEC,UAAU,CAAC;QAC9DE,YAAY,EAAElB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiB,gBAAgB,CAAC,EAAEC,YAAY,CAAC;QACpEa,IAAI,EAAEA,IAAI;QACVvB,KAAK,EAAEA,KAAK;QACZG,SAAS,EAAEA,SAAS;QACpBgB,aAAa,EAAEA,aAAa;QAC5BjB,QAAQ,EAAEA,QAAQ;QAClBc,KAAK,EAAEX,SAAS,GAAGW,KAAK,GAAG,IAAI;QAC/BW,OAAO,EAAErB,WAAW,GAAGW,QAAQ,GAAG;MACpC,CAAC,CAAC;IACJ;IACA,OAAO,CAAC,aAAaxB,KAAK,CAACgC,aAAa,CAAC9B,IAAI,EAAE;MAC7C6B,GAAG,EAAE,QAAQ,CAACE,MAAM,CAACF,GAAG,IAAIX,KAAK,CAAC;MAClCO,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAE7B,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEe,cAAc,CAAC,EAAEc,KAAK,CAAC,EAAEb,UAAU,CAAC;MAC1Ee,IAAI,EAAE,CAAC;MACPvB,KAAK,EAAEA,KAAK;MACZG,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBgB,aAAa,EAAEA,aAAa;MAC5BjB,QAAQ,EAAEA,QAAQ;MAClBc,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE,aAAavB,KAAK,CAACgC,aAAa,CAAC9B,IAAI,EAAE;MACzC6B,GAAG,EAAE,UAAU,CAACE,MAAM,CAACF,GAAG,IAAIX,KAAK,CAAC;MACpCO,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAE7B,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiB,gBAAgB,CAAC,EAAEY,KAAK,CAAC,EAAEX,YAAY,CAAC;MAC9Ea,IAAI,EAAEA,IAAI,GAAG,CAAC,GAAG,CAAC;MAClBpB,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBgB,aAAa,EAAEA,aAAa;MAC5BjB,QAAQ,EAAEA,QAAQ;MAClByB,OAAO,EAAEV;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,IAAIW,GAAG,GAAG,SAASA,GAAGA,CAACb,KAAK,EAAE;EAC5B,IAAIc,WAAW,GAAGpC,KAAK,CAACqC,UAAU,CAACpC,mBAAmB,CAAC;EACvD,IAAIO,SAAS,GAAGc,KAAK,CAACd,SAAS;IAC7B8B,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,GAAG,GAAGjB,KAAK,CAACiB,GAAG;IACfnB,KAAK,GAAGE,KAAK,CAACF,KAAK;IACnBX,QAAQ,GAAGa,KAAK,CAACb,QAAQ;EAC3B,IAAI6B,QAAQ,EAAE;IACZ,OAAO,aAAatC,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAACwC,QAAQ,EAAE,IAAI,EAAE,aAAaxC,KAAK,CAACgC,aAAa,CAAC,IAAI,EAAE;MACnGD,GAAG,EAAE,QAAQ,CAACE,MAAM,CAACb,KAAK,CAAC;MAC3BO,SAAS,EAAE,EAAE,CAACM,MAAM,CAACzB,SAAS,EAAE,MAAM;IACxC,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEjB,KAAK,EAAEvB,QAAQ,CAAC;MAClCW,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;IACb,CAAC,EAAEwB,WAAW,CAAC,CAAC,CAAC,EAAE,aAAapC,KAAK,CAACgC,aAAa,CAAC,IAAI,EAAE;MACxDD,GAAG,EAAE,UAAU,CAACE,MAAM,CAACb,KAAK,CAAC;MAC7BO,SAAS,EAAE,EAAE,CAACM,MAAM,CAACzB,SAAS,EAAE,MAAM;IACxC,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEjB,KAAK,EAAEvB,QAAQ,CAAC;MAClCW,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,SAAS;MACfE,WAAW,EAAE;IACf,CAAC,EAAEuB,WAAW,CAAC,CAAC,CAAC,CAAC;EACpB;EACA,OAAO,aAAapC,KAAK,CAACgC,aAAa,CAAC,IAAI,EAAE;IAC5CD,GAAG,EAAEX,KAAK;IACVO,SAAS,EAAE,EAAE,CAACM,MAAM,CAACzB,SAAS,EAAE,MAAM;EACxC,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEjB,KAAK,EAAEvB,QAAQ,CAAC;IAClCW,SAAS,EAAED,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI;IACzCE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE;EACf,CAAC,EAAEuB,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,eAAeD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}