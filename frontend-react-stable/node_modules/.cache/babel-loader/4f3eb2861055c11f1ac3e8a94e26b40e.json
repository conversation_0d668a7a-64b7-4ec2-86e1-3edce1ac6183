{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/BatchTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Button, Typography, Space, Divider, message, Spin, InputNumber, Checkbox, Progress, Alert, Row, Col, Statistic, Select, List, Tag } from 'antd';\nimport { PlayCircleOutlined, SettingOutlined, FolderOutlined, FileTextOutlined } from '@ant-design/icons';\nimport { modelTrainingAPI } from '../services/api';\nimport { startBatchTrainingAsync } from '../services/taskApi';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst BatchTrainingPage = () => {\n  _s();\n  var _taskResult$summary, _taskResult$summary2, _taskResult$summary3;\n  // 状态管理\n  const [loading, setLoading] = useState(false);\n  const [csvDirectories, setCsvDirectories] = useState([]);\n  const [selectedDirectories, setSelectedDirectories] = useState([]);\n  const [baseDir, setBaseDir] = useState('/data');\n\n  // 训练参数\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip'],\n    UDP: [],\n    ICMP: []\n  });\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 任务管理\n  const {\n    currentTask,\n    isTaskRunning,\n    startTask,\n    stopTask,\n    taskProgress,\n    taskStatus,\n    taskResult\n  } = useTaskManager();\n\n  // 协议和数据类型选项\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 加载CSV目录列表\n  const loadCsvDirectories = async () => {\n    try {\n      setLoading(true);\n      const response = await modelTrainingAPI.listCsvDirectories(baseDir);\n      setCsvDirectories(response.data.directories);\n    } catch (error) {\n      console.error('加载CSV目录失败:', error);\n      message.error('加载CSV目录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadCsvDirectories();\n  }, [baseDir]);\n\n  // 处理协议选择\n  const handleProtocolChange = protocols => {\n    setSelectedProts(protocols);\n    // 清理未选择协议的数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!protocols.includes(prot)) {\n        newDatatypes[prot] = [];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 处理数据类型选择\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证表单\n  const validateForm = () => {\n    if (selectedDirectories.length === 0) {\n      message.error('请至少选择一个CSV目录');\n      return false;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return false;\n    }\n    const hasDatatype = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasDatatype) {\n      message.error('请至少为一种协议选择数据类型');\n      return false;\n    }\n    return true;\n  };\n\n  // 启动批量训练\n  const handleStartBatchTraining = async () => {\n    if (!validateForm()) return;\n    try {\n      const formData = new FormData();\n      formData.append('csv_directories', JSON.stringify(selectedDirectories));\n      formData.append('selected_prots', JSON.stringify(selectedProts));\n      formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n      formData.append('learning_rate', learningRate.toString());\n      formData.append('batch_size', batchSize.toString());\n      formData.append('epochs', epochs.toString());\n      formData.append('sequence_length', sequenceLength.toString());\n      formData.append('hidden_size', hiddenSize.toString());\n      formData.append('num_layers', numLayers.toString());\n      formData.append('dropout', dropout.toString());\n      formData.append('output_folder', outputFolder);\n      const response = await startBatchTrainingAsync(formData);\n      if (response.success) {\n        message.success(`批量训练任务已启动，任务ID: ${response.task_id}`);\n        startTask(response.task_id);\n      } else {\n        message.error('启动批量训练失败');\n      }\n    } catch (error) {\n      console.error('启动批量训练失败:', error);\n      message.error('启动批量训练失败');\n    }\n  };\n\n  // 计算总文件数\n  const getTotalFileCount = () => {\n    return selectedDirectories.reduce((total, dirPath) => {\n      const dir = csvDirectories.find(d => d.directory === dirPath);\n      return total + ((dir === null || dir === void 0 ? void 0 : dir.csv_count) || 0);\n    }, 0);\n  };\n\n  // 计算总模型数\n  const getTotalModelCount = () => {\n    const totalCombinations = selectedProts.reduce((total, prot) => {\n      var _selectedDatatypes$pr;\n      return total + (((_selectedDatatypes$pr = selectedDatatypes[prot]) === null || _selectedDatatypes$pr === void 0 ? void 0 : _selectedDatatypes$pr.length) || 0);\n    }, 0);\n    return getTotalFileCount() * totalCombinations;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), \" \\u6279\\u91CF\\u6A21\\u578B\\u8BAD\\u7EC3\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u9009\\u62E9\\u591A\\u4E2ACSV\\u76EE\\u5F55\\u8FDB\\u884C\\u6279\\u91CF\\u6A21\\u578B\\u8BAD\\u7EC3\\uFF0C\\u6BCF\\u4E2A\\u76EE\\u5F55\\u4E2D\\u7684\\u6240\\u6709CSV\\u6587\\u4EF6\\u90FD\\u5C06\\u88AB\\u7528\\u4E8E\\u8BAD\\u7EC3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 26\n            }, this), \" CSV\\u76EE\\u5F55\\u9009\\u62E9\"]\n          }, void 0, true),\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u57FA\\u7840\\u76EE\\u5F55: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: baseDir,\n                onChange: setBaseDir,\n                style: {\n                  width: 200,\n                  marginLeft: 8\n                },\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"/data\",\n                  children: \"/data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"/data/input\",\n                  children: \"/data/input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"/data/output\",\n                  children: \"/data/output\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: loadCsvDirectories,\n                loading: loading,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: loading,\n              children: /*#__PURE__*/_jsxDEV(List, {\n                dataSource: csvDirectories,\n                renderItem: dir => /*#__PURE__*/_jsxDEV(List.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    checked: selectedDirectories.includes(dir.directory),\n                    onChange: e => {\n                      if (e.target.checked) {\n                        setSelectedDirectories([...selectedDirectories, dir.directory]);\n                      } else {\n                        setSelectedDirectories(selectedDirectories.filter(d => d !== dir.directory));\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        strong: true,\n                        children: dir.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"blue\",\n                        children: [dir.csv_count, \" \\u4E2ACSV\\u6587\\u4EF6\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this),\n                locale: {\n                  emptyText: '没有找到包含CSV文件的目录'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u9009\\u62E9\\u534F\\u8BAE: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                value: selectedProts,\n                onChange: handleProtocolChange,\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                placeholder: \"\\u9009\\u62E9\\u534F\\u8BAE\",\n                children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                  value: prot,\n                  children: prot\n                }, prot, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), selectedProts.map(prot => {\n              var _datatypeOptions;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B: \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"multiple\",\n                  value: selectedDatatypes[prot] || [],\n                  onChange: datatypes => handleDatatypeChange(prot, datatypes),\n                  style: {\n                    width: '100%',\n                    marginTop: 8\n                  },\n                  placeholder: `选择 ${prot} 数据类型`,\n                  children: (_datatypeOptions = datatypeOptions[prot]) === null || _datatypeOptions === void 0 ? void 0 : _datatypeOptions.map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                    value: datatype,\n                    children: datatype\n                  }, datatype, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, prot, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 26\n            }, this), \" \\u8BAD\\u7EC3\\u53C2\\u6570\"]\n          }, void 0, true),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u5B66\\u4E60\\u7387: \", learningRate]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 0.0001,\n                    max: 0.1,\n                    step: 0.0001,\n                    value: learningRate,\n                    onChange: value => setLearningRate(value || 0.001),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u6279\\u91CF\\u5927\\u5C0F: \", batchSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 1,\n                    max: 512,\n                    value: batchSize,\n                    onChange: value => setBatchSize(value || 32),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \", epochs]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 1,\n                    max: 1000,\n                    value: epochs,\n                    onChange: value => setEpochs(value || 100),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u5E8F\\u5217\\u957F\\u5EA6: \", sequenceLength]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 5,\n                    max: 50,\n                    value: sequenceLength,\n                    onChange: value => setSequenceLength(value || 10),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F: \", hiddenSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 10,\n                    max: 200,\n                    value: hiddenSize,\n                    onChange: value => setHiddenSize(value || 50),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: [\"\\u7F51\\u7EDC\\u5C42\\u6570: \", numLayers]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 1,\n                    max: 5,\n                    value: numLayers,\n                    onChange: value => setNumLayers(value || 2),\n                    style: {\n                      width: '100%',\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BAD\\u7EC3\\u6982\\u89C8\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u9009\\u62E9\\u7684\\u76EE\\u5F55\\u6570\",\n              value: selectedDirectories.length,\n              prefix: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603BCSV\\u6587\\u4EF6\\u6570\",\n              value: getTotalFileCount(),\n              prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u9884\\u8BA1\\u8BAD\\u7EC3\\u6A21\\u578B\\u6570\",\n              value: getTotalModelCount(),\n              prefix: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u8BAD\\u7EC3\\u63A7\\u5236\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 23\n              }, this),\n              onClick: handleStartBatchTraining,\n              disabled: isTaskRunning || selectedDirectories.length === 0,\n              size: \"large\",\n              style: {\n                width: '100%'\n              },\n              children: isTaskRunning ? '训练中...' : '开始批量训练'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), isTaskRunning && /*#__PURE__*/_jsxDEV(Button, {\n              danger: true,\n              onClick: stopTask,\n              style: {\n                width: '100%'\n              },\n              children: \"\\u505C\\u6B62\\u8BAD\\u7EC3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), isTaskRunning && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: taskProgress,\n                status: taskStatus === 'failed' ? 'exception' : 'active',\n                style: {\n                  marginTop: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), currentTask && /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u4EFB\\u52A1ID: \", currentTask.task_id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), taskResult && taskStatus === 'completed' && /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u6279\\u91CF\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u6210\\u529F\\u5904\\u7406: \", ((_taskResult$summary = taskResult.summary) === null || _taskResult$summary === void 0 ? void 0 : _taskResult$summary.successful_files) || 0, \" \\u4E2A\\u6587\\u4EF6\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u5931\\u8D25\\u6587\\u4EF6: \", ((_taskResult$summary2 = taskResult.summary) === null || _taskResult$summary2 === void 0 ? void 0 : _taskResult$summary2.failed_files) || 0, \" \\u4E2A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u603B\\u8BAD\\u7EC3\\u6A21\\u578B: \", ((_taskResult$summary3 = taskResult.summary) === null || _taskResult$summary3 === void 0 ? void 0 : _taskResult$summary3.total_models) || 0, \" \\u4E2A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this),\n              type: \"success\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), taskStatus === 'failed' && /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u6279\\u91CF\\u8BAD\\u7EC3\\u5931\\u8D25\",\n              description: \"\\u8BF7\\u68C0\\u67E5\\u65E5\\u5FD7\\u83B7\\u53D6\\u8BE6\\u7EC6\\u9519\\u8BEF\\u4FE1\\u606F\",\n              type: \"error\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n_s(BatchTrainingPage, \"k+eXAsRexUvW11fpJqlqCS9NFoM=\", false, function () {\n  return [useTaskManager];\n});\n_c = BatchTrainingPage;\nexport default BatchTrainingPage;\nvar _c;\n$RefreshReg$(_c, \"BatchTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Select", "List", "Tag", "PlayCircleOutlined", "SettingOutlined", "FolderOutlined", "FileTextOutlined", "modelTrainingAPI", "startBatchTrainingAsync", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Option", "BatchTrainingPage", "_s", "_taskResult$summary", "_taskResult$summary2", "_taskResult$summary3", "loading", "setLoading", "csvDirectories", "setCsvDirectories", "selectedDirectories", "setSelectedDirectories", "baseDir", "setBaseDir", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "UDP", "ICMP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "currentTask", "isTaskRunning", "startTask", "stopTask", "taskProgress", "taskStatus", "taskResult", "protocolOptions", "datatypeOptions", "loadCsvDirectories", "response", "listCsvDirectories", "data", "directories", "error", "console", "handleProtocolChange", "protocols", "newDatatypes", "Object", "keys", "for<PERSON>ach", "prot", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "validateForm", "length", "hasDatatype", "some", "handleStartBatchTraining", "formData", "FormData", "append", "JSON", "stringify", "toString", "success", "task_id", "getTotalFileCount", "reduce", "total", "<PERSON><PERSON><PERSON>", "dir", "find", "d", "directory", "csv_count", "getTotalModelCount", "totalCombinations", "_selectedDatatypes$pr", "style", "padding", "children", "level", "ExperimentOutlined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "gutter", "span", "title", "marginBottom", "direction", "width", "strong", "value", "onChange", "marginLeft", "onClick", "spinning", "dataSource", "renderItem", "<PERSON><PERSON>", "checked", "e", "target", "filter", "name", "color", "locale", "emptyText", "mode", "marginTop", "placeholder", "map", "_datatypeOptions", "datatype", "min", "max", "step", "prefix", "icon", "disabled", "size", "danger", "percent", "status", "fontSize", "description", "summary", "successful_files", "failed_files", "total_models", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/BatchTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Select,\n  List,\n  Tag,\n  Modal,\n} from 'antd';\nimport { \n  PlayCircleOutlined, \n  SettingOutlined, \n  FolderOutlined,\n  FileTextOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { modelTrainingAPI } from '../services/api';\nimport { startBatchTrainingAsync } from '../services/taskApi';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface CsvDirectory {\n  directory: string;\n  name: string;\n  csv_count: number;\n  csv_files: string[];\n}\n\nconst BatchTrainingPage: React.FC = () => {\n  // 状态管理\n  const [loading, setLoading] = useState(false);\n  const [csvDirectories, setCsvDirectories] = useState<CsvDirectory[]>([]);\n  const [selectedDirectories, setSelectedDirectories] = useState<string[]>([]);\n  const [baseDir, setBaseDir] = useState('/data');\n  \n  // 训练参数\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip'],\n    UDP: [],\n    ICMP: []\n  });\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 任务管理\n  const { \n    currentTask, \n    isTaskRunning, \n    startTask, \n    stopTask, \n    taskProgress, \n    taskStatus,\n    taskResult \n  } = useTaskManager();\n\n  // 协议和数据类型选项\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 加载CSV目录列表\n  const loadCsvDirectories = async () => {\n    try {\n      setLoading(true);\n      const response = await modelTrainingAPI.listCsvDirectories(baseDir);\n      setCsvDirectories(response.data.directories);\n    } catch (error) {\n      console.error('加载CSV目录失败:', error);\n      message.error('加载CSV目录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadCsvDirectories();\n  }, [baseDir]);\n\n  // 处理协议选择\n  const handleProtocolChange = (protocols: string[]) => {\n    setSelectedProts(protocols);\n    // 清理未选择协议的数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!protocols.includes(prot)) {\n        newDatatypes[prot] = [];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 处理数据类型选择\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证表单\n  const validateForm = () => {\n    if (selectedDirectories.length === 0) {\n      message.error('请至少选择一个CSV目录');\n      return false;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return false;\n    }\n    const hasDatatype = selectedProts.some(prot => \n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n    if (!hasDatatype) {\n      message.error('请至少为一种协议选择数据类型');\n      return false;\n    }\n    return true;\n  };\n\n  // 启动批量训练\n  const handleStartBatchTraining = async () => {\n    if (!validateForm()) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('csv_directories', JSON.stringify(selectedDirectories));\n      formData.append('selected_prots', JSON.stringify(selectedProts));\n      formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n      formData.append('learning_rate', learningRate.toString());\n      formData.append('batch_size', batchSize.toString());\n      formData.append('epochs', epochs.toString());\n      formData.append('sequence_length', sequenceLength.toString());\n      formData.append('hidden_size', hiddenSize.toString());\n      formData.append('num_layers', numLayers.toString());\n      formData.append('dropout', dropout.toString());\n      formData.append('output_folder', outputFolder);\n\n      const response = await startBatchTrainingAsync(formData);\n      \n      if (response.success) {\n        message.success(`批量训练任务已启动，任务ID: ${response.task_id}`);\n        startTask(response.task_id);\n      } else {\n        message.error('启动批量训练失败');\n      }\n    } catch (error) {\n      console.error('启动批量训练失败:', error);\n      message.error('启动批量训练失败');\n    }\n  };\n\n  // 计算总文件数\n  const getTotalFileCount = () => {\n    return selectedDirectories.reduce((total, dirPath) => {\n      const dir = csvDirectories.find(d => d.directory === dirPath);\n      return total + (dir?.csv_count || 0);\n    }, 0);\n  };\n\n  // 计算总模型数\n  const getTotalModelCount = () => {\n    const totalCombinations = selectedProts.reduce((total, prot) => {\n      return total + (selectedDatatypes[prot]?.length || 0);\n    }, 0);\n    return getTotalFileCount() * totalCombinations;\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <ExperimentOutlined /> 批量模型训练\n      </Title>\n      <Text type=\"secondary\">\n        选择多个CSV目录进行批量模型训练，每个目录中的所有CSV文件都将被用于训练\n      </Text>\n\n      <Divider />\n\n      <Row gutter={24}>\n        {/* 左侧：目录选择和参数配置 */}\n        <Col span={16}>\n          {/* CSV目录选择 */}\n          <Card title={<><FolderOutlined /> CSV目录选择</>} style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>基础目录: </Text>\n                <Select\n                  value={baseDir}\n                  onChange={setBaseDir}\n                  style={{ width: 200, marginLeft: 8 }}\n                >\n                  <Option value=\"/data\">/data</Option>\n                  <Option value=\"/data/input\">/data/input</Option>\n                  <Option value=\"/data/output\">/data/output</Option>\n                </Select>\n                <Button \n                  onClick={loadCsvDirectories} \n                  loading={loading}\n                  style={{ marginLeft: 8 }}\n                >\n                  刷新\n                </Button>\n              </div>\n\n              <Spin spinning={loading}>\n                <List\n                  dataSource={csvDirectories}\n                  renderItem={(dir) => (\n                    <List.Item>\n                      <Checkbox\n                        checked={selectedDirectories.includes(dir.directory)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedDirectories([...selectedDirectories, dir.directory]);\n                          } else {\n                            setSelectedDirectories(selectedDirectories.filter(d => d !== dir.directory));\n                          }\n                        }}\n                      >\n                        <Space>\n                          <FolderOutlined />\n                          <Text strong>{dir.name}</Text>\n                          <Tag color=\"blue\">{dir.csv_count} 个CSV文件</Tag>\n                        </Space>\n                      </Checkbox>\n                    </List.Item>\n                  )}\n                  locale={{ emptyText: '没有找到包含CSV文件的目录' }}\n                />\n              </Spin>\n            </Space>\n          </Card>\n\n          {/* 协议和数据类型选择 */}\n          <Card title=\"协议和数据类型选择\" style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>选择协议: </Text>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedProts}\n                  onChange={handleProtocolChange}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择协议\"\n                >\n                  {protocolOptions.map(prot => (\n                    <Option key={prot} value={prot}>{prot}</Option>\n                  ))}\n                </Select>\n              </div>\n\n              {selectedProts.map(prot => (\n                <div key={prot}>\n                  <Text strong>{prot} 数据类型: </Text>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedDatatypes[prot] || []}\n                    onChange={(datatypes) => handleDatatypeChange(prot, datatypes)}\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder={`选择 ${prot} 数据类型`}\n                  >\n                    {datatypeOptions[prot as keyof typeof datatypeOptions]?.map(datatype => (\n                      <Option key={datatype} value={datatype}>{datatype}</Option>\n                    ))}\n                  </Select>\n                </div>\n              ))}\n            </Space>\n          </Card>\n\n          {/* 训练参数 */}\n          <Card title={<><SettingOutlined /> 训练参数</>}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text>学习率: {learningRate}</Text>\n                    <InputNumber\n                      min={0.0001}\n                      max={0.1}\n                      step={0.0001}\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.001)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>批量大小: {batchSize}</Text>\n                    <InputNumber\n                      min={1}\n                      max={512}\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 32)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>训练轮数: {epochs}</Text>\n                    <InputNumber\n                      min={1}\n                      max={1000}\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                </Space>\n              </Col>\n              <Col span={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text>序列长度: {sequenceLength}</Text>\n                    <InputNumber\n                      min={5}\n                      max={50}\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>隐藏层大小: {hiddenSize}</Text>\n                    <InputNumber\n                      min={10}\n                      max={200}\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>网络层数: {numLayers}</Text>\n                    <InputNumber\n                      min={1}\n                      max={5}\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 右侧：训练状态和结果 */}\n        <Col span={8}>\n          {/* 训练概览 */}\n          <Card title=\"训练概览\" style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Statistic\n                title=\"选择的目录数\"\n                value={selectedDirectories.length}\n                prefix={<FolderOutlined />}\n              />\n              <Statistic\n                title=\"总CSV文件数\"\n                value={getTotalFileCount()}\n                prefix={<FileTextOutlined />}\n              />\n              <Statistic\n                title=\"预计训练模型数\"\n                value={getTotalModelCount()}\n                prefix={<ExperimentOutlined />}\n              />\n            </Space>\n          </Card>\n\n          {/* 训练控制 */}\n          <Card title=\"训练控制\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Button\n                type=\"primary\"\n                icon={<PlayCircleOutlined />}\n                onClick={handleStartBatchTraining}\n                disabled={isTaskRunning || selectedDirectories.length === 0}\n                size=\"large\"\n                style={{ width: '100%' }}\n              >\n                {isTaskRunning ? '训练中...' : '开始批量训练'}\n              </Button>\n\n              {isTaskRunning && (\n                <Button\n                  danger\n                  onClick={stopTask}\n                  style={{ width: '100%' }}\n                >\n                  停止训练\n                </Button>\n              )}\n\n              {/* 训练进度 */}\n              {isTaskRunning && (\n                <div>\n                  <Text strong>训练进度:</Text>\n                  <Progress \n                    percent={taskProgress} \n                    status={taskStatus === 'failed' ? 'exception' : 'active'}\n                    style={{ marginTop: 8 }}\n                  />\n                  {currentTask && (\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      任务ID: {currentTask.task_id}\n                    </Text>\n                  )}\n                </div>\n              )}\n\n              {/* 训练结果 */}\n              {taskResult && taskStatus === 'completed' && (\n                <Alert\n                  message=\"批量训练完成\"\n                  description={\n                    <div>\n                      <p>成功处理: {taskResult.summary?.successful_files || 0} 个文件</p>\n                      <p>失败文件: {taskResult.summary?.failed_files || 0} 个</p>\n                      <p>总训练模型: {taskResult.summary?.total_models || 0} 个</p>\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n              )}\n\n              {taskStatus === 'failed' && (\n                <Alert\n                  message=\"批量训练失败\"\n                  description=\"请检查日志获取详细错误信息\"\n                  type=\"error\"\n                  showIcon\n                />\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default BatchTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,GAAG,QAEE,MAAM;AACb,SACEC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,gBAAgB,QAGX,mBAAmB;AAC1B,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAO,CAAC,GAAGhB,MAAM;AASzB,MAAMiB,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA;EACxC;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAiB,EAAE,CAAC;EACxE,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;EAC5E,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,OAAO,CAAC;;EAE/C;EACA,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAA4B;IACpFkD,GAAG,EAAE,CAAC,aAAa,CAAC;IACpBC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,GAAG,CAAC;EACzC,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,cAAc,CAAC;;EAEhE;EACA,MAAM;IACJqE,WAAW;IACXC,aAAa;IACbC,SAAS;IACTC,QAAQ;IACRC,YAAY;IACZC,UAAU;IACVC;EACF,CAAC,GAAGlD,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmD,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtB3B,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFvC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,QAAQ,GAAG,MAAMxD,gBAAgB,CAACyD,kBAAkB,CAACpC,OAAO,CAAC;MACnEH,iBAAiB,CAACsC,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC5E,OAAO,CAAC4E,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACd6E,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAClC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMyC,oBAAoB,GAAIC,SAAmB,IAAK;IACpDvC,gBAAgB,CAACuC,SAAS,CAAC;IAC3B;IACA,MAAMC,YAAY,GAAG;MAAE,GAAGvC;IAAkB,CAAC;IAC7CwC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACL,SAAS,CAACM,QAAQ,CAACD,IAAI,CAAC,EAAE;QAC7BJ,YAAY,CAACI,IAAI,CAAC,GAAG,EAAE;MACzB;IACF,CAAC,CAAC;IACF1C,oBAAoB,CAACsC,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtE9C,oBAAoB,CAAC+C,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvD,mBAAmB,CAACwD,MAAM,KAAK,CAAC,EAAE;MACpC3F,OAAO,CAAC4E,KAAK,CAAC,cAAc,CAAC;MAC7B,OAAO,KAAK;IACd;IACA,IAAIrC,aAAa,CAACoD,MAAM,KAAK,CAAC,EAAE;MAC9B3F,OAAO,CAAC4E,KAAK,CAAC,WAAW,CAAC;MAC1B,OAAO,KAAK;IACd;IACA,MAAMgB,WAAW,GAAGrD,aAAa,CAACsD,IAAI,CAACT,IAAI,IACzC3C,iBAAiB,CAAC2C,IAAI,CAAC,IAAI3C,iBAAiB,CAAC2C,IAAI,CAAC,CAACO,MAAM,GAAG,CAC9D,CAAC;IACD,IAAI,CAACC,WAAW,EAAE;MAChB5F,OAAO,CAAC4E,KAAK,CAAC,gBAAgB,CAAC;MAC/B,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMkB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI,CAACJ,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACF,MAAMK,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEC,IAAI,CAACC,SAAS,CAAChE,mBAAmB,CAAC,CAAC;MACvE4D,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAAC5D,aAAa,CAAC,CAAC;MAChEwD,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAAC1D,iBAAiB,CAAC,CAAC;MACxEsD,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnD,YAAY,CAACsD,QAAQ,CAAC,CAAC,CAAC;MACzDL,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEjD,SAAS,CAACoD,QAAQ,CAAC,CAAC,CAAC;MACnDL,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/C,MAAM,CAACkD,QAAQ,CAAC,CAAC,CAAC;MAC5CL,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE7C,cAAc,CAACgD,QAAQ,CAAC,CAAC,CAAC;MAC7DL,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE3C,UAAU,CAAC8C,QAAQ,CAAC,CAAC,CAAC;MACrDL,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEzC,SAAS,CAAC4C,QAAQ,CAAC,CAAC,CAAC;MACnDL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvC,OAAO,CAAC0C,QAAQ,CAAC,CAAC,CAAC;MAC9CL,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAErC,YAAY,CAAC;MAE9C,MAAMY,QAAQ,GAAG,MAAMvD,uBAAuB,CAAC8E,QAAQ,CAAC;MAExD,IAAIvB,QAAQ,CAAC6B,OAAO,EAAE;QACpBrG,OAAO,CAACqG,OAAO,CAAC,mBAAmB7B,QAAQ,CAAC8B,OAAO,EAAE,CAAC;QACtDtC,SAAS,CAACQ,QAAQ,CAAC8B,OAAO,CAAC;MAC7B,CAAC,MAAM;QACLtG,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5E,OAAO,CAAC4E,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM2B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOpE,mBAAmB,CAACqE,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAK;MACpD,MAAMC,GAAG,GAAG1E,cAAc,CAAC2E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKJ,OAAO,CAAC;MAC7D,OAAOD,KAAK,IAAI,CAAAE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEI,SAAS,KAAI,CAAC,CAAC;IACtC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,iBAAiB,GAAG1E,aAAa,CAACiE,MAAM,CAAC,CAACC,KAAK,EAAErB,IAAI,KAAK;MAAA,IAAA8B,qBAAA;MAC9D,OAAOT,KAAK,IAAI,EAAAS,qBAAA,GAAAzE,iBAAiB,CAAC2C,IAAI,CAAC,cAAA8B,qBAAA,uBAAvBA,qBAAA,CAAyBvB,MAAM,KAAI,CAAC,CAAC;IACvD,CAAC,EAAE,CAAC,CAAC;IACL,OAAOY,iBAAiB,CAAC,CAAC,GAAGU,iBAAiB;EAChD,CAAC;EAED,oBACE7F,OAAA;IAAK+F,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BjG,OAAA,CAACG,KAAK;MAAC+F,KAAK,EAAE,CAAE;MAAAD,QAAA,gBACdjG,OAAA,CAACmG,kBAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,yCACxB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACRvG,OAAA,CAACI,IAAI;MAACoG,IAAI,EAAC,WAAW;MAAAP,QAAA,EAAC;IAEvB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPvG,OAAA,CAACrB,OAAO;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXvG,OAAA,CAACd,GAAG;MAACuH,MAAM,EAAE,EAAG;MAAAR,QAAA,gBAEdjG,OAAA,CAACb,GAAG;QAACuH,IAAI,EAAE,EAAG;QAAAT,QAAA,gBAEZjG,OAAA,CAACzB,IAAI;UAACoI,KAAK,eAAE3G,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBAAEjG,OAAA,CAACN,cAAc;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gCAAQ;UAAA,eAAE,CAAE;UAACR,KAAK,EAAE;YAAEa,YAAY,EAAE;UAAG,CAAE;UAAAX,QAAA,eACxEjG,OAAA,CAACtB,KAAK;YAACmI,SAAS,EAAC,UAAU;YAACd,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnDjG,OAAA;cAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;gBAAC2G,MAAM;gBAAAd,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BvG,OAAA,CAACX,MAAM;gBACL2H,KAAK,EAAE/F,OAAQ;gBACfgG,QAAQ,EAAE/F,UAAW;gBACrB6E,KAAK,EAAE;kBAAEe,KAAK,EAAE,GAAG;kBAAEI,UAAU,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAErCjG,OAAA,CAACK,MAAM;kBAAC2G,KAAK,EAAC,OAAO;kBAAAf,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCvG,OAAA,CAACK,MAAM;kBAAC2G,KAAK,EAAC,aAAa;kBAAAf,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDvG,OAAA,CAACK,MAAM;kBAAC2G,KAAK,EAAC,cAAc;kBAAAf,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACTvG,OAAA,CAACxB,MAAM;gBACL2I,OAAO,EAAEhE,kBAAmB;gBAC5BxC,OAAO,EAAEA,OAAQ;gBACjBoF,KAAK,EAAE;kBAAEmB,UAAU,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,EAC1B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvG,OAAA,CAACnB,IAAI;cAACuI,QAAQ,EAAEzG,OAAQ;cAAAsF,QAAA,eACtBjG,OAAA,CAACV,IAAI;gBACH+H,UAAU,EAAExG,cAAe;gBAC3ByG,UAAU,EAAG/B,GAAG,iBACdvF,OAAA,CAACV,IAAI,CAACiI,IAAI;kBAAAtB,QAAA,eACRjG,OAAA,CAACjB,QAAQ;oBACPyI,OAAO,EAAEzG,mBAAmB,CAACkD,QAAQ,CAACsB,GAAG,CAACG,SAAS,CAAE;oBACrDuB,QAAQ,EAAGQ,CAAC,IAAK;sBACf,IAAIA,CAAC,CAACC,MAAM,CAACF,OAAO,EAAE;wBACpBxG,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,EAAEwE,GAAG,CAACG,SAAS,CAAC,CAAC;sBACjE,CAAC,MAAM;wBACL1E,sBAAsB,CAACD,mBAAmB,CAAC4G,MAAM,CAAClC,CAAC,IAAIA,CAAC,KAAKF,GAAG,CAACG,SAAS,CAAC,CAAC;sBAC9E;oBACF,CAAE;oBAAAO,QAAA,eAEFjG,OAAA,CAACtB,KAAK;sBAAAuH,QAAA,gBACJjG,OAAA,CAACN,cAAc;wBAAA0G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAClBvG,OAAA,CAACI,IAAI;wBAAC2G,MAAM;wBAAAd,QAAA,EAAEV,GAAG,CAACqC;sBAAI;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9BvG,OAAA,CAACT,GAAG;wBAACsI,KAAK,EAAC,MAAM;wBAAA5B,QAAA,GAAEV,GAAG,CAACI,SAAS,EAAC,wBAAO;sBAAA;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACX;gBACFuB,MAAM,EAAE;kBAAEC,SAAS,EAAE;gBAAiB;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPvG,OAAA,CAACzB,IAAI;UAACoI,KAAK,EAAC,wDAAW;UAACZ,KAAK,EAAE;YAAEa,YAAY,EAAE;UAAG,CAAE;UAAAX,QAAA,eAClDjG,OAAA,CAACtB,KAAK;YAACmI,SAAS,EAAC,UAAU;YAACd,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnDjG,OAAA;cAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;gBAAC2G,MAAM;gBAAAd,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BvG,OAAA,CAACX,MAAM;gBACL2I,IAAI,EAAC,UAAU;gBACfhB,KAAK,EAAE7F,aAAc;gBACrB8F,QAAQ,EAAEvD,oBAAqB;gBAC/BqC,KAAK,EAAE;kBAAEe,KAAK,EAAE,MAAM;kBAAEmB,SAAS,EAAE;gBAAE,CAAE;gBACvCC,WAAW,EAAC,0BAAM;gBAAAjC,QAAA,EAEjBhD,eAAe,CAACkF,GAAG,CAACnE,IAAI,iBACvBhE,OAAA,CAACK,MAAM;kBAAY2G,KAAK,EAAEhD,IAAK;kBAAAiC,QAAA,EAAEjC;gBAAI,GAAxBA,IAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELpF,aAAa,CAACgH,GAAG,CAACnE,IAAI;cAAA,IAAAoE,gBAAA;cAAA,oBACrBpI,OAAA;gBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;kBAAC2G,MAAM;kBAAAd,QAAA,GAAEjC,IAAI,EAAC,6BAAO;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCvG,OAAA,CAACX,MAAM;kBACL2I,IAAI,EAAC,UAAU;kBACfhB,KAAK,EAAE3F,iBAAiB,CAAC2C,IAAI,CAAC,IAAI,EAAG;kBACrCiD,QAAQ,EAAG7C,SAAS,IAAKF,oBAAoB,CAACF,IAAI,EAAEI,SAAS,CAAE;kBAC/D2B,KAAK,EAAE;oBAAEe,KAAK,EAAE,MAAM;oBAAEmB,SAAS,EAAE;kBAAE,CAAE;kBACvCC,WAAW,EAAE,MAAMlE,IAAI,OAAQ;kBAAAiC,QAAA,GAAAmC,gBAAA,GAE9BlF,eAAe,CAACc,IAAI,CAAiC,cAAAoE,gBAAA,uBAArDA,gBAAA,CAAuDD,GAAG,CAACE,QAAQ,iBAClErI,OAAA,CAACK,MAAM;oBAAgB2G,KAAK,EAAEqB,QAAS;oBAAApC,QAAA,EAAEoC;kBAAQ,GAApCA,QAAQ;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA,GAZDvC,IAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaT,CAAC;YAAA,CACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPvG,OAAA,CAACzB,IAAI;UAACoI,KAAK,eAAE3G,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBAAEjG,OAAA,CAACP,eAAe;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAK;UAAA,eAAE,CAAE;UAAAN,QAAA,eACzCjG,OAAA,CAACd,GAAG;YAACuH,MAAM,EAAE,EAAG;YAAAR,QAAA,gBACdjG,OAAA,CAACb,GAAG;cAACuH,IAAI,EAAE,EAAG;cAAAT,QAAA,eACZjG,OAAA,CAACtB,KAAK;gBAACmI,SAAS,EAAC,UAAU;gBAACd,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBACnDjG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,sBAAK,EAACvE,YAAY;kBAAA;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChCvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,MAAO;oBACZC,GAAG,EAAE,GAAI;oBACTC,IAAI,EAAE,MAAO;oBACbxB,KAAK,EAAEtF,YAAa;oBACpBuF,QAAQ,EAAGD,KAAK,IAAKrF,eAAe,CAACqF,KAAK,IAAI,KAAK,CAAE;oBACrDjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,4BAAM,EAACrE,SAAS;kBAAA;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACTvB,KAAK,EAAEpF,SAAU;oBACjBqF,QAAQ,EAAGD,KAAK,IAAKnF,YAAY,CAACmF,KAAK,IAAI,EAAE,CAAE;oBAC/CjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,4BAAM,EAACnE,MAAM;kBAAA;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACVvB,KAAK,EAAElF,MAAO;oBACdmF,QAAQ,EAAGD,KAAK,IAAKjF,SAAS,CAACiF,KAAK,IAAI,GAAG,CAAE;oBAC7CjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvG,OAAA,CAACb,GAAG;cAACuH,IAAI,EAAE,EAAG;cAAAT,QAAA,eACZjG,OAAA,CAACtB,KAAK;gBAACmI,SAAS,EAAC,UAAU;gBAACd,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBACnDjG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,4BAAM,EAACjE,cAAc;kBAAA;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnCvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACRvB,KAAK,EAAEhF,cAAe;oBACtBiF,QAAQ,EAAGD,KAAK,IAAK/E,iBAAiB,CAAC+E,KAAK,IAAI,EAAE,CAAE;oBACpDjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,kCAAO,EAAC/D,UAAU;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChCvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTvB,KAAK,EAAE9E,UAAW;oBAClB+E,QAAQ,EAAGD,KAAK,IAAK7E,aAAa,CAAC6E,KAAK,IAAI,EAAE,CAAE;oBAChDjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNvG,OAAA;kBAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;oBAAA6F,QAAA,GAAC,4BAAM,EAAC7D,SAAS;kBAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9BvG,OAAA,CAAClB,WAAW;oBACVwJ,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,CAAE;oBACPvB,KAAK,EAAE5E,SAAU;oBACjB6E,QAAQ,EAAGD,KAAK,IAAK3E,YAAY,CAAC2E,KAAK,IAAI,CAAC,CAAE;oBAC9CjB,KAAK,EAAE;sBAAEe,KAAK,EAAE,MAAM;sBAAEmB,SAAS,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNvG,OAAA,CAACb,GAAG;QAACuH,IAAI,EAAE,CAAE;QAAAT,QAAA,gBAEXjG,OAAA,CAACzB,IAAI;UAACoI,KAAK,EAAC,0BAAM;UAACZ,KAAK,EAAE;YAAEa,YAAY,EAAE;UAAG,CAAE;UAAAX,QAAA,eAC7CjG,OAAA,CAACtB,KAAK;YAACmI,SAAS,EAAC,UAAU;YAACd,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnDjG,OAAA,CAACZ,SAAS;cACRuH,KAAK,EAAC,sCAAQ;cACdK,KAAK,EAAEjG,mBAAmB,CAACwD,MAAO;cAClCkE,MAAM,eAAEzI,OAAA,CAACN,cAAc;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFvG,OAAA,CAACZ,SAAS;cACRuH,KAAK,EAAC,6BAAS;cACfK,KAAK,EAAE7B,iBAAiB,CAAC,CAAE;cAC3BsD,MAAM,eAAEzI,OAAA,CAACL,gBAAgB;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACFvG,OAAA,CAACZ,SAAS;cACRuH,KAAK,EAAC,4CAAS;cACfK,KAAK,EAAEpB,kBAAkB,CAAC,CAAE;cAC5B6C,MAAM,eAAEzI,OAAA,CAACmG,kBAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPvG,OAAA,CAACzB,IAAI;UAACoI,KAAK,EAAC,0BAAM;UAAAV,QAAA,eAChBjG,OAAA,CAACtB,KAAK;YAACmI,SAAS,EAAC,UAAU;YAACd,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAAb,QAAA,gBACnDjG,OAAA,CAACxB,MAAM;cACLgI,IAAI,EAAC,SAAS;cACdkC,IAAI,eAAE1I,OAAA,CAACR,kBAAkB;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BY,OAAO,EAAEzC,wBAAyB;cAClCiE,QAAQ,EAAEhG,aAAa,IAAI5B,mBAAmB,CAACwD,MAAM,KAAK,CAAE;cAC5DqE,IAAI,EAAC,OAAO;cACZ7C,KAAK,EAAE;gBAAEe,KAAK,EAAE;cAAO,CAAE;cAAAb,QAAA,EAExBtD,aAAa,GAAG,QAAQ,GAAG;YAAQ;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EAER5D,aAAa,iBACZ3C,OAAA,CAACxB,MAAM;cACLqK,MAAM;cACN1B,OAAO,EAAEtE,QAAS;cAClBkD,KAAK,EAAE;gBAAEe,KAAK,EAAE;cAAO,CAAE;cAAAb,QAAA,EAC1B;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAGA5D,aAAa,iBACZ3C,OAAA;cAAAiG,QAAA,gBACEjG,OAAA,CAACI,IAAI;gBAAC2G,MAAM;gBAAAd,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBvG,OAAA,CAAChB,QAAQ;gBACP8J,OAAO,EAAEhG,YAAa;gBACtBiG,MAAM,EAAEhG,UAAU,KAAK,QAAQ,GAAG,WAAW,GAAG,QAAS;gBACzDgD,KAAK,EAAE;kBAAEkC,SAAS,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,EACD7D,WAAW,iBACV1C,OAAA,CAACI,IAAI;gBAACoG,IAAI,EAAC,WAAW;gBAACT,KAAK,EAAE;kBAAEiD,QAAQ,EAAE;gBAAO,CAAE;gBAAA/C,QAAA,GAAC,kBAC5C,EAACvD,WAAW,CAACwC,OAAO;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EAGAvD,UAAU,IAAID,UAAU,KAAK,WAAW,iBACvC/C,OAAA,CAACf,KAAK;cACJL,OAAO,EAAC,sCAAQ;cAChBqK,WAAW,eACTjJ,OAAA;gBAAAiG,QAAA,gBACEjG,OAAA;kBAAAiG,QAAA,GAAG,4BAAM,EAAC,EAAAzF,mBAAA,GAAAwC,UAAU,CAACkG,OAAO,cAAA1I,mBAAA,uBAAlBA,mBAAA,CAAoB2I,gBAAgB,KAAI,CAAC,EAAC,qBAAI;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DvG,OAAA;kBAAAiG,QAAA,GAAG,4BAAM,EAAC,EAAAxF,oBAAA,GAAAuC,UAAU,CAACkG,OAAO,cAAAzI,oBAAA,uBAAlBA,oBAAA,CAAoB2I,YAAY,KAAI,CAAC,EAAC,SAAE;gBAAA;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtDvG,OAAA;kBAAAiG,QAAA,GAAG,kCAAO,EAAC,EAAAvF,oBAAA,GAAAsC,UAAU,CAACkG,OAAO,cAAAxI,oBAAA,uBAAlBA,oBAAA,CAAoB2I,YAAY,KAAI,CAAC,EAAC,SAAE;gBAAA;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CACN;cACDC,IAAI,EAAC,SAAS;cACd8C,QAAQ;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACF,EAEAxD,UAAU,KAAK,QAAQ,iBACtB/C,OAAA,CAACf,KAAK;cACJL,OAAO,EAAC,sCAAQ;cAChBqK,WAAW,EAAC,gFAAe;cAC3BzC,IAAI,EAAC,OAAO;cACZ8C,QAAQ;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChG,EAAA,CAtaID,iBAA2B;EAAA,QAgC3BR,cAAc;AAAA;AAAAyJ,EAAA,GAhCdjJ,iBAA2B;AAwajC,eAAeA,iBAAiB;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}