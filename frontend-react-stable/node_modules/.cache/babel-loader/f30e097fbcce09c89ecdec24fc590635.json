{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.copy = copy;\nexports.default = continuous;\nexports.identity = identity;\nexports.transformer = transformer;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _index2 = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nvar _number = _interopRequireDefault(require(\"./number.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar unit = [0, 1];\nfunction identity(x) {\n  return x;\n}\nfunction normalize(a, b) {\n  return (b -= a = +a) ? function (x) {\n    return (x - a) / b;\n  } : (0, _constant.default)(isNaN(b) ? NaN : 0.5);\n}\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function (x) {\n    return Math.max(a, Math.min(b, x));\n  };\n} // normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\n\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0],\n    d1 = domain[1],\n    r0 = range[0],\n    r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function (x) {\n    return r0(d0(x));\n  };\n}\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n    d = new Array(j),\n    r = new Array(j),\n    i = -1; // Reverse descending domains.\n\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n  return function (x) {\n    var i = (0, _index.bisect)(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\nfunction copy(source, target) {\n  return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());\n}\nfunction transformer() {\n  var domain = unit,\n    range = unit,\n    interpolate = _index2.interpolate,\n    transform,\n    untransform,\n    unknown,\n    clamp = identity,\n    piecewise,\n    output,\n    input;\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n  scale.invert = function (y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), _index2.interpolateNumber)))(y)));\n  };\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_, _number.default), rescale()) : domain.slice();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n  scale.rangeRound = function (_) {\n    return range = Array.from(_), interpolate = _index2.interpolateRound, rescale();\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n  scale.interpolate = function (_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\nfunction continuous() {\n  return transformer()(identity, identity);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "copy", "default", "continuous", "identity", "transformer", "_index", "require", "_index2", "_constant", "_interopRequireDefault", "_number", "obj", "__esModule", "unit", "x", "normalize", "a", "b", "isNaN", "NaN", "clamper", "t", "Math", "max", "min", "bimap", "domain", "range", "interpolate", "d0", "d1", "r0", "r1", "polymap", "j", "length", "d", "Array", "r", "i", "slice", "reverse", "bisect", "source", "target", "clamp", "unknown", "transform", "untransform", "piecewise", "output", "input", "rescale", "n", "scale", "map", "invert", "y", "interpolateNumber", "_", "arguments", "from", "rangeRound", "interpolateRound", "u"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/continuous.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.copy = copy;\nexports.default = continuous;\nexports.identity = identity;\nexports.transformer = transformer;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _index2 = require(\"../../../lib-vendor/d3-interpolate/src/index.js\");\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nvar _number = _interopRequireDefault(require(\"./number.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar unit = [0, 1];\n\nfunction identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= a = +a) ? function (x) {\n    return (x - a) / b;\n  } : (0, _constant.default)(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function (x) {\n    return Math.max(a, Math.min(b, x));\n  };\n} // normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\n\n\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0],\n      d1 = domain[1],\n      r0 = range[0],\n      r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function (x) {\n    return r0(d0(x));\n  };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1; // Reverse descending domains.\n\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function (x) {\n    var i = (0, _index.bisect)(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nfunction copy(source, target) {\n  return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());\n}\n\nfunction transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = _index2.interpolate,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function (y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), _index2.interpolateNumber)))(y)));\n  };\n\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_, _number.default), rescale()) : domain.slice();\n  };\n\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function (_) {\n    return range = Array.from(_), interpolate = _index2.interpolateRound, rescale();\n  };\n\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function (_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function (t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nfunction continuous() {\n  return transformer()(identity, identity);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBF,OAAO,CAACG,OAAO,GAAGC,UAAU;AAC5BJ,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3BL,OAAO,CAACM,WAAW,GAAGA,WAAW;AAEjC,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,OAAO,GAAGD,OAAO,CAAC,iDAAiD,CAAC;AAExE,IAAIE,SAAS,GAAGC,sBAAsB,CAACH,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAII,OAAO,GAAGD,sBAAsB,CAACH,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,SAASG,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,IAAIE,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEjB,SAASV,QAAQA,CAACW,CAAC,EAAE;EACnB,OAAOA,CAAC;AACV;AAEA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAO,CAACA,CAAC,IAAID,CAAC,GAAG,CAACA,CAAC,IAAI,UAAUF,CAAC,EAAE;IAClC,OAAO,CAACA,CAAC,GAAGE,CAAC,IAAIC,CAAC;EACpB,CAAC,GAAG,CAAC,CAAC,EAAET,SAAS,CAACP,OAAO,EAAEiB,KAAK,CAACD,CAAC,CAAC,GAAGE,GAAG,GAAG,GAAG,CAAC;AAClD;AAEA,SAASC,OAAOA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAII,CAAC;EACL,IAAIL,CAAC,GAAGC,CAAC,EAAEI,CAAC,GAAGL,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAEA,CAAC,GAAGI,CAAC;EAC9B,OAAO,UAAUP,CAAC,EAAE;IAClB,OAAOQ,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEM,IAAI,CAACE,GAAG,CAACP,CAAC,EAAEH,CAAC,CAAC,CAAC;EACpC,CAAC;AACH,CAAC,CAAC;AACF;;AAGA,SAASW,KAAKA,CAACC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE;EACzC,IAAIC,EAAE,GAAGH,MAAM,CAAC,CAAC,CAAC;IACdI,EAAE,GAAGJ,MAAM,CAAC,CAAC,CAAC;IACdK,EAAE,GAAGJ,KAAK,CAAC,CAAC,CAAC;IACbK,EAAE,GAAGL,KAAK,CAAC,CAAC,CAAC;EACjB,IAAIG,EAAE,GAAGD,EAAE,EAAEA,EAAE,GAAGd,SAAS,CAACe,EAAE,EAAED,EAAE,CAAC,EAAEE,EAAE,GAAGH,WAAW,CAACI,EAAE,EAAED,EAAE,CAAC,CAAC,KAAKF,EAAE,GAAGd,SAAS,CAACc,EAAE,EAAEC,EAAE,CAAC,EAAEC,EAAE,GAAGH,WAAW,CAACG,EAAE,EAAEC,EAAE,CAAC;EACnH,OAAO,UAAUlB,CAAC,EAAE;IAClB,OAAOiB,EAAE,CAACF,EAAE,CAACf,CAAC,CAAC,CAAC;EAClB,CAAC;AACH;AAEA,SAASmB,OAAOA,CAACP,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC3C,IAAIM,CAAC,GAAGZ,IAAI,CAACE,GAAG,CAACE,MAAM,CAACS,MAAM,EAAER,KAAK,CAACQ,MAAM,CAAC,GAAG,CAAC;IAC7CC,CAAC,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;IAChBI,CAAC,GAAG,IAAID,KAAK,CAACH,CAAC,CAAC;IAChBK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAIb,MAAM,CAACQ,CAAC,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC,EAAE;IACzBA,MAAM,GAAGA,MAAM,CAACc,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACjCd,KAAK,GAAGA,KAAK,CAACa,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACjC;EAEA,OAAO,EAAEF,CAAC,GAAGL,CAAC,EAAE;IACdE,CAAC,CAACG,CAAC,CAAC,GAAGxB,SAAS,CAACW,MAAM,CAACa,CAAC,CAAC,EAAEb,MAAM,CAACa,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1CD,CAAC,CAACC,CAAC,CAAC,GAAGX,WAAW,CAACD,KAAK,CAACY,CAAC,CAAC,EAAEZ,KAAK,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C;EAEA,OAAO,UAAUzB,CAAC,EAAE;IAClB,IAAIyB,CAAC,GAAG,CAAC,CAAC,EAAElC,MAAM,CAACqC,MAAM,EAAEhB,MAAM,EAAEZ,CAAC,EAAE,CAAC,EAAEoB,CAAC,CAAC,GAAG,CAAC;IAC/C,OAAOI,CAAC,CAACC,CAAC,CAAC,CAACH,CAAC,CAACG,CAAC,CAAC,CAACzB,CAAC,CAAC,CAAC;EACtB,CAAC;AACH;AAEA,SAASd,IAAIA,CAAC2C,MAAM,EAAEC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAAClB,MAAM,CAACiB,MAAM,CAACjB,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAACgB,MAAM,CAAChB,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAACe,MAAM,CAACf,WAAW,CAAC,CAAC,CAAC,CAACiB,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAACH,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC;AAC/I;AAEA,SAAS1C,WAAWA,CAAA,EAAG;EACrB,IAAIsB,MAAM,GAAGb,IAAI;IACbc,KAAK,GAAGd,IAAI;IACZe,WAAW,GAAGrB,OAAO,CAACqB,WAAW;IACjCmB,SAAS;IACTC,WAAW;IACXF,OAAO;IACPD,KAAK,GAAG1C,QAAQ;IAChB8C,SAAS;IACTC,MAAM;IACNC,KAAK;EAET,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG/B,IAAI,CAACE,GAAG,CAACE,MAAM,CAACS,MAAM,EAAER,KAAK,CAACQ,MAAM,CAAC;IAC7C,IAAIU,KAAK,KAAK1C,QAAQ,EAAE0C,KAAK,GAAGzB,OAAO,CAACM,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC2B,CAAC,GAAG,CAAC,CAAC,CAAC;IACjEJ,SAAS,GAAGI,CAAC,GAAG,CAAC,GAAGpB,OAAO,GAAGR,KAAK;IACnCyB,MAAM,GAAGC,KAAK,GAAG,IAAI;IACrB,OAAOG,KAAK;EACd;EAEA,SAASA,KAAKA,CAACxC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAII,KAAK,CAACJ,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGgC,OAAO,GAAG,CAACI,MAAM,KAAKA,MAAM,GAAGD,SAAS,CAACvB,MAAM,CAAC6B,GAAG,CAACR,SAAS,CAAC,EAAEpB,KAAK,EAAEC,WAAW,CAAC,CAAC,EAAEmB,SAAS,CAACF,KAAK,CAAC/B,CAAC,CAAC,CAAC,CAAC;EAChJ;EAEAwC,KAAK,CAACE,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1B,OAAOZ,KAAK,CAACG,WAAW,CAAC,CAACG,KAAK,KAAKA,KAAK,GAAGF,SAAS,CAACtB,KAAK,EAAED,MAAM,CAAC6B,GAAG,CAACR,SAAS,CAAC,EAAExC,OAAO,CAACmD,iBAAiB,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC;EACvH,CAAC;EAEDH,KAAK,CAAC5B,MAAM,GAAG,UAAUiC,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACzB,MAAM,IAAIT,MAAM,GAAGW,KAAK,CAACwB,IAAI,CAACF,CAAC,EAAEjD,OAAO,CAACT,OAAO,CAAC,EAAEmD,OAAO,CAAC,CAAC,IAAI1B,MAAM,CAACc,KAAK,CAAC,CAAC;EACjG,CAAC;EAEDc,KAAK,CAAC3B,KAAK,GAAG,UAAUgC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACzB,MAAM,IAAIR,KAAK,GAAGU,KAAK,CAACwB,IAAI,CAACF,CAAC,CAAC,EAAEP,OAAO,CAAC,CAAC,IAAIzB,KAAK,CAACa,KAAK,CAAC,CAAC;EAC9E,CAAC;EAEDc,KAAK,CAACQ,UAAU,GAAG,UAAUH,CAAC,EAAE;IAC9B,OAAOhC,KAAK,GAAGU,KAAK,CAACwB,IAAI,CAACF,CAAC,CAAC,EAAE/B,WAAW,GAAGrB,OAAO,CAACwD,gBAAgB,EAAEX,OAAO,CAAC,CAAC;EACjF,CAAC;EAEDE,KAAK,CAACT,KAAK,GAAG,UAAUc,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACzB,MAAM,IAAIU,KAAK,GAAGc,CAAC,GAAG,IAAI,GAAGxD,QAAQ,EAAEiD,OAAO,CAAC,CAAC,IAAIP,KAAK,KAAK1C,QAAQ;EACzF,CAAC;EAEDmD,KAAK,CAAC1B,WAAW,GAAG,UAAU+B,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACzB,MAAM,IAAIP,WAAW,GAAG+B,CAAC,EAAEP,OAAO,CAAC,CAAC,IAAIxB,WAAW;EACtE,CAAC;EAED0B,KAAK,CAACR,OAAO,GAAG,UAAUa,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACzB,MAAM,IAAIW,OAAO,GAAGa,CAAC,EAAEL,KAAK,IAAIR,OAAO;EAC1D,CAAC;EAED,OAAO,UAAUzB,CAAC,EAAE2C,CAAC,EAAE;IACrBjB,SAAS,GAAG1B,CAAC,EAAE2B,WAAW,GAAGgB,CAAC;IAC9B,OAAOZ,OAAO,CAAC,CAAC;EAClB,CAAC;AACH;AAEA,SAASlD,UAAUA,CAAA,EAAG;EACpB,OAAOE,WAAW,CAAC,CAAC,CAACD,QAAQ,EAAEA,QAAQ,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "script"}