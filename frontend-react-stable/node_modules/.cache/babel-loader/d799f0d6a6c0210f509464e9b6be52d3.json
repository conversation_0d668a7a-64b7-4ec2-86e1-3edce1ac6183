{"ast": null, "code": "var baseForOwn = require('./_baseForOwn'),\n  createBaseEach = require('./_createBaseEach');\n\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEach = createBaseEach(baseForOwn);\nmodule.exports = baseEach;", "map": {"version": 3, "names": ["baseForOwn", "require", "createBaseEach", "baseEach", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseEach.js"], "sourcesContent": ["var baseForOwn = require('./_baseForOwn'),\n    createBaseEach = require('./_createBaseEach');\n\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEach = createBaseEach(baseForOwn);\n\nmodule.exports = baseEach;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,QAAQ,GAAGD,cAAc,CAACF,UAAU,CAAC;AAEzCI,MAAM,CAACC,OAAO,GAAGF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}