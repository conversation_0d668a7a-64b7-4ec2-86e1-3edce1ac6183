{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { ItemGroup } from 'rc-menu';\nimport * as React from 'react';\nimport MenuDivider from '../MenuDivider';\nimport MenuItem from '../MenuItem';\nimport SubMenu from '../SubMenu';\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _a = opt,\n        label = _a.label,\n        children = _a.children,\n        key = _a.key,\n        type = _a.type,\n        restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(ItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        }\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      }\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MenuDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\n// FIXME: Move logic here in v5\n/**\n * We simply convert `items` to ReactNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v5\n */\nexport default function useItems(items) {\n  return React.useMemo(function () {\n    if (!items) {\n      return items;\n    }\n    return convertItemsToNodes(items);\n  }, [items]);\n}", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "ItemGroup", "React", "MenuDivider", "MenuItem", "SubMenu", "convertItemsToNodes", "list", "map", "opt", "index", "_a", "label", "children", "key", "type", "restProps", "mergedKey", "concat", "createElement", "title", "filter", "useItems", "items", "useMemo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/menu/hooks/useItems.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { ItemGroup } from 'rc-menu';\nimport * as React from 'react';\nimport MenuDivider from '../MenuDivider';\nimport MenuItem from '../MenuItem';\nimport SubMenu from '../SubMenu';\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _a = opt,\n        label = _a.label,\n        children = _a.children,\n        key = _a.key,\n        type = _a.type,\n        restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(ItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        }\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      }\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(MenuDivider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\n// FIXME: Move logic here in v5\n/**\n * We simply convert `items` to ReactNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v5\n */\nexport default function useItems(items) {\n  return React.useMemo(function () {\n    if (!items) {\n      return items;\n    }\n    return convertItemsToNodes(items);\n  }, [items]);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,SAASW,SAAS,QAAQ,SAAS;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACjC,OAAO,CAACA,IAAI,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC5C,IAAID,GAAG,IAAIvB,OAAO,CAACuB,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAIE,EAAE,GAAGF,GAAG;QACVG,KAAK,GAAGD,EAAE,CAACC,KAAK;QAChBC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;QACtBC,GAAG,GAAGH,EAAE,CAACG,GAAG;QACZC,IAAI,GAAGJ,EAAE,CAACI,IAAI;QACdC,SAAS,GAAG7B,MAAM,CAACwB,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;MAC9D,IAAIM,SAAS,GAAGH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,MAAM,CAACI,MAAM,CAACR,KAAK,CAAC;MAC3E;MACA,IAAIG,QAAQ,IAAIE,IAAI,KAAK,OAAO,EAAE;QAChC,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpB;UACA,OAAO,aAAab,KAAK,CAACiB,aAAa,CAAClB,SAAS,EAAEhB,QAAQ,CAAC;YAC1D6B,GAAG,EAAEG;UACP,CAAC,EAAED,SAAS,EAAE;YACZI,KAAK,EAAER;UACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;QACpC;QACA;QACA,OAAO,aAAaX,KAAK,CAACiB,aAAa,CAACd,OAAO,EAAEpB,QAAQ,CAAC;UACxD6B,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,EAAE;UACZI,KAAK,EAAER;QACT,CAAC,CAAC,EAAEN,mBAAmB,CAACO,QAAQ,CAAC,CAAC;MACpC;MACA;MACA,IAAIE,IAAI,KAAK,SAAS,EAAE;QACtB,OAAO,aAAab,KAAK,CAACiB,aAAa,CAAChB,WAAW,EAAElB,QAAQ,CAAC;UAC5D6B,GAAG,EAAEG;QACP,CAAC,EAAED,SAAS,CAAC,CAAC;MAChB;MACA,OAAO,aAAad,KAAK,CAACiB,aAAa,CAACf,QAAQ,EAAEnB,QAAQ,CAAC;QACzD6B,GAAG,EAAEG;MACP,CAAC,EAAED,SAAS,CAAC,EAAEJ,KAAK,CAAC;IACvB;IACA,OAAO,IAAI;EACb,CAAC,CAAC,CAACS,MAAM,CAAC,UAAUZ,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASa,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOrB,KAAK,CAACsB,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACD,KAAK,EAAE;MACV,OAAOA,KAAK;IACd;IACA,OAAOjB,mBAAmB,CAACiB,KAAK,CAAC;EACnC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}