{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useEffect, useRef } from 'react';\nimport useValueTexts from './useValueTexts';\nexport default function useHoverValue(valueText, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    internalSetValue = _useState2[1];\n  var raf = useRef(null);\n  function setValue(val) {\n    var immediately = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    cancelAnimationFrame(raf.current);\n    if (immediately) {\n      internalSetValue(val);\n      return;\n    }\n    raf.current = requestAnimationFrame(function () {\n      internalSetValue(val);\n    });\n  }\n  var _useValueTexts = useValueTexts(value, {\n      formatList: formatList,\n      generateConfig: generateConfig,\n      locale: locale\n    }),\n    _useValueTexts2 = _slicedToArray(_useValueTexts, 2),\n    firstText = _useValueTexts2[1];\n  function onEnter(date) {\n    setValue(date);\n  }\n  function onLeave() {\n    var immediately = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    setValue(null, immediately);\n  }\n  useEffect(function () {\n    onLeave(true);\n  }, [valueText]);\n  useEffect(function () {\n    return function () {\n      return cancelAnimationFrame(raf.current);\n    };\n  }, []);\n  return [firstText, onEnter, onLeave];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useState", "useEffect", "useRef", "useValueTexts", "useHoverValue", "valueText", "_ref", "formatList", "generateConfig", "locale", "_useState", "_useState2", "value", "internalSetValue", "raf", "setValue", "val", "immediately", "arguments", "length", "undefined", "cancelAnimationFrame", "current", "requestAnimationFrame", "_useValueTexts", "_useValueTexts2", "firstText", "onEnter", "date", "onLeave"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/hooks/useHoverValue.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useEffect, useRef } from 'react';\nimport useValueTexts from './useValueTexts';\nexport default function useHoverValue(valueText, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    internalSetValue = _useState2[1];\n  var raf = useRef(null);\n  function setValue(val) {\n    var immediately = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    cancelAnimationFrame(raf.current);\n    if (immediately) {\n      internalSetValue(val);\n      return;\n    }\n    raf.current = requestAnimationFrame(function () {\n      internalSetValue(val);\n    });\n  }\n  var _useValueTexts = useValueTexts(value, {\n      formatList: formatList,\n      generateConfig: generateConfig,\n      locale: locale\n    }),\n    _useValueTexts2 = _slicedToArray(_useValueTexts, 2),\n    firstText = _useValueTexts2[1];\n  function onEnter(date) {\n    setValue(date);\n  }\n  function onLeave() {\n    var immediately = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    setValue(null, immediately);\n  }\n  useEffect(function () {\n    onLeave(true);\n  }, [valueText]);\n  useEffect(function () {\n    return function () {\n      return cancelAnimationFrame(raf.current);\n    };\n  }, []);\n  return [firstText, onEnter, onLeave];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACrD,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC9BC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,SAAS,GAAGV,QAAQ,CAAC,IAAI,CAAC;IAC5BW,UAAU,GAAGZ,cAAc,CAACW,SAAS,EAAE,CAAC,CAAC;IACzCE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,GAAG,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACtB,SAASa,QAAQA,CAACC,GAAG,EAAE;IACrB,IAAIC,WAAW,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC3FG,oBAAoB,CAACP,GAAG,CAACQ,OAAO,CAAC;IACjC,IAAIL,WAAW,EAAE;MACfJ,gBAAgB,CAACG,GAAG,CAAC;MACrB;IACF;IACAF,GAAG,CAACQ,OAAO,GAAGC,qBAAqB,CAAC,YAAY;MAC9CV,gBAAgB,CAACG,GAAG,CAAC;IACvB,CAAC,CAAC;EACJ;EACA,IAAIQ,cAAc,GAAGrB,aAAa,CAACS,KAAK,EAAE;MACtCL,UAAU,EAAEA,UAAU;MACtBC,cAAc,EAAEA,cAAc;MAC9BC,MAAM,EAAEA;IACV,CAAC,CAAC;IACFgB,eAAe,GAAG1B,cAAc,CAACyB,cAAc,EAAE,CAAC,CAAC;IACnDE,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;EAChC,SAASE,OAAOA,CAACC,IAAI,EAAE;IACrBb,QAAQ,CAACa,IAAI,CAAC;EAChB;EACA,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIZ,WAAW,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC3FH,QAAQ,CAAC,IAAI,EAAEE,WAAW,CAAC;EAC7B;EACAhB,SAAS,CAAC,YAAY;IACpB4B,OAAO,CAAC,IAAI,CAAC;EACf,CAAC,EAAE,CAACxB,SAAS,CAAC,CAAC;EACfJ,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjB,OAAOoB,oBAAoB,CAACP,GAAG,CAACQ,OAAO,CAAC;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACI,SAAS,EAAEC,OAAO,EAAEE,OAAO,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}