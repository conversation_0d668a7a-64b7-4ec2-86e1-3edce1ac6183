{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  var _selectedTask$result$, _selectedTask$result$2, _selectedTask$result$3;\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = taskId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 13\n      }, this),\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 13\n      }, this),\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",\n            children: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true,\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            danger: true,\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 23\n            }, this),\n            disabled: completedTasks.length === 0,\n            onClick: handleClearCompleted,\n            children: \"\\u6E05\\u7A7A\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: completedTasks,\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleDeleteSingleTask(task.task_id),\n                children: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 17\n          }, this), selectedTask.result && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: selectedTask.task_type === 'training' ? '🎯 训练结果:' : '🔍 预测结果:'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 19\n            }, this), selectedTask.task_type === 'training' && selectedTask.result.r2_score && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"R\\xB2 \\u5206\\u6570: \", selectedTask.result.r2_score.toFixed(4)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u8BAD\\u7EC3\\u6837\\u672C: \", ((_selectedTask$result$ = selectedTask.result.train_shape) === null || _selectedTask$result$ === void 0 ? void 0 : _selectedTask$result$[0]) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"orange\",\n                  children: [\"\\u6D4B\\u8BD5\\u6837\\u672C: \", ((_selectedTask$result$2 = selectedTask.result.test_shape) === null || _selectedTask$result$2 === void 0 ? void 0 : _selectedTask$result$2[0]) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 25\n                }, this), selectedTask.result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5F02\\u5E38\\u9608\\u503C: \", selectedTask.result.static_anomaly_threshold.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 21\n            }, this), selectedTask.task_type === 'prediction' && selectedTask.result.anomaly_count !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5F02\\u5E38\\u6570\\u91CF: \", selectedTask.result.anomaly_count]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"\\u9884\\u6D4B\\u6570\\u636E\\u70B9: \", ((_selectedTask$result$3 = selectedTask.result.predictions) === null || _selectedTask$result$3 === void 0 ? void 0 : _selectedTask$result$3.length) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 25\n                }, this), selectedTask.result.suggested_threshold && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"orange\",\n                  children: [\"\\u5EFA\\u8BAE\\u9608\\u503C: \", selectedTask.result.suggested_threshold.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u6A21\\u578B: \", selectedTask.result.model_name || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 16,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.result, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"r5mtZqJgQ/WOUShIGG9323lPtOs=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "MinusCircleOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "_selectedTask$result$", "_selectedTask$result$2", "_selectedTask$result$3", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "deleteSingleTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleDeleteSingleTask", "taskId", "confirm", "title", "content", "substring", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "okText", "okType", "cancelText", "onOk", "handleViewTaskDetail", "task", "handleCancelTask", "console", "handleClearCompleted", "completedCount", "length", "info", "getTaskStatusIcon", "status", "RUNNING", "spin", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "children", "formatTime", "timeString", "Date", "toLocaleString", "padding", "marginBottom", "display", "justifyContent", "alignItems", "level", "type", "onClick", "gutter", "span", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "slice", "created_at", "progress", "undefined", "marginTop", "percent", "size", "disabled", "fontSize", "updated_at", "open", "onCancel", "footer", "width", "result", "r2_score", "wrap", "toFixed", "train_shape", "test_shape", "static_anomaly_threshold", "anomaly_count", "predictions", "suggested_threshold", "model_name", "background", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>任务管理</Title>\n          <Text type=\"secondary\">\n            查看和管理异步训练、预测任务的状态和结果\n          </Text>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.result && (\n                <Col span={24}>\n                  <Text strong>\n                    {selectedTask.task_type === 'training' ? '🎯 训练结果:' : '🔍 预测结果:'}\n                  </Text>\n                  <br />\n                  {selectedTask.task_type === 'training' && selectedTask.result.r2_score && (\n                    <div style={{ marginTop: 8, marginBottom: 16 }}>\n                      <Space wrap>\n                        <Tag color=\"blue\">R² 分数: {selectedTask.result.r2_score.toFixed(4)}</Tag>\n                        <Tag color=\"green\">训练样本: {selectedTask.result.train_shape?.[0] || 'N/A'}</Tag>\n                        <Tag color=\"orange\">测试样本: {selectedTask.result.test_shape?.[0] || 'N/A'}</Tag>\n                        {selectedTask.result.static_anomaly_threshold && (\n                          <Tag color=\"red\">异常阈值: {selectedTask.result.static_anomaly_threshold.toFixed(2)}</Tag>\n                        )}\n                      </Space>\n                    </div>\n                  )}\n                  {selectedTask.task_type === 'prediction' && selectedTask.result.anomaly_count !== undefined && (\n                    <div style={{ marginTop: 8, marginBottom: 16 }}>\n                      <Space wrap>\n                        <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                        <Tag color=\"blue\">预测数据点: {selectedTask.result.predictions?.length || 'N/A'}</Tag>\n                        {selectedTask.result.suggested_threshold && (\n                          <Tag color=\"orange\">建议阈值: {selectedTask.result.suggested_threshold.toFixed(2)}</Tag>\n                        )}\n                        <Tag color=\"green\">模型: {selectedTask.result.model_name || 'N/A'}</Tag>\n                      </Space>\n                    </div>\n                  )}\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 16,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.result, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGzB,UAAU;AAElC,MAAM0B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,gBAAgB;IAChBC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGrB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd0C,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACA3C,SAAS,CAAC,MAAM;IACd,MAAMuD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCd,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMe,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACb,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCJ,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMK,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF9B,OAAO,CAACgD,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMS,sBAAsB,GAAIC,MAAc,IAAK;IACjDrD,KAAK,CAACsD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,WAAWH,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ;MAClDC,IAAI,eAAExC,OAAA,CAACH,mBAAmB;QAAC4C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1DC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,MAAMlB,OAAO,GAAG,MAAMhB,gBAAgB,CAACmB,MAAM,CAAC;QAC9C,IAAIH,OAAO,EAAE;UACX;UACA,MAAMF,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAIC,IAAU,IAAK;IAC3C/B,eAAe,CAAC+B,IAAI,CAAC;IACrB7B,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM8B,gBAAgB,GAAG,MAAOlB,MAAc,IAAK;IACjD,IAAI;MACF,MAAMpB,UAAU,CAACoB,MAAM,CAAC;MACxB,MAAMtB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,cAAc,GAAG7C,cAAc,CAAC8C,MAAM;IAC5C,IAAID,cAAc,KAAK,CAAC,EAAE;MACxBxE,OAAO,CAAC0E,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA5E,KAAK,CAACsD,OAAO,CAAC;MACZC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,WAAWkB,cAAc,oBAAoB;MACtDhB,IAAI,eAAExC,OAAA,CAACN,cAAc;QAAC+C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrDC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMjC,mBAAmB,CAAC,CAAC;UAC3B;UACA,MAAMa,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAKzC,WAAW,CAAC0C,OAAO;QACtB,oBAAO7D,OAAA,CAACZ,YAAY;UAAC0E,IAAI;UAACrB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK3B,WAAW,CAAC4C,SAAS;QACxB,oBAAO/D,OAAA,CAACX,mBAAmB;UAACoD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK3B,WAAW,CAAC6C,MAAM;QACrB,oBAAOhE,OAAA,CAACV,mBAAmB;UAACmD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK3B,WAAW,CAAC8C,SAAS;QACxB,oBAAOjE,OAAA,CAACT,YAAY;UAACkD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAO9C,OAAA,CAACR,mBAAmB;UAACiD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAIN,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAKzC,WAAW,CAAC0C,OAAO;QACtB,oBAAO7D,OAAA,CAACpB,GAAG;UAAC8D,KAAK,EAAC,YAAY;UAACF,IAAI,eAAExC,OAAA,CAACZ,YAAY;YAAC0E,IAAI;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK3B,WAAW,CAAC4C,SAAS;QACxB,oBAAO/D,OAAA,CAACpB,GAAG;UAAC8D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAExC,OAAA,CAACX,mBAAmB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK3B,WAAW,CAAC6C,MAAM;QACrB,oBAAOhE,OAAA,CAACpB,GAAG;UAAC8D,KAAK,EAAC,OAAO;UAACF,IAAI,eAAExC,OAAA,CAACV,mBAAmB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK3B,WAAW,CAAC8C,SAAS;QACxB,oBAAOjE,OAAA,CAACpB,GAAG;UAAC8D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAExC,OAAA,CAACT,YAAY;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAO9C,OAAA,CAACpB,GAAG;UAAC8D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAExC,OAAA,CAACR,mBAAmB;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;EAED,oBACEvE,OAAA;IAAKyC,KAAK,EAAE;MAAE+B,OAAO,EAAE;IAAO,CAAE;IAAAL,QAAA,gBAC9BnE,OAAA;MAAKyC,KAAK,EAAE;QAAEgC,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAT,QAAA,gBAC3GnE,OAAA;QAAAmE,QAAA,gBACEnE,OAAA,CAACG,KAAK;UAAC0E,KAAK,EAAE,CAAE;UAAAV,QAAA,EAAC;QAAI;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B9C,OAAA,CAACI,IAAI;UAAC0E,IAAI,EAAC,WAAW;UAAAX,QAAA,EAAC;QAEvB;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN9C,OAAA,CAACvB,MAAM;QACLqG,IAAI,EAAC,SAAS;QACdtC,IAAI,eAAExC,OAAA,CAACL,cAAc;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBiC,OAAO,EAAElD,aAAc;QACvBjB,OAAO,EAAEY,UAAW;QAAA2C,QAAA,EACrB;MAED;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN9C,OAAA,CAAC5B,IAAI;MAACqE,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,eACpCnE,OAAA,CAAC3B,GAAG;QAAC2G,MAAM,EAAE,EAAG;QAAAb,QAAA,gBACdnE,OAAA,CAAC1B,GAAG;UAAC2G,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXnE,OAAA,CAACzB,SAAS;YACR8D,KAAK,EAAC,gCAAO;YACb6C,KAAK,EAAExE,YAAY,CAAC+C,MAAO;YAC3B0B,MAAM,eAAEnF,OAAA,CAACZ,YAAY;cAAC0E,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA,CAAC1B,GAAG;UAAC2G,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXnE,OAAA,CAACzB,SAAS;YACR8D,KAAK,EAAC,gCAAO;YACb6C,KAAK,EAAEvE,cAAc,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAKzC,WAAW,CAAC4C,SAAS,CAAC,CAACN,MAAO;YAC7E0B,MAAM,eAAEnF,OAAA,CAACX,mBAAmB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA,CAAC1B,GAAG;UAAC2G,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXnE,OAAA,CAACzB,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAEvE,cAAc,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,CAAC,CAACP,MAAO;YAC1E0B,MAAM,eAAEnF,OAAA,CAACV,mBAAmB;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9C,OAAA,CAAC1B,GAAG;UAAC2G,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXnE,OAAA,CAACzB,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAExE,YAAY,CAAC+C,MAAM,GAAG9C,cAAc,CAAC8C,MAAO;YACnD0B,MAAM,eAAEnF,OAAA,CAACJ,kBAAkB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP9C,OAAA,CAAC3B,GAAG;MAAC2G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAb,QAAA,gBAEpBnE,OAAA,CAAC1B,GAAG;QAAC2G,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZnE,OAAA,CAAC5B,IAAI;UACHiE,KAAK,eACHrC,OAAA,CAACtB,KAAK;YAAAyF,QAAA,gBACJnE,OAAA,CAACZ,YAAY;cAAC0E,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAErB,eAAA9C,OAAA,CAACpB,GAAG;cAAC8D,KAAK,EAAC,YAAY;cAAAyB,QAAA,EAAEzD,YAAY,CAAC+C;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACDyC,KAAK,eACHvF,OAAA,CAACd,OAAO;YAACmD,KAAK,EAAC,gCAAO;YAAA8B,QAAA,eACpBnE,OAAA,CAACZ,YAAY;cAAC0E,IAAI;cAACrB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACV;UAAAqB,QAAA,eAEDnE,OAAA,CAACf,IAAI;YAACuG,QAAQ,EAAE5E,OAAQ;YAAAuD,QAAA,eACtBnE,OAAA,CAACxB,IAAI;cACHiH,UAAU,EAAE/E,YAAa;cACzBgF,MAAM,EAAE;gBAAEC,SAAS,eAAE3F,OAAA,CAACjB,KAAK;kBAAC6G,WAAW,EAAC;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxD+C,UAAU,EAAGzC,IAAI,iBACfpD,OAAA,CAACxB,IAAI,CAACsH,IAAI;gBACRC,OAAO,EAAE,cACP/F,OAAA,CAACvB,MAAM;kBACLqG,IAAI,EAAC,MAAM;kBACXtC,IAAI,eAAExC,OAAA,CAACP,WAAW;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBiC,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACC,IAAI,CAAE;kBAAAe,QAAA,EAC3C;gBAED;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9C,OAAA,CAACb,UAAU;kBACTkD,KAAK,EAAC,oEAAa;kBACnB2D,SAAS,EAAEA,CAAA,KAAM3C,gBAAgB,CAACD,IAAI,CAAC6C,OAAO,CAAE;kBAChDlD,MAAM,EAAC,cAAI;kBACXE,UAAU,EAAC,cAAI;kBAAAkB,QAAA,eAEfnE,OAAA,CAACvB,MAAM;oBACLqG,IAAI,EAAC,MAAM;oBACXoB,MAAM;oBACN1D,IAAI,eAAExC,OAAA,CAACT,YAAY;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAqB,QAAA,EACxB;kBAED;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAqB,QAAA,eAEFnE,OAAA,CAACxB,IAAI,CAACsH,IAAI,CAACK,IAAI;kBACbC,MAAM,EAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;kBACvCvB,KAAK,eACHrC,OAAA,CAACtB,KAAK;oBAAAyF,QAAA,gBACJnE,OAAA,CAACI,IAAI;sBAACiG,MAAM;sBAAAlC,QAAA,EAAEjD,cAAc,CAACkC,IAAI,CAACkD,SAAS;oBAAC;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACD8C,WAAW,eACT5F,OAAA;oBAAAmE,QAAA,gBACEnE,OAAA,CAACI,IAAI;sBAAC0E,IAAI,EAAC,WAAW;sBAACyB,QAAQ,EAAE;wBAAEC,IAAI,EAAEpD,IAAI,CAAC6C;sBAAQ,CAAE;sBAAA9B,QAAA,GAAC,MACnD,EAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAAC6C,OAAO,CAAC1D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAElC,CAAC,eACP9C,OAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;sBAAC0E,IAAI,EAAC,WAAW;sBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACwD,UAAU,CAAC;oBAAA;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEM,IAAI,CAACpE,OAAO,iBACXgB,OAAA,CAAAE,SAAA;sBAAAiE,QAAA,gBACEnE,OAAA;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;wBAAC0E,IAAI,EAAC,WAAW;wBAAAX,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACpE,OAAO;sBAAA;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAM,IAAI,CAACyD,QAAQ,KAAKC,SAAS,iBAC1B9G,OAAA;sBAAKyC,KAAK,EAAE;wBAAEsE,SAAS,EAAE;sBAAE,CAAE;sBAAA5C,QAAA,eAC3BnE,OAAA,CAACnB,QAAQ;wBAACmI,OAAO,EAAE5D,IAAI,CAACyD,QAAS;wBAACI,IAAI,EAAC;sBAAO;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9C,OAAA,CAAC1B,GAAG;QAAC2G,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZnE,OAAA,CAAC5B,IAAI;UACHiE,KAAK,eACHrC,OAAA,CAACtB,KAAK;YAAAyF,QAAA,gBACJnE,OAAA,CAACX,mBAAmB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAA9C,OAAA,CAACpB,GAAG;cAAC8D,KAAK,EAAC,SAAS;cAAAyB,QAAA,EAAExD,cAAc,CAAC8C;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACDyC,KAAK,eACHvF,OAAA,CAACvB,MAAM;YACLqG,IAAI,EAAC,SAAS;YACdoB,MAAM;YACNe,IAAI,EAAC,OAAO;YACZzE,IAAI,eAAExC,OAAA,CAACN,cAAc;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBoE,QAAQ,EAAEvG,cAAc,CAAC8C,MAAM,KAAK,CAAE;YACtCsB,OAAO,EAAExB,oBAAqB;YAAAY,QAAA,EAC/B;UAED;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAqB,QAAA,eAEDnE,OAAA,CAACxB,IAAI;YACHiH,UAAU,EAAE9E,cAAe;YAC3B+E,MAAM,EAAE;cACNC,SAAS,eACP3F,OAAA,CAACjB,KAAK;gBACJ6G,WAAW,eACT5F,OAAA;kBAAAmE,QAAA,gBACEnE,OAAA;oBAAAmE,QAAA,EAAK;kBAAQ;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnB9C,OAAA,CAACI,IAAI;oBAAC0E,IAAI,EAAC,WAAW;oBAACrC,KAAK,EAAE;sBAAE0E,QAAQ,EAAE;oBAAO,CAAE;oBAAAhD,QAAA,EAAC;kBAEpD;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACF+C,UAAU,EAAGzC,IAAI,iBACfpD,OAAA,CAACxB,IAAI,CAACsH,IAAI;cACRC,OAAO,EAAE,cACP/F,OAAA,CAACvB,MAAM;gBACLqG,IAAI,EAAC,MAAM;gBACXtC,IAAI,eAAExC,OAAA,CAACP,WAAW;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBiC,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACC,IAAI,CAAE;gBAAAe,QAAA,EAC3C;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9C,OAAA,CAACvB,MAAM;gBACLqG,IAAI,EAAC,MAAM;gBACXoB,MAAM;gBACN1D,IAAI,eAAExC,OAAA,CAACH,mBAAmB;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BiC,OAAO,EAAEA,CAAA,KAAM7C,sBAAsB,CAACkB,IAAI,CAAC6C,OAAO,CAAE;gBAAA9B,QAAA,EACrD;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAqB,QAAA,eAEFnE,OAAA,CAACxB,IAAI,CAACsH,IAAI,CAACK,IAAI;gBACbC,MAAM,EAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;gBACvCvB,KAAK,eACHrC,OAAA,CAACtB,KAAK;kBAAAyF,QAAA,gBACJnE,OAAA,CAACI,IAAI;oBAACiG,MAAM;oBAAAlC,QAAA,EAAEjD,cAAc,CAACkC,IAAI,CAACkD,SAAS;kBAAC;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACD8C,WAAW,eACT5F,OAAA;kBAAAmE,QAAA,gBACEnE,OAAA,CAACI,IAAI;oBAAC0E,IAAI,EAAC,WAAW;oBAACyB,QAAQ,EAAE;sBAAEC,IAAI,EAAEpD,IAAI,CAAC6C;oBAAQ,CAAE;oBAAA9B,QAAA,GAAC,MACnD,EAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAAC6C,OAAO,CAAC1D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElC,CAAC,eACP9C,OAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;oBAAC0E,IAAI,EAAC,WAAW;oBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACgE,UAAU,CAAC;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEM,IAAI,CAACQ,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,IAAIZ,IAAI,CAACnB,KAAK,iBAC/CjC,OAAA,CAAAE,SAAA;oBAAAiE,QAAA,gBACEnE,OAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;sBAAC0E,IAAI,EAAC,QAAQ;sBAAAX,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACnB,KAAK;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAAClB,KAAK;MACJuD,KAAK,EAAC,0BAAM;MACZgF,IAAI,EAAE/F,iBAAkB;MACxBgG,QAAQ,EAAEA,CAAA,KAAM/F,oBAAoB,CAAC,KAAK,CAAE;MAC5CgG,MAAM,EAAE,cACNvH,OAAA,CAACvB,MAAM;QAAasG,OAAO,EAAEA,CAAA,KAAMxD,oBAAoB,CAAC,KAAK,CAAE;QAAA4C,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACF0E,KAAK,EAAE,GAAI;MAAArD,QAAA,EAEV/C,YAAY,iBACXpB,OAAA;QAAAmE,QAAA,eACEnE,OAAA,CAAC3B,GAAG;UAAC2G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACpBnE,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAACmG,QAAQ;cAAApC,QAAA,EAAE/C,YAAY,CAAC6E;YAAO;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN9C,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAAA+D,QAAA,EAAEjD,cAAc,CAACE,YAAY,CAACkF,SAAS;YAAC;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN9C,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLoB,gBAAgB,CAAC9C,YAAY,CAACwC,MAAM,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN9C,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL1B,YAAY,CAACyF,QAAQ,KAAKC,SAAS,gBAClC9G,OAAA,CAACnB,QAAQ;cAACmI,OAAO,EAAE5F,YAAY,CAACyF,QAAS;cAACI,IAAI,EAAC;YAAO;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD9C,OAAA,CAACI,IAAI;cAAC0E,IAAI,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN9C,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAAA+D,QAAA,EAAEC,UAAU,CAAChD,YAAY,CAACwF,UAAU;YAAC;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN9C,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAAA+D,QAAA,EAAEC,UAAU,CAAChD,YAAY,CAACgG,UAAU;YAAC;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,EACL1B,YAAY,CAACpC,OAAO,iBACnBgB,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAAA+D,QAAA,EAAE/C,YAAY,CAACpC;YAAO;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA1B,YAAY,CAACa,KAAK,iBACjBjC,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9C,OAAA,CAACI,IAAI;cAAC0E,IAAI,EAAC,QAAQ;cAAAX,QAAA,EAAE/C,YAAY,CAACa;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA1B,YAAY,CAACqG,MAAM,iBAClBzH,OAAA,CAAC1B,GAAG;YAAC2G,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZnE,OAAA,CAACI,IAAI;cAACiG,MAAM;cAAAlC,QAAA,EACT/C,YAAY,CAACkF,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG;YAAU;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACP9C,OAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL1B,YAAY,CAACkF,SAAS,KAAK,UAAU,IAAIlF,YAAY,CAACqG,MAAM,CAACC,QAAQ,iBACpE1H,OAAA;cAAKyC,KAAK,EAAE;gBAAEsE,SAAS,EAAE,CAAC;gBAAEtC,YAAY,EAAE;cAAG,CAAE;cAAAN,QAAA,eAC7CnE,OAAA,CAACtB,KAAK;gBAACiJ,IAAI;gBAAAxD,QAAA,gBACTnE,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,MAAM;kBAAAyB,QAAA,GAAC,sBAAO,EAAC/C,YAAY,CAACqG,MAAM,CAACC,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxE9C,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,OAAO;kBAAAyB,QAAA,GAAC,4BAAM,EAAC,EAAA5D,qBAAA,GAAAa,YAAY,CAACqG,MAAM,CAACI,WAAW,cAAAtH,qBAAA,uBAA/BA,qBAAA,CAAkC,CAAC,CAAC,KAAI,KAAK;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9E9C,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,QAAQ;kBAAAyB,QAAA,GAAC,4BAAM,EAAC,EAAA3D,sBAAA,GAAAY,YAAY,CAACqG,MAAM,CAACK,UAAU,cAAAtH,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,CAAC,KAAI,KAAK;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC7E1B,YAAY,CAACqG,MAAM,CAACM,wBAAwB,iBAC3C/H,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,KAAK;kBAAAyB,QAAA,GAAC,4BAAM,EAAC/C,YAAY,CAACqG,MAAM,CAACM,wBAAwB,CAACH,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACA1B,YAAY,CAACkF,SAAS,KAAK,YAAY,IAAIlF,YAAY,CAACqG,MAAM,CAACO,aAAa,KAAKlB,SAAS,iBACzF9G,OAAA;cAAKyC,KAAK,EAAE;gBAAEsE,SAAS,EAAE,CAAC;gBAAEtC,YAAY,EAAE;cAAG,CAAE;cAAAN,QAAA,eAC7CnE,OAAA,CAACtB,KAAK;gBAACiJ,IAAI;gBAAAxD,QAAA,gBACTnE,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,KAAK;kBAAAyB,QAAA,GAAC,4BAAM,EAAC/C,YAAY,CAACqG,MAAM,CAACO,aAAa;gBAAA;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChE9C,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,MAAM;kBAAAyB,QAAA,GAAC,kCAAO,EAAC,EAAA1D,sBAAA,GAAAW,YAAY,CAACqG,MAAM,CAACQ,WAAW,cAAAxH,sBAAA,uBAA/BA,sBAAA,CAAiCgD,MAAM,KAAI,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAChF1B,YAAY,CAACqG,MAAM,CAACS,mBAAmB,iBACtClI,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,QAAQ;kBAAAyB,QAAA,GAAC,4BAAM,EAAC/C,YAAY,CAACqG,MAAM,CAACS,mBAAmB,CAACN,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACpF,eACD9C,OAAA,CAACpB,GAAG;kBAAC8D,KAAK,EAAC,OAAO;kBAAAyB,QAAA,GAAC,gBAAI,EAAC/C,YAAY,CAACqG,MAAM,CAACU,UAAU,IAAI,KAAK;gBAAA;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eACD9C,OAAA;cAAKyC,KAAK,EAAE;gBACV2F,UAAU,EAAE,SAAS;gBACrB5D,OAAO,EAAE,EAAE;gBACX6D,YAAY,EAAE,CAAC;gBACflB,QAAQ,EAAE,EAAE;gBACZmB,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE;cACZ,CAAE;cAAApE,QAAA,eACAnE,OAAA;gBAAAmE,QAAA,EAAMqE,IAAI,CAACC,SAAS,CAACrH,YAAY,CAACqG,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxC,EAAA,CArfID,eAAyB;EAAA,QAYzBP,cAAc;AAAA;AAAA4I,EAAA,GAZdrI,eAAyB;AAuf/B,eAAeA,eAAe;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}