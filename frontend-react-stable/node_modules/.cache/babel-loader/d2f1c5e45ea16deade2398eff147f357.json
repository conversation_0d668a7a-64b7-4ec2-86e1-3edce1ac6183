{"ast": null, "code": "import Tree from './Tree';\nimport TreeNode from './TreeNode';\nexport { TreeNode };\nexport default Tree;", "map": {"version": 3, "names": ["Tree", "TreeNode"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/index.js"], "sourcesContent": ["import Tree from './Tree';\nimport TreeNode from './TreeNode';\nexport { TreeNode };\nexport default Tree;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASA,QAAQ;AACjB,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}