{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport <PERSON><PERSON><PERSON>OutlinedSvg from \"@ant-design/icons-svg/es/asn/DotChartOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar DotChartOutlined = function DotChartOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: Dot<PERSON>hartOutlinedSvg\n  }));\n};\nDotChartOutlined.displayName = 'DotChartOutlined';\nexport default /*#__PURE__*/React.forwardRef(DotChartOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "DotChartOutlinedSvg", "AntdIcon", "DotChartOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/DotChartOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport <PERSON><PERSON><PERSON>OutlinedSvg from \"@ant-design/icons-svg/es/asn/DotChartOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar DotChartOutlined = function DotChartOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: Dot<PERSON>hartOutlinedSvg\n  }));\n};\nDotChartOutlined.displayName = 'DotChartOutlined';\nexport default /*#__PURE__*/React.forwardRef(DotChartOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,gBAAgB,CAACK,WAAW,GAAG,kBAAkB;AACjD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}