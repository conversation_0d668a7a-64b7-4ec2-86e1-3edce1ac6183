{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nvar inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nvar TransButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var onKeyDown = function onKeyDown(event) {\n    var keyCode = event.keyCode;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp(event) {\n    var keyCode = event.keyCode;\n    var onClick = props.onClick;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  var style = props.style,\n    noStyle = props.noStyle,\n    disabled = props.disabled,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  var mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = _extends({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = _extends(_extends({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "KeyCode", "React", "inlineStyle", "border", "background", "padding", "lineHeight", "display", "TransButton", "forwardRef", "props", "ref", "onKeyDown", "event", "keyCode", "ENTER", "preventDefault", "onKeyUp", "onClick", "style", "noStyle", "disabled", "restProps", "mergedStyle", "pointerEvents", "createElement", "role", "tabIndex"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/transButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nvar inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nvar TransButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var onKeyDown = function onKeyDown(event) {\n    var keyCode = event.keyCode;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp(event) {\n    var keyCode = event.keyCode;\n    var onClick = props.onClick;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  var style = props.style,\n    noStyle = props.noStyle,\n    disabled = props.disabled,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  var mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = _extends({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = _extends(_extends({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAOW,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG;EAChBC,MAAM,EAAE,CAAC;EACTC,UAAU,EAAE,aAAa;EACzBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,WAAW,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC3B,IAAIA,OAAO,KAAKd,OAAO,CAACe,KAAK,EAAE;MAC7BF,KAAK,CAACG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACJ,KAAK,EAAE;IACpC,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC3B,IAAII,OAAO,GAAGR,KAAK,CAACQ,OAAO;IAC3B,IAAIJ,OAAO,KAAKd,OAAO,CAACe,KAAK,IAAIG,OAAO,EAAE;MACxCA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EACD,IAAIC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACrBC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,SAAS,GAAGpC,MAAM,CAACwB,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;EAC7D,IAAIa,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI,CAACH,OAAO,EAAE;IACZG,WAAW,GAAGtC,QAAQ,CAAC,CAAC,CAAC,EAAEiB,WAAW,CAAC;EACzC;EACA,IAAImB,QAAQ,EAAE;IACZE,WAAW,CAACC,aAAa,GAAG,MAAM;EACpC;EACAD,WAAW,GAAGtC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsC,WAAW,CAAC,EAAEJ,KAAK,CAAC;EACxD,OAAO,aAAalB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAExC,QAAQ,CAAC;IACtDyC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,CAAC;IACXhB,GAAG,EAAEA;EACP,CAAC,EAAEW,SAAS,EAAE;IACZV,SAAS,EAAEA,SAAS;IACpBK,OAAO,EAAEA,OAAO;IAChBE,KAAK,EAAEI;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}