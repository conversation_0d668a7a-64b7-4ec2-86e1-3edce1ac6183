{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport function renderExpandIcon(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    record = _ref.record,\n    onExpand = _ref.onExpand,\n    expanded = _ref.expanded,\n    expandable = _ref.expandable;\n  var expandClassName = \"\".concat(prefixCls, \"-row-expand-icon\");\n  if (!expandable) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(expandClassName, \"\".concat(prefixCls, \"-row-spaced\"))\n    });\n  }\n  var onClick = function onClick(event) {\n    onExpand(record, event);\n    event.stopPropagation();\n  };\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(expandClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-row-expanded\"), expanded), _defineProperty(_classNames, \"\".concat(prefixCls, \"-row-collapsed\"), !expanded), _classNames)),\n    onClick: onClick\n  });\n}\nexport function findAllChildrenKeys(data, getRowKey, childrenColumnName) {\n  var keys = [];\n  function dig(list) {\n    (list || []).forEach(function (item, index) {\n      keys.push(getRowKey(item, index));\n      dig(item[childrenColumnName]);\n    });\n  }\n  dig(data);\n  return keys;\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "renderExpandIcon", "_ref", "_classNames", "prefixCls", "record", "onExpand", "expanded", "expandable", "expandClassName", "concat", "createElement", "className", "onClick", "event", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "getRowKey", "childrenColumnName", "keys", "dig", "list", "for<PERSON>ach", "item", "index", "push"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/utils/expandUtil.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport function renderExpandIcon(_ref) {\n  var _classNames;\n\n  var prefixCls = _ref.prefixCls,\n      record = _ref.record,\n      onExpand = _ref.onExpand,\n      expanded = _ref.expanded,\n      expandable = _ref.expandable;\n  var expandClassName = \"\".concat(prefixCls, \"-row-expand-icon\");\n\n  if (!expandable) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(expandClassName, \"\".concat(prefixCls, \"-row-spaced\"))\n    });\n  }\n\n  var onClick = function onClick(event) {\n    onExpand(record, event);\n    event.stopPropagation();\n  };\n\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(expandClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-row-expanded\"), expanded), _defineProperty(_classNames, \"\".concat(prefixCls, \"-row-collapsed\"), !expanded), _classNames)),\n    onClick: onClick\n  });\n}\nexport function findAllChildrenKeys(data, getRowKey, childrenColumnName) {\n  var keys = [];\n\n  function dig(list) {\n    (list || []).forEach(function (item, index) {\n      keys.push(getRowKey(item, index));\n      dig(item[childrenColumnName]);\n    });\n  }\n\n  dig(data);\n  return keys;\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,UAAU,GAAGN,IAAI,CAACM,UAAU;EAChC,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,kBAAkB,CAAC;EAE9D,IAAI,CAACI,UAAU,EAAE;IACf,OAAO,aAAaT,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;MAC9CC,SAAS,EAAEZ,UAAU,CAACS,eAAe,EAAE,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,aAAa,CAAC;IAC5E,CAAC,CAAC;EACJ;EAEA,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpCR,QAAQ,CAACD,MAAM,EAAES,KAAK,CAAC;IACvBA,KAAK,CAACC,eAAe,CAAC,CAAC;EACzB,CAAC;EAED,OAAO,aAAahB,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEZ,UAAU,CAACS,eAAe,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,WAAW,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,eAAe,CAAC,EAAEG,QAAQ,CAAC,EAAET,eAAe,CAACK,WAAW,EAAE,EAAE,CAACO,MAAM,CAACN,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACG,QAAQ,CAAC,EAAEJ,WAAW,CAAC,CAAC;IACvOU,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,mBAAmBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,kBAAkB,EAAE;EACvE,IAAIC,IAAI,GAAG,EAAE;EAEb,SAASC,GAAGA,CAACC,IAAI,EAAE;IACjB,CAACA,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;MAC1CL,IAAI,CAACM,IAAI,CAACR,SAAS,CAACM,IAAI,EAAEC,KAAK,CAAC,CAAC;MACjCJ,GAAG,CAACG,IAAI,CAACL,kBAAkB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEAE,GAAG,CAACJ,IAAI,CAAC;EACT,OAAOG,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}