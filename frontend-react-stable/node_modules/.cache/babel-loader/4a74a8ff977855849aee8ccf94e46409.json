{"ast": null, "code": "import { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDom from \"./Dom/canUseDom\";\nvar Portal = /*#__PURE__*/forwardRef(function (props, ref) {\n  var didUpdate = props.didUpdate,\n    getContainer = props.getContainer,\n    children = props.children;\n  var parentRef = useRef();\n  var containerRef = useRef();\n\n  // Ref return nothing, only for wrapper check exist\n  useImperativeHandle(ref, function () {\n    return {};\n  });\n\n  // Create container in client side with sync to avoid useEffect not get ref\n  var initRef = useRef(false);\n  if (!initRef.current && canUseDom()) {\n    containerRef.current = getContainer();\n    parentRef.current = containerRef.current.parentNode;\n    initRef.current = true;\n  }\n\n  // [Legacy] Used by `rc-trigger`\n  useEffect(function () {\n    didUpdate === null || didUpdate === void 0 || didUpdate(props);\n  });\n  useEffect(function () {\n    // Restore container to original place\n    // React 18 StrictMode will unmount first and mount back for effect test:\n    // https://reactjs.org/blog/2022/03/29/react-v18.html#new-strict-mode-behaviors\n    if (containerRef.current.parentNode === null && parentRef.current !== null) {\n      parentRef.current.appendChild(containerRef.current);\n    }\n    return function () {\n      var _containerRef$current;\n      // [Legacy] This should not be handle by Portal but parent PortalWrapper instead.\n      // Since some component use `Portal` directly, we have to keep the logic here.\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 || (_containerRef$current = _containerRef$current.parentNode) === null || _containerRef$current === void 0 || _containerRef$current.removeChild(containerRef.current);\n    };\n  }, []);\n  return containerRef.current ? /*#__PURE__*/ReactDOM.createPortal(children, containerRef.current) : null;\n});\nexport default Portal;", "map": {"version": 3, "names": ["useRef", "useEffect", "forwardRef", "useImperativeHandle", "ReactDOM", "canUseDom", "Portal", "props", "ref", "didUpdate", "getContainer", "children", "parentRef", "containerRef", "initRef", "current", "parentNode", "append<PERSON><PERSON><PERSON>", "_containerRef$current", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/Portal.js"], "sourcesContent": ["import { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport ReactDOM from 'react-dom';\nimport canUseDom from \"./Dom/canUseDom\";\nvar Portal = /*#__PURE__*/forwardRef(function (props, ref) {\n  var didUpdate = props.didUpdate,\n    getContainer = props.getContainer,\n    children = props.children;\n  var parentRef = useRef();\n  var containerRef = useRef();\n\n  // Ref return nothing, only for wrapper check exist\n  useImperativeHandle(ref, function () {\n    return {};\n  });\n\n  // Create container in client side with sync to avoid useEffect not get ref\n  var initRef = useRef(false);\n  if (!initRef.current && canUseDom()) {\n    containerRef.current = getContainer();\n    parentRef.current = containerRef.current.parentNode;\n    initRef.current = true;\n  }\n\n  // [Legacy] Used by `rc-trigger`\n  useEffect(function () {\n    didUpdate === null || didUpdate === void 0 || didUpdate(props);\n  });\n  useEffect(function () {\n    // Restore container to original place\n    // React 18 StrictMode will unmount first and mount back for effect test:\n    // https://reactjs.org/blog/2022/03/29/react-v18.html#new-strict-mode-behaviors\n    if (containerRef.current.parentNode === null && parentRef.current !== null) {\n      parentRef.current.appendChild(containerRef.current);\n    }\n    return function () {\n      var _containerRef$current;\n      // [Legacy] This should not be handle by Portal but parent PortalWrapper instead.\n      // Since some component use `Portal` directly, we have to keep the logic here.\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 || (_containerRef$current = _containerRef$current.parentNode) === null || _containerRef$current === void 0 || _containerRef$current.removeChild(containerRef.current);\n    };\n  }, []);\n  return containerRef.current ? /*#__PURE__*/ReactDOM.createPortal(children, containerRef.current) : null;\n});\nexport default Portal;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAC1E,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,IAAIC,MAAM,GAAG,aAAaJ,UAAU,CAAC,UAAUK,KAAK,EAAEC,GAAG,EAAE;EACzD,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC3B,IAAIC,SAAS,GAAGZ,MAAM,CAAC,CAAC;EACxB,IAAIa,YAAY,GAAGb,MAAM,CAAC,CAAC;;EAE3B;EACAG,mBAAmB,CAACK,GAAG,EAAE,YAAY;IACnC,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;;EAEF;EACA,IAAIM,OAAO,GAAGd,MAAM,CAAC,KAAK,CAAC;EAC3B,IAAI,CAACc,OAAO,CAACC,OAAO,IAAIV,SAAS,CAAC,CAAC,EAAE;IACnCQ,YAAY,CAACE,OAAO,GAAGL,YAAY,CAAC,CAAC;IACrCE,SAAS,CAACG,OAAO,GAAGF,YAAY,CAACE,OAAO,CAACC,UAAU;IACnDF,OAAO,CAACC,OAAO,GAAG,IAAI;EACxB;;EAEA;EACAd,SAAS,CAAC,YAAY;IACpBQ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACF,KAAK,CAAC;EAChE,CAAC,CAAC;EACFN,SAAS,CAAC,YAAY;IACpB;IACA;IACA;IACA,IAAIY,YAAY,CAACE,OAAO,CAACC,UAAU,KAAK,IAAI,IAAIJ,SAAS,CAACG,OAAO,KAAK,IAAI,EAAE;MAC1EH,SAAS,CAACG,OAAO,CAACE,WAAW,CAACJ,YAAY,CAACE,OAAO,CAAC;IACrD;IACA,OAAO,YAAY;MACjB,IAAIG,qBAAqB;MACzB;MACA;MACA,CAACA,qBAAqB,GAAGL,YAAY,CAACE,OAAO,MAAM,IAAI,IAAIG,qBAAqB,KAAK,KAAK,CAAC,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB,CAACF,UAAU,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,WAAW,CAACN,YAAY,CAACE,OAAO,CAAC;IACnQ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,YAAY,CAACE,OAAO,GAAG,aAAaX,QAAQ,CAACgB,YAAY,CAACT,QAAQ,EAAEE,YAAY,CAACE,OAAO,CAAC,GAAG,IAAI;AACzG,CAAC,CAAC;AACF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}