{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nimport _isObject from \"lodash/isObject\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React, { cloneElement, isValidElement, createElement } from 'react';\nimport classNames from 'classnames';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = function getLabel(props) {\n  var value = props.value,\n    formatter = props.formatter;\n  var label = _isNil(props.children) ? value : props.children;\n  if (_isFunction(formatter)) {\n    return formatter(label);\n  }\n  return label;\n};\nvar getDeltaAngle = function getDeltaAngle(startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = function renderRadialLabel(labelProps, label, attrs) {\n  var position = labelProps.position,\n    viewBox = labelProps.viewBox,\n    offset = labelProps.offset,\n    className = labelProps.className;\n  var _ref = viewBox,\n    cx = _ref.cx,\n    cy = _ref.cy,\n    innerRadius = _ref.innerRadius,\n    outerRadius = _ref.outerRadius,\n    startAngle = _ref.startAngle,\n    endAngle = _ref.endAngle,\n    clockWise = _ref.clockWise;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = _isNil(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: classNames('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = function getAttrsOfPolarLabel(props) {\n  var viewBox = props.viewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref2 = viewBox,\n    cx = _ref2.cx,\n    cy = _ref2.cy,\n    innerRadius = _ref2.innerRadius,\n    outerRadius = _ref2.outerRadius,\n    startAngle = _ref2.startAngle,\n    endAngle = _ref2.endAngle;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var _polarToCartesian = polarToCartesian(cx, cy, outerRadius + offset, midAngle),\n      _x = _polarToCartesian.x,\n      _y = _polarToCartesian.y;\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var _polarToCartesian2 = polarToCartesian(cx, cy, r, midAngle),\n    x = _polarToCartesian2.x,\n    y = _polarToCartesian2.y;\n  return {\n    x: x,\n    y: y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = function getAttrsOfCartesianLabel(props) {\n  var viewBox = props.viewBox,\n    parentViewBox = props.parentViewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref3 = viewBox,\n    x = _ref3.x,\n    y = _ref3.y,\n    width = _ref3.width,\n    height = _ref3.height;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width: width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width: width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height: height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height: height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width: width,\n    height: height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (_isObject(position) && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = function isPolar(viewBox) {\n  return 'cx' in viewBox && isNumber(viewBox.cx);\n};\nexport function Label(props) {\n  var viewBox = props.viewBox,\n    position = props.position,\n    value = props.value,\n    children = props.children,\n    content = props.content,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    textBreakAll = props.textBreakAll;\n  if (!viewBox || _isNil(value) && _isNil(children) && ! /*#__PURE__*/isValidElement(content) && !_isFunction(content)) {\n    return null;\n  }\n  if (/*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (_isFunction(content)) {\n    label = /*#__PURE__*/createElement(content, props);\n    if (/*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    className: classNames('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nLabel.defaultProps = {\n  offset: 5\n};\nvar parseViewBox = function parseViewBox(props) {\n  var cx = props.cx,\n    cy = props.cy,\n    angle = props.angle,\n    startAngle = props.startAngle,\n    endAngle = props.endAngle,\n    r = props.r,\n    radius = props.radius,\n    innerRadius = props.innerRadius,\n    outerRadius = props.outerRadius,\n    x = props.x,\n    y = props.y,\n    top = props.top,\n    left = props.left,\n    width = props.width,\n    height = props.height,\n    clockWise = props.clockWise,\n    labelViewBox = props.labelViewBox;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width: width,\n        height: height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x: x,\n      y: y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx: cx,\n      cy: cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise: clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = function parseLabel(label, viewBox) {\n  if (!label) {\n    return null;\n  }\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox\n    });\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox,\n      value: label\n    });\n  }\n  if (/*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, {\n        key: 'label-implicit',\n        viewBox: viewBox\n      });\n    }\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (_isFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (_isObject(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      viewBox: viewBox\n    }, label, {\n      key: \"label-implicit\"\n    }));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var children = parentProps.children;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map(function (child, index) {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox);\n  return [implicitLabel].concat(_toConsumableArray(explicitChildren));\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_isObject", "_isFunction", "_isNil", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "length", "i", "arr2", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "arguments", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "_extends", "assign", "bind", "hasOwnProperty", "React", "cloneElement", "isValidElement", "createElement", "classNames", "Text", "findAllByType", "filterProps", "isNumOrStr", "isNumber", "isPercent", "getPercentValue", "uniqueId", "mathSign", "polarToCartesian", "get<PERSON><PERSON><PERSON>", "props", "formatter", "label", "children", "getDeltaAngle", "startAngle", "endAngle", "sign", "deltaAngle", "Math", "min", "abs", "renderRadialLabel", "labelProps", "attrs", "position", "viewBox", "offset", "className", "_ref", "cx", "cy", "innerRadius", "outerRadius", "clockWise", "radius", "labelAngle", "direction", "startPoint", "endPoint", "path", "concat", "x", "y", "id", "dominantBaseline", "d", "xlinkHref", "getAttrsOfPolarLabel", "_ref2", "midAngle", "_polarToCartesian", "_x", "_y", "textAnchor", "verticalAnchor", "r", "_polarToCartesian2", "getAttrsOfCartesianLabel", "parentViewBox", "_ref3", "width", "height", "verticalSign", "verticalOffset", "verticalEnd", "verticalStart", "horizontalSign", "horizontalOffset", "horizontalEnd", "horizontalStart", "max", "_attrs", "_attrs2", "_attrs3", "sizeAttrs", "isPolar", "Label", "content", "_props$className", "textBreakAll", "isPolarLabel", "positionAttrs", "breakAll", "displayName", "defaultProps", "parseViewBox", "angle", "top", "left", "labelViewBox", "parseLabel", "type", "renderCallByParent", "parentProps", "checkPropsLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "index", "implicit<PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/Label.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nimport _isObject from \"lodash/isObject\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nimport React, { cloneElement, isValidElement, createElement } from 'react';\nimport classNames from 'classnames';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = function getLabel(props) {\n  var value = props.value,\n    formatter = props.formatter;\n  var label = _isNil(props.children) ? value : props.children;\n  if (_isFunction(formatter)) {\n    return formatter(label);\n  }\n  return label;\n};\nvar getDeltaAngle = function getDeltaAngle(startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = function renderRadialLabel(labelProps, label, attrs) {\n  var position = labelProps.position,\n    viewBox = labelProps.viewBox,\n    offset = labelProps.offset,\n    className = labelProps.className;\n  var _ref = viewBox,\n    cx = _ref.cx,\n    cy = _ref.cy,\n    innerRadius = _ref.innerRadius,\n    outerRadius = _ref.outerRadius,\n    startAngle = _ref.startAngle,\n    endAngle = _ref.endAngle,\n    clockWise = _ref.clockWise;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = _isNil(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: classNames('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = function getAttrsOfPolarLabel(props) {\n  var viewBox = props.viewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref2 = viewBox,\n    cx = _ref2.cx,\n    cy = _ref2.cy,\n    innerRadius = _ref2.innerRadius,\n    outerRadius = _ref2.outerRadius,\n    startAngle = _ref2.startAngle,\n    endAngle = _ref2.endAngle;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var _polarToCartesian = polarToCartesian(cx, cy, outerRadius + offset, midAngle),\n      _x = _polarToCartesian.x,\n      _y = _polarToCartesian.y;\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var _polarToCartesian2 = polarToCartesian(cx, cy, r, midAngle),\n    x = _polarToCartesian2.x,\n    y = _polarToCartesian2.y;\n  return {\n    x: x,\n    y: y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = function getAttrsOfCartesianLabel(props) {\n  var viewBox = props.viewBox,\n    parentViewBox = props.parentViewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref3 = viewBox,\n    x = _ref3.x,\n    y = _ref3.y,\n    width = _ref3.width,\n    height = _ref3.height;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width: width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width: width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height: height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height: height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width: width,\n    height: height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (_isObject(position) && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = function isPolar(viewBox) {\n  return 'cx' in viewBox && isNumber(viewBox.cx);\n};\nexport function Label(props) {\n  var viewBox = props.viewBox,\n    position = props.position,\n    value = props.value,\n    children = props.children,\n    content = props.content,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    textBreakAll = props.textBreakAll;\n  if (!viewBox || _isNil(value) && _isNil(children) && ! /*#__PURE__*/isValidElement(content) && !_isFunction(content)) {\n    return null;\n  }\n  if ( /*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (_isFunction(content)) {\n    label = /*#__PURE__*/createElement(content, props);\n    if ( /*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    className: classNames('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nLabel.defaultProps = {\n  offset: 5\n};\nvar parseViewBox = function parseViewBox(props) {\n  var cx = props.cx,\n    cy = props.cy,\n    angle = props.angle,\n    startAngle = props.startAngle,\n    endAngle = props.endAngle,\n    r = props.r,\n    radius = props.radius,\n    innerRadius = props.innerRadius,\n    outerRadius = props.outerRadius,\n    x = props.x,\n    y = props.y,\n    top = props.top,\n    left = props.left,\n    width = props.width,\n    height = props.height,\n    clockWise = props.clockWise,\n    labelViewBox = props.labelViewBox;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width: width,\n        height: height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x: x,\n      y: y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx: cx,\n      cy: cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise: clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = function parseLabel(label, viewBox) {\n  if (!label) {\n    return null;\n  }\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox\n    });\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox,\n      value: label\n    });\n  }\n  if ( /*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, {\n        key: 'label-implicit',\n        viewBox: viewBox\n      });\n    }\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (_isFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (_isObject(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      viewBox: viewBox\n    }, label, {\n      key: \"label-implicit\"\n    }));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var children = parentProps.children;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map(function (child, index) {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox);\n  return [implicitLabel].concat(_toConsumableArray(explicitChildren));\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,OAAOK,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACf,SAAS,CAACgB,QAAQ,CAACC,IAAI,CAACN,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACZ,WAAW,EAAEe,CAAC,GAAGH,CAAC,CAACZ,WAAW,CAACoB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACV,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASL,gBAAgBA,CAACgB,IAAI,EAAE;EAAE,IAAI,OAAO1B,MAAM,KAAK,WAAW,IAAI0B,IAAI,CAAC1B,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIyB,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASjB,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIe,KAAK,CAACI,OAAO,CAACnB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;AAAE;AAC1F,SAASQ,iBAAiBA,CAACR,GAAG,EAAEoB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGpB,GAAG,CAACqB,MAAM,EAAED,GAAG,GAAGpB,GAAG,CAACqB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIR,KAAK,CAACK,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGtB,GAAG,CAACsB,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGjB,MAAM,CAACiB,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIf,MAAM,CAACkB,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGnB,MAAM,CAACkB,qBAAqB,CAACH,MAAM,CAAC;IAAEC,cAAc,KAAKG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOrB,MAAM,CAACsB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACO,IAAI,CAACC,KAAK,CAACR,IAAI,EAAEE,OAAO,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACpV,SAASS,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,SAAS,CAACjB,MAAM,EAAEC,CAAC,EAAE,EAAE;IAAE,IAAIiB,MAAM,GAAG,IAAI,IAAID,SAAS,CAAChB,CAAC,CAAC,GAAGgB,SAAS,CAAChB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGE,OAAO,CAACd,MAAM,CAAC6B,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACL,MAAM,EAAEI,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAG/B,MAAM,CAACiC,yBAAyB,GAAGjC,MAAM,CAACkC,gBAAgB,CAACP,MAAM,EAAE3B,MAAM,CAACiC,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGf,OAAO,CAACd,MAAM,CAAC6B,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE/B,MAAM,CAACmC,cAAc,CAACR,MAAM,EAAEI,GAAG,EAAE/B,MAAM,CAACsB,wBAAwB,CAACO,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AACzf,SAASK,eAAeA,CAACnD,GAAG,EAAEkD,GAAG,EAAEK,KAAK,EAAE;EAAEL,GAAG,GAAGM,cAAc,CAACN,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIlD,GAAG,EAAE;IAAEmB,MAAM,CAACmC,cAAc,CAACtD,GAAG,EAAEkD,GAAG,EAAE;MAAEK,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE1D,GAAG,CAACkD,GAAG,CAAC,GAAGK,KAAK;EAAE;EAAE,OAAOvD,GAAG;AAAE;AAC3O,SAASwD,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIT,GAAG,GAAGU,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO5D,OAAO,CAACmD,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGW,MAAM,CAACX,GAAG,CAAC;AAAE;AAC5H,SAASU,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIhE,OAAO,CAAC+D,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC7D,MAAM,CAACgE,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC3C,IAAI,CAACyC,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIhE,OAAO,CAACoE,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIrD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACiD,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,SAASO,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGlD,MAAM,CAACmD,MAAM,GAAGnD,MAAM,CAACmD,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUzB,MAAM,EAAE;IAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,SAAS,CAACjB,MAAM,EAAEC,CAAC,EAAE,EAAE;MAAE,IAAIiB,MAAM,GAAGD,SAAS,CAAChB,CAAC,CAAC;MAAE,KAAK,IAAImB,GAAG,IAAIF,MAAM,EAAE;QAAE,IAAI7B,MAAM,CAACf,SAAS,CAACoE,cAAc,CAACnD,IAAI,CAAC2B,MAAM,EAAEE,GAAG,CAAC,EAAE;UAAEJ,MAAM,CAACI,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOJ,MAAM;EAAE,CAAC;EAAE,OAAOuB,QAAQ,CAACzB,KAAK,CAAC,IAAI,EAAEG,SAAS,CAAC;AAAE;AAClV,OAAO0B,KAAK,IAAIC,YAAY,EAAEC,cAAc,EAAEC,aAAa,QAAQ,OAAO;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACxG,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIlC,KAAK,GAAGkC,KAAK,CAAClC,KAAK;IACrBmC,SAAS,GAAGD,KAAK,CAACC,SAAS;EAC7B,IAAIC,KAAK,GAAGpF,MAAM,CAACkF,KAAK,CAACG,QAAQ,CAAC,GAAGrC,KAAK,GAAGkC,KAAK,CAACG,QAAQ;EAC3D,IAAItF,WAAW,CAACoF,SAAS,CAAC,EAAE;IAC1B,OAAOA,SAAS,CAACC,KAAK,CAAC;EACzB;EACA,OAAOA,KAAK;AACd,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAC/D,IAAIC,IAAI,GAAGV,QAAQ,CAACS,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAIG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAOE,IAAI,GAAGC,UAAU;AAC1B,CAAC;AACD,IAAII,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,UAAU,EAAEX,KAAK,EAAEY,KAAK,EAAE;EAC3E,IAAIC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;IAChCC,OAAO,GAAGH,UAAU,CAACG,OAAO;IAC5BC,MAAM,GAAGJ,UAAU,CAACI,MAAM;IAC1BC,SAAS,GAAGL,UAAU,CAACK,SAAS;EAClC,IAAIC,IAAI,GAAGH,OAAO;IAChBI,EAAE,GAAGD,IAAI,CAACC,EAAE;IACZC,EAAE,GAAGF,IAAI,CAACE,EAAE;IACZC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAC9BC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BlB,UAAU,GAAGc,IAAI,CAACd,UAAU;IAC5BC,QAAQ,GAAGa,IAAI,CAACb,QAAQ;IACxBkB,SAAS,GAAGL,IAAI,CAACK,SAAS;EAC5B,IAAIC,MAAM,GAAG,CAACH,WAAW,GAAGC,WAAW,IAAI,CAAC;EAC5C,IAAIf,UAAU,GAAGJ,aAAa,CAACC,UAAU,EAAEC,QAAQ,CAAC;EACpD,IAAIC,IAAI,GAAGC,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC,IAAIkB,UAAU,EAAEC,SAAS;EACzB,IAAIZ,QAAQ,KAAK,aAAa,EAAE;IAC9BW,UAAU,GAAGrB,UAAU,GAAGE,IAAI,GAAGU,MAAM;IACvCU,SAAS,GAAGH,SAAS;EACvB,CAAC,MAAM,IAAIT,QAAQ,KAAK,WAAW,EAAE;IACnCW,UAAU,GAAGpB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCU,SAAS,GAAG,CAACH,SAAS;EACxB,CAAC,MAAM,IAAIT,QAAQ,KAAK,KAAK,EAAE;IAC7BW,UAAU,GAAGpB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCU,SAAS,GAAGH,SAAS;EACvB;EACAG,SAAS,GAAGnB,UAAU,IAAI,CAAC,GAAGmB,SAAS,GAAG,CAACA,SAAS;EACpD,IAAIC,UAAU,GAAG9B,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,CAAC;EAC7D,IAAIG,QAAQ,GAAG/B,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,GAAG,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;EACxF,IAAIG,IAAI,GAAG,GAAG,CAACC,MAAM,CAACH,UAAU,CAACI,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACH,UAAU,CAACK,CAAC,EAAE,SAAS,CAAC,CAACF,MAAM,CAACN,MAAM,EAAE,GAAG,CAAC,CAACM,MAAM,CAACN,MAAM,EAAE,OAAO,CAAC,CAACM,MAAM,CAACJ,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAACI,MAAM,CAACF,QAAQ,CAACG,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACF,QAAQ,CAACI,CAAC,CAAC;EACpM,IAAIC,EAAE,GAAGpH,MAAM,CAAC+F,UAAU,CAACqB,EAAE,CAAC,GAAGtC,QAAQ,CAAC,uBAAuB,CAAC,GAAGiB,UAAU,CAACqB,EAAE;EAClF,OAAO,aAAalD,KAAK,CAACG,aAAa,CAAC,MAAM,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;IAClEqB,gBAAgB,EAAE,SAAS;IAC3BjB,SAAS,EAAE9B,UAAU,CAAC,2BAA2B,EAAE8B,SAAS;EAC9D,CAAC,CAAC,EAAE,aAAalC,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1F+C,EAAE,EAAEA,EAAE;IACNE,CAAC,EAAEN;EACL,CAAC,CAAC,CAAC,EAAE,aAAa9C,KAAK,CAACG,aAAa,CAAC,UAAU,EAAE;IAChDkD,SAAS,EAAE,GAAG,CAACN,MAAM,CAACG,EAAE;EAC1B,CAAC,EAAEhC,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,IAAIoC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtC,KAAK,EAAE;EAC9D,IAAIgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBF,QAAQ,GAAGf,KAAK,CAACe,QAAQ;EAC3B,IAAIwB,KAAK,GAAGvB,OAAO;IACjBI,EAAE,GAAGmB,KAAK,CAACnB,EAAE;IACbC,EAAE,GAAGkB,KAAK,CAAClB,EAAE;IACbC,WAAW,GAAGiB,KAAK,CAACjB,WAAW;IAC/BC,WAAW,GAAGgB,KAAK,CAAChB,WAAW;IAC/BlB,UAAU,GAAGkC,KAAK,CAAClC,UAAU;IAC7BC,QAAQ,GAAGiC,KAAK,CAACjC,QAAQ;EAC3B,IAAIkC,QAAQ,GAAG,CAACnC,UAAU,GAAGC,QAAQ,IAAI,CAAC;EAC1C,IAAIS,QAAQ,KAAK,SAAS,EAAE;IAC1B,IAAI0B,iBAAiB,GAAG3C,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEE,WAAW,GAAGN,MAAM,EAAEuB,QAAQ,CAAC;MAC9EE,EAAE,GAAGD,iBAAiB,CAACT,CAAC;MACxBW,EAAE,GAAGF,iBAAiB,CAACR,CAAC;IAC1B,OAAO;MACLD,CAAC,EAAEU,EAAE;MACLT,CAAC,EAAEU,EAAE;MACLC,UAAU,EAAEF,EAAE,IAAItB,EAAE,GAAG,OAAO,GAAG,KAAK;MACtCyB,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,QAAQ,EAAE;IACzB,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAIC,CAAC,GAAG,CAACxB,WAAW,GAAGC,WAAW,IAAI,CAAC;EACvC,IAAIwB,kBAAkB,GAAGjD,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEyB,CAAC,EAAEN,QAAQ,CAAC;IAC5DR,CAAC,GAAGe,kBAAkB,CAACf,CAAC;IACxBC,CAAC,GAAGc,kBAAkB,CAACd,CAAC;EAC1B,OAAO;IACLD,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJW,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;AACH,CAAC;AACD,IAAIG,wBAAwB,GAAG,SAASA,wBAAwBA,CAAChD,KAAK,EAAE;EACtE,IAAIgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBiC,aAAa,GAAGjD,KAAK,CAACiD,aAAa;IACnChC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBF,QAAQ,GAAGf,KAAK,CAACe,QAAQ;EAC3B,IAAImC,KAAK,GAAGlC,OAAO;IACjBgB,CAAC,GAAGkB,KAAK,CAAClB,CAAC;IACXC,CAAC,GAAGiB,KAAK,CAACjB,CAAC;IACXkB,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,MAAM,GAAGF,KAAK,CAACE,MAAM;;EAEvB;EACA,IAAIC,YAAY,GAAGD,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,IAAIE,cAAc,GAAGD,YAAY,GAAGpC,MAAM;EAC1C,IAAIsC,WAAW,GAAGF,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACpD,IAAIG,aAAa,GAAGH,YAAY,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;;EAEtD;EACA,IAAII,cAAc,GAAGN,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC,IAAIO,gBAAgB,GAAGD,cAAc,GAAGxC,MAAM;EAC9C,IAAI0C,aAAa,GAAGF,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACxD,IAAIG,eAAe,GAAGH,cAAc,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;EAC1D,IAAI1C,QAAQ,KAAK,KAAK,EAAE;IACtB,IAAID,KAAK,GAAG;MACVkB,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAG,CAAC;MAChBlB,CAAC,EAAEA,CAAC,GAAGoB,YAAY,GAAGpC,MAAM;MAC5B2B,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEU;IAClB,CAAC;IACD,OAAOnG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,KAAK,CAAC,EAAEmC,aAAa,GAAG;MAC7DG,MAAM,EAAE3C,IAAI,CAACoD,GAAG,CAAC5B,CAAC,GAAGgB,aAAa,CAAChB,CAAC,EAAE,CAAC,CAAC;MACxCkB,KAAK,EAAEA;IACT,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIpC,QAAQ,KAAK,QAAQ,EAAE;IACzB,IAAI+C,MAAM,GAAG;MACX9B,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAG,CAAC;MAChBlB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAGE,cAAc;MAC9BV,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEW;IAClB,CAAC;IACD,OAAOpG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0G,MAAM,CAAC,EAAEb,aAAa,GAAG;MAC9DG,MAAM,EAAE3C,IAAI,CAACoD,GAAG,CAACZ,aAAa,CAAChB,CAAC,GAAGgB,aAAa,CAACG,MAAM,IAAInB,CAAC,GAAGmB,MAAM,CAAC,EAAE,CAAC,CAAC;MAC1ED,KAAK,EAAEA;IACT,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIpC,QAAQ,KAAK,MAAM,EAAE;IACvB,IAAIgD,OAAO,GAAG;MACZ/B,CAAC,EAAEA,CAAC,GAAG0B,gBAAgB;MACvBzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAG,CAAC;MACjBR,UAAU,EAAEe,aAAa;MACzBd,cAAc,EAAE;IAClB,CAAC;IACD,OAAOzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2G,OAAO,CAAC,EAAEd,aAAa,GAAG;MAC/DE,KAAK,EAAE1C,IAAI,CAACoD,GAAG,CAACE,OAAO,CAAC/B,CAAC,GAAGiB,aAAa,CAACjB,CAAC,EAAE,CAAC,CAAC;MAC/CoB,MAAM,EAAEA;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIrC,QAAQ,KAAK,OAAO,EAAE;IACxB,IAAIiD,OAAO,GAAG;MACZhC,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAGO,gBAAgB;MAC/BzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAG,CAAC;MACjBR,UAAU,EAAEgB,eAAe;MAC3Bf,cAAc,EAAE;IAClB,CAAC;IACD,OAAOzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,OAAO,CAAC,EAAEf,aAAa,GAAG;MAC/DE,KAAK,EAAE1C,IAAI,CAACoD,GAAG,CAACZ,aAAa,CAACjB,CAAC,GAAGiB,aAAa,CAACE,KAAK,GAAGa,OAAO,CAAChC,CAAC,EAAE,CAAC,CAAC;MACrEoB,MAAM,EAAEA;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIa,SAAS,GAAGhB,aAAa,GAAG;IAC9BE,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC,GAAG,CAAC,CAAC;EACN,IAAIrC,QAAQ,KAAK,YAAY,EAAE;IAC7B,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAG0B,gBAAgB;MACvBzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAG,CAAC;MACjBR,UAAU,EAAEgB,eAAe;MAC3Bf,cAAc,EAAE;IAClB,CAAC,EAAEoB,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,aAAa,EAAE;IAC9B,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAGO,gBAAgB;MAC/BzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAG,CAAC;MACjBR,UAAU,EAAEe,aAAa;MACzBd,cAAc,EAAE;IAClB,CAAC,EAAEoB,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAG,CAAC;MAChBlB,CAAC,EAAEA,CAAC,GAAGqB,cAAc;MACrBV,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEW;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAG,CAAC;MAChBlB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAGE,cAAc;MAC9BV,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEU;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,eAAe,EAAE;IAChC,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAG0B,gBAAgB;MACvBzB,CAAC,EAAEA,CAAC,GAAGqB,cAAc;MACrBV,UAAU,EAAEgB,eAAe;MAC3Bf,cAAc,EAAEW;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,gBAAgB,EAAE;IACjC,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAGO,gBAAgB;MAC/BzB,CAAC,EAAEA,CAAC,GAAGqB,cAAc;MACrBV,UAAU,EAAEe,aAAa;MACzBd,cAAc,EAAEW;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,kBAAkB,EAAE;IACnC,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAG0B,gBAAgB;MACvBzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAGE,cAAc;MAC9BV,UAAU,EAAEgB,eAAe;MAC3Bf,cAAc,EAAEU;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAIlD,QAAQ,KAAK,mBAAmB,EAAE;IACpC,OAAO3D,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAGO,gBAAgB;MAC/BzB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAGE,cAAc;MAC9BV,UAAU,EAAEe,aAAa;MACzBd,cAAc,EAAEU;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAIrJ,SAAS,CAACmG,QAAQ,CAAC,KAAKtB,QAAQ,CAACsB,QAAQ,CAACiB,CAAC,CAAC,IAAItC,SAAS,CAACqB,QAAQ,CAACiB,CAAC,CAAC,CAAC,KAAKvC,QAAQ,CAACsB,QAAQ,CAACkB,CAAC,CAAC,IAAIvC,SAAS,CAACqB,QAAQ,CAACkB,CAAC,CAAC,CAAC,EAAE;IAC7H,OAAO7E,aAAa,CAAC;MACnB4E,CAAC,EAAEA,CAAC,GAAGrC,eAAe,CAACoB,QAAQ,CAACiB,CAAC,EAAEmB,KAAK,CAAC;MACzClB,CAAC,EAAEA,CAAC,GAAGtC,eAAe,CAACoB,QAAQ,CAACkB,CAAC,EAAEmB,MAAM,CAAC;MAC1CR,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE;IAClB,CAAC,EAAEoB,SAAS,CAAC;EACf;EACA,OAAO7G,aAAa,CAAC;IACnB4E,CAAC,EAAEA,CAAC,GAAGmB,KAAK,GAAG,CAAC;IAChBlB,CAAC,EAAEA,CAAC,GAAGmB,MAAM,GAAG,CAAC;IACjBR,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC,EAAEoB,SAAS,CAAC;AACf,CAAC;AACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAClD,OAAO,EAAE;EACtC,OAAO,IAAI,IAAIA,OAAO,IAAIvB,QAAQ,CAACuB,OAAO,CAACI,EAAE,CAAC;AAChD,CAAC;AACD,OAAO,SAAS+C,KAAKA,CAACnE,KAAK,EAAE;EAC3B,IAAIgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBD,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBjD,KAAK,GAAGkC,KAAK,CAAClC,KAAK;IACnBqC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBiE,OAAO,GAAGpE,KAAK,CAACoE,OAAO;IACvBC,gBAAgB,GAAGrE,KAAK,CAACkB,SAAS;IAClCA,SAAS,GAAGmD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DC,YAAY,GAAGtE,KAAK,CAACsE,YAAY;EACnC,IAAI,CAACtD,OAAO,IAAIlG,MAAM,CAACgD,KAAK,CAAC,IAAIhD,MAAM,CAACqF,QAAQ,CAAC,IAAI,EAAE,aAAajB,cAAc,CAACkF,OAAO,CAAC,IAAI,CAACvJ,WAAW,CAACuJ,OAAO,CAAC,EAAE;IACpH,OAAO,IAAI;EACb;EACA,IAAK,aAAalF,cAAc,CAACkF,OAAO,CAAC,EAAE;IACzC,OAAO,aAAanF,YAAY,CAACmF,OAAO,EAAEpE,KAAK,CAAC;EAClD;EACA,IAAIE,KAAK;EACT,IAAIrF,WAAW,CAACuJ,OAAO,CAAC,EAAE;IACxBlE,KAAK,GAAG,aAAaf,aAAa,CAACiF,OAAO,EAAEpE,KAAK,CAAC;IAClD,IAAK,aAAad,cAAc,CAACgB,KAAK,CAAC,EAAE;MACvC,OAAOA,KAAK;IACd;EACF,CAAC,MAAM;IACLA,KAAK,GAAGH,QAAQ,CAACC,KAAK,CAAC;EACzB;EACA,IAAIuE,YAAY,GAAGL,OAAO,CAAClD,OAAO,CAAC;EACnC,IAAIF,KAAK,GAAGvB,WAAW,CAACS,KAAK,EAAE,IAAI,CAAC;EACpC,IAAIuE,YAAY,KAAKxD,QAAQ,KAAK,aAAa,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAClG,OAAOH,iBAAiB,CAACZ,KAAK,EAAEE,KAAK,EAAEY,KAAK,CAAC;EAC/C;EACA,IAAI0D,aAAa,GAAGD,YAAY,GAAGjC,oBAAoB,CAACtC,KAAK,CAAC,GAAGgD,wBAAwB,CAAChD,KAAK,CAAC;EAChG,OAAO,aAAahB,KAAK,CAACG,aAAa,CAACE,IAAI,EAAET,QAAQ,CAAC;IACrDsC,SAAS,EAAE9B,UAAU,CAAC,gBAAgB,EAAE8B,SAAS;EACnD,CAAC,EAAEJ,KAAK,EAAE0D,aAAa,EAAE;IACvBC,QAAQ,EAAEH;EACZ,CAAC,CAAC,EAAEpE,KAAK,CAAC;AACZ;AACAiE,KAAK,CAACO,WAAW,GAAG,OAAO;AAC3BP,KAAK,CAACQ,YAAY,GAAG;EACnB1D,MAAM,EAAE;AACV,CAAC;AACD,IAAI2D,YAAY,GAAG,SAASA,YAAYA,CAAC5E,KAAK,EAAE;EAC9C,IAAIoB,EAAE,GAAGpB,KAAK,CAACoB,EAAE;IACfC,EAAE,GAAGrB,KAAK,CAACqB,EAAE;IACbwD,KAAK,GAAG7E,KAAK,CAAC6E,KAAK;IACnBxE,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBwC,CAAC,GAAG9C,KAAK,CAAC8C,CAAC;IACXrB,MAAM,GAAGzB,KAAK,CAACyB,MAAM;IACrBH,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;IAC/BS,CAAC,GAAGhC,KAAK,CAACgC,CAAC;IACXC,CAAC,GAAGjC,KAAK,CAACiC,CAAC;IACX6C,GAAG,GAAG9E,KAAK,CAAC8E,GAAG;IACfC,IAAI,GAAG/E,KAAK,CAAC+E,IAAI;IACjB5B,KAAK,GAAGnD,KAAK,CAACmD,KAAK;IACnBC,MAAM,GAAGpD,KAAK,CAACoD,MAAM;IACrB5B,SAAS,GAAGxB,KAAK,CAACwB,SAAS;IAC3BwD,YAAY,GAAGhF,KAAK,CAACgF,YAAY;EACnC,IAAIA,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAIvF,QAAQ,CAAC0D,KAAK,CAAC,IAAI1D,QAAQ,CAAC2D,MAAM,CAAC,EAAE;IACvC,IAAI3D,QAAQ,CAACuC,CAAC,CAAC,IAAIvC,QAAQ,CAACwC,CAAC,CAAC,EAAE;MAC9B,OAAO;QACLD,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJkB,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;IACH;IACA,IAAI3D,QAAQ,CAACqF,GAAG,CAAC,IAAIrF,QAAQ,CAACsF,IAAI,CAAC,EAAE;MACnC,OAAO;QACL/C,CAAC,EAAE8C,GAAG;QACN7C,CAAC,EAAE8C,IAAI;QACP5B,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;IACH;EACF;EACA,IAAI3D,QAAQ,CAACuC,CAAC,CAAC,IAAIvC,QAAQ,CAACwC,CAAC,CAAC,EAAE;IAC9B,OAAO;MACLD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJkB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAI3D,QAAQ,CAAC2B,EAAE,CAAC,IAAI3B,QAAQ,CAAC4B,EAAE,CAAC,EAAE;IAChC,OAAO;MACLD,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNhB,UAAU,EAAEA,UAAU,IAAIwE,KAAK,IAAI,CAAC;MACpCvE,QAAQ,EAAEA,QAAQ,IAAIuE,KAAK,IAAI,CAAC;MAChCvD,WAAW,EAAEA,WAAW,IAAI,CAAC;MAC7BC,WAAW,EAAEA,WAAW,IAAIE,MAAM,IAAIqB,CAAC,IAAI,CAAC;MAC5CtB,SAAS,EAAEA;IACb,CAAC;EACH;EACA,IAAIxB,KAAK,CAACgB,OAAO,EAAE;IACjB,OAAOhB,KAAK,CAACgB,OAAO;EACtB;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AACD,IAAIiE,UAAU,GAAG,SAASA,UAAUA,CAAC/E,KAAK,EAAEc,OAAO,EAAE;EACnD,IAAI,CAACd,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,aAAalB,KAAK,CAACG,aAAa,CAACgF,KAAK,EAAE;MAC7C1G,GAAG,EAAE,gBAAgB;MACrBuD,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAIxB,UAAU,CAACU,KAAK,CAAC,EAAE;IACrB,OAAO,aAAalB,KAAK,CAACG,aAAa,CAACgF,KAAK,EAAE;MAC7C1G,GAAG,EAAE,gBAAgB;MACrBuD,OAAO,EAAEA,OAAO;MAChBlD,KAAK,EAAEoC;IACT,CAAC,CAAC;EACJ;EACA,IAAK,aAAahB,cAAc,CAACgB,KAAK,CAAC,EAAE;IACvC,IAAIA,KAAK,CAACgF,IAAI,KAAKf,KAAK,EAAE;MACxB,OAAO,aAAalF,YAAY,CAACiB,KAAK,EAAE;QACtCzC,GAAG,EAAE,gBAAgB;QACrBuD,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;IACA,OAAO,aAAahC,KAAK,CAACG,aAAa,CAACgF,KAAK,EAAE;MAC7C1G,GAAG,EAAE,gBAAgB;MACrB2G,OAAO,EAAElE,KAAK;MACdc,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAInG,WAAW,CAACqF,KAAK,CAAC,EAAE;IACtB,OAAO,aAAalB,KAAK,CAACG,aAAa,CAACgF,KAAK,EAAE;MAC7C1G,GAAG,EAAE,gBAAgB;MACrB2G,OAAO,EAAElE,KAAK;MACdc,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAIpG,SAAS,CAACsF,KAAK,CAAC,EAAE;IACpB,OAAO,aAAalB,KAAK,CAACG,aAAa,CAACgF,KAAK,EAAEvF,QAAQ,CAAC;MACtDoC,OAAO,EAAEA;IACX,CAAC,EAAEd,KAAK,EAAE;MACRzC,GAAG,EAAE;IACP,CAAC,CAAC,CAAC;EACL;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAI0H,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,WAAW,EAAEpE,OAAO,EAAE;EACzE,IAAIqE,eAAe,GAAG/H,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKmB,SAAS,GAAGnB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9F,IAAI,CAAC8H,WAAW,IAAI,CAACA,WAAW,CAACjF,QAAQ,IAAIkF,eAAe,IAAI,CAACD,WAAW,CAAClF,KAAK,EAAE;IAClF,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,GAAGiF,WAAW,CAACjF,QAAQ;EACnC,IAAI8C,aAAa,GAAG2B,YAAY,CAACQ,WAAW,CAAC;EAC7C,IAAIE,gBAAgB,GAAGhG,aAAa,CAACa,QAAQ,EAAEgE,KAAK,CAAC,CAACoB,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAChF,OAAO,aAAaxG,YAAY,CAACuG,KAAK,EAAE;MACtCxE,OAAO,EAAEA,OAAO,IAAIiC,aAAa;MACjC;MACAxF,GAAG,EAAE,QAAQ,CAACsE,MAAM,CAAC0D,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACJ,eAAe,EAAE;IACpB,OAAOC,gBAAgB;EACzB;EACA,IAAII,aAAa,GAAGT,UAAU,CAACG,WAAW,CAAClF,KAAK,EAAEc,OAAO,IAAIiC,aAAa,CAAC;EAC3E,OAAO,CAACyC,aAAa,CAAC,CAAC3D,MAAM,CAAChH,kBAAkB,CAACuK,gBAAgB,CAAC,CAAC;AACrE,CAAC;AACDnB,KAAK,CAACS,YAAY,GAAGA,YAAY;AACjCT,KAAK,CAACgB,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}