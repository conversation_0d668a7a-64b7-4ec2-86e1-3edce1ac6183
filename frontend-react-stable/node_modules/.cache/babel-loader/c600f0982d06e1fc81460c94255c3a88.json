{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useCallback } from 'react';\nimport warning from \"rc-util/es/warning\";\nimport { nextSlice } from '../utils/timeUtil';\nvar PATH_SPLIT = '__RC_UTIL_PATH_SPLIT__';\nvar getPathStr = function getPathStr(keyPath) {\n  return keyPath.join(PATH_SPLIT);\n};\nvar getPathKeys = function getPathKeys(keyPathStr) {\n  return keyPathStr.split(PATH_SPLIT);\n};\nexport var OVERFLOW_KEY = 'rc-menu-more';\nexport default function useKeyRecords() {\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    internalForceUpdate = _React$useState2[1];\n  var key2pathRef = useRef(new Map());\n  var path2keyRef = useRef(new Map());\n  var _React$useState3 = React.useState([]),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    overflowKeys = _React$useState4[0],\n    setOverflowKeys = _React$useState4[1];\n  var updateRef = useRef(0);\n  var destroyRef = useRef(false);\n  var forceUpdate = function forceUpdate() {\n    if (!destroyRef.current) {\n      internalForceUpdate({});\n    }\n  };\n  var registerPath = useCallback(function (key, keyPath) {\n    // Warning for invalidate or duplicated `key`\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!key2pathRef.current.has(key), \"Duplicated key '\".concat(key, \"' used in Menu by path [\").concat(keyPath.join(' > '), \"]\"));\n    } // Fill map\n\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.set(connectedPath, key);\n    key2pathRef.current.set(key, connectedPath);\n    updateRef.current += 1;\n    var id = updateRef.current;\n    nextSlice(function () {\n      if (id === updateRef.current) {\n        forceUpdate();\n      }\n    });\n  }, []);\n  var unregisterPath = useCallback(function (key, keyPath) {\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.delete(connectedPath);\n    key2pathRef.current.delete(key);\n  }, []);\n  var refreshOverflowKeys = useCallback(function (keys) {\n    setOverflowKeys(keys);\n  }, []);\n  var getKeyPath = useCallback(function (eventKey, includeOverflow) {\n    var fullPath = key2pathRef.current.get(eventKey) || '';\n    var keys = getPathKeys(fullPath);\n    if (includeOverflow && overflowKeys.includes(keys[0])) {\n      keys.unshift(OVERFLOW_KEY);\n    }\n    return keys;\n  }, [overflowKeys]);\n  var isSubPathKey = useCallback(function (pathKeys, eventKey) {\n    return pathKeys.some(function (pathKey) {\n      var pathKeyList = getKeyPath(pathKey, true);\n      return pathKeyList.includes(eventKey);\n    });\n  }, [getKeyPath]);\n  var getKeys = function getKeys() {\n    var keys = _toConsumableArray(key2pathRef.current.keys());\n    if (overflowKeys.length) {\n      keys.push(OVERFLOW_KEY);\n    }\n    return keys;\n  };\n  /**\n   * Find current key related child path keys\n   */\n\n  var getSubPathKeys = useCallback(function (key) {\n    var connectedPath = \"\".concat(key2pathRef.current.get(key)).concat(PATH_SPLIT);\n    var pathKeys = new Set();\n    _toConsumableArray(path2keyRef.current.keys()).forEach(function (pathKey) {\n      if (pathKey.startsWith(connectedPath)) {\n        pathKeys.add(path2keyRef.current.get(pathKey));\n      }\n    });\n    return pathKeys;\n  }, []);\n  React.useEffect(function () {\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  return {\n    // Register\n    registerPath: registerPath,\n    unregisterPath: unregisterPath,\n    refreshOverflowKeys: refreshOverflowKeys,\n    // Util\n    isSubPathKey: isSubPathKey,\n    getKeyPath: getKeyPath,\n    getKeys: getKeys,\n    getSubPathKeys: getSubPathKeys\n  };\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "useRef", "useCallback", "warning", "nextSlice", "PATH_SPLIT", "getPathStr", "keyP<PERSON>", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyPathStr", "split", "OVERFLOW_KEY", "useKeyRecords", "_React$useState", "useState", "_React$useState2", "internalForceUpdate", "key2pathRef", "Map", "path2keyRef", "_React$useState3", "_React$useState4", "overflowKeys", "setOverflowKeys", "updateRef", "destroyRef", "forceUpdate", "current", "registerPath", "key", "process", "env", "NODE_ENV", "has", "concat", "connectedPath", "set", "id", "unregisterPath", "delete", "refreshOverflowKeys", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventKey", "includeOverflow", "fullPath", "get", "includes", "unshift", "isSubPath<PERSON>ey", "pathKeys", "some", "path<PERSON><PERSON>", "pathKeyList", "get<PERSON><PERSON><PERSON>", "length", "push", "getSub<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "for<PERSON>ach", "startsWith", "add", "useEffect"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/hooks/useKeyRecords.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useCallback } from 'react';\nimport warning from \"rc-util/es/warning\";\nimport { nextSlice } from '../utils/timeUtil';\nvar PATH_SPLIT = '__RC_UTIL_PATH_SPLIT__';\n\nvar getPathStr = function getPathStr(keyPath) {\n  return keyPath.join(PATH_SPLIT);\n};\n\nvar getPathKeys = function getPathKeys(keyPathStr) {\n  return keyPathStr.split(PATH_SPLIT);\n};\n\nexport var OVERFLOW_KEY = 'rc-menu-more';\nexport default function useKeyRecords() {\n  var _React$useState = React.useState({}),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      internalForceUpdate = _React$useState2[1];\n\n  var key2pathRef = useRef(new Map());\n  var path2keyRef = useRef(new Map());\n\n  var _React$useState3 = React.useState([]),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      overflowKeys = _React$useState4[0],\n      setOverflowKeys = _React$useState4[1];\n\n  var updateRef = useRef(0);\n  var destroyRef = useRef(false);\n\n  var forceUpdate = function forceUpdate() {\n    if (!destroyRef.current) {\n      internalForceUpdate({});\n    }\n  };\n\n  var registerPath = useCallback(function (key, keyPath) {\n    // Warning for invalidate or duplicated `key`\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!key2pathRef.current.has(key), \"Duplicated key '\".concat(key, \"' used in Menu by path [\").concat(keyPath.join(' > '), \"]\"));\n    } // Fill map\n\n\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.set(connectedPath, key);\n    key2pathRef.current.set(key, connectedPath);\n    updateRef.current += 1;\n    var id = updateRef.current;\n    nextSlice(function () {\n      if (id === updateRef.current) {\n        forceUpdate();\n      }\n    });\n  }, []);\n  var unregisterPath = useCallback(function (key, keyPath) {\n    var connectedPath = getPathStr(keyPath);\n    path2keyRef.current.delete(connectedPath);\n    key2pathRef.current.delete(key);\n  }, []);\n  var refreshOverflowKeys = useCallback(function (keys) {\n    setOverflowKeys(keys);\n  }, []);\n  var getKeyPath = useCallback(function (eventKey, includeOverflow) {\n    var fullPath = key2pathRef.current.get(eventKey) || '';\n    var keys = getPathKeys(fullPath);\n\n    if (includeOverflow && overflowKeys.includes(keys[0])) {\n      keys.unshift(OVERFLOW_KEY);\n    }\n\n    return keys;\n  }, [overflowKeys]);\n  var isSubPathKey = useCallback(function (pathKeys, eventKey) {\n    return pathKeys.some(function (pathKey) {\n      var pathKeyList = getKeyPath(pathKey, true);\n      return pathKeyList.includes(eventKey);\n    });\n  }, [getKeyPath]);\n\n  var getKeys = function getKeys() {\n    var keys = _toConsumableArray(key2pathRef.current.keys());\n\n    if (overflowKeys.length) {\n      keys.push(OVERFLOW_KEY);\n    }\n\n    return keys;\n  };\n  /**\n   * Find current key related child path keys\n   */\n\n\n  var getSubPathKeys = useCallback(function (key) {\n    var connectedPath = \"\".concat(key2pathRef.current.get(key)).concat(PATH_SPLIT);\n    var pathKeys = new Set();\n\n    _toConsumableArray(path2keyRef.current.keys()).forEach(function (pathKey) {\n      if (pathKey.startsWith(connectedPath)) {\n        pathKeys.add(path2keyRef.current.get(pathKey));\n      }\n    });\n\n    return pathKeys;\n  }, []);\n  React.useEffect(function () {\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  return {\n    // Register\n    registerPath: registerPath,\n    unregisterPath: unregisterPath,\n    refreshOverflowKeys: refreshOverflowKeys,\n    // Util\n    isSubPathKey: isSubPathKey,\n    getKeyPath: getKeyPath,\n    getKeys: getKeys,\n    getSubPathKeys: getSubPathKeys\n  };\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,IAAIC,UAAU,GAAG,wBAAwB;AAEzC,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,OAAO,EAAE;EAC5C,OAAOA,OAAO,CAACC,IAAI,CAACH,UAAU,CAAC;AACjC,CAAC;AAED,IAAII,WAAW,GAAG,SAASA,WAAWA,CAACC,UAAU,EAAE;EACjD,OAAOA,UAAU,CAACC,KAAK,CAACN,UAAU,CAAC;AACrC,CAAC;AAED,OAAO,IAAIO,YAAY,GAAG,cAAc;AACxC,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,IAAIC,eAAe,GAAGd,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCC,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,mBAAmB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAE7C,IAAIE,WAAW,GAAGjB,MAAM,CAAC,IAAIkB,GAAG,CAAC,CAAC,CAAC;EACnC,IAAIC,WAAW,GAAGnB,MAAM,CAAC,IAAIkB,GAAG,CAAC,CAAC,CAAC;EAEnC,IAAIE,gBAAgB,GAAGrB,KAAK,CAACe,QAAQ,CAAC,EAAE,CAAC;IACrCO,gBAAgB,GAAGvB,cAAc,CAACsB,gBAAgB,EAAE,CAAC,CAAC;IACtDE,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,IAAIG,SAAS,GAAGxB,MAAM,CAAC,CAAC,CAAC;EACzB,IAAIyB,UAAU,GAAGzB,MAAM,CAAC,KAAK,CAAC;EAE9B,IAAI0B,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAI,CAACD,UAAU,CAACE,OAAO,EAAE;MACvBX,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,IAAIY,YAAY,GAAG3B,WAAW,CAAC,UAAU4B,GAAG,EAAEvB,OAAO,EAAE;IACrD;IACA,IAAIwB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC9B,OAAO,CAAC,CAACe,WAAW,CAACU,OAAO,CAACM,GAAG,CAACJ,GAAG,CAAC,EAAE,kBAAkB,CAACK,MAAM,CAACL,GAAG,EAAE,0BAA0B,CAAC,CAACK,MAAM,CAAC5B,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;IACrI,CAAC,CAAC;;IAGF,IAAI4B,aAAa,GAAG9B,UAAU,CAACC,OAAO,CAAC;IACvCa,WAAW,CAACQ,OAAO,CAACS,GAAG,CAACD,aAAa,EAAEN,GAAG,CAAC;IAC3CZ,WAAW,CAACU,OAAO,CAACS,GAAG,CAACP,GAAG,EAAEM,aAAa,CAAC;IAC3CX,SAAS,CAACG,OAAO,IAAI,CAAC;IACtB,IAAIU,EAAE,GAAGb,SAAS,CAACG,OAAO;IAC1BxB,SAAS,CAAC,YAAY;MACpB,IAAIkC,EAAE,KAAKb,SAAS,CAACG,OAAO,EAAE;QAC5BD,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIY,cAAc,GAAGrC,WAAW,CAAC,UAAU4B,GAAG,EAAEvB,OAAO,EAAE;IACvD,IAAI6B,aAAa,GAAG9B,UAAU,CAACC,OAAO,CAAC;IACvCa,WAAW,CAACQ,OAAO,CAACY,MAAM,CAACJ,aAAa,CAAC;IACzClB,WAAW,CAACU,OAAO,CAACY,MAAM,CAACV,GAAG,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;EACN,IAAIW,mBAAmB,GAAGvC,WAAW,CAAC,UAAUwC,IAAI,EAAE;IACpDlB,eAAe,CAACkB,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,UAAU,GAAGzC,WAAW,CAAC,UAAU0C,QAAQ,EAAEC,eAAe,EAAE;IAChE,IAAIC,QAAQ,GAAG5B,WAAW,CAACU,OAAO,CAACmB,GAAG,CAACH,QAAQ,CAAC,IAAI,EAAE;IACtD,IAAIF,IAAI,GAAGjC,WAAW,CAACqC,QAAQ,CAAC;IAEhC,IAAID,eAAe,IAAItB,YAAY,CAACyB,QAAQ,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACrDA,IAAI,CAACO,OAAO,CAACrC,YAAY,CAAC;IAC5B;IAEA,OAAO8B,IAAI;EACb,CAAC,EAAE,CAACnB,YAAY,CAAC,CAAC;EAClB,IAAI2B,YAAY,GAAGhD,WAAW,CAAC,UAAUiD,QAAQ,EAAEP,QAAQ,EAAE;IAC3D,OAAOO,QAAQ,CAACC,IAAI,CAAC,UAAUC,OAAO,EAAE;MACtC,IAAIC,WAAW,GAAGX,UAAU,CAACU,OAAO,EAAE,IAAI,CAAC;MAC3C,OAAOC,WAAW,CAACN,QAAQ,CAACJ,QAAQ,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;EAEhB,IAAIY,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAIb,IAAI,GAAG5C,kBAAkB,CAACoB,WAAW,CAACU,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC;IAEzD,IAAInB,YAAY,CAACiC,MAAM,EAAE;MACvBd,IAAI,CAACe,IAAI,CAAC7C,YAAY,CAAC;IACzB;IAEA,OAAO8B,IAAI;EACb,CAAC;EACD;AACF;AACA;;EAGE,IAAIgB,cAAc,GAAGxD,WAAW,CAAC,UAAU4B,GAAG,EAAE;IAC9C,IAAIM,aAAa,GAAG,EAAE,CAACD,MAAM,CAACjB,WAAW,CAACU,OAAO,CAACmB,GAAG,CAACjB,GAAG,CAAC,CAAC,CAACK,MAAM,CAAC9B,UAAU,CAAC;IAC9E,IAAI8C,QAAQ,GAAG,IAAIQ,GAAG,CAAC,CAAC;IAExB7D,kBAAkB,CAACsB,WAAW,CAACQ,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC,CAACkB,OAAO,CAAC,UAAUP,OAAO,EAAE;MACxE,IAAIA,OAAO,CAACQ,UAAU,CAACzB,aAAa,CAAC,EAAE;QACrCe,QAAQ,CAACW,GAAG,CAAC1C,WAAW,CAACQ,OAAO,CAACmB,GAAG,CAACM,OAAO,CAAC,CAAC;MAChD;IACF,CAAC,CAAC;IAEF,OAAOF,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC;EACNnD,KAAK,CAAC+D,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBrC,UAAU,CAACE,OAAO,GAAG,IAAI;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACL;IACAC,YAAY,EAAEA,YAAY;IAC1BU,cAAc,EAAEA,cAAc;IAC9BE,mBAAmB,EAAEA,mBAAmB;IACxC;IACAS,YAAY,EAAEA,YAAY;IAC1BP,UAAU,EAAEA,UAAU;IACtBY,OAAO,EAAEA,OAAO;IAChBG,cAAc,EAAEA;EAClB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}