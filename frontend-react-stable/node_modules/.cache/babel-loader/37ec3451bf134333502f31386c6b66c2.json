{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = group;\nexports.flatGroup = flatGroup;\nexports.flatRollup = flatRollup;\nexports.groups = groups;\nexports.index = index;\nexports.indexes = indexes;\nexports.rollup = rollup;\nexports.rollups = rollups;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction group(values) {\n  for (var _len = arguments.length, keys = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    keys[_key - 1] = arguments[_key];\n  }\n  return nest(values, _identity.default, _identity.default, keys);\n}\nfunction groups(values) {\n  for (var _len2 = arguments.length, keys = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    keys[_key2 - 1] = arguments[_key2];\n  }\n  return nest(values, Array.from, _identity.default, keys);\n}\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(_ref => {\n      let [key, value] = _ref;\n      return [...g, key, value];\n    }));\n  }\n  return groups;\n}\nfunction flatGroup(values) {\n  for (var _len3 = arguments.length, keys = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    keys[_key3 - 1] = arguments[_key3];\n  }\n  return flatten(groups(values, ...keys), keys);\n}\nfunction flatRollup(values, reduce) {\n  for (var _len4 = arguments.length, keys = new Array(_len4 > 2 ? _len4 - 2 : 0), _key4 = 2; _key4 < _len4; _key4++) {\n    keys[_key4 - 2] = arguments[_key4];\n  }\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\nfunction rollup(values, reduce) {\n  for (var _len5 = arguments.length, keys = new Array(_len5 > 2 ? _len5 - 2 : 0), _key5 = 2; _key5 < _len5; _key5++) {\n    keys[_key5 - 2] = arguments[_key5];\n  }\n  return nest(values, _identity.default, reduce, keys);\n}\nfunction rollups(values, reduce) {\n  for (var _len6 = arguments.length, keys = new Array(_len6 > 2 ? _len6 - 2 : 0), _key6 = 2; _key6 < _len6; _key6++) {\n    keys[_key6 - 2] = arguments[_key6];\n  }\n  return nest(values, Array.from, reduce, keys);\n}\nfunction index(values) {\n  for (var _len7 = arguments.length, keys = new Array(_len7 > 1 ? _len7 - 1 : 0), _key7 = 1; _key7 < _len7; _key7++) {\n    keys[_key7 - 1] = arguments[_key7];\n  }\n  return nest(values, _identity.default, unique, keys);\n}\nfunction indexes(values) {\n  for (var _len8 = arguments.length, keys = new Array(_len8 > 1 ? _len8 - 1 : 0), _key8 = 1; _key8 < _len8; _key8++) {\n    keys[_key8 - 1] = arguments[_key8];\n  }\n  return nest(values, Array.from, unique, keys);\n}\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new _index.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  }(values, 0);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "group", "flatGroup", "flatRollup", "groups", "index", "indexes", "rollup", "rollups", "_index", "require", "_identity", "_interopRequireDefault", "obj", "__esModule", "values", "_len", "arguments", "length", "keys", "Array", "_key", "nest", "_len2", "_key2", "from", "flatten", "i", "n", "flatMap", "g", "pop", "map", "_ref", "key", "_len3", "_key3", "reduce", "_len4", "_key4", "_len5", "_key5", "_len6", "_key6", "_len7", "_key7", "unique", "_len8", "_key8", "Error", "regroup", "InternMap", "keyof", "get", "push", "set"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/group.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = group;\nexports.flatGroup = flatGroup;\nexports.flatRollup = flatRollup;\nexports.groups = groups;\nexports.index = index;\nexports.indexes = indexes;\nexports.rollup = rollup;\nexports.rollups = rollups;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nvar _identity = _interopRequireDefault(require(\"./identity.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction group(values, ...keys) {\n  return nest(values, _identity.default, _identity.default, keys);\n}\n\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, _identity.default, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n\n  return groups;\n}\n\nfunction flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nfunction flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, _identity.default, reduce, keys);\n}\n\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nfunction index(values, ...keys) {\n  return nest(values, _identity.default, unique, keys);\n}\n\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new _index.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);else groups.set(key, [value]);\n    }\n\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n\n    return map(groups);\n  }(values, 0);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AACvBH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAC7BJ,OAAO,CAACK,UAAU,GAAGA,UAAU;AAC/BL,OAAO,CAACM,MAAM,GAAGA,MAAM;AACvBN,OAAO,CAACO,KAAK,GAAGA,KAAK;AACrBP,OAAO,CAACQ,OAAO,GAAGA,OAAO;AACzBR,OAAO,CAACS,MAAM,GAAGA,MAAM;AACvBT,OAAO,CAACU,OAAO,GAAGA,OAAO;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,IAAIC,SAAS,GAAGC,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEb,OAAO,EAAEa;EAAI,CAAC;AAAE;AAE9F,SAASZ,KAAKA,CAACc,MAAM,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC5B,OAAOC,IAAI,CAACP,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAEW,SAAS,CAACX,OAAO,EAAEmB,IAAI,CAAC;AACjE;AAEA,SAASf,MAAMA,CAACW,MAAM,EAAW;EAAA,SAAAQ,KAAA,GAAAN,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAG,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJL,IAAI,CAAAK,KAAA,QAAAP,SAAA,CAAAO,KAAA;EAAA;EAC7B,OAAOF,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACK,IAAI,EAAEd,SAAS,CAACX,OAAO,EAAEmB,IAAI,CAAC;AAC1D;AAEA,SAASO,OAAOA,CAACtB,MAAM,EAAEe,IAAI,EAAE;EAC7B,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGT,IAAI,CAACD,MAAM,EAAES,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC3CvB,MAAM,GAAGA,MAAM,CAACyB,OAAO,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,CAACC,IAAA;MAAA,IAAC,CAACC,GAAG,EAAEnC,KAAK,CAAC,GAAAkC,IAAA;MAAA,OAAK,CAAC,GAAGH,CAAC,EAAEI,GAAG,EAAEnC,KAAK,CAAC;IAAA,EAAC,CAAC;EACjF;EAEA,OAAOK,MAAM;AACf;AAEA,SAASF,SAASA,CAACa,MAAM,EAAW;EAAA,SAAAoB,KAAA,GAAAlB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAe,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJjB,IAAI,CAAAiB,KAAA,QAAAnB,SAAA,CAAAmB,KAAA;EAAA;EAChC,OAAOV,OAAO,CAACtB,MAAM,CAACW,MAAM,EAAE,GAAGI,IAAI,CAAC,EAAEA,IAAI,CAAC;AAC/C;AAEA,SAAShB,UAAUA,CAACY,MAAM,EAAEsB,MAAM,EAAW;EAAA,SAAAC,KAAA,GAAArB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAkB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJpB,IAAI,CAAAoB,KAAA,QAAAtB,SAAA,CAAAsB,KAAA;EAAA;EACzC,OAAOb,OAAO,CAAClB,OAAO,CAACO,MAAM,EAAEsB,MAAM,EAAE,GAAGlB,IAAI,CAAC,EAAEA,IAAI,CAAC;AACxD;AAEA,SAASZ,MAAMA,CAACQ,MAAM,EAAEsB,MAAM,EAAW;EAAA,SAAAG,KAAA,GAAAvB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAoB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJtB,IAAI,CAAAsB,KAAA,QAAAxB,SAAA,CAAAwB,KAAA;EAAA;EACrC,OAAOnB,IAAI,CAACP,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAEqC,MAAM,EAAElB,IAAI,CAAC;AACtD;AAEA,SAASX,OAAOA,CAACO,MAAM,EAAEsB,MAAM,EAAW;EAAA,SAAAK,KAAA,GAAAzB,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAsB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJxB,IAAI,CAAAwB,KAAA,QAAA1B,SAAA,CAAA0B,KAAA;EAAA;EACtC,OAAOrB,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACK,IAAI,EAAEY,MAAM,EAAElB,IAAI,CAAC;AAC/C;AAEA,SAASd,KAAKA,CAACU,MAAM,EAAW;EAAA,SAAA6B,KAAA,GAAA3B,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAwB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJ1B,IAAI,CAAA0B,KAAA,QAAA5B,SAAA,CAAA4B,KAAA;EAAA;EAC5B,OAAOvB,IAAI,CAACP,MAAM,EAAEJ,SAAS,CAACX,OAAO,EAAE8C,MAAM,EAAE3B,IAAI,CAAC;AACtD;AAEA,SAASb,OAAOA,CAACS,MAAM,EAAW;EAAA,SAAAgC,KAAA,GAAA9B,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAA2B,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJ7B,IAAI,CAAA6B,KAAA,QAAA/B,SAAA,CAAA+B,KAAA;EAAA;EAC9B,OAAO1B,IAAI,CAACP,MAAM,EAAEK,KAAK,CAACK,IAAI,EAAEqB,MAAM,EAAE3B,IAAI,CAAC;AAC/C;AAEA,SAAS2B,MAAMA,CAAC/B,MAAM,EAAE;EACtB,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,MAAM,IAAI+B,KAAK,CAAC,eAAe,CAAC;EACzD,OAAOlC,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,SAASO,IAAIA,CAACP,MAAM,EAAEiB,GAAG,EAAEK,MAAM,EAAElB,IAAI,EAAE;EACvC,OAAO,SAAS+B,OAAOA,CAACnC,MAAM,EAAEY,CAAC,EAAE;IACjC,IAAIA,CAAC,IAAIR,IAAI,CAACD,MAAM,EAAE,OAAOmB,MAAM,CAACtB,MAAM,CAAC;IAC3C,MAAMX,MAAM,GAAG,IAAIK,MAAM,CAAC0C,SAAS,CAAC,CAAC;IACrC,MAAMC,KAAK,GAAGjC,IAAI,CAACQ,CAAC,EAAE,CAAC;IACvB,IAAItB,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,MAAMN,KAAK,IAAIgB,MAAM,EAAE;MAC1B,MAAMmB,GAAG,GAAGkB,KAAK,CAACrD,KAAK,EAAE,EAAEM,KAAK,EAAEU,MAAM,CAAC;MACzC,MAAMd,KAAK,GAAGG,MAAM,CAACiD,GAAG,CAACnB,GAAG,CAAC;MAC7B,IAAIjC,KAAK,EAAEA,KAAK,CAACqD,IAAI,CAACvD,KAAK,CAAC,CAAC,KAAKK,MAAM,CAACmD,GAAG,CAACrB,GAAG,EAAE,CAACnC,KAAK,CAAC,CAAC;IAC5D;IAEA,KAAK,MAAM,CAACmC,GAAG,EAAEnB,MAAM,CAAC,IAAIX,MAAM,EAAE;MAClCA,MAAM,CAACmD,GAAG,CAACrB,GAAG,EAAEgB,OAAO,CAACnC,MAAM,EAAEY,CAAC,CAAC,CAAC;IACrC;IAEA,OAAOK,GAAG,CAAC5B,MAAM,CAAC;EACpB,CAAC,CAACW,MAAM,EAAE,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}