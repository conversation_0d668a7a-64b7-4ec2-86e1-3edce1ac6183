{"ast": null, "code": "import{configureStore}from'@reduxjs/toolkit';import authReducer from'./slices/authSlice';import uiReducer from'./slices/uiSlice';export const store=configureStore({reducer:{auth:authReducer,ui:uiReducer},middleware:getDefaultMiddleware=>getDefaultMiddleware({serializableCheck:{ignoredActions:['persist/PERSIST']}})});", "map": {"version": 3, "names": ["configureStore", "authReducer", "uiReducer", "store", "reducer", "auth", "ui", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["/home/<USER>/frontend-react-stable/src/store/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport uiReducer from './slices/uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    ui: uiReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,OAASA,cAAc,KAAQ,kBAAkB,CACjD,MAAO,CAAAC,WAAW,KAAM,oBAAoB,CAC5C,MAAO,CAAAC,SAAS,KAAM,kBAAkB,CAExC,MAAO,MAAM,CAAAC,KAAK,CAAGH,cAAc,CAAC,CAClCI,OAAO,CAAE,CACPC,IAAI,CAAEJ,WAAW,CACjBK,EAAE,CAAEJ,SACN,CAAC,CACDK,UAAU,CAAGC,oBAAoB,EAC/BA,oBAAoB,CAAC,CACnBC,iBAAiB,CAAE,CACjBC,cAAc,CAAE,CAAC,iBAAiB,CACpC,CACF,CAAC,CACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}