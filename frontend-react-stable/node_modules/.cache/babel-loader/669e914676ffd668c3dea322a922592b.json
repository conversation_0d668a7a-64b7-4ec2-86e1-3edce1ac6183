{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getDirectionStyle } from '../util';\nimport SliderContext from '../context';\nexport default function Dot(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    included = _React$useContext.included,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd;\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd; // ============================ Offset ============================\n\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n}", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "classNames", "getDirectionStyle", "SliderContext", "Dot", "props", "prefixCls", "value", "style", "activeStyle", "_React$useContext", "useContext", "min", "max", "direction", "included", "includedStart", "includedEnd", "dotClassName", "concat", "active", "mergedStyle", "createElement", "className"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/Steps/Dot.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getDirectionStyle } from '../util';\nimport SliderContext from '../context';\nexport default function Dot(props) {\n  var prefixCls = props.prefixCls,\n      value = props.value,\n      style = props.style,\n      activeStyle = props.activeStyle;\n\n  var _React$useContext = React.useContext(SliderContext),\n      min = _React$useContext.min,\n      max = _React$useContext.max,\n      direction = _React$useContext.direction,\n      included = _React$useContext.included,\n      includedStart = _React$useContext.includedStart,\n      includedEnd = _React$useContext.includedEnd;\n\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd; // ============================ Offset ============================\n\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,OAAOC,aAAa,MAAM,YAAY;AACtC,eAAe,SAASC,GAAGA,CAACC,KAAK,EAAE;EACjC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;EAEnC,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACR,aAAa,CAAC;IACnDS,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IAC3BC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,SAAS,GAAGJ,iBAAiB,CAACI,SAAS;IACvCC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,aAAa,GAAGN,iBAAiB,CAACM,aAAa;IAC/CC,WAAW,GAAGP,iBAAiB,CAACO,WAAW;EAE/C,IAAIC,YAAY,GAAG,EAAE,CAACC,MAAM,CAACb,SAAS,EAAE,MAAM,CAAC;EAC/C,IAAIc,MAAM,GAAGL,QAAQ,IAAIC,aAAa,IAAIT,KAAK,IAAIA,KAAK,IAAIU,WAAW,CAAC,CAAC;;EAEzE,IAAII,WAAW,GAAGtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEG,iBAAiB,CAACY,SAAS,EAAEP,KAAK,EAAEK,GAAG,EAAEC,GAAG,CAAC,CAAC,EAAE,OAAOL,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACD,KAAK,CAAC,GAAGC,KAAK,CAAC;EAErJ,IAAIY,MAAM,EAAE;IACVC,WAAW,GAAGtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC,EAAE,OAAOZ,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACF,KAAK,CAAC,GAAGE,WAAW,CAAC;EACnI;EAEA,OAAO,aAAaT,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEtB,UAAU,CAACiB,YAAY,EAAEpB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACD,YAAY,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,CAAC;IACpGZ,KAAK,EAAEa;EACT,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}