{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport React, { useContext, useEffect } from 'react';\nimport warn from \"rc-util/es/warning\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport IconContext from './components/Context';\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        acc[key] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles() {\n  var styleStr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : iconStyles;\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp;\n  useEffect(function () {\n    updateCSS(styleStr, '@ant-design-icons', {\n      prepend: true,\n      csp: csp\n    });\n  }, []);\n};", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "generate", "generateColor", "React", "useContext", "useEffect", "warn", "updateCSS", "IconContext", "warning", "valid", "message", "concat", "isIconDefinition", "target", "name", "theme", "icon", "normalizeAttrs", "attrs", "arguments", "length", "undefined", "Object", "keys", "reduce", "acc", "key", "val", "className", "class", "node", "rootProps", "createElement", "tag", "children", "map", "child", "index", "getSecondaryColor", "primaryColor", "normalizeTwoToneColors", "twoToneColor", "Array", "isArray", "svgBaseProps", "width", "height", "fill", "focusable", "iconStyles", "useInsertStyles", "styleStr", "_useContext", "csp", "prepend"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/utils.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport React, { useContext, useEffect } from 'react';\nimport warn from \"rc-util/es/warning\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport IconContext from './components/Context';\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        acc[key] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles() {\n  var styleStr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : iconStyles;\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp;\n  useEffect(function () {\n    updateCSS(styleStr, '@ant-design-icons', {\n      prepend: true,\n      csp: csp\n    });\n  }, []);\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAC9D,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACpD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtCL,IAAI,CAACI,KAAK,EAAE,sBAAsB,CAACE,MAAM,CAACD,OAAO,CAAC,CAAC;AACrD;AACA,OAAO,SAASE,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOd,OAAO,CAACc,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,QAAQ,IAAI,OAAOD,MAAM,CAACE,KAAK,KAAK,QAAQ,KAAKhB,OAAO,CAACc,MAAM,CAACG,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOH,MAAM,CAACG,IAAI,KAAK,UAAU,CAAC;AACxL;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,OAAOG,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACnD,IAAIC,GAAG,GAAGT,KAAK,CAACQ,GAAG,CAAC;IACpB,QAAQA,GAAG;MACT,KAAK,OAAO;QACVD,GAAG,CAACG,SAAS,GAAGD,GAAG;QACnB,OAAOF,GAAG,CAACI,KAAK;QAChB;MACF;QACEJ,GAAG,CAACC,GAAG,CAAC,GAAGC,GAAG;IAClB;IACA,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,OAAO,SAASzB,QAAQA,CAAC8B,IAAI,EAAEJ,GAAG,EAAEK,SAAS,EAAE;EAC7C,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAACF,IAAI,CAACG,GAAG,EAAEnC,aAAa,CAAC;MAC9D4B,GAAG,EAAEA;IACP,CAAC,EAAET,cAAc,CAACa,IAAI,CAACZ,KAAK,CAAC,CAAC,EAAE,CAACY,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MAChF,OAAOrC,QAAQ,CAACoC,KAAK,EAAE,EAAE,CAACzB,MAAM,CAACe,GAAG,EAAE,GAAG,CAAC,CAACf,MAAM,CAACmB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACtB,MAAM,CAAC0B,KAAK,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAanC,KAAK,CAAC8B,aAAa,CAACF,IAAI,CAACG,GAAG,EAAEnC,aAAa,CAACA,aAAa,CAAC;IAC5E4B,GAAG,EAAEA;EACP,CAAC,EAAET,cAAc,CAACa,IAAI,CAACZ,KAAK,CAAC,CAAC,EAAEa,SAAS,CAAC,EAAE,CAACD,IAAI,CAACI,QAAQ,IAAI,EAAE,EAAEC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC5F,OAAOrC,QAAQ,CAACoC,KAAK,EAAE,EAAE,CAACzB,MAAM,CAACe,GAAG,EAAE,GAAG,CAAC,CAACf,MAAM,CAACmB,IAAI,CAACG,GAAG,EAAE,GAAG,CAAC,CAACtB,MAAM,CAAC0B,KAAK,CAAC,CAAC;EACjF,CAAC,CAAC,CAAC;AACL;AACA,OAAO,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EAC9C;EACA,OAAOtC,aAAa,CAACsC,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC;AACA,OAAO,SAASC,sBAAsBA,CAACC,YAAY,EAAE;EACnD,IAAI,CAACA,YAAY,EAAE;IACjB,OAAO,EAAE;EACX;EACA,OAAOC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;AACpE;AACA;AACA;AACA,OAAO,IAAIG,YAAY,GAAG;EACxBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,cAAc;EACpB,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,w7BAAw7B;AACh9B,OAAO,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EACtD,IAAIC,QAAQ,GAAGhC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG8B,UAAU;EAC7F,IAAIG,WAAW,GAAGjD,UAAU,CAACI,WAAW,CAAC;IACvC8C,GAAG,GAAGD,WAAW,CAACC,GAAG;EACvBjD,SAAS,CAAC,YAAY;IACpBE,SAAS,CAAC6C,QAAQ,EAAE,mBAAmB,EAAE;MACvCG,OAAO,EAAE,IAAI;MACbD,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}