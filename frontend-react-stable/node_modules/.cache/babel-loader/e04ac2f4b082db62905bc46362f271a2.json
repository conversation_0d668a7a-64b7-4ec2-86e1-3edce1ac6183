{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nexport default function useFrameState(defaultValue) {\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    value = _React$useState2[0],\n    setValue = _React$useState2[1];\n  var frameRef = useRef(null);\n  var batchRef = useRef([]);\n  var destroyRef = useRef(false);\n  React.useEffect(function () {\n    destroyRef.current = false;\n    return function () {\n      destroyRef.current = true;\n      raf.cancel(frameRef.current);\n      frameRef.current = null;\n    };\n  }, []);\n  function setFrameValue(updater) {\n    if (destroyRef.current) {\n      return;\n    }\n    if (frameRef.current === null) {\n      batchRef.current = [];\n      frameRef.current = raf(function () {\n        frameRef.current = null;\n        setValue(function (prevValue) {\n          var current = prevValue;\n          batchRef.current.forEach(function (func) {\n            current = func(current);\n          });\n          return current;\n        });\n      });\n    }\n    batchRef.current.push(updater);\n  }\n  return [value, setFrameValue];\n}", "map": {"version": 3, "names": ["_slicedToArray", "raf", "React", "useRef", "useFrameState", "defaultValue", "_React$useState", "useState", "_React$useState2", "value", "setValue", "frameRef", "batchRef", "destroyRef", "useEffect", "current", "cancel", "setFrameValue", "updater", "prevValue", "for<PERSON>ach", "func", "push"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/hooks/useFrameState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nexport default function useFrameState(defaultValue) {\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    value = _React$useState2[0],\n    setValue = _React$useState2[1];\n  var frameRef = useRef(null);\n  var batchRef = useRef([]);\n  var destroyRef = useRef(false);\n  React.useEffect(function () {\n    destroyRef.current = false;\n    return function () {\n      destroyRef.current = true;\n      raf.cancel(frameRef.current);\n      frameRef.current = null;\n    };\n  }, []);\n  function setFrameValue(updater) {\n    if (destroyRef.current) {\n      return;\n    }\n    if (frameRef.current === null) {\n      batchRef.current = [];\n      frameRef.current = raf(function () {\n        frameRef.current = null;\n        setValue(function (prevValue) {\n          var current = prevValue;\n          batchRef.current.forEach(function (func) {\n            current = func(current);\n          });\n          return current;\n        });\n      });\n    }\n    batchRef.current.push(updater);\n  }\n  return [value, setFrameValue];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAACC,YAAY,EAAE;EAClD,IAAIC,eAAe,GAAGJ,KAAK,CAACK,QAAQ,CAACF,YAAY,CAAC;IAChDG,gBAAgB,GAAGR,cAAc,CAACM,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIS,QAAQ,GAAGT,MAAM,CAAC,EAAE,CAAC;EACzB,IAAIU,UAAU,GAAGV,MAAM,CAAC,KAAK,CAAC;EAC9BD,KAAK,CAACY,SAAS,CAAC,YAAY;IAC1BD,UAAU,CAACE,OAAO,GAAG,KAAK;IAC1B,OAAO,YAAY;MACjBF,UAAU,CAACE,OAAO,GAAG,IAAI;MACzBd,GAAG,CAACe,MAAM,CAACL,QAAQ,CAACI,OAAO,CAAC;MAC5BJ,QAAQ,CAACI,OAAO,GAAG,IAAI;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,SAASE,aAAaA,CAACC,OAAO,EAAE;IAC9B,IAAIL,UAAU,CAACE,OAAO,EAAE;MACtB;IACF;IACA,IAAIJ,QAAQ,CAACI,OAAO,KAAK,IAAI,EAAE;MAC7BH,QAAQ,CAACG,OAAO,GAAG,EAAE;MACrBJ,QAAQ,CAACI,OAAO,GAAGd,GAAG,CAAC,YAAY;QACjCU,QAAQ,CAACI,OAAO,GAAG,IAAI;QACvBL,QAAQ,CAAC,UAAUS,SAAS,EAAE;UAC5B,IAAIJ,OAAO,GAAGI,SAAS;UACvBP,QAAQ,CAACG,OAAO,CAACK,OAAO,CAAC,UAAUC,IAAI,EAAE;YACvCN,OAAO,GAAGM,IAAI,CAACN,OAAO,CAAC;UACzB,CAAC,CAAC;UACF,OAAOA,OAAO;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAH,QAAQ,CAACG,OAAO,CAACO,IAAI,CAACJ,OAAO,CAAC;EAChC;EACA,OAAO,CAACT,KAAK,EAAEQ,aAAa,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}