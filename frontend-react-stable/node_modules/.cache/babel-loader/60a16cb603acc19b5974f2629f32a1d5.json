{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport Tooltip from '../tooltip';\nvar SliderTooltip = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var open = props.open;\n  var innerRef = useRef(null);\n  var rafRef = useRef(null);\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n  function keepAlign() {\n    rafRef.current = raf(function () {\n      var _a;\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      rafRef.current = null;\n    });\n  }\n  React.useEffect(function () {\n    if (open) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n    return cancelKeepAlign;\n  }, [open, props.title]);\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    ref: composeRef(innerRef, ref)\n  }, props));\n});\nexport default SliderTooltip;", "map": {"version": 3, "names": ["_extends", "raf", "composeRef", "React", "useRef", "<PERSON><PERSON><PERSON>", "SliderTooltip", "forwardRef", "props", "ref", "open", "innerRef", "rafRef", "cancelKeepAlign", "cancel", "current", "keepAlign", "_a", "forcePopupAlign", "useEffect", "title", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/slider/SliderTooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport raf from \"rc-util/es/raf\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport Tooltip from '../tooltip';\nvar SliderTooltip = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var open = props.open;\n  var innerRef = useRef(null);\n  var rafRef = useRef(null);\n  function cancelKeepAlign() {\n    raf.cancel(rafRef.current);\n    rafRef.current = null;\n  }\n  function keepAlign() {\n    rafRef.current = raf(function () {\n      var _a;\n      (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.forcePopupAlign();\n      rafRef.current = null;\n    });\n  }\n  React.useEffect(function () {\n    if (open) {\n      keepAlign();\n    } else {\n      cancelKeepAlign();\n    }\n    return cancelKeepAlign;\n  }, [open, props.title]);\n  return /*#__PURE__*/React.createElement(Tooltip, _extends({\n    ref: composeRef(innerRef, ref)\n  }, props));\n});\nexport default SliderTooltip;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;AAChC,IAAIC,aAAa,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;EACrB,IAAIC,QAAQ,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIQ,MAAM,GAAGR,MAAM,CAAC,IAAI,CAAC;EACzB,SAASS,eAAeA,CAAA,EAAG;IACzBZ,GAAG,CAACa,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;IAC1BH,MAAM,CAACG,OAAO,GAAG,IAAI;EACvB;EACA,SAASC,SAASA,CAAA,EAAG;IACnBJ,MAAM,CAACG,OAAO,GAAGd,GAAG,CAAC,YAAY;MAC/B,IAAIgB,EAAE;MACN,CAACA,EAAE,GAAGN,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,eAAe,CAAC,CAAC;MACjFN,MAAM,CAACG,OAAO,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EACAZ,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,IAAIT,IAAI,EAAE;MACRM,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLH,eAAe,CAAC,CAAC;IACnB;IACA,OAAOA,eAAe;EACxB,CAAC,EAAE,CAACH,IAAI,EAAEF,KAAK,CAACY,KAAK,CAAC,CAAC;EACvB,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAAChB,OAAO,EAAEL,QAAQ,CAAC;IACxDS,GAAG,EAAEP,UAAU,CAACS,QAAQ,EAAEF,GAAG;EAC/B,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}