{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Typo<PERSON>,Card,Alert,Tabs,Button,Select,Input,Form,message,Table,Space,Modal,Row,Col,Divider}from'antd';import{FileTextOutlined,SettingOutlined,SendOutlined,ReloadOutlined,EyeOutlined,DownloadOutlined,EditOutlined,FolderOpenOutlined,CloudUploadOutlined}from'@ant-design/icons';import{cleanTemplateAPI}from'../services/api';import TextArea from'antd/es/input/TextArea';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TabPane}=Tabs;const{Option}=Select;const CleanTemplatePage=()=>{const[loading,setLoading]=useState(false);const[generateForm]=Form.useForm();const[sendForm]=Form.useForm();const[updateForm]=Form.useForm();// 模板生成相关状态\nconst[resultFiles,setResultFiles]=useState([]);const[resultDir,setResultDir]=useState('');const[lastGeneratedTemplate,setLastGeneratedTemplate]=useState(null);// 模板管理相关状态\nconst[templates,setTemplates]=useState([]);const[templateDir,setTemplateDir]=useState('');const[selectedTemplate,setSelectedTemplate]=useState('');const[templateContent,setTemplateContent]=useState('');const[editModalVisible,setEditModalVisible]=useState(false);const[viewModalVisible,setViewModalVisible]=useState(false);// 模板发送功能的状态\nconst[sendTemplateDir,setSendTemplateDir]=useState('');const[sendTemplates,setSendTemplates]=useState([]);const[sendTemplatesLoading,setSendTemplatesLoading]=useState(false);// 获取结果文件列表\nconst fetchResultFiles=useCallback(async function(){let showMessage=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(!resultDir.trim()){setResultFiles([]);return;}try{const response=await cleanTemplateAPI.listResultFiles(resultDir);if(response.data.files){setResultFiles(response.data.files||[]);if(showMessage&&response.data.files.length>0){message.success(`📁 找到 ${response.data.files.length} 个结果文件`);}else if(showMessage&&response.data.files.length===0){message.info('📁 该目录下暂无结果文件');}}}catch(error){console.error('获取结果文件失败:',error);if(showMessage){var _error$response,_error$response$data;message.error(`❌ 获取结果文件失败: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||error.message}`);}setResultFiles([]);}},[resultDir]);// 获取模板列表\nconst fetchTemplates=useCallback(async function(){let showMessage=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(!templateDir.trim()){setTemplates([]);return;}setLoading(true);try{const response=await cleanTemplateAPI.listTemplates(templateDir);console.log('模板列表API响应:',response.data);// 调试信息\nif(response.data&&response.data.templates){const templatesData=response.data.templates;console.log('模板数据:',templatesData);// 调试信息\n// 检查数据格式并转换\nlet processedTemplates=[];if(Array.isArray(templatesData)){processedTemplates=templatesData.map((item,index)=>{if(typeof item==='string'){// 如果是字符串数组（旧格式），转换为对象\nreturn{template_name:item.replace('_cleantemplate.json',''),filename:item,template_path:`${templateDir}/${item}`,file_size:0,created_time:0,modified_time:0};}else if(typeof item==='object'&&item!==null){var _item$filename;// 如果已经是对象格式，直接使用\nreturn{template_name:item.template_name||((_item$filename=item.filename)===null||_item$filename===void 0?void 0:_item$filename.replace('_cleantemplate.json',''))||`模板${index+1}`,filename:item.filename||item.template_name||`template${index+1}.json`,template_path:item.template_path||`${templateDir}/${item.filename}`,file_size:item.file_size||0,created_time:item.created_time||0,modified_time:item.modified_time||0};}return item;});}console.log('处理后的模板数据:',processedTemplates);// 调试信息\nsetTemplates(processedTemplates);if(showMessage&&processedTemplates.length>0){message.success(`📁 找到 ${processedTemplates.length} 个模板文件`);}else if(showMessage&&processedTemplates.length===0){message.info('📁 该目录下暂无模板文件');}}}catch(error){console.error('获取模板列表失败:',error);if(showMessage){var _error$response2,_error$response2$data;message.error(`❌ 获取模板列表失败: ${((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message}`);}setTemplates([]);}finally{setLoading(false);}},[templateDir]);// 获取发送模板列表\nconst fetchSendTemplates=useCallback(async function(){let showMessage=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(!sendTemplateDir.trim()){setSendTemplates([]);return;}setSendTemplatesLoading(true);try{const response=await cleanTemplateAPI.listTemplates(sendTemplateDir);console.log('发送模板列表API响应:',response.data);// 调试信息\nif(response.data&&response.data.templates){const templatesData=response.data.templates;console.log('发送模板数据:',templatesData);// 调试信息\n// 检查数据格式并转换\nlet processedTemplates=[];if(Array.isArray(templatesData)){processedTemplates=templatesData.map((item,index)=>{if(typeof item==='string'){// 如果是字符串数组（旧格式），转换为对象\nreturn{template_name:item.replace('_cleantemplate.json',''),filename:item,template_path:`${sendTemplateDir}/${item}`,file_size:0,created_time:0,modified_time:0};}else if(typeof item==='object'&&item!==null){var _item$filename2;// 如果已经是对象格式，确保所有必需字段存在\nreturn{template_name:item.template_name||((_item$filename2=item.filename)===null||_item$filename2===void 0?void 0:_item$filename2.replace('_cleantemplate.json',''))||`模板${index+1}`,filename:item.filename||item.template_name||`template${index+1}.json`,template_path:item.template_path||`${sendTemplateDir}/${item.filename}`,file_size:item.file_size||0,created_time:item.created_time||0,modified_time:item.modified_time||0};}return item;});}console.log('处理后的发送模板数据:',processedTemplates);// 调试信息\nsetSendTemplates(processedTemplates);if(showMessage&&processedTemplates.length>0){message.success(`📁 找到 ${processedTemplates.length} 个可发送的模板文件`);}else if(showMessage&&processedTemplates.length===0){message.info('📁 该目录下暂无模板文件');}}}catch(error){console.error('获取发送模板列表失败:',error);if(showMessage){var _error$response3,_error$response3$data;message.error(`❌ 获取发送模板列表失败: ${((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message}`);}setSendTemplates([]);}finally{setSendTemplatesLoading(false);}},[sendTemplateDir]);// 获取模板内容\nconst fetchTemplateContent=async templatePath=>{try{const response=await cleanTemplateAPI.getTemplateContent(templatePath);if(response.data&&response.data.content){setTemplateContent(JSON.stringify(response.data.content,null,2));return response.data.content;}}catch(error){var _error$response4,_error$response4$data;console.error('获取模板内容失败:',error);message.error(`❌ 获取模板内容失败: ${((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message}`);}return null;};// 生成清洗模板\nconst generateTemplate=async values=>{setLoading(true);try{const formData=new FormData();formData.append('results_file',`${resultDir}/${values.selected_result_file}`);formData.append('output_folder',values.output_dir);if(values.template_name){formData.append('template_name',values.template_name);}const response=await cleanTemplateAPI.generateTemplate(formData);if(response.data){const{template_path,updated_thresholds}=response.data;// 显示详细的成功信息\nmessage.success({content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:4},children:\"\\uD83C\\uDF89 \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u6210\\u529F\\uFF01\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[\"\\uD83D\\uDCC1 \\u6587\\u4EF6\\u8DEF\\u5F84: \",template_path,/*#__PURE__*/_jsx(\"br\",{}),\"\\uD83D\\uDD27 \\u66F4\\u65B0\\u9608\\u503C: \",updated_thresholds,\" \\u4E2A\"]})]}),duration:6// 显示6秒\n});// 刷新模板列表\nif(templateDir){fetchTemplates(true);// 显示刷新消息\n}// 设置最近生成的模板信息\nsetLastGeneratedTemplate({path:template_path,name:template_path.split('/').pop()||'未知模板',time:new Date().toLocaleString()});// 重置表单\ngenerateForm.resetFields();setResultFiles([]);// 清空结果文件列表\nsetResultDir('');// 清空目录输入\n}}catch(error){var _error$response5,_error$response5$data;console.error('生成模板失败:',error);message.error(`❌ 生成模板失败: ${((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||error.message}`);}finally{setLoading(false);}};// 更新模板内容\nconst updateTemplate=async values=>{try{const formData=new FormData();formData.append('template_path',selectedTemplate);formData.append('template_content',values.template_content);const response=await cleanTemplateAPI.updateTemplate(formData);if(response.data&&response.data.message){message.success('✅ 模板内容更新成功');setEditModalVisible(false);fetchTemplates(true);// 刷新模板列表\n}}catch(error){var _error$response6,_error$response6$data;console.error('更新模板失败:',error);message.error(`❌ 更新模板失败: ${((_error$response6=error.response)===null||_error$response6===void 0?void 0:(_error$response6$data=_error$response6.data)===null||_error$response6$data===void 0?void 0:_error$response6$data.detail)||error.message}`);}};// 发送模板\nconst sendTemplate=async values=>{console.log('发送模板参数:',values);// 调试信息\nsetLoading(true);try{const formData=new FormData();formData.append('template_path',values.template_path);formData.append('target_host',values.target_host);formData.append('target_username',values.target_username);formData.append('target_password',values.target_password);formData.append('target_port',values.target_port.toString());formData.append('target_path',values.target_path);const response=await cleanTemplateAPI.sendTemplate(formData);if(response.data&&response.data.message){message.success({content:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:4},children:\"\\uD83C\\uDF89 \\u6A21\\u677F\\u53D1\\u9001\\u6210\\u529F\\uFF01\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[\"\\uD83D\\uDCC1 \\u6A21\\u677F: \",values.template_path.split('/').pop(),/*#__PURE__*/_jsx(\"br\",{}),\"\\uD83D\\uDDA5\\uFE0F \\u76EE\\u6807: \",values.target_host,\":\",values.target_port,/*#__PURE__*/_jsx(\"br\",{}),\"\\uD83D\\uDCC2 \\u8DEF\\u5F84: \",values.target_path]})]}),duration:6});// 重置表单，但保留选择模式\nconst currentMode=sendForm.getFieldValue('template_selection_mode');sendForm.resetFields();sendForm.setFieldsValue({template_selection_mode:currentMode});}}catch(error){var _error$response7,_error$response7$data;console.error('发送模板失败:',error);message.error(`❌ 发送模板失败: ${((_error$response7=error.response)===null||_error$response7===void 0?void 0:(_error$response7$data=_error$response7.data)===null||_error$response7$data===void 0?void 0:_error$response7$data.detail)||error.message}`);}finally{setLoading(false);}};// 下载模板\nconst downloadTemplate=async(templatePath,templateName)=>{console.log('下载模板，路径:',templatePath,'名称:',templateName);// 调试信息\nif(!templatePath||templatePath==='undefined'){message.error('模板路径无效');return;}try{const response=await cleanTemplateAPI.downloadTemplate(templatePath);// 创建下载链接\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download',templateName||'template.json');document.body.appendChild(link);link.click();link.remove();window.URL.revokeObjectURL(url);message.success('✅ 模板下载成功');}catch(error){var _error$response8,_error$response8$data;console.error('下载模板失败:',error);message.error(`❌ 下载模板失败: ${((_error$response8=error.response)===null||_error$response8===void 0?void 0:(_error$response8$data=_error$response8.data)===null||_error$response8$data===void 0?void 0:_error$response8$data.detail)||error.message}`);}};// 查看模板详情\nconst viewTemplate=async templatePath=>{console.log('查看模板，路径:',templatePath);// 调试信息\nif(!templatePath||templatePath==='undefined'){message.error('模板路径无效');return;}const content=await fetchTemplateContent(templatePath);if(content){setViewModalVisible(true);}};// 编辑模板\nconst editTemplate=async templatePath=>{console.log('编辑模板，路径:',templatePath);// 调试信息\nif(!templatePath||templatePath==='undefined'){message.error('模板路径无效');return;}const content=await fetchTemplateContent(templatePath);if(content){setSelectedTemplate(templatePath);updateForm.setFieldsValue({template_content:templateContent});setEditModalVisible(true);}};// 防抖hook\nconst useDebounce=(value,delay)=>{const[debouncedValue,setDebouncedValue]=useState(value);useEffect(()=>{const handler=setTimeout(()=>{setDebouncedValue(value);},delay);return()=>{clearTimeout(handler);};},[value,delay]);return debouncedValue;};// 防抖处理路径变化 - 增加延迟时间减少频繁请求\nconst debouncedResultDir=useDebounce(resultDir,1500);const debouncedTemplateDir=useDebounce(templateDir,1500);const debouncedSendTemplateDir=useDebounce(sendTemplateDir,1500);// 页面初始化时不自动获取数据，等待用户手动刷新\n// useEffect(() => {\n//   fetchResultFiles();\n//   fetchTemplates();\n// }, []);\n// 防抖后的路径变化时静默获取数据（不显示消息）\nuseEffect(()=>{if(debouncedResultDir&&debouncedResultDir.trim()!==''){fetchResultFiles(false);// 静默获取，不显示消息\n}else{setResultFiles([]);// 清空列表\n}},[debouncedResultDir,fetchResultFiles]);useEffect(()=>{if(debouncedTemplateDir&&debouncedTemplateDir.trim()!==''){fetchTemplates(false);// 静默获取，不显示消息\n}else{setTemplates([]);// 清空列表\n}},[debouncedTemplateDir,fetchTemplates]);useEffect(()=>{if(debouncedSendTemplateDir&&debouncedSendTemplateDir.trim()!==''){fetchSendTemplates(false);// 静默获取，不显示消息\n}else{setSendTemplates([]);// 清空列表\n}},[debouncedSendTemplateDir,fetchSendTemplates]);// 模板列表表格列定义\nconst templateColumns=[{title:'模板名称',dataIndex:'template_name',key:'template_name',render:(name,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:name}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:record.filename})]})},{title:'操作',key:'actions',render:(_,record)=>{console.log('表格行数据:',record);// 调试信息\nreturn/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>viewTemplate(record.template_path),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>editTemplate(record.template_path),children:\"\\u7F16\\u8F91\"}),/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>downloadTemplate(record.template_path,record.filename),children:\"\\u4E0B\\u8F7D\"})]});}}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u57FA\\u4E8E\\u6A21\\u578B\\u8BAD\\u7EC3\\u6216\\u9884\\u6D4B\\u7ED3\\u679C\\uFF0C\\u751F\\u6210\\u7279\\u5B9A\\u5BA2\\u6237\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u6A21\\u677F\\u914D\\u7F6E\\u6587\\u4EF6\\u3002\"}),/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"1\",style:{marginTop:24},children:[/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),\"\\u6A21\\u677F\\u751F\\u6210\"]}),children:/*#__PURE__*/_jsxs(Card,{title:\"\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\",size:\"small\",children:[/*#__PURE__*/_jsxs(Form,{form:generateForm,layout:\"vertical\",onFinish:generateTemplate,children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7ED3\\u679C\\u6587\\u4EF6\\u76EE\\u5F55\",name:\"result_dir\",rules:[{required:true,message:'请输入结果文件目录'}],children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(FolderOpenOutlined,{}),placeholder:\"\\u4F8B\\u5982: /data/output\",value:resultDir,onChange:e=>setResultDir(e.target.value),addonAfter:/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:()=>fetchResultFiles(true),children:\"\\u5237\\u65B0\"})})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u9009\\u62E9\\u7ED3\\u679C\\u6587\\u4EF6\",name:\"selected_result_file\",rules:[{required:true,message:'请选择结果文件'}],children:/*#__PURE__*/_jsx(Select,{placeholder:resultFiles.length===0?\"请先输入目录路径并点击刷新\":\"选择要生成模板的结果文件\",showSearch:true,notFoundContent:resultFiles.length===0?\"请先输入目录路径并点击刷新获取文件列表\":\"未找到匹配的文件\",filterOption:(input,option)=>{var _option$children;return option===null||option===void 0?void 0:(_option$children=option.children)===null||_option$children===void 0?void 0:_option$children.toLowerCase().includes(input.toLowerCase());},children:resultFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6A21\\u677F\\u8F93\\u51FA\\u76EE\\u5F55\",name:\"output_dir\",rules:[{required:true,message:'请输入输出目录'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982: /data/output\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6A21\\u677F\\u540D\\u79F0\\uFF08\\u53EF\\u9009\\uFF09\",name:\"template_name\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982: customer_name\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(FileTextOutlined,{}),children:\"\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\"})})]}),lastGeneratedTemplate&&/*#__PURE__*/_jsxs(Card,{size:\"small\",style:{marginTop:16,borderColor:'#52c41a'},title:/*#__PURE__*/_jsxs(\"span\",{style:{color:'#52c41a'},children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),\" \\u6700\\u8FD1\\u751F\\u6210\\u7684\\u6A21\\u677F\"]}),extra:/*#__PURE__*/_jsx(Button,{size:\"small\",type:\"text\",onClick:()=>setLastGeneratedTemplate(null),children:\"\\xD7\"}),children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6A21\\u677F\\u540D\\u79F0\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{copyable:true,children:lastGeneratedTemplate.name})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u751F\\u6210\\u65F6\\u95F4\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{children:lastGeneratedTemplate.time})]})]}),/*#__PURE__*/_jsx(Row,{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6587\\u4EF6\\u8DEF\\u5F84\\uFF1A\"}),/*#__PURE__*/_jsx(Text,{copyable:true,style:{fontSize:'12px',color:'#666'},children:lastGeneratedTemplate.path})]})})]})]})},\"1\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(SettingOutlined,{}),\"\\u6A21\\u677F\\u7BA1\\u7406\"]}),children:/*#__PURE__*/_jsx(Card,{title:\"\\u6A21\\u677F\\u5217\\u8868\",size:\"small\",extra:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\",value:templateDir,onChange:e=>setTemplateDir(e.target.value),style:{width:200}}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:()=>fetchTemplates(true),loading:loading,children:\"\\u5237\\u65B0\"})]}),children:templates.length===0&&!loading?/*#__PURE__*/_jsx(Alert,{message:\"\\u6682\\u65E0\\u6A21\\u677F\",description:\"\\uD83D\\uDCC1 \\u8BF7\\u5148\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\\u5E76\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\\u83B7\\u53D6\\u6A21\\u677F\\u5217\\u8868\\uFF0C\\u6216\\u8005\\u5728\\u6A21\\u677F\\u751F\\u6210\\u9875\\u9762\\u521B\\u5EFA\\u65B0\\u6A21\\u677F\\u3002\",type:\"info\",showIcon:true}):/*#__PURE__*/_jsx(Table,{columns:templateColumns,dataSource:templates,rowKey:\"template_path\",loading:loading,pagination:{pageSize:10,showSizeChanger:true,showTotal:total=>`共 ${total} 个模板`},size:\"small\"})})},\"2\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(SendOutlined,{}),\"\\u6A21\\u677F\\u53D1\\u9001\"]}),children:/*#__PURE__*/_jsx(Card,{title:\"\\u53D1\\u9001\\u6A21\\u677F\\u5230\\u76EE\\u6807\\u670D\\u52A1\\u5668\",size:\"small\",children:/*#__PURE__*/_jsxs(Form,{form:sendForm,layout:\"vertical\",onFinish:sendTemplate,children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u6A21\\u677F\\u76EE\\u5F55\",name:\"send_template_directory\",rules:[{required:true,message:'请输入模板目录路径'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF0C\\u4F8B\\u5982: /data/output\",value:sendTemplateDir,onChange:e=>setSendTemplateDir(e.target.value),suffix:/*#__PURE__*/_jsx(Button,{type:\"text\",size:\"small\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),loading:sendTemplatesLoading,onClick:()=>fetchSendTemplates(true)})})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u9009\\u62E9\\u6A21\\u677F\",name:\"template_path\",rules:[{required:true,message:'请选择要发送的模板'}],children:/*#__PURE__*/_jsx(Select,{placeholder:\"\\u8BF7\\u5148\\u8F93\\u5165\\u6A21\\u677F\\u76EE\\u5F55\\uFF0C\\u7136\\u540E\\u9009\\u62E9\\u8981\\u53D1\\u9001\\u7684\\u6A21\\u677F\\u6587\\u4EF6\",showSearch:true,loading:sendTemplatesLoading,filterOption:(input,option)=>{var _option$children2;return option===null||option===void 0?void 0:(_option$children2=option.children)===null||_option$children2===void 0?void 0:_option$children2.toLowerCase().includes(input.toLowerCase());},notFoundContent:sendTemplatesLoading?/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px'},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\uD83D\\uDD04 \\u6B63\\u5728\\u52A0\\u8F7D\\u6A21\\u677F\\u5217\\u8868...\"})}):sendTemplates.length===0?/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px'},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\uD83D\\uDCC1 \\u6682\\u65E0\\u53EF\\u7528\\u6A21\\u677F\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u8BF7\\u68C0\\u67E5\\u76EE\\u5F55\\u8DEF\\u5F84\\u6216\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\"]})}):null,children:sendTemplates.map(template=>/*#__PURE__*/_jsx(Option,{value:template.template_path,children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:template.template_name}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:template.filename})]})},template.template_path))})}),/*#__PURE__*/_jsx(Divider,{children:\"\\u76EE\\u6807\\u670D\\u52A1\\u5668\\u914D\\u7F6E\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u76EE\\u6807\\u4E3B\\u673A\",name:\"target_host\",rules:[{required:true,message:'请输入目标主机地址'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982: *************\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7AEF\\u53E3\",name:\"target_port\",initialValue:22,rules:[{required:true,message:'请输入端口号'}],children:/*#__PURE__*/_jsx(Input,{type:\"number\",placeholder:\"22\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u7528\\u6237\\u540D\",name:\"target_username\",rules:[{required:true,message:'请输入用户名'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982: root\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5BC6\\u7801\",name:\"target_password\",rules:[{required:true,message:'请输入密码'}],children:/*#__PURE__*/_jsx(Input.Password,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"})})})]}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u76EE\\u6807\\u8DEF\\u5F84\",name:\"target_path\",rules:[{required:true,message:'请输入目标路径'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u4F8B\\u5982: /etc/cleantemplate/\"})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(CloudUploadOutlined,{}),children:\"\\u53D1\\u9001\\u6A21\\u677F\"})})]})})},\"3\")]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u6A21\\u677F\\u5185\\u5BB9\",open:viewModalVisible,onCancel:()=>setViewModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setViewModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:/*#__PURE__*/_jsx(TextArea,{value:templateContent,rows:20,readOnly:true,style:{fontFamily:'monospace'}})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u7F16\\u8F91\\u6A21\\u677F\\u5185\\u5BB9\",open:editModalVisible,onCancel:()=>setEditModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setEditModalVisible(false),children:\"\\u53D6\\u6D88\"},\"cancel\"),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:()=>updateForm.submit(),children:\"\\u4FDD\\u5B58\"},\"save\")],width:800,children:/*#__PURE__*/_jsx(Form,{form:updateForm,onFinish:updateTemplate,children:/*#__PURE__*/_jsx(Form.Item,{name:\"template_content\",rules:[{required:true,message:'模板内容不能为空'}],children:/*#__PURE__*/_jsx(TextArea,{rows:20,style:{fontFamily:'monospace'},placeholder:\"\\u8BF7\\u8F93\\u5165JSON\\u683C\\u5F0F\\u7684\\u6A21\\u677F\\u5185\\u5BB9\"})})})})]});};export default CleanTemplatePage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "<PERSON><PERSON>", "Tabs", "<PERSON><PERSON>", "Select", "Input", "Form", "message", "Table", "Space", "Modal", "Row", "Col", "Divider", "FileTextOutlined", "SettingOutlined", "SendOutlined", "ReloadOutlined", "EyeOutlined", "DownloadOutlined", "EditOutlined", "FolderOpenOutlined", "CloudUploadOutlined", "cleanTemplateAPI", "TextArea", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "TabPane", "Option", "CleanTemplatePage", "loading", "setLoading", "generateForm", "useForm", "sendForm", "updateForm", "resultFiles", "setResultFiles", "resultDir", "setResultDir", "lastGeneratedTemplate", "setLastGeneratedTemplate", "templates", "setTemplates", "templateDir", "setTemplateDir", "selectedTemplate", "setSelectedTemplate", "templateContent", "setTemplateContent", "editModalVisible", "setEditModalVisible", "viewModalVisible", "setViewModalVisible", "sendTemplateDir", "setSendTemplateDir", "sendTemplates", "setSendTemplates", "sendTemplatesLoading", "setSendTemplatesLoading", "fetchResultFiles", "showMessage", "arguments", "length", "undefined", "trim", "response", "listResultFiles", "data", "files", "success", "info", "error", "console", "_error$response", "_error$response$data", "detail", "fetchTemplates", "listTemplates", "log", "templatesData", "processedTemplates", "Array", "isArray", "map", "item", "index", "template_name", "replace", "filename", "template_path", "file_size", "created_time", "modified_time", "_item$filename", "_error$response2", "_error$response2$data", "fetchSendTemplates", "_item$filename2", "_error$response3", "_error$response3$data", "fetchTemplateContent", "templatePath", "getTemplateContent", "content", "JSON", "stringify", "_error$response4", "_error$response4$data", "generateTemplate", "values", "formData", "FormData", "append", "selected_result_file", "output_dir", "updated_thresholds", "children", "style", "fontWeight", "marginBottom", "fontSize", "color", "duration", "path", "name", "split", "pop", "time", "Date", "toLocaleString", "resetFields", "_error$response5", "_error$response5$data", "updateTemplate", "template_content", "_error$response6", "_error$response6$data", "sendTemplate", "target_host", "target_username", "target_password", "target_port", "toString", "target_path", "currentMode", "getFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template_selection_mode", "_error$response7", "_error$response7$data", "downloadTemplate", "templateName", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "_error$response8", "_error$response8$data", "viewTemplate", "editTemplate", "useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "setTimeout", "clearTimeout", "debouncedResultDir", "debouncedTemplateDir", "debouncedSendTemplateDir", "templateColumns", "title", "dataIndex", "key", "render", "record", "strong", "type", "_", "size", "icon", "onClick", "level", "defaultActiveKey", "marginTop", "tab", "form", "layout", "onFinish", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "onChange", "e", "target", "addonAfter", "showSearch", "notFoundContent", "filterOption", "input", "option", "_option$children", "toLowerCase", "includes", "file", "htmlType", "borderColor", "extra", "copyable", "width", "description", "showIcon", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total", "suffix", "_option$children2", "textAlign", "padding", "template", "initialValue", "Password", "open", "onCancel", "footer", "rows", "readOnly", "fontFamily", "submit"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/CleanTemplatePage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typo<PERSON>,\n  <PERSON>,\n  Alert,\n  Tabs,\n  Button,\n  Select,\n  Input,\n  Form,\n  message,\n  Table,\n  Space,\n  Modal,\n  Row,\n  Col,\n  Divider\n} from 'antd';\nimport {\n  FileTextOutlined,\n  SettingOutlined,\n  SendOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  DownloadOutlined,\n  EditOutlined,\n  FolderOpenOutlined,\n  CloudUploadOutlined\n} from '@ant-design/icons';\nimport { cleanTemplateAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { Option } = Select;\n\ninterface TemplateInfo {\n  template_name: string;\n  filename: string;\n  template_path: string;\n  file_size: number;\n  created_time: number;\n  modified_time: number;\n}\n\nconst CleanTemplatePage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [generateForm] = Form.useForm();\n  const [sendForm] = Form.useForm();\n  const [updateForm] = Form.useForm();\n\n  // 模板生成相关状态\n  const [resultFiles, setResultFiles] = useState<string[]>([]);\n  const [resultDir, setResultDir] = useState('');\n  const [lastGeneratedTemplate, setLastGeneratedTemplate] = useState<{\n    path: string;\n    name: string;\n    time: string;\n  } | null>(null);\n\n  // 模板管理相关状态\n  const [templates, setTemplates] = useState<TemplateInfo[]>([]);\n  const [templateDir, setTemplateDir] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('');\n  const [templateContent, setTemplateContent] = useState('');\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n\n  // 模板发送功能的状态\n  const [sendTemplateDir, setSendTemplateDir] = useState<string>('');\n  const [sendTemplates, setSendTemplates] = useState<TemplateInfo[]>([]);\n  const [sendTemplatesLoading, setSendTemplatesLoading] = useState(false);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async (showMessage = true) => {\n    if (!resultDir.trim()) {\n      setResultFiles([]);\n      return;\n    }\n\n    try {\n      const response = await cleanTemplateAPI.listResultFiles(resultDir);\n      if (response.data.files) {\n        setResultFiles(response.data.files || []);\n        if (showMessage && response.data.files.length > 0) {\n          message.success(`📁 找到 ${response.data.files.length} 个结果文件`);\n        } else if (showMessage && response.data.files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取结果文件失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取结果文件失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setResultFiles([]);\n    }\n  }, [resultDir]);\n\n  // 获取模板列表\n  const fetchTemplates = useCallback(async (showMessage = true) => {\n    if (!templateDir.trim()) {\n      setTemplates([]);\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(templateDir);\n      console.log('模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${templateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，直接使用\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${templateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的模板数据:', processedTemplates); // 调试信息\n        setTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [templateDir]);\n\n  // 获取发送模板列表\n  const fetchSendTemplates = useCallback(async (showMessage = true) => {\n    if (!sendTemplateDir.trim()) {\n      setSendTemplates([]);\n      return;\n    }\n\n    setSendTemplatesLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(sendTemplateDir);\n      console.log('发送模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('发送模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${sendTemplateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，确保所有必需字段存在\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${sendTemplateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的发送模板数据:', processedTemplates); // 调试信息\n        setSendTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个可发送的模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取发送模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取发送模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setSendTemplates([]);\n    } finally {\n      setSendTemplatesLoading(false);\n    }\n  }, [sendTemplateDir]);\n\n  // 获取模板内容\n  const fetchTemplateContent = async (templatePath: string) => {\n    try {\n      const response = await cleanTemplateAPI.getTemplateContent(templatePath);\n      if (response.data && response.data.content) {\n        setTemplateContent(JSON.stringify(response.data.content, null, 2));\n        return response.data.content;\n      }\n    } catch (error: any) {\n      console.error('获取模板内容失败:', error);\n      message.error(`❌ 获取模板内容失败: ${error.response?.data?.detail || error.message}`);\n    }\n    return null;\n  };\n\n  // 生成清洗模板\n  const generateTemplate = async (values: any) => {\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('results_file', `${resultDir}/${values.selected_result_file}`);\n      formData.append('output_folder', values.output_dir);\n      if (values.template_name) {\n        formData.append('template_name', values.template_name);\n      }\n\n      const response = await cleanTemplateAPI.generateTemplate(formData);\n      if (response.data) {\n        const { template_path, updated_thresholds } = response.data;\n\n        // 显示详细的成功信息\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 清洗模板生成成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 文件路径: {template_path}<br/>\n                🔧 更新阈值: {updated_thresholds} 个\n              </div>\n            </div>\n          ),\n          duration: 6, // 显示6秒\n        });\n\n        // 刷新模板列表\n        if (templateDir) {\n          fetchTemplates(true); // 显示刷新消息\n        }\n\n        // 设置最近生成的模板信息\n        setLastGeneratedTemplate({\n          path: template_path,\n          name: template_path.split('/').pop() || '未知模板',\n          time: new Date().toLocaleString()\n        });\n\n        // 重置表单\n        generateForm.resetFields();\n        setResultFiles([]); // 清空结果文件列表\n        setResultDir(''); // 清空目录输入\n      }\n    } catch (error: any) {\n      console.error('生成模板失败:', error);\n      message.error(`❌ 生成模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新模板内容\n  const updateTemplate = async (values: any) => {\n    try {\n      const formData = new FormData();\n      formData.append('template_path', selectedTemplate);\n      formData.append('template_content', values.template_content);\n\n      const response = await cleanTemplateAPI.updateTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success('✅ 模板内容更新成功');\n        setEditModalVisible(false);\n        fetchTemplates(true); // 刷新模板列表\n      }\n    } catch (error: any) {\n      console.error('更新模板失败:', error);\n      message.error(`❌ 更新模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 发送模板\n  const sendTemplate = async (values: any) => {\n    console.log('发送模板参数:', values); // 调试信息\n\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('template_path', values.template_path);\n      formData.append('target_host', values.target_host);\n      formData.append('target_username', values.target_username);\n      formData.append('target_password', values.target_password);\n      formData.append('target_port', values.target_port.toString());\n      formData.append('target_path', values.target_path);\n\n      const response = await cleanTemplateAPI.sendTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 模板发送成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 模板: {values.template_path.split('/').pop()}<br/>\n                🖥️ 目标: {values.target_host}:{values.target_port}<br/>\n                📂 路径: {values.target_path}\n              </div>\n            </div>\n          ),\n          duration: 6,\n        });\n\n        // 重置表单，但保留选择模式\n        const currentMode = sendForm.getFieldValue('template_selection_mode');\n        sendForm.resetFields();\n        sendForm.setFieldsValue({ template_selection_mode: currentMode });\n      }\n    } catch (error: any) {\n      console.error('发送模板失败:', error);\n      message.error(`❌ 发送模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载模板\n  const downloadTemplate = async (templatePath: string, templateName: string) => {\n    console.log('下载模板，路径:', templatePath, '名称:', templateName); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    try {\n      const response = await cleanTemplateAPI.downloadTemplate(templatePath);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', templateName || 'template.json');\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success('✅ 模板下载成功');\n    } catch (error: any) {\n      console.error('下载模板失败:', error);\n      message.error(`❌ 下载模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 查看模板详情\n  const viewTemplate = async (templatePath: string) => {\n    console.log('查看模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setViewModalVisible(true);\n    }\n  };\n\n  // 编辑模板\n  const editTemplate = async (templatePath: string) => {\n    console.log('编辑模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setSelectedTemplate(templatePath);\n      updateForm.setFieldsValue({ template_content: templateContent });\n      setEditModalVisible(true);\n    }\n  };\n\n  // 防抖hook\n  const useDebounce = (value: string, delay: number) => {\n    const [debouncedValue, setDebouncedValue] = useState(value);\n\n    useEffect(() => {\n      const handler = setTimeout(() => {\n        setDebouncedValue(value);\n      }, delay);\n\n      return () => {\n        clearTimeout(handler);\n      };\n    }, [value, delay]);\n\n    return debouncedValue;\n  };\n\n  // 防抖处理路径变化 - 增加延迟时间减少频繁请求\n  const debouncedResultDir = useDebounce(resultDir, 1500);\n  const debouncedTemplateDir = useDebounce(templateDir, 1500);\n  const debouncedSendTemplateDir = useDebounce(sendTemplateDir, 1500);\n\n  // 页面初始化时不自动获取数据，等待用户手动刷新\n  // useEffect(() => {\n  //   fetchResultFiles();\n  //   fetchTemplates();\n  // }, []);\n\n  // 防抖后的路径变化时静默获取数据（不显示消息）\n  useEffect(() => {\n    if (debouncedResultDir && debouncedResultDir.trim() !== '') {\n      fetchResultFiles(false); // 静默获取，不显示消息\n    } else {\n      setResultFiles([]); // 清空列表\n    }\n  }, [debouncedResultDir, fetchResultFiles]);\n\n  useEffect(() => {\n    if (debouncedTemplateDir && debouncedTemplateDir.trim() !== '') {\n      fetchTemplates(false); // 静默获取，不显示消息\n    } else {\n      setTemplates([]); // 清空列表\n    }\n  }, [debouncedTemplateDir, fetchTemplates]);\n\n  useEffect(() => {\n    if (debouncedSendTemplateDir && debouncedSendTemplateDir.trim() !== '') {\n      fetchSendTemplates(false); // 静默获取，不显示消息\n    } else {\n      setSendTemplates([]); // 清空列表\n    }\n  }, [debouncedSendTemplateDir, fetchSendTemplates]);\n\n  // 模板列表表格列定义\n  const templateColumns = [\n    {\n      title: '模板名称',\n      dataIndex: 'template_name',\n      key: 'template_name',\n      render: (name: string, record: TemplateInfo) => (\n        <div>\n          <Text strong>{name}</Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.filename}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: TemplateInfo) => {\n        console.log('表格行数据:', record); // 调试信息\n        return (\n          <Space>\n            <Button\n              type=\"primary\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => viewTemplate(record.template_path)}\n            >\n              查看\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => editTemplate(record.template_path)}\n            >\n              编辑\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<DownloadOutlined />}\n              onClick={() => downloadTemplate(record.template_path, record.filename)}\n            >\n              下载\n            </Button>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>清洗模板生成</Title>\n      <Text type=\"secondary\">\n        基于模型训练或预测结果，生成特定客户的流量清洗模板配置文件。\n      </Text>\n\n      <Tabs defaultActiveKey=\"1\" style={{ marginTop: 24 }}>\n        <TabPane tab={<span><FileTextOutlined />模板生成</span>} key=\"1\">\n          <Card title=\"生成清洗模板\" size=\"small\">\n            <Form\n              form={generateForm}\n              layout=\"vertical\"\n              onFinish={generateTemplate}\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"结果文件目录\"\n                    name=\"result_dir\"\n                    rules={[{ required: true, message: '请输入结果文件目录' }]}\n                  >\n                    <Input\n                      prefix={<FolderOpenOutlined />}\n                      placeholder=\"例如: /data/output\"\n                      value={resultDir}\n                      onChange={(e) => setResultDir(e.target.value)}\n                      addonAfter={\n                        <Button\n                          size=\"small\"\n                          icon={<ReloadOutlined />}\n                          onClick={() => fetchResultFiles(true)}\n                        >\n                          刷新\n                        </Button>\n                      }\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"选择结果文件\"\n                    name=\"selected_result_file\"\n                    rules={[{ required: true, message: '请选择结果文件' }]}\n                  >\n                    <Select\n                      placeholder={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新\" : \"选择要生成模板的结果文件\"}\n                      showSearch\n                      notFoundContent={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新获取文件列表\" : \"未找到匹配的文件\"}\n                      filterOption={(input, option) =>\n                        (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                      }\n                    >\n                      {resultFiles.map(file => (\n                        <Option key={file} value={file}>\n                          {file}\n                        </Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板输出目录\"\n                    name=\"output_dir\"\n                    rules={[{ required: true, message: '请输入输出目录' }]}\n                  >\n                    <Input placeholder=\"例如: /data/output\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板名称（可选）\"\n                    name=\"template_name\"\n                  >\n                    <Input placeholder=\"例如: customer_name\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<FileTextOutlined />}\n                >\n                  生成清洗模板\n                </Button>\n              </Form.Item>\n            </Form>\n\n            {/* 显示最近生成的模板信息 */}\n            {lastGeneratedTemplate && (\n              <Card\n                size=\"small\"\n                style={{ marginTop: 16, borderColor: '#52c41a' }}\n                title={\n                  <span style={{ color: '#52c41a' }}>\n                    <FileTextOutlined /> 最近生成的模板\n                  </span>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    type=\"text\"\n                    onClick={() => setLastGeneratedTemplate(null)}\n                  >\n                    ×\n                  </Button>\n                }\n              >\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Text strong>模板名称：</Text>\n                    <Text copyable>{lastGeneratedTemplate.name}</Text>\n                  </Col>\n                  <Col span={12}>\n                    <Text strong>生成时间：</Text>\n                    <Text>{lastGeneratedTemplate.time}</Text>\n                  </Col>\n                </Row>\n                <Row style={{ marginTop: 8 }}>\n                  <Col span={24}>\n                    <Text strong>文件路径：</Text>\n                    <Text copyable style={{ fontSize: '12px', color: '#666' }}>\n                      {lastGeneratedTemplate.path}\n                    </Text>\n                  </Col>\n                </Row>\n              </Card>\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SettingOutlined />模板管理</span>} key=\"2\">\n          <Card\n            title=\"模板列表\"\n            size=\"small\"\n            extra={\n              <Space>\n                <Input\n                  placeholder=\"模板目录路径\"\n                  value={templateDir}\n                  onChange={(e) => setTemplateDir(e.target.value)}\n                  style={{ width: 200 }}\n                />\n                <Button\n                  icon={<ReloadOutlined />}\n                  onClick={() => fetchTemplates(true)}\n                  loading={loading}\n                >\n                  刷新\n                </Button>\n              </Space>\n            }\n          >\n            {templates.length === 0 && !loading ? (\n              <Alert\n                message=\"暂无模板\"\n                description=\"📁 请先输入模板目录路径并点击刷新按钮获取模板列表，或者在模板生成页面创建新模板。\"\n                type=\"info\"\n                showIcon\n              />\n            ) : (\n              <Table\n                columns={templateColumns}\n                dataSource={templates}\n                rowKey=\"template_path\"\n                loading={loading}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: (total) => `共 ${total} 个模板`,\n                }}\n                size=\"small\"\n              />\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SendOutlined />模板发送</span>} key=\"3\">\n          <Card title=\"发送模板到目标服务器\" size=\"small\">\n            <Form\n              form={sendForm}\n              layout=\"vertical\"\n              onFinish={sendTemplate}\n            >\n              <Form.Item\n                label=\"模板目录\"\n                name=\"send_template_directory\"\n                rules={[{ required: true, message: '请输入模板目录路径' }]}\n              >\n                <Input\n                  placeholder=\"请输入模板目录路径，例如: /data/output\"\n                  value={sendTemplateDir}\n                  onChange={(e) => setSendTemplateDir(e.target.value)}\n                  suffix={\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      loading={sendTemplatesLoading}\n                      onClick={() => fetchSendTemplates(true)}\n                    />\n                  }\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"选择模板\"\n                name=\"template_path\"\n                rules={[{ required: true, message: '请选择要发送的模板' }]}\n              >\n                <Select\n                  placeholder=\"请先输入模板目录，然后选择要发送的模板文件\"\n                  showSearch\n                  loading={sendTemplatesLoading}\n                  filterOption={(input, option) =>\n                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                  }\n                  notFoundContent={\n                    sendTemplatesLoading ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">🔄 正在加载模板列表...</Text>\n                      </div>\n                    ) : sendTemplates.length === 0 ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">\n                          📁 暂无可用模板<br/>\n                          请检查目录路径或点击刷新按钮\n                        </Text>\n                      </div>\n                    ) : null\n                  }\n                >\n                  {sendTemplates.map(template => (\n                    <Option key={template.template_path} value={template.template_path}>\n                      <div>\n                        <Text strong>{template.template_name}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {template.filename}\n                        </Text>\n                      </div>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Divider>目标服务器配置</Divider>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"目标主机\"\n                    name=\"target_host\"\n                    rules={[{ required: true, message: '请输入目标主机地址' }]}\n                  >\n                    <Input placeholder=\"例如: *************\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"端口\"\n                    name=\"target_port\"\n                    initialValue={22}\n                    rules={[{ required: true, message: '请输入端口号' }]}\n                  >\n                    <Input type=\"number\" placeholder=\"22\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"用户名\"\n                    name=\"target_username\"\n                    rules={[{ required: true, message: '请输入用户名' }]}\n                  >\n                    <Input placeholder=\"例如: root\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"密码\"\n                    name=\"target_password\"\n                    rules={[{ required: true, message: '请输入密码' }]}\n                  >\n                    <Input.Password placeholder=\"请输入密码\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item\n                label=\"目标路径\"\n                name=\"target_path\"\n                rules={[{ required: true, message: '请输入目标路径' }]}\n              >\n                <Input placeholder=\"例如: /etc/cleantemplate/\" />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<CloudUploadOutlined />}\n                >\n                  发送模板\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </TabPane>\n      </Tabs>\n\n      {/* 查看模板内容弹窗 */}\n      <Modal\n        title=\"模板内容\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        <TextArea\n          value={templateContent}\n          rows={20}\n          readOnly\n          style={{ fontFamily: 'monospace' }}\n        />\n      </Modal>\n\n      {/* 编辑模板内容弹窗 */}\n      <Modal\n        title=\"编辑模板内容\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setEditModalVisible(false)}>\n            取消\n          </Button>,\n          <Button key=\"save\" type=\"primary\" onClick={() => updateForm.submit()}>\n            保存\n          </Button>\n        ]}\n        width={800}\n      >\n        <Form\n          form={updateForm}\n          onFinish={updateTemplate}\n        >\n          <Form.Item\n            name=\"template_content\"\n            rules={[{ required: true, message: '模板内容不能为空' }]}\n          >\n            <TextArea\n              rows={20}\n              style={{ fontFamily: 'monospace' }}\n              placeholder=\"请输入JSON格式的模板内容\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CleanTemplatePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,UAAU,CACVC,IAAI,CACJC,KAAK,CACLC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,OAAO,CACPC,KAAK,CACLC,KAAK,CACLC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,OAAO,KACF,MAAM,CACb,OACEC,gBAAgB,CAChBC,eAAe,CACfC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,YAAY,CACZC,kBAAkB,CAClBC,mBAAmB,KACd,mBAAmB,CAC1B,OAASC,gBAAgB,KAAQ,iBAAiB,CAClD,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG/B,UAAU,CAClC,KAAM,CAAEgC,OAAQ,CAAC,CAAG7B,IAAI,CACxB,KAAM,CAAE8B,MAAO,CAAC,CAAG5B,MAAM,CAWzB,KAAM,CAAA6B,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACwC,YAAY,CAAC,CAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC,CACrC,KAAM,CAACC,QAAQ,CAAC,CAAGhC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CACjC,KAAM,CAACE,UAAU,CAAC,CAAGjC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAEnC;AACA,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAG7C,QAAQ,CAAW,EAAE,CAAC,CAC5D,KAAM,CAAC8C,SAAS,CAAEC,YAAY,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgD,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGjD,QAAQ,CAIxD,IAAI,CAAC,CAEf;AACA,KAAM,CAACkD,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAiB,EAAE,CAAC,CAC9D,KAAM,CAACoD,WAAW,CAAEC,cAAc,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvD,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAACwD,eAAe,CAAEC,kBAAkB,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACA,KAAM,CAAC8D,eAAe,CAAEC,kBAAkB,CAAC,CAAG/D,QAAQ,CAAS,EAAE,CAAC,CAClE,KAAM,CAACgE,aAAa,CAAEC,gBAAgB,CAAC,CAAGjE,QAAQ,CAAiB,EAAE,CAAC,CACtE,KAAM,CAACkE,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAEvE;AACA,KAAM,CAAAoE,gBAAgB,CAAGlE,WAAW,CAAC,gBAA8B,IAAvB,CAAAmE,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC5D,GAAI,CAACxB,SAAS,CAAC2B,IAAI,CAAC,CAAC,CAAE,CACrB5B,cAAc,CAAC,EAAE,CAAC,CAClB,OACF,CAEA,GAAI,CACF,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAACgD,eAAe,CAAC7B,SAAS,CAAC,CAClE,GAAI4B,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAE,CACvBhC,cAAc,CAAC6B,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAI,EAAE,CAAC,CACzC,GAAIR,WAAW,EAAIK,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACN,MAAM,CAAG,CAAC,CAAE,CACjD5D,OAAO,CAACmE,OAAO,CAAC,SAASJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACN,MAAM,QAAQ,CAAC,CAC9D,CAAC,IAAM,IAAIF,WAAW,EAAIK,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACN,MAAM,GAAK,CAAC,CAAE,CAC1D5D,OAAO,CAACoE,IAAI,CAAC,eAAe,CAAC,CAC/B,CACF,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,GAAIX,WAAW,CAAE,KAAAa,eAAA,CAAAC,oBAAA,CACfxE,OAAO,CAACqE,KAAK,CAAC,eAAe,EAAAE,eAAA,CAAAF,KAAK,CAACN,QAAQ,UAAAQ,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBN,IAAI,UAAAO,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC/E,CACAkC,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAC,CAAE,CAACC,SAAS,CAAC,CAAC,CAEf;AACA,KAAM,CAAAuC,cAAc,CAAGnF,WAAW,CAAC,gBAA8B,IAAvB,CAAAmE,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC1D,GAAI,CAAClB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAE,CACvBtB,YAAY,CAAC,EAAE,CAAC,CAChB,OACF,CAEAZ,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAmC,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAAC2D,aAAa,CAAClC,WAAW,CAAC,CAClE6B,OAAO,CAACM,GAAG,CAAC,YAAY,CAAEb,QAAQ,CAACE,IAAI,CAAC,CAAE;AAE1C,GAAIF,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAAC1B,SAAS,CAAE,CAC5C,KAAM,CAAAsC,aAAa,CAAGd,QAAQ,CAACE,IAAI,CAAC1B,SAAS,CAC7C+B,OAAO,CAACM,GAAG,CAAC,OAAO,CAAEC,aAAa,CAAC,CAAE;AAErC;AACA,GAAI,CAAAC,kBAAkB,CAAG,EAAE,CAC3B,GAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,CAAE,CAChCC,kBAAkB,CAAGD,aAAa,CAACI,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACtD,GAAI,MAAO,CAAAD,IAAI,GAAK,QAAQ,CAAE,CAC5B;AACA,MAAO,CACLE,aAAa,CAAEF,IAAI,CAACG,OAAO,CAAC,qBAAqB,CAAE,EAAE,CAAC,CACtDC,QAAQ,CAAEJ,IAAI,CACdK,aAAa,CAAE,GAAG9C,WAAW,IAAIyC,IAAI,EAAE,CACvCM,SAAS,CAAE,CAAC,CACZC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CACjB,CAAC,CACH,CAAC,IAAM,IAAI,MAAO,CAAAR,IAAI,GAAK,QAAQ,EAAIA,IAAI,GAAK,IAAI,CAAE,KAAAS,cAAA,CACpD;AACA,MAAO,CACLP,aAAa,CAAEF,IAAI,CAACE,aAAa,IAAAO,cAAA,CAAIT,IAAI,CAACI,QAAQ,UAAAK,cAAA,iBAAbA,cAAA,CAAeN,OAAO,CAAC,qBAAqB,CAAE,EAAE,CAAC,GAAI,KAAKF,KAAK,CAAG,CAAC,EAAE,CAC1GG,QAAQ,CAAEJ,IAAI,CAACI,QAAQ,EAAIJ,IAAI,CAACE,aAAa,EAAI,WAAWD,KAAK,CAAG,CAAC,OAAO,CAC5EI,aAAa,CAAEL,IAAI,CAACK,aAAa,EAAI,GAAG9C,WAAW,IAAIyC,IAAI,CAACI,QAAQ,EAAE,CACtEE,SAAS,CAAEN,IAAI,CAACM,SAAS,EAAI,CAAC,CAC9BC,YAAY,CAAEP,IAAI,CAACO,YAAY,EAAI,CAAC,CACpCC,aAAa,CAAER,IAAI,CAACQ,aAAa,EAAI,CACvC,CAAC,CACH,CACA,MAAO,CAAAR,IAAI,CACb,CAAC,CAAC,CACJ,CAEAZ,OAAO,CAACM,GAAG,CAAC,WAAW,CAAEE,kBAAkB,CAAC,CAAE;AAC9CtC,YAAY,CAACsC,kBAAkB,CAAC,CAEhC,GAAIpB,WAAW,EAAIoB,kBAAkB,CAAClB,MAAM,CAAG,CAAC,CAAE,CAChD5D,OAAO,CAACmE,OAAO,CAAC,SAASW,kBAAkB,CAAClB,MAAM,QAAQ,CAAC,CAC7D,CAAC,IAAM,IAAIF,WAAW,EAAIoB,kBAAkB,CAAClB,MAAM,GAAK,CAAC,CAAE,CACzD5D,OAAO,CAACoE,IAAI,CAAC,eAAe,CAAC,CAC/B,CACF,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,GAAIX,WAAW,CAAE,KAAAkC,gBAAA,CAAAC,qBAAA,CACf7F,OAAO,CAACqE,KAAK,CAAC,eAAe,EAAAuB,gBAAA,CAAAvB,KAAK,CAACN,QAAQ,UAAA6B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3B,IAAI,UAAA4B,qBAAA,iBAApBA,qBAAA,CAAsBpB,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC/E,CACAwC,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,OAAS,CACRZ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACa,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAqD,kBAAkB,CAAGvG,WAAW,CAAC,gBAA8B,IAAvB,CAAAmE,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC9D,GAAI,CAACR,eAAe,CAACW,IAAI,CAAC,CAAC,CAAE,CAC3BR,gBAAgB,CAAC,EAAE,CAAC,CACpB,OACF,CAEAE,uBAAuB,CAAC,IAAI,CAAC,CAC7B,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAAC2D,aAAa,CAACxB,eAAe,CAAC,CACtEmB,OAAO,CAACM,GAAG,CAAC,cAAc,CAAEb,QAAQ,CAACE,IAAI,CAAC,CAAE;AAE5C,GAAIF,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAAC1B,SAAS,CAAE,CAC5C,KAAM,CAAAsC,aAAa,CAAGd,QAAQ,CAACE,IAAI,CAAC1B,SAAS,CAC7C+B,OAAO,CAACM,GAAG,CAAC,SAAS,CAAEC,aAAa,CAAC,CAAE;AAEvC;AACA,GAAI,CAAAC,kBAAkB,CAAG,EAAE,CAC3B,GAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,CAAE,CAChCC,kBAAkB,CAAGD,aAAa,CAACI,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACtD,GAAI,MAAO,CAAAD,IAAI,GAAK,QAAQ,CAAE,CAC5B;AACA,MAAO,CACLE,aAAa,CAAEF,IAAI,CAACG,OAAO,CAAC,qBAAqB,CAAE,EAAE,CAAC,CACtDC,QAAQ,CAAEJ,IAAI,CACdK,aAAa,CAAE,GAAGpC,eAAe,IAAI+B,IAAI,EAAE,CAC3CM,SAAS,CAAE,CAAC,CACZC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CACjB,CAAC,CACH,CAAC,IAAM,IAAI,MAAO,CAAAR,IAAI,GAAK,QAAQ,EAAIA,IAAI,GAAK,IAAI,CAAE,KAAAa,eAAA,CACpD;AACA,MAAO,CACLX,aAAa,CAAEF,IAAI,CAACE,aAAa,IAAAW,eAAA,CAAIb,IAAI,CAACI,QAAQ,UAAAS,eAAA,iBAAbA,eAAA,CAAeV,OAAO,CAAC,qBAAqB,CAAE,EAAE,CAAC,GAAI,KAAKF,KAAK,CAAG,CAAC,EAAE,CAC1GG,QAAQ,CAAEJ,IAAI,CAACI,QAAQ,EAAIJ,IAAI,CAACE,aAAa,EAAI,WAAWD,KAAK,CAAG,CAAC,OAAO,CAC5EI,aAAa,CAAEL,IAAI,CAACK,aAAa,EAAI,GAAGpC,eAAe,IAAI+B,IAAI,CAACI,QAAQ,EAAE,CAC1EE,SAAS,CAAEN,IAAI,CAACM,SAAS,EAAI,CAAC,CAC9BC,YAAY,CAAEP,IAAI,CAACO,YAAY,EAAI,CAAC,CACpCC,aAAa,CAAER,IAAI,CAACQ,aAAa,EAAI,CACvC,CAAC,CACH,CACA,MAAO,CAAAR,IAAI,CACb,CAAC,CAAC,CACJ,CAEAZ,OAAO,CAACM,GAAG,CAAC,aAAa,CAAEE,kBAAkB,CAAC,CAAE;AAChDxB,gBAAgB,CAACwB,kBAAkB,CAAC,CAEpC,GAAIpB,WAAW,EAAIoB,kBAAkB,CAAClB,MAAM,CAAG,CAAC,CAAE,CAChD5D,OAAO,CAACmE,OAAO,CAAC,SAASW,kBAAkB,CAAClB,MAAM,YAAY,CAAC,CACjE,CAAC,IAAM,IAAIF,WAAW,EAAIoB,kBAAkB,CAAClB,MAAM,GAAK,CAAC,CAAE,CACzD5D,OAAO,CAACoE,IAAI,CAAC,eAAe,CAAC,CAC/B,CACF,CACF,CAAE,MAAOC,KAAU,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,GAAIX,WAAW,CAAE,KAAAsC,gBAAA,CAAAC,qBAAA,CACfjG,OAAO,CAACqE,KAAK,CAAC,iBAAiB,EAAA2B,gBAAA,CAAA3B,KAAK,CAACN,QAAQ,UAAAiC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB/B,IAAI,UAAAgC,qBAAA,iBAApBA,qBAAA,CAAsBxB,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CACjF,CACAsD,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,OAAS,CACRE,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAAE,CAACL,eAAe,CAAC,CAAC,CAErB;AACA,KAAM,CAAA+C,oBAAoB,CAAG,KAAO,CAAAC,YAAoB,EAAK,CAC3D,GAAI,CACF,KAAM,CAAApC,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAACoF,kBAAkB,CAACD,YAAY,CAAC,CACxE,GAAIpC,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACoC,OAAO,CAAE,CAC1CvD,kBAAkB,CAACwD,IAAI,CAACC,SAAS,CAACxC,QAAQ,CAACE,IAAI,CAACoC,OAAO,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAClE,MAAO,CAAAtC,QAAQ,CAACE,IAAI,CAACoC,OAAO,CAC9B,CACF,CAAE,MAAOhC,KAAU,CAAE,KAAAmC,gBAAA,CAAAC,qBAAA,CACnBnC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCrE,OAAO,CAACqE,KAAK,CAAC,eAAe,EAAAmC,gBAAA,CAAAnC,KAAK,CAACN,QAAQ,UAAAyC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBvC,IAAI,UAAAwC,qBAAA,iBAApBA,qBAAA,CAAsBhC,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC/E,CACA,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAA0G,gBAAgB,CAAG,KAAO,CAAAC,MAAW,EAAK,CAC9C/E,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAgF,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,cAAc,CAAE,GAAG3E,SAAS,IAAIwE,MAAM,CAACI,oBAAoB,EAAE,CAAC,CAC9EH,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEH,MAAM,CAACK,UAAU,CAAC,CACnD,GAAIL,MAAM,CAACvB,aAAa,CAAE,CACxBwB,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEH,MAAM,CAACvB,aAAa,CAAC,CACxD,CAEA,KAAM,CAAArB,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAAC0F,gBAAgB,CAACE,QAAQ,CAAC,CAClE,GAAI7C,QAAQ,CAACE,IAAI,CAAE,CACjB,KAAM,CAAEsB,aAAa,CAAE0B,kBAAmB,CAAC,CAAGlD,QAAQ,CAACE,IAAI,CAE3D;AACAjE,OAAO,CAACmE,OAAO,CAAC,CACdkC,OAAO,cACLhF,KAAA,QAAA6F,QAAA,eACE/F,IAAA,QAAKgG,KAAK,CAAE,CAAEC,UAAU,CAAE,MAAM,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,qEAErD,CAAK,CAAC,cACN7F,KAAA,QAAK8F,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,EAAC,yCACtC,CAAC3B,aAAa,cAACpE,IAAA,QAAI,CAAC,0CACpB,CAAC8F,kBAAkB,CAAC,SAC/B,EAAK,CAAC,EACH,CACN,CACDO,QAAQ,CAAE,CAAG;AACf,CAAC,CAAC,CAEF;AACA,GAAI/E,WAAW,CAAE,CACfiC,cAAc,CAAC,IAAI,CAAC,CAAE;AACxB,CAEA;AACApC,wBAAwB,CAAC,CACvBmF,IAAI,CAAElC,aAAa,CACnBmC,IAAI,CAAEnC,aAAa,CAACoC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,EAAI,MAAM,CAC9CC,IAAI,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAClC,CAAC,CAAC,CAEF;AACAlG,YAAY,CAACmG,WAAW,CAAC,CAAC,CAC1B9F,cAAc,CAAC,EAAE,CAAC,CAAE;AACpBE,YAAY,CAAC,EAAE,CAAC,CAAE;AACpB,CACF,CAAE,MAAOiC,KAAU,CAAE,KAAA4D,gBAAA,CAAAC,qBAAA,CACnB5D,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrE,OAAO,CAACqE,KAAK,CAAC,aAAa,EAAA4D,gBAAA,CAAA5D,KAAK,CAACN,QAAQ,UAAAkE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBhE,IAAI,UAAAiE,qBAAA,iBAApBA,qBAAA,CAAsBzD,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC7E,CAAC,OAAS,CACR4B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuG,cAAc,CAAG,KAAO,CAAAxB,MAAW,EAAK,CAC5C,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEnE,gBAAgB,CAAC,CAClDiE,QAAQ,CAACE,MAAM,CAAC,kBAAkB,CAAEH,MAAM,CAACyB,gBAAgB,CAAC,CAE5D,KAAM,CAAArE,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAACmH,cAAc,CAACvB,QAAQ,CAAC,CAChE,GAAI7C,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACjE,OAAO,CAAE,CAC1CA,OAAO,CAACmE,OAAO,CAAC,YAAY,CAAC,CAC7BnB,mBAAmB,CAAC,KAAK,CAAC,CAC1B0B,cAAc,CAAC,IAAI,CAAC,CAAE;AACxB,CACF,CAAE,MAAOL,KAAU,CAAE,KAAAgE,gBAAA,CAAAC,qBAAA,CACnBhE,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrE,OAAO,CAACqE,KAAK,CAAC,aAAa,EAAAgE,gBAAA,CAAAhE,KAAK,CAACN,QAAQ,UAAAsE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBpE,IAAI,UAAAqE,qBAAA,iBAApBA,qBAAA,CAAsB7D,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC7E,CACF,CAAC,CAED;AACA,KAAM,CAAAuI,YAAY,CAAG,KAAO,CAAA5B,MAAW,EAAK,CAC1CrC,OAAO,CAACM,GAAG,CAAC,SAAS,CAAE+B,MAAM,CAAC,CAAE;AAEhC/E,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAgF,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEH,MAAM,CAACpB,aAAa,CAAC,CACtDqB,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEH,MAAM,CAAC6B,WAAW,CAAC,CAClD5B,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEH,MAAM,CAAC8B,eAAe,CAAC,CAC1D7B,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEH,MAAM,CAAC+B,eAAe,CAAC,CAC1D9B,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEH,MAAM,CAACgC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC,CAC7DhC,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEH,MAAM,CAACkC,WAAW,CAAC,CAElD,KAAM,CAAA9E,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAACuH,YAAY,CAAC3B,QAAQ,CAAC,CAC9D,GAAI7C,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACjE,OAAO,CAAE,CAC1CA,OAAO,CAACmE,OAAO,CAAC,CACdkC,OAAO,cACLhF,KAAA,QAAA6F,QAAA,eACE/F,IAAA,QAAKgG,KAAK,CAAE,CAAEC,UAAU,CAAE,MAAM,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,yDAErD,CAAK,CAAC,cACN7F,KAAA,QAAK8F,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,EAAC,6BACxC,CAACP,MAAM,CAACpB,aAAa,CAACoC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAACzG,IAAA,QAAI,CAAC,oCAC3C,CAACwF,MAAM,CAAC6B,WAAW,CAAC,GAAC,CAAC7B,MAAM,CAACgC,WAAW,cAACxH,IAAA,QAAI,CAAC,8BAC/C,CAACwF,MAAM,CAACkC,WAAW,EACvB,CAAC,EACH,CACN,CACDrB,QAAQ,CAAE,CACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsB,WAAW,CAAG/G,QAAQ,CAACgH,aAAa,CAAC,yBAAyB,CAAC,CACrEhH,QAAQ,CAACiG,WAAW,CAAC,CAAC,CACtBjG,QAAQ,CAACiH,cAAc,CAAC,CAAEC,uBAAuB,CAAEH,WAAY,CAAC,CAAC,CACnE,CACF,CAAE,MAAOzE,KAAU,CAAE,KAAA6E,gBAAA,CAAAC,qBAAA,CACnB7E,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrE,OAAO,CAACqE,KAAK,CAAC,aAAa,EAAA6E,gBAAA,CAAA7E,KAAK,CAACN,QAAQ,UAAAmF,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBjF,IAAI,UAAAkF,qBAAA,iBAApBA,qBAAA,CAAsB1E,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC7E,CAAC,OAAS,CACR4B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAwH,gBAAgB,CAAG,KAAAA,CAAOjD,YAAoB,CAAEkD,YAAoB,GAAK,CAC7E/E,OAAO,CAACM,GAAG,CAAC,UAAU,CAAEuB,YAAY,CAAE,KAAK,CAAEkD,YAAY,CAAC,CAAE;AAC5D,GAAI,CAAClD,YAAY,EAAIA,YAAY,GAAK,WAAW,CAAE,CACjDnG,OAAO,CAACqE,KAAK,CAAC,QAAQ,CAAC,CACvB,OACF,CACA,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAA/C,gBAAgB,CAACoI,gBAAgB,CAACjD,YAAY,CAAC,CAEtE;AACA,KAAM,CAAAmD,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC3F,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAA0F,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAEV,YAAY,EAAI,eAAe,CAAC,CAC9DO,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CACbZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC,CAE/BtJ,OAAO,CAACmE,OAAO,CAAC,UAAU,CAAC,CAC7B,CAAE,MAAOE,KAAU,CAAE,KAAAgG,gBAAA,CAAAC,qBAAA,CACnBhG,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BrE,OAAO,CAACqE,KAAK,CAAC,aAAa,EAAAgG,gBAAA,CAAAhG,KAAK,CAACN,QAAQ,UAAAsG,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBpG,IAAI,UAAAqG,qBAAA,iBAApBA,qBAAA,CAAsB7F,MAAM,GAAIJ,KAAK,CAACrE,OAAO,EAAE,CAAC,CAC7E,CACF,CAAC,CAED;AACA,KAAM,CAAAuK,YAAY,CAAG,KAAO,CAAApE,YAAoB,EAAK,CACnD7B,OAAO,CAACM,GAAG,CAAC,UAAU,CAAEuB,YAAY,CAAC,CAAE;AACvC,GAAI,CAACA,YAAY,EAAIA,YAAY,GAAK,WAAW,CAAE,CACjDnG,OAAO,CAACqE,KAAK,CAAC,QAAQ,CAAC,CACvB,OACF,CACA,KAAM,CAAAgC,OAAO,CAAG,KAAM,CAAAH,oBAAoB,CAACC,YAAY,CAAC,CACxD,GAAIE,OAAO,CAAE,CACXnD,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAsH,YAAY,CAAG,KAAO,CAAArE,YAAoB,EAAK,CACnD7B,OAAO,CAACM,GAAG,CAAC,UAAU,CAAEuB,YAAY,CAAC,CAAE;AACvC,GAAI,CAACA,YAAY,EAAIA,YAAY,GAAK,WAAW,CAAE,CACjDnG,OAAO,CAACqE,KAAK,CAAC,QAAQ,CAAC,CACvB,OACF,CACA,KAAM,CAAAgC,OAAO,CAAG,KAAM,CAAAH,oBAAoB,CAACC,YAAY,CAAC,CACxD,GAAIE,OAAO,CAAE,CACXzD,mBAAmB,CAACuD,YAAY,CAAC,CACjCnE,UAAU,CAACgH,cAAc,CAAC,CAAEZ,gBAAgB,CAAEvF,eAAgB,CAAC,CAAC,CAChEG,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAyH,WAAW,CAAGA,CAACC,KAAa,CAAEC,KAAa,GAAK,CACpD,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGxL,QAAQ,CAACqL,KAAK,CAAC,CAE3DpL,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwL,OAAO,CAAGC,UAAU,CAAC,IAAM,CAC/BF,iBAAiB,CAACH,KAAK,CAAC,CAC1B,CAAC,CAAEC,KAAK,CAAC,CAET,MAAO,IAAM,CACXK,YAAY,CAACF,OAAO,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,CAACJ,KAAK,CAAEC,KAAK,CAAC,CAAC,CAElB,MAAO,CAAAC,cAAc,CACvB,CAAC,CAED;AACA,KAAM,CAAAK,kBAAkB,CAAGR,WAAW,CAACtI,SAAS,CAAE,IAAI,CAAC,CACvD,KAAM,CAAA+I,oBAAoB,CAAGT,WAAW,CAAChI,WAAW,CAAE,IAAI,CAAC,CAC3D,KAAM,CAAA0I,wBAAwB,CAAGV,WAAW,CAACtH,eAAe,CAAE,IAAI,CAAC,CAEnE;AACA;AACA;AACA;AACA;AAEA;AACA7D,SAAS,CAAC,IAAM,CACd,GAAI2L,kBAAkB,EAAIA,kBAAkB,CAACnH,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC1DL,gBAAgB,CAAC,KAAK,CAAC,CAAE;AAC3B,CAAC,IAAM,CACLvB,cAAc,CAAC,EAAE,CAAC,CAAE;AACtB,CACF,CAAC,CAAE,CAAC+I,kBAAkB,CAAExH,gBAAgB,CAAC,CAAC,CAE1CnE,SAAS,CAAC,IAAM,CACd,GAAI4L,oBAAoB,EAAIA,oBAAoB,CAACpH,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9DY,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,IAAM,CACLlC,YAAY,CAAC,EAAE,CAAC,CAAE;AACpB,CACF,CAAC,CAAE,CAAC0I,oBAAoB,CAAExG,cAAc,CAAC,CAAC,CAE1CpF,SAAS,CAAC,IAAM,CACd,GAAI6L,wBAAwB,EAAIA,wBAAwB,CAACrH,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACtEgC,kBAAkB,CAAC,KAAK,CAAC,CAAE;AAC7B,CAAC,IAAM,CACLxC,gBAAgB,CAAC,EAAE,CAAC,CAAE;AACxB,CACF,CAAC,CAAE,CAAC6H,wBAAwB,CAAErF,kBAAkB,CAAC,CAAC,CAElD;AACA,KAAM,CAAAsF,eAAe,CAAG,CACtB,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,eAAe,CAC1BC,GAAG,CAAE,eAAe,CACpBC,MAAM,CAAEA,CAAC9D,IAAY,CAAE+D,MAAoB,gBACzCpK,KAAA,QAAA6F,QAAA,eACE/F,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAAxE,QAAA,CAAEQ,IAAI,CAAO,CAAC,cAC1BvG,IAAA,QAAK,CAAC,cACNA,IAAA,CAACI,IAAI,EAACoK,IAAI,CAAC,WAAW,CAACxE,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAChDuE,MAAM,CAACnG,QAAQ,CACZ,CAAC,EACJ,CAET,CAAC,CACD,CACE+F,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,SAAS,CACdC,MAAM,CAAEA,CAACI,CAAM,CAAEH,MAAoB,GAAK,CACxCnH,OAAO,CAACM,GAAG,CAAC,QAAQ,CAAE6G,MAAM,CAAC,CAAE;AAC/B,mBACEpK,KAAA,CAACnB,KAAK,EAAAgH,QAAA,eACJ/F,IAAA,CAACvB,MAAM,EACL+L,IAAI,CAAC,SAAS,CACdE,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE3K,IAAA,CAACR,WAAW,GAAE,CAAE,CACtBoL,OAAO,CAAEA,CAAA,GAAMxB,YAAY,CAACkB,MAAM,CAAClG,aAAa,CAAE,CAAA2B,QAAA,CACnD,cAED,CAAQ,CAAC,cACT/F,IAAA,CAACvB,MAAM,EACLiM,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE3K,IAAA,CAACN,YAAY,GAAE,CAAE,CACvBkL,OAAO,CAAEA,CAAA,GAAMvB,YAAY,CAACiB,MAAM,CAAClG,aAAa,CAAE,CAAA2B,QAAA,CACnD,cAED,CAAQ,CAAC,cACT/F,IAAA,CAACvB,MAAM,EACLiM,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE3K,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAC3BmL,OAAO,CAAEA,CAAA,GAAM3C,gBAAgB,CAACqC,MAAM,CAAClG,aAAa,CAAEkG,MAAM,CAACnG,QAAQ,CAAE,CAAA4B,QAAA,CACxE,cAED,CAAQ,CAAC,EACJ,CAAC,CAEZ,CACF,CAAC,CACF,CAED,mBACE7F,KAAA,QAAA6F,QAAA,eACE/F,IAAA,CAACG,KAAK,EAAC0K,KAAK,CAAE,CAAE,CAAC7E,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAH,QAAA,CAAC,sCAAM,CAAO,CAAC,cAClG/F,IAAA,CAACI,IAAI,EAACoK,IAAI,CAAC,WAAW,CAAAzE,QAAA,CAAC,sLAEvB,CAAM,CAAC,cAEP7F,KAAA,CAAC1B,IAAI,EAACsM,gBAAgB,CAAC,GAAG,CAAC9E,KAAK,CAAE,CAAE+E,SAAS,CAAE,EAAG,CAAE,CAAAhF,QAAA,eAClD/F,IAAA,CAACK,OAAO,EAAC2K,GAAG,cAAE9K,KAAA,SAAA6F,QAAA,eAAM/F,IAAA,CAACZ,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAA2G,QAAA,cAClD7F,KAAA,CAAC5B,IAAI,EAAC4L,KAAK,CAAC,sCAAQ,CAACQ,IAAI,CAAC,OAAO,CAAA3E,QAAA,eAC/B7F,KAAA,CAACtB,IAAI,EACHqM,IAAI,CAAEvK,YAAa,CACnBwK,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAE5F,gBAAiB,CAAAQ,QAAA,eAE3B7F,KAAA,CAACjB,GAAG,EAACmM,MAAM,CAAE,EAAG,CAAArF,QAAA,eACd/F,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,sCAAQ,CACdhF,IAAI,CAAC,YAAY,CACjBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,WAAY,CAAC,CAAE,CAAAkH,QAAA,cAElD/F,IAAA,CAACrB,KAAK,EACJ+M,MAAM,cAAE1L,IAAA,CAACL,kBAAkB,GAAE,CAAE,CAC/BgM,WAAW,CAAC,4BAAkB,CAC9BpC,KAAK,CAAEvI,SAAU,CACjB4K,QAAQ,CAAGC,CAAC,EAAK5K,YAAY,CAAC4K,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE,CAC9CwC,UAAU,cACR/L,IAAA,CAACvB,MAAM,EACLiM,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE3K,IAAA,CAACT,cAAc,GAAE,CAAE,CACzBqL,OAAO,CAAEA,CAAA,GAAMtI,gBAAgB,CAAC,IAAI,CAAE,CAAAyD,QAAA,CACvC,cAED,CAAQ,CACT,CACF,CAAC,CACO,CAAC,CACT,CAAC,cACN/F,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,sCAAQ,CACdhF,IAAI,CAAC,sBAAsB,CAC3BiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkH,QAAA,cAEhD/F,IAAA,CAACtB,MAAM,EACLiN,WAAW,CAAE7K,WAAW,CAAC2B,MAAM,GAAK,CAAC,CAAG,eAAe,CAAG,cAAe,CACzEuJ,UAAU,MACVC,eAAe,CAAEnL,WAAW,CAAC2B,MAAM,GAAK,CAAC,CAAG,qBAAqB,CAAG,UAAW,CAC/EyJ,YAAY,CAAEA,CAACC,KAAK,CAAEC,MAAM,QAAAC,gBAAA,OACzB,CAAAD,MAAM,SAANA,MAAM,kBAAAC,gBAAA,CAAND,MAAM,CAAErG,QAAQ,UAAAsG,gBAAA,iBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EACrF,CAAAvG,QAAA,CAEAjF,WAAW,CAACgD,GAAG,CAAC0I,IAAI,eACnBxM,IAAA,CAACM,MAAM,EAAYiJ,KAAK,CAAEiD,IAAK,CAAAzG,QAAA,CAC5ByG,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,cAENtM,KAAA,CAACjB,GAAG,EAACmM,MAAM,CAAE,EAAG,CAAArF,QAAA,eACd/F,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,sCAAQ,CACdhF,IAAI,CAAC,YAAY,CACjBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkH,QAAA,cAEhD/F,IAAA,CAACrB,KAAK,EAACgN,WAAW,CAAC,4BAAkB,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,cACN3L,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,kDAAU,CAChBhF,IAAI,CAAC,eAAe,CAAAR,QAAA,cAEpB/F,IAAA,CAACrB,KAAK,EAACgN,WAAW,CAAC,6BAAmB,CAAE,CAAC,CAChC,CAAC,CACT,CAAC,EACH,CAAC,cAEN3L,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EAAAvF,QAAA,cACR/F,IAAA,CAACvB,MAAM,EACL+L,IAAI,CAAC,SAAS,CACdiC,QAAQ,CAAC,QAAQ,CACjBjM,OAAO,CAAEA,OAAQ,CACjBmK,IAAI,cAAE3K,IAAA,CAACZ,gBAAgB,GAAE,CAAE,CAAA2G,QAAA,CAC5B,sCAED,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,CAGN7E,qBAAqB,eACpBhB,KAAA,CAAC5B,IAAI,EACHoM,IAAI,CAAC,OAAO,CACZ1E,KAAK,CAAE,CAAE+E,SAAS,CAAE,EAAE,CAAE2B,WAAW,CAAE,SAAU,CAAE,CACjDxC,KAAK,cACHhK,KAAA,SAAM8F,KAAK,CAAE,CAAEI,KAAK,CAAE,SAAU,CAAE,CAAAL,QAAA,eAChC/F,IAAA,CAACZ,gBAAgB,GAAE,CAAC,8CACtB,EAAM,CACP,CACDuN,KAAK,cACH3M,IAAA,CAACvB,MAAM,EACLiM,IAAI,CAAC,OAAO,CACZF,IAAI,CAAC,MAAM,CACXI,OAAO,CAAEA,CAAA,GAAMzJ,wBAAwB,CAAC,IAAI,CAAE,CAAA4E,QAAA,CAC/C,MAED,CAAQ,CACT,CAAAA,QAAA,eAED7F,KAAA,CAACjB,GAAG,EAACmM,MAAM,CAAE,EAAG,CAAArF,QAAA,eACd7F,KAAA,CAAChB,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,eACZ/F,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAAxE,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB/F,IAAA,CAACI,IAAI,EAACwM,QAAQ,MAAA7G,QAAA,CAAE7E,qBAAqB,CAACqF,IAAI,CAAO,CAAC,EAC/C,CAAC,cACNrG,KAAA,CAAChB,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,eACZ/F,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAAxE,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB/F,IAAA,CAACI,IAAI,EAAA2F,QAAA,CAAE7E,qBAAqB,CAACwF,IAAI,CAAO,CAAC,EACtC,CAAC,EACH,CAAC,cACN1G,IAAA,CAACf,GAAG,EAAC+G,KAAK,CAAE,CAAE+E,SAAS,CAAE,CAAE,CAAE,CAAAhF,QAAA,cAC3B7F,KAAA,CAAChB,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,eACZ/F,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAAxE,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB/F,IAAA,CAACI,IAAI,EAACwM,QAAQ,MAAC5G,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,CACvD7E,qBAAqB,CAACoF,IAAI,CACvB,CAAC,EACJ,CAAC,CACH,CAAC,EACF,CACP,EACG,CAAC,EA/HgD,GAgIhD,CAAC,cAEVtG,IAAA,CAACK,OAAO,EAAC2K,GAAG,cAAE9K,KAAA,SAAA6F,QAAA,eAAM/F,IAAA,CAACX,eAAe,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAA0G,QAAA,cACjD/F,IAAA,CAAC1B,IAAI,EACH4L,KAAK,CAAC,0BAAM,CACZQ,IAAI,CAAC,OAAO,CACZiC,KAAK,cACHzM,KAAA,CAACnB,KAAK,EAAAgH,QAAA,eACJ/F,IAAA,CAACrB,KAAK,EACJgN,WAAW,CAAC,sCAAQ,CACpBpC,KAAK,CAAEjI,WAAY,CACnBsK,QAAQ,CAAGC,CAAC,EAAKtK,cAAc,CAACsK,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE,CAChDvD,KAAK,CAAE,CAAE6G,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,cACF7M,IAAA,CAACvB,MAAM,EACLkM,IAAI,cAAE3K,IAAA,CAACT,cAAc,GAAE,CAAE,CACzBqL,OAAO,CAAEA,CAAA,GAAMrH,cAAc,CAAC,IAAI,CAAE,CACpC/C,OAAO,CAAEA,OAAQ,CAAAuF,QAAA,CAClB,cAED,CAAQ,CAAC,EACJ,CACR,CAAAA,QAAA,CAEA3E,SAAS,CAACqB,MAAM,GAAK,CAAC,EAAI,CAACjC,OAAO,cACjCR,IAAA,CAACzB,KAAK,EACJM,OAAO,CAAC,0BAAM,CACdiO,WAAW,CAAC,yPAA4C,CACxDtC,IAAI,CAAC,MAAM,CACXuC,QAAQ,MACT,CAAC,cAEF/M,IAAA,CAAClB,KAAK,EACJkO,OAAO,CAAE/C,eAAgB,CACzBgD,UAAU,CAAE7L,SAAU,CACtB8L,MAAM,CAAC,eAAe,CACtB1M,OAAO,CAAEA,OAAQ,CACjB2M,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGC,KAAK,EAAK,KAAKA,KAAK,MAClC,CAAE,CACF7C,IAAI,CAAC,OAAO,CACb,CACF,CACG,CAAC,EA3C+C,GA4C/C,CAAC,cAEV1K,IAAA,CAACK,OAAO,EAAC2K,GAAG,cAAE9K,KAAA,SAAA6F,QAAA,eAAM/F,IAAA,CAACV,YAAY,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAyG,QAAA,cAC9C/F,IAAA,CAAC1B,IAAI,EAAC4L,KAAK,CAAC,8DAAY,CAACQ,IAAI,CAAC,OAAO,CAAA3E,QAAA,cACnC7F,KAAA,CAACtB,IAAI,EACHqM,IAAI,CAAErK,QAAS,CACfsK,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAE/D,YAAa,CAAArB,QAAA,eAEvB/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZhF,IAAI,CAAC,yBAAyB,CAC9BiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,WAAY,CAAC,CAAE,CAAAkH,QAAA,cAElD/F,IAAA,CAACrB,KAAK,EACJgN,WAAW,CAAC,wFAA4B,CACxCpC,KAAK,CAAEvH,eAAgB,CACvB4J,QAAQ,CAAGC,CAAC,EAAK5J,kBAAkB,CAAC4J,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE,CACpDiE,MAAM,cACJxN,IAAA,CAACvB,MAAM,EACL+L,IAAI,CAAC,MAAM,CACXE,IAAI,CAAC,OAAO,CACZC,IAAI,cAAE3K,IAAA,CAACT,cAAc,GAAE,CAAE,CACzBiB,OAAO,CAAE4B,oBAAqB,CAC9BwI,OAAO,CAAEA,CAAA,GAAMjG,kBAAkB,CAAC,IAAI,CAAE,CACzC,CACF,CACF,CAAC,CACO,CAAC,cAEZ3E,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZhF,IAAI,CAAC,eAAe,CACpBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,WAAY,CAAC,CAAE,CAAAkH,QAAA,cAElD/F,IAAA,CAACtB,MAAM,EACLiN,WAAW,CAAC,gIAAuB,CACnCK,UAAU,MACVxL,OAAO,CAAE4B,oBAAqB,CAC9B8J,YAAY,CAAEA,CAACC,KAAK,CAAEC,MAAM,QAAAqB,iBAAA,OACzB,CAAArB,MAAM,SAANA,MAAM,kBAAAqB,iBAAA,CAANrB,MAAM,CAAErG,QAAQ,UAAA0H,iBAAA,iBAAjBA,iBAAA,CAAyCnB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EACrF,CACDL,eAAe,CACb7J,oBAAoB,cAClBpC,IAAA,QAAKgG,KAAK,CAAE,CAAE0H,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAA5H,QAAA,cACnD/F,IAAA,CAACI,IAAI,EAACoK,IAAI,CAAC,WAAW,CAAAzE,QAAA,CAAC,kEAAc,CAAM,CAAC,CACzC,CAAC,CACJ7D,aAAa,CAACO,MAAM,GAAK,CAAC,cAC5BzC,IAAA,QAAKgG,KAAK,CAAE,CAAE0H,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAA5H,QAAA,cACnD7F,KAAA,CAACE,IAAI,EAACoK,IAAI,CAAC,WAAW,CAAAzE,QAAA,EAAC,mDACZ,cAAA/F,IAAA,QAAI,CAAC,uFAEhB,EAAM,CAAC,CACJ,CAAC,CACJ,IACL,CAAA+F,QAAA,CAEA7D,aAAa,CAAC4B,GAAG,CAAC8J,QAAQ,eACzB5N,IAAA,CAACM,MAAM,EAA8BiJ,KAAK,CAAEqE,QAAQ,CAACxJ,aAAc,CAAA2B,QAAA,cACjE7F,KAAA,QAAA6F,QAAA,eACE/F,IAAA,CAACI,IAAI,EAACmK,MAAM,MAAAxE,QAAA,CAAE6H,QAAQ,CAAC3J,aAAa,CAAO,CAAC,cAC5CjE,IAAA,QAAK,CAAC,cACNA,IAAA,CAACI,IAAI,EAACoK,IAAI,CAAC,WAAW,CAACxE,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAChD6H,QAAQ,CAACzJ,QAAQ,CACd,CAAC,EACJ,CAAC,EAPKyJ,QAAQ,CAACxJ,aAQd,CACT,CAAC,CACI,CAAC,CACA,CAAC,cAEZpE,IAAA,CAACb,OAAO,EAAA4G,QAAA,CAAC,4CAAO,CAAS,CAAC,cAE1B7F,KAAA,CAACjB,GAAG,EAACmM,MAAM,CAAE,EAAG,CAAArF,QAAA,eACd/F,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZhF,IAAI,CAAC,aAAa,CAClBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,WAAY,CAAC,CAAE,CAAAkH,QAAA,cAElD/F,IAAA,CAACrB,KAAK,EAACgN,WAAW,CAAC,6BAAmB,CAAE,CAAC,CAChC,CAAC,CACT,CAAC,cACN3L,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,cAAI,CACVhF,IAAI,CAAC,aAAa,CAClBsH,YAAY,CAAE,EAAG,CACjBrC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAkH,QAAA,cAE/C/F,IAAA,CAACrB,KAAK,EAAC6L,IAAI,CAAC,QAAQ,CAACmB,WAAW,CAAC,IAAI,CAAE,CAAC,CAC/B,CAAC,CACT,CAAC,EACH,CAAC,cAENzL,KAAA,CAACjB,GAAG,EAACmM,MAAM,CAAE,EAAG,CAAArF,QAAA,eACd/F,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,oBAAK,CACXhF,IAAI,CAAC,iBAAiB,CACtBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,QAAS,CAAC,CAAE,CAAAkH,QAAA,cAE/C/F,IAAA,CAACrB,KAAK,EAACgN,WAAW,CAAC,oBAAU,CAAE,CAAC,CACvB,CAAC,CACT,CAAC,cACN3L,IAAA,CAACd,GAAG,EAACmM,IAAI,CAAE,EAAG,CAAAtF,QAAA,cACZ/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,cAAI,CACVhF,IAAI,CAAC,iBAAiB,CACtBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,OAAQ,CAAC,CAAE,CAAAkH,QAAA,cAE9C/F,IAAA,CAACrB,KAAK,CAACmP,QAAQ,EAACnC,WAAW,CAAC,gCAAO,CAAE,CAAC,CAC7B,CAAC,CACT,CAAC,EACH,CAAC,cAEN3L,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZhF,IAAI,CAAC,aAAa,CAClBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkH,QAAA,cAEhD/F,IAAA,CAACrB,KAAK,EAACgN,WAAW,CAAC,mCAAyB,CAAE,CAAC,CACtC,CAAC,cAEZ3L,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EAAAvF,QAAA,cACR/F,IAAA,CAACvB,MAAM,EACL+L,IAAI,CAAC,SAAS,CACdiC,QAAQ,CAAC,QAAQ,CACjBjM,OAAO,CAAEA,OAAQ,CACjBmK,IAAI,cAAE3K,IAAA,CAACJ,mBAAmB,GAAE,CAAE,CAAAmG,QAAA,CAC/B,0BAED,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,CACH,CAAC,EArI4C,GAsI5C,CAAC,EACN,CAAC,cAGP/F,IAAA,CAAChB,KAAK,EACJkL,KAAK,CAAC,0BAAM,CACZ6D,IAAI,CAAEjM,gBAAiB,CACvBkM,QAAQ,CAAEA,CAAA,GAAMjM,mBAAmB,CAAC,KAAK,CAAE,CAC3CkM,MAAM,CAAE,cACNjO,IAAA,CAACvB,MAAM,EAAamM,OAAO,CAAEA,CAAA,GAAM7I,mBAAmB,CAAC,KAAK,CAAE,CAAAgE,QAAA,CAAC,cAE/D,EAFY,OAEJ,CAAC,CACT,CACF8G,KAAK,CAAE,GAAI,CAAA9G,QAAA,cAEX/F,IAAA,CAACF,QAAQ,EACPyJ,KAAK,CAAE7H,eAAgB,CACvBwM,IAAI,CAAE,EAAG,CACTC,QAAQ,MACRnI,KAAK,CAAE,CAAEoI,UAAU,CAAE,WAAY,CAAE,CACpC,CAAC,CACG,CAAC,cAGRpO,IAAA,CAAChB,KAAK,EACJkL,KAAK,CAAC,sCAAQ,CACd6D,IAAI,CAAEnM,gBAAiB,CACvBoM,QAAQ,CAAEA,CAAA,GAAMnM,mBAAmB,CAAC,KAAK,CAAE,CAC3CoM,MAAM,CAAE,cACNjO,IAAA,CAACvB,MAAM,EAAcmM,OAAO,CAAEA,CAAA,GAAM/I,mBAAmB,CAAC,KAAK,CAAE,CAAAkE,QAAA,CAAC,cAEhE,EAFY,QAEJ,CAAC,cACT/F,IAAA,CAACvB,MAAM,EAAY+L,IAAI,CAAC,SAAS,CAACI,OAAO,CAAEA,CAAA,GAAM/J,UAAU,CAACwN,MAAM,CAAC,CAAE,CAAAtI,QAAA,CAAC,cAEtE,EAFY,MAEJ,CAAC,CACT,CACF8G,KAAK,CAAE,GAAI,CAAA9G,QAAA,cAEX/F,IAAA,CAACpB,IAAI,EACHqM,IAAI,CAAEpK,UAAW,CACjBsK,QAAQ,CAAEnE,cAAe,CAAAjB,QAAA,cAEzB/F,IAAA,CAACpB,IAAI,CAAC0M,IAAI,EACR/E,IAAI,CAAC,kBAAkB,CACvBiF,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE5M,OAAO,CAAE,UAAW,CAAC,CAAE,CAAAkH,QAAA,cAEjD/F,IAAA,CAACF,QAAQ,EACPoO,IAAI,CAAE,EAAG,CACTlI,KAAK,CAAE,CAAEoI,UAAU,CAAE,WAAY,CAAE,CACnCzC,WAAW,CAAC,kEAAgB,CAC7B,CAAC,CACO,CAAC,CACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAApL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}