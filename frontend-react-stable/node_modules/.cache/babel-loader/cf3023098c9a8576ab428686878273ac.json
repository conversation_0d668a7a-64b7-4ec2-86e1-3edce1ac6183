{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.formatDecimalParts = formatDecimalParts;\nfunction _default(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n} // Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\n\nfunction formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n\n  var i,\n    coefficient = x.slice(0, i); // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n\n  return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +x.slice(i + 1)];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "formatDecimalParts", "x", "Math", "abs", "round", "toLocaleString", "replace", "toString", "p", "i", "toExponential", "indexOf", "coefficient", "slice", "length"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatDecimal.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.formatDecimalParts = formatDecimalParts;\n\nfunction _default(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n} // Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\n\n\nfunction formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n\n  var i,\n      coefficient = x.slice(0, i); // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n\n  return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +x.slice(i + 1)];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,kBAAkB,GAAGA,kBAAkB;AAE/C,SAASD,QAAQA,CAACE,CAAC,EAAE;EACnB,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,GAAGC,IAAI,CAACE,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGA,CAAC,CAACI,cAAc,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAGL,CAAC,CAACM,QAAQ,CAAC,EAAE,CAAC;AACxG,CAAC,CAAC;AACF;AACA;;AAGA,SAASP,kBAAkBA,CAACC,CAAC,EAAEO,CAAC,EAAE;EAChC,IAAI,CAACC,CAAC,GAAG,CAACR,CAAC,GAAGO,CAAC,GAAGP,CAAC,CAACS,aAAa,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGP,CAAC,CAACS,aAAa,CAAC,CAAC,EAAEC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAE9F,IAAIF,CAAC;IACDG,WAAW,GAAGX,CAAC,CAACY,KAAK,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,CAAC;EACjC;;EAEA,OAAO,CAACG,WAAW,CAACE,MAAM,GAAG,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGD,WAAW,EAAE,CAACX,CAAC,CAACY,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAC;AACxG", "ignoreList": []}, "metadata": {}, "sourceType": "script"}