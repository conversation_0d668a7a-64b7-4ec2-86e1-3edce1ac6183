{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _none = _interopRequireDefault(require(\"./none.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  (0, _none.default)(series, order);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_none", "_interopRequireDefault", "require", "obj", "__esModule", "series", "order", "n", "length", "j", "s0", "m", "i", "y"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/offset/silhouette.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _none = _interopRequireDefault(require(\"./none.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series, order) {\n  if (!((n = series.length) > 0)) return;\n\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n\n  (0, _none.default)(series, order);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,KAAK,GAAGC,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,MAAM,EAAEC,KAAK,EAAE;EAC/B,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;EAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGL,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,EAAEI,CAAC,GAAGD,EAAE,CAACF,MAAM,EAAEC,CAAC,GAAGE,CAAC,EAAE,EAAEF,CAAC,EAAE;IACnE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAEC,CAAC,IAAIR,MAAM,CAACO,CAAC,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE5DC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIC,EAAE,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACI,CAAC,GAAG,CAAC;EAC/B;EAEA,CAAC,CAAC,EAAEb,KAAK,CAACF,OAAO,EAAEO,MAAM,EAAEC,KAAK,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}