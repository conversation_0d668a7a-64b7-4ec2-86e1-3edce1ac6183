{"ast": null, "code": "import { createContext } from '../ContextSelector';\nvar HoverContext = createContext();\nexport default HoverContext;", "map": {"version": 3, "names": ["createContext", "HoverContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/context/HoverContext.js"], "sourcesContent": ["import { createContext } from '../ContextSelector';\nvar HoverContext = createContext();\nexport default HoverContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,YAAY,GAAGD,aAAa,CAAC,CAAC;AAClC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}