{"ast": null, "code": "import shallowEqual from 'shallowequal';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { formatValue } from '../utils/dateUtil';\nexport default function useValueTexts(value, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  return useMemo(function () {\n    if (!value) {\n      return [[''], ''];\n    }\n    // We will convert data format back to first format\n    var firstValueText = '';\n    var fullValueTexts = [];\n    for (var i = 0; i < formatList.length; i += 1) {\n      var format = formatList[i];\n      var formatStr = formatValue(value, {\n        generateConfig: generateConfig,\n        locale: locale,\n        format: format\n      });\n      fullValueTexts.push(formatStr);\n      if (i === 0) {\n        firstValueText = formatStr;\n      }\n    }\n    return [fullValueTexts, firstValueText];\n  }, [value, formatList], function (prev, next) {\n    return prev[0] !== next[0] || !shallowEqual(prev[1], next[1]);\n  });\n}", "map": {"version": 3, "names": ["shallowEqual", "useMemo", "formatValue", "useValueTexts", "value", "_ref", "formatList", "generateConfig", "locale", "firstValueText", "fullValueTexts", "i", "length", "format", "formatStr", "push", "prev", "next"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/hooks/useValueTexts.js"], "sourcesContent": ["import shallowEqual from 'shallowequal';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { formatValue } from '../utils/dateUtil';\nexport default function useValueTexts(value, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  return useMemo(function () {\n    if (!value) {\n      return [[''], ''];\n    }\n    // We will convert data format back to first format\n    var firstValueText = '';\n    var fullValueTexts = [];\n    for (var i = 0; i < formatList.length; i += 1) {\n      var format = formatList[i];\n      var formatStr = formatValue(value, {\n        generateConfig: generateConfig,\n        locale: locale,\n        format: format\n      });\n      fullValueTexts.push(formatStr);\n      if (i === 0) {\n        firstValueText = formatStr;\n      }\n    }\n    return [fullValueTexts, firstValueText];\n  }, [value, formatList], function (prev, next) {\n    return prev[0] !== next[0] || !shallowEqual(prev[1], next[1]);\n  });\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,cAAc;AACvC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACjD,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;IAC9BC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,OAAOP,OAAO,CAAC,YAAY;IACzB,IAAI,CAACG,KAAK,EAAE;MACV,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACnB;IACA;IACA,IAAIK,cAAc,GAAG,EAAE;IACvB,IAAIC,cAAc,GAAG,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7C,IAAIE,MAAM,GAAGP,UAAU,CAACK,CAAC,CAAC;MAC1B,IAAIG,SAAS,GAAGZ,WAAW,CAACE,KAAK,EAAE;QACjCG,cAAc,EAAEA,cAAc;QAC9BC,MAAM,EAAEA,MAAM;QACdK,MAAM,EAAEA;MACV,CAAC,CAAC;MACFH,cAAc,CAACK,IAAI,CAACD,SAAS,CAAC;MAC9B,IAAIH,CAAC,KAAK,CAAC,EAAE;QACXF,cAAc,GAAGK,SAAS;MAC5B;IACF;IACA,OAAO,CAACJ,cAAc,EAAED,cAAc,CAAC;EACzC,CAAC,EAAE,CAACL,KAAK,EAAEE,UAAU,CAAC,EAAE,UAAUU,IAAI,EAAEC,IAAI,EAAE;IAC5C,OAAOD,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,IAAI,CAACjB,YAAY,CAACgB,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}