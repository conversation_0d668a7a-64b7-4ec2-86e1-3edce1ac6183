{"ast": null, "code": "import moment from 'moment';\nimport { noteOnce } from \"rc-util/es/warning\";\nvar generateConfig = {\n  // get\n  getNow: function getNow() {\n    return moment();\n  },\n  getFixedDate: function getFixedDate(string) {\n    return moment(string, 'YYYY-MM-DD');\n  },\n  getEndDate: function getEndDate(date) {\n    var clone = date.clone();\n    return clone.endOf('month');\n  },\n  getWeekDay: function getWeekDay(date) {\n    var clone = date.clone().locale('en_US');\n    return clone.weekday() + clone.localeData().firstDayOfWeek();\n  },\n  getYear: function getYear(date) {\n    return date.year();\n  },\n  getMonth: function getMonth(date) {\n    return date.month();\n  },\n  getDate: function getDate(date) {\n    return date.date();\n  },\n  getHour: function getHour(date) {\n    return date.hour();\n  },\n  getMinute: function getMinute(date) {\n    return date.minute();\n  },\n  getSecond: function getSecond(date) {\n    return date.second();\n  },\n  // set\n  addYear: function addYear(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'year');\n  },\n  addMonth: function addMonth(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'month');\n  },\n  addDate: function addDate(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'day');\n  },\n  setYear: function setYear(date, year) {\n    var clone = date.clone();\n    return clone.year(year);\n  },\n  setMonth: function setMonth(date, month) {\n    var clone = date.clone();\n    return clone.month(month);\n  },\n  setDate: function setDate(date, num) {\n    var clone = date.clone();\n    return clone.date(num);\n  },\n  setHour: function setHour(date, hour) {\n    var clone = date.clone();\n    return clone.hour(hour);\n  },\n  setMinute: function setMinute(date, minute) {\n    var clone = date.clone();\n    return clone.minute(minute);\n  },\n  setSecond: function setSecond(date, second) {\n    var clone = date.clone();\n    return clone.second(second);\n  },\n  // Compare\n  isAfter: function isAfter(date1, date2) {\n    return date1.isAfter(date2);\n  },\n  isValidate: function isValidate(date) {\n    return date.isValid();\n  },\n  locale: {\n    getWeekFirstDay: function getWeekFirstDay(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().firstDayOfWeek();\n    },\n    getWeekFirstDate: function getWeekFirstDate(locale, date) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.weekday(0);\n    },\n    getWeek: function getWeek(locale, date) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.week();\n    },\n    getShortWeekDays: function getShortWeekDays(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().weekdaysMin();\n    },\n    getShortMonths: function getShortMonths(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().monthsShort();\n    },\n    format: function format(locale, date, _format) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.format(_format);\n    },\n    parse: function parse(locale, text, formats) {\n      var fallbackFormatList = [];\n      for (var i = 0; i < formats.length; i += 1) {\n        var format = formats[i];\n        var formatText = text;\n        if (format.includes('wo') || format.includes('Wo')) {\n          format = format.replace(/wo/g, 'w').replace(/Wo/g, 'W');\n          var matchFormat = format.match(/[-YyMmDdHhSsWwGg]+/g);\n          var matchText = formatText.match(/[-\\d]+/g);\n          if (matchFormat && matchText) {\n            format = matchFormat.join('');\n            formatText = matchText.join('');\n          } else {\n            fallbackFormatList.push(format.replace(/o/g, ''));\n          }\n        }\n        var date = moment(formatText, format, locale, true);\n        if (date.isValid()) {\n          return date;\n        }\n      }\n      // Fallback to fuzzy matching, this should always not reach match or need fire a issue\n      for (var _i = 0; _i < fallbackFormatList.length; _i += 1) {\n        var _date = moment(text, fallbackFormatList[_i], locale, false);\n        /* istanbul ignore next */\n        if (_date.isValid()) {\n          noteOnce(false, 'Not match any format strictly and fallback to fuzzy match. Please help to fire a issue about this.');\n          return _date;\n        }\n      }\n      return null;\n    }\n  }\n};\nexport default generateConfig;", "map": {"version": 3, "names": ["moment", "noteOnce", "generateConfig", "getNow", "getFixedDate", "string", "getEndDate", "date", "clone", "endOf", "getWeekDay", "locale", "weekday", "localeData", "firstDayOfWeek", "getYear", "year", "getMonth", "month", "getDate", "getHour", "hour", "getMinute", "minute", "getSecond", "second", "addYear", "diff", "add", "addMonth", "addDate", "setYear", "setMonth", "setDate", "num", "setHour", "setMinute", "setSecond", "isAfter", "date1", "date2", "isValidate", "<PERSON><PERSON><PERSON><PERSON>", "getWeekFirstDay", "getWeekFirstDate", "result", "getWeek", "week", "getShortWeekDays", "weekdaysMin", "getShortMonths", "monthsShort", "format", "_format", "parse", "text", "formats", "fallbackFormatList", "i", "length", "formatText", "includes", "replace", "matchFormat", "match", "matchText", "join", "push", "_i", "_date"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/generate/moment.js"], "sourcesContent": ["import moment from 'moment';\nimport { noteOnce } from \"rc-util/es/warning\";\nvar generateConfig = {\n  // get\n  getNow: function getNow() {\n    return moment();\n  },\n  getFixedDate: function getFixedDate(string) {\n    return moment(string, 'YYYY-MM-DD');\n  },\n  getEndDate: function getEndDate(date) {\n    var clone = date.clone();\n    return clone.endOf('month');\n  },\n  getWeekDay: function getWeekDay(date) {\n    var clone = date.clone().locale('en_US');\n    return clone.weekday() + clone.localeData().firstDayOfWeek();\n  },\n  getYear: function getYear(date) {\n    return date.year();\n  },\n  getMonth: function getMonth(date) {\n    return date.month();\n  },\n  getDate: function getDate(date) {\n    return date.date();\n  },\n  getHour: function getHour(date) {\n    return date.hour();\n  },\n  getMinute: function getMinute(date) {\n    return date.minute();\n  },\n  getSecond: function getSecond(date) {\n    return date.second();\n  },\n  // set\n  addYear: function addYear(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'year');\n  },\n  addMonth: function addMonth(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'month');\n  },\n  addDate: function addDate(date, diff) {\n    var clone = date.clone();\n    return clone.add(diff, 'day');\n  },\n  setYear: function setYear(date, year) {\n    var clone = date.clone();\n    return clone.year(year);\n  },\n  setMonth: function setMonth(date, month) {\n    var clone = date.clone();\n    return clone.month(month);\n  },\n  setDate: function setDate(date, num) {\n    var clone = date.clone();\n    return clone.date(num);\n  },\n  setHour: function setHour(date, hour) {\n    var clone = date.clone();\n    return clone.hour(hour);\n  },\n  setMinute: function setMinute(date, minute) {\n    var clone = date.clone();\n    return clone.minute(minute);\n  },\n  setSecond: function setSecond(date, second) {\n    var clone = date.clone();\n    return clone.second(second);\n  },\n  // Compare\n  isAfter: function isAfter(date1, date2) {\n    return date1.isAfter(date2);\n  },\n  isValidate: function isValidate(date) {\n    return date.isValid();\n  },\n  locale: {\n    getWeekFirstDay: function getWeekFirstDay(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().firstDayOfWeek();\n    },\n    getWeekFirstDate: function getWeekFirstDate(locale, date) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.weekday(0);\n    },\n    getWeek: function getWeek(locale, date) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.week();\n    },\n    getShortWeekDays: function getShortWeekDays(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().weekdaysMin();\n    },\n    getShortMonths: function getShortMonths(locale) {\n      var date = moment().locale(locale);\n      return date.localeData().monthsShort();\n    },\n    format: function format(locale, date, _format) {\n      var clone = date.clone();\n      var result = clone.locale(locale);\n      return result.format(_format);\n    },\n    parse: function parse(locale, text, formats) {\n      var fallbackFormatList = [];\n      for (var i = 0; i < formats.length; i += 1) {\n        var format = formats[i];\n        var formatText = text;\n        if (format.includes('wo') || format.includes('Wo')) {\n          format = format.replace(/wo/g, 'w').replace(/Wo/g, 'W');\n          var matchFormat = format.match(/[-YyMmDdHhSsWwGg]+/g);\n          var matchText = formatText.match(/[-\\d]+/g);\n          if (matchFormat && matchText) {\n            format = matchFormat.join('');\n            formatText = matchText.join('');\n          } else {\n            fallbackFormatList.push(format.replace(/o/g, ''));\n          }\n        }\n        var date = moment(formatText, format, locale, true);\n        if (date.isValid()) {\n          return date;\n        }\n      }\n      // Fallback to fuzzy matching, this should always not reach match or need fire a issue\n      for (var _i = 0; _i < fallbackFormatList.length; _i += 1) {\n        var _date = moment(text, fallbackFormatList[_i], locale, false);\n        /* istanbul ignore next */\n        if (_date.isValid()) {\n          noteOnce(false, 'Not match any format strictly and fallback to fuzzy match. Please help to fire a issue about this.');\n          return _date;\n        }\n      }\n      return null;\n    }\n  }\n};\nexport default generateConfig;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,IAAIC,cAAc,GAAG;EACnB;EACAC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAOH,MAAM,CAAC,CAAC;EACjB,CAAC;EACDI,YAAY,EAAE,SAASA,YAAYA,CAACC,MAAM,EAAE;IAC1C,OAAOL,MAAM,CAACK,MAAM,EAAE,YAAY,CAAC;EACrC,CAAC;EACDC,UAAU,EAAE,SAASA,UAAUA,CAACC,IAAI,EAAE;IACpC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACC,KAAK,CAAC,OAAO,CAAC;EAC7B,CAAC;EACDC,UAAU,EAAE,SAASA,UAAUA,CAACH,IAAI,EAAE;IACpC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC,CAACG,MAAM,CAAC,OAAO,CAAC;IACxC,OAAOH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK,CAACK,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EAC9D,CAAC;EACDC,OAAO,EAAE,SAASA,OAAOA,CAACR,IAAI,EAAE;IAC9B,OAAOA,IAAI,CAACS,IAAI,CAAC,CAAC;EACpB,CAAC;EACDC,QAAQ,EAAE,SAASA,QAAQA,CAACV,IAAI,EAAE;IAChC,OAAOA,IAAI,CAACW,KAAK,CAAC,CAAC;EACrB,CAAC;EACDC,OAAO,EAAE,SAASA,OAAOA,CAACZ,IAAI,EAAE;IAC9B,OAAOA,IAAI,CAACA,IAAI,CAAC,CAAC;EACpB,CAAC;EACDa,OAAO,EAAE,SAASA,OAAOA,CAACb,IAAI,EAAE;IAC9B,OAAOA,IAAI,CAACc,IAAI,CAAC,CAAC;EACpB,CAAC;EACDC,SAAS,EAAE,SAASA,SAASA,CAACf,IAAI,EAAE;IAClC,OAAOA,IAAI,CAACgB,MAAM,CAAC,CAAC;EACtB,CAAC;EACDC,SAAS,EAAE,SAASA,SAASA,CAACjB,IAAI,EAAE;IAClC,OAAOA,IAAI,CAACkB,MAAM,CAAC,CAAC;EACtB,CAAC;EACD;EACAC,OAAO,EAAE,SAASA,OAAOA,CAACnB,IAAI,EAAEoB,IAAI,EAAE;IACpC,IAAInB,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACoB,GAAG,CAACD,IAAI,EAAE,MAAM,CAAC;EAChC,CAAC;EACDE,QAAQ,EAAE,SAASA,QAAQA,CAACtB,IAAI,EAAEoB,IAAI,EAAE;IACtC,IAAInB,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACoB,GAAG,CAACD,IAAI,EAAE,OAAO,CAAC;EACjC,CAAC;EACDG,OAAO,EAAE,SAASA,OAAOA,CAACvB,IAAI,EAAEoB,IAAI,EAAE;IACpC,IAAInB,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACoB,GAAG,CAACD,IAAI,EAAE,KAAK,CAAC;EAC/B,CAAC;EACDI,OAAO,EAAE,SAASA,OAAOA,CAACxB,IAAI,EAAES,IAAI,EAAE;IACpC,IAAIR,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACQ,IAAI,CAACA,IAAI,CAAC;EACzB,CAAC;EACDgB,QAAQ,EAAE,SAASA,QAAQA,CAACzB,IAAI,EAAEW,KAAK,EAAE;IACvC,IAAIV,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACU,KAAK,CAACA,KAAK,CAAC;EAC3B,CAAC;EACDe,OAAO,EAAE,SAASA,OAAOA,CAAC1B,IAAI,EAAE2B,GAAG,EAAE;IACnC,IAAI1B,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACD,IAAI,CAAC2B,GAAG,CAAC;EACxB,CAAC;EACDC,OAAO,EAAE,SAASA,OAAOA,CAAC5B,IAAI,EAAEc,IAAI,EAAE;IACpC,IAAIb,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACa,IAAI,CAACA,IAAI,CAAC;EACzB,CAAC;EACDe,SAAS,EAAE,SAASA,SAASA,CAAC7B,IAAI,EAAEgB,MAAM,EAAE;IAC1C,IAAIf,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACe,MAAM,CAACA,MAAM,CAAC;EAC7B,CAAC;EACDc,SAAS,EAAE,SAASA,SAASA,CAAC9B,IAAI,EAAEkB,MAAM,EAAE;IAC1C,IAAIjB,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;IACxB,OAAOA,KAAK,CAACiB,MAAM,CAACA,MAAM,CAAC;EAC7B,CAAC;EACD;EACAa,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACtC,OAAOD,KAAK,CAACD,OAAO,CAACE,KAAK,CAAC;EAC7B,CAAC;EACDC,UAAU,EAAE,SAASA,UAAUA,CAAClC,IAAI,EAAE;IACpC,OAAOA,IAAI,CAACmC,OAAO,CAAC,CAAC;EACvB,CAAC;EACD/B,MAAM,EAAE;IACNgC,eAAe,EAAE,SAASA,eAAeA,CAAChC,MAAM,EAAE;MAChD,IAAIJ,IAAI,GAAGP,MAAM,CAAC,CAAC,CAACW,MAAM,CAACA,MAAM,CAAC;MAClC,OAAOJ,IAAI,CAACM,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;IAC3C,CAAC;IACD8B,gBAAgB,EAAE,SAASA,gBAAgBA,CAACjC,MAAM,EAAEJ,IAAI,EAAE;MACxD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;MACxB,IAAIqC,MAAM,GAAGrC,KAAK,CAACG,MAAM,CAACA,MAAM,CAAC;MACjC,OAAOkC,MAAM,CAACjC,OAAO,CAAC,CAAC,CAAC;IAC1B,CAAC;IACDkC,OAAO,EAAE,SAASA,OAAOA,CAACnC,MAAM,EAAEJ,IAAI,EAAE;MACtC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;MACxB,IAAIqC,MAAM,GAAGrC,KAAK,CAACG,MAAM,CAACA,MAAM,CAAC;MACjC,OAAOkC,MAAM,CAACE,IAAI,CAAC,CAAC;IACtB,CAAC;IACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACrC,MAAM,EAAE;MAClD,IAAIJ,IAAI,GAAGP,MAAM,CAAC,CAAC,CAACW,MAAM,CAACA,MAAM,CAAC;MAClC,OAAOJ,IAAI,CAACM,UAAU,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC;IACxC,CAAC;IACDC,cAAc,EAAE,SAASA,cAAcA,CAACvC,MAAM,EAAE;MAC9C,IAAIJ,IAAI,GAAGP,MAAM,CAAC,CAAC,CAACW,MAAM,CAACA,MAAM,CAAC;MAClC,OAAOJ,IAAI,CAACM,UAAU,CAAC,CAAC,CAACsC,WAAW,CAAC,CAAC;IACxC,CAAC;IACDC,MAAM,EAAE,SAASA,MAAMA,CAACzC,MAAM,EAAEJ,IAAI,EAAE8C,OAAO,EAAE;MAC7C,IAAI7C,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,CAAC;MACxB,IAAIqC,MAAM,GAAGrC,KAAK,CAACG,MAAM,CAACA,MAAM,CAAC;MACjC,OAAOkC,MAAM,CAACO,MAAM,CAACC,OAAO,CAAC;IAC/B,CAAC;IACDC,KAAK,EAAE,SAASA,KAAKA,CAAC3C,MAAM,EAAE4C,IAAI,EAAEC,OAAO,EAAE;MAC3C,IAAIC,kBAAkB,GAAG,EAAE;MAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAIN,MAAM,GAAGI,OAAO,CAACE,CAAC,CAAC;QACvB,IAAIE,UAAU,GAAGL,IAAI;QACrB,IAAIH,MAAM,CAACS,QAAQ,CAAC,IAAI,CAAC,IAAIT,MAAM,CAACS,QAAQ,CAAC,IAAI,CAAC,EAAE;UAClDT,MAAM,GAAGA,MAAM,CAACU,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;UACvD,IAAIC,WAAW,GAAGX,MAAM,CAACY,KAAK,CAAC,qBAAqB,CAAC;UACrD,IAAIC,SAAS,GAAGL,UAAU,CAACI,KAAK,CAAC,SAAS,CAAC;UAC3C,IAAID,WAAW,IAAIE,SAAS,EAAE;YAC5Bb,MAAM,GAAGW,WAAW,CAACG,IAAI,CAAC,EAAE,CAAC;YAC7BN,UAAU,GAAGK,SAAS,CAACC,IAAI,CAAC,EAAE,CAAC;UACjC,CAAC,MAAM;YACLT,kBAAkB,CAACU,IAAI,CAACf,MAAM,CAACU,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;UACnD;QACF;QACA,IAAIvD,IAAI,GAAGP,MAAM,CAAC4D,UAAU,EAAER,MAAM,EAAEzC,MAAM,EAAE,IAAI,CAAC;QACnD,IAAIJ,IAAI,CAACmC,OAAO,CAAC,CAAC,EAAE;UAClB,OAAOnC,IAAI;QACb;MACF;MACA;MACA,KAAK,IAAI6D,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGX,kBAAkB,CAACE,MAAM,EAAES,EAAE,IAAI,CAAC,EAAE;QACxD,IAAIC,KAAK,GAAGrE,MAAM,CAACuD,IAAI,EAAEE,kBAAkB,CAACW,EAAE,CAAC,EAAEzD,MAAM,EAAE,KAAK,CAAC;QAC/D;QACA,IAAI0D,KAAK,CAAC3B,OAAO,CAAC,CAAC,EAAE;UACnBzC,QAAQ,CAAC,KAAK,EAAE,oGAAoG,CAAC;UACrH,OAAOoE,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;EACF;AACF,CAAC;AACD,eAAenE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}