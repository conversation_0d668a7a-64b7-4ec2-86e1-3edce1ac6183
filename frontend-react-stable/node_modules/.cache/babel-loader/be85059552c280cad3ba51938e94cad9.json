{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar REACT_ELEMENT_TYPE_18 = Symbol.for('react.element');\nvar REACT_ELEMENT_TYPE_19 = Symbol.for('react.transitional.element');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\n\n/**\n * Compatible with React 18 or 19 to check if node is a Fragment.\n */\nexport default function isFragment(object) {\n  return (\n    // Base object type\n    object && _typeof(object) === 'object' && (\n    // React Element type\n    object.$$typeof === REACT_ELEMENT_TYPE_18 || object.$$typeof === REACT_ELEMENT_TYPE_19) &&\n    // React Fragment type\n    object.type === REACT_FRAGMENT_TYPE\n  );\n}", "map": {"version": 3, "names": ["_typeof", "REACT_ELEMENT_TYPE_18", "Symbol", "for", "REACT_ELEMENT_TYPE_19", "REACT_FRAGMENT_TYPE", "isFragment", "object", "$$typeof", "type"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/React/isFragment.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar REACT_ELEMENT_TYPE_18 = Symbol.for('react.element');\nvar REACT_ELEMENT_TYPE_19 = Symbol.for('react.transitional.element');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\n\n/**\n * Compatible with React 18 or 19 to check if node is a Fragment.\n */\nexport default function isFragment(object) {\n  return (\n    // Base object type\n    object && _typeof(object) === 'object' && (\n    // React Element type\n    object.$$typeof === REACT_ELEMENT_TYPE_18 || object.$$typeof === REACT_ELEMENT_TYPE_19) &&\n    // React Fragment type\n    object.type === REACT_FRAGMENT_TYPE\n  );\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,IAAIC,qBAAqB,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;AACvD,IAAIC,qBAAqB,GAAGF,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;AACpE,IAAIE,mBAAmB,GAAGH,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;;AAEtD;AACA;AACA;AACA,eAAe,SAASG,UAAUA,CAACC,MAAM,EAAE;EACzC;IACE;IACAA,MAAM,IAAIP,OAAO,CAACO,MAAM,CAAC,KAAK,QAAQ;IACtC;IACAA,MAAM,CAACC,QAAQ,KAAKP,qBAAqB,IAAIM,MAAM,CAACC,QAAQ,KAAKJ,qBAAqB,CAAC;IACvF;IACAG,MAAM,CAACE,IAAI,KAAKJ;EAAmB;AAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}