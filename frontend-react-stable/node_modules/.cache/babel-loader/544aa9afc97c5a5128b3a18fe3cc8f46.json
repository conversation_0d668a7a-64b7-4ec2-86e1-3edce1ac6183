{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useDebounce(value) {\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    cacheValue = _React$useState2[0],\n    setCacheValue = _React$useState2[1];\n  React.useEffect(function () {\n    var timeout = setTimeout(function () {\n      setCacheValue(value);\n    }, value.length ? 0 : 10);\n    return function () {\n      clearTimeout(timeout);\n    };\n  }, [value]);\n  return cacheValue;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useDebounce", "value", "_React$useState", "useState", "_React$useState2", "cacheValue", "setCacheValue", "useEffect", "timeout", "setTimeout", "length", "clearTimeout"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/hooks/useDebounce.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useDebounce(value) {\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    cacheValue = _React$useState2[0],\n    setCacheValue = _React$useState2[1];\n  React.useEffect(function () {\n    var timeout = setTimeout(function () {\n      setCacheValue(value);\n    }, value.length ? 0 : 10);\n    return function () {\n      clearTimeout(timeout);\n    };\n  }, [value]);\n  return cacheValue;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,eAAe,GAAGH,KAAK,CAACI,QAAQ,CAACF,KAAK,CAAC;IACzCG,gBAAgB,GAAGN,cAAc,CAACI,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrCL,KAAK,CAACQ,SAAS,CAAC,YAAY;IAC1B,IAAIC,OAAO,GAAGC,UAAU,CAAC,YAAY;MACnCH,aAAa,CAACL,KAAK,CAAC;IACtB,CAAC,EAAEA,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;IACzB,OAAO,YAAY;MACjBC,YAAY,CAACH,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EACX,OAAOI,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}