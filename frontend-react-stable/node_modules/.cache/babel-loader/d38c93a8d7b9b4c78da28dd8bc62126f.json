{"ast": null, "code": "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\nmodule.exports = cloneDataView;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "cloneDataView", "dataView", "isDeep", "buffer", "constructor", "byteOffset", "byteLength", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_cloneDataView.js"], "sourcesContent": ["var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\nmodule.exports = cloneDataView;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvC,IAAIC,MAAM,GAAGD,MAAM,GAAGJ,gBAAgB,CAACG,QAAQ,CAACE,MAAM,CAAC,GAAGF,QAAQ,CAACE,MAAM;EACzE,OAAO,IAAIF,QAAQ,CAACG,WAAW,CAACD,MAAM,EAAEF,QAAQ,CAACI,UAAU,EAAEJ,QAAQ,CAACK,UAAU,CAAC;AACnF;AAEAC,MAAM,CAACC,OAAO,GAAGR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}