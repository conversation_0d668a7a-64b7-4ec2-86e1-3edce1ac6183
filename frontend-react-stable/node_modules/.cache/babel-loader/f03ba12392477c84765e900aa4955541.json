{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, BarChartOutlined, ExperimentOutlined, AimOutlined, DatabaseOutlined, FileTextOutlined, SearchOutlined, UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\nimport TaskStatusIndicator from '../TaskStatusIndicator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst MainLayout = ({\n  children\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n  const menuItems = [{\n    key: '/data-cleaning',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this),\n    label: '流量分析'\n  }, {\n    key: '/model-training',\n    icon: /*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    label: '模型训练'\n  }, {\n    key: '/model-prediction',\n    icon: /*#__PURE__*/_jsxDEV(AimOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this),\n    label: '模型预测'\n  }, {\n    key: '/model-registry',\n    icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    label: '模型仓库'\n  }, {\n    key: '/clean-template',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this),\n    label: '清洗模板'\n  }, {\n    key: '/data-query',\n    icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    label: '数据查询'\n  }, {\n    key: '/task-manager',\n    icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    label: '任务管理'\n  }, {\n    key: '/user-management',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this),\n    label: '用户管理'\n  }];\n  const handleMenuClick = key => {\n    navigate(key);\n  };\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this),\n    label: '个人信息'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this),\n    label: '设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this),\n    label: '退出登录',\n    onClick: handleLogout\n  }];\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: sidebarCollapsed,\n      width: 200,\n      style: {\n        background: '#fff',\n        boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n        position: 'fixed',\n        left: 0,\n        top: 64,\n        bottom: 0,\n        zIndex: 999,\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: ({\n          key\n        }) => handleMenuClick(key),\n        style: {\n          border: 'none',\n          flex: 1\n        },\n        className: sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: sidebarCollapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 63\n          }, this),\n          onClick: () => dispatch(toggleSidebar()),\n          className: \"sidebar-toggle-btn\",\n          style: {\n            fontSize: '16px',\n            width: '100%',\n            height: 40,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          title: sidebarCollapsed ? '展开菜单' : '收起菜单'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: '0 24px 0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          style: {\n            margin: 0,\n            color: '#000000',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'\n          },\n          children: \"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(TaskStatusIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems\n            },\n            placement: \"bottomRight\",\n            arrow: true,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: user === null || user === void 0 ? void 0 : user.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`,\n          // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden',\n          // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s'\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"mmzqcQVSMbOYssWaIw2AtJ2TNCY=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useEffect", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "Dropdown", "Space", "Typography", "MenuFoldOutlined", "MenuUnfoldOutlined", "BarChartOutlined", "ExperimentOutlined", "AimOutlined", "DatabaseOutlined", "FileTextOutlined", "SearchOutlined", "UserOutlined", "LogoutOutlined", "SettingOutlined", "useNavigate", "useLocation", "useSelector", "useDispatch", "logout", "restoreAuth", "toggleSidebar", "TaskStatusIndicator", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Title", "MainLayout", "children", "_s", "navigate", "location", "dispatch", "user", "state", "auth", "sidebarCollapsed", "ui", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "ClockCircleOutlined", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "style", "minHeight", "trigger", "collapsible", "collapsed", "width", "background", "boxShadow", "position", "left", "top", "bottom", "zIndex", "overflow", "display", "flexDirection", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "border", "flex", "className", "padding", "borderTop", "textAlign", "fontSize", "height", "alignItems", "justifyContent", "title", "right", "level", "margin", "color", "fontWeight", "letterSpacing", "textShadow", "menu", "placement", "arrow", "cursor", "username", "borderRadius", "transition", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  ExperimentOutlined,\n  AimOutlined,\n  DatabaseOutlined,\n  FileTextOutlined,\n  SearchOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\nimport TaskStatusIndicator from '../TaskStatusIndicator';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const { user } = useSelector((state: RootState) => state.auth);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n\n\n\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n\n\n  const menuItems = [\n    {\n      key: '/data-cleaning',\n      icon: <BarChartOutlined />,\n      label: '流量分析',\n    },\n    {\n      key: '/model-training',\n      icon: <ExperimentOutlined />,\n      label: '模型训练',\n    },\n    {\n      key: '/model-prediction',\n      icon: <AimOutlined />,\n      label: '模型预测',\n    },\n    {\n      key: '/model-registry',\n      icon: <DatabaseOutlined />,\n      label: '模型仓库',\n    },\n    {\n      key: '/clean-template',\n      icon: <FileTextOutlined />,\n      label: '清洗模板',\n    },\n    {\n      key: '/data-query',\n      icon: <SearchOutlined />,\n      label: '数据查询',\n    },\n    {\n      key: '/task-manager',\n      icon: <ClockCircleOutlined />,\n      label: '任务管理',\n    },\n    {\n      key: '/user-management',\n      icon: <UserOutlined />,\n      label: '用户管理',\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={200}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          left: 0,\n          top: 64,\n          bottom: 0,\n          zIndex: 999,\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n        }}\n      >\n\n\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          style={{\n            border: 'none',\n            flex: 1,\n          }}\n          className={sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'}\n        />\n\n        {/* 回缩按钮移到侧边栏底部 */}\n        <div style={{\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center',\n        }}>\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => dispatch(toggleSidebar())}\n            className=\"sidebar-toggle-btn\"\n            style={{\n              fontSize: '16px',\n              width: '100%',\n              height: 40,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n            title={sidebarCollapsed ? '展开菜单' : '收起菜单'}\n          />\n        </div>\n\n        {/* 强制应用选中项样式 */}\n        <style>{`\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `}</style>\n\n      </Sider>\n\n      <Layout>\n        <Header style={{\n          padding: '0 24px 0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px',\n        }}>\n          <Title level={3} style={{\n            margin: 0,\n            color: '#000000',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n          }}>\n            AI智能清洗策略系统\n          </Title>\n\n          <Space>\n            <TaskStatusIndicator />\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>{user?.username}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`, // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden', // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAChF,OAAO,kBAAkB;AACzB,OAAO,eAAe;AACtB,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,WAAW,EACXC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,MAAM,EAAEC,WAAW,QAAQ,8BAA8B;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,mBAAmB,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG9B,MAAM;AACzC,MAAM;EAAE+B;AAAM,CAAC,GAAGzB,UAAU;AAM5B,MAAM0B,UAAqC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEiB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC9D,MAAM;IAAEC;EAAiB,CAAC,GAAGrB,WAAW,CAAEmB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EAIxE3C,SAAS,CAAC,MAAM;IACd;IACAsC,QAAQ,CAACd,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACc,QAAQ,CAAC,CAAC;EAId,MAAMM,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,gBAAgB;IACrBC,IAAI,eAAElB,OAAA,CAAClB,gBAAgB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACjB,kBAAkB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAElB,OAAA,CAAChB,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACf,gBAAgB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAElB,OAAA,CAACd,gBAAgB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAElB,OAAA,CAACb,cAAc;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAElB,OAAA,CAACwB,mBAAmB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAElB,OAAA,CAACZ,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,eAAe,GAAIR,GAAW,IAAK;IACvCT,QAAQ,CAACS,GAAG,CAAC;EACf,CAAC;EAED,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBhB,QAAQ,CAACf,MAAM,CAAC,CAAC,CAAC;IAClBa,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMmB,aAAa,GAAG,CACpB;IACEV,GAAG,EAAE,SAAS;IACdC,IAAI,eAAElB,OAAA,CAACZ,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAElB,OAAA,CAACV,eAAe;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEK,IAAI,EAAE;EACR,CAAC,EACD;IACEX,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAElB,OAAA,CAACX,cAAc;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,MAAM;IACbM,OAAO,EAAEH;EACX,CAAC,CACF;EAED,oBACE1B,OAAA,CAAC3B,MAAM;IAACyD,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAzB,QAAA,gBACpCN,OAAA,CAACE,KAAK;MACJ8B,OAAO,EAAE,IAAK;MACdC,WAAW;MACXC,SAAS,EAAEpB,gBAAiB;MAC5BqB,KAAK,EAAE,GAAI;MACXL,KAAK,EAAE;QACLM,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,CAAC;QACPC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;MACjB,CAAE;MAAAvC,QAAA,gBAIFN,OAAA,CAAC1B,IAAI;QACHwE,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACtC,QAAQ,CAACuC,QAAQ,CAAE;QAClCC,KAAK,EAAEjC,SAAU;QACjBa,OAAO,EAAEA,CAAC;UAAEZ;QAAI,CAAC,KAAKQ,eAAe,CAACR,GAAG,CAAE;QAC3Ca,KAAK,EAAE;UACLoB,MAAM,EAAE,MAAM;UACdC,IAAI,EAAE;QACR,CAAE;QACFC,SAAS,EAAEtC,gBAAgB,GAAG,gBAAgB,GAAG;MAAgB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAGFtB,OAAA;QAAK8B,KAAK,EAAE;UACVuB,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,mBAAmB;UAC9BC,SAAS,EAAE;QACb,CAAE;QAAAjD,QAAA,eACAN,OAAA,CAACzB,MAAM;UACLqD,IAAI,EAAC,MAAM;UACXV,IAAI,EAAEJ,gBAAgB,gBAAGd,OAAA,CAACnB,kBAAkB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACpB,gBAAgB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvEO,OAAO,EAAEA,CAAA,KAAMnB,QAAQ,CAACb,aAAa,CAAC,CAAC,CAAE;UACzCuD,SAAS,EAAC,oBAAoB;UAC9BtB,KAAK,EAAE;YACL0B,QAAQ,EAAE,MAAM;YAChBrB,KAAK,EAAE,MAAM;YACbsB,MAAM,EAAE,EAAE;YACVb,OAAO,EAAE,MAAM;YACfc,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UACFC,KAAK,EAAE9C,gBAAgB,GAAG,MAAM,GAAG;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtB,OAAA;QAAAM,QAAA,EAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CAAC,eAERtB,OAAA,CAAC3B,MAAM;MAAAiC,QAAA,gBACLN,OAAA,CAACC,MAAM;QAAC6B,KAAK,EAAE;UACbuB,OAAO,EAAE,eAAe;UACxBjB,UAAU,EAAE,MAAM;UAClBQ,OAAO,EAAE,MAAM;UACfc,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BtB,SAAS,EAAE,2BAA2B;UACtCC,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,CAAC;UACNqB,KAAK,EAAE,CAAC;UACRtB,IAAI,EAAE,CAAC;UACPG,MAAM,EAAE,IAAI;UACZe,MAAM,EAAE;QACV,CAAE;QAAAnD,QAAA,gBACAN,OAAA,CAACI,KAAK;UAAC0D,KAAK,EAAE,CAAE;UAAChC,KAAK,EAAE;YACtBiC,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBR,QAAQ,EAAE,MAAM;YAChBS,UAAU,EAAE,GAAG;YACfC,aAAa,EAAE,OAAO;YACtBC,UAAU,EAAE;UACd,CAAE;UAAA7D,QAAA,EAAC;QAEH;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERtB,OAAA,CAACtB,KAAK;UAAA4B,QAAA,gBACJN,OAAA,CAACF,mBAAmB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvBtB,OAAA,CAACvB,QAAQ;YACP2F,IAAI,EAAE;cAAEnB,KAAK,EAAEtB;YAAc,CAAE;YAC/B0C,SAAS,EAAC,aAAa;YACvBC,KAAK;YAAAhE,QAAA,eAELN,OAAA,CAACtB,KAAK;cAACoD,KAAK,EAAE;gBAAEyC,MAAM,EAAE;cAAU,CAAE;cAAAjE,QAAA,gBAClCN,OAAA,CAACxB,MAAM;gBAAC0C,IAAI,eAAElB,OAAA,CAACZ,YAAY;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCtB,OAAA;gBAAAM,QAAA,EAAOK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D;cAAQ;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETtB,OAAA,CAACG,OAAO;QAAC2B,KAAK,EAAE;UACdiC,MAAM,EAAE,kBAAkB,CAACjD,gBAAgB,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,IAAI;UAAE;UAClEuC,OAAO,EAAE,EAAE;UACXjB,UAAU,EAAE,MAAM;UAClBqC,YAAY,EAAE,KAAK;UACnB1C,SAAS,EAAE,qBAAqB;UAChCY,QAAQ,EAAE,QAAQ;UAAE;UACpB+B,UAAU,EAAE;QACd,CAAE;QAAApE,QAAA,EACCA;MAAQ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACf,EAAA,CAnOIF,UAAqC;EAAA,QACxBd,WAAW,EACXC,WAAW,EACXE,WAAW,EAEXD,WAAW,EACCA,WAAW;AAAA;AAAAkF,EAAA,GANpCtE,UAAqC;AAqO3C,eAAeA,UAAU;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}