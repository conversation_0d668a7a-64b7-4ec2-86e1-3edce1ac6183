{"ast": null, "code": "import * as React from 'react';\nimport SliderContext from '../context';\nimport Track from './Track';\nimport { getIndex } from '../util';\nexport default function Tracks(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    values = props.values,\n    startPoint = props.startPoint,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    included = _React$useContext.included,\n    range = _React$useContext.range,\n    min = _React$useContext.min;\n  var trackList = React.useMemo(function () {\n    if (!range) {\n      // null value do not have track\n      if (values.length === 0) {\n        return [];\n      }\n      var startValue = startPoint !== null && startPoint !== void 0 ? startPoint : min;\n      var endValue = values[0];\n      return [{\n        start: Math.min(startValue, endValue),\n        end: Math.max(startValue, endValue)\n      }];\n    } // Multiple\n\n    var list = [];\n    for (var i = 0; i < values.length - 1; i += 1) {\n      list.push({\n        start: values[i],\n        end: values[i + 1]\n      });\n    }\n    return list;\n  }, [values, range, startPoint, min]);\n  return included ? trackList.map(function (_ref, index) {\n    var start = _ref.start,\n      end = _ref.end;\n    return /*#__PURE__*/React.createElement(Track, {\n      index: index,\n      prefixCls: prefixCls,\n      style: getIndex(style, index),\n      start: start,\n      end: end,\n      key: index,\n      onStartMove: onStartMove\n    });\n  }) : null;\n}", "map": {"version": 3, "names": ["React", "SliderContext", "Track", "getIndex", "Tracks", "props", "prefixCls", "style", "values", "startPoint", "onStartMove", "_React$useContext", "useContext", "included", "range", "min", "trackList", "useMemo", "length", "startValue", "endValue", "start", "Math", "end", "max", "list", "i", "push", "map", "_ref", "index", "createElement", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/Tracks/index.js"], "sourcesContent": ["import * as React from 'react';\nimport SliderContext from '../context';\nimport Track from './Track';\nimport { getIndex } from '../util';\nexport default function Tracks(props) {\n  var prefixCls = props.prefixCls,\n      style = props.style,\n      values = props.values,\n      startPoint = props.startPoint,\n      onStartMove = props.onStartMove;\n\n  var _React$useContext = React.useContext(SliderContext),\n      included = _React$useContext.included,\n      range = _React$useContext.range,\n      min = _React$useContext.min;\n\n  var trackList = React.useMemo(function () {\n    if (!range) {\n      // null value do not have track\n      if (values.length === 0) {\n        return [];\n      }\n\n      var startValue = startPoint !== null && startPoint !== void 0 ? startPoint : min;\n      var endValue = values[0];\n      return [{\n        start: Math.min(startValue, endValue),\n        end: Math.max(startValue, endValue)\n      }];\n    } // Multiple\n\n\n    var list = [];\n\n    for (var i = 0; i < values.length - 1; i += 1) {\n      list.push({\n        start: values[i],\n        end: values[i + 1]\n      });\n    }\n\n    return list;\n  }, [values, range, startPoint, min]);\n  return included ? trackList.map(function (_ref, index) {\n    var start = _ref.start,\n        end = _ref.end;\n    return /*#__PURE__*/React.createElement(Track, {\n      index: index,\n      prefixCls: prefixCls,\n      style: getIndex(style, index),\n      start: start,\n      end: end,\n      key: index,\n      onStartMove: onStartMove\n    });\n  }) : null;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,SAAS;AAClC,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAE;EACpC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,WAAW,GAAGL,KAAK,CAACK,WAAW;EAEnC,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACX,aAAa,CAAC;IACnDY,QAAQ,GAAGF,iBAAiB,CAACE,QAAQ;IACrCC,KAAK,GAAGH,iBAAiB,CAACG,KAAK;IAC/BC,GAAG,GAAGJ,iBAAiB,CAACI,GAAG;EAE/B,IAAIC,SAAS,GAAGhB,KAAK,CAACiB,OAAO,CAAC,YAAY;IACxC,IAAI,CAACH,KAAK,EAAE;MACV;MACA,IAAIN,MAAM,CAACU,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MAEA,IAAIC,UAAU,GAAGV,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGM,GAAG;MAChF,IAAIK,QAAQ,GAAGZ,MAAM,CAAC,CAAC,CAAC;MACxB,OAAO,CAAC;QACNa,KAAK,EAAEC,IAAI,CAACP,GAAG,CAACI,UAAU,EAAEC,QAAQ,CAAC;QACrCG,GAAG,EAAED,IAAI,CAACE,GAAG,CAACL,UAAU,EAAEC,QAAQ;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAIK,IAAI,GAAG,EAAE;IAEb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,MAAM,CAACU,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE;MAC7CD,IAAI,CAACE,IAAI,CAAC;QACRN,KAAK,EAAEb,MAAM,CAACkB,CAAC,CAAC;QAChBH,GAAG,EAAEf,MAAM,CAACkB,CAAC,GAAG,CAAC;MACnB,CAAC,CAAC;IACJ;IAEA,OAAOD,IAAI;EACb,CAAC,EAAE,CAACjB,MAAM,EAAEM,KAAK,EAAEL,UAAU,EAAEM,GAAG,CAAC,CAAC;EACpC,OAAOF,QAAQ,GAAGG,SAAS,CAACY,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACrD,IAAIT,KAAK,GAAGQ,IAAI,CAACR,KAAK;MAClBE,GAAG,GAAGM,IAAI,CAACN,GAAG;IAClB,OAAO,aAAavB,KAAK,CAAC+B,aAAa,CAAC7B,KAAK,EAAE;MAC7C4B,KAAK,EAAEA,KAAK;MACZxB,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEJ,QAAQ,CAACI,KAAK,EAAEuB,KAAK,CAAC;MAC7BT,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAEA,GAAG;MACRS,GAAG,EAAEF,KAAK;MACVpB,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,CAAC,GAAG,IAAI;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module"}