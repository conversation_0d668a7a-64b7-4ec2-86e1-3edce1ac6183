{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"prefixCls\", \"value\", \"defaultValue\", \"onChange\", \"onSelect\", \"onDeselect\", \"searchValue\", \"inputValue\", \"onSearch\", \"autoClearSearchValue\", \"filterTreeNode\", \"treeNodeFilterProp\", \"showCheckedStrategy\", \"treeNodeLabelProp\", \"multiple\", \"treeCheckable\", \"treeCheckStrictly\", \"labelInValue\", \"fieldNames\", \"treeDataSimpleMode\", \"treeData\", \"children\", \"loadData\", \"treeLoadedKeys\", \"onTreeLoad\", \"treeDefaultExpandAll\", \"treeExpandedKeys\", \"treeDefaultExpandedKeys\", \"onTreeExpand\", \"treeExpandAction\", \"virtual\", \"listHeight\", \"listItemHeight\", \"onDropdownVisibleChange\", \"dropdownMatchSelectWidth\", \"treeLine\", \"treeIcon\", \"showTreeIcon\", \"switcherIcon\", \"treeMotion\"];\nimport * as React from 'react';\nimport { BaseSelect } from 'rc-select';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport useId from \"rc-select/es/hooks/useId\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport OptionList from \"./OptionList\";\nimport TreeNode from \"./TreeNode\";\nimport { formatStrategyValues, SHOW_ALL, SHOW_PARENT, SHOW_CHILD } from \"./utils/strategyUtil\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport LegacyContext from \"./LegacyContext\";\nimport useTreeData from \"./hooks/useTreeData\";\nimport { toArray, fillFieldNames, isNil } from \"./utils/valueUtil\";\nimport useCache from \"./hooks/useCache\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport useDataEntities from \"./hooks/useDataEntities\";\nimport { fillAdditionalInfo, fillLegacyProps } from \"./utils/legacyUtil\";\nimport useCheckedKeys from \"./hooks/useCheckedKeys\";\nimport useFilterTreeData from \"./hooks/useFilterTreeData\";\nimport warningProps from \"./utils/warningPropsUtil\";\nimport warning from \"rc-util/es/warning\";\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar TreeSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    onChange = props.onChange,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    searchValue = props.searchValue,\n    inputValue = props.inputValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    filterTreeNode = props.filterTreeNode,\n    _props$treeNodeFilter = props.treeNodeFilterProp,\n    treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,\n    _props$showCheckedStr = props.showCheckedStrategy,\n    showCheckedStrategy = _props$showCheckedStr === void 0 ? SHOW_CHILD : _props$showCheckedStr,\n    treeNodeLabelProp = props.treeNodeLabelProp,\n    multiple = props.multiple,\n    treeCheckable = props.treeCheckable,\n    treeCheckStrictly = props.treeCheckStrictly,\n    labelInValue = props.labelInValue,\n    fieldNames = props.fieldNames,\n    treeDataSimpleMode = props.treeDataSimpleMode,\n    treeData = props.treeData,\n    children = props.children,\n    loadData = props.loadData,\n    treeLoadedKeys = props.treeLoadedKeys,\n    onTreeLoad = props.onTreeLoad,\n    treeDefaultExpandAll = props.treeDefaultExpandAll,\n    treeExpandedKeys = props.treeExpandedKeys,\n    treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,\n    onTreeExpand = props.onTreeExpand,\n    treeExpandAction = props.treeExpandAction,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    treeLine = props.treeLine,\n    treeIcon = props.treeIcon,\n    showTreeIcon = props.showTreeIcon,\n    switcherIcon = props.switcherIcon,\n    treeMotion = props.treeMotion,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var treeConduction = treeCheckable && !treeCheckStrictly;\n  var mergedCheckable = treeCheckable || treeCheckStrictly;\n  var mergedLabelInValue = treeCheckStrictly || labelInValue;\n  var mergedMultiple = mergedCheckable || multiple;\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    internalValue = _useMergedState2[0],\n    setInternalValue = _useMergedState2[1]; // ========================== Warning ===========================\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ========================= FieldNames =========================\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [JSON.stringify(fieldNames)]\n  /* eslint-enable react-hooks/exhaustive-deps */); // =========================== Search ===========================\n\n  var _useMergedState3 = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedSearchValue = _useMergedState4[0],\n    setSearchValue = _useMergedState4[1];\n  var onInternalSearch = function onInternalSearch(searchText) {\n    setSearchValue(searchText);\n    onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n  }; // ============================ Data ============================\n  // `useTreeData` only do convert of `children` or `simpleMode`.\n  // Else will return origin `treeData` for perf consideration.\n  // Do not do anything to loop the data.\n\n  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);\n  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),\n    keyEntities = _useDataEntities.keyEntities,\n    valueEntities = _useDataEntities.valueEntities;\n  /** Get `missingRawValues` which not exist in the tree yet */\n\n  var splitRawValues = React.useCallback(function (newRawValues) {\n    var missingRawValues = [];\n    var existRawValues = []; // Keep missing value in the cache\n\n    newRawValues.forEach(function (val) {\n      if (valueEntities.has(val)) {\n        existRawValues.push(val);\n      } else {\n        missingRawValues.push(val);\n      }\n    });\n    return {\n      missingRawValues: missingRawValues,\n      existRawValues: existRawValues\n    };\n  }, [valueEntities]); // Filtered Tree\n\n  var filteredTreeData = useFilterTreeData(mergedTreeData, mergedSearchValue, {\n    fieldNames: mergedFieldNames,\n    treeNodeFilterProp: treeNodeFilterProp,\n    filterTreeNode: filterTreeNode\n  }); // =========================== Label ============================\n\n  var getLabel = React.useCallback(function (item) {\n    if (item) {\n      if (treeNodeLabelProp) {\n        return item[treeNodeLabelProp];\n      } // Loop from fieldNames\n\n      var titleList = mergedFieldNames._title;\n      for (var i = 0; i < titleList.length; i += 1) {\n        var title = item[titleList[i]];\n        if (title !== undefined) {\n          return title;\n        }\n      }\n    }\n  }, [mergedFieldNames, treeNodeLabelProp]); // ========================= Wrap Value =========================\n\n  var toLabeledValues = React.useCallback(function (draftValues) {\n    var values = toArray(draftValues);\n    return values.map(function (val) {\n      if (isRawValue(val)) {\n        return {\n          value: val\n        };\n      }\n      return val;\n    });\n  }, []);\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    var values = toLabeledValues(draftValues);\n    return values.map(function (item) {\n      var rawLabel = item.label;\n      var rawValue = item.value,\n        rawHalfChecked = item.halfChecked;\n      var rawDisabled;\n      var entity = valueEntities.get(rawValue); // Fill missing label & status\n\n      if (entity) {\n        var _rawLabel;\n        rawLabel = (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);\n        rawDisabled = entity.node.disabled;\n      } else if (rawLabel === undefined) {\n        // We try to find in current `labelInValue` value\n        var labelInValueItem = toLabeledValues(internalValue).find(function (labeledItem) {\n          return labeledItem.value === rawValue;\n        });\n        rawLabel = labelInValueItem.label;\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        halfChecked: rawHalfChecked,\n        disabled: rawDisabled\n      };\n    });\n  }, [valueEntities, getLabel, toLabeledValues, internalValue]); // =========================== Values ===========================\n\n  var rawMixedLabeledValues = React.useMemo(function () {\n    return toLabeledValues(internalValue);\n  }, [toLabeledValues, internalValue]); // Split value into full check and half check\n\n  var _React$useMemo = React.useMemo(function () {\n      var fullCheckValues = [];\n      var halfCheckValues = [];\n      rawMixedLabeledValues.forEach(function (item) {\n        if (item.halfChecked) {\n          halfCheckValues.push(item);\n        } else {\n          fullCheckValues.push(item);\n        }\n      });\n      return [fullCheckValues, halfCheckValues];\n    }, [rawMixedLabeledValues]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    rawLabeledValues = _React$useMemo2[0],\n    rawHalfLabeledValues = _React$useMemo2[1]; // const [mergedValues] = useCache(rawLabeledValues);\n\n  var rawValues = React.useMemo(function () {\n    return rawLabeledValues.map(function (item) {\n      return item.value;\n    });\n  }, [rawLabeledValues]); // Convert value to key. Will fill missed keys for conduct check.\n\n  var _useCheckedKeys = useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),\n    _useCheckedKeys2 = _slicedToArray(_useCheckedKeys, 2),\n    rawCheckedValues = _useCheckedKeys2[0],\n    rawHalfCheckedValues = _useCheckedKeys2[1]; // Convert rawCheckedKeys to check strategy related values\n\n  var displayValues = React.useMemo(function () {\n    // Collect keys which need to show\n    var displayKeys = formatStrategyValues(rawCheckedValues, showCheckedStrategy, keyEntities, mergedFieldNames); // Convert to value and filled with label\n\n    var values = displayKeys.map(function (key) {\n      var _keyEntities$key$node, _keyEntities$key, _keyEntities$key$node2;\n      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 ? void 0 : (_keyEntities$key$node2 = _keyEntities$key.node) === null || _keyEntities$key$node2 === void 0 ? void 0 : _keyEntities$key$node2[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;\n    }); // Back fill with origin label\n\n    var labeledValues = values.map(function (val) {\n      var targetItem = rawLabeledValues.find(function (item) {\n        return item.value === val;\n      });\n      return {\n        value: val,\n        label: targetItem === null || targetItem === void 0 ? void 0 : targetItem.label\n      };\n    });\n    var rawDisplayValues = convert2LabelValues(labeledValues);\n    var firstVal = rawDisplayValues[0];\n    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {\n      return [];\n    }\n    return rawDisplayValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, showCheckedStrategy, keyEntities]);\n  var _useCache = useCache(displayValues),\n    _useCache2 = _slicedToArray(_useCache, 1),\n    cachedDisplayValues = _useCache2[0]; // =========================== Change ===========================\n\n  var triggerChange = useRefFunc(function (newRawValues, extra, source) {\n    var labeledValues = convert2LabelValues(newRawValues);\n    setInternalValue(labeledValues); // Clean up if needed\n\n    if (autoClearSearchValue) {\n      setSearchValue('');\n    } // Generate rest parameters is costly, so only do it when necessary\n\n    if (onChange) {\n      var eventValues = newRawValues;\n      if (treeConduction) {\n        var formattedKeyList = formatStrategyValues(newRawValues, showCheckedStrategy, keyEntities, mergedFieldNames);\n        eventValues = formattedKeyList.map(function (key) {\n          var entity = valueEntities.get(key);\n          return entity ? entity.node[mergedFieldNames.value] : key;\n        });\n      }\n      var _ref = extra || {\n          triggerValue: undefined,\n          selected: undefined\n        },\n        triggerValue = _ref.triggerValue,\n        selected = _ref.selected;\n      var returnRawValues = eventValues; // We need fill half check back\n\n      if (treeCheckStrictly) {\n        var halfValues = rawHalfLabeledValues.filter(function (item) {\n          return !eventValues.includes(item.value);\n        });\n        returnRawValues = [].concat(_toConsumableArray(returnRawValues), _toConsumableArray(halfValues));\n      }\n      var returnLabeledValues = convert2LabelValues(returnRawValues);\n      var additionalInfo = {\n        // [Legacy] Always return as array contains label & value\n        preValue: rawLabeledValues,\n        triggerValue: triggerValue\n      }; // [Legacy] Fill legacy data if user query.\n      // This is expansive that we only fill when user query\n      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx\n\n      var showPosition = true;\n      if (treeCheckStrictly || source === 'selection' && !selected) {\n        showPosition = false;\n      }\n      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);\n      if (mergedCheckable) {\n        additionalInfo.checked = selected;\n      } else {\n        additionalInfo.selected = selected;\n      }\n      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {\n        return item.value;\n      });\n      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {\n        return item.label;\n      }), additionalInfo);\n    }\n  }); // ========================== Options ===========================\n\n  /** Trigger by option list */\n\n  var onOptionSelect = React.useCallback(function (selectedKey, _ref2) {\n    var _node$mergedFieldName;\n    var selected = _ref2.selected,\n      source = _ref2.source;\n    var entity = keyEntities[selectedKey];\n    var node = entity === null || entity === void 0 ? void 0 : entity.node;\n    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey; // Never be falsy but keep it safe\n\n    if (!mergedMultiple) {\n      // Single mode always set value\n      triggerChange([selectedValue], {\n        selected: true,\n        triggerValue: selectedValue\n      }, 'option');\n    } else {\n      var newRawValues = selected ? [].concat(_toConsumableArray(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {\n        return v !== selectedValue;\n      }); // Add keys if tree conduction\n\n      if (treeConduction) {\n        // Should keep missing values\n        var _splitRawValues = splitRawValues(newRawValues),\n          missingRawValues = _splitRawValues.missingRawValues,\n          existRawValues = _splitRawValues.existRawValues;\n        var keyList = existRawValues.map(function (val) {\n          return valueEntities.get(val).key;\n        }); // Conduction by selected or not\n\n        var checkedKeys;\n        if (selected) {\n          var _conductCheck = conductCheck(keyList, true, keyEntities);\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(keyList, {\n            checked: false,\n            halfCheckedKeys: rawHalfCheckedValues\n          }, keyEntities);\n          checkedKeys = _conductCheck2.checkedKeys;\n        } // Fill back of keys\n\n        newRawValues = [].concat(_toConsumableArray(missingRawValues), _toConsumableArray(checkedKeys.map(function (key) {\n          return keyEntities[key].node[mergedFieldNames.value];\n        })));\n      }\n      triggerChange(newRawValues, {\n        selected: selected,\n        triggerValue: selectedValue\n      }, source || 'option');\n    } // Trigger select event\n\n    if (selected || !mergedMultiple) {\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedValue, fillLegacyProps(node));\n    } else {\n      onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectedValue, fillLegacyProps(node));\n    }\n  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues]); // ========================== Dropdown ==========================\n\n  var onInternalDropdownVisibleChange = React.useCallback(function (open) {\n    if (onDropdownVisibleChange) {\n      var legacyParam = {};\n      Object.defineProperty(legacyParam, 'documentClickClose', {\n        get: function get() {\n          warning(false, 'Second param of `onDropdownVisibleChange` has been removed.');\n          return false;\n        }\n      });\n      onDropdownVisibleChange(open, legacyParam);\n    }\n  }, [onDropdownVisibleChange]); // ====================== Display Change ========================\n\n  var onDisplayValuesChange = useRefFunc(function (newValues, info) {\n    var newRawValues = newValues.map(function (item) {\n      return item.value;\n    });\n    if (info.type === 'clear') {\n      triggerChange(newRawValues, {}, 'selection');\n      return;\n    } // TreeSelect only have multiple mode which means display change only has remove\n\n    if (info.values.length) {\n      onOptionSelect(info.values[0].value, {\n        selected: false,\n        source: 'selection'\n      });\n    }\n  }); // ========================== Context ===========================\n\n  var treeSelectContext = React.useMemo(function () {\n    return {\n      virtual: virtual,\n      dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      treeData: filteredTreeData,\n      fieldNames: mergedFieldNames,\n      onSelect: onOptionSelect,\n      treeExpandAction: treeExpandAction\n    };\n  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, filteredTreeData, mergedFieldNames, onOptionSelect, treeExpandAction]); // ======================= Legacy Context =======================\n\n  var legacyContext = React.useMemo(function () {\n    return {\n      checkable: mergedCheckable,\n      loadData: loadData,\n      treeLoadedKeys: treeLoadedKeys,\n      onTreeLoad: onTreeLoad,\n      checkedKeys: rawCheckedValues,\n      halfCheckedKeys: rawHalfCheckedValues,\n      treeDefaultExpandAll: treeDefaultExpandAll,\n      treeExpandedKeys: treeExpandedKeys,\n      treeDefaultExpandedKeys: treeDefaultExpandedKeys,\n      onTreeExpand: onTreeExpand,\n      treeIcon: treeIcon,\n      treeMotion: treeMotion,\n      showTreeIcon: showTreeIcon,\n      switcherIcon: switcherIcon,\n      treeLine: treeLine,\n      treeNodeFilterProp: treeNodeFilterProp,\n      keyEntities: keyEntities\n    };\n  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]); // =========================== Render ===========================\n\n  return /*#__PURE__*/React.createElement(TreeSelectContext.Provider, {\n    value: treeSelectContext\n  }, /*#__PURE__*/React.createElement(LegacyContext.Provider, {\n    value: legacyContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({\n    ref: ref\n  }, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    mode: mergedMultiple ? 'multiple' : undefined // >>> Display Value\n    ,\n\n    displayValues: cachedDisplayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch // >>> Options\n    ,\n\n    OptionList: OptionList,\n    emptyOptions: !mergedTreeData.length,\n    onDropdownVisibleChange: onInternalDropdownVisibleChange,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }))));\n}); // Assign name for Debug\n\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\nvar GenericTreeSelect = TreeSelect;\nGenericTreeSelect.TreeNode = TreeNode;\nGenericTreeSelect.SHOW_ALL = SHOW_ALL;\nGenericTreeSelect.SHOW_PARENT = SHOW_PARENT;\nGenericTreeSelect.SHOW_CHILD = SHOW_CHILD;\nexport default GenericTreeSelect;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_typeof", "_excluded", "React", "BaseSelect", "conduct<PERSON>heck", "useId", "useMergedState", "OptionList", "TreeNode", "formatStrategyValues", "SHOW_ALL", "SHOW_PARENT", "SHOW_CHILD", "TreeSelectContext", "LegacyContext", "useTreeData", "toArray", "fillFieldNames", "isNil", "useCache", "useRefFunc", "useDataEntities", "fillAdditionalInfo", "fillLegacyProps", "useCheckedKeys", "useFilterTreeData", "warningProps", "warning", "isRawValue", "value", "TreeSelect", "forwardRef", "props", "ref", "id", "_props$prefixCls", "prefixCls", "defaultValue", "onChange", "onSelect", "onDeselect", "searchValue", "inputValue", "onSearch", "_props$autoClearSearc", "autoClearSearchValue", "filterTreeNode", "_props$treeNodeFilter", "treeNodeFilterProp", "_props$showCheckedStr", "showCheckedStrategy", "treeNodeLabelProp", "multiple", "treeCheckable", "treeCheckStrictly", "labelInValue", "fieldNames", "treeDataSimpleMode", "treeData", "children", "loadData", "treeLoaded<PERSON><PERSON>s", "onTreeLoad", "treeDefaultExpandAll", "treeExpandedKeys", "treeDefaultExpandedKeys", "onTreeExpand", "treeExpandAction", "virtual", "_props$listHeight", "listHeight", "_props$listItemHeight", "listItemHeight", "onDropdownVisibleChange", "_props$dropdownMatchS", "dropdownMatchSelectWidth", "treeLine", "treeIcon", "showTreeIcon", "switcherIcon", "treeMotion", "restProps", "mergedId", "treeConduction", "mergedCheckable", "mergedLabelInValue", "mergedMultiple", "_useMergedState", "_useMergedState2", "internalValue", "setInternalValue", "process", "env", "NODE_ENV", "mergedFieldNames", "useMemo", "JSON", "stringify", "_useMergedState3", "undefined", "postState", "search", "_useMergedState4", "mergedSearchValue", "setSearchValue", "onInternalSearch", "searchText", "mergedTreeData", "_useDataEntities", "keyEntities", "valueEntities", "splitRawValues", "useCallback", "newRawValues", "missing<PERSON>aw<PERSON><PERSON><PERSON>", "existRawValues", "for<PERSON>ach", "val", "has", "push", "filteredTreeData", "get<PERSON><PERSON><PERSON>", "item", "titleList", "_title", "i", "length", "title", "to<PERSON><PERSON>led<PERSON><PERSON><PERSON>", "draftV<PERSON><PERSON>", "values", "map", "convert2LabelValues", "rawLabel", "label", "rawValue", "rawHalfChecked", "halfChecked", "rawDisabled", "entity", "get", "_raw<PERSON>abel", "node", "disabled", "labelInValueItem", "find", "labeledItem", "rawMixedLabeledValues", "_React$useMemo", "fullCheckV<PERSON>ues", "half<PERSON><PERSON>ck<PERSON><PERSON><PERSON>", "_React$useMemo2", "rawLabeledValues", "rawHalfLabeledValues", "rawValues", "_useCheckedKeys", "_useCheckedKeys2", "rawCheckedValues", "rawHalfCheckedValues", "displayValues", "displayKeys", "key", "_keyEntities$key$node", "_keyEntities$key", "_keyEntities$key$node2", "labeledV<PERSON>ues", "targetItem", "rawDisplayValues", "firstVal", "_item$label", "_useCache", "_useCache2", "cachedDisplayValues", "trigger<PERSON>hange", "extra", "source", "eventValues", "formattedKeyList", "_ref", "triggerValue", "selected", "returnRawValues", "halfV<PERSON>ues", "filter", "includes", "concat", "return<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "additionalInfo", "preValue", "showPosition", "checked", "returnV<PERSON>ues", "onOptionSelect", "<PERSON><PERSON><PERSON>", "_ref2", "_node$mergedFieldName", "selected<PERSON><PERSON><PERSON>", "v", "_splitRawValues", "keyList", "checked<PERSON>eys", "_conductCheck", "_conductCheck2", "halfC<PERSON>cked<PERSON>eys", "onInternalDropdownVisibleChange", "open", "legacyParam", "Object", "defineProperty", "onDisplayValuesChange", "newValues", "info", "type", "treeSelectContext", "legacyContext", "checkable", "createElement", "Provider", "mode", "emptyOptions", "displayName", "GenericTreeSelect"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/TreeSelect.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"prefixCls\", \"value\", \"defaultValue\", \"onChange\", \"onSelect\", \"onDeselect\", \"searchValue\", \"inputValue\", \"onSearch\", \"autoClearSearchValue\", \"filterTreeNode\", \"treeNodeFilterProp\", \"showCheckedStrategy\", \"treeNodeLabelProp\", \"multiple\", \"treeCheckable\", \"treeCheckStrictly\", \"labelInValue\", \"fieldNames\", \"treeDataSimpleMode\", \"treeData\", \"children\", \"loadData\", \"treeLoadedKeys\", \"onTreeLoad\", \"treeDefaultExpandAll\", \"treeExpandedKeys\", \"treeDefaultExpandedKeys\", \"onTreeExpand\", \"treeExpandAction\", \"virtual\", \"listHeight\", \"listItemHeight\", \"onDropdownVisibleChange\", \"dropdownMatchSelectWidth\", \"treeLine\", \"treeIcon\", \"showTreeIcon\", \"switcherIcon\", \"treeMotion\"];\nimport * as React from 'react';\nimport { BaseSelect } from 'rc-select';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport useId from \"rc-select/es/hooks/useId\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport OptionList from \"./OptionList\";\nimport TreeNode from \"./TreeNode\";\nimport { formatStrategyValues, SHOW_ALL, SHOW_PARENT, SHOW_CHILD } from \"./utils/strategyUtil\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport LegacyContext from \"./LegacyContext\";\nimport useTreeData from \"./hooks/useTreeData\";\nimport { toArray, fillFieldNames, isNil } from \"./utils/valueUtil\";\nimport useCache from \"./hooks/useCache\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport useDataEntities from \"./hooks/useDataEntities\";\nimport { fillAdditionalInfo, fillLegacyProps } from \"./utils/legacyUtil\";\nimport useCheckedKeys from \"./hooks/useCheckedKeys\";\nimport useFilterTreeData from \"./hooks/useFilterTreeData\";\nimport warningProps from \"./utils/warningPropsUtil\";\nimport warning from \"rc-util/es/warning\";\n\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\n\nvar TreeSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,\n      value = props.value,\n      defaultValue = props.defaultValue,\n      onChange = props.onChange,\n      onSelect = props.onSelect,\n      onDeselect = props.onDeselect,\n      searchValue = props.searchValue,\n      inputValue = props.inputValue,\n      onSearch = props.onSearch,\n      _props$autoClearSearc = props.autoClearSearchValue,\n      autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n      filterTreeNode = props.filterTreeNode,\n      _props$treeNodeFilter = props.treeNodeFilterProp,\n      treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,\n      _props$showCheckedStr = props.showCheckedStrategy,\n      showCheckedStrategy = _props$showCheckedStr === void 0 ? SHOW_CHILD : _props$showCheckedStr,\n      treeNodeLabelProp = props.treeNodeLabelProp,\n      multiple = props.multiple,\n      treeCheckable = props.treeCheckable,\n      treeCheckStrictly = props.treeCheckStrictly,\n      labelInValue = props.labelInValue,\n      fieldNames = props.fieldNames,\n      treeDataSimpleMode = props.treeDataSimpleMode,\n      treeData = props.treeData,\n      children = props.children,\n      loadData = props.loadData,\n      treeLoadedKeys = props.treeLoadedKeys,\n      onTreeLoad = props.onTreeLoad,\n      treeDefaultExpandAll = props.treeDefaultExpandAll,\n      treeExpandedKeys = props.treeExpandedKeys,\n      treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,\n      onTreeExpand = props.onTreeExpand,\n      treeExpandAction = props.treeExpandAction,\n      virtual = props.virtual,\n      _props$listHeight = props.listHeight,\n      listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n      _props$listItemHeight = props.listItemHeight,\n      listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n      onDropdownVisibleChange = props.onDropdownVisibleChange,\n      _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n      dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n      treeLine = props.treeLine,\n      treeIcon = props.treeIcon,\n      showTreeIcon = props.showTreeIcon,\n      switcherIcon = props.switcherIcon,\n      treeMotion = props.treeMotion,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var mergedId = useId(id);\n  var treeConduction = treeCheckable && !treeCheckStrictly;\n  var mergedCheckable = treeCheckable || treeCheckStrictly;\n  var mergedLabelInValue = treeCheckStrictly || labelInValue;\n  var mergedMultiple = mergedCheckable || multiple;\n\n  var _useMergedState = useMergedState(defaultValue, {\n    value: value\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      internalValue = _useMergedState2[0],\n      setInternalValue = _useMergedState2[1]; // ========================== Warning ===========================\n\n\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  } // ========================= FieldNames =========================\n\n\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames);\n  },\n  /* eslint-disable react-hooks/exhaustive-deps */\n  [JSON.stringify(fieldNames)]\n  /* eslint-enable react-hooks/exhaustive-deps */\n  ); // =========================== Search ===========================\n\n  var _useMergedState3 = useMergedState('', {\n    value: searchValue !== undefined ? searchValue : inputValue,\n    postState: function postState(search) {\n      return search || '';\n    }\n  }),\n      _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n      mergedSearchValue = _useMergedState4[0],\n      setSearchValue = _useMergedState4[1];\n\n  var onInternalSearch = function onInternalSearch(searchText) {\n    setSearchValue(searchText);\n    onSearch === null || onSearch === void 0 ? void 0 : onSearch(searchText);\n  }; // ============================ Data ============================\n  // `useTreeData` only do convert of `children` or `simpleMode`.\n  // Else will return origin `treeData` for perf consideration.\n  // Do not do anything to loop the data.\n\n\n  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);\n\n  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),\n      keyEntities = _useDataEntities.keyEntities,\n      valueEntities = _useDataEntities.valueEntities;\n  /** Get `missingRawValues` which not exist in the tree yet */\n\n\n  var splitRawValues = React.useCallback(function (newRawValues) {\n    var missingRawValues = [];\n    var existRawValues = []; // Keep missing value in the cache\n\n    newRawValues.forEach(function (val) {\n      if (valueEntities.has(val)) {\n        existRawValues.push(val);\n      } else {\n        missingRawValues.push(val);\n      }\n    });\n    return {\n      missingRawValues: missingRawValues,\n      existRawValues: existRawValues\n    };\n  }, [valueEntities]); // Filtered Tree\n\n  var filteredTreeData = useFilterTreeData(mergedTreeData, mergedSearchValue, {\n    fieldNames: mergedFieldNames,\n    treeNodeFilterProp: treeNodeFilterProp,\n    filterTreeNode: filterTreeNode\n  }); // =========================== Label ============================\n\n  var getLabel = React.useCallback(function (item) {\n    if (item) {\n      if (treeNodeLabelProp) {\n        return item[treeNodeLabelProp];\n      } // Loop from fieldNames\n\n\n      var titleList = mergedFieldNames._title;\n\n      for (var i = 0; i < titleList.length; i += 1) {\n        var title = item[titleList[i]];\n\n        if (title !== undefined) {\n          return title;\n        }\n      }\n    }\n  }, [mergedFieldNames, treeNodeLabelProp]); // ========================= Wrap Value =========================\n\n  var toLabeledValues = React.useCallback(function (draftValues) {\n    var values = toArray(draftValues);\n    return values.map(function (val) {\n      if (isRawValue(val)) {\n        return {\n          value: val\n        };\n      }\n\n      return val;\n    });\n  }, []);\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    var values = toLabeledValues(draftValues);\n    return values.map(function (item) {\n      var rawLabel = item.label;\n      var rawValue = item.value,\n          rawHalfChecked = item.halfChecked;\n      var rawDisabled;\n      var entity = valueEntities.get(rawValue); // Fill missing label & status\n\n      if (entity) {\n        var _rawLabel;\n\n        rawLabel = (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);\n        rawDisabled = entity.node.disabled;\n      } else if (rawLabel === undefined) {\n        // We try to find in current `labelInValue` value\n        var labelInValueItem = toLabeledValues(internalValue).find(function (labeledItem) {\n          return labeledItem.value === rawValue;\n        });\n        rawLabel = labelInValueItem.label;\n      }\n\n      return {\n        label: rawLabel,\n        value: rawValue,\n        halfChecked: rawHalfChecked,\n        disabled: rawDisabled\n      };\n    });\n  }, [valueEntities, getLabel, toLabeledValues, internalValue]); // =========================== Values ===========================\n\n  var rawMixedLabeledValues = React.useMemo(function () {\n    return toLabeledValues(internalValue);\n  }, [toLabeledValues, internalValue]); // Split value into full check and half check\n\n  var _React$useMemo = React.useMemo(function () {\n    var fullCheckValues = [];\n    var halfCheckValues = [];\n    rawMixedLabeledValues.forEach(function (item) {\n      if (item.halfChecked) {\n        halfCheckValues.push(item);\n      } else {\n        fullCheckValues.push(item);\n      }\n    });\n    return [fullCheckValues, halfCheckValues];\n  }, [rawMixedLabeledValues]),\n      _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n      rawLabeledValues = _React$useMemo2[0],\n      rawHalfLabeledValues = _React$useMemo2[1]; // const [mergedValues] = useCache(rawLabeledValues);\n\n\n  var rawValues = React.useMemo(function () {\n    return rawLabeledValues.map(function (item) {\n      return item.value;\n    });\n  }, [rawLabeledValues]); // Convert value to key. Will fill missed keys for conduct check.\n\n  var _useCheckedKeys = useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),\n      _useCheckedKeys2 = _slicedToArray(_useCheckedKeys, 2),\n      rawCheckedValues = _useCheckedKeys2[0],\n      rawHalfCheckedValues = _useCheckedKeys2[1]; // Convert rawCheckedKeys to check strategy related values\n\n\n  var displayValues = React.useMemo(function () {\n    // Collect keys which need to show\n    var displayKeys = formatStrategyValues(rawCheckedValues, showCheckedStrategy, keyEntities, mergedFieldNames); // Convert to value and filled with label\n\n    var values = displayKeys.map(function (key) {\n      var _keyEntities$key$node, _keyEntities$key, _keyEntities$key$node2;\n\n      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 ? void 0 : (_keyEntities$key$node2 = _keyEntities$key.node) === null || _keyEntities$key$node2 === void 0 ? void 0 : _keyEntities$key$node2[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;\n    }); // Back fill with origin label\n\n    var labeledValues = values.map(function (val) {\n      var targetItem = rawLabeledValues.find(function (item) {\n        return item.value === val;\n      });\n      return {\n        value: val,\n        label: targetItem === null || targetItem === void 0 ? void 0 : targetItem.label\n      };\n    });\n    var rawDisplayValues = convert2LabelValues(labeledValues);\n    var firstVal = rawDisplayValues[0];\n\n    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {\n      return [];\n    }\n\n    return rawDisplayValues.map(function (item) {\n      var _item$label;\n\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, showCheckedStrategy, keyEntities]);\n\n  var _useCache = useCache(displayValues),\n      _useCache2 = _slicedToArray(_useCache, 1),\n      cachedDisplayValues = _useCache2[0]; // =========================== Change ===========================\n\n\n  var triggerChange = useRefFunc(function (newRawValues, extra, source) {\n    var labeledValues = convert2LabelValues(newRawValues);\n    setInternalValue(labeledValues); // Clean up if needed\n\n    if (autoClearSearchValue) {\n      setSearchValue('');\n    } // Generate rest parameters is costly, so only do it when necessary\n\n\n    if (onChange) {\n      var eventValues = newRawValues;\n\n      if (treeConduction) {\n        var formattedKeyList = formatStrategyValues(newRawValues, showCheckedStrategy, keyEntities, mergedFieldNames);\n        eventValues = formattedKeyList.map(function (key) {\n          var entity = valueEntities.get(key);\n          return entity ? entity.node[mergedFieldNames.value] : key;\n        });\n      }\n\n      var _ref = extra || {\n        triggerValue: undefined,\n        selected: undefined\n      },\n          triggerValue = _ref.triggerValue,\n          selected = _ref.selected;\n\n      var returnRawValues = eventValues; // We need fill half check back\n\n      if (treeCheckStrictly) {\n        var halfValues = rawHalfLabeledValues.filter(function (item) {\n          return !eventValues.includes(item.value);\n        });\n        returnRawValues = [].concat(_toConsumableArray(returnRawValues), _toConsumableArray(halfValues));\n      }\n\n      var returnLabeledValues = convert2LabelValues(returnRawValues);\n      var additionalInfo = {\n        // [Legacy] Always return as array contains label & value\n        preValue: rawLabeledValues,\n        triggerValue: triggerValue\n      }; // [Legacy] Fill legacy data if user query.\n      // This is expansive that we only fill when user query\n      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx\n\n      var showPosition = true;\n\n      if (treeCheckStrictly || source === 'selection' && !selected) {\n        showPosition = false;\n      }\n\n      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);\n\n      if (mergedCheckable) {\n        additionalInfo.checked = selected;\n      } else {\n        additionalInfo.selected = selected;\n      }\n\n      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {\n        return item.value;\n      });\n      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {\n        return item.label;\n      }), additionalInfo);\n    }\n  }); // ========================== Options ===========================\n\n  /** Trigger by option list */\n\n  var onOptionSelect = React.useCallback(function (selectedKey, _ref2) {\n    var _node$mergedFieldName;\n\n    var selected = _ref2.selected,\n        source = _ref2.source;\n    var entity = keyEntities[selectedKey];\n    var node = entity === null || entity === void 0 ? void 0 : entity.node;\n    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey; // Never be falsy but keep it safe\n\n    if (!mergedMultiple) {\n      // Single mode always set value\n      triggerChange([selectedValue], {\n        selected: true,\n        triggerValue: selectedValue\n      }, 'option');\n    } else {\n      var newRawValues = selected ? [].concat(_toConsumableArray(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {\n        return v !== selectedValue;\n      }); // Add keys if tree conduction\n\n      if (treeConduction) {\n        // Should keep missing values\n        var _splitRawValues = splitRawValues(newRawValues),\n            missingRawValues = _splitRawValues.missingRawValues,\n            existRawValues = _splitRawValues.existRawValues;\n\n        var keyList = existRawValues.map(function (val) {\n          return valueEntities.get(val).key;\n        }); // Conduction by selected or not\n\n        var checkedKeys;\n\n        if (selected) {\n          var _conductCheck = conductCheck(keyList, true, keyEntities);\n\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(keyList, {\n            checked: false,\n            halfCheckedKeys: rawHalfCheckedValues\n          }, keyEntities);\n\n          checkedKeys = _conductCheck2.checkedKeys;\n        } // Fill back of keys\n\n\n        newRawValues = [].concat(_toConsumableArray(missingRawValues), _toConsumableArray(checkedKeys.map(function (key) {\n          return keyEntities[key].node[mergedFieldNames.value];\n        })));\n      }\n\n      triggerChange(newRawValues, {\n        selected: selected,\n        triggerValue: selectedValue\n      }, source || 'option');\n    } // Trigger select event\n\n\n    if (selected || !mergedMultiple) {\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedValue, fillLegacyProps(node));\n    } else {\n      onDeselect === null || onDeselect === void 0 ? void 0 : onDeselect(selectedValue, fillLegacyProps(node));\n    }\n  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues]); // ========================== Dropdown ==========================\n\n  var onInternalDropdownVisibleChange = React.useCallback(function (open) {\n    if (onDropdownVisibleChange) {\n      var legacyParam = {};\n      Object.defineProperty(legacyParam, 'documentClickClose', {\n        get: function get() {\n          warning(false, 'Second param of `onDropdownVisibleChange` has been removed.');\n          return false;\n        }\n      });\n      onDropdownVisibleChange(open, legacyParam);\n    }\n  }, [onDropdownVisibleChange]); // ====================== Display Change ========================\n\n  var onDisplayValuesChange = useRefFunc(function (newValues, info) {\n    var newRawValues = newValues.map(function (item) {\n      return item.value;\n    });\n\n    if (info.type === 'clear') {\n      triggerChange(newRawValues, {}, 'selection');\n      return;\n    } // TreeSelect only have multiple mode which means display change only has remove\n\n\n    if (info.values.length) {\n      onOptionSelect(info.values[0].value, {\n        selected: false,\n        source: 'selection'\n      });\n    }\n  }); // ========================== Context ===========================\n\n  var treeSelectContext = React.useMemo(function () {\n    return {\n      virtual: virtual,\n      dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      treeData: filteredTreeData,\n      fieldNames: mergedFieldNames,\n      onSelect: onOptionSelect,\n      treeExpandAction: treeExpandAction\n    };\n  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, filteredTreeData, mergedFieldNames, onOptionSelect, treeExpandAction]); // ======================= Legacy Context =======================\n\n  var legacyContext = React.useMemo(function () {\n    return {\n      checkable: mergedCheckable,\n      loadData: loadData,\n      treeLoadedKeys: treeLoadedKeys,\n      onTreeLoad: onTreeLoad,\n      checkedKeys: rawCheckedValues,\n      halfCheckedKeys: rawHalfCheckedValues,\n      treeDefaultExpandAll: treeDefaultExpandAll,\n      treeExpandedKeys: treeExpandedKeys,\n      treeDefaultExpandedKeys: treeDefaultExpandedKeys,\n      onTreeExpand: onTreeExpand,\n      treeIcon: treeIcon,\n      treeMotion: treeMotion,\n      showTreeIcon: showTreeIcon,\n      switcherIcon: switcherIcon,\n      treeLine: treeLine,\n      treeNodeFilterProp: treeNodeFilterProp,\n      keyEntities: keyEntities\n    };\n  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]); // =========================== Render ===========================\n\n  return /*#__PURE__*/React.createElement(TreeSelectContext.Provider, {\n    value: treeSelectContext\n  }, /*#__PURE__*/React.createElement(LegacyContext.Provider, {\n    value: legacyContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({\n    ref: ref\n  }, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    mode: mergedMultiple ? 'multiple' : undefined // >>> Display Value\n    ,\n    displayValues: cachedDisplayValues,\n    onDisplayValuesChange: onDisplayValuesChange // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch // >>> Options\n    ,\n    OptionList: OptionList,\n    emptyOptions: !mergedTreeData.length,\n    onDropdownVisibleChange: onInternalDropdownVisibleChange,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }))));\n}); // Assign name for Debug\n\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\n\nvar GenericTreeSelect = TreeSelect;\nGenericTreeSelect.TreeNode = TreeNode;\nGenericTreeSelect.SHOW_ALL = SHOW_ALL;\nGenericTreeSelect.SHOW_PARENT = SHOW_PARENT;\nGenericTreeSelect.SHOW_CHILD = SHOW_CHILD;\nexport default GenericTreeSelect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,cAAc,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC;AACprB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,oBAAoB,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC9F,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,OAAO,EAAEC,cAAc,EAAEC,KAAK,QAAQ,mBAAmB;AAClE,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,oBAAoB;AACxE,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,OAAO,MAAM,oBAAoB;AAExC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAACA,KAAK,IAAI7B,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ;AAC9C;AAEA,IAAIC,UAAU,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACbC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,gBAAgB,GAAGA,gBAAgB;IAC7EN,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBQ,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,qBAAqB,GAAGZ,KAAK,CAACa,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACtFE,cAAc,GAAGd,KAAK,CAACc,cAAc;IACrCC,qBAAqB,GAAGf,KAAK,CAACgB,kBAAkB;IAChDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;IACvFE,qBAAqB,GAAGjB,KAAK,CAACkB,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrC,UAAU,GAAGqC,qBAAqB;IAC3FE,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,iBAAiB,GAAGtB,KAAK,CAACsB,iBAAiB;IAC3CC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,UAAU,GAAGxB,KAAK,CAACwB,UAAU;IAC7BC,kBAAkB,GAAGzB,KAAK,CAACyB,kBAAkB;IAC7CC,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IACzBC,QAAQ,GAAG3B,KAAK,CAAC2B,QAAQ;IACzBC,QAAQ,GAAG5B,KAAK,CAAC4B,QAAQ;IACzBC,cAAc,GAAG7B,KAAK,CAAC6B,cAAc;IACrCC,UAAU,GAAG9B,KAAK,CAAC8B,UAAU;IAC7BC,oBAAoB,GAAG/B,KAAK,CAAC+B,oBAAoB;IACjDC,gBAAgB,GAAGhC,KAAK,CAACgC,gBAAgB;IACzCC,uBAAuB,GAAGjC,KAAK,CAACiC,uBAAuB;IACvDC,YAAY,GAAGlC,KAAK,CAACkC,YAAY;IACjCC,gBAAgB,GAAGnC,KAAK,CAACmC,gBAAgB;IACzCC,OAAO,GAAGpC,KAAK,CAACoC,OAAO;IACvBC,iBAAiB,GAAGrC,KAAK,CAACsC,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,qBAAqB,GAAGvC,KAAK,CAACwC,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9EE,uBAAuB,GAAGzC,KAAK,CAACyC,uBAAuB;IACvDC,qBAAqB,GAAG1C,KAAK,CAAC2C,wBAAwB;IACtDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FE,QAAQ,GAAG5C,KAAK,CAAC4C,QAAQ;IACzBC,QAAQ,GAAG7C,KAAK,CAAC6C,QAAQ;IACzBC,YAAY,GAAG9C,KAAK,CAAC8C,YAAY;IACjCC,YAAY,GAAG/C,KAAK,CAAC+C,YAAY;IACjCC,UAAU,GAAGhD,KAAK,CAACgD,UAAU;IAC7BC,SAAS,GAAGlF,wBAAwB,CAACiC,KAAK,EAAE/B,SAAS,CAAC;EAE1D,IAAIiF,QAAQ,GAAG7E,KAAK,CAAC6B,EAAE,CAAC;EACxB,IAAIiD,cAAc,GAAG9B,aAAa,IAAI,CAACC,iBAAiB;EACxD,IAAI8B,eAAe,GAAG/B,aAAa,IAAIC,iBAAiB;EACxD,IAAI+B,kBAAkB,GAAG/B,iBAAiB,IAAIC,YAAY;EAC1D,IAAI+B,cAAc,GAAGF,eAAe,IAAIhC,QAAQ;EAEhD,IAAImC,eAAe,GAAGjF,cAAc,CAAC+B,YAAY,EAAE;MACjDR,KAAK,EAAEA;IACT,CAAC,CAAC;IACE2D,gBAAgB,GAAG1F,cAAc,CAACyF,eAAe,EAAE,CAAC,CAAC;IACrDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG5C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCnE,YAAY,CAACM,KAAK,CAAC;EACrB,CAAC,CAAC;;EAGF,IAAI8D,gBAAgB,GAAG5F,KAAK,CAAC6F,OAAO,CAAC,YAAY;IAC/C,OAAO9E,cAAc,CAACuC,UAAU,CAAC;EACnC,CAAC,EACD;EACA,CAACwC,IAAI,CAACC,SAAS,CAACzC,UAAU,CAAC;EAC3B,+CACA,CAAC,CAAC,CAAC;;EAEH,IAAI0C,gBAAgB,GAAG5F,cAAc,CAAC,EAAE,EAAE;MACxCuB,KAAK,EAAEY,WAAW,KAAK0D,SAAS,GAAG1D,WAAW,GAAGC,UAAU;MAC3D0D,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;QACpC,OAAOA,MAAM,IAAI,EAAE;MACrB;IACF,CAAC,CAAC;IACEC,gBAAgB,GAAGxG,cAAc,CAACoG,gBAAgB,EAAE,CAAC,CAAC;IACtDK,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;IAC3DF,cAAc,CAACE,UAAU,CAAC;IAC1B/D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+D,UAAU,CAAC;EAC1E,CAAC,CAAC,CAAC;EACH;EACA;EACA;;EAGA,IAAIC,cAAc,GAAG5F,WAAW,CAAC2C,QAAQ,EAAEC,QAAQ,EAAEF,kBAAkB,CAAC;EAExE,IAAImD,gBAAgB,GAAGvF,eAAe,CAACsF,cAAc,EAAEb,gBAAgB,CAAC;IACpEe,WAAW,GAAGD,gBAAgB,CAACC,WAAW;IAC1CC,aAAa,GAAGF,gBAAgB,CAACE,aAAa;EAClD;;EAGA,IAAIC,cAAc,GAAG7G,KAAK,CAAC8G,WAAW,CAAC,UAAUC,YAAY,EAAE;IAC7D,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,cAAc,GAAG,EAAE,CAAC,CAAC;;IAEzBF,YAAY,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIP,aAAa,CAACQ,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BF,cAAc,CAACI,IAAI,CAACF,GAAG,CAAC;MAC1B,CAAC,MAAM;QACLH,gBAAgB,CAACK,IAAI,CAACF,GAAG,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,OAAO;MACLH,gBAAgB,EAAEA,gBAAgB;MAClCC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB,IAAIU,gBAAgB,GAAG/F,iBAAiB,CAACkF,cAAc,EAAEJ,iBAAiB,EAAE;IAC1E/C,UAAU,EAAEsC,gBAAgB;IAC5B9C,kBAAkB,EAAEA,kBAAkB;IACtCF,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAI2E,QAAQ,GAAGvH,KAAK,CAAC8G,WAAW,CAAC,UAAUU,IAAI,EAAE;IAC/C,IAAIA,IAAI,EAAE;MACR,IAAIvE,iBAAiB,EAAE;QACrB,OAAOuE,IAAI,CAACvE,iBAAiB,CAAC;MAChC,CAAC,CAAC;;MAGF,IAAIwE,SAAS,GAAG7B,gBAAgB,CAAC8B,MAAM;MAEvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAIE,KAAK,GAAGL,IAAI,CAACC,SAAS,CAACE,CAAC,CAAC,CAAC;QAE9B,IAAIE,KAAK,KAAK5B,SAAS,EAAE;UACvB,OAAO4B,KAAK;QACd;MACF;IACF;EACF,CAAC,EAAE,CAACjC,gBAAgB,EAAE3C,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE3C,IAAI6E,eAAe,GAAG9H,KAAK,CAAC8G,WAAW,CAAC,UAAUiB,WAAW,EAAE;IAC7D,IAAIC,MAAM,GAAGlH,OAAO,CAACiH,WAAW,CAAC;IACjC,OAAOC,MAAM,CAACC,GAAG,CAAC,UAAUd,GAAG,EAAE;MAC/B,IAAIzF,UAAU,CAACyF,GAAG,CAAC,EAAE;QACnB,OAAO;UACLxF,KAAK,EAAEwF;QACT,CAAC;MACH;MAEA,OAAOA,GAAG;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIe,mBAAmB,GAAGlI,KAAK,CAAC8G,WAAW,CAAC,UAAUiB,WAAW,EAAE;IACjE,IAAIC,MAAM,GAAGF,eAAe,CAACC,WAAW,CAAC;IACzC,OAAOC,MAAM,CAACC,GAAG,CAAC,UAAUT,IAAI,EAAE;MAChC,IAAIW,QAAQ,GAAGX,IAAI,CAACY,KAAK;MACzB,IAAIC,QAAQ,GAAGb,IAAI,CAAC7F,KAAK;QACrB2G,cAAc,GAAGd,IAAI,CAACe,WAAW;MACrC,IAAIC,WAAW;MACf,IAAIC,MAAM,GAAG7B,aAAa,CAAC8B,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC;;MAE1C,IAAII,MAAM,EAAE;QACV,IAAIE,SAAS;QAEbR,QAAQ,GAAG,CAACQ,SAAS,GAAGR,QAAQ,MAAM,IAAI,IAAIQ,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGpB,QAAQ,CAACkB,MAAM,CAACG,IAAI,CAAC;QACtGJ,WAAW,GAAGC,MAAM,CAACG,IAAI,CAACC,QAAQ;MACpC,CAAC,MAAM,IAAIV,QAAQ,KAAKlC,SAAS,EAAE;QACjC;QACA,IAAI6C,gBAAgB,GAAGhB,eAAe,CAACvC,aAAa,CAAC,CAACwD,IAAI,CAAC,UAAUC,WAAW,EAAE;UAChF,OAAOA,WAAW,CAACrH,KAAK,KAAK0G,QAAQ;QACvC,CAAC,CAAC;QACFF,QAAQ,GAAGW,gBAAgB,CAACV,KAAK;MACnC;MAEA,OAAO;QACLA,KAAK,EAAED,QAAQ;QACfxG,KAAK,EAAE0G,QAAQ;QACfE,WAAW,EAAED,cAAc;QAC3BO,QAAQ,EAAEL;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC5B,aAAa,EAAEW,QAAQ,EAAEO,eAAe,EAAEvC,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE/D,IAAI0D,qBAAqB,GAAGjJ,KAAK,CAAC6F,OAAO,CAAC,YAAY;IACpD,OAAOiC,eAAe,CAACvC,aAAa,CAAC;EACvC,CAAC,EAAE,CAACuC,eAAe,EAAEvC,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEtC,IAAI2D,cAAc,GAAGlJ,KAAK,CAAC6F,OAAO,CAAC,YAAY;MAC7C,IAAIsD,eAAe,GAAG,EAAE;MACxB,IAAIC,eAAe,GAAG,EAAE;MACxBH,qBAAqB,CAAC/B,OAAO,CAAC,UAAUM,IAAI,EAAE;QAC5C,IAAIA,IAAI,CAACe,WAAW,EAAE;UACpBa,eAAe,CAAC/B,IAAI,CAACG,IAAI,CAAC;QAC5B,CAAC,MAAM;UACL2B,eAAe,CAAC9B,IAAI,CAACG,IAAI,CAAC;QAC5B;MACF,CAAC,CAAC;MACF,OAAO,CAAC2B,eAAe,EAAEC,eAAe,CAAC;IAC3C,CAAC,EAAE,CAACH,qBAAqB,CAAC,CAAC;IACvBI,eAAe,GAAGzJ,cAAc,CAACsJ,cAAc,EAAE,CAAC,CAAC;IACnDI,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACrCE,oBAAoB,GAAGF,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;EAG/C,IAAIG,SAAS,GAAGxJ,KAAK,CAAC6F,OAAO,CAAC,YAAY;IACxC,OAAOyD,gBAAgB,CAACrB,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC1C,OAAOA,IAAI,CAAC7F,KAAK;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC2H,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAExB,IAAIG,eAAe,GAAGnI,cAAc,CAACgI,gBAAgB,EAAEC,oBAAoB,EAAEtE,cAAc,EAAE0B,WAAW,CAAC;IACrG+C,gBAAgB,GAAG9J,cAAc,CAAC6J,eAAe,EAAE,CAAC,CAAC;IACrDE,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGhD,IAAIG,aAAa,GAAG7J,KAAK,CAAC6F,OAAO,CAAC,YAAY;IAC5C;IACA,IAAIiE,WAAW,GAAGvJ,oBAAoB,CAACoJ,gBAAgB,EAAE3G,mBAAmB,EAAE2D,WAAW,EAAEf,gBAAgB,CAAC,CAAC,CAAC;;IAE9G,IAAIoC,MAAM,GAAG8B,WAAW,CAAC7B,GAAG,CAAC,UAAU8B,GAAG,EAAE;MAC1C,IAAIC,qBAAqB,EAAEC,gBAAgB,EAAEC,sBAAsB;MAEnE,OAAO,CAACF,qBAAqB,GAAG,CAACC,gBAAgB,GAAGtD,WAAW,CAACoD,GAAG,CAAC,MAAM,IAAI,IAAIE,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,gBAAgB,CAACrB,IAAI,MAAM,IAAI,IAAIsB,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACtE,gBAAgB,CAACjE,KAAK,CAAC,MAAM,IAAI,IAAIqI,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,GAAG;IAC/V,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAII,aAAa,GAAGnC,MAAM,CAACC,GAAG,CAAC,UAAUd,GAAG,EAAE;MAC5C,IAAIiD,UAAU,GAAGd,gBAAgB,CAACP,IAAI,CAAC,UAAUvB,IAAI,EAAE;QACrD,OAAOA,IAAI,CAAC7F,KAAK,KAAKwF,GAAG;MAC3B,CAAC,CAAC;MACF,OAAO;QACLxF,KAAK,EAAEwF,GAAG;QACViB,KAAK,EAAEgC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChC;MAC5E,CAAC;IACH,CAAC,CAAC;IACF,IAAIiC,gBAAgB,GAAGnC,mBAAmB,CAACiC,aAAa,CAAC;IACzD,IAAIG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAElC,IAAI,CAACjF,cAAc,IAAIkF,QAAQ,IAAItJ,KAAK,CAACsJ,QAAQ,CAAC3I,KAAK,CAAC,IAAIX,KAAK,CAACsJ,QAAQ,CAAClC,KAAK,CAAC,EAAE;MACjF,OAAO,EAAE;IACX;IAEA,OAAOiC,gBAAgB,CAACpC,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC1C,IAAI+C,WAAW;MAEf,OAAO5K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6H,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDY,KAAK,EAAE,CAACmC,WAAW,GAAG/C,IAAI,CAACY,KAAK,MAAM,IAAI,IAAImC,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG/C,IAAI,CAAC7F;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACiE,gBAAgB,EAAER,cAAc,EAAEuE,gBAAgB,EAAEL,gBAAgB,EAAEpB,mBAAmB,EAAElF,mBAAmB,EAAE2D,WAAW,CAAC,CAAC;EAEjI,IAAI6D,SAAS,GAAGvJ,QAAQ,CAAC4I,aAAa,CAAC;IACnCY,UAAU,GAAG7K,cAAc,CAAC4K,SAAS,EAAE,CAAC,CAAC;IACzCE,mBAAmB,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGzC,IAAIE,aAAa,GAAGzJ,UAAU,CAAC,UAAU6F,YAAY,EAAE6D,KAAK,EAAEC,MAAM,EAAE;IACpE,IAAIV,aAAa,GAAGjC,mBAAmB,CAACnB,YAAY,CAAC;IACrDvB,gBAAgB,CAAC2E,aAAa,CAAC,CAAC,CAAC;;IAEjC,IAAIxH,oBAAoB,EAAE;MACxB2D,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC;;IAGF,IAAIlE,QAAQ,EAAE;MACZ,IAAI0I,WAAW,GAAG/D,YAAY;MAE9B,IAAI9B,cAAc,EAAE;QAClB,IAAI8F,gBAAgB,GAAGxK,oBAAoB,CAACwG,YAAY,EAAE/D,mBAAmB,EAAE2D,WAAW,EAAEf,gBAAgB,CAAC;QAC7GkF,WAAW,GAAGC,gBAAgB,CAAC9C,GAAG,CAAC,UAAU8B,GAAG,EAAE;UAChD,IAAItB,MAAM,GAAG7B,aAAa,CAAC8B,GAAG,CAACqB,GAAG,CAAC;UACnC,OAAOtB,MAAM,GAAGA,MAAM,CAACG,IAAI,CAAChD,gBAAgB,CAACjE,KAAK,CAAC,GAAGoI,GAAG;QAC3D,CAAC,CAAC;MACJ;MAEA,IAAIiB,IAAI,GAAGJ,KAAK,IAAI;UAClBK,YAAY,EAAEhF,SAAS;UACvBiF,QAAQ,EAAEjF;QACZ,CAAC;QACGgF,YAAY,GAAGD,IAAI,CAACC,YAAY;QAChCC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MAE5B,IAAIC,eAAe,GAAGL,WAAW,CAAC,CAAC;;MAEnC,IAAI1H,iBAAiB,EAAE;QACrB,IAAIgI,UAAU,GAAG7B,oBAAoB,CAAC8B,MAAM,CAAC,UAAU7D,IAAI,EAAE;UAC3D,OAAO,CAACsD,WAAW,CAACQ,QAAQ,CAAC9D,IAAI,CAAC7F,KAAK,CAAC;QAC1C,CAAC,CAAC;QACFwJ,eAAe,GAAG,EAAE,CAACI,MAAM,CAAC7L,kBAAkB,CAACyL,eAAe,CAAC,EAAEzL,kBAAkB,CAAC0L,UAAU,CAAC,CAAC;MAClG;MAEA,IAAII,mBAAmB,GAAGtD,mBAAmB,CAACiD,eAAe,CAAC;MAC9D,IAAIM,cAAc,GAAG;QACnB;QACAC,QAAQ,EAAEpC,gBAAgB;QAC1B2B,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC;MACH;MACA;;MAEA,IAAIU,YAAY,GAAG,IAAI;MAEvB,IAAIvI,iBAAiB,IAAIyH,MAAM,KAAK,WAAW,IAAI,CAACK,QAAQ,EAAE;QAC5DS,YAAY,GAAG,KAAK;MACtB;MAEAvK,kBAAkB,CAACqK,cAAc,EAAER,YAAY,EAAElE,YAAY,EAAEN,cAAc,EAAEkF,YAAY,EAAE/F,gBAAgB,CAAC;MAE9G,IAAIV,eAAe,EAAE;QACnBuG,cAAc,CAACG,OAAO,GAAGV,QAAQ;MACnC,CAAC,MAAM;QACLO,cAAc,CAACP,QAAQ,GAAGA,QAAQ;MACpC;MAEA,IAAIW,YAAY,GAAG1G,kBAAkB,GAAGqG,mBAAmB,GAAGA,mBAAmB,CAACvD,GAAG,CAAC,UAAUT,IAAI,EAAE;QACpG,OAAOA,IAAI,CAAC7F,KAAK;MACnB,CAAC,CAAC;MACFS,QAAQ,CAACgD,cAAc,GAAGyG,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,EAAE1G,kBAAkB,GAAG,IAAI,GAAGqG,mBAAmB,CAACvD,GAAG,CAAC,UAAUT,IAAI,EAAE;QAC5H,OAAOA,IAAI,CAACY,KAAK;MACnB,CAAC,CAAC,EAAEqD,cAAc,CAAC;IACrB;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ;;EAEA,IAAIK,cAAc,GAAG9L,KAAK,CAAC8G,WAAW,CAAC,UAAUiF,WAAW,EAAEC,KAAK,EAAE;IACnE,IAAIC,qBAAqB;IAEzB,IAAIf,QAAQ,GAAGc,KAAK,CAACd,QAAQ;MACzBL,MAAM,GAAGmB,KAAK,CAACnB,MAAM;IACzB,IAAIpC,MAAM,GAAG9B,WAAW,CAACoF,WAAW,CAAC;IACrC,IAAInD,IAAI,GAAGH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,IAAI;IACtE,IAAIsD,aAAa,GAAG,CAACD,qBAAqB,GAAGrD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAChD,gBAAgB,CAACjE,KAAK,CAAC,MAAM,IAAI,IAAIsK,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGF,WAAW,CAAC,CAAC;;IAE3M,IAAI,CAAC3G,cAAc,EAAE;MACnB;MACAuF,aAAa,CAAC,CAACuB,aAAa,CAAC,EAAE;QAC7BhB,QAAQ,EAAE,IAAI;QACdD,YAAY,EAAEiB;MAChB,CAAC,EAAE,QAAQ,CAAC;IACd,CAAC,MAAM;MACL,IAAInF,YAAY,GAAGmE,QAAQ,GAAG,EAAE,CAACK,MAAM,CAAC7L,kBAAkB,CAAC8J,SAAS,CAAC,EAAE,CAAC0C,aAAa,CAAC,CAAC,GAAGvC,gBAAgB,CAAC0B,MAAM,CAAC,UAAUc,CAAC,EAAE;QAC7H,OAAOA,CAAC,KAAKD,aAAa;MAC5B,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAIjH,cAAc,EAAE;QAClB;QACA,IAAImH,eAAe,GAAGvF,cAAc,CAACE,YAAY,CAAC;UAC9CC,gBAAgB,GAAGoF,eAAe,CAACpF,gBAAgB;UACnDC,cAAc,GAAGmF,eAAe,CAACnF,cAAc;QAEnD,IAAIoF,OAAO,GAAGpF,cAAc,CAACgB,GAAG,CAAC,UAAUd,GAAG,EAAE;UAC9C,OAAOP,aAAa,CAAC8B,GAAG,CAACvB,GAAG,CAAC,CAAC4C,GAAG;QACnC,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIuC,WAAW;QAEf,IAAIpB,QAAQ,EAAE;UACZ,IAAIqB,aAAa,GAAGrM,YAAY,CAACmM,OAAO,EAAE,IAAI,EAAE1F,WAAW,CAAC;UAE5D2F,WAAW,GAAGC,aAAa,CAACD,WAAW;QACzC,CAAC,MAAM;UACL,IAAIE,cAAc,GAAGtM,YAAY,CAACmM,OAAO,EAAE;YACzCT,OAAO,EAAE,KAAK;YACda,eAAe,EAAE7C;UACnB,CAAC,EAAEjD,WAAW,CAAC;UAEf2F,WAAW,GAAGE,cAAc,CAACF,WAAW;QAC1C,CAAC,CAAC;;QAGFvF,YAAY,GAAG,EAAE,CAACwE,MAAM,CAAC7L,kBAAkB,CAACsH,gBAAgB,CAAC,EAAEtH,kBAAkB,CAAC4M,WAAW,CAACrE,GAAG,CAAC,UAAU8B,GAAG,EAAE;UAC/G,OAAOpD,WAAW,CAACoD,GAAG,CAAC,CAACnB,IAAI,CAAChD,gBAAgB,CAACjE,KAAK,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC;MACN;MAEAgJ,aAAa,CAAC5D,YAAY,EAAE;QAC1BmE,QAAQ,EAAEA,QAAQ;QAClBD,YAAY,EAAEiB;MAChB,CAAC,EAAErB,MAAM,IAAI,QAAQ,CAAC;IACxB,CAAC,CAAC;;IAGF,IAAIK,QAAQ,IAAI,CAAC9F,cAAc,EAAE;MAC/B/C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6J,aAAa,EAAE7K,eAAe,CAACuH,IAAI,CAAC,CAAC;IACpG,CAAC,MAAM;MACLtG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC4J,aAAa,EAAE7K,eAAe,CAACuH,IAAI,CAAC,CAAC;IAC1G;EACF,CAAC,EAAE,CAAC/B,cAAc,EAAED,aAAa,EAAED,WAAW,EAAEf,gBAAgB,EAAER,cAAc,EAAEoE,SAAS,EAAEmB,aAAa,EAAE1F,cAAc,EAAE5C,QAAQ,EAAEC,UAAU,EAAEqH,gBAAgB,EAAEC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAE5L,IAAI8C,+BAA+B,GAAG1M,KAAK,CAAC8G,WAAW,CAAC,UAAU6F,IAAI,EAAE;IACtE,IAAIpI,uBAAuB,EAAE;MAC3B,IAAIqI,WAAW,GAAG,CAAC,CAAC;MACpBC,MAAM,CAACC,cAAc,CAACF,WAAW,EAAE,oBAAoB,EAAE;QACvDlE,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;UAClBjH,OAAO,CAAC,KAAK,EAAE,6DAA6D,CAAC;UAC7E,OAAO,KAAK;QACd;MACF,CAAC,CAAC;MACF8C,uBAAuB,CAACoI,IAAI,EAAEC,WAAW,CAAC;IAC5C;EACF,CAAC,EAAE,CAACrI,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIwI,qBAAqB,GAAG7L,UAAU,CAAC,UAAU8L,SAAS,EAAEC,IAAI,EAAE;IAChE,IAAIlG,YAAY,GAAGiG,SAAS,CAAC/E,GAAG,CAAC,UAAUT,IAAI,EAAE;MAC/C,OAAOA,IAAI,CAAC7F,KAAK;IACnB,CAAC,CAAC;IAEF,IAAIsL,IAAI,CAACC,IAAI,KAAK,OAAO,EAAE;MACzBvC,aAAa,CAAC5D,YAAY,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC;MAC5C;IACF,CAAC,CAAC;;IAGF,IAAIkG,IAAI,CAACjF,MAAM,CAACJ,MAAM,EAAE;MACtBkE,cAAc,CAACmB,IAAI,CAACjF,MAAM,CAAC,CAAC,CAAC,CAACrG,KAAK,EAAE;QACnCuJ,QAAQ,EAAE,KAAK;QACfL,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIsC,iBAAiB,GAAGnN,KAAK,CAAC6F,OAAO,CAAC,YAAY;IAChD,OAAO;MACL3B,OAAO,EAAEA,OAAO;MAChBO,wBAAwB,EAAEA,wBAAwB;MAClDL,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9Bd,QAAQ,EAAE8D,gBAAgB;MAC1BhE,UAAU,EAAEsC,gBAAgB;MAC5BvD,QAAQ,EAAEyJ,cAAc;MACxB7H,gBAAgB,EAAEA;IACpB,CAAC;EACH,CAAC,EAAE,CAACC,OAAO,EAAEO,wBAAwB,EAAEL,UAAU,EAAEE,cAAc,EAAEgD,gBAAgB,EAAE1B,gBAAgB,EAAEkG,cAAc,EAAE7H,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAE3I,IAAImJ,aAAa,GAAGpN,KAAK,CAAC6F,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLwH,SAAS,EAAEnI,eAAe;MAC1BxB,QAAQ,EAAEA,QAAQ;MAClBC,cAAc,EAAEA,cAAc;MAC9BC,UAAU,EAAEA,UAAU;MACtB0I,WAAW,EAAE3C,gBAAgB;MAC7B8C,eAAe,EAAE7C,oBAAoB;MACrC/F,oBAAoB,EAAEA,oBAAoB;MAC1CC,gBAAgB,EAAEA,gBAAgB;MAClCC,uBAAuB,EAAEA,uBAAuB;MAChDC,YAAY,EAAEA,YAAY;MAC1BW,QAAQ,EAAEA,QAAQ;MAClBG,UAAU,EAAEA,UAAU;MACtBF,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1BH,QAAQ,EAAEA,QAAQ;MAClB5B,kBAAkB,EAAEA,kBAAkB;MACtC6D,WAAW,EAAEA;IACf,CAAC;EACH,CAAC,EAAE,CAACzB,eAAe,EAAExB,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAE+F,gBAAgB,EAAEC,oBAAoB,EAAE/F,oBAAoB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,YAAY,EAAEW,QAAQ,EAAEG,UAAU,EAAEF,YAAY,EAAEC,YAAY,EAAEH,QAAQ,EAAE5B,kBAAkB,EAAE6D,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEjR,OAAO,aAAa3G,KAAK,CAACsN,aAAa,CAAC3M,iBAAiB,CAAC4M,QAAQ,EAAE;IAClE5L,KAAK,EAAEwL;EACT,CAAC,EAAE,aAAanN,KAAK,CAACsN,aAAa,CAAC1M,aAAa,CAAC2M,QAAQ,EAAE;IAC1D5L,KAAK,EAAEyL;EACT,CAAC,EAAE,aAAapN,KAAK,CAACsN,aAAa,CAACrN,UAAU,EAAER,QAAQ,CAAC;IACvDsC,GAAG,EAAEA;EACP,CAAC,EAAEgD,SAAS,EAAE;IACZ;IACA/C,EAAE,EAAEgD,QAAQ;IACZ9C,SAAS,EAAEA,SAAS;IACpBsL,IAAI,EAAEpI,cAAc,GAAG,UAAU,GAAGa,SAAS,CAAC;IAAA;;IAE9C4D,aAAa,EAAEa,mBAAmB;IAClCqC,qBAAqB,EAAEA,qBAAqB,CAAC;IAAA;;IAE7CxK,WAAW,EAAE8D,iBAAiB;IAC9B5D,QAAQ,EAAE8D,gBAAgB,CAAC;IAAA;;IAE3BlG,UAAU,EAAEA,UAAU;IACtBoN,YAAY,EAAE,CAAChH,cAAc,CAACmB,MAAM;IACpCrD,uBAAuB,EAAEmI,+BAA+B;IACxDjI,wBAAwB,EAAEA;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC,CAAC;;AAEJ,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC/D,UAAU,CAAC8L,WAAW,GAAG,YAAY;AACvC;AAEA,IAAIC,iBAAiB,GAAG/L,UAAU;AAClC+L,iBAAiB,CAACrN,QAAQ,GAAGA,QAAQ;AACrCqN,iBAAiB,CAACnN,QAAQ,GAAGA,QAAQ;AACrCmN,iBAAiB,CAAClN,WAAW,GAAGA,WAAW;AAC3CkN,iBAAiB,CAACjN,UAAU,GAAGA,UAAU;AACzC,eAAeiN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}