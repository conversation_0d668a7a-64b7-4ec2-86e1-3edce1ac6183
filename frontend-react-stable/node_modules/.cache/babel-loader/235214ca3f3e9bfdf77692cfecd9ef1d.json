{"ast": null, "code": "var copyObject = require('./_copyObject'),\n  getSymbols = require('./_getSymbols');\n\n/**\n * Copies own symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\nmodule.exports = copySymbols;", "map": {"version": 3, "names": ["copyObject", "require", "getSymbols", "copySymbols", "source", "object", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_copySymbols.js"], "sourcesContent": ["var copyObject = require('./_copyObject'),\n    getSymbols = require('./_getSymbols');\n\n/**\n * Copies own symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\nmodule.exports = copySymbols;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,OAAOL,UAAU,CAACI,MAAM,EAAEF,UAAU,CAACE,MAAM,CAAC,EAAEC,MAAM,CAAC;AACvD;AAEAC,MAAM,CAACC,OAAO,GAAGJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}