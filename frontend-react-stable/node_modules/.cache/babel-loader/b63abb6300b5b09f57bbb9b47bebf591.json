{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = piecewise;\nvar _value = _interopRequireDefault(require(\"./value.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = _value.default;\n  var i = 0,\n    n = values.length - 1,\n    v = values[0],\n    I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function (t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "piecewise", "_value", "_interopRequireDefault", "require", "obj", "__esModule", "interpolate", "values", "undefined", "i", "n", "length", "v", "I", "Array", "t", "Math", "max", "min", "floor"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/piecewise.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = piecewise;\n\nvar _value = _interopRequireDefault(require(\"./value.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = _value.default;\n  var i = 0,\n      n = values.length - 1,\n      v = values[0],\n      I = new Array(n < 0 ? 0 : n);\n\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n\n  return function (t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,SAASA,CAACM,WAAW,EAAEC,MAAM,EAAE;EACtC,IAAIA,MAAM,KAAKC,SAAS,EAAED,MAAM,GAAGD,WAAW,EAAEA,WAAW,GAAGL,MAAM,CAACF,OAAO;EAC5E,IAAIU,CAAC,GAAG,CAAC;IACLC,CAAC,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC;IACrBC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC;IACbM,CAAC,GAAG,IAAIC,KAAK,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;EAEhC,OAAOD,CAAC,GAAGC,CAAC,EAAEG,CAAC,CAACJ,CAAC,CAAC,GAAGH,WAAW,CAACM,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAAC,EAAEE,CAAC,CAAC,CAAC;EAEpD,OAAO,UAAUM,CAAC,EAAE;IAClB,IAAIN,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,CAAC,GAAG,CAAC,EAAEM,IAAI,CAACG,KAAK,CAACJ,CAAC,IAAIL,CAAC,CAAC,CAAC,CAAC;IACxD,OAAOG,CAAC,CAACJ,CAAC,CAAC,CAACM,CAAC,GAAGN,CAAC,CAAC;EACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}