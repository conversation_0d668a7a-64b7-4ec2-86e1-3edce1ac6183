{"ast": null, "code": "import React, { cloneElement, isValidElement, createRef, PureComponent, Component, forwardRef, useRef, useState, useEffect, useLayoutEffect } from 'react';\nimport { findDOMNode } from 'react-dom';\nimport debounce from 'lodash/debounce';\nimport throttle from 'lodash/throttle'; /*! *****************************************************************************\r\n                                        Copyright (c) Microsoft Corporation.\r\n                                        Permission to use, copy, modify, and/or distribute this software for any\r\n                                        purpose with or without fee is hereby granted.\r\n                                        THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n                                        REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n                                        AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n                                        INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n                                        LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n                                        OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n                                        PERFORMANCE OF THIS SOFTWARE.\r\n                                        ***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nvar patchResizeHandler = function (resizeCallback, refreshMode, refreshRate, refreshOptions) {\n  switch (refreshMode) {\n    case 'debounce':\n      return debounce(resizeCallback, refreshRate, refreshOptions);\n    case 'throttle':\n      return throttle(resizeCallback, refreshRate, refreshOptions);\n    default:\n      return resizeCallback;\n  }\n};\nvar isFunction = function (fn) {\n  return typeof fn === 'function';\n};\nvar isSSR = function () {\n  return typeof window === 'undefined';\n};\nvar isDOMElement = function (element) {\n  return element instanceof Element || element instanceof HTMLDocument;\n};\nvar createNotifier = function (setSize, handleWidth, handleHeight) {\n  return function (_a) {\n    var width = _a.width,\n      height = _a.height;\n    setSize(function (prev) {\n      if (prev.width === width && prev.height === height) {\n        // skip if dimensions haven't changed\n        return prev;\n      }\n      if (prev.width === width && !handleHeight || prev.height === height && !handleWidth) {\n        // process `handleHeight/handleWidth` props\n        return prev;\n      }\n      return {\n        width: width,\n        height: height\n      };\n    });\n  };\n};\nvar ResizeDetector = /** @class */function (_super) {\n  __extends(ResizeDetector, _super);\n  function ResizeDetector(props) {\n    var _this = _super.call(this, props) || this;\n    _this.cancelHandler = function () {\n      if (_this.resizeHandler && _this.resizeHandler.cancel) {\n        // cancel debounced handler\n        _this.resizeHandler.cancel();\n        _this.resizeHandler = null;\n      }\n    };\n    _this.attachObserver = function () {\n      var _a = _this.props,\n        targetRef = _a.targetRef,\n        observerOptions = _a.observerOptions;\n      if (isSSR()) {\n        return;\n      }\n      if (targetRef && targetRef.current) {\n        _this.targetRef.current = targetRef.current;\n      }\n      var element = _this.getElement();\n      if (!element) {\n        // can't find element to observe\n        return;\n      }\n      if (_this.observableElement && _this.observableElement === element) {\n        // element is already observed\n        return;\n      }\n      _this.observableElement = element;\n      _this.resizeObserver.observe(element, observerOptions);\n    };\n    _this.getElement = function () {\n      var _a = _this.props,\n        querySelector = _a.querySelector,\n        targetDomEl = _a.targetDomEl;\n      if (isSSR()) return null;\n      // in case we pass a querySelector\n      if (querySelector) return document.querySelector(querySelector);\n      // in case we pass a DOM element\n      if (targetDomEl && isDOMElement(targetDomEl)) return targetDomEl;\n      // in case we pass a React ref using React.createRef()\n      if (_this.targetRef && isDOMElement(_this.targetRef.current)) return _this.targetRef.current;\n      // the worse case when we don't receive any information from the parent and the library doesn't add any wrappers\n      // we have to use a deprecated `findDOMNode` method in order to find a DOM element to attach to\n      var currentElement = findDOMNode(_this);\n      if (!currentElement) return null;\n      var renderType = _this.getRenderType();\n      switch (renderType) {\n        case 'renderProp':\n          return currentElement;\n        case 'childFunction':\n          return currentElement;\n        case 'child':\n          return currentElement;\n        case 'childArray':\n          return currentElement;\n        default:\n          return currentElement.parentElement;\n      }\n    };\n    _this.createResizeHandler = function (entries) {\n      var _a = _this.props,\n        _b = _a.handleWidth,\n        handleWidth = _b === void 0 ? true : _b,\n        _c = _a.handleHeight,\n        handleHeight = _c === void 0 ? true : _c,\n        onResize = _a.onResize;\n      if (!handleWidth && !handleHeight) return;\n      var notifyResize = createNotifier(function (setStateFunc) {\n        return _this.setState(setStateFunc, function () {\n          return onResize === null || onResize === void 0 ? void 0 : onResize(_this.state.width, _this.state.height);\n        });\n      }, handleWidth, handleHeight);\n      entries.forEach(function (entry) {\n        var _a = entry && entry.contentRect || {},\n          width = _a.width,\n          height = _a.height;\n        var shouldSetSize = !_this.skipOnMount && !isSSR();\n        if (shouldSetSize) {\n          notifyResize({\n            width: width,\n            height: height\n          });\n        }\n        _this.skipOnMount = false;\n      });\n    };\n    _this.getRenderType = function () {\n      var _a = _this.props,\n        render = _a.render,\n        children = _a.children;\n      if (isFunction(render)) {\n        // DEPRECATED. Use `Child Function Pattern` instead\n        return 'renderProp';\n      }\n      if (isFunction(children)) {\n        return 'childFunction';\n      }\n      if (isValidElement(children)) {\n        return 'child';\n      }\n      if (Array.isArray(children)) {\n        // DEPRECATED. Wrap children with a single parent\n        return 'childArray';\n      }\n      // DEPRECATED. Use `Child Function Pattern` instead\n      return 'parent';\n    };\n    var skipOnMount = props.skipOnMount,\n      refreshMode = props.refreshMode,\n      _a = props.refreshRate,\n      refreshRate = _a === void 0 ? 1000 : _a,\n      refreshOptions = props.refreshOptions;\n    _this.state = {\n      width: undefined,\n      height: undefined\n    };\n    _this.skipOnMount = skipOnMount;\n    _this.targetRef = createRef();\n    _this.observableElement = null;\n    if (isSSR()) {\n      return _this;\n    }\n    _this.resizeHandler = patchResizeHandler(_this.createResizeHandler, refreshMode, refreshRate, refreshOptions);\n    _this.resizeObserver = new window.ResizeObserver(_this.resizeHandler);\n    return _this;\n  }\n  ResizeDetector.prototype.componentDidMount = function () {\n    this.attachObserver();\n  };\n  ResizeDetector.prototype.componentDidUpdate = function () {\n    this.attachObserver();\n  };\n  ResizeDetector.prototype.componentWillUnmount = function () {\n    if (isSSR()) {\n      return;\n    }\n    this.observableElement = null;\n    this.resizeObserver.disconnect();\n    this.cancelHandler();\n  };\n  ResizeDetector.prototype.render = function () {\n    var _a = this.props,\n      render = _a.render,\n      children = _a.children,\n      _b = _a.nodeType,\n      WrapperTag = _b === void 0 ? 'div' : _b;\n    var _c = this.state,\n      width = _c.width,\n      height = _c.height;\n    var childProps = {\n      width: width,\n      height: height,\n      targetRef: this.targetRef\n    };\n    var renderType = this.getRenderType();\n    switch (renderType) {\n      case 'renderProp':\n        return render === null || render === void 0 ? void 0 : render(childProps);\n      case 'childFunction':\n        {\n          var childFunction = children;\n          return childFunction === null || childFunction === void 0 ? void 0 : childFunction(childProps);\n        }\n      case 'child':\n        {\n          // @TODO bug prone logic\n          var child = children;\n          if (child.type && typeof child.type === 'string') {\n            // child is a native DOM elements such as div, span etc\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            childProps.targetRef;\n            var nativeProps = __rest(childProps, [\"targetRef\"]);\n            return cloneElement(child, nativeProps);\n          }\n          // class or functional component otherwise\n          return cloneElement(child, childProps);\n        }\n      case 'childArray':\n        {\n          var childArray = children;\n          return childArray.map(function (el) {\n            return !!el && cloneElement(el, childProps);\n          });\n        }\n      default:\n        return React.createElement(WrapperTag, null);\n    }\n  };\n  return ResizeDetector;\n}(PureComponent);\nfunction withResizeDetector(ComponentInner, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ResizeDetectorHOC = /** @class */function (_super) {\n    __extends(ResizeDetectorHOC, _super);\n    function ResizeDetectorHOC() {\n      var _this = _super !== null && _super.apply(this, arguments) || this;\n      _this.ref = createRef();\n      return _this;\n    }\n    ResizeDetectorHOC.prototype.render = function () {\n      var _a = this.props,\n        forwardedRef = _a.forwardedRef,\n        rest = __rest(_a, [\"forwardedRef\"]);\n      var targetRef = forwardedRef !== null && forwardedRef !== void 0 ? forwardedRef : this.ref;\n      return React.createElement(ResizeDetector, __assign({}, options, {\n        targetRef: targetRef\n      }), React.createElement(ComponentInner, __assign({\n        targetRef: targetRef\n      }, rest)));\n    };\n    return ResizeDetectorHOC;\n  }(Component);\n  function forwardRefWrapper(props, ref) {\n    return React.createElement(ResizeDetectorHOC, __assign({}, props, {\n      forwardedRef: ref\n    }));\n  }\n  var name = ComponentInner.displayName || ComponentInner.name;\n  forwardRefWrapper.displayName = \"withResizeDetector(\".concat(name, \")\");\n  return forwardRef(forwardRefWrapper);\n}\nvar useEnhancedEffect = isSSR() ? useEffect : useLayoutEffect;\nfunction useResizeDetector(_a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.skipOnMount,\n    skipOnMount = _c === void 0 ? false : _c,\n    refreshMode = _b.refreshMode,\n    _d = _b.refreshRate,\n    refreshRate = _d === void 0 ? 1000 : _d,\n    refreshOptions = _b.refreshOptions,\n    _e = _b.handleWidth,\n    handleWidth = _e === void 0 ? true : _e,\n    _f = _b.handleHeight,\n    handleHeight = _f === void 0 ? true : _f,\n    targetRef = _b.targetRef,\n    observerOptions = _b.observerOptions,\n    onResize = _b.onResize;\n  var skipResize = useRef(skipOnMount);\n  var localRef = useRef(null);\n  var resizeHandler = useRef();\n  var ref = targetRef !== null && targetRef !== void 0 ? targetRef : localRef;\n  var _g = useState({\n      width: undefined,\n      height: undefined\n    }),\n    size = _g[0],\n    setSize = _g[1];\n  useEnhancedEffect(function () {\n    if (!handleWidth && !handleHeight) return;\n    var notifyResize = createNotifier(setSize, handleWidth, handleHeight);\n    var resizeCallback = function (entries) {\n      if (!handleWidth && !handleHeight) return;\n      entries.forEach(function (entry) {\n        var _a = entry && entry.contentRect || {},\n          width = _a.width,\n          height = _a.height;\n        var shouldSetSize = !skipResize.current;\n        if (shouldSetSize) {\n          notifyResize({\n            width: width,\n            height: height\n          });\n        }\n        skipResize.current = false;\n      });\n    };\n    resizeHandler.current = patchResizeHandler(resizeCallback, refreshMode, refreshRate, refreshOptions);\n    var resizeObserver = new window.ResizeObserver(resizeHandler.current);\n    if (ref.current) {\n      resizeObserver.observe(ref.current, observerOptions);\n    }\n    return function () {\n      var _a, _b;\n      resizeObserver.disconnect();\n      (_b = (_a = resizeHandler.current).cancel) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n  }, [refreshMode, refreshRate, refreshOptions, handleWidth, handleHeight, observerOptions, ref.current]);\n  useEffect(function () {\n    onResize === null || onResize === void 0 ? void 0 : onResize(size.width, size.height);\n  }, [size]);\n  return __assign({\n    ref: ref\n  }, size);\n}\nexport { ResizeDetector as default, useResizeDetector, withResizeDetector };", "map": {"version": 3, "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__extends", "__", "constructor", "prototype", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "call", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "patchResizeHandler", "resizeCallback", "refreshMode", "refreshRate", "refreshOptions", "debounce", "throttle", "isFunction", "fn", "isSSR", "window", "isDOMElement", "element", "Element", "HTMLDocument", "createNotifier", "setSize", "handleWidth", "handleHeight", "_a", "width", "height", "prev", "ResizeDetector", "_super", "props", "_this", "cancelHandler", "resize<PERSON><PERSON>ler", "cancel", "attachObserver", "targetRef", "observerOptions", "current", "getElement", "observableElement", "resizeObserver", "observe", "querySelector", "targetDomEl", "document", "currentElement", "findDOMNode", "renderType", "getRenderType", "parentElement", "createResizeHandler", "entries", "_b", "_c", "onResize", "notifyResize", "setStateFunc", "setState", "state", "for<PERSON>ach", "entry", "contentRect", "shouldSetSize", "skip<PERSON>n<PERSON>ount", "render", "children", "isValidElement", "isArray", "undefined", "createRef", "ResizeObserver", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "disconnect", "nodeType", "WrapperTag", "childProps", "childFunction", "child", "type", "nativeProps", "cloneElement", "<PERSON><PERSON><PERSON><PERSON>", "map", "el", "React", "createElement", "PureComponent", "withResizeDetector", "ComponentInner", "options", "ResizeDetectorHOC", "ref", "forwardedRef", "rest", "Component", "forwardRefWrapper", "name", "displayName", "concat", "forwardRef", "useEnhancedEffect", "useEffect", "useLayoutEffect", "useResizeDetector", "_d", "_e", "_f", "skipResize", "useRef", "localRef", "_g", "useState", "size", "default"], "sources": ["../node_modules/tslib/tslib.es6.js", "../src/utils.ts", "../src/ResizeDetector.tsx", "../src/withResizeDetector.tsx", "../src/useResizeDetector.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "import debounce from 'lodash/debounce';\nimport throttle from 'lodash/throttle';\nimport type { DebouncedFunc } from 'lodash';\n\nimport { Props, ReactResizeDetectorDimensions } from './types';\n\nexport type PatchedResizeObserverCallback = DebouncedFunc<ResizeObserverCallback> | ResizeObserverCallback;\n\nexport const patchResizeHandler = (\n  resizeCallback: ResizeObserverCallback,\n  refreshMode: Props['refreshMode'],\n  refreshRate: Props['refreshRate'],\n  refreshOptions: Props['refreshOptions']\n): PatchedResizeObserverCallback => {\n  switch (refreshMode) {\n    case 'debounce':\n      return debounce(resizeCallback, refreshRate, refreshOptions);\n    case 'throttle':\n      return throttle(resizeCallback, refreshRate, refreshOptions);\n    default:\n      return resizeCallback;\n  }\n};\n\nexport const isFunction = (fn: unknown): boolean => typeof fn === 'function';\n\nexport const isSSR = (): boolean => typeof window === 'undefined';\n\nexport const isDOMElement = (element: unknown): boolean =>\n  element instanceof Element || element instanceof HTMLDocument;\n\nexport const createNotifier =\n  (\n    setSize: React.Dispatch<React.SetStateAction<ReactResizeDetectorDimensions>>,\n    handleWidth: boolean,\n    handleHeight: boolean\n  ) =>\n  ({ width, height }: ReactResizeDetectorDimensions): void => {\n    setSize(prev => {\n      if (prev.width === width && prev.height === height) {\n        // skip if dimensions haven't changed\n        return prev;\n      }\n\n      if ((prev.width === width && !handleHeight) || (prev.height === height && !handleWidth)) {\n        // process `handleHeight/handleWidth` props\n        return prev;\n      }\n\n      return { width, height };\n    });\n  };\n", "import React, { PureComponent, isValidElement, cloneElement, createRef, ReactNode, ReactElement } from 'react';\nimport { findDOMNode } from 'react-dom';\n\nimport { patchResizeHandler, isFunction, isSSR, isDOMElement, createNotifier } from './utils';\nimport { ReactResizeDetectorDimensions, ResizeDetectorProps, ChildFunctionProps } from './types';\n\nclass ResizeDetector<ElementT extends HTMLElement = HTMLElement> extends PureComponent<\n  ResizeDetectorProps<ElementT>,\n  ReactResizeDetectorDimensions\n> {\n  skipOnMount: boolean | undefined;\n  targetRef;\n  observableElement;\n  resizeHandler;\n  resizeObserver;\n  constructor(props: ResizeDetectorProps<ElementT>) {\n    super(props);\n\n    const { skipOnMount, refreshMode, refreshRate = 1000, refreshOptions } = props;\n\n    this.state = {\n      width: undefined,\n      height: undefined\n    };\n\n    this.skipOnMount = skipOnMount;\n    this.targetRef = createRef();\n    this.observableElement = null;\n\n    if (isSSR()) {\n      return;\n    }\n\n    this.resizeHandler = patchResizeHandler(this.createResizeHandler, refreshMode, refreshRate, refreshOptions);\n    this.resizeObserver = new window.ResizeObserver(this.resizeHandler);\n  }\n\n  componentDidMount(): void {\n    this.attachObserver();\n  }\n\n  componentDidUpdate(): void {\n    this.attachObserver();\n  }\n\n  componentWillUnmount(): void {\n    if (isSSR()) {\n      return;\n    }\n    this.observableElement = null;\n    this.resizeObserver.disconnect();\n    this.cancelHandler();\n  }\n\n  cancelHandler = (): void => {\n    if (this.resizeHandler && this.resizeHandler.cancel) {\n      // cancel debounced handler\n      this.resizeHandler.cancel();\n      this.resizeHandler = null;\n    }\n  };\n\n  attachObserver = (): void => {\n    const { targetRef, observerOptions } = this.props;\n\n    if (isSSR()) {\n      return;\n    }\n\n    if (targetRef && targetRef.current) {\n      this.targetRef.current = targetRef.current;\n    }\n\n    const element = this.getElement();\n    if (!element) {\n      // can't find element to observe\n      return;\n    }\n\n    if (this.observableElement && this.observableElement === element) {\n      // element is already observed\n      return;\n    }\n\n    this.observableElement = element;\n    this.resizeObserver.observe(element, observerOptions);\n  };\n\n  getElement = (): Element | Text | null => {\n    const { querySelector, targetDomEl } = this.props;\n\n    if (isSSR()) return null;\n\n    // in case we pass a querySelector\n    if (querySelector) return document.querySelector(querySelector);\n    // in case we pass a DOM element\n    if (targetDomEl && isDOMElement(targetDomEl)) return targetDomEl;\n    // in case we pass a React ref using React.createRef()\n    if (this.targetRef && isDOMElement(this.targetRef.current)) return this.targetRef.current;\n\n    // the worse case when we don't receive any information from the parent and the library doesn't add any wrappers\n    // we have to use a deprecated `findDOMNode` method in order to find a DOM element to attach to\n    const currentElement = findDOMNode(this);\n\n    if (!currentElement) return null;\n\n    const renderType = this.getRenderType();\n    switch (renderType) {\n      case 'renderProp':\n        return currentElement;\n      case 'childFunction':\n        return currentElement;\n      case 'child':\n        return currentElement;\n      case 'childArray':\n        return currentElement;\n      default:\n        return currentElement.parentElement;\n    }\n  };\n\n  createResizeHandler: ResizeObserverCallback = (entries: ResizeObserverEntry[]): void => {\n    const { handleWidth = true, handleHeight = true, onResize } = this.props;\n\n    if (!handleWidth && !handleHeight) return;\n\n    const notifyResize = createNotifier(\n      setStateFunc => this.setState(setStateFunc, () => onResize?.(this.state.width, this.state.height)),\n      handleWidth,\n      handleHeight\n    );\n\n    entries.forEach(entry => {\n      const { width, height } = (entry && entry.contentRect) || {};\n\n      const shouldSetSize = !this.skipOnMount && !isSSR();\n      if (shouldSetSize) {\n        notifyResize({ width, height });\n      }\n\n      this.skipOnMount = false;\n    });\n  };\n\n  getRenderType = (): string => {\n    const { render, children } = this.props;\n    if (isFunction(render)) {\n      // DEPRECATED. Use `Child Function Pattern` instead\n      return 'renderProp';\n    }\n\n    if (isFunction(children)) {\n      return 'childFunction';\n    }\n\n    if (isValidElement(children)) {\n      return 'child';\n    }\n\n    if (Array.isArray(children)) {\n      // DEPRECATED. Wrap children with a single parent\n      return 'childArray';\n    }\n\n    // DEPRECATED. Use `Child Function Pattern` instead\n    return 'parent';\n  };\n\n  render() {\n    const { render, children, nodeType: WrapperTag = 'div' } = this.props;\n    const { width, height } = this.state;\n\n    const childProps = { width, height, targetRef: this.targetRef };\n    const renderType = this.getRenderType();\n\n    switch (renderType) {\n      case 'renderProp':\n        return render?.(childProps);\n      case 'childFunction': {\n        const childFunction = children as (props: ChildFunctionProps<ElementT>) => ReactNode;\n        return childFunction?.(childProps);\n      }\n      case 'child': {\n        // @TODO bug prone logic\n        const child = children as ReactElement;\n        if (child.type && typeof child.type === 'string') {\n          // child is a native DOM elements such as div, span etc\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const { targetRef, ...nativeProps } = childProps;\n          return cloneElement(child, nativeProps);\n        }\n        // class or functional component otherwise\n        return cloneElement(child, childProps);\n      }\n      case 'childArray': {\n        const childArray = children as ReactElement[];\n        return childArray.map(el => !!el && cloneElement(el, childProps));\n      }\n      default:\n        return <WrapperTag />;\n    }\n  }\n}\n\nexport default ResizeDetector;\n", "import React, { Component, createRef, forwardRef, ComponentType, ForwardedRef, MutableRefObject } from 'react';\n\nimport ResizeDetector from './ResizeDetector';\nimport { ResizeDetectorProps } from './types';\n\nfunction withResizeDetector<P, ElementT extends HTMLElement = HTMLElement>(\n  ComponentInner: ComponentType<P>,\n  options: ResizeDetectorProps<ElementT> = {}\n) {\n  class ResizeDetectorHOC extends Component<\n    PropsWithoutResizeDetectorDimensions<P> & { forwardedRef: ForwardedRef<HTMLElement> }\n  > {\n    ref = createRef<HTMLElement>();\n\n    render() {\n      const { forwardedRef, ...rest } = this.props;\n      const targetRef = forwardedRef ?? this.ref;\n\n      return (\n        <ResizeDetector {...options} targetRef={targetRef as MutableRefObject<HTMLElement>}>\n          <ComponentInner targetRef={targetRef} {...(rest as unknown as P)} />\n        </ResizeDetector>\n      );\n    }\n  }\n\n  function forwardRefWrapper(props: PropsWithoutResizeDetectorDimensions<P>, ref: ForwardedRef<HTMLElement>) {\n    return <ResizeDetectorHOC {...props} forwardedRef={ref} />;\n  }\n\n  const name = ComponentInner.displayName || ComponentInner.name;\n  forwardRefWrapper.displayName = `withResizeDetector(${name})`;\n\n  return forwardRef(forwardRefWrapper);\n}\n\n// Just Pick would be sufficient for this, but I'm trying to avoid unnecessary mapping over union types\n// https://github.com/Microsoft/TypeScript/issues/28339\ntype PropsWithoutResizeDetectorDimensions<P> = Without<Without<OptionalKey<P, 'targetRef'>, 'width'>, 'height'>;\ntype Without<T, Key> = Key extends keyof T ? Omit<T, Key> : T;\ntype OptionalKey<T, Key> = Key extends keyof T ? Omit<T, Key> & { [K in Key]?: T[K] } : T;\n\nexport default withResizeDetector;\n", "import { useLayoutEffect, useEffect, useState, useRef } from 'react';\nimport type { MutableRefObject } from 'react';\nimport type { DebouncedFunc } from 'lodash';\n\nimport { patchResizeHandler, createNotifier, isSSR } from './utils';\n\nimport type { PatchedResizeObserverCallback } from './utils';\nimport type { Props, ReactResizeDetectorDimensions } from './types';\n\nconst useEnhancedEffect = isSSR() ? useEffect : useLayoutEffect;\n\nexport interface useResizeDetectorProps<T extends HTMLElement> extends Props {\n  targetRef?: MutableRefObject<T | null>;\n}\n\nfunction useResizeDetector<T extends HTMLElement = any>({\n  skipOnMount = false,\n  refreshMode,\n  refreshRate = 1000,\n  refreshOptions,\n  handleWidth = true,\n  handleHeight = true,\n  targetRef,\n  observerOptions,\n  onResize\n}: useResizeDetectorProps<T> = {}): UseResizeDetectorReturn<T> {\n  const skipResize = useRef<boolean>(skipOnMount);\n  const localRef = useRef<T | null>(null);\n  const resizeHandler = useRef<PatchedResizeObserverCallback>();\n\n  const ref = targetRef ?? localRef;\n\n  const [size, setSize] = useState<ReactResizeDetectorDimensions>({\n    width: undefined,\n    height: undefined\n  });\n\n  useEnhancedEffect(() => {\n    if (!handleWidth && !handleHeight) return;\n\n    const notifyResize = createNotifier(setSize, handleWidth, handleHeight);\n\n    const resizeCallback: ResizeObserverCallback = (entries: ResizeObserverEntry[]) => {\n      if (!handleWidth && !handleHeight) return;\n\n      entries.forEach(entry => {\n        const { width, height } = (entry && entry.contentRect) || {};\n\n        const shouldSetSize = !skipResize.current;\n        if (shouldSetSize) {\n          notifyResize({ width, height });\n        }\n\n        skipResize.current = false;\n      });\n    };\n\n    resizeHandler.current = patchResizeHandler(resizeCallback, refreshMode, refreshRate, refreshOptions);\n\n    const resizeObserver = new window.ResizeObserver(resizeHandler.current);\n\n    if (ref.current) {\n      resizeObserver.observe(ref.current, observerOptions);\n    }\n\n    return () => {\n      resizeObserver.disconnect();\n      (resizeHandler.current as DebouncedFunc<ResizeObserverCallback>).cancel?.();\n    };\n  }, [refreshMode, refreshRate, refreshOptions, handleWidth, handleHeight, observerOptions, ref.current]);\n\n  useEffect(() => {\n    onResize?.(size.width, size.height);\n  }, [size]);\n\n  return { ref, ...size };\n}\n\nexport default useResizeDetector;\n\nexport interface UseResizeDetectorReturn<T> extends ReactResizeDetectorDimensions {\n  ref: MutableRefObject<T | null>;\n}\n"], "mappings": ";;;uCAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;AAEA,IAAIA,aAAa,GAAG,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;EAC/BF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;IAAEC,SAAS,EAAE;EAAE,CAAE,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;EAAC,CAAG,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;IAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAC,CAAE;EAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC9B,CAAC;AAEM,SAASO,SAASA,CAACR,CAAC,EAAEC,CAAC,EAAE;EAC5BF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EACnB,SAASQ,EAAEA,CAAA,EAAG;IAAE,IAAI,CAACC,WAAW,GAAGV,CAAC;EAAC;EACrCA,CAAC,CAACW,SAAS,GAAGV,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACU,MAAM,CAACX,CAAC,CAAC,IAAIQ,EAAE,CAACE,SAAS,GAAGV,CAAC,CAACU,SAAS,EAAE,IAAIF,EAAE,EAAE,CAAC;AACxF;AAEO,IAAII,QAAQ,GAAG,SAAAA,CAAA,EAAW;EAC7BA,QAAQ,GAAGX,MAAM,CAACY,MAAM,IAAI,SAASD,QAAQA,CAACE,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAIX,CAAC,IAAIU,CAAC,EAAE,IAAId,MAAM,CAACS,SAAS,CAACJ,cAAc,CAACc,IAAI,CAACL,CAAC,EAAEV,CAAC,CAAC,EAAES,CAAC,CAACT,CAAC,CAAC,GAAGU,CAAC,CAACV,CAAC,CAAC;IACxF;IACQ,OAAOS,CAAC;EAChB;EACI,OAAOF,QAAQ,CAACS,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;AAC1C;AAEO,SAASI,MAAMA,CAACP,CAAC,EAAEQ,CAAC,EAAE;EACzB,IAAIT,CAAC,GAAG,EAAE;EACV,KAAK,IAAIT,CAAC,IAAIU,CAAC,EAAE,IAAId,MAAM,CAACS,SAAS,CAACJ,cAAc,CAACc,IAAI,CAACL,CAAC,EAAEV,CAAC,CAAC,IAAIkB,CAAC,CAACC,OAAO,CAACnB,CAAC,CAAC,GAAG,CAAC,EAC/ES,CAAC,CAACT,CAAC,CAAC,GAAGU,CAAC,CAACV,CAAC,CAAC;EACf,IAAIU,CAAC,IAAI,IAAI,IAAI,OAAOd,MAAM,CAACwB,qBAAqB,KAAK,UAAU,EAC/D,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEX,CAAC,GAAGJ,MAAM,CAACwB,qBAAqB,CAACV,CAAC,CAAC,EAAEC,CAAC,GAAGX,CAAC,CAACc,MAAM,EAAEH,CAAC,EAAE,EAAE;IACpE,IAAIO,CAAC,CAACC,OAAO,CAACnB,CAAC,CAACW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIf,MAAM,CAACS,SAAS,CAACgB,oBAAoB,CAACN,IAAI,CAACL,CAAC,EAAEV,CAAC,CAACW,CAAC,CAAC,CAAC,EAC1EF,CAAC,CAACT,CAAC,CAACW,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACV,CAAC,CAACW,CAAC,CAAC,CAAC;EACjC;EACI,OAAOF,CAAC;AACZ;AC1CO,IAAMa,kBAAkB,GAAG,SAAAA,CAChCC,cAAsC,EACtCC,WAAiC,EACjCC,WAAiC,EACjCC,cAAuC;EAEvC,QAAQF,WAAW;IACjB,KAAK,UAAU;MACb,OAAOG,QAAQ,CAACJ,cAAc,EAAEE,WAAW,EAAEC,cAAc,CAAC;IAC9D,KAAK,UAAU;MACb,OAAOE,QAAQ,CAACL,cAAc,EAAEE,WAAW,EAAEC,cAAc,CAAC;IAC9D;MACE,OAAOH,cAAc;EACxB;AACH,CAAC;AAEM,IAAMM,UAAU,GAAG,SAAAA,CAACC,EAAW;EAAc,cAAOA,EAAE,KAAK,UAAU;AAAA;AAErE,IAAMC,KAAK,GAAG,SAAAA,CAAA,EAAe;EAAA,cAAOC,MAAM,KAAK,WAAW;AAA7B,CAA6B;AAE1D,IAAMC,YAAY,GAAG,SAAAA,CAACC,OAAgB;EAC3C,OAAAA,OAAO,YAAYC,OAAO,IAAID,OAAO,YAAYE,YAAY;AAA7D,CAA6D;AAExD,IAAMC,cAAc,GACzB,SAAAA,CACEC,OAA4E,EAC5EC,WAAoB,EACpBC,YAAqB;EAEvB,iBAACC,EAAgD;QAA9CC,KAAK,GAAAD,EAAA,CAAAC,KAAA;MAAEC,MAAM,GAAAF,EAAA,CAAAE,MAAA;IACdL,OAAO,CAAC,UAAAM,IAAI;MACV,IAAIA,IAAI,CAACF,KAAK,KAAKA,KAAK,IAAIE,IAAI,CAACD,MAAM,KAAKA,MAAM,EAAE;;QAElD,OAAOC,IAAI;MACZ;MAED,IAAKA,IAAI,CAACF,KAAK,KAAKA,KAAK,IAAI,CAACF,YAAY,IAAMI,IAAI,CAACD,MAAM,KAAKA,MAAM,IAAI,CAACJ,WAAY,EAAE;;QAEvF,OAAOK,IAAI;MACZ;MAED,OAAO;QAAEF,KAAK,EAAAA,KAAA;QAAEC,MAAM,EAAAA;MAAA,CAAE;IAC1B,CAAC,CAAC;GACH;AAdD,CAcC;AC7CH,IAAAE,cAAA,0BAAAC,MAAA;EAAyE5C,SAGxE,CAAA2C,cAAA,EAAAC,MAAA;EAMC,SAAAD,eAAYE,KAAoC;IAAhD,IACEC,KAAA,GAAAF,MAAA,CAAA/B,IAAA,OAAMgC,KAAK,CAAC,IAmBb;IAmBDC,KAAA,CAAAC,aAAa,GAAG;MACd,IAAID,KAAI,CAACE,aAAa,IAAIF,KAAI,CAACE,aAAa,CAACC,MAAM,EAAE;;QAEnDH,KAAI,CAACE,aAAa,CAACC,MAAM,EAAE;QAC3BH,KAAI,CAACE,aAAa,GAAG,IAAI;MAC1B;IACH,CAAC;IAEDF,KAAA,CAAAI,cAAc,GAAG;MACT,IAAAX,EAAA,GAAiCO,KAAI,CAACD,KAAK;QAAzCM,SAAS,GAAAZ,EAAA,CAAAY,SAAA;QAAEC,eAAe,GAAAb,EAAA,CAAAa,eAAe;MAEjD,IAAIvB,KAAK,EAAE,EAAE;QACX;MACD;MAED,IAAIsB,SAAS,IAAIA,SAAS,CAACE,OAAO,EAAE;QAClCP,KAAI,CAACK,SAAS,CAACE,OAAO,GAAGF,SAAS,CAACE,OAAO;MAC3C;MAED,IAAMrB,OAAO,GAAGc,KAAI,CAACQ,UAAU,EAAE;MACjC,IAAI,CAACtB,OAAO,EAAE;;QAEZ;MACD;MAED,IAAIc,KAAI,CAACS,iBAAiB,IAAIT,KAAI,CAACS,iBAAiB,KAAKvB,OAAO,EAAE;;QAEhE;MACD;MAEDc,KAAI,CAACS,iBAAiB,GAAGvB,OAAO;MAChCc,KAAI,CAACU,cAAc,CAACC,OAAO,CAACzB,OAAO,EAAEoB,eAAe,CAAC;IACvD,CAAC;IAEDN,KAAA,CAAAQ,UAAU,GAAG;MACL,IAAAf,EAAA,GAAiCO,KAAI,CAACD,KAAK;QAAzCa,aAAa,GAAAnB,EAAA,CAAAmB,aAAA;QAAEC,WAAW,GAAApB,EAAA,CAAAoB,WAAe;MAEjD,IAAI9B,KAAK,EAAE,EAAE,OAAO,IAAI;;MAGxB,IAAI6B,aAAa,EAAE,OAAOE,QAAQ,CAACF,aAAa,CAACA,aAAa,CAAC;;MAE/D,IAAIC,WAAW,IAAI5B,YAAY,CAAC4B,WAAW,CAAC,EAAE,OAAOA,WAAW;;MAEhE,IAAIb,KAAI,CAACK,SAAS,IAAIpB,YAAY,CAACe,KAAI,CAACK,SAAS,CAACE,OAAO,CAAC,EAAE,OAAOP,KAAI,CAACK,SAAS,CAACE,OAAO;;;MAIzF,IAAMQ,cAAc,GAAGC,WAAW,CAAChB,KAAI,CAAC;MAExC,IAAI,CAACe,cAAc,EAAE,OAAO,IAAI;MAEhC,IAAME,UAAU,GAAGjB,KAAI,CAACkB,aAAa,EAAE;MACvC,QAAQD,UAAU;QAChB,KAAK,YAAY;UACf,OAAOF,cAAc;QACvB,KAAK,eAAe;UAClB,OAAOA,cAAc;QACvB,KAAK,OAAO;UACV,OAAOA,cAAc;QACvB,KAAK,YAAY;UACf,OAAOA,cAAc;QACvB;UACE,OAAOA,cAAc,CAACI,aAAa;MACtC;IACH,CAAC;IAEDnB,KAAmB,CAAAoB,mBAAA,GAA2B,UAACC,OAA8B;MACrE,IAAA5B,EAAA,GAAwDO,KAAI,CAACD,KAAK;QAAhEuB,EAAkB,GAAA7B,EAAA,CAAAF,WAAA;QAAlBA,WAAW,GAAG+B,EAAA,kBAAI,GAAAA,EAAA;QAAEC,EAAA,GAAA9B,EAAA,CAAAD,YAAmB;QAAnBA,YAAY,GAAA+B,EAAA,cAAG,IAAI,GAAAA,EAAA;QAAEC,QAAQ,GAAA/B,EAAA,CAAA+B,QAAe;MAExE,IAAI,CAACjC,WAAW,IAAI,CAACC,YAAY,EAAE;MAEnC,IAAMiC,YAAY,GAAGpC,cAAc,CACjC,UAAAqC,YAAY;QAAI,OAAA1B,KAAI,CAAC2B,QAAQ,CAACD,YAAY,EAAE,YAAM;UAAA,OAAAF,QAAQ,KAAR,QAAAA,QAAQ,uBAARA,QAAQ,CAAGxB,KAAI,CAAC4B,KAAK,CAAClC,KAAK,EAAEM,KAAI,CAAC4B,KAAK,CAACjC,MAAM,CAAC;QAAA,EAAC;MAAA,GAClGJ,WAAW,EACXC,YAAY,CACb;MAED6B,OAAO,CAACQ,OAAO,CAAC,UAAAC,KAAK;QACb,IAAArC,EAAA,GAAqBqC,KAAK,IAAIA,KAAK,CAACC,WAAW,IAAK,EAAE;UAApDrC,KAAK,GAAAD,EAAA,CAAAC,KAAA;UAAEC,MAAM,GAAAF,EAAA,CAAAE,MAAuC;QAE5D,IAAMqC,aAAa,GAAG,CAAChC,KAAI,CAACiC,WAAW,IAAI,CAAClD,KAAK,EAAE;QACnD,IAAIiD,aAAa,EAAE;UACjBP,YAAY,CAAC;YAAE/B,KAAK,EAAAA,KAAA;YAAEC,MAAM,EAAAA;UAAA,CAAE,CAAC;QAChC;QAEDK,KAAI,CAACiC,WAAW,GAAG,KAAK;MAC1B,CAAC,CAAC;IACJ,CAAC;IAEDjC,KAAA,CAAAkB,aAAa,GAAG;MACR,IAAAzB,EAAA,GAAuBO,KAAI,CAACD,KAAK;QAA/BmC,MAAM,GAAAzC,EAAA,CAAAyC,MAAA;QAAEC,QAAQ,GAAA1C,EAAA,CAAA0C,QAAe;MACvC,IAAItD,UAAU,CAACqD,MAAM,CAAC,EAAE;;QAEtB,OAAO,YAAY;MACpB;MAED,IAAIrD,UAAU,CAACsD,QAAQ,CAAC,EAAE;QACxB,OAAO,eAAe;MACvB;MAED,IAAIC,cAAc,CAACD,QAAQ,CAAC,EAAE;QAC5B,OAAO,OAAO;MACf;MAED,IAAIpF,KAAK,CAACsF,OAAO,CAACF,QAAQ,CAAC,EAAE;;QAE3B,OAAO,YAAY;MACpB;;MAGD,OAAO,QAAQ;IACjB,CAAC;IApJS,IAAAF,WAAW,GAAsDlC,KAAK,CAAAkC,WAA3D;MAAEzD,WAAW,GAAyCuB,KAAK,CAAAvB,WAA9C;MAAEiB,EAAA,GAAuCM,KAAK,CAA1BtB,WAAA;MAAlBA,WAAW,GAAAgB,EAAA,cAAG,IAAI,GAAAA,EAAA;MAAEf,cAAc,GAAKqB,KAAK,CAAArB,cAAV;IAEpEsB,KAAI,CAAC4B,KAAK,GAAG;MACXlC,KAAK,EAAE4C,SAAS;MAChB3C,MAAM,EAAE2C;KACT;IAEDtC,KAAI,CAACiC,WAAW,GAAGA,WAAW;IAC9BjC,KAAI,CAACK,SAAS,GAAGkC,SAAS,EAAE;IAC5BvC,KAAI,CAACS,iBAAiB,GAAG,IAAI;IAE7B,IAAI1B,KAAK,EAAE,EAAE;;IAEZ;IAEDiB,KAAI,CAACE,aAAa,GAAG5B,kBAAkB,CAAC0B,KAAI,CAACoB,mBAAmB,EAAE5C,WAAW,EAAEC,WAAW,EAAEC,cAAc,CAAC;IAC3GsB,KAAI,CAACU,cAAc,GAAG,IAAI1B,MAAM,CAACwD,cAAc,CAACxC,KAAI,CAACE,aAAa,CAAC;;;EAGrEL,cAAA,CAAAxC,SAAA,CAAAoF,iBAAiB,GAAjB;IACE,IAAI,CAACrC,cAAc,EAAE;GACtB;EAEDP,cAAA,CAAAxC,SAAA,CAAAqF,kBAAkB,GAAlB;IACE,IAAI,CAACtC,cAAc,EAAE;GACtB;EAEDP,cAAA,CAAAxC,SAAA,CAAAsF,oBAAoB,GAApB;IACE,IAAI5D,KAAK,EAAE,EAAE;MACX;IACD;IACD,IAAI,CAAC0B,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,cAAc,CAACkC,UAAU,EAAE;IAChC,IAAI,CAAC3C,aAAa,EAAE;GACrB;EAoHDJ,cAAA,CAAAxC,SAAA,CAAA6E,MAAM,GAAN;IACQ,IAAAzC,EAAA,GAAqD,IAAI,CAACM,KAAK;MAA7DmC,MAAM,GAAAzC,EAAA,CAAAyC,MAAA;MAAEC,QAAQ,GAAA1C,EAAA,CAAA0C,QAAA;MAAEb,EAA4B,GAAA7B,EAAA,CAAAoD,QAAA;MAAlBC,UAAU,GAAGxB,EAAA,mBAAK,GAAAA,EAAe;IAC/D,IAAAC,EAAA,GAAoB,IAAI,CAACK,KAAK;MAA5BlC,KAAK,GAAA6B,EAAA,CAAA7B,KAAA;MAAEC,MAAM,GAAA4B,EAAA,CAAA5B,MAAe;IAEpC,IAAMoD,UAAU,GAAG;MAAErD,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA,MAAA;MAAEU,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE;IAC/D,IAAMY,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;IAEvC,QAAQD,UAAU;MAChB,KAAK,YAAY;QACf,OAAOiB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGa,UAAU,CAAC;MAC7B,KAAK,eAAe;QAAE;UACpB,IAAMC,aAAa,GAAGb,QAA8D;UACpF,OAAOa,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGD,UAAU,CAAC;QACnC;MACD,KAAK,OAAO;QAAE;;UAEZ,IAAME,KAAK,GAAGd,QAAwB;UACtC,IAAIc,KAAK,CAACC,IAAI,IAAI,OAAOD,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;;;YAGVH,UAAU,CAA/B1C,SAAA;YAAA,IAAK8C,WAAW,GAAAlF,MAAA,CAAK8E,UAAU,EAA1C,CAA6B;YACnC,OAAOK,YAAY,CAACH,KAAK,EAAEE,WAAW,CAAC;UACxC;;UAED,OAAOC,YAAY,CAACH,KAAK,EAAEF,UAAU,CAAC;QACvC;MACD,KAAK,YAAY;QAAE;UACjB,IAAMM,UAAU,GAAGlB,QAA0B;UAC7C,OAAOkB,UAAU,CAACC,GAAG,CAAC,UAAAC,EAAE;YAAI,QAAC,CAACA,EAAE,IAAIH,YAAY,CAACG,EAAE,EAAER,UAAU,CAAC;UAAA,EAAC;QAClE;MACD;QACE,OAAOS,KAAA,CAAAC,aAAA,CAACX,UAAU,OAAG;IACxB;GACF;EACH,OAACjD,cAAA;AAAD,CApMA,CAAyE6D,aAAa,CAoMrF;ACrMD,SAASC,kBAAkBA,CACzBC,cAAgC,EAChCC,OAA2C;EAA3C,IAAAA,OAAA;IAAAA,OAA2C;EAAA;EAE3C,IAAAC,iBAAA,0BAAAhE,MAAA;IAAgC5C,SAE/B,CAAA4G,iBAAA,EAAAhE,MAAA;IAFD,SAAAgE,kBAAA;MAAA,IAeC9D,KAAA,GAAAF,MAAA,aAAAA,MAAA,CAAA9B,KAAA,OAAAH,SAAA;MAZCmC,KAAG,CAAA+D,GAAA,GAAGxB,SAAS,EAAe;;;IAE9BuB,iBAAA,CAAAzG,SAAA,CAAA6E,MAAM,GAAN;MACE,IAAMzC,EAA4B,OAAI,CAACM,KAAK;QAApCiE,YAAY,GAAAvE,EAAA,CAAAuE,YAAA;QAAKC,IAAI,GAAAhG,MAAA,CAAAwB,EAAA,EAAvB,CAAyB,gBAAa;MAC5C,IAAMY,SAAS,GAAG2D,YAAY,KAAZ,QAAAA,YAAY,KAAZ,SAAAA,YAAY,GAAI,IAAI,CAACD,GAAG;MAE1C,OACEP,KAAA,CAAAC,aAAA,CAAC5D,cAAc,EAAAtC,QAAA,KAAKsG,OAAO,EAAE;QAAAxD,SAAS,EAAEA;MAA0C,IAChFmD,KAAC,CAAAC,aAAA,CAAAG,cAAc,EAACrG,QAAA;QAAA8C,SAAS,EAAEA;MAAS,GAAO4D,IAAqB,EAAI,CACrD;KAEpB;IACH,OAACH,iBAAA;GAfD,CAAgCI,SAAS,CAexC;EAED,SAASC,iBAAiBA,CAACpE,KAA8C,EAAEgE,GAA8B;IACvG,OAAOP,KAAA,CAAAC,aAAA,CAACK,iBAAiB,EAAKvG,QAAA,KAAAwC,KAAK;MAAEiE,YAAY,EAAED;IAAG,GAAI;;EAG5D,IAAMK,IAAI,GAAGR,cAAc,CAACS,WAAW,IAAIT,cAAc,CAACQ,IAAI;EAC9DD,iBAAiB,CAACE,WAAW,GAAG,qBAAsB,CAAAC,MAAA,CAAAF,IAAI,MAAG;EAE7D,OAAOG,UAAU,CAACJ,iBAAiB,CAAC;AACtC;ACzBA,IAAMK,iBAAiB,GAAGzF,KAAK,EAAE,GAAG0F,SAAS,GAAGC,eAAe;AAM/D,SAASC,iBAAiBA,CAA8BlF,EAUvB;MAVuB6B,EAUzB,GAAA7B,EAAA,gBAAE,GAAAA,EAAA;IAT/B8B,EAAA,GAAAD,EAAA,CAAAW,WAAmB;IAAnBA,WAAW,GAAAV,EAAA,cAAG,KAAK,GAAAA,EAAA;IACnB/C,WAAW,GAAA8C,EAAA,CAAA9C,WAAA;IACXoG,EAAA,GAAAtD,EAAA,CAAA7C,WAAkB;IAAlBA,WAAW,GAAAmG,EAAA,cAAG,IAAI,GAAAA,EAAA;IAClBlG,cAAc,GAAA4C,EAAA,CAAA5C,cAAA;IACdmG,EAAkB,GAAAvD,EAAA,CAAA/B,WAAA;IAAlBA,WAAW,GAAGsF,EAAA,kBAAI,GAAAA,EAAA;IAClBC,EAAA,GAAAxD,EAAA,CAAA9B,YAAmB;IAAnBA,YAAY,GAAAsF,EAAA,cAAG,IAAI,GAAAA,EAAA;IACnBzE,SAAS,GAAAiB,EAAA,CAAAjB,SAAA;IACTC,eAAe,GAAAgB,EAAA,CAAAhB,eAAA;IACfkB,QAAQ,GAAAF,EAAA,CAAAE,QAAA;EAER,IAAMuD,UAAU,GAAGC,MAAM,CAAU/C,WAAW,CAAC;EAC/C,IAAMgD,QAAQ,GAAGD,MAAM,CAAW,IAAI,CAAC;EACvC,IAAM9E,aAAa,GAAG8E,MAAM,EAAiC;EAE7D,IAAMjB,GAAG,GAAG1D,SAAS,aAATA,SAAS,KAAT,SAAAA,SAAS,GAAI4E,QAAQ;EAE3B,IAAAC,EAAA,GAAkBC,QAAQ,CAAgC;MAC9DzF,KAAK,EAAE4C,SAAS;MAChB3C,MAAM,EAAE2C;IACT,EAAC;IAHK8C,IAAI,GAAAF,EAAA;IAAE5F,OAAO,GAAA4F,EAAA,GAGlB;EAEFV,iBAAiB,CAAC;IAChB,IAAI,CAACjF,WAAW,IAAI,CAACC,YAAY,EAAE;IAEnC,IAAMiC,YAAY,GAAGpC,cAAc,CAACC,OAAO,EAAEC,WAAW,EAAEC,YAAY,CAAC;IAEvE,IAAMjB,cAAc,GAA2B,SAAAA,CAAC8C,OAA8B;MAC5E,IAAI,CAAC9B,WAAW,IAAI,CAACC,YAAY,EAAE;MAEnC6B,OAAO,CAACQ,OAAO,CAAC,UAAAC,KAAK;QACb,IAAArC,EAAA,GAAqBqC,KAAK,IAAIA,KAAK,CAACC,WAAW,IAAK,EAAE;UAApDrC,KAAK,GAAAD,EAAA,CAAAC,KAAA;UAAEC,MAAM,GAAAF,EAAA,CAAAE,MAAuC;QAE5D,IAAMqC,aAAa,GAAG,CAAC+C,UAAU,CAACxE,OAAO;QACzC,IAAIyB,aAAa,EAAE;UACjBP,YAAY,CAAC;YAAE/B,KAAK,EAAAA,KAAA;YAAEC,MAAM,EAAAA;UAAA,CAAE,CAAC;QAChC;QAEDoF,UAAU,CAACxE,OAAO,GAAG,KAAK;MAC5B,CAAC,CAAC;IACJ,CAAC;IAEDL,aAAa,CAACK,OAAO,GAAGjC,kBAAkB,CAACC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,CAAC;IAEpG,IAAMgC,cAAc,GAAG,IAAI1B,MAAM,CAACwD,cAAc,CAACtC,aAAa,CAACK,OAAO,CAAC;IAEvE,IAAIwD,GAAG,CAACxD,OAAO,EAAE;MACfG,cAAc,CAACC,OAAO,CAACoD,GAAG,CAACxD,OAAO,EAAED,eAAe,CAAC;IACrD;IAED,OAAO;;MACLI,cAAc,CAACkC,UAAU,EAAE;MAC3B,CAAAtB,EAAA,IAAA7B,EAAA,GAACS,aAAa,CAACK,OAAiD,EAACJ,MAAM,cAAAmB,EAAA,uBAAAA,EAAA,CAAAvD,IAAA,CAAA0B,EAAA,CAAI;IAC7E,CAAC;EACH,CAAC,EAAE,CAACjB,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEa,WAAW,EAAEC,YAAY,EAAEc,eAAe,EAAEyD,GAAG,CAACxD,OAAO,CAAC,CAAC;EAEvGkE,SAAS,CAAC;IACRjD,QAAQ,KAAR,QAAAA,QAAQ,KAAR,kBAAAA,QAAQ,CAAG4D,IAAI,CAAC1F,KAAK,EAAE0F,IAAI,CAACzF,MAAM,CAAC;EACrC,CAAC,EAAE,CAACyF,IAAI,CAAC,CAAC;EAEV,OAAA7H,QAAA;IAASwG,GAAG,EAAAA;EAAA,CAAK,EAAAqB,IAAI,CAAG;AAC1B;AAAA,SAAAvF,cAAA,IAAAwF,OAAA,EAAAV,iBAAA,EAAAhB,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}