{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport usePatchElement from '../../_util/hooks/usePatchElement';\nimport { withConfirm, withError, withInfo, withSuccess, withWarn } from '../confirm';\nimport HookModal from './HookModal';\nvar uuid = 0;\nvar ElementsHolder = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function (_props, ref) {\n  var _usePatchElement = usePatchElement(),\n    _usePatchElement2 = _slicedToArray(_usePatchElement, 2),\n    elements = _usePatchElement2[0],\n    patchElement = _usePatchElement2[1];\n  React.useImperativeHandle(ref, function () {\n    return {\n      patchElement: patchElement\n    };\n  }, []);\n  // eslint-disable-next-line react/jsx-no-useless-fragment\n  return /*#__PURE__*/React.createElement(React.Fragment, null, elements);\n}));\nexport default function useModal() {\n  var holderRef = React.useRef(null);\n  // ========================== Effect ==========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    actionQueue = _React$useState2[0],\n    setActionQueue = _React$useState2[1];\n  React.useEffect(function () {\n    if (actionQueue.length) {\n      var cloneQueue = _toConsumableArray(actionQueue);\n      cloneQueue.forEach(function (action) {\n        action();\n      });\n      setActionQueue([]);\n    }\n  }, [actionQueue]);\n  // =========================== Hook ===========================\n  var getConfirmFunc = React.useCallback(function (withFunc) {\n    return function hookConfirm(config) {\n      var _a;\n      uuid += 1;\n      var modalRef = /*#__PURE__*/React.createRef();\n      var closeFunc;\n      var modal = /*#__PURE__*/React.createElement(HookModal, {\n        key: \"modal-\".concat(uuid),\n        config: withFunc(config),\n        ref: modalRef,\n        afterClose: function afterClose() {\n          closeFunc();\n        }\n      });\n      closeFunc = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.patchElement(modal);\n      return {\n        destroy: function destroy() {\n          function destroyAction() {\n            var _a;\n            (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n          }\n          if (modalRef.current) {\n            destroyAction();\n          } else {\n            setActionQueue(function (prev) {\n              return [].concat(_toConsumableArray(prev), [destroyAction]);\n            });\n          }\n        },\n        update: function update(newConfig) {\n          function updateAction() {\n            var _a;\n            (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.update(newConfig);\n          }\n          if (modalRef.current) {\n            updateAction();\n          } else {\n            setActionQueue(function (prev) {\n              return [].concat(_toConsumableArray(prev), [updateAction]);\n            });\n          }\n        }\n      };\n    };\n  }, []);\n  var fns = React.useMemo(function () {\n    return {\n      info: getConfirmFunc(withInfo),\n      success: getConfirmFunc(withSuccess),\n      error: getConfirmFunc(withError),\n      warning: getConfirmFunc(withWarn),\n      confirm: getConfirmFunc(withConfirm)\n    };\n  }, []);\n  // eslint-disable-next-line react/jsx-key\n  return [fns, /*#__PURE__*/React.createElement(ElementsHolder, {\n    ref: holderRef\n  })];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "usePatchElement", "withConfirm", "with<PERSON><PERSON><PERSON>", "withInfo", "withSuccess", "with<PERSON><PERSON><PERSON>", "HookModal", "uuid", "ElementsHolder", "memo", "forwardRef", "_props", "ref", "_usePatchElement", "_usePatchElement2", "elements", "patchElement", "useImperativeHandle", "createElement", "Fragment", "useModal", "holder<PERSON><PERSON>", "useRef", "_React$useState", "useState", "_React$useState2", "actionQueue", "setActionQueue", "useEffect", "length", "cloneQueue", "for<PERSON>ach", "action", "getConfirmFunc", "useCallback", "with<PERSON><PERSON><PERSON>", "hookConfirm", "config", "_a", "modalRef", "createRef", "closeFunc", "modal", "key", "concat", "afterClose", "current", "destroy", "destroyAction", "prev", "update", "newConfig", "updateAction", "fns", "useMemo", "info", "success", "error", "warning", "confirm"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/modal/useModal/index.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport usePatchElement from '../../_util/hooks/usePatchElement';\nimport { withConfirm, withError, withInfo, withSuccess, withWarn } from '../confirm';\nimport HookModal from './HookModal';\nvar uuid = 0;\nvar ElementsHolder = /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(function (_props, ref) {\n  var _usePatchElement = usePatchElement(),\n    _usePatchElement2 = _slicedToArray(_usePatchElement, 2),\n    elements = _usePatchElement2[0],\n    patchElement = _usePatchElement2[1];\n  React.useImperativeHandle(ref, function () {\n    return {\n      patchElement: patchElement\n    };\n  }, []);\n  // eslint-disable-next-line react/jsx-no-useless-fragment\n  return /*#__PURE__*/React.createElement(React.Fragment, null, elements);\n}));\nexport default function useModal() {\n  var holderRef = React.useRef(null);\n  // ========================== Effect ==========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    actionQueue = _React$useState2[0],\n    setActionQueue = _React$useState2[1];\n  React.useEffect(function () {\n    if (actionQueue.length) {\n      var cloneQueue = _toConsumableArray(actionQueue);\n      cloneQueue.forEach(function (action) {\n        action();\n      });\n      setActionQueue([]);\n    }\n  }, [actionQueue]);\n  // =========================== Hook ===========================\n  var getConfirmFunc = React.useCallback(function (withFunc) {\n    return function hookConfirm(config) {\n      var _a;\n      uuid += 1;\n      var modalRef = /*#__PURE__*/React.createRef();\n      var closeFunc;\n      var modal = /*#__PURE__*/React.createElement(HookModal, {\n        key: \"modal-\".concat(uuid),\n        config: withFunc(config),\n        ref: modalRef,\n        afterClose: function afterClose() {\n          closeFunc();\n        }\n      });\n      closeFunc = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.patchElement(modal);\n      return {\n        destroy: function destroy() {\n          function destroyAction() {\n            var _a;\n            (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n          }\n          if (modalRef.current) {\n            destroyAction();\n          } else {\n            setActionQueue(function (prev) {\n              return [].concat(_toConsumableArray(prev), [destroyAction]);\n            });\n          }\n        },\n        update: function update(newConfig) {\n          function updateAction() {\n            var _a;\n            (_a = modalRef.current) === null || _a === void 0 ? void 0 : _a.update(newConfig);\n          }\n          if (modalRef.current) {\n            updateAction();\n          } else {\n            setActionQueue(function (prev) {\n              return [].concat(_toConsumableArray(prev), [updateAction]);\n            });\n          }\n        }\n      };\n    };\n  }, []);\n  var fns = React.useMemo(function () {\n    return {\n      info: getConfirmFunc(withInfo),\n      success: getConfirmFunc(withSuccess),\n      error: getConfirmFunc(withError),\n      warning: getConfirmFunc(withWarn),\n      confirm: getConfirmFunc(withConfirm)\n    };\n  }, []);\n  // eslint-disable-next-line react/jsx-key\n  return [fns, /*#__PURE__*/React.createElement(ElementsHolder, {\n    ref: holderRef\n  })];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,YAAY;AACpF,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,IAAI,GAAG,CAAC;AACZ,IAAIC,cAAc,GAAG,aAAaT,KAAK,CAACU,IAAI,CAAE,aAAaV,KAAK,CAACW,UAAU,CAAC,UAAUC,MAAM,EAAEC,GAAG,EAAE;EACjG,IAAIC,gBAAgB,GAAGb,eAAe,CAAC,CAAC;IACtCc,iBAAiB,GAAGhB,cAAc,CAACe,gBAAgB,EAAE,CAAC,CAAC;IACvDE,QAAQ,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACrCf,KAAK,CAACkB,mBAAmB,CAACL,GAAG,EAAE,YAAY;IACzC,OAAO;MACLI,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN;EACA,OAAO,aAAajB,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAACoB,QAAQ,EAAE,IAAI,EAAEJ,QAAQ,CAAC;AACzE,CAAC,CAAC,CAAC;AACH,eAAe,SAASK,QAAQA,CAAA,EAAG;EACjC,IAAIC,SAAS,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EAClC;EACA,IAAIC,eAAe,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAG3B,cAAc,CAACyB,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC1B,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAC1B,IAAIF,WAAW,CAACG,MAAM,EAAE;MACtB,IAAIC,UAAU,GAAGjC,kBAAkB,CAAC6B,WAAW,CAAC;MAChDI,UAAU,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;QACnCA,MAAM,CAAC,CAAC;MACV,CAAC,CAAC;MACFL,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,EAAE,CAACD,WAAW,CAAC,CAAC;EACjB;EACA,IAAIO,cAAc,GAAGlC,KAAK,CAACmC,WAAW,CAAC,UAAUC,QAAQ,EAAE;IACzD,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;MAClC,IAAIC,EAAE;MACN/B,IAAI,IAAI,CAAC;MACT,IAAIgC,QAAQ,GAAG,aAAaxC,KAAK,CAACyC,SAAS,CAAC,CAAC;MAC7C,IAAIC,SAAS;MACb,IAAIC,KAAK,GAAG,aAAa3C,KAAK,CAACmB,aAAa,CAACZ,SAAS,EAAE;QACtDqC,GAAG,EAAE,QAAQ,CAACC,MAAM,CAACrC,IAAI,CAAC;QAC1B8B,MAAM,EAAEF,QAAQ,CAACE,MAAM,CAAC;QACxBzB,GAAG,EAAE2B,QAAQ;QACbM,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;UAChCJ,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC;MACFA,SAAS,GAAG,CAACH,EAAE,GAAGjB,SAAS,CAACyB,OAAO,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACtB,YAAY,CAAC0B,KAAK,CAAC;MAChG,OAAO;QACLK,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1B,SAASC,aAAaA,CAAA,EAAG;YACvB,IAAIV,EAAE;YACN,CAACA,EAAE,GAAGC,QAAQ,CAACO,OAAO,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,OAAO,CAAC,CAAC;UAC3E;UACA,IAAIR,QAAQ,CAACO,OAAO,EAAE;YACpBE,aAAa,CAAC,CAAC;UACjB,CAAC,MAAM;YACLrB,cAAc,CAAC,UAAUsB,IAAI,EAAE;cAC7B,OAAO,EAAE,CAACL,MAAM,CAAC/C,kBAAkB,CAACoD,IAAI,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC;YAC7D,CAAC,CAAC;UACJ;QACF,CAAC;QACDE,MAAM,EAAE,SAASA,MAAMA,CAACC,SAAS,EAAE;UACjC,SAASC,YAAYA,CAAA,EAAG;YACtB,IAAId,EAAE;YACN,CAACA,EAAE,GAAGC,QAAQ,CAACO,OAAO,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,MAAM,CAACC,SAAS,CAAC;UACnF;UACA,IAAIZ,QAAQ,CAACO,OAAO,EAAE;YACpBM,YAAY,CAAC,CAAC;UAChB,CAAC,MAAM;YACLzB,cAAc,CAAC,UAAUsB,IAAI,EAAE;cAC7B,OAAO,EAAE,CAACL,MAAM,CAAC/C,kBAAkB,CAACoD,IAAI,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC;YAC5D,CAAC,CAAC;UACJ;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,GAAG,GAAGtD,KAAK,CAACuD,OAAO,CAAC,YAAY;IAClC,OAAO;MACLC,IAAI,EAAEtB,cAAc,CAAC9B,QAAQ,CAAC;MAC9BqD,OAAO,EAAEvB,cAAc,CAAC7B,WAAW,CAAC;MACpCqD,KAAK,EAAExB,cAAc,CAAC/B,SAAS,CAAC;MAChCwD,OAAO,EAAEzB,cAAc,CAAC5B,QAAQ,CAAC;MACjCsD,OAAO,EAAE1B,cAAc,CAAChC,WAAW;IACrC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN;EACA,OAAO,CAACoD,GAAG,EAAE,aAAatD,KAAK,CAACmB,aAAa,CAACV,cAAc,EAAE;IAC5DI,GAAG,EAAES;EACP,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module"}