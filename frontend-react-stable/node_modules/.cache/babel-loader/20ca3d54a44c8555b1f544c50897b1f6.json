{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport { toArray } from './utils/typeUtil';\nimport { validateRules } from './utils/validateUtil';\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from './utils/valueUtil';\nvar EMPTY_ERRORS = [];\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n  return prevValue !== nextValue;\n}\n// We use Class instead of Hooks here since it will cost much code by using Hooks.\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n  var _super = _createSuper(Field);\n  /**\n   * Follow state should not management in State since it will async update by React.\n   * This makes first render of form can not get correct state value.\n   */\n\n  /**\n   * Mark when touched & validated. Currently only used for `dependencies`.\n   * Note that we do not think field with `initialValue` is dirty\n   * but this will be by `isFieldDirty` func.\n   */\n\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n    _classCallCheck(this, Field);\n    _this = _super.call(this, props);\n    // Register on init\n    _this.state = {\n      resetCount: 0\n    };\n    _this.cancelRegisterFunc = null;\n    _this.mounted = false;\n    _this.touched = false;\n    _this.dirty = false;\n    _this.validatePromise = null;\n    _this.prevValidating = void 0;\n    _this.errors = EMPTY_ERRORS;\n    _this.warnings = EMPTY_ERRORS;\n    _this.cancelRegister = function () {\n      var _this$props = _this.props,\n        preserve = _this$props.preserve,\n        isListField = _this$props.isListField,\n        name = _this$props.name;\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n      _this.cancelRegisterFunc = null;\n    };\n    _this.getNamePath = function () {\n      var _this$props2 = _this.props,\n        name = _this$props2.name,\n        fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n        prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    };\n    _this.getRules = function () {\n      var _this$props3 = _this.props,\n        _this$props3$rules = _this$props3.rules,\n        rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n        fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n        return rule;\n      });\n    };\n    _this.refresh = function () {\n      if (!_this.mounted) return;\n      /**\n       * Clean up current node.\n       */\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    };\n    _this.triggerMetaEvent = function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      onMetaChange === null || onMetaChange === void 0 ? void 0 : onMetaChange(_objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n        destroy: destroy\n      }));\n    };\n    _this.onStoreChange = function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n        shouldUpdate = _this$props4.shouldUpdate,\n        _this$props4$dependen = _this$props4.dependencies,\n        dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n        onReset = _this$props4.onReset;\n      var store = info.store;\n      var namePath = _this.getNamePath();\n      var prevValue = _this.getValue(prevStore);\n      var curValue = _this.getValue(store);\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath);\n      // `setFieldsValue` is a quick access to update related status\n      if (info.type === 'valueUpdate' && info.source === 'external' && prevValue !== curValue) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n        _this.triggerMetaEvent();\n      }\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = null;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n            _this.triggerMetaEvent();\n            onReset === null || onReset === void 0 ? void 0 : onReset();\n            _this.refresh();\n            return;\n          }\n          break;\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n        case 'remove':\n          {\n            if (shouldUpdate) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'setField':\n          {\n            if (namePathMatch) {\n              var data = info.data;\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n              _this.dirty = true;\n              _this.triggerMetaEvent();\n              _this.reRender();\n              return;\n            }\n            // Handle update by `setField` with `shouldUpdate`\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath);\n            // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n            return;\n          }\n          break;\n      }\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    };\n    _this.validateRules = function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n      var currentValue = _this.getValue();\n      // Force change to async to avoid rule OOD under renderProps field\n      var rootPromise = Promise.resolve().then(function () {\n        if (!_this.mounted) {\n          return [];\n        }\n        var _this$props5 = _this.props,\n          _this$props5$validate = _this$props5.validateFirst,\n          validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate,\n          messageVariables = _this$props5.messageVariables;\n        var _ref2 = options || {},\n          triggerName = _ref2.triggerName;\n        var filteredRules = _this.getRules();\n        if (triggerName) {\n          filteredRules = filteredRules.filter(function (rule) {\n            return rule;\n          }).filter(function (rule) {\n            var validateTrigger = rule.validateTrigger;\n            if (!validateTrigger) {\n              return true;\n            }\n            var triggerList = toArray(validateTrigger);\n            return triggerList.includes(triggerName);\n          });\n        }\n        var promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n        promise.catch(function (e) {\n          return e;\n        }).then(function () {\n          var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n          if (_this.validatePromise === rootPromise) {\n            var _ruleErrors$forEach;\n            _this.validatePromise = null;\n            // Get errors & warnings\n            var nextErrors = [];\n            var nextWarnings = [];\n            (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 ? void 0 : _ruleErrors$forEach.call(ruleErrors, function (_ref3) {\n              var warningOnly = _ref3.rule.warningOnly,\n                _ref3$errors = _ref3.errors,\n                errors = _ref3$errors === void 0 ? EMPTY_ERRORS : _ref3$errors;\n              if (warningOnly) {\n                nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n              } else {\n                nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n              }\n            });\n            _this.errors = nextErrors;\n            _this.warnings = nextWarnings;\n            _this.triggerMetaEvent();\n            _this.reRender();\n          }\n        });\n        return promise;\n      });\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n      _this.triggerMetaEvent();\n      // Force trigger re-render since we need sync renderProps with new meta\n      _this.reRender();\n      return rootPromise;\n    };\n    _this.isFieldValidating = function () {\n      return !!_this.validatePromise;\n    };\n    _this.isFieldTouched = function () {\n      return _this.touched;\n    };\n    _this.isFieldDirty = function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      }\n      // Form set initialValue\n      var fieldContext = _this.props.fieldContext;\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n        getInitialValue = _fieldContext$getInte.getInitialValue;\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n      return false;\n    };\n    _this.getErrors = function () {\n      return _this.errors;\n    };\n    _this.getWarnings = function () {\n      return _this.warnings;\n    };\n    _this.isListField = function () {\n      return _this.props.isListField;\n    };\n    _this.isList = function () {\n      return _this.props.isList;\n    };\n    _this.isPreserve = function () {\n      return _this.props.preserve;\n    };\n    _this.getMeta = function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath()\n      };\n      return meta;\n    };\n    _this.getOnlyChild = function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var meta = _this.getMeta();\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), meta, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      }\n      // Filed element only\n      var childList = toChildrenArray(children);\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    };\n    _this.getValue = function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n      var namePath = _this.getNamePath();\n      return getValue(store || getFieldsValue(true), namePath);\n    };\n    _this.getControlled = function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n        trigger = _this$props6.trigger,\n        validateTrigger = _this$props6.validateTrigger,\n        getValueFromEvent = _this$props6.getValueFromEvent,\n        normalize = _this$props6.normalize,\n        valuePropName = _this$props6.valuePropName,\n        getValueProps = _this$props6.getValueProps,\n        fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n      var namePath = _this.getNamePath();\n      var getInternalHooks = fieldContext.getInternalHooks,\n        getFieldsValue = fieldContext.getFieldsValue;\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        dispatch = _getInternalHooks.dispatch;\n      var value = _this.getValue();\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      };\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      var originTriggerFunc = childProps[trigger];\n      var control = _objectSpread(_objectSpread({}, childProps), mergedGetValueProps(value));\n      // Add trigger\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n        _this.triggerMetaEvent();\n        var newValue;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n        dispatch({\n          type: 'updateValue',\n          namePath: namePath,\n          value: newValue\n        });\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      };\n      // Add validateTrigger\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          }\n          // Always use latest rules\n          var rules = _this.props.rules;\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    };\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n        initEntityValue = _getInternalHooks2.initEntityValue;\n      initEntityValue(_assertThisInitialized(_this));\n    }\n    return _this;\n  }\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n        shouldUpdate = _this$props7.shouldUpdate,\n        fieldContext = _this$props7.fieldContext;\n      this.mounted = true;\n      // Register on init\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n          registerField = _getInternalHooks3.registerField;\n        this.cancelRegisterFunc = registerField(this);\n      }\n      // One more render for component in case fields not ready\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n      var _this$getOnlyChild = this.getOnlyChild(children),\n        child = _this$getOnlyChild.child,\n        isFunction = _this$getOnlyChild.isFunction;\n      // Not need to `cloneElement` since user can handle this in render function self\n      var returnChildNode;\n      if (isFunction) {\n        returnChildNode = child;\n      } else if (/*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n  return Field;\n}(React.Component);\nField.contextType = FieldContext;\nField.defaultProps = {\n  trigger: 'onChange',\n  valuePropName: 'value'\n};\nfunction WrapperField(_ref5) {\n  var name = _ref5.name,\n    restProps = _objectWithoutProperties(_ref5, _excluded);\n  var fieldContext = React.useContext(FieldContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var key = 'keep';\n  if (!restProps.isListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  }\n  // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && restProps.isListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\nexport default WrapperField;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_defineProperty", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_excluded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning", "React", "FieldContext", "HOOK_MARK", "toArray", "validateRules", "containsNamePath", "defaultGetValueFromEvent", "getNamePath", "getValue", "EMPTY_ERRORS", "requireUpdate", "shouldUpdate", "prev", "next", "prevValue", "nextValue", "info", "source", "Field", "_React$Component", "_super", "props", "_this", "call", "state", "resetCount", "cancelRegisterFunc", "mounted", "touched", "dirty", "validatePromise", "prevValidating", "errors", "warnings", "cancelRegister", "_this$props", "preserve", "isListField", "name", "_this$props2", "fieldContext", "_fieldContext$prefixN", "prefixName", "undefined", "concat", "getRules", "_this$props3", "_this$props3$rules", "rules", "map", "rule", "refresh", "setState", "_ref", "triggerMetaEvent", "destroy", "onMetaChange", "getMeta", "onStoreChange", "prevStore", "namePathList", "_this$props4", "_this$props4$dependen", "dependencies", "onReset", "store", "namePath", "curValue", "namePathMatch", "type", "reRender", "data", "validating", "Promise", "resolve", "length", "dependencyList", "some", "dependency", "relatedFields", "options", "currentValue", "rootPromise", "then", "_this$props5", "_this$props5$validate", "validate<PERSON><PERSON><PERSON>", "messageVariables", "_ref2", "triggerName", "filteredRules", "filter", "validate<PERSON><PERSON>ger", "triggerList", "includes", "promise", "catch", "e", "ruleErrors", "arguments", "_ruleErrors$forEach", "nextErrors", "nextWarnings", "for<PERSON>ach", "_ref3", "warningOnly", "_ref3$errors", "push", "apply", "isFieldValidating", "isFieldTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialValue", "_fieldContext$getInte", "getInternalHooks", "getInitialValue", "getErrors", "getWarnings", "isList", "isPreserve", "meta", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "getControlled", "isFunction", "childList", "isValidElement", "child", "getFieldsValue", "childProps", "_this$props6", "trigger", "getValueFromEvent", "normalize", "valuePropName", "getValueProps", "mergedValidateTrigger", "_getInternalHooks", "dispatch", "value", "mergedGetValueProps", "val", "originTriggerFunc", "control", "newValue", "_len", "args", "Array", "_key", "validateTriggerList", "originTrigger", "_getInternalHooks2", "initEntityValue", "key", "componentDidMount", "_this$props7", "_getInternalHooks3", "registerField", "componentWillUnmount", "forceUpdate", "render", "_this$getOnlyChild", "returnChildNode", "cloneElement", "createElement", "Fragment", "Component", "contextType", "defaultProps", "WrapperField", "_ref5", "restProps", "useContext", "join", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/Field.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"name\"];\nimport toChildrenArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport FieldContext, { HOOK_MARK } from './FieldContext';\nimport { toArray } from './utils/typeUtil';\nimport { validateRules } from './utils/validateUtil';\nimport { containsNamePath, defaultGetValueFromEvent, getNamePath, getValue } from './utils/valueUtil';\nvar EMPTY_ERRORS = [];\nfunction requireUpdate(shouldUpdate, prev, next, prevValue, nextValue, info) {\n  if (typeof shouldUpdate === 'function') {\n    return shouldUpdate(prev, next, 'source' in info ? {\n      source: info.source\n    } : {});\n  }\n  return prevValue !== nextValue;\n}\n// We use Class instead of Hooks here since it will cost much code by using Hooks.\nvar Field = /*#__PURE__*/function (_React$Component) {\n  _inherits(Field, _React$Component);\n  var _super = _createSuper(Field);\n  /**\n   * Follow state should not management in State since it will async update by React.\n   * This makes first render of form can not get correct state value.\n   */\n\n  /**\n   * Mark when touched & validated. Currently only used for `dependencies`.\n   * Note that we do not think field with `initialValue` is dirty\n   * but this will be by `isFieldDirty` func.\n   */\n\n  // ============================== Subscriptions ==============================\n  function Field(props) {\n    var _this;\n    _classCallCheck(this, Field);\n    _this = _super.call(this, props);\n    // Register on init\n    _this.state = {\n      resetCount: 0\n    };\n    _this.cancelRegisterFunc = null;\n    _this.mounted = false;\n    _this.touched = false;\n    _this.dirty = false;\n    _this.validatePromise = null;\n    _this.prevValidating = void 0;\n    _this.errors = EMPTY_ERRORS;\n    _this.warnings = EMPTY_ERRORS;\n    _this.cancelRegister = function () {\n      var _this$props = _this.props,\n        preserve = _this$props.preserve,\n        isListField = _this$props.isListField,\n        name = _this$props.name;\n      if (_this.cancelRegisterFunc) {\n        _this.cancelRegisterFunc(isListField, preserve, getNamePath(name));\n      }\n      _this.cancelRegisterFunc = null;\n    };\n    _this.getNamePath = function () {\n      var _this$props2 = _this.props,\n        name = _this$props2.name,\n        fieldContext = _this$props2.fieldContext;\n      var _fieldContext$prefixN = fieldContext.prefixName,\n        prefixName = _fieldContext$prefixN === void 0 ? [] : _fieldContext$prefixN;\n      return name !== undefined ? [].concat(_toConsumableArray(prefixName), _toConsumableArray(name)) : [];\n    };\n    _this.getRules = function () {\n      var _this$props3 = _this.props,\n        _this$props3$rules = _this$props3.rules,\n        rules = _this$props3$rules === void 0 ? [] : _this$props3$rules,\n        fieldContext = _this$props3.fieldContext;\n      return rules.map(function (rule) {\n        if (typeof rule === 'function') {\n          return rule(fieldContext);\n        }\n        return rule;\n      });\n    };\n    _this.refresh = function () {\n      if (!_this.mounted) return;\n      /**\n       * Clean up current node.\n       */\n      _this.setState(function (_ref) {\n        var resetCount = _ref.resetCount;\n        return {\n          resetCount: resetCount + 1\n        };\n      });\n    };\n    _this.triggerMetaEvent = function (destroy) {\n      var onMetaChange = _this.props.onMetaChange;\n      onMetaChange === null || onMetaChange === void 0 ? void 0 : onMetaChange(_objectSpread(_objectSpread({}, _this.getMeta()), {}, {\n        destroy: destroy\n      }));\n    };\n    _this.onStoreChange = function (prevStore, namePathList, info) {\n      var _this$props4 = _this.props,\n        shouldUpdate = _this$props4.shouldUpdate,\n        _this$props4$dependen = _this$props4.dependencies,\n        dependencies = _this$props4$dependen === void 0 ? [] : _this$props4$dependen,\n        onReset = _this$props4.onReset;\n      var store = info.store;\n      var namePath = _this.getNamePath();\n      var prevValue = _this.getValue(prevStore);\n      var curValue = _this.getValue(store);\n      var namePathMatch = namePathList && containsNamePath(namePathList, namePath);\n      // `setFieldsValue` is a quick access to update related status\n      if (info.type === 'valueUpdate' && info.source === 'external' && prevValue !== curValue) {\n        _this.touched = true;\n        _this.dirty = true;\n        _this.validatePromise = null;\n        _this.errors = EMPTY_ERRORS;\n        _this.warnings = EMPTY_ERRORS;\n        _this.triggerMetaEvent();\n      }\n      switch (info.type) {\n        case 'reset':\n          if (!namePathList || namePathMatch) {\n            // Clean up state\n            _this.touched = false;\n            _this.dirty = false;\n            _this.validatePromise = null;\n            _this.errors = EMPTY_ERRORS;\n            _this.warnings = EMPTY_ERRORS;\n            _this.triggerMetaEvent();\n            onReset === null || onReset === void 0 ? void 0 : onReset();\n            _this.refresh();\n            return;\n          }\n          break;\n        /**\n         * In case field with `preserve = false` nest deps like:\n         * - A = 1 => show B\n         * - B = 1 => show C\n         * - Reset A, need clean B, C\n         */\n        case 'remove':\n          {\n            if (shouldUpdate) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'setField':\n          {\n            if (namePathMatch) {\n              var data = info.data;\n              if ('touched' in data) {\n                _this.touched = data.touched;\n              }\n              if ('validating' in data && !('originRCField' in data)) {\n                _this.validatePromise = data.validating ? Promise.resolve([]) : null;\n              }\n              if ('errors' in data) {\n                _this.errors = data.errors || EMPTY_ERRORS;\n              }\n              if ('warnings' in data) {\n                _this.warnings = data.warnings || EMPTY_ERRORS;\n              }\n              _this.dirty = true;\n              _this.triggerMetaEvent();\n              _this.reRender();\n              return;\n            }\n            // Handle update by `setField` with `shouldUpdate`\n            if (shouldUpdate && !namePath.length && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        case 'dependenciesUpdate':\n          {\n            /**\n             * Trigger when marked `dependencies` updated. Related fields will all update\n             */\n            var dependencyList = dependencies.map(getNamePath);\n            // No need for `namePathMath` check and `shouldUpdate` check, since `valueUpdate` will be\n            // emitted earlier and they will work there\n            // If set it may cause unnecessary twice rerendering\n            if (dependencyList.some(function (dependency) {\n              return containsNamePath(info.relatedFields, dependency);\n            })) {\n              _this.reRender();\n              return;\n            }\n            break;\n          }\n        default:\n          // 1. If `namePath` exists in `namePathList`, means it's related value and should update\n          //      For example <List name=\"list\"><Field name={['list', 0]}></List>\n          //      If `namePathList` is [['list']] (List value update), Field should be updated\n          //      If `namePathList` is [['list', 0]] (Field value update), List shouldn't be updated\n          // 2.\n          //   2.1 If `dependencies` is set, `name` is not set and `shouldUpdate` is not set,\n          //       don't use `shouldUpdate`. `dependencies` is view as a shortcut if `shouldUpdate`\n          //       is not provided\n          //   2.2 If `shouldUpdate` provided, use customize logic to update the field\n          //       else to check if value changed\n          if (namePathMatch || (!dependencies.length || namePath.length || shouldUpdate) && requireUpdate(shouldUpdate, prevStore, store, prevValue, curValue, info)) {\n            _this.reRender();\n            return;\n          }\n          break;\n      }\n      if (shouldUpdate === true) {\n        _this.reRender();\n      }\n    };\n    _this.validateRules = function (options) {\n      // We should fixed namePath & value to avoid developer change then by form function\n      var namePath = _this.getNamePath();\n      var currentValue = _this.getValue();\n      // Force change to async to avoid rule OOD under renderProps field\n      var rootPromise = Promise.resolve().then(function () {\n        if (!_this.mounted) {\n          return [];\n        }\n        var _this$props5 = _this.props,\n          _this$props5$validate = _this$props5.validateFirst,\n          validateFirst = _this$props5$validate === void 0 ? false : _this$props5$validate,\n          messageVariables = _this$props5.messageVariables;\n        var _ref2 = options || {},\n          triggerName = _ref2.triggerName;\n        var filteredRules = _this.getRules();\n        if (triggerName) {\n          filteredRules = filteredRules.filter(function (rule) {\n            return rule;\n          }).filter(function (rule) {\n            var validateTrigger = rule.validateTrigger;\n            if (!validateTrigger) {\n              return true;\n            }\n            var triggerList = toArray(validateTrigger);\n            return triggerList.includes(triggerName);\n          });\n        }\n        var promise = validateRules(namePath, currentValue, filteredRules, options, validateFirst, messageVariables);\n        promise.catch(function (e) {\n          return e;\n        }).then(function () {\n          var ruleErrors = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : EMPTY_ERRORS;\n          if (_this.validatePromise === rootPromise) {\n            var _ruleErrors$forEach;\n            _this.validatePromise = null;\n            // Get errors & warnings\n            var nextErrors = [];\n            var nextWarnings = [];\n            (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 ? void 0 : _ruleErrors$forEach.call(ruleErrors, function (_ref3) {\n              var warningOnly = _ref3.rule.warningOnly,\n                _ref3$errors = _ref3.errors,\n                errors = _ref3$errors === void 0 ? EMPTY_ERRORS : _ref3$errors;\n              if (warningOnly) {\n                nextWarnings.push.apply(nextWarnings, _toConsumableArray(errors));\n              } else {\n                nextErrors.push.apply(nextErrors, _toConsumableArray(errors));\n              }\n            });\n            _this.errors = nextErrors;\n            _this.warnings = nextWarnings;\n            _this.triggerMetaEvent();\n            _this.reRender();\n          }\n        });\n        return promise;\n      });\n      _this.validatePromise = rootPromise;\n      _this.dirty = true;\n      _this.errors = EMPTY_ERRORS;\n      _this.warnings = EMPTY_ERRORS;\n      _this.triggerMetaEvent();\n      // Force trigger re-render since we need sync renderProps with new meta\n      _this.reRender();\n      return rootPromise;\n    };\n    _this.isFieldValidating = function () {\n      return !!_this.validatePromise;\n    };\n    _this.isFieldTouched = function () {\n      return _this.touched;\n    };\n    _this.isFieldDirty = function () {\n      // Touched or validate or has initialValue\n      if (_this.dirty || _this.props.initialValue !== undefined) {\n        return true;\n      }\n      // Form set initialValue\n      var fieldContext = _this.props.fieldContext;\n      var _fieldContext$getInte = fieldContext.getInternalHooks(HOOK_MARK),\n        getInitialValue = _fieldContext$getInte.getInitialValue;\n      if (getInitialValue(_this.getNamePath()) !== undefined) {\n        return true;\n      }\n      return false;\n    };\n    _this.getErrors = function () {\n      return _this.errors;\n    };\n    _this.getWarnings = function () {\n      return _this.warnings;\n    };\n    _this.isListField = function () {\n      return _this.props.isListField;\n    };\n    _this.isList = function () {\n      return _this.props.isList;\n    };\n    _this.isPreserve = function () {\n      return _this.props.preserve;\n    };\n    _this.getMeta = function () {\n      // Make error & validating in cache to save perf\n      _this.prevValidating = _this.isFieldValidating();\n      var meta = {\n        touched: _this.isFieldTouched(),\n        validating: _this.prevValidating,\n        errors: _this.errors,\n        warnings: _this.warnings,\n        name: _this.getNamePath()\n      };\n      return meta;\n    };\n    _this.getOnlyChild = function (children) {\n      // Support render props\n      if (typeof children === 'function') {\n        var meta = _this.getMeta();\n        return _objectSpread(_objectSpread({}, _this.getOnlyChild(children(_this.getControlled(), meta, _this.props.fieldContext))), {}, {\n          isFunction: true\n        });\n      }\n      // Filed element only\n      var childList = toChildrenArray(children);\n      if (childList.length !== 1 || ! /*#__PURE__*/React.isValidElement(childList[0])) {\n        return {\n          child: childList,\n          isFunction: false\n        };\n      }\n      return {\n        child: childList[0],\n        isFunction: false\n      };\n    };\n    _this.getValue = function (store) {\n      var getFieldsValue = _this.props.fieldContext.getFieldsValue;\n      var namePath = _this.getNamePath();\n      return getValue(store || getFieldsValue(true), namePath);\n    };\n    _this.getControlled = function () {\n      var childProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      var _this$props6 = _this.props,\n        trigger = _this$props6.trigger,\n        validateTrigger = _this$props6.validateTrigger,\n        getValueFromEvent = _this$props6.getValueFromEvent,\n        normalize = _this$props6.normalize,\n        valuePropName = _this$props6.valuePropName,\n        getValueProps = _this$props6.getValueProps,\n        fieldContext = _this$props6.fieldContext;\n      var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : fieldContext.validateTrigger;\n      var namePath = _this.getNamePath();\n      var getInternalHooks = fieldContext.getInternalHooks,\n        getFieldsValue = fieldContext.getFieldsValue;\n      var _getInternalHooks = getInternalHooks(HOOK_MARK),\n        dispatch = _getInternalHooks.dispatch;\n      var value = _this.getValue();\n      var mergedGetValueProps = getValueProps || function (val) {\n        return _defineProperty({}, valuePropName, val);\n      };\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      var originTriggerFunc = childProps[trigger];\n      var control = _objectSpread(_objectSpread({}, childProps), mergedGetValueProps(value));\n      // Add trigger\n      control[trigger] = function () {\n        // Mark as touched\n        _this.touched = true;\n        _this.dirty = true;\n        _this.triggerMetaEvent();\n        var newValue;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        if (getValueFromEvent) {\n          newValue = getValueFromEvent.apply(void 0, args);\n        } else {\n          newValue = defaultGetValueFromEvent.apply(void 0, [valuePropName].concat(args));\n        }\n        if (normalize) {\n          newValue = normalize(newValue, value, getFieldsValue(true));\n        }\n        dispatch({\n          type: 'updateValue',\n          namePath: namePath,\n          value: newValue\n        });\n        if (originTriggerFunc) {\n          originTriggerFunc.apply(void 0, args);\n        }\n      };\n      // Add validateTrigger\n      var validateTriggerList = toArray(mergedValidateTrigger || []);\n      validateTriggerList.forEach(function (triggerName) {\n        // Wrap additional function of component, so that we can get latest value from store\n        var originTrigger = control[triggerName];\n        control[triggerName] = function () {\n          if (originTrigger) {\n            originTrigger.apply(void 0, arguments);\n          }\n          // Always use latest rules\n          var rules = _this.props.rules;\n          if (rules && rules.length) {\n            // We dispatch validate to root,\n            // since it will update related data with other field with same name\n            dispatch({\n              type: 'validateField',\n              namePath: namePath,\n              triggerName: triggerName\n            });\n          }\n        };\n      });\n      return control;\n    };\n    if (props.fieldContext) {\n      var getInternalHooks = props.fieldContext.getInternalHooks;\n      var _getInternalHooks2 = getInternalHooks(HOOK_MARK),\n        initEntityValue = _getInternalHooks2.initEntityValue;\n      initEntityValue(_assertThisInitialized(_this));\n    }\n    return _this;\n  }\n  _createClass(Field, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n        shouldUpdate = _this$props7.shouldUpdate,\n        fieldContext = _this$props7.fieldContext;\n      this.mounted = true;\n      // Register on init\n      if (fieldContext) {\n        var getInternalHooks = fieldContext.getInternalHooks;\n        var _getInternalHooks3 = getInternalHooks(HOOK_MARK),\n          registerField = _getInternalHooks3.registerField;\n        this.cancelRegisterFunc = registerField(this);\n      }\n      // One more render for component in case fields not ready\n      if (shouldUpdate === true) {\n        this.reRender();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.cancelRegister();\n      this.triggerMetaEvent(true);\n      this.mounted = false;\n    }\n  }, {\n    key: \"reRender\",\n    value: function reRender() {\n      if (!this.mounted) return;\n      this.forceUpdate();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var resetCount = this.state.resetCount;\n      var children = this.props.children;\n      var _this$getOnlyChild = this.getOnlyChild(children),\n        child = _this$getOnlyChild.child,\n        isFunction = _this$getOnlyChild.isFunction;\n      // Not need to `cloneElement` since user can handle this in render function self\n      var returnChildNode;\n      if (isFunction) {\n        returnChildNode = child;\n      } else if ( /*#__PURE__*/React.isValidElement(child)) {\n        returnChildNode = /*#__PURE__*/React.cloneElement(child, this.getControlled(child.props));\n      } else {\n        warning(!child, '`children` of Field is not validate ReactElement.');\n        returnChildNode = child;\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, {\n        key: resetCount\n      }, returnChildNode);\n    }\n  }]);\n  return Field;\n}(React.Component);\nField.contextType = FieldContext;\nField.defaultProps = {\n  trigger: 'onChange',\n  valuePropName: 'value'\n};\nfunction WrapperField(_ref5) {\n  var name = _ref5.name,\n    restProps = _objectWithoutProperties(_ref5, _excluded);\n  var fieldContext = React.useContext(FieldContext);\n  var namePath = name !== undefined ? getNamePath(name) : undefined;\n  var key = 'keep';\n  if (!restProps.isListField) {\n    key = \"_\".concat((namePath || []).join('_'));\n  }\n  // Warning if it's a directly list field.\n  // We can still support multiple level field preserve.\n  if (process.env.NODE_ENV !== 'production' && restProps.preserve === false && restProps.isListField && namePath.length <= 1) {\n    warning(false, '`preserve` should not apply on Form.List fields.');\n  }\n  return /*#__PURE__*/React.createElement(Field, _extends({\n    key: key,\n    name: namePath\n  }, restProps, {\n    fieldContext: fieldContext\n  }));\n}\nexport default WrapperField;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,gBAAgB,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AACrG,IAAIC,YAAY,GAAG,EAAE;AACrB,SAASC,aAAaA,CAACC,YAAY,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAE;EAC3E,IAAI,OAAOL,YAAY,KAAK,UAAU,EAAE;IACtC,OAAOA,YAAY,CAACC,IAAI,EAAEC,IAAI,EAAE,QAAQ,IAAIG,IAAI,GAAG;MACjDC,MAAM,EAAED,IAAI,CAACC;IACf,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,OAAOH,SAAS,KAAKC,SAAS;AAChC;AACA;AACA,IAAIG,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnDxB,SAAS,CAACuB,KAAK,EAAEC,gBAAgB,CAAC;EAClC,IAAIC,MAAM,GAAGxB,YAAY,CAACsB,KAAK,CAAC;EAChC;AACF;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;;EAEE;EACA,SAASA,KAAKA,CAACG,KAAK,EAAE;IACpB,IAAIC,KAAK;IACT9B,eAAe,CAAC,IAAI,EAAE0B,KAAK,CAAC;IAC5BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChC;IACAC,KAAK,CAACE,KAAK,GAAG;MACZC,UAAU,EAAE;IACd,CAAC;IACDH,KAAK,CAACI,kBAAkB,GAAG,IAAI;IAC/BJ,KAAK,CAACK,OAAO,GAAG,KAAK;IACrBL,KAAK,CAACM,OAAO,GAAG,KAAK;IACrBN,KAAK,CAACO,KAAK,GAAG,KAAK;IACnBP,KAAK,CAACQ,eAAe,GAAG,IAAI;IAC5BR,KAAK,CAACS,cAAc,GAAG,KAAK,CAAC;IAC7BT,KAAK,CAACU,MAAM,GAAGvB,YAAY;IAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;IAC7Ba,KAAK,CAACY,cAAc,GAAG,YAAY;MACjC,IAAIC,WAAW,GAAGb,KAAK,CAACD,KAAK;QAC3Be,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,IAAI,GAAGH,WAAW,CAACG,IAAI;MACzB,IAAIhB,KAAK,CAACI,kBAAkB,EAAE;QAC5BJ,KAAK,CAACI,kBAAkB,CAACW,WAAW,EAAED,QAAQ,EAAE7B,WAAW,CAAC+B,IAAI,CAAC,CAAC;MACpE;MACAhB,KAAK,CAACI,kBAAkB,GAAG,IAAI;IACjC,CAAC;IACDJ,KAAK,CAACf,WAAW,GAAG,YAAY;MAC9B,IAAIgC,YAAY,GAAGjB,KAAK,CAACD,KAAK;QAC5BiB,IAAI,GAAGC,YAAY,CAACD,IAAI;QACxBE,YAAY,GAAGD,YAAY,CAACC,YAAY;MAC1C,IAAIC,qBAAqB,GAAGD,YAAY,CAACE,UAAU;QACjDA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;MAC5E,OAAOH,IAAI,KAAKK,SAAS,GAAG,EAAE,CAACC,MAAM,CAACrD,kBAAkB,CAACmD,UAAU,CAAC,EAAEnD,kBAAkB,CAAC+C,IAAI,CAAC,CAAC,GAAG,EAAE;IACtG,CAAC;IACDhB,KAAK,CAACuB,QAAQ,GAAG,YAAY;MAC3B,IAAIC,YAAY,GAAGxB,KAAK,CAACD,KAAK;QAC5B0B,kBAAkB,GAAGD,YAAY,CAACE,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;QAC/DP,YAAY,GAAGM,YAAY,CAACN,YAAY;MAC1C,OAAOQ,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;UAC9B,OAAOA,IAAI,CAACV,YAAY,CAAC;QAC3B;QACA,OAAOU,IAAI;MACb,CAAC,CAAC;IACJ,CAAC;IACD5B,KAAK,CAAC6B,OAAO,GAAG,YAAY;MAC1B,IAAI,CAAC7B,KAAK,CAACK,OAAO,EAAE;MACpB;AACN;AACA;MACML,KAAK,CAAC8B,QAAQ,CAAC,UAAUC,IAAI,EAAE;QAC7B,IAAI5B,UAAU,GAAG4B,IAAI,CAAC5B,UAAU;QAChC,OAAO;UACLA,UAAU,EAAEA,UAAU,GAAG;QAC3B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACDH,KAAK,CAACgC,gBAAgB,GAAG,UAAUC,OAAO,EAAE;MAC1C,IAAIC,YAAY,GAAGlC,KAAK,CAACD,KAAK,CAACmC,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAClE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7HF,OAAO,EAAEA;MACX,CAAC,CAAC,CAAC;IACL,CAAC;IACDjC,KAAK,CAACoC,aAAa,GAAG,UAAUC,SAAS,EAAEC,YAAY,EAAE5C,IAAI,EAAE;MAC7D,IAAI6C,YAAY,GAAGvC,KAAK,CAACD,KAAK;QAC5BV,YAAY,GAAGkD,YAAY,CAAClD,YAAY;QACxCmD,qBAAqB,GAAGD,YAAY,CAACE,YAAY;QACjDA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;QAC5EE,OAAO,GAAGH,YAAY,CAACG,OAAO;MAChC,IAAIC,KAAK,GAAGjD,IAAI,CAACiD,KAAK;MACtB,IAAIC,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAIO,SAAS,GAAGQ,KAAK,CAACd,QAAQ,CAACmD,SAAS,CAAC;MACzC,IAAIQ,QAAQ,GAAG7C,KAAK,CAACd,QAAQ,CAACyD,KAAK,CAAC;MACpC,IAAIG,aAAa,GAAGR,YAAY,IAAIvD,gBAAgB,CAACuD,YAAY,EAAEM,QAAQ,CAAC;MAC5E;MACA,IAAIlD,IAAI,CAACqD,IAAI,KAAK,aAAa,IAAIrD,IAAI,CAACC,MAAM,KAAK,UAAU,IAAIH,SAAS,KAAKqD,QAAQ,EAAE;QACvF7C,KAAK,CAACM,OAAO,GAAG,IAAI;QACpBN,KAAK,CAACO,KAAK,GAAG,IAAI;QAClBP,KAAK,CAACQ,eAAe,GAAG,IAAI;QAC5BR,KAAK,CAACU,MAAM,GAAGvB,YAAY;QAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;QAC7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC;MAC1B;MACA,QAAQtC,IAAI,CAACqD,IAAI;QACf,KAAK,OAAO;UACV,IAAI,CAACT,YAAY,IAAIQ,aAAa,EAAE;YAClC;YACA9C,KAAK,CAACM,OAAO,GAAG,KAAK;YACrBN,KAAK,CAACO,KAAK,GAAG,KAAK;YACnBP,KAAK,CAACQ,eAAe,GAAG,IAAI;YAC5BR,KAAK,CAACU,MAAM,GAAGvB,YAAY;YAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;YAC7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC;YACxBU,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;YAC3D1C,KAAK,CAAC6B,OAAO,CAAC,CAAC;YACf;UACF;UACA;QACF;AACR;AACA;AACA;AACA;AACA;QACQ,KAAK,QAAQ;UACX;YACE,IAAIxC,YAAY,EAAE;cAChBW,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF,KAAK,UAAU;UACb;YACE,IAAIF,aAAa,EAAE;cACjB,IAAIG,IAAI,GAAGvD,IAAI,CAACuD,IAAI;cACpB,IAAI,SAAS,IAAIA,IAAI,EAAE;gBACrBjD,KAAK,CAACM,OAAO,GAAG2C,IAAI,CAAC3C,OAAO;cAC9B;cACA,IAAI,YAAY,IAAI2C,IAAI,IAAI,EAAE,eAAe,IAAIA,IAAI,CAAC,EAAE;gBACtDjD,KAAK,CAACQ,eAAe,GAAGyC,IAAI,CAACC,UAAU,GAAGC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI;cACtE;cACA,IAAI,QAAQ,IAAIH,IAAI,EAAE;gBACpBjD,KAAK,CAACU,MAAM,GAAGuC,IAAI,CAACvC,MAAM,IAAIvB,YAAY;cAC5C;cACA,IAAI,UAAU,IAAI8D,IAAI,EAAE;gBACtBjD,KAAK,CAACW,QAAQ,GAAGsC,IAAI,CAACtC,QAAQ,IAAIxB,YAAY;cAChD;cACAa,KAAK,CAACO,KAAK,GAAG,IAAI;cAClBP,KAAK,CAACgC,gBAAgB,CAAC,CAAC;cACxBhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;YACA,IAAI3D,YAAY,IAAI,CAACuD,QAAQ,CAACS,MAAM,IAAIjE,aAAa,CAACC,YAAY,EAAEgD,SAAS,EAAEM,KAAK,EAAEnD,SAAS,EAAEqD,QAAQ,EAAEnD,IAAI,CAAC,EAAE;cAChHM,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF,KAAK,oBAAoB;UACvB;YACE;AACZ;AACA;YACY,IAAIM,cAAc,GAAGb,YAAY,CAACd,GAAG,CAAC1C,WAAW,CAAC;YAClD;YACA;YACA;YACA,IAAIqE,cAAc,CAACC,IAAI,CAAC,UAAUC,UAAU,EAAE;cAC5C,OAAOzE,gBAAgB,CAACW,IAAI,CAAC+D,aAAa,EAAED,UAAU,CAAC;YACzD,CAAC,CAAC,EAAE;cACFxD,KAAK,CAACgD,QAAQ,CAAC,CAAC;cAChB;YACF;YACA;UACF;QACF;UACE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIF,aAAa,IAAI,CAAC,CAACL,YAAY,CAACY,MAAM,IAAIT,QAAQ,CAACS,MAAM,IAAIhE,YAAY,KAAKD,aAAa,CAACC,YAAY,EAAEgD,SAAS,EAAEM,KAAK,EAAEnD,SAAS,EAAEqD,QAAQ,EAAEnD,IAAI,CAAC,EAAE;YAC1JM,KAAK,CAACgD,QAAQ,CAAC,CAAC;YAChB;UACF;UACA;MACJ;MACA,IAAI3D,YAAY,KAAK,IAAI,EAAE;QACzBW,KAAK,CAACgD,QAAQ,CAAC,CAAC;MAClB;IACF,CAAC;IACDhD,KAAK,CAAClB,aAAa,GAAG,UAAU4E,OAAO,EAAE;MACvC;MACA,IAAId,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAI0E,YAAY,GAAG3D,KAAK,CAACd,QAAQ,CAAC,CAAC;MACnC;MACA,IAAI0E,WAAW,GAAGT,OAAO,CAACC,OAAO,CAAC,CAAC,CAACS,IAAI,CAAC,YAAY;QACnD,IAAI,CAAC7D,KAAK,CAACK,OAAO,EAAE;UAClB,OAAO,EAAE;QACX;QACA,IAAIyD,YAAY,GAAG9D,KAAK,CAACD,KAAK;UAC5BgE,qBAAqB,GAAGD,YAAY,CAACE,aAAa;UAClDA,aAAa,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;UAChFE,gBAAgB,GAAGH,YAAY,CAACG,gBAAgB;QAClD,IAAIC,KAAK,GAAGR,OAAO,IAAI,CAAC,CAAC;UACvBS,WAAW,GAAGD,KAAK,CAACC,WAAW;QACjC,IAAIC,aAAa,GAAGpE,KAAK,CAACuB,QAAQ,CAAC,CAAC;QACpC,IAAI4C,WAAW,EAAE;UACfC,aAAa,GAAGA,aAAa,CAACC,MAAM,CAAC,UAAUzC,IAAI,EAAE;YACnD,OAAOA,IAAI;UACb,CAAC,CAAC,CAACyC,MAAM,CAAC,UAAUzC,IAAI,EAAE;YACxB,IAAI0C,eAAe,GAAG1C,IAAI,CAAC0C,eAAe;YAC1C,IAAI,CAACA,eAAe,EAAE;cACpB,OAAO,IAAI;YACb;YACA,IAAIC,WAAW,GAAG1F,OAAO,CAACyF,eAAe,CAAC;YAC1C,OAAOC,WAAW,CAACC,QAAQ,CAACL,WAAW,CAAC;UAC1C,CAAC,CAAC;QACJ;QACA,IAAIM,OAAO,GAAG3F,aAAa,CAAC8D,QAAQ,EAAEe,YAAY,EAAES,aAAa,EAAEV,OAAO,EAAEM,aAAa,EAAEC,gBAAgB,CAAC;QAC5GQ,OAAO,CAACC,KAAK,CAAC,UAAUC,CAAC,EAAE;UACzB,OAAOA,CAAC;QACV,CAAC,CAAC,CAACd,IAAI,CAAC,YAAY;UAClB,IAAIe,UAAU,GAAGC,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG1F,YAAY;UACjG,IAAIa,KAAK,CAACQ,eAAe,KAAKoD,WAAW,EAAE;YACzC,IAAIkB,mBAAmB;YACvB9E,KAAK,CAACQ,eAAe,GAAG,IAAI;YAC5B;YACA,IAAIuE,UAAU,GAAG,EAAE;YACnB,IAAIC,YAAY,GAAG,EAAE;YACrB,CAACF,mBAAmB,GAAGF,UAAU,CAACK,OAAO,MAAM,IAAI,IAAIH,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC7E,IAAI,CAAC2E,UAAU,EAAE,UAAUM,KAAK,EAAE;cACrJ,IAAIC,WAAW,GAAGD,KAAK,CAACtD,IAAI,CAACuD,WAAW;gBACtCC,YAAY,GAAGF,KAAK,CAACxE,MAAM;gBAC3BA,MAAM,GAAG0E,YAAY,KAAK,KAAK,CAAC,GAAGjG,YAAY,GAAGiG,YAAY;cAChE,IAAID,WAAW,EAAE;gBACfH,YAAY,CAACK,IAAI,CAACC,KAAK,CAACN,YAAY,EAAE/G,kBAAkB,CAACyC,MAAM,CAAC,CAAC;cACnE,CAAC,MAAM;gBACLqE,UAAU,CAACM,IAAI,CAACC,KAAK,CAACP,UAAU,EAAE9G,kBAAkB,CAACyC,MAAM,CAAC,CAAC;cAC/D;YACF,CAAC,CAAC;YACFV,KAAK,CAACU,MAAM,GAAGqE,UAAU;YACzB/E,KAAK,CAACW,QAAQ,GAAGqE,YAAY;YAC7BhF,KAAK,CAACgC,gBAAgB,CAAC,CAAC;YACxBhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;UAClB;QACF,CAAC,CAAC;QACF,OAAOyB,OAAO;MAChB,CAAC,CAAC;MACFzE,KAAK,CAACQ,eAAe,GAAGoD,WAAW;MACnC5D,KAAK,CAACO,KAAK,GAAG,IAAI;MAClBP,KAAK,CAACU,MAAM,GAAGvB,YAAY;MAC3Ba,KAAK,CAACW,QAAQ,GAAGxB,YAAY;MAC7Ba,KAAK,CAACgC,gBAAgB,CAAC,CAAC;MACxB;MACAhC,KAAK,CAACgD,QAAQ,CAAC,CAAC;MAChB,OAAOY,WAAW;IACpB,CAAC;IACD5D,KAAK,CAACuF,iBAAiB,GAAG,YAAY;MACpC,OAAO,CAAC,CAACvF,KAAK,CAACQ,eAAe;IAChC,CAAC;IACDR,KAAK,CAACwF,cAAc,GAAG,YAAY;MACjC,OAAOxF,KAAK,CAACM,OAAO;IACtB,CAAC;IACDN,KAAK,CAACyF,YAAY,GAAG,YAAY;MAC/B;MACA,IAAIzF,KAAK,CAACO,KAAK,IAAIP,KAAK,CAACD,KAAK,CAAC2F,YAAY,KAAKrE,SAAS,EAAE;QACzD,OAAO,IAAI;MACb;MACA;MACA,IAAIH,YAAY,GAAGlB,KAAK,CAACD,KAAK,CAACmB,YAAY;MAC3C,IAAIyE,qBAAqB,GAAGzE,YAAY,CAAC0E,gBAAgB,CAAChH,SAAS,CAAC;QAClEiH,eAAe,GAAGF,qBAAqB,CAACE,eAAe;MACzD,IAAIA,eAAe,CAAC7F,KAAK,CAACf,WAAW,CAAC,CAAC,CAAC,KAAKoC,SAAS,EAAE;QACtD,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;IACDrB,KAAK,CAAC8F,SAAS,GAAG,YAAY;MAC5B,OAAO9F,KAAK,CAACU,MAAM;IACrB,CAAC;IACDV,KAAK,CAAC+F,WAAW,GAAG,YAAY;MAC9B,OAAO/F,KAAK,CAACW,QAAQ;IACvB,CAAC;IACDX,KAAK,CAACe,WAAW,GAAG,YAAY;MAC9B,OAAOf,KAAK,CAACD,KAAK,CAACgB,WAAW;IAChC,CAAC;IACDf,KAAK,CAACgG,MAAM,GAAG,YAAY;MACzB,OAAOhG,KAAK,CAACD,KAAK,CAACiG,MAAM;IAC3B,CAAC;IACDhG,KAAK,CAACiG,UAAU,GAAG,YAAY;MAC7B,OAAOjG,KAAK,CAACD,KAAK,CAACe,QAAQ;IAC7B,CAAC;IACDd,KAAK,CAACmC,OAAO,GAAG,YAAY;MAC1B;MACAnC,KAAK,CAACS,cAAc,GAAGT,KAAK,CAACuF,iBAAiB,CAAC,CAAC;MAChD,IAAIW,IAAI,GAAG;QACT5F,OAAO,EAAEN,KAAK,CAACwF,cAAc,CAAC,CAAC;QAC/BtC,UAAU,EAAElD,KAAK,CAACS,cAAc;QAChCC,MAAM,EAAEV,KAAK,CAACU,MAAM;QACpBC,QAAQ,EAAEX,KAAK,CAACW,QAAQ;QACxBK,IAAI,EAAEhB,KAAK,CAACf,WAAW,CAAC;MAC1B,CAAC;MACD,OAAOiH,IAAI;IACb,CAAC;IACDlG,KAAK,CAACmG,YAAY,GAAG,UAAUC,QAAQ,EAAE;MACvC;MACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAIF,IAAI,GAAGlG,KAAK,CAACmC,OAAO,CAAC,CAAC;QAC1B,OAAOnE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAACmG,YAAY,CAACC,QAAQ,CAACpG,KAAK,CAACqG,aAAa,CAAC,CAAC,EAAEH,IAAI,EAAElG,KAAK,CAACD,KAAK,CAACmB,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/HoF,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;MACA;MACA,IAAIC,SAAS,GAAG/H,eAAe,CAAC4H,QAAQ,CAAC;MACzC,IAAIG,SAAS,CAAClD,MAAM,KAAK,CAAC,IAAI,EAAE,aAAa3E,KAAK,CAAC8H,cAAc,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/E,OAAO;UACLE,KAAK,EAAEF,SAAS;UAChBD,UAAU,EAAE;QACd,CAAC;MACH;MACA,OAAO;QACLG,KAAK,EAAEF,SAAS,CAAC,CAAC,CAAC;QACnBD,UAAU,EAAE;MACd,CAAC;IACH,CAAC;IACDtG,KAAK,CAACd,QAAQ,GAAG,UAAUyD,KAAK,EAAE;MAChC,IAAI+D,cAAc,GAAG1G,KAAK,CAACD,KAAK,CAACmB,YAAY,CAACwF,cAAc;MAC5D,IAAI9D,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,OAAOC,QAAQ,CAACyD,KAAK,IAAI+D,cAAc,CAAC,IAAI,CAAC,EAAE9D,QAAQ,CAAC;IAC1D,CAAC;IACD5C,KAAK,CAACqG,aAAa,GAAG,YAAY;MAChC,IAAIM,UAAU,GAAG9B,SAAS,CAACxB,MAAM,GAAG,CAAC,IAAIwB,SAAS,CAAC,CAAC,CAAC,KAAKxD,SAAS,GAAGwD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACvF,IAAI+B,YAAY,GAAG5G,KAAK,CAACD,KAAK;QAC5B8G,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BvC,eAAe,GAAGsC,YAAY,CAACtC,eAAe;QAC9CwC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,aAAa,GAAGJ,YAAY,CAACI,aAAa;QAC1CC,aAAa,GAAGL,YAAY,CAACK,aAAa;QAC1C/F,YAAY,GAAG0F,YAAY,CAAC1F,YAAY;MAC1C,IAAIgG,qBAAqB,GAAG5C,eAAe,KAAKjD,SAAS,GAAGiD,eAAe,GAAGpD,YAAY,CAACoD,eAAe;MAC1G,IAAI1B,QAAQ,GAAG5C,KAAK,CAACf,WAAW,CAAC,CAAC;MAClC,IAAI2G,gBAAgB,GAAG1E,YAAY,CAAC0E,gBAAgB;QAClDc,cAAc,GAAGxF,YAAY,CAACwF,cAAc;MAC9C,IAAIS,iBAAiB,GAAGvB,gBAAgB,CAAChH,SAAS,CAAC;QACjDwI,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;MACvC,IAAIC,KAAK,GAAGrH,KAAK,CAACd,QAAQ,CAAC,CAAC;MAC5B,IAAIoI,mBAAmB,GAAGL,aAAa,IAAI,UAAUM,GAAG,EAAE;QACxD,OAAOxJ,eAAe,CAAC,CAAC,CAAC,EAAEiJ,aAAa,EAAEO,GAAG,CAAC;MAChD,CAAC;MACD;MACA,IAAIC,iBAAiB,GAAGb,UAAU,CAACE,OAAO,CAAC;MAC3C,IAAIY,OAAO,GAAGzJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2I,UAAU,CAAC,EAAEW,mBAAmB,CAACD,KAAK,CAAC,CAAC;MACtF;MACAI,OAAO,CAACZ,OAAO,CAAC,GAAG,YAAY;QAC7B;QACA7G,KAAK,CAACM,OAAO,GAAG,IAAI;QACpBN,KAAK,CAACO,KAAK,GAAG,IAAI;QAClBP,KAAK,CAACgC,gBAAgB,CAAC,CAAC;QACxB,IAAI0F,QAAQ;QACZ,KAAK,IAAIC,IAAI,GAAG9C,SAAS,CAACxB,MAAM,EAAEuE,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;UACvFF,IAAI,CAACE,IAAI,CAAC,GAAGjD,SAAS,CAACiD,IAAI,CAAC;QAC9B;QACA,IAAIhB,iBAAiB,EAAE;UACrBY,QAAQ,GAAGZ,iBAAiB,CAACxB,KAAK,CAAC,KAAK,CAAC,EAAEsC,IAAI,CAAC;QAClD,CAAC,MAAM;UACLF,QAAQ,GAAG1I,wBAAwB,CAACsG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC0B,aAAa,CAAC,CAAC1F,MAAM,CAACsG,IAAI,CAAC,CAAC;QACjF;QACA,IAAIb,SAAS,EAAE;UACbW,QAAQ,GAAGX,SAAS,CAACW,QAAQ,EAAEL,KAAK,EAAEX,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7D;QACAU,QAAQ,CAAC;UACPrE,IAAI,EAAE,aAAa;UACnBH,QAAQ,EAAEA,QAAQ;UAClByE,KAAK,EAAEK;QACT,CAAC,CAAC;QACF,IAAIF,iBAAiB,EAAE;UACrBA,iBAAiB,CAAClC,KAAK,CAAC,KAAK,CAAC,EAAEsC,IAAI,CAAC;QACvC;MACF,CAAC;MACD;MACA,IAAIG,mBAAmB,GAAGlJ,OAAO,CAACqI,qBAAqB,IAAI,EAAE,CAAC;MAC9Da,mBAAmB,CAAC9C,OAAO,CAAC,UAAUd,WAAW,EAAE;QACjD;QACA,IAAI6D,aAAa,GAAGP,OAAO,CAACtD,WAAW,CAAC;QACxCsD,OAAO,CAACtD,WAAW,CAAC,GAAG,YAAY;UACjC,IAAI6D,aAAa,EAAE;YACjBA,aAAa,CAAC1C,KAAK,CAAC,KAAK,CAAC,EAAET,SAAS,CAAC;UACxC;UACA;UACA,IAAInD,KAAK,GAAG1B,KAAK,CAACD,KAAK,CAAC2B,KAAK;UAC7B,IAAIA,KAAK,IAAIA,KAAK,CAAC2B,MAAM,EAAE;YACzB;YACA;YACA+D,QAAQ,CAAC;cACPrE,IAAI,EAAE,eAAe;cACrBH,QAAQ,EAAEA,QAAQ;cAClBuB,WAAW,EAAEA;YACf,CAAC,CAAC;UACJ;QACF,CAAC;MACH,CAAC,CAAC;MACF,OAAOsD,OAAO;IAChB,CAAC;IACD,IAAI1H,KAAK,CAACmB,YAAY,EAAE;MACtB,IAAI0E,gBAAgB,GAAG7F,KAAK,CAACmB,YAAY,CAAC0E,gBAAgB;MAC1D,IAAIqC,kBAAkB,GAAGrC,gBAAgB,CAAChH,SAAS,CAAC;QAClDsJ,eAAe,GAAGD,kBAAkB,CAACC,eAAe;MACtDA,eAAe,CAAC9J,sBAAsB,CAAC4B,KAAK,CAAC,CAAC;IAChD;IACA,OAAOA,KAAK;EACd;EACA7B,YAAY,CAACyB,KAAK,EAAE,CAAC;IACnBuI,GAAG,EAAE,mBAAmB;IACxBd,KAAK,EAAE,SAASe,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,YAAY,GAAG,IAAI,CAACtI,KAAK;QAC3BV,YAAY,GAAGgJ,YAAY,CAAChJ,YAAY;QACxC6B,YAAY,GAAGmH,YAAY,CAACnH,YAAY;MAC1C,IAAI,CAACb,OAAO,GAAG,IAAI;MACnB;MACA,IAAIa,YAAY,EAAE;QAChB,IAAI0E,gBAAgB,GAAG1E,YAAY,CAAC0E,gBAAgB;QACpD,IAAI0C,kBAAkB,GAAG1C,gBAAgB,CAAChH,SAAS,CAAC;UAClD2J,aAAa,GAAGD,kBAAkB,CAACC,aAAa;QAClD,IAAI,CAACnI,kBAAkB,GAAGmI,aAAa,CAAC,IAAI,CAAC;MAC/C;MACA;MACA,IAAIlJ,YAAY,KAAK,IAAI,EAAE;QACzB,IAAI,CAAC2D,QAAQ,CAAC,CAAC;MACjB;IACF;EACF,CAAC,EAAE;IACDmF,GAAG,EAAE,sBAAsB;IAC3Bd,KAAK,EAAE,SAASmB,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAC5H,cAAc,CAAC,CAAC;MACrB,IAAI,CAACoB,gBAAgB,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAC3B,OAAO,GAAG,KAAK;IACtB;EACF,CAAC,EAAE;IACD8H,GAAG,EAAE,UAAU;IACfd,KAAK,EAAE,SAASrE,QAAQA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC3C,OAAO,EAAE;MACnB,IAAI,CAACoI,WAAW,CAAC,CAAC;IACpB;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,QAAQ;IACbd,KAAK,EAAE,SAASqB,MAAMA,CAAA,EAAG;MACvB,IAAIvI,UAAU,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU;MACtC,IAAIiG,QAAQ,GAAG,IAAI,CAACrG,KAAK,CAACqG,QAAQ;MAClC,IAAIuC,kBAAkB,GAAG,IAAI,CAACxC,YAAY,CAACC,QAAQ,CAAC;QAClDK,KAAK,GAAGkC,kBAAkB,CAAClC,KAAK;QAChCH,UAAU,GAAGqC,kBAAkB,CAACrC,UAAU;MAC5C;MACA,IAAIsC,eAAe;MACnB,IAAItC,UAAU,EAAE;QACdsC,eAAe,GAAGnC,KAAK;MACzB,CAAC,MAAM,IAAK,aAAa/H,KAAK,CAAC8H,cAAc,CAACC,KAAK,CAAC,EAAE;QACpDmC,eAAe,GAAG,aAAalK,KAAK,CAACmK,YAAY,CAACpC,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK,CAAC1G,KAAK,CAAC,CAAC;MAC3F,CAAC,MAAM;QACLtB,OAAO,CAAC,CAACgI,KAAK,EAAE,mDAAmD,CAAC;QACpEmC,eAAe,GAAGnC,KAAK;MACzB;MACA,OAAO,aAAa/H,KAAK,CAACoK,aAAa,CAACpK,KAAK,CAACqK,QAAQ,EAAE;QACtDZ,GAAG,EAAEhI;MACP,CAAC,EAAEyI,eAAe,CAAC;IACrB;EACF,CAAC,CAAC,CAAC;EACH,OAAOhJ,KAAK;AACd,CAAC,CAAClB,KAAK,CAACsK,SAAS,CAAC;AAClBpJ,KAAK,CAACqJ,WAAW,GAAGtK,YAAY;AAChCiB,KAAK,CAACsJ,YAAY,GAAG;EACnBrC,OAAO,EAAE,UAAU;EACnBG,aAAa,EAAE;AACjB,CAAC;AACD,SAASmC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIpI,IAAI,GAAGoI,KAAK,CAACpI,IAAI;IACnBqI,SAAS,GAAGvL,wBAAwB,CAACsL,KAAK,EAAE7K,SAAS,CAAC;EACxD,IAAI2C,YAAY,GAAGxC,KAAK,CAAC4K,UAAU,CAAC3K,YAAY,CAAC;EACjD,IAAIiE,QAAQ,GAAG5B,IAAI,KAAKK,SAAS,GAAGpC,WAAW,CAAC+B,IAAI,CAAC,GAAGK,SAAS;EACjE,IAAI8G,GAAG,GAAG,MAAM;EAChB,IAAI,CAACkB,SAAS,CAACtI,WAAW,EAAE;IAC1BoH,GAAG,GAAG,GAAG,CAAC7G,MAAM,CAAC,CAACsB,QAAQ,IAAI,EAAE,EAAE2G,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9C;EACA;EACA;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIL,SAAS,CAACvI,QAAQ,KAAK,KAAK,IAAIuI,SAAS,CAACtI,WAAW,IAAI6B,QAAQ,CAACS,MAAM,IAAI,CAAC,EAAE;IAC1H5E,OAAO,CAAC,KAAK,EAAE,kDAAkD,CAAC;EACpE;EACA,OAAO,aAAaC,KAAK,CAACoK,aAAa,CAAClJ,KAAK,EAAE/B,QAAQ,CAAC;IACtDsK,GAAG,EAAEA,GAAG;IACRnH,IAAI,EAAE4B;EACR,CAAC,EAAEyG,SAAS,EAAE;IACZnI,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC;AACL;AACA,eAAeiI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}