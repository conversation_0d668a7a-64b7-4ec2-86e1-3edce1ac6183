{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm, Pagination } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined, MinusCircleOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  var _selectedTask$result, _selectedTask$result2, _selectedTask$result3, _selectedTask$result4, _selectedTask$result5, _selectedTask$result6, _selectedTask$result$, _selectedTask$result$2, _selectedTask$result$3, _selectedTask$result$4, _selectedTask$result$5, _selectedTask$result$6, _selectedTask$result$7;\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 分页相关状态\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = taskId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 13\n      }, this),\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 13\n      }, this),\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  // 计算当前页的已完成任务数据\n  const getCurrentPageCompletedTasks = () => {\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return completedTasks.slice(startIndex, endIndex);\n  };\n\n  // 分页变化处理\n  const handlePageChange = (page, size) => {\n    setCurrentPage(page);\n    if (size && size !== pageSize) {\n      setPageSize(size);\n      setCurrentPage(1); // 改变页面大小时重置到第一页\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      style: {\n        marginBottom: '24px',\n        display: 'block'\n      },\n      children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u4EFB\\u52A1\\u7EDF\\u8BA1\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 24,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          offset: 1,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u53D6\\u6D88\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.CANCELLED).length,\n            prefix: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          offset: 2,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"function-card\",\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u70B9\\u51FB\\u5237\\u65B0\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 25\n              }, this),\n              onClick: handleRefresh,\n              loading: refreshing,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"function-card\",\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            danger: true,\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 23\n            }, this),\n            disabled: completedTasks.length === 0,\n            onClick: handleClearCompleted,\n            children: \"\\u6E05\\u7A7A\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(List, {\n            dataSource: getCurrentPageCompletedTasks(),\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleDeleteSingleTask(task.task_id),\n                children: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), completedTasks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16,\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Pagination, {\n              current: currentPage,\n              total: completedTasks.length,\n              pageSize: pageSize,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,\n              pageSizeOptions: ['5', '10', '20', '50'],\n              onChange: handlePageChange,\n              onShowSizeChange: handlePageChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6A21\\u578B\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: (_selectedTask$result = selectedTask.result) !== null && _selectedTask$result !== void 0 && _selectedTask$result.is_multi_model || (_selectedTask$result2 = selectedTask.result) !== null && _selectedTask$result2 !== void 0 && _selectedTask$result2.multi_model_results ? `多模型预测 (${((_selectedTask$result3 = selectedTask.result) === null || _selectedTask$result3 === void 0 ? void 0 : _selectedTask$result3.total_models) || ((_selectedTask$result4 = selectedTask.result) === null || _selectedTask$result4 === void 0 ? void 0 : (_selectedTask$result5 = _selectedTask$result4.summary) === null || _selectedTask$result5 === void 0 ? void 0 : _selectedTask$result5.total_models) || 0}个模型)` : ((_selectedTask$result6 = selectedTask.result) === null || _selectedTask$result6 === void 0 ? void 0 : _selectedTask$result6.model_name) || '未知模型'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), selectedTask.status === TASK_STATUS.COMPLETED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: 100,\n              size: \"small\",\n              status: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.FAILED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.CANCELLED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 19\n            }, this) : selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6267\\u884C\\u65F6\\u957F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: (_selectedTask$result7 => {\n                // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                if (selectedTask.started_at && selectedTask.completed_at) {\n                  const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒`;\n                }\n                // 如果任务正在运行，且有开始时间\n                else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                  const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒 (进行中)`;\n                }\n                // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                else if (selectedTask.status === TASK_STATUS.COMPLETED && (_selectedTask$result7 = selectedTask.result) !== null && _selectedTask$result7 !== void 0 && _selectedTask$result7.duration_seconds) {\n                  return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                }\n                // 如果任务已完成但没有时间信息，显示已完成\n                else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                  return '已完成';\n                }\n                // 如果任务失败或取消，显示相应状态\n                else if (selectedTask.status === TASK_STATUS.FAILED) {\n                  return '执行失败';\n                } else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                  return '已取消';\n                }\n                // 其他情况显示等待开始\n                else {\n                  return '等待开始';\n                }\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), selectedTask.current_step && selectedTask.status !== TASK_STATUS.COMPLETED && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5F53\\u524D\\u6B65\\u9AA4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.current_step\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 17\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 17\n          }, this), selectedTask.params && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u53C2\\u6570:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 12,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 200,\n                overflow: 'auto',\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.params, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'training' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 21\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 23\n              }, this), selectedTask.result.results && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"purple\",\n                children: [\"\\u6A21\\u578B\\u6570\\u91CF: \", Object.keys(selectedTask.result.results).length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u8BAD\\u7EC3\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6A21\\u578B\\u8BAD\\u7EC3\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'prediction' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9884\\u6D4B\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 46\n                }, this),\n                children: selectedTask.result.multi_model_results ? '多模型预测已完成' : '预测已完成'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 21\n              }, this), selectedTask.result.is_multi_model || selectedTask.result.multi_model_results ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"\\u603B\\u6A21\\u578B\\u6570: \", selectedTask.result.total_models || ((_selectedTask$result$ = selectedTask.result.summary) === null || _selectedTask$result$ === void 0 ? void 0 : _selectedTask$result$.total_models) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u6210\\u529F: \", selectedTask.result.successful_models || ((_selectedTask$result$2 = selectedTask.result.summary) === null || _selectedTask$result$2 === void 0 ? void 0 : _selectedTask$result$2.successful_models) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 25\n                }, this), (selectedTask.result.failed_models > 0 || ((_selectedTask$result$3 = selectedTask.result.summary) === null || _selectedTask$result$3 === void 0 ? void 0 : _selectedTask$result$3.failed_models) > 0) && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5931\\u8D25: \", selectedTask.result.failed_models || ((_selectedTask$result$4 = selectedTask.result.summary) === null || _selectedTask$result$4 === void 0 ? void 0 : _selectedTask$result$4.failed_models) || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"cyan\",\n                  children: [\"\\u6210\\u529F\\u7387: \", Math.round((selectedTask.result.successful_models || ((_selectedTask$result$5 = selectedTask.result.summary) === null || _selectedTask$result$5 === void 0 ? void 0 : _selectedTask$result$5.successful_models) || 0) / (selectedTask.result.total_models || ((_selectedTask$result$6 = selectedTask.result.summary) === null || _selectedTask$result$6 === void 0 ? void 0 : _selectedTask$result$6.total_models) || 1) * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true) :\n              /*#__PURE__*/\n              /* 单模型预测结果显示 */\n              _jsxDEV(_Fragment, {\n                children: [selectedTask.result.suggested_threshold !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"purple\",\n                  children: [\"\\u5EFA\\u8BAE\\u9608\\u503C (pps): \", selectedTask.result.suggested_threshold]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 27\n                }, this), selectedTask.result.anomaly_count !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5F02\\u5E38\\u6570\\u91CF: \", selectedTask.result.anomaly_count]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 27\n                }, this), ((_selectedTask$result$7 = selectedTask.result.predictions) === null || _selectedTask$result$7 === void 0 ? void 0 : _selectedTask$result$7.length) && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"\\u9884\\u6D4B\\u70B9\\u6570: \", selectedTask.result.predictions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"orange\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u9884\\u6D4B\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6A21\\u578B\\u9884\\u6D4B\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'data_cleaning' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5206\\u6790\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u6D41\\u91CF\\u5206\\u6790\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 21\n              }, this), selectedTask.result.total_tasks && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u5904\\u7406\\u4EFB\\u52A1\\u6570: \", selectedTask.result.total_tasks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 23\n              }, this), selectedTask.result.successful_tasks !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                children: [\"\\u6210\\u529F: \", selectedTask.result.successful_tasks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 23\n              }, this), selectedTask.result.failed_tasks !== undefined && selectedTask.result.failed_tasks > 0 && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"red\",\n                children: [\"\\u5931\\u8D25: \", selectedTask.result.failed_tasks]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 23\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"orange\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u5206\\u6790\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6D41\\u91CF\\u5206\\u6790\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"fRYlSsfQKJV0RtiQk0w+0EYzUuI=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "Pagination", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "MinusCircleOutlined", "ThunderboltOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "_selectedTask$result", "_selectedTask$result2", "_selectedTask$result3", "_selectedTask$result4", "_selectedTask$result5", "_selectedTask$result6", "_selectedTask$result$", "_selectedTask$result$2", "_selectedTask$result$3", "_selectedTask$result$4", "_selectedTask$result$5", "_selectedTask$result$6", "_selectedTask$result$7", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "deleteSingleTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleDeleteSingleTask", "taskId", "confirm", "title", "content", "substring", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "okText", "okType", "cancelText", "onOk", "handleViewTaskDetail", "task", "handleCancelTask", "console", "handleClearCompleted", "completedCount", "length", "info", "getTaskStatusIcon", "status", "RUNNING", "spin", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "children", "formatTime", "timeString", "Date", "toLocaleString", "getCurrentPageCompletedTasks", "startIndex", "endIndex", "slice", "handlePageChange", "page", "size", "level", "fontSize", "fontWeight", "marginBottom", "type", "display", "justifyContent", "onClick", "className", "gutter", "span", "offset", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "created_at", "progress", "undefined", "marginTop", "percent", "disabled", "updated_at", "textAlign", "current", "total", "showSizeChanger", "showQuickJumper", "showTotal", "range", "pageSizeOptions", "onChange", "onShowSizeChange", "open", "onCancel", "footer", "width", "result", "is_multi_model", "multi_model_results", "total_models", "summary", "model_name", "_selectedTask$result7", "started_at", "completed_at", "duration", "Math", "round", "getTime", "duration_seconds", "current_step", "params", "background", "padding", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "wrap", "results", "Object", "keys", "successful_models", "failed_models", "suggested_threshold", "anomaly_count", "predictions", "total_tasks", "successful_tasks", "failed_tasks", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm,\n  Pagination\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 分页相关状态\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  // 计算当前页的已完成任务数据\n  const getCurrentPageCompletedTasks = () => {\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return completedTasks.slice(startIndex, endIndex);\n  };\n\n  // 分页变化处理\n  const handlePageChange = (page: number, size?: number) => {\n    setCurrentPage(page);\n    if (size && size !== pageSize) {\n      setPageSize(size);\n      setCurrentPage(1); // 改变页面大小时重置到第一页\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>任务管理</Title>\n      <Text type=\"secondary\" style={{ marginBottom: '24px', display: 'block' }}>\n        查看和管理异步训练、预测任务的状态和结果\n      </Text>\n\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'flex-end' }}>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card className=\"function-card\" title=\"任务统计\">\n        <Row gutter={24}>\n          <Col span={4} offset={1}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<ThunderboltOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={4}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={4}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={4}>\n            <Statistic\n              title=\"已取消任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.CANCELLED).length}\n              prefix={<MinusCircleOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Col>\n          <Col span={4} offset={2}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            className=\"function-card\"\n            title={\n              <Space>\n                <ThunderboltOutlined />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"点击刷新\">\n                <Button\n                  type=\"text\"\n                  icon={<ReloadOutlined />}\n                  onClick={handleRefresh}\n                  loading={refreshing}\n                  size=\"small\"\n                />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            className=\"function-card\"\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={getCurrentPageCompletedTasks()}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n\n            {/* 分页组件 */}\n            {completedTasks.length > 0 && (\n              <div style={{ marginTop: 16, textAlign: 'center' }}>\n                <Pagination\n                  current={currentPage}\n                  total={completedTasks.length}\n                  pageSize={pageSize}\n                  showSizeChanger\n                  showQuickJumper\n                  showTotal={(total, range) =>\n                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                  }\n                  pageSizeOptions={['5', '10', '20', '50']}\n                  onChange={handlePageChange}\n                  onShowSizeChange={handlePageChange}\n                />\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>模型信息:</Text>\n                <br />\n                <Text>\n                  {selectedTask.result?.is_multi_model || selectedTask.result?.multi_model_results\n                    ? `多模型预测 (${selectedTask.result?.total_models || selectedTask.result?.summary?.total_models || 0}个模型)`\n                    : (selectedTask.result?.model_name || '未知模型')\n                  }\n                </Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.status === TASK_STATUS.COMPLETED ? (\n                  <Progress percent={100} size=\"small\" status=\"success\" />\n                ) : selectedTask.status === TASK_STATUS.FAILED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>执行时长:</Text>\n                <br />\n                <Text>\n                  {(() => {\n                    // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                    if (selectedTask.started_at && selectedTask.completed_at) {\n                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒`;\n                    }\n                    // 如果任务正在运行，且有开始时间\n                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒 (进行中)`;\n                    }\n                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {\n                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                    }\n                    // 如果任务已完成但没有时间信息，显示已完成\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                      return '已完成';\n                    }\n                    // 如果任务失败或取消，显示相应状态\n                    else if (selectedTask.status === TASK_STATUS.FAILED) {\n                      return '执行失败';\n                    }\n                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                      return '已取消';\n                    }\n                    // 其他情况显示等待开始\n                    else {\n                      return '等待开始';\n                    }\n                  })()}\n                </Text>\n              </Col>\n              {selectedTask.current_step && selectedTask.status !== TASK_STATUS.COMPLETED && (\n                <Col span={24}>\n                  <Text strong>当前步骤:</Text>\n                  <br />\n                  <Text>{selectedTask.current_step}</Text>\n                </Col>\n              )}\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.params && (\n                <Col span={24}>\n                  <Text strong>任务参数:</Text>\n                  <br />\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 12,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 200,\n                    overflow: 'auto',\n                    marginTop: 8\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'training' && (\n                <Col span={24}>\n                  <Text strong>训练完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      训练已完成\n                    </Tag>\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"blue\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                    {selectedTask.result.results && (\n                      <Tag color=\"purple\">\n                        模型数量: {Object.keys(selectedTask.result.results).length}\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 训练结果详情请前往\"模型训练\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'prediction' && (\n                <Col span={24}>\n                  <Text strong>预测完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      {selectedTask.result.multi_model_results ? '多模型预测已完成' : '预测已完成'}\n                    </Tag>\n\n                    {/* 多模型预测结果显示 */}\n                    {selectedTask.result.is_multi_model || selectedTask.result.multi_model_results ? (\n                      <>\n                        <Tag color=\"blue\">\n                          总模型数: {selectedTask.result.total_models || selectedTask.result.summary?.total_models || 0}\n                        </Tag>\n                        <Tag color=\"green\">\n                          成功: {selectedTask.result.successful_models || selectedTask.result.summary?.successful_models || 0}\n                        </Tag>\n                        {(selectedTask.result.failed_models > 0 || selectedTask.result.summary?.failed_models > 0) && (\n                          <Tag color=\"red\">\n                            失败: {selectedTask.result.failed_models || selectedTask.result.summary?.failed_models || 0}\n                          </Tag>\n                        )}\n                        <Tag color=\"cyan\">\n                          成功率: {Math.round(((selectedTask.result.successful_models || selectedTask.result.summary?.successful_models || 0) / (selectedTask.result.total_models || selectedTask.result.summary?.total_models || 1)) * 100)}%\n                        </Tag>\n                      </>\n                    ) : (\n                      /* 单模型预测结果显示 */\n                      <>\n                        {selectedTask.result.suggested_threshold !== undefined && (\n                          <Tag color=\"purple\">\n                            建议阈值 (pps): {selectedTask.result.suggested_threshold}\n                          </Tag>\n                        )}\n                        {selectedTask.result.anomaly_count !== undefined && (\n                          <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                        )}\n                        {selectedTask.result.predictions?.length && (\n                          <Tag color=\"blue\">预测点数: {selectedTask.result.predictions.length}</Tag>\n                        )}\n                      </>\n                    )}\n\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 预测结果详情请前往\"模型预测\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'data_cleaning' && (\n                <Col span={24}>\n                  <Text strong>分析完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      流量分析已完成\n                    </Tag>\n                    {selectedTask.result.total_tasks && (\n                      <Tag color=\"blue\">\n                        处理任务数: {selectedTask.result.total_tasks}\n                      </Tag>\n                    )}\n                    {selectedTask.result.successful_tasks !== undefined && (\n                      <Tag color=\"green\">\n                        成功: {selectedTask.result.successful_tasks}\n                      </Tag>\n                    )}\n                    {selectedTask.result.failed_tasks !== undefined && selectedTask.result.failed_tasks > 0 && (\n                      <Tag color=\"red\">\n                        失败: {selectedTask.result.failed_tasks}\n                      </Tag>\n                    )}\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 分析结果详情请前往\"流量分析\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAElC,MAAM4B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,gBAAgB;IAChBC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAG/B,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACdsD,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMuE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjClB,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMmB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACjB,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCR,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMS,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF1C,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjE,OAAO,CAACiE,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRZ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMa,sBAAsB,GAAIC,MAAc,IAAK;IACjDrE,KAAK,CAACsE,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,WAAWH,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ;MAClDC,IAAI,eAAEtD,OAAA,CAACJ,mBAAmB;QAAC2D,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1DC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,MAAMlB,OAAO,GAAG,MAAMpB,gBAAgB,CAACuB,MAAM,CAAC;QAC9C,IAAIH,OAAO,EAAE;UACX;UACA,MAAMF,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAIC,IAAU,IAAK;IAC3CnC,eAAe,CAACmC,IAAI,CAAC;IACrBjC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAG,MAAOlB,MAAc,IAAK;IACjD,IAAI;MACF,MAAMxB,UAAU,CAACwB,MAAM,CAAC;MACxB,MAAM1B,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,cAAc,GAAGjD,cAAc,CAACkD,MAAM;IAC5C,IAAID,cAAc,KAAK,CAAC,EAAE;MACxBxF,OAAO,CAAC0F,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA5F,KAAK,CAACsE,OAAO,CAAC;MACZC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,WAAWkB,cAAc,oBAAoB;MACtDhB,IAAI,eAAEtD,OAAA,CAACP,cAAc;QAAC8D,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrDC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMrC,mBAAmB,CAAC,CAAC;UAC3B;UACA,MAAMiB,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ,CAAC,CAAC,OAAOuB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAK7C,WAAW,CAAC8C,OAAO;QACtB,oBAAO3E,OAAA,CAACb,YAAY;UAACyF,IAAI;UAACrB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK/B,WAAW,CAACgD,SAAS;QACxB,oBAAO7E,OAAA,CAACZ,mBAAmB;UAACmE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK/B,WAAW,CAACiD,MAAM;QACrB,oBAAO9E,OAAA,CAACX,mBAAmB;UAACkE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK/B,WAAW,CAACkD,SAAS;QACxB,oBAAO/E,OAAA,CAACV,YAAY;UAACiE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAO5D,OAAA,CAACT,mBAAmB;UAACgE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAIN,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAK7C,WAAW,CAAC8C,OAAO;QACtB,oBAAO3E,OAAA,CAACtB,GAAG;UAAC8E,KAAK,EAAC,YAAY;UAACF,IAAI,eAAEtD,OAAA,CAACb,YAAY;YAACyF,IAAI;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK/B,WAAW,CAACgD,SAAS;QACxB,oBAAO7E,OAAA,CAACtB,GAAG;UAAC8E,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtD,OAAA,CAACZ,mBAAmB;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK/B,WAAW,CAACiD,MAAM;QACrB,oBAAO9E,OAAA,CAACtB,GAAG;UAAC8E,KAAK,EAAC,OAAO;UAACF,IAAI,eAAEtD,OAAA,CAACX,mBAAmB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK/B,WAAW,CAACkD,SAAS;QACxB,oBAAO/E,OAAA,CAACtB,GAAG;UAAC8E,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtD,OAAA,CAACV,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAO5D,OAAA,CAACtB,GAAG;UAAC8E,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtD,OAAA,CAACT,mBAAmB;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;IACzC,MAAMC,UAAU,GAAG,CAACnD,WAAW,GAAG,CAAC,IAAIE,QAAQ;IAC/C,MAAMkD,QAAQ,GAAGD,UAAU,GAAGjD,QAAQ;IACtC,OAAOjB,cAAc,CAACoE,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACnD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAACC,IAAY,EAAEC,IAAa,KAAK;IACxDvD,cAAc,CAACsD,IAAI,CAAC;IACpB,IAAIC,IAAI,IAAIA,IAAI,KAAKtD,QAAQ,EAAE;MAC7BC,WAAW,CAACqD,IAAI,CAAC;MACjBvD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACErC,OAAA;IAAAiF,QAAA,gBACEjF,OAAA,CAACG,KAAK;MAAC0F,KAAK,EAAE,CAAE;MAACtC,KAAK,EAAE;QAAEuC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAf,QAAA,EAAC;IAAI;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAChG5D,OAAA,CAACI,IAAI;MAAC6F,IAAI,EAAC,WAAW;MAAC1C,KAAK,EAAE;QAAEyC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE;MAAQ,CAAE;MAAAjB,QAAA,EAAC;IAE1E;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP5D,OAAA;MAAKuD,KAAK,EAAE;QAAEyC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAlB,QAAA,eAChFjF,OAAA,CAACzB,MAAM;QACL0H,IAAI,EAAC,SAAS;QACd3C,IAAI,eAAEtD,OAAA,CAACN,cAAc;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBwC,OAAO,EAAEzD,aAAc;QACvBrB,OAAO,EAAEY,UAAW;QAAA+C,QAAA,EACrB;MAED;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN5D,OAAA,CAAC9B,IAAI;MAACmI,SAAS,EAAC,eAAe;MAAClD,KAAK,EAAC,0BAAM;MAAA8B,QAAA,eAC1CjF,OAAA,CAAC7B,GAAG;QAACmI,MAAM,EAAE,EAAG;QAAArB,QAAA,gBACdjF,OAAA,CAAC5B,GAAG;UAACmI,IAAI,EAAE,CAAE;UAACC,MAAM,EAAE,CAAE;UAAAvB,QAAA,eACtBjF,OAAA,CAAC3B,SAAS;YACR8E,KAAK,EAAC,gCAAO;YACbsD,KAAK,EAAErF,YAAY,CAACmD,MAAO;YAC3BmC,MAAM,eAAE1G,OAAA,CAACH,mBAAmB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC+C,UAAU,EAAE;cAAEnD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;UAACmI,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXjF,OAAA,CAAC3B,SAAS;YACR8E,KAAK,EAAC,gCAAO;YACbsD,KAAK,EAAEpF,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAK7C,WAAW,CAACgD,SAAS,CAAC,CAACN,MAAO;YAC7EmC,MAAM,eAAE1G,OAAA,CAACZ,mBAAmB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC+C,UAAU,EAAE;cAAEnD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;UAACmI,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXjF,OAAA,CAAC3B,SAAS;YACR8E,KAAK,EAAC,0BAAM;YACZsD,KAAK,EAAEpF,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAK7C,WAAW,CAACiD,MAAM,CAAC,CAACP,MAAO;YAC1EmC,MAAM,eAAE1G,OAAA,CAACX,mBAAmB;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC+C,UAAU,EAAE;cAAEnD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;UAACmI,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXjF,OAAA,CAAC3B,SAAS;YACR8E,KAAK,EAAC,gCAAO;YACbsD,KAAK,EAAEpF,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAK7C,WAAW,CAACkD,SAAS,CAAC,CAACR,MAAO;YAC7EmC,MAAM,eAAE1G,OAAA,CAACJ,mBAAmB;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC+C,UAAU,EAAE;cAAEnD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;UAACmI,IAAI,EAAE,CAAE;UAACC,MAAM,EAAE,CAAE;UAAAvB,QAAA,eACtBjF,OAAA,CAAC3B,SAAS;YACR8E,KAAK,EAAC,0BAAM;YACZsD,KAAK,EAAErF,YAAY,CAACmD,MAAM,GAAGlD,cAAc,CAACkD,MAAO;YACnDmC,MAAM,eAAE1G,OAAA,CAACL,kBAAkB;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP5D,OAAA,CAAC7B,GAAG;MAACmI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAArB,QAAA,gBAEpBjF,OAAA,CAAC5B,GAAG;QAACmI,IAAI,EAAE,EAAG;QAAAtB,QAAA,eACZjF,OAAA,CAAC9B,IAAI;UACHmI,SAAS,EAAC,eAAe;UACzBlD,KAAK,eACHnD,OAAA,CAACxB,KAAK;YAAAyG,QAAA,gBACJjF,OAAA,CAACH,mBAAmB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAA5D,OAAA,CAACtB,GAAG;cAAC8E,KAAK,EAAC,YAAY;cAAAyB,QAAA,EAAE7D,YAAY,CAACmD;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACDkD,KAAK,eACH9G,OAAA,CAAChB,OAAO;YAACmE,KAAK,EAAC,0BAAM;YAAA8B,QAAA,eACnBjF,OAAA,CAACzB,MAAM;cACL0H,IAAI,EAAC,MAAM;cACX3C,IAAI,eAAEtD,OAAA,CAACN,cAAc;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBwC,OAAO,EAAEzD,aAAc;cACvBrB,OAAO,EAAEY,UAAW;cACpB0D,IAAI,EAAC;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACV;UAAAqB,QAAA,eAEDjF,OAAA,CAACjB,IAAI;YAACgI,QAAQ,EAAEzF,OAAQ;YAAA2D,QAAA,eACtBjF,OAAA,CAAC1B,IAAI;cACH0I,UAAU,EAAE5F,YAAa;cACzB6F,MAAM,EAAE;gBAAEC,SAAS,eAAElH,OAAA,CAACnB,KAAK;kBAACsI,WAAW,EAAC;gBAAU;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxDwD,UAAU,EAAGlD,IAAI,iBACflE,OAAA,CAAC1B,IAAI,CAAC+I,IAAI;gBACRC,OAAO,EAAE,cACPtH,OAAA,CAACzB,MAAM;kBACL0H,IAAI,EAAC,MAAM;kBACX3C,IAAI,eAAEtD,OAAA,CAACR,WAAW;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBwC,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,CAAE;kBAAAe,QAAA,EAC3C;gBAED;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5D,OAAA,CAACf,UAAU;kBACTkE,KAAK,EAAC,oEAAa;kBACnBoE,SAAS,EAAEA,CAAA,KAAMpD,gBAAgB,CAACD,IAAI,CAACsD,OAAO,CAAE;kBAChD3D,MAAM,EAAC,cAAI;kBACXE,UAAU,EAAC,cAAI;kBAAAkB,QAAA,eAEfjF,OAAA,CAACzB,MAAM;oBACL0H,IAAI,EAAC,MAAM;oBACXwB,MAAM;oBACNnE,IAAI,eAAEtD,OAAA,CAACV,YAAY;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAqB,QAAA,EACxB;kBAED;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAqB,QAAA,eAEFjF,OAAA,CAAC1B,IAAI,CAAC+I,IAAI,CAACK,IAAI;kBACbC,MAAM,EAAElD,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;kBACvCvB,KAAK,eACHnD,OAAA,CAACxB,KAAK;oBAAAyG,QAAA,gBACJjF,OAAA,CAACI,IAAI;sBAACwH,MAAM;sBAAA3C,QAAA,EAAErD,cAAc,CAACsC,IAAI,CAAC2D,SAAS;oBAAC;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDuD,WAAW,eACTnH,OAAA;oBAAAiF,QAAA,gBACEjF,OAAA,CAACI,IAAI;sBAAC6F,IAAI,EAAC,WAAW;sBAAC6B,QAAQ,EAAE;wBAAEC,IAAI,EAAE7D,IAAI,CAACsD;sBAAQ,CAAE;sBAAAvC,QAAA,GAAC,MACnD,EAACf,IAAI,CAACsD,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG9D,IAAI,CAACsD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM/D,IAAI,CAACsD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACxC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAACsD,OAAO,CAACnE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAElC,CAAC,eACP5D,OAAA;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;sBAAC6F,IAAI,EAAC,WAAW;sBAAAhB,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACgE,UAAU,CAAC;oBAAA;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEM,IAAI,CAACpF,OAAO,iBACXkB,OAAA,CAAAE,SAAA;sBAAA+E,QAAA,gBACEjF,OAAA;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;wBAAC6F,IAAI,EAAC,WAAW;wBAAAhB,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACpF,OAAO;sBAAA;wBAAA2E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAM,IAAI,CAACiE,QAAQ,KAAKC,SAAS,iBAC1BpI,OAAA;sBAAKuD,KAAK,EAAE;wBAAE8E,SAAS,EAAE;sBAAE,CAAE;sBAAApD,QAAA,eAC3BjF,OAAA,CAACrB,QAAQ;wBAAC2J,OAAO,EAAEpE,IAAI,CAACiE,QAAS;wBAACvC,IAAI,EAAC;sBAAO;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5D,OAAA,CAAC5B,GAAG;QAACmI,IAAI,EAAE,EAAG;QAAAtB,QAAA,eACZjF,OAAA,CAAC9B,IAAI;UACHmI,SAAS,EAAC,eAAe;UACzBlD,KAAK,eACHnD,OAAA,CAACxB,KAAK;YAAAyG,QAAA,gBACJjF,OAAA,CAACZ,mBAAmB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAA5D,OAAA,CAACtB,GAAG;cAAC8E,KAAK,EAAC,SAAS;cAAAyB,QAAA,EAAE5D,cAAc,CAACkD;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACDkD,KAAK,eACH9G,OAAA,CAACzB,MAAM;YACL0H,IAAI,EAAC,SAAS;YACdwB,MAAM;YACN7B,IAAI,EAAC,OAAO;YACZtC,IAAI,eAAEtD,OAAA,CAACP,cAAc;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB2E,QAAQ,EAAElH,cAAc,CAACkD,MAAM,KAAK,CAAE;YACtC6B,OAAO,EAAE/B,oBAAqB;YAAAY,QAAA,EAC/B;UAED;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAqB,QAAA,gBAEDjF,OAAA,CAAC1B,IAAI;YACH0I,UAAU,EAAE1B,4BAA4B,CAAC,CAAE;YAC3C2B,MAAM,EAAE;cACNC,SAAS,eACPlH,OAAA,CAACnB,KAAK;gBACJsI,WAAW,eACTnH,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA;oBAAAiF,QAAA,EAAK;kBAAQ;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnB5D,OAAA,CAACI,IAAI;oBAAC6F,IAAI,EAAC,WAAW;oBAAC1C,KAAK,EAAE;sBAAEuC,QAAQ,EAAE;oBAAO,CAAE;oBAAAb,QAAA,EAAC;kBAEpD;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACFwD,UAAU,EAAGlD,IAAI,iBACflE,OAAA,CAAC1B,IAAI,CAAC+I,IAAI;cACRC,OAAO,EAAE,cACPtH,OAAA,CAACzB,MAAM;gBACL0H,IAAI,EAAC,MAAM;gBACX3C,IAAI,eAAEtD,OAAA,CAACR,WAAW;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBwC,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,CAAE;gBAAAe,QAAA,EAC3C;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5D,OAAA,CAACzB,MAAM;gBACL0H,IAAI,EAAC,MAAM;gBACXwB,MAAM;gBACNnE,IAAI,eAAEtD,OAAA,CAACJ,mBAAmB;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BwC,OAAO,EAAEA,CAAA,KAAMpD,sBAAsB,CAACkB,IAAI,CAACsD,OAAO,CAAE;gBAAAvC,QAAA,EACrD;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAqB,QAAA,eAEFjF,OAAA,CAAC1B,IAAI,CAAC+I,IAAI,CAACK,IAAI;gBACbC,MAAM,EAAElD,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;gBACvCvB,KAAK,eACHnD,OAAA,CAACxB,KAAK;kBAAAyG,QAAA,gBACJjF,OAAA,CAACI,IAAI;oBAACwH,MAAM;oBAAA3C,QAAA,EAAErD,cAAc,CAACsC,IAAI,CAAC2D,SAAS;kBAAC;oBAAApE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACDuD,WAAW,eACTnH,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA,CAACI,IAAI;oBAAC6F,IAAI,EAAC,WAAW;oBAAC6B,QAAQ,EAAE;sBAAEC,IAAI,EAAE7D,IAAI,CAACsD;oBAAQ,CAAE;oBAAAvC,QAAA,GAAC,MACnD,EAACf,IAAI,CAACsD,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG9D,IAAI,CAACsD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM/D,IAAI,CAACsD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACxC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAACsD,OAAO,CAACnE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElC,CAAC,eACP5D,OAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;oBAAC6F,IAAI,EAAC,WAAW;oBAAAhB,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACsE,UAAU,CAAC;kBAAA;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEM,IAAI,CAACQ,MAAM,KAAK7C,WAAW,CAACiD,MAAM,IAAIZ,IAAI,CAACnB,KAAK,iBAC/C/C,OAAA,CAAAE,SAAA;oBAAA+E,QAAA,gBACEjF,OAAA;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;sBAAC6F,IAAI,EAAC,QAAQ;sBAAAhB,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACnB,KAAK;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGDvC,cAAc,CAACkD,MAAM,GAAG,CAAC,iBACxBvE,OAAA;YAAKuD,KAAK,EAAE;cAAE8E,SAAS,EAAE,EAAE;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAxD,QAAA,eACjDjF,OAAA,CAACd,UAAU;cACTwJ,OAAO,EAAEtG,WAAY;cACrBuG,KAAK,EAAEtH,cAAc,CAACkD,MAAO;cAC7BjC,QAAQ,EAAEA,QAAS;cACnBsG,eAAe;cACfC,eAAe;cACfC,SAAS,EAAEA,CAACH,KAAK,EAAEI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQJ,KAAK,IACvC;cACDK,eAAe,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;cACzCC,QAAQ,EAAEvD,gBAAiB;cAC3BwD,gBAAgB,EAAExD;YAAiB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA,CAACpB,KAAK;MACJuE,KAAK,EAAC,0BAAM;MACZgG,IAAI,EAAEnH,iBAAkB;MACxBoH,QAAQ,EAAEA,CAAA,KAAMnH,oBAAoB,CAAC,KAAK,CAAE;MAC5CoH,MAAM,EAAE,cACNrJ,OAAA,CAACzB,MAAM;QAAa6H,OAAO,EAAEA,CAAA,KAAMnE,oBAAoB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACF0F,KAAK,EAAE,GAAI;MAAArE,QAAA,EAEVnD,YAAY,iBACX9B,OAAA;QAAAiF,QAAA,eACEjF,OAAA,CAAC7B,GAAG;UAACmI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAArB,QAAA,gBACpBjF,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAC0H,QAAQ;cAAA7C,QAAA,EAAEnD,YAAY,CAAC0F;YAAO;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EAAErD,cAAc,CAACE,YAAY,CAAC+F,SAAS;YAAC;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EACF,CAAA1E,oBAAA,GAAAuB,YAAY,CAACyH,MAAM,cAAAhJ,oBAAA,eAAnBA,oBAAA,CAAqBiJ,cAAc,KAAAhJ,qBAAA,GAAIsB,YAAY,CAACyH,MAAM,cAAA/I,qBAAA,eAAnBA,qBAAA,CAAqBiJ,mBAAmB,GAC5E,UAAU,EAAAhJ,qBAAA,GAAAqB,YAAY,CAACyH,MAAM,cAAA9I,qBAAA,uBAAnBA,qBAAA,CAAqBiJ,YAAY,OAAAhJ,qBAAA,GAAIoB,YAAY,CAACyH,MAAM,cAAA7I,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBiJ,OAAO,cAAAhJ,qBAAA,uBAA5BA,qBAAA,CAA8B+I,YAAY,KAAI,CAAC,MAAM,GACnG,EAAA9I,qBAAA,GAAAkB,YAAY,CAACyH,MAAM,cAAA3I,qBAAA,uBAAnBA,qBAAA,CAAqBgJ,UAAU,KAAI;YAAO;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLoB,gBAAgB,CAAClD,YAAY,CAAC4C,MAAM,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,gBAC5C7E,OAAA,CAACrB,QAAQ;cAAC2J,OAAO,EAAE,GAAI;cAAC1C,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACtD9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACiD,MAAM,gBAC5C9E,OAAA,CAACrB,QAAQ;cAAC2J,OAAO,EAAExG,YAAY,CAACqG,QAAQ,IAAI,CAAE;cAACvC,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACkD,SAAS,gBAC/C/E,OAAA,CAACrB,QAAQ;cAAC2J,OAAO,EAAExG,YAAY,CAACqG,QAAQ,IAAI,CAAE;cAACvC,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E9B,YAAY,CAACqG,QAAQ,KAAKC,SAAS,gBACrCpI,OAAA,CAACrB,QAAQ;cAAC2J,OAAO,EAAExG,YAAY,CAACqG,QAAS;cAACvC,IAAI,EAAC;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD5D,OAAA,CAACI,IAAI;cAAC6F,IAAI,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EAAEC,UAAU,CAACpD,YAAY,CAACoG,UAAU;YAAC;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EAAEC,UAAU,CAACpD,YAAY,CAAC0G,UAAU;YAAC;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN5D,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EACF,CAAC4E,qBAAA,IAAM;gBACN;gBACA,IAAI/H,YAAY,CAACgI,UAAU,IAAIhI,YAAY,CAACiI,YAAY,EAAE;kBACxD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI9E,IAAI,CAACtD,YAAY,CAACiI,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAI/E,IAAI,CAACtD,YAAY,CAACgI,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACjI,OAAO,GAAGH,QAAQ,GAAG;gBACvB;gBACA;gBAAA,KACK,IAAIlI,YAAY,CAACgI,UAAU,IAAIhI,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAAC8C,OAAO,EAAE;kBAC/E,MAAMqF,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI9E,IAAI,CAAC,CAAC,CAAC+E,OAAO,CAAC,CAAC,GAAG,IAAI/E,IAAI,CAACtD,YAAY,CAACgI,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACxG,OAAO,GAAGH,QAAQ,SAAS;gBAC7B;gBACA;gBAAA,KACK,IAAIlI,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,KAAAgF,qBAAA,GAAI/H,YAAY,CAACyH,MAAM,cAAAM,qBAAA,eAAnBA,qBAAA,CAAqBO,gBAAgB,EAAE;kBAC/F,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACpI,YAAY,CAACyH,MAAM,CAACa,gBAAgB,CAAC,GAAG;gBAC/D;gBACA;gBAAA,KACK,IAAItI,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK,IAAI/C,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACiD,MAAM,EAAE;kBACnD,OAAO,MAAM;gBACf,CAAC,MACI,IAAIhD,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACkD,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK;kBACH,OAAO,MAAM;gBACf;cACF,CAAC,EAAE;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL9B,YAAY,CAACuI,YAAY,IAAIvI,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,iBACzE7E,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EAAEnD,YAAY,CAACuI;YAAY;cAAA5G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACN,EACA9B,YAAY,CAAChD,OAAO,iBACnBkB,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAA6E,QAAA,EAAEnD,YAAY,CAAChD;YAAO;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA9B,YAAY,CAACiB,KAAK,iBACjB/C,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACI,IAAI;cAAC6F,IAAI,EAAC,QAAQ;cAAAhB,QAAA,EAAEnD,YAAY,CAACiB;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA9B,YAAY,CAACwI,MAAM,iBAClBtK,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA;cAAKuD,KAAK,EAAE;gBACVgH,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,CAAC;gBACf3E,QAAQ,EAAE,EAAE;gBACZ4E,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE,MAAM;gBAChBtC,SAAS,EAAE;cACb,CAAE;cAAApD,QAAA,eACAjF,OAAA;gBAAAiF,QAAA,EAAM2F,IAAI,CAACC,SAAS,CAAC/I,YAAY,CAACwI,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAA7G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA9B,YAAY,CAACyH,MAAM,IAAIzH,YAAY,CAAC+F,SAAS,KAAK,UAAU,iBAC3D7H,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACxB,KAAK;cAACsM,IAAI;cAACvH,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,gBAClCjF,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAEtD,OAAA,CAACZ,mBAAmB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL9B,YAAY,CAACyH,MAAM,CAACa,gBAAgB,iBACnCpK,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,gBACZ,EAACgF,IAAI,CAACC,KAAK,CAACpI,YAAY,CAACyH,MAAM,CAACa,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACA9B,YAAY,CAACyH,MAAM,CAACwB,OAAO,iBAC1B/K,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,4BACZ,EAAC+F,MAAM,CAACC,IAAI,CAACnJ,YAAY,CAACyH,MAAM,CAACwB,OAAO,CAAC,CAACxG,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5D,OAAA;cAAKuD,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,eAC3BjF,OAAA,CAACI,IAAI;gBAAC6F,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA9B,YAAY,CAACyH,MAAM,IAAIzH,YAAY,CAAC+F,SAAS,KAAK,YAAY,iBAC7D7H,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACxB,KAAK;cAACsM,IAAI;cAACvH,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,gBAClCjF,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAEtD,OAAA,CAACZ,mBAAmB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAC9CnD,YAAY,CAACyH,MAAM,CAACE,mBAAmB,GAAG,UAAU,GAAG;cAAO;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EAGL9B,YAAY,CAACyH,MAAM,CAACC,cAAc,IAAI1H,YAAY,CAACyH,MAAM,CAACE,mBAAmB,gBAC5EzJ,OAAA,CAAAE,SAAA;gBAAA+E,QAAA,gBACEjF,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,MAAM;kBAAAyB,QAAA,GAAC,4BACV,EAACnD,YAAY,CAACyH,MAAM,CAACG,YAAY,MAAA7I,qBAAA,GAAIiB,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAA9I,qBAAA,uBAA3BA,qBAAA,CAA6B6I,YAAY,KAAI,CAAC;gBAAA;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,eACN5D,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,OAAO;kBAAAyB,QAAA,GAAC,gBACb,EAACnD,YAAY,CAACyH,MAAM,CAAC2B,iBAAiB,MAAApK,sBAAA,GAAIgB,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAA7I,sBAAA,uBAA3BA,sBAAA,CAA6BoK,iBAAiB,KAAI,CAAC;gBAAA;kBAAAzH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,EACL,CAAC9B,YAAY,CAACyH,MAAM,CAAC4B,aAAa,GAAG,CAAC,IAAI,EAAApK,sBAAA,GAAAe,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAA5I,sBAAA,uBAA3BA,sBAAA,CAA6BoK,aAAa,IAAG,CAAC,kBACvFnL,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,KAAK;kBAAAyB,QAAA,GAAC,gBACX,EAACnD,YAAY,CAACyH,MAAM,CAAC4B,aAAa,MAAAnK,sBAAA,GAAIc,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAA3I,sBAAA,uBAA3BA,sBAAA,CAA6BmK,aAAa,KAAI,CAAC;gBAAA;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CACN,eACD5D,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,MAAM;kBAAAyB,QAAA,GAAC,sBACX,EAACgF,IAAI,CAACC,KAAK,CAAE,CAACpI,YAAY,CAACyH,MAAM,CAAC2B,iBAAiB,MAAAjK,sBAAA,GAAIa,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAA1I,sBAAA,uBAA3BA,sBAAA,CAA6BiK,iBAAiB,KAAI,CAAC,KAAKpJ,YAAY,CAACyH,MAAM,CAACG,YAAY,MAAAxI,sBAAA,GAAIY,YAAY,CAACyH,MAAM,CAACI,OAAO,cAAAzI,sBAAA,uBAA3BA,sBAAA,CAA6BwI,YAAY,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC,EAAC,GAClN;gBAAA;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,eACN,CAAC;cAAA;cAEH;cACA5D,OAAA,CAAAE,SAAA;gBAAA+E,QAAA,GACGnD,YAAY,CAACyH,MAAM,CAAC6B,mBAAmB,KAAKhD,SAAS,iBACpDpI,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,QAAQ;kBAAAyB,QAAA,GAAC,kCACN,EAACnD,YAAY,CAACyH,MAAM,CAAC6B,mBAAmB;gBAAA;kBAAA3H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CACN,EACA9B,YAAY,CAACyH,MAAM,CAAC8B,aAAa,KAAKjD,SAAS,iBAC9CpI,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,KAAK;kBAAAyB,QAAA,GAAC,4BAAM,EAACnD,YAAY,CAACyH,MAAM,CAAC8B,aAAa;gBAAA;kBAAA5H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAChE,EACA,EAAAzC,sBAAA,GAAAW,YAAY,CAACyH,MAAM,CAAC+B,WAAW,cAAAnK,sBAAA,uBAA/BA,sBAAA,CAAiCoD,MAAM,kBACtCvE,OAAA,CAACtB,GAAG;kBAAC8E,KAAK,EAAC,MAAM;kBAAAyB,QAAA,GAAC,4BAAM,EAACnD,YAAY,CAACyH,MAAM,CAAC+B,WAAW,CAAC/G,MAAM;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACtE;cAAA,eACD,CACH,EAEA9B,YAAY,CAACyH,MAAM,CAACa,gBAAgB,iBACnCpK,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,gBACd,EAACgF,IAAI,CAACC,KAAK,CAACpI,YAAY,CAACyH,MAAM,CAACa,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5D,OAAA;cAAKuD,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,eAC3BjF,OAAA,CAACI,IAAI;gBAAC6F,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA9B,YAAY,CAACyH,MAAM,IAAIzH,YAAY,CAAC+F,SAAS,KAAK,eAAe,iBAChE7H,OAAA,CAAC5B,GAAG;YAACmI,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZjF,OAAA,CAACI,IAAI;cAACwH,MAAM;cAAA3C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5D,OAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5D,OAAA,CAACxB,KAAK;cAACsM,IAAI;cAACvH,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,gBAClCjF,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAEtD,OAAA,CAACZ,mBAAmB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL9B,YAAY,CAACyH,MAAM,CAACgC,WAAW,iBAC9BvL,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,kCACT,EAACnD,YAAY,CAACyH,MAAM,CAACgC,WAAW;cAAA;gBAAA9H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACN,EACA9B,YAAY,CAACyH,MAAM,CAACiC,gBAAgB,KAAKpD,SAAS,iBACjDpI,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,OAAO;gBAAAyB,QAAA,GAAC,gBACb,EAACnD,YAAY,CAACyH,MAAM,CAACiC,gBAAgB;cAAA;gBAAA/H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACN,EACA9B,YAAY,CAACyH,MAAM,CAACkC,YAAY,KAAKrD,SAAS,IAAItG,YAAY,CAACyH,MAAM,CAACkC,YAAY,GAAG,CAAC,iBACrFzL,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,KAAK;gBAAAyB,QAAA,GAAC,gBACX,EAACnD,YAAY,CAACyH,MAAM,CAACkC,YAAY;cAAA;gBAAAhI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CACN,EACA9B,YAAY,CAACyH,MAAM,CAACa,gBAAgB,iBACnCpK,OAAA,CAACtB,GAAG;gBAAC8E,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,gBACd,EAACgF,IAAI,CAACC,KAAK,CAACpI,YAAY,CAACyH,MAAM,CAACa,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5D,OAAA;cAAKuD,KAAK,EAAE;gBAAE8E,SAAS,EAAE;cAAE,CAAE;cAAApD,QAAA,eAC3BjF,OAAA,CAACI,IAAI;gBAAC6F,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtD,EAAA,CArsBID,eAAyB;EAAA,QAYzBP,cAAc;AAAA;AAAA4L,EAAA,GAZdrL,eAAyB;AAusB/B,eAAeA,eAAe;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}