{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = number;\nfunction number(x) {\n  return +x;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "number", "x"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/number.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = number;\n\nfunction number(x) {\n  return +x;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,CAACA,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script"}