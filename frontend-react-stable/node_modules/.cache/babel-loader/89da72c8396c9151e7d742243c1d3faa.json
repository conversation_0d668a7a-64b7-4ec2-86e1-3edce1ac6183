{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = intersection;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction intersection(values) {\n  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    others[_key - 1] = arguments[_key];\n  }\n  values = new _index.InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\nfunction set(values) {\n  return values instanceof _index.InternSet ? values : new _index.InternSet(values);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "intersection", "_index", "require", "values", "_len", "arguments", "length", "others", "Array", "_key", "InternSet", "map", "set", "out", "other", "has", "delete"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/intersection.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = intersection;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction intersection(values, ...others) {\n  values = new _index.InternSet(values);\n  others = others.map(set);\n\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof _index.InternSet ? values : new _index.InternSet(values);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,YAAY;AAE9B,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,YAAYA,CAACG,MAAM,EAAa;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACrCN,MAAM,GAAG,IAAIF,MAAM,CAACS,SAAS,CAACP,MAAM,CAAC;EACrCI,MAAM,GAAGA,MAAM,CAACI,GAAG,CAACC,GAAG,CAAC;EAExBC,GAAG,EAAE,KAAK,MAAMf,KAAK,IAAIK,MAAM,EAAE;IAC/B,KAAK,MAAMW,KAAK,IAAIP,MAAM,EAAE;MAC1B,IAAI,CAACO,KAAK,CAACC,GAAG,CAACjB,KAAK,CAAC,EAAE;QACrBK,MAAM,CAACa,MAAM,CAAClB,KAAK,CAAC;QACpB,SAASe,GAAG;MACd;IACF;EACF;EAEA,OAAOV,MAAM;AACf;AAEA,SAASS,GAAGA,CAACT,MAAM,EAAE;EACnB,OAAOA,MAAM,YAAYF,MAAM,CAACS,SAAS,GAAGP,MAAM,GAAG,IAAIF,MAAM,CAACS,SAAS,CAACP,MAAM,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}