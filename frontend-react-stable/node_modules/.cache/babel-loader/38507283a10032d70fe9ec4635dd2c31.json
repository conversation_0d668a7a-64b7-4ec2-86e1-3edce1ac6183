{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Popover from '../popover';\nimport { cloneElement } from '../_util/reactNode';\nimport Avatar from './avatar';\nimport { SizeContextProvider } from './SizeContext';\nvar Group = function Group(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    maxCount = props.maxCount,\n    maxStyle = props.maxStyle,\n    size = props.size;\n  var prefixCls = getPrefixCls('avatar-group', customizePrefixCls);\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  var children = props.children,\n    _props$maxPopoverPlac = props.maxPopoverPlacement,\n    maxPopoverPlacement = _props$maxPopoverPlac === void 0 ? 'top' : _props$maxPopoverPlac,\n    _props$maxPopoverTrig = props.maxPopoverTrigger,\n    maxPopoverTrigger = _props$maxPopoverTrig === void 0 ? 'hover' : _props$maxPopoverTrig;\n  var childrenWithProps = toArray(children).map(function (child, index) {\n    return cloneElement(child, {\n      key: \"avatar-key-\".concat(index)\n    });\n  });\n  var numOfChildren = childrenWithProps.length;\n  if (maxCount && maxCount < numOfChildren) {\n    var childrenShow = childrenWithProps.slice(0, maxCount);\n    var childrenHidden = childrenWithProps.slice(maxCount, numOfChildren);\n    childrenShow.push(/*#__PURE__*/React.createElement(Popover, {\n      key: \"avatar-popover-key\",\n      content: childrenHidden,\n      trigger: maxPopoverTrigger,\n      placement: maxPopoverPlacement,\n      overlayClassName: \"\".concat(prefixCls, \"-popover\")\n    }, /*#__PURE__*/React.createElement(Avatar, {\n      style: maxStyle\n    }, \"+\".concat(numOfChildren - maxCount))));\n    return /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: size\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: props.style\n    }, childrenShow));\n  }\n  return /*#__PURE__*/React.createElement(SizeContextProvider, {\n    size: size\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: props.style\n  }, childrenWithProps));\n};\nexport default Group;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "toArray", "React", "ConfigContext", "Popover", "cloneElement", "Avatar", "SizeContextProvider", "Group", "props", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$className", "className", "maxCount", "maxStyle", "size", "cls", "concat", "children", "_props$maxPopoverPlac", "maxPopoverPlacement", "_props$maxPopoverTrig", "maxPopoverTrigger", "childrenWithProps", "map", "child", "index", "key", "numOfChildren", "length", "childrenShow", "slice", "childrenH<PERSON>den", "push", "createElement", "content", "trigger", "placement", "overlayClassName", "style"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/avatar/group.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Popover from '../popover';\nimport { cloneElement } from '../_util/reactNode';\nimport Avatar from './avatar';\nimport { SizeContextProvider } from './SizeContext';\nvar Group = function Group(props) {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    maxCount = props.maxCount,\n    maxStyle = props.maxStyle,\n    size = props.size;\n  var prefixCls = getPrefixCls('avatar-group', customizePrefixCls);\n  var cls = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  var children = props.children,\n    _props$maxPopoverPlac = props.maxPopoverPlacement,\n    maxPopoverPlacement = _props$maxPopoverPlac === void 0 ? 'top' : _props$maxPopoverPlac,\n    _props$maxPopoverTrig = props.maxPopoverTrigger,\n    maxPopoverTrigger = _props$maxPopoverTrig === void 0 ? 'hover' : _props$maxPopoverTrig;\n  var childrenWithProps = toArray(children).map(function (child, index) {\n    return cloneElement(child, {\n      key: \"avatar-key-\".concat(index)\n    });\n  });\n  var numOfChildren = childrenWithProps.length;\n  if (maxCount && maxCount < numOfChildren) {\n    var childrenShow = childrenWithProps.slice(0, maxCount);\n    var childrenHidden = childrenWithProps.slice(maxCount, numOfChildren);\n    childrenShow.push( /*#__PURE__*/React.createElement(Popover, {\n      key: \"avatar-popover-key\",\n      content: childrenHidden,\n      trigger: maxPopoverTrigger,\n      placement: maxPopoverPlacement,\n      overlayClassName: \"\".concat(prefixCls, \"-popover\")\n    }, /*#__PURE__*/React.createElement(Avatar, {\n      style: maxStyle\n    }, \"+\".concat(numOfChildren - maxCount))));\n    return /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: size\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: props.style\n    }, childrenShow));\n  }\n  return /*#__PURE__*/React.createElement(SizeContextProvider, {\n    size: size\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: props.style\n  }, childrenWithProps));\n};\nexport default Group;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,mBAAmB,QAAQ,eAAe;AACnD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,iBAAiB,GAAGR,KAAK,CAACS,UAAU,CAACR,aAAa,CAAC;IACrDS,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;IACtCC,gBAAgB,GAAGP,KAAK,CAACQ,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,IAAI,GAAGX,KAAK,CAACW,IAAI;EACnB,IAAIL,SAAS,GAAGH,YAAY,CAAC,cAAc,EAAEE,kBAAkB,CAAC;EAChE,IAAIO,GAAG,GAAGrB,UAAU,CAACe,SAAS,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuB,MAAM,CAACP,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAEI,SAAS,CAAC;EAClH,IAAIM,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IAC3BC,qBAAqB,GAAGf,KAAK,CAACgB,mBAAmB;IACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACtFE,qBAAqB,GAAGjB,KAAK,CAACkB,iBAAiB;IAC/CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;EACxF,IAAIE,iBAAiB,GAAG3B,OAAO,CAACsB,QAAQ,CAAC,CAACM,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACpE,OAAO1B,YAAY,CAACyB,KAAK,EAAE;MACzBE,GAAG,EAAE,aAAa,CAACV,MAAM,CAACS,KAAK;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIE,aAAa,GAAGL,iBAAiB,CAACM,MAAM;EAC5C,IAAIhB,QAAQ,IAAIA,QAAQ,GAAGe,aAAa,EAAE;IACxC,IAAIE,YAAY,GAAGP,iBAAiB,CAACQ,KAAK,CAAC,CAAC,EAAElB,QAAQ,CAAC;IACvD,IAAImB,cAAc,GAAGT,iBAAiB,CAACQ,KAAK,CAAClB,QAAQ,EAAEe,aAAa,CAAC;IACrEE,YAAY,CAACG,IAAI,CAAE,aAAapC,KAAK,CAACqC,aAAa,CAACnC,OAAO,EAAE;MAC3D4B,GAAG,EAAE,oBAAoB;MACzBQ,OAAO,EAAEH,cAAc;MACvBI,OAAO,EAAEd,iBAAiB;MAC1Be,SAAS,EAAEjB,mBAAmB;MAC9BkB,gBAAgB,EAAE,EAAE,CAACrB,MAAM,CAACP,SAAS,EAAE,UAAU;IACnD,CAAC,EAAE,aAAab,KAAK,CAACqC,aAAa,CAACjC,MAAM,EAAE;MAC1CsC,KAAK,EAAEzB;IACT,CAAC,EAAE,GAAG,CAACG,MAAM,CAACW,aAAa,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1C,OAAO,aAAahB,KAAK,CAACqC,aAAa,CAAChC,mBAAmB,EAAE;MAC3Da,IAAI,EAAEA;IACR,CAAC,EAAE,aAAalB,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;MACzCtB,SAAS,EAAEI,GAAG;MACduB,KAAK,EAAEnC,KAAK,CAACmC;IACf,CAAC,EAAET,YAAY,CAAC,CAAC;EACnB;EACA,OAAO,aAAajC,KAAK,CAACqC,aAAa,CAAChC,mBAAmB,EAAE;IAC3Da,IAAI,EAAEA;EACR,CAAC,EAAE,aAAalB,KAAK,CAACqC,aAAa,CAAC,KAAK,EAAE;IACzCtB,SAAS,EAAEI,GAAG;IACduB,KAAK,EAAEnC,KAAK,CAACmC;EACf,CAAC,EAAEhB,iBAAiB,CAAC,CAAC;AACxB,CAAC;AACD,eAAepB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}