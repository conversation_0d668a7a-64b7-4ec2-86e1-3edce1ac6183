{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport warning from '../_util/warning';\nimport CollapsePanel from './CollapsePanel';\nvar Collapse = function Collapse(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$bordered = props.bordered,\n    bordered = _props$bordered === void 0 ? true : _props$bordered,\n    ghost = props.ghost,\n    _props$expandIconPosi = props.expandIconPosition,\n    expandIconPosition = _props$expandIconPosi === void 0 ? 'start' : _props$expandIconPosi;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  // Warning if use legacy type `expandIconPosition`\n  process.env.NODE_ENV !== \"production\" ? warning(expandIconPosition !== 'left' && expandIconPosition !== 'right', 'Collapse', '`expandIconPosition` with `left` or `right` is deprecated. Please use `start` or `end` instead.') : void 0;\n  // Align with logic position\n  var mergedExpandIconPosition = React.useMemo(function () {\n    if (expandIconPosition === 'left') {\n      return 'start';\n    }\n    return expandIconPosition === 'right' ? 'end' : expandIconPosition;\n  }, [expandIconPosition]);\n  var renderExpandIcon = function renderExpandIcon() {\n    var panelProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expandIcon = props.expandIcon;\n    var icon = expandIcon ? expandIcon(panelProps) : /*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? 90 : undefined\n    });\n    return cloneElement(icon, function () {\n      return {\n        className: classNames(icon.props.className, \"\".concat(prefixCls, \"-arrow\"))\n      };\n    });\n  };\n  var collapseClassName = classNames(\"\".concat(prefixCls, \"-icon-position-\").concat(mergedExpandIconPosition), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), !!ghost), _classNames), className);\n  var openMotion = _extends(_extends({}, collapseMotion), {\n    motionAppear: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  });\n  var getItems = function getItems() {\n    var children = props.children;\n    return toArray(children).map(function (child, index) {\n      var _a;\n      if ((_a = child.props) === null || _a === void 0 ? void 0 : _a.disabled) {\n        var key = child.key || String(index);\n        var _child$props = child.props,\n          disabled = _child$props.disabled,\n          collapsible = _child$props.collapsible;\n        var childProps = _extends(_extends({}, omit(child.props, ['disabled'])), {\n          key: key,\n          collapsible: collapsible !== null && collapsible !== void 0 ? collapsible : disabled ? 'disabled' : undefined\n        });\n        return cloneElement(child, childProps);\n      }\n      return child;\n    });\n  };\n  return /*#__PURE__*/React.createElement(RcCollapse, _extends({\n    openMotion: openMotion\n  }, props, {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName\n  }), getItems());\n};\nCollapse.Panel = CollapsePanel;\nexport default Collapse;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "RightOutlined", "classNames", "RcCollapse", "React", "toArray", "omit", "ConfigContext", "collapseMotion", "cloneElement", "warning", "CollapsePanel", "Collapse", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$className", "className", "_props$bordered", "bordered", "ghost", "_props$expandIconPosi", "expandIconPosition", "process", "env", "NODE_ENV", "mergedExpandIconPosition", "useMemo", "renderExpandIcon", "panelProps", "arguments", "length", "undefined", "expandIcon", "icon", "createElement", "rotate", "isActive", "concat", "collapseClassName", "openMotion", "motionAppear", "leavedClassName", "getItems", "children", "map", "child", "index", "_a", "disabled", "key", "String", "_child$props", "collapsible", "childProps", "Panel"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/collapse/Collapse.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcCollapse from 'rc-collapse';\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { ConfigContext } from '../config-provider';\nimport collapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport warning from '../_util/warning';\nimport CollapsePanel from './CollapsePanel';\nvar Collapse = function Collapse(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$bordered = props.bordered,\n    bordered = _props$bordered === void 0 ? true : _props$bordered,\n    ghost = props.ghost,\n    _props$expandIconPosi = props.expandIconPosition,\n    expandIconPosition = _props$expandIconPosi === void 0 ? 'start' : _props$expandIconPosi;\n  var prefixCls = getPrefixCls('collapse', customizePrefixCls);\n  // Warning if use legacy type `expandIconPosition`\n  process.env.NODE_ENV !== \"production\" ? warning(expandIconPosition !== 'left' && expandIconPosition !== 'right', 'Collapse', '`expandIconPosition` with `left` or `right` is deprecated. Please use `start` or `end` instead.') : void 0;\n  // Align with logic position\n  var mergedExpandIconPosition = React.useMemo(function () {\n    if (expandIconPosition === 'left') {\n      return 'start';\n    }\n    return expandIconPosition === 'right' ? 'end' : expandIconPosition;\n  }, [expandIconPosition]);\n  var renderExpandIcon = function renderExpandIcon() {\n    var panelProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var expandIcon = props.expandIcon;\n    var icon = expandIcon ? expandIcon(panelProps) : /*#__PURE__*/React.createElement(RightOutlined, {\n      rotate: panelProps.isActive ? 90 : undefined\n    });\n    return cloneElement(icon, function () {\n      return {\n        className: classNames(icon.props.className, \"\".concat(prefixCls, \"-arrow\"))\n      };\n    });\n  };\n  var collapseClassName = classNames(\"\".concat(prefixCls, \"-icon-position-\").concat(mergedExpandIconPosition), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-borderless\"), !bordered), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), !!ghost), _classNames), className);\n  var openMotion = _extends(_extends({}, collapseMotion), {\n    motionAppear: false,\n    leavedClassName: \"\".concat(prefixCls, \"-content-hidden\")\n  });\n  var getItems = function getItems() {\n    var children = props.children;\n    return toArray(children).map(function (child, index) {\n      var _a;\n      if ((_a = child.props) === null || _a === void 0 ? void 0 : _a.disabled) {\n        var key = child.key || String(index);\n        var _child$props = child.props,\n          disabled = _child$props.disabled,\n          collapsible = _child$props.collapsible;\n        var childProps = _extends(_extends({}, omit(child.props, ['disabled'])), {\n          key: key,\n          collapsible: collapsible !== null && collapsible !== void 0 ? collapsible : disabled ? 'disabled' : undefined\n        });\n        return cloneElement(child, childProps);\n      }\n      return child;\n    });\n  };\n  return /*#__PURE__*/React.createElement(RcCollapse, _extends({\n    openMotion: openMotion\n  }, props, {\n    expandIcon: renderExpandIcon,\n    prefixCls: prefixCls,\n    className: collapseClassName\n  }), getItems());\n};\nCollapse.Panel = CollapsePanel;\nexport default Collapse;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,WAAW;EACf,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;IACrDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,kBAAkB,GAAGN,KAAK,CAACO,SAAS;IACtCC,gBAAgB,GAAGR,KAAK,CAACS,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,eAAe,GAAGV,KAAK,CAACW,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,KAAK,GAAGZ,KAAK,CAACY,KAAK;IACnBC,qBAAqB,GAAGb,KAAK,CAACc,kBAAkB;IAChDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;EACzF,IAAIN,SAAS,GAAGH,YAAY,CAAC,UAAU,EAAEE,kBAAkB,CAAC;EAC5D;EACAS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,OAAO,CAACiB,kBAAkB,KAAK,MAAM,IAAIA,kBAAkB,KAAK,OAAO,EAAE,UAAU,EAAE,iGAAiG,CAAC,GAAG,KAAK,CAAC;EACxO;EACA,IAAII,wBAAwB,GAAG3B,KAAK,CAAC4B,OAAO,CAAC,YAAY;IACvD,IAAIL,kBAAkB,KAAK,MAAM,EAAE;MACjC,OAAO,OAAO;IAChB;IACA,OAAOA,kBAAkB,KAAK,OAAO,GAAG,KAAK,GAAGA,kBAAkB;EACpE,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACxB,IAAIM,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvF,IAAIG,UAAU,GAAGzB,KAAK,CAACyB,UAAU;IACjC,IAAIC,IAAI,GAAGD,UAAU,GAAGA,UAAU,CAACJ,UAAU,CAAC,GAAG,aAAa9B,KAAK,CAACoC,aAAa,CAACvC,aAAa,EAAE;MAC/FwC,MAAM,EAAEP,UAAU,CAACQ,QAAQ,GAAG,EAAE,GAAGL;IACrC,CAAC,CAAC;IACF,OAAO5B,YAAY,CAAC8B,IAAI,EAAE,YAAY;MACpC,OAAO;QACLjB,SAAS,EAAEpB,UAAU,CAACqC,IAAI,CAAC1B,KAAK,CAACS,SAAS,EAAE,EAAE,CAACqB,MAAM,CAACvB,SAAS,EAAE,QAAQ,CAAC;MAC5E,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,IAAIwB,iBAAiB,GAAG1C,UAAU,CAAC,EAAE,CAACyC,MAAM,CAACvB,SAAS,EAAE,iBAAiB,CAAC,CAACuB,MAAM,CAACZ,wBAAwB,CAAC,GAAGjB,WAAW,GAAG,CAAC,CAAC,EAAEd,eAAe,CAACc,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,aAAa,CAAC,EAAE,CAACI,QAAQ,CAAC,EAAExB,eAAe,CAACc,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAElB,eAAe,CAACc,WAAW,EAAE,EAAE,CAAC6B,MAAM,CAACvB,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAACK,KAAK,CAAC,EAAEX,WAAW,GAAGQ,SAAS,CAAC;EAC9X,IAAIuB,UAAU,GAAG9C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAES,cAAc,CAAC,EAAE;IACtDsC,YAAY,EAAE,KAAK;IACnBC,eAAe,EAAE,EAAE,CAACJ,MAAM,CAACvB,SAAS,EAAE,iBAAiB;EACzD,CAAC,CAAC;EACF,IAAI4B,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,IAAIC,QAAQ,GAAGpC,KAAK,CAACoC,QAAQ;IAC7B,OAAO5C,OAAO,CAAC4C,QAAQ,CAAC,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MACnD,IAAIC,EAAE;MACN,IAAI,CAACA,EAAE,GAAGF,KAAK,CAACtC,KAAK,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,EAAE;QACvE,IAAIC,GAAG,GAAGJ,KAAK,CAACI,GAAG,IAAIC,MAAM,CAACJ,KAAK,CAAC;QACpC,IAAIK,YAAY,GAAGN,KAAK,CAACtC,KAAK;UAC5ByC,QAAQ,GAAGG,YAAY,CAACH,QAAQ;UAChCI,WAAW,GAAGD,YAAY,CAACC,WAAW;QACxC,IAAIC,UAAU,GAAG5D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,IAAI,CAAC6C,KAAK,CAACtC,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;UACvE0C,GAAG,EAAEA,GAAG;UACRG,WAAW,EAAEA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGJ,QAAQ,GAAG,UAAU,GAAGjB;QACtG,CAAC,CAAC;QACF,OAAO5B,YAAY,CAAC0C,KAAK,EAAEQ,UAAU,CAAC;MACxC;MACA,OAAOR,KAAK;IACd,CAAC,CAAC;EACJ,CAAC;EACD,OAAO,aAAa/C,KAAK,CAACoC,aAAa,CAACrC,UAAU,EAAEJ,QAAQ,CAAC;IAC3D8C,UAAU,EAAEA;EACd,CAAC,EAAEhC,KAAK,EAAE;IACRyB,UAAU,EAAEL,gBAAgB;IAC5Bb,SAAS,EAAEA,SAAS;IACpBE,SAAS,EAAEsB;EACb,CAAC,CAAC,EAAEI,QAAQ,CAAC,CAAC,CAAC;AACjB,CAAC;AACDpC,QAAQ,CAACgD,KAAK,GAAGjD,aAAa;AAC9B,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}