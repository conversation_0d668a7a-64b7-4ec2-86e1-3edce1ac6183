{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport RcSwitch from 'rc-switch';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport SizeContext from '../config-provider/SizeContext';\nimport warning from '../_util/warning';\nimport Wave from '../_util/wave';\nvar Switch = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    customizeSize = _a.size,\n    customDisabled = _a.disabled,\n    loading = _a.loading,\n    _a$className = _a.className,\n    className = _a$className === void 0 ? '' : _a$className,\n    props = __rest(_a, [\"prefixCls\", \"size\", \"disabled\", \"loading\", \"className\"]);\n  process.env.NODE_ENV !== \"production\" ? warning('checked' in props || !('value' in props), 'Switch', '`value` is not a valid prop, do you mean `checked`?') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var size = React.useContext(SizeContext);\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = (customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled) || loading;\n  var prefixCls = getPrefixCls('switch', customizePrefixCls);\n  var loadingIcon = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-handle\")\n  }, loading && /*#__PURE__*/React.createElement(LoadingOutlined, {\n    className: \"\".concat(prefixCls, \"-loading-icon\")\n  }));\n  var classes = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-small\"), (customizeSize || size) === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  return /*#__PURE__*/React.createElement(Wave, {\n    insertExtraNode: true\n  }, /*#__PURE__*/React.createElement(RcSwitch, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: classes,\n    disabled: mergedDisabled,\n    ref: ref,\n    loadingIcon: loadingIcon\n  })));\n});\nSwitch.__ANT_SWITCH = true;\nif (process.env.NODE_ENV !== 'production') {\n  Switch.displayName = 'Switch';\n}\nexport default Switch;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "LoadingOutlined", "classNames", "RcSwitch", "React", "ConfigContext", "DisabledContext", "SizeContext", "warning", "Wave", "Switch", "forwardRef", "_a", "ref", "_classNames", "customizePrefixCls", "prefixCls", "customizeSize", "size", "customDisabled", "disabled", "loading", "_a$className", "className", "props", "process", "env", "NODE_ENV", "_React$useContext", "useContext", "getPrefixCls", "direction", "mergedDisabled", "loadingIcon", "createElement", "concat", "classes", "insertExtraNode", "__ANT_SWITCH", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/switch/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport RcSwitch from 'rc-switch';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport SizeContext from '../config-provider/SizeContext';\nimport warning from '../_util/warning';\nimport Wave from '../_util/wave';\nvar Switch = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var customizePrefixCls = _a.prefixCls,\n    customizeSize = _a.size,\n    customDisabled = _a.disabled,\n    loading = _a.loading,\n    _a$className = _a.className,\n    className = _a$className === void 0 ? '' : _a$className,\n    props = __rest(_a, [\"prefixCls\", \"size\", \"disabled\", \"loading\", \"className\"]);\n  process.env.NODE_ENV !== \"production\" ? warning('checked' in props || !('value' in props), 'Switch', '`value` is not a valid prop, do you mean `checked`?') : void 0;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var size = React.useContext(SizeContext);\n  // ===================== Disabled =====================\n  var disabled = React.useContext(DisabledContext);\n  var mergedDisabled = (customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled) || loading;\n  var prefixCls = getPrefixCls('switch', customizePrefixCls);\n  var loadingIcon = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-handle\")\n  }, loading && /*#__PURE__*/React.createElement(LoadingOutlined, {\n    className: \"\".concat(prefixCls, \"-loading-icon\")\n  }));\n  var classes = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-small\"), (customizeSize || size) === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-loading\"), loading), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  return /*#__PURE__*/React.createElement(Wave, {\n    insertExtraNode: true\n  }, /*#__PURE__*/React.createElement(RcSwitch, _extends({}, props, {\n    prefixCls: prefixCls,\n    className: classes,\n    disabled: mergedDisabled,\n    ref: ref,\n    loadingIcon: loadingIcon\n  })));\n});\nSwitch.__ANT_SWITCH = true;\nif (process.env.NODE_ENV !== 'production') {\n  Switch.displayName = 'Switch';\n}\nexport default Switch;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,IAAI,MAAM,eAAe;AAChC,IAAIC,MAAM,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAC5D,IAAIC,WAAW;EACf,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACnCC,aAAa,GAAGL,EAAE,CAACM,IAAI;IACvBC,cAAc,GAAGP,EAAE,CAACQ,QAAQ;IAC5BC,OAAO,GAAGT,EAAE,CAACS,OAAO;IACpBC,YAAY,GAAGV,EAAE,CAACW,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,YAAY;IACvDE,KAAK,GAAGrC,MAAM,CAACyB,EAAE,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;EAC/Ea,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,OAAO,CAAC,SAAS,IAAIgB,KAAK,IAAI,EAAE,OAAO,IAAIA,KAAK,CAAC,EAAE,QAAQ,EAAE,qDAAqD,CAAC,GAAG,KAAK,CAAC;EACpK,IAAII,iBAAiB,GAAGxB,KAAK,CAACyB,UAAU,CAACxB,aAAa,CAAC;IACrDyB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIb,IAAI,GAAGd,KAAK,CAACyB,UAAU,CAACtB,WAAW,CAAC;EACxC;EACA,IAAIa,QAAQ,GAAGhB,KAAK,CAACyB,UAAU,CAACvB,eAAe,CAAC;EAChD,IAAI0B,cAAc,GAAG,CAACb,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGC,QAAQ,KAAKC,OAAO;EAClH,IAAIL,SAAS,GAAGc,YAAY,CAAC,QAAQ,EAAEf,kBAAkB,CAAC;EAC1D,IAAIkB,WAAW,GAAG,aAAa7B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACxDX,SAAS,EAAE,EAAE,CAACY,MAAM,CAACnB,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEK,OAAO,IAAI,aAAajB,KAAK,CAAC8B,aAAa,CAACjC,eAAe,EAAE;IAC9DsB,SAAS,EAAE,EAAE,CAACY,MAAM,CAACnB,SAAS,EAAE,eAAe;EACjD,CAAC,CAAC,CAAC;EACH,IAAIoB,OAAO,GAAGlC,UAAU,EAAEY,WAAW,GAAG,CAAC,CAAC,EAAE5B,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACqB,MAAM,CAACnB,SAAS,EAAE,QAAQ,CAAC,EAAE,CAACC,aAAa,IAAIC,IAAI,MAAM,OAAO,CAAC,EAAEhC,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACqB,MAAM,CAACnB,SAAS,EAAE,UAAU,CAAC,EAAEK,OAAO,CAAC,EAAEnC,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACqB,MAAM,CAACnB,SAAS,EAAE,MAAM,CAAC,EAAEe,SAAS,KAAK,KAAK,CAAC,EAAEjB,WAAW,GAAGS,SAAS,CAAC;EACjU,OAAO,aAAanB,KAAK,CAAC8B,aAAa,CAACzB,IAAI,EAAE;IAC5C4B,eAAe,EAAE;EACnB,CAAC,EAAE,aAAajC,KAAK,CAAC8B,aAAa,CAAC/B,QAAQ,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;IAChER,SAAS,EAAEA,SAAS;IACpBO,SAAS,EAAEa,OAAO;IAClBhB,QAAQ,EAAEY,cAAc;IACxBnB,GAAG,EAAEA,GAAG;IACRoB,WAAW,EAAEA;EACf,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACFvB,MAAM,CAAC4B,YAAY,GAAG,IAAI;AAC1B,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCjB,MAAM,CAAC6B,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAe7B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}