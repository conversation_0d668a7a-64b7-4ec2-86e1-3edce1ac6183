{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  var _selectedTask$result$, _selectedTask$result$2, _selectedTask$result$3;\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空已完成任务\n  const handleClearCompleted = async () => {\n    try {\n      await clearCompletedTasks();\n      message.success('已清空完成的任务');\n    } catch (error) {\n      console.error('清空任务失败:', error);\n    }\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",\n            children: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true,\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 278,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Popconfirm, {\n            title: \"\\u786E\\u5B9A\\u8981\\u6E05\\u7A7A\\u6240\\u6709\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\\u5417\\uFF1F\",\n            onConfirm: handleClearCompleted,\n            okText: \"\\u786E\\u5B9A\",\n            cancelText: \"\\u53D6\\u6D88\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              danger: true,\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 25\n              }, this),\n              disabled: completedTasks.length === 0,\n              children: \"\\u6E05\\u7A7A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: completedTasks,\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 17\n          }, this), selectedTask.result && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: selectedTask.task_type === 'training' ? '🎯 训练结果:' : '🔍 预测结果:'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 19\n            }, this), selectedTask.task_type === 'training' && selectedTask.result.r2_score && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"R\\xB2 \\u5206\\u6570: \", selectedTask.result.r2_score.toFixed(4)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u8BAD\\u7EC3\\u6837\\u672C: \", ((_selectedTask$result$ = selectedTask.result.train_shape) === null || _selectedTask$result$ === void 0 ? void 0 : _selectedTask$result$[0]) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"orange\",\n                  children: [\"\\u6D4B\\u8BD5\\u6837\\u672C: \", ((_selectedTask$result$2 = selectedTask.result.test_shape) === null || _selectedTask$result$2 === void 0 ? void 0 : _selectedTask$result$2[0]) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 25\n                }, this), selectedTask.result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5F02\\u5E38\\u9608\\u503C: \", selectedTask.result.static_anomaly_threshold.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 21\n            }, this), selectedTask.task_type === 'prediction' && selectedTask.result.anomaly_count !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u5F02\\u5E38\\u6570\\u91CF: \", selectedTask.result.anomaly_count]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [\"\\u9884\\u6D4B\\u6570\\u636E\\u70B9: \", ((_selectedTask$result$3 = selectedTask.result.predictions) === null || _selectedTask$result$3 === void 0 ? void 0 : _selectedTask$result$3.length) || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 25\n                }, this), selectedTask.result.suggested_threshold && /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"orange\",\n                  children: [\"\\u5EFA\\u8BAE\\u9608\\u503C: \", selectedTask.result.suggested_threshold.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u6A21\\u578B: \", selectedTask.result.model_name || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 16,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.result, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"PqritZihkqxsa0b8+o2T2yh9hdU=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "_selectedTask$result$", "_selectedTask$result$2", "_selectedTask$result$3", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleViewTaskDetail", "task", "handleCancelTask", "taskId", "console", "handleClearCompleted", "getTaskStatusIcon", "status", "RUNNING", "spin", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "icon", "children", "formatTime", "timeString", "Date", "toLocaleString", "padding", "marginBottom", "display", "justifyContent", "alignItems", "level", "type", "onClick", "gutter", "span", "title", "value", "length", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "okText", "cancelText", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "slice", "substring", "created_at", "progress", "undefined", "marginTop", "percent", "size", "disabled", "fontSize", "updated_at", "open", "onCancel", "footer", "width", "result", "r2_score", "wrap", "toFixed", "train_shape", "test_shape", "static_anomaly_threshold", "anomaly_count", "predictions", "suggested_threshold", "model_name", "background", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空已完成任务\n  const handleClearCompleted = async () => {\n    try {\n      await clearCompletedTasks();\n      message.success('已清空完成的任务');\n    } catch (error) {\n      console.error('清空任务失败:', error);\n    }\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>任务管理</Title>\n          <Text type=\"secondary\">\n            查看和管理异步训练、预测任务的状态和结果\n          </Text>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Popconfirm\n                title=\"确定要清空所有已完成的任务吗？\"\n                onConfirm={handleClearCompleted}\n                okText=\"确定\"\n                cancelText=\"取消\"\n              >\n                <Button\n                  type=\"primary\"\n                  danger\n                  size=\"small\"\n                  icon={<DeleteOutlined />}\n                  disabled={completedTasks.length === 0}\n                >\n                  清空\n                </Button>\n              </Popconfirm>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.result && (\n                <Col span={24}>\n                  <Text strong>\n                    {selectedTask.task_type === 'training' ? '🎯 训练结果:' : '🔍 预测结果:'}\n                  </Text>\n                  <br />\n                  {selectedTask.task_type === 'training' && selectedTask.result.r2_score && (\n                    <div style={{ marginTop: 8, marginBottom: 16 }}>\n                      <Space wrap>\n                        <Tag color=\"blue\">R² 分数: {selectedTask.result.r2_score.toFixed(4)}</Tag>\n                        <Tag color=\"green\">训练样本: {selectedTask.result.train_shape?.[0] || 'N/A'}</Tag>\n                        <Tag color=\"orange\">测试样本: {selectedTask.result.test_shape?.[0] || 'N/A'}</Tag>\n                        {selectedTask.result.static_anomaly_threshold && (\n                          <Tag color=\"red\">异常阈值: {selectedTask.result.static_anomaly_threshold.toFixed(2)}</Tag>\n                        )}\n                      </Space>\n                    </div>\n                  )}\n                  {selectedTask.task_type === 'prediction' && selectedTask.result.anomaly_count !== undefined && (\n                    <div style={{ marginTop: 8, marginBottom: 16 }}>\n                      <Space wrap>\n                        <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                        <Tag color=\"blue\">预测数据点: {selectedTask.result.predictions?.length || 'N/A'}</Tag>\n                        {selectedTask.result.suggested_threshold && (\n                          <Tag color=\"orange\">建议阈值: {selectedTask.result.suggested_threshold.toFixed(2)}</Tag>\n                        )}\n                        <Tag color=\"green\">模型: {selectedTask.result.model_name || 'N/A'}</Tag>\n                      </Space>\n                    </div>\n                  )}\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 16,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.result, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,QAEb,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAElC,MAAMyB,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGpB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACdyC,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACA1C,SAAS,CAAC,MAAM;IACd,MAAMqD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMc,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACZ,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMe,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCJ,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMK,OAAO,CAACC,GAAG,CAAC,CAChBjB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF7B,OAAO,CAAC8C,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAIC,IAAU,IAAK;IAC3Cd,eAAe,CAACc,IAAI,CAAC;IACrBZ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMa,gBAAgB,GAAG,MAAOC,MAAc,IAAK;IACjD,IAAI;MACF,MAAMrB,UAAU,CAACqB,MAAM,CAAC;MACxB,MAAMvB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMtB,mBAAmB,CAAC,CAAC;MAC3B/B,OAAO,CAAC8C,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMO,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAKtB,WAAW,CAACuB,OAAO;QACtB,oBAAOzC,OAAA,CAACX,YAAY;UAACqD,IAAI;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK9B,WAAW,CAAC+B,SAAS;QACxB,oBAAOjD,OAAA,CAACV,mBAAmB;UAACqD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK9B,WAAW,CAACgC,MAAM;QACrB,oBAAOlD,OAAA,CAACT,mBAAmB;UAACoD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK9B,WAAW,CAACiC,SAAS;QACxB,oBAAOnD,OAAA,CAACR,YAAY;UAACmD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAOhD,OAAA,CAACP,mBAAmB;UAACkD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAIZ,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAKtB,WAAW,CAACuB,OAAO;QACtB,oBAAOzC,OAAA,CAACnB,GAAG;UAAC+D,KAAK,EAAC,YAAY;UAACS,IAAI,eAAErD,OAAA,CAACX,YAAY;YAACqD,IAAI;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK9B,WAAW,CAAC+B,SAAS;QACxB,oBAAOjD,OAAA,CAACnB,GAAG;UAAC+D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAErD,OAAA,CAACV,mBAAmB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK9B,WAAW,CAACgC,MAAM;QACrB,oBAAOlD,OAAA,CAACnB,GAAG;UAAC+D,KAAK,EAAC,OAAO;UAACS,IAAI,eAAErD,OAAA,CAACT,mBAAmB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK9B,WAAW,CAACiC,SAAS;QACxB,oBAAOnD,OAAA,CAACnB,GAAG;UAAC+D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAErD,OAAA,CAACR,YAAY;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAOhD,OAAA,CAACnB,GAAG;UAAC+D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAErD,OAAA,CAACP,mBAAmB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMO,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;EAED,oBACE1D,OAAA;IAAK2C,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAO,CAAE;IAAAL,QAAA,gBAC9BtD,OAAA;MAAK2C,KAAK,EAAE;QAAEiB,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAT,QAAA,gBAC3GtD,OAAA;QAAAsD,QAAA,gBACEtD,OAAA,CAACG,KAAK;UAAC6D,KAAK,EAAE,CAAE;UAAAV,QAAA,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7BhD,OAAA,CAACI,IAAI;UAAC6D,IAAI,EAAC,WAAW;UAAAX,QAAA,EAAC;QAEvB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhD,OAAA,CAACtB,MAAM;QACLuF,IAAI,EAAC,SAAS;QACdZ,IAAI,eAAErD,OAAA,CAACJ,cAAc;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBkB,OAAO,EAAEtC,aAAc;QACvBhB,OAAO,EAAEW,UAAW;QAAA+B,QAAA,EACrB;MAED;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhD,OAAA,CAAC3B,IAAI;MAACsE,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,eACpCtD,OAAA,CAAC1B,GAAG;QAAC6F,MAAM,EAAE,EAAG;QAAAb,QAAA,gBACdtD,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXtD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE5D,YAAY,CAAC6D,MAAO;YAC3BC,MAAM,eAAExE,OAAA,CAACX,YAAY;cAACqD,IAAI;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9ByB,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXtD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE3D,cAAc,CAAC+D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAKtB,WAAW,CAAC+B,SAAS,CAAC,CAACsB,MAAO;YAC7EC,MAAM,eAAExE,OAAA,CAACV,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCyB,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXtD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE3D,cAAc,CAAC+D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnC,MAAM,KAAKtB,WAAW,CAACgC,MAAM,CAAC,CAACqB,MAAO;YAC1EC,MAAM,eAAExE,OAAA,CAACT,mBAAmB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCyB,UAAU,EAAE;cAAE7B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXtD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE5D,YAAY,CAAC6D,MAAM,GAAG5D,cAAc,CAAC4D,MAAO;YACnDC,MAAM,eAAExE,OAAA,CAACH,kBAAkB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPhD,OAAA,CAAC1B,GAAG;MAAC6F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAb,QAAA,gBAEpBtD,OAAA,CAACzB,GAAG;QAAC6F,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZtD,OAAA,CAAC3B,IAAI;UACHgG,KAAK,eACHrE,OAAA,CAACrB,KAAK;YAAA2E,QAAA,gBACJtD,OAAA,CAACX,YAAY;cAACqD,IAAI;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAErB,eAAAhD,OAAA,CAACnB,GAAG;cAAC+D,KAAK,EAAC,YAAY;cAAAU,QAAA,EAAE5C,YAAY,CAAC6D;YAAM;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACD4B,KAAK,eACH5E,OAAA,CAACb,OAAO;YAACkF,KAAK,EAAC,gCAAO;YAAAf,QAAA,eACpBtD,OAAA,CAACX,YAAY;cAACqD,IAAI;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACV;UAAAM,QAAA,eAEDtD,OAAA,CAACd,IAAI;YAAC2F,QAAQ,EAAEjE,OAAQ;YAAA0C,QAAA,eACtBtD,OAAA,CAACvB,IAAI;cACHqG,UAAU,EAAEpE,YAAa;cACzBqE,MAAM,EAAE;gBAAEC,SAAS,eAAEhF,OAAA,CAAChB,KAAK;kBAACiG,WAAW,EAAC;gBAAU;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxDkC,UAAU,EAAGhD,IAAI,iBACflC,OAAA,CAACvB,IAAI,CAAC0G,IAAI;gBACRC,OAAO,EAAE,cACPpF,OAAA,CAACtB,MAAM;kBACLuF,IAAI,EAAC,MAAM;kBACXZ,IAAI,eAAErD,OAAA,CAACN,WAAW;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBkB,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAACC,IAAI,CAAE;kBAAAoB,QAAA,EAC3C;gBAED;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThD,OAAA,CAACZ,UAAU;kBACTiF,KAAK,EAAC,oEAAa;kBACnBgB,SAAS,EAAEA,CAAA,KAAMlD,gBAAgB,CAACD,IAAI,CAACoD,OAAO,CAAE;kBAChDC,MAAM,EAAC,cAAI;kBACXC,UAAU,EAAC,cAAI;kBAAAlC,QAAA,eAEftD,OAAA,CAACtB,MAAM;oBACLuF,IAAI,EAAC,MAAM;oBACXwB,MAAM;oBACNpC,IAAI,eAAErD,OAAA,CAACR,YAAY;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAM,QAAA,EACxB;kBAED;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAM,QAAA,eAEFtD,OAAA,CAACvB,IAAI,CAAC0G,IAAI,CAACO,IAAI;kBACbC,MAAM,EAAEpD,iBAAiB,CAACL,IAAI,CAACM,MAAM,CAAE;kBACvC6B,KAAK,eACHrE,OAAA,CAACrB,KAAK;oBAAA2E,QAAA,gBACJtD,OAAA,CAACI,IAAI;sBAACwF,MAAM;sBAAAtC,QAAA,EAAErC,cAAc,CAACiB,IAAI,CAAC2D,SAAS;oBAAC;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDI,gBAAgB,CAAClB,IAAI,CAACM,MAAM,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDiC,WAAW,eACTjF,OAAA;oBAAAsD,QAAA,gBACEtD,OAAA,CAACI,IAAI;sBAAC6D,IAAI,EAAC,WAAW;sBAAC6B,QAAQ,EAAE;wBAAEC,IAAI,EAAE7D,IAAI,CAACoD;sBAAQ,CAAE;sBAAAhC,QAAA,GAAC,MACnD,EAACpB,IAAI,CAACoD,OAAO,CAACU,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG9D,IAAI,CAACoD,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM/D,IAAI,CAACoD,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGjE,IAAI,CAACoD,OAAO,CAACa,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;oBAAA;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAElC,CAAC,eACPhD,OAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;sBAAC6D,IAAI,EAAC,WAAW;sBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAACrB,IAAI,CAACkE,UAAU,CAAC;oBAAA;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEd,IAAI,CAACjD,OAAO,iBACXe,OAAA,CAAAE,SAAA;sBAAAoD,QAAA,gBACEtD,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;wBAAC6D,IAAI,EAAC,WAAW;wBAAAX,QAAA,GAAC,gBAAI,EAACpB,IAAI,CAACjD,OAAO;sBAAA;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAd,IAAI,CAACmE,QAAQ,KAAKC,SAAS,iBAC1BtG,OAAA;sBAAK2C,KAAK,EAAE;wBAAE4D,SAAS,EAAE;sBAAE,CAAE;sBAAAjD,QAAA,eAC3BtD,OAAA,CAAClB,QAAQ;wBAAC0H,OAAO,EAAEtE,IAAI,CAACmE,QAAS;wBAACI,IAAI,EAAC;sBAAO;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNhD,OAAA,CAACzB,GAAG;QAAC6F,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZtD,OAAA,CAAC3B,IAAI;UACHgG,KAAK,eACHrE,OAAA,CAACrB,KAAK;YAAA2E,QAAA,gBACJtD,OAAA,CAACV,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAAhD,OAAA,CAACnB,GAAG;cAAC+D,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAE3C,cAAc,CAAC4D;YAAM;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACD4B,KAAK,eACH5E,OAAA,CAACZ,UAAU;YACTiF,KAAK,EAAC,4FAAiB;YACvBgB,SAAS,EAAE/C,oBAAqB;YAChCiD,MAAM,EAAC,cAAI;YACXC,UAAU,EAAC,cAAI;YAAAlC,QAAA,eAEftD,OAAA,CAACtB,MAAM;cACLuF,IAAI,EAAC,SAAS;cACdwB,MAAM;cACNgB,IAAI,EAAC,OAAO;cACZpD,IAAI,eAAErD,OAAA,CAACL,cAAc;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB0D,QAAQ,EAAE/F,cAAc,CAAC4D,MAAM,KAAK,CAAE;cAAAjB,QAAA,EACvC;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACb;UAAAM,QAAA,eAEDtD,OAAA,CAACvB,IAAI;YACHqG,UAAU,EAAEnE,cAAe;YAC3BoE,MAAM,EAAE;cACNC,SAAS,eACPhF,OAAA,CAAChB,KAAK;gBACJiG,WAAW,eACTjF,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA;oBAAAsD,QAAA,EAAK;kBAAQ;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnBhD,OAAA,CAACI,IAAI;oBAAC6D,IAAI,EAAC,WAAW;oBAACtB,KAAK,EAAE;sBAAEgE,QAAQ,EAAE;oBAAO,CAAE;oBAAArD,QAAA,EAAC;kBAEpD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACFkC,UAAU,EAAGhD,IAAI,iBACflC,OAAA,CAACvB,IAAI,CAAC0G,IAAI;cACRC,OAAO,EAAE,cACPpF,OAAA,CAACtB,MAAM;gBACLuF,IAAI,EAAC,MAAM;gBACXZ,IAAI,eAAErD,OAAA,CAACN,WAAW;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBkB,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAACC,IAAI,CAAE;gBAAAoB,QAAA,EAC3C;cAED;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAM,QAAA,eAEFtD,OAAA,CAACvB,IAAI,CAAC0G,IAAI,CAACO,IAAI;gBACbC,MAAM,EAAEpD,iBAAiB,CAACL,IAAI,CAACM,MAAM,CAAE;gBACvC6B,KAAK,eACHrE,OAAA,CAACrB,KAAK;kBAAA2E,QAAA,gBACJtD,OAAA,CAACI,IAAI;oBAACwF,MAAM;oBAAAtC,QAAA,EAAErC,cAAc,CAACiB,IAAI,CAAC2D,SAAS;kBAAC;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDI,gBAAgB,CAAClB,IAAI,CAACM,MAAM,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACDiC,WAAW,eACTjF,OAAA;kBAAAsD,QAAA,gBACEtD,OAAA,CAACI,IAAI;oBAAC6D,IAAI,EAAC,WAAW;oBAAC6B,QAAQ,EAAE;sBAAEC,IAAI,EAAE7D,IAAI,CAACoD;oBAAQ,CAAE;oBAAAhC,QAAA,GAAC,MACnD,EAACpB,IAAI,CAACoD,OAAO,CAACU,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG9D,IAAI,CAACoD,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM/D,IAAI,CAACoD,OAAO,CAACW,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGjE,IAAI,CAACoD,OAAO,CAACa,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElC,CAAC,eACPhD,OAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;oBAAC6D,IAAI,EAAC,WAAW;oBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAACrB,IAAI,CAAC0E,UAAU,CAAC;kBAAA;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEd,IAAI,CAACM,MAAM,KAAKtB,WAAW,CAACgC,MAAM,IAAIhB,IAAI,CAACF,KAAK,iBAC/ChC,OAAA,CAAAE,SAAA;oBAAAoD,QAAA,gBACEtD,OAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;sBAAC6D,IAAI,EAAC,QAAQ;sBAAAX,QAAA,GAAC,gBAAI,EAACpB,IAAI,CAACF,KAAK;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA,CAACjB,KAAK;MACJsF,KAAK,EAAC,0BAAM;MACZwC,IAAI,EAAExF,iBAAkB;MACxByF,QAAQ,EAAEA,CAAA,KAAMxF,oBAAoB,CAAC,KAAK,CAAE;MAC5CyF,MAAM,EAAE,cACN/G,OAAA,CAACtB,MAAM;QAAawF,OAAO,EAAEA,CAAA,KAAM5C,oBAAoB,CAAC,KAAK,CAAE;QAAAgC,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFgE,KAAK,EAAE,GAAI;MAAA1D,QAAA,EAEVnC,YAAY,iBACXnB,OAAA;QAAAsD,QAAA,eACEtD,OAAA,CAAC1B,GAAG;UAAC6F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACpBtD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAC0F,QAAQ;cAAAxC,QAAA,EAAEnC,YAAY,CAACmE;YAAO;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNhD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAkD,QAAA,EAAErC,cAAc,CAACE,YAAY,CAAC0E,SAAS;YAAC;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNhD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLI,gBAAgB,CAACjC,YAAY,CAACqB,MAAM,CAAC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNhD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL7B,YAAY,CAACkF,QAAQ,KAAKC,SAAS,gBAClCtG,OAAA,CAAClB,QAAQ;cAAC0H,OAAO,EAAErF,YAAY,CAACkF,QAAS;cAACI,IAAI,EAAC;YAAO;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzDhD,OAAA,CAACI,IAAI;cAAC6D,IAAI,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAkD,QAAA,EAAEC,UAAU,CAACpC,YAAY,CAACiF,UAAU;YAAC;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNhD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAkD,QAAA,EAAEC,UAAU,CAACpC,YAAY,CAACyF,UAAU;YAAC;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,EACL7B,YAAY,CAAClC,OAAO,iBACnBe,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAkD,QAAA,EAAEnC,YAAY,CAAClC;YAAO;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA7B,YAAY,CAACa,KAAK,iBACjBhC,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAC6D,IAAI,EAAC,QAAQ;cAAAX,QAAA,EAAEnC,YAAY,CAACa;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA7B,YAAY,CAAC8F,MAAM,iBAClBjH,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZtD,OAAA,CAACI,IAAI;cAACwF,MAAM;cAAAtC,QAAA,EACTnC,YAAY,CAAC0E,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG;YAAU;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACPhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL7B,YAAY,CAAC0E,SAAS,KAAK,UAAU,IAAI1E,YAAY,CAAC8F,MAAM,CAACC,QAAQ,iBACpElH,OAAA;cAAK2C,KAAK,EAAE;gBAAE4D,SAAS,EAAE,CAAC;gBAAE3C,YAAY,EAAE;cAAG,CAAE;cAAAN,QAAA,eAC7CtD,OAAA,CAACrB,KAAK;gBAACwI,IAAI;gBAAA7D,QAAA,gBACTtD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,MAAM;kBAAAU,QAAA,GAAC,sBAAO,EAACnC,YAAY,CAAC8F,MAAM,CAACC,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxEhD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,OAAO;kBAAAU,QAAA,GAAC,4BAAM,EAAC,EAAA/C,qBAAA,GAAAY,YAAY,CAAC8F,MAAM,CAACI,WAAW,cAAA9G,qBAAA,uBAA/BA,qBAAA,CAAkC,CAAC,CAAC,KAAI,KAAK;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EhD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,QAAQ;kBAAAU,QAAA,GAAC,4BAAM,EAAC,EAAA9C,sBAAA,GAAAW,YAAY,CAAC8F,MAAM,CAACK,UAAU,cAAA9G,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,CAAC,KAAI,KAAK;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC7E7B,YAAY,CAAC8F,MAAM,CAACM,wBAAwB,iBAC3CvH,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,KAAK;kBAAAU,QAAA,GAAC,4BAAM,EAACnC,YAAY,CAAC8F,MAAM,CAACM,wBAAwB,CAACH,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACA7B,YAAY,CAAC0E,SAAS,KAAK,YAAY,IAAI1E,YAAY,CAAC8F,MAAM,CAACO,aAAa,KAAKlB,SAAS,iBACzFtG,OAAA;cAAK2C,KAAK,EAAE;gBAAE4D,SAAS,EAAE,CAAC;gBAAE3C,YAAY,EAAE;cAAG,CAAE;cAAAN,QAAA,eAC7CtD,OAAA,CAACrB,KAAK;gBAACwI,IAAI;gBAAA7D,QAAA,gBACTtD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,KAAK;kBAAAU,QAAA,GAAC,4BAAM,EAACnC,YAAY,CAAC8F,MAAM,CAACO,aAAa;gBAAA;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEhD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,MAAM;kBAAAU,QAAA,GAAC,kCAAO,EAAC,EAAA7C,sBAAA,GAAAU,YAAY,CAAC8F,MAAM,CAACQ,WAAW,cAAAhH,sBAAA,uBAA/BA,sBAAA,CAAiC8D,MAAM,KAAI,KAAK;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAChF7B,YAAY,CAAC8F,MAAM,CAACS,mBAAmB,iBACtC1H,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,QAAQ;kBAAAU,QAAA,GAAC,4BAAM,EAACnC,YAAY,CAAC8F,MAAM,CAACS,mBAAmB,CAACN,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACpF,eACDhD,OAAA,CAACnB,GAAG;kBAAC+D,KAAK,EAAC,OAAO;kBAAAU,QAAA,GAAC,gBAAI,EAACnC,YAAY,CAAC8F,MAAM,CAACU,UAAU,IAAI,KAAK;gBAAA;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eACDhD,OAAA;cAAK2C,KAAK,EAAE;gBACViF,UAAU,EAAE,SAAS;gBACrBjE,OAAO,EAAE,EAAE;gBACXkE,YAAY,EAAE,CAAC;gBACflB,QAAQ,EAAE,EAAE;gBACZmB,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE;cACZ,CAAE;cAAAzE,QAAA,eACAtD,OAAA;gBAAAsD,QAAA,EAAM0E,IAAI,CAACC,SAAS,CAAC9G,YAAY,CAAC8F,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAxcID,eAAyB;EAAA,QAWzBP,cAAc;AAAA;AAAAoI,EAAA,GAXd7H,eAAyB;AA0c/B,eAAeA,eAAe;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}