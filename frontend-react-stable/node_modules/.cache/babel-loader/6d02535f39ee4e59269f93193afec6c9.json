{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport get from \"rc-util/es/utils/get\";\nimport set from \"rc-util/es/utils/set\";\nimport { toArray } from './typeUtil';\nimport cloneDeep from '../utils/cloneDeep';\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function getValue(store, namePath) {\n  var value = get(store, namePath);\n  return value;\n}\nexport function setValue(store, namePath, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var newStore = set(store, namePath, value, removeIfUndefined);\n  return newStore;\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\nexport function containsNamePath(namePathList, namePath) {\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(path, namePath);\n  });\n}\nfunction isObject(obj) {\n  return _typeof(obj) === 'object' && obj !== null && Object.getPrototypeOf(obj) === Object.prototype;\n}\n/**\n * Copy values into store and return a new values object\n * ({ a: 1, b: { c: 2 } }, { a: 4, b: { d: 5 } }) => { a: 4, b: { c: 2, d: 5 } }\n */\nfunction internalSetValues(store, values) {\n  var newStore = Array.isArray(store) ? _toConsumableArray(store) : _objectSpread({}, store);\n  if (!values) {\n    return newStore;\n  }\n  Object.keys(values).forEach(function (key) {\n    var prevValue = newStore[key];\n    var value = values[key];\n    // If both are object (but target is not array), we use recursion to set deep value\n    var recursive = isObject(prevValue) && isObject(value);\n    newStore[key] = recursive ? internalSetValues(prevValue, value || {}) : cloneDeep(value); // Clone deep for arrays\n  });\n  return newStore;\n}\nexport function setValues(store) {\n  for (var _len = arguments.length, restValues = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    restValues[_key - 1] = arguments[_key];\n  }\n  return restValues.reduce(function (current, newStore) {\n    return internalSetValues(current, newStore);\n  }, store);\n}\nexport function matchNamePath(namePath, changedNamePath) {\n  if (!namePath || !changedNamePath || namePath.length !== changedNamePath.length) {\n    return false;\n  }\n  return namePath.every(function (nameUnit, i) {\n    return changedNamePath[i] === nameUnit;\n  });\n}\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "_typeof", "get", "set", "toArray", "cloneDeep", "getNamePath", "path", "getValue", "store", "namePath", "value", "setValue", "removeIfUndefined", "arguments", "length", "undefined", "newStore", "cloneByNamePathList", "namePathList", "for<PERSON>ach", "containsNamePath", "some", "matchNamePath", "isObject", "obj", "Object", "getPrototypeOf", "prototype", "internalSetValues", "values", "Array", "isArray", "keys", "key", "prevValue", "recursive", "set<PERSON><PERSON><PERSON>", "_len", "restValues", "_key", "reduce", "current", "changedNamePath", "every", "nameUnit", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source", "target", "sourceKeys", "targetKeys", "Set", "concat", "sourceValue", "targetValue", "defaultGetValueFromEvent", "valuePropName", "event", "move", "array", "moveIndex", "toIndex", "item", "diff", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/utils/valueUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport get from \"rc-util/es/utils/get\";\nimport set from \"rc-util/es/utils/set\";\nimport { toArray } from './typeUtil';\nimport cloneDeep from '../utils/cloneDeep';\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function getValue(store, namePath) {\n  var value = get(store, namePath);\n  return value;\n}\nexport function setValue(store, namePath, value) {\n  var removeIfUndefined = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var newStore = set(store, namePath, value, removeIfUndefined);\n  return newStore;\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\nexport function containsNamePath(namePathList, namePath) {\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(path, namePath);\n  });\n}\nfunction isObject(obj) {\n  return _typeof(obj) === 'object' && obj !== null && Object.getPrototypeOf(obj) === Object.prototype;\n}\n/**\n * Copy values into store and return a new values object\n * ({ a: 1, b: { c: 2 } }, { a: 4, b: { d: 5 } }) => { a: 4, b: { c: 2, d: 5 } }\n */\nfunction internalSetValues(store, values) {\n  var newStore = Array.isArray(store) ? _toConsumableArray(store) : _objectSpread({}, store);\n  if (!values) {\n    return newStore;\n  }\n  Object.keys(values).forEach(function (key) {\n    var prevValue = newStore[key];\n    var value = values[key];\n    // If both are object (but target is not array), we use recursion to set deep value\n    var recursive = isObject(prevValue) && isObject(value);\n    newStore[key] = recursive ? internalSetValues(prevValue, value || {}) : cloneDeep(value); // Clone deep for arrays\n  });\n\n  return newStore;\n}\nexport function setValues(store) {\n  for (var _len = arguments.length, restValues = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    restValues[_key - 1] = arguments[_key];\n  }\n  return restValues.reduce(function (current, newStore) {\n    return internalSetValues(current, newStore);\n  }, store);\n}\nexport function matchNamePath(namePath, changedNamePath) {\n  if (!namePath || !changedNamePath || namePath.length !== changedNamePath.length) {\n    return false;\n  }\n  return namePath.every(function (nameUnit, i) {\n    return changedNamePath[i] === nameUnit;\n  });\n}\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,GAAG,MAAM,sBAAsB;AACtC,OAAOC,GAAG,MAAM,sBAAsB;AACtC,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOH,OAAO,CAACG,IAAI,CAAC;AACtB;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACxC,IAAIC,KAAK,GAAGT,GAAG,CAACO,KAAK,EAAEC,QAAQ,CAAC;EAChC,OAAOC,KAAK;AACd;AACA,OAAO,SAASC,QAAQA,CAACH,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIE,iBAAiB,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACjG,IAAIG,QAAQ,GAAGd,GAAG,CAACM,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEE,iBAAiB,CAAC;EAC7D,OAAOI,QAAQ;AACjB;AACA,OAAO,SAASC,mBAAmBA,CAACT,KAAK,EAAEU,YAAY,EAAE;EACvD,IAAIF,QAAQ,GAAG,CAAC,CAAC;EACjBE,YAAY,CAACC,OAAO,CAAC,UAAUV,QAAQ,EAAE;IACvC,IAAIC,KAAK,GAAGH,QAAQ,CAACC,KAAK,EAAEC,QAAQ,CAAC;IACrCO,QAAQ,GAAGL,QAAQ,CAACK,QAAQ,EAAEP,QAAQ,EAAEC,KAAK,CAAC;EAChD,CAAC,CAAC;EACF,OAAOM,QAAQ;AACjB;AACA,OAAO,SAASI,gBAAgBA,CAACF,YAAY,EAAET,QAAQ,EAAE;EACvD,OAAOS,YAAY,IAAIA,YAAY,CAACG,IAAI,CAAC,UAAUf,IAAI,EAAE;IACvD,OAAOgB,aAAa,CAAChB,IAAI,EAAEG,QAAQ,CAAC;EACtC,CAAC,CAAC;AACJ;AACA,SAASc,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOxB,OAAO,CAACwB,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIC,MAAM,CAACC,cAAc,CAACF,GAAG,CAAC,KAAKC,MAAM,CAACE,SAAS;AACrG;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACpB,KAAK,EAAEqB,MAAM,EAAE;EACxC,IAAIb,QAAQ,GAAGc,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC,GAAGT,kBAAkB,CAACS,KAAK,CAAC,GAAGV,aAAa,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC;EAC1F,IAAI,CAACqB,MAAM,EAAE;IACX,OAAOb,QAAQ;EACjB;EACAS,MAAM,CAACO,IAAI,CAACH,MAAM,CAAC,CAACV,OAAO,CAAC,UAAUc,GAAG,EAAE;IACzC,IAAIC,SAAS,GAAGlB,QAAQ,CAACiB,GAAG,CAAC;IAC7B,IAAIvB,KAAK,GAAGmB,MAAM,CAACI,GAAG,CAAC;IACvB;IACA,IAAIE,SAAS,GAAGZ,QAAQ,CAACW,SAAS,CAAC,IAAIX,QAAQ,CAACb,KAAK,CAAC;IACtDM,QAAQ,CAACiB,GAAG,CAAC,GAAGE,SAAS,GAAGP,iBAAiB,CAACM,SAAS,EAAExB,KAAK,IAAI,CAAC,CAAC,CAAC,GAAGN,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC;EAC5F,CAAC,CAAC;EAEF,OAAOM,QAAQ;AACjB;AACA,OAAO,SAASoB,SAASA,CAAC5B,KAAK,EAAE;EAC/B,KAAK,IAAI6B,IAAI,GAAGxB,SAAS,CAACC,MAAM,EAAEwB,UAAU,GAAG,IAAIR,KAAK,CAACO,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAChHD,UAAU,CAACC,IAAI,GAAG,CAAC,CAAC,GAAG1B,SAAS,CAAC0B,IAAI,CAAC;EACxC;EACA,OAAOD,UAAU,CAACE,MAAM,CAAC,UAAUC,OAAO,EAAEzB,QAAQ,EAAE;IACpD,OAAOY,iBAAiB,CAACa,OAAO,EAAEzB,QAAQ,CAAC;EAC7C,CAAC,EAAER,KAAK,CAAC;AACX;AACA,OAAO,SAASc,aAAaA,CAACb,QAAQ,EAAEiC,eAAe,EAAE;EACvD,IAAI,CAACjC,QAAQ,IAAI,CAACiC,eAAe,IAAIjC,QAAQ,CAACK,MAAM,KAAK4B,eAAe,CAAC5B,MAAM,EAAE;IAC/E,OAAO,KAAK;EACd;EACA,OAAOL,QAAQ,CAACkC,KAAK,CAAC,UAAUC,QAAQ,EAAEC,CAAC,EAAE;IAC3C,OAAOH,eAAe,CAACG,CAAC,CAAC,KAAKD,QAAQ;EACxC,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,IAAID,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO,IAAI;EACb;EACA,IAAI,CAACD,MAAM,IAAIC,MAAM,IAAID,MAAM,IAAI,CAACC,MAAM,EAAE;IAC1C,OAAO,KAAK;EACd;EACA,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,IAAIhD,OAAO,CAAC+C,MAAM,CAAC,KAAK,QAAQ,IAAI/C,OAAO,CAACgD,MAAM,CAAC,KAAK,QAAQ,EAAE;IACtF,OAAO,KAAK;EACd;EACA,IAAIC,UAAU,GAAGxB,MAAM,CAACO,IAAI,CAACe,MAAM,CAAC;EACpC,IAAIG,UAAU,GAAGzB,MAAM,CAACO,IAAI,CAACgB,MAAM,CAAC;EACpC,IAAIhB,IAAI,GAAG,IAAImB,GAAG,CAAC,EAAE,CAACC,MAAM,CAACH,UAAU,EAAEC,UAAU,CAAC,CAAC;EACrD,OAAOnD,kBAAkB,CAACiC,IAAI,CAAC,CAACW,KAAK,CAAC,UAAUV,GAAG,EAAE;IACnD,IAAIoB,WAAW,GAAGN,MAAM,CAACd,GAAG,CAAC;IAC7B,IAAIqB,WAAW,GAAGN,MAAM,CAACf,GAAG,CAAC;IAC7B,IAAI,OAAOoB,WAAW,KAAK,UAAU,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;MAC1E,OAAO,IAAI;IACb;IACA,OAAOD,WAAW,KAAKC,WAAW;EACpC,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,wBAAwBA,CAACC,aAAa,EAAE;EACtD,IAAIC,KAAK,GAAG5C,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC;EAC5D,IAAI4C,KAAK,IAAIA,KAAK,CAACT,MAAM,IAAIhD,OAAO,CAACyD,KAAK,CAACT,MAAM,CAAC,KAAK,QAAQ,IAAIQ,aAAa,IAAIC,KAAK,CAACT,MAAM,EAAE;IAChG,OAAOS,KAAK,CAACT,MAAM,CAACQ,aAAa,CAAC;EACpC;EACA,OAAOC,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC9C,IAAI/C,MAAM,GAAG6C,KAAK,CAAC7C,MAAM;EACzB,IAAI8C,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAI9C,MAAM,IAAI+C,OAAO,GAAG,CAAC,IAAIA,OAAO,IAAI/C,MAAM,EAAE;IAC5E,OAAO6C,KAAK;EACd;EACA,IAAIG,IAAI,GAAGH,KAAK,CAACC,SAAS,CAAC;EAC3B,IAAIG,IAAI,GAAGH,SAAS,GAAGC,OAAO;EAC9B,IAAIE,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACX,MAAM,CAACrD,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEH,OAAO,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE/D,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAACH,OAAO,EAAED,SAAS,CAAC,CAAC,EAAE7D,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAE9C,MAAM,CAAC,CAAC,CAAC;EACpL;EACA,IAAIiD,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACX,MAAM,CAACrD,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC,EAAE7D,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE/D,kBAAkB,CAAC4D,KAAK,CAACK,KAAK,CAACH,OAAO,GAAG,CAAC,EAAE/C,MAAM,CAAC,CAAC,CAAC;EAC5L;EACA,OAAO6C,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}