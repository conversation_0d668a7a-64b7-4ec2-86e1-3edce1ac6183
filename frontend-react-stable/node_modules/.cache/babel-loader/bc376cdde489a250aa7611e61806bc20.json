{"ast": null, "code": "/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */import{useState,useEffect,useCallback,useRef}from'react';import{message,notification}from'antd';import taskApi,{TASK_STATUS,TASK_TYPE}from'../services/taskApi';export const useTaskManager=()=>{const[tasks,setTasks]=useState([]);const[loading,setLoading]=useState(false);const[runningTasks,setRunningTasks]=useState([]);const[completedTasks,setCompletedTasks]=useState([]);const[initialized,setInitialized]=useState(false);const pollingIntervals=useRef(new Map());// 存储轮询定时器\n/**\n   * 获取所有任务\n   */const fetchAllTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;try{setLoading(true);const response=await taskApi.getAllTasks();if(response.success){setTasks(response.tasks||[]);}}catch(error){console.error('获取任务列表失败:',error);if(showError){message.error('获取任务列表失败');}}finally{setLoading(false);}},[]);/**\n   * 获取正在运行的任务\n   */const fetchRunningTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{const response=await taskApi.getRunningTasks();if(response.success){setRunningTasks(response.tasks||[]);}}catch(error){console.error('获取运行中任务失败:',error);if(showError){message.error('获取运行中任务失败');}}},[]);/**\n   * 获取已完成的任务\n   */const fetchCompletedTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{const response=await taskApi.getCompletedTasks();if(response.success){setCompletedTasks(response.tasks||[]);}}catch(error){console.error('获取已完成任务失败:',error);if(showError){message.error('获取已完成任务失败');}}},[]);/**\n   * 清空已完成的任务\n   */const clearCompletedTasks=useCallback(async()=>{try{const response=await taskApi.clearCompletedTasks();if(response.success){setCompletedTasks([]);message.success('已清空完成的任务');}}catch(error){console.error('清空已完成任务失败:',error);message.error('清空已完成任务失败');}},[]);/**\n   * 启动任务轮询\n   */const startPolling=useCallback((taskId,onProgress)=>{// 如果已经在轮询，先清除\nif(pollingIntervals.current.has(taskId)){clearInterval(pollingIntervals.current.get(taskId));}const interval=setInterval(async()=>{try{const response=await taskApi.getTaskStatus(taskId);const task=response.task;if(!task)return;// 更新任务列表中的对应任务\nsetTasks(prevTasks=>{const index=prevTasks.findIndex(t=>t.task_id===taskId);if(index>=0){const newTasks=[...prevTasks];newTasks[index]=task;return newTasks;}else{return[...prevTasks,task];}});// 调用进度回调\nif(onProgress){onProgress(task);}// 如果任务完成，停止轮询并发送通知\nif([TASK_STATUS.COMPLETED,TASK_STATUS.FAILED,TASK_STATUS.CANCELLED].includes(task.status)){clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);// 发送通知\nif(task.status===TASK_STATUS.COMPLETED){notification.success({message:'任务完成',description:`${taskApi.formatTaskType(task.type)}任务已成功完成`,duration:5});}else if(task.status===TASK_STATUS.FAILED){notification.error({message:'任务失败',description:`${taskApi.formatTaskType(task.type)}任务执行失败: ${task.error||'未知错误'}`,duration:8});}// 更新运行中任务列表\nfetchRunningTasks();}}catch(error){console.error(`轮询任务 ${taskId} 状态失败:`,error);clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);}},2000);pollingIntervals.current.set(taskId,interval);},[fetchRunningTasks]);/**\n   * 停止任务轮询\n   */const stopPolling=useCallback(taskId=>{if(pollingIntervals.current.has(taskId)){clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);}},[]);/**\n   * 初始化任务管理器（延迟初始化）\n   */const initializeTaskManager=useCallback(async()=>{if(initialized)return;try{// 尝试获取运行中任务\nawait fetchRunningTasks(false);// 为已存在的运行中任务启动轮询\nconst response=await taskApi.getRunningTasks();if(response.success&&response.tasks){response.tasks.forEach(task=>{startPolling(task.task_id);});}setInitialized(true);}catch(error){console.warn('初始化任务管理失败:',error);// 静默失败，不影响用户体验\n}},[initialized,fetchRunningTasks,startPolling]);/**\n   * 提交训练任务\n   */const submitTrainingTask=useCallback(async formData=>{try{// 确保任务管理器已初始化\nawait initializeTaskManager();const response=await taskApi.startTrainingAsync(formData);if(response.success){const taskId=response.task_id;if(taskId){message.success('训练任务已启动，可在任务管理中查看进度');// 开始轮询任务状态\nstartPolling(taskId);// 更新运行中任务列表\nfetchRunningTasks(false);return taskId;}}}catch(error){var _error$response,_error$response$data;console.error('启动训练任务失败:',error);message.error('启动训练任务失败: '+(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||error.message));throw error;}},[initializeTaskManager,startPolling,fetchRunningTasks]);/**\n   * 提交预测任务\n   */const submitPredictionTask=useCallback(async formData=>{try{// 确保任务管理器已初始化\nawait initializeTaskManager();const response=await taskApi.startPredictionAsync(formData);if(response.success){const taskId=response.task_id;if(taskId){message.success('预测任务已启动，可在任务管理中查看进度');// 开始轮询任务状态\nstartPolling(taskId);// 更新运行中任务列表\nfetchRunningTasks(false);return taskId;}}}catch(error){var _error$response2,_error$response2$data;console.error('启动预测任务失败:',error);message.error('启动预测任务失败: '+(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message));throw error;}},[initializeTaskManager,startPolling,fetchRunningTasks]);/**\n   * 取消任务\n   */const cancelTask=useCallback(async taskId=>{try{const response=await taskApi.cancelTask(taskId);if(response.success){message.success('任务已取消');// 停止轮询\nstopPolling(taskId);// 刷新任务列表\nfetchAllTasks();fetchRunningTasks();}}catch(error){var _error$response3,_error$response3$data;console.error('取消任务失败:',error);message.error('取消任务失败: '+(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message));}},[stopPolling,fetchAllTasks,fetchRunningTasks]);/**\n   * 获取任务详情\n   */const getTaskDetail=useCallback(async taskId=>{try{const response=await taskApi.getTaskStatus(taskId);return response.task||null;}catch(error){console.error('获取任务详情失败:',error);message.error('获取任务详情失败');return null;}},[]);// 组件挂载时不立即初始化，等待用户操作\nuseEffect(()=>{// 清理函数：清除所有轮询\nreturn()=>{const intervals=pollingIntervals.current;intervals.forEach(interval=>clearInterval(interval));intervals.clear();};},[]);return{// 状态\ntasks,runningTasks,completedTasks,loading,// 方法\nfetchAllTasks,fetchRunningTasks,fetchCompletedTasks,clearCompletedTasks,submitTrainingTask,submitPredictionTask,cancelTask,getTaskDetail,startPolling,stopPolling,// 工具方法\nformatTaskStatus:taskApi.formatTaskStatus,formatTaskType:taskApi.formatTaskType,getTaskStatusColor:taskApi.getTaskStatusColor,calculateTaskDuration:taskApi.calculateTaskDuration,// 常量\nTASK_STATUS,TASK_TYPE};};export default useTaskManager;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "message", "notification", "taskApi", "TASK_STATUS", "TASK_TYPE", "useTaskManager", "tasks", "setTasks", "loading", "setLoading", "runningTasks", "setRunningTasks", "completedTasks", "setCompletedTasks", "initialized", "setInitialized", "pollingIntervals", "Map", "fetchAllTasks", "showError", "arguments", "length", "undefined", "response", "getAllTasks", "success", "error", "console", "fetchRunningTasks", "getRunningTasks", "fetchCompletedTasks", "getCompletedTasks", "clearCompletedTasks", "startPolling", "taskId", "onProgress", "current", "has", "clearInterval", "get", "interval", "setInterval", "getTaskStatus", "task", "prevTasks", "index", "findIndex", "t", "task_id", "newTasks", "COMPLETED", "FAILED", "CANCELLED", "includes", "status", "delete", "description", "formatTaskType", "type", "duration", "set", "stopPolling", "initializeTaskManager", "for<PERSON>ach", "warn", "submitTrainingTask", "formData", "startTrainingAsync", "_error$response", "_error$response$data", "data", "detail", "submitPredictionTask", "startPredictionAsync", "_error$response2", "_error$response2$data", "cancelTask", "_error$response3", "_error$response3$data", "getTaskDetail", "intervals", "clear", "formatTaskStatus", "getTaskStatusColor", "calculateTaskDuration"], "sources": ["/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts"], "sourcesContent": ["/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { message, notification } from 'antd';\nimport taskApi, {\n  TASK_STATUS,\n  TASK_TYPE,\n  Task\n} from '../services/taskApi';\n\nexport const useTaskManager = () => {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [runningTasks, setRunningTasks] = useState<Task[]>([]);\n  const [completedTasks, setCompletedTasks] = useState<Task[]>([]);\n  const [initialized, setInitialized] = useState(false);\n  const pollingIntervals = useRef(new Map<string, NodeJS.Timeout>()); // 存储轮询定时器\n\n  /**\n   * 获取所有任务\n   */\n  const fetchAllTasks = useCallback(async (showError = true) => {\n    try {\n      setLoading(true);\n      const response = await taskApi.getAllTasks();\n      if (response.success) {\n        setTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取任务列表失败:', error);\n      if (showError) {\n        message.error('获取任务列表失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * 获取正在运行的任务\n   */\n  const fetchRunningTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getRunningTasks();\n      if (response.success) {\n        setRunningTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取运行中任务失败:', error);\n      if (showError) {\n        message.error('获取运行中任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 获取已完成的任务\n   */\n  const fetchCompletedTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getCompletedTasks();\n      if (response.success) {\n        setCompletedTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取已完成任务失败:', error);\n      if (showError) {\n        message.error('获取已完成任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 清空已完成的任务\n   */\n  const clearCompletedTasks = useCallback(async () => {\n    try {\n      const response = await taskApi.clearCompletedTasks();\n      if (response.success) {\n        setCompletedTasks([]);\n        message.success('已清空完成的任务');\n      }\n    } catch (error) {\n      console.error('清空已完成任务失败:', error);\n      message.error('清空已完成任务失败');\n    }\n  }, []);\n\n  /**\n   * 启动任务轮询\n   */\n  const startPolling = useCallback((taskId: string, onProgress?: (task: Task) => void) => {\n    // 如果已经在轮询，先清除\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const response = await taskApi.getTaskStatus(taskId);\n        const task = response.task;\n\n        if (!task) return;\n\n        // 更新任务列表中的对应任务\n        setTasks(prevTasks => {\n          const index = prevTasks.findIndex(t => t.task_id === taskId);\n          if (index >= 0) {\n            const newTasks = [...prevTasks];\n            newTasks[index] = task;\n            return newTasks;\n          } else {\n            return [...prevTasks, task];\n          }\n        });\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 如果任务完成，停止轮询并发送通知\n        if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED].includes(task.status)) {\n          clearInterval(pollingIntervals.current.get(taskId)!);\n          pollingIntervals.current.delete(taskId);\n\n          // 发送通知\n          if (task.status === TASK_STATUS.COMPLETED) {\n            notification.success({\n              message: '任务完成',\n              description: `${taskApi.formatTaskType(task.type)}任务已成功完成`,\n              duration: 5,\n            });\n          } else if (task.status === TASK_STATUS.FAILED) {\n            notification.error({\n              message: '任务失败',\n              description: `${taskApi.formatTaskType(task.type)}任务执行失败: ${task.error || '未知错误'}`,\n              duration: 8,\n            });\n          }\n\n          // 更新运行中任务列表\n          fetchRunningTasks();\n        }\n      } catch (error) {\n        console.error(`轮询任务 ${taskId} 状态失败:`, error);\n        clearInterval(pollingIntervals.current.get(taskId)!);\n        pollingIntervals.current.delete(taskId);\n      }\n    }, 2000);\n\n    pollingIntervals.current.set(taskId, interval);\n  }, [fetchRunningTasks]);\n\n  /**\n   * 停止任务轮询\n   */\n  const stopPolling = useCallback((taskId: string) => {\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n      pollingIntervals.current.delete(taskId);\n    }\n  }, []);\n\n  /**\n   * 初始化任务管理器（延迟初始化）\n   */\n  const initializeTaskManager = useCallback(async () => {\n    if (initialized) return;\n\n    try {\n      // 尝试获取运行中任务\n      await fetchRunningTasks(false);\n\n      // 为已存在的运行中任务启动轮询\n      const response = await taskApi.getRunningTasks();\n      if (response.success && response.tasks) {\n        response.tasks.forEach(task => {\n          startPolling(task.task_id);\n        });\n      }\n\n      setInitialized(true);\n    } catch (error) {\n      console.warn('初始化任务管理失败:', error);\n      // 静默失败，不影响用户体验\n    }\n  }, [initialized, fetchRunningTasks, startPolling]);\n\n  /**\n   * 提交训练任务\n   */\n  const submitTrainingTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startTrainingAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('训练任务已启动，可在任务管理中查看进度');\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动训练任务失败:', error);\n      message.error('启动训练任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 提交预测任务\n   */\n  const submitPredictionTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startPredictionAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('预测任务已启动，可在任务管理中查看进度');\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动预测任务失败:', error);\n      message.error('启动预测任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 取消任务\n   */\n  const cancelTask = useCallback(async (taskId: string) => {\n    try {\n      const response = await taskApi.cancelTask(taskId);\n      if (response.success) {\n        message.success('任务已取消');\n        \n        // 停止轮询\n        stopPolling(taskId);\n        \n        // 刷新任务列表\n        fetchAllTasks();\n        fetchRunningTasks();\n      }\n    } catch (error: any) {\n      console.error('取消任务失败:', error);\n      message.error('取消任务失败: ' + (error.response?.data?.detail || error.message));\n    }\n  }, [stopPolling, fetchAllTasks, fetchRunningTasks]);\n\n  /**\n   * 获取任务详情\n   */\n  const getTaskDetail = useCallback(async (taskId: string): Promise<Task | null> => {\n    try {\n      const response = await taskApi.getTaskStatus(taskId);\n      return response.task || null;\n    } catch (error) {\n      console.error('获取任务详情失败:', error);\n      message.error('获取任务详情失败');\n      return null;\n    }\n  }, []);\n\n  // 组件挂载时不立即初始化，等待用户操作\n  useEffect(() => {\n    // 清理函数：清除所有轮询\n    return () => {\n      const intervals = pollingIntervals.current;\n      intervals.forEach(interval => clearInterval(interval));\n      intervals.clear();\n    };\n  }, []);\n\n  return {\n    // 状态\n    tasks,\n    runningTasks,\n    completedTasks,\n    loading,\n\n    // 方法\n    fetchAllTasks,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    clearCompletedTasks,\n    submitTrainingTask,\n    submitPredictionTask,\n    cancelTask,\n    getTaskDetail,\n    startPolling,\n    stopPolling,\n\n    // 工具方法\n    formatTaskStatus: taskApi.formatTaskStatus,\n    formatTaskType: taskApi.formatTaskType,\n    getTaskStatusColor: taskApi.getTaskStatusColor,\n    calculateTaskDuration: taskApi.calculateTaskDuration,\n\n    // 常量\n    TASK_STATUS,\n    TASK_TYPE\n  };\n};\n\nexport default useTaskManager;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CAChE,OAASC,OAAO,CAAEC,YAAY,KAAQ,MAAM,CAC5C,MAAO,CAAAC,OAAO,EACZC,WAAW,CACXC,SAAS,KAEJ,qBAAqB,CAE5B,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGX,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACc,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAoB,gBAAgB,CAAGjB,MAAM,CAAC,GAAI,CAAAkB,GAAG,CAAyB,CAAC,CAAC,CAAE;AAEpE;AACF;AACA,KACE,KAAM,CAAAC,aAAa,CAAGpB,WAAW,CAAC,gBAA4B,IAArB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACvD,GAAI,CACFX,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACsB,WAAW,CAAC,CAAC,CAC5C,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBlB,QAAQ,CAACgB,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CAChC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAmB,iBAAiB,CAAG9B,WAAW,CAAC,gBAA6B,IAAtB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC5D,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC2B,eAAe,CAAC,CAAC,CAChD,GAAIN,QAAQ,CAACE,OAAO,CAAE,CACpBd,eAAe,CAACY,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CACvC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAI,mBAAmB,CAAGhC,WAAW,CAAC,gBAA6B,IAAtB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC9D,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC6B,iBAAiB,CAAC,CAAC,CAClD,GAAIR,QAAQ,CAACE,OAAO,CAAE,CACpBZ,iBAAiB,CAACU,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CACzC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAM,mBAAmB,CAAGlC,WAAW,CAAC,SAAY,CAClD,GAAI,CACF,KAAM,CAAAyB,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC8B,mBAAmB,CAAC,CAAC,CACpD,GAAIT,QAAQ,CAACE,OAAO,CAAE,CACpBZ,iBAAiB,CAAC,EAAE,CAAC,CACrBb,OAAO,CAACyB,OAAO,CAAC,UAAU,CAAC,CAC7B,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAO,YAAY,CAAGnC,WAAW,CAAC,CAACoC,MAAc,CAAEC,UAAiC,GAAK,CACtF;AACA,GAAInB,gBAAgB,CAACoB,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC,CAAE,CACxCI,aAAa,CAACtB,gBAAgB,CAACoB,OAAO,CAACG,GAAG,CAACL,MAAM,CAAE,CAAC,CACtD,CAEA,KAAM,CAAAM,QAAQ,CAAGC,WAAW,CAAC,SAAY,CACvC,GAAI,CACF,KAAM,CAAAlB,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACwC,aAAa,CAACR,MAAM,CAAC,CACpD,KAAM,CAAAS,IAAI,CAAGpB,QAAQ,CAACoB,IAAI,CAE1B,GAAI,CAACA,IAAI,CAAE,OAEX;AACApC,QAAQ,CAACqC,SAAS,EAAI,CACpB,KAAM,CAAAC,KAAK,CAAGD,SAAS,CAACE,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,GAAKd,MAAM,CAAC,CAC5D,GAAIW,KAAK,EAAI,CAAC,CAAE,CACd,KAAM,CAAAI,QAAQ,CAAG,CAAC,GAAGL,SAAS,CAAC,CAC/BK,QAAQ,CAACJ,KAAK,CAAC,CAAGF,IAAI,CACtB,MAAO,CAAAM,QAAQ,CACjB,CAAC,IAAM,CACL,MAAO,CAAC,GAAGL,SAAS,CAAED,IAAI,CAAC,CAC7B,CACF,CAAC,CAAC,CAEF;AACA,GAAIR,UAAU,CAAE,CACdA,UAAU,CAACQ,IAAI,CAAC,CAClB,CAEA;AACA,GAAI,CAACxC,WAAW,CAAC+C,SAAS,CAAE/C,WAAW,CAACgD,MAAM,CAAEhD,WAAW,CAACiD,SAAS,CAAC,CAACC,QAAQ,CAACV,IAAI,CAACW,MAAM,CAAC,CAAE,CAC5FhB,aAAa,CAACtB,gBAAgB,CAACoB,OAAO,CAACG,GAAG,CAACL,MAAM,CAAE,CAAC,CACpDlB,gBAAgB,CAACoB,OAAO,CAACmB,MAAM,CAACrB,MAAM,CAAC,CAEvC;AACA,GAAIS,IAAI,CAACW,MAAM,GAAKnD,WAAW,CAAC+C,SAAS,CAAE,CACzCjD,YAAY,CAACwB,OAAO,CAAC,CACnBzB,OAAO,CAAE,MAAM,CACfwD,WAAW,CAAE,GAAGtD,OAAO,CAACuD,cAAc,CAACd,IAAI,CAACe,IAAI,CAAC,SAAS,CAC1DC,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIhB,IAAI,CAACW,MAAM,GAAKnD,WAAW,CAACgD,MAAM,CAAE,CAC7ClD,YAAY,CAACyB,KAAK,CAAC,CACjB1B,OAAO,CAAE,MAAM,CACfwD,WAAW,CAAE,GAAGtD,OAAO,CAACuD,cAAc,CAACd,IAAI,CAACe,IAAI,CAAC,WAAWf,IAAI,CAACjB,KAAK,EAAI,MAAM,EAAE,CAClFiC,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CAEA;AACA/B,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQQ,MAAM,QAAQ,CAAER,KAAK,CAAC,CAC5CY,aAAa,CAACtB,gBAAgB,CAACoB,OAAO,CAACG,GAAG,CAACL,MAAM,CAAE,CAAC,CACpDlB,gBAAgB,CAACoB,OAAO,CAACmB,MAAM,CAACrB,MAAM,CAAC,CACzC,CACF,CAAC,CAAE,IAAI,CAAC,CAERlB,gBAAgB,CAACoB,OAAO,CAACwB,GAAG,CAAC1B,MAAM,CAAEM,QAAQ,CAAC,CAChD,CAAC,CAAE,CAACZ,iBAAiB,CAAC,CAAC,CAEvB;AACF;AACA,KACE,KAAM,CAAAiC,WAAW,CAAG/D,WAAW,CAAEoC,MAAc,EAAK,CAClD,GAAIlB,gBAAgB,CAACoB,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC,CAAE,CACxCI,aAAa,CAACtB,gBAAgB,CAACoB,OAAO,CAACG,GAAG,CAACL,MAAM,CAAE,CAAC,CACpDlB,gBAAgB,CAACoB,OAAO,CAACmB,MAAM,CAACrB,MAAM,CAAC,CACzC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAA4B,qBAAqB,CAAGhE,WAAW,CAAC,SAAY,CACpD,GAAIgB,WAAW,CAAE,OAEjB,GAAI,CACF;AACA,KAAM,CAAAc,iBAAiB,CAAC,KAAK,CAAC,CAE9B;AACA,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC2B,eAAe,CAAC,CAAC,CAChD,GAAIN,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACjB,KAAK,CAAE,CACtCiB,QAAQ,CAACjB,KAAK,CAACyD,OAAO,CAACpB,IAAI,EAAI,CAC7BV,YAAY,CAACU,IAAI,CAACK,OAAO,CAAC,CAC5B,CAAC,CAAC,CACJ,CAEAjC,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACqC,IAAI,CAAC,YAAY,CAAEtC,KAAK,CAAC,CACjC;AACF,CACF,CAAC,CAAE,CAACZ,WAAW,CAAEc,iBAAiB,CAAEK,YAAY,CAAC,CAAC,CAElD;AACF;AACA,KACE,KAAM,CAAAgC,kBAAkB,CAAGnE,WAAW,CAAC,KAAO,CAAAoE,QAAkB,EAAkC,CAChG,GAAI,CACF;AACA,KAAM,CAAAJ,qBAAqB,CAAC,CAAC,CAE7B,KAAM,CAAAvC,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACiE,kBAAkB,CAACD,QAAQ,CAAC,CAC3D,GAAI3C,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAS,MAAM,CAAGX,QAAQ,CAACyB,OAAO,CAC/B,GAAId,MAAM,CAAE,CACVlC,OAAO,CAACyB,OAAO,CAAC,qBAAqB,CAAC,CAEtC;AACAQ,YAAY,CAACC,MAAM,CAAC,CAEpB;AACAN,iBAAiB,CAAC,KAAK,CAAC,CAExB,MAAO,CAAAM,MAAM,CACf,CACF,CACF,CAAE,MAAOR,KAAU,CAAE,KAAA0C,eAAA,CAAAC,oBAAA,CACnB1C,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAI,EAAA0C,eAAA,CAAA1C,KAAK,CAACH,QAAQ,UAAA6C,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBE,IAAI,UAAAD,oBAAA,iBAApBA,oBAAA,CAAsBE,MAAM,GAAI7C,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,KAAM,CAAA0B,KAAK,CACb,CACF,CAAC,CAAE,CAACoC,qBAAqB,CAAE7B,YAAY,CAAEL,iBAAiB,CAAC,CAAC,CAE5D;AACF;AACA,KACE,KAAM,CAAA4C,oBAAoB,CAAG1E,WAAW,CAAC,KAAO,CAAAoE,QAAkB,EAAkC,CAClG,GAAI,CACF;AACA,KAAM,CAAAJ,qBAAqB,CAAC,CAAC,CAE7B,KAAM,CAAAvC,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACuE,oBAAoB,CAACP,QAAQ,CAAC,CAC7D,GAAI3C,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAS,MAAM,CAAGX,QAAQ,CAACyB,OAAO,CAC/B,GAAId,MAAM,CAAE,CACVlC,OAAO,CAACyB,OAAO,CAAC,qBAAqB,CAAC,CAEtC;AACAQ,YAAY,CAACC,MAAM,CAAC,CAEpB;AACAN,iBAAiB,CAAC,KAAK,CAAC,CAExB,MAAO,CAAAM,MAAM,CACf,CACF,CACF,CAAE,MAAOR,KAAU,CAAE,KAAAgD,gBAAA,CAAAC,qBAAA,CACnBhD,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAI,EAAAgD,gBAAA,CAAAhD,KAAK,CAACH,QAAQ,UAAAmD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBJ,IAAI,UAAAK,qBAAA,iBAApBA,qBAAA,CAAsBJ,MAAM,GAAI7C,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,KAAM,CAAA0B,KAAK,CACb,CACF,CAAC,CAAE,CAACoC,qBAAqB,CAAE7B,YAAY,CAAEL,iBAAiB,CAAC,CAAC,CAE5D;AACF;AACA,KACE,KAAM,CAAAgD,UAAU,CAAG9E,WAAW,CAAC,KAAO,CAAAoC,MAAc,EAAK,CACvD,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC0E,UAAU,CAAC1C,MAAM,CAAC,CACjD,GAAIX,QAAQ,CAACE,OAAO,CAAE,CACpBzB,OAAO,CAACyB,OAAO,CAAC,OAAO,CAAC,CAExB;AACAoC,WAAW,CAAC3B,MAAM,CAAC,CAEnB;AACAhB,aAAa,CAAC,CAAC,CACfU,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAE,MAAOF,KAAU,CAAE,KAAAmD,gBAAA,CAAAC,qBAAA,CACnBnD,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAI,EAAAmD,gBAAA,CAAAnD,KAAK,CAACH,QAAQ,UAAAsD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBP,IAAI,UAAAQ,qBAAA,iBAApBA,qBAAA,CAAsBP,MAAM,GAAI7C,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,CACF,CAAC,CAAE,CAAC6D,WAAW,CAAE3C,aAAa,CAAEU,iBAAiB,CAAC,CAAC,CAEnD;AACF;AACA,KACE,KAAM,CAAAmD,aAAa,CAAGjF,WAAW,CAAC,KAAO,CAAAoC,MAAc,EAA2B,CAChF,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACwC,aAAa,CAACR,MAAM,CAAC,CACpD,MAAO,CAAAX,QAAQ,CAACoB,IAAI,EAAI,IAAI,CAC9B,CAAE,MAAOjB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC,CACzB,MAAO,KAAI,CACb,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7B,SAAS,CAAC,IAAM,CACd;AACA,MAAO,IAAM,CACX,KAAM,CAAAmF,SAAS,CAAGhE,gBAAgB,CAACoB,OAAO,CAC1C4C,SAAS,CAACjB,OAAO,CAACvB,QAAQ,EAAIF,aAAa,CAACE,QAAQ,CAAC,CAAC,CACtDwC,SAAS,CAACC,KAAK,CAAC,CAAC,CACnB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,CACL;AACA3E,KAAK,CACLI,YAAY,CACZE,cAAc,CACdJ,OAAO,CAEP;AACAU,aAAa,CACbU,iBAAiB,CACjBE,mBAAmB,CACnBE,mBAAmB,CACnBiC,kBAAkB,CAClBO,oBAAoB,CACpBI,UAAU,CACVG,aAAa,CACb9C,YAAY,CACZ4B,WAAW,CAEX;AACAqB,gBAAgB,CAAEhF,OAAO,CAACgF,gBAAgB,CAC1CzB,cAAc,CAAEvD,OAAO,CAACuD,cAAc,CACtC0B,kBAAkB,CAAEjF,OAAO,CAACiF,kBAAkB,CAC9CC,qBAAqB,CAAElF,OAAO,CAACkF,qBAAqB,CAEpD;AACAjF,WAAW,CACXC,SACF,CAAC,CACH,CAAC,CAED,cAAe,CAAAC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}