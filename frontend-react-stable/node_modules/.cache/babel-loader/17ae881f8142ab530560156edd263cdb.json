{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = union;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction union() {\n  const set = new _index.InternSet();\n  for (var _len = arguments.length, others = new Array(_len), _key = 0; _key < _len; _key++) {\n    others[_key] = arguments[_key];\n  }\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "union", "_index", "require", "set", "InternSet", "_len", "arguments", "length", "others", "Array", "_key", "other", "o", "add"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/union.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = union;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction union(...others) {\n  const set = new _index.InternSet();\n\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n\n  return set;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,KAAKA,CAAA,EAAY;EACxB,MAAMG,GAAG,GAAG,IAAIF,MAAM,CAACG,SAAS,CAAC,CAAC;EAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADnBC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAGtB,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMI,CAAC,IAAID,KAAK,EAAE;MACrBR,GAAG,CAACU,GAAG,CAACD,CAAC,CAAC;IACZ;EACF;EAEA,OAAOT,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}