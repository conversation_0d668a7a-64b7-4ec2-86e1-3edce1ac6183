{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\", \"children\"];\nimport * as React from 'react';\nimport Icon from './Icon';\nvar customCache = new Set();\nfunction isValidCustomScriptUrl(scriptUrl) {\n  return Boolean(typeof scriptUrl === 'string' && scriptUrl.length && !customCache.has(scriptUrl));\n}\nfunction createScriptUrlElements(scriptUrls) {\n  var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currentScriptUrl = scriptUrls[index];\n  if (isValidCustomScriptUrl(currentScriptUrl)) {\n    var script = document.createElement('script');\n    script.setAttribute('src', currentScriptUrl);\n    script.setAttribute('data-namespace', currentScriptUrl);\n    if (scriptUrls.length > index + 1) {\n      script.onload = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n      script.onerror = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n    }\n    customCache.add(currentScriptUrl);\n    document.body.appendChild(script);\n  }\n}\nexport default function create() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var scriptUrl = options.scriptUrl,\n    _options$extraCommonP = options.extraCommonProps,\n    extraCommonProps = _options$extraCommonP === void 0 ? {} : _options$extraCommonP;\n  /**\n   * DOM API required.\n   * Make sure in browser environment.\n   * The Custom Icon will create a <script/>\n   * that loads SVG symbols and insert the SVG Element into the document body.\n   */\n  if (scriptUrl && typeof document !== 'undefined' && typeof window !== 'undefined' && typeof document.createElement === 'function') {\n    if (Array.isArray(scriptUrl)) {\n      // 因为iconfont资源会把svg插入before，所以前加载相同type会覆盖后加载，为了数组覆盖顺序，倒叙插入\n      createScriptUrlElements(scriptUrl.reverse());\n    } else {\n      createScriptUrlElements([scriptUrl]);\n    }\n  }\n  var Iconfont = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var type = props.type,\n      children = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    // children > type\n    var content = null;\n    if (props.type) {\n      content = /*#__PURE__*/React.createElement(\"use\", {\n        xlinkHref: \"#\".concat(type)\n      });\n    }\n    if (children) {\n      content = children;\n    }\n    return /*#__PURE__*/React.createElement(Icon, _objectSpread(_objectSpread(_objectSpread({}, extraCommonProps), restProps), {}, {\n      ref: ref\n    }), content);\n  });\n  Iconfont.displayName = 'Iconfont';\n  return Iconfont;\n}", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "Icon", "customCache", "Set", "isValidCustomScriptUrl", "scriptUrl", "Boolean", "length", "has", "createScriptUrlElements", "scriptUrls", "index", "arguments", "undefined", "currentScriptUrl", "script", "document", "createElement", "setAttribute", "onload", "onerror", "add", "body", "append<PERSON><PERSON><PERSON>", "create", "options", "_options$extraCommonP", "extraCommonProps", "window", "Array", "isArray", "reverse", "Iconfont", "forwardRef", "props", "ref", "type", "children", "restProps", "content", "xlinkHref", "concat", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/components/IconFont.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\", \"children\"];\nimport * as React from 'react';\nimport Icon from './Icon';\nvar customCache = new Set();\nfunction isValidCustomScriptUrl(scriptUrl) {\n  return Boolean(typeof scriptUrl === 'string' && scriptUrl.length && !customCache.has(scriptUrl));\n}\nfunction createScriptUrlElements(scriptUrls) {\n  var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currentScriptUrl = scriptUrls[index];\n  if (isValidCustomScriptUrl(currentScriptUrl)) {\n    var script = document.createElement('script');\n    script.setAttribute('src', currentScriptUrl);\n    script.setAttribute('data-namespace', currentScriptUrl);\n    if (scriptUrls.length > index + 1) {\n      script.onload = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n      script.onerror = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n    }\n    customCache.add(currentScriptUrl);\n    document.body.appendChild(script);\n  }\n}\nexport default function create() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var scriptUrl = options.scriptUrl,\n    _options$extraCommonP = options.extraCommonProps,\n    extraCommonProps = _options$extraCommonP === void 0 ? {} : _options$extraCommonP;\n  /**\n   * DOM API required.\n   * Make sure in browser environment.\n   * The Custom Icon will create a <script/>\n   * that loads SVG symbols and insert the SVG Element into the document body.\n   */\n  if (scriptUrl && typeof document !== 'undefined' && typeof window !== 'undefined' && typeof document.createElement === 'function') {\n    if (Array.isArray(scriptUrl)) {\n      // 因为iconfont资源会把svg插入before，所以前加载相同type会覆盖后加载，为了数组覆盖顺序，倒叙插入\n      createScriptUrlElements(scriptUrl.reverse());\n    } else {\n      createScriptUrlElements([scriptUrl]);\n    }\n  }\n  var Iconfont = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var type = props.type,\n      children = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    // children > type\n    var content = null;\n    if (props.type) {\n      content = /*#__PURE__*/React.createElement(\"use\", {\n        xlinkHref: \"#\".concat(type)\n      });\n    }\n    if (children) {\n      content = children;\n    }\n    return /*#__PURE__*/React.createElement(Icon, _objectSpread(_objectSpread(_objectSpread({}, extraCommonProps), restProps), {}, {\n      ref: ref\n    }), content);\n  });\n  Iconfont.displayName = 'Iconfont';\n  return Iconfont;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;AACpC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,SAASC,sBAAsBA,CAACC,SAAS,EAAE;EACzC,OAAOC,OAAO,CAAC,OAAOD,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACE,MAAM,IAAI,CAACL,WAAW,CAACM,GAAG,CAACH,SAAS,CAAC,CAAC;AAClG;AACA,SAASI,uBAAuBA,CAACC,UAAU,EAAE;EAC3C,IAAIC,KAAK,GAAGC,SAAS,CAACL,MAAM,GAAG,CAAC,IAAIK,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjF,IAAIE,gBAAgB,GAAGJ,UAAU,CAACC,KAAK,CAAC;EACxC,IAAIP,sBAAsB,CAACU,gBAAgB,CAAC,EAAE;IAC5C,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7CF,MAAM,CAACG,YAAY,CAAC,KAAK,EAAEJ,gBAAgB,CAAC;IAC5CC,MAAM,CAACG,YAAY,CAAC,gBAAgB,EAAEJ,gBAAgB,CAAC;IACvD,IAAIJ,UAAU,CAACH,MAAM,GAAGI,KAAK,GAAG,CAAC,EAAE;MACjCI,MAAM,CAACI,MAAM,GAAG,YAAY;QAC1BV,uBAAuB,CAACC,UAAU,EAAEC,KAAK,GAAG,CAAC,CAAC;MAChD,CAAC;MACDI,MAAM,CAACK,OAAO,GAAG,YAAY;QAC3BX,uBAAuB,CAACC,UAAU,EAAEC,KAAK,GAAG,CAAC,CAAC;MAChD,CAAC;IACH;IACAT,WAAW,CAACmB,GAAG,CAACP,gBAAgB,CAAC;IACjCE,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,MAAM,CAAC;EACnC;AACF;AACA,eAAe,SAASS,MAAMA,CAAA,EAAG;EAC/B,IAAIC,OAAO,GAAGb,SAAS,CAACL,MAAM,GAAG,CAAC,IAAIK,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIP,SAAS,GAAGoB,OAAO,CAACpB,SAAS;IAC/BqB,qBAAqB,GAAGD,OAAO,CAACE,gBAAgB;IAChDA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;EAClF;AACF;AACA;AACA;AACA;AACA;EACE,IAAIrB,SAAS,IAAI,OAAOW,QAAQ,KAAK,WAAW,IAAI,OAAOY,MAAM,KAAK,WAAW,IAAI,OAAOZ,QAAQ,CAACC,aAAa,KAAK,UAAU,EAAE;IACjI,IAAIY,KAAK,CAACC,OAAO,CAACzB,SAAS,CAAC,EAAE;MAC5B;MACAI,uBAAuB,CAACJ,SAAS,CAAC0B,OAAO,CAAC,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLtB,uBAAuB,CAAC,CAACJ,SAAS,CAAC,CAAC;IACtC;EACF;EACA,IAAI2B,QAAQ,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACjE,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;MACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACzBC,SAAS,GAAGxC,wBAAwB,CAACoC,KAAK,EAAEnC,SAAS,CAAC;IACxD;IACA,IAAIwC,OAAO,GAAG,IAAI;IAClB,IAAIL,KAAK,CAACE,IAAI,EAAE;MACdG,OAAO,GAAG,aAAavC,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;QAChDuB,SAAS,EAAE,GAAG,CAACC,MAAM,CAACL,IAAI;MAC5B,CAAC,CAAC;IACJ;IACA,IAAIC,QAAQ,EAAE;MACZE,OAAO,GAAGF,QAAQ;IACpB;IACA,OAAO,aAAarC,KAAK,CAACiB,aAAa,CAAChB,IAAI,EAAEJ,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,gBAAgB,CAAC,EAAEW,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7HH,GAAG,EAAEA;IACP,CAAC,CAAC,EAAEI,OAAO,CAAC;EACd,CAAC,CAAC;EACFP,QAAQ,CAACU,WAAW,GAAG,UAAU;EACjC,OAAOV,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}