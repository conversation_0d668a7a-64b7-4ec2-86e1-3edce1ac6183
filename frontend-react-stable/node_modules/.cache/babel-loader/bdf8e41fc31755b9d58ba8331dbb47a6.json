{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = defaultLocale;\nexports.formatPrefix = exports.format = void 0;\nvar _locale = _interopRequireDefault(require(\"./locale.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar locale;\nvar format;\nexports.format = format;\nvar formatPrefix;\nexports.formatPrefix = formatPrefix;\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\nfunction defaultLocale(definition) {\n  locale = (0, _locale.default)(definition);\n  exports.format = format = locale.format;\n  exports.formatPrefix = formatPrefix = locale.formatPrefix;\n  return locale;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "defaultLocale", "formatPrefix", "format", "_locale", "_interopRequireDefault", "require", "obj", "__esModule", "locale", "thousands", "grouping", "currency", "definition"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/defaultLocale.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = defaultLocale;\nexports.formatPrefix = exports.format = void 0;\n\nvar _locale = _interopRequireDefault(require(\"./locale.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar locale;\nvar format;\nexports.format = format;\nvar formatPrefix;\nexports.formatPrefix = formatPrefix;\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nfunction defaultLocale(definition) {\n  locale = (0, _locale.default)(definition);\n  exports.format = format = locale.format;\n  exports.formatPrefix = formatPrefix = locale.formatPrefix;\n  return locale;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,aAAa;AAC/BH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAG,KAAK,CAAC;AAE9C,IAAIC,OAAO,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEP,OAAO,EAAEO;EAAI,CAAC;AAAE;AAE9F,IAAIE,MAAM;AACV,IAAIN,MAAM;AACVL,OAAO,CAACK,MAAM,GAAGA,MAAM;AACvB,IAAID,YAAY;AAChBJ,OAAO,CAACI,YAAY,GAAGA,YAAY;AACnCD,aAAa,CAAC;EACZS,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,CAAC,CAAC,CAAC;EACbC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;AACpB,CAAC,CAAC;AAEF,SAASX,aAAaA,CAACY,UAAU,EAAE;EACjCJ,MAAM,GAAG,CAAC,CAAC,EAAEL,OAAO,CAACJ,OAAO,EAAEa,UAAU,CAAC;EACzCf,OAAO,CAACK,MAAM,GAAGA,MAAM,GAAGM,MAAM,CAACN,MAAM;EACvCL,OAAO,CAACI,YAAY,GAAGA,YAAY,GAAGO,MAAM,CAACP,YAAY;EACzD,OAAOO,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script"}