{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useForceUpdate from '../_util/hooks/useForceUpdate';\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCountdown } from './utils';\nvar REFRESH_INTERVAL = 1000 / 30;\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nvar Countdown = function Countdown(props) {\n  var value = props.value,\n    _props$format = props.format,\n    format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,\n    onChange = props.onChange,\n    onFinish = props.onFinish;\n  var forceUpdate = useForceUpdate();\n  var countdown = React.useRef(null);\n  var stopTimer = function stopTimer() {\n    onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n    if (countdown.current) {\n      clearInterval(countdown.current);\n      countdown.current = null;\n    }\n  };\n  var syncTimer = function syncTimer() {\n    var timestamp = getTime(value);\n    if (timestamp >= Date.now()) {\n      countdown.current = setInterval(function () {\n        forceUpdate();\n        onChange === null || onChange === void 0 ? void 0 : onChange(timestamp - Date.now());\n        if (timestamp < Date.now()) {\n          stopTimer();\n        }\n      }, REFRESH_INTERVAL);\n    }\n  };\n  React.useEffect(function () {\n    syncTimer();\n    return function () {\n      if (countdown.current) {\n        clearInterval(countdown.current);\n        countdown.current = null;\n      }\n    };\n  }, [value]);\n  var formatter = function formatter(formatValue, config) {\n    return formatCountdown(formatValue, _extends(_extends({}, config), {\n      format: format\n    }));\n  };\n  var valueRender = function valueRender(node) {\n    return cloneElement(node, {\n      title: undefined\n    });\n  };\n  return /*#__PURE__*/React.createElement(Statistic, _extends({}, props, {\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);", "map": {"version": 3, "names": ["_extends", "React", "useForceUpdate", "cloneElement", "Statistic", "formatCountdown", "REFRESH_INTERVAL", "getTime", "value", "Date", "Countdown", "props", "_props$format", "format", "onChange", "onFinish", "forceUpdate", "countdown", "useRef", "stopTimer", "current", "clearInterval", "syncTimer", "timestamp", "now", "setInterval", "useEffect", "formatter", "formatValue", "config", "valueRender", "node", "title", "undefined", "createElement", "memo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/statistic/Countdown.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useForceUpdate from '../_util/hooks/useForceUpdate';\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCountdown } from './utils';\nvar REFRESH_INTERVAL = 1000 / 30;\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nvar Countdown = function Countdown(props) {\n  var value = props.value,\n    _props$format = props.format,\n    format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,\n    onChange = props.onChange,\n    onFinish = props.onFinish;\n  var forceUpdate = useForceUpdate();\n  var countdown = React.useRef(null);\n  var stopTimer = function stopTimer() {\n    onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n    if (countdown.current) {\n      clearInterval(countdown.current);\n      countdown.current = null;\n    }\n  };\n  var syncTimer = function syncTimer() {\n    var timestamp = getTime(value);\n    if (timestamp >= Date.now()) {\n      countdown.current = setInterval(function () {\n        forceUpdate();\n        onChange === null || onChange === void 0 ? void 0 : onChange(timestamp - Date.now());\n        if (timestamp < Date.now()) {\n          stopTimer();\n        }\n      }, REFRESH_INTERVAL);\n    }\n  };\n  React.useEffect(function () {\n    syncTimer();\n    return function () {\n      if (countdown.current) {\n        clearInterval(countdown.current);\n        countdown.current = null;\n      }\n    };\n  }, [value]);\n  var formatter = function formatter(formatValue, config) {\n    return formatCountdown(formatValue, _extends(_extends({}, config), {\n      format: format\n    }));\n  };\n  var valueRender = function valueRender(node) {\n    return cloneElement(node, {\n      title: undefined\n    });\n  };\n  return /*#__PURE__*/React.createElement(Statistic, _extends({}, props, {\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,eAAe,QAAQ,SAAS;AACzC,IAAIC,gBAAgB,GAAG,IAAI,GAAG,EAAE;AAChC,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,OAAO,IAAIC,IAAI,CAACD,KAAK,CAAC,CAACD,OAAO,CAAC,CAAC;AAClC;AACA,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;EACxC,IAAIH,KAAK,GAAGG,KAAK,CAACH,KAAK;IACrBI,aAAa,GAAGD,KAAK,CAACE,MAAM;IAC5BA,MAAM,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,aAAa;IAC9DE,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC3B,IAAIC,WAAW,GAAGd,cAAc,CAAC,CAAC;EAClC,IAAIe,SAAS,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAClC,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;IAC9D,IAAIE,SAAS,CAACG,OAAO,EAAE;MACrBC,aAAa,CAACJ,SAAS,CAACG,OAAO,CAAC;MAChCH,SAAS,CAACG,OAAO,GAAG,IAAI;IAC1B;EACF,CAAC;EACD,IAAIE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,SAAS,GAAGhB,OAAO,CAACC,KAAK,CAAC;IAC9B,IAAIe,SAAS,IAAId,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE;MAC3BP,SAAS,CAACG,OAAO,GAAGK,WAAW,CAAC,YAAY;QAC1CT,WAAW,CAAC,CAAC;QACbF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACS,SAAS,GAAGd,IAAI,CAACe,GAAG,CAAC,CAAC,CAAC;QACpF,IAAID,SAAS,GAAGd,IAAI,CAACe,GAAG,CAAC,CAAC,EAAE;UAC1BL,SAAS,CAAC,CAAC;QACb;MACF,CAAC,EAAEb,gBAAgB,CAAC;IACtB;EACF,CAAC;EACDL,KAAK,CAACyB,SAAS,CAAC,YAAY;IAC1BJ,SAAS,CAAC,CAAC;IACX,OAAO,YAAY;MACjB,IAAIL,SAAS,CAACG,OAAO,EAAE;QACrBC,aAAa,CAACJ,SAAS,CAACG,OAAO,CAAC;QAChCH,SAAS,CAACG,OAAO,GAAG,IAAI;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAACZ,KAAK,CAAC,CAAC;EACX,IAAImB,SAAS,GAAG,SAASA,SAASA,CAACC,WAAW,EAAEC,MAAM,EAAE;IACtD,OAAOxB,eAAe,CAACuB,WAAW,EAAE5B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6B,MAAM,CAAC,EAAE;MACjEhB,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAIiB,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;IAC3C,OAAO5B,YAAY,CAAC4B,IAAI,EAAE;MACxBC,KAAK,EAAEC;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAO,aAAahC,KAAK,CAACiC,aAAa,CAAC9B,SAAS,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACrEmB,WAAW,EAAEA,WAAW;IACxBH,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAa1B,KAAK,CAACkC,IAAI,CAACzB,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}