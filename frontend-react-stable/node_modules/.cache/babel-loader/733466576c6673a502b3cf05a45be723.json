{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdScott;\nvar _count = _interopRequireDefault(require(\"../count.js\"));\nvar _deviation = _interopRequireDefault(require(\"../deviation.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction thresholdScott(values, min, max) {\n  return Math.ceil((max - min) * Math.cbrt((0, _count.default)(values)) / (3.49 * (0, _deviation.default)(values)));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "<PERSON><PERSON><PERSON>", "_count", "_interopRequireDefault", "require", "_deviation", "obj", "__esModule", "values", "min", "max", "Math", "ceil", "cbrt"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/threshold/scott.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdScott;\n\nvar _count = _interopRequireDefault(require(\"../count.js\"));\n\nvar _deviation = _interopRequireDefault(require(\"../deviation.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction thresholdScott(values, min, max) {\n  return Math.ceil((max - min) * Math.cbrt((0, _count.default)(values)) / (3.49 * (0, _deviation.default)(values)));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,cAAc;AAEhC,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE3D,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEnE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,cAAcA,CAACO,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACxC,OAAOC,IAAI,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGD,GAAG,IAAIE,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEX,MAAM,CAACF,OAAO,EAAEQ,MAAM,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAEH,UAAU,CAACL,OAAO,EAAEQ,MAAM,CAAC,CAAC,CAAC;AACnH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}