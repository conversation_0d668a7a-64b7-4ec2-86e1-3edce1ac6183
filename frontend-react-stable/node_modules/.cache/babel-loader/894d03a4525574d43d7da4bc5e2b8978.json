{"ast": null, "code": "import React from'react';import{Routes,Route,Navigate}from'react-router-dom';import{Layout}from'antd';import{useSelector}from'react-redux';import LoginPage from'./pages/LoginPage';import MainLayout from'./components/Layout/MainLayout';import DataCleaningPage from'./pages/DataCleaningPage';import ModelTrainingPage from'./pages/ModelTrainingPage';import BatchTrainingPage from'./pages/BatchTrainingPage';import ModelPredictionPage from'./pages/ModelPredictionPage';import ModelRegistryPage from'./pages/ModelRegistryPage';import CleanTemplatePage from'./pages/CleanTemplatePage';import DataQueryPage from'./pages/DataQueryPage';import UserManagementPage from'./pages/UserManagementPage';import TaskManagerPage from'./pages/TaskManagerPage';import'./App.css';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const{Content}=Layout;// 路由守卫组件\nconst ProtectedRoute=_ref=>{let{children}=_ref;const isAuthenticated=useSelector(state=>state.auth.isAuthenticated);if(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};const App=()=>{const isAuthenticated=useSelector(state=>state.auth.isAuthenticated);return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true}):/*#__PURE__*/_jsx(LoginPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/*\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(MainLayout,{children:/*#__PURE__*/_jsx(Content,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/data-cleaning\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/data-cleaning\",element:/*#__PURE__*/_jsx(DataCleaningPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/model-training\",element:/*#__PURE__*/_jsx(ModelTrainingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/batch-training\",element:/*#__PURE__*/_jsx(BatchTrainingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/model-prediction\",element:/*#__PURE__*/_jsx(ModelPredictionPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/model-registry\",element:/*#__PURE__*/_jsx(ModelRegistryPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/clean-template\",element:/*#__PURE__*/_jsx(CleanTemplatePage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/data-query\",element:/*#__PURE__*/_jsx(DataQueryPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/user-management\",element:/*#__PURE__*/_jsx(UserManagementPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/task-manager\",element:/*#__PURE__*/_jsx(TaskManagerPage,{})})]})})})})})]})});};export default App;", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Layout", "useSelector", "LoginPage", "MainLayout", "DataCleaningPage", "ModelTrainingPage", "BatchTrainingPage", "ModelPredictionPage", "ModelRegistryPage", "CleanTemplatePage", "DataQueryPage", "UserManagementPage", "TaskManagerPage", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Content", "ProtectedRoute", "_ref", "children", "isAuthenticated", "state", "auth", "to", "replace", "App", "className", "path", "element"], "sources": ["/home/<USER>/frontend-react-stable/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store/store';\nimport LoginPage from './pages/LoginPage';\nimport MainLayout from './components/Layout/MainLayout';\nimport DataCleaningPage from './pages/DataCleaningPage';\nimport ModelTrainingPage from './pages/ModelTrainingPage';\nimport BatchTrainingPage from './pages/BatchTrainingPage';\nimport ModelPredictionPage from './pages/ModelPredictionPage';\nimport ModelRegistryPage from './pages/ModelRegistryPage';\nimport CleanTemplatePage from './pages/CleanTemplatePage';\nimport DataQueryPage from './pages/DataQueryPage';\nimport UserManagementPage from './pages/UserManagementPage';\nimport TaskManagerPage from './pages/TaskManagerPage';\nimport './App.css';\n\nconst { Content } = Layout;\n\n// 路由守卫组件\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n  \n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  \n  return <>{children}</>;\n};\n\nconst App: React.FC = () => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={\n            isAuthenticated ? <Navigate to=\"/\" replace /> : <LoginPage />\n          } \n        />\n        <Route\n          path=\"/*\"\n          element={\n            <ProtectedRoute>\n              <MainLayout>\n                <Content>\n                  <Routes>\n                    <Route path=\"/\" element={<Navigate to=\"/data-cleaning\" replace />} />\n                    <Route path=\"/data-cleaning\" element={<DataCleaningPage />} />\n                    <Route path=\"/model-training\" element={<ModelTrainingPage />} />\n                    <Route path=\"/batch-training\" element={<BatchTrainingPage />} />\n                    <Route path=\"/model-prediction\" element={<ModelPredictionPage />} />\n                    <Route path=\"/model-registry\" element={<ModelRegistryPage />} />\n                    <Route path=\"/clean-template\" element={<CleanTemplatePage />} />\n                    <Route path=\"/data-query\" element={<DataQueryPage />} />\n                    <Route path=\"/user-management\" element={<UserManagementPage />} />\n                    <Route path=\"/task-manager\" element={<TaskManagerPage />} />\n                  </Routes>\n                </Content>\n              </MainLayout>\n            </ProtectedRoute>\n          }\n        />\n      </Routes>\n    </div>\n  );\n};\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,MAAM,KAAQ,MAAM,CAC7B,OAASC,WAAW,KAAQ,aAAa,CAEzC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CACvD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,kBAAkB,KAAM,4BAA4B,CAC3D,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,KAAM,CAAEC,OAAQ,CAAC,CAAGnB,MAAM,CAE1B;AACA,KAAM,CAAAoB,cAAuD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC3E,KAAM,CAAAE,eAAe,CAAGtB,WAAW,CAAEuB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAACF,eAAe,CAAC,CAErF,GAAI,CAACA,eAAe,CAAE,CACpB,mBAAOT,IAAA,CAACf,QAAQ,EAAC2B,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA,mBAAOb,IAAA,CAAAE,SAAA,EAAAM,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED,KAAM,CAAAM,GAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAL,eAAe,CAAGtB,WAAW,CAAEuB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAACF,eAAe,CAAC,CAErF,mBACET,IAAA,QAAKe,SAAS,CAAC,KAAK,CAAAP,QAAA,cAClBJ,KAAA,CAACrB,MAAM,EAAAyB,QAAA,eACLR,IAAA,CAAChB,KAAK,EACJgC,IAAI,CAAC,QAAQ,CACbC,OAAO,CACLR,eAAe,cAAGT,IAAA,CAACf,QAAQ,EAAC2B,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,cAAGb,IAAA,CAACZ,SAAS,GAAE,CAC7D,CACF,CAAC,cACFY,IAAA,CAAChB,KAAK,EACJgC,IAAI,CAAC,IAAI,CACTC,OAAO,cACLjB,IAAA,CAACM,cAAc,EAAAE,QAAA,cACbR,IAAA,CAACX,UAAU,EAAAmB,QAAA,cACTR,IAAA,CAACK,OAAO,EAAAG,QAAA,cACNJ,KAAA,CAACrB,MAAM,EAAAyB,QAAA,eACLR,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEjB,IAAA,CAACf,QAAQ,EAAC2B,EAAE,CAAC,gBAAgB,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cACrEb,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEjB,IAAA,CAACV,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC9DU,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEjB,IAAA,CAACT,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChES,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEjB,IAAA,CAACR,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChEQ,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEjB,IAAA,CAACP,mBAAmB,GAAE,CAAE,CAAE,CAAC,cACpEO,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEjB,IAAA,CAACN,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChEM,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEjB,IAAA,CAACL,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAChEK,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEjB,IAAA,CAACJ,aAAa,GAAE,CAAE,CAAE,CAAC,cACxDI,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEjB,IAAA,CAACH,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAClEG,IAAA,CAAChB,KAAK,EAACgC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEjB,IAAA,CAACF,eAAe,GAAE,CAAE,CAAE,CAAC,EACtD,CAAC,CACF,CAAC,CACA,CAAC,CACC,CACjB,CACF,CAAC,EACI,CAAC,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAgB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}