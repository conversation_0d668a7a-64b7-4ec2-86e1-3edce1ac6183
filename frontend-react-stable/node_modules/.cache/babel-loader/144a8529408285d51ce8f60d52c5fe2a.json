{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { detectFlexGapSupported } from '../styleChecker';\nexport default (function () {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    flexible = _React$useState2[0],\n    setFlexible = _React$useState2[1];\n  React.useEffect(function () {\n    setFlexible(detectFlexGapSupported());\n  }, []);\n  return flexible;\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "detectFlexGapSupported", "_React$useState", "useState", "_React$useState2", "flexible", "setFlexible", "useEffect"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/hooks/useFlexGapSupport.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { detectFlexGapSupported } from '../styleChecker';\nexport default (function () {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    flexible = _React$useState2[0],\n    setFlexible = _React$useState2[1];\n  React.useEffect(function () {\n    setFlexible(detectFlexGapSupported());\n  }, []);\n  return flexible;\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,iBAAiB;AACxD,gBAAgB,YAAY;EAC1B,IAAIC,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGL,cAAc,CAACG,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnCJ,KAAK,CAACO,SAAS,CAAC,YAAY;IAC1BD,WAAW,CAACL,sBAAsB,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;EACN,OAAOI,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}