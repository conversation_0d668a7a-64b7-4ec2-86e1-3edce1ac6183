{"ast": null, "code": "import * as React from 'react';\nexport default /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, _ref2) {\n  var shouldUpdate = _ref2.shouldUpdate;\n  return !shouldUpdate;\n});", "map": {"version": 3, "names": ["React", "memo", "_ref", "children", "_", "_ref2", "shouldUpdate"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dialog/es/Dialog/Content/MemoChildren.js"], "sourcesContent": ["import * as React from 'react';\nexport default /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, _ref2) {\n  var shouldUpdate = _ref2.shouldUpdate;\n  return !shouldUpdate;\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,aAAaA,KAAK,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;EACrD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,CAAC,EAAEC,KAAK,EAAE;EACrB,IAAIC,YAAY,GAAGD,KAAK,CAACC,YAAY;EACrC,OAAO,CAACA,YAAY;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}