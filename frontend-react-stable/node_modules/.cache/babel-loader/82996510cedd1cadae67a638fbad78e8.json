{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport Upload from './Upload';\nvar Dragger = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var style = _a.style,\n    height = _a.height,\n    restProps = __rest(_a, [\"style\", \"height\"]);\n  return /*#__PURE__*/React.createElement(Upload, _extends({\n    ref: ref\n  }, restProps, {\n    type: \"drag\",\n    style: _extends(_extends({}, style), {\n      height: height\n    })\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Dragger.displayName = 'Dragger';\n}\nexport default Dragger;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "Upload", "<PERSON><PERSON>", "forwardRef", "_a", "ref", "style", "height", "restProps", "createElement", "type", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/upload/Dragger.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport Upload from './Upload';\nvar Dragger = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var style = _a.style,\n    height = _a.height,\n    restProps = __rest(_a, [\"style\", \"height\"]);\n  return /*#__PURE__*/React.createElement(Upload, _extends({\n    ref: ref\n  }, restProps, {\n    type: \"drag\",\n    style: _extends(_extends({}, style), {\n      height: height\n    })\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Dragger.displayName = 'Dragger';\n}\nexport default Dragger;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,IAAIC,OAAO,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAC7D,IAAIC,KAAK,GAAGF,EAAE,CAACE,KAAK;IAClBC,MAAM,GAAGH,EAAE,CAACG,MAAM;IAClBC,SAAS,GAAGtB,MAAM,CAACkB,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;EAC7C,OAAO,aAAaJ,KAAK,CAACS,aAAa,CAACR,MAAM,EAAEhB,QAAQ,CAAC;IACvDoB,GAAG,EAAEA;EACP,CAAC,EAAEG,SAAS,EAAE;IACZE,IAAI,EAAE,MAAM;IACZJ,KAAK,EAAErB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,CAAC,EAAE;MACnCC,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCX,OAAO,CAACY,WAAW,GAAG,SAAS;AACjC;AACA,eAAeZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}