{"ast": null, "code": "var baseExtremum = require('./_baseExtremum'),\n  baseGt = require('./_baseGt'),\n  identity = require('./identity');\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return array && array.length ? baseExtremum(array, identity, baseGt) : undefined;\n}\nmodule.exports = max;", "map": {"version": 3, "names": ["baseExtremum", "require", "baseGt", "identity", "max", "array", "length", "undefined", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/max.js"], "sourcesContent": ["var baseExtremum = require('./_baseExtremum'),\n    baseGt = require('./_baseGt'),\n    identity = require('./identity');\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseGt)\n    : undefined;\n}\n\nmodule.exports = max;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,MAAM,GAAGD,OAAO,CAAC,WAAW,CAAC;EAC7BE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,GAAGA,CAACC,KAAK,EAAE;EAClB,OAAQA,KAAK,IAAIA,KAAK,CAACC,MAAM,GACzBN,YAAY,CAACK,KAAK,EAAEF,QAAQ,EAAED,MAAM,CAAC,GACrCK,SAAS;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGL,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script"}