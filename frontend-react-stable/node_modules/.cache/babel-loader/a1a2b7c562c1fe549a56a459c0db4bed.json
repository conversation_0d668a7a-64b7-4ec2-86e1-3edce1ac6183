{"ast": null, "code": "'use strict';\n\nvar bind = require('./helpers/bind');\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\n\n// eslint-disable-next-line func-names\nvar kindOf = function (cache) {\n  // eslint-disable-next-line func-names\n  return function (thing) {\n    var str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n  };\n}(Object.create(null));\nfunction kindOfTest(type) {\n  type = type.toLowerCase();\n  return function isKindOf(thing) {\n    return kindOf(thing) === type;\n  };\n}\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Array, otherwise false\n */\nfunction isArray(val) {\n  return Array.isArray(val);\n}\n\n/**\n * Determine if a value is undefined\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nfunction isUndefined(val) {\n  return typeof val === 'undefined';\n}\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nvar isArrayBuffer = kindOfTest('ArrayBuffer');\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if (typeof ArrayBuffer !== 'undefined' && ArrayBuffer.isView) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = val && val.buffer && isArrayBuffer(val.buffer);\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a String, otherwise false\n */\nfunction isString(val) {\n  return typeof val === 'string';\n}\n\n/**\n * Determine if a value is a Number\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Number, otherwise false\n */\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\n\n/**\n * Determine if a value is an Object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Object, otherwise false\n */\nfunction isObject(val) {\n  return val !== null && typeof val === 'object';\n}\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {Object} val The value to test\n * @return {boolean} True if value is a plain Object, otherwise false\n */\nfunction isPlainObject(val) {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n  var prototype = Object.getPrototypeOf(val);\n  return prototype === null || prototype === Object.prototype;\n}\n\n/**\n * Determine if a value is a Date\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Date, otherwise false\n */\nvar isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nvar isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Function\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nfunction isFunction(val) {\n  return toString.call(val) === '[object Function]';\n}\n\n/**\n * Determine if a value is a Stream\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nfunction isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n}\n\n/**\n * Determine if a value is a FormData\n *\n * @param {Object} thing The value to test\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nfunction isFormData(thing) {\n  var pattern = '[object FormData]';\n  return thing && (typeof FormData === 'function' && thing instanceof FormData || toString.call(thing) === pattern || isFunction(thing.toString) && thing.toString() === pattern);\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nvar isURLSearchParams = kindOfTest('URLSearchParams');\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n * @returns {String} The String freed of excess whitespace\n */\nfunction trim(str) {\n  return str.trim ? str.trim() : str.replace(/^\\s+|\\s+$/g, '');\n}\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n */\nfunction isStandardBrowserEnv() {\n  if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' || navigator.product === 'NativeScript' || navigator.product === 'NS')) {\n    return false;\n  }\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n */\nfunction forEach(obj, fn) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (var i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    for (var key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        fn.call(null, obj[key], key, obj);\n      }\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */\n) {\n  var result = {};\n  function assignValue(val, key) {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  }\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n * @return {Object} The resulting value of object a\n */\nfunction extend(a, b, thisArg) {\n  forEach(b, function assignValue(val, key) {\n    if (thisArg && typeof val === 'function') {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  });\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n * @return {string} content value without BOM\n */\nfunction stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n */\n\nfunction inherits(constructor, superConstructor, props, descriptors) {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  props && Object.assign(constructor.prototype, props);\n}\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function} [filter]\n * @returns {Object}\n */\n\nfunction toFlatObject(sourceObj, destObj, filter) {\n  var props;\n  var i;\n  var prop;\n  var merged = {};\n  destObj = destObj || {};\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if (!merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = Object.getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n  return destObj;\n}\n\n/*\n * determines whether a string ends with the characters of a specified string\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n * @returns {boolean}\n */\nfunction endsWith(str, searchString, position) {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  var lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n}\n\n/**\n * Returns new array from array like object\n * @param {*} [thing]\n * @returns {Array}\n */\nfunction toArray(thing) {\n  if (!thing) return null;\n  var i = thing.length;\n  if (isUndefined(i)) return null;\n  var arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n}\n\n// eslint-disable-next-line func-names\nvar isTypedArray = function (TypedArray) {\n  // eslint-disable-next-line func-names\n  return function (thing) {\n    return TypedArray && thing instanceof TypedArray;\n  };\n}(typeof Uint8Array !== 'undefined' && Object.getPrototypeOf(Uint8Array));\nmodule.exports = {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isStandardBrowserEnv: isStandardBrowserEnv,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM,\n  inherits: inherits,\n  toFlatObject: toFlatObject,\n  kindOf: kindOf,\n  kindOfTest: kindOfTest,\n  endsWith: endsWith,\n  toArray: toArray,\n  isTypedArray: isTypedArray,\n  isFileList: isFileList\n};", "map": {"version": 3, "names": ["bind", "require", "toString", "Object", "prototype", "kindOf", "cache", "thing", "str", "call", "slice", "toLowerCase", "create", "kindOfTest", "type", "isKindOf", "isArray", "val", "Array", "isUndefined", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArrayBuffer<PERSON>iew", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "isString", "isNumber", "isObject", "isPlainObject", "getPrototypeOf", "isDate", "isFile", "isBlob", "isFileList", "isFunction", "isStream", "pipe", "isFormData", "pattern", "FormData", "isURLSearchParams", "trim", "replace", "isStandardBrowserEnv", "navigator", "product", "window", "document", "for<PERSON>ach", "obj", "fn", "i", "l", "length", "key", "hasOwnProperty", "merge", "assignValue", "arguments", "extend", "a", "b", "thisArg", "stripBOM", "content", "charCodeAt", "inherits", "superConstructor", "props", "descriptors", "assign", "toFlatObject", "sourceObj", "destObj", "filter", "prop", "merged", "getOwnPropertyNames", "endsWith", "searchString", "position", "String", "undefined", "lastIndex", "indexOf", "toArray", "arr", "isTypedArray", "TypedArray", "Uint8Array", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/utils.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('./helpers/bind');\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\n\n// eslint-disable-next-line func-names\nvar kindOf = (function(cache) {\n  // eslint-disable-next-line func-names\n  return function(thing) {\n    var str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n  };\n})(Object.create(null));\n\nfunction kindOfTest(type) {\n  type = type.toLowerCase();\n  return function isKindOf(thing) {\n    return kindOf(thing) === type;\n  };\n}\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Array, otherwise false\n */\nfunction isArray(val) {\n  return Array.isArray(val);\n}\n\n/**\n * Determine if a value is undefined\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nfunction isUndefined(val) {\n  return typeof val === 'undefined';\n}\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nvar isArrayBuffer = kindOfTest('ArrayBuffer');\n\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (isArrayBuffer(val.buffer));\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a String, otherwise false\n */\nfunction isString(val) {\n  return typeof val === 'string';\n}\n\n/**\n * Determine if a value is a Number\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Number, otherwise false\n */\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\n\n/**\n * Determine if a value is an Object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Object, otherwise false\n */\nfunction isObject(val) {\n  return val !== null && typeof val === 'object';\n}\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {Object} val The value to test\n * @return {boolean} True if value is a plain Object, otherwise false\n */\nfunction isPlainObject(val) {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n\n  var prototype = Object.getPrototypeOf(val);\n  return prototype === null || prototype === Object.prototype;\n}\n\n/**\n * Determine if a value is a Date\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Date, otherwise false\n */\nvar isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nvar isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Function\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nfunction isFunction(val) {\n  return toString.call(val) === '[object Function]';\n}\n\n/**\n * Determine if a value is a Stream\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nfunction isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n}\n\n/**\n * Determine if a value is a FormData\n *\n * @param {Object} thing The value to test\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nfunction isFormData(thing) {\n  var pattern = '[object FormData]';\n  return thing && (\n    (typeof FormData === 'function' && thing instanceof FormData) ||\n    toString.call(thing) === pattern ||\n    (isFunction(thing.toString) && thing.toString() === pattern)\n  );\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n * @function\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nvar isURLSearchParams = kindOfTest('URLSearchParams');\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n * @returns {String} The String freed of excess whitespace\n */\nfunction trim(str) {\n  return str.trim ? str.trim() : str.replace(/^\\s+|\\s+$/g, '');\n}\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n */\nfunction isStandardBrowserEnv() {\n  if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||\n                                           navigator.product === 'NativeScript' ||\n                                           navigator.product === 'NS')) {\n    return false;\n  }\n  return (\n    typeof window !== 'undefined' &&\n    typeof document !== 'undefined'\n  );\n}\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n */\nfunction forEach(obj, fn) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (var i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    for (var key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        fn.call(null, obj[key], key, obj);\n      }\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  var result = {};\n  function assignValue(val, key) {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n * @return {Object} The resulting value of object a\n */\nfunction extend(a, b, thisArg) {\n  forEach(b, function assignValue(val, key) {\n    if (thisArg && typeof val === 'function') {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  });\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n * @return {string} content value without BOM\n */\nfunction stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n */\n\nfunction inherits(constructor, superConstructor, props, descriptors) {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  props && Object.assign(constructor.prototype, props);\n}\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function} [filter]\n * @returns {Object}\n */\n\nfunction toFlatObject(sourceObj, destObj, filter) {\n  var props;\n  var i;\n  var prop;\n  var merged = {};\n\n  destObj = destObj || {};\n\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if (!merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = Object.getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n\n  return destObj;\n}\n\n/*\n * determines whether a string ends with the characters of a specified string\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n * @returns {boolean}\n */\nfunction endsWith(str, searchString, position) {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  var lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n}\n\n\n/**\n * Returns new array from array like object\n * @param {*} [thing]\n * @returns {Array}\n */\nfunction toArray(thing) {\n  if (!thing) return null;\n  var i = thing.length;\n  if (isUndefined(i)) return null;\n  var arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n}\n\n// eslint-disable-next-line func-names\nvar isTypedArray = (function(TypedArray) {\n  // eslint-disable-next-line func-names\n  return function(thing) {\n    return TypedArray && thing instanceof TypedArray;\n  };\n})(typeof Uint8Array !== 'undefined' && Object.getPrototypeOf(Uint8Array));\n\nmodule.exports = {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isStandardBrowserEnv: isStandardBrowserEnv,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM,\n  inherits: inherits,\n  toFlatObject: toFlatObject,\n  kindOf: kindOf,\n  kindOfTest: kindOfTest,\n  endsWith: endsWith,\n  toArray: toArray,\n  isTypedArray: isTypedArray,\n  isFileList: isFileList\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,gBAAgB,CAAC;;AAEpC;;AAEA,IAAIC,QAAQ,GAAGC,MAAM,CAACC,SAAS,CAACF,QAAQ;;AAExC;AACA,IAAIG,MAAM,GAAI,UAASC,KAAK,EAAE;EAC5B;EACA,OAAO,UAASC,KAAK,EAAE;IACrB,IAAIC,GAAG,GAAGN,QAAQ,CAACO,IAAI,CAACF,KAAK,CAAC;IAC9B,OAAOD,KAAK,CAACE,GAAG,CAAC,KAAKF,KAAK,CAACE,GAAG,CAAC,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EACpE,CAAC;AACH,CAAC,CAAER,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC,CAAC;AAEvB,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxBA,IAAI,GAAGA,IAAI,CAACH,WAAW,CAAC,CAAC;EACzB,OAAO,SAASI,QAAQA,CAACR,KAAK,EAAE;IAC9B,OAAOF,MAAM,CAACE,KAAK,CAAC,KAAKO,IAAI;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACF,GAAG,EAAE;EACxB,OAAO,OAAOA,GAAG,KAAK,WAAW;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACH,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAK,IAAI,IAAI,CAACE,WAAW,CAACF,GAAG,CAAC,IAAIA,GAAG,CAACI,WAAW,KAAK,IAAI,IAAI,CAACF,WAAW,CAACF,GAAG,CAACI,WAAW,CAAC,IAChG,OAAOJ,GAAG,CAACI,WAAW,CAACD,QAAQ,KAAK,UAAU,IAAIH,GAAG,CAACI,WAAW,CAACD,QAAQ,CAACH,GAAG,CAAC;AACtF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,aAAa,GAAGT,UAAU,CAAC,aAAa,CAAC;;AAG7C;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,iBAAiBA,CAACN,GAAG,EAAE;EAC9B,IAAIO,MAAM;EACV,IAAK,OAAOC,WAAW,KAAK,WAAW,IAAMA,WAAW,CAACC,MAAO,EAAE;IAChEF,MAAM,GAAGC,WAAW,CAACC,MAAM,CAACT,GAAG,CAAC;EAClC,CAAC,MAAM;IACLO,MAAM,GAAIP,GAAG,IAAMA,GAAG,CAACU,MAAO,IAAKL,aAAa,CAACL,GAAG,CAACU,MAAM,CAAE;EAC/D;EACA,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACX,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,QAAQA,CAACZ,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,QAAQA,CAACb,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,aAAaA,CAACd,GAAG,EAAE;EAC1B,IAAIZ,MAAM,CAACY,GAAG,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAO,KAAK;EACd;EAEA,IAAIb,SAAS,GAAGD,MAAM,CAAC6B,cAAc,CAACf,GAAG,CAAC;EAC1C,OAAOb,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKD,MAAM,CAACC,SAAS;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI6B,MAAM,GAAGpB,UAAU,CAAC,MAAM,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIqB,MAAM,GAAGrB,UAAU,CAAC,MAAM,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsB,MAAM,GAAGtB,UAAU,CAAC,MAAM,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuB,UAAU,GAAGvB,UAAU,CAAC,UAAU,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,UAAUA,CAACpB,GAAG,EAAE;EACvB,OAAOf,QAAQ,CAACO,IAAI,CAACQ,GAAG,CAAC,KAAK,mBAAmB;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,QAAQA,CAACrB,GAAG,EAAE;EACrB,OAAOa,QAAQ,CAACb,GAAG,CAAC,IAAIoB,UAAU,CAACpB,GAAG,CAACsB,IAAI,CAAC;AAC9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACjC,KAAK,EAAE;EACzB,IAAIkC,OAAO,GAAG,mBAAmB;EACjC,OAAOlC,KAAK,KACT,OAAOmC,QAAQ,KAAK,UAAU,IAAInC,KAAK,YAAYmC,QAAQ,IAC5DxC,QAAQ,CAACO,IAAI,CAACF,KAAK,CAAC,KAAKkC,OAAO,IAC/BJ,UAAU,CAAC9B,KAAK,CAACL,QAAQ,CAAC,IAAIK,KAAK,CAACL,QAAQ,CAAC,CAAC,KAAKuC,OAAQ,CAC7D;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,iBAAiB,GAAG9B,UAAU,CAAC,iBAAiB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+B,IAAIA,CAACpC,GAAG,EAAE;EACjB,OAAOA,GAAG,CAACoC,IAAI,GAAGpC,GAAG,CAACoC,IAAI,CAAC,CAAC,GAAGpC,GAAG,CAACqC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,IAAI,OAAOC,SAAS,KAAK,WAAW,KAAKA,SAAS,CAACC,OAAO,KAAK,aAAa,IACnCD,SAAS,CAACC,OAAO,KAAK,cAAc,IACpCD,SAAS,CAACC,OAAO,KAAK,IAAI,CAAC,EAAE;IACpE,OAAO,KAAK;EACd;EACA,OACE,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOC,QAAQ,KAAK,WAAW;AAEnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,EAAE,EAAE;EACxB;EACA,IAAID,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;IAC9C;EACF;;EAEA;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B;IACAA,GAAG,GAAG,CAACA,GAAG,CAAC;EACb;EAEA,IAAIpC,OAAO,CAACoC,GAAG,CAAC,EAAE;IAChB;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC1CD,EAAE,CAAC5C,IAAI,CAAC,IAAI,EAAE2C,GAAG,CAACE,CAAC,CAAC,EAAEA,CAAC,EAAEF,GAAG,CAAC;IAC/B;EACF,CAAC,MAAM;IACL;IACA,KAAK,IAAIK,GAAG,IAAIL,GAAG,EAAE;MACnB,IAAIjD,MAAM,CAACC,SAAS,CAACsD,cAAc,CAACjD,IAAI,CAAC2C,GAAG,EAAEK,GAAG,CAAC,EAAE;QAClDJ,EAAE,CAAC5C,IAAI,CAAC,IAAI,EAAE2C,GAAG,CAACK,GAAG,CAAC,EAAEA,GAAG,EAAEL,GAAG,CAAC;MACnC;IACF;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,KAAKA,CAAC;AAAA,EAA6B;EAC1C,IAAInC,MAAM,GAAG,CAAC,CAAC;EACf,SAASoC,WAAWA,CAAC3C,GAAG,EAAEwC,GAAG,EAAE;IAC7B,IAAI1B,aAAa,CAACP,MAAM,CAACiC,GAAG,CAAC,CAAC,IAAI1B,aAAa,CAACd,GAAG,CAAC,EAAE;MACpDO,MAAM,CAACiC,GAAG,CAAC,GAAGE,KAAK,CAACnC,MAAM,CAACiC,GAAG,CAAC,EAAExC,GAAG,CAAC;IACvC,CAAC,MAAM,IAAIc,aAAa,CAACd,GAAG,CAAC,EAAE;MAC7BO,MAAM,CAACiC,GAAG,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,EAAE1C,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAID,OAAO,CAACC,GAAG,CAAC,EAAE;MACvBO,MAAM,CAACiC,GAAG,CAAC,GAAGxC,GAAG,CAACP,KAAK,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLc,MAAM,CAACiC,GAAG,CAAC,GAAGxC,GAAG;IACnB;EACF;EAEA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGM,SAAS,CAACL,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAChDH,OAAO,CAACU,SAAS,CAACP,CAAC,CAAC,EAAEM,WAAW,CAAC;EACpC;EACA,OAAOpC,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EAC7Bd,OAAO,CAACa,CAAC,EAAE,SAASJ,WAAWA,CAAC3C,GAAG,EAAEwC,GAAG,EAAE;IACxC,IAAIQ,OAAO,IAAI,OAAOhD,GAAG,KAAK,UAAU,EAAE;MACxC8C,CAAC,CAACN,GAAG,CAAC,GAAGzD,IAAI,CAACiB,GAAG,EAAEgD,OAAO,CAAC;IAC7B,CAAC,MAAM;MACLF,CAAC,CAACN,GAAG,CAAC,GAAGxC,GAAG;IACd;EACF,CAAC,CAAC;EACF,OAAO8C,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIA,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;IACpCD,OAAO,GAAGA,OAAO,CAACzD,KAAK,CAAC,CAAC,CAAC;EAC5B;EACA,OAAOyD,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,QAAQA,CAAChD,WAAW,EAAEiD,gBAAgB,EAAEC,KAAK,EAAEC,WAAW,EAAE;EACnEnD,WAAW,CAACjB,SAAS,GAAGD,MAAM,CAACS,MAAM,CAAC0D,gBAAgB,CAAClE,SAAS,EAAEoE,WAAW,CAAC;EAC9EnD,WAAW,CAACjB,SAAS,CAACiB,WAAW,GAAGA,WAAW;EAC/CkD,KAAK,IAAIpE,MAAM,CAACsE,MAAM,CAACpD,WAAW,CAACjB,SAAS,EAAEmE,KAAK,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,YAAYA,CAACC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EAChD,IAAIN,KAAK;EACT,IAAIjB,CAAC;EACL,IAAIwB,IAAI;EACR,IAAIC,MAAM,GAAG,CAAC,CAAC;EAEfH,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,GAAG;IACDL,KAAK,GAAGpE,MAAM,CAAC6E,mBAAmB,CAACL,SAAS,CAAC;IAC7CrB,CAAC,GAAGiB,KAAK,CAACf,MAAM;IAChB,OAAOF,CAAC,EAAE,GAAG,CAAC,EAAE;MACdwB,IAAI,GAAGP,KAAK,CAACjB,CAAC,CAAC;MACf,IAAI,CAACyB,MAAM,CAACD,IAAI,CAAC,EAAE;QACjBF,OAAO,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;QAC/BC,MAAM,CAACD,IAAI,CAAC,GAAG,IAAI;MACrB;IACF;IACAH,SAAS,GAAGxE,MAAM,CAAC6B,cAAc,CAAC2C,SAAS,CAAC;EAC9C,CAAC,QAAQA,SAAS,KAAK,CAACE,MAAM,IAAIA,MAAM,CAACF,SAAS,EAAEC,OAAO,CAAC,CAAC,IAAID,SAAS,KAAKxE,MAAM,CAACC,SAAS;EAE/F,OAAOwE,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACzE,GAAG,EAAE0E,YAAY,EAAEC,QAAQ,EAAE;EAC7C3E,GAAG,GAAG4E,MAAM,CAAC5E,GAAG,CAAC;EACjB,IAAI2E,QAAQ,KAAKE,SAAS,IAAIF,QAAQ,GAAG3E,GAAG,CAACgD,MAAM,EAAE;IACnD2B,QAAQ,GAAG3E,GAAG,CAACgD,MAAM;EACvB;EACA2B,QAAQ,IAAID,YAAY,CAAC1B,MAAM;EAC/B,IAAI8B,SAAS,GAAG9E,GAAG,CAAC+E,OAAO,CAACL,YAAY,EAAEC,QAAQ,CAAC;EACnD,OAAOG,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAKH,QAAQ;AACnD;;AAGA;AACA;AACA;AACA;AACA;AACA,SAASK,OAAOA,CAACjF,KAAK,EAAE;EACtB,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,IAAI+C,CAAC,GAAG/C,KAAK,CAACiD,MAAM;EACpB,IAAIrC,WAAW,CAACmC,CAAC,CAAC,EAAE,OAAO,IAAI;EAC/B,IAAImC,GAAG,GAAG,IAAIvE,KAAK,CAACoC,CAAC,CAAC;EACtB,OAAOA,CAAC,EAAE,GAAG,CAAC,EAAE;IACdmC,GAAG,CAACnC,CAAC,CAAC,GAAG/C,KAAK,CAAC+C,CAAC,CAAC;EACnB;EACA,OAAOmC,GAAG;AACZ;;AAEA;AACA,IAAIC,YAAY,GAAI,UAASC,UAAU,EAAE;EACvC;EACA,OAAO,UAASpF,KAAK,EAAE;IACrB,OAAOoF,UAAU,IAAIpF,KAAK,YAAYoF,UAAU;EAClD,CAAC;AACH,CAAC,CAAE,OAAOC,UAAU,KAAK,WAAW,IAAIzF,MAAM,CAAC6B,cAAc,CAAC4D,UAAU,CAAC,CAAC;AAE1EC,MAAM,CAACC,OAAO,GAAG;EACf9E,OAAO,EAAEA,OAAO;EAChBM,aAAa,EAAEA,aAAa;EAC5BF,QAAQ,EAAEA,QAAQ;EAClBoB,UAAU,EAAEA,UAAU;EACtBjB,iBAAiB,EAAEA,iBAAiB;EACpCK,QAAQ,EAAEA,QAAQ;EAClBC,QAAQ,EAAEA,QAAQ;EAClBC,QAAQ,EAAEA,QAAQ;EAClBC,aAAa,EAAEA,aAAa;EAC5BZ,WAAW,EAAEA,WAAW;EACxBc,MAAM,EAAEA,MAAM;EACdC,MAAM,EAAEA,MAAM;EACdC,MAAM,EAAEA,MAAM;EACdE,UAAU,EAAEA,UAAU;EACtBC,QAAQ,EAAEA,QAAQ;EAClBK,iBAAiB,EAAEA,iBAAiB;EACpCG,oBAAoB,EAAEA,oBAAoB;EAC1CK,OAAO,EAAEA,OAAO;EAChBQ,KAAK,EAAEA,KAAK;EACZG,MAAM,EAAEA,MAAM;EACdlB,IAAI,EAAEA,IAAI;EACVsB,QAAQ,EAAEA,QAAQ;EAClBG,QAAQ,EAAEA,QAAQ;EAClBK,YAAY,EAAEA,YAAY;EAC1BrE,MAAM,EAAEA,MAAM;EACdQ,UAAU,EAAEA,UAAU;EACtBoE,QAAQ,EAAEA,QAAQ;EAClBO,OAAO,EAAEA,OAAO;EAChBE,YAAY,EAAEA,YAAY;EAC1BtD,UAAU,EAAEA;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}