{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getTransitionName } from '../../_util/motion';\nvar motion = {\n  motionAppear: false,\n  motionEnter: true,\n  motionLeave: true\n};\nexport default function useAnimateConfig(prefixCls) {\n  var animated = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: true\n    };\n  } else {\n    mergedAnimated = _extends({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  }\n  if (mergedAnimated.tabPane) {\n    mergedAnimated.tabPaneMotion = _extends(_extends({}, motion), {\n      motionName: getTransitionName(prefixCls, 'switch')\n    });\n  }\n  return mergedAnimated;\n}", "map": {"version": 3, "names": ["_typeof", "_extends", "getTransitionName", "motion", "motionAppear", "motionEnter", "motionLeave", "useAnimateConfig", "prefixCls", "animated", "arguments", "length", "undefined", "inkBar", "tabPane", "mergedAnimated", "tabPaneMotion", "motionName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tabs/hooks/useAnimateConfig.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getTransitionName } from '../../_util/motion';\nvar motion = {\n  motionAppear: false,\n  motionEnter: true,\n  motionLeave: true\n};\nexport default function useAnimateConfig(prefixCls) {\n  var animated = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: true\n    };\n  } else {\n    mergedAnimated = _extends({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  }\n  if (mergedAnimated.tabPane) {\n    mergedAnimated.tabPaneMotion = _extends(_extends({}, motion), {\n      motionName: getTransitionName(prefixCls, 'switch')\n    });\n  }\n  return mergedAnimated;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,IAAIC,MAAM,GAAG;EACXC,YAAY,EAAE,KAAK;EACnBC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE;AACf,CAAC;AACD,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAClD,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IACjFG,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,cAAc;EAClB,IAAIN,QAAQ,KAAK,KAAK,EAAE;IACtBM,cAAc,GAAG;MACfF,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM,IAAIL,QAAQ,KAAK,IAAI,EAAE;IAC5BM,cAAc,GAAG;MACfF,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAC;EACH,CAAC,MAAM;IACLC,cAAc,GAAGd,QAAQ,CAAC;MACxBY,MAAM,EAAE;IACV,CAAC,EAAEb,OAAO,CAACS,QAAQ,CAAC,KAAK,QAAQ,GAAGA,QAAQ,GAAG,CAAC,CAAC,CAAC;EACpD;EACA,IAAIM,cAAc,CAACD,OAAO,EAAE;IAC1BC,cAAc,CAACC,aAAa,GAAGf,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,EAAE;MAC5Dc,UAAU,EAAEf,iBAAiB,CAACM,SAAS,EAAE,QAAQ;IACnD,CAAC,CAAC;EACJ;EACA,OAAOO,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}