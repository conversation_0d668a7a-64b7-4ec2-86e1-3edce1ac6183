{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = tickFormat;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _index2 = require(\"../../../lib-vendor/d3-format/src/index.js\");\nfunction tickFormat(start, stop, count, specifier) {\n  var step = (0, _index.tickStep)(start, stop, count),\n    precision;\n  specifier = (0, _index2.formatSpecifier)(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\":\n      {\n        var value = Math.max(Math.abs(start), Math.abs(stop));\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionPrefix)(step, value))) specifier.precision = precision;\n        return (0, _index2.formatPrefix)(specifier, value);\n      }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\":\n      {\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionRound)(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n        break;\n      }\n    case \"f\":\n    case \"%\":\n      {\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionFixed)(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n        break;\n      }\n  }\n  return (0, _index2.format)(specifier);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "tickFormat", "_index", "require", "_index2", "start", "stop", "count", "specifier", "step", "tickStep", "precision", "formatSpecifier", "type", "Math", "max", "abs", "isNaN", "precisionPrefix", "formatPrefix", "precisionRound", "precisionFixed", "format"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/tickFormat.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = tickFormat;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _index2 = require(\"../../../lib-vendor/d3-format/src/index.js\");\n\nfunction tickFormat(start, stop, count, specifier) {\n  var step = (0, _index.tickStep)(start, stop, count),\n      precision;\n  specifier = (0, _index2.formatSpecifier)(specifier == null ? \",f\" : specifier);\n\n  switch (specifier.type) {\n    case \"s\":\n      {\n        var value = Math.max(Math.abs(start), Math.abs(stop));\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionPrefix)(step, value))) specifier.precision = precision;\n        return (0, _index2.formatPrefix)(specifier, value);\n      }\n\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\":\n      {\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionRound)(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n        break;\n      }\n\n    case \"f\":\n    case \"%\":\n      {\n        if (specifier.precision == null && !isNaN(precision = (0, _index2.precisionFixed)(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n        break;\n      }\n  }\n\n  return (0, _index2.format)(specifier);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,OAAO,GAAGD,OAAO,CAAC,4CAA4C,CAAC;AAEnE,SAASF,UAAUA,CAACI,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACjD,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAEP,MAAM,CAACQ,QAAQ,EAAEL,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IAC/CI,SAAS;EACbH,SAAS,GAAG,CAAC,CAAC,EAAEJ,OAAO,CAACQ,eAAe,EAAEJ,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,CAAC;EAE9E,QAAQA,SAAS,CAACK,IAAI;IACpB,KAAK,GAAG;MACN;QACE,IAAId,KAAK,GAAGe,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACX,KAAK,CAAC,EAAES,IAAI,CAACE,GAAG,CAACV,IAAI,CAAC,CAAC;QACrD,IAAIE,SAAS,CAACG,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACc,eAAe,EAAET,IAAI,EAAEV,KAAK,CAAC,CAAC,EAAES,SAAS,CAACG,SAAS,GAAGA,SAAS;QACjI,OAAO,CAAC,CAAC,EAAEP,OAAO,CAACe,YAAY,EAAEX,SAAS,EAAET,KAAK,CAAC;MACpD;IAEF,KAAK,EAAE;IACP,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,GAAG;IACR,KAAK,GAAG;MACN;QACE,IAAIS,SAAS,CAACG,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACgB,cAAc,EAAEX,IAAI,EAAEK,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACX,KAAK,CAAC,EAAES,IAAI,CAACE,GAAG,CAACV,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEE,SAAS,CAACG,SAAS,GAAGA,SAAS,IAAIH,SAAS,CAACK,IAAI,KAAK,GAAG,CAAC;QAC/L;MACF;IAEF,KAAK,GAAG;IACR,KAAK,GAAG;MACN;QACE,IAAIL,SAAS,CAACG,SAAS,IAAI,IAAI,IAAI,CAACM,KAAK,CAACN,SAAS,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACiB,cAAc,EAAEZ,IAAI,CAAC,CAAC,EAAED,SAAS,CAACG,SAAS,GAAGA,SAAS,GAAG,CAACH,SAAS,CAACK,IAAI,KAAK,GAAG,IAAI,CAAC;QACxJ;MACF;EACJ;EAEA,OAAO,CAAC,CAAC,EAAET,OAAO,CAACkB,MAAM,EAAEd,SAAS,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}