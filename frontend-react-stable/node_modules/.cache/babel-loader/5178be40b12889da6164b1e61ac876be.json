{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"children\", \"locked\"];\nimport * as React from 'react';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport shallowEqual from 'shallowequal';\nexport var MenuContext = /*#__PURE__*/React.createContext(null);\nfunction mergeProps(origin, target) {\n  var clone = _objectSpread({}, origin);\n  Object.keys(target).forEach(function (key) {\n    var value = target[key];\n    if (value !== undefined) {\n      clone[key] = value;\n    }\n  });\n  return clone;\n}\nexport default function InheritableContextProvider(_ref) {\n  var children = _ref.children,\n    locked = _ref.locked,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var context = React.useContext(MenuContext);\n  var inheritableContext = useMemo(function () {\n    return mergeProps(context, restProps);\n  }, [context, restProps], function (prev, next) {\n    return !locked && (prev[0] !== next[0] || !shallowEqual(prev[1], next[1]));\n  });\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: inheritableContext\n  }, children);\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_objectSpread", "_excluded", "React", "useMemo", "shallowEqual", "MenuContext", "createContext", "mergeProps", "origin", "target", "clone", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "InheritableContextProvider", "_ref", "children", "locked", "restProps", "context", "useContext", "inheritable<PERSON><PERSON><PERSON><PERSON>", "prev", "next", "createElement", "Provider"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/context/MenuContext.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"children\", \"locked\"];\nimport * as React from 'react';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport shallowEqual from 'shallowequal';\nexport var MenuContext = /*#__PURE__*/React.createContext(null);\n\nfunction mergeProps(origin, target) {\n  var clone = _objectSpread({}, origin);\n\n  Object.keys(target).forEach(function (key) {\n    var value = target[key];\n\n    if (value !== undefined) {\n      clone[key] = value;\n    }\n  });\n  return clone;\n}\n\nexport default function InheritableContextProvider(_ref) {\n  var children = _ref.children,\n      locked = _ref.locked,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var context = React.useContext(MenuContext);\n  var inheritableContext = useMemo(function () {\n    return mergeProps(context, restProps);\n  }, [context, restProps], function (prev, next) {\n    return !locked && (prev[0] !== next[0] || !shallowEqual(prev[1], next[1]));\n  });\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: inheritableContext\n  }, children);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;AACtC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,YAAY,MAAM,cAAc;AACvC,OAAO,IAAIC,WAAW,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AAE/D,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,IAAIC,KAAK,GAAGV,aAAa,CAAC,CAAC,CAAC,EAAEQ,MAAM,CAAC;EAErCG,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;IACzC,IAAIC,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;IAEvB,IAAIC,KAAK,KAAKC,SAAS,EAAE;MACvBN,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF,CAAC,CAAC;EACF,OAAOL,KAAK;AACd;AAEA,eAAe,SAASO,0BAA0BA,CAACC,IAAI,EAAE;EACvD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,SAAS,GAAGtB,wBAAwB,CAACmB,IAAI,EAAEjB,SAAS,CAAC;EAEzD,IAAIqB,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAAClB,WAAW,CAAC;EAC3C,IAAImB,kBAAkB,GAAGrB,OAAO,CAAC,YAAY;IAC3C,OAAOI,UAAU,CAACe,OAAO,EAAED,SAAS,CAAC;EACvC,CAAC,EAAE,CAACC,OAAO,EAAED,SAAS,CAAC,EAAE,UAAUI,IAAI,EAAEC,IAAI,EAAE;IAC7C,OAAO,CAACN,MAAM,KAAKK,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,IAAI,CAACtB,YAAY,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5E,CAAC,CAAC;EACF,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IAC5Db,KAAK,EAAES;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}