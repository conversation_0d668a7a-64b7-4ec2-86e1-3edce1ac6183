{"ast": null, "code": "// eslint-disable-next-line import/prefer-default-export\nexport function hasPrefixSuffix(props) {\n  return !!(props.prefix || props.suffix || props.allowClear);\n}", "map": {"version": 3, "names": ["hasPrefixSuffix", "props", "prefix", "suffix", "allowClear"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/utils.js"], "sourcesContent": ["// eslint-disable-next-line import/prefer-default-export\nexport function hasPrefixSuffix(props) {\n  return !!(props.prefix || props.suffix || props.allowClear);\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,CAAC,EAAEA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,UAAU,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module"}