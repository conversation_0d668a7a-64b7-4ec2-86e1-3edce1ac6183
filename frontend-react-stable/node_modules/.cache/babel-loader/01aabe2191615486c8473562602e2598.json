{"ast": null, "code": "var DataView = require('./_DataView'),\n  Map = require('./_Map'),\n  Promise = require('./_Promise'),\n  Set = require('./_Set'),\n  WeakMap = require('./_WeakMap'),\n  baseGetTag = require('./_baseGetTag'),\n  toSource = require('./_toSource');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  objectTag = '[object Object]',\n  promiseTag = '[object Promise]',\n  setTag = '[object Set]',\n  weakMapTag = '[object WeakMap]';\nvar dataViewTag = '[object DataView]';\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n  mapCtorString = toSource(Map),\n  promiseCtorString = toSource(Promise),\n  setCtorString = toSource(Set),\n  weakMapCtorString = toSource(WeakMap);\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map()) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set()) != setTag || WeakMap && getTag(new WeakMap()) != weakMapTag) {\n  getTag = function (value) {\n    var result = baseGetTag(value),\n      Ctor = result == objectTag ? value.constructor : undefined,\n      ctorString = Ctor ? toSource(Ctor) : '';\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString:\n          return dataViewTag;\n        case mapCtorString:\n          return mapTag;\n        case promiseCtorString:\n          return promiseTag;\n        case setCtorString:\n          return setTag;\n        case weakMapCtorString:\n          return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\nmodule.exports = getTag;", "map": {"version": 3, "names": ["DataView", "require", "Map", "Promise", "Set", "WeakMap", "baseGetTag", "toSource", "mapTag", "objectTag", "promiseTag", "setTag", "weakMapTag", "dataViewTag", "dataViewCtorString", "mapCtorString", "promiseCtorString", "setCtorString", "weakMapCtorString", "getTag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "value", "result", "Ctor", "constructor", "undefined", "ctorString", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_getTag.js"], "sourcesContent": ["var DataView = require('./_DataView'),\n    Map = require('./_Map'),\n    Promise = require('./_Promise'),\n    Set = require('./_Set'),\n    WeakMap = require('./_WeakMap'),\n    baseGetTag = require('./_baseGetTag'),\n    toSource = require('./_toSource');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    setTag = '[object Set]',\n    weakMapTag = '[object WeakMap]';\n\nvar dataViewTag = '[object DataView]';\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\n    (Map && getTag(new Map) != mapTag) ||\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\n    (Set && getTag(new Set) != setTag) ||\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\n  getTag = function(value) {\n    var result = baseGetTag(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : '';\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString: return dataViewTag;\n        case mapCtorString: return mapTag;\n        case promiseCtorString: return promiseTag;\n        case setCtorString: return setTag;\n        case weakMapCtorString: return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\n\nmodule.exports = getTag;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,GAAG,GAAGD,OAAO,CAAC,QAAQ,CAAC;EACvBE,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;EAC/BG,GAAG,GAAGH,OAAO,CAAC,QAAQ,CAAC;EACvBI,OAAO,GAAGJ,OAAO,CAAC,YAAY,CAAC;EAC/BK,UAAU,GAAGL,OAAO,CAAC,eAAe,CAAC;EACrCM,QAAQ,GAAGN,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA,IAAIO,MAAM,GAAG,cAAc;EACvBC,SAAS,GAAG,iBAAiB;EAC7BC,UAAU,GAAG,kBAAkB;EAC/BC,MAAM,GAAG,cAAc;EACvBC,UAAU,GAAG,kBAAkB;AAEnC,IAAIC,WAAW,GAAG,mBAAmB;;AAErC;AACA,IAAIC,kBAAkB,GAAGP,QAAQ,CAACP,QAAQ,CAAC;EACvCe,aAAa,GAAGR,QAAQ,CAACL,GAAG,CAAC;EAC7Bc,iBAAiB,GAAGT,QAAQ,CAACJ,OAAO,CAAC;EACrCc,aAAa,GAAGV,QAAQ,CAACH,GAAG,CAAC;EAC7Bc,iBAAiB,GAAGX,QAAQ,CAACF,OAAO,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIc,MAAM,GAAGb,UAAU;;AAEvB;AACA,IAAKN,QAAQ,IAAImB,MAAM,CAAC,IAAInB,QAAQ,CAAC,IAAIoB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIP,WAAW,IACnEX,GAAG,IAAIiB,MAAM,CAAC,IAAIjB,GAAG,CAAD,CAAC,CAAC,IAAIM,MAAO,IACjCL,OAAO,IAAIgB,MAAM,CAAChB,OAAO,CAACkB,OAAO,CAAC,CAAC,CAAC,IAAIX,UAAW,IACnDN,GAAG,IAAIe,MAAM,CAAC,IAAIf,GAAG,CAAD,CAAC,CAAC,IAAIO,MAAO,IACjCN,OAAO,IAAIc,MAAM,CAAC,IAAId,OAAO,CAAD,CAAC,CAAC,IAAIO,UAAW,EAAE;EAClDO,MAAM,GAAG,SAAAA,CAASG,KAAK,EAAE;IACvB,IAAIC,MAAM,GAAGjB,UAAU,CAACgB,KAAK,CAAC;MAC1BE,IAAI,GAAGD,MAAM,IAAId,SAAS,GAAGa,KAAK,CAACG,WAAW,GAAGC,SAAS;MAC1DC,UAAU,GAAGH,IAAI,GAAGjB,QAAQ,CAACiB,IAAI,CAAC,GAAG,EAAE;IAE3C,IAAIG,UAAU,EAAE;MACd,QAAQA,UAAU;QAChB,KAAKb,kBAAkB;UAAE,OAAOD,WAAW;QAC3C,KAAKE,aAAa;UAAE,OAAOP,MAAM;QACjC,KAAKQ,iBAAiB;UAAE,OAAON,UAAU;QACzC,KAAKO,aAAa;UAAE,OAAON,MAAM;QACjC,KAAKO,iBAAiB;UAAE,OAAON,UAAU;MAC3C;IACF;IACA,OAAOW,MAAM;EACf,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}