{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport { useSelector } from 'react-redux';\nimport LoginPage from './pages/LoginPage';\nimport MainLayout from './components/Layout/MainLayout';\nimport DataCleaningPage from './pages/DataCleaningPage';\nimport ModelTrainingPage from './pages/ModelTrainingPage';\nimport ModelPredictionPage from './pages/ModelPredictionPage';\nimport ModelRegistryPage from './pages/ModelRegistryPage';\nimport CleanTemplatePage from './pages/CleanTemplatePage';\nimport DataQueryPage from './pages/DataQueryPage';\nimport UserManagementPage from './pages/UserManagementPage';\nimport TaskManagerPage from './pages/TaskManagerPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\n\n// 路由守卫组件\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const isAuthenticated = useSelector(state => state.auth.isAuthenticated);\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"Tis/laFBs1mUsdamrhCn/+4kssg=\", false, function () {\n  return [useSelector];\n});\n_c = ProtectedRoute;\nconst App = () => {\n  _s2();\n  const isAuthenticated = useSelector(state => state.auth.isAuthenticated);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 61\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/*\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(Content, {\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/data-cleaning\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 46\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/data-cleaning\",\n                  element: /*#__PURE__*/_jsxDEV(DataCleaningPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 51,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/model-training\",\n                  element: /*#__PURE__*/_jsxDEV(ModelTrainingPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/batch-training\",\n                  element: /*#__PURE__*/_jsxDEV(BatchTrainingPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/model-prediction\",\n                  element: /*#__PURE__*/_jsxDEV(ModelPredictionPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 54,\n                    columnNumber: 62\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/model-registry\",\n                  element: /*#__PURE__*/_jsxDEV(ModelRegistryPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 55,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/clean-template\",\n                  element: /*#__PURE__*/_jsxDEV(CleanTemplatePage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/data-query\",\n                  element: /*#__PURE__*/_jsxDEV(DataQueryPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 56\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/user-management\",\n                  element: /*#__PURE__*/_jsxDEV(UserManagementPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/task-manager\",\n                  element: /*#__PURE__*/_jsxDEV(TaskManagerPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 58\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s2(App, \"Tis/laFBs1mUsdamrhCn/+4kssg=\", false, function () {\n  return [useSelector];\n});\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Layout", "useSelector", "LoginPage", "MainLayout", "DataCleaningPage", "ModelTrainingPage", "ModelPredictionPage", "ModelRegistryPage", "CleanTemplatePage", "DataQueryPage", "UserManagementPage", "TaskManagerPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Content", "ProtectedRoute", "children", "_s", "isAuthenticated", "state", "auth", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_s2", "className", "path", "element", "BatchTrainingPage", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store/store';\nimport LoginPage from './pages/LoginPage';\nimport MainLayout from './components/Layout/MainLayout';\nimport DataCleaningPage from './pages/DataCleaningPage';\nimport ModelTrainingPage from './pages/ModelTrainingPage';\nimport ModelPredictionPage from './pages/ModelPredictionPage';\nimport ModelRegistryPage from './pages/ModelRegistryPage';\nimport CleanTemplatePage from './pages/CleanTemplatePage';\nimport DataQueryPage from './pages/DataQueryPage';\nimport UserManagementPage from './pages/UserManagementPage';\nimport TaskManagerPage from './pages/TaskManagerPage';\nimport './App.css';\n\nconst { Content } = Layout;\n\n// 路由守卫组件\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n  \n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  \n  return <>{children}</>;\n};\n\nconst App: React.FC = () => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={\n            isAuthenticated ? <Navigate to=\"/\" replace /> : <LoginPage />\n          } \n        />\n        <Route\n          path=\"/*\"\n          element={\n            <ProtectedRoute>\n              <MainLayout>\n                <Content>\n                  <Routes>\n                    <Route path=\"/\" element={<Navigate to=\"/data-cleaning\" replace />} />\n                    <Route path=\"/data-cleaning\" element={<DataCleaningPage />} />\n                    <Route path=\"/model-training\" element={<ModelTrainingPage />} />\n                    <Route path=\"/batch-training\" element={<BatchTrainingPage />} />\n                    <Route path=\"/model-prediction\" element={<ModelPredictionPage />} />\n                    <Route path=\"/model-registry\" element={<ModelRegistryPage />} />\n                    <Route path=\"/clean-template\" element={<CleanTemplatePage />} />\n                    <Route path=\"/data-query\" element={<DataQueryPage />} />\n                    <Route path=\"/user-management\" element={<UserManagementPage />} />\n                    <Route path=\"/task-manager\" element={<TaskManagerPage />} />\n                  </Routes>\n                </Content>\n              </MainLayout>\n            </ProtectedRoute>\n          }\n        />\n      </Routes>\n    </div>\n  );\n};\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,MAAM,QAAQ,MAAM;AAC7B,SAASC,WAAW,QAAQ,aAAa;AAEzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,MAAM;EAAEC;AAAQ,CAAC,GAAGhB,MAAM;;AAE1B;AACA,MAAMiB,cAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAMC,eAAe,GAAGnB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAACF,eAAe,CAAC;EAErF,IAAI,CAACA,eAAe,EAAE;IACpB,oBAAOP,OAAA,CAACd,QAAQ;MAACwB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,oBAAOf,OAAA,CAAAE,SAAA;IAAAG,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CARIF,cAAuD;EAAA,QACnChB,WAAW;AAAA;AAAA4B,EAAA,GAD/BZ,cAAuD;AAU7D,MAAMa,GAAa,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC1B,MAAMX,eAAe,GAAGnB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAACF,eAAe,CAAC;EAErF,oBACEP,OAAA;IAAKmB,SAAS,EAAC,KAAK;IAAAd,QAAA,eAClBL,OAAA,CAAChB,MAAM;MAAAqB,QAAA,gBACLL,OAAA,CAACf,KAAK;QACJmC,IAAI,EAAC,QAAQ;QACbC,OAAO,EACLd,eAAe,gBAAGP,OAAA,CAACd,QAAQ;UAACwB,EAAE,EAAC,GAAG;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGf,OAAA,CAACX,SAAS;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC7D;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFf,OAAA,CAACf,KAAK;QACJmC,IAAI,EAAC,IAAI;QACTC,OAAO,eACLrB,OAAA,CAACI,cAAc;UAAAC,QAAA,eACbL,OAAA,CAACV,UAAU;YAAAe,QAAA,eACTL,OAAA,CAACG,OAAO;cAAAE,QAAA,eACNL,OAAA,CAAChB,MAAM;gBAAAqB,QAAA,gBACLL,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAErB,OAAA,CAACd,QAAQ;oBAACwB,EAAE,EAAC,gBAAgB;oBAACC,OAAO;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,gBAAgB;kBAACC,OAAO,eAAErB,OAAA,CAACT,gBAAgB;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9Df,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eAAErB,OAAA,CAACR,iBAAiB;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eAAErB,OAAA,CAACsB,iBAAiB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,mBAAmB;kBAACC,OAAO,eAAErB,OAAA,CAACP,mBAAmB;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eAAErB,OAAA,CAACN,iBAAiB;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eAAErB,OAAA,CAACL,iBAAiB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,aAAa;kBAACC,OAAO,eAAErB,OAAA,CAACJ,aAAa;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,kBAAkB;kBAACC,OAAO,eAAErB,OAAA,CAACH,kBAAkB;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEf,OAAA,CAACf,KAAK;kBAACmC,IAAI,EAAC,eAAe;kBAACC,OAAO,eAAErB,OAAA,CAACF,eAAe;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACG,GAAA,CAtCID,GAAa;EAAA,QACO7B,WAAW;AAAA;AAAAmC,GAAA,GAD/BN,GAAa;AAwCnB,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}