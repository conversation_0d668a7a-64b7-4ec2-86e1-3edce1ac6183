{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport warning from \"rc-util/es/warning\";\nimport { isNil } from \"../utils/valueUtil\";\nexport default (function (treeData, fieldNames) {\n  return React.useMemo(function () {\n    var collection = convertDataToEntities(treeData, {\n      fieldNames: fieldNames,\n      initWrapper: function initWrapper(wrapper) {\n        return _objectSpread(_objectSpread({}, wrapper), {}, {\n          valueEntities: new Map()\n        });\n      },\n      processEntity: function processEntity(entity, wrapper) {\n        var val = entity.node[fieldNames.value]; // Check if exist same value\n\n        if (process.env.NODE_ENV !== 'production') {\n          var key = entity.node.key;\n          warning(!isNil(val), 'TreeNode `value` is invalidate: undefined');\n          warning(!wrapper.valueEntities.has(val), \"Same `value` exist in the tree: \".concat(val));\n          warning(!key || String(key) === String(val), \"`key` or `value` with TreeNode must be the same or you can remove one of them. key: \".concat(key, \", value: \").concat(val, \".\"));\n        }\n        wrapper.valueEntities.set(val, entity);\n      }\n    });\n    return collection;\n  }, [treeData, fieldNames]);\n});", "map": {"version": 3, "names": ["_objectSpread", "React", "convertDataToEntities", "warning", "isNil", "treeData", "fieldNames", "useMemo", "collection", "initWrapper", "wrapper", "valueEntities", "Map", "processEntity", "entity", "val", "node", "value", "process", "env", "NODE_ENV", "key", "has", "concat", "String", "set"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/hooks/useDataEntities.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport warning from \"rc-util/es/warning\";\nimport { isNil } from \"../utils/valueUtil\";\nexport default (function (treeData, fieldNames) {\n  return React.useMemo(function () {\n    var collection = convertDataToEntities(treeData, {\n      fieldNames: fieldNames,\n      initWrapper: function initWrapper(wrapper) {\n        return _objectSpread(_objectSpread({}, wrapper), {}, {\n          valueEntities: new Map()\n        });\n      },\n      processEntity: function processEntity(entity, wrapper) {\n        var val = entity.node[fieldNames.value]; // Check if exist same value\n\n        if (process.env.NODE_ENV !== 'production') {\n          var key = entity.node.key;\n          warning(!isNil(val), 'TreeNode `value` is invalidate: undefined');\n          warning(!wrapper.valueEntities.has(val), \"Same `value` exist in the tree: \".concat(val));\n          warning(!key || String(key) === String(val), \"`key` or `value` with TreeNode must be the same or you can remove one of them. key: \".concat(key, \", value: \").concat(val, \".\"));\n        }\n\n        wrapper.valueEntities.set(val, entity);\n      }\n    });\n    return collection;\n  }, [treeData, fieldNames]);\n});"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,gBAAgB,UAAUC,QAAQ,EAAEC,UAAU,EAAE;EAC9C,OAAOL,KAAK,CAACM,OAAO,CAAC,YAAY;IAC/B,IAAIC,UAAU,GAAGN,qBAAqB,CAACG,QAAQ,EAAE;MAC/CC,UAAU,EAAEA,UAAU;MACtBG,WAAW,EAAE,SAASA,WAAWA,CAACC,OAAO,EAAE;QACzC,OAAOV,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEU,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;UACnDC,aAAa,EAAE,IAAIC,GAAG,CAAC;QACzB,CAAC,CAAC;MACJ,CAAC;MACDC,aAAa,EAAE,SAASA,aAAaA,CAACC,MAAM,EAAEJ,OAAO,EAAE;QACrD,IAAIK,GAAG,GAAGD,MAAM,CAACE,IAAI,CAACV,UAAU,CAACW,KAAK,CAAC,CAAC,CAAC;;QAEzC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIC,GAAG,GAAGP,MAAM,CAACE,IAAI,CAACK,GAAG;UACzBlB,OAAO,CAAC,CAACC,KAAK,CAACW,GAAG,CAAC,EAAE,2CAA2C,CAAC;UACjEZ,OAAO,CAAC,CAACO,OAAO,CAACC,aAAa,CAACW,GAAG,CAACP,GAAG,CAAC,EAAE,kCAAkC,CAACQ,MAAM,CAACR,GAAG,CAAC,CAAC;UACxFZ,OAAO,CAAC,CAACkB,GAAG,IAAIG,MAAM,CAACH,GAAG,CAAC,KAAKG,MAAM,CAACT,GAAG,CAAC,EAAE,sFAAsF,CAACQ,MAAM,CAACF,GAAG,EAAE,WAAW,CAAC,CAACE,MAAM,CAACR,GAAG,EAAE,GAAG,CAAC,CAAC;QAChL;QAEAL,OAAO,CAACC,aAAa,CAACc,GAAG,CAACV,GAAG,EAAED,MAAM,CAAC;MACxC;IACF,CAAC,CAAC;IACF,OAAON,UAAU;EACnB,CAAC,EAAE,CAACH,QAAQ,EAAEC,UAAU,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}