{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar MenuDivider = function MenuDivider(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    dashed = _a.dashed,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"dashed\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('menu', customizePrefixCls);\n  var classString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-item-divider-dashed\"), !!dashed), className);\n  return /*#__PURE__*/React.createElement(Divider, _extends({\n    className: classString\n  }, restProps));\n};\nexport default MenuDivider;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "Divider", "React", "ConfigContext", "MenuDivider", "_a", "customizePrefixCls", "prefixCls", "className", "dashed", "restProps", "_React$useContext", "useContext", "getPrefixCls", "classString", "concat", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/menu/MenuDivider.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport { Divider } from 'rc-menu';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar MenuDivider = function MenuDivider(_a) {\n  var customizePrefixCls = _a.prefixCls,\n    className = _a.className,\n    dashed = _a.dashed,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"dashed\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('menu', customizePrefixCls);\n  var classString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-item-divider-dashed\"), !!dashed), className);\n  return /*#__PURE__*/React.createElement(Divider, _extends({\n    className: classString\n  }, restProps));\n};\nexport default MenuDivider;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAE;EACzC,IAAIC,kBAAkB,GAAGD,EAAE,CAACE,SAAS;IACnCC,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxBC,MAAM,GAAGJ,EAAE,CAACI,MAAM;IAClBC,SAAS,GAAGxB,MAAM,CAACmB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;EAC9D,IAAIM,iBAAiB,GAAGT,KAAK,CAACU,UAAU,CAACT,aAAa,CAAC;IACrDU,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIN,SAAS,GAAGM,YAAY,CAAC,MAAM,EAAEP,kBAAkB,CAAC;EACxD,IAAIQ,WAAW,GAAGd,UAAU,CAACf,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC8B,MAAM,CAACR,SAAS,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAACE,MAAM,CAAC,EAAED,SAAS,CAAC;EACpH,OAAO,aAAaN,KAAK,CAACc,aAAa,CAACf,OAAO,EAAEjB,QAAQ,CAAC;IACxDwB,SAAS,EAAEM;EACb,CAAC,EAAEJ,SAAS,CAAC,CAAC;AAChB,CAAC;AACD,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}