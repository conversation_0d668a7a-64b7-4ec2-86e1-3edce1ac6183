{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Typography,Card,Alert,Form,Input,Button,message,Space,Collapse,Table,Tag,Row,Col}from'antd';import{LockOutlined,UserAddOutlined,UserOutlined,EyeInvisibleOutlined,EyeTwoTone,TeamOutlined,SafetyOutlined,ExclamationCircleOutlined}from'@ant-design/icons';import{useSelector}from'react-redux';import{authAPI}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Panel}=Collapse;// const { confirm } = Modal;\nconst UserManagementPage=()=>{const[loading,setLoading]=useState(false);const[users,setUsers]=useState([]);const[changePasswordForm]=Form.useForm();const[addUserForm]=Form.useForm();// 从Redux store获取当前用户信息\nconst{user,token}=useSelector(state=>state.auth);const currentUser=(user===null||user===void 0?void 0:user.username)||'';const isAdmin=currentUser==='admin';// 获取用户列表（仅管理员）\nconst fetchUsers=useCallback(async()=>{if(!token)return;try{const response=await authAPI.getUsers(token);if(response.data){// 将用户对象转换为数组格式\nconst userList=Object.entries(response.data).map(_ref=>{let[username,userData]=_ref;return{username,is_admin:username==='admin',created_time:userData.created_time||'',last_login:userData.last_login||''};});setUsers(userList);}}catch(error){var _error$response,_error$response$data;console.error('获取用户列表失败:',error);message.error(`❌ 获取用户列表失败: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||error.message}`);}},[token]);// 获取当前用户信息\nuseEffect(()=>{if(isAdmin&&token){fetchUsers();}},[isAdmin,token,fetchUsers]);// 修改密码\nconst handleChangePassword=async values=>{if(!token)return;setLoading(true);try{const response=await authAPI.changePassword({username:currentUser,old_password:values.old_password,new_password:values.new_password,confirm_password:values.confirm_password},token);if(response.data.message){message.success('✅ 密码修改成功');changePasswordForm.resetFields();}}catch(error){var _error$response2,_error$response2$data;console.error('修改密码失败:',error);message.error(`❌ 修改密码失败: ${((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message}`);}finally{setLoading(false);}};// 添加用户（仅管理员）\nconst handleAddUser=async values=>{if(!token)return;setLoading(true);try{const response=await authAPI.addUser({username:currentUser,new_username:values.new_username,new_user_password:values.new_user_password,confirm_user_password:values.confirm_user_password},token);if(response.data.message){message.success('✅ 用户添加成功');addUserForm.resetFields();fetchUsers();// 刷新用户列表\n}}catch(error){var _error$response3,_error$response3$data;console.error('添加用户失败:',error);message.error(`❌ 添加用户失败: ${((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message}`);}finally{setLoading(false);}};// 密码强度验证\nconst validatePassword=(_,value)=>{if(!value){return Promise.reject(new Error('请输入密码'));}if(value.length<6){return Promise.reject(new Error('密码长度至少6位'));}if(!/(?=.*[a-zA-Z])(?=.*\\d)/.test(value)){return Promise.reject(new Error('密码必须包含字母和数字'));}return Promise.resolve();};// 确认密码验证\nconst validateConfirmPassword=(_,value)=>{const form=changePasswordForm||addUserForm;const passwordField=form===changePasswordForm?'new_password':'new_user_password';const password=form.getFieldValue(passwordField);if(!value){return Promise.reject(new Error('请确认密码'));}if(value!==password){return Promise.reject(new Error('两次输入的密码不一致'));}return Promise.resolve();};// 用户列表表格列定义\nconst userColumns=[{title:'用户名',dataIndex:'username',key:'username',render:username=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(UserOutlined,{}),/*#__PURE__*/_jsx(Text,{strong:true,children:username})]})},{title:'角色',dataIndex:'is_admin',key:'is_admin',render:isAdmin=>/*#__PURE__*/_jsx(Tag,{color:isAdmin?'red':'blue',children:isAdmin?'管理员':'普通用户'})},{title:'创建时间',dataIndex:'created_time',key:'created_time',render:time=>time?new Date(time).toLocaleString():'N/A'},{title:'最后登录',dataIndex:'last_login',key:'last_login',render:time=>time?new Date(time).toLocaleString():'从未登录'}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(TeamOutlined,{}),\"\\u7528\\u6237\\u7BA1\\u7406\"]})}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4FEE\\u6539\\u5BC6\\u7801\\u3001\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u7B49\\u7528\\u6237\\u7BA1\\u7406\\u529F\\u80FD\\u3002\"}),/*#__PURE__*/_jsxs(Row,{gutter:[24,24],style:{marginTop:24},children:[/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsxs(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(LockOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u4FEE\\u6539\\u5BC6\\u7801\"})]}),size:\"small\",children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",description:\"\\u4E3A\\u4E86\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u4FEE\\u6539\\u5BC6\\u7801\\u3002\\u65B0\\u5BC6\\u7801\\u5E94\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF0C\\u957F\\u5EA6\\u81F3\\u5C116\\u4F4D\\u3002\",type:\"info\",showIcon:true,style:{marginBottom:24}}),/*#__PURE__*/_jsxs(Form,{form:changePasswordForm,layout:\"vertical\",onFinish:handleChangePassword,style:{maxWidth:600},children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u5F53\\u524D\\u7528\\u6237\",name:\"current_user\",initialValue:currentUser,children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(UserOutlined,{}),disabled:true,value:currentUser})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u539F\\u5BC6\\u7801\",name:\"old_password\",rules:[{required:true,message:'请输入原密码'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u539F\\u5BC6\\u7801\",iconRender:visible=>visible?/*#__PURE__*/_jsx(EyeTwoTone,{}):/*#__PURE__*/_jsx(EyeInvisibleOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u65B0\\u5BC6\\u7801\",name:\"new_password\",rules:[{validator:validatePassword}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(SafetyOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",iconRender:visible=>visible?/*#__PURE__*/_jsx(EyeTwoTone,{}):/*#__PURE__*/_jsx(EyeInvisibleOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",name:\"confirm_password\",rules:[{validator:validateConfirmPassword}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(SafetyOutlined,{}),placeholder:\"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",iconRender:visible=>visible?/*#__PURE__*/_jsx(EyeTwoTone,{}):/*#__PURE__*/_jsx(EyeInvisibleOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(LockOutlined,{}),size:\"large\",children:\"\\u4FEE\\u6539\\u5BC6\\u7801\"})})]})]})}),isAdmin&&/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(TeamOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7528\\u6237\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Tag,{color:\"red\",children:\"\\u7BA1\\u7406\\u5458\\u4E13\\u7528\"})]}),size:\"small\",children:/*#__PURE__*/_jsxs(Collapse,{defaultActiveKey:['1'],ghost:true,children:[/*#__PURE__*/_jsx(Panel,{header:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(UserAddOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\"})]}),children:/*#__PURE__*/_jsxs(Form,{form:addUserForm,layout:\"vertical\",onFinish:handleAddUser,style:{maxWidth:600},children:[/*#__PURE__*/_jsx(Form.Item,{label:\"\\u65B0\\u7528\\u6237\\u540D\",name:\"new_username\",rules:[{required:true,message:'请输入用户名'},{min:3,message:'用户名至少3位'},{pattern:/^[a-zA-Z0-9_]+$/,message:'用户名只能包含字母、数字和下划线'}],children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(UserOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u540D\"})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",name:\"new_user_password\",rules:[{validator:validatePassword}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",iconRender:visible=>visible?/*#__PURE__*/_jsx(EyeTwoTone,{}):/*#__PURE__*/_jsx(EyeInvisibleOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{label:\"\\u786E\\u8BA4\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",name:\"confirm_user_password\",rules:[{validator:validateConfirmPassword}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(SafetyOutlined,{}),placeholder:\"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",iconRender:visible=>visible?/*#__PURE__*/_jsx(EyeTwoTone,{}):/*#__PURE__*/_jsx(EyeInvisibleOutlined,{})})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,icon:/*#__PURE__*/_jsx(UserAddOutlined,{}),size:\"large\",children:\"\\u6DFB\\u52A0\\u7528\\u6237\"})})]})},\"1\"),/*#__PURE__*/_jsxs(Panel,{header:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(TeamOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7528\\u6237\\u5217\\u8868\"}),/*#__PURE__*/_jsxs(Tag,{color:\"blue\",children:[users.length,\" \\u4E2A\\u7528\\u6237\"]})]}),children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16},children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(TeamOutlined,{}),onClick:fetchUsers,loading:loading,children:\"\\u5237\\u65B0\\u7528\\u6237\\u5217\\u8868\"})}),/*#__PURE__*/_jsx(Table,{columns:userColumns,dataSource:users,rowKey:\"username\",pagination:{pageSize:10,showSizeChanger:true,showTotal:total=>`共 ${total} 个用户`},size:\"small\"})]},\"2\")]})})}),!isAdmin&&/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Alert,{message:\"\\u6743\\u9650\\u63D0\\u793A\",description:\"\\u60A8\\u5F53\\u524D\\u662F\\u666E\\u901A\\u7528\\u6237\\uFF0C\\u53EA\\u80FD\\u4FEE\\u6539\\u81EA\\u5DF1\\u7684\\u5BC6\\u7801\\u3002\\u5982\\u9700\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u6216\\u67E5\\u770B\\u7528\\u6237\\u5217\\u8868\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u3002\",type:\"warning\",showIcon:true,icon:/*#__PURE__*/_jsx(ExclamationCircleOutlined,{})})})]})]});};export default UserManagementPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "<PERSON><PERSON>", "Form", "Input", "<PERSON><PERSON>", "message", "Space", "Collapse", "Table", "Tag", "Row", "Col", "LockOutlined", "UserAddOutlined", "UserOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "TeamOutlined", "SafetyOutlined", "ExclamationCircleOutlined", "useSelector", "authAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Panel", "UserManagementPage", "loading", "setLoading", "users", "setUsers", "changePasswordForm", "useForm", "addUserForm", "user", "token", "state", "auth", "currentUser", "username", "isAdmin", "fetchUsers", "response", "getUsers", "data", "userList", "Object", "entries", "map", "_ref", "userData", "is_admin", "created_time", "last_login", "error", "_error$response", "_error$response$data", "console", "detail", "handleChangePassword", "values", "changePassword", "old_password", "new_password", "confirm_password", "success", "resetFields", "_error$response2", "_error$response2$data", "handleAddUser", "addUser", "new_username", "new_user_password", "confirm_user_password", "_error$response3", "_error$response3$data", "validatePassword", "_", "value", "Promise", "reject", "Error", "length", "test", "resolve", "validateConfirmPassword", "form", "passwordField", "password", "getFieldValue", "userColumns", "title", "dataIndex", "key", "render", "children", "strong", "color", "time", "Date", "toLocaleString", "level", "style", "fontSize", "fontWeight", "marginBottom", "type", "gutter", "marginTop", "span", "size", "description", "showIcon", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "initialValue", "prefix", "disabled", "rules", "required", "Password", "placeholder", "iconRender", "visible", "validator", "htmlType", "icon", "defaultActiveKey", "ghost", "header", "min", "pattern", "onClick", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n  Alert,\n  Form,\n  Input,\n  Button,\n  message,\n  Space,\n\n  Collapse,\n  Table,\n  Tag,\n\n  Row,\n  Col\n} from 'antd';\nimport {\n  LockOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  TeamOutlined,\n  SafetyOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { authAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Panel } = Collapse;\n// const { confirm } = Modal;\n\ninterface User {\n  username: string;\n  is_admin: boolean;\n  created_time?: string;\n  last_login?: string;\n}\n\nconst UserManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState<User[]>([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const { user, token } = useSelector((state: RootState) => state.auth);\n  const currentUser = user?.username || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username, userData]: [string, any]) => ({\n          username,\n          is_admin: username === 'admin',\n          created_time: userData.created_time || '',\n          last_login: userData.last_login || '',\n        }));\n        setUsers(userList);\n      }\n    } catch (error: any) {\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error: any) {\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_: any, value: string) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 6) {\n      return Promise.reject(new Error('密码长度至少6位'));\n    }\n    if (!/(?=.*[a-zA-Z])(?=.*\\d)/.test(value)) {\n      return Promise.reject(new Error('密码必须包含字母和数字'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_: any, value: string) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (username: string) => (\n        <Space>\n          <UserOutlined />\n          <Text strong>{username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'is_admin',\n      key: 'is_admin',\n      render: (isAdmin: boolean) => (\n        <Tag color={isAdmin ? 'red' : 'blue'}>\n          {isAdmin ? '管理员' : '普通用户'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      render: (time: string) => time ? new Date(time).toLocaleString() : '从未登录',\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>\n        <Space>\n          <TeamOutlined />\n          用户管理\n        </Space>\n      </Title>\n      <Text type=\"secondary\">\n        修改密码、添加新用户等用户管理功能。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* 修改密码 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <LockOutlined />\n                <span>修改密码</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Alert\n              message=\"密码安全提示\"\n              description=\"为了账户安全，建议定期修改密码。新密码应包含字母和数字，长度至少6位。\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n\n            <Form\n              form={changePasswordForm}\n              layout=\"vertical\"\n              onFinish={handleChangePassword}\n              style={{ maxWidth: 600 }}\n            >\n              <Form.Item\n                label=\"当前用户\"\n                name=\"current_user\"\n                initialValue={currentUser}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  disabled\n                  value={currentUser}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"原密码\"\n                name=\"old_password\"\n                rules={[{ required: true, message: '请输入原密码' }]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"请输入原密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"新密码\"\n                name=\"new_password\"\n                rules={[{ validator: validatePassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请输入新密码（至少6位，包含字母和数字）\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"确认新密码\"\n                name=\"confirm_password\"\n                rules={[{ validator: validateConfirmPassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请再次输入新密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LockOutlined />}\n                  size=\"large\"\n                >\n                  修改密码\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 用户管理（仅管理员） */}\n        {isAdmin && (\n          <Col span={24}>\n            <Card\n              title={\n                <Space>\n                  <TeamOutlined />\n                  <span>用户管理</span>\n                  <Tag color=\"red\">管理员专用</Tag>\n                </Space>\n              }\n              size=\"small\"\n            >\n              <Collapse defaultActiveKey={['1']} ghost>\n                <Panel\n                  header={\n                    <Space>\n                      <UserAddOutlined />\n                      <span>添加新用户</span>\n                    </Space>\n                  }\n                  key=\"1\"\n                >\n                  <Form\n                    form={addUserForm}\n                    layout=\"vertical\"\n                    onFinish={handleAddUser}\n                    style={{ maxWidth: 600 }}\n                  >\n                    <Form.Item\n                      label=\"新用户名\"\n                      name=\"new_username\"\n                      rules={[\n                        { required: true, message: '请输入用户名' },\n                        { min: 3, message: '用户名至少3位' },\n                        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }\n                      ]}\n                    >\n                      <Input\n                        prefix={<UserOutlined />}\n                        placeholder=\"请输入新用户名\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"新用户密码\"\n                      name=\"new_user_password\"\n                      rules={[{ validator: validatePassword }]}\n                    >\n                      <Input.Password\n                        prefix={<LockOutlined />}\n                        placeholder=\"请输入新用户密码（至少6位，包含字母和数字）\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"确认新用户密码\"\n                      name=\"confirm_user_password\"\n                      rules={[{ validator: validateConfirmPassword }]}\n                    >\n                      <Input.Password\n                        prefix={<SafetyOutlined />}\n                        placeholder=\"请再次输入新用户密码\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        icon={<UserAddOutlined />}\n                        size=\"large\"\n                      >\n                        添加用户\n                      </Button>\n                    </Form.Item>\n                  </Form>\n                </Panel>\n\n                <Panel\n                  header={\n                    <Space>\n                      <TeamOutlined />\n                      <span>用户列表</span>\n                      <Tag color=\"blue\">{users.length} 个用户</Tag>\n                    </Space>\n                  }\n                  key=\"2\"\n                >\n                  <div style={{ marginBottom: 16 }}>\n                    <Button\n                      icon={<TeamOutlined />}\n                      onClick={fetchUsers}\n                      loading={loading}\n                    >\n                      刷新用户列表\n                    </Button>\n                  </div>\n\n                  <Table\n                    columns={userColumns}\n                    dataSource={users}\n                    rowKey=\"username\"\n                    pagination={{\n                      pageSize: 10,\n                      showSizeChanger: true,\n                      showTotal: (total) => `共 ${total} 个用户`,\n                    }}\n                    size=\"small\"\n                  />\n                </Panel>\n              </Collapse>\n            </Card>\n          </Col>\n        )}\n\n        {/* 非管理员提示 */}\n        {!isAdmin && (\n          <Col span={24}>\n            <Alert\n              message=\"权限提示\"\n              description=\"您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。\"\n              type=\"warning\"\n              showIcon\n              icon={<ExclamationCircleOutlined />}\n            />\n          </Col>\n        )}\n      </Row>\n    </div>\n  );\n};\n\nexport default UserManagementPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,UAAU,CACVC,IAAI,CACJC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,OAAO,CACPC,KAAK,CAELC,QAAQ,CACRC,KAAK,CACLC,GAAG,CAEHC,GAAG,CACHC,GAAG,KACE,MAAM,CACb,OACEC,YAAY,CACZC,eAAe,CACfC,YAAY,CACZC,oBAAoB,CACpBC,UAAU,CACVC,YAAY,CACZC,cAAc,CACdC,yBAAyB,KACpB,mBAAmB,CAC1B,OAASC,WAAW,KAAQ,aAAa,CAEzC,OAASC,OAAO,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG5B,UAAU,CAClC,KAAM,CAAE6B,KAAM,CAAC,CAAGrB,QAAQ,CAC1B;AASA,KAAM,CAAAsB,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACoC,KAAK,CAAEC,QAAQ,CAAC,CAAGrC,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACsC,kBAAkB,CAAC,CAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC,CAC3C,KAAM,CAACC,WAAW,CAAC,CAAGlC,IAAI,CAACiC,OAAO,CAAC,CAAC,CAEpC;AACA,KAAM,CAAEE,IAAI,CAAEC,KAAM,CAAC,CAAGlB,WAAW,CAAEmB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CACrE,KAAM,CAAAC,WAAW,CAAG,CAAAJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,QAAQ,GAAI,EAAE,CACxC,KAAM,CAAAC,OAAO,CAAGF,WAAW,GAAK,OAAO,CAEvC;AACA,KAAM,CAAAG,UAAU,CAAG9C,WAAW,CAAC,SAAY,CACzC,GAAI,CAACwC,KAAK,CAAE,OAEZ,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAxB,OAAO,CAACyB,QAAQ,CAACR,KAAK,CAAC,CAC9C,GAAIO,QAAQ,CAACE,IAAI,CAAE,CACjB;AACA,KAAM,CAAAC,QAAQ,CAAGC,MAAM,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,CAACI,GAAG,CAACC,IAAA,MAAC,CAACV,QAAQ,CAAEW,QAAQ,CAAgB,CAAAD,IAAA,OAAM,CAC3FV,QAAQ,CACRY,QAAQ,CAAEZ,QAAQ,GAAK,OAAO,CAC9Ba,YAAY,CAAEF,QAAQ,CAACE,YAAY,EAAI,EAAE,CACzCC,UAAU,CAAEH,QAAQ,CAACG,UAAU,EAAI,EACrC,CAAC,EAAC,CAAC,CACHvB,QAAQ,CAACe,QAAQ,CAAC,CACpB,CACF,CAAE,MAAOS,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnBC,OAAO,CAACH,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCpD,OAAO,CAACoD,KAAK,CAAC,eAAe,EAAAC,eAAA,CAAAD,KAAK,CAACZ,QAAQ,UAAAa,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBX,IAAI,UAAAY,oBAAA,iBAApBA,oBAAA,CAAsBE,MAAM,GAAIJ,KAAK,CAACpD,OAAO,EAAE,CAAC,CAC/E,CACF,CAAC,CAAE,CAACiC,KAAK,CAAC,CAAC,CAEX;AACAzC,SAAS,CAAC,IAAM,CACd,GAAI8C,OAAO,EAAIL,KAAK,CAAE,CACpBM,UAAU,CAAC,CAAC,CACd,CACF,CAAC,CAAE,CAACD,OAAO,CAAEL,KAAK,CAAEM,UAAU,CAAC,CAAC,CAEhC;AACA,KAAM,CAAAkB,oBAAoB,CAAG,KAAO,CAAAC,MAAW,EAAK,CAClD,GAAI,CAACzB,KAAK,CAAE,OAEZP,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAxB,OAAO,CAAC2C,cAAc,CAAC,CAC5CtB,QAAQ,CAAED,WAAW,CACrBwB,YAAY,CAAEF,MAAM,CAACE,YAAY,CACjCC,YAAY,CAAEH,MAAM,CAACG,YAAY,CACjCC,gBAAgB,CAAEJ,MAAM,CAACI,gBAC3B,CAAC,CAAE7B,KAAK,CAAC,CAET,GAAIO,QAAQ,CAACE,IAAI,CAAC1C,OAAO,CAAE,CACzBA,OAAO,CAAC+D,OAAO,CAAC,UAAU,CAAC,CAC3BlC,kBAAkB,CAACmC,WAAW,CAAC,CAAC,CAClC,CACF,CAAE,MAAOZ,KAAU,CAAE,KAAAa,gBAAA,CAAAC,qBAAA,CACnBX,OAAO,CAACH,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BpD,OAAO,CAACoD,KAAK,CAAC,aAAa,EAAAa,gBAAA,CAAAb,KAAK,CAACZ,QAAQ,UAAAyB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBvB,IAAI,UAAAwB,qBAAA,iBAApBA,qBAAA,CAAsBV,MAAM,GAAIJ,KAAK,CAACpD,OAAO,EAAE,CAAC,CAC7E,CAAC,OAAS,CACR0B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,aAAa,CAAG,KAAO,CAAAT,MAAW,EAAK,CAC3C,GAAI,CAACzB,KAAK,CAAE,OAEZP,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAxB,OAAO,CAACoD,OAAO,CAAC,CACrC/B,QAAQ,CAAED,WAAW,CACrBiC,YAAY,CAAEX,MAAM,CAACW,YAAY,CACjCC,iBAAiB,CAAEZ,MAAM,CAACY,iBAAiB,CAC3CC,qBAAqB,CAAEb,MAAM,CAACa,qBAChC,CAAC,CAAEtC,KAAK,CAAC,CAET,GAAIO,QAAQ,CAACE,IAAI,CAAC1C,OAAO,CAAE,CACzBA,OAAO,CAAC+D,OAAO,CAAC,UAAU,CAAC,CAC3BhC,WAAW,CAACiC,WAAW,CAAC,CAAC,CACzBzB,UAAU,CAAC,CAAC,CAAE;AAChB,CACF,CAAE,MAAOa,KAAU,CAAE,KAAAoB,gBAAA,CAAAC,qBAAA,CACnBlB,OAAO,CAACH,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BpD,OAAO,CAACoD,KAAK,CAAC,aAAa,EAAAoB,gBAAA,CAAApB,KAAK,CAACZ,QAAQ,UAAAgC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB9B,IAAI,UAAA+B,qBAAA,iBAApBA,qBAAA,CAAsBjB,MAAM,GAAIJ,KAAK,CAACpD,OAAO,EAAE,CAAC,CAC7E,CAAC,OAAS,CACR0B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAgD,gBAAgB,CAAGA,CAACC,CAAM,CAAEC,KAAa,GAAK,CAClD,GAAI,CAACA,KAAK,CAAE,CACV,MAAO,CAAAC,OAAO,CAACC,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAC3C,CACA,GAAIH,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CACpB,MAAO,CAAAH,OAAO,CAACC,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAC9C,CACA,GAAI,CAAC,wBAAwB,CAACE,IAAI,CAACL,KAAK,CAAC,CAAE,CACzC,MAAO,CAAAC,OAAO,CAACC,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CACjD,CACA,MAAO,CAAAF,OAAO,CAACK,OAAO,CAAC,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAACR,CAAM,CAAEC,KAAa,GAAK,CACzD,KAAM,CAAAQ,IAAI,CAAGvD,kBAAkB,EAAIE,WAAW,CAC9C,KAAM,CAAAsD,aAAa,CAAGD,IAAI,GAAKvD,kBAAkB,CAAG,cAAc,CAAG,mBAAmB,CACxF,KAAM,CAAAyD,QAAQ,CAAGF,IAAI,CAACG,aAAa,CAACF,aAAa,CAAC,CAElD,GAAI,CAACT,KAAK,CAAE,CACV,MAAO,CAAAC,OAAO,CAACC,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAC3C,CACA,GAAIH,KAAK,GAAKU,QAAQ,CAAE,CACtB,MAAO,CAAAT,OAAO,CAACC,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAChD,CACA,MAAO,CAAAF,OAAO,CAACK,OAAO,CAAC,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAM,WAAW,CAAG,CAClB,CACEC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,MAAM,CAAGvD,QAAgB,eACvBjB,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACT,YAAY,GAAE,CAAC,cAChBS,IAAA,CAACI,IAAI,EAACwE,MAAM,MAAAD,QAAA,CAAExD,QAAQ,CAAO,CAAC,EACzB,CAEX,CAAC,CACD,CACEoD,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,MAAM,CAAGtD,OAAgB,eACvBpB,IAAA,CAACd,GAAG,EAAC2F,KAAK,CAAEzD,OAAO,CAAG,KAAK,CAAG,MAAO,CAAAuD,QAAA,CAClCvD,OAAO,CAAG,KAAK,CAAG,MAAM,CACtB,CAET,CAAC,CACD,CACEmD,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,MAAM,CAAGI,IAAY,EAAKA,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,CAAG,KACrE,CAAC,CACD,CACET,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBC,MAAM,CAAGI,IAAY,EAAKA,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,CAAG,MACrE,CAAC,CACF,CAED,mBACE9E,KAAA,QAAAyE,QAAA,eACE3E,IAAA,CAACG,KAAK,EAAC8E,KAAK,CAAE,CAAE,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAV,QAAA,cACjFzE,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACN,YAAY,GAAE,CAAC,2BAElB,EAAO,CAAC,CACH,CAAC,cACRM,IAAA,CAACI,IAAI,EAACkF,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,8GAEvB,CAAM,CAAC,cAEPzE,KAAA,CAACf,GAAG,EAACoG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACL,KAAK,CAAE,CAAEM,SAAS,CAAE,EAAG,CAAE,CAAAb,QAAA,eAE9C3E,IAAA,CAACZ,GAAG,EAACqG,IAAI,CAAE,EAAG,CAAAd,QAAA,cACZzE,KAAA,CAACzB,IAAI,EACH8F,KAAK,cACHrE,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACX,YAAY,GAAE,CAAC,cAChBW,IAAA,SAAA2E,QAAA,CAAM,0BAAI,CAAM,CAAC,EACZ,CACR,CACDe,IAAI,CAAC,OAAO,CAAAf,QAAA,eAEZ3E,IAAA,CAACtB,KAAK,EACJI,OAAO,CAAC,sCAAQ,CAChB6G,WAAW,CAAC,+MAAqC,CACjDL,IAAI,CAAC,MAAM,CACXM,QAAQ,MACRV,KAAK,CAAE,CAAEG,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cAEFnF,KAAA,CAACvB,IAAI,EACHuF,IAAI,CAAEvD,kBAAmB,CACzBkF,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAEvD,oBAAqB,CAC/B2C,KAAK,CAAE,CAAEa,QAAQ,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEzB3E,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,cAAc,CACnBC,YAAY,CAAEjF,WAAY,CAAAyD,QAAA,cAE1B3E,IAAA,CAACpB,KAAK,EACJwH,MAAM,cAAEpG,IAAA,CAACT,YAAY,GAAE,CAAE,CACzB8G,QAAQ,MACR3C,KAAK,CAAExC,WAAY,CACpB,CAAC,CACO,CAAC,cAEZlB,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,oBAAK,CACXC,IAAI,CAAC,cAAc,CACnBI,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAEzH,OAAO,CAAE,QAAS,CAAC,CAAE,CAAA6F,QAAA,cAE/C3E,IAAA,CAACpB,KAAK,CAAC4H,QAAQ,EACbJ,MAAM,cAAEpG,IAAA,CAACX,YAAY,GAAE,CAAE,CACzBoH,WAAW,CAAC,sCAAQ,CACpBC,UAAU,CAAGC,OAAO,EAAMA,OAAO,cAAG3G,IAAA,CAACP,UAAU,GAAE,CAAC,cAAGO,IAAA,CAACR,oBAAoB,GAAE,CAAG,CAChF,CAAC,CACO,CAAC,cAEZQ,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,oBAAK,CACXC,IAAI,CAAC,cAAc,CACnBI,KAAK,CAAE,CAAC,CAAEM,SAAS,CAAEpD,gBAAiB,CAAC,CAAE,CAAAmB,QAAA,cAEzC3E,IAAA,CAACpB,KAAK,CAAC4H,QAAQ,EACbJ,MAAM,cAAEpG,IAAA,CAACL,cAAc,GAAE,CAAE,CAC3B8G,WAAW,CAAC,qHAAsB,CAClCC,UAAU,CAAGC,OAAO,EAAMA,OAAO,cAAG3G,IAAA,CAACP,UAAU,GAAE,CAAC,cAAGO,IAAA,CAACR,oBAAoB,GAAE,CAAG,CAChF,CAAC,CACO,CAAC,cAEZQ,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,gCAAO,CACbC,IAAI,CAAC,kBAAkB,CACvBI,KAAK,CAAE,CAAC,CAAEM,SAAS,CAAE3C,uBAAwB,CAAC,CAAE,CAAAU,QAAA,cAEhD3E,IAAA,CAACpB,KAAK,CAAC4H,QAAQ,EACbJ,MAAM,cAAEpG,IAAA,CAACL,cAAc,GAAE,CAAE,CAC3B8G,WAAW,CAAC,kDAAU,CACtBC,UAAU,CAAGC,OAAO,EAAMA,OAAO,cAAG3G,IAAA,CAACP,UAAU,GAAE,CAAC,cAAGO,IAAA,CAACR,oBAAoB,GAAE,CAAG,CAChF,CAAC,CACO,CAAC,cAEZQ,IAAA,CAACrB,IAAI,CAACqH,IAAI,EAAArB,QAAA,cACR3E,IAAA,CAACnB,MAAM,EACLyG,IAAI,CAAC,SAAS,CACduB,QAAQ,CAAC,QAAQ,CACjBtG,OAAO,CAAEA,OAAQ,CACjBuG,IAAI,cAAE9G,IAAA,CAACX,YAAY,GAAE,CAAE,CACvBqG,IAAI,CAAC,OAAO,CAAAf,QAAA,CACb,0BAED,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,EACH,CAAC,CACJ,CAAC,CAGLvD,OAAO,eACNpB,IAAA,CAACZ,GAAG,EAACqG,IAAI,CAAE,EAAG,CAAAd,QAAA,cACZ3E,IAAA,CAACvB,IAAI,EACH8F,KAAK,cACHrE,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACN,YAAY,GAAE,CAAC,cAChBM,IAAA,SAAA2E,QAAA,CAAM,0BAAI,CAAM,CAAC,cACjB3E,IAAA,CAACd,GAAG,EAAC2F,KAAK,CAAC,KAAK,CAAAF,QAAA,CAAC,gCAAK,CAAK,CAAC,EACvB,CACR,CACDe,IAAI,CAAC,OAAO,CAAAf,QAAA,cAEZzE,KAAA,CAAClB,QAAQ,EAAC+H,gBAAgB,CAAE,CAAC,GAAG,CAAE,CAACC,KAAK,MAAArC,QAAA,eACtC3E,IAAA,CAACK,KAAK,EACJ4G,MAAM,cACJ/G,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACV,eAAe,GAAE,CAAC,cACnBU,IAAA,SAAA2E,QAAA,CAAM,gCAAK,CAAM,CAAC,EACb,CACR,CAAAA,QAAA,cAGDzE,KAAA,CAACvB,IAAI,EACHuF,IAAI,CAAErD,WAAY,CAClBgF,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAE7C,aAAc,CACxBiC,KAAK,CAAE,CAAEa,QAAQ,CAAE,GAAI,CAAE,CAAApB,QAAA,eAEzB3E,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,0BAAM,CACZC,IAAI,CAAC,cAAc,CACnBI,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEzH,OAAO,CAAE,QAAS,CAAC,CACrC,CAAEoI,GAAG,CAAE,CAAC,CAAEpI,OAAO,CAAE,SAAU,CAAC,CAC9B,CAAEqI,OAAO,CAAE,iBAAiB,CAAErI,OAAO,CAAE,kBAAmB,CAAC,CAC3D,CAAA6F,QAAA,cAEF3E,IAAA,CAACpB,KAAK,EACJwH,MAAM,cAAEpG,IAAA,CAACT,YAAY,GAAE,CAAE,CACzBkH,WAAW,CAAC,4CAAS,CACtB,CAAC,CACO,CAAC,cAEZzG,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,gCAAO,CACbC,IAAI,CAAC,mBAAmB,CACxBI,KAAK,CAAE,CAAC,CAAEM,SAAS,CAAEpD,gBAAiB,CAAC,CAAE,CAAAmB,QAAA,cAEzC3E,IAAA,CAACpB,KAAK,CAAC4H,QAAQ,EACbJ,MAAM,cAAEpG,IAAA,CAACX,YAAY,GAAE,CAAE,CACzBoH,WAAW,CAAC,iIAAwB,CACpCC,UAAU,CAAGC,OAAO,EAAMA,OAAO,cAAG3G,IAAA,CAACP,UAAU,GAAE,CAAC,cAAGO,IAAA,CAACR,oBAAoB,GAAE,CAAG,CAChF,CAAC,CACO,CAAC,cAEZQ,IAAA,CAACrB,IAAI,CAACqH,IAAI,EACRC,KAAK,CAAC,4CAAS,CACfC,IAAI,CAAC,uBAAuB,CAC5BI,KAAK,CAAE,CAAC,CAAEM,SAAS,CAAE3C,uBAAwB,CAAC,CAAE,CAAAU,QAAA,cAEhD3E,IAAA,CAACpB,KAAK,CAAC4H,QAAQ,EACbJ,MAAM,cAAEpG,IAAA,CAACL,cAAc,GAAE,CAAE,CAC3B8G,WAAW,CAAC,8DAAY,CACxBC,UAAU,CAAGC,OAAO,EAAMA,OAAO,cAAG3G,IAAA,CAACP,UAAU,GAAE,CAAC,cAAGO,IAAA,CAACR,oBAAoB,GAAE,CAAG,CAChF,CAAC,CACO,CAAC,cAEZQ,IAAA,CAACrB,IAAI,CAACqH,IAAI,EAAArB,QAAA,cACR3E,IAAA,CAACnB,MAAM,EACLyG,IAAI,CAAC,SAAS,CACduB,QAAQ,CAAC,QAAQ,CACjBtG,OAAO,CAAEA,OAAQ,CACjBuG,IAAI,cAAE9G,IAAA,CAACV,eAAe,GAAE,CAAE,CAC1BoG,IAAI,CAAC,OAAO,CAAAf,QAAA,CACb,0BAED,CAAQ,CAAC,CACA,CAAC,EACR,CAAC,EA1DH,GA2DC,CAAC,cAERzE,KAAA,CAACG,KAAK,EACJ4G,MAAM,cACJ/G,KAAA,CAACnB,KAAK,EAAA4F,QAAA,eACJ3E,IAAA,CAACN,YAAY,GAAE,CAAC,cAChBM,IAAA,SAAA2E,QAAA,CAAM,0BAAI,CAAM,CAAC,cACjBzE,KAAA,CAAChB,GAAG,EAAC2F,KAAK,CAAC,MAAM,CAAAF,QAAA,EAAElE,KAAK,CAACqD,MAAM,CAAC,qBAAI,EAAK,CAAC,EACrC,CACR,CAAAa,QAAA,eAGD3E,IAAA,QAAKkF,KAAK,CAAE,CAAEG,YAAY,CAAE,EAAG,CAAE,CAAAV,QAAA,cAC/B3E,IAAA,CAACnB,MAAM,EACLiI,IAAI,cAAE9G,IAAA,CAACN,YAAY,GAAE,CAAE,CACvB0H,OAAO,CAAE/F,UAAW,CACpBd,OAAO,CAAEA,OAAQ,CAAAoE,QAAA,CAClB,sCAED,CAAQ,CAAC,CACN,CAAC,cAEN3E,IAAA,CAACf,KAAK,EACJoI,OAAO,CAAE/C,WAAY,CACrBgD,UAAU,CAAE7G,KAAM,CAClB8G,MAAM,CAAC,UAAU,CACjBC,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGC,KAAK,EAAK,KAAKA,KAAK,MAClC,CAAE,CACFlC,IAAI,CAAC,OAAO,CACb,CAAC,GAtBE,GAuBC,CAAC,EACA,CAAC,CACP,CAAC,CACJ,CACN,CAGA,CAACtE,OAAO,eACPpB,IAAA,CAACZ,GAAG,EAACqG,IAAI,CAAE,EAAG,CAAAd,QAAA,cACZ3E,IAAA,CAACtB,KAAK,EACJI,OAAO,CAAC,0BAAM,CACd6G,WAAW,CAAC,wPAA2C,CACvDL,IAAI,CAAC,SAAS,CACdM,QAAQ,MACRkB,IAAI,cAAE9G,IAAA,CAACJ,yBAAyB,GAAE,CAAE,CACrC,CAAC,CACC,CACN,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAU,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}