{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Pagination from '../pagination';\nimport { tuple } from '../_util/type';\nimport ListItem from './ListItem';\nexport var OmitProps = tuple('handleFilter', 'handleClear', 'checkedKeys');\nfunction parsePagination(pagination) {\n  if (!pagination) {\n    return null;\n  }\n  var defaultPagination = {\n    pageSize: 10,\n    simple: true,\n    showSizeChanger: false,\n    showLessItems: false\n  };\n  if (_typeof(pagination) === 'object') {\n    return _extends(_extends({}, defaultPagination), pagination);\n  }\n  return defaultPagination;\n}\nvar ListBody = /*#__PURE__*/function (_React$Component) {\n  _inherits(ListBody, _React$Component);\n  var _super = _createSuper(ListBody);\n  function ListBody() {\n    var _this;\n    _classCallCheck(this, ListBody);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      current: 1\n    };\n    _this.onItemSelect = function (item) {\n      var _this$props = _this.props,\n        onItemSelect = _this$props.onItemSelect,\n        selectedKeys = _this$props.selectedKeys;\n      var checked = selectedKeys.includes(item.key);\n      onItemSelect(item.key, !checked);\n    };\n    _this.onItemRemove = function (item) {\n      var onItemRemove = _this.props.onItemRemove;\n      onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove([item.key]);\n    };\n    _this.onPageChange = function (current) {\n      _this.setState({\n        current: current\n      });\n    };\n    _this.getItems = function () {\n      var current = _this.state.current;\n      var _this$props2 = _this.props,\n        pagination = _this$props2.pagination,\n        filteredRenderItems = _this$props2.filteredRenderItems;\n      var mergedPagination = parsePagination(pagination);\n      var displayItems = filteredRenderItems;\n      if (mergedPagination) {\n        displayItems = filteredRenderItems.slice((current - 1) * mergedPagination.pageSize, current * mergedPagination.pageSize);\n      }\n      return displayItems;\n    };\n    return _this;\n  }\n  _createClass(ListBody, [{\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var current = this.state.current;\n      var _this$props3 = this.props,\n        prefixCls = _this$props3.prefixCls,\n        onScroll = _this$props3.onScroll,\n        filteredRenderItems = _this$props3.filteredRenderItems,\n        selectedKeys = _this$props3.selectedKeys,\n        globalDisabled = _this$props3.disabled,\n        showRemove = _this$props3.showRemove,\n        pagination = _this$props3.pagination;\n      var mergedPagination = parsePagination(pagination);\n      var paginationNode = null;\n      if (mergedPagination) {\n        paginationNode = /*#__PURE__*/React.createElement(Pagination, {\n          simple: mergedPagination.simple,\n          showSizeChanger: mergedPagination.showSizeChanger,\n          showLessItems: mergedPagination.showLessItems,\n          size: \"small\",\n          disabled: globalDisabled,\n          className: \"\".concat(prefixCls, \"-pagination\"),\n          total: filteredRenderItems.length,\n          pageSize: mergedPagination.pageSize,\n          current: current,\n          onChange: this.onPageChange\n        });\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ul\", {\n        className: classNames(\"\".concat(prefixCls, \"-content\"), _defineProperty({}, \"\".concat(prefixCls, \"-content-show-remove\"), showRemove)),\n        onScroll: onScroll\n      }, this.getItems().map(function (_ref) {\n        var renderedEl = _ref.renderedEl,\n          renderedText = _ref.renderedText,\n          item = _ref.item;\n        var disabled = item.disabled;\n        var checked = selectedKeys.includes(item.key);\n        return /*#__PURE__*/React.createElement(ListItem, {\n          disabled: globalDisabled || disabled,\n          key: item.key,\n          item: item,\n          renderedText: renderedText,\n          renderedEl: renderedEl,\n          checked: checked,\n          prefixCls: prefixCls,\n          onClick: _this2.onItemSelect,\n          onRemove: _this2.onItemRemove,\n          showRemove: showRemove\n        });\n      })), paginationNode);\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(_ref2, _ref3) {\n      var filteredRenderItems = _ref2.filteredRenderItems,\n        pagination = _ref2.pagination;\n      var current = _ref3.current;\n      var mergedPagination = parsePagination(pagination);\n      if (mergedPagination) {\n        // Calculate the page number\n        var maxPageCount = Math.ceil(filteredRenderItems.length / mergedPagination.pageSize);\n        if (current > maxPageCount) {\n          return {\n            current: maxPageCount\n          };\n        }\n      }\n      return null;\n    }\n  }]);\n  return ListBody;\n}(React.Component);\nexport default ListBody;", "map": {"version": 3, "names": ["_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_extends", "_typeof", "classNames", "React", "Pagination", "tuple", "ListItem", "OmitProps", "parsePagination", "pagination", "defaultPagination", "pageSize", "simple", "showSizeChanger", "showLessItems", "ListBody", "_React$Component", "_super", "_this", "apply", "arguments", "state", "current", "onItemSelect", "item", "_this$props", "props", "<PERSON><PERSON><PERSON><PERSON>", "checked", "includes", "key", "onItemRemove", "onPageChange", "setState", "getItems", "_this$props2", "filteredRenderItems", "mergedPagination", "displayItems", "slice", "value", "render", "_this2", "_this$props3", "prefixCls", "onScroll", "globalDisabled", "disabled", "showRemove", "paginationNode", "createElement", "size", "className", "concat", "total", "length", "onChange", "Fragment", "map", "_ref", "renderedEl", "renderedText", "onClick", "onRemove", "getDerivedStateFromProps", "_ref2", "_ref3", "maxPageCount", "Math", "ceil", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/transfer/ListBody.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Pagination from '../pagination';\nimport { tuple } from '../_util/type';\nimport ListItem from './ListItem';\nexport var OmitProps = tuple('handleFilter', 'handleClear', 'checkedKeys');\nfunction parsePagination(pagination) {\n  if (!pagination) {\n    return null;\n  }\n  var defaultPagination = {\n    pageSize: 10,\n    simple: true,\n    showSizeChanger: false,\n    showLessItems: false\n  };\n  if (_typeof(pagination) === 'object') {\n    return _extends(_extends({}, defaultPagination), pagination);\n  }\n  return defaultPagination;\n}\nvar ListBody = /*#__PURE__*/function (_React$Component) {\n  _inherits(ListBody, _React$Component);\n  var _super = _createSuper(ListBody);\n  function ListBody() {\n    var _this;\n    _classCallCheck(this, ListBody);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      current: 1\n    };\n    _this.onItemSelect = function (item) {\n      var _this$props = _this.props,\n        onItemSelect = _this$props.onItemSelect,\n        selectedKeys = _this$props.selectedKeys;\n      var checked = selectedKeys.includes(item.key);\n      onItemSelect(item.key, !checked);\n    };\n    _this.onItemRemove = function (item) {\n      var onItemRemove = _this.props.onItemRemove;\n      onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove([item.key]);\n    };\n    _this.onPageChange = function (current) {\n      _this.setState({\n        current: current\n      });\n    };\n    _this.getItems = function () {\n      var current = _this.state.current;\n      var _this$props2 = _this.props,\n        pagination = _this$props2.pagination,\n        filteredRenderItems = _this$props2.filteredRenderItems;\n      var mergedPagination = parsePagination(pagination);\n      var displayItems = filteredRenderItems;\n      if (mergedPagination) {\n        displayItems = filteredRenderItems.slice((current - 1) * mergedPagination.pageSize, current * mergedPagination.pageSize);\n      }\n      return displayItems;\n    };\n    return _this;\n  }\n  _createClass(ListBody, [{\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var current = this.state.current;\n      var _this$props3 = this.props,\n        prefixCls = _this$props3.prefixCls,\n        onScroll = _this$props3.onScroll,\n        filteredRenderItems = _this$props3.filteredRenderItems,\n        selectedKeys = _this$props3.selectedKeys,\n        globalDisabled = _this$props3.disabled,\n        showRemove = _this$props3.showRemove,\n        pagination = _this$props3.pagination;\n      var mergedPagination = parsePagination(pagination);\n      var paginationNode = null;\n      if (mergedPagination) {\n        paginationNode = /*#__PURE__*/React.createElement(Pagination, {\n          simple: mergedPagination.simple,\n          showSizeChanger: mergedPagination.showSizeChanger,\n          showLessItems: mergedPagination.showLessItems,\n          size: \"small\",\n          disabled: globalDisabled,\n          className: \"\".concat(prefixCls, \"-pagination\"),\n          total: filteredRenderItems.length,\n          pageSize: mergedPagination.pageSize,\n          current: current,\n          onChange: this.onPageChange\n        });\n      }\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ul\", {\n        className: classNames(\"\".concat(prefixCls, \"-content\"), _defineProperty({}, \"\".concat(prefixCls, \"-content-show-remove\"), showRemove)),\n        onScroll: onScroll\n      }, this.getItems().map(function (_ref) {\n        var renderedEl = _ref.renderedEl,\n          renderedText = _ref.renderedText,\n          item = _ref.item;\n        var disabled = item.disabled;\n        var checked = selectedKeys.includes(item.key);\n        return /*#__PURE__*/React.createElement(ListItem, {\n          disabled: globalDisabled || disabled,\n          key: item.key,\n          item: item,\n          renderedText: renderedText,\n          renderedEl: renderedEl,\n          checked: checked,\n          prefixCls: prefixCls,\n          onClick: _this2.onItemSelect,\n          onRemove: _this2.onItemRemove,\n          showRemove: showRemove\n        });\n      })), paginationNode);\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(_ref2, _ref3) {\n      var filteredRenderItems = _ref2.filteredRenderItems,\n        pagination = _ref2.pagination;\n      var current = _ref3.current;\n      var mergedPagination = parsePagination(pagination);\n      if (mergedPagination) {\n        // Calculate the page number\n        var maxPageCount = Math.ceil(filteredRenderItems.length / mergedPagination.pageSize);\n        if (current > maxPageCount) {\n          return {\n            current: maxPageCount\n          };\n        }\n      }\n      return null;\n    }\n  }]);\n  return ListBody;\n}(React.Component);\nexport default ListBody;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,IAAIC,SAAS,GAAGF,KAAK,CAAC,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC;AAC1E,SAASG,eAAeA,CAACC,UAAU,EAAE;EACnC,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAIC,iBAAiB,GAAG;IACtBC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,KAAK;IACtBC,aAAa,EAAE;EACjB,CAAC;EACD,IAAIb,OAAO,CAACQ,UAAU,CAAC,KAAK,QAAQ,EAAE;IACpC,OAAOT,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEU,iBAAiB,CAAC,EAAED,UAAU,CAAC;EAC9D;EACA,OAAOC,iBAAiB;AAC1B;AACA,IAAIK,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtDlB,SAAS,CAACiB,QAAQ,EAAEC,gBAAgB,CAAC;EACrC,IAAIC,MAAM,GAAGlB,YAAY,CAACgB,QAAQ,CAAC;EACnC,SAASA,QAAQA,CAAA,EAAG;IAClB,IAAIG,KAAK;IACTtB,eAAe,CAAC,IAAI,EAAEmB,QAAQ,CAAC;IAC/BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrCF,KAAK,CAACG,KAAK,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;IACDJ,KAAK,CAACK,YAAY,GAAG,UAAUC,IAAI,EAAE;MACnC,IAAIC,WAAW,GAAGP,KAAK,CAACQ,KAAK;QAC3BH,YAAY,GAAGE,WAAW,CAACF,YAAY;QACvCI,YAAY,GAAGF,WAAW,CAACE,YAAY;MACzC,IAAIC,OAAO,GAAGD,YAAY,CAACE,QAAQ,CAACL,IAAI,CAACM,GAAG,CAAC;MAC7CP,YAAY,CAACC,IAAI,CAACM,GAAG,EAAE,CAACF,OAAO,CAAC;IAClC,CAAC;IACDV,KAAK,CAACa,YAAY,GAAG,UAAUP,IAAI,EAAE;MACnC,IAAIO,YAAY,GAAGb,KAAK,CAACQ,KAAK,CAACK,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAACP,IAAI,CAACM,GAAG,CAAC,CAAC;IACtF,CAAC;IACDZ,KAAK,CAACc,YAAY,GAAG,UAAUV,OAAO,EAAE;MACtCJ,KAAK,CAACe,QAAQ,CAAC;QACbX,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ,CAAC;IACDJ,KAAK,CAACgB,QAAQ,GAAG,YAAY;MAC3B,IAAIZ,OAAO,GAAGJ,KAAK,CAACG,KAAK,CAACC,OAAO;MACjC,IAAIa,YAAY,GAAGjB,KAAK,CAACQ,KAAK;QAC5BjB,UAAU,GAAG0B,YAAY,CAAC1B,UAAU;QACpC2B,mBAAmB,GAAGD,YAAY,CAACC,mBAAmB;MACxD,IAAIC,gBAAgB,GAAG7B,eAAe,CAACC,UAAU,CAAC;MAClD,IAAI6B,YAAY,GAAGF,mBAAmB;MACtC,IAAIC,gBAAgB,EAAE;QACpBC,YAAY,GAAGF,mBAAmB,CAACG,KAAK,CAAC,CAACjB,OAAO,GAAG,CAAC,IAAIe,gBAAgB,CAAC1B,QAAQ,EAAEW,OAAO,GAAGe,gBAAgB,CAAC1B,QAAQ,CAAC;MAC1H;MACA,OAAO2B,YAAY;IACrB,CAAC;IACD,OAAOpB,KAAK;EACd;EACArB,YAAY,CAACkB,QAAQ,EAAE,CAAC;IACtBe,GAAG,EAAE,QAAQ;IACbU,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIpB,OAAO,GAAG,IAAI,CAACD,KAAK,CAACC,OAAO;MAChC,IAAIqB,YAAY,GAAG,IAAI,CAACjB,KAAK;QAC3BkB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCT,mBAAmB,GAAGO,YAAY,CAACP,mBAAmB;QACtDT,YAAY,GAAGgB,YAAY,CAAChB,YAAY;QACxCmB,cAAc,GAAGH,YAAY,CAACI,QAAQ;QACtCC,UAAU,GAAGL,YAAY,CAACK,UAAU;QACpCvC,UAAU,GAAGkC,YAAY,CAAClC,UAAU;MACtC,IAAI4B,gBAAgB,GAAG7B,eAAe,CAACC,UAAU,CAAC;MAClD,IAAIwC,cAAc,GAAG,IAAI;MACzB,IAAIZ,gBAAgB,EAAE;QACpBY,cAAc,GAAG,aAAa9C,KAAK,CAAC+C,aAAa,CAAC9C,UAAU,EAAE;UAC5DQ,MAAM,EAAEyB,gBAAgB,CAACzB,MAAM;UAC/BC,eAAe,EAAEwB,gBAAgB,CAACxB,eAAe;UACjDC,aAAa,EAAEuB,gBAAgB,CAACvB,aAAa;UAC7CqC,IAAI,EAAE,OAAO;UACbJ,QAAQ,EAAED,cAAc;UACxBM,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,aAAa,CAAC;UAC9CU,KAAK,EAAElB,mBAAmB,CAACmB,MAAM;UACjC5C,QAAQ,EAAE0B,gBAAgB,CAAC1B,QAAQ;UACnCW,OAAO,EAAEA,OAAO;UAChBkC,QAAQ,EAAE,IAAI,CAACxB;QACjB,CAAC,CAAC;MACJ;MACA,OAAO,aAAa7B,KAAK,CAAC+C,aAAa,CAAC/C,KAAK,CAACsD,QAAQ,EAAE,IAAI,EAAE,aAAatD,KAAK,CAAC+C,aAAa,CAAC,IAAI,EAAE;QACnGE,SAAS,EAAElD,UAAU,CAAC,EAAE,CAACmD,MAAM,CAACT,SAAS,EAAE,UAAU,CAAC,EAAEjD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0D,MAAM,CAACT,SAAS,EAAE,sBAAsB,CAAC,EAAEI,UAAU,CAAC,CAAC;QACtIH,QAAQ,EAAEA;MACZ,CAAC,EAAE,IAAI,CAACX,QAAQ,CAAC,CAAC,CAACwB,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrC,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;UAC9BC,YAAY,GAAGF,IAAI,CAACE,YAAY;UAChCrC,IAAI,GAAGmC,IAAI,CAACnC,IAAI;QAClB,IAAIuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ;QAC5B,IAAInB,OAAO,GAAGD,YAAY,CAACE,QAAQ,CAACL,IAAI,CAACM,GAAG,CAAC;QAC7C,OAAO,aAAa3B,KAAK,CAAC+C,aAAa,CAAC5C,QAAQ,EAAE;UAChDyC,QAAQ,EAAED,cAAc,IAAIC,QAAQ;UACpCjB,GAAG,EAAEN,IAAI,CAACM,GAAG;UACbN,IAAI,EAAEA,IAAI;UACVqC,YAAY,EAAEA,YAAY;UAC1BD,UAAU,EAAEA,UAAU;UACtBhC,OAAO,EAAEA,OAAO;UAChBgB,SAAS,EAAEA,SAAS;UACpBkB,OAAO,EAAEpB,MAAM,CAACnB,YAAY;UAC5BwC,QAAQ,EAAErB,MAAM,CAACX,YAAY;UAC7BiB,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,EAAEC,cAAc,CAAC;IACtB;EACF,CAAC,CAAC,EAAE,CAAC;IACHnB,GAAG,EAAE,0BAA0B;IAC/BU,KAAK,EAAE,SAASwB,wBAAwBA,CAACC,KAAK,EAAEC,KAAK,EAAE;MACrD,IAAI9B,mBAAmB,GAAG6B,KAAK,CAAC7B,mBAAmB;QACjD3B,UAAU,GAAGwD,KAAK,CAACxD,UAAU;MAC/B,IAAIa,OAAO,GAAG4C,KAAK,CAAC5C,OAAO;MAC3B,IAAIe,gBAAgB,GAAG7B,eAAe,CAACC,UAAU,CAAC;MAClD,IAAI4B,gBAAgB,EAAE;QACpB;QACA,IAAI8B,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACjC,mBAAmB,CAACmB,MAAM,GAAGlB,gBAAgB,CAAC1B,QAAQ,CAAC;QACpF,IAAIW,OAAO,GAAG6C,YAAY,EAAE;UAC1B,OAAO;YACL7C,OAAO,EAAE6C;UACX,CAAC;QACH;MACF;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOpD,QAAQ;AACjB,CAAC,CAACZ,KAAK,CAACmE,SAAS,CAAC;AAClB,eAAevD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}