{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _options$getContainer = options.getContainer,\n    getContainer = _options$getContainer === void 0 ? function () {\n      return window;\n    } : _options$getContainer,\n    callback = options.callback,\n    _options$duration = options.duration,\n    duration = _options$duration === void 0 ? 450 : _options$duration;\n  var container = getContainer();\n  var scrollTop = getScroll(container, true);\n  var startTime = Date.now();\n  var frameFunc = function frameFunc() {\n    var timestamp = Date.now();\n    var time = timestamp - startTime;\n    var nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}", "map": {"version": 3, "names": ["raf", "easeInOutCubic", "getScroll", "isWindow", "scrollTo", "y", "options", "arguments", "length", "undefined", "_options$getContainer", "getContainer", "window", "callback", "_options$duration", "duration", "container", "scrollTop", "startTime", "Date", "now", "frameFunc", "timestamp", "time", "nextScrollTop", "pageXOffset", "Document", "constructor", "name", "documentElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/scrollTo.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _options$getContainer = options.getContainer,\n    getContainer = _options$getContainer === void 0 ? function () {\n      return window;\n    } : _options$getContainer,\n    callback = options.callback,\n    _options$duration = options.duration,\n    duration = _options$duration === void 0 ? 450 : _options$duration;\n  var container = getContainer();\n  var scrollTop = getScroll(container, true);\n  var startTime = Date.now();\n  var frameFunc = function frameFunc() {\n    var timestamp = Date.now();\n    var time = timestamp - startTime;\n    var nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,SAAS,IAAIC,QAAQ,QAAQ,aAAa;AACjD,eAAe,SAASC,QAAQA,CAACC,CAAC,EAAE;EAClC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIG,qBAAqB,GAAGJ,OAAO,CAACK,YAAY;IAC9CA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,YAAY;MAC5D,OAAOE,MAAM;IACf,CAAC,GAAGF,qBAAqB;IACzBG,QAAQ,GAAGP,OAAO,CAACO,QAAQ;IAC3BC,iBAAiB,GAAGR,OAAO,CAACS,QAAQ;IACpCA,QAAQ,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;EACnE,IAAIE,SAAS,GAAGL,YAAY,CAAC,CAAC;EAC9B,IAAIM,SAAS,GAAGf,SAAS,CAACc,SAAS,EAAE,IAAI,CAAC;EAC1C,IAAIE,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC1B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,IAAIG,IAAI,GAAGD,SAAS,GAAGJ,SAAS;IAChC,IAAIM,aAAa,GAAGvB,cAAc,CAACsB,IAAI,GAAGR,QAAQ,GAAGA,QAAQ,GAAGQ,IAAI,EAAEN,SAAS,EAAEZ,CAAC,EAAEU,QAAQ,CAAC;IAC7F,IAAIZ,QAAQ,CAACa,SAAS,CAAC,EAAE;MACvBA,SAAS,CAACZ,QAAQ,CAACQ,MAAM,CAACa,WAAW,EAAED,aAAa,CAAC;IACvD,CAAC,MAAM,IAAIR,SAAS,YAAYU,QAAQ,IAAIV,SAAS,CAACW,WAAW,CAACC,IAAI,KAAK,cAAc,EAAE;MACzFZ,SAAS,CAACa,eAAe,CAACZ,SAAS,GAAGO,aAAa;IACrD,CAAC,MAAM;MACLR,SAAS,CAACC,SAAS,GAAGO,aAAa;IACrC;IACA,IAAID,IAAI,GAAGR,QAAQ,EAAE;MACnBf,GAAG,CAACqB,SAAS,CAAC;IAChB,CAAC,MAAM,IAAI,OAAOR,QAAQ,KAAK,UAAU,EAAE;MACzCA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDb,GAAG,CAACqB,SAAS,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}