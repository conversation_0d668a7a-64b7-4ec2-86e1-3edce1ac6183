{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nvar uuid = 0;\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && canUseDom();\n/** Get unique id for accessibility usage */\nfunction getUUID() {\n  var retId;\n  // Test never reach\n  /* istanbul ignore if */\n  if (isBrowserClient) {\n    retId = uuid;\n    uuid += 1;\n  } else {\n    retId = 'TEST_OR_SSR';\n  }\n  return retId;\n}\nexport default (function (id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    setInnerId(\"rc_progress_\".concat(getUUID()));\n  }, []);\n  return id || innerId;\n});", "map": {"version": 3, "names": ["_slicedToArray", "React", "canUseDom", "uuid", "isBrowserClient", "process", "env", "NODE_ENV", "getUUID", "retId", "id", "_React$useState", "useState", "_React$useState2", "innerId", "setInnerId", "useEffect", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-progress/es/hooks/useId.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nvar uuid = 0;\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && canUseDom();\n/** Get unique id for accessibility usage */\nfunction getUUID() {\n  var retId;\n  // Test never reach\n  /* istanbul ignore if */\n  if (isBrowserClient) {\n    retId = uuid;\n    uuid += 1;\n  } else {\n    retId = 'TEST_OR_SSR';\n  }\n  return retId;\n}\nexport default (function (id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    setInnerId(\"rc_progress_\".concat(getUUID()));\n  }, []);\n  return id || innerId;\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD,IAAIC,IAAI,GAAG,CAAC;AACZ;AACA,OAAO,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIL,SAAS,CAAC,CAAC;AAC3E;AACA,SAASM,OAAOA,CAAA,EAAG;EACjB,IAAIC,KAAK;EACT;EACA;EACA,IAAIL,eAAe,EAAE;IACnBK,KAAK,GAAGN,IAAI;IACZA,IAAI,IAAI,CAAC;EACX,CAAC,MAAM;IACLM,KAAK,GAAG,aAAa;EACvB;EACA,OAAOA,KAAK;AACd;AACA,gBAAgB,UAAUC,EAAE,EAAE;EAC5B;EACA,IAAIC,eAAe,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC;IACpCC,gBAAgB,GAAGb,cAAc,CAACW,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClCZ,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1BD,UAAU,CAAC,cAAc,CAACE,MAAM,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,EAAE,EAAE,CAAC;EACN,OAAOE,EAAE,IAAII,OAAO;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}