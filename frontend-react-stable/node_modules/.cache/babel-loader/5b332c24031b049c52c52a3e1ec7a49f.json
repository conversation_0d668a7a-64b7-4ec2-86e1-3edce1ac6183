{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport Ribbon from './Ribbon';\nimport ScrollNumber from './ScrollNumber';\nimport { isPresetColor } from './utils';\nvar Badge = function Badge(_a) {\n  var _classNames, _classNames2;\n  var customizePrefixCls = _a.prefixCls,\n    customizeScrollNumberPrefixCls = _a.scrollNumberPrefixCls,\n    children = _a.children,\n    status = _a.status,\n    text = _a.text,\n    color = _a.color,\n    _a$count = _a.count,\n    count = _a$count === void 0 ? null : _a$count,\n    _a$overflowCount = _a.overflowCount,\n    overflowCount = _a$overflowCount === void 0 ? 99 : _a$overflowCount,\n    _a$dot = _a.dot,\n    dot = _a$dot === void 0 ? false : _a$dot,\n    _a$size = _a.size,\n    size = _a$size === void 0 ? 'default' : _a$size,\n    title = _a.title,\n    offset = _a.offset,\n    style = _a.style,\n    className = _a.className,\n    _a$showZero = _a.showZero,\n    showZero = _a$showZero === void 0 ? false : _a$showZero,\n    restProps = __rest(_a, [\"prefixCls\", \"scrollNumberPrefixCls\", \"children\", \"status\", \"text\", \"color\", \"count\", \"overflowCount\", \"dot\", \"size\", \"title\", \"offset\", \"style\", \"className\", \"showZero\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('badge', customizePrefixCls);\n  // ================================ Misc ================================\n  var numberedDisplayCount = count > overflowCount ? \"\".concat(overflowCount, \"+\") : count;\n  var isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;\n  var ignoreCount = count === null || isZero;\n  var hasStatus = (status !== null && status !== undefined || color !== null && color !== undefined) && ignoreCount;\n  var showAsDot = dot && !isZero;\n  var mergedCount = showAsDot ? '' : numberedDisplayCount;\n  var isHidden = useMemo(function () {\n    var isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';\n    return (isEmpty || isZero && !showZero) && !showAsDot;\n  }, [mergedCount, isZero, showZero, showAsDot]);\n  // Count should be cache in case hidden change it\n  var countRef = useRef(count);\n  if (!isHidden) {\n    countRef.current = count;\n  }\n  var livingCount = countRef.current;\n  // We need cache count since remove motion should not change count display\n  var displayCountRef = useRef(mergedCount);\n  if (!isHidden) {\n    displayCountRef.current = mergedCount;\n  }\n  var displayCount = displayCountRef.current;\n  // We will cache the dot status to avoid shaking on leaved motion\n  var isDotRef = useRef(showAsDot);\n  if (!isHidden) {\n    isDotRef.current = showAsDot;\n  }\n  // =============================== Styles ===============================\n  var mergedStyle = useMemo(function () {\n    if (!offset) {\n      return _extends({}, style);\n    }\n    var offsetStyle = {\n      marginTop: offset[1]\n    };\n    if (direction === 'rtl') {\n      offsetStyle.left = parseInt(offset[0], 10);\n    } else {\n      offsetStyle.right = -parseInt(offset[0], 10);\n    }\n    return _extends(_extends({}, offsetStyle), style);\n  }, [direction, offset, style]);\n  // =============================== Render ===============================\n  // >>> Title\n  var titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined;\n  // >>> Status Text\n  var statusTextNode = isHidden || !text ? null : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-status-text\")\n  }, text);\n  // >>> Display Component\n  var displayNode = !livingCount || _typeof(livingCount) !== 'object' ? undefined : cloneElement(livingCount, function (oriProps) {\n    return {\n      style: _extends(_extends({}, mergedStyle), oriProps.style)\n    };\n  });\n  // Shared styles\n  var statusCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-dot\"), hasStatus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames));\n  var statusStyle = {};\n  if (color && !isPresetColor(color)) {\n    statusStyle.background = color;\n  }\n  var badgeClassName = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-status\"), hasStatus), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-not-a-wrapper\"), !children), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2), className);\n  // <Badge status=\"success\" />\n  if (!children && hasStatus) {\n    var statusTextColor = mergedStyle.color;\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n      className: badgeClassName,\n      style: mergedStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: statusCls,\n      style: statusStyle\n    }), text && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        color: statusTextColor\n      },\n      className: \"\".concat(prefixCls, \"-status-text\")\n    }, text));\n  }\n  // <Badge status=\"success\" count={<Icon type=\"xxx\" />}></Badge>\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: badgeClassName\n  }), children, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !isHidden,\n    motionName: \"\".concat(prefixCls, \"-zoom\"),\n    motionAppear: false,\n    motionDeadline: 1000\n  }, function (_ref) {\n    var _classNames3;\n    var motionClassName = _ref.className;\n    var scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);\n    var isDot = isDotRef.current;\n    var scrollNumberCls = classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-dot\"), isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count\"), !isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count-sm\"), size === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-multiple-words\"), !isDot && displayCount && displayCount.toString().length > 1), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames3));\n    var scrollNumberStyle = _extends({}, mergedStyle);\n    if (color && !isPresetColor(color)) {\n      scrollNumberStyle = scrollNumberStyle || {};\n      scrollNumberStyle.background = color;\n    }\n    return /*#__PURE__*/React.createElement(ScrollNumber, {\n      prefixCls: scrollNumberPrefixCls,\n      show: !isHidden,\n      motionClassName: motionClassName,\n      className: scrollNumberCls,\n      count: displayCount,\n      title: titleNode,\n      style: scrollNumberStyle,\n      key: \"scrollNumber\"\n    }, displayNode);\n  }), statusTextNode);\n};\nBadge.Ribbon = Ribbon;\nexport default Badge;", "map": {"version": 3, "names": ["_defineProperty", "_typeof", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "CSSMotion", "React", "useMemo", "useRef", "ConfigContext", "cloneElement", "Ribbon", "ScrollNumber", "isPresetColor", "Badge", "_a", "_classNames", "_classNames2", "customizePrefixCls", "prefixCls", "customizeScrollNumberPrefixCls", "scrollNumberPrefixCls", "children", "status", "text", "color", "_a$count", "count", "_a$overflowCount", "overflowCount", "_a$dot", "dot", "_a$size", "size", "title", "offset", "style", "className", "_a$showZero", "showZero", "restProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "numberedDisplayCount", "concat", "isZero", "ignoreCount", "hasStatus", "undefined", "showAsDot", "mergedCount", "isHidden", "isEmpty", "countRef", "current", "livingCount", "displayCountRef", "displayCount", "isDotRef", "mergedStyle", "offsetStyle", "marginTop", "left", "parseInt", "right", "titleNode", "statusTextNode", "createElement", "displayNode", "oriProps", "statusCls", "statusStyle", "background", "badgeClassName", "statusTextColor", "visible", "motionName", "motionAppear", "motionDeadline", "_ref", "_classNames3", "motionClassName", "isDot", "scrollNumberCls", "toString", "scrollNumberStyle", "show", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/badge/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport { useMemo, useRef } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { cloneElement } from '../_util/reactNode';\nimport Ribbon from './Ribbon';\nimport ScrollNumber from './ScrollNumber';\nimport { isPresetColor } from './utils';\nvar Badge = function Badge(_a) {\n  var _classNames, _classNames2;\n  var customizePrefixCls = _a.prefixCls,\n    customizeScrollNumberPrefixCls = _a.scrollNumberPrefixCls,\n    children = _a.children,\n    status = _a.status,\n    text = _a.text,\n    color = _a.color,\n    _a$count = _a.count,\n    count = _a$count === void 0 ? null : _a$count,\n    _a$overflowCount = _a.overflowCount,\n    overflowCount = _a$overflowCount === void 0 ? 99 : _a$overflowCount,\n    _a$dot = _a.dot,\n    dot = _a$dot === void 0 ? false : _a$dot,\n    _a$size = _a.size,\n    size = _a$size === void 0 ? 'default' : _a$size,\n    title = _a.title,\n    offset = _a.offset,\n    style = _a.style,\n    className = _a.className,\n    _a$showZero = _a.showZero,\n    showZero = _a$showZero === void 0 ? false : _a$showZero,\n    restProps = __rest(_a, [\"prefixCls\", \"scrollNumberPrefixCls\", \"children\", \"status\", \"text\", \"color\", \"count\", \"overflowCount\", \"dot\", \"size\", \"title\", \"offset\", \"style\", \"className\", \"showZero\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var prefixCls = getPrefixCls('badge', customizePrefixCls);\n  // ================================ Misc ================================\n  var numberedDisplayCount = count > overflowCount ? \"\".concat(overflowCount, \"+\") : count;\n  var isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;\n  var ignoreCount = count === null || isZero;\n  var hasStatus = (status !== null && status !== undefined || color !== null && color !== undefined) && ignoreCount;\n  var showAsDot = dot && !isZero;\n  var mergedCount = showAsDot ? '' : numberedDisplayCount;\n  var isHidden = useMemo(function () {\n    var isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';\n    return (isEmpty || isZero && !showZero) && !showAsDot;\n  }, [mergedCount, isZero, showZero, showAsDot]);\n  // Count should be cache in case hidden change it\n  var countRef = useRef(count);\n  if (!isHidden) {\n    countRef.current = count;\n  }\n  var livingCount = countRef.current;\n  // We need cache count since remove motion should not change count display\n  var displayCountRef = useRef(mergedCount);\n  if (!isHidden) {\n    displayCountRef.current = mergedCount;\n  }\n  var displayCount = displayCountRef.current;\n  // We will cache the dot status to avoid shaking on leaved motion\n  var isDotRef = useRef(showAsDot);\n  if (!isHidden) {\n    isDotRef.current = showAsDot;\n  }\n  // =============================== Styles ===============================\n  var mergedStyle = useMemo(function () {\n    if (!offset) {\n      return _extends({}, style);\n    }\n    var offsetStyle = {\n      marginTop: offset[1]\n    };\n    if (direction === 'rtl') {\n      offsetStyle.left = parseInt(offset[0], 10);\n    } else {\n      offsetStyle.right = -parseInt(offset[0], 10);\n    }\n    return _extends(_extends({}, offsetStyle), style);\n  }, [direction, offset, style]);\n  // =============================== Render ===============================\n  // >>> Title\n  var titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined;\n  // >>> Status Text\n  var statusTextNode = isHidden || !text ? null : /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-status-text\")\n  }, text);\n  // >>> Display Component\n  var displayNode = !livingCount || _typeof(livingCount) !== 'object' ? undefined : cloneElement(livingCount, function (oriProps) {\n    return {\n      style: _extends(_extends({}, mergedStyle), oriProps.style)\n    };\n  });\n  // Shared styles\n  var statusCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-dot\"), hasStatus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames));\n  var statusStyle = {};\n  if (color && !isPresetColor(color)) {\n    statusStyle.background = color;\n  }\n  var badgeClassName = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-status\"), hasStatus), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-not-a-wrapper\"), !children), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames2), className);\n  // <Badge status=\"success\" />\n  if (!children && hasStatus) {\n    var statusTextColor = mergedStyle.color;\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n      className: badgeClassName,\n      style: mergedStyle\n    }), /*#__PURE__*/React.createElement(\"span\", {\n      className: statusCls,\n      style: statusStyle\n    }), text && /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        color: statusTextColor\n      },\n      className: \"\".concat(prefixCls, \"-status-text\")\n    }, text));\n  }\n  // <Badge status=\"success\" count={<Icon type=\"xxx\" />}></Badge>\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, restProps, {\n    className: badgeClassName\n  }), children, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: !isHidden,\n    motionName: \"\".concat(prefixCls, \"-zoom\"),\n    motionAppear: false,\n    motionDeadline: 1000\n  }, function (_ref) {\n    var _classNames3;\n    var motionClassName = _ref.className;\n    var scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);\n    var isDot = isDotRef.current;\n    var scrollNumberCls = classNames((_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-dot\"), isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count\"), !isDot), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-count-sm\"), size === 'small'), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-multiple-words\"), !isDot && displayCount && displayCount.toString().length > 1), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(status), !!status), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-status-\").concat(color), isPresetColor(color)), _classNames3));\n    var scrollNumberStyle = _extends({}, mergedStyle);\n    if (color && !isPresetColor(color)) {\n      scrollNumberStyle = scrollNumberStyle || {};\n      scrollNumberStyle.background = color;\n    }\n    return /*#__PURE__*/React.createElement(ScrollNumber, {\n      prefixCls: scrollNumberPrefixCls,\n      show: !isHidden,\n      motionClassName: motionClassName,\n      className: scrollNumberCls,\n      count: displayCount,\n      title: titleNode,\n      style: scrollNumberStyle,\n      key: \"scrollNumber\"\n    }, displayNode);\n  }), statusTextNode);\n};\nBadge.Ribbon = Ribbon;\nexport default Badge;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,aAAa,QAAQ,SAAS;AACvC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;EAC7B,IAAIC,WAAW,EAAEC,YAAY;EAC7B,IAAIC,kBAAkB,GAAGH,EAAE,CAACI,SAAS;IACnCC,8BAA8B,GAAGL,EAAE,CAACM,qBAAqB;IACzDC,QAAQ,GAAGP,EAAE,CAACO,QAAQ;IACtBC,MAAM,GAAGR,EAAE,CAACQ,MAAM;IAClBC,IAAI,GAAGT,EAAE,CAACS,IAAI;IACdC,KAAK,GAAGV,EAAE,CAACU,KAAK;IAChBC,QAAQ,GAAGX,EAAE,CAACY,KAAK;IACnBA,KAAK,GAAGD,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,QAAQ;IAC7CE,gBAAgB,GAAGb,EAAE,CAACc,aAAa;IACnCA,aAAa,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IACnEE,MAAM,GAAGf,EAAE,CAACgB,GAAG;IACfA,GAAG,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,MAAM;IACxCE,OAAO,GAAGjB,EAAE,CAACkB,IAAI;IACjBA,IAAI,GAAGD,OAAO,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,OAAO;IAC/CE,KAAK,GAAGnB,EAAE,CAACmB,KAAK;IAChBC,MAAM,GAAGpB,EAAE,CAACoB,MAAM;IAClBC,KAAK,GAAGrB,EAAE,CAACqB,KAAK;IAChBC,SAAS,GAAGtB,EAAE,CAACsB,SAAS;IACxBC,WAAW,GAAGvB,EAAE,CAACwB,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,WAAW;IACvDE,SAAS,GAAGlD,MAAM,CAACyB,EAAE,EAAE,CAAC,WAAW,EAAE,uBAAuB,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EACrM,IAAI0B,iBAAiB,GAAGnC,KAAK,CAACoC,UAAU,CAACjC,aAAa,CAAC;IACrDkC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIzB,SAAS,GAAGwB,YAAY,CAAC,OAAO,EAAEzB,kBAAkB,CAAC;EACzD;EACA,IAAI2B,oBAAoB,GAAGlB,KAAK,GAAGE,aAAa,GAAG,EAAE,CAACiB,MAAM,CAACjB,aAAa,EAAE,GAAG,CAAC,GAAGF,KAAK;EACxF,IAAIoB,MAAM,GAAGF,oBAAoB,KAAK,GAAG,IAAIA,oBAAoB,KAAK,CAAC;EACvE,IAAIG,WAAW,GAAGrB,KAAK,KAAK,IAAI,IAAIoB,MAAM;EAC1C,IAAIE,SAAS,GAAG,CAAC1B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK2B,SAAS,IAAIzB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKyB,SAAS,KAAKF,WAAW;EACjH,IAAIG,SAAS,GAAGpB,GAAG,IAAI,CAACgB,MAAM;EAC9B,IAAIK,WAAW,GAAGD,SAAS,GAAG,EAAE,GAAGN,oBAAoB;EACvD,IAAIQ,QAAQ,GAAG9C,OAAO,CAAC,YAAY;IACjC,IAAI+C,OAAO,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKF,SAAS,IAAIE,WAAW,KAAK,EAAE;IACrF,OAAO,CAACE,OAAO,IAAIP,MAAM,IAAI,CAACR,QAAQ,KAAK,CAACY,SAAS;EACvD,CAAC,EAAE,CAACC,WAAW,EAAEL,MAAM,EAAER,QAAQ,EAAEY,SAAS,CAAC,CAAC;EAC9C;EACA,IAAII,QAAQ,GAAG/C,MAAM,CAACmB,KAAK,CAAC;EAC5B,IAAI,CAAC0B,QAAQ,EAAE;IACbE,QAAQ,CAACC,OAAO,GAAG7B,KAAK;EAC1B;EACA,IAAI8B,WAAW,GAAGF,QAAQ,CAACC,OAAO;EAClC;EACA,IAAIE,eAAe,GAAGlD,MAAM,CAAC4C,WAAW,CAAC;EACzC,IAAI,CAACC,QAAQ,EAAE;IACbK,eAAe,CAACF,OAAO,GAAGJ,WAAW;EACvC;EACA,IAAIO,YAAY,GAAGD,eAAe,CAACF,OAAO;EAC1C;EACA,IAAII,QAAQ,GAAGpD,MAAM,CAAC2C,SAAS,CAAC;EAChC,IAAI,CAACE,QAAQ,EAAE;IACbO,QAAQ,CAACJ,OAAO,GAAGL,SAAS;EAC9B;EACA;EACA,IAAIU,WAAW,GAAGtD,OAAO,CAAC,YAAY;IACpC,IAAI,CAAC4B,MAAM,EAAE;MACX,OAAO9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,CAAC;IAC5B;IACA,IAAI0B,WAAW,GAAG;MAChBC,SAAS,EAAE5B,MAAM,CAAC,CAAC;IACrB,CAAC;IACD,IAAIS,SAAS,KAAK,KAAK,EAAE;MACvBkB,WAAW,CAACE,IAAI,GAAGC,QAAQ,CAAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5C,CAAC,MAAM;MACL2B,WAAW,CAACI,KAAK,GAAG,CAACD,QAAQ,CAAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9C;IACA,OAAO9C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyE,WAAW,CAAC,EAAE1B,KAAK,CAAC;EACnD,CAAC,EAAE,CAACQ,SAAS,EAAET,MAAM,EAAEC,KAAK,CAAC,CAAC;EAC9B;EACA;EACA,IAAI+B,SAAS,GAAGjC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,OAAOuB,WAAW,KAAK,QAAQ,IAAI,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGP,SAAS;EACzJ;EACA,IAAIkB,cAAc,GAAGf,QAAQ,IAAI,CAAC7B,IAAI,GAAG,IAAI,GAAG,aAAalB,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAE;IACvFhC,SAAS,EAAE,EAAE,CAACS,MAAM,CAAC3B,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEK,IAAI,CAAC;EACR;EACA,IAAI8C,WAAW,GAAG,CAACb,WAAW,IAAIrE,OAAO,CAACqE,WAAW,CAAC,KAAK,QAAQ,GAAGP,SAAS,GAAGxC,YAAY,CAAC+C,WAAW,EAAE,UAAUc,QAAQ,EAAE;IAC9H,OAAO;MACLnC,KAAK,EAAE/C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwE,WAAW,CAAC,EAAEU,QAAQ,CAACnC,KAAK;IAC3D,CAAC;EACH,CAAC,CAAC;EACF;EACA,IAAIoC,SAAS,GAAGpE,UAAU,EAAEY,WAAW,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,aAAa,CAAC,EAAE8B,SAAS,CAAC,EAAE9D,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACvB,MAAM,CAAC,EAAE,CAAC,CAACA,MAAM,CAAC,EAAEpC,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAAC8B,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACrB,KAAK,CAAC,EAAEZ,aAAa,CAACY,KAAK,CAAC,CAAC,EAAET,WAAW,CAAC,CAAC;EACtU,IAAIyD,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIhD,KAAK,IAAI,CAACZ,aAAa,CAACY,KAAK,CAAC,EAAE;IAClCgD,WAAW,CAACC,UAAU,GAAGjD,KAAK;EAChC;EACA,IAAIkD,cAAc,GAAGvE,UAAU,CAACe,SAAS,GAAGF,YAAY,GAAG,CAAC,CAAC,EAAE9B,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,SAAS,CAAC,EAAE8B,SAAS,CAAC,EAAE9D,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACG,QAAQ,CAAC,EAAEnC,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAAC6B,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEyB,SAAS,KAAK,KAAK,CAAC,EAAE3B,YAAY,GAAGoB,SAAS,CAAC;EACvU;EACA,IAAI,CAACf,QAAQ,IAAI2B,SAAS,EAAE;IAC1B,IAAI2B,eAAe,GAAGf,WAAW,CAACpC,KAAK;IACvC,OAAO,aAAanB,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAEmD,SAAS,EAAE;MACtEH,SAAS,EAAEsC,cAAc;MACzBvC,KAAK,EAAEyB;IACT,CAAC,CAAC,EAAE,aAAavD,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAE;MAC3ChC,SAAS,EAAEmC,SAAS;MACpBpC,KAAK,EAAEqC;IACT,CAAC,CAAC,EAAEjD,IAAI,IAAI,aAAalB,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAE;MACnDjC,KAAK,EAAE;QACLX,KAAK,EAAEmD;MACT,CAAC;MACDvC,SAAS,EAAE,EAAE,CAACS,MAAM,CAAC3B,SAAS,EAAE,cAAc;IAChD,CAAC,EAAEK,IAAI,CAAC,CAAC;EACX;EACA;EACA,OAAO,aAAalB,KAAK,CAAC+D,aAAa,CAAC,MAAM,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAEmD,SAAS,EAAE;IACtEH,SAAS,EAAEsC;EACb,CAAC,CAAC,EAAErD,QAAQ,EAAE,aAAahB,KAAK,CAAC+D,aAAa,CAAChE,SAAS,EAAE;IACxDwE,OAAO,EAAE,CAACxB,QAAQ;IAClByB,UAAU,EAAE,EAAE,CAAChC,MAAM,CAAC3B,SAAS,EAAE,OAAO,CAAC;IACzC4D,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE;EAClB,CAAC,EAAE,UAAUC,IAAI,EAAE;IACjB,IAAIC,YAAY;IAChB,IAAIC,eAAe,GAAGF,IAAI,CAAC5C,SAAS;IACpC,IAAIhB,qBAAqB,GAAGsB,YAAY,CAAC,eAAe,EAAEvB,8BAA8B,CAAC;IACzF,IAAIgE,KAAK,GAAGxB,QAAQ,CAACJ,OAAO;IAC5B,IAAI6B,eAAe,GAAGjF,UAAU,EAAE8E,YAAY,GAAG,CAAC,CAAC,EAAE/F,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,MAAM,CAAC,EAAEiE,KAAK,CAAC,EAAEjG,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,QAAQ,CAAC,EAAE,CAACiE,KAAK,CAAC,EAAEjG,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,WAAW,CAAC,EAAEc,IAAI,KAAK,OAAO,CAAC,EAAE9C,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,iBAAiB,CAAC,EAAE,CAACiE,KAAK,IAAIzB,YAAY,IAAIA,YAAY,CAAC2B,QAAQ,CAAC,CAAC,CAACpF,MAAM,GAAG,CAAC,CAAC,EAAEf,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACvB,MAAM,CAAC,EAAE,CAAC,CAACA,MAAM,CAAC,EAAEpC,eAAe,CAAC+F,YAAY,EAAE,EAAE,CAACpC,MAAM,CAAC3B,SAAS,EAAE,UAAU,CAAC,CAAC2B,MAAM,CAACrB,KAAK,CAAC,EAAEZ,aAAa,CAACY,KAAK,CAAC,CAAC,EAAEyD,YAAY,CAAC,CAAC;IACvmB,IAAIK,iBAAiB,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAEwE,WAAW,CAAC;IACjD,IAAIpC,KAAK,IAAI,CAACZ,aAAa,CAACY,KAAK,CAAC,EAAE;MAClC8D,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,CAAC;MAC3CA,iBAAiB,CAACb,UAAU,GAAGjD,KAAK;IACtC;IACA,OAAO,aAAanB,KAAK,CAAC+D,aAAa,CAACzD,YAAY,EAAE;MACpDO,SAAS,EAAEE,qBAAqB;MAChCmE,IAAI,EAAE,CAACnC,QAAQ;MACf8B,eAAe,EAAEA,eAAe;MAChC9C,SAAS,EAAEgD,eAAe;MAC1B1D,KAAK,EAAEgC,YAAY;MACnBzB,KAAK,EAAEiC,SAAS;MAChB/B,KAAK,EAAEmD,iBAAiB;MACxBE,GAAG,EAAE;IACP,CAAC,EAAEnB,WAAW,CAAC;EACjB,CAAC,CAAC,EAAEF,cAAc,CAAC;AACrB,CAAC;AACDtD,KAAK,CAACH,MAAM,GAAGA,MAAM;AACrB,eAAeG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}