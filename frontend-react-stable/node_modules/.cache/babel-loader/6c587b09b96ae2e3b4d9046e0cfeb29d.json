{"ast": null, "code": "var TabPane = function TabPane() {\n  return null;\n};\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'DeprecatedTabPane';\n}\nexport default TabPane;", "map": {"version": 3, "names": ["TabPane", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tabs/TabPane.js"], "sourcesContent": ["var TabPane = function TabPane() {\n  return null;\n};\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'DeprecatedTabPane';\n}\nexport default TabPane;"], "mappings": "AAAA,IAAIA,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EAC/B,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,OAAO,CAACI,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}