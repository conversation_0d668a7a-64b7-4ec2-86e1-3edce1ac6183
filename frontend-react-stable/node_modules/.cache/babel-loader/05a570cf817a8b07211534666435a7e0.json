{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { useLocaleReceiver } from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (_typeof(tooltip) === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nvar FormItemLabel = function FormItemLabel(_ref) {\n  var prefixCls = _ref.prefixCls,\n    label = _ref.label,\n    htmlFor = _ref.htmlFor,\n    labelCol = _ref.labelCol,\n    labelAlign = _ref.labelAlign,\n    colon = _ref.colon,\n    required = _ref.required,\n    requiredMark = _ref.requiredMark,\n    tooltip = _ref.tooltip;\n  var _useLocaleReceiver = useLocaleReceiver('Form'),\n    _useLocaleReceiver2 = _slicedToArray(_useLocaleReceiver, 1),\n    formLocale = _useLocaleReceiver2[0];\n  if (!label) return null;\n  return /*#__PURE__*/React.createElement(FormContext.Consumer, {\n    key: \"label\"\n  }, function (_ref2) {\n    var _classNames2;\n    var vertical = _ref2.vertical,\n      contextLabelAlign = _ref2.labelAlign,\n      contextLabelCol = _ref2.labelCol,\n      labelWrap = _ref2.labelWrap,\n      contextColon = _ref2.colon;\n    var _a;\n    var mergedLabelCol = labelCol || contextLabelCol || {};\n    var mergedLabelAlign = labelAlign || contextLabelAlign;\n    var labelClsBasic = \"\".concat(prefixCls, \"-item-label\");\n    var labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && \"\".concat(labelClsBasic, \"-left\"), mergedLabelCol.className, _defineProperty({}, \"\".concat(labelClsBasic, \"-wrap\"), !!labelWrap));\n    var labelChildren = label;\n    // Keep label is original where there should have no colon\n    var computedColon = colon === true || contextColon !== false && colon !== false;\n    var haveColon = computedColon && !vertical;\n    // Remove duplicated user input colon\n    if (haveColon && typeof label === 'string' && label.trim() !== '') {\n      labelChildren = label.replace(/[:|：]\\s*$/, '');\n    }\n    // Tooltip\n    var tooltipProps = toTooltipProps(tooltip);\n    if (tooltipProps) {\n      var _tooltipProps$icon = tooltipProps.icon,\n        icon = _tooltipProps$icon === void 0 ? /*#__PURE__*/React.createElement(QuestionCircleOutlined, null) : _tooltipProps$icon,\n        restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n      var tooltipNode = /*#__PURE__*/React.createElement(Tooltip, _extends({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n        className: \"\".concat(prefixCls, \"-item-tooltip\"),\n        title: ''\n      }));\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n    }\n    // Add required mark if optional\n    if (requiredMark === 'optional' && !required) {\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-optional\"),\n        title: \"\"\n      }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n    }\n    var labelClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required\"), required), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required-mark-optional\"), requiredMark === 'optional'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-no-colon\"), !computedColon), _classNames2));\n    return /*#__PURE__*/React.createElement(Col, _extends({}, mergedLabelCol, {\n      className: labelColClassName\n    }), /*#__PURE__*/React.createElement(\"label\", {\n      htmlFor: htmlFor,\n      className: labelClassName,\n      title: typeof label === 'string' ? label : ''\n    }, labelChildren));\n  });\n};\nexport default FormItemLabel;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "QuestionCircleOutlined", "classNames", "React", "Col", "useLocaleReceiver", "defaultLocale", "<PERSON><PERSON><PERSON>", "FormContext", "toTooltipProps", "tooltip", "isValidElement", "title", "FormItemLabel", "_ref", "prefixCls", "label", "htmlFor", "labelCol", "labelAlign", "colon", "required", "requiredMark", "_useLocaleReceiver", "_useLocaleReceiver2", "formLocale", "createElement", "Consumer", "key", "_ref2", "_classNames2", "vertical", "contextLabelAlign", "contextLabelCol", "labelWrap", "contextColon", "_a", "mergedLabelCol", "mergedLabelAlign", "labelClsBasic", "concat", "labelColClassName", "className", "labelChildren", "computedColon", "haveColon", "trim", "replace", "tooltipProps", "_tooltipProps$icon", "icon", "restTooltipProps", "tooltipNode", "cloneElement", "Fragment", "optional", "Form", "labelClassName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/FormItemLabel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { useLocaleReceiver } from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (_typeof(tooltip) === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nvar FormItemLabel = function FormItemLabel(_ref) {\n  var prefixCls = _ref.prefixCls,\n    label = _ref.label,\n    htmlFor = _ref.htmlFor,\n    labelCol = _ref.labelCol,\n    labelAlign = _ref.labelAlign,\n    colon = _ref.colon,\n    required = _ref.required,\n    requiredMark = _ref.requiredMark,\n    tooltip = _ref.tooltip;\n  var _useLocaleReceiver = useLocaleReceiver('Form'),\n    _useLocaleReceiver2 = _slicedToArray(_useLocaleReceiver, 1),\n    formLocale = _useLocaleReceiver2[0];\n  if (!label) return null;\n  return /*#__PURE__*/React.createElement(FormContext.Consumer, {\n    key: \"label\"\n  }, function (_ref2) {\n    var _classNames2;\n    var vertical = _ref2.vertical,\n      contextLabelAlign = _ref2.labelAlign,\n      contextLabelCol = _ref2.labelCol,\n      labelWrap = _ref2.labelWrap,\n      contextColon = _ref2.colon;\n    var _a;\n    var mergedLabelCol = labelCol || contextLabelCol || {};\n    var mergedLabelAlign = labelAlign || contextLabelAlign;\n    var labelClsBasic = \"\".concat(prefixCls, \"-item-label\");\n    var labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && \"\".concat(labelClsBasic, \"-left\"), mergedLabelCol.className, _defineProperty({}, \"\".concat(labelClsBasic, \"-wrap\"), !!labelWrap));\n    var labelChildren = label;\n    // Keep label is original where there should have no colon\n    var computedColon = colon === true || contextColon !== false && colon !== false;\n    var haveColon = computedColon && !vertical;\n    // Remove duplicated user input colon\n    if (haveColon && typeof label === 'string' && label.trim() !== '') {\n      labelChildren = label.replace(/[:|：]\\s*$/, '');\n    }\n    // Tooltip\n    var tooltipProps = toTooltipProps(tooltip);\n    if (tooltipProps) {\n      var _tooltipProps$icon = tooltipProps.icon,\n        icon = _tooltipProps$icon === void 0 ? /*#__PURE__*/React.createElement(QuestionCircleOutlined, null) : _tooltipProps$icon,\n        restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n      var tooltipNode = /*#__PURE__*/React.createElement(Tooltip, _extends({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n        className: \"\".concat(prefixCls, \"-item-tooltip\"),\n        title: ''\n      }));\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n    }\n    // Add required mark if optional\n    if (requiredMark === 'optional' && !required) {\n      labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-item-optional\"),\n        title: \"\"\n      }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n    }\n    var labelClassName = classNames((_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required\"), required), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-required-mark-optional\"), requiredMark === 'optional'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-item-no-colon\"), !computedColon), _classNames2));\n    return /*#__PURE__*/React.createElement(Col, _extends({}, mergedLabelCol, {\n      className: labelColClassName\n    }), /*#__PURE__*/React.createElement(\"label\", {\n      htmlFor: htmlFor,\n      className: labelClassName,\n      title: typeof label === 'string' ? label : ''\n    }, labelChildren));\n  });\n};\nexport default FormItemLabel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,sBAAsB,MAAM,mDAAmD;AACtF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAIxB,OAAO,CAACwB,OAAO,CAAC,KAAK,QAAQ,IAAI,EAAE,aAAaP,KAAK,CAACQ,cAAc,CAACD,OAAO,CAAC,EAAE;IACjF,OAAOA,OAAO;EAChB;EACA,OAAO;IACLE,KAAK,EAAEF;EACT,CAAC;AACH;AACA,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC/C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,UAAU,GAAGL,IAAI,CAACK,UAAU;IAC5BC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAClBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,YAAY,GAAGR,IAAI,CAACQ,YAAY;IAChCZ,OAAO,GAAGI,IAAI,CAACJ,OAAO;EACxB,IAAIa,kBAAkB,GAAGlB,iBAAiB,CAAC,MAAM,CAAC;IAChDmB,mBAAmB,GAAGvC,cAAc,CAACsC,kBAAkB,EAAE,CAAC,CAAC;IAC3DE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;EACrC,IAAI,CAACR,KAAK,EAAE,OAAO,IAAI;EACvB,OAAO,aAAab,KAAK,CAACuB,aAAa,CAAClB,WAAW,CAACmB,QAAQ,EAAE;IAC5DC,GAAG,EAAE;EACP,CAAC,EAAE,UAAUC,KAAK,EAAE;IAClB,IAAIC,YAAY;IAChB,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;MAC3BC,iBAAiB,GAAGH,KAAK,CAACV,UAAU;MACpCc,eAAe,GAAGJ,KAAK,CAACX,QAAQ;MAChCgB,SAAS,GAAGL,KAAK,CAACK,SAAS;MAC3BC,YAAY,GAAGN,KAAK,CAACT,KAAK;IAC5B,IAAIgB,EAAE;IACN,IAAIC,cAAc,GAAGnB,QAAQ,IAAIe,eAAe,IAAI,CAAC,CAAC;IACtD,IAAIK,gBAAgB,GAAGnB,UAAU,IAAIa,iBAAiB;IACtD,IAAIO,aAAa,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,aAAa,CAAC;IACvD,IAAI0B,iBAAiB,GAAGvC,UAAU,CAACqC,aAAa,EAAED,gBAAgB,KAAK,MAAM,IAAI,EAAE,CAACE,MAAM,CAACD,aAAa,EAAE,OAAO,CAAC,EAAEF,cAAc,CAACK,SAAS,EAAE1D,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwD,MAAM,CAACD,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC,CAACL,SAAS,CAAC,CAAC;IAClN,IAAIS,aAAa,GAAG3B,KAAK;IACzB;IACA,IAAI4B,aAAa,GAAGxB,KAAK,KAAK,IAAI,IAAIe,YAAY,KAAK,KAAK,IAAIf,KAAK,KAAK,KAAK;IAC/E,IAAIyB,SAAS,GAAGD,aAAa,IAAI,CAACb,QAAQ;IAC1C;IACA,IAAIc,SAAS,IAAI,OAAO7B,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC8B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEH,aAAa,GAAG3B,KAAK,CAAC+B,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IAChD;IACA;IACA,IAAIC,YAAY,GAAGvC,cAAc,CAACC,OAAO,CAAC;IAC1C,IAAIsC,YAAY,EAAE;MAChB,IAAIC,kBAAkB,GAAGD,YAAY,CAACE,IAAI;QACxCA,IAAI,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,aAAa9C,KAAK,CAACuB,aAAa,CAACzB,sBAAsB,EAAE,IAAI,CAAC,GAAGgD,kBAAkB;QAC1HE,gBAAgB,GAAGhE,MAAM,CAAC6D,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;MACnD,IAAII,WAAW,GAAG,aAAajD,KAAK,CAACuB,aAAa,CAACnB,OAAO,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEoE,gBAAgB,CAAC,EAAE,aAAahD,KAAK,CAACkD,YAAY,CAACH,IAAI,EAAE;QAChIR,SAAS,EAAE,EAAE,CAACF,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC;QAChDH,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;MACH+B,aAAa,GAAG,aAAaxC,KAAK,CAACuB,aAAa,CAACvB,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEX,aAAa,EAAES,WAAW,CAAC;IACpG;IACA;IACA,IAAI9B,YAAY,KAAK,UAAU,IAAI,CAACD,QAAQ,EAAE;MAC5CsB,aAAa,GAAG,aAAaxC,KAAK,CAACuB,aAAa,CAACvB,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEX,aAAa,EAAE,aAAaxC,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;QAC7HgB,SAAS,EAAE,EAAE,CAACF,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC;QACjDH,KAAK,EAAE;MACT,CAAC,EAAE,CAACa,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC8B,QAAQ,MAAM,CAACnB,EAAE,GAAG9B,aAAa,CAACkD,IAAI,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,QAAQ,CAAC,CAAC,CAAC;IACpK;IACA,IAAIE,cAAc,GAAGvD,UAAU,EAAE4B,YAAY,GAAG,CAAC,CAAC,EAAE9C,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAEM,QAAQ,CAAC,EAAErC,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,8BAA8B,CAAC,EAAEO,YAAY,KAAK,UAAU,CAAC,EAAEtC,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACU,MAAM,CAACzB,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAAC6B,aAAa,CAAC,EAAEd,YAAY,CAAC,CAAC;IAC5V,OAAO,aAAa3B,KAAK,CAACuB,aAAa,CAACtB,GAAG,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEsD,cAAc,EAAE;MACxEK,SAAS,EAAED;IACb,CAAC,CAAC,EAAE,aAAatC,KAAK,CAACuB,aAAa,CAAC,OAAO,EAAE;MAC5CT,OAAO,EAAEA,OAAO;MAChByB,SAAS,EAAEe,cAAc;MACzB7C,KAAK,EAAE,OAAOI,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;IAC7C,CAAC,EAAE2B,aAAa,CAAC,CAAC;EACpB,CAAC,CAAC;AACJ,CAAC;AACD,eAAe9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}