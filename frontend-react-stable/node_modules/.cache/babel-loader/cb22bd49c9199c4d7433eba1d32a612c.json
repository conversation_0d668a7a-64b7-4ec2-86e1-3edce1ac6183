{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport { Component } from 'react';\nimport ReactDOM from 'react-dom';\nimport classNames from 'classnames';\nvar Notice = /*#__PURE__*/function (_Component) {\n  _inherits(Notice, _Component);\n  var _super = _createSuper(Notice);\n  function Notice() {\n    var _this;\n    _classCallCheck(this, Notice);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n      _this.clearCloseTimer();\n      var _this$props = _this.props,\n        onClose = _this$props.onClose,\n        noticeKey = _this$props.noticeKey;\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n    return _this;\n  }\n  _createClass(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark ||\n      // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closable = _this$props2.closable,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style,\n        onClick = _this$props2.onClick,\n        children = _this$props2.children,\n        holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", _extends({\n        className: classNames(componentClass, className, _defineProperty({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n      if (holder) {\n        return /*#__PURE__*/ReactDOM.createPortal(node, holder);\n      }\n      return node;\n    }\n  }]);\n  return Notice;\n}(Component);\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};\nexport { Notice as default };", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "Component", "ReactDOM", "classNames", "Notice", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "closeTimer", "close", "e", "stopPropagation", "clearCloseTimer", "_this$props", "props", "onClose", "<PERSON><PERSON><PERSON>", "startCloseTimer", "duration", "window", "setTimeout", "clearTimeout", "key", "value", "componentDidMount", "componentDidUpdate", "prevProps", "updateMark", "visible", "restartCloseTimer", "componentWillUnmount", "render", "_this2", "_this$props2", "prefixCls", "className", "closable", "closeIcon", "style", "onClick", "children", "holder", "componentClass", "dataOrAriaAttributeProps", "Object", "keys", "reduce", "acc", "substr", "node", "createElement", "onMouseEnter", "onMouseLeave", "tabIndex", "createPortal", "defaultProps", "default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-notification/es/Notice.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport { Component } from 'react';\nimport ReactDOM from 'react-dom';\nimport classNames from 'classnames';\nvar Notice = /*#__PURE__*/function (_Component) {\n  _inherits(Notice, _Component);\n  var _super = _createSuper(Notice);\n  function Notice() {\n    var _this;\n    _classCallCheck(this, Notice);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.closeTimer = null;\n    _this.close = function (e) {\n      if (e) {\n        e.stopPropagation();\n      }\n      _this.clearCloseTimer();\n      var _this$props = _this.props,\n        onClose = _this$props.onClose,\n        noticeKey = _this$props.noticeKey;\n      if (onClose) {\n        onClose(noticeKey);\n      }\n    };\n    _this.startCloseTimer = function () {\n      if (_this.props.duration) {\n        _this.closeTimer = window.setTimeout(function () {\n          _this.close();\n        }, _this.props.duration * 1000);\n      }\n    };\n    _this.clearCloseTimer = function () {\n      if (_this.closeTimer) {\n        clearTimeout(_this.closeTimer);\n        _this.closeTimer = null;\n      }\n    };\n    return _this;\n  }\n  _createClass(Notice, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.duration !== prevProps.duration || this.props.updateMark !== prevProps.updateMark ||\n      // Visible again need reset timer\n      this.props.visible !== prevProps.visible && this.props.visible) {\n        this.restartCloseTimer();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.clearCloseTimer();\n    }\n  }, {\n    key: \"restartCloseTimer\",\n    value: function restartCloseTimer() {\n      this.clearCloseTimer();\n      this.startCloseTimer();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        className = _this$props2.className,\n        closable = _this$props2.closable,\n        closeIcon = _this$props2.closeIcon,\n        style = _this$props2.style,\n        onClick = _this$props2.onClick,\n        children = _this$props2.children,\n        holder = _this$props2.holder;\n      var componentClass = \"\".concat(prefixCls, \"-notice\");\n      var dataOrAriaAttributeProps = Object.keys(this.props).reduce(function (acc, key) {\n        if (key.substr(0, 5) === 'data-' || key.substr(0, 5) === 'aria-' || key === 'role') {\n          acc[key] = _this2.props[key];\n        }\n        return acc;\n      }, {});\n      var node = /*#__PURE__*/React.createElement(\"div\", _extends({\n        className: classNames(componentClass, className, _defineProperty({}, \"\".concat(componentClass, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: this.clearCloseTimer,\n        onMouseLeave: this.startCloseTimer,\n        onClick: onClick\n      }, dataOrAriaAttributeProps), /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(componentClass, \"-content\")\n      }, children), closable ? /*#__PURE__*/React.createElement(\"a\", {\n        tabIndex: 0,\n        onClick: this.close,\n        className: \"\".concat(componentClass, \"-close\")\n      }, closeIcon || /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(componentClass, \"-close-x\")\n      })) : null);\n      if (holder) {\n        return /*#__PURE__*/ReactDOM.createPortal(node, holder);\n      }\n      return node;\n    }\n  }]);\n  return Notice;\n}(Component);\nNotice.defaultProps = {\n  onClose: function onClose() {},\n  duration: 1.5\n};\nexport { Notice as default };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,MAAM,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC9CP,SAAS,CAACM,MAAM,EAAEC,UAAU,CAAC;EAC7B,IAAIC,MAAM,GAAGP,YAAY,CAACK,MAAM,CAAC;EACjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IACTX,eAAe,CAAC,IAAI,EAAEQ,MAAM,CAAC;IAC7B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDJ,KAAK,CAACU,UAAU,GAAG,IAAI;IACvBV,KAAK,CAACW,KAAK,GAAG,UAAUC,CAAC,EAAE;MACzB,IAAIA,CAAC,EAAE;QACLA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB;MACAb,KAAK,CAACc,eAAe,CAAC,CAAC;MACvB,IAAIC,WAAW,GAAGf,KAAK,CAACgB,KAAK;QAC3BC,OAAO,GAAGF,WAAW,CAACE,OAAO;QAC7BC,SAAS,GAAGH,WAAW,CAACG,SAAS;MACnC,IAAID,OAAO,EAAE;QACXA,OAAO,CAACC,SAAS,CAAC;MACpB;IACF,CAAC;IACDlB,KAAK,CAACmB,eAAe,GAAG,YAAY;MAClC,IAAInB,KAAK,CAACgB,KAAK,CAACI,QAAQ,EAAE;QACxBpB,KAAK,CAACU,UAAU,GAAGW,MAAM,CAACC,UAAU,CAAC,YAAY;UAC/CtB,KAAK,CAACW,KAAK,CAAC,CAAC;QACf,CAAC,EAAEX,KAAK,CAACgB,KAAK,CAACI,QAAQ,GAAG,IAAI,CAAC;MACjC;IACF,CAAC;IACDpB,KAAK,CAACc,eAAe,GAAG,YAAY;MAClC,IAAId,KAAK,CAACU,UAAU,EAAE;QACpBa,YAAY,CAACvB,KAAK,CAACU,UAAU,CAAC;QAC9BV,KAAK,CAACU,UAAU,GAAG,IAAI;MACzB;IACF,CAAC;IACD,OAAOV,KAAK;EACd;EACAV,YAAY,CAACO,MAAM,EAAE,CAAC;IACpB2B,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACP,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE,SAASE,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACZ,KAAK,CAACI,QAAQ,KAAKQ,SAAS,CAACR,QAAQ,IAAI,IAAI,CAACJ,KAAK,CAACa,UAAU,KAAKD,SAAS,CAACC,UAAU;MAChG;MACA,IAAI,CAACb,KAAK,CAACc,OAAO,KAAKF,SAAS,CAACE,OAAO,IAAI,IAAI,CAACd,KAAK,CAACc,OAAO,EAAE;QAC9D,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC1B;IACF;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE,SAASO,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAAClB,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDU,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASM,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACjB,eAAe,CAAC,CAAC;MACtB,IAAI,CAACK,eAAe,CAAC,CAAC;IACxB;EACF,CAAC,EAAE;IACDK,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASQ,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACnB,KAAK;QAC3BoB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClCC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;QAChCC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BC,OAAO,GAAGN,YAAY,CAACM,OAAO;QAC9BC,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,MAAM,GAAGR,YAAY,CAACQ,MAAM;MAC9B,IAAIC,cAAc,GAAG,EAAE,CAACnC,MAAM,CAAC2B,SAAS,EAAE,SAAS,CAAC;MACpD,IAAIS,wBAAwB,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/B,KAAK,CAAC,CAACgC,MAAM,CAAC,UAAUC,GAAG,EAAEzB,GAAG,EAAE;QAChF,IAAIA,GAAG,CAAC0B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAI1B,GAAG,CAAC0B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,IAAI1B,GAAG,KAAK,MAAM,EAAE;UAClFyB,GAAG,CAACzB,GAAG,CAAC,GAAGU,MAAM,CAAClB,KAAK,CAACQ,GAAG,CAAC;QAC9B;QACA,OAAOyB,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAIE,IAAI,GAAG,aAAa1D,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAEjE,QAAQ,CAAC;QAC1DkD,SAAS,EAAEzC,UAAU,CAACgD,cAAc,EAAEP,SAAS,EAAEjD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACmC,cAAc,EAAE,WAAW,CAAC,EAAEN,QAAQ,CAAC,CAAC;QACvHE,KAAK,EAAEA,KAAK;QACZa,YAAY,EAAE,IAAI,CAACvC,eAAe;QAClCwC,YAAY,EAAE,IAAI,CAACnC,eAAe;QAClCsB,OAAO,EAAEA;MACX,CAAC,EAAEI,wBAAwB,CAAC,EAAE,aAAapD,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;QACpEf,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAACmC,cAAc,EAAE,UAAU;MACjD,CAAC,EAAEF,QAAQ,CAAC,EAAEJ,QAAQ,GAAG,aAAa7C,KAAK,CAAC2D,aAAa,CAAC,GAAG,EAAE;QAC7DG,QAAQ,EAAE,CAAC;QACXd,OAAO,EAAE,IAAI,CAAC9B,KAAK;QACnB0B,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAACmC,cAAc,EAAE,QAAQ;MAC/C,CAAC,EAAEL,SAAS,IAAI,aAAa9C,KAAK,CAAC2D,aAAa,CAAC,MAAM,EAAE;QACvDf,SAAS,EAAE,EAAE,CAAC5B,MAAM,CAACmC,cAAc,EAAE,UAAU;MACjD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACX,IAAID,MAAM,EAAE;QACV,OAAO,aAAahD,QAAQ,CAAC6D,YAAY,CAACL,IAAI,EAAER,MAAM,CAAC;MACzD;MACA,OAAOQ,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOtD,MAAM;AACf,CAAC,CAACH,SAAS,CAAC;AACZG,MAAM,CAAC4D,YAAY,GAAG;EACpBxC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC,CAAC;EAC9BG,QAAQ,EAAE;AACZ,CAAC;AACD,SAASvB,MAAM,IAAI6D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}