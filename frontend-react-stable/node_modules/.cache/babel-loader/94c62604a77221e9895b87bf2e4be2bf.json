{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = some;\nfunction some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "some", "values", "test", "TypeError", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/some.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = some;\n\nfunction some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n\n  return false;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,SAASA,IAAIA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC1B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,MAAMN,KAAK,IAAIG,MAAM,EAAE;IAC1B,IAAIC,IAAI,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,EAAE;MAChC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}