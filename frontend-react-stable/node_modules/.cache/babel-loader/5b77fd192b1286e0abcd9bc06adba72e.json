{"ast": null, "code": "module.exports = function walk(nodes, cb, bubble) {\n  var i, max, node, result;\n  for (i = 0, max = nodes.length; i < max; i += 1) {\n    node = nodes[i];\n    if (!bubble) {\n      result = cb(node, i, nodes);\n    }\n    if (result !== false && node.type === \"function\" && Array.isArray(node.nodes)) {\n      walk(node.nodes, cb, bubble);\n    }\n    if (bubble) {\n      cb(node, i, nodes);\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "walk", "nodes", "cb", "bubble", "i", "max", "node", "result", "length", "type", "Array", "isArray"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/node_modules/postcss-value-parser/lib/walk.js"], "sourcesContent": ["module.exports = function walk(nodes, cb, bubble) {\n  var i, max, node, result;\n\n  for (i = 0, max = nodes.length; i < max; i += 1) {\n    node = nodes[i];\n    if (!bubble) {\n      result = cb(node, i, nodes);\n    }\n\n    if (\n      result !== false &&\n      node.type === \"function\" &&\n      Array.isArray(node.nodes)\n    ) {\n      walk(node.nodes, cb, bubble);\n    }\n\n    if (bubble) {\n      cb(node, i, nodes);\n    }\n  }\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,KAAK,EAAEC,EAAE,EAAEC,MAAM,EAAE;EAChD,IAAIC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM;EAExB,KAAKH,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,KAAK,CAACO,MAAM,EAAEJ,CAAC,GAAGC,GAAG,EAAED,CAAC,IAAI,CAAC,EAAE;IAC/CE,IAAI,GAAGL,KAAK,CAACG,CAAC,CAAC;IACf,IAAI,CAACD,MAAM,EAAE;MACXI,MAAM,GAAGL,EAAE,CAACI,IAAI,EAAEF,CAAC,EAAEH,KAAK,CAAC;IAC7B;IAEA,IACEM,MAAM,KAAK,KAAK,IAChBD,IAAI,CAACG,IAAI,KAAK,UAAU,IACxBC,KAAK,CAACC,OAAO,CAACL,IAAI,CAACL,KAAK,CAAC,EACzB;MACAD,IAAI,CAACM,IAAI,CAACL,KAAK,EAAEC,EAAE,EAAEC,MAAM,CAAC;IAC9B;IAEA,IAAIA,MAAM,EAAE;MACVD,EAAE,CAACI,IAAI,EAAEF,CAAC,EAAEH,KAAK,CAAC;IACpB;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}