{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { FieldContext } from '.';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { useState, useContext, useEffect, useRef, useMemo } from 'react';\nimport { getNamePath, getValue } from './utils/valueUtil';\nexport function stringify(value) {\n  try {\n    return JSON.stringify(value);\n  } catch (err) {\n    return Math.random();\n  }\n}\nvar useWatchWarning = process.env.NODE_ENV !== 'production' ? function (namePath) {\n  var fullyStr = namePath.join('__RC_FIELD_FORM_SPLIT__');\n  var nameStrRef = useRef(fullyStr);\n  warning(nameStrRef.current === fullyStr, '`useWatch` is not support dynamic `namePath`. Please provide static instead.');\n} : function () {};\nfunction useWatch() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var _args$ = args[0],\n    dependencies = _args$ === void 0 ? [] : _args$,\n    form = args[1];\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    setValue = _useState2[1];\n  var valueStr = useMemo(function () {\n    return stringify(value);\n  }, [value]);\n  var valueStrRef = useRef(valueStr);\n  valueStrRef.current = valueStr;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init;\n  // Warning if not exist form instance\n  if (process.env.NODE_ENV !== 'production') {\n    warning(args.length === 2 ? form ? isValidForm : true : isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useWatchWarning(namePath);\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n    var getFieldsValue = formInstance.getFieldsValue,\n      getInternalHooks = formInstance.getInternalHooks;\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n      registerWatch = _getInternalHooks.registerWatch;\n    var cancelRegister = registerWatch(function (store) {\n      var newValue = getValue(store, namePathRef.current);\n      var nextValueStr = stringify(newValue);\n      // Compare stringify in case it's nest object\n      if (valueStrRef.current !== nextValueStr) {\n        valueStrRef.current = nextValueStr;\n        setValue(newValue);\n      }\n    });\n    // TODO: We can improve this perf in future\n    var initialValue = getValue(getFieldsValue(), namePathRef.current);\n    setValue(initialValue);\n    return cancelRegister;\n  },\n  // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [isValidForm]);\n  return value;\n}\nexport default useWatch;", "map": {"version": 3, "names": ["_slicedToArray", "FieldContext", "warning", "HOOK_MARK", "useState", "useContext", "useEffect", "useRef", "useMemo", "getNamePath", "getValue", "stringify", "value", "JSON", "err", "Math", "random", "useWatchWarning", "process", "env", "NODE_ENV", "namePath", "fullyStr", "join", "nameStrRef", "current", "useWatch", "_len", "arguments", "length", "args", "Array", "_key", "_args$", "dependencies", "form", "_useState", "_useState2", "setValue", "valueStr", "valueStrRef", "fieldContext", "formInstance", "isValidForm", "_init", "namePathRef", "getFieldsValue", "getInternalHooks", "_getInternalHooks", "registerWatch", "cancelRegister", "store", "newValue", "nextValueStr", "initialValue"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/useWatch.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { FieldContext } from '.';\nimport warning from \"rc-util/es/warning\";\nimport { HOOK_MARK } from './FieldContext';\nimport { useState, useContext, useEffect, useRef, useMemo } from 'react';\nimport { getNamePath, getValue } from './utils/valueUtil';\nexport function stringify(value) {\n  try {\n    return JSON.stringify(value);\n  } catch (err) {\n    return Math.random();\n  }\n}\nvar useWatchWarning = process.env.NODE_ENV !== 'production' ? function (namePath) {\n  var fullyStr = namePath.join('__RC_FIELD_FORM_SPLIT__');\n  var nameStrRef = useRef(fullyStr);\n  warning(nameStrRef.current === fullyStr, '`useWatch` is not support dynamic `namePath`. Please provide static instead.');\n} : function () {};\nfunction useWatch() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var _args$ = args[0],\n    dependencies = _args$ === void 0 ? [] : _args$,\n    form = args[1];\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    setValue = _useState2[1];\n  var valueStr = useMemo(function () {\n    return stringify(value);\n  }, [value]);\n  var valueStrRef = useRef(valueStr);\n  valueStrRef.current = valueStr;\n  var fieldContext = useContext(FieldContext);\n  var formInstance = form || fieldContext;\n  var isValidForm = formInstance && formInstance._init;\n  // Warning if not exist form instance\n  if (process.env.NODE_ENV !== 'production') {\n    warning(args.length === 2 ? form ? isValidForm : true : isValidForm, 'useWatch requires a form instance since it can not auto detect from context.');\n  }\n  var namePath = getNamePath(dependencies);\n  var namePathRef = useRef(namePath);\n  namePathRef.current = namePath;\n  useWatchWarning(namePath);\n  useEffect(function () {\n    // Skip if not exist form instance\n    if (!isValidForm) {\n      return;\n    }\n    var getFieldsValue = formInstance.getFieldsValue,\n      getInternalHooks = formInstance.getInternalHooks;\n    var _getInternalHooks = getInternalHooks(HOOK_MARK),\n      registerWatch = _getInternalHooks.registerWatch;\n    var cancelRegister = registerWatch(function (store) {\n      var newValue = getValue(store, namePathRef.current);\n      var nextValueStr = stringify(newValue);\n      // Compare stringify in case it's nest object\n      if (valueStrRef.current !== nextValueStr) {\n        valueStrRef.current = nextValueStr;\n        setValue(newValue);\n      }\n    });\n    // TODO: We can improve this perf in future\n    var initialValue = getValue(getFieldsValue(), namePathRef.current);\n    setValue(initialValue);\n    return cancelRegister;\n  },\n  // We do not need re-register since namePath content is the same\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [isValidForm]);\n  return value;\n}\nexport default useWatch;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,GAAG;AAChC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,WAAW,EAAEC,QAAQ,QAAQ,mBAAmB;AACzD,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,IAAI;IACF,OAAOC,IAAI,CAACF,SAAS,CAACC,KAAK,CAAC;EAC9B,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZ,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC;EACtB;AACF;AACA,IAAIC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,UAAUC,QAAQ,EAAE;EAChF,IAAIC,QAAQ,GAAGD,QAAQ,CAACE,IAAI,CAAC,yBAAyB,CAAC;EACvD,IAAIC,UAAU,GAAGjB,MAAM,CAACe,QAAQ,CAAC;EACjCpB,OAAO,CAACsB,UAAU,CAACC,OAAO,KAAKH,QAAQ,EAAE,8EAA8E,CAAC;AAC1H,CAAC,GAAG,YAAY,CAAC,CAAC;AAClB,SAASI,QAAQA,CAAA,EAAG;EAClB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,IAAIC,MAAM,GAAGH,IAAI,CAAC,CAAC,CAAC;IAClBI,YAAY,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,MAAM;IAC9CE,IAAI,GAAGL,IAAI,CAAC,CAAC,CAAC;EAChB,IAAIM,SAAS,GAAGhC,QAAQ,CAAC,CAAC;IACxBiC,UAAU,GAAGrC,cAAc,CAACoC,SAAS,EAAE,CAAC,CAAC;IACzCxB,KAAK,GAAGyB,UAAU,CAAC,CAAC,CAAC;IACrBC,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIE,QAAQ,GAAG/B,OAAO,CAAC,YAAY;IACjC,OAAOG,SAAS,CAACC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,IAAI4B,WAAW,GAAGjC,MAAM,CAACgC,QAAQ,CAAC;EAClCC,WAAW,CAACf,OAAO,GAAGc,QAAQ;EAC9B,IAAIE,YAAY,GAAGpC,UAAU,CAACJ,YAAY,CAAC;EAC3C,IAAIyC,YAAY,GAAGP,IAAI,IAAIM,YAAY;EACvC,IAAIE,WAAW,GAAGD,YAAY,IAAIA,YAAY,CAACE,KAAK;EACpD;EACA,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzClB,OAAO,CAAC4B,IAAI,CAACD,MAAM,KAAK,CAAC,GAAGM,IAAI,GAAGQ,WAAW,GAAG,IAAI,GAAGA,WAAW,EAAE,8EAA8E,CAAC;EACtJ;EACA,IAAItB,QAAQ,GAAGZ,WAAW,CAACyB,YAAY,CAAC;EACxC,IAAIW,WAAW,GAAGtC,MAAM,CAACc,QAAQ,CAAC;EAClCwB,WAAW,CAACpB,OAAO,GAAGJ,QAAQ;EAC9BJ,eAAe,CAACI,QAAQ,CAAC;EACzBf,SAAS,CAAC,YAAY;IACpB;IACA,IAAI,CAACqC,WAAW,EAAE;MAChB;IACF;IACA,IAAIG,cAAc,GAAGJ,YAAY,CAACI,cAAc;MAC9CC,gBAAgB,GAAGL,YAAY,CAACK,gBAAgB;IAClD,IAAIC,iBAAiB,GAAGD,gBAAgB,CAAC5C,SAAS,CAAC;MACjD8C,aAAa,GAAGD,iBAAiB,CAACC,aAAa;IACjD,IAAIC,cAAc,GAAGD,aAAa,CAAC,UAAUE,KAAK,EAAE;MAClD,IAAIC,QAAQ,GAAG1C,QAAQ,CAACyC,KAAK,EAAEN,WAAW,CAACpB,OAAO,CAAC;MACnD,IAAI4B,YAAY,GAAG1C,SAAS,CAACyC,QAAQ,CAAC;MACtC;MACA,IAAIZ,WAAW,CAACf,OAAO,KAAK4B,YAAY,EAAE;QACxCb,WAAW,CAACf,OAAO,GAAG4B,YAAY;QAClCf,QAAQ,CAACc,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC;IACF;IACA,IAAIE,YAAY,GAAG5C,QAAQ,CAACoC,cAAc,CAAC,CAAC,EAAED,WAAW,CAACpB,OAAO,CAAC;IAClEa,QAAQ,CAACgB,YAAY,CAAC;IACtB,OAAOJ,cAAc;EACvB,CAAC;EACD;EACA;EACA,CAACP,WAAW,CAAC,CAAC;EACd,OAAO/B,KAAK;AACd;AACA,eAAec,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}