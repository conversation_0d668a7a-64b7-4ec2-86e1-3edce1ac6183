{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Cubehelix = Cubehelix;\nexports.default = cubehelix;\nvar _define = _interopRequireWildcard(require(\"./define.js\"));\nvar _color = require(\"./color.js\");\nvar _math = require(\"./math.js\");\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nvar A = -0.14861,\n  B = +1.78277,\n  C = -0.29227,\n  D = -0.90649,\n  E = +1.97294,\n  ED = E * D,\n  EB = E * B,\n  BC_DA = B * C - D * A;\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof _color.Rgb)) o = (0, _color.rgbConvert)(o);\n  var r = o.r / 255,\n    g = o.g / 255,\n    b = o.b / 255,\n    l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n    bl = b - l,\n    k = (E * (g - l) - C * bl) / D,\n    s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)),\n    // NaN if l=0 or l=1\n    h = s ? Math.atan2(k, bl) * _math.degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\nfunction cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\nfunction Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n(0, _define.default)(Cubehelix, cubehelix, (0, _define.extend)(_color.Color, {\n  brighter(k) {\n    k = k == null ? _color.brighter : Math.pow(_color.brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? _color.darker : Math.pow(_color.darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * _math.radians,\n      l = +this.l,\n      a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n      cosh = Math.cos(h),\n      sinh = Math.sin(h);\n    return new _color.Rgb(255 * (l + a * (A * cosh + B * sinh)), 255 * (l + a * (C * cosh + D * sinh)), 255 * (l + a * (E * cosh)), this.opacity);\n  }\n}));", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Cubehel<PERSON>", "default", "cubehelix", "_define", "_interopRequireWildcard", "require", "_color", "_math", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "A", "B", "C", "D", "E", "ED", "EB", "BC_DA", "cubehelixConvert", "o", "h", "s", "l", "opacity", "Rgb", "rgbConvert", "r", "g", "b", "bl", "k", "Math", "sqrt", "atan2", "degrees", "NaN", "arguments", "length", "extend", "Color", "brighter", "pow", "darker", "rgb", "isNaN", "radians", "a", "cosh", "cos", "sinh", "sin"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-color/src/cubehelix.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Cubehelix = Cubehelix;\nexports.default = cubehelix;\n\nvar _define = _interopRequireWildcard(require(\"./define.js\"));\n\nvar _color = require(\"./color.js\");\n\nvar _math = require(\"./math.js\");\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nvar A = -0.14861,\n    B = +1.78277,\n    C = -0.29227,\n    D = -0.90649,\n    E = +1.97294,\n    ED = E * D,\n    EB = E * B,\n    BC_DA = B * C - D * A;\n\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof _color.Rgb)) o = (0, _color.rgbConvert)(o);\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n      bl = b - l,\n      k = (E * (g - l) - C * bl) / D,\n      s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)),\n      // NaN if l=0 or l=1\n  h = s ? Math.atan2(k, bl) * _math.degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\n\nfunction cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\n(0, _define.default)(Cubehelix, cubehelix, (0, _define.extend)(_color.Color, {\n  brighter(k) {\n    k = k == null ? _color.brighter : Math.pow(_color.brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n\n  darker(k) {\n    k = k == null ? _color.darker : Math.pow(_color.darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * _math.radians,\n        l = +this.l,\n        a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n        cosh = Math.cos(h),\n        sinh = Math.sin(h);\n    return new _color.Rgb(255 * (l + a * (A * cosh + B * sinh)), 255 * (l + a * (C * cosh + D * sinh)), 255 * (l + a * (E * cosh)), this.opacity);\n  }\n\n}));"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAGA,SAAS;AAC7BF,OAAO,CAACG,OAAO,GAAGC,SAAS;AAE3B,IAAIC,OAAO,GAAGC,uBAAuB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE7D,IAAIC,MAAM,GAAGD,OAAO,CAAC,YAAY,CAAC;AAElC,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASG,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASL,uBAAuBA,CAACS,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEZ,OAAO,EAAEY;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGvB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACwB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIzB,MAAM,CAAC0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGvB,MAAM,CAACwB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE9B,MAAM,CAACC,cAAc,CAACqB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAACjB,OAAO,GAAGY,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,IAAIS,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,EAAE,GAAGD,CAAC,GAAGD,CAAC;EACVG,EAAE,GAAGF,CAAC,GAAGH,CAAC;EACVM,KAAK,GAAGN,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAGH,CAAC;AAEzB,SAASQ,gBAAgBA,CAACC,CAAC,EAAE;EAC3B,IAAIA,CAAC,YAAYpC,SAAS,EAAE,OAAO,IAAIA,SAAS,CAACoC,CAAC,CAACC,CAAC,EAAED,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,OAAO,CAAC;EAC1E,IAAI,EAAEJ,CAAC,YAAY9B,MAAM,CAACmC,GAAG,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAACoC,UAAU,EAAEN,CAAC,CAAC;EAC7D,IAAIO,CAAC,GAAGP,CAAC,CAACO,CAAC,GAAG,GAAG;IACbC,CAAC,GAAGR,CAAC,CAACQ,CAAC,GAAG,GAAG;IACbC,CAAC,GAAGT,CAAC,CAACS,CAAC,GAAG,GAAG;IACbN,CAAC,GAAG,CAACL,KAAK,GAAGW,CAAC,GAAGb,EAAE,GAAGW,CAAC,GAAGV,EAAE,GAAGW,CAAC,KAAKV,KAAK,GAAGF,EAAE,GAAGC,EAAE,CAAC;IACrDa,EAAE,GAAGD,CAAC,GAAGN,CAAC;IACVQ,CAAC,GAAG,CAAChB,CAAC,IAAIa,CAAC,GAAGL,CAAC,CAAC,GAAGV,CAAC,GAAGiB,EAAE,IAAIhB,CAAC;IAC9BQ,CAAC,GAAGU,IAAI,CAACC,IAAI,CAACF,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGA,EAAE,CAAC,IAAIf,CAAC,GAAGQ,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC;IAClD;IACJF,CAAC,GAAGC,CAAC,GAAGU,IAAI,CAACE,KAAK,CAACH,CAAC,EAAED,EAAE,CAAC,GAAGvC,KAAK,CAAC4C,OAAO,GAAG,GAAG,GAAGC,GAAG;EACrD,OAAO,IAAIpD,SAAS,CAACqC,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAGA,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEH,CAAC,CAACI,OAAO,CAAC;AAC5D;AAEA,SAAStC,SAASA,CAACmC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACnC,OAAOa,SAAS,CAACC,MAAM,KAAK,CAAC,GAAGnB,gBAAgB,CAACE,CAAC,CAAC,GAAG,IAAIrC,SAAS,CAACqC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAAC;AAC7G;AAEA,SAASxC,SAASA,CAACqC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACnC,IAAI,CAACH,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,OAAO,GAAG,CAACA,OAAO;AACzB;AAEA,CAAC,CAAC,EAAErC,OAAO,CAACF,OAAO,EAAED,SAAS,EAAEE,SAAS,EAAE,CAAC,CAAC,EAAEC,OAAO,CAACoD,MAAM,EAAEjD,MAAM,CAACkD,KAAK,EAAE;EAC3EC,QAAQA,CAACV,CAAC,EAAE;IACVA,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGzC,MAAM,CAACmD,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACpD,MAAM,CAACmD,QAAQ,EAAEV,CAAC,CAAC;IAC9D,OAAO,IAAI/C,SAAS,CAAC,IAAI,CAACqC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,CAAC,EAAE,IAAI,CAACP,OAAO,CAAC;EAChE,CAAC;EAEDmB,MAAMA,CAACZ,CAAC,EAAE;IACRA,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGzC,MAAM,CAACqD,MAAM,GAAGX,IAAI,CAACU,GAAG,CAACpD,MAAM,CAACqD,MAAM,EAAEZ,CAAC,CAAC;IAC1D,OAAO,IAAI/C,SAAS,CAAC,IAAI,CAACqC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGQ,CAAC,EAAE,IAAI,CAACP,OAAO,CAAC;EAChE,CAAC;EAEDoB,GAAGA,CAAA,EAAG;IACJ,IAAIvB,CAAC,GAAGwB,KAAK,CAAC,IAAI,CAACxB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC,GAAG,GAAG,IAAI9B,KAAK,CAACuD,OAAO;MACtDvB,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC;MACXwB,CAAC,GAAGF,KAAK,CAAC,IAAI,CAACvB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC;MAC5CyB,IAAI,GAAGhB,IAAI,CAACiB,GAAG,CAAC5B,CAAC,CAAC;MAClB6B,IAAI,GAAGlB,IAAI,CAACmB,GAAG,CAAC9B,CAAC,CAAC;IACtB,OAAO,IAAI/B,MAAM,CAACmC,GAAG,CAAC,GAAG,IAAIF,CAAC,GAAGwB,CAAC,IAAIpC,CAAC,GAAGqC,IAAI,GAAGpC,CAAC,GAAGsC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI3B,CAAC,GAAGwB,CAAC,IAAIlC,CAAC,GAAGmC,IAAI,GAAGlC,CAAC,GAAGoC,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI3B,CAAC,GAAGwB,CAAC,IAAIhC,CAAC,GAAGiC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACxB,OAAO,CAAC;EAC/I;AAEF,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}