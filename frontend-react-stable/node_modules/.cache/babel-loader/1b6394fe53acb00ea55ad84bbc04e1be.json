{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\"];\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nimport Placements from './placements';\nimport useAccessibility from './hooks/useAccessibility';\nfunction Dropdown(props, ref) {\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? Placements : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  useAccessibility({\n    visible: mergedVisible,\n    setTriggerVisible: setTriggerVisible,\n    triggerRef: triggerRef,\n    onVisibleChange: props.onVisibleChange,\n    autoFocus: autoFocus\n  });\n  var getOverlayElement = function getOverlayElement() {\n    var overlay = props.overlay;\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  };\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var onVisibleChange = function onVisibleChange(newVisible) {\n    var onVisibleChangeProp = props.onVisibleChange;\n    setTriggerVisible(newVisible);\n    if (typeof onVisibleChangeProp === 'function') {\n      onVisibleChangeProp(newVisible);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    var overlayElement = getOverlayElement();\n    return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-arrow\")\n    }), overlayElement);\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    var overlay = props.overlay;\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var renderChildren = function renderChildren() {\n    var children = props.children;\n    var childrenProps = children.props ? children.props : {};\n    var childClassName = classNames(childrenProps.className, getOpenClassName());\n    return mergedVisible && children ? /*#__PURE__*/React.cloneElement(children, {\n      className: childClassName\n    }) : children;\n  };\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/React.createElement(Trigger, _objectSpread(_objectSpread({\n    builtinPlacements: placements\n  }, otherProps), {}, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction || [],\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: onVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), renderChildren());\n}\nexport default /*#__PURE__*/React.forwardRef(Dropdown);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "<PERSON><PERSON>", "classNames", "Placements", "useAccessibility", "Dropdown", "props", "ref", "_props$arrow", "arrow", "_props$prefixCls", "prefixCls", "transitionName", "animation", "align", "_props$placement", "placement", "_props$placements", "placements", "getPopupContainer", "showAction", "hideAction", "overlayClassName", "overlayStyle", "visible", "_props$trigger", "trigger", "autoFocus", "otherProps", "_React$useState", "useState", "_React$useState2", "triggerVisible", "setTriggerVisible", "mergedVisible", "triggerRef", "useRef", "useImperativeHandle", "current", "onVisibleChange", "getOverlayElement", "overlay", "overlayElement", "onClick", "e", "onOverlayClick", "newVisible", "onVisibleChangeProp", "getMenuElement", "createElement", "Fragment", "className", "concat", "getMenuElementOrLambda", "getMinOverlayWidthMatchTrigger", "minOverlayWidthMatchTrigger", "alignPoint", "getOpenClassName", "openClassName", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "childrenProps", "childClassName", "cloneElement", "triggerHideAction", "indexOf", "builtinPlacements", "popupClassName", "popupStyle", "action", "popupPlacement", "popupAlign", "popupTransitionName", "popupAnimation", "popupVisible", "stretch", "popup", "onPopupVisibleChange", "onPopupClick", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dropdown/es/Dropdown.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\"];\nimport * as React from 'react';\nimport Trigger from 'rc-trigger';\nimport classNames from 'classnames';\nimport Placements from './placements';\nimport useAccessibility from './hooks/useAccessibility';\n\nfunction Dropdown(props, ref) {\n  var _props$arrow = props.arrow,\n      arrow = _props$arrow === void 0 ? false : _props$arrow,\n      _props$prefixCls = props.prefixCls,\n      prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n      transitionName = props.transitionName,\n      animation = props.animation,\n      align = props.align,\n      _props$placement = props.placement,\n      placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n      _props$placements = props.placements,\n      placements = _props$placements === void 0 ? Placements : _props$placements,\n      getPopupContainer = props.getPopupContainer,\n      showAction = props.showAction,\n      hideAction = props.hideAction,\n      overlayClassName = props.overlayClassName,\n      overlayStyle = props.overlayStyle,\n      visible = props.visible,\n      _props$trigger = props.trigger,\n      trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n      autoFocus = props.autoFocus,\n      otherProps = _objectWithoutProperties(props, _excluded);\n\n  var _React$useState = React.useState(),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      triggerVisible = _React$useState2[0],\n      setTriggerVisible = _React$useState2[1];\n\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  useAccessibility({\n    visible: mergedVisible,\n    setTriggerVisible: setTriggerVisible,\n    triggerRef: triggerRef,\n    onVisibleChange: props.onVisibleChange,\n    autoFocus: autoFocus\n  });\n\n  var getOverlayElement = function getOverlayElement() {\n    var overlay = props.overlay;\n    var overlayElement;\n\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n\n    return overlayElement;\n  };\n\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n\n  var onVisibleChange = function onVisibleChange(newVisible) {\n    var onVisibleChangeProp = props.onVisibleChange;\n    setTriggerVisible(newVisible);\n\n    if (typeof onVisibleChangeProp === 'function') {\n      onVisibleChangeProp(newVisible);\n    }\n  };\n\n  var getMenuElement = function getMenuElement() {\n    var overlayElement = getOverlayElement();\n    return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-arrow\")\n    }), overlayElement);\n  };\n\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    var overlay = props.overlay;\n\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n\n    return getMenuElement();\n  };\n\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n        alignPoint = props.alignPoint;\n\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n\n    return !alignPoint;\n  };\n\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n\n    return \"\".concat(prefixCls, \"-open\");\n  };\n\n  var renderChildren = function renderChildren() {\n    var children = props.children;\n    var childrenProps = children.props ? children.props : {};\n    var childClassName = classNames(childrenProps.className, getOpenClassName());\n    return mergedVisible && children ? /*#__PURE__*/React.cloneElement(children, {\n      className: childClassName\n    }) : children;\n  };\n\n  var triggerHideAction = hideAction;\n\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n\n  return /*#__PURE__*/React.createElement(Trigger, _objectSpread(_objectSpread({\n    builtinPlacements: placements\n  }, otherProps), {}, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classNames(overlayClassName, _defineProperty({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction || [],\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: onVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), renderChildren());\n}\n\nexport default /*#__PURE__*/React.forwardRef(Dropdown);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,kBAAkB,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;AACjO,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,0BAA0B;AAEvD,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5B,IAAIC,YAAY,GAAGF,KAAK,CAACG,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IACtDE,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,gBAAgB,GAAGT,KAAK,CAACU,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEE,iBAAiB,GAAGX,KAAK,CAACY,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGd,UAAU,GAAGc,iBAAiB;IAC1EE,iBAAiB,GAAGb,KAAK,CAACa,iBAAiB;IAC3CC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,UAAU,GAAGf,KAAK,CAACe,UAAU;IAC7BC,gBAAgB,GAAGhB,KAAK,CAACgB,gBAAgB;IACzCC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,OAAO,GAAGlB,KAAK,CAACkB,OAAO;IACvBC,cAAc,GAAGnB,KAAK,CAACoB,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,cAAc;IAChEE,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,UAAU,GAAG9B,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EAE3D,IAAI8B,eAAe,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,CAAC;IAClCC,gBAAgB,GAAGlC,cAAc,CAACgC,eAAe,EAAE,CAAC,CAAC;IACrDG,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,aAAa,GAAG,SAAS,IAAI5B,KAAK,GAAGkB,OAAO,GAAGQ,cAAc;EACjE,IAAIG,UAAU,GAAGnC,KAAK,CAACoC,MAAM,CAAC,IAAI,CAAC;EACnCpC,KAAK,CAACqC,mBAAmB,CAAC9B,GAAG,EAAE,YAAY;IACzC,OAAO4B,UAAU,CAACG,OAAO;EAC3B,CAAC,CAAC;EACFlC,gBAAgB,CAAC;IACfoB,OAAO,EAAEU,aAAa;IACtBD,iBAAiB,EAAEA,iBAAiB;IACpCE,UAAU,EAAEA,UAAU;IACtBI,eAAe,EAAEjC,KAAK,CAACiC,eAAe;IACtCZ,SAAS,EAAEA;EACb,CAAC,CAAC;EAEF,IAAIa,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,IAAIC,OAAO,GAAGnC,KAAK,CAACmC,OAAO;IAC3B,IAAIC,cAAc;IAElB,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MACjCC,cAAc,GAAGD,OAAO,CAAC,CAAC;IAC5B,CAAC,MAAM;MACLC,cAAc,GAAGD,OAAO;IAC1B;IAEA,OAAOC,cAAc;EACvB,CAAC;EAED,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAE;IAChC,IAAIC,cAAc,GAAGvC,KAAK,CAACuC,cAAc;IACzCZ,iBAAiB,CAAC,KAAK,CAAC;IAExB,IAAIY,cAAc,EAAE;MAClBA,cAAc,CAACD,CAAC,CAAC;IACnB;EACF,CAAC;EAED,IAAIL,eAAe,GAAG,SAASA,eAAeA,CAACO,UAAU,EAAE;IACzD,IAAIC,mBAAmB,GAAGzC,KAAK,CAACiC,eAAe;IAC/CN,iBAAiB,CAACa,UAAU,CAAC;IAE7B,IAAI,OAAOC,mBAAmB,KAAK,UAAU,EAAE;MAC7CA,mBAAmB,CAACD,UAAU,CAAC;IACjC;EACF,CAAC;EAED,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,IAAIN,cAAc,GAAGF,iBAAiB,CAAC,CAAC;IACxC,OAAO,aAAaxC,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAACkD,QAAQ,EAAE,IAAI,EAAEzC,KAAK,IAAI,aAAaT,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;MAC7GE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACzC,SAAS,EAAE,QAAQ;IAC1C,CAAC,CAAC,EAAE+B,cAAc,CAAC;EACrB,CAAC;EAED,IAAIW,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;IAC7D,IAAIZ,OAAO,GAAGnC,KAAK,CAACmC,OAAO;IAE3B,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOO,cAAc;IACvB;IAEA,OAAOA,cAAc,CAAC,CAAC;EACzB,CAAC;EAED,IAAIM,8BAA8B,GAAG,SAASA,8BAA8BA,CAAA,EAAG;IAC7E,IAAIC,2BAA2B,GAAGjD,KAAK,CAACiD,2BAA2B;MAC/DC,UAAU,GAAGlD,KAAK,CAACkD,UAAU;IAEjC,IAAI,6BAA6B,IAAIlD,KAAK,EAAE;MAC1C,OAAOiD,2BAA2B;IACpC;IAEA,OAAO,CAACC,UAAU;EACpB,CAAC;EAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,IAAIC,aAAa,GAAGpD,KAAK,CAACoD,aAAa;IAEvC,IAAIA,aAAa,KAAKC,SAAS,EAAE;MAC/B,OAAOD,aAAa;IACtB;IAEA,OAAO,EAAE,CAACN,MAAM,CAACzC,SAAS,EAAE,OAAO,CAAC;EACtC,CAAC;EAED,IAAIiD,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,IAAIC,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ;IAC7B,IAAIC,aAAa,GAAGD,QAAQ,CAACvD,KAAK,GAAGuD,QAAQ,CAACvD,KAAK,GAAG,CAAC,CAAC;IACxD,IAAIyD,cAAc,GAAG7D,UAAU,CAAC4D,aAAa,CAACX,SAAS,EAAEM,gBAAgB,CAAC,CAAC,CAAC;IAC5E,OAAOvB,aAAa,IAAI2B,QAAQ,GAAG,aAAa7D,KAAK,CAACgE,YAAY,CAACH,QAAQ,EAAE;MAC3EV,SAAS,EAAEY;IACb,CAAC,CAAC,GAAGF,QAAQ;EACf,CAAC;EAED,IAAII,iBAAiB,GAAG5C,UAAU;EAElC,IAAI,CAAC4C,iBAAiB,IAAIvC,OAAO,CAACwC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;IAC/DD,iBAAiB,GAAG,CAAC,OAAO,CAAC;EAC/B;EAEA,OAAO,aAAajE,KAAK,CAACiD,aAAa,CAAChD,OAAO,EAAEL,aAAa,CAACA,aAAa,CAAC;IAC3EuE,iBAAiB,EAAEjD;EACrB,CAAC,EAAEU,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IAClBjB,SAAS,EAAEA,SAAS;IACpBJ,GAAG,EAAE4B,UAAU;IACfiC,cAAc,EAAElE,UAAU,CAACoB,gBAAgB,EAAE3B,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACyD,MAAM,CAACzC,SAAS,EAAE,aAAa,CAAC,EAAEF,KAAK,CAAC,CAAC;IAC7G4D,UAAU,EAAE9C,YAAY;IACxB+C,MAAM,EAAE5C,OAAO;IACfN,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAE4C,iBAAiB,IAAI,EAAE;IACnCM,cAAc,EAAEvD,SAAS;IACzBwD,UAAU,EAAE1D,KAAK;IACjB2D,mBAAmB,EAAE7D,cAAc;IACnC8D,cAAc,EAAE7D,SAAS;IACzB8D,YAAY,EAAEzC,aAAa;IAC3B0C,OAAO,EAAEtB,8BAA8B,CAAC,CAAC,GAAG,UAAU,GAAG,EAAE;IAC3DuB,KAAK,EAAExB,sBAAsB,CAAC,CAAC;IAC/ByB,oBAAoB,EAAEvC,eAAe;IACrCwC,YAAY,EAAEpC,OAAO;IACrBxB,iBAAiB,EAAEA;EACrB,CAAC,CAAC,EAAEyC,cAAc,CAAC,CAAC,CAAC;AACvB;AAEA,eAAe,aAAa5D,KAAK,CAACgF,UAAU,CAAC3E,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}