{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _none = _interopRequireDefault(require(\"./none.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n        sij0 = si[j][1] || 0,\n        sij1 = si[j - 1][1] || 0,\n        s3 = (sij0 - sij1) / 2;\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n          skj0 = sk[j][1] || 0,\n          skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n      s1 += sij0, s2 += s3 * sij0;\n    }\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  (0, _none.default)(series, order);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_none", "_interopRequireDefault", "require", "obj", "__esModule", "series", "order", "n", "length", "m", "s0", "y", "j", "i", "s1", "s2", "si", "sij0", "sij1", "s3", "k", "sk", "skj0", "skj1"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/offset/wiggle.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _none = _interopRequireDefault(require(\"./none.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n          sij0 = si[j][1] || 0,\n          sij1 = si[j - 1][1] || 0,\n          s3 = (sij0 - sij1) / 2;\n\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n            skj0 = sk[j][1] || 0,\n            skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n\n      s1 += sij0, s2 += s3 * sij0;\n    }\n\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  (0, _none.default)(series, order);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,KAAK,GAAGC,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,QAAQA,CAACM,MAAM,EAAEC,KAAK,EAAE;EAC/B,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,IAAI,EAAE,CAACC,CAAC,GAAG,CAACC,EAAE,GAAGL,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEE,MAAM,IAAI,CAAC,CAAC,EAAE;EAE/E,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEF,EAAE,EAAED,CAAC,EAAEF,CAAC,EAAEK,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;IAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEF,CAAC,GAAGN,CAAC,EAAE,EAAEM,CAAC,EAAE;MAC1C,IAAIG,EAAE,GAAGX,MAAM,CAACC,KAAK,CAACO,CAAC,CAAC,CAAC;QACrBI,IAAI,GAAGD,EAAE,CAACJ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACpBM,IAAI,GAAGF,EAAE,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACxBO,EAAE,GAAG,CAACF,IAAI,GAAGC,IAAI,IAAI,CAAC;MAE1B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,EAAE,EAAEO,CAAC,EAAE;QAC1B,IAAIC,EAAE,GAAGhB,MAAM,CAACC,KAAK,CAACc,CAAC,CAAC,CAAC;UACrBE,IAAI,GAAGD,EAAE,CAACT,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;UACpBW,IAAI,GAAGF,EAAE,CAACT,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5BO,EAAE,IAAIG,IAAI,GAAGC,IAAI;MACnB;MAEAT,EAAE,IAAIG,IAAI,EAAEF,EAAE,IAAII,EAAE,GAAGF,IAAI;IAC7B;IAEAP,EAAE,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIF,EAAE,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC;IAChC,IAAIG,EAAE,EAAEH,CAAC,IAAII,EAAE,GAAGD,EAAE;EACtB;EAEAJ,EAAE,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIF,EAAE,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC;EAChC,CAAC,CAAC,EAAEX,KAAK,CAACF,OAAO,EAAEO,MAAM,EAAEC,KAAK,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}