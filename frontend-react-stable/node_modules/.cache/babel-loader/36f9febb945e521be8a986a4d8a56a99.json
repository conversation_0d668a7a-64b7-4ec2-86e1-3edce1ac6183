{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Button,Typography,Space,Divider,message,Spin,InputNumber,Checkbox,Progress,Alert,Row,Col,Statistic,Select,List,Tag}from'antd';import{PlayCircleOutlined,SettingOutlined,FolderOutlined,FileTextOutlined,ExperimentOutlined}from'@ant-design/icons';import{modelTrainingAPI}from'../services/api';import{startBatchTrainingAsync}from'../services/taskApi';import useTaskManager from'../hooks/useTaskManager';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const BatchTrainingPage=()=>{var _taskResult$summary,_taskResult$summary2,_taskResult$summary3;// 状态管理\nconst[loading,setLoading]=useState(false);const[csvDirectories,setCsvDirectories]=useState([]);const[selectedDirectories,setSelectedDirectories]=useState([]);const[baseDir,setBaseDir]=useState('/data');// 训练参数\nconst[selectedProts,setSelectedProts]=useState(['TCP']);const[selectedDatatypes,setSelectedDatatypes]=useState({TCP:['spt_sip_dip'],UDP:[],ICMP:[]});const[learningRate,setLearningRate]=useState(0.001);const[batchSize,setBatchSize]=useState(32);const[epochs,setEpochs]=useState(100);const[sequenceLength,setSequenceLength]=useState(10);const[hiddenSize,setHiddenSize]=useState(50);const[numLayers,setNumLayers]=useState(2);const[dropout,setDropout]=useState(0.2);const[outputFolder,setOutputFolder]=useState('/data/output');// 任务管理\nconst{currentTask,isTaskRunning,startTask,stopTask,taskProgress,taskStatus,taskResult}=useTaskManager();// 协议和数据类型选项\nconst protocolOptions=['TCP','UDP','ICMP'];const datatypeOptions={TCP:['spt_sip_dip','dpt_sip_dip','len_dpt_syn','seq_ack_dip'],UDP:['spt_sip_dip','dpt_sip_dip'],ICMP:['dip']};// 加载CSV目录列表\nconst loadCsvDirectories=async()=>{try{setLoading(true);const response=await modelTrainingAPI.listCsvDirectories(baseDir);setCsvDirectories(response.data.directories);}catch(error){console.error('加载CSV目录失败:',error);message.error('加载CSV目录失败');}finally{setLoading(false);}};useEffect(()=>{loadCsvDirectories();},[baseDir]);// 处理协议选择\nconst handleProtocolChange=protocols=>{setSelectedProts(protocols);// 清理未选择协议的数据类型\nconst newDatatypes={...selectedDatatypes};Object.keys(newDatatypes).forEach(prot=>{if(!protocols.includes(prot)){newDatatypes[prot]=[];}});setSelectedDatatypes(newDatatypes);};// 处理数据类型选择\nconst handleDatatypeChange=(protocol,datatypes)=>{setSelectedDatatypes(prev=>({...prev,[protocol]:datatypes}));};// 验证表单\nconst validateForm=()=>{if(selectedDirectories.length===0){message.error('请至少选择一个CSV目录');return false;}if(selectedProts.length===0){message.error('请至少选择一种协议');return false;}const hasDatatype=selectedProts.some(prot=>selectedDatatypes[prot]&&selectedDatatypes[prot].length>0);if(!hasDatatype){message.error('请至少为一种协议选择数据类型');return false;}return true;};// 启动批量训练\nconst handleStartBatchTraining=async()=>{if(!validateForm())return;try{const formData=new FormData();formData.append('csv_directories',JSON.stringify(selectedDirectories));formData.append('selected_prots',JSON.stringify(selectedProts));formData.append('selected_datatypes',JSON.stringify(selectedDatatypes));formData.append('learning_rate',learningRate.toString());formData.append('batch_size',batchSize.toString());formData.append('epochs',epochs.toString());formData.append('sequence_length',sequenceLength.toString());formData.append('hidden_size',hiddenSize.toString());formData.append('num_layers',numLayers.toString());formData.append('dropout',dropout.toString());formData.append('output_folder',outputFolder);const response=await startBatchTrainingAsync(formData);if(response.success){message.success(`批量训练任务已启动，任务ID: ${response.task_id}`);startTask(response.task_id);}else{message.error('启动批量训练失败');}}catch(error){console.error('启动批量训练失败:',error);message.error('启动批量训练失败');}};// 计算总文件数\nconst getTotalFileCount=()=>{return selectedDirectories.reduce((total,dirPath)=>{const dir=csvDirectories.find(d=>d.directory===dirPath);return total+((dir===null||dir===void 0?void 0:dir.csv_count)||0);},0);};// 计算总模型数\nconst getTotalModelCount=()=>{const totalCombinations=selectedProts.reduce((total,prot)=>{var _selectedDatatypes$pr;return total+(((_selectedDatatypes$pr=selectedDatatypes[prot])===null||_selectedDatatypes$pr===void 0?void 0:_selectedDatatypes$pr.length)||0);},0);return getTotalFileCount()*totalCombinations;};return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(Title,{level:2,children:[/*#__PURE__*/_jsx(ExperimentOutlined,{}),\" \\u6279\\u91CF\\u6A21\\u578B\\u8BAD\\u7EC3\"]}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u9009\\u62E9\\u591A\\u4E2ACSV\\u76EE\\u5F55\\u8FDB\\u884C\\u6279\\u91CF\\u6A21\\u578B\\u8BAD\\u7EC3\\uFF0C\\u6BCF\\u4E2A\\u76EE\\u5F55\\u4E2D\\u7684\\u6240\\u6709CSV\\u6587\\u4EF6\\u90FD\\u5C06\\u88AB\\u7528\\u4E8E\\u8BAD\\u7EC3\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Row,{gutter:24,children:[/*#__PURE__*/_jsxs(Col,{span:16,children:[/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FolderOutlined,{}),\" CSV\\u76EE\\u5F55\\u9009\\u62E9\"]}),style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u57FA\\u7840\\u76EE\\u5F55: \"}),/*#__PURE__*/_jsxs(Select,{value:baseDir,onChange:setBaseDir,style:{width:200,marginLeft:8},children:[/*#__PURE__*/_jsx(Option,{value:\"/data\",children:\"/data\"}),/*#__PURE__*/_jsx(Option,{value:\"/data/input\",children:\"/data/input\"}),/*#__PURE__*/_jsx(Option,{value:\"/data/output\",children:\"/data/output\"})]}),/*#__PURE__*/_jsx(Button,{onClick:loadCsvDirectories,loading:loading,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]}),/*#__PURE__*/_jsx(Spin,{spinning:loading,children:/*#__PURE__*/_jsx(List,{dataSource:csvDirectories,renderItem:dir=>/*#__PURE__*/_jsx(List.Item,{children:/*#__PURE__*/_jsx(Checkbox,{checked:selectedDirectories.includes(dir.directory),onChange:e=>{if(e.target.checked){setSelectedDirectories([...selectedDirectories,dir.directory]);}else{setSelectedDirectories(selectedDirectories.filter(d=>d!==dir.directory));}},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(FolderOutlined,{}),/*#__PURE__*/_jsx(Text,{strong:true,children:dir.name}),/*#__PURE__*/_jsxs(Tag,{color:\"blue\",children:[dir.csv_count,\" \\u4E2ACSV\\u6587\\u4EF6\"]})]})})}),locale:{emptyText:'没有找到包含CSV文件的目录'}})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u534F\\u8BAE: \"}),/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedProts,onChange:handleProtocolChange,style:{width:'100%',marginTop:8},placeholder:\"\\u9009\\u62E9\\u534F\\u8BAE\",children:protocolOptions.map(prot=>/*#__PURE__*/_jsx(Option,{value:prot,children:prot},prot))})]}),selectedProts.map(prot=>{var _datatypeOptions;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{strong:true,children:[prot,\" \\u6570\\u636E\\u7C7B\\u578B: \"]}),/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedDatatypes[prot]||[],onChange:datatypes=>handleDatatypeChange(prot,datatypes),style:{width:'100%',marginTop:8},placeholder:`选择 ${prot} 数据类型`,children:(_datatypeOptions=datatypeOptions[prot])===null||_datatypeOptions===void 0?void 0:_datatypeOptions.map(datatype=>/*#__PURE__*/_jsx(Option,{value:datatype,children:datatype},datatype))})]},prot);})]})}),/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(SettingOutlined,{}),\" \\u8BAD\\u7EC3\\u53C2\\u6570\"]}),children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u5B66\\u4E60\\u7387: \",learningRate]}),/*#__PURE__*/_jsx(InputNumber,{min:0.0001,max:0.1,step:0.0001,value:learningRate,onChange:value=>setLearningRate(value||0.001),style:{width:'100%',marginTop:4}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u6279\\u91CF\\u5927\\u5C0F: \",batchSize]}),/*#__PURE__*/_jsx(InputNumber,{min:1,max:512,value:batchSize,onChange:value=>setBatchSize(value||32),style:{width:'100%',marginTop:4}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \",epochs]}),/*#__PURE__*/_jsx(InputNumber,{min:1,max:1000,value:epochs,onChange:value=>setEpochs(value||100),style:{width:'100%',marginTop:4}})]})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u5E8F\\u5217\\u957F\\u5EA6: \",sequenceLength]}),/*#__PURE__*/_jsx(InputNumber,{min:5,max:50,value:sequenceLength,onChange:value=>setSequenceLength(value||10),style:{width:'100%',marginTop:4}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F: \",hiddenSize]}),/*#__PURE__*/_jsx(InputNumber,{min:10,max:200,value:hiddenSize,onChange:value=>setHiddenSize(value||50),style:{width:'100%',marginTop:4}})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u7F51\\u7EDC\\u5C42\\u6570: \",numLayers]}),/*#__PURE__*/_jsx(InputNumber,{min:1,max:5,value:numLayers,onChange:value=>setNumLayers(value||2),style:{width:'100%',marginTop:4}})]})]})})]})})]}),/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Card,{title:\"\\u8BAD\\u7EC3\\u6982\\u89C8\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u9009\\u62E9\\u7684\\u76EE\\u5F55\\u6570\",value:selectedDirectories.length,prefix:/*#__PURE__*/_jsx(FolderOutlined,{})}),/*#__PURE__*/_jsx(Statistic,{title:\"\\u603BCSV\\u6587\\u4EF6\\u6570\",value:getTotalFileCount(),prefix:/*#__PURE__*/_jsx(FileTextOutlined,{})}),/*#__PURE__*/_jsx(Statistic,{title:\"\\u9884\\u8BA1\\u8BAD\\u7EC3\\u6A21\\u578B\\u6570\",value:getTotalModelCount(),prefix:/*#__PURE__*/_jsx(ExperimentOutlined,{})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u8BAD\\u7EC3\\u63A7\\u5236\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleStartBatchTraining,disabled:isTaskRunning||selectedDirectories.length===0,size:\"large\",style:{width:'100%'},children:isTaskRunning?'训练中...':'开始批量训练'}),isTaskRunning&&/*#__PURE__*/_jsx(Button,{danger:true,onClick:stopTask,style:{width:'100%'},children:\"\\u505C\\u6B62\\u8BAD\\u7EC3\"}),isTaskRunning&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BAD\\u7EC3\\u8FDB\\u5EA6:\"}),/*#__PURE__*/_jsx(Progress,{percent:taskProgress,status:taskStatus==='failed'?'exception':'active',style:{marginTop:8}}),currentTask&&/*#__PURE__*/_jsxs(Text,{type:\"secondary\",style:{fontSize:'12px'},children:[\"\\u4EFB\\u52A1ID: \",currentTask.task_id]})]}),taskResult&&taskStatus==='completed'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u6279\\u91CF\\u8BAD\\u7EC3\\u5B8C\\u6210\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u6210\\u529F\\u5904\\u7406: \",((_taskResult$summary=taskResult.summary)===null||_taskResult$summary===void 0?void 0:_taskResult$summary.successful_files)||0,\" \\u4E2A\\u6587\\u4EF6\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5931\\u8D25\\u6587\\u4EF6: \",((_taskResult$summary2=taskResult.summary)===null||_taskResult$summary2===void 0?void 0:_taskResult$summary2.failed_files)||0,\" \\u4E2A\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u603B\\u8BAD\\u7EC3\\u6A21\\u578B: \",((_taskResult$summary3=taskResult.summary)===null||_taskResult$summary3===void 0?void 0:_taskResult$summary3.total_models)||0,\" \\u4E2A\"]})]}),type:\"success\",showIcon:true}),taskStatus==='failed'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u6279\\u91CF\\u8BAD\\u7EC3\\u5931\\u8D25\",description:\"\\u8BF7\\u68C0\\u67E5\\u65E5\\u5FD7\\u83B7\\u53D6\\u8BE6\\u7EC6\\u9519\\u8BEF\\u4FE1\\u606F\",type:\"error\",showIcon:true})]})})]})]})]});};export default BatchTrainingPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Select", "List", "Tag", "PlayCircleOutlined", "SettingOutlined", "FolderOutlined", "FileTextOutlined", "ExperimentOutlined", "modelTrainingAPI", "startBatchTrainingAsync", "useTaskManager", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "Option", "BatchTrainingPage", "_taskResult$summary", "_taskResult$summary2", "_taskResult$summary3", "loading", "setLoading", "csvDirectories", "setCsvDirectories", "selectedDirectories", "setSelectedDirectories", "baseDir", "setBaseDir", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "UDP", "ICMP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "currentTask", "isTaskRunning", "startTask", "stopTask", "taskProgress", "taskStatus", "taskResult", "protocolOptions", "datatypeOptions", "loadCsvDirectories", "response", "listCsvDirectories", "data", "directories", "error", "console", "handleProtocolChange", "protocols", "newDatatypes", "Object", "keys", "for<PERSON>ach", "prot", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "validateForm", "length", "hasDatatype", "some", "handleStartBatchTraining", "formData", "FormData", "append", "JSON", "stringify", "toString", "success", "task_id", "getTotalFileCount", "reduce", "total", "<PERSON><PERSON><PERSON>", "dir", "find", "d", "directory", "csv_count", "getTotalModelCount", "totalCombinations", "_selectedDatatypes$pr", "style", "padding", "children", "level", "type", "gutter", "span", "title", "marginBottom", "direction", "width", "strong", "value", "onChange", "marginLeft", "onClick", "spinning", "dataSource", "renderItem", "<PERSON><PERSON>", "checked", "e", "target", "filter", "name", "color", "locale", "emptyText", "mode", "marginTop", "placeholder", "map", "_datatypeOptions", "datatype", "min", "max", "step", "prefix", "icon", "disabled", "size", "danger", "percent", "status", "fontSize", "description", "summary", "successful_files", "failed_files", "total_models", "showIcon"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/BatchTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Select,\n  List,\n  Tag,\n  Modal,\n} from 'antd';\nimport {\n  PlayCircleOutlined,\n  SettingOutlined,\n  FolderOutlined,\n  FileTextOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ExperimentOutlined\n} from '@ant-design/icons';\nimport { modelTrainingAPI } from '../services/api';\nimport { startBatchTrainingAsync } from '../services/taskApi';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\ninterface CsvDirectory {\n  directory: string;\n  name: string;\n  csv_count: number;\n  csv_files: string[];\n}\n\nconst BatchTrainingPage: React.FC = () => {\n  // 状态管理\n  const [loading, setLoading] = useState(false);\n  const [csvDirectories, setCsvDirectories] = useState<CsvDirectory[]>([]);\n  const [selectedDirectories, setSelectedDirectories] = useState<string[]>([]);\n  const [baseDir, setBaseDir] = useState('/data');\n  \n  // 训练参数\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip'],\n    UDP: [],\n    ICMP: []\n  });\n  const [learningRate, setLearningRate] = useState(0.001);\n  const [batchSize, setBatchSize] = useState(32);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('/data/output');\n\n  // 任务管理\n  const { \n    currentTask, \n    isTaskRunning, \n    startTask, \n    stopTask, \n    taskProgress, \n    taskStatus,\n    taskResult \n  } = useTaskManager();\n\n  // 协议和数据类型选项\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 加载CSV目录列表\n  const loadCsvDirectories = async () => {\n    try {\n      setLoading(true);\n      const response = await modelTrainingAPI.listCsvDirectories(baseDir);\n      setCsvDirectories(response.data.directories);\n    } catch (error) {\n      console.error('加载CSV目录失败:', error);\n      message.error('加载CSV目录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadCsvDirectories();\n  }, [baseDir]);\n\n  // 处理协议选择\n  const handleProtocolChange = (protocols: string[]) => {\n    setSelectedProts(protocols);\n    // 清理未选择协议的数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!protocols.includes(prot)) {\n        newDatatypes[prot] = [];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 处理数据类型选择\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证表单\n  const validateForm = () => {\n    if (selectedDirectories.length === 0) {\n      message.error('请至少选择一个CSV目录');\n      return false;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return false;\n    }\n    const hasDatatype = selectedProts.some(prot => \n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n    if (!hasDatatype) {\n      message.error('请至少为一种协议选择数据类型');\n      return false;\n    }\n    return true;\n  };\n\n  // 启动批量训练\n  const handleStartBatchTraining = async () => {\n    if (!validateForm()) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('csv_directories', JSON.stringify(selectedDirectories));\n      formData.append('selected_prots', JSON.stringify(selectedProts));\n      formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n      formData.append('learning_rate', learningRate.toString());\n      formData.append('batch_size', batchSize.toString());\n      formData.append('epochs', epochs.toString());\n      formData.append('sequence_length', sequenceLength.toString());\n      formData.append('hidden_size', hiddenSize.toString());\n      formData.append('num_layers', numLayers.toString());\n      formData.append('dropout', dropout.toString());\n      formData.append('output_folder', outputFolder);\n\n      const response = await startBatchTrainingAsync(formData);\n      \n      if (response.success) {\n        message.success(`批量训练任务已启动，任务ID: ${response.task_id}`);\n        startTask(response.task_id);\n      } else {\n        message.error('启动批量训练失败');\n      }\n    } catch (error) {\n      console.error('启动批量训练失败:', error);\n      message.error('启动批量训练失败');\n    }\n  };\n\n  // 计算总文件数\n  const getTotalFileCount = () => {\n    return selectedDirectories.reduce((total, dirPath) => {\n      const dir = csvDirectories.find(d => d.directory === dirPath);\n      return total + (dir?.csv_count || 0);\n    }, 0);\n  };\n\n  // 计算总模型数\n  const getTotalModelCount = () => {\n    const totalCombinations = selectedProts.reduce((total, prot) => {\n      return total + (selectedDatatypes[prot]?.length || 0);\n    }, 0);\n    return getTotalFileCount() * totalCombinations;\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <ExperimentOutlined /> 批量模型训练\n      </Title>\n      <Text type=\"secondary\">\n        选择多个CSV目录进行批量模型训练，每个目录中的所有CSV文件都将被用于训练\n      </Text>\n\n      <Divider />\n\n      <Row gutter={24}>\n        {/* 左侧：目录选择和参数配置 */}\n        <Col span={16}>\n          {/* CSV目录选择 */}\n          <Card title={<><FolderOutlined /> CSV目录选择</>} style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>基础目录: </Text>\n                <Select\n                  value={baseDir}\n                  onChange={setBaseDir}\n                  style={{ width: 200, marginLeft: 8 }}\n                >\n                  <Option value=\"/data\">/data</Option>\n                  <Option value=\"/data/input\">/data/input</Option>\n                  <Option value=\"/data/output\">/data/output</Option>\n                </Select>\n                <Button \n                  onClick={loadCsvDirectories} \n                  loading={loading}\n                  style={{ marginLeft: 8 }}\n                >\n                  刷新\n                </Button>\n              </div>\n\n              <Spin spinning={loading}>\n                <List\n                  dataSource={csvDirectories}\n                  renderItem={(dir) => (\n                    <List.Item>\n                      <Checkbox\n                        checked={selectedDirectories.includes(dir.directory)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setSelectedDirectories([...selectedDirectories, dir.directory]);\n                          } else {\n                            setSelectedDirectories(selectedDirectories.filter(d => d !== dir.directory));\n                          }\n                        }}\n                      >\n                        <Space>\n                          <FolderOutlined />\n                          <Text strong>{dir.name}</Text>\n                          <Tag color=\"blue\">{dir.csv_count} 个CSV文件</Tag>\n                        </Space>\n                      </Checkbox>\n                    </List.Item>\n                  )}\n                  locale={{ emptyText: '没有找到包含CSV文件的目录' }}\n                />\n              </Spin>\n            </Space>\n          </Card>\n\n          {/* 协议和数据类型选择 */}\n          <Card title=\"协议和数据类型选择\" style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>选择协议: </Text>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedProts}\n                  onChange={handleProtocolChange}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择协议\"\n                >\n                  {protocolOptions.map(prot => (\n                    <Option key={prot} value={prot}>{prot}</Option>\n                  ))}\n                </Select>\n              </div>\n\n              {selectedProts.map(prot => (\n                <div key={prot}>\n                  <Text strong>{prot} 数据类型: </Text>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedDatatypes[prot] || []}\n                    onChange={(datatypes) => handleDatatypeChange(prot, datatypes)}\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder={`选择 ${prot} 数据类型`}\n                  >\n                    {datatypeOptions[prot as keyof typeof datatypeOptions]?.map(datatype => (\n                      <Option key={datatype} value={datatype}>{datatype}</Option>\n                    ))}\n                  </Select>\n                </div>\n              ))}\n            </Space>\n          </Card>\n\n          {/* 训练参数 */}\n          <Card title={<><SettingOutlined /> 训练参数</>}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text>学习率: {learningRate}</Text>\n                    <InputNumber\n                      min={0.0001}\n                      max={0.1}\n                      step={0.0001}\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.001)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>批量大小: {batchSize}</Text>\n                    <InputNumber\n                      min={1}\n                      max={512}\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 32)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>训练轮数: {epochs}</Text>\n                    <InputNumber\n                      min={1}\n                      max={1000}\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                </Space>\n              </Col>\n              <Col span={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div>\n                    <Text>序列长度: {sequenceLength}</Text>\n                    <InputNumber\n                      min={5}\n                      max={50}\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>隐藏层大小: {hiddenSize}</Text>\n                    <InputNumber\n                      min={10}\n                      max={200}\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                  <div>\n                    <Text>网络层数: {numLayers}</Text>\n                    <InputNumber\n                      min={1}\n                      max={5}\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      style={{ width: '100%', marginTop: 4 }}\n                    />\n                  </div>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 右侧：训练状态和结果 */}\n        <Col span={8}>\n          {/* 训练概览 */}\n          <Card title=\"训练概览\" style={{ marginBottom: 16 }}>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Statistic\n                title=\"选择的目录数\"\n                value={selectedDirectories.length}\n                prefix={<FolderOutlined />}\n              />\n              <Statistic\n                title=\"总CSV文件数\"\n                value={getTotalFileCount()}\n                prefix={<FileTextOutlined />}\n              />\n              <Statistic\n                title=\"预计训练模型数\"\n                value={getTotalModelCount()}\n                prefix={<ExperimentOutlined />}\n              />\n            </Space>\n          </Card>\n\n          {/* 训练控制 */}\n          <Card title=\"训练控制\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Button\n                type=\"primary\"\n                icon={<PlayCircleOutlined />}\n                onClick={handleStartBatchTraining}\n                disabled={isTaskRunning || selectedDirectories.length === 0}\n                size=\"large\"\n                style={{ width: '100%' }}\n              >\n                {isTaskRunning ? '训练中...' : '开始批量训练'}\n              </Button>\n\n              {isTaskRunning && (\n                <Button\n                  danger\n                  onClick={stopTask}\n                  style={{ width: '100%' }}\n                >\n                  停止训练\n                </Button>\n              )}\n\n              {/* 训练进度 */}\n              {isTaskRunning && (\n                <div>\n                  <Text strong>训练进度:</Text>\n                  <Progress \n                    percent={taskProgress} \n                    status={taskStatus === 'failed' ? 'exception' : 'active'}\n                    style={{ marginTop: 8 }}\n                  />\n                  {currentTask && (\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                      任务ID: {currentTask.task_id}\n                    </Text>\n                  )}\n                </div>\n              )}\n\n              {/* 训练结果 */}\n              {taskResult && taskStatus === 'completed' && (\n                <Alert\n                  message=\"批量训练完成\"\n                  description={\n                    <div>\n                      <p>成功处理: {taskResult.summary?.successful_files || 0} 个文件</p>\n                      <p>失败文件: {taskResult.summary?.failed_files || 0} 个</p>\n                      <p>总训练模型: {taskResult.summary?.total_models || 0} 个</p>\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n              )}\n\n              {taskStatus === 'failed' && (\n                <Alert\n                  message=\"批量训练失败\"\n                  description=\"请检查日志获取详细错误信息\"\n                  type=\"error\"\n                  showIcon\n                />\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default BatchTrainingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,QAAQ,CACRC,QAAQ,CACRC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,MAAM,CACNC,IAAI,CACJC,GAAG,KAEE,MAAM,CACb,OACEC,kBAAkB,CAClBC,eAAe,CACfC,cAAc,CACdC,gBAAgB,CAGhBC,kBAAkB,KACb,mBAAmB,CAC1B,OAASC,gBAAgB,KAAQ,iBAAiB,CAClD,OAASC,uBAAuB,KAAQ,qBAAqB,CAC7D,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG9B,UAAU,CAClC,KAAM,CAAE+B,MAAO,CAAC,CAAGnB,MAAM,CASzB,KAAM,CAAAoB,iBAA2B,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CACxC;AACA,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0C,cAAc,CAAEC,iBAAiB,CAAC,CAAG3C,QAAQ,CAAiB,EAAE,CAAC,CACxE,KAAM,CAAC4C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7C,QAAQ,CAAW,EAAE,CAAC,CAC5E,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,OAAO,CAAC,CAE/C;AACA,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC,CACrE,KAAM,CAACkD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnD,QAAQ,CAA4B,CACpFoD,GAAG,CAAE,CAAC,aAAa,CAAC,CACpBC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EACR,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGxD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACyD,SAAS,CAAEC,YAAY,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2D,MAAM,CAAEC,SAAS,CAAC,CAAG5D,QAAQ,CAAC,GAAG,CAAC,CACzC,KAAM,CAAC6D,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+D,UAAU,CAAEC,aAAa,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiE,SAAS,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACmE,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,GAAG,CAAC,CAC3C,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,cAAc,CAAC,CAEhE;AACA,KAAM,CACJuE,WAAW,CACXC,aAAa,CACbC,SAAS,CACTC,QAAQ,CACRC,YAAY,CACZC,UAAU,CACVC,UACF,CAAC,CAAGnD,cAAc,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAoD,eAAe,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CAC9C,KAAM,CAAAC,eAAe,CAAG,CACtB3B,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CACjEC,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAC,CACnCC,IAAI,CAAE,CAAC,KAAK,CACd,CAAC,CAED;AACA,KAAM,CAAA0B,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFvC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwC,QAAQ,CAAG,KAAM,CAAAzD,gBAAgB,CAAC0D,kBAAkB,CAACpC,OAAO,CAAC,CACnEH,iBAAiB,CAACsC,QAAQ,CAACE,IAAI,CAACC,WAAW,CAAC,CAC9C,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC9E,OAAO,CAAC8E,KAAK,CAAC,WAAW,CAAC,CAC5B,CAAC,OAAS,CACR5C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDxC,SAAS,CAAC,IAAM,CACd+E,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,CAAClC,OAAO,CAAC,CAAC,CAEb;AACA,KAAM,CAAAyC,oBAAoB,CAAIC,SAAmB,EAAK,CACpDvC,gBAAgB,CAACuC,SAAS,CAAC,CAC3B;AACA,KAAM,CAAAC,YAAY,CAAG,CAAE,GAAGvC,iBAAkB,CAAC,CAC7CwC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG,OAAO,CAACC,IAAI,EAAI,CACxC,GAAI,CAACL,SAAS,CAACM,QAAQ,CAACD,IAAI,CAAC,CAAE,CAC7BJ,YAAY,CAACI,IAAI,CAAC,CAAG,EAAE,CACzB,CACF,CAAC,CAAC,CACF1C,oBAAoB,CAACsC,YAAY,CAAC,CACpC,CAAC,CAED;AACA,KAAM,CAAAM,oBAAoB,CAAGA,CAACC,QAAgB,CAAEC,SAAmB,GAAK,CACtE9C,oBAAoB,CAAC+C,IAAI,GAAK,CAC5B,GAAGA,IAAI,CACP,CAACF,QAAQ,EAAGC,SACd,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIvD,mBAAmB,CAACwD,MAAM,GAAK,CAAC,CAAE,CACpC7F,OAAO,CAAC8E,KAAK,CAAC,cAAc,CAAC,CAC7B,MAAO,MAAK,CACd,CACA,GAAIrC,aAAa,CAACoD,MAAM,GAAK,CAAC,CAAE,CAC9B7F,OAAO,CAAC8E,KAAK,CAAC,WAAW,CAAC,CAC1B,MAAO,MAAK,CACd,CACA,KAAM,CAAAgB,WAAW,CAAGrD,aAAa,CAACsD,IAAI,CAACT,IAAI,EACzC3C,iBAAiB,CAAC2C,IAAI,CAAC,EAAI3C,iBAAiB,CAAC2C,IAAI,CAAC,CAACO,MAAM,CAAG,CAC9D,CAAC,CACD,GAAI,CAACC,WAAW,CAAE,CAChB9F,OAAO,CAAC8E,KAAK,CAAC,gBAAgB,CAAC,CAC/B,MAAO,MAAK,CACd,CACA,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAkB,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CAC3C,GAAI,CAACJ,YAAY,CAAC,CAAC,CAAE,OAErB,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEC,IAAI,CAACC,SAAS,CAAChE,mBAAmB,CAAC,CAAC,CACvE4D,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEC,IAAI,CAACC,SAAS,CAAC5D,aAAa,CAAC,CAAC,CAChEwD,QAAQ,CAACE,MAAM,CAAC,oBAAoB,CAAEC,IAAI,CAACC,SAAS,CAAC1D,iBAAiB,CAAC,CAAC,CACxEsD,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEnD,YAAY,CAACsD,QAAQ,CAAC,CAAC,CAAC,CACzDL,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEjD,SAAS,CAACoD,QAAQ,CAAC,CAAC,CAAC,CACnDL,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAE/C,MAAM,CAACkD,QAAQ,CAAC,CAAC,CAAC,CAC5CL,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAE7C,cAAc,CAACgD,QAAQ,CAAC,CAAC,CAAC,CAC7DL,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE3C,UAAU,CAAC8C,QAAQ,CAAC,CAAC,CAAC,CACrDL,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEzC,SAAS,CAAC4C,QAAQ,CAAC,CAAC,CAAC,CACnDL,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEvC,OAAO,CAAC0C,QAAQ,CAAC,CAAC,CAAC,CAC9CL,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAErC,YAAY,CAAC,CAE9C,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAxD,uBAAuB,CAAC+E,QAAQ,CAAC,CAExD,GAAIvB,QAAQ,CAAC6B,OAAO,CAAE,CACpBvG,OAAO,CAACuG,OAAO,CAAC,mBAAmB7B,QAAQ,CAAC8B,OAAO,EAAE,CAAC,CACtDtC,SAAS,CAACQ,QAAQ,CAAC8B,OAAO,CAAC,CAC7B,CAAC,IAAM,CACLxG,OAAO,CAAC8E,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAE,MAAOA,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC9E,OAAO,CAAC8E,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,MAAO,CAAApE,mBAAmB,CAACqE,MAAM,CAAC,CAACC,KAAK,CAAEC,OAAO,GAAK,CACpD,KAAM,CAAAC,GAAG,CAAG1E,cAAc,CAAC2E,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,SAAS,GAAKJ,OAAO,CAAC,CAC7D,MAAO,CAAAD,KAAK,EAAI,CAAAE,GAAG,SAAHA,GAAG,iBAAHA,GAAG,CAAEI,SAAS,GAAI,CAAC,CAAC,CACtC,CAAC,CAAE,CAAC,CAAC,CACP,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,iBAAiB,CAAG1E,aAAa,CAACiE,MAAM,CAAC,CAACC,KAAK,CAAErB,IAAI,GAAK,KAAA8B,qBAAA,CAC9D,MAAO,CAAAT,KAAK,EAAI,EAAAS,qBAAA,CAAAzE,iBAAiB,CAAC2C,IAAI,CAAC,UAAA8B,qBAAA,iBAAvBA,qBAAA,CAAyBvB,MAAM,GAAI,CAAC,CAAC,CACvD,CAAC,CAAE,CAAC,CAAC,CACL,MAAO,CAAAY,iBAAiB,CAAC,CAAC,CAAGU,iBAAiB,CAChD,CAAC,CAED,mBACE5F,KAAA,QAAK8F,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eAC9BhG,KAAA,CAACG,KAAK,EAAC8F,KAAK,CAAE,CAAE,CAAAD,QAAA,eACdlG,IAAA,CAACL,kBAAkB,GAAE,CAAC,wCACxB,EAAO,CAAC,cACRK,IAAA,CAACM,IAAI,EAAC8F,IAAI,CAAC,WAAW,CAAAF,QAAA,CAAC,wMAEvB,CAAM,CAAC,cAEPlG,IAAA,CAACtB,OAAO,GAAE,CAAC,cAEXwB,KAAA,CAACjB,GAAG,EAACoH,MAAM,CAAE,EAAG,CAAAH,QAAA,eAEdhG,KAAA,CAAChB,GAAG,EAACoH,IAAI,CAAE,EAAG,CAAAJ,QAAA,eAEZlG,IAAA,CAAC1B,IAAI,EAACiI,KAAK,cAAErG,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eAAElG,IAAA,CAACP,cAAc,GAAE,CAAC,+BAAQ,EAAE,CAAE,CAACuG,KAAK,CAAE,CAAEQ,YAAY,CAAE,EAAG,CAAE,CAAAN,QAAA,cACxEhG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDhG,KAAA,QAAAgG,QAAA,eACElG,IAAA,CAACM,IAAI,EAACqG,MAAM,MAAAT,QAAA,CAAC,4BAAM,CAAM,CAAC,cAC1BhG,KAAA,CAACd,MAAM,EACLwH,KAAK,CAAE1F,OAAQ,CACf2F,QAAQ,CAAE1F,UAAW,CACrB6E,KAAK,CAAE,CAAEU,KAAK,CAAE,GAAG,CAAEI,UAAU,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAErClG,IAAA,CAACO,MAAM,EAACqG,KAAK,CAAC,OAAO,CAAAV,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpClG,IAAA,CAACO,MAAM,EAACqG,KAAK,CAAC,aAAa,CAAAV,QAAA,CAAC,aAAW,CAAQ,CAAC,cAChDlG,IAAA,CAACO,MAAM,EAACqG,KAAK,CAAC,cAAc,CAAAV,QAAA,CAAC,cAAY,CAAQ,CAAC,EAC5C,CAAC,cACTlG,IAAA,CAACzB,MAAM,EACLwI,OAAO,CAAE3D,kBAAmB,CAC5BxC,OAAO,CAAEA,OAAQ,CACjBoF,KAAK,CAAE,CAAEc,UAAU,CAAE,CAAE,CAAE,CAAAZ,QAAA,CAC1B,cAED,CAAQ,CAAC,EACN,CAAC,cAENlG,IAAA,CAACpB,IAAI,EAACoI,QAAQ,CAAEpG,OAAQ,CAAAsF,QAAA,cACtBlG,IAAA,CAACX,IAAI,EACH4H,UAAU,CAAEnG,cAAe,CAC3BoG,UAAU,CAAG1B,GAAG,eACdxF,IAAA,CAACX,IAAI,CAAC8H,IAAI,EAAAjB,QAAA,cACRlG,IAAA,CAAClB,QAAQ,EACPsI,OAAO,CAAEpG,mBAAmB,CAACkD,QAAQ,CAACsB,GAAG,CAACG,SAAS,CAAE,CACrDkB,QAAQ,CAAGQ,CAAC,EAAK,CACf,GAAIA,CAAC,CAACC,MAAM,CAACF,OAAO,CAAE,CACpBnG,sBAAsB,CAAC,CAAC,GAAGD,mBAAmB,CAAEwE,GAAG,CAACG,SAAS,CAAC,CAAC,CACjE,CAAC,IAAM,CACL1E,sBAAsB,CAACD,mBAAmB,CAACuG,MAAM,CAAC7B,CAAC,EAAIA,CAAC,GAAKF,GAAG,CAACG,SAAS,CAAC,CAAC,CAC9E,CACF,CAAE,CAAAO,QAAA,cAEFhG,KAAA,CAACzB,KAAK,EAAAyH,QAAA,eACJlG,IAAA,CAACP,cAAc,GAAE,CAAC,cAClBO,IAAA,CAACM,IAAI,EAACqG,MAAM,MAAAT,QAAA,CAAEV,GAAG,CAACgC,IAAI,CAAO,CAAC,cAC9BtH,KAAA,CAACZ,GAAG,EAACmI,KAAK,CAAC,MAAM,CAAAvB,QAAA,EAAEV,GAAG,CAACI,SAAS,CAAC,wBAAO,EAAK,CAAC,EACzC,CAAC,CACA,CAAC,CACF,CACX,CACF8B,MAAM,CAAE,CAAEC,SAAS,CAAE,gBAAiB,CAAE,CACzC,CAAC,CACE,CAAC,EACF,CAAC,CACJ,CAAC,cAGP3H,IAAA,CAAC1B,IAAI,EAACiI,KAAK,CAAC,wDAAW,CAACP,KAAK,CAAE,CAAEQ,YAAY,CAAE,EAAG,CAAE,CAAAN,QAAA,cAClDhG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDhG,KAAA,QAAAgG,QAAA,eACElG,IAAA,CAACM,IAAI,EAACqG,MAAM,MAAAT,QAAA,CAAC,4BAAM,CAAM,CAAC,cAC1BlG,IAAA,CAACZ,MAAM,EACLwI,IAAI,CAAC,UAAU,CACfhB,KAAK,CAAExF,aAAc,CACrByF,QAAQ,CAAElD,oBAAqB,CAC/BqC,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACvCC,WAAW,CAAC,0BAAM,CAAA5B,QAAA,CAEjBhD,eAAe,CAAC6E,GAAG,CAAC9D,IAAI,eACvBjE,IAAA,CAACO,MAAM,EAAYqG,KAAK,CAAE3C,IAAK,CAAAiC,QAAA,CAAEjC,IAAI,EAAxBA,IAAiC,CAC/C,CAAC,CACI,CAAC,EACN,CAAC,CAEL7C,aAAa,CAAC2G,GAAG,CAAC9D,IAAI,OAAA+D,gBAAA,oBACrB9H,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAACqG,MAAM,MAAAT,QAAA,EAAEjC,IAAI,CAAC,6BAAO,EAAM,CAAC,cACjCjE,IAAA,CAACZ,MAAM,EACLwI,IAAI,CAAC,UAAU,CACfhB,KAAK,CAAEtF,iBAAiB,CAAC2C,IAAI,CAAC,EAAI,EAAG,CACrC4C,QAAQ,CAAGxC,SAAS,EAAKF,oBAAoB,CAACF,IAAI,CAAEI,SAAS,CAAE,CAC/D2B,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACvCC,WAAW,CAAE,MAAM7D,IAAI,OAAQ,CAAAiC,QAAA,EAAA8B,gBAAA,CAE9B7E,eAAe,CAACc,IAAI,CAAiC,UAAA+D,gBAAA,iBAArDA,gBAAA,CAAuDD,GAAG,CAACE,QAAQ,eAClEjI,IAAA,CAACO,MAAM,EAAgBqG,KAAK,CAAEqB,QAAS,CAAA/B,QAAA,CAAE+B,QAAQ,EAApCA,QAA6C,CAC3D,CAAC,CACI,CAAC,GAZDhE,IAaL,CAAC,EACP,CAAC,EACG,CAAC,CACJ,CAAC,cAGPjE,IAAA,CAAC1B,IAAI,EAACiI,KAAK,cAAErG,KAAA,CAAAE,SAAA,EAAA8F,QAAA,eAAElG,IAAA,CAACR,eAAe,GAAE,CAAC,4BAAK,EAAE,CAAE,CAAA0G,QAAA,cACzChG,KAAA,CAACjB,GAAG,EAACoH,MAAM,CAAE,EAAG,CAAAH,QAAA,eACdlG,IAAA,CAACd,GAAG,EAACoH,IAAI,CAAE,EAAG,CAAAJ,QAAA,cACZhG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDhG,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,sBAAK,CAACvE,YAAY,EAAO,CAAC,cAChC3B,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,MAAO,CACZC,GAAG,CAAE,GAAI,CACTC,IAAI,CAAE,MAAO,CACbxB,KAAK,CAAEjF,YAAa,CACpBkF,QAAQ,CAAGD,KAAK,EAAKhF,eAAe,CAACgF,KAAK,EAAI,KAAK,CAAE,CACrDZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,cACN3H,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,4BAAM,CAACrE,SAAS,EAAO,CAAC,cAC9B7B,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTvB,KAAK,CAAE/E,SAAU,CACjBgF,QAAQ,CAAGD,KAAK,EAAK9E,YAAY,CAAC8E,KAAK,EAAI,EAAE,CAAE,CAC/CZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,cACN3H,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,4BAAM,CAACnE,MAAM,EAAO,CAAC,cAC3B/B,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,IAAK,CACVvB,KAAK,CAAE7E,MAAO,CACd8E,QAAQ,CAAGD,KAAK,EAAK5E,SAAS,CAAC4E,KAAK,EAAI,GAAG,CAAE,CAC7CZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,EACD,CAAC,CACL,CAAC,cACN7H,IAAA,CAACd,GAAG,EAACoH,IAAI,CAAE,EAAG,CAAAJ,QAAA,cACZhG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDhG,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,4BAAM,CAACjE,cAAc,EAAO,CAAC,cACnCjC,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRvB,KAAK,CAAE3E,cAAe,CACtB4E,QAAQ,CAAGD,KAAK,EAAK1E,iBAAiB,CAAC0E,KAAK,EAAI,EAAE,CAAE,CACpDZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,cACN3H,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,kCAAO,CAAC/D,UAAU,EAAO,CAAC,cAChCnC,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,EAAG,CACRC,GAAG,CAAE,GAAI,CACTvB,KAAK,CAAEzE,UAAW,CAClB0E,QAAQ,CAAGD,KAAK,EAAKxE,aAAa,CAACwE,KAAK,EAAI,EAAE,CAAE,CAChDZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,cACN3H,KAAA,QAAAgG,QAAA,eACEhG,KAAA,CAACI,IAAI,EAAA4F,QAAA,EAAC,4BAAM,CAAC7D,SAAS,EAAO,CAAC,cAC9BrC,IAAA,CAACnB,WAAW,EACVqJ,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPvB,KAAK,CAAEvE,SAAU,CACjBwE,QAAQ,CAAGD,KAAK,EAAKtE,YAAY,CAACsE,KAAK,EAAI,CAAC,CAAE,CAC9CZ,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEmB,SAAS,CAAE,CAAE,CAAE,CACxC,CAAC,EACC,CAAC,EACD,CAAC,CACL,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,cAGN3H,KAAA,CAAChB,GAAG,EAACoH,IAAI,CAAE,CAAE,CAAAJ,QAAA,eAEXlG,IAAA,CAAC1B,IAAI,EAACiI,KAAK,CAAC,0BAAM,CAACP,KAAK,CAAE,CAAEQ,YAAY,CAAE,EAAG,CAAE,CAAAN,QAAA,cAC7ChG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDlG,IAAA,CAACb,SAAS,EACRoH,KAAK,CAAC,sCAAQ,CACdK,KAAK,CAAE5F,mBAAmB,CAACwD,MAAO,CAClC6D,MAAM,cAAErI,IAAA,CAACP,cAAc,GAAE,CAAE,CAC5B,CAAC,cACFO,IAAA,CAACb,SAAS,EACRoH,KAAK,CAAC,6BAAS,CACfK,KAAK,CAAExB,iBAAiB,CAAC,CAAE,CAC3BiD,MAAM,cAAErI,IAAA,CAACN,gBAAgB,GAAE,CAAE,CAC9B,CAAC,cACFM,IAAA,CAACb,SAAS,EACRoH,KAAK,CAAC,4CAAS,CACfK,KAAK,CAAEf,kBAAkB,CAAC,CAAE,CAC5BwC,MAAM,cAAErI,IAAA,CAACL,kBAAkB,GAAE,CAAE,CAChC,CAAC,EACG,CAAC,CACJ,CAAC,cAGPK,IAAA,CAAC1B,IAAI,EAACiI,KAAK,CAAC,0BAAM,CAAAL,QAAA,cAChBhG,KAAA,CAACzB,KAAK,EAACgI,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,eACnDlG,IAAA,CAACzB,MAAM,EACL6H,IAAI,CAAC,SAAS,CACdkC,IAAI,cAAEtI,IAAA,CAACT,kBAAkB,GAAE,CAAE,CAC7BwH,OAAO,CAAEpC,wBAAyB,CAClC4D,QAAQ,CAAE3F,aAAa,EAAI5B,mBAAmB,CAACwD,MAAM,GAAK,CAAE,CAC5DgE,IAAI,CAAC,OAAO,CACZxC,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,CAExBtD,aAAa,CAAG,QAAQ,CAAG,QAAQ,CAC9B,CAAC,CAERA,aAAa,eACZ5C,IAAA,CAACzB,MAAM,EACLkK,MAAM,MACN1B,OAAO,CAAEjE,QAAS,CAClBkD,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,CAC1B,0BAED,CAAQ,CACT,CAGAtD,aAAa,eACZ1C,KAAA,QAAAgG,QAAA,eACElG,IAAA,CAACM,IAAI,EAACqG,MAAM,MAAAT,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzBlG,IAAA,CAACjB,QAAQ,EACP2J,OAAO,CAAE3F,YAAa,CACtB4F,MAAM,CAAE3F,UAAU,GAAK,QAAQ,CAAG,WAAW,CAAG,QAAS,CACzDgD,KAAK,CAAE,CAAE6B,SAAS,CAAE,CAAE,CAAE,CACzB,CAAC,CACDlF,WAAW,eACVzC,KAAA,CAACI,IAAI,EAAC8F,IAAI,CAAC,WAAW,CAACJ,KAAK,CAAE,CAAE4C,QAAQ,CAAE,MAAO,CAAE,CAAA1C,QAAA,EAAC,kBAC5C,CAACvD,WAAW,CAACwC,OAAO,EACtB,CACP,EACE,CACN,CAGAlC,UAAU,EAAID,UAAU,GAAK,WAAW,eACvChD,IAAA,CAAChB,KAAK,EACJL,OAAO,CAAC,sCAAQ,CAChBkK,WAAW,cACT3I,KAAA,QAAAgG,QAAA,eACEhG,KAAA,MAAAgG,QAAA,EAAG,4BAAM,CAAC,EAAAzF,mBAAA,CAAAwC,UAAU,CAAC6F,OAAO,UAAArI,mBAAA,iBAAlBA,mBAAA,CAAoBsI,gBAAgB,GAAI,CAAC,CAAC,qBAAI,EAAG,CAAC,cAC5D7I,KAAA,MAAAgG,QAAA,EAAG,4BAAM,CAAC,EAAAxF,oBAAA,CAAAuC,UAAU,CAAC6F,OAAO,UAAApI,oBAAA,iBAAlBA,oBAAA,CAAoBsI,YAAY,GAAI,CAAC,CAAC,SAAE,EAAG,CAAC,cACtD9I,KAAA,MAAAgG,QAAA,EAAG,kCAAO,CAAC,EAAAvF,oBAAA,CAAAsC,UAAU,CAAC6F,OAAO,UAAAnI,oBAAA,iBAAlBA,oBAAA,CAAoBsI,YAAY,GAAI,CAAC,CAAC,SAAE,EAAG,CAAC,EACpD,CACN,CACD7C,IAAI,CAAC,SAAS,CACd8C,QAAQ,MACT,CACF,CAEAlG,UAAU,GAAK,QAAQ,eACtBhD,IAAA,CAAChB,KAAK,EACJL,OAAO,CAAC,sCAAQ,CAChBkK,WAAW,CAAC,gFAAe,CAC3BzC,IAAI,CAAC,OAAO,CACZ8C,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1I,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}