{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"item\"];\nimport warning from \"rc-util/es/warning\";\n/**\n * `onClick` event return `info.item` which point to react node directly.\n * We should warning this since it will not work on FC.\n */\n\nexport function warnItemProp(_ref) {\n  var item = _ref.item,\n    restInfo = _objectWithoutProperties(_ref, _excluded);\n  Object.defineProperty(restInfo, 'item', {\n    get: function get() {\n      warning(false, '`info.item` is deprecated since we will move to function component that not provides React Node instance in future.');\n      return item;\n    }\n  });\n  return restInfo;\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_excluded", "warning", "warnItemProp", "_ref", "item", "restInfo", "Object", "defineProperty", "get"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/utils/warnUtil.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"item\"];\nimport warning from \"rc-util/es/warning\";\n/**\n * `onClick` event return `info.item` which point to react node directly.\n * We should warning this since it will not work on FC.\n */\n\nexport function warnItemProp(_ref) {\n  var item = _ref.item,\n      restInfo = _objectWithoutProperties(_ref, _excluded);\n\n  Object.defineProperty(restInfo, 'item', {\n    get: function get() {\n      warning(false, '`info.item` is deprecated since we will move to function component that not provides React Node instance in future.');\n      return item;\n    }\n  });\n  return restInfo;\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,QAAQ,GAAGN,wBAAwB,CAACI,IAAI,EAAEH,SAAS,CAAC;EAExDM,MAAM,CAACC,cAAc,CAACF,QAAQ,EAAE,MAAM,EAAE;IACtCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClBP,OAAO,CAAC,KAAK,EAAE,qHAAqH,CAAC;MACrI,OAAOG,IAAI;IACb;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}