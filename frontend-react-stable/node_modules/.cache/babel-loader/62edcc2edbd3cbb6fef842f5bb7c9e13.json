{"ast": null, "code": "var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.every` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`\n */\nfunction baseEvery(collection, predicate) {\n  var result = true;\n  baseEach(collection, function (value, index, collection) {\n    result = !!predicate(value, index, collection);\n    return result;\n  });\n  return result;\n}\nmodule.exports = baseEvery;", "map": {"version": 3, "names": ["baseEach", "require", "baseEvery", "collection", "predicate", "result", "value", "index", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseEvery.js"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.every` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`\n */\nfunction baseEvery(collection, predicate) {\n  var result = true;\n  baseEach(collection, function(value, index, collection) {\n    result = !!predicate(value, index, collection);\n    return result;\n  });\n  return result;\n}\n\nmodule.exports = baseEvery;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACxC,IAAIC,MAAM,GAAG,IAAI;EACjBL,QAAQ,CAACG,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtDE,MAAM,GAAG,CAAC,CAACD,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC;IAC9C,OAAOE,MAAM;EACf,CAAC,CAAC;EACF,OAAOA,MAAM;AACf;AAEAG,MAAM,CAACC,OAAO,GAAGP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}