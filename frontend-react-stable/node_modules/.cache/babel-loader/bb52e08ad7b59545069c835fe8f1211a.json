{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useRaf, { useRafState } from '../hooks/useRaf';\nimport TabNode from './TabNode';\nimport useOffsets from '../hooks/useOffsets';\nimport useVisibleRange from '../hooks/useVisibleRange';\nimport OperationNode from './OperationNode';\nimport TabContext from '../TabContext';\nimport useTouchMove from '../hooks/useTouchMove';\nimport useRefs from '../hooks/useRefs';\nimport AddButton from './AddButton';\nimport useSyncState from '../hooks/useSyncState';\nimport { stringify } from '../util';\nimport ExtraContent from './ExtraContent';\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n  return [offsetWidth, offsetHeight];\n};\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\n\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nfunction TabNavList(props, ref) {\n  var _classNames;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll;\n  var containerRef = useRef();\n  var extraLeftRef = useRef();\n  var extraRightRef = useRef();\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n  var _useRefs = useRefs(),\n    _useRefs2 = _slicedToArray(_useRefs, 2),\n    getBtnRef = _useRefs2[0],\n    removeBtnRef = _useRefs2[1];\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useRafState = useRafState(new Map()),\n    _useRafState2 = _slicedToArray(_useRafState, 2),\n    tabSizes = _useRafState2[0],\n    setTabSizes = _useRafState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]); // ========================== Unit =========================\n\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = containerExcludeExtraSizeValue < tabContentSizeValue + addSizeValue;\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue; // ========================== Util =========================\n\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  } // ========================= Mobile ========================\n\n  var touchMovingRef = useRef();\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    } // Skip scroll if place is enough\n\n    if (containerExcludeExtraSizeValue >= tabContentSizeValue) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]); // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1]; // ========================= Scroll ========================\n\n  var scrollToTab = function scrollToTab() {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft; // RTL\n\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      } // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  }; // ========================== Tab ==========================\n\n  var tabNodeStyle = {};\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      ref: getBtnRef(key),\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onRemove: function onRemove() {\n        removeBtnRef(key);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        } // Focus element will make scrollLeft change which we should reset back\n\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n  var onListHolderResize = useRaf(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize); // Which includes add button size\n\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]); // Update buttons records\n\n    setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var key = _ref2.key;\n        var btnNode = getBtnRef(key).current;\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  }); // ======================== Dropdown =======================\n\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs)); // =================== Link & Operations ===================\n\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    inkStyle = _useState12[0],\n    setInkStyle = _useState12[1];\n  var activeTabOffset = tabOffsets.get(activeKey); // Delay set ink style to avoid remove tab blink\n\n  var inkBarRafRef = useRef();\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]); // ========================= Effect ========================\n\n  useEffect(function () {\n    scrollToTab(); // eslint-disable-next-line\n  }, [activeKey, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]); // Should recalculate when rtl changed\n\n  useEffect(function () {\n    onListHolderResize(); // eslint-disable-next-line\n  }, [rtl]); // ========================= Render ========================\n\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft + containerExcludeExtraSizeValue < tabContentSizeValue;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = -transformLeft + containerExcludeExtraSizeValue < tabContentSizeValue;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = -transformTop + containerExcludeExtraSizeValue < tabContentSizeValue;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  })))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n}\nexport default /*#__PURE__*/React.forwardRef(TabNavList);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "_objectSpread", "_slicedToArray", "React", "useState", "useRef", "useEffect", "classNames", "raf", "useComposeRef", "ResizeObserver", "useRaf", "useRafState", "TabNode", "useOffsets", "useVisibleRange", "OperationNode", "TabContext", "useTouchMove", "useRefs", "AddButton", "useSyncState", "stringify", "ExtraContent", "getSize", "refObj", "_ref", "current", "_ref$offsetWidth", "offsetWidth", "_ref$offsetHeight", "offsetHeight", "getUnitValue", "size", "tabPositionTopOrBottom", "TabNavList", "props", "ref", "_classNames", "_React$useContext", "useContext", "prefixCls", "tabs", "className", "style", "id", "animated", "active<PERSON><PERSON>", "rtl", "extra", "editable", "locale", "tabPosition", "tabBarGutter", "children", "onTabClick", "onTabScroll", "containerRef", "extraLeftRef", "extraRightRef", "tabsWrapperRef", "tabListRef", "operationsRef", "innerAddButtonRef", "_useRefs", "_useRefs2", "getBtnRef", "removeBtnRef", "_useSyncState", "next", "prev", "direction", "_useSyncState2", "transformLeft", "setTransformLeft", "_useSyncState3", "_useSyncState4", "transformTop", "setTransformTop", "_useState", "_useState2", "containerExcludeExtraSize", "setContainerExcludeExtraSize", "_useState3", "_useState4", "tabContentSize", "setTabContentSize", "_useState5", "_useState6", "addSize", "setAddSize", "_useState7", "_useState8", "operationSize", "setOperationSize", "_useRafState", "Map", "_useRafState2", "tabSizes", "setTabSizes", "tabOffsets", "containerExcludeExtraSizeValue", "tabContentSizeValue", "addSizeValue", "operationSizeValue", "needScroll", "visibleTabContentValue", "operationsHiddenClassName", "concat", "transformMin", "transformMax", "Math", "min", "max", "alignInRange", "value", "touchMovingRef", "_useState9", "_useState10", "lockAnimation", "setLockAnimation", "doLockAnimation", "Date", "now", "clearTouchMoving", "window", "clearTimeout", "offsetX", "offsetY", "do<PERSON>ove", "setState", "offset", "newValue", "setTimeout", "_useVisibleRange", "_useVisibleRange2", "visibleStart", "visibleEnd", "scrollToTab", "key", "arguments", "length", "undefined", "tabOffset", "get", "width", "height", "left", "right", "top", "newTransform", "_newTransform", "tabNodeStyle", "marginTop", "tabNodes", "map", "tab", "i", "createElement", "closable", "active", "renderWrapper", "removeAriaLabel", "onClick", "e", "onRemove", "onFocus", "scrollLeft", "scrollTop", "onListHolderResize", "containerSize", "extraLeftSize", "extraRightSize", "newAddSize", "newOperationSize", "tabContentFullSize", "newSizes", "for<PERSON>ach", "_ref2", "btnNode", "set", "offsetLeft", "offsetTop", "startHiddenTabs", "slice", "endHiddenTabs", "hiddenTabs", "_useState11", "_useState12", "inkStyle", "setInkStyle", "activeTabOffset", "inkBarRafRef", "cleanInkBarRaf", "cancel", "newInkStyle", "hasDropdown", "wrapPrefix", "pingLeft", "pingRight", "pingTop", "pingBottom", "onResize", "role", "onKeyDown", "position", "transform", "transition", "visibility", "inkBar", "tabMoving", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabNavList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport ResizeObserver from 'rc-resize-observer';\nimport useRaf, { useRafState } from '../hooks/useRaf';\nimport TabNode from './TabNode';\nimport useOffsets from '../hooks/useOffsets';\nimport useVisibleRange from '../hooks/useVisibleRange';\nimport OperationNode from './OperationNode';\nimport TabContext from '../TabContext';\nimport useTouchMove from '../hooks/useTouchMove';\nimport useRefs from '../hooks/useRefs';\nimport AddButton from './AddButton';\nimport useSyncState from '../hooks/useSyncState';\nimport { stringify } from '../util';\nimport ExtraContent from './ExtraContent';\n\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n      _ref$offsetWidth = _ref.offsetWidth,\n      offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n      _ref$offsetHeight = _ref.offsetHeight,\n      offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  return [offsetWidth, offsetHeight];\n};\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\n\n\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\n\nfunction TabNavList(props, ref) {\n  var _classNames;\n\n  var _React$useContext = React.useContext(TabContext),\n      prefixCls = _React$useContext.prefixCls,\n      tabs = _React$useContext.tabs;\n\n  var className = props.className,\n      style = props.style,\n      id = props.id,\n      animated = props.animated,\n      activeKey = props.activeKey,\n      rtl = props.rtl,\n      extra = props.extra,\n      editable = props.editable,\n      locale = props.locale,\n      tabPosition = props.tabPosition,\n      tabBarGutter = props.tabBarGutter,\n      children = props.children,\n      onTabClick = props.onTabClick,\n      onTabScroll = props.onTabScroll;\n  var containerRef = useRef();\n  var extraLeftRef = useRef();\n  var extraRightRef = useRef();\n  var tabsWrapperRef = useRef();\n  var tabListRef = useRef();\n  var operationsRef = useRef();\n  var innerAddButtonRef = useRef();\n\n  var _useRefs = useRefs(),\n      _useRefs2 = _slicedToArray(_useRefs, 2),\n      getBtnRef = _useRefs2[0],\n      removeBtnRef = _useRefs2[1];\n\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n\n  var _useSyncState = useSyncState(0, function (next, prev) {\n    if (tabPositionTopOrBottom && onTabScroll) {\n      onTabScroll({\n        direction: next > prev ? 'left' : 'right'\n      });\n    }\n  }),\n      _useSyncState2 = _slicedToArray(_useSyncState, 2),\n      transformLeft = _useSyncState2[0],\n      setTransformLeft = _useSyncState2[1];\n\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n    if (!tabPositionTopOrBottom && onTabScroll) {\n      onTabScroll({\n        direction: next > prev ? 'top' : 'bottom'\n      });\n    }\n  }),\n      _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n      transformTop = _useSyncState4[0],\n      setTransformTop = _useSyncState4[1];\n\n  var _useState = useState([0, 0]),\n      _useState2 = _slicedToArray(_useState, 2),\n      containerExcludeExtraSize = _useState2[0],\n      setContainerExcludeExtraSize = _useState2[1];\n\n  var _useState3 = useState([0, 0]),\n      _useState4 = _slicedToArray(_useState3, 2),\n      tabContentSize = _useState4[0],\n      setTabContentSize = _useState4[1];\n\n  var _useState5 = useState([0, 0]),\n      _useState6 = _slicedToArray(_useState5, 2),\n      addSize = _useState6[0],\n      setAddSize = _useState6[1];\n\n  var _useState7 = useState([0, 0]),\n      _useState8 = _slicedToArray(_useState7, 2),\n      operationSize = _useState8[0],\n      setOperationSize = _useState8[1];\n\n  var _useRafState = useRafState(new Map()),\n      _useRafState2 = _slicedToArray(_useRafState, 2),\n      tabSizes = _useRafState2[0],\n      setTabSizes = _useRafState2[1];\n\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]); // ========================== Unit =========================\n\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = containerExcludeExtraSizeValue < tabContentSizeValue + addSizeValue;\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue; // ========================== Util =========================\n\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n\n    if (value > transformMax) {\n      return transformMax;\n    }\n\n    return value;\n  } // ========================= Mobile ========================\n\n\n  var touchMovingRef = useRef();\n\n  var _useState9 = useState(),\n      _useState10 = _slicedToArray(_useState9, 2),\n      lockAnimation = _useState10[0],\n      setLockAnimation = _useState10[1];\n\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n\n  function clearTouchMoving() {\n    window.clearTimeout(touchMovingRef.current);\n  }\n\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    } // Skip scroll if place is enough\n\n\n    if (containerExcludeExtraSizeValue >= tabContentSizeValue) {\n      return false;\n    }\n\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n\n    if (lockAnimation) {\n      touchMovingRef.current = window.setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n\n    return clearTouchMoving;\n  }, [lockAnimation]); // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n\n  var _useVisibleRange = useVisibleRange(tabOffsets, // Container\n  visibleTabContentValue, // Transform\n  tabPositionTopOrBottom ? transformLeft : transformTop, // Tabs\n  tabContentSizeValue, // Add\n  addSizeValue, // Operation\n  operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n    tabs: tabs\n  })),\n      _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n      visibleStart = _useVisibleRange2[0],\n      visibleEnd = _useVisibleRange2[1]; // ========================= Scroll ========================\n\n\n  var scrollToTab = function scrollToTab() {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft; // RTL\n\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      } // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  }; // ========================== Tab ==========================\n\n\n  var tabNodeStyle = {};\n\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */\n      ,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      ref: getBtnRef(key),\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onRemove: function onRemove() {\n        removeBtnRef(key);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n\n        if (!tabsWrapperRef.current) {\n          return;\n        } // Focus element will make scrollLeft change which we should reset back\n\n\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n  var onListHolderResize = useRaf(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize); // Which includes add button size\n\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]); // Update buttons records\n\n    setTabSizes(function () {\n      var newSizes = new Map();\n      tabs.forEach(function (_ref2) {\n        var key = _ref2.key;\n        var btnNode = getBtnRef(key).current;\n\n        if (btnNode) {\n          newSizes.set(key, {\n            width: btnNode.offsetWidth,\n            height: btnNode.offsetHeight,\n            left: btnNode.offsetLeft,\n            top: btnNode.offsetTop\n          });\n        }\n      });\n      return newSizes;\n    });\n  }); // ======================== Dropdown =======================\n\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs)); // =================== Link & Operations ===================\n\n  var _useState11 = useState(),\n      _useState12 = _slicedToArray(_useState11, 2),\n      inkStyle = _useState12[0],\n      setInkStyle = _useState12[1];\n\n  var activeTabOffset = tabOffsets.get(activeKey); // Delay set ink style to avoid remove tab blink\n\n  var inkBarRafRef = useRef();\n\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n\n  useEffect(function () {\n    var newInkStyle = {};\n\n    if (activeTabOffset) {\n      if (tabPositionTopOrBottom) {\n        if (rtl) {\n          newInkStyle.right = activeTabOffset.right;\n        } else {\n          newInkStyle.left = activeTabOffset.left;\n        }\n\n        newInkStyle.width = activeTabOffset.width;\n      } else {\n        newInkStyle.top = activeTabOffset.top;\n        newInkStyle.height = activeTabOffset.height;\n      }\n    }\n\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, tabPositionTopOrBottom, rtl]); // ========================= Effect ========================\n\n  useEffect(function () {\n    scrollToTab(); // eslint-disable-next-line\n  }, [activeKey, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]); // Should recalculate when rtl changed\n\n  useEffect(function () {\n    onListHolderResize(); // eslint-disable-next-line\n  }, [rtl]); // ========================= Render ========================\n\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft + containerExcludeExtraSizeValue < tabContentSizeValue;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = -transformLeft + containerExcludeExtraSizeValue < tabContentSizeValue;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = -transformTop + containerExcludeExtraSizeValue < tabContentSizeValue;\n  }\n\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), _defineProperty(_classNames, \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom), _classNames)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: inkStyle\n  })))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n}\n\nexport default /*#__PURE__*/React.forwardRef(TabNavList);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,MAAM,IAAIC,WAAW,QAAQ,iBAAiB;AACrD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,MAAM,EAAE;EACrC,IAAIC,IAAI,GAAGD,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC;IAC3BC,gBAAgB,GAAGF,IAAI,CAACG,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;IAChEE,iBAAiB,GAAGJ,IAAI,CAACK,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;EAEvE,OAAO,CAACD,WAAW,EAAEE,YAAY,CAAC;AACpC,CAAC;AACD;AACA;AACA;;AAGA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,sBAAsB,EAAE;EACrE,OAAOD,IAAI,CAACC,sBAAsB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AAED,SAASC,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAIC,WAAW;EAEf,IAAIC,iBAAiB,GAAGpC,KAAK,CAACqC,UAAU,CAACvB,UAAU,CAAC;IAChDwB,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAEjC,IAAIC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,EAAE,GAAGT,KAAK,CAACS,EAAE;IACbC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,GAAG,GAAGZ,KAAK,CAACY,GAAG;IACfC,KAAK,GAAGb,KAAK,CAACa,KAAK;IACnBC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,WAAW,GAAGhB,KAAK,CAACgB,WAAW;IAC/BC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,UAAU,GAAGnB,KAAK,CAACmB,UAAU;IAC7BC,WAAW,GAAGpB,KAAK,CAACoB,WAAW;EACnC,IAAIC,YAAY,GAAGpD,MAAM,CAAC,CAAC;EAC3B,IAAIqD,YAAY,GAAGrD,MAAM,CAAC,CAAC;EAC3B,IAAIsD,aAAa,GAAGtD,MAAM,CAAC,CAAC;EAC5B,IAAIuD,cAAc,GAAGvD,MAAM,CAAC,CAAC;EAC7B,IAAIwD,UAAU,GAAGxD,MAAM,CAAC,CAAC;EACzB,IAAIyD,aAAa,GAAGzD,MAAM,CAAC,CAAC;EAC5B,IAAI0D,iBAAiB,GAAG1D,MAAM,CAAC,CAAC;EAEhC,IAAI2D,QAAQ,GAAG7C,OAAO,CAAC,CAAC;IACpB8C,SAAS,GAAG/D,cAAc,CAAC8D,QAAQ,EAAE,CAAC,CAAC;IACvCE,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC;IACxBE,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE/B,IAAI/B,sBAAsB,GAAGkB,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ;EAE9E,IAAIgB,aAAa,GAAG/C,YAAY,CAAC,CAAC,EAAE,UAAUgD,IAAI,EAAEC,IAAI,EAAE;MACxD,IAAIpC,sBAAsB,IAAIsB,WAAW,EAAE;QACzCA,WAAW,CAAC;UACVe,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,MAAM,GAAG;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACEE,cAAc,GAAGtE,cAAc,CAACkE,aAAa,EAAE,CAAC,CAAC;IACjDK,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;IACjCE,gBAAgB,GAAGF,cAAc,CAAC,CAAC,CAAC;EAExC,IAAIG,cAAc,GAAGtD,YAAY,CAAC,CAAC,EAAE,UAAUgD,IAAI,EAAEC,IAAI,EAAE;MACzD,IAAI,CAACpC,sBAAsB,IAAIsB,WAAW,EAAE;QAC1CA,WAAW,CAAC;UACVe,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,KAAK,GAAG;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACEM,cAAc,GAAG1E,cAAc,CAACyE,cAAc,EAAE,CAAC,CAAC;IAClDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;IAChCE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EAEvC,IAAIG,SAAS,GAAG3E,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B4E,UAAU,GAAG9E,cAAc,CAAC6E,SAAS,EAAE,CAAC,CAAC;IACzCE,yBAAyB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzCE,4BAA4B,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEhD,IAAIG,UAAU,GAAG/E,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7BgF,UAAU,GAAGlF,cAAc,CAACiF,UAAU,EAAE,CAAC,CAAC;IAC1CE,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC9BE,iBAAiB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAErC,IAAIG,UAAU,GAAGnF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7BoF,UAAU,GAAGtF,cAAc,CAACqF,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAE9B,IAAIG,UAAU,GAAGvF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7BwF,UAAU,GAAG1F,cAAc,CAACyF,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIG,YAAY,GAAGnF,WAAW,CAAC,IAAIoF,GAAG,CAAC,CAAC,CAAC;IACrCC,aAAa,GAAG/F,cAAc,CAAC6F,YAAY,EAAE,CAAC,CAAC;IAC/CG,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;IAC3BE,WAAW,GAAGF,aAAa,CAAC,CAAC,CAAC;EAElC,IAAIG,UAAU,GAAGtF,UAAU,CAAC4B,IAAI,EAAEwD,QAAQ,EAAEb,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhE,IAAIgB,8BAA8B,GAAGrE,YAAY,CAACiD,yBAAyB,EAAE/C,sBAAsB,CAAC;EACpG,IAAIoE,mBAAmB,GAAGtE,YAAY,CAACqD,cAAc,EAAEnD,sBAAsB,CAAC;EAC9E,IAAIqE,YAAY,GAAGvE,YAAY,CAACyD,OAAO,EAAEvD,sBAAsB,CAAC;EAChE,IAAIsE,kBAAkB,GAAGxE,YAAY,CAAC6D,aAAa,EAAE3D,sBAAsB,CAAC;EAC5E,IAAIuE,UAAU,GAAGJ,8BAA8B,GAAGC,mBAAmB,GAAGC,YAAY;EACpF,IAAIG,sBAAsB,GAAGD,UAAU,GAAGJ,8BAA8B,GAAGG,kBAAkB,GAAGH,8BAA8B,GAAGE,YAAY,CAAC,CAAC;;EAE/I,IAAII,yBAAyB,GAAG,EAAE,CAACC,MAAM,CAACnE,SAAS,EAAE,wBAAwB,CAAC;EAC9E,IAAIoE,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EAEpB,IAAI,CAAC5E,sBAAsB,EAAE;IAC3B2E,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,sBAAsB,GAAGJ,mBAAmB,CAAC;IACxEQ,YAAY,GAAG,CAAC;EAClB,CAAC,MAAM,IAAI9D,GAAG,EAAE;IACd6D,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEX,mBAAmB,GAAGI,sBAAsB,CAAC;EAC1E,CAAC,MAAM;IACLG,YAAY,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,sBAAsB,GAAGJ,mBAAmB,CAAC;IACxEQ,YAAY,GAAG,CAAC;EAClB;EAEA,SAASI,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,GAAGN,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IAEA,IAAIM,KAAK,GAAGL,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IAEA,OAAOK,KAAK;EACd,CAAC,CAAC;;EAGF,IAAIC,cAAc,GAAG/G,MAAM,CAAC,CAAC;EAE7B,IAAIgH,UAAU,GAAGjH,QAAQ,CAAC,CAAC;IACvBkH,WAAW,GAAGpH,cAAc,CAACmH,UAAU,EAAE,CAAC,CAAC;IAC3CE,aAAa,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC9BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC;EAErC,SAASG,eAAeA,CAAA,EAAG;IACzBD,gBAAgB,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC9B;EAEA,SAASC,gBAAgBA,CAAA,EAAG;IAC1BC,MAAM,CAACC,YAAY,CAACV,cAAc,CAACzF,OAAO,CAAC;EAC7C;EAEAT,YAAY,CAAC0C,cAAc,EAAE,UAAUmE,OAAO,EAAEC,OAAO,EAAE;IACvD,SAASC,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MAChCD,QAAQ,CAAC,UAAUf,KAAK,EAAE;QACxB,IAAIiB,QAAQ,GAAGlB,YAAY,CAACC,KAAK,GAAGgB,MAAM,CAAC;QAC3C,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAI/B,8BAA8B,IAAIC,mBAAmB,EAAE;MACzD,OAAO,KAAK;IACd;IAEA,IAAIpE,sBAAsB,EAAE;MAC1B+F,MAAM,CAACvD,gBAAgB,EAAEqD,OAAO,CAAC;IACnC,CAAC,MAAM;MACLE,MAAM,CAACnD,eAAe,EAAEkD,OAAO,CAAC;IAClC;IAEAJ,gBAAgB,CAAC,CAAC;IAClBH,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACb,CAAC,CAAC;EACFnH,SAAS,CAAC,YAAY;IACpBsH,gBAAgB,CAAC,CAAC;IAElB,IAAIL,aAAa,EAAE;MACjBH,cAAc,CAACzF,OAAO,GAAGkG,MAAM,CAACQ,UAAU,CAAC,YAAY;QACrDb,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,OAAOI,gBAAgB;EACzB,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,IAAIe,gBAAgB,GAAGvH,eAAe,CAACqF,UAAU;IAAE;IACnDM,sBAAsB;IAAE;IACxBxE,sBAAsB,GAAGuC,aAAa,GAAGI,YAAY;IAAE;IACvDyB,mBAAmB;IAAE;IACrBC,YAAY;IAAE;IACdC,kBAAkB,EAAEvG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DM,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;IACC6F,iBAAiB,GAAGrI,cAAc,CAACoI,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAGvC,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIC,GAAG,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG7F,SAAS;IACvF,IAAIgG,SAAS,GAAG3C,UAAU,CAAC4C,GAAG,CAACL,GAAG,CAAC,IAAI;MACrCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;IACP,CAAC;IAED,IAAInH,sBAAsB,EAAE;MAC1B;MACA,IAAIoH,YAAY,GAAG7E,aAAa,CAAC,CAAC;;MAElC,IAAIzB,GAAG,EAAE;QACP,IAAI+F,SAAS,CAACK,KAAK,GAAG3E,aAAa,EAAE;UACnC6E,YAAY,GAAGP,SAAS,CAACK,KAAK;QAChC,CAAC,MAAM,IAAIL,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACE,KAAK,GAAGxE,aAAa,GAAGiC,sBAAsB,EAAE;UACrF4C,YAAY,GAAGP,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACE,KAAK,GAAGvC,sBAAsB;QAC3E;MACF,CAAC,CAAC;MAAA,KACG,IAAIqC,SAAS,CAACI,IAAI,GAAG,CAAC1E,aAAa,EAAE;QACxC6E,YAAY,GAAG,CAACP,SAAS,CAACI,IAAI;MAChC,CAAC,MAAM,IAAIJ,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACE,KAAK,GAAG,CAACxE,aAAa,GAAGiC,sBAAsB,EAAE;QACrF4C,YAAY,GAAG,EAAEP,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACE,KAAK,GAAGvC,sBAAsB,CAAC;MAC7E;MAEA5B,eAAe,CAAC,CAAC,CAAC;MAClBJ,gBAAgB,CAACwC,YAAY,CAACoC,YAAY,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACA,IAAIC,aAAa,GAAG1E,YAAY;MAEhC,IAAIkE,SAAS,CAACM,GAAG,GAAG,CAACxE,YAAY,EAAE;QACjC0E,aAAa,GAAG,CAACR,SAAS,CAACM,GAAG;MAChC,CAAC,MAAM,IAAIN,SAAS,CAACM,GAAG,GAAGN,SAAS,CAACG,MAAM,GAAG,CAACrE,YAAY,GAAG6B,sBAAsB,EAAE;QACpF6C,aAAa,GAAG,EAAER,SAAS,CAACM,GAAG,GAAGN,SAAS,CAACG,MAAM,GAAGxC,sBAAsB,CAAC;MAC9E;MAEAhC,gBAAgB,CAAC,CAAC,CAAC;MACnBI,eAAe,CAACoC,YAAY,CAACqC,aAAa,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,IAAIpG,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,EAAE;IACrDoG,YAAY,CAACxG,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,GAAGK,YAAY;EACjE,CAAC,MAAM;IACLmG,YAAY,CAACC,SAAS,GAAGpG,YAAY;EACvC;EAEA,IAAIqG,QAAQ,GAAGhH,IAAI,CAACiH,GAAG,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;IACxC,IAAIlB,GAAG,GAAGiB,GAAG,CAACjB,GAAG;IACjB,OAAO,aAAaxI,KAAK,CAAC2J,aAAa,CAACjJ,OAAO,EAAE;MAC/CgC,EAAE,EAAEA,EAAE;MACNJ,SAAS,EAAEA,SAAS;MACpBkG,GAAG,EAAEA,GAAG;MACRiB,GAAG,EAAEA;MACL;;MAEAhH,KAAK,EAAEiH,CAAC,KAAK,CAAC,GAAGf,SAAS,GAAGU,YAAY;MACzCO,QAAQ,EAAEH,GAAG,CAACG,QAAQ;MACtB7G,QAAQ,EAAEA,QAAQ;MAClB8G,MAAM,EAAErB,GAAG,KAAK5F,SAAS;MACzBkH,aAAa,EAAE3G,QAAQ;MACvB4G,eAAe,EAAE/G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC+G,eAAe;MACvF7H,GAAG,EAAE6B,SAAS,CAACyE,GAAG,CAAC;MACnBwB,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3B7G,UAAU,CAACoF,GAAG,EAAEyB,CAAC,CAAC;MACpB,CAAC;MACDC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;QAC5BlG,YAAY,CAACwE,GAAG,CAAC;MACnB,CAAC;MACD2B,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B5B,WAAW,CAACC,GAAG,CAAC;QAChBlB,eAAe,CAAC,CAAC;QAEjB,IAAI,CAAC7D,cAAc,CAACjC,OAAO,EAAE;UAC3B;QACF,CAAC,CAAC;;QAGF,IAAI,CAACqB,GAAG,EAAE;UACRY,cAAc,CAACjC,OAAO,CAAC4I,UAAU,GAAG,CAAC;QACvC;QAEA3G,cAAc,CAACjC,OAAO,CAAC6I,SAAS,GAAG,CAAC;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIC,kBAAkB,GAAG9J,MAAM,CAAC,YAAY;IAC1C;IACA,IAAI+J,aAAa,GAAGlJ,OAAO,CAACiC,YAAY,CAAC;IACzC,IAAIkH,aAAa,GAAGnJ,OAAO,CAACkC,YAAY,CAAC;IACzC,IAAIkH,cAAc,GAAGpJ,OAAO,CAACmC,aAAa,CAAC;IAC3CuB,4BAA4B,CAAC,CAACwF,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAChJ,IAAIC,UAAU,GAAGrJ,OAAO,CAACuC,iBAAiB,CAAC;IAC3C2B,UAAU,CAACmF,UAAU,CAAC;IACtB,IAAIC,gBAAgB,GAAGtJ,OAAO,CAACsC,aAAa,CAAC;IAC7CgC,gBAAgB,CAACgF,gBAAgB,CAAC,CAAC,CAAC;;IAEpC,IAAIC,kBAAkB,GAAGvJ,OAAO,CAACqC,UAAU,CAAC;IAC5CyB,iBAAiB,CAAC,CAACyF,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnG1E,WAAW,CAAC,YAAY;MACtB,IAAI6E,QAAQ,GAAG,IAAIhF,GAAG,CAAC,CAAC;MACxBtD,IAAI,CAACuI,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC5B,IAAIvC,GAAG,GAAGuC,KAAK,CAACvC,GAAG;QACnB,IAAIwC,OAAO,GAAGjH,SAAS,CAACyE,GAAG,CAAC,CAAChH,OAAO;QAEpC,IAAIwJ,OAAO,EAAE;UACXH,QAAQ,CAACI,GAAG,CAACzC,GAAG,EAAE;YAChBM,KAAK,EAAEkC,OAAO,CAACtJ,WAAW;YAC1BqH,MAAM,EAAEiC,OAAO,CAACpJ,YAAY;YAC5BoH,IAAI,EAAEgC,OAAO,CAACE,UAAU;YACxBhC,GAAG,EAAE8B,OAAO,CAACG;UACf,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF,OAAON,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIO,eAAe,GAAG7I,IAAI,CAAC8I,KAAK,CAAC,CAAC,EAAEhD,YAAY,CAAC;EACjD,IAAIiD,aAAa,GAAG/I,IAAI,CAAC8I,KAAK,CAAC/C,UAAU,GAAG,CAAC,CAAC;EAC9C,IAAIiD,UAAU,GAAG,EAAE,CAAC9E,MAAM,CAAC5G,kBAAkB,CAACuL,eAAe,CAAC,EAAEvL,kBAAkB,CAACyL,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEpG,IAAIE,WAAW,GAAGvL,QAAQ,CAAC,CAAC;IACxBwL,WAAW,GAAG1L,cAAc,CAACyL,WAAW,EAAE,CAAC,CAAC;IAC5CE,QAAQ,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAIG,eAAe,GAAG3F,UAAU,CAAC4C,GAAG,CAACjG,SAAS,CAAC,CAAC,CAAC;;EAEjD,IAAIiJ,YAAY,GAAG3L,MAAM,CAAC,CAAC;EAE3B,SAAS4L,cAAcA,CAAA,EAAG;IACxBzL,GAAG,CAAC0L,MAAM,CAACF,YAAY,CAACrK,OAAO,CAAC;EAClC;EAEArB,SAAS,CAAC,YAAY;IACpB,IAAI6L,WAAW,GAAG,CAAC,CAAC;IAEpB,IAAIJ,eAAe,EAAE;MACnB,IAAI7J,sBAAsB,EAAE;QAC1B,IAAIc,GAAG,EAAE;UACPmJ,WAAW,CAAC/C,KAAK,GAAG2C,eAAe,CAAC3C,KAAK;QAC3C,CAAC,MAAM;UACL+C,WAAW,CAAChD,IAAI,GAAG4C,eAAe,CAAC5C,IAAI;QACzC;QAEAgD,WAAW,CAAClD,KAAK,GAAG8C,eAAe,CAAC9C,KAAK;MAC3C,CAAC,MAAM;QACLkD,WAAW,CAAC9C,GAAG,GAAG0C,eAAe,CAAC1C,GAAG;QACrC8C,WAAW,CAACjD,MAAM,GAAG6C,eAAe,CAAC7C,MAAM;MAC7C;IACF;IAEA+C,cAAc,CAAC,CAAC;IAChBD,YAAY,CAACrK,OAAO,GAAGnB,GAAG,CAAC,YAAY;MACrCsL,WAAW,CAACK,WAAW,CAAC;IAC1B,CAAC,CAAC;IACF,OAAOF,cAAc;EACvB,CAAC,EAAE,CAACF,eAAe,EAAE7J,sBAAsB,EAAEc,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEpD1C,SAAS,CAAC,YAAY;IACpBoI,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC3F,SAAS,EAAEzB,SAAS,CAACyK,eAAe,CAAC,EAAEzK,SAAS,CAAC8E,UAAU,CAAC,EAAElE,sBAAsB,CAAC,CAAC,CAAC,CAAC;;EAE5F5B,SAAS,CAAC,YAAY;IACpBmK,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACzH,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEX,IAAIoJ,WAAW,GAAG,CAAC,CAACV,UAAU,CAAC7C,MAAM;EACrC,IAAIwD,UAAU,GAAG,EAAE,CAACzF,MAAM,CAACnE,SAAS,EAAE,WAAW,CAAC;EAClD,IAAI6J,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,UAAU;EAEd,IAAIvK,sBAAsB,EAAE;IAC1B,IAAIc,GAAG,EAAE;MACPuJ,SAAS,GAAG9H,aAAa,GAAG,CAAC;MAC7B6H,QAAQ,GAAG7H,aAAa,GAAG4B,8BAA8B,GAAGC,mBAAmB;IACjF,CAAC,MAAM;MACLgG,QAAQ,GAAG7H,aAAa,GAAG,CAAC;MAC5B8H,SAAS,GAAG,CAAC9H,aAAa,GAAG4B,8BAA8B,GAAGC,mBAAmB;IACnF;EACF,CAAC,MAAM;IACLkG,OAAO,GAAG3H,YAAY,GAAG,CAAC;IAC1B4H,UAAU,GAAG,CAAC5H,YAAY,GAAGwB,8BAA8B,GAAGC,mBAAmB;EACnF;EAEA,OAAO,aAAanG,KAAK,CAAC2J,aAAa,CAACpJ,cAAc,EAAE;IACtDgM,QAAQ,EAAEjC;EACZ,CAAC,EAAE,aAAatK,KAAK,CAAC2J,aAAa,CAAC,KAAK,EAAE;IACzCzH,GAAG,EAAE5B,aAAa,CAAC4B,GAAG,EAAEoB,YAAY,CAAC;IACrCkJ,IAAI,EAAE,SAAS;IACfhK,SAAS,EAAEpC,UAAU,CAAC,EAAE,CAACqG,MAAM,CAACnE,SAAS,EAAE,MAAM,CAAC,EAAEE,SAAS,CAAC;IAC9DC,KAAK,EAAEA,KAAK;IACZgK,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;MAC9B;MACAnF,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,aAAatH,KAAK,CAAC2J,aAAa,CAACvI,YAAY,EAAE;IAChDc,GAAG,EAAEqB,YAAY;IACjBmJ,QAAQ,EAAE,MAAM;IAChB5J,KAAK,EAAEA,KAAK;IACZR,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAatC,KAAK,CAAC2J,aAAa,CAAC,KAAK,EAAE;IAC1CnH,SAAS,EAAEpC,UAAU,CAAC8L,UAAU,GAAG/J,WAAW,GAAG,CAAC,CAAC,EAAEvC,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACsE,MAAM,CAACyF,UAAU,EAAE,YAAY,CAAC,EAAEC,QAAQ,CAAC,EAAEvM,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACsE,MAAM,CAACyF,UAAU,EAAE,aAAa,CAAC,EAAEE,SAAS,CAAC,EAAExM,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACsE,MAAM,CAACyF,UAAU,EAAE,WAAW,CAAC,EAAEG,OAAO,CAAC,EAAEzM,eAAe,CAACuC,WAAW,EAAE,EAAE,CAACsE,MAAM,CAACyF,UAAU,EAAE,cAAc,CAAC,EAAEI,UAAU,CAAC,EAAEnK,WAAW,CAAC,CAAC;IAC1XD,GAAG,EAAEuB;EACP,CAAC,EAAE,aAAazD,KAAK,CAAC2J,aAAa,CAACpJ,cAAc,EAAE;IAClDgM,QAAQ,EAAEjC;EACZ,CAAC,EAAE,aAAatK,KAAK,CAAC2J,aAAa,CAAC,KAAK,EAAE;IACzCzH,GAAG,EAAEwB,UAAU;IACflB,SAAS,EAAE,EAAE,CAACiE,MAAM,CAACnE,SAAS,EAAE,WAAW,CAAC;IAC5CG,KAAK,EAAE;MACLkK,SAAS,EAAE,YAAY,CAAClG,MAAM,CAACnC,aAAa,EAAE,MAAM,CAAC,CAACmC,MAAM,CAAC/B,YAAY,EAAE,KAAK,CAAC;MACjFkI,UAAU,EAAExF,aAAa,GAAG,MAAM,GAAGuB;IACvC;EACF,CAAC,EAAEY,QAAQ,EAAE,aAAavJ,KAAK,CAAC2J,aAAa,CAAC1I,SAAS,EAAE;IACvDiB,GAAG,EAAE0B,iBAAiB;IACtBtB,SAAS,EAAEA,SAAS;IACpBU,MAAM,EAAEA,MAAM;IACdD,QAAQ,EAAEA,QAAQ;IAClBN,KAAK,EAAE3C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyJ,QAAQ,CAACb,MAAM,KAAK,CAAC,GAAGC,SAAS,GAAGU,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5FwD,UAAU,EAAEZ,WAAW,GAAG,QAAQ,GAAG;IACvC,CAAC;EACH,CAAC,CAAC,EAAE,aAAajM,KAAK,CAAC2J,aAAa,CAAC,KAAK,EAAE;IAC1CnH,SAAS,EAAEpC,UAAU,CAAC,EAAE,CAACqG,MAAM,CAACnE,SAAS,EAAE,UAAU,CAAC,EAAE1C,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6G,MAAM,CAACnE,SAAS,EAAE,mBAAmB,CAAC,EAAEK,QAAQ,CAACmK,MAAM,CAAC,CAAC;IACxIrK,KAAK,EAAEiJ;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa1L,KAAK,CAAC2J,aAAa,CAAC9I,aAAa,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEsC,KAAK,EAAE;IACzE8H,eAAe,EAAE/G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC+G,eAAe;IACvF7H,GAAG,EAAEyB,aAAa;IAClBrB,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEgJ,UAAU;IAChB/I,SAAS,EAAE,CAACyJ,WAAW,IAAIzF,yBAAyB;IACpDuG,SAAS,EAAE,CAAC,CAAC3F;EACf,CAAC,CAAC,CAAC,EAAE,aAAapH,KAAK,CAAC2J,aAAa,CAACvI,YAAY,EAAE;IAClDc,GAAG,EAAEsB,aAAa;IAClBkJ,QAAQ,EAAE,OAAO;IACjB5J,KAAK,EAAEA,KAAK;IACZR,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AAEA,eAAe,aAAatC,KAAK,CAACgN,UAAU,CAAChL,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}