{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nimport _isFunction from \"lodash/isFunction\";\nimport _uniqBy from \"lodash/uniqBy\";\nvar _excluded = [\"ref\"];\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n/**\n * @fileOverview Legend\n */\nimport React, { PureComponent } from 'react';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction getUniqPayload(option, payload) {\n  if (option === true) {\n    return _uniqBy(payload, defaultUniqBy);\n  }\n  if (_isFunction(option)) {\n    return _uniqBy(payload, option);\n  }\n  return payload;\n}\nfunction renderContent(content, props) {\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (_isFunction(content)) {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  var ref = props.ref,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, otherProps);\n}\nvar EPS = 1;\nexport var Legend = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Legend, _PureComponent);\n  var _super = _createSuper(Legend);\n  function Legend() {\n    var _this;\n    _classCallCheck(this, Legend);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      boxWidth: -1,\n      boxHeight: -1\n    });\n    return _this;\n  }\n  _createClass(Legend, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"getBBox\",\n    value: function getBBox() {\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        return this.wrapperNode.getBoundingClientRect();\n      }\n      return null;\n    }\n  }, {\n    key: \"getBBoxSnapshot\",\n    value: function getBBoxSnapshot() {\n      var _this$state = this.state,\n        boxWidth = _this$state.boxWidth,\n        boxHeight = _this$state.boxHeight;\n      if (boxWidth >= 0 && boxHeight >= 0) {\n        return {\n          width: boxWidth,\n          height: boxHeight\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getDefaultPosition\",\n    value: function getDefaultPosition(style) {\n      var _this$props = this.props,\n        layout = _this$props.layout,\n        align = _this$props.align,\n        verticalAlign = _this$props.verticalAlign,\n        margin = _this$props.margin,\n        chartWidth = _this$props.chartWidth,\n        chartHeight = _this$props.chartHeight;\n      var hPos, vPos;\n      if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n        if (align === 'center' && layout === 'vertical') {\n          var _box = this.getBBoxSnapshot() || {\n            width: 0\n          };\n          hPos = {\n            left: ((chartWidth || 0) - _box.width) / 2\n          };\n        } else {\n          hPos = align === 'right' ? {\n            right: margin && margin.right || 0\n          } : {\n            left: margin && margin.left || 0\n          };\n        }\n      }\n      if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n        if (verticalAlign === 'middle') {\n          var _box2 = this.getBBoxSnapshot() || {\n            height: 0\n          };\n          vPos = {\n            top: ((chartHeight || 0) - _box2.height) / 2\n          };\n        } else {\n          vPos = verticalAlign === 'bottom' ? {\n            bottom: margin && margin.bottom || 0\n          } : {\n            top: margin && margin.top || 0\n          };\n        }\n      }\n      return _objectSpread(_objectSpread({}, hPos), vPos);\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var _this$state2 = this.state,\n        boxWidth = _this$state2.boxWidth,\n        boxHeight = _this$state2.boxHeight;\n      var onBBoxUpdate = this.props.onBBoxUpdate;\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var _box3 = this.wrapperNode.getBoundingClientRect();\n        if (Math.abs(_box3.width - boxWidth) > EPS || Math.abs(_box3.height - boxHeight) > EPS) {\n          this.setState({\n            boxWidth: _box3.width,\n            boxHeight: _box3.height\n          }, function () {\n            if (onBBoxUpdate) {\n              onBBoxUpdate(_box3);\n            }\n          });\n        }\n      } else if (boxWidth !== -1 || boxHeight !== -1) {\n        this.setState({\n          boxWidth: -1,\n          boxHeight: -1\n        }, function () {\n          if (onBBoxUpdate) {\n            onBBoxUpdate(null);\n          }\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        content = _this$props2.content,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        wrapperStyle = _this$props2.wrapperStyle,\n        payloadUniqBy = _this$props2.payloadUniqBy,\n        payload = _this$props2.payload;\n      var outerStyle = _objectSpread(_objectSpread({\n        position: 'absolute',\n        width: width || 'auto',\n        height: height || 'auto'\n      }, this.getDefaultPosition(wrapperStyle)), wrapperStyle);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-legend-wrapper\",\n        style: outerStyle,\n        ref: function ref(node) {\n          _this2.wrapperNode = node;\n        }\n      }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n        payload: getUniqPayload(payloadUniqBy, payload)\n      })));\n    }\n  }], [{\n    key: \"getWithHeight\",\n    value: function getWithHeight(item, chartWidth) {\n      var layout = item.props.layout;\n      if (layout === 'vertical' && isNumber(item.props.height)) {\n        return {\n          height: item.props.height\n        };\n      }\n      if (layout === 'horizontal') {\n        return {\n          width: item.props.width || chartWidth\n        };\n      }\n      return null;\n    }\n  }]);\n  return Legend;\n}(PureComponent);\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'bottom'\n});", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_isFunction", "_uniqBy", "_excluded", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "bind", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "React", "PureComponent", "DefaultLegendContent", "isNumber", "defaultUniqBy", "entry", "getUniqPayload", "option", "payload", "renderContent", "content", "isValidElement", "cloneElement", "createElement", "ref", "otherProps", "EPS", "Legend", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "boxWidth", "boxHeight", "componentDidMount", "updateBBox", "componentDidUpdate", "getBBox", "wrapperNode", "getBoundingClientRect", "getBBoxSnapshot", "_this$state", "state", "width", "height", "getDefaultPosition", "style", "_this$props", "layout", "align", "verticalAlign", "margin", "chartWidth", "chartHeight", "hPos", "vPos", "left", "right", "_box", "top", "bottom", "_box2", "_this$state2", "onBBoxUpdate", "_box3", "Math", "abs", "setState", "render", "_this2", "_this$props2", "wrapperStyle", "payloadUniqBy", "outerStyle", "position", "className", "node", "getWithHeight", "item", "iconSize"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/Legend.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nimport _isFunction from \"lodash/isFunction\";\nimport _uniqBy from \"lodash/uniqBy\";\nvar _excluded = [\"ref\"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n/**\n * @fileOverview Legend\n */\nimport React, { PureComponent } from 'react';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction getUniqPayload(option, payload) {\n  if (option === true) {\n    return _uniqBy(payload, defaultUniqBy);\n  }\n  if (_isFunction(option)) {\n    return _uniqBy(payload, option);\n  }\n  return payload;\n}\nfunction renderContent(content, props) {\n  if ( /*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (_isFunction(content)) {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  var ref = props.ref,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, otherProps);\n}\nvar EPS = 1;\nexport var Legend = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Legend, _PureComponent);\n  var _super = _createSuper(Legend);\n  function Legend() {\n    var _this;\n    _classCallCheck(this, Legend);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      boxWidth: -1,\n      boxHeight: -1\n    });\n    return _this;\n  }\n  _createClass(Legend, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"getBBox\",\n    value: function getBBox() {\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        return this.wrapperNode.getBoundingClientRect();\n      }\n      return null;\n    }\n  }, {\n    key: \"getBBoxSnapshot\",\n    value: function getBBoxSnapshot() {\n      var _this$state = this.state,\n        boxWidth = _this$state.boxWidth,\n        boxHeight = _this$state.boxHeight;\n      if (boxWidth >= 0 && boxHeight >= 0) {\n        return {\n          width: boxWidth,\n          height: boxHeight\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getDefaultPosition\",\n    value: function getDefaultPosition(style) {\n      var _this$props = this.props,\n        layout = _this$props.layout,\n        align = _this$props.align,\n        verticalAlign = _this$props.verticalAlign,\n        margin = _this$props.margin,\n        chartWidth = _this$props.chartWidth,\n        chartHeight = _this$props.chartHeight;\n      var hPos, vPos;\n      if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n        if (align === 'center' && layout === 'vertical') {\n          var _box = this.getBBoxSnapshot() || {\n            width: 0\n          };\n          hPos = {\n            left: ((chartWidth || 0) - _box.width) / 2\n          };\n        } else {\n          hPos = align === 'right' ? {\n            right: margin && margin.right || 0\n          } : {\n            left: margin && margin.left || 0\n          };\n        }\n      }\n      if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n        if (verticalAlign === 'middle') {\n          var _box2 = this.getBBoxSnapshot() || {\n            height: 0\n          };\n          vPos = {\n            top: ((chartHeight || 0) - _box2.height) / 2\n          };\n        } else {\n          vPos = verticalAlign === 'bottom' ? {\n            bottom: margin && margin.bottom || 0\n          } : {\n            top: margin && margin.top || 0\n          };\n        }\n      }\n      return _objectSpread(_objectSpread({}, hPos), vPos);\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var _this$state2 = this.state,\n        boxWidth = _this$state2.boxWidth,\n        boxHeight = _this$state2.boxHeight;\n      var onBBoxUpdate = this.props.onBBoxUpdate;\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var _box3 = this.wrapperNode.getBoundingClientRect();\n        if (Math.abs(_box3.width - boxWidth) > EPS || Math.abs(_box3.height - boxHeight) > EPS) {\n          this.setState({\n            boxWidth: _box3.width,\n            boxHeight: _box3.height\n          }, function () {\n            if (onBBoxUpdate) {\n              onBBoxUpdate(_box3);\n            }\n          });\n        }\n      } else if (boxWidth !== -1 || boxHeight !== -1) {\n        this.setState({\n          boxWidth: -1,\n          boxHeight: -1\n        }, function () {\n          if (onBBoxUpdate) {\n            onBBoxUpdate(null);\n          }\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        content = _this$props2.content,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        wrapperStyle = _this$props2.wrapperStyle,\n        payloadUniqBy = _this$props2.payloadUniqBy,\n        payload = _this$props2.payload;\n      var outerStyle = _objectSpread(_objectSpread({\n        position: 'absolute',\n        width: width || 'auto',\n        height: height || 'auto'\n      }, this.getDefaultPosition(wrapperStyle)), wrapperStyle);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-legend-wrapper\",\n        style: outerStyle,\n        ref: function ref(node) {\n          _this2.wrapperNode = node;\n        }\n      }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n        payload: getUniqPayload(payloadUniqBy, payload)\n      })));\n    }\n  }], [{\n    key: \"getWithHeight\",\n    value: function getWithHeight(item, chartWidth) {\n      var layout = item.props.layout;\n      if (layout === 'vertical' && isNumber(item.props.height)) {\n        return {\n          height: item.props.height\n        };\n      }\n      if (layout === 'horizontal') {\n        return {\n          width: item.props.width || chartWidth\n        };\n      }\n      return null;\n    }\n  }]);\n  return Legend;\n}(PureComponent);\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'bottom'\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,OAAOK,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,eAAe;AACnC,IAAIC,SAAS,GAAG,CAAC,KAAK,CAAC;AACvB,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AACzf,SAASW,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACf,MAAM,EAAEgB,KAAK,EAAE;EAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IAAEgB,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IAAEqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE7B,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEoB,cAAc,CAACH,UAAU,CAACX,GAAG,CAAC,EAAEW,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAAC/B,SAAS,EAAEwC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEjC,MAAM,CAACoB,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAAC3C,SAAS,GAAGQ,MAAM,CAACqC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC5C,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE+C,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE5B,MAAM,CAACoB,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGvC,MAAM,CAAC0C,cAAc,GAAG1C,MAAM,CAAC0C,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASJ,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASI,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC5D,WAAW;MAAE6D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEtC,SAAS,EAAEyC,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC1C,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;IAAE;IAAE,OAAO4C,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKvE,OAAO,CAACuE,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIlC,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACF,IAAI,CAAC;AAAE;AAC/R,SAASE,sBAAsBA,CAACF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACM,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACvE,SAAS,CAACwE,OAAO,CAACN,IAAI,CAACJ,OAAO,CAACC,SAAS,CAACQ,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASd,eAAeA,CAACX,CAAC,EAAE;EAAEW,eAAe,GAAGnD,MAAM,CAAC0C,cAAc,GAAG1C,MAAM,CAACkE,cAAc,CAACvB,IAAI,CAAC,CAAC,GAAG,SAASQ,eAAeA,CAACX,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACI,SAAS,IAAI5C,MAAM,CAACkE,cAAc,CAAC1B,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOW,eAAe,CAACX,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC7B,GAAG,EAAE4B,GAAG,EAAEsB,KAAK,EAAE;EAAEtB,GAAG,GAAGc,cAAc,CAACd,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI5B,GAAG,EAAE;IAAEY,MAAM,CAACoB,cAAc,CAAChC,GAAG,EAAE4B,GAAG,EAAE;MAAEsB,KAAK,EAAEA,KAAK;MAAEhC,UAAU,EAAE,IAAI;MAAEsB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEzC,GAAG,CAAC4B,GAAG,CAAC,GAAGsB,KAAK;EAAE;EAAE,OAAOlD,GAAG;AAAE;AAC3O,SAAS0C,cAAcA,CAACqC,GAAG,EAAE;EAAE,IAAInD,GAAG,GAAGoD,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhF,OAAO,CAAC6B,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGqD,MAAM,CAACrD,GAAG,CAAC;AAAE;AAC5H,SAASoD,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpF,OAAO,CAACmF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjF,MAAM,CAACoF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACd,IAAI,CAACY,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpF,OAAO,CAACwF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAInD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC+C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,SAASO,wBAAwBA,CAAC/D,MAAM,EAAEgE,QAAQ,EAAE;EAAE,IAAIhE,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGqE,6BAA6B,CAACjE,MAAM,EAAEgE,QAAQ,CAAC;EAAE,IAAI9D,GAAG,EAAEL,CAAC;EAAE,IAAIX,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAI+E,gBAAgB,GAAGhF,MAAM,CAACC,qBAAqB,CAACa,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqE,gBAAgB,CAACnE,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEK,GAAG,GAAGgE,gBAAgB,CAACrE,CAAC,CAAC;MAAE,IAAImE,QAAQ,CAACG,OAAO,CAACjE,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAAChB,MAAM,CAACR,SAAS,CAAC0F,oBAAoB,CAACxB,IAAI,CAAC5C,MAAM,EAAEE,GAAG,CAAC,EAAE;MAAUN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAC3e,SAASqE,6BAA6BA,CAACjE,MAAM,EAAEgE,QAAQ,EAAE;EAAE,IAAIhE,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIyE,UAAU,GAAGnF,MAAM,CAACD,IAAI,CAACe,MAAM,CAAC;EAAE,IAAIE,GAAG,EAAEL,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,UAAU,CAACtE,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAEK,GAAG,GAAGmE,UAAU,CAACxE,CAAC,CAAC;IAAE,IAAImE,QAAQ,CAACG,OAAO,CAACjE,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AAClT;AACA;AACA;AACA,OAAO0E,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACnD,KAAK;AACpB;AACA,SAASoD,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvC,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOjG,OAAO,CAACkG,OAAO,EAAEJ,aAAa,CAAC;EACxC;EACA,IAAI/F,WAAW,CAACkG,MAAM,CAAC,EAAE;IACvB,OAAOjG,OAAO,CAACkG,OAAO,EAAED,MAAM,CAAC;EACjC;EACA,OAAOC,OAAO;AAChB;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEpE,KAAK,EAAE;EACrC,IAAK,aAAa0D,KAAK,CAACW,cAAc,CAACD,OAAO,CAAC,EAAE;IAC/C,OAAO,aAAaV,KAAK,CAACY,YAAY,CAACF,OAAO,EAAEpE,KAAK,CAAC;EACxD;EACA,IAAIjC,WAAW,CAACqG,OAAO,CAAC,EAAE;IACxB,OAAO,aAAaV,KAAK,CAACa,aAAa,CAACH,OAAO,EAAEpE,KAAK,CAAC;EACzD;EACA,IAAIwE,GAAG,GAAGxE,KAAK,CAACwE,GAAG;IACjBC,UAAU,GAAGtB,wBAAwB,CAACnD,KAAK,EAAE/B,SAAS,CAAC;EACzD,OAAO,aAAayF,KAAK,CAACa,aAAa,CAACX,oBAAoB,EAAEa,UAAU,CAAC;AAC3E;AACA,IAAIC,GAAG,GAAG,CAAC;AACX,OAAO,IAAIC,MAAM,GAAG,aAAa,UAAUC,cAAc,EAAE;EACzDpE,SAAS,CAACmE,MAAM,EAAEC,cAAc,CAAC;EACjC,IAAIC,MAAM,GAAG1D,YAAY,CAACwD,MAAM,CAAC;EACjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IACTnF,eAAe,CAAC,IAAI,EAAEgF,MAAM,CAAC;IAC7B,KAAK,IAAII,IAAI,GAAG7F,SAAS,CAACC,MAAM,EAAE6F,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGhG,SAAS,CAACgG,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAAC7C,IAAI,CAAClD,KAAK,CAAC+F,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDzF,eAAe,CAAC0C,sBAAsB,CAAC6C,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDM,QAAQ,EAAE,CAAC,CAAC;MACZC,SAAS,EAAE,CAAC;IACd,CAAC,CAAC;IACF,OAAOP,KAAK;EACd;EACAzE,YAAY,CAACsE,MAAM,EAAE,CAAC;IACpBrF,GAAG,EAAE,mBAAmB;IACxBsB,KAAK,EAAE,SAAS0E,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,oBAAoB;IACzBsB,KAAK,EAAE,SAAS4E,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDjG,GAAG,EAAE,SAAS;IACdsB,KAAK,EAAE,SAAS6E,OAAOA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,qBAAqB,EAAE;QAC9D,OAAO,IAAI,CAACD,WAAW,CAACC,qBAAqB,CAAC,CAAC;MACjD;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDrG,GAAG,EAAE,iBAAiB;IACtBsB,KAAK,EAAE,SAASgF,eAAeA,CAAA,EAAG;MAChC,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QAC1BV,QAAQ,GAAGS,WAAW,CAACT,QAAQ;QAC/BC,SAAS,GAAGQ,WAAW,CAACR,SAAS;MACnC,IAAID,QAAQ,IAAI,CAAC,IAAIC,SAAS,IAAI,CAAC,EAAE;QACnC,OAAO;UACLU,KAAK,EAAEX,QAAQ;UACfY,MAAM,EAAEX;QACV,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,oBAAoB;IACzBsB,KAAK,EAAE,SAASqF,kBAAkBA,CAACC,KAAK,EAAE;MACxC,IAAIC,WAAW,GAAG,IAAI,CAACnG,KAAK;QAC1BoG,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;QACzBC,aAAa,GAAGH,WAAW,CAACG,aAAa;QACzCC,MAAM,GAAGJ,WAAW,CAACI,MAAM;QAC3BC,UAAU,GAAGL,WAAW,CAACK,UAAU;QACnCC,WAAW,GAAGN,WAAW,CAACM,WAAW;MACvC,IAAIC,IAAI,EAAEC,IAAI;MACd,IAAI,CAACT,KAAK,IAAI,CAACA,KAAK,CAACU,IAAI,KAAK5D,SAAS,IAAIkD,KAAK,CAACU,IAAI,KAAK,IAAI,MAAMV,KAAK,CAACW,KAAK,KAAK7D,SAAS,IAAIkD,KAAK,CAACW,KAAK,KAAK,IAAI,CAAC,EAAE;QACtH,IAAIR,KAAK,KAAK,QAAQ,IAAID,MAAM,KAAK,UAAU,EAAE;UAC/C,IAAIU,IAAI,GAAG,IAAI,CAAClB,eAAe,CAAC,CAAC,IAAI;YACnCG,KAAK,EAAE;UACT,CAAC;UACDW,IAAI,GAAG;YACLE,IAAI,EAAE,CAAC,CAACJ,UAAU,IAAI,CAAC,IAAIM,IAAI,CAACf,KAAK,IAAI;UAC3C,CAAC;QACH,CAAC,MAAM;UACLW,IAAI,GAAGL,KAAK,KAAK,OAAO,GAAG;YACzBQ,KAAK,EAAEN,MAAM,IAAIA,MAAM,CAACM,KAAK,IAAI;UACnC,CAAC,GAAG;YACFD,IAAI,EAAEL,MAAM,IAAIA,MAAM,CAACK,IAAI,IAAI;UACjC,CAAC;QACH;MACF;MACA,IAAI,CAACV,KAAK,IAAI,CAACA,KAAK,CAACa,GAAG,KAAK/D,SAAS,IAAIkD,KAAK,CAACa,GAAG,KAAK,IAAI,MAAMb,KAAK,CAACc,MAAM,KAAKhE,SAAS,IAAIkD,KAAK,CAACc,MAAM,KAAK,IAAI,CAAC,EAAE;QACtH,IAAIV,aAAa,KAAK,QAAQ,EAAE;UAC9B,IAAIW,KAAK,GAAG,IAAI,CAACrB,eAAe,CAAC,CAAC,IAAI;YACpCI,MAAM,EAAE;UACV,CAAC;UACDW,IAAI,GAAG;YACLI,GAAG,EAAE,CAAC,CAACN,WAAW,IAAI,CAAC,IAAIQ,KAAK,CAACjB,MAAM,IAAI;UAC7C,CAAC;QACH,CAAC,MAAM;UACLW,IAAI,GAAGL,aAAa,KAAK,QAAQ,GAAG;YAClCU,MAAM,EAAET,MAAM,IAAIA,MAAM,CAACS,MAAM,IAAI;UACrC,CAAC,GAAG;YACFD,GAAG,EAAER,MAAM,IAAIA,MAAM,CAACQ,GAAG,IAAI;UAC/B,CAAC;QACH;MACF;MACA,OAAOhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2H,IAAI,CAAC,EAAEC,IAAI,CAAC;IACrD;EACF,CAAC,EAAE;IACDrH,GAAG,EAAE,YAAY;IACjBsB,KAAK,EAAE,SAAS2E,UAAUA,CAAA,EAAG;MAC3B,IAAI2B,YAAY,GAAG,IAAI,CAACpB,KAAK;QAC3BV,QAAQ,GAAG8B,YAAY,CAAC9B,QAAQ;QAChCC,SAAS,GAAG6B,YAAY,CAAC7B,SAAS;MACpC,IAAI8B,YAAY,GAAG,IAAI,CAACnH,KAAK,CAACmH,YAAY;MAC1C,IAAI,IAAI,CAACzB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,qBAAqB,EAAE;QAC9D,IAAIyB,KAAK,GAAG,IAAI,CAAC1B,WAAW,CAACC,qBAAqB,CAAC,CAAC;QACpD,IAAI0B,IAAI,CAACC,GAAG,CAACF,KAAK,CAACrB,KAAK,GAAGX,QAAQ,CAAC,GAAGV,GAAG,IAAI2C,IAAI,CAACC,GAAG,CAACF,KAAK,CAACpB,MAAM,GAAGX,SAAS,CAAC,GAAGX,GAAG,EAAE;UACtF,IAAI,CAAC6C,QAAQ,CAAC;YACZnC,QAAQ,EAAEgC,KAAK,CAACrB,KAAK;YACrBV,SAAS,EAAE+B,KAAK,CAACpB;UACnB,CAAC,EAAE,YAAY;YACb,IAAImB,YAAY,EAAE;cAChBA,YAAY,CAACC,KAAK,CAAC;YACrB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIhC,QAAQ,KAAK,CAAC,CAAC,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACkC,QAAQ,CAAC;UACZnC,QAAQ,EAAE,CAAC,CAAC;UACZC,SAAS,EAAE,CAAC;QACd,CAAC,EAAE,YAAY;UACb,IAAI8B,YAAY,EAAE;YAChBA,YAAY,CAAC,IAAI,CAAC;UACpB;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACD7H,GAAG,EAAE,QAAQ;IACbsB,KAAK,EAAE,SAAS4G,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC1H,KAAK;QAC3BoE,OAAO,GAAGsD,YAAY,CAACtD,OAAO;QAC9B2B,KAAK,GAAG2B,YAAY,CAAC3B,KAAK;QAC1BC,MAAM,GAAG0B,YAAY,CAAC1B,MAAM;QAC5B2B,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCC,aAAa,GAAGF,YAAY,CAACE,aAAa;QAC1C1D,OAAO,GAAGwD,YAAY,CAACxD,OAAO;MAChC,IAAI2D,UAAU,GAAG9I,aAAa,CAACA,aAAa,CAAC;QAC3C+I,QAAQ,EAAE,UAAU;QACpB/B,KAAK,EAAEA,KAAK,IAAI,MAAM;QACtBC,MAAM,EAAEA,MAAM,IAAI;MACpB,CAAC,EAAE,IAAI,CAACC,kBAAkB,CAAC0B,YAAY,CAAC,CAAC,EAAEA,YAAY,CAAC;MACxD,OAAO,aAAajE,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;QAC7CwD,SAAS,EAAE,yBAAyB;QACpC7B,KAAK,EAAE2B,UAAU;QACjBrD,GAAG,EAAE,SAASA,GAAGA,CAACwD,IAAI,EAAE;UACtBP,MAAM,CAAC/B,WAAW,GAAGsC,IAAI;QAC3B;MACF,CAAC,EAAE7D,aAAa,CAACC,OAAO,EAAErF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzEkE,OAAO,EAAEF,cAAc,CAAC4D,aAAa,EAAE1D,OAAO;MAChD,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,CAAC,EAAE,CAAC;IACH5E,GAAG,EAAE,eAAe;IACpBsB,KAAK,EAAE,SAASqH,aAAaA,CAACC,IAAI,EAAE1B,UAAU,EAAE;MAC9C,IAAIJ,MAAM,GAAG8B,IAAI,CAAClI,KAAK,CAACoG,MAAM;MAC9B,IAAIA,MAAM,KAAK,UAAU,IAAIvC,QAAQ,CAACqE,IAAI,CAAClI,KAAK,CAACgG,MAAM,CAAC,EAAE;QACxD,OAAO;UACLA,MAAM,EAAEkC,IAAI,CAAClI,KAAK,CAACgG;QACrB,CAAC;MACH;MACA,IAAII,MAAM,KAAK,YAAY,EAAE;QAC3B,OAAO;UACLL,KAAK,EAAEmC,IAAI,CAAClI,KAAK,CAAC+F,KAAK,IAAIS;QAC7B,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO7B,MAAM;AACf,CAAC,CAAChB,aAAa,CAAC;AAChBpE,eAAe,CAACoF,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChDpF,eAAe,CAACoF,MAAM,EAAE,cAAc,EAAE;EACtCwD,QAAQ,EAAE,EAAE;EACZ/B,MAAM,EAAE,YAAY;EACpBC,KAAK,EAAE,QAAQ;EACfC,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}