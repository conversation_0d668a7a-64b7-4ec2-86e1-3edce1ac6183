{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar PropTypes = _interopRequireWildcard(require(\"prop-types\"));\nvar _addClass = _interopRequireDefault(require(\"dom-helpers/class/addClass\"));\nvar _removeClass = _interopRequireDefault(require(\"dom-helpers/class/removeClass\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _Transition = _interopRequireDefault(require(\"./Transition\"));\nvar _PropTypes = require(\"./utils/PropTypes\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n          var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {};\n          if (desc.get || desc.set) {\n            Object.defineProperty(newObj, key, desc);\n          } else {\n            newObj[key] = obj[key];\n          }\n        }\n      }\n    }\n    newObj.default = obj;\n    return newObj;\n  }\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nvar addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return (0, _addClass.default)(node, c);\n  });\n};\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return (0, _removeClass.default)(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](http://www.nganimate.org/) library, you should use it if you're\n * using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity: 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**.\n */\n\nvar CSSTransition = /*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n  function CSSTransition() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.onEnter = function (node, appearing) {\n      var _this$getClassNames = _this.getClassNames(appearing ? 'appear' : 'enter'),\n        className = _this$getClassNames.className;\n      _this.removeClasses(node, 'exit');\n      addClass(node, className);\n      if (_this.props.onEnter) {\n        _this.props.onEnter(node, appearing);\n      }\n    };\n    _this.onEntering = function (node, appearing) {\n      var _this$getClassNames2 = _this.getClassNames(appearing ? 'appear' : 'enter'),\n        activeClassName = _this$getClassNames2.activeClassName;\n      _this.reflowAndAddClass(node, activeClassName);\n      if (_this.props.onEntering) {\n        _this.props.onEntering(node, appearing);\n      }\n    };\n    _this.onEntered = function (node, appearing) {\n      var appearClassName = _this.getClassNames('appear').doneClassName;\n      var enterClassName = _this.getClassNames('enter').doneClassName;\n      var doneClassName = appearing ? appearClassName + \" \" + enterClassName : enterClassName;\n      _this.removeClasses(node, appearing ? 'appear' : 'enter');\n      addClass(node, doneClassName);\n      if (_this.props.onEntered) {\n        _this.props.onEntered(node, appearing);\n      }\n    };\n    _this.onExit = function (node) {\n      var _this$getClassNames3 = _this.getClassNames('exit'),\n        className = _this$getClassNames3.className;\n      _this.removeClasses(node, 'appear');\n      _this.removeClasses(node, 'enter');\n      addClass(node, className);\n      if (_this.props.onExit) {\n        _this.props.onExit(node);\n      }\n    };\n    _this.onExiting = function (node) {\n      var _this$getClassNames4 = _this.getClassNames('exit'),\n        activeClassName = _this$getClassNames4.activeClassName;\n      _this.reflowAndAddClass(node, activeClassName);\n      if (_this.props.onExiting) {\n        _this.props.onExiting(node);\n      }\n    };\n    _this.onExited = function (node) {\n      var _this$getClassNames5 = _this.getClassNames('exit'),\n        doneClassName = _this$getClassNames5.doneClassName;\n      _this.removeClasses(node, 'exit');\n      addClass(node, doneClassName);\n      if (_this.props.onExited) {\n        _this.props.onExited(node);\n      }\n    };\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + '-' : '';\n      var className = isStringClassNames ? prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? className + '-active' : classNames[type + 'Active'];\n      var doneClassName = isStringClassNames ? className + '-done' : classNames[type + 'Done'];\n      return {\n        className: className,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n    return _this;\n  }\n  var _proto = CSSTransition.prototype;\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$getClassNames6 = this.getClassNames(type),\n      className = _this$getClassNames6.className,\n      activeClassName = _this$getClassNames6.activeClassName,\n      doneClassName = _this$getClassNames6.doneClassName;\n    className && removeClass(node, className);\n    activeClassName && removeClass(node, activeClassName);\n    doneClassName && removeClass(node, doneClassName);\n  };\n  _proto.reflowAndAddClass = function reflowAndAddClass(node, className) {\n    // This is for to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n    if (className) {\n      /* eslint-disable no-unused-expressions */\n      node && node.scrollTop;\n      /* eslint-enable no-unused-expressions */\n\n      addClass(node, className);\n    }\n  };\n  _proto.render = function render() {\n    var props = _extends({}, this.props);\n    delete props.classNames;\n    return _react.default.createElement(_Transition.default, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n  return CSSTransition;\n}(_react.default.Component);\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, _Transition.default.propTypes, {\n  /**\n   * The animation classNames applied to the component as it enters, exits or\n   * has finished the transition. A single name can be provided and it will be\n   * suffixed for each stage: e.g.\n   *\n   * `classNames=\"fade\"` applies `fade-enter`, `fade-enter-active`,\n   * `fade-enter-done`, `fade-exit`, `fade-exit-active`, `fade-exit-done`,\n   * `fade-appear`, `fade-appear-active`, and `fade-appear-done`.\n   *\n   * **Note**: `fade-appear-done` and `fade-enter-done` will _both_ be applied.\n   * This allows you to define different behavior for when appearing is done and\n   * when regular entering is done, using selectors like\n   * `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply an\n   * epic entrance animation when element first appears in the DOM using\n   * [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   * simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: _PropTypes.classNamesShape,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nvar _default = CSSTransition;\nexports.default = _default;\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["exports", "__esModule", "default", "PropTypes", "_interopRequireWildcard", "require", "_addClass", "_interopRequireDefault", "_removeClass", "_react", "_Transition", "_PropTypes", "obj", "newObj", "key", "Object", "prototype", "hasOwnProperty", "call", "desc", "defineProperty", "getOwnPropertyDescriptor", "get", "set", "_extends", "assign", "target", "i", "arguments", "length", "source", "apply", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "__proto__", "addClass", "node", "classes", "split", "for<PERSON>ach", "c", "removeClass", "CSSTransition", "_React$Component", "_this", "_len", "args", "Array", "_key", "concat", "onEnter", "appearing", "_this$getClassNames", "getClassNames", "className", "removeClasses", "props", "onEntering", "_this$getClassNames2", "activeClassName", "reflowAndAddClass", "onEntered", "appearClassName", "doneClassName", "enterClassName", "onExit", "_this$getClassNames3", "onExiting", "_this$getClassNames4", "onExited", "_this$getClassNames5", "type", "classNames", "isStringClassNames", "prefix", "_proto", "_this$getClassNames6", "scrollTop", "render", "createElement", "Component", "defaultProps", "propTypes", "process", "env", "NODE_ENV", "classNamesShape", "func", "_default", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-transition-group/CSSTransition.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n\nvar PropTypes = _interopRequireWildcard(require(\"prop-types\"));\n\nvar _addClass = _interopRequireDefault(require(\"dom-helpers/class/addClass\"));\n\nvar _removeClass = _interopRequireDefault(require(\"dom-helpers/class/removeClass\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _Transition = _interopRequireDefault(require(\"./Transition\"));\n\nvar _PropTypes = require(\"./utils/PropTypes\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = Object.defineProperty && Object.getOwnPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : {}; if (desc.get || desc.set) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } } newObj.default = obj; return newObj; } }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; subClass.__proto__ = superClass; }\n\nvar addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return (0, _addClass.default)(node, c);\n  });\n};\n\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return (0, _removeClass.default)(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](http://www.nganimate.org/) library, you should use it if you're\n * using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity: 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**.\n */\n\n\nvar CSSTransition =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n\n  function CSSTransition() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _this.onEnter = function (node, appearing) {\n      var _this$getClassNames = _this.getClassNames(appearing ? 'appear' : 'enter'),\n          className = _this$getClassNames.className;\n\n      _this.removeClasses(node, 'exit');\n\n      addClass(node, className);\n\n      if (_this.props.onEnter) {\n        _this.props.onEnter(node, appearing);\n      }\n    };\n\n    _this.onEntering = function (node, appearing) {\n      var _this$getClassNames2 = _this.getClassNames(appearing ? 'appear' : 'enter'),\n          activeClassName = _this$getClassNames2.activeClassName;\n\n      _this.reflowAndAddClass(node, activeClassName);\n\n      if (_this.props.onEntering) {\n        _this.props.onEntering(node, appearing);\n      }\n    };\n\n    _this.onEntered = function (node, appearing) {\n      var appearClassName = _this.getClassNames('appear').doneClassName;\n\n      var enterClassName = _this.getClassNames('enter').doneClassName;\n\n      var doneClassName = appearing ? appearClassName + \" \" + enterClassName : enterClassName;\n\n      _this.removeClasses(node, appearing ? 'appear' : 'enter');\n\n      addClass(node, doneClassName);\n\n      if (_this.props.onEntered) {\n        _this.props.onEntered(node, appearing);\n      }\n    };\n\n    _this.onExit = function (node) {\n      var _this$getClassNames3 = _this.getClassNames('exit'),\n          className = _this$getClassNames3.className;\n\n      _this.removeClasses(node, 'appear');\n\n      _this.removeClasses(node, 'enter');\n\n      addClass(node, className);\n\n      if (_this.props.onExit) {\n        _this.props.onExit(node);\n      }\n    };\n\n    _this.onExiting = function (node) {\n      var _this$getClassNames4 = _this.getClassNames('exit'),\n          activeClassName = _this$getClassNames4.activeClassName;\n\n      _this.reflowAndAddClass(node, activeClassName);\n\n      if (_this.props.onExiting) {\n        _this.props.onExiting(node);\n      }\n    };\n\n    _this.onExited = function (node) {\n      var _this$getClassNames5 = _this.getClassNames('exit'),\n          doneClassName = _this$getClassNames5.doneClassName;\n\n      _this.removeClasses(node, 'exit');\n\n      addClass(node, doneClassName);\n\n      if (_this.props.onExited) {\n        _this.props.onExited(node);\n      }\n    };\n\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + '-' : '';\n      var className = isStringClassNames ? prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? className + '-active' : classNames[type + 'Active'];\n      var doneClassName = isStringClassNames ? className + '-done' : classNames[type + 'Done'];\n      return {\n        className: className,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n\n    return _this;\n  }\n\n  var _proto = CSSTransition.prototype;\n\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$getClassNames6 = this.getClassNames(type),\n        className = _this$getClassNames6.className,\n        activeClassName = _this$getClassNames6.activeClassName,\n        doneClassName = _this$getClassNames6.doneClassName;\n\n    className && removeClass(node, className);\n    activeClassName && removeClass(node, activeClassName);\n    doneClassName && removeClass(node, doneClassName);\n  };\n\n  _proto.reflowAndAddClass = function reflowAndAddClass(node, className) {\n    // This is for to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n    if (className) {\n      /* eslint-disable no-unused-expressions */\n      node && node.scrollTop;\n      /* eslint-enable no-unused-expressions */\n\n      addClass(node, className);\n    }\n  };\n\n  _proto.render = function render() {\n    var props = _extends({}, this.props);\n\n    delete props.classNames;\n    return _react.default.createElement(_Transition.default, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n\n  return CSSTransition;\n}(_react.default.Component);\n\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, _Transition.default.propTypes, {\n  /**\n   * The animation classNames applied to the component as it enters, exits or\n   * has finished the transition. A single name can be provided and it will be\n   * suffixed for each stage: e.g.\n   *\n   * `classNames=\"fade\"` applies `fade-enter`, `fade-enter-active`,\n   * `fade-enter-done`, `fade-exit`, `fade-exit-active`, `fade-exit-done`,\n   * `fade-appear`, `fade-appear-active`, and `fade-appear-done`.\n   *\n   * **Note**: `fade-appear-done` and `fade-enter-done` will _both_ be applied.\n   * This allows you to define different behavior for when appearing is done and\n   * when regular entering is done, using selectors like\n   * `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply an\n   * epic entrance animation when element first appears in the DOM using\n   * [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   * simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: _PropTypes.classNamesShape,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nvar _default = CSSTransition;\nexports.default = _default;\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,SAAS,GAAGC,uBAAuB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIC,SAAS,GAAGC,sBAAsB,CAACF,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAE7E,IAAIG,YAAY,GAAGD,sBAAsB,CAACF,OAAO,CAAC,+BAA+B,CAAC,CAAC;AAEnF,IAAII,MAAM,GAAGF,sBAAsB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIK,WAAW,GAAGH,sBAAsB,CAACF,OAAO,CAAC,cAAc,CAAC,CAAC;AAEjE,IAAIM,UAAU,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAE7C,SAASE,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACX,UAAU,GAAGW,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,SAASR,uBAAuBA,CAACQ,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAIA,GAAG,CAACX,UAAU,EAAE;IAAE,OAAOW,GAAG;EAAE,CAAC,MAAM;IAAE,IAAIC,MAAM,GAAG,CAAC,CAAC;IAAE,IAAID,GAAG,IAAI,IAAI,EAAE;MAAE,KAAK,IAAIE,GAAG,IAAIF,GAAG,EAAE;QAAE,IAAIG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,GAAG,EAAEE,GAAG,CAAC,EAAE;UAAE,IAAIK,IAAI,GAAGJ,MAAM,CAACK,cAAc,IAAIL,MAAM,CAACM,wBAAwB,GAAGN,MAAM,CAACM,wBAAwB,CAACT,GAAG,EAAEE,GAAG,CAAC,GAAG,CAAC,CAAC;UAAE,IAAIK,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,GAAG,EAAE;YAAER,MAAM,CAACK,cAAc,CAACP,MAAM,EAAEC,GAAG,EAAEK,IAAI,CAAC;UAAE,CAAC,MAAM;YAAEN,MAAM,CAACC,GAAG,CAAC,GAAGF,GAAG,CAACE,GAAG,CAAC;UAAE;QAAE;MAAE;IAAE;IAAED,MAAM,CAACX,OAAO,GAAGU,GAAG;IAAE,OAAOC,MAAM;EAAE;AAAE;AAEvd,SAASW,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,IAAI,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAIb,GAAG,IAAIgB,MAAM,EAAE;QAAE,IAAIf,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACY,MAAM,EAAEhB,GAAG,CAAC,EAAE;UAAEY,MAAM,CAACZ,GAAG,CAAC,GAAGgB,MAAM,CAAChB,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOY,MAAM;EAAE,CAAC;EAAE,OAAOF,QAAQ,CAACO,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;AAAE;AAE5T,SAASI,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAED,QAAQ,CAACjB,SAAS,GAAGD,MAAM,CAACoB,MAAM,CAACD,UAAU,CAAClB,SAAS,CAAC;EAAEiB,QAAQ,CAACjB,SAAS,CAACoB,WAAW,GAAGH,QAAQ;EAAEA,QAAQ,CAACI,SAAS,GAAGH,UAAU;AAAE;AAEtL,IAAII,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC9C,OAAOD,IAAI,IAAIC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;IAChE,OAAO,CAAC,CAAC,EAAErC,SAAS,CAACJ,OAAO,EAAEqC,IAAI,EAAEI,CAAC,CAAC;EACxC,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACL,IAAI,EAAEC,OAAO,EAAE;EACpD,OAAOD,IAAI,IAAIC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;IAChE,OAAO,CAAC,CAAC,EAAEnC,YAAY,CAACN,OAAO,EAAEqC,IAAI,EAAEI,CAAC,CAAC;EAC3C,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIE,aAAa,GACjB;AACA,UAAUC,gBAAgB,EAAE;EAC1Bd,cAAc,CAACa,aAAa,EAAEC,gBAAgB,CAAC;EAE/C,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK;IAET,KAAK,IAAIC,IAAI,GAAGpB,SAAS,CAACC,MAAM,EAAEoB,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGvB,SAAS,CAACuB,IAAI,CAAC;IAC9B;IAEAJ,KAAK,GAAGD,gBAAgB,CAAC5B,IAAI,CAACa,KAAK,CAACe,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC,IAAI,IAAI;IAElFF,KAAK,CAACM,OAAO,GAAG,UAAUd,IAAI,EAAEe,SAAS,EAAE;MACzC,IAAIC,mBAAmB,GAAGR,KAAK,CAACS,aAAa,CAACF,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC;QACzEG,SAAS,GAAGF,mBAAmB,CAACE,SAAS;MAE7CV,KAAK,CAACW,aAAa,CAACnB,IAAI,EAAE,MAAM,CAAC;MAEjCD,QAAQ,CAACC,IAAI,EAAEkB,SAAS,CAAC;MAEzB,IAAIV,KAAK,CAACY,KAAK,CAACN,OAAO,EAAE;QACvBN,KAAK,CAACY,KAAK,CAACN,OAAO,CAACd,IAAI,EAAEe,SAAS,CAAC;MACtC;IACF,CAAC;IAEDP,KAAK,CAACa,UAAU,GAAG,UAAUrB,IAAI,EAAEe,SAAS,EAAE;MAC5C,IAAIO,oBAAoB,GAAGd,KAAK,CAACS,aAAa,CAACF,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC;QAC1EQ,eAAe,GAAGD,oBAAoB,CAACC,eAAe;MAE1Df,KAAK,CAACgB,iBAAiB,CAACxB,IAAI,EAAEuB,eAAe,CAAC;MAE9C,IAAIf,KAAK,CAACY,KAAK,CAACC,UAAU,EAAE;QAC1Bb,KAAK,CAACY,KAAK,CAACC,UAAU,CAACrB,IAAI,EAAEe,SAAS,CAAC;MACzC;IACF,CAAC;IAEDP,KAAK,CAACiB,SAAS,GAAG,UAAUzB,IAAI,EAAEe,SAAS,EAAE;MAC3C,IAAIW,eAAe,GAAGlB,KAAK,CAACS,aAAa,CAAC,QAAQ,CAAC,CAACU,aAAa;MAEjE,IAAIC,cAAc,GAAGpB,KAAK,CAACS,aAAa,CAAC,OAAO,CAAC,CAACU,aAAa;MAE/D,IAAIA,aAAa,GAAGZ,SAAS,GAAGW,eAAe,GAAG,GAAG,GAAGE,cAAc,GAAGA,cAAc;MAEvFpB,KAAK,CAACW,aAAa,CAACnB,IAAI,EAAEe,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC;MAEzDhB,QAAQ,CAACC,IAAI,EAAE2B,aAAa,CAAC;MAE7B,IAAInB,KAAK,CAACY,KAAK,CAACK,SAAS,EAAE;QACzBjB,KAAK,CAACY,KAAK,CAACK,SAAS,CAACzB,IAAI,EAAEe,SAAS,CAAC;MACxC;IACF,CAAC;IAEDP,KAAK,CAACqB,MAAM,GAAG,UAAU7B,IAAI,EAAE;MAC7B,IAAI8B,oBAAoB,GAAGtB,KAAK,CAACS,aAAa,CAAC,MAAM,CAAC;QAClDC,SAAS,GAAGY,oBAAoB,CAACZ,SAAS;MAE9CV,KAAK,CAACW,aAAa,CAACnB,IAAI,EAAE,QAAQ,CAAC;MAEnCQ,KAAK,CAACW,aAAa,CAACnB,IAAI,EAAE,OAAO,CAAC;MAElCD,QAAQ,CAACC,IAAI,EAAEkB,SAAS,CAAC;MAEzB,IAAIV,KAAK,CAACY,KAAK,CAACS,MAAM,EAAE;QACtBrB,KAAK,CAACY,KAAK,CAACS,MAAM,CAAC7B,IAAI,CAAC;MAC1B;IACF,CAAC;IAEDQ,KAAK,CAACuB,SAAS,GAAG,UAAU/B,IAAI,EAAE;MAChC,IAAIgC,oBAAoB,GAAGxB,KAAK,CAACS,aAAa,CAAC,MAAM,CAAC;QAClDM,eAAe,GAAGS,oBAAoB,CAACT,eAAe;MAE1Df,KAAK,CAACgB,iBAAiB,CAACxB,IAAI,EAAEuB,eAAe,CAAC;MAE9C,IAAIf,KAAK,CAACY,KAAK,CAACW,SAAS,EAAE;QACzBvB,KAAK,CAACY,KAAK,CAACW,SAAS,CAAC/B,IAAI,CAAC;MAC7B;IACF,CAAC;IAEDQ,KAAK,CAACyB,QAAQ,GAAG,UAAUjC,IAAI,EAAE;MAC/B,IAAIkC,oBAAoB,GAAG1B,KAAK,CAACS,aAAa,CAAC,MAAM,CAAC;QAClDU,aAAa,GAAGO,oBAAoB,CAACP,aAAa;MAEtDnB,KAAK,CAACW,aAAa,CAACnB,IAAI,EAAE,MAAM,CAAC;MAEjCD,QAAQ,CAACC,IAAI,EAAE2B,aAAa,CAAC;MAE7B,IAAInB,KAAK,CAACY,KAAK,CAACa,QAAQ,EAAE;QACxBzB,KAAK,CAACY,KAAK,CAACa,QAAQ,CAACjC,IAAI,CAAC;MAC5B;IACF,CAAC;IAEDQ,KAAK,CAACS,aAAa,GAAG,UAAUkB,IAAI,EAAE;MACpC,IAAIC,UAAU,GAAG5B,KAAK,CAACY,KAAK,CAACgB,UAAU;MACvC,IAAIC,kBAAkB,GAAG,OAAOD,UAAU,KAAK,QAAQ;MACvD,IAAIE,MAAM,GAAGD,kBAAkB,IAAID,UAAU,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAE;MACrE,IAAIlB,SAAS,GAAGmB,kBAAkB,GAAGC,MAAM,GAAGH,IAAI,GAAGC,UAAU,CAACD,IAAI,CAAC;MACrE,IAAIZ,eAAe,GAAGc,kBAAkB,GAAGnB,SAAS,GAAG,SAAS,GAAGkB,UAAU,CAACD,IAAI,GAAG,QAAQ,CAAC;MAC9F,IAAIR,aAAa,GAAGU,kBAAkB,GAAGnB,SAAS,GAAG,OAAO,GAAGkB,UAAU,CAACD,IAAI,GAAG,MAAM,CAAC;MACxF,OAAO;QACLjB,SAAS,EAAEA,SAAS;QACpBK,eAAe,EAAEA,eAAe;QAChCI,aAAa,EAAEA;MACjB,CAAC;IACH,CAAC;IAED,OAAOnB,KAAK;EACd;EAEA,IAAI+B,MAAM,GAAGjC,aAAa,CAAC7B,SAAS;EAEpC8D,MAAM,CAACpB,aAAa,GAAG,SAASA,aAAaA,CAACnB,IAAI,EAAEmC,IAAI,EAAE;IACxD,IAAIK,oBAAoB,GAAG,IAAI,CAACvB,aAAa,CAACkB,IAAI,CAAC;MAC/CjB,SAAS,GAAGsB,oBAAoB,CAACtB,SAAS;MAC1CK,eAAe,GAAGiB,oBAAoB,CAACjB,eAAe;MACtDI,aAAa,GAAGa,oBAAoB,CAACb,aAAa;IAEtDT,SAAS,IAAIb,WAAW,CAACL,IAAI,EAAEkB,SAAS,CAAC;IACzCK,eAAe,IAAIlB,WAAW,CAACL,IAAI,EAAEuB,eAAe,CAAC;IACrDI,aAAa,IAAItB,WAAW,CAACL,IAAI,EAAE2B,aAAa,CAAC;EACnD,CAAC;EAEDY,MAAM,CAACf,iBAAiB,GAAG,SAASA,iBAAiBA,CAACxB,IAAI,EAAEkB,SAAS,EAAE;IACrE;IACA;IACA,IAAIA,SAAS,EAAE;MACb;MACAlB,IAAI,IAAIA,IAAI,CAACyC,SAAS;MACtB;;MAEA1C,QAAQ,CAACC,IAAI,EAAEkB,SAAS,CAAC;IAC3B;EACF,CAAC;EAEDqB,MAAM,CAACG,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAItB,KAAK,GAAGnC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmC,KAAK,CAAC;IAEpC,OAAOA,KAAK,CAACgB,UAAU;IACvB,OAAOlE,MAAM,CAACP,OAAO,CAACgF,aAAa,CAACxE,WAAW,CAACR,OAAO,EAAEsB,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;MAC3EN,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBW,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBJ,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BQ,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBE,QAAQ,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAO3B,aAAa;AACtB,CAAC,CAACpC,MAAM,CAACP,OAAO,CAACiF,SAAS,CAAC;AAE3BtC,aAAa,CAACuC,YAAY,GAAG;EAC3BT,UAAU,EAAE;AACd,CAAC;AACD9B,aAAa,CAACwC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhE,QAAQ,CAAC,CAAC,CAAC,EAAEd,WAAW,CAACR,OAAO,CAACmF,SAAS,EAAE;EAC5G;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEV,UAAU,EAAEhE,UAAU,CAAC8E,eAAe;EAEtC;AACF;AACA;AACA;AACA;AACA;EACEpC,OAAO,EAAElD,SAAS,CAACuF,IAAI;EAEvB;AACF;AACA;AACA;AACA;AACA;EACE9B,UAAU,EAAEzD,SAAS,CAACuF,IAAI;EAE1B;AACF;AACA;AACA;AACA;AACA;EACE1B,SAAS,EAAE7D,SAAS,CAACuF,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;EACEtB,MAAM,EAAEjE,SAAS,CAACuF,IAAI;EAEtB;AACF;AACA;AACA;AACA;EACEpB,SAAS,EAAEnE,SAAS,CAACuF,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;EACElB,QAAQ,EAAErE,SAAS,CAACuF;AACtB,CAAC,CAAC,GAAG,CAAC,CAAC;AACP,IAAIC,QAAQ,GAAG9C,aAAa;AAC5B7C,OAAO,CAACE,OAAO,GAAGyF,QAAQ;AAC1BC,MAAM,CAAC5F,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}