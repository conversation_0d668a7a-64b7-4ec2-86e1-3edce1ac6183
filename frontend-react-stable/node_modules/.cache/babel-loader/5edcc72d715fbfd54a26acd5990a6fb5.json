{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nexport var SpaceCompactItemContext = /*#__PURE__*/React.createContext(null);\nexport var useCompactItemContext = function useCompactItemContext(prefixCls, direction) {\n  var compactItemContext = React.useContext(SpaceCompactItemContext);\n  var compactItemClassnames = React.useMemo(function () {\n    var _classNames;\n    if (!compactItemContext) return '';\n    var compactDirection = compactItemContext.compactDirection,\n      isFirstItem = compactItemContext.isFirstItem,\n      isLastItem = compactItemContext.isLastItem;\n    var separator = compactDirection === 'vertical' ? '-vertical-' : '-';\n    return classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"first-item\"), isFirstItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"last-item\"), isLastItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"item-rtl\"), direction === 'rtl'), _classNames));\n  }, [prefixCls, direction, compactItemContext]);\n  return {\n    compactSize: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactSize,\n    compactDirection: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactDirection,\n    compactItemClassnames: compactItemClassnames\n  };\n};\nexport var NoCompactStyle = function NoCompactStyle(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: null\n  }, children);\n};\nvar CompactItem = function CompactItem(_a) {\n  var children = _a.children,\n    otherProps = __rest(_a, [\"children\"]);\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: otherProps\n  }, children);\n};\nvar Compact = function Compact(props) {\n  var _classNames2;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    directionConfig = _React$useContext.direction;\n  var _props$size = props.size,\n    size = _props$size === void 0 ? 'middle' : _props$size,\n    direction = props.direction,\n    block = props.block,\n    customizePrefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    restProps = __rest(props, [\"size\", \"direction\", \"block\", \"prefixCls\", \"className\", \"children\"]);\n  var prefixCls = getPrefixCls('space-compact', customizePrefixCls);\n  var clx = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-block\"), block), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-vertical\"), direction === 'vertical'), _classNames2), className);\n  var compactItemContext = React.useContext(SpaceCompactItemContext);\n  var childNodes = toArray(children);\n  var nodes = React.useMemo(function () {\n    return childNodes.map(function (child, i) {\n      var key = child && child.key || \"\".concat(prefixCls, \"-item-\").concat(i);\n      return /*#__PURE__*/React.createElement(CompactItem, {\n        key: key,\n        compactSize: size,\n        compactDirection: direction,\n        isFirstItem: i === 0 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isFirstItem)),\n        isLastItem: i === childNodes.length - 1 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isLastItem))\n      }, child);\n    });\n  }, [size, childNodes, compactItemContext]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: clx\n  }, restProps), nodes);\n};\nexport default Compact;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "toArray", "React", "ConfigContext", "SpaceCompactItemContext", "createContext", "useCompactItemContext", "prefixCls", "direction", "compactItemContext", "useContext", "compactItemClassnames", "useMemo", "_classNames", "compactDirection", "isFirstItem", "isLastItem", "separator", "concat", "compactSize", "NoCompactStyle", "_ref", "children", "createElement", "Provider", "value", "CompactItem", "_a", "otherProps", "Compact", "props", "_classNames2", "_React$useContext", "getPrefixCls", "directionConfig", "_props$size", "size", "block", "customizePrefixCls", "className", "restProps", "clx", "childNodes", "nodes", "map", "child", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/space/Compact.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nexport var SpaceCompactItemContext = /*#__PURE__*/React.createContext(null);\nexport var useCompactItemContext = function useCompactItemContext(prefixCls, direction) {\n  var compactItemContext = React.useContext(SpaceCompactItemContext);\n  var compactItemClassnames = React.useMemo(function () {\n    var _classNames;\n    if (!compactItemContext) return '';\n    var compactDirection = compactItemContext.compactDirection,\n      isFirstItem = compactItemContext.isFirstItem,\n      isLastItem = compactItemContext.isLastItem;\n    var separator = compactDirection === 'vertical' ? '-vertical-' : '-';\n    return classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"first-item\"), isFirstItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"last-item\"), isLastItem), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\").concat(separator, \"item-rtl\"), direction === 'rtl'), _classNames));\n  }, [prefixCls, direction, compactItemContext]);\n  return {\n    compactSize: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactSize,\n    compactDirection: compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.compactDirection,\n    compactItemClassnames: compactItemClassnames\n  };\n};\nexport var NoCompactStyle = function NoCompactStyle(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: null\n  }, children);\n};\nvar CompactItem = function CompactItem(_a) {\n  var children = _a.children,\n    otherProps = __rest(_a, [\"children\"]);\n  return /*#__PURE__*/React.createElement(SpaceCompactItemContext.Provider, {\n    value: otherProps\n  }, children);\n};\nvar Compact = function Compact(props) {\n  var _classNames2;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    directionConfig = _React$useContext.direction;\n  var _props$size = props.size,\n    size = _props$size === void 0 ? 'middle' : _props$size,\n    direction = props.direction,\n    block = props.block,\n    customizePrefixCls = props.prefixCls,\n    className = props.className,\n    children = props.children,\n    restProps = __rest(props, [\"size\", \"direction\", \"block\", \"prefixCls\", \"className\", \"children\"]);\n  var prefixCls = getPrefixCls('space-compact', customizePrefixCls);\n  var clx = classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), directionConfig === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-block\"), block), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-vertical\"), direction === 'vertical'), _classNames2), className);\n  var compactItemContext = React.useContext(SpaceCompactItemContext);\n  var childNodes = toArray(children);\n  var nodes = React.useMemo(function () {\n    return childNodes.map(function (child, i) {\n      var key = child && child.key || \"\".concat(prefixCls, \"-item-\").concat(i);\n      return /*#__PURE__*/React.createElement(CompactItem, {\n        key: key,\n        compactSize: size,\n        compactDirection: direction,\n        isFirstItem: i === 0 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isFirstItem)),\n        isLastItem: i === childNodes.length - 1 && (!compactItemContext || (compactItemContext === null || compactItemContext === void 0 ? void 0 : compactItemContext.isLastItem))\n      }, child);\n    });\n  }, [size, childNodes, compactItemContext]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: clx\n  }, restProps), nodes);\n};\nexport default Compact;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,IAAIC,uBAAuB,GAAG,aAAaF,KAAK,CAACG,aAAa,CAAC,IAAI,CAAC;AAC3E,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACtF,IAAIC,kBAAkB,GAAGP,KAAK,CAACQ,UAAU,CAACN,uBAAuB,CAAC;EAClE,IAAIO,qBAAqB,GAAGT,KAAK,CAACU,OAAO,CAAC,YAAY;IACpD,IAAIC,WAAW;IACf,IAAI,CAACJ,kBAAkB,EAAE,OAAO,EAAE;IAClC,IAAIK,gBAAgB,GAAGL,kBAAkB,CAACK,gBAAgB;MACxDC,WAAW,GAAGN,kBAAkB,CAACM,WAAW;MAC5CC,UAAU,GAAGP,kBAAkB,CAACO,UAAU;IAC5C,IAAIC,SAAS,GAAGH,gBAAgB,KAAK,UAAU,GAAG,YAAY,GAAG,GAAG;IACpE,OAAOd,UAAU,EAAEa,WAAW,GAAG,CAAC,CAAC,EAAE5B,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACK,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,CAACW,MAAM,CAACD,SAAS,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAEhC,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACK,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,CAACW,MAAM,CAACD,SAAS,EAAE,YAAY,CAAC,EAAEF,WAAW,CAAC,EAAE9B,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACK,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,CAACW,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAED,UAAU,CAAC,EAAE/B,eAAe,CAAC4B,WAAW,EAAE,EAAE,CAACK,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,CAACW,MAAM,CAACD,SAAS,EAAE,UAAU,CAAC,EAAET,SAAS,KAAK,KAAK,CAAC,EAAEK,WAAW,CAAC,CAAC;EAC/d,CAAC,EAAE,CAACN,SAAS,EAAEC,SAAS,EAAEC,kBAAkB,CAAC,CAAC;EAC9C,OAAO;IACLU,WAAW,EAAEV,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACU,WAAW;IACnHL,gBAAgB,EAAEL,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACK,gBAAgB;IAC7HH,qBAAqB,EAAEA;EACzB,CAAC;AACH,CAAC;AACD,OAAO,IAAIS,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;EACxD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAO,aAAapB,KAAK,CAACqB,aAAa,CAACnB,uBAAuB,CAACoB,QAAQ,EAAE;IACxEC,KAAK,EAAE;EACT,CAAC,EAAEH,QAAQ,CAAC;AACd,CAAC;AACD,IAAII,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAE;EACzC,IAAIL,QAAQ,GAAGK,EAAE,CAACL,QAAQ;IACxBM,UAAU,GAAG1C,MAAM,CAACyC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACvC,OAAO,aAAazB,KAAK,CAACqB,aAAa,CAACnB,uBAAuB,CAACoB,QAAQ,EAAE;IACxEC,KAAK,EAAEG;EACT,CAAC,EAAEN,QAAQ,CAAC;AACd,CAAC;AACD,IAAIO,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,YAAY;EAChB,IAAIC,iBAAiB,GAAG9B,KAAK,CAACQ,UAAU,CAACP,aAAa,CAAC;IACrD8B,YAAY,GAAGD,iBAAiB,CAACC,YAAY;IAC7CC,eAAe,GAAGF,iBAAiB,CAACxB,SAAS;EAC/C,IAAI2B,WAAW,GAAGL,KAAK,CAACM,IAAI;IAC1BA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,WAAW;IACtD3B,SAAS,GAAGsB,KAAK,CAACtB,SAAS;IAC3B6B,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,kBAAkB,GAAGR,KAAK,CAACvB,SAAS;IACpCgC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BjB,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;IACzBkB,SAAS,GAAGtD,MAAM,CAAC4C,KAAK,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;EACjG,IAAIvB,SAAS,GAAG0B,YAAY,CAAC,eAAe,EAAEK,kBAAkB,CAAC;EACjE,IAAIG,GAAG,GAAGzC,UAAU,CAACO,SAAS,GAAGwB,YAAY,GAAG,CAAC,CAAC,EAAE9C,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACb,MAAM,CAACX,SAAS,EAAE,MAAM,CAAC,EAAE2B,eAAe,KAAK,KAAK,CAAC,EAAEjD,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACb,MAAM,CAACX,SAAS,EAAE,QAAQ,CAAC,EAAE8B,KAAK,CAAC,EAAEpD,eAAe,CAAC8C,YAAY,EAAE,EAAE,CAACb,MAAM,CAACX,SAAS,EAAE,WAAW,CAAC,EAAEC,SAAS,KAAK,UAAU,CAAC,EAAEuB,YAAY,GAAGQ,SAAS,CAAC;EACvU,IAAI9B,kBAAkB,GAAGP,KAAK,CAACQ,UAAU,CAACN,uBAAuB,CAAC;EAClE,IAAIsC,UAAU,GAAGzC,OAAO,CAACqB,QAAQ,CAAC;EAClC,IAAIqB,KAAK,GAAGzC,KAAK,CAACU,OAAO,CAAC,YAAY;IACpC,OAAO8B,UAAU,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAEhD,CAAC,EAAE;MACxC,IAAIiD,GAAG,GAAGD,KAAK,IAAIA,KAAK,CAACC,GAAG,IAAI,EAAE,CAAC5B,MAAM,CAACX,SAAS,EAAE,QAAQ,CAAC,CAACW,MAAM,CAACrB,CAAC,CAAC;MACxE,OAAO,aAAaK,KAAK,CAACqB,aAAa,CAACG,WAAW,EAAE;QACnDoB,GAAG,EAAEA,GAAG;QACR3B,WAAW,EAAEiB,IAAI;QACjBtB,gBAAgB,EAAEN,SAAS;QAC3BO,WAAW,EAAElB,CAAC,KAAK,CAAC,KAAK,CAACY,kBAAkB,KAAKA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACM,WAAW,CAAC,CAAC;QACzJC,UAAU,EAAEnB,CAAC,KAAK6C,UAAU,CAAC5C,MAAM,GAAG,CAAC,KAAK,CAACW,kBAAkB,KAAKA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACO,UAAU,CAAC;MAC5K,CAAC,EAAE6B,KAAK,CAAC;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,IAAI,EAAEM,UAAU,EAAEjC,kBAAkB,CAAC,CAAC;EAC1C;EACA,IAAIiC,UAAU,CAAC5C,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,OAAO,aAAaI,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAEvC,QAAQ,CAAC;IACtDuD,SAAS,EAAEE;EACb,CAAC,EAAED,SAAS,CAAC,EAAEG,KAAK,CAAC;AACvB,CAAC;AACD,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}