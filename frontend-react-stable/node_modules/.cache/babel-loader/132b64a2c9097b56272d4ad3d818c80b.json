{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = deviation;\nvar _variance = _interopRequireDefault(require(\"./variance.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction deviation(values, valueof) {\n  const v = (0, _variance.default)(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "deviation", "_variance", "_interopRequireDefault", "require", "obj", "__esModule", "values", "valueof", "v", "Math", "sqrt"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/deviation.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = deviation;\n\nvar _variance = _interopRequireDefault(require(\"./variance.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction deviation(values, valueof) {\n  const v = (0, _variance.default)(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,SAASA,CAACM,MAAM,EAAEC,OAAO,EAAE;EAClC,MAAMC,CAAC,GAAG,CAAC,CAAC,EAAEP,SAAS,CAACF,OAAO,EAAEO,MAAM,EAAEC,OAAO,CAAC;EACjD,OAAOC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGA,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}