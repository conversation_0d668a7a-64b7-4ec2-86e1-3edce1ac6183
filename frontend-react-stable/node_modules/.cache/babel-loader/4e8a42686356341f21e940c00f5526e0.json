{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, Alert, Statistic, Row, Col, Progress } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay = ({\n  result\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",\n          value: result.suggested_threshold,\n          precision: 2,\n          valueStyle: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), (result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        children: \"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [result.duration_seconds !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u6D4B\\u8017\\u65F6\",\n            value: result.duration_seconds,\n            precision: 2,\n            suffix: \"\\u79D2\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 15\n        }, this), result.cpu_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 15\n        }, this), result.memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this), result.gpu_memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 15\n        }, this), result.gpu_utilization_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5229\\u7528\\u7387\",\n            value: result.gpu_utilization_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#eb2f96'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\n_c = PredictionResultDisplay;\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\nconst ModelPredictionPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('local');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState([]);\n  const [selectedModels, setSelectedModels] = useState([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState('');\n  const [selectedProt, setSelectedProt] = useState('');\n  const [selectedDatatype, setSelectedDatatype] = useState('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 任务管理\n  const {\n    submitPredictionTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为简化格式\n      const asyncResult = {\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        message: selectedTask.result.message || '预测完成',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async modelFile => {\n    if (!modelFile || !modelDir) return;\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = modelFile => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict = [];\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels = [];\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error) {\n          var _error$response4, _error$response4$data;\n          message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n        }\n      }\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n      modelsToPredict = validModels;\n    }\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults = [];\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round(i / modelsToPredict.length * 90));\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n        const response = await modelPredictionAPI.predict(formData);\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent\n          });\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      message.error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : csvDir && selectedCsvFile;\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: csvFilesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: csvFilesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCsvFile,\n                onChange: setSelectedCsvFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: csvFilesLoading,\n                children: availableCsvFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6A21\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n            compact: true,\n            style: {\n              marginTop: 8,\n              display: 'flex'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              value: modelDir,\n              onChange: e => setModelDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              style: {\n                flex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: fetchModelFiles,\n              loading: modelsLoading,\n              disabled: !modelDir,\n              style: {\n                marginLeft: 8\n              },\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: predictionMode,\n            onChange: e => setPredictionMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multiple\",\n              children: \"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this), predictionMode === 'single' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedModelFile,\n              onChange: handleModelFileChange,\n              placeholder: \"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), selectedModelFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                  spinning: matchingFilesLoading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 8,\n                      padding: 12,\n                      backgroundColor: '#f5f5f5',\n                      borderRadius: 4\n                    },\n                    children: matchingFilesLoading ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u53C2\\u6570\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 704,\n                          columnNumber: 34\n                        }, this), \" \", selectedParamsFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 705,\n                          columnNumber: 34\n                        }, this), \" \", selectedScalerFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 31\n                      }, this), !showManualSelection && selectedProt && selectedDatatype && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u534F\\u8BAE:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 708,\n                            columnNumber: 38\n                          }, this), \" \", selectedProt]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 708,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 38\n                          }, this), \" \", selectedDatatype]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 709,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this), showManualSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedProt,\n                      onChange: setSelectedProt,\n                      placeholder: \"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",\n                      style: {\n                        width: '100%'\n                      },\n                      children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                        value: prot,\n                        children: prot\n                      }, prot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 29\n                    }, this), selectedProt && /*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedDatatype,\n                      onChange: setSelectedDatatype,\n                      placeholder: `选择与模型对应的 ${selectedProt} 数据类型`,\n                      style: {\n                        width: '100%'\n                      },\n                      children: (datatypeOptions[selectedProt] || []).map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                        value: datatype,\n                        children: datatype\n                      }, datatype, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 744,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              mode: \"multiple\",\n              value: selectedModels,\n              onChange: setSelectedModels,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this), availablePthFiles.length === 0 && !modelsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",\n          description: \"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u9884\\u6D4B\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncPrediction,\n            onChange: e => setUseAsyncPrediction(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            disabled: predictionMode === 'multiple' // 多模型预测暂不支持异步\n            ,\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u9884\\u6D4B\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this), useAsyncPrediction && predictionMode === 'single' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this), predictionMode === 'multiple' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\",\n          description: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\\u76EE\\u524D\\u4EC5\\u652F\\u6301\\u540C\\u6B65\\u6A21\\u5F0F\\uFF0C\\u9884\\u6D4B\\u8FC7\\u7A0B\\u4E2D\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartPrediction,\n        loading: predicting,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: predicting ? '正在预测...' : '开始预测与检测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 9\n      }, this), predicting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 7\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      children: results.length > 1 ?\n      /*#__PURE__*/\n      // 多模型结果展示 - 与Streamlit版本一致\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n            value: selectedResultIndex,\n            onChange: value => setSelectedResultIndex(value),\n            children: results.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: index,\n              children: result.model_name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 878,\n          columnNumber: 15\n        }, this), results[selectedResultIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: [\"\\u6A21\\u578B: \", results[selectedResultIndex].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: results[selectedResultIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // 单模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", results[0].model_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n          result: results[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 9\n    }, this), completedPredictionTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\",\n        description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u9884\\u6D4B\\u6570\\u636E\\u548C\\u5F02\\u5E38\\u68C0\\u6D4B\\u62A5\\u544A\\u3002\",\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u4EFB\\u52A1\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedAsyncTaskId,\n            onChange: handleAsyncTaskSelect,\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u9884\\u6D4B\\u4EFB\\u52A1\",\n            children: completedPredictionTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n              value: task.task_id,\n              children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n            }, task.task_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 13\n        }, this), asyncPredictionResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", asyncPredictionResults[0].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: asyncPredictionResults[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 552,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelPredictionPage, \"E02M65p0bmWfcqbFOGasvd9Bij8=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelPredictionPage;\nexport default ModelPredictionPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PredictionResultDisplay\");\n$RefreshReg$(_c2, \"ModelPredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "modelPredictionAPI", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "result", "children", "gutter", "style", "marginBottom", "span", "title", "value", "suggested_threshold", "precision", "valueStyle", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "showIcon", "marginTop", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "_c", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "_s", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "autoGenerateTemplate", "setAutoGenerateTemplate", "submitPredictionTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedPredictionTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "asyncResult", "model_name", "length", "latestTask", "fetchCsvFiles", "response", "listCsvFiles", "data", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "toString", "allResults", "i", "model", "Math", "round", "predict", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "fontSize", "fontWeight", "className", "direction", "size", "width", "strong", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "map", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status", "index", "includes", "split", "Date", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\n\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n\n      </Row>\n\n\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  suggested_threshold: number;\n  message?: string;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 任务管理\n  const { submitPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为简化格式\n      const asyncResult: PredictionResult = {\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        message: selectedTask.result.message || '预测完成',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测数据和异常检测报告。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                <PredictionResultDisplay result={asyncPredictionResults[0]} />\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,QACH,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AAErE,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGnB,UAAU;AAClC,MAAM;EAAEoB;AAAQ,CAAC,GAAGxB,MAAM;AAC1B,MAAM;EAAEyB;AAAO,CAAC,GAAGvB,MAAM;;AAEzB;AACA,MAAMwB,uBAA+D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAGtF,oBACER,OAAA;IAAAS,QAAA,gBACET,OAAA,CAACR,GAAG;MAACkB,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,eAC3CT,OAAA,CAACP,GAAG;QAACoB,IAAI,EAAE,EAAG;QAAAJ,QAAA,gBACZT,OAAA,CAACT,SAAS;UACRuB,KAAK,EAAC,8DAAiB;UACvBC,KAAK,EAAEP,MAAM,CAACQ,mBAAoB;UAClCC,SAAS,EAAE,CAAE;UACbC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDf,MAAM,CAACQ,mBAAmB,iBACzBhB,OAAA,CAACV,KAAK;UACJF,OAAO,EAAC,kNAAwC;UAChDoC,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRd,KAAK,EAAE;YAAEe,SAAS,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAKL,CAACf,MAAM,CAACmB,gBAAgB,KAAKC,SAAS,IAAIpB,MAAM,CAACqB,WAAW,KAAKD,SAAS,IAAIpB,MAAM,CAACsB,SAAS,KAAKF,SAAS,IAAIpB,MAAM,CAACuB,aAAa,KAAKH,SAAS,IAAIpB,MAAM,CAACwB,uBAAuB,KAAKJ,SAAS,kBACjM5B,OAAA;MAAKW,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBAC/BT,OAAA,CAACG,KAAK;QAAC8B,KAAK,EAAE,CAAE;QAAAxB,QAAA,EAAC;MAAM;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BvB,OAAA,CAACR,GAAG;QAACkB,MAAM,EAAE,EAAG;QAAAD,QAAA,GACbD,MAAM,CAACmB,gBAAgB,KAAKC,SAAS,iBACpC5B,OAAA,CAACP,GAAG;UAACyC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACT,SAAS;YACRuB,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEP,MAAM,CAACmB,gBAAiB;YAC/BV,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,QAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACqB,WAAW,KAAKD,SAAS,iBAC/B5B,OAAA,CAACP,GAAG;UAACyC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACT,SAAS;YACRuB,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAEP,MAAM,CAACqB,WAAY;YAC1BZ,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,GAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACsB,SAAS,KAAKF,SAAS,iBAC7B5B,OAAA,CAACP,GAAG;UAACyC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACT,SAAS;YACRuB,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEP,MAAM,CAACsB,SAAU;YACxBb,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,IAAI;YACXjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACuB,aAAa,KAAKH,SAAS,iBACjC5B,OAAA,CAACP,GAAG;UAACyC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACT,SAAS;YACRuB,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAEP,MAAM,CAACuB,aAAc;YAC5Bd,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,IAAI;YACXjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAf,MAAM,CAACwB,uBAAuB,KAAKJ,SAAS,iBAC3C5B,OAAA,CAACP,GAAG;UAACyC,IAAI,EAAC,MAAM;UAAAzB,QAAA,eACdT,OAAA,CAACT,SAAS;YACRuB,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAEP,MAAM,CAACwB,uBAAwB;YACtCf,SAAS,EAAE,CAAE;YACbkB,MAAM,EAAC,GAAG;YACVjB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CAAC;AAEV,CAAC;;AAED;AAAAa,EAAA,GAhGM7B,uBAA+D;AAiGrE,MAAM8B,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC9C,MAAMC,eAAe,GAAG;EACtBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACnCC,IAAI,EAAE,CAAC,KAAK;AACd,CAAC;AAcD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACuE,MAAM,EAAEC,SAAS,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAAC6E,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACqF,aAAa,EAAEC,gBAAgB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAwB,QAAQ,CAAC;;EAErF;EACA,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAAC2F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5F,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC6F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9F,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACmG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACqG,UAAU,EAAEC,aAAa,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuG,QAAQ,EAAEC,WAAW,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACyG,OAAO,EAAEC,UAAU,CAAC,GAAG1G,QAAQ,CAAqB,EAAE,CAAC;EAC9D,MAAM,CAAC2G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5G,QAAQ,CAAS,CAAC,CAAC;EACzE,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC+G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM;IAAEiH,oBAAoB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAG9F,cAAc,CAAC,CAAC;EAC/F,MAAM,CAAC+F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACsH,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvH,QAAQ,CAAqB,EAAE,CAAC;EAC5F,MAAM,CAACwH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzH,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAM0H,wBAAwB,GAAGR,uBAAuB,CAAC,YAAY,CAAC;;EAEtE;EACA,MAAMS,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,wBAAwB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IAEnF,IAAIC,YAAY,IAAIA,YAAY,CAAC9F,MAAM,EAAE;MACvC;MACA,MAAMkG,WAA6B,GAAG;QACpC1F,mBAAmB,EAAEsF,YAAY,CAAC9F,MAAM,CAACQ,mBAAmB,IAAI,CAAC;QACjE2F,UAAU,EAAEL,YAAY,CAAC9F,MAAM,CAACmG,UAAU,IAAI,MAAM;QACpDvH,OAAO,EAAEkH,YAAY,CAAC9F,MAAM,CAACpB,OAAO,IAAI,MAAM;QAC9CuC,gBAAgB,EAAE2E,YAAY,CAAC9F,MAAM,CAACmB,gBAAgB;QACtDE,WAAW,EAAEyE,YAAY,CAAC9F,MAAM,CAACqB,WAAW;QAC5CC,SAAS,EAAEwE,YAAY,CAAC9F,MAAM,CAACsB,SAAS;QACxCC,aAAa,EAAEuE,YAAY,CAAC9F,MAAM,CAACuB,aAAa;QAChDC,uBAAuB,EAAEsE,YAAY,CAAC9F,MAAM,CAACwB;MAC/C,CAAC;MAEDgE,yBAAyB,CAAC,CAACU,WAAW,CAAC,CAAC;IAC1C;EACF,CAAC;;EAED;EACAhI,SAAS,CAAC,MAAM;IACdkH,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAlH,SAAS,CAAC,MAAM;IACd,IAAIyH,wBAAwB,CAACS,MAAM,GAAG,CAAC,IAAI,CAACX,mBAAmB,EAAE;MAC/D,MAAMY,UAAU,GAAGV,wBAAwB,CAACA,wBAAwB,CAACS,MAAM,GAAG,CAAC,CAAC;MAChFR,qBAAqB,CAACS,UAAU,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,wBAAwB,EAAEF,mBAAmB,CAAC,CAAC;;EAEnD;EACA,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC9D,MAAM,EAAE;IAEbO,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMwD,QAAQ,GAAG,MAAMlH,kBAAkB,CAACmH,YAAY,CAAChE,MAAM,CAAC;MAC9DG,oBAAoB,CAAC4D,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBjI,OAAO,CAAC+H,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAa,CAAC;MAC5DnE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMgE,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC/D,QAAQ,EAAE;IAEfO,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAMlH,kBAAkB,CAAC2H,cAAc,CAAChE,QAAQ,CAAC;MAClEG,oBAAoB,CAACoD,QAAQ,CAACE,IAAI,CAACQ,SAAS,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAON,KAAU,EAAE;MAAA,IAAAO,gBAAA,EAAAC,qBAAA;MACnBvI,OAAO,CAAC+H,KAAK,CAAC,EAAAO,gBAAA,GAAAP,KAAK,CAACJ,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,YAAY,CAAC;MAC3D3D,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM6D,cAAc,GAAG,MAAOC,SAAiB,IAAK;IAClD,IAAI,CAACA,SAAS,IAAI,CAACrE,QAAQ,EAAE;IAE7B+B,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF;MACA,MAAMwB,QAAQ,GAAG,MAAMlH,kBAAkB,CAACiI,gBAAgB,CAACD,SAAS,EAAErE,QAAQ,CAAC;MAE/E,IAAIuD,QAAQ,CAACE,IAAI,EAAE;QACjB,MAAMc,aAAa,GAAGhB,QAAQ,CAACE,IAAI;;QAEnC;QACA5C,qBAAqB,CAAC0D,aAAa,CAACC,eAAe,IAAI,EAAE,CAAC;QAC1DzD,qBAAqB,CAACwD,aAAa,CAACE,eAAe,IAAI,EAAE,CAAC;QAC1DxD,eAAe,CAACsD,aAAa,CAACG,QAAQ,IAAI,EAAE,CAAC;QAC7CvD,mBAAmB,CAACoD,aAAa,CAACI,QAAQ,IAAI,EAAE,CAAC;;QAEjD;QACA,IAAIJ,aAAa,CAACC,eAAe,IAAID,aAAa,CAACE,eAAe,EAAE;UAClE7I,OAAO,CAACgJ,OAAO,CAAC,aAAa,CAAC;QAChC;QAEA,IAAIL,aAAa,CAACG,QAAQ,IAAIH,aAAa,CAACI,QAAQ,EAAE;UACpD/I,OAAO,CAACgJ,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC;UAC5FtD,sBAAsB,CAAC,KAAK,CAAC;QAC/B,CAAC,MAAM;UACLzF,OAAO,CAACiJ,OAAO,CAAC,4BAA4B,CAAC;UAC7CxD,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC,CAAC,OAAOsC,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnBnJ,OAAO,CAAC+H,KAAK,CAAC,EAAAmB,gBAAA,GAAAnB,KAAK,CAACJ,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAI,UAAU,CAAC;MACzD;MACA,MAAMkB,kBAAkB,GAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACnEpE,qBAAqB,CAAC,GAAGmE,kBAAkB,cAAc,CAAC;MAC1DjE,qBAAqB,CAAC,GAAGiE,kBAAkB,eAAe,CAAC;MAC3D3D,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,SAAS;MACRU,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMmD,qBAAqB,GAAIb,SAAiB,IAAK;IACnD1D,oBAAoB,CAAC0D,SAAS,CAAC;IAC/B;IACAxD,qBAAqB,CAAC,EAAE,CAAC;IACzBE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,eAAe,CAAC,EAAE,CAAC;IACnBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,sBAAsB,CAAC,KAAK,CAAC;;IAE7B;IACA+C,cAAc,CAACC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACAnJ,SAAS,CAAC,MAAM;IACd,IAAIkE,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAAC4D,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAM+B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B9B,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM+B,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC/F,UAAU,EAAEI,MAAM,CAAC,CAAC;EAExBtE,SAAS,CAAC,MAAM;IACd,IAAI8E,QAAQ,IAAIA,QAAQ,CAACoD,MAAM,GAAG,CAAC,EAAE;MAAE;MACrC,MAAM+B,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BrB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMsB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACnF,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMsF,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAE;QAC5B7D,eAAe,CAACqG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLtG,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMuG,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAI1G,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;MAC5C1D,OAAO,CAAC+H,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIvE,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,eAAe,CAAC,EAAE;MAC3DhE,OAAO,CAAC+H,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;;IAEA;IACA,IAAIoC,eAMF,GAAG,EAAE;IAEP,IAAIvF,cAAc,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACE,iBAAiB,IAAI,CAACM,YAAY,IAAI,CAACE,gBAAgB,EAAE;QAC5DtF,OAAO,CAAC+H,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MACAoC,eAAe,GAAG,CAAC;QACjBC,UAAU,EAAEtF,iBAAiB;QAC7BuF,WAAW,EAAErF,kBAAkB;QAC/BsF,WAAW,EAAEpF,kBAAkB;QAC/B4D,QAAQ,EAAE1D,YAAY;QACtB2D,QAAQ,EAAEzD;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAId,cAAc,CAACgD,MAAM,KAAK,CAAC,EAAE;QAC/BxH,OAAO,CAAC+H,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;;MAEA;MACA,MAAMwC,WAMJ,GAAG,EAAE;MAEP,KAAK,MAAM9B,SAAS,IAAIjE,cAAc,EAAE;QACtC,IAAI;UACF,MAAMmD,QAAQ,GAAG,MAAMlH,kBAAkB,CAACiI,gBAAgB,CAACD,SAAS,EAAErE,QAAQ,CAAC;UAC/E,IAAIuD,QAAQ,CAACE,IAAI,EAAE;YACjB,MAAMc,aAAa,GAAGhB,QAAQ,CAACE,IAAI;YACnC,MAAMwC,WAAW,GAAG1B,aAAa,CAACC,eAAe;YACjD,MAAM0B,WAAW,GAAG3B,aAAa,CAACE,eAAe;YACjD,MAAMC,QAAQ,GAAGH,aAAa,CAACG,QAAQ;YACvC,MAAMC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;;YAEvC;YACA,IAAIsB,WAAW,IAAIC,WAAW,IAAIxB,QAAQ,IAAIC,QAAQ,EAAE;cACtDwB,WAAW,CAACC,IAAI,CAAC;gBACfJ,UAAU,EAAE3B,SAAS;gBACrB4B,WAAW;gBACXC,WAAW;gBACXxB,QAAQ;gBACRC;cACF,CAAC,CAAC;cACF/I,OAAO,CAACgJ,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC;YACrE,CAAC,MAAM;cACL/I,OAAO,CAACiJ,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC;YACxD;UACF,CAAC,MAAM;YACLzI,OAAO,CAAC+H,KAAK,CAAC,QAAQU,SAAS,UAAU,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOV,KAAU,EAAE;UAAA,IAAA0C,gBAAA,EAAAC,qBAAA;UACnB1K,OAAO,CAAC+H,KAAK,CAAC,QAAQU,SAAS,SAAS,EAAAgC,gBAAA,GAAA1C,KAAK,CAACJ,QAAQ,cAAA8C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBxC,MAAM,KAAIH,KAAK,CAAC/H,OAAO,EAAE,CAAC;QAC1F;MACF;MAEA,IAAIuK,WAAW,CAAC/C,MAAM,KAAK,CAAC,EAAE;QAC5BxH,OAAO,CAAC+H,KAAK,CAAC,wCAAwC,CAAC;QACvD;MACF;MAEAoC,eAAe,GAAGI,WAAW;IAC/B;IAEA5E,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIU,kBAAkB,IAAI7B,cAAc,KAAK,QAAQ,EAAE;QACrD;QACA,MAAM+F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAIpH,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3CiH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEnH,YAAY,CAACoH,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEjH,MAAM,CAAC;UAClC+G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE7G,eAAe,CAAC;QACnD;QAEA2G,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE/F,iBAAiB,CAAC;QACpD6F,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE7F,kBAAkB,CAAC;QACtD2F,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE3F,kBAAkB,CAAC;QACtDyF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEzF,YAAY,CAAC;QAC9CuF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEvF,gBAAgB,CAAC;QACtDqF,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEzG,QAAQ,CAAC;QACtCuG,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEzG,QAAQ,CAAC;QAC1CuG,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEzE,oBAAoB,CAAC2E,QAAQ,CAAC,CAAC,CAAC;;QAE1E;QACA,MAAM9D,MAAM,GAAG,MAAMX,oBAAoB,CAACqE,QAAQ,CAAC;QAEnD,IAAI1D,MAAM,EAAE;UACVjH,OAAO,CAACgJ,OAAO,CAAC,gCAAgC,CAAC;UACjD;UACArD,aAAa,CAAC,KAAK,CAAC;UACpBE,WAAW,CAAC,CAAC,CAAC;QAChB;QAEA,OAAO,CAAC;MACV;;MAEA;MACA,MAAMmF,UAA8B,GAAG,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,eAAe,CAAC3C,MAAM,EAAEyD,CAAC,EAAE,EAAE;QAC/C,MAAMC,KAAK,GAAGf,eAAe,CAACc,CAAC,CAAC;;QAEhC;QACApF,WAAW,CAACsF,IAAI,CAACC,KAAK,CAAEH,CAAC,GAAGd,eAAe,CAAC3C,MAAM,GAAI,EAAE,CAAC,CAAC;QAE1D,IAAI2C,eAAe,CAAC3C,MAAM,GAAG,CAAC,EAAE;UAC9BxH,OAAO,CAACgK,IAAI,CAAC,UAAUiB,CAAC,GAAG,CAAC,IAAId,eAAe,CAAC3C,MAAM,KAAK0D,KAAK,CAACd,UAAU,UAAU,CAAC;QACxF;QAEA,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAIpH,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3C;UACAiH,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEnH,YAAY,CAACoH,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEjH,MAAM,CAAC;UAClC+G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE7G,eAAe,CAAC;QACnD;QAEA2G,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEK,KAAK,CAACd,UAAU,CAAC;QACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEK,KAAK,CAACb,WAAW,CAAC;QACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEK,KAAK,CAACZ,WAAW,CAAC;QACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEK,KAAK,CAACpC,QAAQ,CAAC;QAChD6B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEK,KAAK,CAACnC,QAAQ,CAAC;QACpD4B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEzG,QAAQ,CAAC;QAC1CuG,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEzE,oBAAoB,CAAC2E,QAAQ,CAAC,CAAC,CAAC;QAE1E,MAAMpD,QAAQ,GAAG,MAAMlH,kBAAkB,CAAC4K,OAAO,CAACV,QAAQ,CAAC;QAE3D,IAAIhD,QAAQ,CAACE,IAAI,EAAE;UACjBmD,UAAU,CAACR,IAAI,CAAC;YACdjD,UAAU,EAAEI,QAAQ,CAACE,IAAI,CAACN,UAAU,IAAI,GAAG2D,KAAK,CAACpC,QAAQ,IAAIoC,KAAK,CAACnC,QAAQ,EAAE;YAC7EnH,mBAAmB,EAAE+F,QAAQ,CAACE,IAAI,CAACjG,mBAAmB,IAAI,CAAC;YAC3D5B,OAAO,EAAE2H,QAAQ,CAACE,IAAI,CAAC7H,OAAO,IAAI,MAAM;YACxC;YACAuC,gBAAgB,EAAEoF,QAAQ,CAACE,IAAI,CAACtF,gBAAgB;YAChDE,WAAW,EAAEkF,QAAQ,CAACE,IAAI,CAACpF,WAAW;YACtCC,SAAS,EAAEiF,QAAQ,CAACE,IAAI,CAACnF,SAAS;YAClCC,aAAa,EAAEgF,QAAQ,CAACE,IAAI,CAAClF,aAAa;YAC1CC,uBAAuB,EAAE+E,QAAQ,CAACE,IAAI,CAACjF;UACzC,CAAC,CAAC;UAEF,IAAIuH,eAAe,CAAC3C,MAAM,GAAG,CAAC,EAAE;YAC9BxH,OAAO,CAACgJ,OAAO,CAAC,QAAQkC,KAAK,CAACd,UAAU,OAAO,CAAC;UAClD;QACF;MACF;MAEAvE,WAAW,CAAC,GAAG,CAAC;MAChBE,UAAU,CAACiF,UAAU,CAAC;MACtBhL,OAAO,CAACgJ,OAAO,CAAC,SAASgC,UAAU,CAACxD,MAAM,SAAS,CAAC;IAEtD,CAAC,CAAC,OAAOO,KAAU,EAAE;MAAA,IAAAuD,gBAAA,EAAAC,qBAAA;MACnBvL,OAAO,CAAC+H,KAAK,CAAC,EAAAuD,gBAAA,GAAAvD,KAAK,CAACJ,QAAQ,cAAA2D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsBrD,MAAM,KAAI,MAAM,CAAC;IACvD,CAAC,SAAS;MACRvC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM6F,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAGjI,UAAU,KAAK,QAAQ,GAAGE,YAAY,GAAIE,MAAM,IAAII,eAAgB;IAEpF,IAAIY,cAAc,KAAK,QAAQ,EAAE;MAC/B,OAAO6G,OAAO,IAAI3G,iBAAiB,IAAIM,YAAY,IAAIE,gBAAgB;IACzE,CAAC,MAAM;MACL,OAAOmG,OAAO,IAAIjH,cAAc,CAACgD,MAAM,GAAG,CAAC;IAC7C;EACF,CAAC;EAID,oBACE5G,OAAA;IAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;MAAC8B,KAAK,EAAE,CAAE;MAACtB,KAAK,EAAE;QAAEmK,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEnK,YAAY,EAAE;MAAM,CAAE;MAAAH,QAAA,EAAC;IAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACvGvB,OAAA,CAACI,IAAI;MAACoB,IAAI,EAAC,WAAW;MAAAf,QAAA,EAAC;IAEvB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPvB,OAAA,CAACb,OAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXvB,OAAA,CAACrB,IAAI;MAACmC,KAAK,EAAC,gCAAO;MAACkK,SAAS,EAAC,eAAe;MAAAvK,QAAA,eAC3CT,OAAA,CAACd,KAAK;QAAC+L,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvK,KAAK,EAAE;UAAEwK,KAAK,EAAE;QAAO,CAAE;QAAA1K,QAAA,gBAChET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAM;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BvB,OAAA,CAACpB,KAAK,CAACyM,KAAK;YACVtK,KAAK,EAAE6B,UAAW;YAClBuG,QAAQ,EAAGmC,CAAC,IAAKzI,aAAa,CAACyI,CAAC,CAACC,MAAM,CAACxK,KAAK,CAAE;YAC/CJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAExBT,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCvB,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLqB,UAAU,KAAK,OAAO,iBACrB5C,OAAA,CAACd,KAAK;UAAC+L,SAAS,EAAC,UAAU;UAACtK,KAAK,EAAE;YAAEwK,KAAK,EAAE;UAAO,CAAE;UAAA1K,QAAA,gBACnDT,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;cAACgL,MAAM;cAAA3K,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BvB,OAAA,CAAClB,KAAK,CAACuM,KAAK;cAACG,OAAO;cAAC7K,KAAK,EAAE;gBAAEe,SAAS,EAAE,CAAC;gBAAE+J,OAAO,EAAE;cAAO,CAAE;cAAAhL,QAAA,gBAC5DT,OAAA,CAAClB,KAAK;gBACJiC,KAAK,EAAEiC,MAAO;gBACdmG,QAAQ,EAAGmC,CAAC,IAAKrI,SAAS,CAACqI,CAAC,CAACC,MAAM,CAACxK,KAAK,CAAE;gBAC3C2K,WAAW,EAAC,4BAAkB;gBAC9B/K,KAAK,EAAE;kBAAEuB,IAAI,EAAE;gBAAE;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFvB,OAAA,CAAChB,MAAM;gBACLwC,IAAI,EAAC,SAAS;gBACdmK,OAAO,EAAE7E,aAAc;gBACvB8E,OAAO,EAAEtI,eAAgB;gBACzBuI,QAAQ,EAAE,CAAC7I,MAAO;gBAClBrC,KAAK,EAAE;kBAAEmL,UAAU,EAAE;gBAAE,CAAE;gBAAArL,QAAA,EAC1B;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENvB,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;cAACgL,MAAM;cAAA3K,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BvB,OAAA,CAACX,IAAI;cAAC0M,QAAQ,EAAEzI,eAAgB;cAAA7C,QAAA,eAC9BT,OAAA,CAACjB,MAAM;gBACLgC,KAAK,EAAEqC,eAAgB;gBACvB+F,QAAQ,EAAE9F,kBAAmB;gBAC7BqI,WAAW,EAAC,mCAAU;gBACtB/K,KAAK,EAAE;kBAAEwK,KAAK,EAAE,MAAM;kBAAEzJ,SAAS,EAAE;gBAAE,CAAE;gBACvCkK,OAAO,EAAEtI,eAAgB;gBAAA7C,QAAA,EAExByC,iBAAiB,CAAC8I,GAAG,CAAEC,IAAI,iBAC1BjM,OAAA,CAACM,MAAM;kBAAYS,KAAK,EAAEkL,IAAK;kBAAAxL,QAAA,EAC5BwL;gBAAI,GADMA,IAAI;kBAAA7K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGAqB,UAAU,KAAK,QAAQ,iBACtB5C,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCvB,OAAA,CAACK,OAAO;YAAA,GAAKyI,WAAW;YAAEnI,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAChDT,OAAA;cAAGgL,SAAS,EAAC,sBAAsB;cAAAvK,QAAA,eACjCT,OAAA,CAACL,aAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJvB,OAAA;cAAGgL,SAAS,EAAC,iBAAiB;cAAAvK,QAAA,EAAC;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDvB,OAAA;cAAGgL,SAAS,EAAC,iBAAiB;cAAAvK,QAAA,EAAC;YAE/B;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACrB,IAAI;MAACmC,KAAK,EAAC,0BAAM;MAACkK,SAAS,EAAC,eAAe;MAAAvK,QAAA,eAC1CT,OAAA,CAACd,KAAK;QAAC+L,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvK,KAAK,EAAE;UAAEwK,KAAK,EAAE;QAAO,CAAE;QAAA1K,QAAA,gBAChET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvB,OAAA,CAAClB,KAAK,CAACuM,KAAK;YAACG,OAAO;YAAC7K,KAAK,EAAE;cAAEe,SAAS,EAAE,CAAC;cAAE+J,OAAO,EAAE;YAAO,CAAE;YAAAhL,QAAA,gBAC5DT,OAAA,CAAClB,KAAK;cACJiC,KAAK,EAAEyC,QAAS;cAChB2F,QAAQ,EAAGmC,CAAC,IAAK7H,WAAW,CAAC6H,CAAC,CAACC,MAAM,CAACxK,KAAK,CAAE;cAC7C2K,WAAW,EAAC,4BAAkB;cAC9B/K,KAAK,EAAE;gBAAEuB,IAAI,EAAE;cAAE;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFvB,OAAA,CAAChB,MAAM;cACLwC,IAAI,EAAC,SAAS;cACdmK,OAAO,EAAEpE,eAAgB;cACzBqE,OAAO,EAAE9H,aAAc;cACvB+H,QAAQ,EAAE,CAACrI,QAAS;cACpB7C,KAAK,EAAE;gBAAEmL,UAAU,EAAE;cAAE,CAAE;cAAArL,QAAA,EAC1B;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAENvB,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvB,OAAA,CAACpB,KAAK,CAACyM,KAAK;YACVtK,KAAK,EAAEiD,cAAe;YACtBmF,QAAQ,EAAGmC,CAAC,IAAKrH,iBAAiB,CAACqH,CAAC,CAACC,MAAM,CAACxK,KAAK,CAAE;YACnDJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBAExBT,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCvB,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELyC,cAAc,KAAK,QAAQ,gBAC1BhE,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAACX,IAAI;YAAC0M,QAAQ,EAAEjI,aAAc;YAAArD,QAAA,eAC5BT,OAAA,CAACjB,MAAM;cACLgC,KAAK,EAAEmD,iBAAkB;cACzBiF,QAAQ,EAAET,qBAAsB;cAChCgD,WAAW,EAAC,kMAAkC;cAC9C/K,KAAK,EAAE;gBAAEwK,KAAK,EAAE,MAAM;gBAAEzJ,SAAS,EAAE;cAAE,CAAE;cACvCkK,OAAO,EAAE9H,aAAc;cAAArD,QAAA,EAEtBiD,iBAAiB,CAACsI,GAAG,CAAEC,IAAI,iBAC1BjM,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEkL,IAAK;gBAAAxL,QAAA,EAC5BwL;cAAI,GADMA,IAAI;gBAAA7K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAEN2C,iBAAiB,iBAChBlE,OAAA;YAAKW,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAG,CAAE;YAAAjB,QAAA,eAC5BT,OAAA,CAACd,KAAK;cAAC+L,SAAS,EAAC,UAAU;cAACtK,KAAK,EAAE;gBAAEwK,KAAK,EAAE;cAAO,CAAE;cAAA1K,QAAA,gBACnDT,OAAA;gBAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAAAf,QAAA,EAAC;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCvB,OAAA,CAACX,IAAI;kBAAC0M,QAAQ,EAAEzG,oBAAqB;kBAAA7E,QAAA,eACnCT,OAAA;oBAAKW,KAAK,EAAE;sBAAEe,SAAS,EAAE,CAAC;sBAAEwK,OAAO,EAAE,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAA3L,QAAA,EACpF6E,oBAAoB,gBACnBtF,OAAA;sBAAAS,QAAA,EAAG;oBAAa;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,gBAEpBvB,OAAA,CAAAE,SAAA;sBAAAO,QAAA,gBACET,OAAA;wBAAAS,QAAA,gBAAGT,OAAA;0BAAAS,QAAA,EAAQ;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC6C,kBAAkB,IAAI,KAAK;sBAAA;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3DvB,OAAA;wBAAAS,QAAA,gBAAGT,OAAA;0BAAAS,QAAA,EAAQ;wBAAO;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC+C,kBAAkB,IAAI,KAAK;sBAAA;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC5D,CAACqD,mBAAmB,IAAIJ,YAAY,IAAIE,gBAAgB,iBACvD1E,OAAA,CAAAE,SAAA;wBAAAO,QAAA,gBACET,OAAA;0BAAAS,QAAA,gBAAGT,OAAA;4BAAAS,QAAA,EAAQ;0BAAG;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACiD,YAAY;wBAAA;0BAAApD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1CvB,OAAA;0BAAAS,QAAA,gBAAGT,OAAA;4BAAAS,QAAA,EAAQ;0BAAK;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmD,gBAAgB;wBAAA;0BAAAtD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eAChD,CACH;oBAAA,eACD;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAELqD,mBAAmB,iBAClB5E,OAAA;gBAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;kBAACgL,MAAM;kBAAA3K,QAAA,EAAC;gBAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCvB,OAAA;kBAAKW,KAAK,EAAE;oBAAEe,SAAS,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,eAC3BT,OAAA,CAACd,KAAK;oBAAC+L,SAAS,EAAC,UAAU;oBAACtK,KAAK,EAAE;sBAAEwK,KAAK,EAAE;oBAAO,CAAE;oBAAA1K,QAAA,gBACnDT,OAAA,CAACjB,MAAM;sBACLgC,KAAK,EAAEyD,YAAa;sBACpB2E,QAAQ,EAAE1E,eAAgB;sBAC1BiH,WAAW,EAAC,8DAAY;sBACxB/K,KAAK,EAAE;wBAAEwK,KAAK,EAAE;sBAAO,CAAE;sBAAA1K,QAAA,EAExB4B,eAAe,CAAC2J,GAAG,CAAEK,IAAI,iBACxBrM,OAAA,CAACM,MAAM;wBAAYS,KAAK,EAAEsL,IAAK;wBAAA5L,QAAA,EAC5B4L;sBAAI,GADMA,IAAI;wBAAAjL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EAERiD,YAAY,iBACXxE,OAAA,CAACjB,MAAM;sBACLgC,KAAK,EAAE2D,gBAAiB;sBACxByE,QAAQ,EAAExE,mBAAoB;sBAC9B+G,WAAW,EAAE,YAAYlH,YAAY,OAAQ;sBAC7C7D,KAAK,EAAE;wBAAEwK,KAAK,EAAE;sBAAO,CAAE;sBAAA1K,QAAA,EAExB,CAAC6B,eAAe,CAACkC,YAAY,CAAiC,IAAI,EAAE,EAAEwH,GAAG,CAAE7D,QAAQ,iBAClFnI,OAAA,CAACM,MAAM;wBAAgBS,KAAK,EAAEoH,QAAS;wBAAA1H,QAAA,EACpC0H;sBAAQ,GADEA,QAAQ;wBAAA/G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEb,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENvB,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BvB,OAAA,CAACX,IAAI;YAAC0M,QAAQ,EAAEjI,aAAc;YAAArD,QAAA,eAC5BT,OAAA,CAACjB,MAAM;cACLuN,IAAI,EAAC,UAAU;cACfvL,KAAK,EAAE6C,cAAe;cACtBuF,QAAQ,EAAEtF,iBAAkB;cAC5B6H,WAAW,EAAC,4FAAiB;cAC7B/K,KAAK,EAAE;gBAAEwK,KAAK,EAAE,MAAM;gBAAEzJ,SAAS,EAAE;cAAE,CAAE;cACvCkK,OAAO,EAAE9H,aAAc;cAAArD,QAAA,EAEtBiD,iBAAiB,CAACsI,GAAG,CAAEC,IAAI,iBAC1BjM,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEkL,IAAK;gBAAAxL,QAAA,EAC5BwL;cAAI,GADMA,IAAI;gBAAA7K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAmC,iBAAiB,CAACkD,MAAM,KAAK,CAAC,IAAI,CAAC9C,aAAa,iBAC/C9D,OAAA,CAACV,KAAK;UACJF,OAAO,EAAC,4CAAS;UACjBmN,WAAW,EAAC,oOAA2C;UACvD/K,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACrB,IAAI;MAACqM,SAAS,EAAC,eAAe;MAAClK,KAAK,EAAC,0BAAM;MAAAL,QAAA,eAC1CT,OAAA,CAACd,KAAK;QAAC+L,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACvK,KAAK,EAAE;UAAEwK,KAAK,EAAE;QAAO,CAAE;QAAA1K,QAAA,gBACjET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAACpB,KAAK,CAACyM,KAAK;YACVtK,KAAK,EAAE8E,kBAAmB;YAC1BsD,QAAQ,EAAGmC,CAAC,IAAKxF,qBAAqB,CAACwF,CAAC,CAACC,MAAM,CAACxK,KAAK,CAAE;YACvDJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YACxBmK,QAAQ,EAAE7H,cAAc,KAAK,UAAW,CAAC;YAAA;YAAAvD,QAAA,gBAEzCT,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAE,IAAK;cAAAN,QAAA,eACjBT,OAAA,CAACd,KAAK;gBAAAuB,QAAA,GAAC,kDAEL,eAAAT,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEmK,QAAQ,EAAE;kBAAG,CAAE;kBAAArK,QAAA,EAAC;gBAEhD;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRvB,OAAA,CAACpB,KAAK;cAACmC,KAAK,EAAE,KAAM;cAAAN,QAAA,eAClBT,OAAA,CAACd,KAAK;gBAAAuB,QAAA,GAAC,0BAEL,eAAAT,OAAA,CAACI,IAAI;kBAACoB,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEmK,QAAQ,EAAE;kBAAG,CAAE;kBAAArK,QAAA,EAAC;gBAEhD;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELsE,kBAAkB,IAAI7B,cAAc,KAAK,QAAQ,iBAChDhE,OAAA,CAACV,KAAK;UACJF,OAAO,EAAC,sCAAQ;UAChBmN,WAAW,eACTvM,OAAA;YAAAS,QAAA,GAAK,8JAEH,eAAAT,OAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAvB,OAAA;cAAAS,QAAA,EAAQ;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDC,IAAI,EAAC,MAAM;UACXC,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EAEAyC,cAAc,KAAK,UAAU,iBAC5BhE,OAAA,CAACV,KAAK;UACJF,OAAO,EAAC,4CAAS;UACjBmN,WAAW,EAAC,0KAA8B;UAC1C/K,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPvB,OAAA,CAACrB,IAAI;MAACqM,SAAS,EAAC,eAAe;MAAAvK,QAAA,gBAC7BT,OAAA,CAAChB,MAAM;QACLwC,IAAI,EAAC,SAAS;QACd0J,IAAI,EAAC,OAAO;QACZsB,IAAI,eAAExM,OAAA,CAACJ,kBAAkB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BoK,OAAO,EAAErC,qBAAsB;QAC/BsC,OAAO,EAAE9G,UAAW;QACpB+G,QAAQ,EAAE,CAACjB,WAAW,CAAC,CAAE;QACzBI,SAAS,EAAC,eAAe;QAAAvK,QAAA,EAExBqE,UAAU,GAAG,SAAS,GAAG;MAAS;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EAGRuD,UAAU,iBACT9E,OAAA;QAAKgL,SAAS,EAAC,kBAAkB;QAAAvK,QAAA,gBAC/BT,OAAA,CAACI,IAAI;UAAAK,QAAA,EAAC;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBvB,OAAA,CAACN,QAAQ;UAAC+M,OAAO,EAAEzH,QAAS;UAAC0H,MAAM,EAAC;QAAQ;UAAAtL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGN2D,OAAO,CAAC0B,MAAM,GAAG,CAAC,iBACjB5G,OAAA,CAACrB,IAAI;MAACmC,KAAK,EAAC,0BAAM;MAACkK,SAAS,EAAC,eAAe;MAAAvK,QAAA,EACzCyE,OAAO,CAAC0B,MAAM,GAAG,CAAC;MAAA;MACjB;MACA5G,OAAA;QAAAS,QAAA,gBACET,OAAA,CAACb,OAAO;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXvB,OAAA,CAACG,KAAK;UAAC8B,KAAK,EAAE,CAAE;UAAAxB,QAAA,EAAC;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCvB,OAAA;UAAKW,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAH,QAAA,gBAC/BT,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BvB,OAAA,CAACjB,MAAM;YACL4B,KAAK,EAAE;cAAEwK,KAAK,EAAE,MAAM;cAAEzJ,SAAS,EAAE;YAAE,CAAE;YACvCgK,WAAW,EAAC,sCAAQ;YACpB3K,KAAK,EAAEqE,mBAAoB;YAC3B+D,QAAQ,EAAGpI,KAAK,IAAKsE,sBAAsB,CAACtE,KAAK,CAAE;YAAAN,QAAA,EAElDyE,OAAO,CAAC8G,GAAG,CAAC,CAACxL,MAAM,EAAEmM,KAAK,kBACzB3M,OAAA,CAACM,MAAM;cAAaS,KAAK,EAAE4L,KAAM;cAAAlM,QAAA,EAC9BD,MAAM,CAACmG;YAAU,GADPgG,KAAK;cAAAvL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL2D,OAAO,CAACE,mBAAmB,CAAC,iBAC3BpF,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;YAAC8B,KAAK,EAAE,CAAE;YAAAxB,QAAA,GAAC,gBAAI,EAACyE,OAAO,CAACE,mBAAmB,CAAC,CAACuB,UAAU;UAAA;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtEvB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAE0E,OAAO,CAACE,mBAAmB;UAAE;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAvB,OAAA;QAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;UAAC8B,KAAK,EAAE,CAAE;UAAAxB,QAAA,GAAC,6BAAO,EAACyE,OAAO,CAAC,CAAC,CAAC,CAACyB,UAAU;QAAA;UAAAvF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvDvB,OAAA,CAACO,uBAAuB;UAACC,MAAM,EAAE0E,OAAO,CAAC,CAAC;QAAE;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACP,EAGA4E,wBAAwB,CAACS,MAAM,GAAG,CAAC,iBAClC5G,OAAA,CAACrB,IAAI;MAACmC,KAAK,EAAC,sCAAQ;MAACkK,SAAS,EAAC,eAAe;MAACrK,KAAK,EAAE;QAAEe,SAAS,EAAE;MAAG,CAAE;MAAAjB,QAAA,gBACtET,OAAA,CAACV,KAAK;QACJF,OAAO,EAAC,4CAAS;QACjBmN,WAAW,EAAC,sLAAgC;QAC5C/K,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRd,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFvB,OAAA,CAACd,KAAK;QAAC+L,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvK,KAAK,EAAE;UAAEwK,KAAK,EAAE;QAAO,CAAE;QAAA1K,QAAA,gBAEhET,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACI,IAAI;YAACgL,MAAM;YAAA3K,QAAA,EAAC;UAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BvB,OAAA,CAACjB,MAAM;YACLgC,KAAK,EAAEkF,mBAAoB;YAC3BkD,QAAQ,EAAE/C,qBAAsB;YAChCzF,KAAK,EAAE;cAAEwK,KAAK,EAAE,MAAM;cAAEzJ,SAAS,EAAE;YAAE,CAAE;YACvCgK,WAAW,EAAC,oEAAa;YAAAjL,QAAA,EAExB0F,wBAAwB,CAAC6F,GAAG,CAAExF,IAAI,iBACjCxG,OAAA,CAACM,MAAM;cAAoBS,KAAK,EAAEyF,IAAI,CAACC,OAAQ;cAAAhG,QAAA,EAC5C+F,IAAI,CAACC,OAAO,CAACmG,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAGpG,IAAI,CAACC,OAAO,CAACoG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIC,IAAI,CAACtG,IAAI,CAACuG,UAAU,IAAIvG,IAAI,CAACwG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAMzG,IAAI,CAACC,OAAO,CAACyG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAIJ,IAAI,CAACtG,IAAI,CAACuG,UAAU,IAAIvG,IAAI,CAACwG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAAG,GAHjGzG,IAAI,CAACC,OAAO;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLwE,sBAAsB,CAACa,MAAM,GAAG,CAAC,iBAChC5G,OAAA;UAAAS,QAAA,gBACET,OAAA,CAACG,KAAK;YAAC8B,KAAK,EAAE,CAAE;YAAAxB,QAAA,GAAC,6BAAO,EAACsF,sBAAsB,CAAC,CAAC,CAAC,CAACY,UAAU;UAAA;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtEvB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEuF,sBAAsB,CAAC,CAAC;UAAE;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACoB,EAAA,CAzyBID,mBAA6B;EAAA,QAgC8C5C,cAAc;AAAA;AAAAqN,GAAA,GAhCzFzK,mBAA6B;AA2yBnC,eAAeA,mBAAmB;AAAC,IAAAN,EAAA,EAAA+K,GAAA;AAAAC,YAAA,CAAAhL,EAAA;AAAAgL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}