{"ast": null, "code": "import * as React from 'react';\nimport TableContext from '../context/TableContext';\nimport Summary from './Summary';\nimport SummaryContext from './SummaryContext';\nfunction Footer(_ref) {\n  var children = _ref.children,\n    stickyOffsets = _ref.stickyOffsets,\n    flattenColumns = _ref.flattenColumns;\n  var tableContext = React.useContext(TableContext);\n  var prefixCls = tableContext.prefixCls;\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: (scrollColumn === null || scrollColumn === void 0 ? void 0 : scrollColumn.scrollbar) ? lastColumnIndex : null\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default Footer;\nexport var FooterComponents = Summary;", "map": {"version": 3, "names": ["React", "TableContext", "Summary", "SummaryContext", "Footer", "_ref", "children", "stickyOffsets", "flattenColumns", "tableContext", "useContext", "prefixCls", "lastColumnIndex", "length", "scrollColumn", "summaryContext", "useMemo", "scrollColumnIndex", "scrollbar", "createElement", "Provider", "value", "className", "concat", "FooterComponents"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Footer/index.js"], "sourcesContent": ["import * as React from 'react';\nimport TableContext from '../context/TableContext';\nimport Summary from './Summary';\nimport SummaryContext from './SummaryContext';\n\nfunction Footer(_ref) {\n  var children = _ref.children,\n      stickyOffsets = _ref.stickyOffsets,\n      flattenColumns = _ref.flattenColumns;\n  var tableContext = React.useContext(TableContext);\n  var prefixCls = tableContext.prefixCls;\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: (scrollColumn === null || scrollColumn === void 0 ? void 0 : scrollColumn.scrollbar) ? lastColumnIndex : null\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\n\nexport default Footer;\nexport var FooterComponents = Summary;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACxC,IAAIC,YAAY,GAAGT,KAAK,CAACU,UAAU,CAACT,YAAY,CAAC;EACjD,IAAIU,SAAS,GAAGF,YAAY,CAACE,SAAS;EACtC,IAAIC,eAAe,GAAGJ,cAAc,CAACK,MAAM,GAAG,CAAC;EAC/C,IAAIC,YAAY,GAAGN,cAAc,CAACI,eAAe,CAAC;EAClD,IAAIG,cAAc,GAAGf,KAAK,CAACgB,OAAO,CAAC,YAAY;IAC7C,OAAO;MACLT,aAAa,EAAEA,aAAa;MAC5BC,cAAc,EAAEA,cAAc;MAC9BS,iBAAiB,EAAE,CAACH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACI,SAAS,IAAIN,eAAe,GAAG;IAC9H,CAAC;EACH,CAAC,EAAE,CAACE,YAAY,EAAEN,cAAc,EAAEI,eAAe,EAAEL,aAAa,CAAC,CAAC;EAClE,OAAO,aAAaP,KAAK,CAACmB,aAAa,CAAChB,cAAc,CAACiB,QAAQ,EAAE;IAC/DC,KAAK,EAAEN;EACT,CAAC,EAAE,aAAaf,KAAK,CAACmB,aAAa,CAAC,OAAO,EAAE;IAC3CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEL,QAAQ,CAAC,CAAC;AACf;AAEA,eAAeF,MAAM;AACrB,OAAO,IAAIoB,gBAAgB,GAAGtB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}