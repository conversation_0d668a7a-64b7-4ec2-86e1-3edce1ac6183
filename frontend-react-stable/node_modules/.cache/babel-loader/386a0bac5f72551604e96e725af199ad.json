{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\nvar CanceledError = require('../cancel/CanceledError');\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData.call(config, config.data, config.headers, config.transformRequest);\n\n  // Flatten headers\n  config.headers = utils.merge(config.headers.common || {}, config.headers[config.method] || {}, config.headers);\n  utils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch', 'common'], function cleanHeaderConfig(method) {\n    delete config.headers[method];\n  });\n  var adapter = config.adapter || defaults.adapter;\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(config, response.data, response.headers, config.transformResponse);\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(config, reason.response.data, reason.response.headers, config.transformResponse);\n      }\n    }\n    return Promise.reject(reason);\n  });\n};", "map": {"version": 3, "names": ["utils", "require", "transformData", "isCancel", "defaults", "CanceledError", "throwIfCancellationRequested", "config", "cancelToken", "throwIfRequested", "signal", "aborted", "module", "exports", "dispatchRequest", "headers", "data", "call", "transformRequest", "merge", "common", "method", "for<PERSON>ach", "cleanHeaderConfig", "adapter", "then", "onAdapterResolution", "response", "transformResponse", "onAdapterRejection", "reason", "Promise", "reject"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/core/dispatchRequest.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\nvar CanceledError = require('../cancel/CanceledError');\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.data,\n    config.headers,\n    config.transformRequest\n  );\n\n  // Flatten headers\n  config.headers = utils.merge(\n    config.headers.common || {},\n    config.headers[config.method] || {},\n    config.headers\n  );\n\n  utils.forEach(\n    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n    function cleanHeaderConfig(method) {\n      delete config.headers[method];\n    }\n  );\n\n  var adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      response.data,\n      response.headers,\n      config.transformResponse\n    );\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          reason.response.data,\n          reason.response.headers,\n          config.transformResponse\n        );\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC9C,IAAIE,QAAQ,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAC5C,IAAIG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;AACrC,IAAII,aAAa,GAAGJ,OAAO,CAAC,yBAAyB,CAAC;;AAEtD;AACA;AACA;AACA,SAASK,4BAA4BA,CAACC,MAAM,EAAE;EAC5C,IAAIA,MAAM,CAACC,WAAW,EAAE;IACtBD,MAAM,CAACC,WAAW,CAACC,gBAAgB,CAAC,CAAC;EACvC;EAEA,IAAIF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACG,MAAM,CAACC,OAAO,EAAE;IAC1C,MAAM,IAAIN,aAAa,CAAC,CAAC;EAC3B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAO,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAACP,MAAM,EAAE;EAChDD,4BAA4B,CAACC,MAAM,CAAC;;EAEpC;EACAA,MAAM,CAACQ,OAAO,GAAGR,MAAM,CAACQ,OAAO,IAAI,CAAC,CAAC;;EAErC;EACAR,MAAM,CAACS,IAAI,GAAGd,aAAa,CAACe,IAAI,CAC9BV,MAAM,EACNA,MAAM,CAACS,IAAI,EACXT,MAAM,CAACQ,OAAO,EACdR,MAAM,CAACW,gBACT,CAAC;;EAED;EACAX,MAAM,CAACQ,OAAO,GAAGf,KAAK,CAACmB,KAAK,CAC1BZ,MAAM,CAACQ,OAAO,CAACK,MAAM,IAAI,CAAC,CAAC,EAC3Bb,MAAM,CAACQ,OAAO,CAACR,MAAM,CAACc,MAAM,CAAC,IAAI,CAAC,CAAC,EACnCd,MAAM,CAACQ,OACT,CAAC;EAEDf,KAAK,CAACsB,OAAO,CACX,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,EAC3D,SAASC,iBAAiBA,CAACF,MAAM,EAAE;IACjC,OAAOd,MAAM,CAACQ,OAAO,CAACM,MAAM,CAAC;EAC/B,CACF,CAAC;EAED,IAAIG,OAAO,GAAGjB,MAAM,CAACiB,OAAO,IAAIpB,QAAQ,CAACoB,OAAO;EAEhD,OAAOA,OAAO,CAACjB,MAAM,CAAC,CAACkB,IAAI,CAAC,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;IACjErB,4BAA4B,CAACC,MAAM,CAAC;;IAEpC;IACAoB,QAAQ,CAACX,IAAI,GAAGd,aAAa,CAACe,IAAI,CAChCV,MAAM,EACNoB,QAAQ,CAACX,IAAI,EACbW,QAAQ,CAACZ,OAAO,EAChBR,MAAM,CAACqB,iBACT,CAAC;IAED,OAAOD,QAAQ;EACjB,CAAC,EAAE,SAASE,kBAAkBA,CAACC,MAAM,EAAE;IACrC,IAAI,CAAC3B,QAAQ,CAAC2B,MAAM,CAAC,EAAE;MACrBxB,4BAA4B,CAACC,MAAM,CAAC;;MAEpC;MACA,IAAIuB,MAAM,IAAIA,MAAM,CAACH,QAAQ,EAAE;QAC7BG,MAAM,CAACH,QAAQ,CAACX,IAAI,GAAGd,aAAa,CAACe,IAAI,CACvCV,MAAM,EACNuB,MAAM,CAACH,QAAQ,CAACX,IAAI,EACpBc,MAAM,CAACH,QAAQ,CAACZ,OAAO,EACvBR,MAAM,CAACqB,iBACT,CAAC;MACH;IACF;IAEA,OAAOG,OAAO,CAACC,MAAM,CAACF,MAAM,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}