{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport SearchOutlinedSvg from \"@ant-design/icons-svg/es/asn/SearchOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar SearchOutlined = function SearchOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: SearchOutlinedSvg\n  }));\n};\nSearchOutlined.displayName = 'SearchOutlined';\nexport default /*#__PURE__*/React.forwardRef(SearchOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "SearchOutlinedSvg", "AntdIcon", "SearchOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/SearchOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport SearchOutlinedSvg from \"@ant-design/icons-svg/es/asn/SearchOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar SearchOutlined = function SearchOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: SearchOutlinedSvg\n  }));\n};\nSearchOutlined.displayName = 'SearchOutlined';\nexport default /*#__PURE__*/React.forwardRef(SearchOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAC7C,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}