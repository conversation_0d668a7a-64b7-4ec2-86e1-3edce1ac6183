{"ast": null, "code": "import _get from \"lodash/get\";\nimport _isFunction from \"lodash/isFunction\";\nimport _sumBy from \"lodash/sumBy\";\nimport _min from \"lodash/min\";\nimport _maxBy from \"lodash/maxBy\";\nvar _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\"],\n  _excluded2 = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @file TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterSvgElements, validateWidthHeight, findChildByType, filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nvar defaultCoordinateOfTooltip = {\n  x: 0,\n  y: 0\n};\nvar interpolationGenerator = function interpolationGenerator(a, b) {\n  var ka = +a;\n  var kb = b - ka;\n  return function (t) {\n    return ka + kb * t;\n  };\n};\nvar centerY = function centerY(node) {\n  return node.y + node.dy / 2;\n};\nvar getValue = function getValue(entry) {\n  return entry && entry.value || 0;\n};\nvar getSumOfIds = function getSumOfIds(links, ids) {\n  return ids.reduce(function (result, id) {\n    return result + getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedSource = function getSumWithWeightedSource(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var sourceNode = tree[link.source];\n    return result + centerY(sourceNode) * getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedTarget = function getSumWithWeightedTarget(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var targetNode = tree[link.target];\n    return result + centerY(targetNode) * getValue(links[id]);\n  }, 0);\n};\nvar ascendingY = function ascendingY(a, b) {\n  return a.y - b.y;\n};\nvar searchTargetsAndSources = function searchTargetsAndSources(links, id) {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes: sourceNodes,\n    sourceLinks: sourceLinks,\n    targetLinks: targetLinks,\n    targetNodes: targetNodes\n  };\n};\nvar updateDepthOfTargets = function updateDepthOfTargets(tree, curNode) {\n  var targetNodes = curNode.targetNodes;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = function getNodesTree(_ref, width, nodeWidth) {\n  var nodes = _ref.nodes,\n    links = _ref.links;\n  var tree = nodes.map(function (entry, index) {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = _maxBy(tree, function (entry) {\n    return entry.depth;\n  }).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree: tree,\n    maxDepth: maxDepth\n  };\n};\nvar getDepthTree = function getDepthTree(tree) {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = function updateYOfTree(depthTree, height, nodePadding, links) {\n  var yRatio = _min(depthTree.map(function (nodes) {\n    return (height - (nodes.length - 1) * nodePadding) / _sumBy(nodes, getValue);\n  }));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(function (link) {\n    return _objectSpread(_objectSpread({}, link), {}, {\n      dy: getValue(link) * yRatio\n    });\n  });\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    nodes.sort(ascendingY);\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = function relaxLeftToRight(tree, depthTree, links, alpha) {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = function relaxRightToLeft(tree, depthTree, links, alpha) {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = function updateYOfLinks(tree, links) {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort(function (a, b) {\n      return tree[links[a].target].y - tree[links[b].target].y;\n    });\n    node.sourceLinks.sort(function (a, b) {\n      return tree[links[a].source].y - tree[links[b].source].y;\n    });\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = function computeData(_ref2) {\n  var data = _ref2.data,\n    width = _ref2.width,\n    height = _ref2.height,\n    iterations = _ref2.iterations,\n    nodeWidth = _ref2.nodeWidth,\n    nodePadding = _ref2.nodePadding;\n  var links = data.links;\n  var _getNodesTree = getNodesTree(data, width, nodeWidth),\n    tree = _getNodesTree.tree;\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = function getCoordinateOfTooltip(el, type) {\n  if (type === 'node') {\n    return {\n      x: el.x + el.width / 2,\n      y: el.y + el.height / 2\n    };\n  }\n  return {\n    x: (el.sourceX + el.targetX) / 2,\n    y: (el.sourceY + el.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = function getPayloadOfTooltip(el, type, nameKey) {\n  var payload = el.payload;\n  if (type === 'node') {\n    return [{\n      payload: el,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  if (payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return [{\n      payload: el,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  return [];\n};\nexport var Sankey = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Sankey, _PureComponent);\n  var _super = _createSuper(Sankey);\n  function Sankey() {\n    var _this;\n    _classCallCheck(this, Sankey);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key = 0; _key < _len2; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      activeElement: null,\n      activeElementType: null,\n      isTooltipActive: false,\n      nodes: [],\n      links: []\n    });\n    return _this;\n  }\n  _createClass(Sankey, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(el, type, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(el, type, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(el, type, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(el, type, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(el, type, e) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        children = _this$props3.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem && tooltipItem.props.trigger === 'click') {\n        if (this.state.isTooltipActive) {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          });\n        } else {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          });\n        }\n      }\n      if (onClick) onClick(el, type, e);\n    }\n  }, {\n    key: \"renderLinks\",\n    value: function renderLinks(links, nodes) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        linkCurvature = _this$props4.linkCurvature,\n        linkContent = _this$props4.link,\n        margin = _this$props4.margin;\n      var top = _get(margin, 'top') || 0;\n      var left = _get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-links\",\n        key: \"recharts-sankey-links\"\n      }, links.map(function (link, i) {\n        var sourceRelativeY = link.sy,\n          targetRelativeY = link.ty,\n          linkWidth = link.dy;\n        var source = nodes[link.source];\n        var target = nodes[link.target];\n        var sourceX = source.x + source.dx + left;\n        var targetX = target.x + left;\n        var interpolationFunc = interpolationGenerator(sourceX, targetX);\n        var sourceControlX = interpolationFunc(linkCurvature);\n        var targetControlX = interpolationFunc(1 - linkCurvature);\n        var sourceY = source.y + sourceRelativeY + linkWidth / 2 + top;\n        var targetY = target.y + targetRelativeY + linkWidth / 2 + top;\n        var linkProps = _objectSpread({\n          sourceX: sourceX,\n          targetX: targetX,\n          sourceY: sourceY,\n          targetY: targetY,\n          sourceControlX: sourceControlX,\n          targetControlX: targetControlX,\n          sourceRelativeY: sourceRelativeY,\n          targetRelativeY: targetRelativeY,\n          linkWidth: linkWidth,\n          index: i,\n          payload: _objectSpread(_objectSpread({}, link), {}, {\n            source: source,\n            target: target\n          })\n        }, filterProps(linkContent));\n        var events = {\n          onMouseEnter: _this2.handleMouseEnter.bind(_this2, linkProps, 'link'),\n          onMouseLeave: _this2.handleMouseLeave.bind(_this2, linkProps, 'link'),\n          onClick: _this2.handleClick.bind(_this2, linkProps, 'link')\n        };\n        return (/*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, _extends({\n            key: \"link\".concat(i)\n          }, events), _this2.constructor.renderLinkItem(linkContent, linkProps))\n        );\n      }));\n    }\n  }, {\n    key: \"renderNodes\",\n    value: function renderNodes(nodes) {\n      var _this3 = this;\n      var _this$props5 = this.props,\n        nodeContent = _this$props5.node,\n        margin = _this$props5.margin;\n      var top = _get(margin, 'top') || 0;\n      var left = _get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-nodes\",\n        key: \"recharts-sankey-nodes\"\n      }, nodes.map(function (node, i) {\n        var x = node.x,\n          y = node.y,\n          dx = node.dx,\n          dy = node.dy;\n        var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent)), {}, {\n          x: x + left,\n          y: y + top,\n          width: dx,\n          height: dy,\n          index: i,\n          payload: node\n        });\n        var events = {\n          onMouseEnter: _this3.handleMouseEnter.bind(_this3, nodeProps, 'node'),\n          onMouseLeave: _this3.handleMouseLeave.bind(_this3, nodeProps, 'node'),\n          onClick: _this3.handleClick.bind(_this3, nodeProps, 'node')\n        };\n        return (/*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, _extends({\n            key: \"node\".concat(i)\n          }, events), _this3.constructor.renderNodeItem(nodeContent, nodeProps))\n        );\n      }));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props6 = this.props,\n        children = _this$props6.children,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        nameKey = _this$props6.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeElement = _this$state.activeElement,\n        activeElementType = _this$state.activeElementType;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeElement ? getCoordinateOfTooltip(activeElement, activeElementType) : defaultCoordinateOfTooltip;\n      var payload = activeElement ? getPayloadOfTooltip(activeElement, activeElementType, nameKey) : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props7 = this.props,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        className = _this$props7.className,\n        style = _this$props7.style,\n        children = _this$props7.children,\n        others = _objectWithoutProperties(_this$props7, _excluded);\n      var _this$state2 = this.state,\n        links = _this$state2.links,\n        nodes = _this$state2.nodes;\n      var attrs = filterProps(others);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: height\n      }), filterSvgElements(children), this.renderLinks(links, nodes), this.renderNodes(nodes)), this.renderTooltip());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        height = nextProps.height,\n        margin = nextProps.margin,\n        iterations = nextProps.iterations,\n        nodeWidth = nextProps.nodeWidth,\n        nodePadding = nextProps.nodePadding;\n      if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding) {\n        var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n        var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n        var _computeData = computeData({\n            data: data,\n            width: contentWidth,\n            height: contentHeight,\n            iterations: iterations,\n            nodeWidth: nodeWidth,\n            nodePadding: nodePadding\n          }),\n          links = _computeData.links,\n          nodes = _computeData.nodes;\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          nodes: nodes,\n          links: links,\n          prevData: data,\n          prevWidth: iterations,\n          prevHeight: height,\n          prevMargin: margin,\n          prevNodePadding: nodePadding,\n          prevNodeWidth: nodeWidth,\n          prevIterations: iterations\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderLinkItem\",\n    value: function renderLinkItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      var sourceX = props.sourceX,\n        sourceY = props.sourceY,\n        sourceControlX = props.sourceControlX,\n        targetX = props.targetX,\n        targetY = props.targetY,\n        targetControlX = props.targetControlX,\n        linkWidth = props.linkWidth,\n        others = _objectWithoutProperties(props, _excluded2);\n      return /*#__PURE__*/React.createElement(\"path\", _extends({\n        className: \"recharts-sankey-link\",\n        d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n        fill: \"none\",\n        stroke: \"#333\",\n        strokeWidth: linkWidth,\n        strokeOpacity: \"0.2\"\n      }, filterProps(others)));\n    }\n  }, {\n    key: \"renderNodeItem\",\n    value: function renderNodeItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Rectangle, _extends({\n        className: \"recharts-sankey-node\",\n        fill: \"#0088fe\",\n        fillOpacity: \"0.8\"\n      }, filterProps(props), {\n        role: \"img\"\n      }));\n    }\n  }]);\n  return Sankey;\n}(PureComponent);\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  }\n});", "map": {"version": 3, "names": ["_get", "_isFunction", "_sumBy", "_min", "_maxBy", "_excluded", "_excluded2", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "_extends", "assign", "bind", "arguments", "hasOwnProperty", "apply", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "classNames", "Surface", "Layer", "<PERSON><PERSON><PERSON>", "Rectangle", "shallowEqual", "filterSvgElements", "validateWidthHeight", "findChildByType", "filterProps", "getValueByDataKey", "defaultCoordinateOfTooltip", "x", "y", "interpolationGenerator", "a", "b", "ka", "kb", "t", "centerY", "node", "dy", "getValue", "entry", "getSumOfIds", "links", "ids", "reduce", "id", "getSumWithWeightedSource", "tree", "link", "sourceNode", "getSumWithWeightedTarget", "targetNode", "ascendingY", "searchTargetsAndSources", "sourceNodes", "sourceLinks", "targetNodes", "targetLinks", "len", "updateDepthOfTargets", "curNode", "depth", "Math", "max", "getNodesTree", "_ref", "width", "nodeWidth", "nodes", "map", "index", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_i", "_len", "_node", "dx", "getDepth<PERSON>ree", "updateYOfTree", "depthTree", "height", "nodePadding", "yRatio", "d", "resolveCollisions", "n", "sort", "y0", "j", "_j", "_node2", "_dy", "relaxLeftToRight", "alpha", "sourceSum", "weightedSum", "relaxRightToLeft", "targetSum", "updateYOfLinks", "sy", "ty", "tLen", "_j2", "sLen", "_link", "computeData", "_ref2", "data", "iterations", "_getNodesTree", "newLinks", "getCoordinateOfTooltip", "el", "type", "sourceX", "targetX", "sourceY", "targetY", "getPayloadOfTooltip", "<PERSON><PERSON><PERSON>", "payload", "name", "sourceName", "targetName", "concat", "<PERSON><PERSON>", "_PureComponent", "_super", "_this", "_len2", "args", "Array", "_key", "activeElement", "activeElementType", "isTooltipActive", "handleMouseEnter", "_this$props", "onMouseEnter", "children", "tooltipItem", "setState", "prev", "trigger", "handleMouseLeave", "_this$props2", "onMouseLeave", "handleClick", "_this$props3", "onClick", "state", "renderLinks", "_this2", "_this$props4", "linkCurvature", "linkContent", "margin", "top", "left", "createElement", "className", "sourceRelativeY", "targetRelativeY", "linkWidth", "interpolationFunc", "sourceControlX", "targetControlX", "linkProps", "events", "renderLinkItem", "renderNodes", "_this3", "_this$props5", "nodeContent", "nodeProps", "renderNodeItem", "renderTooltip", "_this$props6", "_this$state", "viewBox", "coordinate", "cloneElement", "active", "label", "render", "_this$props7", "style", "others", "_this$state2", "attrs", "position", "cursor", "role", "getDerivedStateFromProps", "nextProps", "prevState", "prevData", "prevWidth", "prevHeight", "<PERSON>v<PERSON><PERSON><PERSON>", "prevIterations", "prevNodeWidth", "prevNodePadding", "contentWidth", "right", "contentHeight", "bottom", "_computeData", "option", "isValidElement", "fill", "stroke", "strokeWidth", "strokeOpacity", "fillOpacity", "dataKey"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/chart/Sankey.js"], "sourcesContent": ["import _get from \"lodash/get\";\nimport _isFunction from \"lodash/isFunction\";\nimport _sumBy from \"lodash/sumBy\";\nimport _min from \"lodash/min\";\nimport _maxBy from \"lodash/maxBy\";\nvar _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\"],\n  _excluded2 = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @file TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterSvgElements, validateWidthHeight, findChildByType, filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nvar defaultCoordinateOfTooltip = {\n  x: 0,\n  y: 0\n};\nvar interpolationGenerator = function interpolationGenerator(a, b) {\n  var ka = +a;\n  var kb = b - ka;\n  return function (t) {\n    return ka + kb * t;\n  };\n};\nvar centerY = function centerY(node) {\n  return node.y + node.dy / 2;\n};\nvar getValue = function getValue(entry) {\n  return entry && entry.value || 0;\n};\nvar getSumOfIds = function getSumOfIds(links, ids) {\n  return ids.reduce(function (result, id) {\n    return result + getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedSource = function getSumWithWeightedSource(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var sourceNode = tree[link.source];\n    return result + centerY(sourceNode) * getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedTarget = function getSumWithWeightedTarget(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var targetNode = tree[link.target];\n    return result + centerY(targetNode) * getValue(links[id]);\n  }, 0);\n};\nvar ascendingY = function ascendingY(a, b) {\n  return a.y - b.y;\n};\nvar searchTargetsAndSources = function searchTargetsAndSources(links, id) {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes: sourceNodes,\n    sourceLinks: sourceLinks,\n    targetLinks: targetLinks,\n    targetNodes: targetNodes\n  };\n};\nvar updateDepthOfTargets = function updateDepthOfTargets(tree, curNode) {\n  var targetNodes = curNode.targetNodes;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = function getNodesTree(_ref, width, nodeWidth) {\n  var nodes = _ref.nodes,\n    links = _ref.links;\n  var tree = nodes.map(function (entry, index) {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = _maxBy(tree, function (entry) {\n    return entry.depth;\n  }).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree: tree,\n    maxDepth: maxDepth\n  };\n};\nvar getDepthTree = function getDepthTree(tree) {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = function updateYOfTree(depthTree, height, nodePadding, links) {\n  var yRatio = _min(depthTree.map(function (nodes) {\n    return (height - (nodes.length - 1) * nodePadding) / _sumBy(nodes, getValue);\n  }));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(function (link) {\n    return _objectSpread(_objectSpread({}, link), {}, {\n      dy: getValue(link) * yRatio\n    });\n  });\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    nodes.sort(ascendingY);\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = function relaxLeftToRight(tree, depthTree, links, alpha) {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = function relaxRightToLeft(tree, depthTree, links, alpha) {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = function updateYOfLinks(tree, links) {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort(function (a, b) {\n      return tree[links[a].target].y - tree[links[b].target].y;\n    });\n    node.sourceLinks.sort(function (a, b) {\n      return tree[links[a].source].y - tree[links[b].source].y;\n    });\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = function computeData(_ref2) {\n  var data = _ref2.data,\n    width = _ref2.width,\n    height = _ref2.height,\n    iterations = _ref2.iterations,\n    nodeWidth = _ref2.nodeWidth,\n    nodePadding = _ref2.nodePadding;\n  var links = data.links;\n  var _getNodesTree = getNodesTree(data, width, nodeWidth),\n    tree = _getNodesTree.tree;\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = function getCoordinateOfTooltip(el, type) {\n  if (type === 'node') {\n    return {\n      x: el.x + el.width / 2,\n      y: el.y + el.height / 2\n    };\n  }\n  return {\n    x: (el.sourceX + el.targetX) / 2,\n    y: (el.sourceY + el.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = function getPayloadOfTooltip(el, type, nameKey) {\n  var payload = el.payload;\n  if (type === 'node') {\n    return [{\n      payload: el,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  if (payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return [{\n      payload: el,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  return [];\n};\nexport var Sankey = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Sankey, _PureComponent);\n  var _super = _createSuper(Sankey);\n  function Sankey() {\n    var _this;\n    _classCallCheck(this, Sankey);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key = 0; _key < _len2; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      activeElement: null,\n      activeElementType: null,\n      isTooltipActive: false,\n      nodes: [],\n      links: []\n    });\n    return _this;\n  }\n  _createClass(Sankey, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(el, type, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(el, type, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(el, type, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(el, type, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(el, type, e) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        children = _this$props3.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem && tooltipItem.props.trigger === 'click') {\n        if (this.state.isTooltipActive) {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          });\n        } else {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          });\n        }\n      }\n      if (onClick) onClick(el, type, e);\n    }\n  }, {\n    key: \"renderLinks\",\n    value: function renderLinks(links, nodes) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        linkCurvature = _this$props4.linkCurvature,\n        linkContent = _this$props4.link,\n        margin = _this$props4.margin;\n      var top = _get(margin, 'top') || 0;\n      var left = _get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-links\",\n        key: \"recharts-sankey-links\"\n      }, links.map(function (link, i) {\n        var sourceRelativeY = link.sy,\n          targetRelativeY = link.ty,\n          linkWidth = link.dy;\n        var source = nodes[link.source];\n        var target = nodes[link.target];\n        var sourceX = source.x + source.dx + left;\n        var targetX = target.x + left;\n        var interpolationFunc = interpolationGenerator(sourceX, targetX);\n        var sourceControlX = interpolationFunc(linkCurvature);\n        var targetControlX = interpolationFunc(1 - linkCurvature);\n        var sourceY = source.y + sourceRelativeY + linkWidth / 2 + top;\n        var targetY = target.y + targetRelativeY + linkWidth / 2 + top;\n        var linkProps = _objectSpread({\n          sourceX: sourceX,\n          targetX: targetX,\n          sourceY: sourceY,\n          targetY: targetY,\n          sourceControlX: sourceControlX,\n          targetControlX: targetControlX,\n          sourceRelativeY: sourceRelativeY,\n          targetRelativeY: targetRelativeY,\n          linkWidth: linkWidth,\n          index: i,\n          payload: _objectSpread(_objectSpread({}, link), {}, {\n            source: source,\n            target: target\n          })\n        }, filterProps(linkContent));\n        var events = {\n          onMouseEnter: _this2.handleMouseEnter.bind(_this2, linkProps, 'link'),\n          onMouseLeave: _this2.handleMouseLeave.bind(_this2, linkProps, 'link'),\n          onClick: _this2.handleClick.bind(_this2, linkProps, 'link')\n        };\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, _extends({\n            key: \"link\".concat(i)\n          }, events), _this2.constructor.renderLinkItem(linkContent, linkProps))\n        );\n      }));\n    }\n  }, {\n    key: \"renderNodes\",\n    value: function renderNodes(nodes) {\n      var _this3 = this;\n      var _this$props5 = this.props,\n        nodeContent = _this$props5.node,\n        margin = _this$props5.margin;\n      var top = _get(margin, 'top') || 0;\n      var left = _get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-nodes\",\n        key: \"recharts-sankey-nodes\"\n      }, nodes.map(function (node, i) {\n        var x = node.x,\n          y = node.y,\n          dx = node.dx,\n          dy = node.dy;\n        var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent)), {}, {\n          x: x + left,\n          y: y + top,\n          width: dx,\n          height: dy,\n          index: i,\n          payload: node\n        });\n        var events = {\n          onMouseEnter: _this3.handleMouseEnter.bind(_this3, nodeProps, 'node'),\n          onMouseLeave: _this3.handleMouseLeave.bind(_this3, nodeProps, 'node'),\n          onClick: _this3.handleClick.bind(_this3, nodeProps, 'node')\n        };\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(Layer, _extends({\n            key: \"node\".concat(i)\n          }, events), _this3.constructor.renderNodeItem(nodeContent, nodeProps))\n        );\n      }));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props6 = this.props,\n        children = _this$props6.children,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        nameKey = _this$props6.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeElement = _this$state.activeElement,\n        activeElementType = _this$state.activeElementType;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeElement ? getCoordinateOfTooltip(activeElement, activeElementType) : defaultCoordinateOfTooltip;\n      var payload = activeElement ? getPayloadOfTooltip(activeElement, activeElementType, nameKey) : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props7 = this.props,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        className = _this$props7.className,\n        style = _this$props7.style,\n        children = _this$props7.children,\n        others = _objectWithoutProperties(_this$props7, _excluded);\n      var _this$state2 = this.state,\n        links = _this$state2.links,\n        nodes = _this$state2.nodes;\n      var attrs = filterProps(others);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: height\n      }), filterSvgElements(children), this.renderLinks(links, nodes), this.renderNodes(nodes)), this.renderTooltip());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        height = nextProps.height,\n        margin = nextProps.margin,\n        iterations = nextProps.iterations,\n        nodeWidth = nextProps.nodeWidth,\n        nodePadding = nextProps.nodePadding;\n      if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding) {\n        var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n        var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n        var _computeData = computeData({\n            data: data,\n            width: contentWidth,\n            height: contentHeight,\n            iterations: iterations,\n            nodeWidth: nodeWidth,\n            nodePadding: nodePadding\n          }),\n          links = _computeData.links,\n          nodes = _computeData.nodes;\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          nodes: nodes,\n          links: links,\n          prevData: data,\n          prevWidth: iterations,\n          prevHeight: height,\n          prevMargin: margin,\n          prevNodePadding: nodePadding,\n          prevNodeWidth: nodeWidth,\n          prevIterations: iterations\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderLinkItem\",\n    value: function renderLinkItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      var sourceX = props.sourceX,\n        sourceY = props.sourceY,\n        sourceControlX = props.sourceControlX,\n        targetX = props.targetX,\n        targetY = props.targetY,\n        targetControlX = props.targetControlX,\n        linkWidth = props.linkWidth,\n        others = _objectWithoutProperties(props, _excluded2);\n      return /*#__PURE__*/React.createElement(\"path\", _extends({\n        className: \"recharts-sankey-link\",\n        d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n        fill: \"none\",\n        stroke: \"#333\",\n        strokeWidth: linkWidth,\n        strokeOpacity: \"0.2\"\n      }, filterProps(others)));\n    }\n  }, {\n    key: \"renderNodeItem\",\n    value: function renderNodeItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (_isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Rectangle, _extends({\n        className: \"recharts-sankey-node\",\n        fill: \"#0088fe\",\n        fillOpacity: \"0.8\"\n      }, filterProps(props), {\n        role: \"img\"\n      }));\n    }\n  }]);\n  return Sankey;\n}(PureComponent);\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  }\n});"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC;EACnEC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAC5G,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT,SAASa,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUf,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGkB,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACqB,cAAc,CAACP,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOa,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AAAE;AAClV,SAASG,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACvB,MAAM,EAAEwB,KAAK,EAAE;EAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,KAAK,CAACjB,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIsB,UAAU,GAAGD,KAAK,CAACrB,CAAC,CAAC;IAAEsB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAExB,MAAM,CAACyB,cAAc,CAAC7B,MAAM,EAAE8B,cAAc,CAACL,UAAU,CAACvB,GAAG,CAAC,EAAEuB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACF,WAAW,CAACzB,SAAS,EAAEoC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACF,WAAW,EAAEY,WAAW,CAAC;EAAE7B,MAAM,CAACyB,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASa,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAId,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEa,QAAQ,CAACvC,SAAS,GAAGQ,MAAM,CAACiC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxC,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE2C,KAAK,EAAEH,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEvB,MAAM,CAACyB,cAAc,CAACM,QAAQ,EAAE,WAAW,EAAE;IAAEP,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIQ,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGnC,MAAM,CAACsC,cAAc,GAAGtC,MAAM,CAACsC,cAAc,CAAC3B,IAAI,CAAC,CAAC,GAAG,SAASwB,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACvD,WAAW;MAAEwD,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEjC,SAAS,EAAEoC,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC/B,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAAE;IAAE,OAAOuC,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE9C,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIY,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACjE,SAAS,CAACkE,OAAO,CAACpD,IAAI,CAAC2C,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG9C,MAAM,CAACsC,cAAc,GAAGtC,MAAM,CAAC4D,cAAc,CAACjD,IAAI,CAAC,CAAC,GAAG,SAASmC,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAIvC,MAAM,CAAC4D,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASyB,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIvD,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACsD,MAAM,CAAC;EAAE,IAAI9D,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAI+D,OAAO,GAAGhE,MAAM,CAACC,qBAAqB,CAAC6D,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOlE,MAAM,CAACmE,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAAC5C,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEd,IAAI,CAAC4D,IAAI,CAACtD,KAAK,CAACN,IAAI,EAAEwD,OAAO,CAAC;EAAE;EAAE,OAAOxD,IAAI;AAAE;AACpV,SAAS6D,aAAaA,CAACzE,MAAM,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIL,MAAM,GAAG,IAAI,IAAIkB,SAAS,CAACb,CAAC,CAAC,GAAGa,SAAS,CAACb,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG8D,OAAO,CAAC7D,MAAM,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC4E,OAAO,CAAC,UAAUxE,GAAG,EAAE;MAAEyE,eAAe,CAAC3E,MAAM,EAAEE,GAAG,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACwE,yBAAyB,GAAGxE,MAAM,CAACyE,gBAAgB,CAAC7E,MAAM,EAAEI,MAAM,CAACwE,yBAAyB,CAAC9E,MAAM,CAAC,CAAC,GAAGmE,OAAO,CAAC7D,MAAM,CAACN,MAAM,CAAC,CAAC,CAAC4E,OAAO,CAAC,UAAUxE,GAAG,EAAE;MAAEE,MAAM,CAACyB,cAAc,CAAC7B,MAAM,EAAEE,GAAG,EAAEE,MAAM,CAACmE,wBAAwB,CAACzE,MAAM,EAAEI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AACzf,SAAS2E,eAAeA,CAACnF,GAAG,EAAEU,GAAG,EAAEoC,KAAK,EAAE;EAAEpC,GAAG,GAAG4B,cAAc,CAAC5B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIV,GAAG,EAAE;IAAEY,MAAM,CAACyB,cAAc,CAACrC,GAAG,EAAEU,GAAG,EAAE;MAAEoC,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEpC,GAAG,CAACU,GAAG,CAAC,GAAGoC,KAAK;EAAE;EAAE,OAAO9C,GAAG;AAAE;AAC3O,SAASsC,cAAcA,CAACgD,GAAG,EAAE;EAAE,IAAI5E,GAAG,GAAG6E,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOvF,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAG8E,MAAM,CAAC9E,GAAG,CAAC;AAAE;AAC5H,SAAS6E,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI3F,OAAO,CAAC0F,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACxF,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACzE,IAAI,CAACuE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI3F,OAAO,CAAC+F,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIhE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC4D,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACzG,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,IAAIC,0BAA0B,GAAG;EAC/BC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjE,IAAIC,EAAE,GAAG,CAACF,CAAC;EACX,IAAIG,EAAE,GAAGF,CAAC,GAAGC,EAAE;EACf,OAAO,UAAUE,CAAC,EAAE;IAClB,OAAOF,EAAE,GAAGC,EAAE,GAAGC,CAAC;EACpB,CAAC;AACH,CAAC;AACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnC,OAAOA,IAAI,CAACR,CAAC,GAAGQ,IAAI,CAACC,EAAE,GAAG,CAAC;AAC7B,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOA,KAAK,IAAIA,KAAK,CAAC5E,KAAK,IAAI,CAAC;AAClC,CAAC;AACD,IAAI6E,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUnE,MAAM,EAAEoE,EAAE,EAAE;IACtC,OAAOpE,MAAM,GAAG8D,QAAQ,CAACG,KAAK,CAACG,EAAE,CAAC,CAAC;EACrC,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAEL,KAAK,EAAEC,GAAG,EAAE;EACjF,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUnE,MAAM,EAAEoE,EAAE,EAAE;IACtC,IAAIG,IAAI,GAAGN,KAAK,CAACG,EAAE,CAAC;IACpB,IAAII,UAAU,GAAGF,IAAI,CAACC,IAAI,CAAC5H,MAAM,CAAC;IAClC,OAAOqD,MAAM,GAAG2D,OAAO,CAACa,UAAU,CAAC,GAAGV,QAAQ,CAACG,KAAK,CAACG,EAAE,CAAC,CAAC;EAC3D,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIK,wBAAwB,GAAG,SAASA,wBAAwBA,CAACH,IAAI,EAAEL,KAAK,EAAEC,GAAG,EAAE;EACjF,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUnE,MAAM,EAAEoE,EAAE,EAAE;IACtC,IAAIG,IAAI,GAAGN,KAAK,CAACG,EAAE,CAAC;IACpB,IAAIM,UAAU,GAAGJ,IAAI,CAACC,IAAI,CAAC1H,MAAM,CAAC;IAClC,OAAOmD,MAAM,GAAG2D,OAAO,CAACe,UAAU,CAAC,GAAGZ,QAAQ,CAACG,KAAK,CAACG,EAAE,CAAC,CAAC;EAC3D,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIO,UAAU,GAAG,SAASA,UAAUA,CAACrB,CAAC,EAAEC,CAAC,EAAE;EACzC,OAAOD,CAAC,CAACF,CAAC,GAAGG,CAAC,CAACH,CAAC;AAClB,CAAC;AACD,IAAIwB,uBAAuB,GAAG,SAASA,uBAAuBA,CAACX,KAAK,EAAEG,EAAE,EAAE;EACxE,IAAIS,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,KAAK,IAAIhI,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGhB,KAAK,CAAC7G,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IAChD,IAAIuH,IAAI,GAAGN,KAAK,CAACjH,CAAC,CAAC;IACnB,IAAIuH,IAAI,CAAC5H,MAAM,KAAKyH,EAAE,EAAE;MACtBW,WAAW,CAAC1D,IAAI,CAACkD,IAAI,CAAC1H,MAAM,CAAC;MAC7BmI,WAAW,CAAC3D,IAAI,CAACrE,CAAC,CAAC;IACrB;IACA,IAAIuH,IAAI,CAAC1H,MAAM,KAAKuH,EAAE,EAAE;MACtBS,WAAW,CAACxD,IAAI,CAACkD,IAAI,CAAC5H,MAAM,CAAC;MAC7BmI,WAAW,CAACzD,IAAI,CAACrE,CAAC,CAAC;IACrB;EACF;EACA,OAAO;IACL6H,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA,WAAW;IACxBE,WAAW,EAAEA,WAAW;IACxBD,WAAW,EAAEA;EACf,CAAC;AACH,CAAC;AACD,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACZ,IAAI,EAAEa,OAAO,EAAE;EACtE,IAAIJ,WAAW,GAAGI,OAAO,CAACJ,WAAW;EACrC,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGF,WAAW,CAAC3H,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IACtD,IAAIH,MAAM,GAAGyH,IAAI,CAACS,WAAW,CAAC/H,CAAC,CAAC,CAAC;IACjC,IAAIH,MAAM,EAAE;MACVA,MAAM,CAACuI,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACC,KAAK,GAAG,CAAC,EAAEvI,MAAM,CAACuI,KAAK,CAAC;MACxDF,oBAAoB,CAACZ,IAAI,EAAEzH,MAAM,CAAC;IACpC;EACF;AACF,CAAC;AACD,IAAI0I,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/D,IAAIC,KAAK,GAAGH,IAAI,CAACG,KAAK;IACpB1B,KAAK,GAAGuB,IAAI,CAACvB,KAAK;EACpB,IAAIK,IAAI,GAAGqB,KAAK,CAACC,GAAG,CAAC,UAAU7B,KAAK,EAAE8B,KAAK,EAAE;IAC3C,IAAI7F,MAAM,GAAG4E,uBAAuB,CAACX,KAAK,EAAE4B,KAAK,CAAC;IAClD,OAAOvE,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,KAAK,CAAC,EAAE/D,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACxEb,KAAK,EAAEkG,IAAI,CAACC,GAAG,CAACtB,WAAW,CAACC,KAAK,EAAEjE,MAAM,CAAC8E,WAAW,CAAC,EAAEd,WAAW,CAACC,KAAK,EAAEjE,MAAM,CAACgF,WAAW,CAAC,CAAC;MAC/FI,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,KAAK,IAAIpI,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGX,IAAI,CAAClH,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IAC/C,IAAI4G,IAAI,GAAGU,IAAI,CAACtH,CAAC,CAAC;IAClB,IAAI,CAAC4G,IAAI,CAACiB,WAAW,CAACzH,MAAM,EAAE;MAC5B8H,oBAAoB,CAACZ,IAAI,EAAEV,IAAI,CAAC;IAClC;EACF;EACA,IAAIkC,QAAQ,GAAG7J,MAAM,CAACqI,IAAI,EAAE,UAAUP,KAAK,EAAE;IAC3C,OAAOA,KAAK,CAACqB,KAAK;EACpB,CAAC,CAAC,CAACA,KAAK;EACR,IAAIU,QAAQ,IAAI,CAAC,EAAE;IACjB,IAAIC,UAAU,GAAG,CAACN,KAAK,GAAGC,SAAS,IAAII,QAAQ;IAC/C,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,IAAI,GAAG3B,IAAI,CAAClH,MAAM,EAAE4I,EAAE,GAAGC,IAAI,EAAED,EAAE,EAAE,EAAE;MACpD,IAAIE,KAAK,GAAG5B,IAAI,CAAC0B,EAAE,CAAC;MACpB,IAAI,CAACE,KAAK,CAACnB,WAAW,CAAC3H,MAAM,EAAE;QAC7B8I,KAAK,CAACd,KAAK,GAAGU,QAAQ;MACxB;MACAI,KAAK,CAAC/C,CAAC,GAAG+C,KAAK,CAACd,KAAK,GAAGW,UAAU;MAClCG,KAAK,CAACC,EAAE,GAAGT,SAAS;IACtB;EACF;EACA,OAAO;IACLpB,IAAI,EAAEA,IAAI;IACVwB,QAAQ,EAAEA;EACZ,CAAC;AACH,CAAC;AACD,IAAIM,YAAY,GAAG,SAASA,YAAYA,CAAC9B,IAAI,EAAE;EAC7C,IAAItE,MAAM,GAAG,EAAE;EACf,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGX,IAAI,CAAClH,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IAC/C,IAAI4G,IAAI,GAAGU,IAAI,CAACtH,CAAC,CAAC;IAClB,IAAI,CAACgD,MAAM,CAAC4D,IAAI,CAACwB,KAAK,CAAC,EAAE;MACvBpF,MAAM,CAAC4D,IAAI,CAACwB,KAAK,CAAC,GAAG,EAAE;IACzB;IACApF,MAAM,CAAC4D,IAAI,CAACwB,KAAK,CAAC,CAAC/D,IAAI,CAACuC,IAAI,CAAC;EAC/B;EACA,OAAO5D,MAAM;AACf,CAAC;AACD,IAAIqG,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEvC,KAAK,EAAE;EAChF,IAAIwC,MAAM,GAAGzK,IAAI,CAACsK,SAAS,CAACV,GAAG,CAAC,UAAUD,KAAK,EAAE;IAC/C,OAAO,CAACY,MAAM,GAAG,CAACZ,KAAK,CAACvI,MAAM,GAAG,CAAC,IAAIoJ,WAAW,IAAIzK,MAAM,CAAC4J,KAAK,EAAE7B,QAAQ,CAAC;EAC9E,CAAC,CAAC,CAAC;EACH,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEZ,QAAQ,GAAGQ,SAAS,CAAClJ,MAAM,EAAEsJ,CAAC,GAAGZ,QAAQ,EAAEY,CAAC,EAAE,EAAE;IAC9D,KAAK,IAAI1J,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGqB,SAAS,CAACI,CAAC,CAAC,CAACtJ,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;MACvD,IAAI4G,IAAI,GAAG0C,SAAS,CAACI,CAAC,CAAC,CAAC1J,CAAC,CAAC;MAC1B4G,IAAI,CAACR,CAAC,GAAGpG,CAAC;MACV4G,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACzE,KAAK,GAAGsH,MAAM;IAC/B;EACF;EACA,OAAOxC,KAAK,CAAC2B,GAAG,CAAC,UAAUrB,IAAI,EAAE;IAC/B,OAAOjD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDV,EAAE,EAAEC,QAAQ,CAACS,IAAI,CAAC,GAAGkC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAIE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACjF,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGqB,SAAS,CAAClJ,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IACpD,IAAI2I,KAAK,GAAGW,SAAS,CAACtJ,CAAC,CAAC;IACxB,IAAI4J,CAAC,GAAGjB,KAAK,CAACvI,MAAM;;IAEpB;IACAuI,KAAK,CAACkB,IAAI,CAAClC,UAAU,CAAC;IACtB,IAAImC,EAAE,GAAG,CAAC;IACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAInD,IAAI,GAAG+B,KAAK,CAACoB,CAAC,CAAC;MACnB,IAAIlD,EAAE,GAAGiD,EAAE,GAAGlD,IAAI,CAACR,CAAC;MACpB,IAAIS,EAAE,GAAG,CAAC,EAAE;QACVD,IAAI,CAACR,CAAC,IAAIS,EAAE;MACd;MACAiD,EAAE,GAAGlD,IAAI,CAACR,CAAC,GAAGQ,IAAI,CAACC,EAAE,GAAG2C,WAAW;IACrC;IACAM,EAAE,GAAGP,MAAM,GAAGC,WAAW;IACzB,KAAK,IAAIQ,EAAE,GAAGJ,CAAC,GAAG,CAAC,EAAEI,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MAClC,IAAIC,MAAM,GAAGtB,KAAK,CAACqB,EAAE,CAAC;MACtB,IAAIE,GAAG,GAAGD,MAAM,CAAC7D,CAAC,GAAG6D,MAAM,CAACpD,EAAE,GAAG2C,WAAW,GAAGM,EAAE;MACjD,IAAII,GAAG,GAAG,CAAC,EAAE;QACXD,MAAM,CAAC7D,CAAC,IAAI8D,GAAG;QACfJ,EAAE,GAAGG,MAAM,CAAC7D,CAAC;MACf,CAAC,MAAM;QACL;MACF;IACF;EACF;AACF,CAAC;AACD,IAAI+D,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC7C,IAAI,EAAEgC,SAAS,EAAErC,KAAK,EAAEmD,KAAK,EAAE;EAC9E,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAE8I,QAAQ,GAAGQ,SAAS,CAAClJ,MAAM,EAAEJ,CAAC,GAAG8I,QAAQ,EAAE9I,CAAC,EAAE,EAAE;IAC9D,IAAI2I,KAAK,GAAGW,SAAS,CAACtJ,CAAC,CAAC;IACxB,KAAK,IAAI+J,CAAC,GAAG,CAAC,EAAE9B,GAAG,GAAGU,KAAK,CAACvI,MAAM,EAAE2J,CAAC,GAAG9B,GAAG,EAAE8B,CAAC,EAAE,EAAE;MAChD,IAAInD,IAAI,GAAG+B,KAAK,CAACoB,CAAC,CAAC;MACnB,IAAInD,IAAI,CAACkB,WAAW,CAAC1H,MAAM,EAAE;QAC3B,IAAIiK,SAAS,GAAGrD,WAAW,CAACC,KAAK,EAAEL,IAAI,CAACkB,WAAW,CAAC;QACpD,IAAIwC,WAAW,GAAGjD,wBAAwB,CAACC,IAAI,EAAEL,KAAK,EAAEL,IAAI,CAACkB,WAAW,CAAC;QACzE,IAAI1B,CAAC,GAAGkE,WAAW,GAAGD,SAAS;QAC/BzD,IAAI,CAACR,CAAC,IAAI,CAACA,CAAC,GAAGO,OAAO,CAACC,IAAI,CAAC,IAAIwD,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACjD,IAAI,EAAEgC,SAAS,EAAErC,KAAK,EAAEmD,KAAK,EAAE;EAC9E,KAAK,IAAIpK,CAAC,GAAGsJ,SAAS,CAAClJ,MAAM,GAAG,CAAC,EAAEJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAI2I,KAAK,GAAGW,SAAS,CAACtJ,CAAC,CAAC;IACxB,KAAK,IAAI+J,CAAC,GAAG,CAAC,EAAE9B,GAAG,GAAGU,KAAK,CAACvI,MAAM,EAAE2J,CAAC,GAAG9B,GAAG,EAAE8B,CAAC,EAAE,EAAE;MAChD,IAAInD,IAAI,GAAG+B,KAAK,CAACoB,CAAC,CAAC;MACnB,IAAInD,IAAI,CAACoB,WAAW,CAAC5H,MAAM,EAAE;QAC3B,IAAIoK,SAAS,GAAGxD,WAAW,CAACC,KAAK,EAAEL,IAAI,CAACoB,WAAW,CAAC;QACpD,IAAIsC,WAAW,GAAG7C,wBAAwB,CAACH,IAAI,EAAEL,KAAK,EAAEL,IAAI,CAACoB,WAAW,CAAC;QACzE,IAAI5B,CAAC,GAAGkE,WAAW,GAAGE,SAAS;QAC/B5D,IAAI,CAACR,CAAC,IAAI,CAACA,CAAC,GAAGO,OAAO,CAACC,IAAI,CAAC,IAAIwD,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIK,cAAc,GAAG,SAASA,cAAcA,CAACnD,IAAI,EAAEL,KAAK,EAAE;EACxD,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEiI,GAAG,GAAGX,IAAI,CAAClH,MAAM,EAAEJ,CAAC,GAAGiI,GAAG,EAAEjI,CAAC,EAAE,EAAE;IAC/C,IAAI4G,IAAI,GAAGU,IAAI,CAACtH,CAAC,CAAC;IAClB,IAAI0K,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV/D,IAAI,CAACoB,WAAW,CAAC6B,IAAI,CAAC,UAAUvD,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOe,IAAI,CAACL,KAAK,CAACX,CAAC,CAAC,CAACzG,MAAM,CAAC,CAACuG,CAAC,GAAGkB,IAAI,CAACL,KAAK,CAACV,CAAC,CAAC,CAAC1G,MAAM,CAAC,CAACuG,CAAC;IAC1D,CAAC,CAAC;IACFQ,IAAI,CAACkB,WAAW,CAAC+B,IAAI,CAAC,UAAUvD,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOe,IAAI,CAACL,KAAK,CAACX,CAAC,CAAC,CAAC3G,MAAM,CAAC,CAACyG,CAAC,GAAGkB,IAAI,CAACL,KAAK,CAACV,CAAC,CAAC,CAAC5G,MAAM,CAAC,CAACyG,CAAC;IAC1D,CAAC,CAAC;IACF,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEa,IAAI,GAAGhE,IAAI,CAACoB,WAAW,CAAC5H,MAAM,EAAE2J,CAAC,GAAGa,IAAI,EAAEb,CAAC,EAAE,EAAE;MAC7D,IAAIxC,IAAI,GAAGN,KAAK,CAACL,IAAI,CAACoB,WAAW,CAAC+B,CAAC,CAAC,CAAC;MACrC,IAAIxC,IAAI,EAAE;QACRA,IAAI,CAACmD,EAAE,GAAGA,EAAE;QACZA,EAAE,IAAInD,IAAI,CAACV,EAAE;MACf;IACF;IACA,KAAK,IAAIgE,GAAG,GAAG,CAAC,EAAEC,IAAI,GAAGlE,IAAI,CAACkB,WAAW,CAAC1H,MAAM,EAAEyK,GAAG,GAAGC,IAAI,EAAED,GAAG,EAAE,EAAE;MACnE,IAAIE,KAAK,GAAG9D,KAAK,CAACL,IAAI,CAACkB,WAAW,CAAC+C,GAAG,CAAC,CAAC;MACxC,IAAIE,KAAK,EAAE;QACTA,KAAK,CAACJ,EAAE,GAAGA,EAAE;QACbA,EAAE,IAAII,KAAK,CAAClE,EAAE;MAChB;IACF;EACF;AACF,CAAC;AACD,IAAImE,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBzC,KAAK,GAAGwC,KAAK,CAACxC,KAAK;IACnBc,MAAM,GAAG0B,KAAK,CAAC1B,MAAM;IACrB4B,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BzC,SAAS,GAAGuC,KAAK,CAACvC,SAAS;IAC3Bc,WAAW,GAAGyB,KAAK,CAACzB,WAAW;EACjC,IAAIvC,KAAK,GAAGiE,IAAI,CAACjE,KAAK;EACtB,IAAImE,aAAa,GAAG7C,YAAY,CAAC2C,IAAI,EAAEzC,KAAK,EAAEC,SAAS,CAAC;IACtDpB,IAAI,GAAG8D,aAAa,CAAC9D,IAAI;EAC3B,IAAIgC,SAAS,GAAGF,YAAY,CAAC9B,IAAI,CAAC;EAClC,IAAI+D,QAAQ,GAAGhC,aAAa,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEvC,KAAK,CAAC;EACnE0C,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,CAAC;EACjD,IAAIY,KAAK,GAAG,CAAC;EACb,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImL,UAAU,EAAEnL,CAAC,EAAE,EAAE;IACpCuK,gBAAgB,CAACjD,IAAI,EAAEgC,SAAS,EAAE+B,QAAQ,EAAEjB,KAAK,IAAI,IAAI,CAAC;IAC1DT,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,CAAC;IACjDW,gBAAgB,CAAC7C,IAAI,EAAEgC,SAAS,EAAE+B,QAAQ,EAAEjB,KAAK,CAAC;IAClDT,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,CAAC;EACnD;EACAiB,cAAc,CAACnD,IAAI,EAAE+D,QAAQ,CAAC;EAC9B,OAAO;IACL1C,KAAK,EAAErB,IAAI;IACXL,KAAK,EAAEoE;EACT,CAAC;AACH,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACrE,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLrF,CAAC,EAAEoF,EAAE,CAACpF,CAAC,GAAGoF,EAAE,CAAC9C,KAAK,GAAG,CAAC;MACtBrC,CAAC,EAAEmF,EAAE,CAACnF,CAAC,GAAGmF,EAAE,CAAChC,MAAM,GAAG;IACxB,CAAC;EACH;EACA,OAAO;IACLpD,CAAC,EAAE,CAACoF,EAAE,CAACE,OAAO,GAAGF,EAAE,CAACG,OAAO,IAAI,CAAC;IAChCtF,CAAC,EAAE,CAACmF,EAAE,CAACI,OAAO,GAAGJ,EAAE,CAACK,OAAO,IAAI;EACjC,CAAC;AACH,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACN,EAAE,EAAEC,IAAI,EAAEM,OAAO,EAAE;EACxE,IAAIC,OAAO,GAAGR,EAAE,CAACQ,OAAO;EACxB,IAAIP,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,CAAC;MACNO,OAAO,EAAER,EAAE;MACXS,IAAI,EAAE/F,iBAAiB,CAAC8F,OAAO,EAAED,OAAO,EAAE,EAAE,CAAC;MAC7C3J,KAAK,EAAE8D,iBAAiB,CAAC8F,OAAO,EAAE,OAAO;IAC3C,CAAC,CAAC;EACJ;EACA,IAAIA,OAAO,CAACpM,MAAM,IAAIoM,OAAO,CAAClM,MAAM,EAAE;IACpC,IAAIoM,UAAU,GAAGhG,iBAAiB,CAAC8F,OAAO,CAACpM,MAAM,EAAEmM,OAAO,EAAE,EAAE,CAAC;IAC/D,IAAII,UAAU,GAAGjG,iBAAiB,CAAC8F,OAAO,CAAClM,MAAM,EAAEiM,OAAO,EAAE,EAAE,CAAC;IAC/D,OAAO,CAAC;MACNC,OAAO,EAAER,EAAE;MACXS,IAAI,EAAE,EAAE,CAACG,MAAM,CAACF,UAAU,EAAE,KAAK,CAAC,CAACE,MAAM,CAACD,UAAU,CAAC;MACrD/J,KAAK,EAAE8D,iBAAiB,CAAC8F,OAAO,EAAE,OAAO;IAC3C,CAAC,CAAC;EACJ;EACA,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIK,MAAM,GAAG,aAAa,UAAUC,cAAc,EAAE;EACzDtK,SAAS,CAACqK,MAAM,EAAEC,cAAc,CAAC;EACjC,IAAIC,MAAM,GAAG7J,YAAY,CAAC2J,MAAM,CAAC;EACjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IACTvL,eAAe,CAAC,IAAI,EAAEoL,MAAM,CAAC;IAC7B,KAAK,IAAII,KAAK,GAAG3L,SAAS,CAACT,MAAM,EAAEqM,IAAI,GAAG,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,KAAK,EAAEG,IAAI,EAAE,EAAE;MAC1FF,IAAI,CAACE,IAAI,CAAC,GAAG9L,SAAS,CAAC8L,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAAC/L,IAAI,CAACQ,KAAK,CAACuL,MAAM,EAAE,CAAC,IAAI,CAAC,CAACH,MAAM,CAACM,IAAI,CAAC,CAAC;IACtDjI,eAAe,CAAClB,sBAAsB,CAACiJ,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDK,aAAa,EAAE,IAAI;MACnBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,KAAK;MACtBnE,KAAK,EAAE,EAAE;MACT1B,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOsF,KAAK;EACd;EACA3K,YAAY,CAACwK,MAAM,EAAE,CAAC;IACpBrM,GAAG,EAAE,kBAAkB;IACvBoC,KAAK,EAAE,SAAS4K,gBAAgBA,CAACxB,EAAE,EAAEC,IAAI,EAAE5H,CAAC,EAAE;MAC5C,IAAIoJ,WAAW,GAAG,IAAI,CAAC3L,KAAK;QAC1B4L,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;MACjC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,EAAE;QACf,IAAI,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;UAC5B,IAAIF,WAAW,CAAC9L,KAAK,CAACiM,OAAO,KAAK,OAAO,EAAE;YACzC,OAAOhJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAErB,EAAE;cACjBsB,iBAAiB,EAAErB,IAAI;cACvBsB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;UACA,OAAOO,IAAI;QACb,CAAC,EAAE,YAAY;UACb,IAAIJ,YAAY,EAAE;YAChBA,YAAY,CAAC1B,EAAE,EAAEC,IAAI,EAAE5H,CAAC,CAAC;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIqJ,YAAY,EAAE;QACvBA,YAAY,CAAC1B,EAAE,EAAEC,IAAI,EAAE5H,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,kBAAkB;IACvBoC,KAAK,EAAE,SAASoL,gBAAgBA,CAAChC,EAAE,EAAEC,IAAI,EAAE5H,CAAC,EAAE;MAC5C,IAAI4J,YAAY,GAAG,IAAI,CAACnM,KAAK;QAC3BoM,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCP,QAAQ,GAAGM,YAAY,CAACN,QAAQ;MAClC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,EAAE;QACf,IAAI,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;UAC5B,IAAIF,WAAW,CAAC9L,KAAK,CAACiM,OAAO,KAAK,OAAO,EAAE;YACzC,OAAOhJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAE1H,SAAS;cACxB2H,iBAAiB,EAAE3H,SAAS;cAC5B4H,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;UACA,OAAOO,IAAI;QACb,CAAC,EAAE,YAAY;UACb,IAAII,YAAY,EAAE;YAChBA,YAAY,CAAClC,EAAE,EAAEC,IAAI,EAAE5H,CAAC,CAAC;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI6J,YAAY,EAAE;QACvBA,YAAY,CAAClC,EAAE,EAAEC,IAAI,EAAE5H,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,aAAa;IAClBoC,KAAK,EAAE,SAASuL,WAAWA,CAACnC,EAAE,EAAEC,IAAI,EAAE5H,CAAC,EAAE;MACvC,IAAI+J,YAAY,GAAG,IAAI,CAACtM,KAAK;QAC3BuM,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BV,QAAQ,GAAGS,YAAY,CAACT,QAAQ;MAClC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,IAAIA,WAAW,CAAC9L,KAAK,CAACiM,OAAO,KAAK,OAAO,EAAE;QACxD,IAAI,IAAI,CAACO,KAAK,CAACf,eAAe,EAAE;UAC9B,IAAI,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;YAC5B,OAAO/I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAE1H,SAAS;cACxB2H,iBAAiB,EAAE3H,SAAS;cAC5B4H,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;YAC5B,OAAO/I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAErB,EAAE;cACjBsB,iBAAiB,EAAErB,IAAI;cACvBsB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;MACA,IAAIc,OAAO,EAAEA,OAAO,CAACrC,EAAE,EAAEC,IAAI,EAAE5H,CAAC,CAAC;IACnC;EACF,CAAC,EAAE;IACD7D,GAAG,EAAE,aAAa;IAClBoC,KAAK,EAAE,SAAS2L,WAAWA,CAAC7G,KAAK,EAAE0B,KAAK,EAAE;MACxC,IAAIoF,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC3M,KAAK;QAC3B4M,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,WAAW,GAAGF,YAAY,CAACzG,IAAI;QAC/B4G,MAAM,GAAGH,YAAY,CAACG,MAAM;MAC9B,IAAIC,GAAG,GAAGvP,IAAI,CAACsP,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;MAClC,IAAIE,IAAI,GAAGxP,IAAI,CAACsP,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;MACpC,OAAO,aAAa9I,KAAK,CAACiJ,aAAa,CAAC7I,KAAK,EAAE;QAC7C8I,SAAS,EAAE,uBAAuB;QAClCxO,GAAG,EAAE;MACP,CAAC,EAAEkH,KAAK,CAAC2B,GAAG,CAAC,UAAUrB,IAAI,EAAEvH,CAAC,EAAE;QAC9B,IAAIwO,eAAe,GAAGjH,IAAI,CAACmD,EAAE;UAC3B+D,eAAe,GAAGlH,IAAI,CAACoD,EAAE;UACzB+D,SAAS,GAAGnH,IAAI,CAACV,EAAE;QACrB,IAAIlH,MAAM,GAAGgJ,KAAK,CAACpB,IAAI,CAAC5H,MAAM,CAAC;QAC/B,IAAIE,MAAM,GAAG8I,KAAK,CAACpB,IAAI,CAAC1H,MAAM,CAAC;QAC/B,IAAI4L,OAAO,GAAG9L,MAAM,CAACwG,CAAC,GAAGxG,MAAM,CAACwJ,EAAE,GAAGkF,IAAI;QACzC,IAAI3C,OAAO,GAAG7L,MAAM,CAACsG,CAAC,GAAGkI,IAAI;QAC7B,IAAIM,iBAAiB,GAAGtI,sBAAsB,CAACoF,OAAO,EAAEC,OAAO,CAAC;QAChE,IAAIkD,cAAc,GAAGD,iBAAiB,CAACV,aAAa,CAAC;QACrD,IAAIY,cAAc,GAAGF,iBAAiB,CAAC,CAAC,GAAGV,aAAa,CAAC;QACzD,IAAItC,OAAO,GAAGhM,MAAM,CAACyG,CAAC,GAAGoI,eAAe,GAAGE,SAAS,GAAG,CAAC,GAAGN,GAAG;QAC9D,IAAIxC,OAAO,GAAG/L,MAAM,CAACuG,CAAC,GAAGqI,eAAe,GAAGC,SAAS,GAAG,CAAC,GAAGN,GAAG;QAC9D,IAAIU,SAAS,GAAGxK,aAAa,CAAC;UAC5BmH,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChBgD,cAAc,EAAEA,cAAc;UAC9BC,cAAc,EAAEA,cAAc;UAC9BL,eAAe,EAAEA,eAAe;UAChCC,eAAe,EAAEA,eAAe;UAChCC,SAAS,EAAEA,SAAS;UACpB7F,KAAK,EAAE7I,CAAC;UACR+L,OAAO,EAAEzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YAClD5H,MAAM,EAAEA,MAAM;YACdE,MAAM,EAAEA;UACV,CAAC;QACH,CAAC,EAAEmG,WAAW,CAACkI,WAAW,CAAC,CAAC;QAC5B,IAAIa,MAAM,GAAG;UACX9B,YAAY,EAAEc,MAAM,CAAChB,gBAAgB,CAACnM,IAAI,CAACmN,MAAM,EAAEe,SAAS,EAAE,MAAM,CAAC;UACrErB,YAAY,EAAEM,MAAM,CAACR,gBAAgB,CAAC3M,IAAI,CAACmN,MAAM,EAAEe,SAAS,EAAE,MAAM,CAAC;UACrElB,OAAO,EAAEG,MAAM,CAACL,WAAW,CAAC9M,IAAI,CAACmN,MAAM,EAAEe,SAAS,EAAE,MAAM;QAC5D,CAAC;QACD,QACE;UACA;UACAzJ,KAAK,CAACiJ,aAAa,CAAC7I,KAAK,EAAE/E,QAAQ,CAAC;YAClCX,GAAG,EAAE,MAAM,CAACoM,MAAM,CAACnM,CAAC;UACtB,CAAC,EAAE+O,MAAM,CAAC,EAAEhB,MAAM,CAACvO,WAAW,CAACwP,cAAc,CAACd,WAAW,EAAEY,SAAS,CAAC;QAAC;MAE1E,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,aAAa;IAClBoC,KAAK,EAAE,SAAS8M,WAAWA,CAACtG,KAAK,EAAE;MACjC,IAAIuG,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC9N,KAAK;QAC3B+N,WAAW,GAAGD,YAAY,CAACvI,IAAI;QAC/BuH,MAAM,GAAGgB,YAAY,CAAChB,MAAM;MAC9B,IAAIC,GAAG,GAAGvP,IAAI,CAACsP,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;MAClC,IAAIE,IAAI,GAAGxP,IAAI,CAACsP,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;MACpC,OAAO,aAAa9I,KAAK,CAACiJ,aAAa,CAAC7I,KAAK,EAAE;QAC7C8I,SAAS,EAAE,uBAAuB;QAClCxO,GAAG,EAAE;MACP,CAAC,EAAE4I,KAAK,CAACC,GAAG,CAAC,UAAUhC,IAAI,EAAE5G,CAAC,EAAE;QAC9B,IAAImG,CAAC,GAAGS,IAAI,CAACT,CAAC;UACZC,CAAC,GAAGQ,IAAI,CAACR,CAAC;UACV+C,EAAE,GAAGvC,IAAI,CAACuC,EAAE;UACZtC,EAAE,GAAGD,IAAI,CAACC,EAAE;QACd,IAAIwI,SAAS,GAAG/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,WAAW,CAACoJ,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7EjJ,CAAC,EAAEA,CAAC,GAAGkI,IAAI;UACXjI,CAAC,EAAEA,CAAC,GAAGgI,GAAG;UACV3F,KAAK,EAAEU,EAAE;UACTI,MAAM,EAAE1C,EAAE;UACVgC,KAAK,EAAE7I,CAAC;UACR+L,OAAO,EAAEnF;QACX,CAAC,CAAC;QACF,IAAImI,MAAM,GAAG;UACX9B,YAAY,EAAEiC,MAAM,CAACnC,gBAAgB,CAACnM,IAAI,CAACsO,MAAM,EAAEG,SAAS,EAAE,MAAM,CAAC;UACrE5B,YAAY,EAAEyB,MAAM,CAAC3B,gBAAgB,CAAC3M,IAAI,CAACsO,MAAM,EAAEG,SAAS,EAAE,MAAM,CAAC;UACrEzB,OAAO,EAAEsB,MAAM,CAACxB,WAAW,CAAC9M,IAAI,CAACsO,MAAM,EAAEG,SAAS,EAAE,MAAM;QAC5D,CAAC;QACD,QACE;UACA;UACAhK,KAAK,CAACiJ,aAAa,CAAC7I,KAAK,EAAE/E,QAAQ,CAAC;YAClCX,GAAG,EAAE,MAAM,CAACoM,MAAM,CAACnM,CAAC;UACtB,CAAC,EAAE+O,MAAM,CAAC,EAAEG,MAAM,CAAC1P,WAAW,CAAC8P,cAAc,CAACF,WAAW,EAAEC,SAAS,CAAC;QAAC;MAE1E,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDtP,GAAG,EAAE,eAAe;IACpBoC,KAAK,EAAE,SAASoN,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAACnO,KAAK;QAC3B6L,QAAQ,GAAGsC,YAAY,CAACtC,QAAQ;QAChCzE,KAAK,GAAG+G,YAAY,CAAC/G,KAAK;QAC1Bc,MAAM,GAAGiG,YAAY,CAACjG,MAAM;QAC5BuC,OAAO,GAAG0D,YAAY,CAAC1D,OAAO;MAChC,IAAIqB,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAI,CAACyH,WAAW,EAAE;QAChB,OAAO,IAAI;MACb;MACA,IAAIsC,WAAW,GAAG,IAAI,CAAC5B,KAAK;QAC1Bf,eAAe,GAAG2C,WAAW,CAAC3C,eAAe;QAC7CF,aAAa,GAAG6C,WAAW,CAAC7C,aAAa;QACzCC,iBAAiB,GAAG4C,WAAW,CAAC5C,iBAAiB;MACnD,IAAI6C,OAAO,GAAG;QACZvJ,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJqC,KAAK,EAAEA,KAAK;QACZc,MAAM,EAAEA;MACV,CAAC;MACD,IAAIoG,UAAU,GAAG/C,aAAa,GAAGtB,sBAAsB,CAACsB,aAAa,EAAEC,iBAAiB,CAAC,GAAG3G,0BAA0B;MACtH,IAAI6F,OAAO,GAAGa,aAAa,GAAGf,mBAAmB,CAACe,aAAa,EAAEC,iBAAiB,EAAEf,OAAO,CAAC,GAAG,EAAE;MACjG,OAAO,aAAazG,KAAK,CAACuK,YAAY,CAACzC,WAAW,EAAE;QAClDuC,OAAO,EAAEA,OAAO;QAChBG,MAAM,EAAE/C,eAAe;QACvB6C,UAAU,EAAEA,UAAU;QACtBG,KAAK,EAAE,EAAE;QACT/D,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDhM,GAAG,EAAE,QAAQ;IACboC,KAAK,EAAE,SAAS4N,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACjK,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAIkK,YAAY,GAAG,IAAI,CAAC3O,KAAK;QAC3BoH,KAAK,GAAGuH,YAAY,CAACvH,KAAK;QAC1Bc,MAAM,GAAGyG,YAAY,CAACzG,MAAM;QAC5BgF,SAAS,GAAGyB,YAAY,CAACzB,SAAS;QAClC0B,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1B/C,QAAQ,GAAG8C,YAAY,CAAC9C,QAAQ;QAChCgD,MAAM,GAAGxQ,wBAAwB,CAACsQ,YAAY,EAAE9Q,SAAS,CAAC;MAC5D,IAAIiR,YAAY,GAAG,IAAI,CAACtC,KAAK;QAC3B5G,KAAK,GAAGkJ,YAAY,CAAClJ,KAAK;QAC1B0B,KAAK,GAAGwH,YAAY,CAACxH,KAAK;MAC5B,IAAIyH,KAAK,GAAGpK,WAAW,CAACkK,MAAM,CAAC;MAC/B,OAAO,aAAa7K,KAAK,CAACiJ,aAAa,CAAC,KAAK,EAAE;QAC7CC,SAAS,EAAEhJ,UAAU,CAAC,kBAAkB,EAAEgJ,SAAS,CAAC;QACpD0B,KAAK,EAAE3L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2L,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDI,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE,SAAS;UACjB7H,KAAK,EAAEA,KAAK;UACZc,MAAM,EAAEA;QACV,CAAC,CAAC;QACFgH,IAAI,EAAE;MACR,CAAC,EAAE,aAAalL,KAAK,CAACiJ,aAAa,CAAC9I,OAAO,EAAE9E,QAAQ,CAAC,CAAC,CAAC,EAAE0P,KAAK,EAAE;QAC/D3H,KAAK,EAAEA,KAAK;QACZc,MAAM,EAAEA;MACV,CAAC,CAAC,EAAE1D,iBAAiB,CAACqH,QAAQ,CAAC,EAAE,IAAI,CAACY,WAAW,CAAC7G,KAAK,EAAE0B,KAAK,CAAC,EAAE,IAAI,CAACsG,WAAW,CAACtG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC4G,aAAa,CAAC,CAAC,CAAC;IAClH;EACF,CAAC,CAAC,EAAE,CAAC;IACHxP,GAAG,EAAE,0BAA0B;IAC/BoC,KAAK,EAAE,SAASqO,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAIxF,IAAI,GAAGuF,SAAS,CAACvF,IAAI;QACvBzC,KAAK,GAAGgI,SAAS,CAAChI,KAAK;QACvBc,MAAM,GAAGkH,SAAS,CAAClH,MAAM;QACzB4E,MAAM,GAAGsC,SAAS,CAACtC,MAAM;QACzBhD,UAAU,GAAGsF,SAAS,CAACtF,UAAU;QACjCzC,SAAS,GAAG+H,SAAS,CAAC/H,SAAS;QAC/Bc,WAAW,GAAGiH,SAAS,CAACjH,WAAW;MACrC,IAAI0B,IAAI,KAAKwF,SAAS,CAACC,QAAQ,IAAIlI,KAAK,KAAKiI,SAAS,CAACE,SAAS,IAAIrH,MAAM,KAAKmH,SAAS,CAACG,UAAU,IAAI,CAACjL,YAAY,CAACuI,MAAM,EAAEuC,SAAS,CAACI,UAAU,CAAC,IAAI3F,UAAU,KAAKuF,SAAS,CAACK,cAAc,IAAIrI,SAAS,KAAKgI,SAAS,CAACM,aAAa,IAAIxH,WAAW,KAAKkH,SAAS,CAACO,eAAe,EAAE;QACnR,IAAIC,YAAY,GAAGzI,KAAK,IAAI0F,MAAM,IAAIA,MAAM,CAACE,IAAI,IAAI,CAAC,CAAC,IAAIF,MAAM,IAAIA,MAAM,CAACgD,KAAK,IAAI,CAAC,CAAC;QACvF,IAAIC,aAAa,GAAG7H,MAAM,IAAI4E,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAI,CAAC,CAAC,IAAID,MAAM,IAAIA,MAAM,CAACkD,MAAM,IAAI,CAAC,CAAC;QACzF,IAAIC,YAAY,GAAGtG,WAAW,CAAC;YAC3BE,IAAI,EAAEA,IAAI;YACVzC,KAAK,EAAEyI,YAAY;YACnB3H,MAAM,EAAE6H,aAAa;YACrBjG,UAAU,EAAEA,UAAU;YACtBzC,SAAS,EAAEA,SAAS;YACpBc,WAAW,EAAEA;UACf,CAAC,CAAC;UACFvC,KAAK,GAAGqK,YAAY,CAACrK,KAAK;UAC1B0B,KAAK,GAAG2I,YAAY,CAAC3I,KAAK;QAC5B,OAAOrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoM,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACrD/H,KAAK,EAAEA,KAAK;UACZ1B,KAAK,EAAEA,KAAK;UACZ0J,QAAQ,EAAEzF,IAAI;UACd0F,SAAS,EAAEzF,UAAU;UACrB0F,UAAU,EAAEtH,MAAM;UAClBuH,UAAU,EAAE3C,MAAM;UAClB8C,eAAe,EAAEzH,WAAW;UAC5BwH,aAAa,EAAEtI,SAAS;UACxBqI,cAAc,EAAE5F;QAClB,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDpL,GAAG,EAAE,gBAAgB;IACrBoC,KAAK,EAAE,SAAS6M,cAAcA,CAACuC,MAAM,EAAElQ,KAAK,EAAE;MAC5C,IAAK,aAAagE,KAAK,CAACmM,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAalM,KAAK,CAACuK,YAAY,CAAC2B,MAAM,EAAElQ,KAAK,CAAC;MACvD;MACA,IAAIvC,WAAW,CAACyS,MAAM,CAAC,EAAE;QACvB,OAAOA,MAAM,CAAClQ,KAAK,CAAC;MACtB;MACA,IAAIoK,OAAO,GAAGpK,KAAK,CAACoK,OAAO;QACzBE,OAAO,GAAGtK,KAAK,CAACsK,OAAO;QACvBiD,cAAc,GAAGvN,KAAK,CAACuN,cAAc;QACrClD,OAAO,GAAGrK,KAAK,CAACqK,OAAO;QACvBE,OAAO,GAAGvK,KAAK,CAACuK,OAAO;QACvBiD,cAAc,GAAGxN,KAAK,CAACwN,cAAc;QACrCH,SAAS,GAAGrN,KAAK,CAACqN,SAAS;QAC3BwB,MAAM,GAAGxQ,wBAAwB,CAAC2B,KAAK,EAAElC,UAAU,CAAC;MACtD,OAAO,aAAakG,KAAK,CAACiJ,aAAa,CAAC,MAAM,EAAE5N,QAAQ,CAAC;QACvD6N,SAAS,EAAE,sBAAsB;QACjC7E,CAAC,EAAE,eAAe,CAACyC,MAAM,CAACV,OAAO,EAAE,GAAG,CAAC,CAACU,MAAM,CAACR,OAAO,EAAE,eAAe,CAAC,CAACQ,MAAM,CAACyC,cAAc,EAAE,GAAG,CAAC,CAACzC,MAAM,CAACR,OAAO,EAAE,GAAG,CAAC,CAACQ,MAAM,CAAC0C,cAAc,EAAE,GAAG,CAAC,CAAC1C,MAAM,CAACP,OAAO,EAAE,GAAG,CAAC,CAACO,MAAM,CAACT,OAAO,EAAE,GAAG,CAAC,CAACS,MAAM,CAACP,OAAO,EAAE,YAAY,CAAC;QAC7N6F,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAEjD,SAAS;QACtBkD,aAAa,EAAE;MACjB,CAAC,EAAE5L,WAAW,CAACkK,MAAM,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDnQ,GAAG,EAAE,gBAAgB;IACrBoC,KAAK,EAAE,SAASmN,cAAcA,CAACiC,MAAM,EAAElQ,KAAK,EAAE;MAC5C,IAAK,aAAagE,KAAK,CAACmM,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAalM,KAAK,CAACuK,YAAY,CAAC2B,MAAM,EAAElQ,KAAK,CAAC;MACvD;MACA,IAAIvC,WAAW,CAACyS,MAAM,CAAC,EAAE;QACvB,OAAOA,MAAM,CAAClQ,KAAK,CAAC;MACtB;MACA,OAAO,aAAagE,KAAK,CAACiJ,aAAa,CAAC3I,SAAS,EAAEjF,QAAQ,CAAC;QAC1D6N,SAAS,EAAE,sBAAsB;QACjCkD,IAAI,EAAE,SAAS;QACfI,WAAW,EAAE;MACf,CAAC,EAAE7L,WAAW,CAAC3E,KAAK,CAAC,EAAE;QACrBkP,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOnE,MAAM;AACf,CAAC,CAAC9G,aAAa,CAAC;AAChBd,eAAe,CAAC4H,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChD5H,eAAe,CAAC4H,MAAM,EAAE,cAAc,EAAE;EACtCN,OAAO,EAAE,MAAM;EACfgG,OAAO,EAAE,OAAO;EAChBtI,WAAW,EAAE,EAAE;EACfd,SAAS,EAAE,EAAE;EACbuF,aAAa,EAAE,GAAG;EAClB9C,UAAU,EAAE,EAAE;EACdgD,MAAM,EAAE;IACNC,GAAG,EAAE,CAAC;IACN+C,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE,CAAC;IACThD,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}