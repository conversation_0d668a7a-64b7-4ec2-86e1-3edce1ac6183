{"ast": null, "code": "import _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nvar _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Line, _PureComponent);\n  var _super = _createSuper(Line);\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0;; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_assertThisInitialized(_this), \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      }\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item, i) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"bar-\".concat(i),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          value: entry.value,\n          dataKey: dataKey,\n          cx: entry.x,\n          cy: entry.y,\n          index: i,\n          payload: entry.payload\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps, {\n        role: \"img\"\n      }), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = \"\".concat(curLength, \"px \").concat(totalLength - curLength, \"px\");\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !_isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = classNames('recharts-line', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var className = classNames('recharts-line-dot', option ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n  return Line;\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n_defineProperty(Line, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    dataKey = _ref2.dataKey,\n    bandSize = _ref2.bandSize,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: _isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: _isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});", "map": {"version": 3, "names": ["_isEqual", "_isFunction", "_isNil", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "_extends", "assign", "bind", "arguments", "hasOwnProperty", "apply", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "Animate", "classNames", "Curve", "Dot", "Layer", "LabelList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uniqueId", "interpolateNumber", "findAllByType", "filterProps", "Global", "getCateCoordinateOfLine", "getValueByDataKey", "Line", "_PureComponent", "_super", "_this", "_len", "args", "_key", "concat", "isAnimationFinished", "totalLength", "lines", "lineLength", "reduce", "pre", "next", "count", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON>", "restLength", "remainLines", "sum", "emptyLines", "repeat", "map", "line", "join", "node", "mainCurve", "setState", "onAnimationEnd", "onAnimationStart", "componentDidMount", "isAnimationActive", "getTotalLength", "curveDom", "err", "renderErrorBar", "needClip", "clipPathId", "state", "_this$props", "points", "xAxis", "yAxis", "layout", "children", "errorBarItems", "dataPointFormatter", "dataPoint", "dataKey", "x", "y", "errorVal", "payload", "errorBarProps", "clipPath", "createElement", "item", "cloneElement", "data", "renderDots", "_this$props2", "dot", "lineProps", "customDotProps", "dots", "entry", "dotProps", "r", "cx", "cy", "index", "renderDotItem", "dotsProps", "className", "role", "renderCurveStatically", "_this$props3", "type", "connectNulls", "ref", "others", "curveProps", "fill", "pathRef", "renderCurveWithAnimation", "_this2", "_this$props4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animationBegin", "animationDuration", "animationEasing", "animationId", "animate<PERSON>ew<PERSON><PERSON><PERSON>", "width", "height", "_this$state", "prevPoints", "begin", "duration", "isActive", "easing", "t", "to", "handleAnimationEnd", "handleAnimationStart", "_ref", "prevPointsDiffFactor", "stepData", "prevPointIndex", "prev", "interpolatorX", "interpolatorY", "_interpolatorX", "_interpolatorY", "interpolator", "curL<PERSON>th", "currentStrokeDasharray", "split", "num", "parseFloat", "getStrokeDasharray", "renderCurve", "_this$props5", "_this$state2", "render", "_this$props6", "hide", "top", "left", "id", "hasSinglePoint", "layerClass", "allowDataOverflow", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "linesUnit", "option", "dotItem", "isValidElement", "xAxisId", "yAxisId", "activeDot", "legendType", "stroke", "strokeWidth", "isSsr", "label", "_ref2", "xAxisTicks", "yAxisTicks", "bandSize", "displayedData", "offset", "axis", "ticks", "scale"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/Line.js"], "sourcesContent": ["import _isEqual from \"lodash/isEqual\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNil from \"lodash/isNil\";\nvar _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport classNames from 'classnames';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Line, _PureComponent);\n  var _super = _createSuper(Line);\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0;; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_assertThisInitialized(_this), \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      }\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item, i) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          // eslint-disable-next-line react/no-array-index-key\n          key: \"bar-\".concat(i),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          value: entry.value,\n          dataKey: dataKey,\n          cx: entry.x,\n          cy: entry.y,\n          index: i,\n          payload: entry.payload\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps, {\n        role: \"img\"\n      }), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = \"\".concat(curLength, \"px \").concat(totalLength - curLength, \"px\");\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !_isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = classNames('recharts-line', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: height\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (_isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var className = classNames('recharts-line-dot', option ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n  return Line;\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n_defineProperty(Line, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    dataKey = _ref2.dataKey,\n    bandSize = _ref2.bandSize,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: _isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: _isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC;AACzD,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT,SAASa,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUf,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGkB,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACqB,cAAc,CAACP,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOa,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AAAE;AAClV,SAASG,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIT,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACQ,MAAM,CAAC;EAAE,IAAIhB,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIiB,OAAO,GAAGlB,MAAM,CAACC,qBAAqB,CAACe,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOpB,MAAM,CAACqB,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEd,IAAI,CAACe,IAAI,CAACT,KAAK,CAACN,IAAI,EAAEU,OAAO,CAAC;EAAE;EAAE,OAAOV,IAAI;AAAE;AACpV,SAASgB,aAAaA,CAAC5B,MAAM,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIL,MAAM,GAAG,IAAI,IAAIkB,SAAS,CAACb,CAAC,CAAC,GAAGa,SAAS,CAACb,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAE4B,eAAe,CAAC9B,MAAM,EAAEE,GAAG,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAAC2B,yBAAyB,GAAG3B,MAAM,CAAC4B,gBAAgB,CAAChC,MAAM,EAAEI,MAAM,CAAC2B,yBAAyB,CAACjC,MAAM,CAAC,CAAC,GAAGqB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAEE,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAEE,GAAG,EAAEE,MAAM,CAACqB,wBAAwB,CAAC3B,MAAM,EAAEI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AACzf,SAASkC,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGxC,MAAM,CAACR,SAAS,CAACiD,QAAQ,CAACnC,IAAI,CAAC+B,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAAC9C,WAAW,EAAEiD,CAAC,GAAGH,CAAC,CAAC9C,WAAW,CAACoD,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAACR,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASL,gBAAgBA,CAACc,IAAI,EAAE;EAAE,IAAI,OAAO1D,MAAM,KAAK,WAAW,IAAI0D,IAAI,CAAC1D,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIyD,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASf,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIa,KAAK,CAACI,OAAO,CAACjB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;AAAE;AAC1F,SAASQ,iBAAiBA,CAACR,GAAG,EAAEkB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGlB,GAAG,CAAC5B,MAAM,EAAE8C,GAAG,GAAGlB,GAAG,CAAC5B,MAAM;EAAE,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEmD,IAAI,GAAG,IAAIN,KAAK,CAACK,GAAG,CAAC,EAAElD,CAAC,GAAGkD,GAAG,EAAElD,CAAC,EAAE,EAAEmD,IAAI,CAACnD,CAAC,CAAC,GAAGgC,GAAG,CAAChC,CAAC,CAAC;EAAE,OAAOmD,IAAI;AAAE;AAClL,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIjB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASkB,iBAAiBA,CAAC1D,MAAM,EAAE2D,KAAK,EAAE;EAAE,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,KAAK,CAACpD,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIyD,UAAU,GAAGD,KAAK,CAACxD,CAAC,CAAC;IAAEyD,UAAU,CAAClC,UAAU,GAAGkC,UAAU,CAAClC,UAAU,IAAI,KAAK;IAAEkC,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE1D,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAE+D,cAAc,CAACH,UAAU,CAAC1D,GAAG,CAAC,EAAE0D,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACD,WAAW,CAAC7D,SAAS,EAAEqE,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACD,WAAW,EAAES,WAAW,CAAC;EAAE9D,MAAM,CAAC6B,cAAc,CAACwB,WAAW,EAAE,WAAW,EAAE;IAAEK,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOL,WAAW;AAAE;AAC5R,SAASU,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI7B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAE4B,QAAQ,CAACxE,SAAS,GAAGQ,MAAM,CAACkE,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACzE,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE4E,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEzD,MAAM,CAAC6B,cAAc,CAACmC,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAC/B,CAAC,EAAEgC,CAAC,EAAE;EAAED,eAAe,GAAGpE,MAAM,CAACsE,cAAc,GAAGtE,MAAM,CAACsE,cAAc,CAAC3D,IAAI,CAAC,CAAC,GAAG,SAASyD,eAAeA,CAAC/B,CAAC,EAAEgC,CAAC,EAAE;IAAEhC,CAAC,CAACkC,SAAS,GAAGF,CAAC;IAAE,OAAOhC,CAAC;EAAE,CAAC;EAAE,OAAO+B,eAAe,CAAC/B,CAAC,EAAEgC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACvF,WAAW;MAAEwF,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEjE,SAAS,EAAEoE,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC/D,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAAE;IAAE,OAAOuE,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE9E,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI8B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiD,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACjG,SAAS,CAACkG,OAAO,CAACpF,IAAI,CAAC2E,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACzC,CAAC,EAAE;EAAEyC,eAAe,GAAG9E,MAAM,CAACsE,cAAc,GAAGtE,MAAM,CAAC4F,cAAc,CAACjF,IAAI,CAAC,CAAC,GAAG,SAASmE,eAAeA,CAACzC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACkC,SAAS,IAAIvE,MAAM,CAAC4F,cAAc,CAACvD,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOyC,eAAe,CAACzC,CAAC,CAAC;AAAE;AACnN,SAASX,eAAeA,CAACtC,GAAG,EAAEU,GAAG,EAAEqE,KAAK,EAAE;EAAErE,GAAG,GAAG6D,cAAc,CAAC7D,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIV,GAAG,EAAE;IAAEY,MAAM,CAAC6B,cAAc,CAACzC,GAAG,EAAEU,GAAG,EAAE;MAAEqE,KAAK,EAAEA,KAAK;MAAE7C,UAAU,EAAE,IAAI;MAAEmC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEtE,GAAG,CAACU,GAAG,CAAC,GAAGqE,KAAK;EAAE;EAAE,OAAO/E,GAAG;AAAE;AAC3O,SAASuE,cAAcA,CAACkC,GAAG,EAAE;EAAE,IAAI/F,GAAG,GAAGgG,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO1G,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGiG,MAAM,CAACjG,GAAG,CAAC;AAAE;AAC5H,SAASgG,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI9G,OAAO,CAAC6G,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC3G,MAAM,CAAC8G,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC5F,IAAI,CAAC0F,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI9G,OAAO,CAACkH,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6D,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC/D,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,uBAAuB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC/E,OAAO,IAAIC,IAAI,GAAG,aAAa,UAAUC,cAAc,EAAE;EACvDzD,SAAS,CAACwD,IAAI,EAAEC,cAAc,CAAC;EAC/B,IAAIC,MAAM,GAAGjD,YAAY,CAAC+C,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTvE,eAAe,CAAC,IAAI,EAAEoE,IAAI,CAAC;IAC3B,KAAK,IAAII,IAAI,GAAG/G,SAAS,CAACT,MAAM,EAAEyH,IAAI,GAAG,IAAIhF,KAAK,CAAC+E,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGjH,SAAS,CAACiH,IAAI,CAAC;IAC9B;IACAH,KAAK,GAAGD,MAAM,CAACnH,IAAI,CAACQ,KAAK,CAAC2G,MAAM,EAAE,CAAC,IAAI,CAAC,CAACK,MAAM,CAACF,IAAI,CAAC,CAAC;IACtDlG,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDK,mBAAmB,EAAE,IAAI;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFtG,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,oBAAoB,EAAE,UAAUvH,MAAM,EAAE6H,WAAW,EAAEC,KAAK,EAAE;MACzG,IAAIC,UAAU,GAAGD,KAAK,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;QACjD,OAAOD,GAAG,GAAGC,IAAI;MACnB,CAAC,CAAC;MACF,IAAIC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACrI,MAAM,GAAG+H,UAAU,CAAC;MAC3C,IAAIO,YAAY,GAAGtI,MAAM,GAAG+H,UAAU;MACtC,IAAIQ,UAAU,GAAGV,WAAW,GAAG7H,MAAM;MACrC,IAAIwI,WAAW,GAAG,EAAE;MACpB,KAAK,IAAI5I,CAAC,GAAG,CAAC,EAAE6I,GAAG,GAAG,CAAC,GAAGA,GAAG,IAAIX,KAAK,CAAClI,CAAC,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC9C,IAAI6I,GAAG,GAAGX,KAAK,CAAClI,CAAC,CAAC,GAAG0I,YAAY,EAAE;UACjCE,WAAW,GAAG,EAAE,CAACb,MAAM,CAAChG,kBAAkB,CAACmG,KAAK,CAACvF,KAAK,CAAC,CAAC,EAAE3C,CAAC,CAAC,CAAC,EAAE,CAAC0I,YAAY,GAAGG,GAAG,CAAC,CAAC;UACpF;QACF;MACF;MACA,IAAIC,UAAU,GAAGF,WAAW,CAACxI,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAEuI,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC;MAC9E,OAAO,EAAE,CAACZ,MAAM,CAAChG,kBAAkB,CAACyF,IAAI,CAACuB,MAAM,CAACb,KAAK,EAAEK,KAAK,CAAC,CAAC,EAAExG,kBAAkB,CAAC6G,WAAW,CAAC,EAAEE,UAAU,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/H,OAAO,EAAE,CAAClB,MAAM,CAACkB,IAAI,EAAE,IAAI,CAAC;MAC9B,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;IACFvH,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,IAAI,EAAEV,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAChFtF,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,SAAS,EAAE,UAAUwB,IAAI,EAAE;MACxExB,KAAK,CAACyB,SAAS,GAAGD,IAAI;IACxB,CAAC,CAAC;IACFxH,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/EA,KAAK,CAAC0B,QAAQ,CAAC;QACbrB,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIL,KAAK,CAACnE,KAAK,CAAC8F,cAAc,EAAE;QAC9B3B,KAAK,CAACnE,KAAK,CAAC8F,cAAc,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;IACF3H,eAAe,CAAC2D,sBAAsB,CAACqC,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjFA,KAAK,CAAC0B,QAAQ,CAAC;QACbrB,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIL,KAAK,CAACnE,KAAK,CAAC+F,gBAAgB,EAAE;QAChC5B,KAAK,CAACnE,KAAK,CAAC+F,gBAAgB,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IACF,OAAO5B,KAAK;EACd;EACA9D,YAAY,CAAC2D,IAAI,EAAE,CAAC;IAClBzH,GAAG,EAAE,mBAAmB;IACxBqE,KAAK,EAAE,SAASoF,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAC,IAAI,CAAChG,KAAK,CAACiG,iBAAiB,EAAE;QACjC;MACF;MACA,IAAIxB,WAAW,GAAG,IAAI,CAACyB,cAAc,CAAC,CAAC;MACvC,IAAI,CAACL,QAAQ,CAAC;QACZpB,WAAW,EAAEA;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDlI,GAAG,EAAE,gBAAgB;IACrBqE,KAAK,EAAE,SAASsF,cAAcA,CAAA,EAAG;MAC/B,IAAIC,QAAQ,GAAG,IAAI,CAACP,SAAS;MAC7B,IAAI;QACF,OAAOO,QAAQ,IAAIA,QAAQ,CAACD,cAAc,IAAIC,QAAQ,CAACD,cAAc,CAAC,CAAC,IAAI,CAAC;MAC9E,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZ,OAAO,CAAC;MACV;IACF;EACF,CAAC,EAAE;IACD7J,GAAG,EAAE,gBAAgB;IACrBqE,KAAK,EAAE,SAASyF,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;MACnD,IAAI,IAAI,CAACvG,KAAK,CAACiG,iBAAiB,IAAI,CAAC,IAAI,CAACO,KAAK,CAAChC,mBAAmB,EAAE;QACnE,OAAO,IAAI;MACb;MACA,IAAIiC,WAAW,GAAG,IAAI,CAACzG,KAAK;QAC1B0G,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;QACzBC,KAAK,GAAGH,WAAW,CAACG,KAAK;QACzBC,MAAM,GAAGJ,WAAW,CAACI,MAAM;QAC3BC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;MACjC,IAAIC,aAAa,GAAGpD,aAAa,CAACmD,QAAQ,EAAEtD,QAAQ,CAAC;MACrD,IAAI,CAACuD,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAE;QAC9C,OAAO;UACLC,CAAC,EAAEF,SAAS,CAACE,CAAC;UACdC,CAAC,EAAEH,SAAS,CAACG,CAAC;UACdxG,KAAK,EAAEqG,SAAS,CAACrG,KAAK;UACtByG,QAAQ,EAAEtD,iBAAiB,CAACkD,SAAS,CAACK,OAAO,EAAEJ,OAAO;QACxD,CAAC;MACH;MACA,IAAIK,aAAa,GAAG;QAClBC,QAAQ,EAAElB,QAAQ,GAAG,gBAAgB,CAAC/B,MAAM,CAACgC,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAavD,KAAK,CAACyE,aAAa,CAACnE,KAAK,EAAEiE,aAAa,EAAER,aAAa,CAACvB,GAAG,CAAC,UAAUkC,IAAI,EAAElL,CAAC,EAAE;QACjG,OAAO,aAAawG,KAAK,CAAC2E,YAAY,CAACD,IAAI,EAAE;UAC3C;UACAnL,GAAG,EAAE,MAAM,CAACgI,MAAM,CAAC/H,CAAC,CAAC;UACrBoL,IAAI,EAAElB,MAAM;UACZC,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdG,kBAAkB,EAAEA;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDzK,GAAG,EAAE,YAAY;IACjBqE,KAAK,EAAE,SAASiH,UAAUA,CAACvB,QAAQ,EAAEC,UAAU,EAAE;MAC/C,IAAIN,iBAAiB,GAAG,IAAI,CAACjG,KAAK,CAACiG,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACO,KAAK,CAAChC,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAIsD,YAAY,GAAG,IAAI,CAAC9H,KAAK;QAC3B+H,GAAG,GAAGD,YAAY,CAACC,GAAG;QACtBrB,MAAM,GAAGoB,YAAY,CAACpB,MAAM;QAC5BQ,OAAO,GAAGY,YAAY,CAACZ,OAAO;MAChC,IAAIc,SAAS,GAAGpE,WAAW,CAAC,IAAI,CAAC5D,KAAK,CAAC;MACvC,IAAIiI,cAAc,GAAGrE,WAAW,CAACmE,GAAG,EAAE,IAAI,CAAC;MAC3C,IAAIG,IAAI,GAAGxB,MAAM,CAAClB,GAAG,CAAC,UAAU2C,KAAK,EAAE3L,CAAC,EAAE;QACxC,IAAI4L,QAAQ,GAAGnK,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACvD1B,GAAG,EAAE,MAAM,CAACgI,MAAM,CAAC/H,CAAC,CAAC;UACrB6L,CAAC,EAAE;QACL,CAAC,EAAEL,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UAClCrH,KAAK,EAAEuH,KAAK,CAACvH,KAAK;UAClBsG,OAAO,EAAEA,OAAO;UAChBoB,EAAE,EAAEH,KAAK,CAAChB,CAAC;UACXoB,EAAE,EAAEJ,KAAK,CAACf,CAAC;UACXoB,KAAK,EAAEhM,CAAC;UACR8K,OAAO,EAAEa,KAAK,CAACb;QACjB,CAAC,CAAC;QACF,OAAOtD,IAAI,CAACyE,aAAa,CAACV,GAAG,EAAEK,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF,IAAIM,SAAS,GAAG;QACdlB,QAAQ,EAAElB,QAAQ,GAAG,gBAAgB,CAAC/B,MAAM,CAACgC,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAavD,KAAK,CAACyE,aAAa,CAACnE,KAAK,EAAEpG,QAAQ,CAAC;QACtDyL,SAAS,EAAE,oBAAoB;QAC/BpM,GAAG,EAAE;MACP,CAAC,EAAEmM,SAAS,EAAE;QACZE,IAAI,EAAE;MACR,CAAC,CAAC,EAAEV,IAAI,CAAC;IACX;EACF,CAAC,EAAE;IACD3L,GAAG,EAAE,uBAAuB;IAC5BqE,KAAK,EAAE,SAASiI,qBAAqBA,CAACnC,MAAM,EAAEJ,QAAQ,EAAEC,UAAU,EAAEvG,KAAK,EAAE;MACzE,IAAI8I,YAAY,GAAG,IAAI,CAAC9I,KAAK;QAC3B+I,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBlC,MAAM,GAAGiC,YAAY,CAACjC,MAAM;QAC5BmC,YAAY,GAAGF,YAAY,CAACE,YAAY;QACxCC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBC,MAAM,GAAGhN,wBAAwB,CAAC4M,YAAY,EAAEnN,SAAS,CAAC;MAC5D,IAAIwN,UAAU,GAAGlL,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,WAAW,CAACsF,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7FE,IAAI,EAAE,MAAM;QACZT,SAAS,EAAE,qBAAqB;QAChCnB,QAAQ,EAAElB,QAAQ,GAAG,gBAAgB,CAAC/B,MAAM,CAACgC,UAAU,EAAE,GAAG,CAAC,GAAG,IAAI;QACpEG,MAAM,EAAEA;MACV,CAAC,EAAE1G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACb+I,IAAI,EAAEA,IAAI;QACVlC,MAAM,EAAEA,MAAM;QACdmC,YAAY,EAAEA;MAChB,CAAC,CAAC;MACF,OAAO,aAAahG,KAAK,CAACyE,aAAa,CAACrE,KAAK,EAAElG,QAAQ,CAAC,CAAC,CAAC,EAAEiM,UAAU,EAAE;QACtEE,OAAO,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD9M,GAAG,EAAE,0BAA0B;IAC/BqE,KAAK,EAAE,SAAS0I,wBAAwBA,CAAChD,QAAQ,EAAEC,UAAU,EAAE;MAC7D,IAAIgD,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACxJ,KAAK;QAC3B0G,MAAM,GAAG8C,YAAY,CAAC9C,MAAM;QAC5B+C,eAAe,GAAGD,YAAY,CAACC,eAAe;QAC9CxD,iBAAiB,GAAGuD,YAAY,CAACvD,iBAAiB;QAClDyD,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,WAAW,GAAGL,YAAY,CAACK,WAAW;QACtCC,gBAAgB,GAAGN,YAAY,CAACM,gBAAgB;QAChDC,KAAK,GAAGP,YAAY,CAACO,KAAK;QAC1BC,MAAM,GAAGR,YAAY,CAACQ,MAAM;MAC9B,IAAIC,WAAW,GAAG,IAAI,CAACzD,KAAK;QAC1B0D,UAAU,GAAGD,WAAW,CAACC,UAAU;QACnCzF,WAAW,GAAGwF,WAAW,CAACxF,WAAW;MACvC,OAAO,aAAazB,KAAK,CAACyE,aAAa,CAACvE,OAAO,EAAE;QAC/CiH,KAAK,EAAET,cAAc;QACrBU,QAAQ,EAAET,iBAAiB;QAC3BU,QAAQ,EAAEpE,iBAAiB;QAC3BqE,MAAM,EAAEV,eAAe;QACvBtK,IAAI,EAAE;UACJiL,CAAC,EAAE;QACL,CAAC;QACDC,EAAE,EAAE;UACFD,CAAC,EAAE;QACL,CAAC;QACDhO,GAAG,EAAE,OAAO,CAACgI,MAAM,CAACsF,WAAW,CAAC;QAChC/D,cAAc,EAAE,IAAI,CAAC2E,kBAAkB;QACvC1E,gBAAgB,EAAE,IAAI,CAAC2E;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIJ,CAAC,GAAGI,IAAI,CAACJ,CAAC;QACd,IAAIL,UAAU,EAAE;UACd,IAAIU,oBAAoB,GAAGV,UAAU,CAACtN,MAAM,GAAG8J,MAAM,CAAC9J,MAAM;UAC5D,IAAIiO,QAAQ,GAAGnE,MAAM,CAAClB,GAAG,CAAC,UAAU2C,KAAK,EAAEK,KAAK,EAAE;YAChD,IAAIsC,cAAc,GAAG9F,IAAI,CAACC,KAAK,CAACuD,KAAK,GAAGoC,oBAAoB,CAAC;YAC7D,IAAIV,UAAU,CAACY,cAAc,CAAC,EAAE;cAC9B,IAAIC,IAAI,GAAGb,UAAU,CAACY,cAAc,CAAC;cACrC,IAAIE,aAAa,GAAGtH,iBAAiB,CAACqH,IAAI,CAAC5D,CAAC,EAAEgB,KAAK,CAAChB,CAAC,CAAC;cACtD,IAAI8D,aAAa,GAAGvH,iBAAiB,CAACqH,IAAI,CAAC3D,CAAC,EAAEe,KAAK,CAACf,CAAC,CAAC;cACtD,OAAOnJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDhB,CAAC,EAAE6D,aAAa,CAACT,CAAC,CAAC;gBACnBnD,CAAC,EAAE6D,aAAa,CAACV,CAAC;cACpB,CAAC,CAAC;YACJ;;YAEA;YACA,IAAIT,gBAAgB,EAAE;cACpB,IAAIoB,cAAc,GAAGxH,iBAAiB,CAACqG,KAAK,GAAG,CAAC,EAAE5B,KAAK,CAAChB,CAAC,CAAC;cAC1D,IAAIgE,cAAc,GAAGzH,iBAAiB,CAACsG,MAAM,GAAG,CAAC,EAAE7B,KAAK,CAACf,CAAC,CAAC;cAC3D,OAAOnJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDhB,CAAC,EAAE+D,cAAc,CAACX,CAAC,CAAC;gBACpBnD,CAAC,EAAE+D,cAAc,CAACZ,CAAC;cACrB,CAAC,CAAC;YACJ;YACA,OAAOtM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDhB,CAAC,EAAEgB,KAAK,CAAChB,CAAC;cACVC,CAAC,EAAEe,KAAK,CAACf;YACX,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,OAAOmC,MAAM,CAACV,qBAAqB,CAACgC,QAAQ,EAAEvE,QAAQ,EAAEC,UAAU,CAAC;QACrE;QACA,IAAI6E,YAAY,GAAG1H,iBAAiB,CAAC,CAAC,EAAEe,WAAW,CAAC;QACpD,IAAI4G,SAAS,GAAGD,YAAY,CAACb,CAAC,CAAC;QAC/B,IAAIe,sBAAsB;QAC1B,IAAI7B,eAAe,EAAE;UACnB,IAAI/E,KAAK,GAAG,EAAE,CAACH,MAAM,CAACkF,eAAe,CAAC,CAAC8B,KAAK,CAAC,WAAW,CAAC,CAAC/F,GAAG,CAAC,UAAUgG,GAAG,EAAE;YAC3E,OAAOC,UAAU,CAACD,GAAG,CAAC;UACxB,CAAC,CAAC;UACFF,sBAAsB,GAAG/B,MAAM,CAACmC,kBAAkB,CAACL,SAAS,EAAE5G,WAAW,EAAEC,KAAK,CAAC;QACnF,CAAC,MAAM;UACL4G,sBAAsB,GAAG,EAAE,CAAC/G,MAAM,CAAC8G,SAAS,EAAE,KAAK,CAAC,CAAC9G,MAAM,CAACE,WAAW,GAAG4G,SAAS,EAAE,IAAI,CAAC;QAC5F;QACA,OAAO9B,MAAM,CAACV,qBAAqB,CAACnC,MAAM,EAAEJ,QAAQ,EAAEC,UAAU,EAAE;UAChEkD,eAAe,EAAE6B;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/O,GAAG,EAAE,aAAa;IAClBqE,KAAK,EAAE,SAAS+K,WAAWA,CAACrF,QAAQ,EAAEC,UAAU,EAAE;MAChD,IAAIqF,YAAY,GAAG,IAAI,CAAC5L,KAAK;QAC3B0G,MAAM,GAAGkF,YAAY,CAAClF,MAAM;QAC5BT,iBAAiB,GAAG2F,YAAY,CAAC3F,iBAAiB;MACpD,IAAI4F,YAAY,GAAG,IAAI,CAACrF,KAAK;QAC3B0D,UAAU,GAAG2B,YAAY,CAAC3B,UAAU;QACpCzF,WAAW,GAAGoH,YAAY,CAACpH,WAAW;MACxC,IAAIwB,iBAAiB,IAAIS,MAAM,IAAIA,MAAM,CAAC9J,MAAM,KAAK,CAACsN,UAAU,IAAIzF,WAAW,GAAG,CAAC,IAAI,CAACjJ,QAAQ,CAAC0O,UAAU,EAAExD,MAAM,CAAC,CAAC,EAAE;QACrH,OAAO,IAAI,CAAC4C,wBAAwB,CAAChD,QAAQ,EAAEC,UAAU,CAAC;MAC5D;MACA,OAAO,IAAI,CAACsC,qBAAqB,CAACnC,MAAM,EAAEJ,QAAQ,EAAEC,UAAU,CAAC;IACjE;EACF,CAAC,EAAE;IACDhK,GAAG,EAAE,QAAQ;IACbqE,KAAK,EAAE,SAASkL,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC/L,KAAK;QAC3BgM,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBjE,GAAG,GAAGgE,YAAY,CAAChE,GAAG;QACtBrB,MAAM,GAAGqF,YAAY,CAACrF,MAAM;QAC5BiC,SAAS,GAAGoD,YAAY,CAACpD,SAAS;QAClChC,KAAK,GAAGoF,YAAY,CAACpF,KAAK;QAC1BC,KAAK,GAAGmF,YAAY,CAACnF,KAAK;QAC1BqF,GAAG,GAAGF,YAAY,CAACE,GAAG;QACtBC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBnC,KAAK,GAAGgC,YAAY,CAAChC,KAAK;QAC1BC,MAAM,GAAG+B,YAAY,CAAC/B,MAAM;QAC5B/D,iBAAiB,GAAG8F,YAAY,CAAC9F,iBAAiB;QAClDkG,EAAE,GAAGJ,YAAY,CAACI,EAAE;MACtB,IAAIH,IAAI,IAAI,CAACtF,MAAM,IAAI,CAACA,MAAM,CAAC9J,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAI4H,mBAAmB,GAAG,IAAI,CAACgC,KAAK,CAAChC,mBAAmB;MACxD,IAAI4H,cAAc,GAAG1F,MAAM,CAAC9J,MAAM,KAAK,CAAC;MACxC,IAAIyP,UAAU,GAAGlJ,UAAU,CAAC,eAAe,EAAEwF,SAAS,CAAC;MACvD,IAAIrC,QAAQ,GAAGK,KAAK,IAAIA,KAAK,CAAC2F,iBAAiB,IAAI1F,KAAK,IAAIA,KAAK,CAAC0F,iBAAiB;MACnF,IAAI/F,UAAU,GAAG7K,MAAM,CAACyQ,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MAC1C,OAAO,aAAanJ,KAAK,CAACyE,aAAa,CAACnE,KAAK,EAAE;QAC7CqF,SAAS,EAAE0D;MACb,CAAC,EAAE/F,QAAQ,GAAG,aAAatD,KAAK,CAACyE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAazE,KAAK,CAACyE,aAAa,CAAC,UAAU,EAAE;QACxG0E,EAAE,EAAE,WAAW,CAAC5H,MAAM,CAACgC,UAAU;MACnC,CAAC,EAAE,aAAavD,KAAK,CAACyE,aAAa,CAAC,MAAM,EAAE;QAC1CN,CAAC,EAAE+E,IAAI;QACP9E,CAAC,EAAE6E,GAAG;QACNlC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAACoC,cAAc,IAAI,IAAI,CAACT,WAAW,CAACrF,QAAQ,EAAEC,UAAU,CAAC,EAAE,IAAI,CAACF,cAAc,CAACC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC6F,cAAc,IAAIrE,GAAG,KAAK,IAAI,CAACF,UAAU,CAACvB,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,CAACN,iBAAiB,IAAIzB,mBAAmB,KAAKjB,SAAS,CAACgJ,kBAAkB,CAAC,IAAI,CAACvM,KAAK,EAAE0G,MAAM,CAAC,CAAC;IACvR;EACF,CAAC,CAAC,EAAE,CAAC;IACHnK,GAAG,EAAE,0BAA0B;IAC/BqE,KAAK,EAAE,SAAS4L,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC5C,WAAW,KAAK6C,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAC5C,WAAW;UACtC+C,SAAS,EAAEH,SAAS,CAAC/F,MAAM;UAC3BwD,UAAU,EAAEwC,SAAS,CAACE;QACxB,CAAC;MACH;MACA,IAAIH,SAAS,CAAC/F,MAAM,KAAKgG,SAAS,CAACE,SAAS,EAAE;QAC5C,OAAO;UACLA,SAAS,EAAEH,SAAS,CAAC/F;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDnK,GAAG,EAAE,QAAQ;IACbqE,KAAK,EAAE,SAAS2E,MAAMA,CAACb,KAAK,EAAEK,KAAK,EAAE;MACnC,IAAI8H,SAAS,GAAGnI,KAAK,CAAC9H,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC2H,MAAM,CAAChG,kBAAkB,CAACmG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK;MAC1F,IAAIlD,MAAM,GAAG,EAAE;MACf,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,KAAK,EAAE,EAAEvI,CAAC,EAAE;QAC9BgF,MAAM,GAAG,EAAE,CAAC+C,MAAM,CAAChG,kBAAkB,CAACiD,MAAM,CAAC,EAAEjD,kBAAkB,CAACsO,SAAS,CAAC,CAAC;MAC/E;MACA,OAAOrL,MAAM;IACf;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,eAAe;IACpBqE,KAAK,EAAE,SAAS6H,aAAaA,CAACqE,MAAM,EAAE9M,KAAK,EAAE;MAC3C,IAAI+M,OAAO;MACX,IAAK,aAAa/J,KAAK,CAACgK,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,OAAO,GAAG,aAAa/J,KAAK,CAAC2E,YAAY,CAACmF,MAAM,EAAE9M,KAAK,CAAC;MAC1D,CAAC,MAAM,IAAIvE,WAAW,CAACqR,MAAM,CAAC,EAAE;QAC9BC,OAAO,GAAGD,MAAM,CAAC9M,KAAK,CAAC;MACzB,CAAC,MAAM;QACL,IAAI2I,SAAS,GAAGxF,UAAU,CAAC,mBAAmB,EAAE2J,MAAM,GAAGA,MAAM,CAACnE,SAAS,GAAG,EAAE,CAAC;QAC/EoE,OAAO,GAAG,aAAa/J,KAAK,CAACyE,aAAa,CAACpE,GAAG,EAAEnG,QAAQ,CAAC,CAAC,CAAC,EAAE8C,KAAK,EAAE;UAClE2I,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;MACL;MACA,OAAOoE,OAAO;IAChB;EACF,CAAC,CAAC,CAAC;EACH,OAAO/I,IAAI;AACb,CAAC,CAACf,aAAa,CAAC;AAChB9E,eAAe,CAAC6F,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5C7F,eAAe,CAAC6F,IAAI,EAAE,cAAc,EAAE;EACpCiJ,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVlE,YAAY,EAAE,KAAK;EACnBmE,SAAS,EAAE,IAAI;EACfpF,GAAG,EAAE,IAAI;EACTqF,UAAU,EAAE,MAAM;EAClBC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,CAAC;EACdlE,IAAI,EAAE,MAAM;EACZ1C,MAAM,EAAE,EAAE;EACVT,iBAAiB,EAAE,CAACpC,MAAM,CAAC0J,KAAK;EAChCzD,gBAAgB,EAAE,IAAI;EACtBJ,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBoC,IAAI,EAAE,KAAK;EACXwB,KAAK,EAAE;AACT,CAAC,CAAC;AACFrP,eAAe,CAAC6F,IAAI,EAAE,iBAAiB,EAAE,UAAUyJ,KAAK,EAAE;EACxD,IAAIzN,KAAK,GAAGyN,KAAK,CAACzN,KAAK;IACrB2G,KAAK,GAAG8G,KAAK,CAAC9G,KAAK;IACnBC,KAAK,GAAG6G,KAAK,CAAC7G,KAAK;IACnB8G,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BzG,OAAO,GAAGuG,KAAK,CAACvG,OAAO;IACvB0G,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAIjH,MAAM,GAAG7G,KAAK,CAAC6G,MAAM;EACzB,IAAIH,MAAM,GAAGmH,aAAa,CAACrI,GAAG,CAAC,UAAU2C,KAAK,EAAEK,KAAK,EAAE;IACrD,IAAI5H,KAAK,GAAGmD,iBAAiB,CAACoE,KAAK,EAAEjB,OAAO,CAAC;IAC7C,IAAIL,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLM,CAAC,EAAErD,uBAAuB,CAAC;UACzBiK,IAAI,EAAEpH,KAAK;UACXqH,KAAK,EAAEN,UAAU;UACjBE,QAAQ,EAAEA,QAAQ;UAClBzF,KAAK,EAAEA,KAAK;UACZK,KAAK,EAAEA;QACT,CAAC,CAAC;QACFpB,CAAC,EAAE1L,MAAM,CAACkF,KAAK,CAAC,GAAG,IAAI,GAAGgG,KAAK,CAACqH,KAAK,CAACrN,KAAK,CAAC;QAC5CA,KAAK,EAAEA,KAAK;QACZ0G,OAAO,EAAEa;MACX,CAAC;IACH;IACA,OAAO;MACLhB,CAAC,EAAEzL,MAAM,CAACkF,KAAK,CAAC,GAAG,IAAI,GAAG+F,KAAK,CAACsH,KAAK,CAACrN,KAAK,CAAC;MAC5CwG,CAAC,EAAEtD,uBAAuB,CAAC;QACzBiK,IAAI,EAAEnH,KAAK;QACXoH,KAAK,EAAEL,UAAU;QACjBC,QAAQ,EAAEA,QAAQ;QAClBzF,KAAK,EAAEA,KAAK;QACZK,KAAK,EAAEA;MACT,CAAC,CAAC;MACF5H,KAAK,EAAEA,KAAK;MACZ0G,OAAO,EAAEa;IACX,CAAC;EACH,CAAC,CAAC;EACF,OAAOlK,aAAa,CAAC;IACnByI,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAEA;EACV,CAAC,EAAEiH,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}