{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nmodule.exports = utils.isStandardBrowserEnv() ?\n// Standard browser envs support document.cookie\nfunction standardBrowserEnv() {\n  return {\n    write: function write(name, value, expires, path, domain, secure) {\n      var cookie = [];\n      cookie.push(name + '=' + encodeURIComponent(value));\n      if (utils.isNumber(expires)) {\n        cookie.push('expires=' + new Date(expires).toGMTString());\n      }\n      if (utils.isString(path)) {\n        cookie.push('path=' + path);\n      }\n      if (utils.isString(domain)) {\n        cookie.push('domain=' + domain);\n      }\n      if (secure === true) {\n        cookie.push('secure');\n      }\n      document.cookie = cookie.join('; ');\n    },\n    read: function read(name) {\n      var match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return match ? decodeURIComponent(match[3]) : null;\n    },\n    remove: function remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  };\n}() :\n// Non standard browser env (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n  return {\n    write: function write() {},\n    read: function read() {\n      return null;\n    },\n    remove: function remove() {}\n  };\n}();", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "isStandardBrowserEnv", "standardBrowserEnv", "write", "name", "value", "expires", "path", "domain", "secure", "cookie", "push", "encodeURIComponent", "isNumber", "Date", "toGMTString", "isString", "document", "join", "read", "match", "RegExp", "decodeURIComponent", "remove", "now", "nonStandardBrowserEnv"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/helpers/cookies.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs support document.cookie\n    (function standardBrowserEnv() {\n      return {\n        write: function write(name, value, expires, path, domain, secure) {\n          var cookie = [];\n          cookie.push(name + '=' + encodeURIComponent(value));\n\n          if (utils.isNumber(expires)) {\n            cookie.push('expires=' + new Date(expires).toGMTString());\n          }\n\n          if (utils.isString(path)) {\n            cookie.push('path=' + path);\n          }\n\n          if (utils.isString(domain)) {\n            cookie.push('domain=' + domain);\n          }\n\n          if (secure === true) {\n            cookie.push('secure');\n          }\n\n          document.cookie = cookie.join('; ');\n        },\n\n        read: function read(name) {\n          var match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n          return (match ? decodeURIComponent(match[3]) : null);\n        },\n\n        remove: function remove(name) {\n          this.write(name, '', Date.now() - 86400000);\n        }\n      };\n    })() :\n\n  // Non standard browser env (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return {\n        write: function write() {},\n        read: function read() { return null; },\n        remove: function remove() {}\n      };\n    })()\n);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjCC,MAAM,CAACC,OAAO,GACZH,KAAK,CAACI,oBAAoB,CAAC,CAAC;AAE5B;AACG,SAASC,kBAAkBA,CAAA,EAAG;EAC7B,OAAO;IACLC,KAAK,EAAE,SAASA,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;MAChE,IAAIC,MAAM,GAAG,EAAE;MACfA,MAAM,CAACC,IAAI,CAACP,IAAI,GAAG,GAAG,GAAGQ,kBAAkB,CAACP,KAAK,CAAC,CAAC;MAEnD,IAAIR,KAAK,CAACgB,QAAQ,CAACP,OAAO,CAAC,EAAE;QAC3BI,MAAM,CAACC,IAAI,CAAC,UAAU,GAAG,IAAIG,IAAI,CAACR,OAAO,CAAC,CAACS,WAAW,CAAC,CAAC,CAAC;MAC3D;MAEA,IAAIlB,KAAK,CAACmB,QAAQ,CAACT,IAAI,CAAC,EAAE;QACxBG,MAAM,CAACC,IAAI,CAAC,OAAO,GAAGJ,IAAI,CAAC;MAC7B;MAEA,IAAIV,KAAK,CAACmB,QAAQ,CAACR,MAAM,CAAC,EAAE;QAC1BE,MAAM,CAACC,IAAI,CAAC,SAAS,GAAGH,MAAM,CAAC;MACjC;MAEA,IAAIC,MAAM,KAAK,IAAI,EAAE;QACnBC,MAAM,CAACC,IAAI,CAAC,QAAQ,CAAC;MACvB;MAEAM,QAAQ,CAACP,MAAM,GAAGA,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC;IAEDC,IAAI,EAAE,SAASA,IAAIA,CAACf,IAAI,EAAE;MACxB,IAAIgB,KAAK,GAAGH,QAAQ,CAACP,MAAM,CAACU,KAAK,CAAC,IAAIC,MAAM,CAAC,YAAY,GAAGjB,IAAI,GAAG,WAAW,CAAC,CAAC;MAChF,OAAQgB,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IACrD,CAAC;IAEDG,MAAM,EAAE,SAASA,MAAMA,CAACnB,IAAI,EAAE;MAC5B,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE,EAAE,EAAEU,IAAI,CAACU,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IAC7C;EACF,CAAC;AACH,CAAC,CAAE,CAAC;AAEN;AACG,SAASC,qBAAqBA,CAAA,EAAG;EAChC,OAAO;IACLtB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG,CAAC,CAAC;IAC1BgB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MAAE,OAAO,IAAI;IAAE,CAAC;IACtCI,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG,CAAC;EAC7B,CAAC;AACH,CAAC,CAAE,CACN", "ignoreList": []}, "metadata": {}, "sourceType": "script"}