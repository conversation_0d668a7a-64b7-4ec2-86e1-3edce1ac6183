{"ast": null, "code": "import { useRef } from 'react';\nimport warning from \"rc-util/es/warning\";\n/**\n * Keep input cursor in the correct position if possible.\n * Is this necessary since we have `formatter` which may mass the content?\n */\nexport default function useCursor(input, focused) {\n  var selectionRef = useRef(null);\n  function recordCursor() {\n    // Record position\n    try {\n      var start = input.selectionStart,\n        end = input.selectionEnd,\n        value = input.value;\n      var beforeTxt = value.substring(0, start);\n      var afterTxt = value.substring(end);\n      selectionRef.current = {\n        start: start,\n        end: end,\n        value: value,\n        beforeTxt: beforeTxt,\n        afterTxt: afterTxt\n      };\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  }\n  /**\n   * Restore logic:\n   *  1. back string same\n   *  2. start string same\n   */\n  function restoreCursor() {\n    if (input && selectionRef.current && focused) {\n      try {\n        var value = input.value;\n        var _selectionRef$current = selectionRef.current,\n          beforeTxt = _selectionRef$current.beforeTxt,\n          afterTxt = _selectionRef$current.afterTxt,\n          start = _selectionRef$current.start;\n        var startPos = value.length;\n        if (value.endsWith(afterTxt)) {\n          startPos = value.length - selectionRef.current.afterTxt.length;\n        } else if (value.startsWith(beforeTxt)) {\n          startPos = beforeTxt.length;\n        } else {\n          var beforeLastChar = beforeTxt[start - 1];\n          var newIndex = value.indexOf(beforeLastChar, start - 1);\n          if (newIndex !== -1) {\n            startPos = newIndex + 1;\n          }\n        }\n        input.setSelectionRange(startPos, startPos);\n      } catch (e) {\n        warning(false, \"Something warning of cursor restore. Please fire issue about this: \".concat(e.message));\n      }\n    }\n  }\n  return [recordCursor, restoreCursor];\n}", "map": {"version": 3, "names": ["useRef", "warning", "useCursor", "input", "focused", "selectionRef", "recordCursor", "start", "selectionStart", "end", "selectionEnd", "value", "beforeTxt", "substring", "afterTxt", "current", "e", "restoreCursor", "_selectionRef$current", "startPos", "length", "endsWith", "startsWith", "beforeLastChar", "newIndex", "indexOf", "setSelectionRange", "concat", "message"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-input-number/es/hooks/useCursor.js"], "sourcesContent": ["import { useRef } from 'react';\nimport warning from \"rc-util/es/warning\";\n/**\n * Keep input cursor in the correct position if possible.\n * Is this necessary since we have `formatter` which may mass the content?\n */\nexport default function useCursor(input, focused) {\n  var selectionRef = useRef(null);\n  function recordCursor() {\n    // Record position\n    try {\n      var start = input.selectionStart,\n        end = input.selectionEnd,\n        value = input.value;\n      var beforeTxt = value.substring(0, start);\n      var afterTxt = value.substring(end);\n      selectionRef.current = {\n        start: start,\n        end: end,\n        value: value,\n        beforeTxt: beforeTxt,\n        afterTxt: afterTxt\n      };\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  }\n  /**\n   * Restore logic:\n   *  1. back string same\n   *  2. start string same\n   */\n  function restoreCursor() {\n    if (input && selectionRef.current && focused) {\n      try {\n        var value = input.value;\n        var _selectionRef$current = selectionRef.current,\n          beforeTxt = _selectionRef$current.beforeTxt,\n          afterTxt = _selectionRef$current.afterTxt,\n          start = _selectionRef$current.start;\n        var startPos = value.length;\n        if (value.endsWith(afterTxt)) {\n          startPos = value.length - selectionRef.current.afterTxt.length;\n        } else if (value.startsWith(beforeTxt)) {\n          startPos = beforeTxt.length;\n        } else {\n          var beforeLastChar = beforeTxt[start - 1];\n          var newIndex = value.indexOf(beforeLastChar, start - 1);\n          if (newIndex !== -1) {\n            startPos = newIndex + 1;\n          }\n        }\n        input.setSelectionRange(startPos, startPos);\n      } catch (e) {\n        warning(false, \"Something warning of cursor restore. Please fire issue about this: \".concat(e.message));\n      }\n    }\n  }\n  return [recordCursor, restoreCursor];\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAChD,IAAIC,YAAY,GAAGL,MAAM,CAAC,IAAI,CAAC;EAC/B,SAASM,YAAYA,CAAA,EAAG;IACtB;IACA,IAAI;MACF,IAAIC,KAAK,GAAGJ,KAAK,CAACK,cAAc;QAC9BC,GAAG,GAAGN,KAAK,CAACO,YAAY;QACxBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;MACrB,IAAIC,SAAS,GAAGD,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEN,KAAK,CAAC;MACzC,IAAIO,QAAQ,GAAGH,KAAK,CAACE,SAAS,CAACJ,GAAG,CAAC;MACnCJ,YAAY,CAACU,OAAO,GAAG;QACrBR,KAAK,EAAEA,KAAK;QACZE,GAAG,EAAEA,GAAG;QACRE,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBE,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV;MACA;MACA;IAAA;EAEJ;EACA;AACF;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAAA,EAAG;IACvB,IAAId,KAAK,IAAIE,YAAY,CAACU,OAAO,IAAIX,OAAO,EAAE;MAC5C,IAAI;QACF,IAAIO,KAAK,GAAGR,KAAK,CAACQ,KAAK;QACvB,IAAIO,qBAAqB,GAAGb,YAAY,CAACU,OAAO;UAC9CH,SAAS,GAAGM,qBAAqB,CAACN,SAAS;UAC3CE,QAAQ,GAAGI,qBAAqB,CAACJ,QAAQ;UACzCP,KAAK,GAAGW,qBAAqB,CAACX,KAAK;QACrC,IAAIY,QAAQ,GAAGR,KAAK,CAACS,MAAM;QAC3B,IAAIT,KAAK,CAACU,QAAQ,CAACP,QAAQ,CAAC,EAAE;UAC5BK,QAAQ,GAAGR,KAAK,CAACS,MAAM,GAAGf,YAAY,CAACU,OAAO,CAACD,QAAQ,CAACM,MAAM;QAChE,CAAC,MAAM,IAAIT,KAAK,CAACW,UAAU,CAACV,SAAS,CAAC,EAAE;UACtCO,QAAQ,GAAGP,SAAS,CAACQ,MAAM;QAC7B,CAAC,MAAM;UACL,IAAIG,cAAc,GAAGX,SAAS,CAACL,KAAK,GAAG,CAAC,CAAC;UACzC,IAAIiB,QAAQ,GAAGb,KAAK,CAACc,OAAO,CAACF,cAAc,EAAEhB,KAAK,GAAG,CAAC,CAAC;UACvD,IAAIiB,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnBL,QAAQ,GAAGK,QAAQ,GAAG,CAAC;UACzB;QACF;QACArB,KAAK,CAACuB,iBAAiB,CAACP,QAAQ,EAAEA,QAAQ,CAAC;MAC7C,CAAC,CAAC,OAAOH,CAAC,EAAE;QACVf,OAAO,CAAC,KAAK,EAAE,qEAAqE,CAAC0B,MAAM,CAACX,CAAC,CAACY,OAAO,CAAC,CAAC;MACzG;IACF;EACF;EACA,OAAO,CAACtB,YAAY,EAAEW,aAAa,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}