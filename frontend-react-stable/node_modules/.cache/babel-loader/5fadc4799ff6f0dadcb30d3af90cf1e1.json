{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { Field, FieldContext, ListContext } from 'rc-field-form';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useFormItemStatus from '../hooks/useFormItemStatus';\nimport { ConfigContext } from '../../config-provider';\nimport { cloneElement, isValidElement } from '../../_util/reactNode';\nimport { tuple } from '../../_util/type';\nimport warning from '../../_util/warning';\nimport { FormContext, NoStyleItemContext } from '../context';\nimport useFrameState from '../hooks/useFrameState';\nimport useItemRef from '../hooks/useItemRef';\nimport { getFieldId, toArray } from '../util';\nimport ItemHolder from './ItemHolder';\nvar NAME_SPLIT = '__SPLIT__';\nvar ValidateStatuses = tuple('success', 'warning', 'error', 'validating', '');\nvar MemoInput = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  return prev.value === next.value && prev.update === next.update && prev.childProps.length === next.childProps.length && prev.childProps.every(function (value, index) {\n    return value === next.childProps[index];\n  });\n});\nfunction hasValidName(name) {\n  if (name === null) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', '`null` is passed as `name` property') : void 0;\n  }\n  return !(name === undefined || name === null);\n}\nfunction genEmptyMeta() {\n  return {\n    errors: [],\n    warnings: [],\n    touched: false,\n    validating: false,\n    name: []\n  };\n}\nfunction InternalFormItem(props) {\n  var name = props.name,\n    noStyle = props.noStyle,\n    dependencies = props.dependencies,\n    customizePrefixCls = props.prefixCls,\n    shouldUpdate = props.shouldUpdate,\n    rules = props.rules,\n    children = props.children,\n    required = props.required,\n    label = props.label,\n    messageVariables = props.messageVariables,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? 'onChange' : _props$trigger,\n    validateTrigger = props.validateTrigger,\n    hidden = props.hidden;\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var _useContext2 = useContext(FormContext),\n    formName = _useContext2.name;\n  var isRenderProps = typeof children === 'function';\n  var notifyParentMetaChange = useContext(NoStyleItemContext);\n  var _useContext3 = useContext(FieldContext),\n    contextValidateTrigger = _useContext3.validateTrigger;\n  var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;\n  var hasName = hasValidName(name);\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  // ========================= MISC =========================\n  // Get `noStyle` required info\n  var listContext = React.useContext(ListContext);\n  var fieldKeyPathRef = React.useRef();\n  // ======================== Errors ========================\n  // >>>>> Collect sub field errors\n  var _useFrameState = useFrameState({}),\n    _useFrameState2 = _slicedToArray(_useFrameState, 2),\n    subFieldErrors = _useFrameState2[0],\n    setSubFieldErrors = _useFrameState2[1];\n  // >>>>> Current field errors\n  var _useState = useState(function () {\n      return genEmptyMeta();\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    meta = _useState2[0],\n    setMeta = _useState2[1];\n  var onMetaChange = function onMetaChange(nextMeta) {\n    // This keyInfo is not correct when field is removed\n    // Since origin keyManager no longer keep the origin key anymore\n    // Which means we need cache origin one and reuse when removed\n    var keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name);\n    // Destroy will reset all the meta\n    setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true);\n    // Bump to parent since noStyle\n    if (noStyle && notifyParentMetaChange) {\n      var namePath = nextMeta.name;\n      if (!nextMeta.destroy) {\n        if (keyInfo !== undefined) {\n          var _keyInfo = _slicedToArray(keyInfo, 2),\n            fieldKey = _keyInfo[0],\n            restPath = _keyInfo[1];\n          namePath = [fieldKey].concat(_toConsumableArray(restPath));\n          fieldKeyPathRef.current = namePath;\n        }\n      } else {\n        // Use origin cache data\n        namePath = fieldKeyPathRef.current || namePath;\n      }\n      notifyParentMetaChange(nextMeta, namePath);\n    }\n  };\n  // >>>>> Collect noStyle Field error to the top FormItem\n  var onSubItemMetaChange = function onSubItemMetaChange(subMeta, uniqueKeys) {\n    // Only `noStyle` sub item will trigger\n    setSubFieldErrors(function (prevSubFieldErrors) {\n      var clone = _extends({}, prevSubFieldErrors);\n      // name: ['user', 1] + key: [4] = ['user', 4]\n      var mergedNamePath = [].concat(_toConsumableArray(subMeta.name.slice(0, -1)), _toConsumableArray(uniqueKeys));\n      var mergedNameKey = mergedNamePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        // Remove\n        delete clone[mergedNameKey];\n      } else {\n        // Update\n        clone[mergedNameKey] = subMeta;\n      }\n      return clone;\n    });\n  };\n  // >>>>> Get merged errors\n  var _React$useMemo = React.useMemo(function () {\n      var errorList = _toConsumableArray(meta.errors);\n      var warningList = _toConsumableArray(meta.warnings);\n      Object.values(subFieldErrors).forEach(function (subFieldError) {\n        errorList.push.apply(errorList, _toConsumableArray(subFieldError.errors || []));\n        warningList.push.apply(warningList, _toConsumableArray(subFieldError.warnings || []));\n      });\n      return [errorList, warningList];\n    }, [subFieldErrors, meta.errors, meta.warnings]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    mergedErrors = _React$useMemo2[0],\n    mergedWarnings = _React$useMemo2[1];\n  // ===================== Children Ref =====================\n  var getItemRef = useItemRef();\n  // ======================== Render ========================\n  function renderLayout(baseChildren, fieldId, isRequired) {\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n    return /*#__PURE__*/React.createElement(ItemHolder, _extends({\n      key: \"row\"\n    }, props, {\n      prefixCls: prefixCls,\n      fieldId: fieldId,\n      isRequired: isRequired,\n      errors: mergedErrors,\n      warnings: mergedWarnings,\n      meta: meta,\n      onSubItemMetaChange: onSubItemMetaChange\n    }), baseChildren);\n  }\n  if (!hasName && !isRenderProps && !dependencies) {\n    return renderLayout(children);\n  }\n  var variables = {};\n  if (typeof label === 'string') {\n    variables.label = label;\n  } else if (name) {\n    variables.label = String(name);\n  }\n  if (messageVariables) {\n    variables = _extends(_extends({}, variables), messageVariables);\n  }\n  // >>>>> With Field\n  return /*#__PURE__*/React.createElement(Field, _extends({}, props, {\n    messageVariables: variables,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange\n  }), function (control, renderMeta, context) {\n    var mergedName = toArray(name).length && renderMeta ? renderMeta.name : [];\n    var fieldId = getFieldId(mergedName, formName);\n    var isRequired = required !== undefined ? required : !!(rules && rules.some(function (rule) {\n      if (rule && _typeof(rule) === 'object' && rule.required && !rule.warningOnly) {\n        return true;\n      }\n      if (typeof rule === 'function') {\n        var ruleEntity = rule(context);\n        return ruleEntity && ruleEntity.required && !ruleEntity.warningOnly;\n      }\n      return false;\n    }));\n    // ======================= Children =======================\n    var mergedControl = _extends({}, control);\n    var childNode = null;\n    process.env.NODE_ENV !== \"production\" ? warning(!(shouldUpdate && dependencies), 'Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together. See https://ant.design/components/form/#dependencies.\") : void 0;\n    if (Array.isArray(children) && hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', '`children` is array of render props cannot have `name`.') : void 0;\n      childNode = children;\n    } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {\n      process.env.NODE_ENV !== \"production\" ? warning(!!(shouldUpdate || dependencies), 'Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.') : void 0;\n      process.env.NODE_ENV !== \"production\" ? warning(!hasName, 'Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\") : void 0;\n    } else if (dependencies && !isRenderProps && !hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', 'Must set `name` or use render props when `dependencies` is set.') : void 0;\n    } else if (isValidElement(children)) {\n      process.env.NODE_ENV !== \"production\" ? warning(children.props.defaultValue === undefined, 'Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.') : void 0;\n      var childProps = _extends(_extends({}, children.props), mergedControl);\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      if (props.help || mergedErrors.length > 0 || mergedWarnings.length > 0 || props.extra) {\n        var describedbyArr = [];\n        if (props.help || mergedErrors.length > 0) {\n          describedbyArr.push(\"\".concat(fieldId, \"_help\"));\n        }\n        if (props.extra) {\n          describedbyArr.push(\"\".concat(fieldId, \"_extra\"));\n        }\n        childProps['aria-describedby'] = describedbyArr.join(' ');\n      }\n      if (mergedErrors.length > 0) {\n        childProps['aria-invalid'] = 'true';\n      }\n      if (isRequired) {\n        childProps['aria-required'] = 'true';\n      }\n      if (supportRef(children)) {\n        childProps.ref = getItemRef(mergedName, children);\n      }\n      // We should keep user origin event handler\n      var triggers = new Set([].concat(_toConsumableArray(toArray(trigger)), _toConsumableArray(toArray(mergedValidateTrigger))));\n      triggers.forEach(function (eventName) {\n        childProps[eventName] = function () {\n          var _a2, _c2;\n          var _a, _b, _c;\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [mergedControl].concat(args));\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [_b].concat(args));\n        };\n      });\n      // List of props that need to be watched for changes -> if changes are detected in MemoInput -> rerender\n      var watchingChildProps = [childProps['aria-required'], childProps['aria-invalid'], childProps['aria-describedby']];\n      childNode = /*#__PURE__*/React.createElement(MemoInput, {\n        value: mergedControl[props.valuePropName || 'value'],\n        update: children,\n        childProps: watchingChildProps\n      }, cloneElement(children, childProps));\n    } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {\n      childNode = children(context);\n    } else {\n      process.env.NODE_ENV !== \"production\" ? warning(!mergedName.length, 'Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.') : void 0;\n      childNode = children;\n    }\n    return renderLayout(childNode, fieldId, isRequired);\n  });\n}\nvar FormItem = InternalFormItem;\nFormItem.useStatus = useFormItemStatus;\nexport default FormItem;", "map": {"version": 3, "names": ["_typeof", "_extends", "_toConsumableArray", "_slicedToArray", "Field", "FieldContext", "ListContext", "useState", "supportRef", "React", "useContext", "useFormItemStatus", "ConfigContext", "cloneElement", "isValidElement", "tuple", "warning", "FormContext", "NoStyleItemContext", "useFrameState", "useItemRef", "getFieldId", "toArray", "ItemHolder", "NAME_SPLIT", "ValidateStatuses", "MemoInput", "memo", "_ref", "children", "prev", "next", "value", "update", "childProps", "length", "every", "index", "hasValidName", "name", "process", "env", "NODE_ENV", "undefined", "genEmptyMeta", "errors", "warnings", "touched", "validating", "InternalFormItem", "props", "noStyle", "dependencies", "customizePrefixCls", "prefixCls", "shouldUpdate", "rules", "required", "label", "messageVariables", "_props$trigger", "trigger", "validate<PERSON><PERSON>ger", "hidden", "_useContext", "getPrefixCls", "_useContext2", "formName", "isRenderProps", "notifyParentMetaChange", "_useContext3", "contextValidateTrigger", "mergedValidateTrigger", "<PERSON><PERSON><PERSON>", "listContext", "fieldKeyPathRef", "useRef", "_useFrameState", "_useFrameState2", "subFieldErrors", "setSubFieldErrors", "_useState", "_useState2", "meta", "setMeta", "onMetaChange", "nextMeta", "keyInfo", "<PERSON><PERSON><PERSON>", "destroy", "namePath", "_keyInfo", "<PERSON><PERSON><PERSON>", "restPath", "concat", "current", "onSubItemMetaChange", "subMeta", "uniqueKeys", "prevSubFieldErrors", "clone", "mergedNamePath", "slice", "mergedNameKey", "join", "_React$useMemo", "useMemo", "errorList", "warningList", "Object", "values", "for<PERSON>ach", "subFieldError", "push", "apply", "_React$useMemo2", "mergedErrors", "mergedWarnings", "getItemRef", "renderLayout", "baseChildren", "fieldId", "isRequired", "createElement", "key", "variables", "String", "control", "renderMeta", "context", "mergedName", "some", "rule", "warningOnly", "ruleEntity", "mergedControl", "childNode", "Array", "isArray", "defaultValue", "id", "help", "extra", "<PERSON><PERSON><PERSON><PERSON>", "ref", "triggers", "Set", "eventName", "_a2", "_c2", "_a", "_b", "_c", "_len", "arguments", "args", "_key", "call", "watchingChildProps", "valuePropName", "FormItem", "useStatus"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/FormItem/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { Field, FieldContext, ListContext } from 'rc-field-form';\nimport useState from \"rc-util/es/hooks/useState\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useFormItemStatus from '../hooks/useFormItemStatus';\nimport { ConfigContext } from '../../config-provider';\nimport { cloneElement, isValidElement } from '../../_util/reactNode';\nimport { tuple } from '../../_util/type';\nimport warning from '../../_util/warning';\nimport { FormContext, NoStyleItemContext } from '../context';\nimport useFrameState from '../hooks/useFrameState';\nimport useItemRef from '../hooks/useItemRef';\nimport { getFieldId, toArray } from '../util';\nimport ItemHolder from './ItemHolder';\nvar NAME_SPLIT = '__SPLIT__';\nvar ValidateStatuses = tuple('success', 'warning', 'error', 'validating', '');\nvar MemoInput = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (prev, next) {\n  return prev.value === next.value && prev.update === next.update && prev.childProps.length === next.childProps.length && prev.childProps.every(function (value, index) {\n    return value === next.childProps[index];\n  });\n});\nfunction hasValidName(name) {\n  if (name === null) {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', '`null` is passed as `name` property') : void 0;\n  }\n  return !(name === undefined || name === null);\n}\nfunction genEmptyMeta() {\n  return {\n    errors: [],\n    warnings: [],\n    touched: false,\n    validating: false,\n    name: []\n  };\n}\nfunction InternalFormItem(props) {\n  var name = props.name,\n    noStyle = props.noStyle,\n    dependencies = props.dependencies,\n    customizePrefixCls = props.prefixCls,\n    shouldUpdate = props.shouldUpdate,\n    rules = props.rules,\n    children = props.children,\n    required = props.required,\n    label = props.label,\n    messageVariables = props.messageVariables,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? 'onChange' : _props$trigger,\n    validateTrigger = props.validateTrigger,\n    hidden = props.hidden;\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var _useContext2 = useContext(FormContext),\n    formName = _useContext2.name;\n  var isRenderProps = typeof children === 'function';\n  var notifyParentMetaChange = useContext(NoStyleItemContext);\n  var _useContext3 = useContext(FieldContext),\n    contextValidateTrigger = _useContext3.validateTrigger;\n  var mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;\n  var hasName = hasValidName(name);\n  var prefixCls = getPrefixCls('form', customizePrefixCls);\n  // ========================= MISC =========================\n  // Get `noStyle` required info\n  var listContext = React.useContext(ListContext);\n  var fieldKeyPathRef = React.useRef();\n  // ======================== Errors ========================\n  // >>>>> Collect sub field errors\n  var _useFrameState = useFrameState({}),\n    _useFrameState2 = _slicedToArray(_useFrameState, 2),\n    subFieldErrors = _useFrameState2[0],\n    setSubFieldErrors = _useFrameState2[1];\n  // >>>>> Current field errors\n  var _useState = useState(function () {\n      return genEmptyMeta();\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    meta = _useState2[0],\n    setMeta = _useState2[1];\n  var onMetaChange = function onMetaChange(nextMeta) {\n    // This keyInfo is not correct when field is removed\n    // Since origin keyManager no longer keep the origin key anymore\n    // Which means we need cache origin one and reuse when removed\n    var keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name);\n    // Destroy will reset all the meta\n    setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true);\n    // Bump to parent since noStyle\n    if (noStyle && notifyParentMetaChange) {\n      var namePath = nextMeta.name;\n      if (!nextMeta.destroy) {\n        if (keyInfo !== undefined) {\n          var _keyInfo = _slicedToArray(keyInfo, 2),\n            fieldKey = _keyInfo[0],\n            restPath = _keyInfo[1];\n          namePath = [fieldKey].concat(_toConsumableArray(restPath));\n          fieldKeyPathRef.current = namePath;\n        }\n      } else {\n        // Use origin cache data\n        namePath = fieldKeyPathRef.current || namePath;\n      }\n      notifyParentMetaChange(nextMeta, namePath);\n    }\n  };\n  // >>>>> Collect noStyle Field error to the top FormItem\n  var onSubItemMetaChange = function onSubItemMetaChange(subMeta, uniqueKeys) {\n    // Only `noStyle` sub item will trigger\n    setSubFieldErrors(function (prevSubFieldErrors) {\n      var clone = _extends({}, prevSubFieldErrors);\n      // name: ['user', 1] + key: [4] = ['user', 4]\n      var mergedNamePath = [].concat(_toConsumableArray(subMeta.name.slice(0, -1)), _toConsumableArray(uniqueKeys));\n      var mergedNameKey = mergedNamePath.join(NAME_SPLIT);\n      if (subMeta.destroy) {\n        // Remove\n        delete clone[mergedNameKey];\n      } else {\n        // Update\n        clone[mergedNameKey] = subMeta;\n      }\n      return clone;\n    });\n  };\n  // >>>>> Get merged errors\n  var _React$useMemo = React.useMemo(function () {\n      var errorList = _toConsumableArray(meta.errors);\n      var warningList = _toConsumableArray(meta.warnings);\n      Object.values(subFieldErrors).forEach(function (subFieldError) {\n        errorList.push.apply(errorList, _toConsumableArray(subFieldError.errors || []));\n        warningList.push.apply(warningList, _toConsumableArray(subFieldError.warnings || []));\n      });\n      return [errorList, warningList];\n    }, [subFieldErrors, meta.errors, meta.warnings]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    mergedErrors = _React$useMemo2[0],\n    mergedWarnings = _React$useMemo2[1];\n  // ===================== Children Ref =====================\n  var getItemRef = useItemRef();\n  // ======================== Render ========================\n  function renderLayout(baseChildren, fieldId, isRequired) {\n    if (noStyle && !hidden) {\n      return baseChildren;\n    }\n    return /*#__PURE__*/React.createElement(ItemHolder, _extends({\n      key: \"row\"\n    }, props, {\n      prefixCls: prefixCls,\n      fieldId: fieldId,\n      isRequired: isRequired,\n      errors: mergedErrors,\n      warnings: mergedWarnings,\n      meta: meta,\n      onSubItemMetaChange: onSubItemMetaChange\n    }), baseChildren);\n  }\n  if (!hasName && !isRenderProps && !dependencies) {\n    return renderLayout(children);\n  }\n  var variables = {};\n  if (typeof label === 'string') {\n    variables.label = label;\n  } else if (name) {\n    variables.label = String(name);\n  }\n  if (messageVariables) {\n    variables = _extends(_extends({}, variables), messageVariables);\n  }\n  // >>>>> With Field\n  return /*#__PURE__*/React.createElement(Field, _extends({}, props, {\n    messageVariables: variables,\n    trigger: trigger,\n    validateTrigger: mergedValidateTrigger,\n    onMetaChange: onMetaChange\n  }), function (control, renderMeta, context) {\n    var mergedName = toArray(name).length && renderMeta ? renderMeta.name : [];\n    var fieldId = getFieldId(mergedName, formName);\n    var isRequired = required !== undefined ? required : !!(rules && rules.some(function (rule) {\n      if (rule && _typeof(rule) === 'object' && rule.required && !rule.warningOnly) {\n        return true;\n      }\n      if (typeof rule === 'function') {\n        var ruleEntity = rule(context);\n        return ruleEntity && ruleEntity.required && !ruleEntity.warningOnly;\n      }\n      return false;\n    }));\n    // ======================= Children =======================\n    var mergedControl = _extends({}, control);\n    var childNode = null;\n    process.env.NODE_ENV !== \"production\" ? warning(!(shouldUpdate && dependencies), 'Form.Item', \"`shouldUpdate` and `dependencies` shouldn't be used together. See https://ant.design/components/form/#dependencies.\") : void 0;\n    if (Array.isArray(children) && hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', '`children` is array of render props cannot have `name`.') : void 0;\n      childNode = children;\n    } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {\n      process.env.NODE_ENV !== \"production\" ? warning(!!(shouldUpdate || dependencies), 'Form.Item', '`children` of render props only work with `shouldUpdate` or `dependencies`.') : void 0;\n      process.env.NODE_ENV !== \"production\" ? warning(!hasName, 'Form.Item', \"Do not use `name` with `children` of render props since it's not a field.\") : void 0;\n    } else if (dependencies && !isRenderProps && !hasName) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'Form.Item', 'Must set `name` or use render props when `dependencies` is set.') : void 0;\n    } else if (isValidElement(children)) {\n      process.env.NODE_ENV !== \"production\" ? warning(children.props.defaultValue === undefined, 'Form.Item', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.') : void 0;\n      var childProps = _extends(_extends({}, children.props), mergedControl);\n      if (!childProps.id) {\n        childProps.id = fieldId;\n      }\n      if (props.help || mergedErrors.length > 0 || mergedWarnings.length > 0 || props.extra) {\n        var describedbyArr = [];\n        if (props.help || mergedErrors.length > 0) {\n          describedbyArr.push(\"\".concat(fieldId, \"_help\"));\n        }\n        if (props.extra) {\n          describedbyArr.push(\"\".concat(fieldId, \"_extra\"));\n        }\n        childProps['aria-describedby'] = describedbyArr.join(' ');\n      }\n      if (mergedErrors.length > 0) {\n        childProps['aria-invalid'] = 'true';\n      }\n      if (isRequired) {\n        childProps['aria-required'] = 'true';\n      }\n      if (supportRef(children)) {\n        childProps.ref = getItemRef(mergedName, children);\n      }\n      // We should keep user origin event handler\n      var triggers = new Set([].concat(_toConsumableArray(toArray(trigger)), _toConsumableArray(toArray(mergedValidateTrigger))));\n      triggers.forEach(function (eventName) {\n        childProps[eventName] = function () {\n          var _a2, _c2;\n          var _a, _b, _c;\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [mergedControl].concat(args));\n          (_c = (_b = children.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [_b].concat(args));\n        };\n      });\n      // List of props that need to be watched for changes -> if changes are detected in MemoInput -> rerender\n      var watchingChildProps = [childProps['aria-required'], childProps['aria-invalid'], childProps['aria-describedby']];\n      childNode = /*#__PURE__*/React.createElement(MemoInput, {\n        value: mergedControl[props.valuePropName || 'value'],\n        update: children,\n        childProps: watchingChildProps\n      }, cloneElement(children, childProps));\n    } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {\n      childNode = children(context);\n    } else {\n      process.env.NODE_ENV !== \"production\" ? warning(!mergedName.length, 'Form.Item', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.') : void 0;\n      childNode = children;\n    }\n    return renderLayout(childNode, fieldId, isRequired);\n  });\n}\nvar FormItem = InternalFormItem;\nFormItem.useStatus = useFormItemStatus;\nexport default FormItem;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,KAAK,EAAEC,YAAY,EAAEC,WAAW,QAAQ,eAAe;AAChE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,YAAY,EAAEC,cAAc,QAAQ,uBAAuB;AACpE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,YAAY;AAC5D,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,UAAU,EAAEC,OAAO,QAAQ,SAAS;AAC7C,OAAOC,UAAU,MAAM,cAAc;AACrC,IAAIC,UAAU,GAAG,WAAW;AAC5B,IAAIC,gBAAgB,GAAGV,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;AAC7E,IAAIW,SAAS,GAAG,aAAajB,KAAK,CAACkB,IAAI,CAAC,UAAUC,IAAI,EAAE;EACtD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,IAAI,EAAEC,IAAI,EAAE;EACvB,OAAOD,IAAI,CAACE,KAAK,KAAKD,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACG,MAAM,KAAKF,IAAI,CAACE,MAAM,IAAIH,IAAI,CAACI,UAAU,CAACC,MAAM,KAAKJ,IAAI,CAACG,UAAU,CAACC,MAAM,IAAIL,IAAI,CAACI,UAAU,CAACE,KAAK,CAAC,UAAUJ,KAAK,EAAEK,KAAK,EAAE;IACpK,OAAOL,KAAK,KAAKD,IAAI,CAACG,UAAU,CAACG,KAAK,CAAC;EACzC,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjBC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,qCAAqC,CAAC,GAAG,KAAK,CAAC;EACrH;EACA,OAAO,EAAEuB,IAAI,KAAKI,SAAS,IAAIJ,IAAI,KAAK,IAAI,CAAC;AAC/C;AACA,SAASK,YAAYA,CAAA,EAAG;EACtB,OAAO;IACLC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBT,IAAI,EAAE;EACR,CAAC;AACH;AACA,SAASU,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,IAAIX,IAAI,GAAGW,KAAK,CAACX,IAAI;IACnBY,OAAO,GAAGD,KAAK,CAACC,OAAO;IACvBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,kBAAkB,GAAGH,KAAK,CAACI,SAAS;IACpCC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnB3B,QAAQ,GAAGqB,KAAK,CAACrB,QAAQ;IACzB4B,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,gBAAgB,GAAGT,KAAK,CAACS,gBAAgB;IACzCC,cAAc,GAAGV,KAAK,CAACW,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,cAAc;IACjEE,eAAe,GAAGZ,KAAK,CAACY,eAAe;IACvCC,MAAM,GAAGb,KAAK,CAACa,MAAM;EACvB,IAAIC,WAAW,GAAGtD,UAAU,CAACE,aAAa,CAAC;IACzCqD,YAAY,GAAGD,WAAW,CAACC,YAAY;EACzC,IAAIC,YAAY,GAAGxD,UAAU,CAACO,WAAW,CAAC;IACxCkD,QAAQ,GAAGD,YAAY,CAAC3B,IAAI;EAC9B,IAAI6B,aAAa,GAAG,OAAOvC,QAAQ,KAAK,UAAU;EAClD,IAAIwC,sBAAsB,GAAG3D,UAAU,CAACQ,kBAAkB,CAAC;EAC3D,IAAIoD,YAAY,GAAG5D,UAAU,CAACL,YAAY,CAAC;IACzCkE,sBAAsB,GAAGD,YAAY,CAACR,eAAe;EACvD,IAAIU,qBAAqB,GAAGV,eAAe,KAAKnB,SAAS,GAAGmB,eAAe,GAAGS,sBAAsB;EACpG,IAAIE,OAAO,GAAGnC,YAAY,CAACC,IAAI,CAAC;EAChC,IAAIe,SAAS,GAAGW,YAAY,CAAC,MAAM,EAAEZ,kBAAkB,CAAC;EACxD;EACA;EACA,IAAIqB,WAAW,GAAGjE,KAAK,CAACC,UAAU,CAACJ,WAAW,CAAC;EAC/C,IAAIqE,eAAe,GAAGlE,KAAK,CAACmE,MAAM,CAAC,CAAC;EACpC;EACA;EACA,IAAIC,cAAc,GAAG1D,aAAa,CAAC,CAAC,CAAC,CAAC;IACpC2D,eAAe,GAAG3E,cAAc,CAAC0E,cAAc,EAAE,CAAC,CAAC;IACnDE,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;IACnCE,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC;EACxC;EACA,IAAIG,SAAS,GAAG1E,QAAQ,CAAC,YAAY;MACjC,OAAOqC,YAAY,CAAC,CAAC;IACvB,CAAC,CAAC;IACFsC,UAAU,GAAG/E,cAAc,CAAC8E,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EACzB,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACC,QAAQ,EAAE;IACjD;IACA;IACA;IACA,IAAIC,OAAO,GAAGb,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACc,MAAM,CAACF,QAAQ,CAAC/C,IAAI,CAAC;IACzG;IACA6C,OAAO,CAACE,QAAQ,CAACG,OAAO,GAAG7C,YAAY,CAAC,CAAC,GAAG0C,QAAQ,EAAE,IAAI,CAAC;IAC3D;IACA,IAAInC,OAAO,IAAIkB,sBAAsB,EAAE;MACrC,IAAIqB,QAAQ,GAAGJ,QAAQ,CAAC/C,IAAI;MAC5B,IAAI,CAAC+C,QAAQ,CAACG,OAAO,EAAE;QACrB,IAAIF,OAAO,KAAK5C,SAAS,EAAE;UACzB,IAAIgD,QAAQ,GAAGxF,cAAc,CAACoF,OAAO,EAAE,CAAC,CAAC;YACvCK,QAAQ,GAAGD,QAAQ,CAAC,CAAC,CAAC;YACtBE,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;UACxBD,QAAQ,GAAG,CAACE,QAAQ,CAAC,CAACE,MAAM,CAAC5F,kBAAkB,CAAC2F,QAAQ,CAAC,CAAC;UAC1DlB,eAAe,CAACoB,OAAO,GAAGL,QAAQ;QACpC;MACF,CAAC,MAAM;QACL;QACAA,QAAQ,GAAGf,eAAe,CAACoB,OAAO,IAAIL,QAAQ;MAChD;MACArB,sBAAsB,CAACiB,QAAQ,EAAEI,QAAQ,CAAC;IAC5C;EACF,CAAC;EACD;EACA,IAAIM,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAEC,UAAU,EAAE;IAC1E;IACAlB,iBAAiB,CAAC,UAAUmB,kBAAkB,EAAE;MAC9C,IAAIC,KAAK,GAAGnG,QAAQ,CAAC,CAAC,CAAC,EAAEkG,kBAAkB,CAAC;MAC5C;MACA,IAAIE,cAAc,GAAG,EAAE,CAACP,MAAM,CAAC5F,kBAAkB,CAAC+F,OAAO,CAAC1D,IAAI,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEpG,kBAAkB,CAACgG,UAAU,CAAC,CAAC;MAC7G,IAAIK,aAAa,GAAGF,cAAc,CAACG,IAAI,CAAChF,UAAU,CAAC;MACnD,IAAIyE,OAAO,CAACR,OAAO,EAAE;QACnB;QACA,OAAOW,KAAK,CAACG,aAAa,CAAC;MAC7B,CAAC,MAAM;QACL;QACAH,KAAK,CAACG,aAAa,CAAC,GAAGN,OAAO;MAChC;MACA,OAAOG,KAAK;IACd,CAAC,CAAC;EACJ,CAAC;EACD;EACA,IAAIK,cAAc,GAAGhG,KAAK,CAACiG,OAAO,CAAC,YAAY;MAC3C,IAAIC,SAAS,GAAGzG,kBAAkB,CAACiF,IAAI,CAACtC,MAAM,CAAC;MAC/C,IAAI+D,WAAW,GAAG1G,kBAAkB,CAACiF,IAAI,CAACrC,QAAQ,CAAC;MACnD+D,MAAM,CAACC,MAAM,CAAC/B,cAAc,CAAC,CAACgC,OAAO,CAAC,UAAUC,aAAa,EAAE;QAC7DL,SAAS,CAACM,IAAI,CAACC,KAAK,CAACP,SAAS,EAAEzG,kBAAkB,CAAC8G,aAAa,CAACnE,MAAM,IAAI,EAAE,CAAC,CAAC;QAC/E+D,WAAW,CAACK,IAAI,CAACC,KAAK,CAACN,WAAW,EAAE1G,kBAAkB,CAAC8G,aAAa,CAAClE,QAAQ,IAAI,EAAE,CAAC,CAAC;MACvF,CAAC,CAAC;MACF,OAAO,CAAC6D,SAAS,EAAEC,WAAW,CAAC;IACjC,CAAC,EAAE,CAAC7B,cAAc,EAAEI,IAAI,CAACtC,MAAM,EAAEsC,IAAI,CAACrC,QAAQ,CAAC,CAAC;IAChDqE,eAAe,GAAGhH,cAAc,CAACsG,cAAc,EAAE,CAAC,CAAC;IACnDW,YAAY,GAAGD,eAAe,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,eAAe,CAAC,CAAC,CAAC;EACrC;EACA,IAAIG,UAAU,GAAGlG,UAAU,CAAC,CAAC;EAC7B;EACA,SAASmG,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAE;IACvD,IAAIvE,OAAO,IAAI,CAACY,MAAM,EAAE;MACtB,OAAOyD,YAAY;IACrB;IACA,OAAO,aAAa/G,KAAK,CAACkH,aAAa,CAACpG,UAAU,EAAEtB,QAAQ,CAAC;MAC3D2H,GAAG,EAAE;IACP,CAAC,EAAE1E,KAAK,EAAE;MACRI,SAAS,EAAEA,SAAS;MACpBmE,OAAO,EAAEA,OAAO;MAChBC,UAAU,EAAEA,UAAU;MACtB7E,MAAM,EAAEuE,YAAY;MACpBtE,QAAQ,EAAEuE,cAAc;MACxBlC,IAAI,EAAEA,IAAI;MACVa,mBAAmB,EAAEA;IACvB,CAAC,CAAC,EAAEwB,YAAY,CAAC;EACnB;EACA,IAAI,CAAC/C,OAAO,IAAI,CAACL,aAAa,IAAI,CAAChB,YAAY,EAAE;IAC/C,OAAOmE,YAAY,CAAC1F,QAAQ,CAAC;EAC/B;EACA,IAAIgG,SAAS,GAAG,CAAC,CAAC;EAClB,IAAI,OAAOnE,KAAK,KAAK,QAAQ,EAAE;IAC7BmE,SAAS,CAACnE,KAAK,GAAGA,KAAK;EACzB,CAAC,MAAM,IAAInB,IAAI,EAAE;IACfsF,SAAS,CAACnE,KAAK,GAAGoE,MAAM,CAACvF,IAAI,CAAC;EAChC;EACA,IAAIoB,gBAAgB,EAAE;IACpBkE,SAAS,GAAG5H,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4H,SAAS,CAAC,EAAElE,gBAAgB,CAAC;EACjE;EACA;EACA,OAAO,aAAalD,KAAK,CAACkH,aAAa,CAACvH,KAAK,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEiD,KAAK,EAAE;IACjES,gBAAgB,EAAEkE,SAAS;IAC3BhE,OAAO,EAAEA,OAAO;IAChBC,eAAe,EAAEU,qBAAqB;IACtCa,YAAY,EAAEA;EAChB,CAAC,CAAC,EAAE,UAAU0C,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAE;IAC1C,IAAIC,UAAU,GAAG5G,OAAO,CAACiB,IAAI,CAAC,CAACJ,MAAM,IAAI6F,UAAU,GAAGA,UAAU,CAACzF,IAAI,GAAG,EAAE;IAC1E,IAAIkF,OAAO,GAAGpG,UAAU,CAAC6G,UAAU,EAAE/D,QAAQ,CAAC;IAC9C,IAAIuD,UAAU,GAAGjE,QAAQ,KAAKd,SAAS,GAAGc,QAAQ,GAAG,CAAC,EAAED,KAAK,IAAIA,KAAK,CAAC2E,IAAI,CAAC,UAAUC,IAAI,EAAE;MAC1F,IAAIA,IAAI,IAAIpI,OAAO,CAACoI,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC3E,QAAQ,IAAI,CAAC2E,IAAI,CAACC,WAAW,EAAE;QAC5E,OAAO,IAAI;MACb;MACA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAIE,UAAU,GAAGF,IAAI,CAACH,OAAO,CAAC;QAC9B,OAAOK,UAAU,IAAIA,UAAU,CAAC7E,QAAQ,IAAI,CAAC6E,UAAU,CAACD,WAAW;MACrE;MACA,OAAO,KAAK;IACd,CAAC,CAAC,CAAC;IACH;IACA,IAAIE,aAAa,GAAGtI,QAAQ,CAAC,CAAC,CAAC,EAAE8H,OAAO,CAAC;IACzC,IAAIS,SAAS,GAAG,IAAI;IACpBhG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,EAAEuC,YAAY,IAAIH,YAAY,CAAC,EAAE,WAAW,EAAE,qHAAqH,CAAC,GAAG,KAAK,CAAC;IAC7N,IAAIqF,KAAK,CAACC,OAAO,CAAC7G,QAAQ,CAAC,IAAI4C,OAAO,EAAE;MACtCjC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,yDAAyD,CAAC,GAAG,KAAK,CAAC;MACvIwH,SAAS,GAAG3G,QAAQ;IACtB,CAAC,MAAM,IAAIuC,aAAa,KAAK,EAAEb,YAAY,IAAIH,YAAY,CAAC,IAAIqB,OAAO,CAAC,EAAE;MACxEjC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,CAAC,EAAEuC,YAAY,IAAIH,YAAY,CAAC,EAAE,WAAW,EAAE,6EAA6E,CAAC,GAAG,KAAK,CAAC;MACtLZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,CAACyD,OAAO,EAAE,WAAW,EAAE,2EAA2E,CAAC,GAAG,KAAK,CAAC;IAC9J,CAAC,MAAM,IAAIrB,YAAY,IAAI,CAACgB,aAAa,IAAI,CAACK,OAAO,EAAE;MACrDjC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,iEAAiE,CAAC,GAAG,KAAK,CAAC;IACjJ,CAAC,MAAM,IAAIF,cAAc,CAACe,QAAQ,CAAC,EAAE;MACnCW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAACa,QAAQ,CAACqB,KAAK,CAACyF,YAAY,KAAKhG,SAAS,EAAE,WAAW,EAAE,mGAAmG,CAAC,GAAG,KAAK,CAAC;MACrN,IAAIT,UAAU,GAAGjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4B,QAAQ,CAACqB,KAAK,CAAC,EAAEqF,aAAa,CAAC;MACtE,IAAI,CAACrG,UAAU,CAAC0G,EAAE,EAAE;QAClB1G,UAAU,CAAC0G,EAAE,GAAGnB,OAAO;MACzB;MACA,IAAIvE,KAAK,CAAC2F,IAAI,IAAIzB,YAAY,CAACjF,MAAM,GAAG,CAAC,IAAIkF,cAAc,CAAClF,MAAM,GAAG,CAAC,IAAIe,KAAK,CAAC4F,KAAK,EAAE;QACrF,IAAIC,cAAc,GAAG,EAAE;QACvB,IAAI7F,KAAK,CAAC2F,IAAI,IAAIzB,YAAY,CAACjF,MAAM,GAAG,CAAC,EAAE;UACzC4G,cAAc,CAAC9B,IAAI,CAAC,EAAE,CAACnB,MAAM,CAAC2B,OAAO,EAAE,OAAO,CAAC,CAAC;QAClD;QACA,IAAIvE,KAAK,CAAC4F,KAAK,EAAE;UACfC,cAAc,CAAC9B,IAAI,CAAC,EAAE,CAACnB,MAAM,CAAC2B,OAAO,EAAE,QAAQ,CAAC,CAAC;QACnD;QACAvF,UAAU,CAAC,kBAAkB,CAAC,GAAG6G,cAAc,CAACvC,IAAI,CAAC,GAAG,CAAC;MAC3D;MACA,IAAIY,YAAY,CAACjF,MAAM,GAAG,CAAC,EAAE;QAC3BD,UAAU,CAAC,cAAc,CAAC,GAAG,MAAM;MACrC;MACA,IAAIwF,UAAU,EAAE;QACdxF,UAAU,CAAC,eAAe,CAAC,GAAG,MAAM;MACtC;MACA,IAAI1B,UAAU,CAACqB,QAAQ,CAAC,EAAE;QACxBK,UAAU,CAAC8G,GAAG,GAAG1B,UAAU,CAACY,UAAU,EAAErG,QAAQ,CAAC;MACnD;MACA;MACA,IAAIoH,QAAQ,GAAG,IAAIC,GAAG,CAAC,EAAE,CAACpD,MAAM,CAAC5F,kBAAkB,CAACoB,OAAO,CAACuC,OAAO,CAAC,CAAC,EAAE3D,kBAAkB,CAACoB,OAAO,CAACkD,qBAAqB,CAAC,CAAC,CAAC,CAAC;MAC3HyE,QAAQ,CAAClC,OAAO,CAAC,UAAUoC,SAAS,EAAE;QACpCjH,UAAU,CAACiH,SAAS,CAAC,GAAG,YAAY;UAClC,IAAIC,GAAG,EAAEC,GAAG;UACZ,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;UACd,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACvH,MAAM,EAAEwH,IAAI,GAAG,IAAIlB,KAAK,CAACgB,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;YACvFD,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;UAC9B;UACA,CAACN,EAAE,GAAGf,aAAa,CAACY,SAAS,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACF,GAAG,GAAGE,EAAE,EAAEO,IAAI,CAAC3C,KAAK,CAACkC,GAAG,EAAE,CAACb,aAAa,CAAC,CAACzC,MAAM,CAAC6D,IAAI,CAAC,CAAC;UAC7H,CAACH,EAAE,GAAG,CAACD,EAAE,GAAG1H,QAAQ,CAACqB,KAAK,EAAEiG,SAAS,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACH,GAAG,GAAGG,EAAE,EAAEK,IAAI,CAAC3C,KAAK,CAACmC,GAAG,EAAE,CAACE,EAAE,CAAC,CAACzD,MAAM,CAAC6D,IAAI,CAAC,CAAC;QAC5H,CAAC;MACH,CAAC,CAAC;MACF;MACA,IAAIG,kBAAkB,GAAG,CAAC5H,UAAU,CAAC,eAAe,CAAC,EAAEA,UAAU,CAAC,cAAc,CAAC,EAAEA,UAAU,CAAC,kBAAkB,CAAC,CAAC;MAClHsG,SAAS,GAAG,aAAa/H,KAAK,CAACkH,aAAa,CAACjG,SAAS,EAAE;QACtDM,KAAK,EAAEuG,aAAa,CAACrF,KAAK,CAAC6G,aAAa,IAAI,OAAO,CAAC;QACpD9H,MAAM,EAAEJ,QAAQ;QAChBK,UAAU,EAAE4H;MACd,CAAC,EAAEjJ,YAAY,CAACgB,QAAQ,EAAEK,UAAU,CAAC,CAAC;IACxC,CAAC,MAAM,IAAIkC,aAAa,KAAKb,YAAY,IAAIH,YAAY,CAAC,IAAI,CAACqB,OAAO,EAAE;MACtE+D,SAAS,GAAG3G,QAAQ,CAACoG,OAAO,CAAC;IAC/B,CAAC,MAAM;MACLzF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,OAAO,CAAC,CAACkH,UAAU,CAAC/F,MAAM,EAAE,WAAW,EAAE,6HAA6H,CAAC,GAAG,KAAK,CAAC;MACxNqG,SAAS,GAAG3G,QAAQ;IACtB;IACA,OAAO0F,YAAY,CAACiB,SAAS,EAAEf,OAAO,EAAEC,UAAU,CAAC;EACrD,CAAC,CAAC;AACJ;AACA,IAAIsC,QAAQ,GAAG/G,gBAAgB;AAC/B+G,QAAQ,CAACC,SAAS,GAAGtJ,iBAAiB;AACtC,eAAeqJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}