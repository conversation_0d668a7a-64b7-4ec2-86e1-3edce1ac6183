{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.FormatSpecifier = FormatSpecifier;\nexports.default = formatSpecifier;\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nfunction formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nfunction FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function () {\n  return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "FormatSpecifier", "default", "formatSpecifier", "re", "specifier", "match", "exec", "Error", "fill", "align", "sign", "symbol", "zero", "width", "comma", "precision", "slice", "trim", "type", "prototype", "undefined", "toString", "Math", "max"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatSpecifier.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.FormatSpecifier = FormatSpecifier;\nexports.default = formatSpecifier;\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nfunction formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nfunction FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function () {\n  return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzCF,OAAO,CAACG,OAAO,GAAGC,eAAe;AACjC;AACA,IAAIC,EAAE,GAAG,0EAA0E;AAEnF,SAASD,eAAeA,CAACE,SAAS,EAAE;EAClC,IAAI,EAAEC,KAAK,GAAGF,EAAE,CAACG,IAAI,CAACF,SAAS,CAAC,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,kBAAkB,GAAGH,SAAS,CAAC;EAClF,IAAIC,KAAK;EACT,OAAO,IAAIL,eAAe,CAAC;IACzBQ,IAAI,EAAEH,KAAK,CAAC,CAAC,CAAC;IACdI,KAAK,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACfK,IAAI,EAAEL,KAAK,CAAC,CAAC,CAAC;IACdM,MAAM,EAAEN,KAAK,CAAC,CAAC,CAAC;IAChBO,IAAI,EAAEP,KAAK,CAAC,CAAC,CAAC;IACdQ,KAAK,EAAER,KAAK,CAAC,CAAC,CAAC;IACfS,KAAK,EAAET,KAAK,CAAC,CAAC,CAAC;IACfU,SAAS,EAAEV,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC;IACxCC,IAAI,EAAEZ,KAAK,CAAC,CAAC,CAAC;IACda,IAAI,EAAEb,KAAK,CAAC,EAAE;EAChB,CAAC,CAAC;AACJ;AAEAH,eAAe,CAACiB,SAAS,GAAGnB,eAAe,CAACmB,SAAS,CAAC,CAAC;;AAEvD,SAASnB,eAAeA,CAACI,SAAS,EAAE;EAClC,IAAI,CAACI,IAAI,GAAGJ,SAAS,CAACI,IAAI,KAAKY,SAAS,GAAG,GAAG,GAAGhB,SAAS,CAACI,IAAI,GAAG,EAAE;EACpE,IAAI,CAACC,KAAK,GAAGL,SAAS,CAACK,KAAK,KAAKW,SAAS,GAAG,GAAG,GAAGhB,SAAS,CAACK,KAAK,GAAG,EAAE;EACvE,IAAI,CAACC,IAAI,GAAGN,SAAS,CAACM,IAAI,KAAKU,SAAS,GAAG,GAAG,GAAGhB,SAAS,CAACM,IAAI,GAAG,EAAE;EACpE,IAAI,CAACC,MAAM,GAAGP,SAAS,CAACO,MAAM,KAAKS,SAAS,GAAG,EAAE,GAAGhB,SAAS,CAACO,MAAM,GAAG,EAAE;EACzE,IAAI,CAACC,IAAI,GAAG,CAAC,CAACR,SAAS,CAACQ,IAAI;EAC5B,IAAI,CAACC,KAAK,GAAGT,SAAS,CAACS,KAAK,KAAKO,SAAS,GAAGA,SAAS,GAAG,CAAChB,SAAS,CAACS,KAAK;EACzE,IAAI,CAACC,KAAK,GAAG,CAAC,CAACV,SAAS,CAACU,KAAK;EAC9B,IAAI,CAACC,SAAS,GAAGX,SAAS,CAACW,SAAS,KAAKK,SAAS,GAAGA,SAAS,GAAG,CAAChB,SAAS,CAACW,SAAS;EACrF,IAAI,CAACE,IAAI,GAAG,CAAC,CAACb,SAAS,CAACa,IAAI;EAC5B,IAAI,CAACC,IAAI,GAAGd,SAAS,CAACc,IAAI,KAAKE,SAAS,GAAG,EAAE,GAAGhB,SAAS,CAACc,IAAI,GAAG,EAAE;AACrE;AAEAlB,eAAe,CAACmB,SAAS,CAACE,QAAQ,GAAG,YAAY;EAC/C,OAAO,IAAI,CAACb,IAAI,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,CAACC,KAAK,KAAKO,SAAS,GAAG,EAAE,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAACC,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,CAACC,SAAS,KAAKK,SAAS,GAAG,EAAE,GAAG,GAAG,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACR,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAACE,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAACC,IAAI;AAC/R,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}