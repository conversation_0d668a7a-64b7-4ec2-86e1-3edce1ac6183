{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable default-case */\nimport classNames from 'classnames';\nimport { useBaseProps } from 'rc-select';\nimport * as React from 'react';\nimport CascaderContext from '../context';\nimport { isLeaf, scrollIntoParentView, toPathKey, toPathKeys, toPathValueStr } from '../utils/commonUtil';\nimport { toPathOptions } from '../utils/treeUtil';\nimport Column, { FIX_LABEL } from './Column';\nimport useActive from './useActive';\nimport useKeyboard from './useKeyboard';\nvar RefOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _optionColumns$, _optionColumns$$optio, _ref3, _classNames;\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    direction = _useBaseProps.direction;\n  var containerRef = React.useRef();\n  var rtl = direction === 'rtl';\n  var _React$useContext = React.useContext(CascaderContext),\n    options = _React$useContext.options,\n    values = _React$useContext.values,\n    halfValues = _React$useContext.halfValues,\n    fieldNames = _React$useContext.fieldNames,\n    changeOnSelect = _React$useContext.changeOnSelect,\n    onSelect = _React$useContext.onSelect,\n    searchOptions = _React$useContext.searchOptions,\n    dropdownPrefixCls = _React$useContext.dropdownPrefixCls,\n    loadData = _React$useContext.loadData,\n    expandTrigger = _React$useContext.expandTrigger;\n  var mergedPrefixCls = dropdownPrefixCls || prefixCls;\n  // ========================= loadData =========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loadingKeys = _React$useState2[0],\n    setLoadingKeys = _React$useState2[1];\n  var internalLoadData = function internalLoadData(valueCells) {\n    // Do not load when search\n    if (!loadData || searchValue) {\n      return;\n    }\n    var optionList = toPathOptions(valueCells, options, fieldNames);\n    var rawOptions = optionList.map(function (_ref) {\n      var option = _ref.option;\n      return option;\n    });\n    var lastOption = rawOptions[rawOptions.length - 1];\n    if (lastOption && !isLeaf(lastOption, fieldNames)) {\n      var pathKey = toPathKey(valueCells);\n      setLoadingKeys(function (keys) {\n        return [].concat(_toConsumableArray(keys), [pathKey]);\n      });\n      loadData(rawOptions);\n    }\n  };\n  // zombieJ: This is bad. We should make this same as `rc-tree` to use Promise instead.\n  React.useEffect(function () {\n    if (loadingKeys.length) {\n      loadingKeys.forEach(function (loadingKey) {\n        var valueStrCells = toPathValueStr(loadingKey);\n        var optionList = toPathOptions(valueStrCells, options, fieldNames, true).map(function (_ref2) {\n          var option = _ref2.option;\n          return option;\n        });\n        var lastOption = optionList[optionList.length - 1];\n        if (!lastOption || lastOption[fieldNames.children] || isLeaf(lastOption, fieldNames)) {\n          setLoadingKeys(function (keys) {\n            return keys.filter(function (key) {\n              return key !== loadingKey;\n            });\n          });\n        }\n      });\n    }\n  }, [options, loadingKeys, fieldNames]);\n  // ========================== Values ==========================\n  var checkedSet = React.useMemo(function () {\n    return new Set(toPathKeys(values));\n  }, [values]);\n  var halfCheckedSet = React.useMemo(function () {\n    return new Set(toPathKeys(halfValues));\n  }, [halfValues]);\n  // ====================== Accessibility =======================\n  var _useActive = useActive(),\n    _useActive2 = _slicedToArray(_useActive, 2),\n    activeValueCells = _useActive2[0],\n    setActiveValueCells = _useActive2[1];\n  // =========================== Path ===========================\n  var onPathOpen = function onPathOpen(nextValueCells) {\n    setActiveValueCells(nextValueCells);\n    // Trigger loadData\n    internalLoadData(nextValueCells);\n  };\n  var isSelectable = function isSelectable(option) {\n    var disabled = option.disabled;\n    var isMergedLeaf = isLeaf(option, fieldNames);\n    return !disabled && (isMergedLeaf || changeOnSelect || multiple);\n  };\n  var onPathSelect = function onPathSelect(valuePath, leaf) {\n    var fromKeyboard = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    onSelect(valuePath);\n    if (!multiple && (leaf || changeOnSelect && (expandTrigger === 'hover' || fromKeyboard))) {\n      toggleOpen(false);\n    }\n  };\n  // ========================== Option ==========================\n  var mergedOptions = React.useMemo(function () {\n    if (searchValue) {\n      return searchOptions;\n    }\n    return options;\n  }, [searchValue, searchOptions, options]);\n  // ========================== Column ==========================\n  var optionColumns = React.useMemo(function () {\n    var optionList = [{\n      options: mergedOptions\n    }];\n    var currentList = mergedOptions;\n    var _loop = function _loop() {\n      var activeValueCell = activeValueCells[i];\n      var currentOption = currentList.find(function (option) {\n        return option[fieldNames.value] === activeValueCell;\n      });\n      var subOptions = currentOption === null || currentOption === void 0 ? void 0 : currentOption[fieldNames.children];\n      if (!(subOptions === null || subOptions === void 0 ? void 0 : subOptions.length)) {\n        return \"break\";\n      }\n      currentList = subOptions;\n      optionList.push({\n        options: subOptions\n      });\n    };\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _ret = _loop();\n      if (_ret === \"break\") break;\n    }\n    return optionList;\n  }, [mergedOptions, activeValueCells, fieldNames]);\n  // ========================= Keyboard =========================\n  var onKeyboardSelect = function onKeyboardSelect(selectValueCells, option) {\n    if (isSelectable(option)) {\n      onPathSelect(selectValueCells, isLeaf(option, fieldNames), true);\n    }\n  };\n  useKeyboard(ref, mergedOptions, fieldNames, activeValueCells, onPathOpen, onKeyboardSelect);\n  // >>>>> Active Scroll\n  React.useEffect(function () {\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _containerRef$current;\n      var cellPath = activeValueCells.slice(0, i + 1);\n      var cellKeyPath = toPathKey(cellPath);\n      var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelector(\"li[data-path-key=\\\"\".concat(cellKeyPath.replace(/\\\\{0,2}\"/g, '\\\\\"'), \"\\\"]\"));\n      if (ele) {\n        scrollIntoParentView(ele);\n      }\n    }\n  }, [activeValueCells]);\n  // ========================== Render ==========================\n  // >>>>> Empty\n  var isEmpty = !((_optionColumns$ = optionColumns[0]) === null || _optionColumns$ === void 0 ? void 0 : (_optionColumns$$optio = _optionColumns$.options) === null || _optionColumns$$optio === void 0 ? void 0 : _optionColumns$$optio.length);\n  var emptyList = [(_ref3 = {}, _defineProperty(_ref3, fieldNames.value, '__EMPTY__'), _defineProperty(_ref3, FIX_LABEL, notFoundContent), _defineProperty(_ref3, \"disabled\", true), _ref3)];\n  var columnProps = _objectSpread(_objectSpread({}, props), {}, {\n    multiple: !isEmpty && multiple,\n    onSelect: onPathSelect,\n    onActive: onPathOpen,\n    onToggleOpen: toggleOpen,\n    checkedSet: checkedSet,\n    halfCheckedSet: halfCheckedSet,\n    loadingKeys: loadingKeys,\n    isSelectable: isSelectable\n  });\n  // >>>>> Columns\n  var mergedOptionColumns = isEmpty ? [{\n    options: emptyList\n  }] : optionColumns;\n  var columnNodes = mergedOptionColumns.map(function (col, index) {\n    var prevValuePath = activeValueCells.slice(0, index);\n    var activeValue = activeValueCells[index];\n    return /*#__PURE__*/React.createElement(Column, _extends({\n      key: index\n    }, columnProps, {\n      prefixCls: mergedPrefixCls,\n      options: col.options,\n      prevValuePath: prevValuePath,\n      activeValue: activeValue\n    }));\n  });\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(mergedPrefixCls, \"-menus\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-menu-empty\"), isEmpty), _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-rtl\"), rtl), _classNames)),\n    ref: containerRef\n  }, columnNodes);\n});\nexport default RefOptionList;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_toConsumableArray", "_slicedToArray", "classNames", "useBaseProps", "React", "CascaderContext", "<PERSON><PERSON><PERSON><PERSON>", "scrollIntoParentView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toPathValueStr", "toPathOptions", "Column", "FIX_LABEL", "useActive", "useKeyboard", "RefOptionList", "forwardRef", "props", "ref", "_optionColumns$", "_optionColumns$$optio", "_ref3", "_classNames", "_useBaseProps", "prefixCls", "multiple", "searchValue", "toggle<PERSON><PERSON>", "notFoundContent", "direction", "containerRef", "useRef", "rtl", "_React$useContext", "useContext", "options", "values", "halfV<PERSON>ues", "fieldNames", "changeOnSelect", "onSelect", "searchOptions", "dropdownPrefixCls", "loadData", "expandTrigger", "mergedPrefixCls", "_React$useState", "useState", "_React$useState2", "loadingKeys", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "internalLoadData", "valueCells", "optionList", "rawOptions", "map", "_ref", "option", "lastOption", "length", "path<PERSON><PERSON>", "keys", "concat", "useEffect", "for<PERSON>ach", "loadingKey", "valueStrCells", "_ref2", "children", "filter", "key", "checkedSet", "useMemo", "Set", "halfCheckedSet", "_useActive", "_useActive2", "activeValueCells", "setActiveValueCells", "onPathOpen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSelectable", "disabled", "isMergedLeaf", "onPathSelect", "valuePath", "leaf", "fromKeyboard", "arguments", "undefined", "mergedOptions", "optionColumns", "currentList", "_loop", "activeValueCell", "i", "currentOption", "find", "value", "subOptions", "push", "_ret", "onKeyboardSelect", "selectV<PERSON>ueCells", "_containerRef$current", "cellPath", "slice", "cellKeyPath", "ele", "current", "querySelector", "replace", "isEmpty", "emptyList", "columnProps", "onActive", "onToggleOpen", "mergedOptionColumns", "columnNodes", "col", "index", "prev<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeValue", "createElement", "className"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-cascader/es/OptionList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable default-case */\nimport classNames from 'classnames';\nimport { useBaseProps } from 'rc-select';\nimport * as React from 'react';\nimport CascaderContext from '../context';\nimport { isLeaf, scrollIntoParentView, toPathKey, toPathKeys, toPathValueStr } from '../utils/commonUtil';\nimport { toPathOptions } from '../utils/treeUtil';\nimport Column, { FIX_LABEL } from './Column';\nimport useActive from './useActive';\nimport useKeyboard from './useKeyboard';\nvar RefOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _optionColumns$, _optionColumns$$optio, _ref3, _classNames;\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    direction = _useBaseProps.direction;\n  var containerRef = React.useRef();\n  var rtl = direction === 'rtl';\n  var _React$useContext = React.useContext(CascaderContext),\n    options = _React$useContext.options,\n    values = _React$useContext.values,\n    halfValues = _React$useContext.halfValues,\n    fieldNames = _React$useContext.fieldNames,\n    changeOnSelect = _React$useContext.changeOnSelect,\n    onSelect = _React$useContext.onSelect,\n    searchOptions = _React$useContext.searchOptions,\n    dropdownPrefixCls = _React$useContext.dropdownPrefixCls,\n    loadData = _React$useContext.loadData,\n    expandTrigger = _React$useContext.expandTrigger;\n  var mergedPrefixCls = dropdownPrefixCls || prefixCls;\n  // ========================= loadData =========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loadingKeys = _React$useState2[0],\n    setLoadingKeys = _React$useState2[1];\n  var internalLoadData = function internalLoadData(valueCells) {\n    // Do not load when search\n    if (!loadData || searchValue) {\n      return;\n    }\n    var optionList = toPathOptions(valueCells, options, fieldNames);\n    var rawOptions = optionList.map(function (_ref) {\n      var option = _ref.option;\n      return option;\n    });\n    var lastOption = rawOptions[rawOptions.length - 1];\n    if (lastOption && !isLeaf(lastOption, fieldNames)) {\n      var pathKey = toPathKey(valueCells);\n      setLoadingKeys(function (keys) {\n        return [].concat(_toConsumableArray(keys), [pathKey]);\n      });\n      loadData(rawOptions);\n    }\n  };\n  // zombieJ: This is bad. We should make this same as `rc-tree` to use Promise instead.\n  React.useEffect(function () {\n    if (loadingKeys.length) {\n      loadingKeys.forEach(function (loadingKey) {\n        var valueStrCells = toPathValueStr(loadingKey);\n        var optionList = toPathOptions(valueStrCells, options, fieldNames, true).map(function (_ref2) {\n          var option = _ref2.option;\n          return option;\n        });\n        var lastOption = optionList[optionList.length - 1];\n        if (!lastOption || lastOption[fieldNames.children] || isLeaf(lastOption, fieldNames)) {\n          setLoadingKeys(function (keys) {\n            return keys.filter(function (key) {\n              return key !== loadingKey;\n            });\n          });\n        }\n      });\n    }\n  }, [options, loadingKeys, fieldNames]);\n  // ========================== Values ==========================\n  var checkedSet = React.useMemo(function () {\n    return new Set(toPathKeys(values));\n  }, [values]);\n  var halfCheckedSet = React.useMemo(function () {\n    return new Set(toPathKeys(halfValues));\n  }, [halfValues]);\n  // ====================== Accessibility =======================\n  var _useActive = useActive(),\n    _useActive2 = _slicedToArray(_useActive, 2),\n    activeValueCells = _useActive2[0],\n    setActiveValueCells = _useActive2[1];\n  // =========================== Path ===========================\n  var onPathOpen = function onPathOpen(nextValueCells) {\n    setActiveValueCells(nextValueCells);\n    // Trigger loadData\n    internalLoadData(nextValueCells);\n  };\n  var isSelectable = function isSelectable(option) {\n    var disabled = option.disabled;\n    var isMergedLeaf = isLeaf(option, fieldNames);\n    return !disabled && (isMergedLeaf || changeOnSelect || multiple);\n  };\n  var onPathSelect = function onPathSelect(valuePath, leaf) {\n    var fromKeyboard = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    onSelect(valuePath);\n    if (!multiple && (leaf || changeOnSelect && (expandTrigger === 'hover' || fromKeyboard))) {\n      toggleOpen(false);\n    }\n  };\n  // ========================== Option ==========================\n  var mergedOptions = React.useMemo(function () {\n    if (searchValue) {\n      return searchOptions;\n    }\n    return options;\n  }, [searchValue, searchOptions, options]);\n  // ========================== Column ==========================\n  var optionColumns = React.useMemo(function () {\n    var optionList = [{\n      options: mergedOptions\n    }];\n    var currentList = mergedOptions;\n    var _loop = function _loop() {\n      var activeValueCell = activeValueCells[i];\n      var currentOption = currentList.find(function (option) {\n        return option[fieldNames.value] === activeValueCell;\n      });\n      var subOptions = currentOption === null || currentOption === void 0 ? void 0 : currentOption[fieldNames.children];\n      if (!(subOptions === null || subOptions === void 0 ? void 0 : subOptions.length)) {\n        return \"break\";\n      }\n      currentList = subOptions;\n      optionList.push({\n        options: subOptions\n      });\n    };\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _ret = _loop();\n      if (_ret === \"break\") break;\n    }\n    return optionList;\n  }, [mergedOptions, activeValueCells, fieldNames]);\n  // ========================= Keyboard =========================\n  var onKeyboardSelect = function onKeyboardSelect(selectValueCells, option) {\n    if (isSelectable(option)) {\n      onPathSelect(selectValueCells, isLeaf(option, fieldNames), true);\n    }\n  };\n  useKeyboard(ref, mergedOptions, fieldNames, activeValueCells, onPathOpen, onKeyboardSelect);\n  // >>>>> Active Scroll\n  React.useEffect(function () {\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _containerRef$current;\n      var cellPath = activeValueCells.slice(0, i + 1);\n      var cellKeyPath = toPathKey(cellPath);\n      var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelector(\"li[data-path-key=\\\"\".concat(cellKeyPath.replace(/\\\\{0,2}\"/g, '\\\\\"'), \"\\\"]\"));\n      if (ele) {\n        scrollIntoParentView(ele);\n      }\n    }\n  }, [activeValueCells]);\n  // ========================== Render ==========================\n  // >>>>> Empty\n  var isEmpty = !((_optionColumns$ = optionColumns[0]) === null || _optionColumns$ === void 0 ? void 0 : (_optionColumns$$optio = _optionColumns$.options) === null || _optionColumns$$optio === void 0 ? void 0 : _optionColumns$$optio.length);\n  var emptyList = [(_ref3 = {}, _defineProperty(_ref3, fieldNames.value, '__EMPTY__'), _defineProperty(_ref3, FIX_LABEL, notFoundContent), _defineProperty(_ref3, \"disabled\", true), _ref3)];\n  var columnProps = _objectSpread(_objectSpread({}, props), {}, {\n    multiple: !isEmpty && multiple,\n    onSelect: onPathSelect,\n    onActive: onPathOpen,\n    onToggleOpen: toggleOpen,\n    checkedSet: checkedSet,\n    halfCheckedSet: halfCheckedSet,\n    loadingKeys: loadingKeys,\n    isSelectable: isSelectable\n  });\n  // >>>>> Columns\n  var mergedOptionColumns = isEmpty ? [{\n    options: emptyList\n  }] : optionColumns;\n  var columnNodes = mergedOptionColumns.map(function (col, index) {\n    var prevValuePath = activeValueCells.slice(0, index);\n    var activeValue = activeValueCells[index];\n    return /*#__PURE__*/React.createElement(Column, _extends({\n      key: index\n    }, columnProps, {\n      prefixCls: mergedPrefixCls,\n      options: col.options,\n      prevValuePath: prevValuePath,\n      activeValue: activeValue\n    }));\n  });\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(mergedPrefixCls, \"-menus\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-menu-empty\"), isEmpty), _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-rtl\"), rtl), _classNames)),\n    ref: containerRef\n  }, columnNodes);\n});\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,YAAY;AACxC,SAASC,MAAM,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;AACzG,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,MAAM,IAAIC,SAAS,QAAQ,UAAU;AAC5C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,aAAa,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,eAAe,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,WAAW;EAC9D,IAAIC,aAAa,GAAGrB,YAAY,CAAC,CAAC;IAChCsB,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;IACjCC,WAAW,GAAGH,aAAa,CAACG,WAAW;IACvCC,UAAU,GAAGJ,aAAa,CAACI,UAAU;IACrCC,eAAe,GAAGL,aAAa,CAACK,eAAe;IAC/CC,SAAS,GAAGN,aAAa,CAACM,SAAS;EACrC,IAAIC,YAAY,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EACjC,IAAIC,GAAG,GAAGH,SAAS,KAAK,KAAK;EAC7B,IAAII,iBAAiB,GAAG9B,KAAK,CAAC+B,UAAU,CAAC9B,eAAe,CAAC;IACvD+B,OAAO,GAAGF,iBAAiB,CAACE,OAAO;IACnCC,MAAM,GAAGH,iBAAiB,CAACG,MAAM;IACjCC,UAAU,GAAGJ,iBAAiB,CAACI,UAAU;IACzCC,UAAU,GAAGL,iBAAiB,CAACK,UAAU;IACzCC,cAAc,GAAGN,iBAAiB,CAACM,cAAc;IACjDC,QAAQ,GAAGP,iBAAiB,CAACO,QAAQ;IACrCC,aAAa,GAAGR,iBAAiB,CAACQ,aAAa;IAC/CC,iBAAiB,GAAGT,iBAAiB,CAACS,iBAAiB;IACvDC,QAAQ,GAAGV,iBAAiB,CAACU,QAAQ;IACrCC,aAAa,GAAGX,iBAAiB,CAACW,aAAa;EACjD,IAAIC,eAAe,GAAGH,iBAAiB,IAAIlB,SAAS;EACpD;EACA,IAAIsB,eAAe,GAAG3C,KAAK,CAAC4C,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGhD,cAAc,CAAC8C,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;IAC3D;IACA,IAAI,CAACT,QAAQ,IAAIjB,WAAW,EAAE;MAC5B;IACF;IACA,IAAI2B,UAAU,GAAG3C,aAAa,CAAC0C,UAAU,EAAEjB,OAAO,EAAEG,UAAU,CAAC;IAC/D,IAAIgB,UAAU,GAAGD,UAAU,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC9C,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,OAAOA,MAAM;IACf,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGJ,UAAU,CAACA,UAAU,CAACK,MAAM,GAAG,CAAC,CAAC;IAClD,IAAID,UAAU,IAAI,CAACrD,MAAM,CAACqD,UAAU,EAAEpB,UAAU,CAAC,EAAE;MACjD,IAAIsB,OAAO,GAAGrD,SAAS,CAAC6C,UAAU,CAAC;MACnCF,cAAc,CAAC,UAAUW,IAAI,EAAE;QAC7B,OAAO,EAAE,CAACC,MAAM,CAAC/D,kBAAkB,CAAC8D,IAAI,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;MACvD,CAAC,CAAC;MACFjB,QAAQ,CAACW,UAAU,CAAC;IACtB;EACF,CAAC;EACD;EACAnD,KAAK,CAAC4D,SAAS,CAAC,YAAY;IAC1B,IAAId,WAAW,CAACU,MAAM,EAAE;MACtBV,WAAW,CAACe,OAAO,CAAC,UAAUC,UAAU,EAAE;QACxC,IAAIC,aAAa,GAAGzD,cAAc,CAACwD,UAAU,CAAC;QAC9C,IAAIZ,UAAU,GAAG3C,aAAa,CAACwD,aAAa,EAAE/B,OAAO,EAAEG,UAAU,EAAE,IAAI,CAAC,CAACiB,GAAG,CAAC,UAAUY,KAAK,EAAE;UAC5F,IAAIV,MAAM,GAAGU,KAAK,CAACV,MAAM;UACzB,OAAOA,MAAM;QACf,CAAC,CAAC;QACF,IAAIC,UAAU,GAAGL,UAAU,CAACA,UAAU,CAACM,MAAM,GAAG,CAAC,CAAC;QAClD,IAAI,CAACD,UAAU,IAAIA,UAAU,CAACpB,UAAU,CAAC8B,QAAQ,CAAC,IAAI/D,MAAM,CAACqD,UAAU,EAAEpB,UAAU,CAAC,EAAE;UACpFY,cAAc,CAAC,UAAUW,IAAI,EAAE;YAC7B,OAAOA,IAAI,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAE;cAChC,OAAOA,GAAG,KAAKL,UAAU;YAC3B,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9B,OAAO,EAAEc,WAAW,EAAEX,UAAU,CAAC,CAAC;EACtC;EACA,IAAIiC,UAAU,GAAGpE,KAAK,CAACqE,OAAO,CAAC,YAAY;IACzC,OAAO,IAAIC,GAAG,CAACjE,UAAU,CAAC4B,MAAM,CAAC,CAAC;EACpC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAIsC,cAAc,GAAGvE,KAAK,CAACqE,OAAO,CAAC,YAAY;IAC7C,OAAO,IAAIC,GAAG,CAACjE,UAAU,CAAC6B,UAAU,CAAC,CAAC;EACxC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAChB;EACA,IAAIsC,UAAU,GAAG9D,SAAS,CAAC,CAAC;IAC1B+D,WAAW,GAAG5E,cAAc,CAAC2E,UAAU,EAAE,CAAC,CAAC;IAC3CE,gBAAgB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACjCE,mBAAmB,GAAGF,WAAW,CAAC,CAAC,CAAC;EACtC;EACA,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAACC,cAAc,EAAE;IACnDF,mBAAmB,CAACE,cAAc,CAAC;IACnC;IACA7B,gBAAgB,CAAC6B,cAAc,CAAC;EAClC,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACxB,MAAM,EAAE;IAC/C,IAAIyB,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ;IAC9B,IAAIC,YAAY,GAAG9E,MAAM,CAACoD,MAAM,EAAEnB,UAAU,CAAC;IAC7C,OAAO,CAAC4C,QAAQ,KAAKC,YAAY,IAAI5C,cAAc,IAAId,QAAQ,CAAC;EAClE,CAAC;EACD,IAAI2D,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACxD,IAAIC,YAAY,GAAGC,SAAS,CAAC7B,MAAM,GAAG,CAAC,IAAI6B,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC5FhD,QAAQ,CAAC6C,SAAS,CAAC;IACnB,IAAI,CAAC5D,QAAQ,KAAK6D,IAAI,IAAI/C,cAAc,KAAKK,aAAa,KAAK,OAAO,IAAI2C,YAAY,CAAC,CAAC,EAAE;MACxF5D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD;EACA,IAAI+D,aAAa,GAAGvF,KAAK,CAACqE,OAAO,CAAC,YAAY;IAC5C,IAAI9C,WAAW,EAAE;MACf,OAAOe,aAAa;IACtB;IACA,OAAON,OAAO;EAChB,CAAC,EAAE,CAACT,WAAW,EAAEe,aAAa,EAAEN,OAAO,CAAC,CAAC;EACzC;EACA,IAAIwD,aAAa,GAAGxF,KAAK,CAACqE,OAAO,CAAC,YAAY;IAC5C,IAAInB,UAAU,GAAG,CAAC;MAChBlB,OAAO,EAAEuD;IACX,CAAC,CAAC;IACF,IAAIE,WAAW,GAAGF,aAAa;IAC/B,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MAC3B,IAAIC,eAAe,GAAGjB,gBAAgB,CAACkB,CAAC,CAAC;MACzC,IAAIC,aAAa,GAAGJ,WAAW,CAACK,IAAI,CAAC,UAAUxC,MAAM,EAAE;QACrD,OAAOA,MAAM,CAACnB,UAAU,CAAC4D,KAAK,CAAC,KAAKJ,eAAe;MACrD,CAAC,CAAC;MACF,IAAIK,UAAU,GAAGH,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC1D,UAAU,CAAC8B,QAAQ,CAAC;MACjH,IAAI,EAAE+B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACxC,MAAM,CAAC,EAAE;QAChF,OAAO,OAAO;MAChB;MACAiC,WAAW,GAAGO,UAAU;MACxB9C,UAAU,CAAC+C,IAAI,CAAC;QACdjE,OAAO,EAAEgE;MACX,CAAC,CAAC;IACJ,CAAC;IACD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,gBAAgB,CAAClB,MAAM,EAAEoC,CAAC,IAAI,CAAC,EAAE;MACnD,IAAIM,IAAI,GAAGR,KAAK,CAAC,CAAC;MAClB,IAAIQ,IAAI,KAAK,OAAO,EAAE;IACxB;IACA,OAAOhD,UAAU;EACnB,CAAC,EAAE,CAACqC,aAAa,EAAEb,gBAAgB,EAAEvC,UAAU,CAAC,CAAC;EACjD;EACA,IAAIgE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,gBAAgB,EAAE9C,MAAM,EAAE;IACzE,IAAIwB,YAAY,CAACxB,MAAM,CAAC,EAAE;MACxB2B,YAAY,CAACmB,gBAAgB,EAAElG,MAAM,CAACoD,MAAM,EAAEnB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClE;EACF,CAAC;EACDxB,WAAW,CAACI,GAAG,EAAEwE,aAAa,EAAEpD,UAAU,EAAEuC,gBAAgB,EAAEE,UAAU,EAAEuB,gBAAgB,CAAC;EAC3F;EACAnG,KAAK,CAAC4D,SAAS,CAAC,YAAY;IAC1B,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,gBAAgB,CAAClB,MAAM,EAAEoC,CAAC,IAAI,CAAC,EAAE;MACnD,IAAIS,qBAAqB;MACzB,IAAIC,QAAQ,GAAG5B,gBAAgB,CAAC6B,KAAK,CAAC,CAAC,EAAEX,CAAC,GAAG,CAAC,CAAC;MAC/C,IAAIY,WAAW,GAAGpG,SAAS,CAACkG,QAAQ,CAAC;MACrC,IAAIG,GAAG,GAAG,CAACJ,qBAAqB,GAAG1E,YAAY,CAAC+E,OAAO,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACM,aAAa,CAAC,qBAAqB,CAAChD,MAAM,CAAC6C,WAAW,CAACI,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;MAClO,IAAIH,GAAG,EAAE;QACPtG,oBAAoB,CAACsG,GAAG,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAC/B,gBAAgB,CAAC,CAAC;EACtB;EACA;EACA,IAAImC,OAAO,GAAG,EAAE,CAAC7F,eAAe,GAAGwE,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIxE,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACgB,OAAO,MAAM,IAAI,IAAIf,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACuC,MAAM,CAAC;EAC9O,IAAIsD,SAAS,GAAG,EAAE5F,KAAK,GAAG,CAAC,CAAC,EAAEvB,eAAe,CAACuB,KAAK,EAAEiB,UAAU,CAAC4D,KAAK,EAAE,WAAW,CAAC,EAAEpG,eAAe,CAACuB,KAAK,EAAET,SAAS,EAAEgB,eAAe,CAAC,EAAE9B,eAAe,CAACuB,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,EAAEA,KAAK,EAAE;EAC1L,IAAI6F,WAAW,GAAGrH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5DQ,QAAQ,EAAE,CAACuF,OAAO,IAAIvF,QAAQ;IAC9Be,QAAQ,EAAE4C,YAAY;IACtB+B,QAAQ,EAAEpC,UAAU;IACpBqC,YAAY,EAAEzF,UAAU;IACxB4C,UAAU,EAAEA,UAAU;IACtBG,cAAc,EAAEA,cAAc;IAC9BzB,WAAW,EAAEA,WAAW;IACxBgC,YAAY,EAAEA;EAChB,CAAC,CAAC;EACF;EACA,IAAIoC,mBAAmB,GAAGL,OAAO,GAAG,CAAC;IACnC7E,OAAO,EAAE8E;EACX,CAAC,CAAC,GAAGtB,aAAa;EAClB,IAAI2B,WAAW,GAAGD,mBAAmB,CAAC9D,GAAG,CAAC,UAAUgE,GAAG,EAAEC,KAAK,EAAE;IAC9D,IAAIC,aAAa,GAAG5C,gBAAgB,CAAC6B,KAAK,CAAC,CAAC,EAAEc,KAAK,CAAC;IACpD,IAAIE,WAAW,GAAG7C,gBAAgB,CAAC2C,KAAK,CAAC;IACzC,OAAO,aAAarH,KAAK,CAACwH,aAAa,CAAChH,MAAM,EAAEf,QAAQ,CAAC;MACvD0E,GAAG,EAAEkD;IACP,CAAC,EAAEN,WAAW,EAAE;MACd1F,SAAS,EAAEqB,eAAe;MAC1BV,OAAO,EAAEoF,GAAG,CAACpF,OAAO;MACpBsF,aAAa,EAAEA,aAAa;MAC5BC,WAAW,EAAEA;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF;EACA,OAAO,aAAavH,KAAK,CAACwH,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE3H,UAAU,CAAC,EAAE,CAAC6D,MAAM,CAACjB,eAAe,EAAE,QAAQ,CAAC,GAAGvB,WAAW,GAAG,CAAC,CAAC,EAAExB,eAAe,CAACwB,WAAW,EAAE,EAAE,CAACwC,MAAM,CAACjB,eAAe,EAAE,aAAa,CAAC,EAAEmE,OAAO,CAAC,EAAElH,eAAe,CAACwB,WAAW,EAAE,EAAE,CAACwC,MAAM,CAACjB,eAAe,EAAE,MAAM,CAAC,EAAEb,GAAG,CAAC,EAAEV,WAAW,CAAC,CAAC;IACrPJ,GAAG,EAAEY;EACP,CAAC,EAAEwF,WAAW,CAAC;AACjB,CAAC,CAAC;AACF,eAAevG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}