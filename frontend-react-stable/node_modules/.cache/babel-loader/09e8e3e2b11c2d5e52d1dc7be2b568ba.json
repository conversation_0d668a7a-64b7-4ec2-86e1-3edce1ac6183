{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"controls\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport getMiniDecimal, { toFixed } from './utils/MiniDecimal';\nimport StepHandler from './StepHandler';\nimport { getNumberPrecision, num2str, getDecupleSteps, validateNumber } from './utils/numberUtil';\nimport useCursor from './hooks/useCursor';\nimport useFrame from './hooks/useFrame';\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n * format max or min value\n * 1. if isInvalid return null\n * 2. if precision is undefined, return decimal\n * 3. format with precision\n *    I. if max > 0, round down with precision. Example: max= 3.5, precision=0  afterFormat: 3\n *    II. if max < 0, round up with precision. Example: max= -3.5, precision=0  afterFormat: -4\n *    III. if min > 0, round up with precision. Example: min= 3.5, precision=0  afterFormat: 4\n *    IV. if min < 0, round down with precision. Example: max= -3.5, precision=0  afterFormat: -3\n */\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n  return decimalValue.toNumber();\n};\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    min = props.min,\n    max = props.max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    upHandler = props.upHandler,\n    downHandler = props.downHandler,\n    keyboard = props.keyboard,\n    _props$controls = props.controls,\n    controls = _props$controls === void 0 ? true : _props$controls,\n    stringMode = props.stringMode,\n    parser = props.parser,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    onPressEnter = props.onPressEnter,\n    onStep = props.onStep,\n    inputProps = _objectWithoutProperties(props, _excluded);\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focus = _React$useState2[0],\n    setFocus = _React$useState2[1];\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false);\n  var shiftKeyRef = React.useRef(false);\n  // ============================ Value =============================\n  // Real value control\n  var _React$useState3 = React.useState(function () {\n      return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    decimalValue = _React$useState4[0],\n    setDecimalValue = _React$useState4[1];\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  }\n  // ====================== Parser & Formatter ======================\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n    if (precision >= 0) {\n      return precision;\n    }\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]);\n  // >>> Parser\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n    if (parser) {\n      return parser(numStr);\n    }\n    var parsedStr = numStr;\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    }\n    // [Legacy] We still support auto convert `$ 123,456` to `123456`\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]);\n  // >>> Formatter\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n    var str = typeof number === 'number' ? num2str(number) : number;\n    // User typing will not auto format with precision directly\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]);\n  // ========================== InputValue ==========================\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It update with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n  var _React$useState5 = React.useState(function () {\n      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n      if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n        return Number.isNaN(initValue) ? '' : initValue;\n      }\n      return mergedFormatter(decimalValue.toString(), false);\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inputValue = _React$useState6[0],\n    setInternalInputValue = _React$useState6[1];\n  inputValueRef.current = inputValue;\n  // Should always be string\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter(\n    // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  }\n  // >>> Max & Min limit\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max, precision]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min, precision]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]);\n  // Cursor controller\n  var _useCursor = useCursor(inputRef.current, focus),\n    _useCursor2 = _slicedToArray(_useCursor, 2),\n    recordCursor = _useCursor2[0],\n    restoreCursor = _useCursor2[1];\n  // ============================= Data =============================\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    }\n    // target < min\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n    return null;\n  };\n  /**\n   * Check value is in [min, max] range\n   */\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();\n    // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n        // When to fixed. The value may out of min & max range.\n        // 4 in [0, 3.8] => 3.8 => 4 (toFixed)\n        if (!isInRange(updateValue)) {\n          updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision, true));\n        }\n      }\n      // Trigger event\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 ? void 0 : onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue));\n        // Reformat input if value is not controlled\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n      return updateValue;\n    }\n    return decimalValue;\n  };\n  // ========================== User Input ==========================\n  var onNextPromise = useFrame();\n  // >>> Collect input value\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor();\n    // Update inputValue incase input can not parse as number\n    setInternalInputValue(inputStr);\n    // Parse number\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    }\n    // Trigger onInput later to let user customize value if they want do handle something after onChange\n    onInput === null || onInput === void 0 ? void 0 : onInput(inputStr);\n    // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  };\n  // >>> Composition\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  };\n  // >>> Input\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  };\n  // ============================= Step =============================\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    }\n    // Clear typing status since it may caused by up & down key.\n    // We should sync with input value.\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(shiftKeyRef.current ? getDecupleSteps(step) : step);\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 ? void 0 : onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: shiftKeyRef.current ? getDecupleSteps(step) : step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  };\n  // ============================ Flush =============================\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed\n   */\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue = parsedValue;\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = decimalValue;\n    }\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n  // Solve the issue of the event triggering sequence when entering numbers in chinese input (Safari)\n  var onBeforeInput = function onBeforeInput() {\n    userTypingRef.current = true;\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var which = event.which,\n      shiftKey = event.shiftKey;\n    userTypingRef.current = true;\n    if (shiftKey) {\n      shiftKeyRef.current = true;\n    } else {\n      shiftKeyRef.current = false;\n    }\n    if (which === KeyCode.ENTER) {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 ? void 0 : onPressEnter(event);\n    }\n    if (keyboard === false) {\n      return;\n    }\n    // Do step\n    if (!compositionRef.current && [KeyCode.UP, KeyCode.DOWN].includes(which)) {\n      onInternalStep(KeyCode.UP === which);\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n    shiftKeyRef.current = false;\n  };\n  // >>> Focus & Blur\n  var onBlur = function onBlur() {\n    flushInputValue(false);\n    setFocus(false);\n    userTypingRef.current = false;\n  };\n  // ========================== Controlled ==========================\n  // Input by precision\n  useLayoutUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision]);\n  // Input by value\n  useLayoutUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue));\n    // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]);\n  // ============================ Cursor ============================\n  useLayoutUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]);\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames, \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), _defineProperty(_classNames, \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue)), _classNames)),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBeforeInput: onBeforeInput\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nInputNumber.displayName = 'InputNumber';\nexport default InputNumber;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "classNames", "KeyCode", "useLayoutUpdateEffect", "composeRef", "getMiniDecimal", "toFixed", "<PERSON><PERSON><PERSON><PERSON>", "getNumberPrecision", "num2str", "getDecupleSteps", "validateNumber", "useCursor", "useFrame", "getDecimalValue", "stringMode", "decimalValue", "isEmpty", "toString", "toNumber", "getDecimalIfValidate", "value", "decimal", "isInvalidate", "InputNumber", "forwardRef", "props", "ref", "_classNames", "_props$prefixCls", "prefixCls", "className", "style", "min", "max", "_props$step", "step", "defaultValue", "disabled", "readOnly", "up<PERSON><PERSON><PERSON>", "downHandler", "keyboard", "_props$controls", "controls", "parser", "formatter", "precision", "decimalSeparator", "onChange", "onInput", "onPressEnter", "onStep", "inputProps", "inputClassName", "concat", "inputRef", "useRef", "_React$useState", "useState", "_React$useState2", "focus", "setFocus", "userTypingRef", "compositionRef", "shiftKeyRef", "_React$useState3", "_React$useState4", "setDecimalValue", "setUncontrolledDecimalValue", "newDecimal", "undefined", "getPrecision", "useCallback", "numStr", "userTyping", "Math", "mergedParser", "num", "String", "parsedStr", "replace", "inputValueRef", "mergedFormatter", "number", "input", "current", "str", "mergedPrecision", "separatorStr", "_React$useState5", "initValue", "includes", "Number", "isNaN", "_React$useState6", "inputValue", "setInternalInputValue", "setInputValue", "newValue", "maxDecimal", "useMemo", "minDecimal", "upDisabled", "lessEquals", "downDisabled", "_useCursor", "_useCursor2", "recordCursor", "restoreCursor", "getRangeValue", "target", "isInRange", "triggerValueUpdate", "updateValue", "isRangeValidate", "equals", "onNextPromise", "collectInputValue", "inputStr", "finalValue", "finalDecimal", "nextInputStr", "onCompositionStart", "onCompositionEnd", "onInternalInput", "e", "onInternalStep", "up", "_inputRef$current", "stepDecimal", "negate", "add", "updatedValue", "offset", "type", "flushInputValue", "parsedValue", "formatValue", "onBeforeInput", "onKeyDown", "event", "which", "shift<PERSON>ey", "ENTER", "UP", "DOWN", "preventDefault", "onKeyUp", "onBlur", "currentParsedValue", "createElement", "onFocus", "upNode", "downNode", "autoComplete", "role", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-input-number/es/InputNumber.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"min\", \"max\", \"step\", \"defaultValue\", \"value\", \"disabled\", \"readOnly\", \"upHandler\", \"downHandler\", \"keyboard\", \"controls\", \"stringMode\", \"parser\", \"formatter\", \"precision\", \"decimalSeparator\", \"onChange\", \"onInput\", \"onPressEnter\", \"onStep\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport getMiniDecimal, { toFixed } from './utils/MiniDecimal';\nimport StepHandler from './StepHandler';\nimport { getNumberPrecision, num2str, getDecupleSteps, validateNumber } from './utils/numberUtil';\nimport useCursor from './hooks/useCursor';\nimport useFrame from './hooks/useFrame';\n/**\n * We support `stringMode` which need handle correct type when user call in onChange\n * format max or min value\n * 1. if isInvalid return null\n * 2. if precision is undefined, return decimal\n * 3. format with precision\n *    I. if max > 0, round down with precision. Example: max= 3.5, precision=0  afterFormat: 3\n *    II. if max < 0, round up with precision. Example: max= -3.5, precision=0  afterFormat: -4\n *    III. if min > 0, round up with precision. Example: min= 3.5, precision=0  afterFormat: 4\n *    IV. if min < 0, round down with precision. Example: max= -3.5, precision=0  afterFormat: -3\n */\nvar getDecimalValue = function getDecimalValue(stringMode, decimalValue) {\n  if (stringMode || decimalValue.isEmpty()) {\n    return decimalValue.toString();\n  }\n  return decimalValue.toNumber();\n};\nvar getDecimalIfValidate = function getDecimalIfValidate(value) {\n  var decimal = getMiniDecimal(value);\n  return decimal.isInvalidate() ? null : decimal;\n};\nvar InputNumber = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input-number' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    min = props.min,\n    max = props.max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    upHandler = props.upHandler,\n    downHandler = props.downHandler,\n    keyboard = props.keyboard,\n    _props$controls = props.controls,\n    controls = _props$controls === void 0 ? true : _props$controls,\n    stringMode = props.stringMode,\n    parser = props.parser,\n    formatter = props.formatter,\n    precision = props.precision,\n    decimalSeparator = props.decimalSeparator,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    onPressEnter = props.onPressEnter,\n    onStep = props.onStep,\n    inputProps = _objectWithoutProperties(props, _excluded);\n  var inputClassName = \"\".concat(prefixCls, \"-input\");\n  var inputRef = React.useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focus = _React$useState2[0],\n    setFocus = _React$useState2[1];\n  var userTypingRef = React.useRef(false);\n  var compositionRef = React.useRef(false);\n  var shiftKeyRef = React.useRef(false);\n  // ============================ Value =============================\n  // Real value control\n  var _React$useState3 = React.useState(function () {\n      return getMiniDecimal(value !== null && value !== void 0 ? value : defaultValue);\n    }),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    decimalValue = _React$useState4[0],\n    setDecimalValue = _React$useState4[1];\n  function setUncontrolledDecimalValue(newDecimal) {\n    if (value === undefined) {\n      setDecimalValue(newDecimal);\n    }\n  }\n  // ====================== Parser & Formatter ======================\n  /**\n   * `precision` is used for formatter & onChange.\n   * It will auto generate by `value` & `step`.\n   * But it will not block user typing.\n   *\n   * Note: Auto generate `precision` is used for legacy logic.\n   * We should remove this since we already support high precision with BigInt.\n   *\n   * @param number  Provide which number should calculate precision\n   * @param userTyping  Change by user typing\n   */\n  var getPrecision = React.useCallback(function (numStr, userTyping) {\n    if (userTyping) {\n      return undefined;\n    }\n    if (precision >= 0) {\n      return precision;\n    }\n    return Math.max(getNumberPrecision(numStr), getNumberPrecision(step));\n  }, [precision, step]);\n  // >>> Parser\n  var mergedParser = React.useCallback(function (num) {\n    var numStr = String(num);\n    if (parser) {\n      return parser(numStr);\n    }\n    var parsedStr = numStr;\n    if (decimalSeparator) {\n      parsedStr = parsedStr.replace(decimalSeparator, '.');\n    }\n    // [Legacy] We still support auto convert `$ 123,456` to `123456`\n    return parsedStr.replace(/[^\\w.-]+/g, '');\n  }, [parser, decimalSeparator]);\n  // >>> Formatter\n  var inputValueRef = React.useRef('');\n  var mergedFormatter = React.useCallback(function (number, userTyping) {\n    if (formatter) {\n      return formatter(number, {\n        userTyping: userTyping,\n        input: String(inputValueRef.current)\n      });\n    }\n    var str = typeof number === 'number' ? num2str(number) : number;\n    // User typing will not auto format with precision directly\n    if (!userTyping) {\n      var mergedPrecision = getPrecision(str, userTyping);\n      if (validateNumber(str) && (decimalSeparator || mergedPrecision >= 0)) {\n        // Separator\n        var separatorStr = decimalSeparator || '.';\n        str = toFixed(str, separatorStr, mergedPrecision);\n      }\n    }\n    return str;\n  }, [formatter, getPrecision, decimalSeparator]);\n  // ========================== InputValue ==========================\n  /**\n   * Input text value control\n   *\n   * User can not update input content directly. It update with follow rules by priority:\n   *  1. controlled `value` changed\n   *    * [SPECIAL] Typing like `1.` should not immediately convert to `1`\n   *  2. User typing with format (not precision)\n   *  3. Blur or Enter trigger revalidate\n   */\n  var _React$useState5 = React.useState(function () {\n      var initValue = defaultValue !== null && defaultValue !== void 0 ? defaultValue : value;\n      if (decimalValue.isInvalidate() && ['string', 'number'].includes(_typeof(initValue))) {\n        return Number.isNaN(initValue) ? '' : initValue;\n      }\n      return mergedFormatter(decimalValue.toString(), false);\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    inputValue = _React$useState6[0],\n    setInternalInputValue = _React$useState6[1];\n  inputValueRef.current = inputValue;\n  // Should always be string\n  function setInputValue(newValue, userTyping) {\n    setInternalInputValue(mergedFormatter(\n    // Invalidate number is sometime passed by external control, we should let it go\n    // Otherwise is controlled by internal interactive logic which check by userTyping\n    // You can ref 'show limited value when input is not focused' test for more info.\n    newValue.isInvalidate() ? newValue.toString(false) : newValue.toString(!userTyping), userTyping));\n  }\n  // >>> Max & Min limit\n  var maxDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(max);\n  }, [max, precision]);\n  var minDecimal = React.useMemo(function () {\n    return getDecimalIfValidate(min);\n  }, [min, precision]);\n  var upDisabled = React.useMemo(function () {\n    if (!maxDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return maxDecimal.lessEquals(decimalValue);\n  }, [maxDecimal, decimalValue]);\n  var downDisabled = React.useMemo(function () {\n    if (!minDecimal || !decimalValue || decimalValue.isInvalidate()) {\n      return false;\n    }\n    return decimalValue.lessEquals(minDecimal);\n  }, [minDecimal, decimalValue]);\n  // Cursor controller\n  var _useCursor = useCursor(inputRef.current, focus),\n    _useCursor2 = _slicedToArray(_useCursor, 2),\n    recordCursor = _useCursor2[0],\n    restoreCursor = _useCursor2[1];\n  // ============================= Data =============================\n  /**\n   * Find target value closet within range.\n   * e.g. [11, 28]:\n   *    3  => 11\n   *    23 => 23\n   *    99 => 28\n   */\n  var getRangeValue = function getRangeValue(target) {\n    // target > max\n    if (maxDecimal && !target.lessEquals(maxDecimal)) {\n      return maxDecimal;\n    }\n    // target < min\n    if (minDecimal && !minDecimal.lessEquals(target)) {\n      return minDecimal;\n    }\n    return null;\n  };\n  /**\n   * Check value is in [min, max] range\n   */\n  var isInRange = function isInRange(target) {\n    return !getRangeValue(target);\n  };\n  /**\n   * Trigger `onChange` if value validated and not equals of origin.\n   * Return the value that re-align in range.\n   */\n  var triggerValueUpdate = function triggerValueUpdate(newValue, userTyping) {\n    var updateValue = newValue;\n    var isRangeValidate = isInRange(updateValue) || updateValue.isEmpty();\n    // Skip align value when trigger value is empty.\n    // We just trigger onChange(null)\n    // This should not block user typing\n    if (!updateValue.isEmpty() && !userTyping) {\n      // Revert value in range if needed\n      updateValue = getRangeValue(updateValue) || updateValue;\n      isRangeValidate = true;\n    }\n    if (!readOnly && !disabled && isRangeValidate) {\n      var numStr = updateValue.toString();\n      var mergedPrecision = getPrecision(numStr, userTyping);\n      if (mergedPrecision >= 0) {\n        updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision));\n        // When to fixed. The value may out of min & max range.\n        // 4 in [0, 3.8] => 3.8 => 4 (toFixed)\n        if (!isInRange(updateValue)) {\n          updateValue = getMiniDecimal(toFixed(numStr, '.', mergedPrecision, true));\n        }\n      }\n      // Trigger event\n      if (!updateValue.equals(decimalValue)) {\n        setUncontrolledDecimalValue(updateValue);\n        onChange === null || onChange === void 0 ? void 0 : onChange(updateValue.isEmpty() ? null : getDecimalValue(stringMode, updateValue));\n        // Reformat input if value is not controlled\n        if (value === undefined) {\n          setInputValue(updateValue, userTyping);\n        }\n      }\n      return updateValue;\n    }\n    return decimalValue;\n  };\n  // ========================== User Input ==========================\n  var onNextPromise = useFrame();\n  // >>> Collect input value\n  var collectInputValue = function collectInputValue(inputStr) {\n    recordCursor();\n    // Update inputValue incase input can not parse as number\n    setInternalInputValue(inputStr);\n    // Parse number\n    if (!compositionRef.current) {\n      var finalValue = mergedParser(inputStr);\n      var finalDecimal = getMiniDecimal(finalValue);\n      if (!finalDecimal.isNaN()) {\n        triggerValueUpdate(finalDecimal, true);\n      }\n    }\n    // Trigger onInput later to let user customize value if they want do handle something after onChange\n    onInput === null || onInput === void 0 ? void 0 : onInput(inputStr);\n    // optimize for chinese input experience\n    // https://github.com/ant-design/ant-design/issues/8196\n    onNextPromise(function () {\n      var nextInputStr = inputStr;\n      if (!parser) {\n        nextInputStr = inputStr.replace(/。/g, '.');\n      }\n      if (nextInputStr !== inputStr) {\n        collectInputValue(nextInputStr);\n      }\n    });\n  };\n  // >>> Composition\n  var onCompositionStart = function onCompositionStart() {\n    compositionRef.current = true;\n  };\n  var onCompositionEnd = function onCompositionEnd() {\n    compositionRef.current = false;\n    collectInputValue(inputRef.current.value);\n  };\n  // >>> Input\n  var onInternalInput = function onInternalInput(e) {\n    collectInputValue(e.target.value);\n  };\n  // ============================= Step =============================\n  var onInternalStep = function onInternalStep(up) {\n    var _inputRef$current;\n    // Ignore step since out of range\n    if (up && upDisabled || !up && downDisabled) {\n      return;\n    }\n    // Clear typing status since it may caused by up & down key.\n    // We should sync with input value.\n    userTypingRef.current = false;\n    var stepDecimal = getMiniDecimal(shiftKeyRef.current ? getDecupleSteps(step) : step);\n    if (!up) {\n      stepDecimal = stepDecimal.negate();\n    }\n    var target = (decimalValue || getMiniDecimal(0)).add(stepDecimal.toString());\n    var updatedValue = triggerValueUpdate(target, false);\n    onStep === null || onStep === void 0 ? void 0 : onStep(getDecimalValue(stringMode, updatedValue), {\n      offset: shiftKeyRef.current ? getDecupleSteps(step) : step,\n      type: up ? 'up' : 'down'\n    });\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  };\n  // ============================ Flush =============================\n  /**\n   * Flush current input content to trigger value change & re-formatter input if needed\n   */\n  var flushInputValue = function flushInputValue(userTyping) {\n    var parsedValue = getMiniDecimal(mergedParser(inputValue));\n    var formatValue = parsedValue;\n    if (!parsedValue.isNaN()) {\n      // Only validate value or empty value can be re-fill to inputValue\n      // Reassign the formatValue within ranged of trigger control\n      formatValue = triggerValueUpdate(parsedValue, userTyping);\n    } else {\n      formatValue = decimalValue;\n    }\n    if (value !== undefined) {\n      // Reset back with controlled value first\n      setInputValue(decimalValue, false);\n    } else if (!formatValue.isNaN()) {\n      // Reset input back since no validate value\n      setInputValue(formatValue, false);\n    }\n  };\n  // Solve the issue of the event triggering sequence when entering numbers in chinese input (Safari)\n  var onBeforeInput = function onBeforeInput() {\n    userTypingRef.current = true;\n  };\n  var onKeyDown = function onKeyDown(event) {\n    var which = event.which,\n      shiftKey = event.shiftKey;\n    userTypingRef.current = true;\n    if (shiftKey) {\n      shiftKeyRef.current = true;\n    } else {\n      shiftKeyRef.current = false;\n    }\n    if (which === KeyCode.ENTER) {\n      if (!compositionRef.current) {\n        userTypingRef.current = false;\n      }\n      flushInputValue(false);\n      onPressEnter === null || onPressEnter === void 0 ? void 0 : onPressEnter(event);\n    }\n    if (keyboard === false) {\n      return;\n    }\n    // Do step\n    if (!compositionRef.current && [KeyCode.UP, KeyCode.DOWN].includes(which)) {\n      onInternalStep(KeyCode.UP === which);\n      event.preventDefault();\n    }\n  };\n  var onKeyUp = function onKeyUp() {\n    userTypingRef.current = false;\n    shiftKeyRef.current = false;\n  };\n  // >>> Focus & Blur\n  var onBlur = function onBlur() {\n    flushInputValue(false);\n    setFocus(false);\n    userTypingRef.current = false;\n  };\n  // ========================== Controlled ==========================\n  // Input by precision\n  useLayoutUpdateEffect(function () {\n    if (!decimalValue.isInvalidate()) {\n      setInputValue(decimalValue, false);\n    }\n  }, [precision]);\n  // Input by value\n  useLayoutUpdateEffect(function () {\n    var newValue = getMiniDecimal(value);\n    setDecimalValue(newValue);\n    var currentParsedValue = getMiniDecimal(mergedParser(inputValue));\n    // When user typing from `1.2` to `1.`, we should not convert to `1` immediately.\n    // But let it go if user set `formatter`\n    if (!newValue.equals(currentParsedValue) || !userTypingRef.current || formatter) {\n      // Update value as effect\n      setInputValue(newValue, userTypingRef.current);\n    }\n  }, [value]);\n  // ============================ Cursor ============================\n  useLayoutUpdateEffect(function () {\n    if (formatter) {\n      restoreCursor();\n    }\n  }, [inputValue]);\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(prefixCls, className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focus), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-readonly\"), readOnly), _defineProperty(_classNames, \"\".concat(prefixCls, \"-not-a-number\"), decimalValue.isNaN()), _defineProperty(_classNames, \"\".concat(prefixCls, \"-out-of-range\"), !decimalValue.isInvalidate() && !isInRange(decimalValue)), _classNames)),\n    style: style,\n    onFocus: function onFocus() {\n      setFocus(true);\n    },\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    onCompositionStart: onCompositionStart,\n    onCompositionEnd: onCompositionEnd,\n    onBeforeInput: onBeforeInput\n  }, controls && /*#__PURE__*/React.createElement(StepHandler, {\n    prefixCls: prefixCls,\n    upNode: upHandler,\n    downNode: downHandler,\n    upDisabled: upDisabled,\n    downDisabled: downDisabled,\n    onStep: onInternalStep\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(inputClassName, \"-wrap\")\n  }, /*#__PURE__*/React.createElement(\"input\", _extends({\n    autoComplete: \"off\",\n    role: \"spinbutton\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": decimalValue.isInvalidate() ? null : decimalValue.toString(),\n    step: step\n  }, inputProps, {\n    ref: composeRef(inputRef, ref),\n    className: inputClassName,\n    value: inputValue,\n    onChange: onInternalInput,\n    disabled: disabled,\n    readOnly: readOnly\n  }))));\n});\nInputNumber.displayName = 'InputNumber';\nexport default InputNumber;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,CAAC;AACrS,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,qBAAqB,QAAQ,kCAAkC;AACxE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,cAAc,IAAIC,OAAO,QAAQ,qBAAqB;AAC7D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,EAAEC,OAAO,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACjG,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,UAAU,EAAEC,YAAY,EAAE;EACvE,IAAID,UAAU,IAAIC,YAAY,CAACC,OAAO,CAAC,CAAC,EAAE;IACxC,OAAOD,YAAY,CAACE,QAAQ,CAAC,CAAC;EAChC;EACA,OAAOF,YAAY,CAACG,QAAQ,CAAC,CAAC;AAChC,CAAC;AACD,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAE;EAC9D,IAAIC,OAAO,GAAGjB,cAAc,CAACgB,KAAK,CAAC;EACnC,OAAOC,OAAO,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGD,OAAO;AAChD,CAAC;AACD,IAAIE,WAAW,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIC,WAAW;EACf,IAAIC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EE,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,GAAG,GAAGP,KAAK,CAACO,GAAG;IACfC,GAAG,GAAGR,KAAK,CAACQ,GAAG;IACfC,WAAW,GAAGT,KAAK,CAACU,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC/CE,YAAY,GAAGX,KAAK,CAACW,YAAY;IACjChB,KAAK,GAAGK,KAAK,CAACL,KAAK;IACnBiB,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,eAAe,GAAGjB,KAAK,CAACkB,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9D5B,UAAU,GAAGW,KAAK,CAACX,UAAU;IAC7B8B,MAAM,GAAGnB,KAAK,CAACmB,MAAM;IACrBC,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IAC3BC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,gBAAgB,GAAGtB,KAAK,CAACsB,gBAAgB;IACzCC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;IACzBC,OAAO,GAAGxB,KAAK,CAACwB,OAAO;IACvBC,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACjCC,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;IACrBC,UAAU,GAAGvD,wBAAwB,CAAC4B,KAAK,EAAE3B,SAAS,CAAC;EACzD,IAAIuD,cAAc,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,QAAQ,CAAC;EACnD,IAAI0B,QAAQ,GAAGxD,KAAK,CAACyD,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,eAAe,GAAG1D,KAAK,CAAC2D,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAG/D,cAAc,CAAC6D,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,aAAa,GAAG/D,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;EACvC,IAAIO,cAAc,GAAGhE,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;EACxC,IAAIQ,WAAW,GAAGjE,KAAK,CAACyD,MAAM,CAAC,KAAK,CAAC;EACrC;EACA;EACA,IAAIS,gBAAgB,GAAGlE,KAAK,CAAC2D,QAAQ,CAAC,YAAY;MAC9C,OAAOtD,cAAc,CAACgB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGgB,YAAY,CAAC;IAClF,CAAC,CAAC;IACF8B,gBAAgB,GAAGtE,cAAc,CAACqE,gBAAgB,EAAE,CAAC,CAAC;IACtDlD,YAAY,GAAGmD,gBAAgB,CAAC,CAAC,CAAC;IAClCC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACvC,SAASE,2BAA2BA,CAACC,UAAU,EAAE;IAC/C,IAAIjD,KAAK,KAAKkD,SAAS,EAAE;MACvBH,eAAe,CAACE,UAAU,CAAC;IAC7B;EACF;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIE,YAAY,GAAGxE,KAAK,CAACyE,WAAW,CAAC,UAAUC,MAAM,EAAEC,UAAU,EAAE;IACjE,IAAIA,UAAU,EAAE;MACd,OAAOJ,SAAS;IAClB;IACA,IAAIxB,SAAS,IAAI,CAAC,EAAE;MAClB,OAAOA,SAAS;IAClB;IACA,OAAO6B,IAAI,CAAC1C,GAAG,CAAC1B,kBAAkB,CAACkE,MAAM,CAAC,EAAElE,kBAAkB,CAAC4B,IAAI,CAAC,CAAC;EACvE,CAAC,EAAE,CAACW,SAAS,EAAEX,IAAI,CAAC,CAAC;EACrB;EACA,IAAIyC,YAAY,GAAG7E,KAAK,CAACyE,WAAW,CAAC,UAAUK,GAAG,EAAE;IAClD,IAAIJ,MAAM,GAAGK,MAAM,CAACD,GAAG,CAAC;IACxB,IAAIjC,MAAM,EAAE;MACV,OAAOA,MAAM,CAAC6B,MAAM,CAAC;IACvB;IACA,IAAIM,SAAS,GAAGN,MAAM;IACtB,IAAI1B,gBAAgB,EAAE;MACpBgC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAACjC,gBAAgB,EAAE,GAAG,CAAC;IACtD;IACA;IACA,OAAOgC,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC3C,CAAC,EAAE,CAACpC,MAAM,EAAEG,gBAAgB,CAAC,CAAC;EAC9B;EACA,IAAIkC,aAAa,GAAGlF,KAAK,CAACyD,MAAM,CAAC,EAAE,CAAC;EACpC,IAAI0B,eAAe,GAAGnF,KAAK,CAACyE,WAAW,CAAC,UAAUW,MAAM,EAAET,UAAU,EAAE;IACpE,IAAI7B,SAAS,EAAE;MACb,OAAOA,SAAS,CAACsC,MAAM,EAAE;QACvBT,UAAU,EAAEA,UAAU;QACtBU,KAAK,EAAEN,MAAM,CAACG,aAAa,CAACI,OAAO;MACrC,CAAC,CAAC;IACJ;IACA,IAAIC,GAAG,GAAG,OAAOH,MAAM,KAAK,QAAQ,GAAG3E,OAAO,CAAC2E,MAAM,CAAC,GAAGA,MAAM;IAC/D;IACA,IAAI,CAACT,UAAU,EAAE;MACf,IAAIa,eAAe,GAAGhB,YAAY,CAACe,GAAG,EAAEZ,UAAU,CAAC;MACnD,IAAIhE,cAAc,CAAC4E,GAAG,CAAC,KAAKvC,gBAAgB,IAAIwC,eAAe,IAAI,CAAC,CAAC,EAAE;QACrE;QACA,IAAIC,YAAY,GAAGzC,gBAAgB,IAAI,GAAG;QAC1CuC,GAAG,GAAGjF,OAAO,CAACiF,GAAG,EAAEE,YAAY,EAAED,eAAe,CAAC;MACnD;IACF;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAACzC,SAAS,EAAE0B,YAAY,EAAExB,gBAAgB,CAAC,CAAC;EAC/C;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI0C,gBAAgB,GAAG1F,KAAK,CAAC2D,QAAQ,CAAC,YAAY;MAC9C,IAAIgC,SAAS,GAAGtD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGhB,KAAK;MACvF,IAAIL,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACqE,QAAQ,CAAChG,OAAO,CAAC+F,SAAS,CAAC,CAAC,EAAE;QACpF,OAAOE,MAAM,CAACC,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE,GAAGA,SAAS;MACjD;MACA,OAAOR,eAAe,CAACnE,YAAY,CAACE,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;IACxD,CAAC,CAAC;IACF6E,gBAAgB,GAAGlG,cAAc,CAAC6F,gBAAgB,EAAE,CAAC,CAAC;IACtDM,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7Cb,aAAa,CAACI,OAAO,GAAGU,UAAU;EAClC;EACA,SAASE,aAAaA,CAACC,QAAQ,EAAExB,UAAU,EAAE;IAC3CsB,qBAAqB,CAACd,eAAe;IACrC;IACA;IACA;IACAgB,QAAQ,CAAC5E,YAAY,CAAC,CAAC,GAAG4E,QAAQ,CAACjF,QAAQ,CAAC,KAAK,CAAC,GAAGiF,QAAQ,CAACjF,QAAQ,CAAC,CAACyD,UAAU,CAAC,EAAEA,UAAU,CAAC,CAAC;EACnG;EACA;EACA,IAAIyB,UAAU,GAAGpG,KAAK,CAACqG,OAAO,CAAC,YAAY;IACzC,OAAOjF,oBAAoB,CAACc,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,EAAEa,SAAS,CAAC,CAAC;EACpB,IAAIuD,UAAU,GAAGtG,KAAK,CAACqG,OAAO,CAAC,YAAY;IACzC,OAAOjF,oBAAoB,CAACa,GAAG,CAAC;EAClC,CAAC,EAAE,CAACA,GAAG,EAAEc,SAAS,CAAC,CAAC;EACpB,IAAIwD,UAAU,GAAGvG,KAAK,CAACqG,OAAO,CAAC,YAAY;IACzC,IAAI,CAACD,UAAU,IAAI,CAACpF,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAO6E,UAAU,CAACI,UAAU,CAACxF,YAAY,CAAC;EAC5C,CAAC,EAAE,CAACoF,UAAU,EAAEpF,YAAY,CAAC,CAAC;EAC9B,IAAIyF,YAAY,GAAGzG,KAAK,CAACqG,OAAO,CAAC,YAAY;IAC3C,IAAI,CAACC,UAAU,IAAI,CAACtF,YAAY,IAAIA,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAOP,YAAY,CAACwF,UAAU,CAACF,UAAU,CAAC;EAC5C,CAAC,EAAE,CAACA,UAAU,EAAEtF,YAAY,CAAC,CAAC;EAC9B;EACA,IAAI0F,UAAU,GAAG9F,SAAS,CAAC4C,QAAQ,CAAC8B,OAAO,EAAEzB,KAAK,CAAC;IACjD8C,WAAW,GAAG9G,cAAc,CAAC6G,UAAU,EAAE,CAAC,CAAC;IAC3CE,YAAY,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC7BE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;EAChC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAE;IACjD;IACA,IAAIX,UAAU,IAAI,CAACW,MAAM,CAACP,UAAU,CAACJ,UAAU,CAAC,EAAE;MAChD,OAAOA,UAAU;IACnB;IACA;IACA,IAAIE,UAAU,IAAI,CAACA,UAAU,CAACE,UAAU,CAACO,MAAM,CAAC,EAAE;MAChD,OAAOT,UAAU;IACnB;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACE,IAAIU,SAAS,GAAG,SAASA,SAASA,CAACD,MAAM,EAAE;IACzC,OAAO,CAACD,aAAa,CAACC,MAAM,CAAC;EAC/B,CAAC;EACD;AACF;AACA;AACA;EACE,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAACd,QAAQ,EAAExB,UAAU,EAAE;IACzE,IAAIuC,WAAW,GAAGf,QAAQ;IAC1B,IAAIgB,eAAe,GAAGH,SAAS,CAACE,WAAW,CAAC,IAAIA,WAAW,CAACjG,OAAO,CAAC,CAAC;IACrE;IACA;IACA;IACA,IAAI,CAACiG,WAAW,CAACjG,OAAO,CAAC,CAAC,IAAI,CAAC0D,UAAU,EAAE;MACzC;MACAuC,WAAW,GAAGJ,aAAa,CAACI,WAAW,CAAC,IAAIA,WAAW;MACvDC,eAAe,GAAG,IAAI;IACxB;IACA,IAAI,CAAC5E,QAAQ,IAAI,CAACD,QAAQ,IAAI6E,eAAe,EAAE;MAC7C,IAAIzC,MAAM,GAAGwC,WAAW,CAAChG,QAAQ,CAAC,CAAC;MACnC,IAAIsE,eAAe,GAAGhB,YAAY,CAACE,MAAM,EAAEC,UAAU,CAAC;MACtD,IAAIa,eAAe,IAAI,CAAC,EAAE;QACxB0B,WAAW,GAAG7G,cAAc,CAACC,OAAO,CAACoE,MAAM,EAAE,GAAG,EAAEc,eAAe,CAAC,CAAC;QACnE;QACA;QACA,IAAI,CAACwB,SAAS,CAACE,WAAW,CAAC,EAAE;UAC3BA,WAAW,GAAG7G,cAAc,CAACC,OAAO,CAACoE,MAAM,EAAE,GAAG,EAAEc,eAAe,EAAE,IAAI,CAAC,CAAC;QAC3E;MACF;MACA;MACA,IAAI,CAAC0B,WAAW,CAACE,MAAM,CAACpG,YAAY,CAAC,EAAE;QACrCqD,2BAA2B,CAAC6C,WAAW,CAAC;QACxCjE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiE,WAAW,CAACjG,OAAO,CAAC,CAAC,GAAG,IAAI,GAAGH,eAAe,CAACC,UAAU,EAAEmG,WAAW,CAAC,CAAC;QACrI;QACA,IAAI7F,KAAK,KAAKkD,SAAS,EAAE;UACvB2B,aAAa,CAACgB,WAAW,EAAEvC,UAAU,CAAC;QACxC;MACF;MACA,OAAOuC,WAAW;IACpB;IACA,OAAOlG,YAAY;EACrB,CAAC;EACD;EACA,IAAIqG,aAAa,GAAGxG,QAAQ,CAAC,CAAC;EAC9B;EACA,IAAIyG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;IAC3DX,YAAY,CAAC,CAAC;IACd;IACAX,qBAAqB,CAACsB,QAAQ,CAAC;IAC/B;IACA,IAAI,CAACvD,cAAc,CAACsB,OAAO,EAAE;MAC3B,IAAIkC,UAAU,GAAG3C,YAAY,CAAC0C,QAAQ,CAAC;MACvC,IAAIE,YAAY,GAAGpH,cAAc,CAACmH,UAAU,CAAC;MAC7C,IAAI,CAACC,YAAY,CAAC3B,KAAK,CAAC,CAAC,EAAE;QACzBmB,kBAAkB,CAACQ,YAAY,EAAE,IAAI,CAAC;MACxC;IACF;IACA;IACAvE,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqE,QAAQ,CAAC;IACnE;IACA;IACAF,aAAa,CAAC,YAAY;MACxB,IAAIK,YAAY,GAAGH,QAAQ;MAC3B,IAAI,CAAC1E,MAAM,EAAE;QACX6E,YAAY,GAAGH,QAAQ,CAACtC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAC5C;MACA,IAAIyC,YAAY,KAAKH,QAAQ,EAAE;QAC7BD,iBAAiB,CAACI,YAAY,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD3D,cAAc,CAACsB,OAAO,GAAG,IAAI;EAC/B,CAAC;EACD,IAAIsC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD5D,cAAc,CAACsB,OAAO,GAAG,KAAK;IAC9BgC,iBAAiB,CAAC9D,QAAQ,CAAC8B,OAAO,CAACjE,KAAK,CAAC;EAC3C,CAAC;EACD;EACA,IAAIwG,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;IAChDR,iBAAiB,CAACQ,CAAC,CAACf,MAAM,CAAC1F,KAAK,CAAC;EACnC,CAAC;EACD;EACA,IAAI0G,cAAc,GAAG,SAASA,cAAcA,CAACC,EAAE,EAAE;IAC/C,IAAIC,iBAAiB;IACrB;IACA,IAAID,EAAE,IAAIzB,UAAU,IAAI,CAACyB,EAAE,IAAIvB,YAAY,EAAE;MAC3C;IACF;IACA;IACA;IACA1C,aAAa,CAACuB,OAAO,GAAG,KAAK;IAC7B,IAAI4C,WAAW,GAAG7H,cAAc,CAAC4D,WAAW,CAACqB,OAAO,GAAG5E,eAAe,CAAC0B,IAAI,CAAC,GAAGA,IAAI,CAAC;IACpF,IAAI,CAAC4F,EAAE,EAAE;MACPE,WAAW,GAAGA,WAAW,CAACC,MAAM,CAAC,CAAC;IACpC;IACA,IAAIpB,MAAM,GAAG,CAAC/F,YAAY,IAAIX,cAAc,CAAC,CAAC,CAAC,EAAE+H,GAAG,CAACF,WAAW,CAAChH,QAAQ,CAAC,CAAC,CAAC;IAC5E,IAAImH,YAAY,GAAGpB,kBAAkB,CAACF,MAAM,EAAE,KAAK,CAAC;IACpD3D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACtC,eAAe,CAACC,UAAU,EAAEsH,YAAY,CAAC,EAAE;MAChGC,MAAM,EAAErE,WAAW,CAACqB,OAAO,GAAG5E,eAAe,CAAC0B,IAAI,CAAC,GAAGA,IAAI;MAC1DmG,IAAI,EAAEP,EAAE,GAAG,IAAI,GAAG;IACpB,CAAC,CAAC;IACF,CAACC,iBAAiB,GAAGzE,QAAQ,CAAC8B,OAAO,MAAM,IAAI,IAAI2C,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACpE,KAAK,CAAC,CAAC;EACtH,CAAC;EACD;EACA;AACF;AACA;EACE,IAAI2E,eAAe,GAAG,SAASA,eAAeA,CAAC7D,UAAU,EAAE;IACzD,IAAI8D,WAAW,GAAGpI,cAAc,CAACwE,YAAY,CAACmB,UAAU,CAAC,CAAC;IAC1D,IAAI0C,WAAW,GAAGD,WAAW;IAC7B,IAAI,CAACA,WAAW,CAAC3C,KAAK,CAAC,CAAC,EAAE;MACxB;MACA;MACA4C,WAAW,GAAGzB,kBAAkB,CAACwB,WAAW,EAAE9D,UAAU,CAAC;IAC3D,CAAC,MAAM;MACL+D,WAAW,GAAG1H,YAAY;IAC5B;IACA,IAAIK,KAAK,KAAKkD,SAAS,EAAE;MACvB;MACA2B,aAAa,CAAClF,YAAY,EAAE,KAAK,CAAC;IACpC,CAAC,MAAM,IAAI,CAAC0H,WAAW,CAAC5C,KAAK,CAAC,CAAC,EAAE;MAC/B;MACAI,aAAa,CAACwC,WAAW,EAAE,KAAK,CAAC;IACnC;EACF,CAAC;EACD;EACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C5E,aAAa,CAACuB,OAAO,GAAG,IAAI;EAC9B,CAAC;EACD,IAAIsD,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACrBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BhF,aAAa,CAACuB,OAAO,GAAG,IAAI;IAC5B,IAAIyD,QAAQ,EAAE;MACZ9E,WAAW,CAACqB,OAAO,GAAG,IAAI;IAC5B,CAAC,MAAM;MACLrB,WAAW,CAACqB,OAAO,GAAG,KAAK;IAC7B;IACA,IAAIwD,KAAK,KAAK5I,OAAO,CAAC8I,KAAK,EAAE;MAC3B,IAAI,CAAChF,cAAc,CAACsB,OAAO,EAAE;QAC3BvB,aAAa,CAACuB,OAAO,GAAG,KAAK;MAC/B;MACAkD,eAAe,CAAC,KAAK,CAAC;MACtBrF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC0F,KAAK,CAAC;IACjF;IACA,IAAInG,QAAQ,KAAK,KAAK,EAAE;MACtB;IACF;IACA;IACA,IAAI,CAACsB,cAAc,CAACsB,OAAO,IAAI,CAACpF,OAAO,CAAC+I,EAAE,EAAE/I,OAAO,CAACgJ,IAAI,CAAC,CAACtD,QAAQ,CAACkD,KAAK,CAAC,EAAE;MACzEf,cAAc,CAAC7H,OAAO,CAAC+I,EAAE,KAAKH,KAAK,CAAC;MACpCD,KAAK,CAACM,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BrF,aAAa,CAACuB,OAAO,GAAG,KAAK;IAC7BrB,WAAW,CAACqB,OAAO,GAAG,KAAK;EAC7B,CAAC;EACD;EACA,IAAI+D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAC7Bb,eAAe,CAAC,KAAK,CAAC;IACtB1E,QAAQ,CAAC,KAAK,CAAC;IACfC,aAAa,CAACuB,OAAO,GAAG,KAAK;EAC/B,CAAC;EACD;EACA;EACAnF,qBAAqB,CAAC,YAAY;IAChC,IAAI,CAACa,YAAY,CAACO,YAAY,CAAC,CAAC,EAAE;MAChC2E,aAAa,CAAClF,YAAY,EAAE,KAAK,CAAC;IACpC;EACF,CAAC,EAAE,CAAC+B,SAAS,CAAC,CAAC;EACf;EACA5C,qBAAqB,CAAC,YAAY;IAChC,IAAIgG,QAAQ,GAAG9F,cAAc,CAACgB,KAAK,CAAC;IACpC+C,eAAe,CAAC+B,QAAQ,CAAC;IACzB,IAAImD,kBAAkB,GAAGjJ,cAAc,CAACwE,YAAY,CAACmB,UAAU,CAAC,CAAC;IACjE;IACA;IACA,IAAI,CAACG,QAAQ,CAACiB,MAAM,CAACkC,kBAAkB,CAAC,IAAI,CAACvF,aAAa,CAACuB,OAAO,IAAIxC,SAAS,EAAE;MAC/E;MACAoD,aAAa,CAACC,QAAQ,EAAEpC,aAAa,CAACuB,OAAO,CAAC;IAChD;EACF,CAAC,EAAE,CAACjE,KAAK,CAAC,CAAC;EACX;EACAlB,qBAAqB,CAAC,YAAY;IAChC,IAAI2C,SAAS,EAAE;MACb+D,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;EAChB;EACA,OAAO,aAAahG,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAC7CxH,SAAS,EAAE9B,UAAU,CAAC6B,SAAS,EAAEC,SAAS,GAAGH,WAAW,GAAG,CAAC,CAAC,EAAEjC,eAAe,CAACiC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,UAAU,CAAC,EAAE+B,KAAK,CAAC,EAAElE,eAAe,CAACiC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,WAAW,CAAC,EAAEQ,QAAQ,CAAC,EAAE3C,eAAe,CAACiC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,WAAW,CAAC,EAAES,QAAQ,CAAC,EAAE5C,eAAe,CAACiC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC,EAAEd,YAAY,CAAC8E,KAAK,CAAC,CAAC,CAAC,EAAEnG,eAAe,CAACiC,WAAW,EAAE,EAAE,CAAC2B,MAAM,CAACzB,SAAS,EAAE,eAAe,CAAC,EAAE,CAACd,YAAY,CAACO,YAAY,CAAC,CAAC,IAAI,CAACyF,SAAS,CAAChG,YAAY,CAAC,CAAC,EAAEY,WAAW,CAAC,CAAC;IACngBI,KAAK,EAAEA,KAAK;IACZwH,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B1F,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC;IACDuF,MAAM,EAAEA,MAAM;IACdT,SAAS,EAAEA,SAAS;IACpBQ,OAAO,EAAEA,OAAO;IAChBzB,kBAAkB,EAAEA,kBAAkB;IACtCC,gBAAgB,EAAEA,gBAAgB;IAClCe,aAAa,EAAEA;EACjB,CAAC,EAAE/F,QAAQ,IAAI,aAAa5C,KAAK,CAACuJ,aAAa,CAAChJ,WAAW,EAAE;IAC3DuB,SAAS,EAAEA,SAAS;IACpB2H,MAAM,EAAEjH,SAAS;IACjBkH,QAAQ,EAAEjH,WAAW;IACrB8D,UAAU,EAAEA,UAAU;IACtBE,YAAY,EAAEA,YAAY;IAC1BrD,MAAM,EAAE2E;EACV,CAAC,CAAC,EAAE,aAAa/H,KAAK,CAACuJ,aAAa,CAAC,KAAK,EAAE;IAC1CxH,SAAS,EAAE,EAAE,CAACwB,MAAM,CAACD,cAAc,EAAE,OAAO;EAC9C,CAAC,EAAE,aAAatD,KAAK,CAACuJ,aAAa,CAAC,OAAO,EAAE7J,QAAQ,CAAC;IACpDiK,YAAY,EAAE,KAAK;IACnBC,IAAI,EAAE,YAAY;IAClB,eAAe,EAAE3H,GAAG;IACpB,eAAe,EAAEC,GAAG;IACpB,eAAe,EAAElB,YAAY,CAACO,YAAY,CAAC,CAAC,GAAG,IAAI,GAAGP,YAAY,CAACE,QAAQ,CAAC,CAAC;IAC7EkB,IAAI,EAAEA;EACR,CAAC,EAAEiB,UAAU,EAAE;IACb1B,GAAG,EAAEvB,UAAU,CAACoD,QAAQ,EAAE7B,GAAG,CAAC;IAC9BI,SAAS,EAAEuB,cAAc;IACzBjC,KAAK,EAAE2E,UAAU;IACjB/C,QAAQ,EAAE4E,eAAe;IACzBvF,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACFf,WAAW,CAACqI,WAAW,GAAG,aAAa;AACvC,eAAerI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}