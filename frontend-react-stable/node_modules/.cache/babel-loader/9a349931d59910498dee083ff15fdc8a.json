{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport * as React from 'react';\nimport { attachType<PERSON><PERSON>, getKeyThenIncreaseKey, typeList } from '..';\nimport { ConfigConsumer } from '../../config-provider';\nexport default function createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer;\n    // We create a proxy to handle delay created instance\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = useRCNotification(proxy),\n      _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || getKeyThenIncreaseKey();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n          return resolve(true);\n        };\n        getRcNotificationInstance(_extends(_extends({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n            instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps(_extends(_extends({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n      result.promise = closePromise;\n      return result;\n    }\n    // Fill functions\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    typeList.forEach(function (type) {\n      return attachTypeApi(hookApiRef.current, type);\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n  return useMessage;\n}", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "useRCNotification", "React", "attachTypeApi", "getKeyThenIncreaseKey", "typeList", "ConfigConsumer", "createUseMessage", "getRcNotificationInstance", "getRCNoticeProps", "useMessage", "getPrefixCls", "getPopupContainer", "innerInstance", "proxy", "add", "noticeProps", "<PERSON><PERSON><PERSON><PERSON>", "component", "_useRCNotification", "_useRCNotification2", "hookNotify", "holder", "notify", "args", "customizePrefixCls", "prefixCls", "mergedPrefixCls", "rootPrefixCls", "target", "key", "closePromise", "Promise", "resolve", "callback", "onClose", "_ref", "instance", "result", "removeNotice", "then", "filled", "rejected", "promise", "hookApiRef", "useRef", "current", "open", "for<PERSON>ach", "type", "createElement", "context"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/message/hooks/useMessage.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useRCNotification from \"rc-notification/es/useNotification\";\nimport * as React from 'react';\nimport { attachType<PERSON><PERSON>, getKeyThenIncreaseKey, typeList } from '..';\nimport { ConfigConsumer } from '../../config-provider';\nexport default function createUseMessage(getRcNotificationInstance, getRCNoticeProps) {\n  var useMessage = function useMessage() {\n    // We can only get content by render\n    var getPrefixCls;\n    var getPopupContainer;\n    // We create a proxy to handle delay created instance\n    var innerInstance = null;\n    var proxy = {\n      add: function add(noticeProps, holderCallback) {\n        innerInstance === null || innerInstance === void 0 ? void 0 : innerInstance.component.add(noticeProps, holderCallback);\n      }\n    };\n    var _useRCNotification = useRCNotification(proxy),\n      _useRCNotification2 = _slicedToArray(_useRCNotification, 2),\n      hookNotify = _useRCNotification2[0],\n      holder = _useRCNotification2[1];\n    function notify(args) {\n      var customizePrefixCls = args.prefixCls;\n      var mergedPrefixCls = getPrefixCls('message', customizePrefixCls);\n      var rootPrefixCls = getPrefixCls();\n      var target = args.key || getKeyThenIncreaseKey();\n      var closePromise = new Promise(function (resolve) {\n        var callback = function callback() {\n          if (typeof args.onClose === 'function') {\n            args.onClose();\n          }\n          return resolve(true);\n        };\n        getRcNotificationInstance(_extends(_extends({}, args), {\n          prefixCls: mergedPrefixCls,\n          rootPrefixCls: rootPrefixCls,\n          getPopupContainer: getPopupContainer\n        }), function (_ref) {\n          var prefixCls = _ref.prefixCls,\n            instance = _ref.instance;\n          innerInstance = instance;\n          hookNotify(getRCNoticeProps(_extends(_extends({}, args), {\n            key: target,\n            onClose: callback\n          }), prefixCls));\n        });\n      });\n      var result = function result() {\n        if (innerInstance) {\n          innerInstance.removeNotice(target);\n        }\n      };\n      result.then = function (filled, rejected) {\n        return closePromise.then(filled, rejected);\n      };\n      result.promise = closePromise;\n      return result;\n    }\n    // Fill functions\n    var hookApiRef = React.useRef({});\n    hookApiRef.current.open = notify;\n    typeList.forEach(function (type) {\n      return attachTypeApi(hookApiRef.current, type);\n    });\n    return [hookApiRef.current, /*#__PURE__*/React.createElement(ConfigConsumer, {\n      key: \"holder\"\n    }, function (context) {\n      getPrefixCls = context.getPrefixCls;\n      getPopupContainer = context.getPopupContainer;\n      return holder;\n    })];\n  };\n  return useMessage;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,qBAAqB,EAAEC,QAAQ,QAAQ,IAAI;AACnE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,eAAe,SAASC,gBAAgBA,CAACC,yBAAyB,EAAEC,gBAAgB,EAAE;EACpF,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC;IACA,IAAIC,YAAY;IAChB,IAAIC,iBAAiB;IACrB;IACA,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,KAAK,GAAG;MACVC,GAAG,EAAE,SAASA,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAE;QAC7CJ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,SAAS,CAACH,GAAG,CAACC,WAAW,EAAEC,cAAc,CAAC;MACxH;IACF,CAAC;IACD,IAAIE,kBAAkB,GAAGlB,iBAAiB,CAACa,KAAK,CAAC;MAC/CM,mBAAmB,GAAGpB,cAAc,CAACmB,kBAAkB,EAAE,CAAC,CAAC;MAC3DE,UAAU,GAAGD,mBAAmB,CAAC,CAAC,CAAC;MACnCE,MAAM,GAAGF,mBAAmB,CAAC,CAAC,CAAC;IACjC,SAASG,MAAMA,CAACC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAGD,IAAI,CAACE,SAAS;MACvC,IAAIC,eAAe,GAAGhB,YAAY,CAAC,SAAS,EAAEc,kBAAkB,CAAC;MACjE,IAAIG,aAAa,GAAGjB,YAAY,CAAC,CAAC;MAClC,IAAIkB,MAAM,GAAGL,IAAI,CAACM,GAAG,IAAI1B,qBAAqB,CAAC,CAAC;MAChD,IAAI2B,YAAY,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QAChD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,IAAI,OAAOV,IAAI,CAACW,OAAO,KAAK,UAAU,EAAE;YACtCX,IAAI,CAACW,OAAO,CAAC,CAAC;UAChB;UACA,OAAOF,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QACDzB,yBAAyB,CAACT,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,IAAI,CAAC,EAAE;UACrDE,SAAS,EAAEC,eAAe;UAC1BC,aAAa,EAAEA,aAAa;UAC5BhB,iBAAiB,EAAEA;QACrB,CAAC,CAAC,EAAE,UAAUwB,IAAI,EAAE;UAClB,IAAIV,SAAS,GAAGU,IAAI,CAACV,SAAS;YAC5BW,QAAQ,GAAGD,IAAI,CAACC,QAAQ;UAC1BxB,aAAa,GAAGwB,QAAQ;UACxBhB,UAAU,CAACZ,gBAAgB,CAACV,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,IAAI,CAAC,EAAE;YACvDM,GAAG,EAAED,MAAM;YACXM,OAAO,EAAED;UACX,CAAC,CAAC,EAAER,SAAS,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIY,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;QAC7B,IAAIzB,aAAa,EAAE;UACjBA,aAAa,CAAC0B,YAAY,CAACV,MAAM,CAAC;QACpC;MACF,CAAC;MACDS,MAAM,CAACE,IAAI,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;QACxC,OAAOX,YAAY,CAACS,IAAI,CAACC,MAAM,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MACDJ,MAAM,CAACK,OAAO,GAAGZ,YAAY;MAC7B,OAAOO,MAAM;IACf;IACA;IACA,IAAIM,UAAU,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,CAAC,CAAC,CAAC;IACjCD,UAAU,CAACE,OAAO,CAACC,IAAI,GAAGxB,MAAM;IAChClB,QAAQ,CAAC2C,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC/B,OAAO9C,aAAa,CAACyC,UAAU,CAACE,OAAO,EAAEG,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,OAAO,CAACL,UAAU,CAACE,OAAO,EAAE,aAAa5C,KAAK,CAACgD,aAAa,CAAC5C,cAAc,EAAE;MAC3EwB,GAAG,EAAE;IACP,CAAC,EAAE,UAAUqB,OAAO,EAAE;MACpBxC,YAAY,GAAGwC,OAAO,CAACxC,YAAY;MACnCC,iBAAiB,GAAGuC,OAAO,CAACvC,iBAAiB;MAC7C,OAAOU,MAAM;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAOZ,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}