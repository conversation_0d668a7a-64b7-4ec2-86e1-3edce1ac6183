{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Radio,Upload,Input,Select,Button,Alert,Progress,Typography,Space,Divider,message,Spin,Row,Col}from'antd';import{InboxOutlined,PlayCircleOutlined,DeleteOutlined,PlusOutlined}from'@ant-design/icons';import{dataCleaningAPI}from'../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Dragger}=Upload;const{Option}=Select;const DataCleaningPage=()=>{const[dataSource,setDataSource]=useState('local');const[processingMode,setProcessingMode]=useState('single');const[uploadedFiles,setUploadedFiles]=useState([]);const[folderPath,setFolderPath]=useState('');const[availableFiles,setAvailableFiles]=useState([]);const[selectedFiles,setSelectedFiles]=useState([]);const[outputDir,setOutputDir]=useState('');const[loading,setLoading]=useState(false);const[filesLoading,setFilesLoading]=useState(false);const[progress,setProgress]=useState(0);const[result,setResult]=useState(null);// 批量任务相关状态\nconst[batchTasks,setBatchTasks]=useState([]);const[batchLoading,setBatchLoading]=useState(false);const[batchProgress,setBatchProgress]=useState({});// 批量任务管理函数\nconst addBatchTask=()=>{const newTask={id:Date.now().toString(),inputDir:'',outputDir:'',fileCount:0};setBatchTasks([...batchTasks,newTask]);};const updateBatchTask=(id,field,value)=>{setBatchTasks(batchTasks.map(task=>task.id===id?{...task,[field]:value}:task));};const removeBatchTask=id=>{setBatchTasks(batchTasks.filter(task=>task.id!==id));};const validateBatchTask=async task=>{try{const response=await dataCleaningAPI.listFiles(task.inputDir);const files=response.data.files||[];const txtFiles=files.filter(file=>file.toLowerCase().endsWith('.txt'));updateBatchTask(task.id,'fileCount',txtFiles.length.toString());return txtFiles.length>0;}catch(error){message.error(`验证目录 ${task.inputDir} 失败`);return false;}};const startBatchAnalysis=async()=>{// 验证所有任务\nconst validTasks=[];for(let i=0;i<batchTasks.length;i++){const task=batchTasks[i];const taskName=`任务${i+1}`;if(!task.inputDir||!task.outputDir){message.error(`请完善 \"${taskName}\" 的配置`);return;}const isValid=await validateBatchTask(task);if(isValid){validTasks.push({...task,taskName});}}if(validTasks.length===0){message.error('没有有效的批量任务');return;}setBatchLoading(true);try{// 调用批量分析API\nconst response=await dataCleaningAPI.batchAnalyze({tasks:validTasks.map((task,index)=>({customer:`任务${index+1}`,input_dir:task.inputDir,output_dir:task.outputDir}))});if(response.data.success){const batchId=response.data.batch_id;message.success(`批量任务已启动，任务ID: ${batchId}`);// 开始监控批量任务进度\nmonitorBatchProgress(batchId);}else{message.error('批量任务启动失败');}}catch(error){var _error$response,_error$response$data;message.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'批量分析失败');}finally{setBatchLoading(false);}};const monitorBatchProgress=async batchId=>{const maxAttempts=60;// 10分钟监控\nlet attempt=0;const checkProgress=async()=>{try{const response=await dataCleaningAPI.getBatchStatus(batchId);console.log('批量任务状态响应:',response.data);// 添加调试日志\nif(response.data&&response.data.success){const{status,progress,current_step,error}=response.data;console.log(`批量任务状态: ${status}, 进度: ${progress}%`);// 添加调试日志\n// 更新进度状态\nsetBatchProgress(prev=>({...prev,[batchId]:progress||0}));if(status==='completed'){message.success('批量分析完成！');setBatchLoading(false);console.log('批量任务成功完成');return;}else if(status==='failed'){const errorMsg=error||'未知错误';message.error(`批量分析失败: ${errorMsg}`);setBatchLoading(false);console.error('批量任务失败:',errorMsg);return;}// 如果任务还在进行中，继续监控\nif(attempt<maxAttempts&&(status==='running'||status==='pending'||!status)){attempt++;setTimeout(checkProgress,5000);// 5秒后再次检查\n}else if(attempt>=maxAttempts){// 超时时不显示失败，而是提示用户手动检查\nmessage.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');setBatchLoading(false);console.warn('批量任务监控超时');}}else{// API响应格式问题，但不立即判定为失败\nconsole.warn('批量任务状态API响应格式异常:',response.data);if(attempt<maxAttempts){attempt++;setTimeout(checkProgress,5000);}else{message.warning('无法获取批量任务状态，请手动检查任务结果');setBatchLoading(false);}}}catch(error){console.error('监控批量任务进度失败:',error);// 网络错误或API错误，增加重试次数\nif(attempt<10){// 增加重试次数到10次\nattempt++;console.log(`批量任务监控重试 ${attempt}/10`);setTimeout(checkProgress,5000);}else{// 多次重试后仍失败，提示用户但不判定任务失败\nmessage.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');setBatchLoading(false);console.error('批量任务监控最终失败');}}};// 开始监控\ncheckProgress();};// 获取本地文件列表\nconst fetchLocalFiles=async()=>{if(!folderPath)return;setFilesLoading(true);try{const response=await dataCleaningAPI.listFiles(folderPath);const files=response.data.files||[];setAvailableFiles(files);// 自动选择所有txt文件\nconst txtFiles=files.filter(file=>file.toLowerCase().endsWith('.txt'));if(txtFiles.length>0){setSelectedFiles(txtFiles);message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);}else{setSelectedFiles([]);if(files.length>0){message.warning('目录中没有找到TXT文件');}}}catch(error){var _error$response2,_error$response2$data;message.error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||'获取文件列表失败');setAvailableFiles([]);setSelectedFiles([]);}finally{setFilesLoading(false);}};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(dataSource==='local'&&folderPath&&folderPath.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchLocalFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[dataSource,folderPath]);// 文件上传配置\nconst uploadProps={name:'files',multiple:true,accept:'.txt',beforeUpload:()=>false,// 阻止自动上传\nonChange:info=>{setUploadedFiles(info.fileList);},onDrop:e=>{console.log('Dropped files',e.dataTransfer.files);}};// 执行数据清洗\nconst handleCleanData=async()=>{// 验证输入\nif(dataSource==='upload'&&uploadedFiles.length===0){message.error('请上传至少一个文件');return;}if(dataSource==='local'&&(!folderPath||selectedFiles.length===0)){message.error('请提供文件夹路径并选择至少一个文件');return;}setLoading(true);setProgress(0);setResult(null);try{let response;if(dataSource==='upload'){const formData=new FormData();uploadedFiles.forEach(file=>{formData.append('files',file.originFileObj);});formData.append('output_dir',outputDir);// 模拟进度更新\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+10;});},500);response=await dataCleaningAPI.cleanData(formData);clearInterval(progressInterval);}else{// 本地文件处理\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+10;});},500);response=await dataCleaningAPI.cleanDataLocal({folder_path:folderPath,selected_files:selectedFiles,output_dir:outputDir});clearInterval(progressInterval);}setProgress(100);setResult(response.data);message.success('数据清洗完成！');}catch(error){var _error$response3,_error$response3$data;message.error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||'数据清洗失败');}finally{setLoading(false);}};const isFormValid=()=>{if(dataSource==='upload'){return uploadedFiles.length>0;}else{return folderPath&&selectedFiles.length>0;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6D41\\u91CF\\u6570\\u636E\\u5206\\u6790\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\u6216\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u4E2D\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\uFF0C\\u5206\\u6790\\u540E\\u751F\\u6210CSV\\u6587\\u4EF6\\u5E76\\u4FDD\\u5B58\\u5230\\u6307\\u5B9A\\u76EE\\u5F55\\u3002\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6570\\u636E\\u5206\\u6790\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5904\\u7406\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:processingMode,onChange:e=>setProcessingMode(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"single\",children:\"\\u5355\\u4E2A\\u76EE\\u5F55\\u5206\\u6790\"}),/*#__PURE__*/_jsx(Radio,{value:\"batch\",children:\"\\u6279\\u91CF\\u76EE\\u5F55\\u5206\\u6790\"})]})]}),processingMode==='single'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6D41\\u91CF\\u6570\\u636E\\u6E90\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:dataSource,onChange:e=>setDataSource(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"local\",children:\"\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Radio,{value:\"upload\",children:\"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636ETXT\\u6587\\u4EF6\"})]})]}),dataSource==='local'&&/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u672C\\u5730\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:folderPath,onChange:e=>setFolderPath(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/aizhinengqingxicepingdaliu\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchLocalFiles,loading:filesLoading,disabled:!folderPath,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:filesLoading,children:/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedFiles,onChange:setSelectedFiles,placeholder:\"\\u8BF7\\u9009\\u62E9TXT\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:filesLoading,children:availableFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]})]}),dataSource==='upload'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsxs(Dragger,{...uploadProps,style:{marginTop:8},children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(InboxOutlined,{})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301\\u5355\\u4E2A\\u6216\\u6279\\u91CF\\u4E0A\\u4F20TXT\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:outputDir,onChange:e=>setOutputDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{marginTop:8}})]}),/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleCleanData,loading:loading,disabled:!isFormValid(),className:\"action-button\",children:loading?'正在处理...':'执行流量分析'}),loading&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsx(Text,{children:\"\\u5904\\u7406\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:progress,status:\"active\"})]}),result&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5904\\u7406\\u5B8C\\u6210\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5904\\u7406\\u7ED3\\u679C\\uFF1A\",result.message]}),result.output_file&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u8F93\\u51FA\\u6587\\u4EF6\\uFF1A\",result.output_file]}),result.processed_files&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5904\\u7406\\u7684\\u6587\\u4EF6\\u6570\\uFF1A\",result.processed_files]}),result.total_rows&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u603B\\u884C\\u6570\\uFF1A\",result.total_rows]})]}),type:\"success\",showIcon:true})]}),processingMode==='batch'&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6279\\u91CF\\u4EFB\\u52A1\\u914D\\u7F6E\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[batchTasks.map((task,index)=>/*#__PURE__*/_jsxs(Card,{size:\"small\",style:{marginBottom:16},title:`任务 ${index+1}`,extra:/*#__PURE__*/_jsx(Button,{type:\"text\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),onClick:()=>removeBatchTask(task.id)}),children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8F93\\u5165\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:task.inputDir,onChange:e=>updateBatchTask(task.id,'inputDir',e.target.value),placeholder:\"\\u4F8B\\u5982\\uFF1A/data/input\",style:{marginTop:4}})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:task.outputDir,onChange:e=>updateBatchTask(task.id,'outputDir',e.target.value),placeholder:\"\\u4F8B\\u5982\\uFF1A/data/output\",style:{marginTop:4}})]})]}),task.fileCount!==undefined&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u68C0\\u6D4B\\u5230 \",task.fileCount,\" \\u4E2ATXT\\u6587\\u4EF6\"]})})]},task.id)),/*#__PURE__*/_jsx(Button,{type:\"dashed\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:addBatchTask,style:{width:'100%',marginBottom:16},children:\"\\u6DFB\\u52A0\\u4EFB\\u52A1\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",loading:batchLoading,disabled:batchTasks.length===0,onClick:startBatchAnalysis,style:{width:'100%'},children:batchLoading?'批量分析中...':'开始批量分析'}),batchLoading&&Object.keys(batchProgress).length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:16},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6279\\u91CF\\u4EFB\\u52A1\\u8FDB\\u5EA6\\uFF1A\"}),Object.entries(batchProgress).map(_ref=>{let[taskId,progress]=_ref;return/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Progress,{percent:progress,status:progress===100?'success':'active',format:percent=>`${percent}%`})},taskId);})]})]})]})})]})})]});};export default DataCleaningPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "Progress", "Typography", "Space", "Divider", "message", "Spin", "Row", "Col", "InboxOutlined", "PlayCircleOutlined", "DeleteOutlined", "PlusOutlined", "dataCleaningAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "DataCleaningPage", "dataSource", "setDataSource", "processingMode", "setProcessingMode", "uploadedFiles", "setUploadedFiles", "folderPath", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "loading", "setLoading", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "batchTasks", "setBatchTasks", "batchLoading", "setBatchLoading", "batchProgress", "setBatchProgress", "addBatchTask", "newTask", "id", "Date", "now", "toString", "inputDir", "fileCount", "updateBatchTask", "field", "value", "map", "task", "removeBatchTask", "filter", "validateBatchTask", "response", "listFiles", "files", "data", "txtFiles", "file", "toLowerCase", "endsWith", "length", "error", "startBatchAnalysis", "validTasks", "i", "taskName", "<PERSON><PERSON><PERSON><PERSON>", "push", "batchAnalyze", "tasks", "index", "customer", "input_dir", "output_dir", "success", "batchId", "batch_id", "monitorBatchProgress", "_error$response", "_error$response$data", "detail", "maxAttempts", "attempt", "checkProgress", "getBatchStatus", "console", "log", "status", "current_step", "prev", "errorMsg", "setTimeout", "warning", "warn", "fetchLocalFiles", "_error$response2", "_error$response2$data", "timer", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "dataTransfer", "handleCleanData", "formData", "FormData", "for<PERSON>ach", "append", "originFileObj", "progressInterval", "setInterval", "clearInterval", "cleanData", "cleanDataLocal", "folder_path", "selected_files", "_error$response3", "_error$response3$data", "isFormValid", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "type", "title", "className", "direction", "size", "width", "strong", "Group", "target", "marginTop", "compact", "display", "placeholder", "flex", "onClick", "disabled", "marginLeft", "spinning", "mode", "icon", "percent", "description", "output_file", "processed_files", "total_rows", "showIcon", "extra", "danger", "gutter", "span", "undefined", "Object", "keys", "entries", "_ref", "taskId", "format"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Row,\n  Col,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [processingMode, setProcessingMode] = useState<'single' | 'batch'>('single');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState<Array<{\n    id: string;\n    inputDir: string;\n    outputDir: string;\n    fileCount?: number;\n  }>>([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState<{[key: string]: number}>({});\n\n  // 批量任务管理函数\n  const addBatchTask = () => {\n    const newTask = {\n      id: Date.now().toString(),\n      inputDir: '',\n      outputDir: '',\n      fileCount: 0\n    };\n    setBatchTasks([...batchTasks, newTask]);\n  };\n\n  const updateBatchTask = (id: string, field: string, value: string) => {\n    setBatchTasks(batchTasks.map(task =>\n      task.id === id ? { ...task, [field]: value } : task\n    ));\n  };\n\n  const removeBatchTask = (id: string) => {\n    setBatchTasks(batchTasks.filter(task => task.id !== id));\n  };\n\n  const validateBatchTask = async (task: any) => {\n    try {\n      const response = await dataCleaningAPI.listFiles(task.inputDir);\n      const files = response.data.files || [];\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      updateBatchTask(task.id, 'fileCount', txtFiles.length.toString());\n      return txtFiles.length > 0;\n    } catch (error) {\n      message.error(`验证目录 ${task.inputDir} 失败`);\n      return false;\n    }\n  };\n\n  const startBatchAnalysis = async () => {\n    // 验证所有任务\n    const validTasks = [];\n    for (let i = 0; i < batchTasks.length; i++) {\n      const task = batchTasks[i];\n      const taskName = `任务${i + 1}`;\n\n      if (!task.inputDir || !task.outputDir) {\n        message.error(`请完善 \"${taskName}\" 的配置`);\n        return;\n      }\n\n      const isValid = await validateBatchTask(task);\n      if (isValid) {\n        validTasks.push({...task, taskName});\n      }\n    }\n\n    if (validTasks.length === 0) {\n      message.error('没有有效的批量任务');\n      return;\n    }\n\n    setBatchLoading(true);\n\n    try {\n      // 调用批量分析API\n      const response = await dataCleaningAPI.batchAnalyze({\n        tasks: validTasks.map((task, index) => ({\n          customer: `任务${index + 1}`,\n          input_dir: task.inputDir,\n          output_dir: task.outputDir\n        }))\n      });\n\n      if (response.data.success) {\n        const batchId = response.data.batch_id;\n        message.success(`批量任务已启动，任务ID: ${batchId}`);\n\n        // 开始监控批量任务进度\n        monitorBatchProgress(batchId);\n      } else {\n        message.error('批量任务启动失败');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '批量分析失败');\n    } finally {\n      setBatchLoading(false);\n    }\n  };\n\n  const monitorBatchProgress = async (batchId: string) => {\n    const maxAttempts = 60; // 10分钟监控\n    let attempt = 0;\n\n    const checkProgress = async () => {\n      try {\n        const response = await dataCleaningAPI.getBatchStatus(batchId);\n\n        console.log('批量任务状态响应:', response.data); // 添加调试日志\n\n        if (response.data && response.data.success) {\n          const { status, progress, current_step, error } = response.data;\n\n          console.log(`批量任务状态: ${status}, 进度: ${progress}%`); // 添加调试日志\n\n          // 更新进度状态\n          setBatchProgress(prev => ({\n            ...prev,\n            [batchId]: progress || 0\n          }));\n\n          if (status === 'completed') {\n            message.success('批量分析完成！');\n            setBatchLoading(false);\n            console.log('批量任务成功完成');\n            return;\n          } else if (status === 'failed') {\n            const errorMsg = error || '未知错误';\n            message.error(`批量分析失败: ${errorMsg}`);\n            setBatchLoading(false);\n            console.error('批量任务失败:', errorMsg);\n            return;\n          }\n\n          // 如果任务还在进行中，继续监控\n          if (attempt < maxAttempts && (status === 'running' || status === 'pending' || !status)) {\n            attempt++;\n            setTimeout(checkProgress, 5000); // 5秒后再次检查\n          } else if (attempt >= maxAttempts) {\n            // 超时时不显示失败，而是提示用户手动检查\n            message.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');\n            setBatchLoading(false);\n            console.warn('批量任务监控超时');\n          }\n        } else {\n          // API响应格式问题，但不立即判定为失败\n          console.warn('批量任务状态API响应格式异常:', response.data);\n          if (attempt < maxAttempts) {\n            attempt++;\n            setTimeout(checkProgress, 5000);\n          } else {\n            message.warning('无法获取批量任务状态，请手动检查任务结果');\n            setBatchLoading(false);\n          }\n        }\n      } catch (error: any) {\n        console.error('监控批量任务进度失败:', error);\n\n        // 网络错误或API错误，增加重试次数\n        if (attempt < 10) { // 增加重试次数到10次\n          attempt++;\n          console.log(`批量任务监控重试 ${attempt}/10`);\n          setTimeout(checkProgress, 5000);\n        } else {\n          // 多次重试后仍失败，提示用户但不判定任务失败\n          message.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');\n          setBatchLoading(false);\n          console.error('批量任务监控最终失败');\n        }\n      }\n    };\n\n    // 开始监控\n    checkProgress();\n  };\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 处理模式选择 */}\n          <div>\n            <Text strong>处理模式：</Text>\n            <Radio.Group\n              value={processingMode}\n              onChange={(e) => setProcessingMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个目录分析</Radio>\n              <Radio value=\"batch\">批量目录分析</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 单个目录模式 */}\n          {processingMode === 'single' && (\n            <>\n              {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地目录文件</Radio>\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n              {/* 结果展示 */}\n              {result && (\n                <Alert\n                  message=\"处理完成\"\n                  description={\n                    <div>\n                      <p>处理结果：{result.message}</p>\n                      {result.output_file && <p>输出文件：{result.output_file}</p>}\n                      {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                      {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n              )}\n            </>\n          )}\n\n          {/* 批量目录模式 */}\n          {processingMode === 'batch' && (\n            <>\n              <div>\n                <Text strong>批量任务配置：</Text>\n                <div style={{ marginTop: 16 }}>\n                  {batchTasks.map((task, index) => (\n                    <Card\n                      key={task.id}\n                      size=\"small\"\n                      style={{ marginBottom: 16 }}\n                      title={`任务 ${index + 1}`}\n                      extra={\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          onClick={() => removeBatchTask(task.id)}\n                        />\n                      }\n                    >\n                      <Row gutter={16}>\n                        <Col span={12}>\n                          <Text strong>输入目录：</Text>\n                          <Input\n                            value={task.inputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'inputDir', e.target.value)}\n                            placeholder=\"例如：/data/input\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                        <Col span={12}>\n                          <Text strong>输出目录：</Text>\n                          <Input\n                            value={task.outputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'outputDir', e.target.value)}\n                            placeholder=\"例如：/data/output\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                      </Row>\n                      {task.fileCount !== undefined && (\n                        <div style={{ marginTop: 8 }}>\n                          <Text type=\"secondary\">\n                            检测到 {task.fileCount} 个TXT文件\n                          </Text>\n                        </div>\n                      )}\n                    </Card>\n                  ))}\n\n                  <Button\n                    type=\"dashed\"\n                    icon={<PlusOutlined />}\n                    onClick={addBatchTask}\n                    style={{ width: '100%', marginBottom: 16 }}\n                  >\n                    添加任务\n                  </Button>\n\n                  <Button\n                    type=\"primary\"\n                    size=\"large\"\n                    loading={batchLoading}\n                    disabled={batchTasks.length === 0}\n                    onClick={startBatchAnalysis}\n                    style={{ width: '100%' }}\n                  >\n                    {batchLoading ? '批量分析中...' : '开始批量分析'}\n                  </Button>\n\n                  {/* 批量任务进度显示 */}\n                  {batchLoading && Object.keys(batchProgress).length > 0 && (\n                    <div style={{ marginTop: 16 }}>\n                      <Text strong>批量任务进度：</Text>\n                      {Object.entries(batchProgress).map(([taskId, progress]) => (\n                        <div key={taskId} style={{ marginTop: 8 }}>\n                          <Progress\n                            percent={progress}\n                            status={progress === 100 ? 'success' : 'active'}\n                            format={(percent) => `${percent}%`}\n                          />\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </>\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,QAAQ,CACRC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,GAAG,CACHC,GAAG,KACE,MAAM,CACb,OAASC,aAAa,CAAEC,kBAAkB,CAAEC,cAAc,CAAEC,YAAY,KAAQ,mBAAmB,CACnG,OAASC,eAAe,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAElD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGnB,UAAU,CAClC,KAAM,CAAEoB,OAAQ,CAAC,CAAG1B,MAAM,CAC1B,KAAM,CAAE2B,MAAO,CAAC,CAAGzB,MAAM,CAEzB,KAAM,CAAA0B,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlC,QAAQ,CAAqB,OAAO,CAAC,CACzE,KAAM,CAACmC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpC,QAAQ,CAAqB,QAAQ,CAAC,CAClF,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAQ,EAAE,CAAC,CAC7D,KAAM,CAACuC,UAAU,CAAEC,aAAa,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACyC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1C,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAAC2C,aAAa,CAAEC,gBAAgB,CAAC,CAAG5C,QAAQ,CAAW,EAAE,CAAC,CAChE,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGhD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACiD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACqD,MAAM,CAAEC,SAAS,CAAC,CAAGtD,QAAQ,CAAM,IAAI,CAAC,CAE/C;AACA,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAKxC,EAAE,CAAC,CACP,KAAM,CAACyD,YAAY,CAAEC,eAAe,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC2D,aAAa,CAAEC,gBAAgB,CAAC,CAAG5D,QAAQ,CAA0B,CAAC,CAAC,CAAC,CAE/E;AACA,KAAM,CAAA6D,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,OAAO,CAAG,CACdC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBC,QAAQ,CAAE,EAAE,CACZtB,SAAS,CAAE,EAAE,CACbuB,SAAS,CAAE,CACb,CAAC,CACDZ,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEO,OAAO,CAAC,CAAC,CACzC,CAAC,CAED,KAAM,CAAAO,eAAe,CAAGA,CAACN,EAAU,CAAEO,KAAa,CAAEC,KAAa,GAAK,CACpEf,aAAa,CAACD,UAAU,CAACiB,GAAG,CAACC,IAAI,EAC/BA,IAAI,CAACV,EAAE,GAAKA,EAAE,CAAG,CAAE,GAAGU,IAAI,CAAE,CAACH,KAAK,EAAGC,KAAM,CAAC,CAAGE,IACjD,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIX,EAAU,EAAK,CACtCP,aAAa,CAACD,UAAU,CAACoB,MAAM,CAACF,IAAI,EAAIA,IAAI,CAACV,EAAE,GAAKA,EAAE,CAAC,CAAC,CAC1D,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAG,KAAO,CAAAH,IAAS,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAACyD,SAAS,CAACL,IAAI,CAACN,QAAQ,CAAC,CAC/D,KAAM,CAAAY,KAAK,CAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,EAAI,EAAE,CACvC,KAAM,CAAAE,QAAQ,CAAGF,KAAK,CAACJ,MAAM,CAAEO,IAAY,EACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC,CAEDf,eAAe,CAACI,IAAI,CAACV,EAAE,CAAE,WAAW,CAAEkB,QAAQ,CAACI,MAAM,CAACnB,QAAQ,CAAC,CAAC,CAAC,CACjE,MAAO,CAAAe,QAAQ,CAACI,MAAM,CAAG,CAAC,CAC5B,CAAE,MAAOC,KAAK,CAAE,CACdzE,OAAO,CAACyE,KAAK,CAAC,QAAQb,IAAI,CAACN,QAAQ,KAAK,CAAC,CACzC,MAAO,MAAK,CACd,CACF,CAAC,CAED,KAAM,CAAAoB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC;AACA,KAAM,CAAAC,UAAU,CAAG,EAAE,CACrB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGlC,UAAU,CAAC8B,MAAM,CAAEI,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAhB,IAAI,CAAGlB,UAAU,CAACkC,CAAC,CAAC,CAC1B,KAAM,CAAAC,QAAQ,CAAG,KAAKD,CAAC,CAAG,CAAC,EAAE,CAE7B,GAAI,CAAChB,IAAI,CAACN,QAAQ,EAAI,CAACM,IAAI,CAAC5B,SAAS,CAAE,CACrChC,OAAO,CAACyE,KAAK,CAAC,QAAQI,QAAQ,OAAO,CAAC,CACtC,OACF,CAEA,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAAf,iBAAiB,CAACH,IAAI,CAAC,CAC7C,GAAIkB,OAAO,CAAE,CACXH,UAAU,CAACI,IAAI,CAAC,CAAC,GAAGnB,IAAI,CAAEiB,QAAQ,CAAC,CAAC,CACtC,CACF,CAEA,GAAIF,UAAU,CAACH,MAAM,GAAK,CAAC,CAAE,CAC3BxE,OAAO,CAACyE,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA5B,eAAe,CAAC,IAAI,CAAC,CAErB,GAAI,CACF;AACA,KAAM,CAAAmB,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAACwE,YAAY,CAAC,CAClDC,KAAK,CAAEN,UAAU,CAAChB,GAAG,CAAC,CAACC,IAAI,CAAEsB,KAAK,IAAM,CACtCC,QAAQ,CAAE,KAAKD,KAAK,CAAG,CAAC,EAAE,CAC1BE,SAAS,CAAExB,IAAI,CAACN,QAAQ,CACxB+B,UAAU,CAAEzB,IAAI,CAAC5B,SACnB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,GAAIgC,QAAQ,CAACG,IAAI,CAACmB,OAAO,CAAE,CACzB,KAAM,CAAAC,OAAO,CAAGvB,QAAQ,CAACG,IAAI,CAACqB,QAAQ,CACtCxF,OAAO,CAACsF,OAAO,CAAC,iBAAiBC,OAAO,EAAE,CAAC,CAE3C;AACAE,oBAAoB,CAACF,OAAO,CAAC,CAC/B,CAAC,IAAM,CACLvF,OAAO,CAACyE,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAE,MAAOA,KAAU,CAAE,KAAAiB,eAAA,CAAAC,oBAAA,CACnB3F,OAAO,CAACyE,KAAK,CAAC,EAAAiB,eAAA,CAAAjB,KAAK,CAACT,QAAQ,UAAA0B,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBvB,IAAI,UAAAwB,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,QAAQ,CAAC,CACzD,CAAC,OAAS,CACR/C,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA4C,oBAAoB,CAAG,KAAO,CAAAF,OAAe,EAAK,CACtD,KAAM,CAAAM,WAAW,CAAG,EAAE,CAAE;AACxB,GAAI,CAAAC,OAAO,CAAG,CAAC,CAEf,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAA/B,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAACwF,cAAc,CAACT,OAAO,CAAC,CAE9DU,OAAO,CAACC,GAAG,CAAC,WAAW,CAAElC,QAAQ,CAACG,IAAI,CAAC,CAAE;AAEzC,GAAIH,QAAQ,CAACG,IAAI,EAAIH,QAAQ,CAACG,IAAI,CAACmB,OAAO,CAAE,CAC1C,KAAM,CAAEa,MAAM,CAAE7D,QAAQ,CAAE8D,YAAY,CAAE3B,KAAM,CAAC,CAAGT,QAAQ,CAACG,IAAI,CAE/D8B,OAAO,CAACC,GAAG,CAAC,WAAWC,MAAM,SAAS7D,QAAQ,GAAG,CAAC,CAAE;AAEpD;AACAS,gBAAgB,CAACsD,IAAI,GAAK,CACxB,GAAGA,IAAI,CACP,CAACd,OAAO,EAAGjD,QAAQ,EAAI,CACzB,CAAC,CAAC,CAAC,CAEH,GAAI6D,MAAM,GAAK,WAAW,CAAE,CAC1BnG,OAAO,CAACsF,OAAO,CAAC,SAAS,CAAC,CAC1BzC,eAAe,CAAC,KAAK,CAAC,CACtBoD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvB,OACF,CAAC,IAAM,IAAIC,MAAM,GAAK,QAAQ,CAAE,CAC9B,KAAM,CAAAG,QAAQ,CAAG7B,KAAK,EAAI,MAAM,CAChCzE,OAAO,CAACyE,KAAK,CAAC,WAAW6B,QAAQ,EAAE,CAAC,CACpCzD,eAAe,CAAC,KAAK,CAAC,CACtBoD,OAAO,CAACxB,KAAK,CAAC,SAAS,CAAE6B,QAAQ,CAAC,CAClC,OACF,CAEA;AACA,GAAIR,OAAO,CAAGD,WAAW,GAAKM,MAAM,GAAK,SAAS,EAAIA,MAAM,GAAK,SAAS,EAAI,CAACA,MAAM,CAAC,CAAE,CACtFL,OAAO,EAAE,CACTS,UAAU,CAACR,aAAa,CAAE,IAAI,CAAC,CAAE;AACnC,CAAC,IAAM,IAAID,OAAO,EAAID,WAAW,CAAE,CACjC;AACA7F,OAAO,CAACwG,OAAO,CAAC,+BAA+B,CAAC,CAChD3D,eAAe,CAAC,KAAK,CAAC,CACtBoD,OAAO,CAACQ,IAAI,CAAC,UAAU,CAAC,CAC1B,CACF,CAAC,IAAM,CACL;AACAR,OAAO,CAACQ,IAAI,CAAC,kBAAkB,CAAEzC,QAAQ,CAACG,IAAI,CAAC,CAC/C,GAAI2B,OAAO,CAAGD,WAAW,CAAE,CACzBC,OAAO,EAAE,CACTS,UAAU,CAACR,aAAa,CAAE,IAAI,CAAC,CACjC,CAAC,IAAM,CACL/F,OAAO,CAACwG,OAAO,CAAC,sBAAsB,CAAC,CACvC3D,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CACF,CAAE,MAAO4B,KAAU,CAAE,CACnBwB,OAAO,CAACxB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CAEnC;AACA,GAAIqB,OAAO,CAAG,EAAE,CAAE,CAAE;AAClBA,OAAO,EAAE,CACTG,OAAO,CAACC,GAAG,CAAC,YAAYJ,OAAO,KAAK,CAAC,CACrCS,UAAU,CAACR,aAAa,CAAE,IAAI,CAAC,CACjC,CAAC,IAAM,CACL;AACA/F,OAAO,CAACwG,OAAO,CAAC,iCAAiC,CAAC,CAClD3D,eAAe,CAAC,KAAK,CAAC,CACtBoD,OAAO,CAACxB,KAAK,CAAC,YAAY,CAAC,CAC7B,CACF,CACF,CAAC,CAED;AACAsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAAW,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAChF,UAAU,CAAE,OAEjBW,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAA2B,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAACyD,SAAS,CAACvC,UAAU,CAAC,CAC5D,KAAM,CAAAwC,KAAK,CAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,EAAI,EAAE,CACvCrC,iBAAiB,CAACqC,KAAK,CAAC,CAExB;AACA,KAAM,CAAAE,QAAQ,CAAGF,KAAK,CAACJ,MAAM,CAAEO,IAAY,EACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC,CAED,GAAIH,QAAQ,CAACI,MAAM,CAAG,CAAC,CAAE,CACvBzC,gBAAgB,CAACqC,QAAQ,CAAC,CAC1BpE,OAAO,CAACsF,OAAO,CAAC,SAASlB,QAAQ,CAACI,MAAM,SAAS,CAAC,CACpD,CAAC,IAAM,CACLzC,gBAAgB,CAAC,EAAE,CAAC,CACpB,GAAImC,KAAK,CAACM,MAAM,CAAG,CAAC,CAAE,CACpBxE,OAAO,CAACwG,OAAO,CAAC,cAAc,CAAC,CACjC,CACF,CACF,CAAE,MAAO/B,KAAU,CAAE,KAAAkC,gBAAA,CAAAC,qBAAA,CACnB5G,OAAO,CAACyE,KAAK,CAAC,EAAAkC,gBAAA,CAAAlC,KAAK,CAACT,QAAQ,UAAA2C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxC,IAAI,UAAAyC,qBAAA,iBAApBA,qBAAA,CAAsBhB,MAAM,GAAI,UAAU,CAAC,CACzD/D,iBAAiB,CAAC,EAAE,CAAC,CACrBE,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,OAAS,CACRM,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACAjD,SAAS,CAAC,IAAM,CACd,GAAIgC,UAAU,GAAK,OAAO,EAAIM,UAAU,EAAIA,UAAU,CAAC8C,MAAM,CAAG,CAAC,CAAE,CAAE;AACnE,KAAM,CAAAqC,KAAK,CAAGN,UAAU,CAAC,IAAM,CAC7BG,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMI,YAAY,CAACD,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAACzF,UAAU,CAAEM,UAAU,CAAC,CAAC,CAE5B;AACA,KAAM,CAAAqF,WAAW,CAAG,CAClBC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAEA,CAAA,GAAM,KAAK,CAAE;AAC3BC,QAAQ,CAAGC,IAAS,EAAK,CACvB5F,gBAAgB,CAAC4F,IAAI,CAACC,QAAQ,CAAC,CACjC,CAAC,CACDC,MAAM,CAAGC,CAAM,EAAK,CAClBvB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEsB,CAAC,CAACC,YAAY,CAACvD,KAAK,CAAC,CACpD,CACF,CAAC,CAED;AACA,KAAM,CAAAwD,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAItG,UAAU,GAAK,QAAQ,EAAII,aAAa,CAACgD,MAAM,GAAK,CAAC,CAAE,CACzDxE,OAAO,CAACyE,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA,GAAIrD,UAAU,GAAK,OAAO,GAAK,CAACM,UAAU,EAAII,aAAa,CAAC0C,MAAM,GAAK,CAAC,CAAC,CAAE,CACzExE,OAAO,CAACyE,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEAtC,UAAU,CAAC,IAAI,CAAC,CAChBI,WAAW,CAAC,CAAC,CAAC,CACdE,SAAS,CAAC,IAAI,CAAC,CAEf,GAAI,CACF,GAAI,CAAAuB,QAAQ,CAEZ,GAAI5C,UAAU,GAAK,QAAQ,CAAE,CAC3B,KAAM,CAAAuG,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BpG,aAAa,CAACqG,OAAO,CAAExD,IAAI,EAAK,CAC9BsD,QAAQ,CAACG,MAAM,CAAC,OAAO,CAAEzD,IAAI,CAAC0D,aAAa,CAAC,CAC9C,CAAC,CAAC,CACFJ,QAAQ,CAACG,MAAM,CAAC,YAAY,CAAE9F,SAAS,CAAC,CAExC;AACA,KAAM,CAAAgG,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzC1F,WAAW,CAAE8D,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACd6B,aAAa,CAACF,gBAAgB,CAAC,CAC/B,MAAO,CAAA3B,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEPrC,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAAC2H,SAAS,CAACR,QAAQ,CAAC,CACpDO,aAAa,CAACF,gBAAgB,CAAC,CACjC,CAAC,IAAM,CACL;AACA,KAAM,CAAAA,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzC1F,WAAW,CAAE8D,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACd6B,aAAa,CAACF,gBAAgB,CAAC,CAC/B,MAAO,CAAA3B,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEPrC,QAAQ,CAAG,KAAM,CAAAxD,eAAe,CAAC4H,cAAc,CAAC,CAC9CC,WAAW,CAAE3G,UAAU,CACvB4G,cAAc,CAAExG,aAAa,CAC7BuD,UAAU,CAAErD,SACd,CAAC,CAAC,CACFkG,aAAa,CAACF,gBAAgB,CAAC,CACjC,CAEAzF,WAAW,CAAC,GAAG,CAAC,CAChBE,SAAS,CAACuB,QAAQ,CAACG,IAAI,CAAC,CACxBnE,OAAO,CAACsF,OAAO,CAAC,SAAS,CAAC,CAE5B,CAAE,MAAOb,KAAU,CAAE,KAAA8D,gBAAA,CAAAC,qBAAA,CACnBxI,OAAO,CAACyE,KAAK,CAAC,EAAA8D,gBAAA,CAAA9D,KAAK,CAACT,QAAQ,UAAAuE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBpE,IAAI,UAAAqE,qBAAA,iBAApBA,qBAAA,CAAsB5C,MAAM,GAAI,QAAQ,CAAC,CACzD,CAAC,OAAS,CACRzD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsG,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIrH,UAAU,GAAK,QAAQ,CAAE,CAC3B,MAAO,CAAAI,aAAa,CAACgD,MAAM,CAAG,CAAC,CACjC,CAAC,IAAM,CACL,MAAO,CAAA9C,UAAU,EAAII,aAAa,CAAC0C,MAAM,CAAG,CAAC,CAC/C,CACF,CAAC,CAED,mBACE5D,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACK,KAAK,EAAC4H,KAAK,CAAE,CAAE,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,CAAC,sCAAM,CAAO,CAAC,cAClGhI,IAAA,CAACM,IAAI,EAACgI,IAAI,CAAC,WAAW,CAAAN,QAAA,CAAC,qPAEvB,CAAM,CAAC,cAEPhI,IAAA,CAACX,OAAO,GAAE,CAAC,cAEXW,IAAA,CAACrB,IAAI,EAAC4J,KAAK,CAAC,0BAAM,CAACC,SAAS,CAAC,eAAe,CAAAR,QAAA,cAC1C9H,KAAA,CAACd,KAAK,EAACqJ,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACR,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eAEhE9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB9H,KAAA,CAACtB,KAAK,CAACiK,KAAK,EACV7F,KAAK,CAAEpC,cAAe,CACtB8F,QAAQ,CAAGI,CAAC,EAAKjG,iBAAiB,CAACiG,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CACnDkF,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CAAAf,QAAA,eAExBhI,IAAA,CAACpB,KAAK,EAACoE,KAAK,CAAC,QAAQ,CAAAgF,QAAA,CAAC,sCAAM,CAAO,CAAC,cACpChI,IAAA,CAACpB,KAAK,EAACoE,KAAK,CAAC,OAAO,CAAAgF,QAAA,CAAC,sCAAM,CAAO,CAAC,EACxB,CAAC,EACX,CAAC,CAGLpH,cAAc,GAAK,QAAQ,eAC1BV,KAAA,CAAAE,SAAA,EAAA4H,QAAA,eAEF9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,kDAAQ,CAAM,CAAC,cAC5B9H,KAAA,CAACtB,KAAK,CAACiK,KAAK,EACV7F,KAAK,CAAEtC,UAAW,CAClBgG,QAAQ,CAAGI,CAAC,EAAKnG,aAAa,CAACmG,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CAC/CkF,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CAAAf,QAAA,eAExBhI,IAAA,CAACpB,KAAK,EAACoE,KAAK,CAAC,OAAO,CAAAgF,QAAA,CAAC,kDAAQ,CAAO,CAAC,cACrChI,IAAA,CAACpB,KAAK,EAACoE,KAAK,CAAC,QAAQ,CAAAgF,QAAA,CAAC,qDAAW,CAAO,CAAC,EAC9B,CAAC,EACX,CAAC,CAGLtH,UAAU,GAAK,OAAO,eACrBR,KAAA,CAACd,KAAK,EAACqJ,SAAS,CAAC,UAAU,CAACP,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eACnD9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3B9H,KAAA,CAACpB,KAAK,CAAC+J,KAAK,EAACG,OAAO,MAACd,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAC,CAAEE,OAAO,CAAE,MAAO,CAAE,CAAAjB,QAAA,eAC5DhI,IAAA,CAAClB,KAAK,EACJkE,KAAK,CAAEhC,UAAW,CAClB0F,QAAQ,CAAGI,CAAC,EAAK7F,aAAa,CAAC6F,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CAC/CkG,WAAW,CAAC,gDAAsC,CAClDhB,KAAK,CAAE,CAAEiB,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACFnJ,IAAA,CAAChB,MAAM,EACLsJ,IAAI,CAAC,SAAS,CACdc,OAAO,CAAEpD,eAAgB,CACzBxE,OAAO,CAAEE,YAAa,CACtB2H,QAAQ,CAAE,CAACrI,UAAW,CACtBkH,KAAK,CAAE,CAAEoB,UAAU,CAAE,CAAE,CAAE,CAAAtB,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAEN9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBhI,IAAA,CAACT,IAAI,EAACgK,QAAQ,CAAE7H,YAAa,CAAAsG,QAAA,cAC3BhI,IAAA,CAACjB,MAAM,EACLyK,IAAI,CAAC,UAAU,CACfxG,KAAK,CAAE5B,aAAc,CACrBsF,QAAQ,CAAErF,gBAAiB,CAC3B6H,WAAW,CAAC,mCAAU,CACtBhB,KAAK,CAAE,CAAES,KAAK,CAAE,MAAM,CAAEI,SAAS,CAAE,CAAE,CAAE,CACvCvH,OAAO,CAAEE,YAAa,CAAAsG,QAAA,CAErB9G,cAAc,CAAC+B,GAAG,CAAEU,IAAI,eACvB3D,IAAA,CAACQ,MAAM,EAAYwC,KAAK,CAAEW,IAAK,CAAAqE,QAAA,CAC5BrE,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CAAC,EACD,CACR,CAGAjD,UAAU,GAAK,QAAQ,eACtBR,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB9H,KAAA,CAACK,OAAO,KAAK8F,WAAW,CAAE6B,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CAAAf,QAAA,eAChDhI,IAAA,MAAGwI,SAAS,CAAC,sBAAsB,CAAAR,QAAA,cACjChI,IAAA,CAACN,aAAa,GAAE,CAAC,CAChB,CAAC,cACJM,IAAA,MAAGwI,SAAS,CAAC,iBAAiB,CAAAR,QAAA,CAAC,gFAAa,CAAG,CAAC,cAChDhI,IAAA,MAAGwI,SAAS,CAAC,iBAAiB,CAAAR,QAAA,CAAC,iHAE/B,CAAG,CAAC,EACG,CAAC,EACP,CACN,cAGD9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5BhI,IAAA,CAAClB,KAAK,EACJkE,KAAK,CAAE1B,SAAU,CACjBoF,QAAQ,CAAGI,CAAC,EAAKvF,YAAY,CAACuF,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CAC9CkG,WAAW,CAAC,4BAAkB,CAC9BhB,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CACzB,CAAC,EACC,CAAC,cAGN/I,IAAA,CAAChB,MAAM,EACLsJ,IAAI,CAAC,SAAS,CACdI,IAAI,CAAC,OAAO,CACZe,IAAI,cAAEzJ,IAAA,CAACL,kBAAkB,GAAE,CAAE,CAC7ByJ,OAAO,CAAEpC,eAAgB,CACzBxF,OAAO,CAAEA,OAAQ,CACjB6H,QAAQ,CAAE,CAACtB,WAAW,CAAC,CAAE,CACzBS,SAAS,CAAC,eAAe,CAAAR,QAAA,CAExBxG,OAAO,CAAG,SAAS,CAAG,QAAQ,CACzB,CAAC,CAGRA,OAAO,eACNtB,KAAA,QAAKsI,SAAS,CAAC,kBAAkB,CAAAR,QAAA,eAC/BhI,IAAA,CAACM,IAAI,EAAA0H,QAAA,CAAC,gCAAK,CAAM,CAAC,cAClBhI,IAAA,CAACd,QAAQ,EAACwK,OAAO,CAAE9H,QAAS,CAAC6D,MAAM,CAAC,QAAQ,CAAE,CAAC,EAC5C,CACN,CAGI3D,MAAM,eACL9B,IAAA,CAACf,KAAK,EACJK,OAAO,CAAC,0BAAM,CACdqK,WAAW,cACTzJ,KAAA,QAAA8H,QAAA,eACE9H,KAAA,MAAA8H,QAAA,EAAG,gCAAK,CAAClG,MAAM,CAACxC,OAAO,EAAI,CAAC,CAC3BwC,MAAM,CAAC8H,WAAW,eAAI1J,KAAA,MAAA8H,QAAA,EAAG,gCAAK,CAAClG,MAAM,CAAC8H,WAAW,EAAI,CAAC,CACtD9H,MAAM,CAAC+H,eAAe,eAAI3J,KAAA,MAAA8H,QAAA,EAAG,4CAAO,CAAClG,MAAM,CAAC+H,eAAe,EAAI,CAAC,CAChE/H,MAAM,CAACgI,UAAU,eAAI5J,KAAA,MAAA8H,QAAA,EAAG,0BAAI,CAAClG,MAAM,CAACgI,UAAU,EAAI,CAAC,EACjD,CACN,CACDxB,IAAI,CAAC,SAAS,CACdyB,QAAQ,MACT,CACF,EACD,CACH,CAGAnJ,cAAc,GAAK,OAAO,eACzBZ,IAAA,CAAAI,SAAA,EAAA4H,QAAA,cACE9H,KAAA,QAAA8H,QAAA,eACEhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3B9H,KAAA,QAAKgI,KAAK,CAAE,CAAEa,SAAS,CAAE,EAAG,CAAE,CAAAf,QAAA,EAC3BhG,UAAU,CAACiB,GAAG,CAAC,CAACC,IAAI,CAAEsB,KAAK,gBAC1BtE,KAAA,CAACvB,IAAI,EAEH+J,IAAI,CAAC,OAAO,CACZR,KAAK,CAAE,CAAEG,YAAY,CAAE,EAAG,CAAE,CAC5BE,KAAK,CAAE,MAAM/D,KAAK,CAAG,CAAC,EAAG,CACzBwF,KAAK,cACHhK,IAAA,CAAChB,MAAM,EACLsJ,IAAI,CAAC,MAAM,CACX2B,MAAM,MACNR,IAAI,cAAEzJ,IAAA,CAACJ,cAAc,GAAE,CAAE,CACzBwJ,OAAO,CAAEA,CAAA,GAAMjG,eAAe,CAACD,IAAI,CAACV,EAAE,CAAE,CACzC,CACF,CAAAwF,QAAA,eAED9H,KAAA,CAACV,GAAG,EAAC0K,MAAM,CAAE,EAAG,CAAAlC,QAAA,eACd9H,KAAA,CAACT,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAAnC,QAAA,eACZhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBhI,IAAA,CAAClB,KAAK,EACJkE,KAAK,CAAEE,IAAI,CAACN,QAAS,CACrB8D,QAAQ,CAAGI,CAAC,EAAKhE,eAAe,CAACI,IAAI,CAACV,EAAE,CAAE,UAAU,CAAEsE,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CACtEkG,WAAW,CAAC,+BAAgB,CAC5BhB,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CACzB,CAAC,EACC,CAAC,cACN7I,KAAA,CAACT,GAAG,EAAC0K,IAAI,CAAE,EAAG,CAAAnC,QAAA,eACZhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBhI,IAAA,CAAClB,KAAK,EACJkE,KAAK,CAAEE,IAAI,CAAC5B,SAAU,CACtBoF,QAAQ,CAAGI,CAAC,EAAKhE,eAAe,CAACI,IAAI,CAACV,EAAE,CAAE,WAAW,CAAEsE,CAAC,CAACgC,MAAM,CAAC9F,KAAK,CAAE,CACvEkG,WAAW,CAAC,gCAAiB,CAC7BhB,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CACzB,CAAC,EACC,CAAC,EACH,CAAC,CACL7F,IAAI,CAACL,SAAS,GAAKuH,SAAS,eAC3BpK,IAAA,QAAKkI,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CAAAf,QAAA,cAC3B9H,KAAA,CAACI,IAAI,EAACgI,IAAI,CAAC,WAAW,CAAAN,QAAA,EAAC,qBACjB,CAAC9E,IAAI,CAACL,SAAS,CAAC,wBACtB,EAAM,CAAC,CACJ,CACN,GAvCIK,IAAI,CAACV,EAwCN,CACP,CAAC,cAEFxC,IAAA,CAAChB,MAAM,EACLsJ,IAAI,CAAC,QAAQ,CACbmB,IAAI,cAAEzJ,IAAA,CAACH,YAAY,GAAE,CAAE,CACvBuJ,OAAO,CAAE9G,YAAa,CACtB4F,KAAK,CAAE,CAAES,KAAK,CAAE,MAAM,CAAEN,YAAY,CAAE,EAAG,CAAE,CAAAL,QAAA,CAC5C,0BAED,CAAQ,CAAC,cAEThI,IAAA,CAAChB,MAAM,EACLsJ,IAAI,CAAC,SAAS,CACdI,IAAI,CAAC,OAAO,CACZlH,OAAO,CAAEU,YAAa,CACtBmH,QAAQ,CAAErH,UAAU,CAAC8B,MAAM,GAAK,CAAE,CAClCsF,OAAO,CAAEpF,kBAAmB,CAC5BkE,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,CAExB9F,YAAY,CAAG,UAAU,CAAG,QAAQ,CAC/B,CAAC,CAGRA,YAAY,EAAImI,MAAM,CAACC,IAAI,CAAClI,aAAa,CAAC,CAAC0B,MAAM,CAAG,CAAC,eACpD5D,KAAA,QAAKgI,KAAK,CAAE,CAAEa,SAAS,CAAE,EAAG,CAAE,CAAAf,QAAA,eAC5BhI,IAAA,CAACM,IAAI,EAACsI,MAAM,MAAAZ,QAAA,CAAC,4CAAO,CAAM,CAAC,CAC1BqC,MAAM,CAACE,OAAO,CAACnI,aAAa,CAAC,CAACa,GAAG,CAACuH,IAAA,MAAC,CAACC,MAAM,CAAE7I,QAAQ,CAAC,CAAA4I,IAAA,oBACpDxK,IAAA,QAAkBkI,KAAK,CAAE,CAAEa,SAAS,CAAE,CAAE,CAAE,CAAAf,QAAA,cACxChI,IAAA,CAACd,QAAQ,EACPwK,OAAO,CAAE9H,QAAS,CAClB6D,MAAM,CAAE7D,QAAQ,GAAK,GAAG,CAAG,SAAS,CAAG,QAAS,CAChD8I,MAAM,CAAGhB,OAAO,EAAK,GAAGA,OAAO,GAAI,CACpC,CAAC,EALMe,MAML,CAAC,EACP,CAAC,EACC,CACN,EACE,CAAC,EACH,CAAC,CACN,CACH,EACI,CAAC,CACJ,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}