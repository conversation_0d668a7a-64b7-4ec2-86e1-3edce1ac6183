{"ast": null, "code": "import * as React from 'react';\nexport var CollectionContext = /*#__PURE__*/React.createContext(null);\n/**\n * Collect all the resize event from children ResizeObserver\n */\nexport function Collection(_ref) {\n  var children = _ref.children,\n    onBatchResize = _ref.onBatchResize;\n  var resizeIdRef = React.useRef(0);\n  var resizeInfosRef = React.useRef([]);\n  var onCollectionResize = React.useContext(CollectionContext);\n  var onResize = React.useCallback(function (size, element, data) {\n    resizeIdRef.current += 1;\n    var currentId = resizeIdRef.current;\n    resizeInfosRef.current.push({\n      size: size,\n      element: element,\n      data: data\n    });\n    Promise.resolve().then(function () {\n      if (currentId === resizeIdRef.current) {\n        onBatchResize === null || onBatchResize === void 0 || onBatchResize(resizeInfosRef.current);\n        resizeInfosRef.current = [];\n      }\n    });\n\n    // Continue bubbling if parent exist\n    onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(size, element, data);\n  }, [onBatchResize, onCollectionResize]);\n  return /*#__PURE__*/React.createElement(CollectionContext.Provider, {\n    value: onResize\n  }, children);\n}", "map": {"version": 3, "names": ["React", "CollectionContext", "createContext", "Collection", "_ref", "children", "onBatchResize", "resizeIdRef", "useRef", "resizeInfosRef", "onCollectionResize", "useContext", "onResize", "useCallback", "size", "element", "data", "current", "currentId", "push", "Promise", "resolve", "then", "createElement", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-resize-observer/es/Collection.js"], "sourcesContent": ["import * as React from 'react';\nexport var CollectionContext = /*#__PURE__*/React.createContext(null);\n/**\n * Collect all the resize event from children ResizeObserver\n */\nexport function Collection(_ref) {\n  var children = _ref.children,\n    onBatchResize = _ref.onBatchResize;\n  var resizeIdRef = React.useRef(0);\n  var resizeInfosRef = React.useRef([]);\n  var onCollectionResize = React.useContext(CollectionContext);\n  var onResize = React.useCallback(function (size, element, data) {\n    resizeIdRef.current += 1;\n    var currentId = resizeIdRef.current;\n    resizeInfosRef.current.push({\n      size: size,\n      element: element,\n      data: data\n    });\n    Promise.resolve().then(function () {\n      if (currentId === resizeIdRef.current) {\n        onBatchResize === null || onBatchResize === void 0 || onBatchResize(resizeInfosRef.current);\n        resizeInfosRef.current = [];\n      }\n    });\n\n    // Continue bubbling if parent exist\n    onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(size, element, data);\n  }, [onBatchResize, onCollectionResize]);\n  return /*#__PURE__*/React.createElement(CollectionContext.Provider, {\n    value: onResize\n  }, children);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACrE;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,aAAa,GAAGF,IAAI,CAACE,aAAa;EACpC,IAAIC,WAAW,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC;EACjC,IAAIC,cAAc,GAAGT,KAAK,CAACQ,MAAM,CAAC,EAAE,CAAC;EACrC,IAAIE,kBAAkB,GAAGV,KAAK,CAACW,UAAU,CAACV,iBAAiB,CAAC;EAC5D,IAAIW,QAAQ,GAAGZ,KAAK,CAACa,WAAW,CAAC,UAAUC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAC9DT,WAAW,CAACU,OAAO,IAAI,CAAC;IACxB,IAAIC,SAAS,GAAGX,WAAW,CAACU,OAAO;IACnCR,cAAc,CAACQ,OAAO,CAACE,IAAI,CAAC;MAC1BL,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAEA;IACR,CAAC,CAAC;IACFI,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;MACjC,IAAIJ,SAAS,KAAKX,WAAW,CAACU,OAAO,EAAE;QACrCX,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAACG,cAAc,CAACQ,OAAO,CAAC;QAC3FR,cAAc,CAACQ,OAAO,GAAG,EAAE;MAC7B;IACF,CAAC,CAAC;;IAEF;IACAP,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACI,IAAI,EAAEC,OAAO,EAAEC,IAAI,CAAC;EACzG,CAAC,EAAE,CAACV,aAAa,EAAEI,kBAAkB,CAAC,CAAC;EACvC,OAAO,aAAaV,KAAK,CAACuB,aAAa,CAACtB,iBAAiB,CAACuB,QAAQ,EAAE;IAClEC,KAAK,EAAEb;EACT,CAAC,EAAEP,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}