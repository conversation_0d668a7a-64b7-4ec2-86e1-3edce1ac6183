{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = utcTime;\nvar _index = require(\"../../../lib-vendor/d3-time/src/index.js\");\nvar _index2 = require(\"../../../lib-vendor/d3-time-format/src/index.js\");\nvar _time = require(\"./time.js\");\nvar _init = require(\"./init.js\");\nfunction utcTime() {\n  return _init.initRange.apply((0, _time.calendar)(_index.utcTicks, _index.utcTickInterval, _index.utcYear, _index.utcMonth, _index.utcWeek, _index.utcDay, _index.utcHour, _index.utcMinute, _index.utcSecond, _index2.utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "utcTime", "_index", "require", "_index2", "_time", "_init", "initRange", "apply", "calendar", "utcTicks", "utcTickInterval", "utcYear", "utcMonth", "utcWeek", "utcDay", "utcHour", "utcMinute", "utcSecond", "utcFormat", "domain", "Date", "UTC", "arguments"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/utcTime.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = utcTime;\n\nvar _index = require(\"../../../lib-vendor/d3-time/src/index.js\");\n\nvar _index2 = require(\"../../../lib-vendor/d3-time-format/src/index.js\");\n\nvar _time = require(\"./time.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction utcTime() {\n  return _init.initRange.apply((0, _time.calendar)(_index.utcTicks, _index.utcTickInterval, _index.utcYear, _index.utcMonth, _index.utcWeek, _index.utcDay, _index.utcHour, _index.utcMinute, _index.utcSecond, _index2.utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,OAAO;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,0CAA0C,CAAC;AAEhE,IAAIC,OAAO,GAAGD,OAAO,CAAC,iDAAiD,CAAC;AAExE,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIG,KAAK,GAAGH,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASF,OAAOA,CAAA,EAAG;EACjB,OAAOK,KAAK,CAACC,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,EAAEH,KAAK,CAACI,QAAQ,EAAEP,MAAM,CAACQ,QAAQ,EAAER,MAAM,CAACS,eAAe,EAAET,MAAM,CAACU,OAAO,EAAEV,MAAM,CAACW,QAAQ,EAAEX,MAAM,CAACY,OAAO,EAAEZ,MAAM,CAACa,MAAM,EAAEb,MAAM,CAACc,OAAO,EAAEd,MAAM,CAACe,SAAS,EAAEf,MAAM,CAACgB,SAAS,EAAEd,OAAO,CAACe,SAAS,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAED,IAAI,CAACC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEC,SAAS,CAAC;AACnS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}