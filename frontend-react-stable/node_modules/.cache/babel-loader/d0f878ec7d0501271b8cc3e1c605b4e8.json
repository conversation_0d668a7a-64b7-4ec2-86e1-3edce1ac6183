{"ast": null, "code": "'use strict';\n\nvar AxiosError = require('../core/AxiosError');\nvar utils = require('../utils');\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction CanceledError(message) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED);\n  this.name = 'CanceledError';\n}\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\nmodule.exports = CanceledError;", "map": {"version": 3, "names": ["AxiosError", "require", "utils", "CanceledError", "message", "call", "ERR_CANCELED", "name", "inherits", "__CANCEL__", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/cancel/CanceledError.js"], "sourcesContent": ["'use strict';\n\nvar AxiosError = require('../core/AxiosError');\nvar utils = require('../utils');\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction CanceledError(message) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\n\nmodule.exports = CanceledError;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC9C,IAAIC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACC,OAAO,EAAE;EAC9B;EACAJ,UAAU,CAACK,IAAI,CAAC,IAAI,EAAED,OAAO,IAAI,IAAI,GAAG,UAAU,GAAGA,OAAO,EAAEJ,UAAU,CAACM,YAAY,CAAC;EACtF,IAAI,CAACC,IAAI,GAAG,eAAe;AAC7B;AAEAL,KAAK,CAACM,QAAQ,CAACL,aAAa,EAAEH,UAAU,EAAE;EACxCS,UAAU,EAAE;AACd,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}