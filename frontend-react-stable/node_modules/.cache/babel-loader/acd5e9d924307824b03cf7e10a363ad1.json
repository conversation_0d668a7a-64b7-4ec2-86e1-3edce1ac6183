{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm, Pagination } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  var _selectedTask$result$;\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 分页相关状态\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = taskId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 13\n      }, this),\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 13\n      }, this),\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  // 计算当前页的已完成任务数据\n  const getCurrentPageCompletedTasks = () => {\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return completedTasks.slice(startIndex, endIndex);\n  };\n\n  // 分页变化处理\n  const handlePageChange = (page, size) => {\n    setCurrentPage(page);\n    if (size && size !== pageSize) {\n      setPageSize(size);\n      setCurrentPage(1); // 改变页面大小时重置到第一页\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      style: {\n        marginBottom: '24px',\n        display: 'block'\n      },\n      children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u4EFB\\u52A1\\u7EDF\\u8BA1\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"function-card\",\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",\n            children: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true,\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            danger: true,\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 23\n            }, this),\n            disabled: completedTasks.length === 0,\n            onClick: handleClearCompleted,\n            children: \"\\u6E05\\u7A7A\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(List, {\n            dataSource: getCurrentPageCompletedTasks(),\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleDeleteSingleTask(task.task_id),\n                children: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), completedTasks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16,\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Pagination, {\n              current: currentPage,\n              total: completedTasks.length,\n              pageSize: pageSize,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,\n              pageSizeOptions: ['5', '10', '20', '50'],\n              onChange: handlePageChange,\n              onShowSizeChange: handlePageChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), selectedTask.status === TASK_STATUS.COMPLETED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: 100,\n              size: \"small\",\n              status: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.FAILED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.CANCELLED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 19\n            }, this) : selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6267\\u884C\\u65F6\\u957F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: (_selectedTask$result => {\n                // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                if (selectedTask.started_at && selectedTask.completed_at) {\n                  const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒`;\n                }\n                // 如果任务正在运行，且有开始时间\n                else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                  const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒 (进行中)`;\n                }\n                // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                else if (selectedTask.status === TASK_STATUS.COMPLETED && (_selectedTask$result = selectedTask.result) !== null && _selectedTask$result !== void 0 && _selectedTask$result.duration_seconds) {\n                  return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                }\n                // 如果任务已完成但没有时间信息，显示已完成\n                else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                  return '已完成';\n                }\n                // 如果任务失败或取消，显示相应状态\n                else if (selectedTask.status === TASK_STATUS.FAILED) {\n                  return '执行失败';\n                } else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                  return '已取消';\n                }\n                // 其他情况显示等待开始\n                else {\n                  return '等待开始';\n                }\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), selectedTask.current_step && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5F53\\u524D\\u6B65\\u9AA4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.current_step\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 17\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this), selectedTask.params && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u53C2\\u6570:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 12,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 200,\n                overflow: 'auto',\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.params, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'training' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 23\n              }, this), selectedTask.result.results && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"purple\",\n                children: [\"\\u6A21\\u578B\\u6570\\u91CF: \", Object.keys(selectedTask.result.results).length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u8BAD\\u7EC3\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6A21\\u578B\\u8BAD\\u7EC3\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'prediction' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9884\\u6D4B\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 21\n              }, this), selectedTask.result.anomaly_count !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"red\",\n                children: [\"\\u5F02\\u5E38\\u6570\\u91CF: \", selectedTask.result.anomaly_count]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 23\n              }, this), ((_selectedTask$result$ = selectedTask.result.predictions) === null || _selectedTask$result$ === void 0 ? void 0 : _selectedTask$result$.length) && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u9884\\u6D4B\\u70B9\\u6570: \", selectedTask.result.predictions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 23\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"orange\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u9884\\u6D4B\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u5F02\\u5E38\\u68C0\\u6D4B\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"fRYlSsfQKJV0RtiQk0w+0EYzUuI=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "Pagination", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "MinusCircleOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "_selectedTask$result$", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "deleteSingleTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleDeleteSingleTask", "taskId", "confirm", "title", "content", "substring", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "okText", "okType", "cancelText", "onOk", "handleViewTaskDetail", "task", "handleCancelTask", "console", "handleClearCompleted", "completedCount", "length", "info", "getTaskStatusIcon", "status", "RUNNING", "spin", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "children", "formatTime", "timeString", "Date", "toLocaleString", "getCurrentPageCompletedTasks", "startIndex", "endIndex", "slice", "handlePageChange", "page", "size", "level", "fontSize", "fontWeight", "marginBottom", "type", "display", "justifyContent", "onClick", "className", "gutter", "span", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "created_at", "progress", "undefined", "marginTop", "percent", "disabled", "updated_at", "textAlign", "current", "total", "showSizeChanger", "showQuickJumper", "showTotal", "range", "pageSizeOptions", "onChange", "onShowSizeChange", "open", "onCancel", "footer", "width", "_selectedTask$result", "started_at", "completed_at", "duration", "Math", "round", "getTime", "result", "duration_seconds", "current_step", "params", "background", "padding", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "wrap", "results", "Object", "keys", "anomaly_count", "predictions", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm,\n  Pagination\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 分页相关状态\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  // 计算当前页的已完成任务数据\n  const getCurrentPageCompletedTasks = () => {\n    const startIndex = (currentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    return completedTasks.slice(startIndex, endIndex);\n  };\n\n  // 分页变化处理\n  const handlePageChange = (page: number, size?: number) => {\n    setCurrentPage(page);\n    if (size && size !== pageSize) {\n      setPageSize(size);\n      setCurrentPage(1); // 改变页面大小时重置到第一页\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>任务管理</Title>\n      <Text type=\"secondary\" style={{ marginBottom: '24px', display: 'block' }}>\n        查看和管理异步训练、预测任务的状态和结果\n      </Text>\n\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'flex-end' }}>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card className=\"function-card\" title=\"任务统计\">\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            className=\"function-card\"\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={getCurrentPageCompletedTasks()}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n\n            {/* 分页组件 */}\n            {completedTasks.length > 0 && (\n              <div style={{ marginTop: 16, textAlign: 'center' }}>\n                <Pagination\n                  current={currentPage}\n                  total={completedTasks.length}\n                  pageSize={pageSize}\n                  showSizeChanger\n                  showQuickJumper\n                  showTotal={(total, range) =>\n                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n                  }\n                  pageSizeOptions={['5', '10', '20', '50']}\n                  onChange={handlePageChange}\n                  onShowSizeChange={handlePageChange}\n                />\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.status === TASK_STATUS.COMPLETED ? (\n                  <Progress percent={100} size=\"small\" status=\"success\" />\n                ) : selectedTask.status === TASK_STATUS.FAILED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>执行时长:</Text>\n                <br />\n                <Text>\n                  {(() => {\n                    // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                    if (selectedTask.started_at && selectedTask.completed_at) {\n                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒`;\n                    }\n                    // 如果任务正在运行，且有开始时间\n                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒 (进行中)`;\n                    }\n                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {\n                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                    }\n                    // 如果任务已完成但没有时间信息，显示已完成\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                      return '已完成';\n                    }\n                    // 如果任务失败或取消，显示相应状态\n                    else if (selectedTask.status === TASK_STATUS.FAILED) {\n                      return '执行失败';\n                    }\n                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                      return '已取消';\n                    }\n                    // 其他情况显示等待开始\n                    else {\n                      return '等待开始';\n                    }\n                  })()}\n                </Text>\n              </Col>\n              {selectedTask.current_step && (\n                <Col span={24}>\n                  <Text strong>当前步骤:</Text>\n                  <br />\n                  <Text>{selectedTask.current_step}</Text>\n                </Col>\n              )}\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.params && (\n                <Col span={24}>\n                  <Text strong>任务参数:</Text>\n                  <br />\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 12,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 200,\n                    overflow: 'auto',\n                    marginTop: 8\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'training' && (\n                <Col span={24}>\n                  <Text strong>训练完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      训练已完成\n                    </Tag>\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"blue\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                    {selectedTask.result.results && (\n                      <Tag color=\"purple\">\n                        模型数量: {Object.keys(selectedTask.result.results).length}\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 训练结果详情请前往\"模型训练\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'prediction' && (\n                <Col span={24}>\n                  <Text strong>预测完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      预测已完成\n                    </Tag>\n                    {selectedTask.result.anomaly_count !== undefined && (\n                      <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                    )}\n                    {selectedTask.result.predictions?.length && (\n                      <Tag color=\"blue\">预测点数: {selectedTask.result.predictions.length}</Tag>\n                    )}\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 预测结果详情请前往\"异常检测\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG1B,UAAU;AAElC,MAAM2B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,gBAAgB;IAChBC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGnB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACdyC,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACA1C,SAAS,CAAC,MAAM;IACd,MAAM0D,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjClB,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMmB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACjB,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCR,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMS,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF7B,OAAO,CAACmD,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRZ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMa,sBAAsB,GAAIC,MAAc,IAAK;IACjDxD,KAAK,CAACyD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,WAAWH,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ;MAClDC,IAAI,eAAE1C,OAAA,CAACH,mBAAmB;QAAC8C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1DC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,MAAMlB,OAAO,GAAG,MAAMpB,gBAAgB,CAACuB,MAAM,CAAC;QAC9C,IAAIH,OAAO,EAAE;UACX;UACA,MAAMF,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAIC,IAAU,IAAK;IAC3CnC,eAAe,CAACmC,IAAI,CAAC;IACrBjC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAG,MAAOlB,MAAc,IAAK;IACjD,IAAI;MACF,MAAMxB,UAAU,CAACwB,MAAM,CAAC;MACxB,MAAM1B,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,cAAc,GAAGjD,cAAc,CAACkD,MAAM;IAC5C,IAAID,cAAc,KAAK,CAAC,EAAE;MACxB3E,OAAO,CAAC6E,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA/E,KAAK,CAACyD,OAAO,CAAC;MACZC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,WAAWkB,cAAc,oBAAoB;MACtDhB,IAAI,eAAE1C,OAAA,CAACN,cAAc;QAACiD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrDC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMrC,mBAAmB,CAAC,CAAC;UAC3B;UACA,MAAMiB,OAAO,CAACC,GAAG,CAAC,CAChBtB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ,CAAC,CAAC,OAAOuB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAK7C,WAAW,CAAC8C,OAAO;QACtB,oBAAO/D,OAAA,CAACZ,YAAY;UAAC4E,IAAI;UAACrB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK/B,WAAW,CAACgD,SAAS;QACxB,oBAAOjE,OAAA,CAACX,mBAAmB;UAACsD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK/B,WAAW,CAACiD,MAAM;QACrB,oBAAOlE,OAAA,CAACV,mBAAmB;UAACqD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK/B,WAAW,CAACkD,SAAS;QACxB,oBAAOnE,OAAA,CAACT,YAAY;UAACoD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAOhD,OAAA,CAACR,mBAAmB;UAACmD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAIN,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAK7C,WAAW,CAAC8C,OAAO;QACtB,oBAAO/D,OAAA,CAACrB,GAAG;UAACiE,KAAK,EAAC,YAAY;UAACF,IAAI,eAAE1C,OAAA,CAACZ,YAAY;YAAC4E,IAAI;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK/B,WAAW,CAACgD,SAAS;QACxB,oBAAOjE,OAAA,CAACrB,GAAG;UAACiE,KAAK,EAAC,SAAS;UAACF,IAAI,eAAE1C,OAAA,CAACX,mBAAmB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK/B,WAAW,CAACiD,MAAM;QACrB,oBAAOlE,OAAA,CAACrB,GAAG;UAACiE,KAAK,EAAC,OAAO;UAACF,IAAI,eAAE1C,OAAA,CAACV,mBAAmB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK/B,WAAW,CAACkD,SAAS;QACxB,oBAAOnE,OAAA,CAACrB,GAAG;UAACiE,KAAK,EAAC,SAAS;UAACF,IAAI,eAAE1C,OAAA,CAACT,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAOhD,OAAA,CAACrB,GAAG;UAACiE,KAAK,EAAC,SAAS;UAACF,IAAI,eAAE1C,OAAA,CAACR,mBAAmB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;IACzC,MAAMC,UAAU,GAAG,CAACnD,WAAW,GAAG,CAAC,IAAIE,QAAQ;IAC/C,MAAMkD,QAAQ,GAAGD,UAAU,GAAGjD,QAAQ;IACtC,OAAOjB,cAAc,CAACoE,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACnD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAACC,IAAY,EAAEC,IAAa,KAAK;IACxDvD,cAAc,CAACsD,IAAI,CAAC;IACpB,IAAIC,IAAI,IAAIA,IAAI,KAAKtD,QAAQ,EAAE;MAC7BC,WAAW,CAACqD,IAAI,CAAC;MACjBvD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACEzB,OAAA;IAAAqE,QAAA,gBACErE,OAAA,CAACG,KAAK;MAAC8E,KAAK,EAAE,CAAE;MAACtC,KAAK,EAAE;QAAEuC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAf,QAAA,EAAC;IAAI;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAChGhD,OAAA,CAACI,IAAI;MAACiF,IAAI,EAAC,WAAW;MAAC1C,KAAK,EAAE;QAAEyC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE;MAAQ,CAAE;MAAAjB,QAAA,EAAC;IAE1E;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPhD,OAAA;MAAK2C,KAAK,EAAE;QAAEyC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAlB,QAAA,eAChFrE,OAAA,CAACxB,MAAM;QACL6G,IAAI,EAAC,SAAS;QACd3C,IAAI,eAAE1C,OAAA,CAACL,cAAc;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBwC,OAAO,EAAEzD,aAAc;QACvBrB,OAAO,EAAEY,UAAW;QAAA+C,QAAA,EACrB;MAED;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhD,OAAA,CAAC7B,IAAI;MAACsH,SAAS,EAAC,eAAe;MAAClD,KAAK,EAAC,0BAAM;MAAA8B,QAAA,eAC1CrE,OAAA,CAAC5B,GAAG;QAACsH,MAAM,EAAE,EAAG;QAAArB,QAAA,gBACdrE,OAAA,CAAC3B,GAAG;UAACsH,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXrE,OAAA,CAAC1B,SAAS;YACRiE,KAAK,EAAC,gCAAO;YACbqD,KAAK,EAAEpF,YAAY,CAACmD,MAAO;YAC3BkC,MAAM,eAAE7F,OAAA,CAACZ,YAAY;cAAC4E,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9B8C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAAC3B,GAAG;UAACsH,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXrE,OAAA,CAAC1B,SAAS;YACRiE,KAAK,EAAC,gCAAO;YACbqD,KAAK,EAAEnF,cAAc,CAACsF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,MAAM,KAAK7C,WAAW,CAACgD,SAAS,CAAC,CAACN,MAAO;YAC7EkC,MAAM,eAAE7F,OAAA,CAACX,mBAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC8C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAAC3B,GAAG;UAACsH,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXrE,OAAA,CAAC1B,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZqD,KAAK,EAAEnF,cAAc,CAACsF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,MAAM,KAAK7C,WAAW,CAACiD,MAAM,CAAC,CAACP,MAAO;YAC1EkC,MAAM,eAAE7F,OAAA,CAACV,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC8C,UAAU,EAAE;cAAElD,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhD,OAAA,CAAC3B,GAAG;UAACsH,IAAI,EAAE,CAAE;UAAAtB,QAAA,eACXrE,OAAA,CAAC1B,SAAS;YACRiE,KAAK,EAAC,0BAAM;YACZqD,KAAK,EAAEpF,YAAY,CAACmD,MAAM,GAAGlD,cAAc,CAACkD,MAAO;YACnDkC,MAAM,eAAE7F,OAAA,CAACJ,kBAAkB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPhD,OAAA,CAAC5B,GAAG;MAACsH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAArB,QAAA,gBAEpBrE,OAAA,CAAC3B,GAAG;QAACsH,IAAI,EAAE,EAAG;QAAAtB,QAAA,eACZrE,OAAA,CAAC7B,IAAI;UACHsH,SAAS,EAAC,eAAe;UACzBlD,KAAK,eACHvC,OAAA,CAACvB,KAAK;YAAA4F,QAAA,gBACJrE,OAAA,CAACZ,YAAY;cAAC4E,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAErB,eAAAhD,OAAA,CAACrB,GAAG;cAACiE,KAAK,EAAC,YAAY;cAAAyB,QAAA,EAAE7D,YAAY,CAACmD;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACDiD,KAAK,eACHjG,OAAA,CAACf,OAAO;YAACsD,KAAK,EAAC,gCAAO;YAAA8B,QAAA,eACpBrE,OAAA,CAACZ,YAAY;cAAC4E,IAAI;cAACrB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACV;UAAAqB,QAAA,eAEDrE,OAAA,CAAChB,IAAI;YAACkH,QAAQ,EAAExF,OAAQ;YAAA2D,QAAA,eACtBrE,OAAA,CAACzB,IAAI;cACH4H,UAAU,EAAE3F,YAAa;cACzB4F,MAAM,EAAE;gBAAEC,SAAS,eAAErG,OAAA,CAAClB,KAAK;kBAACwH,WAAW,EAAC;gBAAU;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxDuD,UAAU,EAAGjD,IAAI,iBACftD,OAAA,CAACzB,IAAI,CAACiI,IAAI;gBACRC,OAAO,EAAE,cACPzG,OAAA,CAACxB,MAAM;kBACL6G,IAAI,EAAC,MAAM;kBACX3C,IAAI,eAAE1C,OAAA,CAACP,WAAW;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBwC,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,CAAE;kBAAAe,QAAA,EAC3C;gBAED;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThD,OAAA,CAACd,UAAU;kBACTqD,KAAK,EAAC,oEAAa;kBACnBmE,SAAS,EAAEA,CAAA,KAAMnD,gBAAgB,CAACD,IAAI,CAACqD,OAAO,CAAE;kBAChD1D,MAAM,EAAC,cAAI;kBACXE,UAAU,EAAC,cAAI;kBAAAkB,QAAA,eAEfrE,OAAA,CAACxB,MAAM;oBACL6G,IAAI,EAAC,MAAM;oBACXuB,MAAM;oBACNlE,IAAI,eAAE1C,OAAA,CAACT,YAAY;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAqB,QAAA,EACxB;kBAED;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAqB,QAAA,eAEFrE,OAAA,CAACzB,IAAI,CAACiI,IAAI,CAACK,IAAI;kBACbC,MAAM,EAAEjD,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;kBACvCvB,KAAK,eACHvC,OAAA,CAACvB,KAAK;oBAAA4F,QAAA,gBACJrE,OAAA,CAACI,IAAI;sBAAC2G,MAAM;sBAAA1C,QAAA,EAAErD,cAAc,CAACsC,IAAI,CAAC0D,SAAS;oBAAC;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDsD,WAAW,eACTtG,OAAA;oBAAAqE,QAAA,gBACErE,OAAA,CAACI,IAAI;sBAACiF,IAAI,EAAC,WAAW;sBAAC4B,QAAQ,EAAE;wBAAEC,IAAI,EAAE5D,IAAI,CAACqD;sBAAQ,CAAE;sBAAAtC,QAAA,GAAC,MACnD,EAACf,IAAI,CAACqD,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG7D,IAAI,CAACqD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM9D,IAAI,CAACqD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAACqD,OAAO,CAAClE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAElC,CAAC,eACPhD,OAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;sBAACiF,IAAI,EAAC,WAAW;sBAAAhB,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAAC+D,UAAU,CAAC;oBAAA;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEM,IAAI,CAACvE,OAAO,iBACXiB,OAAA,CAAAE,SAAA;sBAAAmE,QAAA,gBACErE,OAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;wBAACiF,IAAI,EAAC,WAAW;wBAAAhB,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACvE,OAAO;sBAAA;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAM,IAAI,CAACgE,QAAQ,KAAKC,SAAS,iBAC1BvH,OAAA;sBAAK2C,KAAK,EAAE;wBAAE6E,SAAS,EAAE;sBAAE,CAAE;sBAAAnD,QAAA,eAC3BrE,OAAA,CAACpB,QAAQ;wBAAC6I,OAAO,EAAEnE,IAAI,CAACgE,QAAS;wBAACtC,IAAI,EAAC;sBAAO;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNhD,OAAA,CAAC3B,GAAG;QAACsH,IAAI,EAAE,EAAG;QAAAtB,QAAA,eACZrE,OAAA,CAAC7B,IAAI;UACHoE,KAAK,eACHvC,OAAA,CAACvB,KAAK;YAAA4F,QAAA,gBACJrE,OAAA,CAACX,mBAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAAhD,OAAA,CAACrB,GAAG;cAACiE,KAAK,EAAC,SAAS;cAAAyB,QAAA,EAAE5D,cAAc,CAACkD;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACDiD,KAAK,eACHjG,OAAA,CAACxB,MAAM;YACL6G,IAAI,EAAC,SAAS;YACduB,MAAM;YACN5B,IAAI,EAAC,OAAO;YACZtC,IAAI,eAAE1C,OAAA,CAACN,cAAc;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB0E,QAAQ,EAAEjH,cAAc,CAACkD,MAAM,KAAK,CAAE;YACtC6B,OAAO,EAAE/B,oBAAqB;YAAAY,QAAA,EAC/B;UAED;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAqB,QAAA,gBAEDrE,OAAA,CAACzB,IAAI;YACH4H,UAAU,EAAEzB,4BAA4B,CAAC,CAAE;YAC3C0B,MAAM,EAAE;cACNC,SAAS,eACPrG,OAAA,CAAClB,KAAK;gBACJwH,WAAW,eACTtG,OAAA;kBAAAqE,QAAA,gBACErE,OAAA;oBAAAqE,QAAA,EAAK;kBAAQ;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnBhD,OAAA,CAACI,IAAI;oBAACiF,IAAI,EAAC,WAAW;oBAAC1C,KAAK,EAAE;sBAAEuC,QAAQ,EAAE;oBAAO,CAAE;oBAAAb,QAAA,EAAC;kBAEpD;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACFuD,UAAU,EAAGjD,IAAI,iBACftD,OAAA,CAACzB,IAAI,CAACiI,IAAI;cACRC,OAAO,EAAE,cACPzG,OAAA,CAACxB,MAAM;gBACL6G,IAAI,EAAC,MAAM;gBACX3C,IAAI,eAAE1C,OAAA,CAACP,WAAW;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBwC,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAACC,IAAI,CAAE;gBAAAe,QAAA,EAC3C;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThD,OAAA,CAACxB,MAAM;gBACL6G,IAAI,EAAC,MAAM;gBACXuB,MAAM;gBACNlE,IAAI,eAAE1C,OAAA,CAACH,mBAAmB;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BwC,OAAO,EAAEA,CAAA,KAAMpD,sBAAsB,CAACkB,IAAI,CAACqD,OAAO,CAAE;gBAAAtC,QAAA,EACrD;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAqB,QAAA,eAEFrE,OAAA,CAACzB,IAAI,CAACiI,IAAI,CAACK,IAAI;gBACbC,MAAM,EAAEjD,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;gBACvCvB,KAAK,eACHvC,OAAA,CAACvB,KAAK;kBAAA4F,QAAA,gBACJrE,OAAA,CAACI,IAAI;oBAAC2G,MAAM;oBAAA1C,QAAA,EAAErD,cAAc,CAACsC,IAAI,CAAC0D,SAAS;kBAAC;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACDsD,WAAW,eACTtG,OAAA;kBAAAqE,QAAA,gBACErE,OAAA,CAACI,IAAI;oBAACiF,IAAI,EAAC,WAAW;oBAAC4B,QAAQ,EAAE;sBAAEC,IAAI,EAAE5D,IAAI,CAACqD;oBAAQ,CAAE;oBAAAtC,QAAA,GAAC,MACnD,EAACf,IAAI,CAACqD,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAG7D,IAAI,CAACqD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM9D,IAAI,CAACqD,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAACqD,OAAO,CAAClE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElC,CAAC,eACPhD,OAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;oBAACiF,IAAI,EAAC,WAAW;oBAAAhB,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACqE,UAAU,CAAC;kBAAA;oBAAA9E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEM,IAAI,CAACQ,MAAM,KAAK7C,WAAW,CAACiD,MAAM,IAAIZ,IAAI,CAACnB,KAAK,iBAC/CnC,OAAA,CAAAE,SAAA;oBAAAmE,QAAA,gBACErE,OAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;sBAACiF,IAAI,EAAC,QAAQ;sBAAAhB,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACnB,KAAK;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGDvC,cAAc,CAACkD,MAAM,GAAG,CAAC,iBACxB3D,OAAA;YAAK2C,KAAK,EAAE;cAAE6E,SAAS,EAAE,EAAE;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAvD,QAAA,eACjDrE,OAAA,CAACb,UAAU;cACT0I,OAAO,EAAErG,WAAY;cACrBsG,KAAK,EAAErH,cAAc,CAACkD,MAAO;cAC7BjC,QAAQ,EAAEA,QAAS;cACnBqG,eAAe;cACfC,eAAe;cACfC,SAAS,EAAEA,CAACH,KAAK,EAAEI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQJ,KAAK,IACvC;cACDK,eAAe,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;cACzCC,QAAQ,EAAEtD,gBAAiB;cAC3BuD,gBAAgB,EAAEvD;YAAiB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA,CAACnB,KAAK;MACJ0D,KAAK,EAAC,0BAAM;MACZ+F,IAAI,EAAElH,iBAAkB;MACxBmH,QAAQ,EAAEA,CAAA,KAAMlH,oBAAoB,CAAC,KAAK,CAAE;MAC5CmH,MAAM,EAAE,cACNxI,OAAA,CAACxB,MAAM;QAAagH,OAAO,EAAEA,CAAA,KAAMnE,oBAAoB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFyF,KAAK,EAAE,GAAI;MAAApE,QAAA,EAEVnD,YAAY,iBACXlB,OAAA;QAAAqE,QAAA,eACErE,OAAA,CAAC5B,GAAG;UAACsH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAArB,QAAA,gBACpBrE,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAC6G,QAAQ;cAAA5C,QAAA,EAAEnD,YAAY,CAACyF;YAAO;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EAAErD,cAAc,CAACE,YAAY,CAAC8F,SAAS;YAAC;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLoB,gBAAgB,CAAClD,YAAY,CAAC4C,MAAM,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,gBAC5CjE,OAAA,CAACpB,QAAQ;cAAC6I,OAAO,EAAE,GAAI;cAACzC,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACtD9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACiD,MAAM,gBAC5ClE,OAAA,CAACpB,QAAQ;cAAC6I,OAAO,EAAEvG,YAAY,CAACoG,QAAQ,IAAI,CAAE;cAACtC,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E9B,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACkD,SAAS,gBAC/CnE,OAAA,CAACpB,QAAQ;cAAC6I,OAAO,EAAEvG,YAAY,CAACoG,QAAQ,IAAI,CAAE;cAACtC,IAAI,EAAC,OAAO;cAAClB,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E9B,YAAY,CAACoG,QAAQ,KAAKC,SAAS,gBACrCvH,OAAA,CAACpB,QAAQ;cAAC6I,OAAO,EAAEvG,YAAY,CAACoG,QAAS;cAACtC,IAAI,EAAC;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzDhD,OAAA,CAACI,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EAAEC,UAAU,CAACpD,YAAY,CAACmG,UAAU;YAAC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EAAEC,UAAU,CAACpD,YAAY,CAACyG,UAAU;YAAC;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNhD,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EACF,CAACqE,oBAAA,IAAM;gBACN;gBACA,IAAIxH,YAAY,CAACyH,UAAU,IAAIzH,YAAY,CAAC0H,YAAY,EAAE;kBACxD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIvE,IAAI,CAACtD,YAAY,CAAC0H,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAIxE,IAAI,CAACtD,YAAY,CAACyH,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACjI,OAAO,GAAGH,QAAQ,GAAG;gBACvB;gBACA;gBAAA,KACK,IAAI3H,YAAY,CAACyH,UAAU,IAAIzH,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAAC8C,OAAO,EAAE;kBAC/E,MAAM8E,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIvE,IAAI,CAAC,CAAC,CAACwE,OAAO,CAAC,CAAC,GAAG,IAAIxE,IAAI,CAACtD,YAAY,CAACyH,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACxG,OAAO,GAAGH,QAAQ,SAAS;gBAC7B;gBACA;gBAAA,KACK,IAAI3H,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,KAAAyE,oBAAA,GAAIxH,YAAY,CAAC+H,MAAM,cAAAP,oBAAA,eAAnBA,oBAAA,CAAqBQ,gBAAgB,EAAE;kBAC/F,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAAC7H,YAAY,CAAC+H,MAAM,CAACC,gBAAgB,CAAC,GAAG;gBAC/D;gBACA;gBAAA,KACK,IAAIhI,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACgD,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK,IAAI/C,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACiD,MAAM,EAAE;kBACnD,OAAO,MAAM;gBACf,CAAC,MACI,IAAIhD,YAAY,CAAC4C,MAAM,KAAK7C,WAAW,CAACkD,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK;kBACH,OAAO,MAAM;gBACf;cACF,CAAC,EAAE;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL9B,YAAY,CAACiI,YAAY,iBACxBnJ,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EAAEnD,YAAY,CAACiI;YAAY;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACN,EACA9B,YAAY,CAACnC,OAAO,iBACnBiB,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAAAiE,QAAA,EAAEnD,YAAY,CAACnC;YAAO;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA9B,YAAY,CAACiB,KAAK,iBACjBnC,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACI,IAAI;cAACiF,IAAI,EAAC,QAAQ;cAAAhB,QAAA,EAAEnD,YAAY,CAACiB;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA9B,YAAY,CAACkI,MAAM,iBAClBpJ,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA;cAAK2C,KAAK,EAAE;gBACV0G,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,CAAC;gBACfrE,QAAQ,EAAE,EAAE;gBACZsE,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE,MAAM;gBAChBjC,SAAS,EAAE;cACb,CAAE;cAAAnD,QAAA,eACArE,OAAA;gBAAAqE,QAAA,EAAMqF,IAAI,CAACC,SAAS,CAACzI,YAAY,CAACkI,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAAvG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA9B,YAAY,CAAC+H,MAAM,IAAI/H,YAAY,CAAC8F,SAAS,KAAK,UAAU,iBAC3DhH,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACvB,KAAK;cAACmL,IAAI;cAACjH,KAAK,EAAE;gBAAE6E,SAAS,EAAE;cAAE,CAAE;cAAAnD,QAAA,gBAClCrE,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAE1C,OAAA,CAACX,mBAAmB;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL9B,YAAY,CAAC+H,MAAM,CAACC,gBAAgB,iBACnClJ,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,gBACZ,EAACyE,IAAI,CAACC,KAAK,CAAC7H,YAAY,CAAC+H,MAAM,CAACC,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAArG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACA9B,YAAY,CAAC+H,MAAM,CAACY,OAAO,iBAC1B7J,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,4BACZ,EAACyF,MAAM,CAACC,IAAI,CAAC7I,YAAY,CAAC+H,MAAM,CAACY,OAAO,CAAC,CAAClG,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACRhD,OAAA;cAAK2C,KAAK,EAAE;gBAAE6E,SAAS,EAAE;cAAE,CAAE;cAAAnD,QAAA,eAC3BrE,OAAA,CAACI,IAAI;gBAACiF,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA9B,YAAY,CAAC+H,MAAM,IAAI/H,YAAY,CAAC8F,SAAS,KAAK,YAAY,iBAC7DhH,OAAA,CAAC3B,GAAG;YAACsH,IAAI,EAAE,EAAG;YAAAtB,QAAA,gBACZrE,OAAA,CAACI,IAAI;cAAC2G,MAAM;cAAA1C,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBhD,OAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhD,OAAA,CAACvB,KAAK;cAACmL,IAAI;cAACjH,KAAK,EAAE;gBAAE6E,SAAS,EAAE;cAAE,CAAE;cAAAnD,QAAA,gBAClCrE,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAE1C,OAAA,CAACX,mBAAmB;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL9B,YAAY,CAAC+H,MAAM,CAACe,aAAa,KAAKzC,SAAS,iBAC9CvH,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,KAAK;gBAAAyB,QAAA,GAAC,4BAAM,EAACnD,YAAY,CAAC+H,MAAM,CAACe,aAAa;cAAA;gBAAAnH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAChE,EACA,EAAAzC,qBAAA,GAAAW,YAAY,CAAC+H,MAAM,CAACgB,WAAW,cAAA1J,qBAAA,uBAA/BA,qBAAA,CAAiCoD,MAAM,kBACtC3D,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,4BAAM,EAACnD,YAAY,CAAC+H,MAAM,CAACgB,WAAW,CAACtG,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACtE,EACA9B,YAAY,CAAC+H,MAAM,CAACC,gBAAgB,iBACnClJ,OAAA,CAACrB,GAAG;gBAACiE,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,gBACd,EAACyE,IAAI,CAACC,KAAK,CAAC7H,YAAY,CAAC+H,MAAM,CAACC,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAArG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACRhD,OAAA;cAAK2C,KAAK,EAAE;gBAAE6E,SAAS,EAAE;cAAE,CAAE;cAAAnD,QAAA,eAC3BrE,OAAA,CAACI,IAAI;gBAACiF,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA1mBID,eAAyB;EAAA,QAYzBP,cAAc;AAAA;AAAAoK,EAAA,GAZd7J,eAAyB;AA4mB/B,eAAeA,eAAe;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}