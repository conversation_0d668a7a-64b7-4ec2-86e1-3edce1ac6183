{"ast": null, "code": "import { useContext } from 'react';\nimport { FormItemInputContext } from '../context';\nimport warning from '../../_util/warning';\nvar useFormItemStatus = function useFormItemStatus() {\n  var _useContext = useContext(FormItemInputContext),\n    status = _useContext.status;\n  process.env.NODE_ENV !== \"production\" ? warning(status !== undefined, 'Form.Item', \"Form.Item.useStatus should be used under Form.Item component. For more information: \".concat(window.location.protocol, \"//\").concat(window.location.host, \"/components/form-cn/#Form.Item.useStatus\")) : void 0;\n  return {\n    status: status\n  };\n};\nexport default useFormItemStatus;", "map": {"version": 3, "names": ["useContext", "FormItemInputContext", "warning", "useFormItemStatus", "_useContext", "status", "process", "env", "NODE_ENV", "undefined", "concat", "window", "location", "protocol", "host"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/hooks/useFormItemStatus.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { FormItemInputContext } from '../context';\nimport warning from '../../_util/warning';\nvar useFormItemStatus = function useFormItemStatus() {\n  var _useContext = useContext(FormItemInputContext),\n    status = _useContext.status;\n  process.env.NODE_ENV !== \"production\" ? warning(status !== undefined, 'Form.Item', \"Form.Item.useStatus should be used under Form.Item component. For more information: \".concat(window.location.protocol, \"//\").concat(window.location.host, \"/components/form-cn/#Form.Item.useStatus\")) : void 0;\n  return {\n    status: status\n  };\n};\nexport default useFormItemStatus;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,oBAAoB,QAAQ,YAAY;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;EACnD,IAAIC,WAAW,GAAGJ,UAAU,CAACC,oBAAoB,CAAC;IAChDI,MAAM,GAAGD,WAAW,CAACC,MAAM;EAC7BC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,OAAO,CAACG,MAAM,KAAKI,SAAS,EAAE,WAAW,EAAE,sFAAsF,CAACC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,EAAE,IAAI,CAAC,CAACH,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACE,IAAI,EAAE,0CAA0C,CAAC,CAAC,GAAG,KAAK,CAAC;EACnS,OAAO;IACLT,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AACD,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}