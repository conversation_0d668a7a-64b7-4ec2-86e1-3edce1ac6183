{"ast": null, "code": "import _every from \"lodash/every\";\nimport _find from \"lodash/find\";\nimport _isFunction from \"lodash/isFunction\";\nimport _throttle from \"lodash/throttle\";\nimport _sortBy from \"lodash/sortBy\";\nimport _get from \"lodash/get\";\nimport _range from \"lodash/range\";\nimport _isNil from \"lodash/isNil\";\nimport _isBoolean from \"lodash/isBoolean\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"item\"],\n  _excluded2 = [\"children\", \"className\", \"width\", \"height\", \"style\", \"compact\", \"title\", \"desc\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s,\n      _e,\n      _x,\n      _r,\n      _arr = [],\n      _n = !0,\n      _d = !1;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i) return;\n        _n = !1;\n      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);\n    } catch (err) {\n      _d = !0, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return;\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport React, { Component, cloneElement, isValidElement, createElement } from 'react';\nimport classNames from 'classnames';\nimport { getTicks } from '../cartesian/getTicks';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Legend } from '../component/Legend';\nimport { Curve } from '../shape/Curve';\nimport { Cross } from '../shape/Cross';\nimport { Sector } from '../shape/Sector';\nimport { Dot } from '../shape/Dot';\nimport { isInRectangle, Rectangle } from '../shape/Rectangle';\nimport { findAllByType, findChildByType, getDisplayName, parseChildIndex, validateWidthHeight, isChildrenEqual, renderByOrder, getReactEventByType, filterProps } from '../util/ReactUtils';\nimport { CartesianAxis } from '../cartesian/CartesianAxis';\nimport { Brush } from '../cartesian/Brush';\nimport { getOffset, calculateChartCoordinate } from '../util/DOMUtils';\nimport { getAnyElementOfObject, hasDuplicate, uniqueId, isNumber, findEntryInArray } from '../util/DataUtils';\nimport { calculateActiveTickIndex, getMainColorOfGraphicItem, getBarSizeList, getBarPosition, appendOffsetOfLegend, getLegendProps, combineEventHandlers, getTicksOfAxis, getCoordinatesOfGrid, getStackedDataOfItem, parseErrorBarsOfAxis, getBandSizeOfAxis, getStackGroupsByAxisId, isCategoricalAxis, getDomainOfItemsWithSameAxis, getDomainOfStackGroups, getDomainOfDataByKey, parseSpecifiedDomain, parseDomainOfCategoryAxis, getTooltipItem } from '../util/ChartUtils';\nimport { detectReferenceElementsDomain } from '../util/DetectReferenceElementsDomain';\nimport { inRangeOfSector, polarToCartesian } from '../util/PolarUtils';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { eventCenter, SYNC_EVENT } from '../util/Events';\nimport { adaptEventHandlers } from '../util/types';\nvar ORIENT_MAP = {\n  xAxis: ['bottom', 'top'],\n  yAxis: ['left', 'right']\n};\nvar originCoordinate = {\n  x: 0,\n  y: 0\n};\n\n// use legacy isFinite only if there is a problem (aka IE)\n// eslint-disable-next-line no-restricted-globals\nvar isFinit = Number.isFinite ? Number.isFinite : isFinite;\nvar defer =\n// eslint-disable-next-line no-nested-ternary\ntypeof requestAnimationFrame === 'function' ? requestAnimationFrame : typeof setImmediate === 'function' ? setImmediate : setTimeout;\nvar deferClear =\n// eslint-disable-next-line no-nested-ternary\ntypeof cancelAnimationFrame === 'function' ? cancelAnimationFrame : typeof clearImmediate === 'function' ? clearImmediate : clearTimeout;\nvar calculateTooltipPos = function calculateTooltipPos(rangeObj, layout) {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};\nvar getActiveCoordinate = function getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj) {\n  var entry = tooltipTicks.find(function (tick) {\n    return tick && tick.index === activeIndex;\n  });\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var _radius = rangeObj.radius;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var angle = rangeObj.angle;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle: angle,\n      radius: radius\n    });\n  }\n  return originCoordinate;\n};\nvar getDisplayedData = function getDisplayedData(data, _ref, item) {\n  var graphicalItems = _ref.graphicalItems,\n    dataStartIndex = _ref.dataStartIndex,\n    dataEndIndex = _ref.dataEndIndex;\n  var itemsData = (graphicalItems || []).reduce(function (result, child) {\n    var itemData = child.props.data;\n    if (itemData && itemData.length) {\n      return [].concat(_toConsumableArray(result), _toConsumableArray(itemData));\n    }\n    return result;\n  }, []);\n  if (itemsData && itemsData.length > 0) {\n    return itemsData;\n  }\n  if (item && item.props && item.props.data && item.props.data.length > 0) {\n    return item.props.data;\n  }\n  if (data && data.length && isNumber(dataStartIndex) && isNumber(dataEndIndex)) {\n    return data.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  return [];\n};\n\n/**\n * Takes a domain and user props to determine whether he provided the domain via props or if we need to calculate it.\n * @param   {AxisDomain}  domain              The potential domain from props\n * @param   {Boolean}     allowDataOverflow   from props\n * @param   {String}      axisType            from props\n * @returns {Boolean}                         `true` if domain is specified by user\n */\nfunction isDomainSpecifiedByUser(domain, allowDataOverflow, axisType) {\n  if (axisType === 'number' && allowDataOverflow === true && Array.isArray(domain)) {\n    var domainStart = domain === null || domain === void 0 ? void 0 : domain[0];\n    var domainEnd = domain === null || domain === void 0 ? void 0 : domain[1];\n\n    /*\n     * The `isNumber` check is needed because the user could also provide strings like \"dataMin\" via the domain props.\n     * In such case, we have to compute the domain from the data.\n     */\n    if (!!domainStart && !!domainEnd && isNumber(domainStart) && isNumber(domainEnd)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction getDefaultDomainByAxisType(axisType) {\n  return axisType === 'number' ? [0, 'auto'] : undefined;\n}\n\n/**\n * Get the content to be displayed in the tooltip\n * @param  {Object} state          Current state\n * @param  {Array}  chartData      The data defined in chart\n * @param  {Number} activeIndex    Active index of data\n * @param  {String} activeLabel    Active label of data\n * @return {Array}                 The content of tooltip\n */\nvar getTooltipContent = function getTooltipContent(state, chartData, activeIndex, activeLabel) {\n  var graphicalItems = state.graphicalItems,\n    tooltipAxis = state.tooltipAxis;\n  var displayedData = getDisplayedData(chartData, state);\n  if (activeIndex < 0 || !graphicalItems || !graphicalItems.length || activeIndex >= displayedData.length) {\n    return null;\n  }\n  // get data by activeIndex when the axis don't allow duplicated category\n  return graphicalItems.reduce(function (result, child) {\n    var hide = child.props.hide;\n    if (hide) {\n      return result;\n    }\n    var data = child.props.data;\n    var payload;\n    if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n      // graphic child has data props\n      var entries = data === undefined ? displayedData : data;\n      payload = findEntryInArray(entries, tooltipAxis.dataKey, activeLabel);\n    } else {\n      payload = data && data[activeIndex] || displayedData[activeIndex];\n    }\n    if (!payload) {\n      return result;\n    }\n    return [].concat(_toConsumableArray(result), [getTooltipItem(child, payload)]);\n  }, []);\n};\n\n/**\n * Returns tooltip data based on a mouse position (as a parameter or in state)\n * @param  {Object} state     current state\n * @param  {Array}  chartData the data defined in chart\n * @param  {String} layout     The layout type of chart\n * @param  {Object} rangeObj  { x, y } coordinates\n * @return {Object}           Tooltip data data\n */\nvar getTooltipData = function getTooltipData(state, chartData, layout, rangeObj) {\n  var rangeData = rangeObj || {\n    x: state.chartX,\n    y: state.chartY\n  };\n  var pos = calculateTooltipPos(rangeData, layout);\n  var ticks = state.orderedTooltipTicks,\n    axis = state.tooltipAxis,\n    tooltipTicks = state.tooltipTicks;\n  var activeIndex = calculateActiveTickIndex(pos, ticks, tooltipTicks, axis);\n  if (activeIndex >= 0 && tooltipTicks) {\n    var activeLabel = tooltipTicks[activeIndex] && tooltipTicks[activeIndex].value;\n    var activePayload = getTooltipContent(state, chartData, activeIndex, activeLabel);\n    var activeCoordinate = getActiveCoordinate(layout, ticks, activeIndex, rangeData);\n    return {\n      activeTooltipIndex: activeIndex,\n      activeLabel: activeLabel,\n      activePayload: activePayload,\n      activeCoordinate: activeCoordinate\n    };\n  }\n  return null;\n};\n\n/**\n * Get the configuration of axis by the options of axis instance\n * @param  {Object} props         Latest props\n * @param {Array}  axes           The instance of axes\n * @param  {Array} graphicalItems The instances of item\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}      Configuration\n */\nexport var getAxisMapByAxes = function getAxisMapByAxes(props, _ref2) {\n  var axes = _ref2.axes,\n    graphicalItems = _ref2.graphicalItems,\n    axisType = _ref2.axisType,\n    axisIdKey = _ref2.axisIdKey,\n    stackGroups = _ref2.stackGroups,\n    dataStartIndex = _ref2.dataStartIndex,\n    dataEndIndex = _ref2.dataEndIndex;\n  var layout = props.layout,\n    children = props.children,\n    stackOffset = props.stackOffset;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n\n  // Eliminate duplicated axes\n  var axisMap = axes.reduce(function (result, child) {\n    var _child$props$domain2;\n    var _child$props = child.props,\n      type = _child$props.type,\n      dataKey = _child$props.dataKey,\n      allowDataOverflow = _child$props.allowDataOverflow,\n      allowDuplicatedCategory = _child$props.allowDuplicatedCategory,\n      scale = _child$props.scale,\n      ticks = _child$props.ticks,\n      includeHidden = _child$props.includeHidden;\n    var axisId = child.props[axisIdKey];\n    if (result[axisId]) {\n      return result;\n    }\n    var displayedData = getDisplayedData(props.data, {\n      graphicalItems: graphicalItems.filter(function (item) {\n        return item.props[axisIdKey] === axisId;\n      }),\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n    var len = displayedData.length;\n    var domain, duplicateDomain, categoricalDomain;\n\n    /*\n     * This is a hack to short-circuit the domain creation here to enhance performance.\n     * Usually, the data is used to determine the domain, but when the user specifies\n     * a domain upfront (via props), there is no need to calculate the domain start and end,\n     * which is very expensive for a larger amount of data.\n     * The only thing that would prohibit short-circuiting is when the user doesn't allow data overflow,\n     * because the axis is supposed to ignore the specified domain that way.\n     */\n    if (isDomainSpecifiedByUser(child.props.domain, allowDataOverflow, type)) {\n      domain = parseSpecifiedDomain(child.props.domain, null, allowDataOverflow);\n      /* The chart can be categorical and have the domain specified in numbers\n       * we still need to calculate the categorical domain\n       * TODO: refactor this more\n       */\n      if (isCategorical && (type === 'number' || scale !== 'auto')) {\n        categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n      }\n    }\n\n    // if the domain is defaulted we need this for `originalDomain` as well\n    var defaultDomain = getDefaultDomainByAxisType(type);\n\n    // we didn't create the domain from user's props above, so we need to calculate it\n    if (!domain || domain.length === 0) {\n      var _child$props$domain;\n      var childDomain = (_child$props$domain = child.props.domain) !== null && _child$props$domain !== void 0 ? _child$props$domain : defaultDomain;\n      if (dataKey) {\n        // has dataKey in <Axis />\n        domain = getDomainOfDataByKey(displayedData, dataKey, type);\n        if (type === 'category' && isCategorical) {\n          // the field type is category data and this axis is categorical axis\n          var duplicate = hasDuplicate(domain);\n          if (allowDuplicatedCategory && duplicate) {\n            duplicateDomain = domain;\n            // When category axis has duplicated text, serial numbers are used to generate scale\n            domain = _range(0, len);\n          } else if (!allowDuplicatedCategory) {\n            // remove duplicated category\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          }\n        } else if (type === 'category') {\n          // the field type is category data and this axis is numerical axis\n          if (!allowDuplicatedCategory) {\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 || entry === '' || _isNil(entry) ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          } else {\n            // eliminate undefined or null or empty string\n            domain = domain.filter(function (entry) {\n              return entry !== '' && !_isNil(entry);\n            });\n          }\n        } else if (type === 'number') {\n          // the field type is numerical\n          var errorBarsDomain = parseErrorBarsOfAxis(displayedData, graphicalItems.filter(function (item) {\n            return item.props[axisIdKey] === axisId && (includeHidden || !item.props.hide);\n          }), dataKey, axisType, layout);\n          if (errorBarsDomain) {\n            domain = errorBarsDomain;\n          }\n        }\n        if (isCategorical && (type === 'number' || scale !== 'auto')) {\n          categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n        }\n      } else if (isCategorical) {\n        // the axis is a categorical axis\n        domain = _range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack && type === 'number') {\n        // when stackOffset is 'expand', the domain may be calculated as [0, 1.000000000002]\n        domain = stackOffset === 'expand' ? [0, 1] : getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n      } else {\n        domain = getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          return item.props[axisIdKey] === axisId && (includeHidden || !item.props.hide);\n        }), type, layout, true);\n      }\n      if (type === 'number') {\n        // To detect wether there is any reference lines whose props alwaysShow is true\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType, ticks);\n        if (childDomain) {\n          domain = parseSpecifiedDomain(childDomain, domain, allowDataOverflow);\n        }\n      } else if (type === 'category' && childDomain) {\n        var axisDomain = childDomain;\n        var isDomainValid = domain.every(function (entry) {\n          return axisDomain.indexOf(entry) >= 0;\n        });\n        if (isDomainValid) {\n          domain = axisDomain;\n        }\n      }\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({}, child.props), {}, {\n      axisType: axisType,\n      domain: domain,\n      categoricalDomain: categoricalDomain,\n      duplicateDomain: duplicateDomain,\n      originalDomain: (_child$props$domain2 = child.props.domain) !== null && _child$props$domain2 !== void 0 ? _child$props$domain2 : defaultDomain,\n      isCategorical: isCategorical,\n      layout: layout\n    })));\n  }, {});\n  return axisMap;\n};\n\n/**\n * Get the configuration of axis by the options of item,\n * this kind of axis does not display in chart\n * @param  {Object} props         Latest props\n * @param  {Array} graphicalItems The instances of item\n * @param  {ReactElement} Axis    Axis Component\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}               Configuration\n */\nvar getAxisMapByItems = function getAxisMapByItems(props, _ref3) {\n  var graphicalItems = _ref3.graphicalItems,\n    Axis = _ref3.Axis,\n    axisType = _ref3.axisType,\n    axisIdKey = _ref3.axisIdKey,\n    stackGroups = _ref3.stackGroups,\n    dataStartIndex = _ref3.dataStartIndex,\n    dataEndIndex = _ref3.dataEndIndex;\n  var layout = props.layout,\n    children = props.children;\n  var displayedData = getDisplayedData(props.data, {\n    graphicalItems: graphicalItems,\n    dataStartIndex: dataStartIndex,\n    dataEndIndex: dataEndIndex\n  });\n  var len = displayedData.length;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var index = -1;\n\n  // The default type of x-axis is category axis,\n  // The default contents of x-axis is the serial numbers of data\n  // The default type of y-axis is number axis\n  // The default contents of y-axis is the domain of data\n  var axisMap = graphicalItems.reduce(function (result, child) {\n    var axisId = child.props[axisIdKey];\n    var originalDomain = getDefaultDomainByAxisType('number');\n    if (!result[axisId]) {\n      index++;\n      var domain;\n      if (isCategorical) {\n        domain = _range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack) {\n        domain = getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      } else {\n        domain = parseSpecifiedDomain(originalDomain, getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          return item.props[axisIdKey] === axisId && !item.props.hide;\n        }), 'number', layout), Axis.defaultProps.allowDataOverflow);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      }\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({\n        axisType: axisType\n      }, Axis.defaultProps), {}, {\n        hide: true,\n        orientation: _get(ORIENT_MAP, \"\".concat(axisType, \".\").concat(index % 2), null),\n        domain: domain,\n        originalDomain: originalDomain,\n        isCategorical: isCategorical,\n        layout: layout\n        // specify scale when no Axis\n        // scale: isCategorical ? 'band' : 'linear',\n      })));\n    }\n    return result;\n  }, {});\n  return axisMap;\n};\n\n/**\n * Get the configuration of all x-axis or y-axis\n * @param  {Object} props          Latest props\n * @param  {String} axisType       The type of axis\n * @param  {Array}  graphicalItems The instances of item\n * @param  {Object} stackGroups    The items grouped by axisId and stackId\n * @param {Number} dataStartIndex  The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex    The end index of the data series when a brush is applied\n * @return {Object}          Configuration\n */\nvar getAxisMap = function getAxisMap(props, _ref4) {\n  var _ref4$axisType = _ref4.axisType,\n    axisType = _ref4$axisType === void 0 ? 'xAxis' : _ref4$axisType,\n    AxisComp = _ref4.AxisComp,\n    graphicalItems = _ref4.graphicalItems,\n    stackGroups = _ref4.stackGroups,\n    dataStartIndex = _ref4.dataStartIndex,\n    dataEndIndex = _ref4.dataEndIndex;\n  var children = props.children;\n  var axisIdKey = \"\".concat(axisType, \"Id\");\n  // Get all the instance of Axis\n  var axes = findAllByType(children, AxisComp);\n  var axisMap = {};\n  if (axes && axes.length) {\n    axisMap = getAxisMapByAxes(props, {\n      axes: axes,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  } else if (graphicalItems && graphicalItems.length) {\n    axisMap = getAxisMapByItems(props, {\n      Axis: AxisComp,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  }\n  return axisMap;\n};\nvar tooltipTicksGenerator = function tooltipTicksGenerator(axisMap) {\n  var axis = getAnyElementOfObject(axisMap);\n  var tooltipTicks = getTicksOfAxis(axis, false, true);\n  return {\n    tooltipTicks: tooltipTicks,\n    orderedTooltipTicks: _sortBy(tooltipTicks, function (o) {\n      return o.coordinate;\n    }),\n    tooltipAxis: axis,\n    tooltipAxisBandSize: getBandSizeOfAxis(axis, tooltipTicks)\n  };\n};\n\n/**\n * Returns default, reset state for the categorical chart.\n * @param {Object} props Props object to use when creating the default state\n * @return {Object} Whole new state\n */\nvar createDefaultState = function createDefaultState(props) {\n  var _brushItem$props, _brushItem$props2;\n  var children = props.children,\n    defaultShowTooltip = props.defaultShowTooltip;\n  var brushItem = findChildByType(children, Brush);\n  var startIndex = brushItem && brushItem.props && brushItem.props.startIndex || 0;\n  var endIndex = (brushItem === null || brushItem === void 0 ? void 0 : (_brushItem$props = brushItem.props) === null || _brushItem$props === void 0 ? void 0 : _brushItem$props.endIndex) !== undefined ? brushItem === null || brushItem === void 0 ? void 0 : (_brushItem$props2 = brushItem.props) === null || _brushItem$props2 === void 0 ? void 0 : _brushItem$props2.endIndex : props.data && props.data.length - 1 || 0;\n  return {\n    chartX: 0,\n    chartY: 0,\n    dataStartIndex: startIndex,\n    dataEndIndex: endIndex,\n    activeTooltipIndex: -1,\n    isTooltipActive: !_isNil(defaultShowTooltip) ? defaultShowTooltip : false\n  };\n};\nvar hasGraphicalBarItem = function hasGraphicalBarItem(graphicalItems) {\n  if (!graphicalItems || !graphicalItems.length) {\n    return false;\n  }\n  return graphicalItems.some(function (item) {\n    var name = getDisplayName(item && item.type);\n    return name && name.indexOf('Bar') >= 0;\n  });\n};\nvar getAxisNameByLayout = function getAxisNameByLayout(layout) {\n  if (layout === 'horizontal') {\n    return {\n      numericAxisName: 'yAxis',\n      cateAxisName: 'xAxis'\n    };\n  }\n  if (layout === 'vertical') {\n    return {\n      numericAxisName: 'xAxis',\n      cateAxisName: 'yAxis'\n    };\n  }\n  if (layout === 'centric') {\n    return {\n      numericAxisName: 'radiusAxis',\n      cateAxisName: 'angleAxis'\n    };\n  }\n  return {\n    numericAxisName: 'angleAxis',\n    cateAxisName: 'radiusAxis'\n  };\n};\n\n/**\n * Calculate the offset of main part in the svg element\n * @param  {Object} props          Latest props\n * graphicalItems The instances of item\n * xAxisMap       The configuration of x-axis\n * yAxisMap       The configuration of y-axis\n * @param  {Object} prevLegendBBox          the boundary box of legend\n * @return {Object} The offset of main part in the svg element\n */\nvar calculateOffset = function calculateOffset(_ref5, prevLegendBBox) {\n  var props = _ref5.props,\n    graphicalItems = _ref5.graphicalItems,\n    _ref5$xAxisMap = _ref5.xAxisMap,\n    xAxisMap = _ref5$xAxisMap === void 0 ? {} : _ref5$xAxisMap,\n    _ref5$yAxisMap = _ref5.yAxisMap,\n    yAxisMap = _ref5$yAxisMap === void 0 ? {} : _ref5$yAxisMap;\n  var width = props.width,\n    height = props.height,\n    children = props.children;\n  var margin = props.margin || {};\n  var brushItem = findChildByType(children, Brush);\n  var legendItem = findChildByType(children, Legend);\n  var offsetH = Object.keys(yAxisMap).reduce(function (result, id) {\n    var entry = yAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, result[orientation] + entry.width));\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = Object.keys(xAxisMap).reduce(function (result, id) {\n    var entry = xAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, _get(result, \"\".concat(orientation)) + entry.height));\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  if (brushItem) {\n    offset.bottom += brushItem.props.height || Brush.defaultProps.height;\n  }\n  if (legendItem && prevLegendBBox) {\n    offset = appendOffsetOfLegend(offset, graphicalItems, props, prevLegendBBox);\n  }\n  return _objectSpread(_objectSpread({\n    brushBottom: brushBottom\n  }, offset), {}, {\n    width: width - offset.left - offset.right,\n    height: height - offset.top - offset.bottom\n  });\n};\nexport var generateCategoricalChart = function generateCategoricalChart(_ref6) {\n  var _class;\n  var chartName = _ref6.chartName,\n    GraphicalChild = _ref6.GraphicalChild,\n    _ref6$defaultTooltipE = _ref6.defaultTooltipEventType,\n    defaultTooltipEventType = _ref6$defaultTooltipE === void 0 ? 'axis' : _ref6$defaultTooltipE,\n    _ref6$validateTooltip = _ref6.validateTooltipEventTypes,\n    validateTooltipEventTypes = _ref6$validateTooltip === void 0 ? ['axis'] : _ref6$validateTooltip,\n    axisComponents = _ref6.axisComponents,\n    legendContent = _ref6.legendContent,\n    formatAxisMap = _ref6.formatAxisMap,\n    defaultProps = _ref6.defaultProps;\n  var getFormatItems = function getFormatItems(props, currentState) {\n    var graphicalItems = currentState.graphicalItems,\n      stackGroups = currentState.stackGroups,\n      offset = currentState.offset,\n      updateId = currentState.updateId,\n      dataStartIndex = currentState.dataStartIndex,\n      dataEndIndex = currentState.dataEndIndex;\n    var barSize = props.barSize,\n      layout = props.layout,\n      barGap = props.barGap,\n      barCategoryGap = props.barCategoryGap,\n      globalMaxBarSize = props.maxBarSize;\n    var _getAxisNameByLayout = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout.numericAxisName,\n      cateAxisName = _getAxisNameByLayout.cateAxisName;\n    var hasBar = hasGraphicalBarItem(graphicalItems);\n    var sizeList = hasBar && getBarSizeList({\n      barSize: barSize,\n      stackGroups: stackGroups\n    });\n    var formattedItems = [];\n    graphicalItems.forEach(function (item, index) {\n      var displayedData = getDisplayedData(props.data, {\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }, item);\n      var _item$props = item.props,\n        dataKey = _item$props.dataKey,\n        childMaxBarSize = _item$props.maxBarSize;\n      var numericAxisId = item.props[\"\".concat(numericAxisName, \"Id\")];\n      var cateAxisId = item.props[\"\".concat(cateAxisName, \"Id\")];\n      var axisObj = axisComponents.reduce(function (result, entry) {\n        var _objectSpread6;\n        var axisMap = currentState[\"\".concat(entry.axisType, \"Map\")];\n        var id = item.props[\"\".concat(entry.axisType, \"Id\")];\n        var axis = axisMap && axisMap[id];\n        return _objectSpread(_objectSpread({}, result), {}, (_objectSpread6 = {}, _defineProperty(_objectSpread6, entry.axisType, axis), _defineProperty(_objectSpread6, \"\".concat(entry.axisType, \"Ticks\"), getTicksOfAxis(axis)), _objectSpread6));\n      }, {});\n      var cateAxis = axisObj[cateAxisName];\n      var cateTicks = axisObj[\"\".concat(cateAxisName, \"Ticks\")];\n      var stackedData = stackGroups && stackGroups[numericAxisId] && stackGroups[numericAxisId].hasStack && getStackedDataOfItem(item, stackGroups[numericAxisId].stackGroups);\n      var itemIsBar = getDisplayName(item.type).indexOf('Bar') >= 0;\n      var bandSize = getBandSizeOfAxis(cateAxis, cateTicks);\n      var barPosition = [];\n      if (itemIsBar) {\n        var _ref7, _getBandSizeOfAxis;\n        // 如果是bar，计算bar的位置\n        var maxBarSize = _isNil(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n        var barBandSize = (_ref7 = (_getBandSizeOfAxis = getBandSizeOfAxis(cateAxis, cateTicks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref7 !== void 0 ? _ref7 : 0;\n        barPosition = getBarPosition({\n          barGap: barGap,\n          barCategoryGap: barCategoryGap,\n          bandSize: barBandSize !== bandSize ? barBandSize : bandSize,\n          sizeList: sizeList[cateAxisId],\n          maxBarSize: maxBarSize\n        });\n        if (barBandSize !== bandSize) {\n          barPosition = barPosition.map(function (pos) {\n            return _objectSpread(_objectSpread({}, pos), {}, {\n              position: _objectSpread(_objectSpread({}, pos.position), {}, {\n                offset: pos.position.offset - barBandSize / 2\n              })\n            });\n          });\n        }\n      }\n      var composedFn = item && item.type && item.type.getComposedData;\n      if (composedFn) {\n        var _objectSpread7;\n        formattedItems.push({\n          props: _objectSpread(_objectSpread({}, composedFn(_objectSpread(_objectSpread({}, axisObj), {}, {\n            displayedData: displayedData,\n            props: props,\n            dataKey: dataKey,\n            item: item,\n            bandSize: bandSize,\n            barPosition: barPosition,\n            offset: offset,\n            stackedData: stackedData,\n            layout: layout,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }))), {}, (_objectSpread7 = {\n            key: item.key || \"item-\".concat(index)\n          }, _defineProperty(_objectSpread7, numericAxisName, axisObj[numericAxisName]), _defineProperty(_objectSpread7, cateAxisName, axisObj[cateAxisName]), _defineProperty(_objectSpread7, \"animationId\", updateId), _objectSpread7)),\n          childIndex: parseChildIndex(item, props.children),\n          item: item\n        });\n      }\n    });\n    return formattedItems;\n  };\n\n  /**\n   * The AxisMaps are expensive to render on large data sets\n   * so provide the ability to store them in state and only update them when necessary\n   * they are dependent upon the start and end index of\n   * the brush so it's important that this method is called _after_\n   * the state is updated with any new start/end indices\n   *\n   * @param {Object} props          The props object to be used for updating the axismaps\n   * dataStartIndex: The start index of the data series when a brush is applied\n   * dataEndIndex: The end index of the data series when a brush is applied\n   * updateId: The update id\n   * @param {Object} prevState      Prev state\n   * @return {Object} state New state to set\n   */\n  var updateStateOfAxisMapsOffsetAndStackGroups = function updateStateOfAxisMapsOffsetAndStackGroups(_ref8, prevState) {\n    var props = _ref8.props,\n      dataStartIndex = _ref8.dataStartIndex,\n      dataEndIndex = _ref8.dataEndIndex,\n      updateId = _ref8.updateId;\n    if (!validateWidthHeight({\n      props: props\n    })) {\n      return null;\n    }\n    var children = props.children,\n      layout = props.layout,\n      stackOffset = props.stackOffset,\n      data = props.data,\n      reverseStackOrder = props.reverseStackOrder;\n    var _getAxisNameByLayout2 = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout2.numericAxisName,\n      cateAxisName = _getAxisNameByLayout2.cateAxisName;\n    var graphicalItems = findAllByType(children, GraphicalChild);\n    var stackGroups = getStackGroupsByAxisId(data, graphicalItems, \"\".concat(numericAxisName, \"Id\"), \"\".concat(cateAxisName, \"Id\"), stackOffset, reverseStackOrder);\n    var axisObj = axisComponents.reduce(function (result, entry) {\n      var name = \"\".concat(entry.axisType, \"Map\");\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, name, getAxisMap(props, _objectSpread(_objectSpread({}, entry), {}, {\n        graphicalItems: graphicalItems,\n        stackGroups: entry.axisType === numericAxisName && stackGroups,\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }))));\n    }, {});\n    var offset = calculateOffset(_objectSpread(_objectSpread({}, axisObj), {}, {\n      props: props,\n      graphicalItems: graphicalItems\n    }), prevState === null || prevState === void 0 ? void 0 : prevState.legendBBox);\n    Object.keys(axisObj).forEach(function (key) {\n      axisObj[key] = formatAxisMap(props, axisObj[key], offset, key.replace('Map', ''), chartName);\n    });\n    var cateAxisMap = axisObj[\"\".concat(cateAxisName, \"Map\")];\n    var ticksObj = tooltipTicksGenerator(cateAxisMap);\n    var formattedGraphicalItems = getFormatItems(props, _objectSpread(_objectSpread({}, axisObj), {}, {\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex,\n      updateId: updateId,\n      graphicalItems: graphicalItems,\n      stackGroups: stackGroups,\n      offset: offset\n    }));\n    return _objectSpread(_objectSpread({\n      formattedGraphicalItems: formattedGraphicalItems,\n      graphicalItems: graphicalItems,\n      offset: offset,\n      stackGroups: stackGroups\n    }, ticksObj), axisObj);\n  };\n  return _class = /*#__PURE__*/function (_Component) {\n    _inherits(CategoricalChartWrapper, _Component);\n    var _super = _createSuper(CategoricalChartWrapper);\n    // todo join specific chart propTypes\n\n    function CategoricalChartWrapper(_props) {\n      var _this;\n      _classCallCheck(this, CategoricalChartWrapper);\n      _this = _super.call(this, _props);\n      _defineProperty(_assertThisInitialized(_this), \"clearDeferId\", function () {\n        if (!_isNil(_this.deferId) && deferClear) {\n          deferClear(_this.deferId);\n        }\n        _this.deferId = null;\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleLegendBBoxUpdate\", function (box) {\n        if (box) {\n          var _this$state = _this.state,\n            dataStartIndex = _this$state.dataStartIndex,\n            dataEndIndex = _this$state.dataEndIndex,\n            updateId = _this$state.updateId;\n          _this.setState(_objectSpread({\n            legendBBox: box\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _objectSpread(_objectSpread({}, _this.state), {}, {\n            legendBBox: box\n          }))));\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleReceiveSyncEvent\", function (cId, chartId, data) {\n        var syncId = _this.props.syncId;\n        if (syncId === cId && chartId !== _this.uniqueChartId) {\n          _this.clearDeferId();\n          _this.deferId = defer && defer(_this.applySyncEvent.bind(_assertThisInitialized(_this), data));\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleBrushChange\", function (_ref9) {\n        var startIndex = _ref9.startIndex,\n          endIndex = _ref9.endIndex;\n        // Only trigger changes if the extents of the brush have actually changed\n        if (startIndex !== _this.state.dataStartIndex || endIndex !== _this.state.dataEndIndex) {\n          var updateId = _this.state.updateId;\n          _this.setState(function () {\n            return _objectSpread({\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex\n            }, updateStateOfAxisMapsOffsetAndStackGroups({\n              props: _this.props,\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex,\n              updateId: updateId\n            }, _this.state));\n          });\n          _this.triggerSyncEvent({\n            dataStartIndex: startIndex,\n            dataEndIndex: endIndex\n          });\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseEnter\", function (e) {\n        var onMouseEnter = _this.props.onMouseEnter;\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState);\n          _this.triggerSyncEvent(_nextState);\n          if (_isFunction(onMouseEnter)) {\n            onMouseEnter(_nextState, e);\n          }\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"triggeredAfterMouseMove\", function (e) {\n        var onMouseMove = _this.props.onMouseMove;\n        var mouse = _this.getMouseInfo(e);\n        var nextState = mouse ? _objectSpread(_objectSpread({}, mouse), {}, {\n          isTooltipActive: true\n        }) : {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        if (_isFunction(onMouseMove)) {\n          onMouseMove(nextState, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleItemMouseEnter\", function (el) {\n        _this.setState(function () {\n          return {\n            isTooltipActive: true,\n            activeItem: el,\n            activePayload: el.tooltipPayload,\n            activeCoordinate: el.tooltipPosition || {\n              x: el.cx,\n              y: el.cy\n            }\n          };\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleItemMouseLeave\", function () {\n        _this.setState(function () {\n          return {\n            isTooltipActive: false\n          };\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseMove\", function (e) {\n        if (e && _isFunction(e.persist)) {\n          e.persist();\n        }\n        _this.triggeredAfterMouseMove(e);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseLeave\", function (e) {\n        var onMouseLeave = _this.props.onMouseLeave;\n        var nextState = {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        if (_isFunction(onMouseLeave)) {\n          onMouseLeave(nextState, e);\n        }\n        _this.cancelThrottledTriggerAfterMouseMove();\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleOuterEvent\", function (e) {\n        var eventName = getReactEventByType(e);\n        var event = _get(_this.props, \"\".concat(eventName));\n        if (eventName && _isFunction(event)) {\n          var mouse;\n          if (/.*touch.*/i.test(eventName)) {\n            mouse = _this.getMouseInfo(e.changedTouches[0]);\n          } else {\n            mouse = _this.getMouseInfo(e);\n          }\n          var handler = event;\n\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          handler(mouse, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleClick\", function (e) {\n        var onClick = _this.props.onClick;\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState2 = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState2);\n          _this.triggerSyncEvent(_nextState2);\n          if (_isFunction(onClick)) {\n            onClick(_nextState2, e);\n          }\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseDown\", function (e) {\n        var onMouseDown = _this.props.onMouseDown;\n        if (_isFunction(onMouseDown)) {\n          var _nextState3 = _this.getMouseInfo(e);\n          onMouseDown(_nextState3, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseUp\", function (e) {\n        var onMouseUp = _this.props.onMouseUp;\n        if (_isFunction(onMouseUp)) {\n          var _nextState4 = _this.getMouseInfo(e);\n          onMouseUp(_nextState4, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchMove\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseMove(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchStart\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseDown(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchEnd\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseUp(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"verticalCoordinatesGenerator\", function (_ref10) {\n        var xAxis = _ref10.xAxis,\n          width = _ref10.width,\n          height = _ref10.height,\n          offset = _ref10.offset;\n        return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n          ticks: getTicksOfAxis(xAxis, true),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }\n        })), offset.left, offset.left + offset.width);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"horizontalCoordinatesGenerator\", function (_ref11) {\n        var yAxis = _ref11.yAxis,\n          width = _ref11.width,\n          height = _ref11.height,\n          offset = _ref11.offset;\n        return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n          ticks: getTicksOfAxis(yAxis, true),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }\n        })), offset.top, offset.top + offset.height);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"axesTicksGenerator\", function (axis) {\n        return getTicksOfAxis(axis, true);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderCursor\", function (element) {\n        var _this$state2 = _this.state,\n          isTooltipActive = _this$state2.isTooltipActive,\n          activeCoordinate = _this$state2.activeCoordinate,\n          activePayload = _this$state2.activePayload,\n          offset = _this$state2.offset,\n          activeTooltipIndex = _this$state2.activeTooltipIndex;\n        var tooltipEventType = _this.getTooltipEventType();\n        if (!element || !element.props.cursor || !isTooltipActive || !activeCoordinate || chartName !== 'ScatterChart' && tooltipEventType !== 'axis') {\n          return null;\n        }\n        var layout = _this.props.layout;\n        var restProps;\n        var cursorComp = Curve;\n        if (chartName === 'ScatterChart') {\n          restProps = activeCoordinate;\n          cursorComp = Cross;\n        } else if (chartName === 'BarChart') {\n          restProps = _this.getCursorRectangle();\n          cursorComp = Rectangle;\n        } else if (layout === 'radial') {\n          var _this$getCursorPoints = _this.getCursorPoints(),\n            cx = _this$getCursorPoints.cx,\n            cy = _this$getCursorPoints.cy,\n            radius = _this$getCursorPoints.radius,\n            startAngle = _this$getCursorPoints.startAngle,\n            endAngle = _this$getCursorPoints.endAngle;\n          restProps = {\n            cx: cx,\n            cy: cy,\n            startAngle: startAngle,\n            endAngle: endAngle,\n            innerRadius: radius,\n            outerRadius: radius\n          };\n          cursorComp = Sector;\n        } else {\n          restProps = {\n            points: _this.getCursorPoints()\n          };\n          cursorComp = Curve;\n        }\n        var key = element.key || '_recharts-cursor';\n        var cursorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          stroke: '#ccc',\n          pointerEvents: 'none'\n        }, offset), restProps), filterProps(element.props.cursor)), {}, {\n          payload: activePayload,\n          payloadIndex: activeTooltipIndex,\n          key: key,\n          className: 'recharts-tooltip-cursor'\n        });\n        return /*#__PURE__*/isValidElement(element.props.cursor) ? /*#__PURE__*/cloneElement(element.props.cursor, cursorProps) : /*#__PURE__*/createElement(cursorComp, cursorProps);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderPolarAxis\", function (element, displayName, index) {\n        var axisType = _get(element, 'type.axisType');\n        var axisMap = _get(_this.state, \"\".concat(axisType, \"Map\"));\n        var axisOption = axisMap && axisMap[element.props[\"\".concat(axisType, \"Id\")]];\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, axisOption), {}, {\n          className: axisType,\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          ticks: getTicksOfAxis(axisOption, true)\n        }));\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderXAxis\", function (element, displayName, index) {\n        var xAxisMap = _this.state.xAxisMap;\n        var axisObj = xAxisMap[element.props.xAxisId];\n        return _this.renderAxis(axisObj, element, displayName, index);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderYAxis\", function (element, displayName, index) {\n        var yAxisMap = _this.state.yAxisMap;\n        var axisObj = yAxisMap[element.props.yAxisId];\n        return _this.renderAxis(axisObj, element, displayName, index);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderGrid\", function (element) {\n        var _this$state3 = _this.state,\n          xAxisMap = _this$state3.xAxisMap,\n          yAxisMap = _this$state3.yAxisMap,\n          offset = _this$state3.offset;\n        var _this$props = _this.props,\n          width = _this$props.width,\n          height = _this$props.height;\n        var xAxis = getAnyElementOfObject(xAxisMap);\n        var yAxisWithFiniteDomain = _find(yAxisMap, function (axis) {\n          return _every(axis.domain, isFinit);\n        });\n        var yAxis = yAxisWithFiniteDomain || getAnyElementOfObject(yAxisMap);\n        var props = element.props || {};\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || 'grid',\n          x: isNumber(props.x) ? props.x : offset.left,\n          y: isNumber(props.y) ? props.y : offset.top,\n          width: isNumber(props.width) ? props.width : offset.width,\n          height: isNumber(props.height) ? props.height : offset.height,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          offset: offset,\n          chartWidth: width,\n          chartHeight: height,\n          verticalCoordinatesGenerator: props.verticalCoordinatesGenerator || _this.verticalCoordinatesGenerator,\n          horizontalCoordinatesGenerator: props.horizontalCoordinatesGenerator || _this.horizontalCoordinatesGenerator\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderPolarGrid\", function (element) {\n        var _element$props = element.props,\n          radialLines = _element$props.radialLines,\n          polarAngles = _element$props.polarAngles,\n          polarRadius = _element$props.polarRadius;\n        var _this$state4 = _this.state,\n          radiusAxisMap = _this$state4.radiusAxisMap,\n          angleAxisMap = _this$state4.angleAxisMap;\n        var radiusAxis = getAnyElementOfObject(radiusAxisMap);\n        var angleAxis = getAnyElementOfObject(angleAxisMap);\n        var cx = angleAxis.cx,\n          cy = angleAxis.cy,\n          innerRadius = angleAxis.innerRadius,\n          outerRadius = angleAxis.outerRadius;\n        return /*#__PURE__*/cloneElement(element, {\n          polarAngles: _isArray(polarAngles) ? polarAngles : getTicksOfAxis(angleAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          polarRadius: _isArray(polarRadius) ? polarRadius : getTicksOfAxis(radiusAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          key: element.key || 'polar-grid',\n          radialLines: radialLines\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderLegend\", function () {\n        var formattedGraphicalItems = _this.state.formattedGraphicalItems;\n        var _this$props2 = _this.props,\n          children = _this$props2.children,\n          width = _this$props2.width,\n          height = _this$props2.height;\n        var margin = _this.props.margin || {};\n        var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n        var props = getLegendProps({\n          children: children,\n          formattedGraphicalItems: formattedGraphicalItems,\n          legendWidth: legendWidth,\n          legendContent: legendContent\n        });\n        if (!props) {\n          return null;\n        }\n        var item = props.item,\n          otherProps = _objectWithoutProperties(props, _excluded);\n        return /*#__PURE__*/cloneElement(item, _objectSpread(_objectSpread({}, otherProps), {}, {\n          chartWidth: width,\n          chartHeight: height,\n          margin: margin,\n          ref: function ref(legend) {\n            _this.legendInstance = legend;\n          },\n          onBBoxUpdate: _this.handleLegendBBoxUpdate\n        }));\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderTooltip\", function () {\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        if (!tooltipItem) {\n          return null;\n        }\n        var _this$state5 = _this.state,\n          isTooltipActive = _this$state5.isTooltipActive,\n          activeCoordinate = _this$state5.activeCoordinate,\n          activePayload = _this$state5.activePayload,\n          activeLabel = _this$state5.activeLabel,\n          offset = _this$state5.offset;\n        return /*#__PURE__*/cloneElement(tooltipItem, {\n          viewBox: _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          }),\n          active: isTooltipActive,\n          label: activeLabel,\n          payload: isTooltipActive ? activePayload : [],\n          coordinate: activeCoordinate\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderBrush\", function (element) {\n        var _this$props3 = _this.props,\n          margin = _this$props3.margin,\n          data = _this$props3.data;\n        var _this$state6 = _this.state,\n          offset = _this$state6.offset,\n          dataStartIndex = _this$state6.dataStartIndex,\n          dataEndIndex = _this$state6.dataEndIndex,\n          updateId = _this$state6.updateId;\n\n        // TODO: update brush when children update\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || '_recharts-brush',\n          onChange: combineEventHandlers(_this.handleBrushChange, null, element.props.onChange),\n          data: data,\n          x: isNumber(element.props.x) ? element.props.x : offset.left,\n          y: isNumber(element.props.y) ? element.props.y : offset.top + offset.height + offset.brushBottom - (margin.bottom || 0),\n          width: isNumber(element.props.width) ? element.props.width : offset.width,\n          startIndex: dataStartIndex,\n          endIndex: dataEndIndex,\n          updateId: \"brush-\".concat(updateId)\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderReferenceElement\", function (element, displayName, index) {\n        if (!element) {\n          return null;\n        }\n        var _assertThisInitialize = _assertThisInitialized(_this),\n          clipPathId = _assertThisInitialize.clipPathId;\n        var _this$state7 = _this.state,\n          xAxisMap = _this$state7.xAxisMap,\n          yAxisMap = _this$state7.yAxisMap,\n          offset = _this$state7.offset;\n        var _element$props2 = element.props,\n          xAxisId = _element$props2.xAxisId,\n          yAxisId = _element$props2.yAxisId;\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          xAxis: xAxisMap[xAxisId],\n          yAxis: yAxisMap[yAxisId],\n          viewBox: {\n            x: offset.left,\n            y: offset.top,\n            width: offset.width,\n            height: offset.height\n          },\n          clipPathId: clipPathId\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderActivePoints\", function (_ref12) {\n        var item = _ref12.item,\n          activePoint = _ref12.activePoint,\n          basePoint = _ref12.basePoint,\n          childIndex = _ref12.childIndex,\n          isRange = _ref12.isRange;\n        var result = [];\n        var key = item.props.key;\n        var _item$item$props = item.item.props,\n          activeDot = _item$item$props.activeDot,\n          dataKey = _item$item$props.dataKey;\n        var dotProps = _objectSpread(_objectSpread({\n          index: childIndex,\n          dataKey: dataKey,\n          cx: activePoint.x,\n          cy: activePoint.y,\n          r: 4,\n          fill: getMainColorOfGraphicItem(item.item),\n          strokeWidth: 2,\n          stroke: '#fff',\n          payload: activePoint.payload,\n          value: activePoint.value,\n          key: \"\".concat(key, \"-activePoint-\").concat(childIndex)\n        }, filterProps(activeDot)), adaptEventHandlers(activeDot));\n        result.push(CategoricalChartWrapper.renderActiveDot(activeDot, dotProps));\n        if (basePoint) {\n          result.push(CategoricalChartWrapper.renderActiveDot(activeDot, _objectSpread(_objectSpread({}, dotProps), {}, {\n            cx: basePoint.x,\n            cy: basePoint.y,\n            key: \"\".concat(key, \"-basePoint-\").concat(childIndex)\n          })));\n        } else if (isRange) {\n          result.push(null);\n        }\n        return result;\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderGraphicChild\", function (element, displayName, index) {\n        var item = _this.filterFormatItem(element, displayName, index);\n        if (!item) {\n          return null;\n        }\n        var tooltipEventType = _this.getTooltipEventType();\n        var _this$state8 = _this.state,\n          isTooltipActive = _this$state8.isTooltipActive,\n          tooltipAxis = _this$state8.tooltipAxis,\n          activeTooltipIndex = _this$state8.activeTooltipIndex,\n          activeLabel = _this$state8.activeLabel;\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        var _item$props2 = item.props,\n          points = _item$props2.points,\n          isRange = _item$props2.isRange,\n          baseLine = _item$props2.baseLine;\n        var _item$item$props2 = item.item.props,\n          activeDot = _item$item$props2.activeDot,\n          hide = _item$item$props2.hide;\n        var hasActive = !hide && isTooltipActive && tooltipItem && activeDot && activeTooltipIndex >= 0;\n        var itemEvents = {};\n        if (tooltipEventType !== 'axis' && tooltipItem && tooltipItem.props.trigger === 'click') {\n          itemEvents = {\n            onClick: combineEventHandlers(_this.handleItemMouseEnter, null, element.props.onCLick)\n          };\n        } else if (tooltipEventType !== 'axis') {\n          itemEvents = {\n            onMouseLeave: combineEventHandlers(_this.handleItemMouseLeave, null, element.props.onMouseLeave),\n            onMouseEnter: combineEventHandlers(_this.handleItemMouseEnter, null, element.props.onMouseEnter)\n          };\n        }\n        var graphicalItem = /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, item.props), itemEvents));\n        function findWithPayload(entry) {\n          // TODO needs to verify dataKey is Function\n          return typeof tooltipAxis.dataKey === 'function' ? tooltipAxis.dataKey(entry.payload) : null;\n        }\n        if (hasActive) {\n          var activePoint, basePoint;\n          if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n            // number transform to string\n            var specifiedKey = typeof tooltipAxis.dataKey === 'function' ? findWithPayload : 'payload.'.concat(tooltipAxis.dataKey.toString());\n            activePoint = findEntryInArray(points, specifiedKey, activeLabel);\n            basePoint = isRange && baseLine && findEntryInArray(baseLine, specifiedKey, activeLabel);\n          } else {\n            activePoint = points[activeTooltipIndex];\n            basePoint = isRange && baseLine && baseLine[activeTooltipIndex];\n          }\n          if (!_isNil(activePoint)) {\n            return [graphicalItem].concat(_toConsumableArray(_this.renderActivePoints({\n              item: item,\n              activePoint: activePoint,\n              basePoint: basePoint,\n              childIndex: activeTooltipIndex,\n              isRange: isRange\n            })));\n          }\n        }\n        if (isRange) {\n          return [graphicalItem, null, null];\n        }\n        return [graphicalItem, null];\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderCustomized\", function (element, displayName, index) {\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({\n          key: \"recharts-customized-\".concat(index)\n        }, _this.props), _this.state));\n      });\n      _this.uniqueChartId = _isNil(_props.id) ? uniqueId('recharts') : _props.id;\n      _this.clipPathId = \"\".concat(_this.uniqueChartId, \"-clip\");\n      if (_props.throttleDelay) {\n        _this.triggeredAfterMouseMove = _throttle(_this.triggeredAfterMouseMove, _props.throttleDelay);\n      }\n      _this.state = {};\n      return _this;\n    }\n    _createClass(CategoricalChartWrapper, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if (!_isNil(this.props.syncId)) {\n          this.addListener();\n        }\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        // add syncId\n        if (_isNil(prevProps.syncId) && !_isNil(this.props.syncId)) {\n          this.addListener();\n        }\n        // remove syncId\n        if (!_isNil(prevProps.syncId) && _isNil(this.props.syncId)) {\n          this.removeListener();\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDeferId();\n        if (!_isNil(this.props.syncId)) {\n          this.removeListener();\n        }\n        this.cancelThrottledTriggerAfterMouseMove();\n      }\n    }, {\n      key: \"cancelThrottledTriggerAfterMouseMove\",\n      value: function cancelThrottledTriggerAfterMouseMove() {\n        if (typeof this.triggeredAfterMouseMove.cancel === 'function') {\n          this.triggeredAfterMouseMove.cancel();\n        }\n      }\n    }, {\n      key: \"getTooltipEventType\",\n      value: function getTooltipEventType() {\n        var tooltipItem = findChildByType(this.props.children, Tooltip);\n        if (tooltipItem && _isBoolean(tooltipItem.props.shared)) {\n          var eventType = tooltipItem.props.shared ? 'axis' : 'item';\n          return validateTooltipEventTypes.indexOf(eventType) >= 0 ? eventType : defaultTooltipEventType;\n        }\n        return defaultTooltipEventType;\n      }\n\n      /**\n       * Get the information of mouse in chart, return null when the mouse is not in the chart\n       * @param  {Object} event    The event object\n       * @return {Object}          Mouse data\n       */\n    }, {\n      key: \"getMouseInfo\",\n      value: function getMouseInfo(event) {\n        if (!this.container) {\n          return null;\n        }\n        var containerOffset = getOffset(this.container);\n        var e = calculateChartCoordinate(event, containerOffset);\n        var rangeObj = this.inRange(e.chartX, e.chartY);\n        if (!rangeObj) {\n          return null;\n        }\n        var _this$state9 = this.state,\n          xAxisMap = _this$state9.xAxisMap,\n          yAxisMap = _this$state9.yAxisMap;\n        var tooltipEventType = this.getTooltipEventType();\n        if (tooltipEventType !== 'axis' && xAxisMap && yAxisMap) {\n          var xScale = getAnyElementOfObject(xAxisMap).scale;\n          var yScale = getAnyElementOfObject(yAxisMap).scale;\n          var xValue = xScale && xScale.invert ? xScale.invert(e.chartX) : null;\n          var yValue = yScale && yScale.invert ? yScale.invert(e.chartY) : null;\n          return _objectSpread(_objectSpread({}, e), {}, {\n            xValue: xValue,\n            yValue: yValue\n          });\n        }\n        var toolTipData = getTooltipData(this.state, this.props.data, this.props.layout, rangeObj);\n        if (toolTipData) {\n          return _objectSpread(_objectSpread({}, e), toolTipData);\n        }\n        return null;\n      }\n    }, {\n      key: \"getCursorRectangle\",\n      value: function getCursorRectangle() {\n        var layout = this.props.layout;\n        var _this$state10 = this.state,\n          activeCoordinate = _this$state10.activeCoordinate,\n          offset = _this$state10.offset,\n          tooltipAxisBandSize = _this$state10.tooltipAxisBandSize;\n        var halfSize = tooltipAxisBandSize / 2;\n        return {\n          stroke: 'none',\n          fill: '#ccc',\n          x: layout === 'horizontal' ? activeCoordinate.x - halfSize : offset.left + 0.5,\n          y: layout === 'horizontal' ? offset.top + 0.5 : activeCoordinate.y - halfSize,\n          width: layout === 'horizontal' ? tooltipAxisBandSize : offset.width - 1,\n          height: layout === 'horizontal' ? offset.height - 1 : tooltipAxisBandSize\n        };\n      }\n    }, {\n      key: \"getCursorPoints\",\n      value: function getCursorPoints() {\n        var layout = this.props.layout;\n        var _this$state11 = this.state,\n          activeCoordinate = _this$state11.activeCoordinate,\n          offset = _this$state11.offset;\n        var x1, y1, x2, y2;\n        if (layout === 'horizontal') {\n          x1 = activeCoordinate.x;\n          x2 = x1;\n          y1 = offset.top;\n          y2 = offset.top + offset.height;\n        } else if (layout === 'vertical') {\n          y1 = activeCoordinate.y;\n          y2 = y1;\n          x1 = offset.left;\n          x2 = offset.left + offset.width;\n        } else if (!_isNil(activeCoordinate.cx) || !_isNil(activeCoordinate.cy)) {\n          if (layout === 'centric') {\n            var cx = activeCoordinate.cx,\n              cy = activeCoordinate.cy,\n              innerRadius = activeCoordinate.innerRadius,\n              outerRadius = activeCoordinate.outerRadius,\n              angle = activeCoordinate.angle;\n            var innerPoint = polarToCartesian(cx, cy, innerRadius, angle);\n            var outerPoint = polarToCartesian(cx, cy, outerRadius, angle);\n            x1 = innerPoint.x;\n            y1 = innerPoint.y;\n            x2 = outerPoint.x;\n            y2 = outerPoint.y;\n          } else {\n            var _cx = activeCoordinate.cx,\n              _cy = activeCoordinate.cy,\n              radius = activeCoordinate.radius,\n              startAngle = activeCoordinate.startAngle,\n              endAngle = activeCoordinate.endAngle;\n            var startPoint = polarToCartesian(_cx, _cy, radius, startAngle);\n            var endPoint = polarToCartesian(_cx, _cy, radius, endAngle);\n            return {\n              points: [startPoint, endPoint],\n              cx: _cx,\n              cy: _cy,\n              radius: radius,\n              startAngle: startAngle,\n              endAngle: endAngle\n            };\n          }\n        }\n        return [{\n          x: x1,\n          y: y1\n        }, {\n          x: x2,\n          y: y2\n        }];\n      }\n    }, {\n      key: \"inRange\",\n      value: function inRange(x, y) {\n        var layout = this.props.layout;\n        if (layout === 'horizontal' || layout === 'vertical') {\n          var offset = this.state.offset;\n          var isInRange = x >= offset.left && x <= offset.left + offset.width && y >= offset.top && y <= offset.top + offset.height;\n          return isInRange ? {\n            x: x,\n            y: y\n          } : null;\n        }\n        var _this$state12 = this.state,\n          angleAxisMap = _this$state12.angleAxisMap,\n          radiusAxisMap = _this$state12.radiusAxisMap;\n        if (angleAxisMap && radiusAxisMap) {\n          var angleAxis = getAnyElementOfObject(angleAxisMap);\n          return inRangeOfSector({\n            x: x,\n            y: y\n          }, angleAxis);\n        }\n        return null;\n      }\n    }, {\n      key: \"parseEventsOfWrapper\",\n      value: function parseEventsOfWrapper() {\n        var children = this.props.children;\n        var tooltipEventType = this.getTooltipEventType();\n        var tooltipItem = findChildByType(children, Tooltip);\n        var tooltipEvents = {};\n        if (tooltipItem && tooltipEventType === 'axis') {\n          if (tooltipItem.props.trigger === 'click') {\n            tooltipEvents = {\n              onClick: this.handleClick\n            };\n          } else {\n            tooltipEvents = {\n              onMouseEnter: this.handleMouseEnter,\n              onMouseMove: this.handleMouseMove,\n              onMouseLeave: this.handleMouseLeave,\n              onTouchMove: this.handleTouchMove,\n              onTouchStart: this.handleTouchStart,\n              onTouchEnd: this.handleTouchEnd\n            };\n          }\n        }\n        var outerEvents = adaptEventHandlers(this.props, this.handleOuterEvent);\n        return _objectSpread(_objectSpread({}, outerEvents), tooltipEvents);\n      }\n\n      /* eslint-disable  no-underscore-dangle */\n    }, {\n      key: \"addListener\",\n      value: function addListener() {\n        eventCenter.on(SYNC_EVENT, this.handleReceiveSyncEvent);\n        if (eventCenter.setMaxListeners && eventCenter._maxListeners) {\n          eventCenter.setMaxListeners(eventCenter._maxListeners + 1);\n        }\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener() {\n        eventCenter.removeListener(SYNC_EVENT, this.handleReceiveSyncEvent);\n        if (eventCenter.setMaxListeners && eventCenter._maxListeners) {\n          eventCenter.setMaxListeners(eventCenter._maxListeners - 1);\n        }\n      }\n    }, {\n      key: \"triggerSyncEvent\",\n      value: function triggerSyncEvent(data) {\n        var syncId = this.props.syncId;\n        if (!_isNil(syncId)) {\n          eventCenter.emit(SYNC_EVENT, syncId, this.uniqueChartId, data);\n        }\n      }\n    }, {\n      key: \"applySyncEvent\",\n      value: function applySyncEvent(data) {\n        var _this$props4 = this.props,\n          layout = _this$props4.layout,\n          syncMethod = _this$props4.syncMethod;\n        var updateId = this.state.updateId;\n        var dataStartIndex = data.dataStartIndex,\n          dataEndIndex = data.dataEndIndex;\n        if (!_isNil(data.dataStartIndex) || !_isNil(data.dataEndIndex)) {\n          this.setState(_objectSpread({\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, this.state)));\n        } else if (!_isNil(data.activeTooltipIndex)) {\n          var chartX = data.chartX,\n            chartY = data.chartY;\n          var activeTooltipIndex = data.activeTooltipIndex;\n          var _this$state13 = this.state,\n            offset = _this$state13.offset,\n            tooltipTicks = _this$state13.tooltipTicks;\n          if (!offset) {\n            return;\n          }\n          if (typeof syncMethod === 'function') {\n            // Call a callback function. If there is an application specific algorithm\n            activeTooltipIndex = syncMethod(tooltipTicks, data);\n          } else if (syncMethod === 'value') {\n            // Set activeTooltipIndex to the index with the same value as data.activeLabel\n            // For loop instead of findIndex because the latter is very slow in some browsers\n            activeTooltipIndex = -1; // in case we cannot find the element\n            for (var i = 0; i < tooltipTicks.length; i++) {\n              if (tooltipTicks[i].value === data.activeLabel) {\n                activeTooltipIndex = i;\n                break;\n              }\n            }\n          }\n          var viewBox = _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          });\n          // When a categorical chart is combined with another chart, the value of chartX\n          // and chartY may beyond the boundaries.\n          var validateChartX = Math.min(chartX, viewBox.x + viewBox.width);\n          var validateChartY = Math.min(chartY, viewBox.y + viewBox.height);\n          var activeLabel = tooltipTicks[activeTooltipIndex] && tooltipTicks[activeTooltipIndex].value;\n          var activePayload = getTooltipContent(this.state, this.props.data, activeTooltipIndex);\n          var activeCoordinate = tooltipTicks[activeTooltipIndex] ? {\n            x: layout === 'horizontal' ? tooltipTicks[activeTooltipIndex].coordinate : validateChartX,\n            y: layout === 'horizontal' ? validateChartY : tooltipTicks[activeTooltipIndex].coordinate\n          } : originCoordinate;\n          this.setState(_objectSpread(_objectSpread({}, data), {}, {\n            activeLabel: activeLabel,\n            activeCoordinate: activeCoordinate,\n            activePayload: activePayload,\n            activeTooltipIndex: activeTooltipIndex\n          }));\n        } else {\n          this.setState(data);\n        }\n      }\n    }, {\n      key: \"filterFormatItem\",\n      value: function filterFormatItem(item, displayName, childIndex) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n          var entry = formattedGraphicalItems[i];\n          if (entry.item === item || entry.props.key === item.key || displayName === getDisplayName(entry.item.type) && childIndex === entry.childIndex) {\n            return entry;\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"renderAxis\",\n      value:\n      /**\n       * Draw axis\n       * @param {Object} axisOptions The options of axis\n       * @param {Object} element      The axis element\n       * @param {String} displayName  The display name of axis\n       * @param {Number} index        The index of element\n       * @return {ReactElement}       The instance of x-axes\n       */\n      function renderAxis(axisOptions, element, displayName, index) {\n        var _this$props5 = this.props,\n          width = _this$props5.width,\n          height = _this$props5.height;\n        return /*#__PURE__*/React.createElement(CartesianAxis, _extends({}, axisOptions, {\n          className: \"recharts-\".concat(axisOptions.axisType, \" \").concat(axisOptions.axisType),\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          },\n          ticksGenerator: this.axesTicksGenerator\n        }));\n      }\n\n      /**\n       * Draw grid\n       * @param  {ReactElement} element the grid item\n       * @return {ReactElement} The instance of grid\n       */\n    }, {\n      key: \"renderClipPath\",\n      value: function renderClipPath() {\n        var clipPathId = this.clipPathId;\n        var _this$state$offset = this.state.offset,\n          left = _this$state$offset.left,\n          top = _this$state$offset.top,\n          height = _this$state$offset.height,\n          width = _this$state$offset.width;\n        return /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: clipPathId\n        }, /*#__PURE__*/React.createElement(\"rect\", {\n          x: left,\n          y: top,\n          height: height,\n          width: width\n        })));\n      }\n    }, {\n      key: \"getXScales\",\n      value: function getXScales() {\n        var xAxisMap = this.state.xAxisMap;\n        return xAxisMap ? Object.entries(xAxisMap).reduce(function (res, _ref13) {\n          var _ref14 = _slicedToArray(_ref13, 2),\n            axisId = _ref14[0],\n            axisProps = _ref14[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getYScales\",\n      value: function getYScales() {\n        var yAxisMap = this.state.yAxisMap;\n        return yAxisMap ? Object.entries(yAxisMap).reduce(function (res, _ref15) {\n          var _ref16 = _slicedToArray(_ref15, 2),\n            axisId = _ref16[0],\n            axisProps = _ref16[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getXScaleByAxisId\",\n      value: function getXScaleByAxisId(axisId) {\n        var _this$state$xAxisMap, _this$state$xAxisMap$;\n        return (_this$state$xAxisMap = this.state.xAxisMap) === null || _this$state$xAxisMap === void 0 ? void 0 : (_this$state$xAxisMap$ = _this$state$xAxisMap[axisId]) === null || _this$state$xAxisMap$ === void 0 ? void 0 : _this$state$xAxisMap$.scale;\n      }\n    }, {\n      key: \"getYScaleByAxisId\",\n      value: function getYScaleByAxisId(axisId) {\n        var _this$state$yAxisMap, _this$state$yAxisMap$;\n        return (_this$state$yAxisMap = this.state.yAxisMap) === null || _this$state$yAxisMap === void 0 ? void 0 : (_this$state$yAxisMap$ = _this$state$yAxisMap[axisId]) === null || _this$state$yAxisMap$ === void 0 ? void 0 : _this$state$yAxisMap$.scale;\n      }\n    }, {\n      key: \"getItemByXY\",\n      value: function getItemByXY(chartXY) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        if (formattedGraphicalItems && formattedGraphicalItems.length) {\n          for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n            var graphicalItem = formattedGraphicalItems[i];\n            var props = graphicalItem.props,\n              item = graphicalItem.item;\n            var itemDisplayName = getDisplayName(item.type);\n            if (itemDisplayName === 'Bar') {\n              var activeBarItem = (props.data || []).find(function (entry) {\n                return isInRectangle(chartXY, entry);\n              });\n              if (activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: activeBarItem\n                };\n              }\n            } else if (itemDisplayName === 'RadialBar') {\n              var _activeBarItem = (props.data || []).find(function (entry) {\n                return inRangeOfSector(chartXY, entry);\n              });\n              if (_activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: _activeBarItem\n                };\n              }\n            }\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        if (!validateWidthHeight(this)) {\n          return null;\n        }\n        var _this$props6 = this.props,\n          children = _this$props6.children,\n          className = _this$props6.className,\n          width = _this$props6.width,\n          height = _this$props6.height,\n          style = _this$props6.style,\n          compact = _this$props6.compact,\n          title = _this$props6.title,\n          desc = _this$props6.desc,\n          others = _objectWithoutProperties(_this$props6, _excluded2);\n        var attrs = filterProps(others);\n        var map = {\n          CartesianGrid: {\n            handler: this.renderGrid,\n            once: true\n          },\n          ReferenceArea: {\n            handler: this.renderReferenceElement\n          },\n          ReferenceLine: {\n            handler: this.renderReferenceElement\n          },\n          ReferenceDot: {\n            handler: this.renderReferenceElement\n          },\n          XAxis: {\n            handler: this.renderXAxis\n          },\n          YAxis: {\n            handler: this.renderYAxis\n          },\n          Brush: {\n            handler: this.renderBrush,\n            once: true\n          },\n          Bar: {\n            handler: this.renderGraphicChild\n          },\n          Line: {\n            handler: this.renderGraphicChild\n          },\n          Area: {\n            handler: this.renderGraphicChild\n          },\n          Radar: {\n            handler: this.renderGraphicChild\n          },\n          RadialBar: {\n            handler: this.renderGraphicChild\n          },\n          Scatter: {\n            handler: this.renderGraphicChild\n          },\n          Pie: {\n            handler: this.renderGraphicChild\n          },\n          Funnel: {\n            handler: this.renderGraphicChild\n          },\n          Tooltip: {\n            handler: this.renderCursor,\n            once: true\n          },\n          PolarGrid: {\n            handler: this.renderPolarGrid,\n            once: true\n          },\n          PolarAngleAxis: {\n            handler: this.renderPolarAxis\n          },\n          PolarRadiusAxis: {\n            handler: this.renderPolarAxis\n          },\n          Customized: {\n            handler: this.renderCustomized\n          }\n        };\n\n        // The \"compact\" mode is mainly used as the panorama within Brush\n        if (compact) {\n          return /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n            width: width,\n            height: height,\n            title: title,\n            desc: desc\n          }), this.renderClipPath(), renderByOrder(children, map));\n        }\n        var events = this.parseEventsOfWrapper();\n        return /*#__PURE__*/React.createElement(\"div\", _extends({\n          className: classNames('recharts-wrapper', className),\n          style: _objectSpread({\n            position: 'relative',\n            cursor: 'default',\n            width: width,\n            height: height\n          }, style)\n        }, events, {\n          ref: function ref(node) {\n            _this2.container = node;\n          },\n          role: \"region\"\n        }), /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n          width: width,\n          height: height,\n          title: title,\n          desc: desc\n        }), this.renderClipPath(), renderByOrder(children, map)), this.renderLegend(), this.renderTooltip());\n      }\n    }]);\n    return CategoricalChartWrapper;\n  }(Component), _defineProperty(_class, \"displayName\", chartName), _defineProperty(_class, \"defaultProps\", _objectSpread({\n    layout: 'horizontal',\n    stackOffset: 'none',\n    barCategoryGap: '10%',\n    barGap: 4,\n    margin: {\n      top: 5,\n      right: 5,\n      bottom: 5,\n      left: 5\n    },\n    reverseStackOrder: false,\n    syncMethod: 'index'\n  }, defaultProps)), _defineProperty(_class, \"getDerivedStateFromProps\", function (nextProps, prevState) {\n    var data = nextProps.data,\n      children = nextProps.children,\n      width = nextProps.width,\n      height = nextProps.height,\n      layout = nextProps.layout,\n      stackOffset = nextProps.stackOffset,\n      margin = nextProps.margin;\n    if (_isNil(prevState.updateId)) {\n      var defaultState = createDefaultState(nextProps);\n      return _objectSpread(_objectSpread(_objectSpread({}, defaultState), {}, {\n        updateId: 0\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, defaultState), {}, {\n        updateId: 0\n      }), prevState)), {}, {\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || layout !== prevState.prevLayout || stackOffset !== prevState.prevStackOffset || !shallowEqual(margin, prevState.prevMargin)) {\n      var _defaultState = createDefaultState(nextProps);\n\n      // Fixes https://github.com/recharts/recharts/issues/2143\n      var keepFromPrevState = {\n        // (chartX, chartY) are (0,0) in default state, but we want to keep the last mouse position to avoid\n        // any flickering\n        chartX: prevState.chartX,\n        chartY: prevState.chartY,\n        // The tooltip should stay active when it was active in the previous render. If this is not\n        // the case, the tooltip disappears and immediately re-appears, causing a flickering effect\n        isTooltipActive: prevState.isTooltipActive\n      };\n      var updatesToState = _objectSpread(_objectSpread({}, getTooltipData(prevState, data, layout)), {}, {\n        updateId: prevState.updateId + 1\n      });\n      var newState = _objectSpread(_objectSpread(_objectSpread({}, _defaultState), keepFromPrevState), updatesToState);\n      return _objectSpread(_objectSpread(_objectSpread({}, newState), updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread({\n        props: nextProps\n      }, newState), prevState)), {}, {\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (!isChildrenEqual(children, prevState.prevChildren)) {\n      // update configuration in children\n      var hasGlobalData = !_isNil(data);\n      var newUpdateId = hasGlobalData ? prevState.updateId : prevState.updateId + 1;\n      return _objectSpread(_objectSpread({\n        updateId: newUpdateId\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, prevState), {}, {\n        updateId: newUpdateId\n      }), prevState)), {}, {\n        prevChildren: children\n      });\n    }\n    return null;\n  }), _defineProperty(_class, \"renderActiveDot\", function (option, props) {\n    var dot;\n    if (/*#__PURE__*/isValidElement(option)) {\n      dot = /*#__PURE__*/cloneElement(option, props);\n    } else if (_isFunction(option)) {\n      dot = option(props);\n    } else {\n      dot = /*#__PURE__*/React.createElement(Dot, props);\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-active-dot\",\n      key: props.key\n    }, dot);\n  }), _class;\n};", "map": {"version": 3, "names": ["_every", "_find", "_isFunction", "_throttle", "_sortBy", "_get", "_range", "_isNil", "_isBoolean", "_isArray", "_excluded", "_excluded2", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_slicedToArray", "arr", "i", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "_i", "_s", "_e", "_x", "_r", "_arr", "_n", "_d", "call", "next", "Object", "done", "push", "value", "length", "err", "Array", "isArray", "_extends", "assign", "bind", "target", "arguments", "source", "key", "hasOwnProperty", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "keys", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "from", "test", "iter", "len", "arr2", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "Component", "cloneElement", "isValidElement", "createElement", "classNames", "getTicks", "Surface", "Layer", "<PERSON><PERSON><PERSON>", "Legend", "Curve", "Cross", "Sector", "Dot", "isInRectangle", "Rectangle", "findAllByType", "findChildByType", "getDisplayName", "parseChildIndex", "validateWidthHeight", "isChildrenEqual", "renderByOrder", "getReactEventByType", "filterProps", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "Brush", "getOffset", "calculateChartCoordinate", "getAnyElementOfObject", "hasDuplicate", "uniqueId", "isNumber", "findEntryInArray", "calculateActiveTickIndex", "getMainColorOfGraphicItem", "getBarSizeList", "getBarPosition", "appendOffsetOfLegend", "getLegendProps", "combineEventHandlers", "getTicksOfAxis", "getCoordinatesOfGrid", "getStackedDataOfItem", "parseErrorBarsOfAxis", "getBandSizeOfAxis", "getStackGroupsByAxisId", "isCategoricalAxis", "getDomainOfItemsWithSameAxis", "getDomainOfStackGroups", "getDomainOfDataByKey", "parseSpecifiedDomain", "parseDomainOfCategoryAxis", "getTooltipItem", "detectReferenceElementsDomain", "inRangeOfSector", "polarToCartesian", "shallowEqual", "eventCenter", "SYNC_EVENT", "adaptEventHandlers", "ORIENT_MAP", "xAxis", "yAxis", "originCoordinate", "x", "y", "isFinit", "isFinite", "defer", "requestAnimationFrame", "setImmediate", "setTimeout", "deferClear", "cancelAnimationFrame", "clearImmediate", "clearTimeout", "calculateTooltipPos", "rangeObj", "layout", "angle", "radius", "getActiveCoordinate", "tooltipTicks", "activeIndex", "entry", "find", "tick", "index", "coordinate", "_angle", "_radius", "cx", "cy", "getDisplayedData", "data", "_ref", "item", "graphicalItems", "dataStartIndex", "dataEndIndex", "itemsData", "reduce", "child", "itemData", "concat", "isDomainSpecifiedByUser", "domain", "allowDataOverflow", "axisType", "domainStart", "domainEnd", "getDefaultDomainByAxisType", "getTooltipContent", "state", "chartData", "activeLabel", "tooltipAxis", "displayedData", "hide", "payload", "dataKey", "allowDuplicatedCategory", "entries", "getTooltipData", "rangeData", "chartX", "chartY", "pos", "ticks", "orderedTooltipTicks", "axis", "activePayload", "activeCoordinate", "activeTooltipIndex", "getAxisMapByAxes", "_ref2", "axes", "axisIdKey", "stackGroups", "children", "stackOffset", "isCategorical", "axisMap", "_child$props$domain2", "_child$props", "type", "scale", "includeHidden", "axisId", "duplicateDomain", "categoricalDomain", "defaultDomain", "_child$props$domain", "childDomain", "duplicate", "finalDomain", "errorBarsDomain", "hasStack", "axisDomain", "isDomain<PERSON><PERSON><PERSON>", "every", "originalDomain", "getAxisMapByItems", "_ref3", "Axis", "defaultProps", "orientation", "getAxisMap", "_ref4", "_ref4$axisType", "AxisComp", "tooltipTicksGenerator", "tooltipAxisBandSize", "createDefaultState", "_brushItem$props", "_brushItem$props2", "defaultShowTooltip", "brushItem", "startIndex", "endIndex", "isTooltipActive", "hasGraphicalBarItem", "some", "getAxisNameByLayout", "numericAxisName", "cateAxisName", "calculateOffset", "_ref5", "prevLegendBBox", "_ref5$xAxisMap", "xAxisMap", "_ref5$yAxisMap", "yAxisMap", "width", "height", "margin", "legendItem", "offsetH", "id", "mirror", "left", "right", "offsetV", "top", "bottom", "offset", "brushBottom", "generateCategoricalChart", "_ref6", "_class", "chartName", "GraphicalChild", "_ref6$defaultTooltipE", "defaultTooltipEventType", "_ref6$validateTooltip", "validateTooltipEventTypes", "axisComponents", "<PERSON><PERSON><PERSON><PERSON>", "formatAxisMap", "getFormatItems", "currentState", "updateId", "barSize", "barGap", "barCategoryGap", "globalMaxBarSize", "maxBarSize", "_getAxisNameByLayout", "<PERSON><PERSON><PERSON>", "sizeList", "formattedItems", "_item$props", "childMaxBarSize", "numericAxisId", "cateAxisId", "axisObj", "_objectSpread6", "cateAxis", "cateTicks", "stackedData", "itemIsBar", "bandSize", "barPosition", "_ref7", "_getBandSizeOfAxis", "barBandSize", "map", "position", "composedFn", "getComposedData", "_objectSpread7", "childIndex", "updateStateOfAxisMapsOffsetAndStackGroups", "_ref8", "prevState", "reverseStackOrder", "_getAxisNameByLayout2", "legend<PERSON><PERSON>", "replace", "cateAxisMap", "ticksObj", "formattedGraphicalItems", "_Component", "CategoricalChartWrapper", "_super", "_props", "_this", "deferId", "box", "_this$state", "setState", "cId", "chartId", "syncId", "uniqueChartId", "clearDeferId", "applySyncEvent", "_ref9", "triggerSyncEvent", "onMouseEnter", "mouse", "getMouseInfo", "_nextState", "onMouseMove", "nextState", "el", "activeItem", "tooltipPayload", "tooltipPosition", "persist", "triggeredAfterMouseMove", "onMouseLeave", "cancelThrottledTriggerAfterMouseMove", "eventName", "event", "changedTouches", "handler", "onClick", "_nextState2", "onMouseDown", "_nextState3", "onMouseUp", "_nextState4", "handleMouseMove", "handleMouseDown", "handleMouseUp", "_ref10", "viewBox", "_ref11", "element", "_this$state2", "tooltipEventType", "getTooltipEventType", "cursor", "restProps", "cursor<PERSON>omp", "getCursorRectangle", "_this$getCursorPoints", "getCursorPoints", "startAngle", "endAngle", "innerRadius", "outerRadius", "points", "cursorProps", "stroke", "pointerEvents", "payloadIndex", "className", "displayName", "axisOption", "xAxisId", "renderAxis", "yAxisId", "_this$state3", "_this$props", "yAxisWithFiniteDomain", "chartWidth", "chartHeight", "verticalCoordinatesGenerator", "horizontalCoordinatesGenerator", "_element$props", "radialLines", "polarAngles", "polarRadius", "_this$state4", "radiusAxisMap", "angleAxisMap", "radiusAxis", "angleAxis", "_this$props2", "legend<PERSON><PERSON><PERSON>", "otherProps", "ref", "legend", "legendInstance", "onBBoxUpdate", "handleLegendBBoxUpdate", "tooltipItem", "_this$state5", "active", "label", "_this$props3", "_this$state6", "onChange", "handleBrushChange", "_assertThisInitialize", "clipPathId", "_this$state7", "_element$props2", "_ref12", "activePoint", "basePoint", "isRange", "_item$item$props", "activeDot", "dotProps", "r", "fill", "strokeWidth", "renderActiveDot", "filterFormatItem", "_this$state8", "_item$props2", "baseLine", "_item$item$props2", "hasActive", "itemEvents", "trigger", "handleItemMouseEnter", "onCLick", "handleItemMouseLeave", "graphicalItem", "findWithPayload", "<PERSON><PERSON><PERSON>", "renderActivePoints", "throttle<PERSON><PERSON><PERSON>", "componentDidMount", "addListener", "componentDidUpdate", "prevProps", "removeListener", "componentWillUnmount", "cancel", "shared", "eventType", "container", "containerOffset", "inRange", "_this$state9", "xScale", "yScale", "xValue", "invert", "yValue", "toolTipData", "_this$state10", "halfSize", "_this$state11", "x1", "y1", "x2", "y2", "innerPoint", "outerPoint", "_cx", "_cy", "startPoint", "endPoint", "isInRange", "_this$state12", "parseEventsOfWrapper", "tooltipEvents", "handleClick", "handleMouseEnter", "handleMouseLeave", "onTouchMove", "handleTouchMove", "onTouchStart", "handleTouchStart", "onTouchEnd", "handleTouchEnd", "outerEvents", "handleOuterEvent", "on", "handleReceiveSyncEvent", "setMaxListeners", "_maxListeners", "emit", "_this$props4", "syncMethod", "_this$state13", "validateChartX", "Math", "min", "validateChartY", "axisOptions", "_this$props5", "ticksGenerator", "axesTicksGenerator", "renderClipPath", "_this$state$offset", "getXScales", "_ref13", "_ref14", "axisProps", "getYScales", "_ref15", "_ref16", "getXScaleByAxisId", "_this$state$xAxisMap", "_this$state$xAxisMap$", "getYScaleByAxisId", "_this$state$yAxisMap", "_this$state$yAxisMap$", "getItemByXY", "chartXY", "itemDisplayName", "activeBarItem", "_activeBarItem", "render", "_this2", "_this$props6", "style", "compact", "title", "desc", "others", "attrs", "Cartesian<PERSON><PERSON>", "renderGrid", "once", "ReferenceArea", "renderReferenceElement", "ReferenceLine", "ReferenceDot", "XAxis", "renderXAxis", "YA<PERSON>s", "renderYAxis", "renderBrush", "Bar", "renderGraphicChild", "Line", "Area", "Radar", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pie", "Funnel", "renderCursor", "PolarGrid", "renderPolarGrid", "PolarAngleAxis", "renderPolarAxis", "PolarRadiusAxis", "Customized", "renderCustomized", "events", "node", "role", "renderLegend", "renderTooltip", "nextProps", "defaultState", "prevData", "prevWidth", "prevHeight", "prevLayout", "prevStackOffset", "<PERSON>v<PERSON><PERSON><PERSON>", "prev<PERSON><PERSON><PERSON><PERSON>", "_defaultState", "keepFromPrevState", "updatesToState", "newState", "hasGlobalData", "newUpdateId", "option", "dot"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/chart/generateCategoricalChart.js"], "sourcesContent": ["import _every from \"lodash/every\";\nimport _find from \"lodash/find\";\nimport _isFunction from \"lodash/isFunction\";\nimport _throttle from \"lodash/throttle\";\nimport _sortBy from \"lodash/sortBy\";\nimport _get from \"lodash/get\";\nimport _range from \"lodash/range\";\nimport _isNil from \"lodash/isNil\";\nimport _isBoolean from \"lodash/isBoolean\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"item\"],\n  _excluded2 = [\"children\", \"className\", \"width\", \"height\", \"style\", \"compact\", \"title\", \"desc\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport React, { Component, cloneElement, isValidElement, createElement } from 'react';\nimport classNames from 'classnames';\nimport { getTicks } from '../cartesian/getTicks';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Legend } from '../component/Legend';\nimport { Curve } from '../shape/Curve';\nimport { Cross } from '../shape/Cross';\nimport { Sector } from '../shape/Sector';\nimport { Dot } from '../shape/Dot';\nimport { isInRectangle, Rectangle } from '../shape/Rectangle';\nimport { findAllByType, findChildByType, getDisplayName, parseChildIndex, validateWidthHeight, isChildrenEqual, renderByOrder, getReactEventByType, filterProps } from '../util/ReactUtils';\nimport { CartesianAxis } from '../cartesian/CartesianAxis';\nimport { Brush } from '../cartesian/Brush';\nimport { getOffset, calculateChartCoordinate } from '../util/DOMUtils';\nimport { getAnyElementOfObject, hasDuplicate, uniqueId, isNumber, findEntryInArray } from '../util/DataUtils';\nimport { calculateActiveTickIndex, getMainColorOfGraphicItem, getBarSizeList, getBarPosition, appendOffsetOfLegend, getLegendProps, combineEventHandlers, getTicksOfAxis, getCoordinatesOfGrid, getStackedDataOfItem, parseErrorBarsOfAxis, getBandSizeOfAxis, getStackGroupsByAxisId, isCategoricalAxis, getDomainOfItemsWithSameAxis, getDomainOfStackGroups, getDomainOfDataByKey, parseSpecifiedDomain, parseDomainOfCategoryAxis, getTooltipItem } from '../util/ChartUtils';\nimport { detectReferenceElementsDomain } from '../util/DetectReferenceElementsDomain';\nimport { inRangeOfSector, polarToCartesian } from '../util/PolarUtils';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { eventCenter, SYNC_EVENT } from '../util/Events';\nimport { adaptEventHandlers } from '../util/types';\nvar ORIENT_MAP = {\n  xAxis: ['bottom', 'top'],\n  yAxis: ['left', 'right']\n};\nvar originCoordinate = {\n  x: 0,\n  y: 0\n};\n\n// use legacy isFinite only if there is a problem (aka IE)\n// eslint-disable-next-line no-restricted-globals\nvar isFinit = Number.isFinite ? Number.isFinite : isFinite;\nvar defer =\n// eslint-disable-next-line no-nested-ternary\ntypeof requestAnimationFrame === 'function' ? requestAnimationFrame : typeof setImmediate === 'function' ? setImmediate : setTimeout;\nvar deferClear =\n// eslint-disable-next-line no-nested-ternary\ntypeof cancelAnimationFrame === 'function' ? cancelAnimationFrame : typeof clearImmediate === 'function' ? clearImmediate : clearTimeout;\nvar calculateTooltipPos = function calculateTooltipPos(rangeObj, layout) {\n  if (layout === 'horizontal') {\n    return rangeObj.x;\n  }\n  if (layout === 'vertical') {\n    return rangeObj.y;\n  }\n  if (layout === 'centric') {\n    return rangeObj.angle;\n  }\n  return rangeObj.radius;\n};\nvar getActiveCoordinate = function getActiveCoordinate(layout, tooltipTicks, activeIndex, rangeObj) {\n  var entry = tooltipTicks.find(function (tick) {\n    return tick && tick.index === activeIndex;\n  });\n  if (entry) {\n    if (layout === 'horizontal') {\n      return {\n        x: entry.coordinate,\n        y: rangeObj.y\n      };\n    }\n    if (layout === 'vertical') {\n      return {\n        x: rangeObj.x,\n        y: entry.coordinate\n      };\n    }\n    if (layout === 'centric') {\n      var _angle = entry.coordinate;\n      var _radius = rangeObj.radius;\n      return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, _radius, _angle)), {}, {\n        angle: _angle,\n        radius: _radius\n      });\n    }\n    var radius = entry.coordinate;\n    var angle = rangeObj.angle;\n    return _objectSpread(_objectSpread(_objectSpread({}, rangeObj), polarToCartesian(rangeObj.cx, rangeObj.cy, radius, angle)), {}, {\n      angle: angle,\n      radius: radius\n    });\n  }\n  return originCoordinate;\n};\nvar getDisplayedData = function getDisplayedData(data, _ref, item) {\n  var graphicalItems = _ref.graphicalItems,\n    dataStartIndex = _ref.dataStartIndex,\n    dataEndIndex = _ref.dataEndIndex;\n  var itemsData = (graphicalItems || []).reduce(function (result, child) {\n    var itemData = child.props.data;\n    if (itemData && itemData.length) {\n      return [].concat(_toConsumableArray(result), _toConsumableArray(itemData));\n    }\n    return result;\n  }, []);\n  if (itemsData && itemsData.length > 0) {\n    return itemsData;\n  }\n  if (item && item.props && item.props.data && item.props.data.length > 0) {\n    return item.props.data;\n  }\n  if (data && data.length && isNumber(dataStartIndex) && isNumber(dataEndIndex)) {\n    return data.slice(dataStartIndex, dataEndIndex + 1);\n  }\n  return [];\n};\n\n/**\n * Takes a domain and user props to determine whether he provided the domain via props or if we need to calculate it.\n * @param   {AxisDomain}  domain              The potential domain from props\n * @param   {Boolean}     allowDataOverflow   from props\n * @param   {String}      axisType            from props\n * @returns {Boolean}                         `true` if domain is specified by user\n */\nfunction isDomainSpecifiedByUser(domain, allowDataOverflow, axisType) {\n  if (axisType === 'number' && allowDataOverflow === true && Array.isArray(domain)) {\n    var domainStart = domain === null || domain === void 0 ? void 0 : domain[0];\n    var domainEnd = domain === null || domain === void 0 ? void 0 : domain[1];\n\n    /*\n     * The `isNumber` check is needed because the user could also provide strings like \"dataMin\" via the domain props.\n     * In such case, we have to compute the domain from the data.\n     */\n    if (!!domainStart && !!domainEnd && isNumber(domainStart) && isNumber(domainEnd)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction getDefaultDomainByAxisType(axisType) {\n  return axisType === 'number' ? [0, 'auto'] : undefined;\n}\n\n/**\n * Get the content to be displayed in the tooltip\n * @param  {Object} state          Current state\n * @param  {Array}  chartData      The data defined in chart\n * @param  {Number} activeIndex    Active index of data\n * @param  {String} activeLabel    Active label of data\n * @return {Array}                 The content of tooltip\n */\nvar getTooltipContent = function getTooltipContent(state, chartData, activeIndex, activeLabel) {\n  var graphicalItems = state.graphicalItems,\n    tooltipAxis = state.tooltipAxis;\n  var displayedData = getDisplayedData(chartData, state);\n  if (activeIndex < 0 || !graphicalItems || !graphicalItems.length || activeIndex >= displayedData.length) {\n    return null;\n  }\n  // get data by activeIndex when the axis don't allow duplicated category\n  return graphicalItems.reduce(function (result, child) {\n    var hide = child.props.hide;\n    if (hide) {\n      return result;\n    }\n    var data = child.props.data;\n    var payload;\n    if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n      // graphic child has data props\n      var entries = data === undefined ? displayedData : data;\n      payload = findEntryInArray(entries, tooltipAxis.dataKey, activeLabel);\n    } else {\n      payload = data && data[activeIndex] || displayedData[activeIndex];\n    }\n    if (!payload) {\n      return result;\n    }\n    return [].concat(_toConsumableArray(result), [getTooltipItem(child, payload)]);\n  }, []);\n};\n\n/**\n * Returns tooltip data based on a mouse position (as a parameter or in state)\n * @param  {Object} state     current state\n * @param  {Array}  chartData the data defined in chart\n * @param  {String} layout     The layout type of chart\n * @param  {Object} rangeObj  { x, y } coordinates\n * @return {Object}           Tooltip data data\n */\nvar getTooltipData = function getTooltipData(state, chartData, layout, rangeObj) {\n  var rangeData = rangeObj || {\n    x: state.chartX,\n    y: state.chartY\n  };\n  var pos = calculateTooltipPos(rangeData, layout);\n  var ticks = state.orderedTooltipTicks,\n    axis = state.tooltipAxis,\n    tooltipTicks = state.tooltipTicks;\n  var activeIndex = calculateActiveTickIndex(pos, ticks, tooltipTicks, axis);\n  if (activeIndex >= 0 && tooltipTicks) {\n    var activeLabel = tooltipTicks[activeIndex] && tooltipTicks[activeIndex].value;\n    var activePayload = getTooltipContent(state, chartData, activeIndex, activeLabel);\n    var activeCoordinate = getActiveCoordinate(layout, ticks, activeIndex, rangeData);\n    return {\n      activeTooltipIndex: activeIndex,\n      activeLabel: activeLabel,\n      activePayload: activePayload,\n      activeCoordinate: activeCoordinate\n    };\n  }\n  return null;\n};\n\n/**\n * Get the configuration of axis by the options of axis instance\n * @param  {Object} props         Latest props\n * @param {Array}  axes           The instance of axes\n * @param  {Array} graphicalItems The instances of item\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}      Configuration\n */\nexport var getAxisMapByAxes = function getAxisMapByAxes(props, _ref2) {\n  var axes = _ref2.axes,\n    graphicalItems = _ref2.graphicalItems,\n    axisType = _ref2.axisType,\n    axisIdKey = _ref2.axisIdKey,\n    stackGroups = _ref2.stackGroups,\n    dataStartIndex = _ref2.dataStartIndex,\n    dataEndIndex = _ref2.dataEndIndex;\n  var layout = props.layout,\n    children = props.children,\n    stackOffset = props.stackOffset;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n\n  // Eliminate duplicated axes\n  var axisMap = axes.reduce(function (result, child) {\n    var _child$props$domain2;\n    var _child$props = child.props,\n      type = _child$props.type,\n      dataKey = _child$props.dataKey,\n      allowDataOverflow = _child$props.allowDataOverflow,\n      allowDuplicatedCategory = _child$props.allowDuplicatedCategory,\n      scale = _child$props.scale,\n      ticks = _child$props.ticks,\n      includeHidden = _child$props.includeHidden;\n    var axisId = child.props[axisIdKey];\n    if (result[axisId]) {\n      return result;\n    }\n    var displayedData = getDisplayedData(props.data, {\n      graphicalItems: graphicalItems.filter(function (item) {\n        return item.props[axisIdKey] === axisId;\n      }),\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n    var len = displayedData.length;\n    var domain, duplicateDomain, categoricalDomain;\n\n    /*\n     * This is a hack to short-circuit the domain creation here to enhance performance.\n     * Usually, the data is used to determine the domain, but when the user specifies\n     * a domain upfront (via props), there is no need to calculate the domain start and end,\n     * which is very expensive for a larger amount of data.\n     * The only thing that would prohibit short-circuiting is when the user doesn't allow data overflow,\n     * because the axis is supposed to ignore the specified domain that way.\n     */\n    if (isDomainSpecifiedByUser(child.props.domain, allowDataOverflow, type)) {\n      domain = parseSpecifiedDomain(child.props.domain, null, allowDataOverflow);\n      /* The chart can be categorical and have the domain specified in numbers\n       * we still need to calculate the categorical domain\n       * TODO: refactor this more\n       */\n      if (isCategorical && (type === 'number' || scale !== 'auto')) {\n        categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n      }\n    }\n\n    // if the domain is defaulted we need this for `originalDomain` as well\n    var defaultDomain = getDefaultDomainByAxisType(type);\n\n    // we didn't create the domain from user's props above, so we need to calculate it\n    if (!domain || domain.length === 0) {\n      var _child$props$domain;\n      var childDomain = (_child$props$domain = child.props.domain) !== null && _child$props$domain !== void 0 ? _child$props$domain : defaultDomain;\n      if (dataKey) {\n        // has dataKey in <Axis />\n        domain = getDomainOfDataByKey(displayedData, dataKey, type);\n        if (type === 'category' && isCategorical) {\n          // the field type is category data and this axis is categorical axis\n          var duplicate = hasDuplicate(domain);\n          if (allowDuplicatedCategory && duplicate) {\n            duplicateDomain = domain;\n            // When category axis has duplicated text, serial numbers are used to generate scale\n            domain = _range(0, len);\n          } else if (!allowDuplicatedCategory) {\n            // remove duplicated category\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          }\n        } else if (type === 'category') {\n          // the field type is category data and this axis is numerical axis\n          if (!allowDuplicatedCategory) {\n            domain = parseDomainOfCategoryAxis(childDomain, domain, child).reduce(function (finalDomain, entry) {\n              return finalDomain.indexOf(entry) >= 0 || entry === '' || _isNil(entry) ? finalDomain : [].concat(_toConsumableArray(finalDomain), [entry]);\n            }, []);\n          } else {\n            // eliminate undefined or null or empty string\n            domain = domain.filter(function (entry) {\n              return entry !== '' && !_isNil(entry);\n            });\n          }\n        } else if (type === 'number') {\n          // the field type is numerical\n          var errorBarsDomain = parseErrorBarsOfAxis(displayedData, graphicalItems.filter(function (item) {\n            return item.props[axisIdKey] === axisId && (includeHidden || !item.props.hide);\n          }), dataKey, axisType, layout);\n          if (errorBarsDomain) {\n            domain = errorBarsDomain;\n          }\n        }\n        if (isCategorical && (type === 'number' || scale !== 'auto')) {\n          categoricalDomain = getDomainOfDataByKey(displayedData, dataKey, 'category');\n        }\n      } else if (isCategorical) {\n        // the axis is a categorical axis\n        domain = _range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack && type === 'number') {\n        // when stackOffset is 'expand', the domain may be calculated as [0, 1.000000000002]\n        domain = stackOffset === 'expand' ? [0, 1] : getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n      } else {\n        domain = getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          return item.props[axisIdKey] === axisId && (includeHidden || !item.props.hide);\n        }), type, layout, true);\n      }\n      if (type === 'number') {\n        // To detect wether there is any reference lines whose props alwaysShow is true\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType, ticks);\n        if (childDomain) {\n          domain = parseSpecifiedDomain(childDomain, domain, allowDataOverflow);\n        }\n      } else if (type === 'category' && childDomain) {\n        var axisDomain = childDomain;\n        var isDomainValid = domain.every(function (entry) {\n          return axisDomain.indexOf(entry) >= 0;\n        });\n        if (isDomainValid) {\n          domain = axisDomain;\n        }\n      }\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({}, child.props), {}, {\n      axisType: axisType,\n      domain: domain,\n      categoricalDomain: categoricalDomain,\n      duplicateDomain: duplicateDomain,\n      originalDomain: (_child$props$domain2 = child.props.domain) !== null && _child$props$domain2 !== void 0 ? _child$props$domain2 : defaultDomain,\n      isCategorical: isCategorical,\n      layout: layout\n    })));\n  }, {});\n  return axisMap;\n};\n\n/**\n * Get the configuration of axis by the options of item,\n * this kind of axis does not display in chart\n * @param  {Object} props         Latest props\n * @param  {Array} graphicalItems The instances of item\n * @param  {ReactElement} Axis    Axis Component\n * @param  {String} axisType      The type of axis, xAxis - x-axis, yAxis - y-axis\n * @param  {String} axisIdKey     The unique id of an axis\n * @param  {Object} stackGroups   The items grouped by axisId and stackId\n * @param {Number} dataStartIndex The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex   The end index of the data series when a brush is applied\n * @return {Object}               Configuration\n */\nvar getAxisMapByItems = function getAxisMapByItems(props, _ref3) {\n  var graphicalItems = _ref3.graphicalItems,\n    Axis = _ref3.Axis,\n    axisType = _ref3.axisType,\n    axisIdKey = _ref3.axisIdKey,\n    stackGroups = _ref3.stackGroups,\n    dataStartIndex = _ref3.dataStartIndex,\n    dataEndIndex = _ref3.dataEndIndex;\n  var layout = props.layout,\n    children = props.children;\n  var displayedData = getDisplayedData(props.data, {\n    graphicalItems: graphicalItems,\n    dataStartIndex: dataStartIndex,\n    dataEndIndex: dataEndIndex\n  });\n  var len = displayedData.length;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var index = -1;\n\n  // The default type of x-axis is category axis,\n  // The default contents of x-axis is the serial numbers of data\n  // The default type of y-axis is number axis\n  // The default contents of y-axis is the domain of data\n  var axisMap = graphicalItems.reduce(function (result, child) {\n    var axisId = child.props[axisIdKey];\n    var originalDomain = getDefaultDomainByAxisType('number');\n    if (!result[axisId]) {\n      index++;\n      var domain;\n      if (isCategorical) {\n        domain = _range(0, len);\n      } else if (stackGroups && stackGroups[axisId] && stackGroups[axisId].hasStack) {\n        domain = getDomainOfStackGroups(stackGroups[axisId].stackGroups, dataStartIndex, dataEndIndex);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      } else {\n        domain = parseSpecifiedDomain(originalDomain, getDomainOfItemsWithSameAxis(displayedData, graphicalItems.filter(function (item) {\n          return item.props[axisIdKey] === axisId && !item.props.hide;\n        }), 'number', layout), Axis.defaultProps.allowDataOverflow);\n        domain = detectReferenceElementsDomain(children, domain, axisId, axisType);\n      }\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, _objectSpread(_objectSpread({\n        axisType: axisType\n      }, Axis.defaultProps), {}, {\n        hide: true,\n        orientation: _get(ORIENT_MAP, \"\".concat(axisType, \".\").concat(index % 2), null),\n        domain: domain,\n        originalDomain: originalDomain,\n        isCategorical: isCategorical,\n        layout: layout\n        // specify scale when no Axis\n        // scale: isCategorical ? 'band' : 'linear',\n      })));\n    }\n\n    return result;\n  }, {});\n  return axisMap;\n};\n\n/**\n * Get the configuration of all x-axis or y-axis\n * @param  {Object} props          Latest props\n * @param  {String} axisType       The type of axis\n * @param  {Array}  graphicalItems The instances of item\n * @param  {Object} stackGroups    The items grouped by axisId and stackId\n * @param {Number} dataStartIndex  The start index of the data series when a brush is applied\n * @param {Number} dataEndIndex    The end index of the data series when a brush is applied\n * @return {Object}          Configuration\n */\nvar getAxisMap = function getAxisMap(props, _ref4) {\n  var _ref4$axisType = _ref4.axisType,\n    axisType = _ref4$axisType === void 0 ? 'xAxis' : _ref4$axisType,\n    AxisComp = _ref4.AxisComp,\n    graphicalItems = _ref4.graphicalItems,\n    stackGroups = _ref4.stackGroups,\n    dataStartIndex = _ref4.dataStartIndex,\n    dataEndIndex = _ref4.dataEndIndex;\n  var children = props.children;\n  var axisIdKey = \"\".concat(axisType, \"Id\");\n  // Get all the instance of Axis\n  var axes = findAllByType(children, AxisComp);\n  var axisMap = {};\n  if (axes && axes.length) {\n    axisMap = getAxisMapByAxes(props, {\n      axes: axes,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  } else if (graphicalItems && graphicalItems.length) {\n    axisMap = getAxisMapByItems(props, {\n      Axis: AxisComp,\n      graphicalItems: graphicalItems,\n      axisType: axisType,\n      axisIdKey: axisIdKey,\n      stackGroups: stackGroups,\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex\n    });\n  }\n  return axisMap;\n};\nvar tooltipTicksGenerator = function tooltipTicksGenerator(axisMap) {\n  var axis = getAnyElementOfObject(axisMap);\n  var tooltipTicks = getTicksOfAxis(axis, false, true);\n  return {\n    tooltipTicks: tooltipTicks,\n    orderedTooltipTicks: _sortBy(tooltipTicks, function (o) {\n      return o.coordinate;\n    }),\n    tooltipAxis: axis,\n    tooltipAxisBandSize: getBandSizeOfAxis(axis, tooltipTicks)\n  };\n};\n\n/**\n * Returns default, reset state for the categorical chart.\n * @param {Object} props Props object to use when creating the default state\n * @return {Object} Whole new state\n */\nvar createDefaultState = function createDefaultState(props) {\n  var _brushItem$props, _brushItem$props2;\n  var children = props.children,\n    defaultShowTooltip = props.defaultShowTooltip;\n  var brushItem = findChildByType(children, Brush);\n  var startIndex = brushItem && brushItem.props && brushItem.props.startIndex || 0;\n  var endIndex = (brushItem === null || brushItem === void 0 ? void 0 : (_brushItem$props = brushItem.props) === null || _brushItem$props === void 0 ? void 0 : _brushItem$props.endIndex) !== undefined ? brushItem === null || brushItem === void 0 ? void 0 : (_brushItem$props2 = brushItem.props) === null || _brushItem$props2 === void 0 ? void 0 : _brushItem$props2.endIndex : props.data && props.data.length - 1 || 0;\n  return {\n    chartX: 0,\n    chartY: 0,\n    dataStartIndex: startIndex,\n    dataEndIndex: endIndex,\n    activeTooltipIndex: -1,\n    isTooltipActive: !_isNil(defaultShowTooltip) ? defaultShowTooltip : false\n  };\n};\nvar hasGraphicalBarItem = function hasGraphicalBarItem(graphicalItems) {\n  if (!graphicalItems || !graphicalItems.length) {\n    return false;\n  }\n  return graphicalItems.some(function (item) {\n    var name = getDisplayName(item && item.type);\n    return name && name.indexOf('Bar') >= 0;\n  });\n};\nvar getAxisNameByLayout = function getAxisNameByLayout(layout) {\n  if (layout === 'horizontal') {\n    return {\n      numericAxisName: 'yAxis',\n      cateAxisName: 'xAxis'\n    };\n  }\n  if (layout === 'vertical') {\n    return {\n      numericAxisName: 'xAxis',\n      cateAxisName: 'yAxis'\n    };\n  }\n  if (layout === 'centric') {\n    return {\n      numericAxisName: 'radiusAxis',\n      cateAxisName: 'angleAxis'\n    };\n  }\n  return {\n    numericAxisName: 'angleAxis',\n    cateAxisName: 'radiusAxis'\n  };\n};\n\n/**\n * Calculate the offset of main part in the svg element\n * @param  {Object} props          Latest props\n * graphicalItems The instances of item\n * xAxisMap       The configuration of x-axis\n * yAxisMap       The configuration of y-axis\n * @param  {Object} prevLegendBBox          the boundary box of legend\n * @return {Object} The offset of main part in the svg element\n */\nvar calculateOffset = function calculateOffset(_ref5, prevLegendBBox) {\n  var props = _ref5.props,\n    graphicalItems = _ref5.graphicalItems,\n    _ref5$xAxisMap = _ref5.xAxisMap,\n    xAxisMap = _ref5$xAxisMap === void 0 ? {} : _ref5$xAxisMap,\n    _ref5$yAxisMap = _ref5.yAxisMap,\n    yAxisMap = _ref5$yAxisMap === void 0 ? {} : _ref5$yAxisMap;\n  var width = props.width,\n    height = props.height,\n    children = props.children;\n  var margin = props.margin || {};\n  var brushItem = findChildByType(children, Brush);\n  var legendItem = findChildByType(children, Legend);\n  var offsetH = Object.keys(yAxisMap).reduce(function (result, id) {\n    var entry = yAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, result[orientation] + entry.width));\n    }\n    return result;\n  }, {\n    left: margin.left || 0,\n    right: margin.right || 0\n  });\n  var offsetV = Object.keys(xAxisMap).reduce(function (result, id) {\n    var entry = xAxisMap[id];\n    var orientation = entry.orientation;\n    if (!entry.mirror && !entry.hide) {\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, orientation, _get(result, \"\".concat(orientation)) + entry.height));\n    }\n    return result;\n  }, {\n    top: margin.top || 0,\n    bottom: margin.bottom || 0\n  });\n  var offset = _objectSpread(_objectSpread({}, offsetV), offsetH);\n  var brushBottom = offset.bottom;\n  if (brushItem) {\n    offset.bottom += brushItem.props.height || Brush.defaultProps.height;\n  }\n  if (legendItem && prevLegendBBox) {\n    offset = appendOffsetOfLegend(offset, graphicalItems, props, prevLegendBBox);\n  }\n  return _objectSpread(_objectSpread({\n    brushBottom: brushBottom\n  }, offset), {}, {\n    width: width - offset.left - offset.right,\n    height: height - offset.top - offset.bottom\n  });\n};\nexport var generateCategoricalChart = function generateCategoricalChart(_ref6) {\n  var _class;\n  var chartName = _ref6.chartName,\n    GraphicalChild = _ref6.GraphicalChild,\n    _ref6$defaultTooltipE = _ref6.defaultTooltipEventType,\n    defaultTooltipEventType = _ref6$defaultTooltipE === void 0 ? 'axis' : _ref6$defaultTooltipE,\n    _ref6$validateTooltip = _ref6.validateTooltipEventTypes,\n    validateTooltipEventTypes = _ref6$validateTooltip === void 0 ? ['axis'] : _ref6$validateTooltip,\n    axisComponents = _ref6.axisComponents,\n    legendContent = _ref6.legendContent,\n    formatAxisMap = _ref6.formatAxisMap,\n    defaultProps = _ref6.defaultProps;\n  var getFormatItems = function getFormatItems(props, currentState) {\n    var graphicalItems = currentState.graphicalItems,\n      stackGroups = currentState.stackGroups,\n      offset = currentState.offset,\n      updateId = currentState.updateId,\n      dataStartIndex = currentState.dataStartIndex,\n      dataEndIndex = currentState.dataEndIndex;\n    var barSize = props.barSize,\n      layout = props.layout,\n      barGap = props.barGap,\n      barCategoryGap = props.barCategoryGap,\n      globalMaxBarSize = props.maxBarSize;\n    var _getAxisNameByLayout = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout.numericAxisName,\n      cateAxisName = _getAxisNameByLayout.cateAxisName;\n    var hasBar = hasGraphicalBarItem(graphicalItems);\n    var sizeList = hasBar && getBarSizeList({\n      barSize: barSize,\n      stackGroups: stackGroups\n    });\n    var formattedItems = [];\n    graphicalItems.forEach(function (item, index) {\n      var displayedData = getDisplayedData(props.data, {\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }, item);\n      var _item$props = item.props,\n        dataKey = _item$props.dataKey,\n        childMaxBarSize = _item$props.maxBarSize;\n      var numericAxisId = item.props[\"\".concat(numericAxisName, \"Id\")];\n      var cateAxisId = item.props[\"\".concat(cateAxisName, \"Id\")];\n      var axisObj = axisComponents.reduce(function (result, entry) {\n        var _objectSpread6;\n        var axisMap = currentState[\"\".concat(entry.axisType, \"Map\")];\n        var id = item.props[\"\".concat(entry.axisType, \"Id\")];\n        var axis = axisMap && axisMap[id];\n        return _objectSpread(_objectSpread({}, result), {}, (_objectSpread6 = {}, _defineProperty(_objectSpread6, entry.axisType, axis), _defineProperty(_objectSpread6, \"\".concat(entry.axisType, \"Ticks\"), getTicksOfAxis(axis)), _objectSpread6));\n      }, {});\n      var cateAxis = axisObj[cateAxisName];\n      var cateTicks = axisObj[\"\".concat(cateAxisName, \"Ticks\")];\n      var stackedData = stackGroups && stackGroups[numericAxisId] && stackGroups[numericAxisId].hasStack && getStackedDataOfItem(item, stackGroups[numericAxisId].stackGroups);\n      var itemIsBar = getDisplayName(item.type).indexOf('Bar') >= 0;\n      var bandSize = getBandSizeOfAxis(cateAxis, cateTicks);\n      var barPosition = [];\n      if (itemIsBar) {\n        var _ref7, _getBandSizeOfAxis;\n        // 如果是bar，计算bar的位置\n        var maxBarSize = _isNil(childMaxBarSize) ? globalMaxBarSize : childMaxBarSize;\n        var barBandSize = (_ref7 = (_getBandSizeOfAxis = getBandSizeOfAxis(cateAxis, cateTicks, true)) !== null && _getBandSizeOfAxis !== void 0 ? _getBandSizeOfAxis : maxBarSize) !== null && _ref7 !== void 0 ? _ref7 : 0;\n        barPosition = getBarPosition({\n          barGap: barGap,\n          barCategoryGap: barCategoryGap,\n          bandSize: barBandSize !== bandSize ? barBandSize : bandSize,\n          sizeList: sizeList[cateAxisId],\n          maxBarSize: maxBarSize\n        });\n        if (barBandSize !== bandSize) {\n          barPosition = barPosition.map(function (pos) {\n            return _objectSpread(_objectSpread({}, pos), {}, {\n              position: _objectSpread(_objectSpread({}, pos.position), {}, {\n                offset: pos.position.offset - barBandSize / 2\n              })\n            });\n          });\n        }\n      }\n      var composedFn = item && item.type && item.type.getComposedData;\n      if (composedFn) {\n        var _objectSpread7;\n        formattedItems.push({\n          props: _objectSpread(_objectSpread({}, composedFn(_objectSpread(_objectSpread({}, axisObj), {}, {\n            displayedData: displayedData,\n            props: props,\n            dataKey: dataKey,\n            item: item,\n            bandSize: bandSize,\n            barPosition: barPosition,\n            offset: offset,\n            stackedData: stackedData,\n            layout: layout,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }))), {}, (_objectSpread7 = {\n            key: item.key || \"item-\".concat(index)\n          }, _defineProperty(_objectSpread7, numericAxisName, axisObj[numericAxisName]), _defineProperty(_objectSpread7, cateAxisName, axisObj[cateAxisName]), _defineProperty(_objectSpread7, \"animationId\", updateId), _objectSpread7)),\n          childIndex: parseChildIndex(item, props.children),\n          item: item\n        });\n      }\n    });\n    return formattedItems;\n  };\n\n  /**\n   * The AxisMaps are expensive to render on large data sets\n   * so provide the ability to store them in state and only update them when necessary\n   * they are dependent upon the start and end index of\n   * the brush so it's important that this method is called _after_\n   * the state is updated with any new start/end indices\n   *\n   * @param {Object} props          The props object to be used for updating the axismaps\n   * dataStartIndex: The start index of the data series when a brush is applied\n   * dataEndIndex: The end index of the data series when a brush is applied\n   * updateId: The update id\n   * @param {Object} prevState      Prev state\n   * @return {Object} state New state to set\n   */\n  var updateStateOfAxisMapsOffsetAndStackGroups = function updateStateOfAxisMapsOffsetAndStackGroups(_ref8, prevState) {\n    var props = _ref8.props,\n      dataStartIndex = _ref8.dataStartIndex,\n      dataEndIndex = _ref8.dataEndIndex,\n      updateId = _ref8.updateId;\n    if (!validateWidthHeight({\n      props: props\n    })) {\n      return null;\n    }\n    var children = props.children,\n      layout = props.layout,\n      stackOffset = props.stackOffset,\n      data = props.data,\n      reverseStackOrder = props.reverseStackOrder;\n    var _getAxisNameByLayout2 = getAxisNameByLayout(layout),\n      numericAxisName = _getAxisNameByLayout2.numericAxisName,\n      cateAxisName = _getAxisNameByLayout2.cateAxisName;\n    var graphicalItems = findAllByType(children, GraphicalChild);\n    var stackGroups = getStackGroupsByAxisId(data, graphicalItems, \"\".concat(numericAxisName, \"Id\"), \"\".concat(cateAxisName, \"Id\"), stackOffset, reverseStackOrder);\n    var axisObj = axisComponents.reduce(function (result, entry) {\n      var name = \"\".concat(entry.axisType, \"Map\");\n      return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, name, getAxisMap(props, _objectSpread(_objectSpread({}, entry), {}, {\n        graphicalItems: graphicalItems,\n        stackGroups: entry.axisType === numericAxisName && stackGroups,\n        dataStartIndex: dataStartIndex,\n        dataEndIndex: dataEndIndex\n      }))));\n    }, {});\n    var offset = calculateOffset(_objectSpread(_objectSpread({}, axisObj), {}, {\n      props: props,\n      graphicalItems: graphicalItems\n    }), prevState === null || prevState === void 0 ? void 0 : prevState.legendBBox);\n    Object.keys(axisObj).forEach(function (key) {\n      axisObj[key] = formatAxisMap(props, axisObj[key], offset, key.replace('Map', ''), chartName);\n    });\n    var cateAxisMap = axisObj[\"\".concat(cateAxisName, \"Map\")];\n    var ticksObj = tooltipTicksGenerator(cateAxisMap);\n    var formattedGraphicalItems = getFormatItems(props, _objectSpread(_objectSpread({}, axisObj), {}, {\n      dataStartIndex: dataStartIndex,\n      dataEndIndex: dataEndIndex,\n      updateId: updateId,\n      graphicalItems: graphicalItems,\n      stackGroups: stackGroups,\n      offset: offset\n    }));\n    return _objectSpread(_objectSpread({\n      formattedGraphicalItems: formattedGraphicalItems,\n      graphicalItems: graphicalItems,\n      offset: offset,\n      stackGroups: stackGroups\n    }, ticksObj), axisObj);\n  };\n  return _class = /*#__PURE__*/function (_Component) {\n    _inherits(CategoricalChartWrapper, _Component);\n    var _super = _createSuper(CategoricalChartWrapper);\n    // todo join specific chart propTypes\n\n    function CategoricalChartWrapper(_props) {\n      var _this;\n      _classCallCheck(this, CategoricalChartWrapper);\n      _this = _super.call(this, _props);\n      _defineProperty(_assertThisInitialized(_this), \"clearDeferId\", function () {\n        if (!_isNil(_this.deferId) && deferClear) {\n          deferClear(_this.deferId);\n        }\n        _this.deferId = null;\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleLegendBBoxUpdate\", function (box) {\n        if (box) {\n          var _this$state = _this.state,\n            dataStartIndex = _this$state.dataStartIndex,\n            dataEndIndex = _this$state.dataEndIndex,\n            updateId = _this$state.updateId;\n          _this.setState(_objectSpread({\n            legendBBox: box\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: _this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, _objectSpread(_objectSpread({}, _this.state), {}, {\n            legendBBox: box\n          }))));\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleReceiveSyncEvent\", function (cId, chartId, data) {\n        var syncId = _this.props.syncId;\n        if (syncId === cId && chartId !== _this.uniqueChartId) {\n          _this.clearDeferId();\n          _this.deferId = defer && defer(_this.applySyncEvent.bind(_assertThisInitialized(_this), data));\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleBrushChange\", function (_ref9) {\n        var startIndex = _ref9.startIndex,\n          endIndex = _ref9.endIndex;\n        // Only trigger changes if the extents of the brush have actually changed\n        if (startIndex !== _this.state.dataStartIndex || endIndex !== _this.state.dataEndIndex) {\n          var updateId = _this.state.updateId;\n          _this.setState(function () {\n            return _objectSpread({\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex\n            }, updateStateOfAxisMapsOffsetAndStackGroups({\n              props: _this.props,\n              dataStartIndex: startIndex,\n              dataEndIndex: endIndex,\n              updateId: updateId\n            }, _this.state));\n          });\n          _this.triggerSyncEvent({\n            dataStartIndex: startIndex,\n            dataEndIndex: endIndex\n          });\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseEnter\", function (e) {\n        var onMouseEnter = _this.props.onMouseEnter;\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState);\n          _this.triggerSyncEvent(_nextState);\n          if (_isFunction(onMouseEnter)) {\n            onMouseEnter(_nextState, e);\n          }\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"triggeredAfterMouseMove\", function (e) {\n        var onMouseMove = _this.props.onMouseMove;\n        var mouse = _this.getMouseInfo(e);\n        var nextState = mouse ? _objectSpread(_objectSpread({}, mouse), {}, {\n          isTooltipActive: true\n        }) : {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        if (_isFunction(onMouseMove)) {\n          onMouseMove(nextState, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleItemMouseEnter\", function (el) {\n        _this.setState(function () {\n          return {\n            isTooltipActive: true,\n            activeItem: el,\n            activePayload: el.tooltipPayload,\n            activeCoordinate: el.tooltipPosition || {\n              x: el.cx,\n              y: el.cy\n            }\n          };\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleItemMouseLeave\", function () {\n        _this.setState(function () {\n          return {\n            isTooltipActive: false\n          };\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseMove\", function (e) {\n        if (e && _isFunction(e.persist)) {\n          e.persist();\n        }\n        _this.triggeredAfterMouseMove(e);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseLeave\", function (e) {\n        var onMouseLeave = _this.props.onMouseLeave;\n        var nextState = {\n          isTooltipActive: false\n        };\n        _this.setState(nextState);\n        _this.triggerSyncEvent(nextState);\n        if (_isFunction(onMouseLeave)) {\n          onMouseLeave(nextState, e);\n        }\n        _this.cancelThrottledTriggerAfterMouseMove();\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleOuterEvent\", function (e) {\n        var eventName = getReactEventByType(e);\n        var event = _get(_this.props, \"\".concat(eventName));\n        if (eventName && _isFunction(event)) {\n          var mouse;\n          if (/.*touch.*/i.test(eventName)) {\n            mouse = _this.getMouseInfo(e.changedTouches[0]);\n          } else {\n            mouse = _this.getMouseInfo(e);\n          }\n          var handler = event;\n\n          // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n          // @ts-ignore\n          handler(mouse, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleClick\", function (e) {\n        var onClick = _this.props.onClick;\n        var mouse = _this.getMouseInfo(e);\n        if (mouse) {\n          var _nextState2 = _objectSpread(_objectSpread({}, mouse), {}, {\n            isTooltipActive: true\n          });\n          _this.setState(_nextState2);\n          _this.triggerSyncEvent(_nextState2);\n          if (_isFunction(onClick)) {\n            onClick(_nextState2, e);\n          }\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseDown\", function (e) {\n        var onMouseDown = _this.props.onMouseDown;\n        if (_isFunction(onMouseDown)) {\n          var _nextState3 = _this.getMouseInfo(e);\n          onMouseDown(_nextState3, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleMouseUp\", function (e) {\n        var onMouseUp = _this.props.onMouseUp;\n        if (_isFunction(onMouseUp)) {\n          var _nextState4 = _this.getMouseInfo(e);\n          onMouseUp(_nextState4, e);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchMove\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseMove(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchStart\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseDown(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"handleTouchEnd\", function (e) {\n        if (e.changedTouches != null && e.changedTouches.length > 0) {\n          _this.handleMouseUp(e.changedTouches[0]);\n        }\n      });\n      _defineProperty(_assertThisInitialized(_this), \"verticalCoordinatesGenerator\", function (_ref10) {\n        var xAxis = _ref10.xAxis,\n          width = _ref10.width,\n          height = _ref10.height,\n          offset = _ref10.offset;\n        return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n          ticks: getTicksOfAxis(xAxis, true),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }\n        })), offset.left, offset.left + offset.width);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"horizontalCoordinatesGenerator\", function (_ref11) {\n        var yAxis = _ref11.yAxis,\n          width = _ref11.width,\n          height = _ref11.height,\n          offset = _ref11.offset;\n        return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n          ticks: getTicksOfAxis(yAxis, true),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }\n        })), offset.top, offset.top + offset.height);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"axesTicksGenerator\", function (axis) {\n        return getTicksOfAxis(axis, true);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderCursor\", function (element) {\n        var _this$state2 = _this.state,\n          isTooltipActive = _this$state2.isTooltipActive,\n          activeCoordinate = _this$state2.activeCoordinate,\n          activePayload = _this$state2.activePayload,\n          offset = _this$state2.offset,\n          activeTooltipIndex = _this$state2.activeTooltipIndex;\n        var tooltipEventType = _this.getTooltipEventType();\n        if (!element || !element.props.cursor || !isTooltipActive || !activeCoordinate || chartName !== 'ScatterChart' && tooltipEventType !== 'axis') {\n          return null;\n        }\n        var layout = _this.props.layout;\n        var restProps;\n        var cursorComp = Curve;\n        if (chartName === 'ScatterChart') {\n          restProps = activeCoordinate;\n          cursorComp = Cross;\n        } else if (chartName === 'BarChart') {\n          restProps = _this.getCursorRectangle();\n          cursorComp = Rectangle;\n        } else if (layout === 'radial') {\n          var _this$getCursorPoints = _this.getCursorPoints(),\n            cx = _this$getCursorPoints.cx,\n            cy = _this$getCursorPoints.cy,\n            radius = _this$getCursorPoints.radius,\n            startAngle = _this$getCursorPoints.startAngle,\n            endAngle = _this$getCursorPoints.endAngle;\n          restProps = {\n            cx: cx,\n            cy: cy,\n            startAngle: startAngle,\n            endAngle: endAngle,\n            innerRadius: radius,\n            outerRadius: radius\n          };\n          cursorComp = Sector;\n        } else {\n          restProps = {\n            points: _this.getCursorPoints()\n          };\n          cursorComp = Curve;\n        }\n        var key = element.key || '_recharts-cursor';\n        var cursorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n          stroke: '#ccc',\n          pointerEvents: 'none'\n        }, offset), restProps), filterProps(element.props.cursor)), {}, {\n          payload: activePayload,\n          payloadIndex: activeTooltipIndex,\n          key: key,\n          className: 'recharts-tooltip-cursor'\n        });\n        return /*#__PURE__*/isValidElement(element.props.cursor) ? /*#__PURE__*/cloneElement(element.props.cursor, cursorProps) : /*#__PURE__*/createElement(cursorComp, cursorProps);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderPolarAxis\", function (element, displayName, index) {\n        var axisType = _get(element, 'type.axisType');\n        var axisMap = _get(_this.state, \"\".concat(axisType, \"Map\"));\n        var axisOption = axisMap && axisMap[element.props[\"\".concat(axisType, \"Id\")]];\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, axisOption), {}, {\n          className: axisType,\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          ticks: getTicksOfAxis(axisOption, true)\n        }));\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderXAxis\", function (element, displayName, index) {\n        var xAxisMap = _this.state.xAxisMap;\n        var axisObj = xAxisMap[element.props.xAxisId];\n        return _this.renderAxis(axisObj, element, displayName, index);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderYAxis\", function (element, displayName, index) {\n        var yAxisMap = _this.state.yAxisMap;\n        var axisObj = yAxisMap[element.props.yAxisId];\n        return _this.renderAxis(axisObj, element, displayName, index);\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderGrid\", function (element) {\n        var _this$state3 = _this.state,\n          xAxisMap = _this$state3.xAxisMap,\n          yAxisMap = _this$state3.yAxisMap,\n          offset = _this$state3.offset;\n        var _this$props = _this.props,\n          width = _this$props.width,\n          height = _this$props.height;\n        var xAxis = getAnyElementOfObject(xAxisMap);\n        var yAxisWithFiniteDomain = _find(yAxisMap, function (axis) {\n          return _every(axis.domain, isFinit);\n        });\n        var yAxis = yAxisWithFiniteDomain || getAnyElementOfObject(yAxisMap);\n        var props = element.props || {};\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || 'grid',\n          x: isNumber(props.x) ? props.x : offset.left,\n          y: isNumber(props.y) ? props.y : offset.top,\n          width: isNumber(props.width) ? props.width : offset.width,\n          height: isNumber(props.height) ? props.height : offset.height,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          offset: offset,\n          chartWidth: width,\n          chartHeight: height,\n          verticalCoordinatesGenerator: props.verticalCoordinatesGenerator || _this.verticalCoordinatesGenerator,\n          horizontalCoordinatesGenerator: props.horizontalCoordinatesGenerator || _this.horizontalCoordinatesGenerator\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderPolarGrid\", function (element) {\n        var _element$props = element.props,\n          radialLines = _element$props.radialLines,\n          polarAngles = _element$props.polarAngles,\n          polarRadius = _element$props.polarRadius;\n        var _this$state4 = _this.state,\n          radiusAxisMap = _this$state4.radiusAxisMap,\n          angleAxisMap = _this$state4.angleAxisMap;\n        var radiusAxis = getAnyElementOfObject(radiusAxisMap);\n        var angleAxis = getAnyElementOfObject(angleAxisMap);\n        var cx = angleAxis.cx,\n          cy = angleAxis.cy,\n          innerRadius = angleAxis.innerRadius,\n          outerRadius = angleAxis.outerRadius;\n        return /*#__PURE__*/cloneElement(element, {\n          polarAngles: _isArray(polarAngles) ? polarAngles : getTicksOfAxis(angleAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          polarRadius: _isArray(polarRadius) ? polarRadius : getTicksOfAxis(radiusAxis, true).map(function (entry) {\n            return entry.coordinate;\n          }),\n          cx: cx,\n          cy: cy,\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          key: element.key || 'polar-grid',\n          radialLines: radialLines\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderLegend\", function () {\n        var formattedGraphicalItems = _this.state.formattedGraphicalItems;\n        var _this$props2 = _this.props,\n          children = _this$props2.children,\n          width = _this$props2.width,\n          height = _this$props2.height;\n        var margin = _this.props.margin || {};\n        var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n        var props = getLegendProps({\n          children: children,\n          formattedGraphicalItems: formattedGraphicalItems,\n          legendWidth: legendWidth,\n          legendContent: legendContent\n        });\n        if (!props) {\n          return null;\n        }\n        var item = props.item,\n          otherProps = _objectWithoutProperties(props, _excluded);\n        return /*#__PURE__*/cloneElement(item, _objectSpread(_objectSpread({}, otherProps), {}, {\n          chartWidth: width,\n          chartHeight: height,\n          margin: margin,\n          ref: function ref(legend) {\n            _this.legendInstance = legend;\n          },\n          onBBoxUpdate: _this.handleLegendBBoxUpdate\n        }));\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderTooltip\", function () {\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        if (!tooltipItem) {\n          return null;\n        }\n        var _this$state5 = _this.state,\n          isTooltipActive = _this$state5.isTooltipActive,\n          activeCoordinate = _this$state5.activeCoordinate,\n          activePayload = _this$state5.activePayload,\n          activeLabel = _this$state5.activeLabel,\n          offset = _this$state5.offset;\n        return /*#__PURE__*/cloneElement(tooltipItem, {\n          viewBox: _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          }),\n          active: isTooltipActive,\n          label: activeLabel,\n          payload: isTooltipActive ? activePayload : [],\n          coordinate: activeCoordinate\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderBrush\", function (element) {\n        var _this$props3 = _this.props,\n          margin = _this$props3.margin,\n          data = _this$props3.data;\n        var _this$state6 = _this.state,\n          offset = _this$state6.offset,\n          dataStartIndex = _this$state6.dataStartIndex,\n          dataEndIndex = _this$state6.dataEndIndex,\n          updateId = _this$state6.updateId;\n\n        // TODO: update brush when children update\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || '_recharts-brush',\n          onChange: combineEventHandlers(_this.handleBrushChange, null, element.props.onChange),\n          data: data,\n          x: isNumber(element.props.x) ? element.props.x : offset.left,\n          y: isNumber(element.props.y) ? element.props.y : offset.top + offset.height + offset.brushBottom - (margin.bottom || 0),\n          width: isNumber(element.props.width) ? element.props.width : offset.width,\n          startIndex: dataStartIndex,\n          endIndex: dataEndIndex,\n          updateId: \"brush-\".concat(updateId)\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderReferenceElement\", function (element, displayName, index) {\n        if (!element) {\n          return null;\n        }\n        var _assertThisInitialize = _assertThisInitialized(_this),\n          clipPathId = _assertThisInitialize.clipPathId;\n        var _this$state7 = _this.state,\n          xAxisMap = _this$state7.xAxisMap,\n          yAxisMap = _this$state7.yAxisMap,\n          offset = _this$state7.offset;\n        var _element$props2 = element.props,\n          xAxisId = _element$props2.xAxisId,\n          yAxisId = _element$props2.yAxisId;\n        return /*#__PURE__*/cloneElement(element, {\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          xAxis: xAxisMap[xAxisId],\n          yAxis: yAxisMap[yAxisId],\n          viewBox: {\n            x: offset.left,\n            y: offset.top,\n            width: offset.width,\n            height: offset.height\n          },\n          clipPathId: clipPathId\n        });\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderActivePoints\", function (_ref12) {\n        var item = _ref12.item,\n          activePoint = _ref12.activePoint,\n          basePoint = _ref12.basePoint,\n          childIndex = _ref12.childIndex,\n          isRange = _ref12.isRange;\n        var result = [];\n        var key = item.props.key;\n        var _item$item$props = item.item.props,\n          activeDot = _item$item$props.activeDot,\n          dataKey = _item$item$props.dataKey;\n        var dotProps = _objectSpread(_objectSpread({\n          index: childIndex,\n          dataKey: dataKey,\n          cx: activePoint.x,\n          cy: activePoint.y,\n          r: 4,\n          fill: getMainColorOfGraphicItem(item.item),\n          strokeWidth: 2,\n          stroke: '#fff',\n          payload: activePoint.payload,\n          value: activePoint.value,\n          key: \"\".concat(key, \"-activePoint-\").concat(childIndex)\n        }, filterProps(activeDot)), adaptEventHandlers(activeDot));\n        result.push(CategoricalChartWrapper.renderActiveDot(activeDot, dotProps));\n        if (basePoint) {\n          result.push(CategoricalChartWrapper.renderActiveDot(activeDot, _objectSpread(_objectSpread({}, dotProps), {}, {\n            cx: basePoint.x,\n            cy: basePoint.y,\n            key: \"\".concat(key, \"-basePoint-\").concat(childIndex)\n          })));\n        } else if (isRange) {\n          result.push(null);\n        }\n        return result;\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderGraphicChild\", function (element, displayName, index) {\n        var item = _this.filterFormatItem(element, displayName, index);\n        if (!item) {\n          return null;\n        }\n        var tooltipEventType = _this.getTooltipEventType();\n        var _this$state8 = _this.state,\n          isTooltipActive = _this$state8.isTooltipActive,\n          tooltipAxis = _this$state8.tooltipAxis,\n          activeTooltipIndex = _this$state8.activeTooltipIndex,\n          activeLabel = _this$state8.activeLabel;\n        var children = _this.props.children;\n        var tooltipItem = findChildByType(children, Tooltip);\n        var _item$props2 = item.props,\n          points = _item$props2.points,\n          isRange = _item$props2.isRange,\n          baseLine = _item$props2.baseLine;\n        var _item$item$props2 = item.item.props,\n          activeDot = _item$item$props2.activeDot,\n          hide = _item$item$props2.hide;\n        var hasActive = !hide && isTooltipActive && tooltipItem && activeDot && activeTooltipIndex >= 0;\n        var itemEvents = {};\n        if (tooltipEventType !== 'axis' && tooltipItem && tooltipItem.props.trigger === 'click') {\n          itemEvents = {\n            onClick: combineEventHandlers(_this.handleItemMouseEnter, null, element.props.onCLick)\n          };\n        } else if (tooltipEventType !== 'axis') {\n          itemEvents = {\n            onMouseLeave: combineEventHandlers(_this.handleItemMouseLeave, null, element.props.onMouseLeave),\n            onMouseEnter: combineEventHandlers(_this.handleItemMouseEnter, null, element.props.onMouseEnter)\n          };\n        }\n        var graphicalItem = /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({}, item.props), itemEvents));\n        function findWithPayload(entry) {\n          // TODO needs to verify dataKey is Function\n          return typeof tooltipAxis.dataKey === 'function' ? tooltipAxis.dataKey(entry.payload) : null;\n        }\n        if (hasActive) {\n          var activePoint, basePoint;\n          if (tooltipAxis.dataKey && !tooltipAxis.allowDuplicatedCategory) {\n            // number transform to string\n            var specifiedKey = typeof tooltipAxis.dataKey === 'function' ? findWithPayload : 'payload.'.concat(tooltipAxis.dataKey.toString());\n            activePoint = findEntryInArray(points, specifiedKey, activeLabel);\n            basePoint = isRange && baseLine && findEntryInArray(baseLine, specifiedKey, activeLabel);\n          } else {\n            activePoint = points[activeTooltipIndex];\n            basePoint = isRange && baseLine && baseLine[activeTooltipIndex];\n          }\n          if (!_isNil(activePoint)) {\n            return [graphicalItem].concat(_toConsumableArray(_this.renderActivePoints({\n              item: item,\n              activePoint: activePoint,\n              basePoint: basePoint,\n              childIndex: activeTooltipIndex,\n              isRange: isRange\n            })));\n          }\n        }\n        if (isRange) {\n          return [graphicalItem, null, null];\n        }\n        return [graphicalItem, null];\n      });\n      _defineProperty(_assertThisInitialized(_this), \"renderCustomized\", function (element, displayName, index) {\n        return /*#__PURE__*/cloneElement(element, _objectSpread(_objectSpread({\n          key: \"recharts-customized-\".concat(index)\n        }, _this.props), _this.state));\n      });\n      _this.uniqueChartId = _isNil(_props.id) ? uniqueId('recharts') : _props.id;\n      _this.clipPathId = \"\".concat(_this.uniqueChartId, \"-clip\");\n      if (_props.throttleDelay) {\n        _this.triggeredAfterMouseMove = _throttle(_this.triggeredAfterMouseMove, _props.throttleDelay);\n      }\n      _this.state = {};\n      return _this;\n    }\n    _createClass(CategoricalChartWrapper, [{\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if (!_isNil(this.props.syncId)) {\n          this.addListener();\n        }\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        // add syncId\n        if (_isNil(prevProps.syncId) && !_isNil(this.props.syncId)) {\n          this.addListener();\n        }\n        // remove syncId\n        if (!_isNil(prevProps.syncId) && _isNil(this.props.syncId)) {\n          this.removeListener();\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.clearDeferId();\n        if (!_isNil(this.props.syncId)) {\n          this.removeListener();\n        }\n        this.cancelThrottledTriggerAfterMouseMove();\n      }\n    }, {\n      key: \"cancelThrottledTriggerAfterMouseMove\",\n      value: function cancelThrottledTriggerAfterMouseMove() {\n        if (typeof this.triggeredAfterMouseMove.cancel === 'function') {\n          this.triggeredAfterMouseMove.cancel();\n        }\n      }\n    }, {\n      key: \"getTooltipEventType\",\n      value: function getTooltipEventType() {\n        var tooltipItem = findChildByType(this.props.children, Tooltip);\n        if (tooltipItem && _isBoolean(tooltipItem.props.shared)) {\n          var eventType = tooltipItem.props.shared ? 'axis' : 'item';\n          return validateTooltipEventTypes.indexOf(eventType) >= 0 ? eventType : defaultTooltipEventType;\n        }\n        return defaultTooltipEventType;\n      }\n\n      /**\n       * Get the information of mouse in chart, return null when the mouse is not in the chart\n       * @param  {Object} event    The event object\n       * @return {Object}          Mouse data\n       */\n    }, {\n      key: \"getMouseInfo\",\n      value: function getMouseInfo(event) {\n        if (!this.container) {\n          return null;\n        }\n        var containerOffset = getOffset(this.container);\n        var e = calculateChartCoordinate(event, containerOffset);\n        var rangeObj = this.inRange(e.chartX, e.chartY);\n        if (!rangeObj) {\n          return null;\n        }\n        var _this$state9 = this.state,\n          xAxisMap = _this$state9.xAxisMap,\n          yAxisMap = _this$state9.yAxisMap;\n        var tooltipEventType = this.getTooltipEventType();\n        if (tooltipEventType !== 'axis' && xAxisMap && yAxisMap) {\n          var xScale = getAnyElementOfObject(xAxisMap).scale;\n          var yScale = getAnyElementOfObject(yAxisMap).scale;\n          var xValue = xScale && xScale.invert ? xScale.invert(e.chartX) : null;\n          var yValue = yScale && yScale.invert ? yScale.invert(e.chartY) : null;\n          return _objectSpread(_objectSpread({}, e), {}, {\n            xValue: xValue,\n            yValue: yValue\n          });\n        }\n        var toolTipData = getTooltipData(this.state, this.props.data, this.props.layout, rangeObj);\n        if (toolTipData) {\n          return _objectSpread(_objectSpread({}, e), toolTipData);\n        }\n        return null;\n      }\n    }, {\n      key: \"getCursorRectangle\",\n      value: function getCursorRectangle() {\n        var layout = this.props.layout;\n        var _this$state10 = this.state,\n          activeCoordinate = _this$state10.activeCoordinate,\n          offset = _this$state10.offset,\n          tooltipAxisBandSize = _this$state10.tooltipAxisBandSize;\n        var halfSize = tooltipAxisBandSize / 2;\n        return {\n          stroke: 'none',\n          fill: '#ccc',\n          x: layout === 'horizontal' ? activeCoordinate.x - halfSize : offset.left + 0.5,\n          y: layout === 'horizontal' ? offset.top + 0.5 : activeCoordinate.y - halfSize,\n          width: layout === 'horizontal' ? tooltipAxisBandSize : offset.width - 1,\n          height: layout === 'horizontal' ? offset.height - 1 : tooltipAxisBandSize\n        };\n      }\n    }, {\n      key: \"getCursorPoints\",\n      value: function getCursorPoints() {\n        var layout = this.props.layout;\n        var _this$state11 = this.state,\n          activeCoordinate = _this$state11.activeCoordinate,\n          offset = _this$state11.offset;\n        var x1, y1, x2, y2;\n        if (layout === 'horizontal') {\n          x1 = activeCoordinate.x;\n          x2 = x1;\n          y1 = offset.top;\n          y2 = offset.top + offset.height;\n        } else if (layout === 'vertical') {\n          y1 = activeCoordinate.y;\n          y2 = y1;\n          x1 = offset.left;\n          x2 = offset.left + offset.width;\n        } else if (!_isNil(activeCoordinate.cx) || !_isNil(activeCoordinate.cy)) {\n          if (layout === 'centric') {\n            var cx = activeCoordinate.cx,\n              cy = activeCoordinate.cy,\n              innerRadius = activeCoordinate.innerRadius,\n              outerRadius = activeCoordinate.outerRadius,\n              angle = activeCoordinate.angle;\n            var innerPoint = polarToCartesian(cx, cy, innerRadius, angle);\n            var outerPoint = polarToCartesian(cx, cy, outerRadius, angle);\n            x1 = innerPoint.x;\n            y1 = innerPoint.y;\n            x2 = outerPoint.x;\n            y2 = outerPoint.y;\n          } else {\n            var _cx = activeCoordinate.cx,\n              _cy = activeCoordinate.cy,\n              radius = activeCoordinate.radius,\n              startAngle = activeCoordinate.startAngle,\n              endAngle = activeCoordinate.endAngle;\n            var startPoint = polarToCartesian(_cx, _cy, radius, startAngle);\n            var endPoint = polarToCartesian(_cx, _cy, radius, endAngle);\n            return {\n              points: [startPoint, endPoint],\n              cx: _cx,\n              cy: _cy,\n              radius: radius,\n              startAngle: startAngle,\n              endAngle: endAngle\n            };\n          }\n        }\n        return [{\n          x: x1,\n          y: y1\n        }, {\n          x: x2,\n          y: y2\n        }];\n      }\n    }, {\n      key: \"inRange\",\n      value: function inRange(x, y) {\n        var layout = this.props.layout;\n        if (layout === 'horizontal' || layout === 'vertical') {\n          var offset = this.state.offset;\n          var isInRange = x >= offset.left && x <= offset.left + offset.width && y >= offset.top && y <= offset.top + offset.height;\n          return isInRange ? {\n            x: x,\n            y: y\n          } : null;\n        }\n        var _this$state12 = this.state,\n          angleAxisMap = _this$state12.angleAxisMap,\n          radiusAxisMap = _this$state12.radiusAxisMap;\n        if (angleAxisMap && radiusAxisMap) {\n          var angleAxis = getAnyElementOfObject(angleAxisMap);\n          return inRangeOfSector({\n            x: x,\n            y: y\n          }, angleAxis);\n        }\n        return null;\n      }\n    }, {\n      key: \"parseEventsOfWrapper\",\n      value: function parseEventsOfWrapper() {\n        var children = this.props.children;\n        var tooltipEventType = this.getTooltipEventType();\n        var tooltipItem = findChildByType(children, Tooltip);\n        var tooltipEvents = {};\n        if (tooltipItem && tooltipEventType === 'axis') {\n          if (tooltipItem.props.trigger === 'click') {\n            tooltipEvents = {\n              onClick: this.handleClick\n            };\n          } else {\n            tooltipEvents = {\n              onMouseEnter: this.handleMouseEnter,\n              onMouseMove: this.handleMouseMove,\n              onMouseLeave: this.handleMouseLeave,\n              onTouchMove: this.handleTouchMove,\n              onTouchStart: this.handleTouchStart,\n              onTouchEnd: this.handleTouchEnd\n            };\n          }\n        }\n        var outerEvents = adaptEventHandlers(this.props, this.handleOuterEvent);\n        return _objectSpread(_objectSpread({}, outerEvents), tooltipEvents);\n      }\n\n      /* eslint-disable  no-underscore-dangle */\n    }, {\n      key: \"addListener\",\n      value: function addListener() {\n        eventCenter.on(SYNC_EVENT, this.handleReceiveSyncEvent);\n        if (eventCenter.setMaxListeners && eventCenter._maxListeners) {\n          eventCenter.setMaxListeners(eventCenter._maxListeners + 1);\n        }\n      }\n    }, {\n      key: \"removeListener\",\n      value: function removeListener() {\n        eventCenter.removeListener(SYNC_EVENT, this.handleReceiveSyncEvent);\n        if (eventCenter.setMaxListeners && eventCenter._maxListeners) {\n          eventCenter.setMaxListeners(eventCenter._maxListeners - 1);\n        }\n      }\n    }, {\n      key: \"triggerSyncEvent\",\n      value: function triggerSyncEvent(data) {\n        var syncId = this.props.syncId;\n        if (!_isNil(syncId)) {\n          eventCenter.emit(SYNC_EVENT, syncId, this.uniqueChartId, data);\n        }\n      }\n    }, {\n      key: \"applySyncEvent\",\n      value: function applySyncEvent(data) {\n        var _this$props4 = this.props,\n          layout = _this$props4.layout,\n          syncMethod = _this$props4.syncMethod;\n        var updateId = this.state.updateId;\n        var dataStartIndex = data.dataStartIndex,\n          dataEndIndex = data.dataEndIndex;\n        if (!_isNil(data.dataStartIndex) || !_isNil(data.dataEndIndex)) {\n          this.setState(_objectSpread({\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex\n          }, updateStateOfAxisMapsOffsetAndStackGroups({\n            props: this.props,\n            dataStartIndex: dataStartIndex,\n            dataEndIndex: dataEndIndex,\n            updateId: updateId\n          }, this.state)));\n        } else if (!_isNil(data.activeTooltipIndex)) {\n          var chartX = data.chartX,\n            chartY = data.chartY;\n          var activeTooltipIndex = data.activeTooltipIndex;\n          var _this$state13 = this.state,\n            offset = _this$state13.offset,\n            tooltipTicks = _this$state13.tooltipTicks;\n          if (!offset) {\n            return;\n          }\n          if (typeof syncMethod === 'function') {\n            // Call a callback function. If there is an application specific algorithm\n            activeTooltipIndex = syncMethod(tooltipTicks, data);\n          } else if (syncMethod === 'value') {\n            // Set activeTooltipIndex to the index with the same value as data.activeLabel\n            // For loop instead of findIndex because the latter is very slow in some browsers\n            activeTooltipIndex = -1; // in case we cannot find the element\n            for (var i = 0; i < tooltipTicks.length; i++) {\n              if (tooltipTicks[i].value === data.activeLabel) {\n                activeTooltipIndex = i;\n                break;\n              }\n            }\n          }\n          var viewBox = _objectSpread(_objectSpread({}, offset), {}, {\n            x: offset.left,\n            y: offset.top\n          });\n          // When a categorical chart is combined with another chart, the value of chartX\n          // and chartY may beyond the boundaries.\n          var validateChartX = Math.min(chartX, viewBox.x + viewBox.width);\n          var validateChartY = Math.min(chartY, viewBox.y + viewBox.height);\n          var activeLabel = tooltipTicks[activeTooltipIndex] && tooltipTicks[activeTooltipIndex].value;\n          var activePayload = getTooltipContent(this.state, this.props.data, activeTooltipIndex);\n          var activeCoordinate = tooltipTicks[activeTooltipIndex] ? {\n            x: layout === 'horizontal' ? tooltipTicks[activeTooltipIndex].coordinate : validateChartX,\n            y: layout === 'horizontal' ? validateChartY : tooltipTicks[activeTooltipIndex].coordinate\n          } : originCoordinate;\n          this.setState(_objectSpread(_objectSpread({}, data), {}, {\n            activeLabel: activeLabel,\n            activeCoordinate: activeCoordinate,\n            activePayload: activePayload,\n            activeTooltipIndex: activeTooltipIndex\n          }));\n        } else {\n          this.setState(data);\n        }\n      }\n    }, {\n      key: \"filterFormatItem\",\n      value: function filterFormatItem(item, displayName, childIndex) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n          var entry = formattedGraphicalItems[i];\n          if (entry.item === item || entry.props.key === item.key || displayName === getDisplayName(entry.item.type) && childIndex === entry.childIndex) {\n            return entry;\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"renderAxis\",\n      value:\n      /**\n       * Draw axis\n       * @param {Object} axisOptions The options of axis\n       * @param {Object} element      The axis element\n       * @param {String} displayName  The display name of axis\n       * @param {Number} index        The index of element\n       * @return {ReactElement}       The instance of x-axes\n       */\n      function renderAxis(axisOptions, element, displayName, index) {\n        var _this$props5 = this.props,\n          width = _this$props5.width,\n          height = _this$props5.height;\n        return /*#__PURE__*/React.createElement(CartesianAxis, _extends({}, axisOptions, {\n          className: \"recharts-\".concat(axisOptions.axisType, \" \").concat(axisOptions.axisType),\n          key: element.key || \"\".concat(displayName, \"-\").concat(index),\n          viewBox: {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          },\n          ticksGenerator: this.axesTicksGenerator\n        }));\n      }\n\n      /**\n       * Draw grid\n       * @param  {ReactElement} element the grid item\n       * @return {ReactElement} The instance of grid\n       */\n    }, {\n      key: \"renderClipPath\",\n      value: function renderClipPath() {\n        var clipPathId = this.clipPathId;\n        var _this$state$offset = this.state.offset,\n          left = _this$state$offset.left,\n          top = _this$state$offset.top,\n          height = _this$state$offset.height,\n          width = _this$state$offset.width;\n        return /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: clipPathId\n        }, /*#__PURE__*/React.createElement(\"rect\", {\n          x: left,\n          y: top,\n          height: height,\n          width: width\n        })));\n      }\n    }, {\n      key: \"getXScales\",\n      value: function getXScales() {\n        var xAxisMap = this.state.xAxisMap;\n        return xAxisMap ? Object.entries(xAxisMap).reduce(function (res, _ref13) {\n          var _ref14 = _slicedToArray(_ref13, 2),\n            axisId = _ref14[0],\n            axisProps = _ref14[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getYScales\",\n      value: function getYScales() {\n        var yAxisMap = this.state.yAxisMap;\n        return yAxisMap ? Object.entries(yAxisMap).reduce(function (res, _ref15) {\n          var _ref16 = _slicedToArray(_ref15, 2),\n            axisId = _ref16[0],\n            axisProps = _ref16[1];\n          return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, axisId, axisProps.scale));\n        }, {}) : null;\n      }\n    }, {\n      key: \"getXScaleByAxisId\",\n      value: function getXScaleByAxisId(axisId) {\n        var _this$state$xAxisMap, _this$state$xAxisMap$;\n        return (_this$state$xAxisMap = this.state.xAxisMap) === null || _this$state$xAxisMap === void 0 ? void 0 : (_this$state$xAxisMap$ = _this$state$xAxisMap[axisId]) === null || _this$state$xAxisMap$ === void 0 ? void 0 : _this$state$xAxisMap$.scale;\n      }\n    }, {\n      key: \"getYScaleByAxisId\",\n      value: function getYScaleByAxisId(axisId) {\n        var _this$state$yAxisMap, _this$state$yAxisMap$;\n        return (_this$state$yAxisMap = this.state.yAxisMap) === null || _this$state$yAxisMap === void 0 ? void 0 : (_this$state$yAxisMap$ = _this$state$yAxisMap[axisId]) === null || _this$state$yAxisMap$ === void 0 ? void 0 : _this$state$yAxisMap$.scale;\n      }\n    }, {\n      key: \"getItemByXY\",\n      value: function getItemByXY(chartXY) {\n        var formattedGraphicalItems = this.state.formattedGraphicalItems;\n        if (formattedGraphicalItems && formattedGraphicalItems.length) {\n          for (var i = 0, len = formattedGraphicalItems.length; i < len; i++) {\n            var graphicalItem = formattedGraphicalItems[i];\n            var props = graphicalItem.props,\n              item = graphicalItem.item;\n            var itemDisplayName = getDisplayName(item.type);\n            if (itemDisplayName === 'Bar') {\n              var activeBarItem = (props.data || []).find(function (entry) {\n                return isInRectangle(chartXY, entry);\n              });\n              if (activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: activeBarItem\n                };\n              }\n            } else if (itemDisplayName === 'RadialBar') {\n              var _activeBarItem = (props.data || []).find(function (entry) {\n                return inRangeOfSector(chartXY, entry);\n              });\n              if (_activeBarItem) {\n                return {\n                  graphicalItem: graphicalItem,\n                  payload: _activeBarItem\n                };\n              }\n            }\n          }\n        }\n        return null;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        if (!validateWidthHeight(this)) {\n          return null;\n        }\n        var _this$props6 = this.props,\n          children = _this$props6.children,\n          className = _this$props6.className,\n          width = _this$props6.width,\n          height = _this$props6.height,\n          style = _this$props6.style,\n          compact = _this$props6.compact,\n          title = _this$props6.title,\n          desc = _this$props6.desc,\n          others = _objectWithoutProperties(_this$props6, _excluded2);\n        var attrs = filterProps(others);\n        var map = {\n          CartesianGrid: {\n            handler: this.renderGrid,\n            once: true\n          },\n          ReferenceArea: {\n            handler: this.renderReferenceElement\n          },\n          ReferenceLine: {\n            handler: this.renderReferenceElement\n          },\n          ReferenceDot: {\n            handler: this.renderReferenceElement\n          },\n          XAxis: {\n            handler: this.renderXAxis\n          },\n          YAxis: {\n            handler: this.renderYAxis\n          },\n          Brush: {\n            handler: this.renderBrush,\n            once: true\n          },\n          Bar: {\n            handler: this.renderGraphicChild\n          },\n          Line: {\n            handler: this.renderGraphicChild\n          },\n          Area: {\n            handler: this.renderGraphicChild\n          },\n          Radar: {\n            handler: this.renderGraphicChild\n          },\n          RadialBar: {\n            handler: this.renderGraphicChild\n          },\n          Scatter: {\n            handler: this.renderGraphicChild\n          },\n          Pie: {\n            handler: this.renderGraphicChild\n          },\n          Funnel: {\n            handler: this.renderGraphicChild\n          },\n          Tooltip: {\n            handler: this.renderCursor,\n            once: true\n          },\n          PolarGrid: {\n            handler: this.renderPolarGrid,\n            once: true\n          },\n          PolarAngleAxis: {\n            handler: this.renderPolarAxis\n          },\n          PolarRadiusAxis: {\n            handler: this.renderPolarAxis\n          },\n          Customized: {\n            handler: this.renderCustomized\n          }\n        };\n\n        // The \"compact\" mode is mainly used as the panorama within Brush\n        if (compact) {\n          return /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n            width: width,\n            height: height,\n            title: title,\n            desc: desc\n          }), this.renderClipPath(), renderByOrder(children, map));\n        }\n        var events = this.parseEventsOfWrapper();\n        return /*#__PURE__*/React.createElement(\"div\", _extends({\n          className: classNames('recharts-wrapper', className),\n          style: _objectSpread({\n            position: 'relative',\n            cursor: 'default',\n            width: width,\n            height: height\n          }, style)\n        }, events, {\n          ref: function ref(node) {\n            _this2.container = node;\n          },\n          role: \"region\"\n        }), /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n          width: width,\n          height: height,\n          title: title,\n          desc: desc\n        }), this.renderClipPath(), renderByOrder(children, map)), this.renderLegend(), this.renderTooltip());\n      }\n    }]);\n    return CategoricalChartWrapper;\n  }(Component), _defineProperty(_class, \"displayName\", chartName), _defineProperty(_class, \"defaultProps\", _objectSpread({\n    layout: 'horizontal',\n    stackOffset: 'none',\n    barCategoryGap: '10%',\n    barGap: 4,\n    margin: {\n      top: 5,\n      right: 5,\n      bottom: 5,\n      left: 5\n    },\n    reverseStackOrder: false,\n    syncMethod: 'index'\n  }, defaultProps)), _defineProperty(_class, \"getDerivedStateFromProps\", function (nextProps, prevState) {\n    var data = nextProps.data,\n      children = nextProps.children,\n      width = nextProps.width,\n      height = nextProps.height,\n      layout = nextProps.layout,\n      stackOffset = nextProps.stackOffset,\n      margin = nextProps.margin;\n    if (_isNil(prevState.updateId)) {\n      var defaultState = createDefaultState(nextProps);\n      return _objectSpread(_objectSpread(_objectSpread({}, defaultState), {}, {\n        updateId: 0\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, defaultState), {}, {\n        updateId: 0\n      }), prevState)), {}, {\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || layout !== prevState.prevLayout || stackOffset !== prevState.prevStackOffset || !shallowEqual(margin, prevState.prevMargin)) {\n      var _defaultState = createDefaultState(nextProps);\n\n      // Fixes https://github.com/recharts/recharts/issues/2143\n      var keepFromPrevState = {\n        // (chartX, chartY) are (0,0) in default state, but we want to keep the last mouse position to avoid\n        // any flickering\n        chartX: prevState.chartX,\n        chartY: prevState.chartY,\n        // The tooltip should stay active when it was active in the previous render. If this is not\n        // the case, the tooltip disappears and immediately re-appears, causing a flickering effect\n        isTooltipActive: prevState.isTooltipActive\n      };\n      var updatesToState = _objectSpread(_objectSpread({}, getTooltipData(prevState, data, layout)), {}, {\n        updateId: prevState.updateId + 1\n      });\n      var newState = _objectSpread(_objectSpread(_objectSpread({}, _defaultState), keepFromPrevState), updatesToState);\n      return _objectSpread(_objectSpread(_objectSpread({}, newState), updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread({\n        props: nextProps\n      }, newState), prevState)), {}, {\n        prevData: data,\n        prevWidth: width,\n        prevHeight: height,\n        prevLayout: layout,\n        prevStackOffset: stackOffset,\n        prevMargin: margin,\n        prevChildren: children\n      });\n    }\n    if (!isChildrenEqual(children, prevState.prevChildren)) {\n      // update configuration in children\n      var hasGlobalData = !_isNil(data);\n      var newUpdateId = hasGlobalData ? prevState.updateId : prevState.updateId + 1;\n      return _objectSpread(_objectSpread({\n        updateId: newUpdateId\n      }, updateStateOfAxisMapsOffsetAndStackGroups(_objectSpread(_objectSpread({\n        props: nextProps\n      }, prevState), {}, {\n        updateId: newUpdateId\n      }), prevState)), {}, {\n        prevChildren: children\n      });\n    }\n    return null;\n  }), _defineProperty(_class, \"renderActiveDot\", function (option, props) {\n    var dot;\n    if ( /*#__PURE__*/isValidElement(option)) {\n      dot = /*#__PURE__*/cloneElement(option, props);\n    } else if (_isFunction(option)) {\n      dot = option(props);\n    } else {\n      dot = /*#__PURE__*/React.createElement(Dot, props);\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-active-dot\",\n      key: props.key\n    }, dot);\n  }), _class;\n};"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;EACtBC,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;AAChG,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,cAAcA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAAE,OAAOC,eAAe,CAACF,GAAG,CAAC,IAAIG,qBAAqB,CAACH,GAAG,EAAEC,CAAC,CAAC,IAAIG,2BAA2B,CAACJ,GAAG,EAAEC,CAAC,CAAC,IAAII,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASH,qBAAqBA,CAACH,GAAG,EAAEC,CAAC,EAAE;EAAE,IAAIM,EAAE,GAAG,IAAI,IAAIP,GAAG,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOL,MAAM,IAAIK,GAAG,CAACL,MAAM,CAACC,QAAQ,CAAC,IAAII,GAAG,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIO,EAAE,EAAE;IAAE,IAAIC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,IAAI,GAAG,EAAE;MAAEC,EAAE,GAAG,CAAC,CAAC;MAAEC,EAAE,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIJ,EAAE,GAAG,CAACH,EAAE,GAAGA,EAAE,CAACQ,IAAI,CAACf,GAAG,CAAC,EAAEgB,IAAI,EAAE,CAAC,KAAKf,CAAC,EAAE;QAAE,IAAIgB,MAAM,CAACV,EAAE,CAAC,KAAKA,EAAE,EAAE;QAAQM,EAAE,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,EAAE,GAAG,CAACL,EAAE,GAAGE,EAAE,CAACK,IAAI,CAACR,EAAE,CAAC,EAAEW,IAAI,CAAC,KAAKN,IAAI,CAACO,IAAI,CAACX,EAAE,CAACY,KAAK,CAAC,EAAER,IAAI,CAACS,MAAM,KAAKpB,CAAC,CAAC,EAAEY,EAAE,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOS,GAAG,EAAE;MAAER,EAAE,GAAG,CAAC,CAAC,EAAEL,EAAE,GAAGa,GAAG;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACT,EAAE,IAAI,IAAI,IAAIN,EAAE,CAAC,QAAQ,CAAC,KAAKI,EAAE,GAAGJ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEU,MAAM,CAACN,EAAE,CAAC,KAAKA,EAAE,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAIG,EAAE,EAAE,MAAML,EAAE;MAAE;IAAE;IAAE,OAAOG,IAAI;EAAE;AAAE;AACjlB,SAASV,eAAeA,CAACF,GAAG,EAAE;EAAE,IAAIuB,KAAK,CAACC,OAAO,CAACxB,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAASyB,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,SAAS,CAACR,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAAE,IAAI6B,MAAM,GAAGD,SAAS,CAAC5B,CAAC,CAAC;MAAE,KAAK,IAAI8B,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIb,MAAM,CAACnB,SAAS,CAACkC,cAAc,CAACjB,IAAI,CAACe,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEH,MAAM,CAACG,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOH,MAAM;EAAE,CAAC;EAAE,OAAOH,QAAQ,CAACQ,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;AAAE;AAClV,SAASK,wBAAwBA,CAACJ,MAAM,EAAEK,QAAQ,EAAE;EAAE,IAAIL,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,MAAM,GAAGQ,6BAA6B,CAACN,MAAM,EAAEK,QAAQ,CAAC;EAAE,IAAIJ,GAAG,EAAE9B,CAAC;EAAE,IAAIgB,MAAM,CAACoB,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGrB,MAAM,CAACoB,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAK7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,gBAAgB,CAACjB,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAAE8B,GAAG,GAAGO,gBAAgB,CAACrC,CAAC,CAAC;MAAE,IAAIkC,QAAQ,CAACI,OAAO,CAACR,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACd,MAAM,CAACnB,SAAS,CAAC0C,oBAAoB,CAACzB,IAAI,CAACe,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUH,MAAM,CAACG,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOH,MAAM;AAAE;AAC3e,SAASQ,6BAA6BA,CAACN,MAAM,EAAEK,QAAQ,EAAE;EAAE,IAAIL,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIa,UAAU,GAAGxB,MAAM,CAACyB,IAAI,CAACZ,MAAM,CAAC;EAAE,IAAIC,GAAG,EAAE9B,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,UAAU,CAACpB,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE8B,GAAG,GAAGU,UAAU,CAACxC,CAAC,CAAC;IAAE,IAAIkC,QAAQ,CAACI,OAAO,CAACR,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUH,MAAM,CAACG,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAAE;EAAE,OAAOH,MAAM;AAAE;AAClT,SAASe,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIvC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASwC,iBAAiBA,CAAClB,MAAM,EAAEmB,KAAK,EAAE;EAAE,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,KAAK,CAAC1B,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE,IAAI+C,UAAU,GAAGD,KAAK,CAAC9C,CAAC,CAAC;IAAE+C,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAElC,MAAM,CAACmC,cAAc,CAACxB,MAAM,EAAEyB,cAAc,CAACL,UAAU,CAACjB,GAAG,CAAC,EAAEiB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACT,WAAW,EAAEU,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACD,WAAW,CAAC/C,SAAS,EAAEyD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACD,WAAW,EAAEW,WAAW,CAAC;EAAEvC,MAAM,CAACmC,cAAc,CAACP,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASY,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIrD,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEoD,QAAQ,CAAC5D,SAAS,GAAGmB,MAAM,CAAC2C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC7D,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEuB,KAAK,EAAEsC,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEjC,MAAM,CAACmC,cAAc,CAACM,QAAQ,EAAE,WAAW,EAAE;IAAEP,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIQ,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAG5C,MAAM,CAAC+C,cAAc,GAAG/C,MAAM,CAAC+C,cAAc,CAACrC,IAAI,CAAC,CAAC,GAAG,SAASkC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC3E,WAAW;MAAE4E,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE1C,SAAS,EAAE6C,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACtC,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;IAAE;IAAE,OAAOgD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE/D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKtB,OAAO,CAACsB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIT,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOyE,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACrF,SAAS,CAACsF,OAAO,CAACrE,IAAI,CAAC4D,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAGvD,MAAM,CAAC+C,cAAc,GAAG/C,MAAM,CAACqE,cAAc,CAAC3D,IAAI,CAAC,CAAC,GAAG,SAAS6C,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAIhD,MAAM,CAACqE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASyB,kBAAkBA,CAACvF,GAAG,EAAE;EAAE,OAAOwF,kBAAkB,CAACxF,GAAG,CAAC,IAAIyF,gBAAgB,CAACzF,GAAG,CAAC,IAAII,2BAA2B,CAACJ,GAAG,CAAC,IAAI0F,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIpF,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAAC0D,CAAC,EAAE6B,MAAM,EAAE;EAAE,IAAI,CAAC7B,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAO8B,iBAAiB,CAAC9B,CAAC,EAAE6B,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAG5E,MAAM,CAACnB,SAAS,CAACgG,QAAQ,CAAC/E,IAAI,CAAC+C,CAAC,CAAC,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAI/B,CAAC,CAACjE,WAAW,EAAEgG,CAAC,GAAG/B,CAAC,CAACjE,WAAW,CAACmG,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOtE,KAAK,CAAC0E,IAAI,CAACnC,CAAC,CAAC;EAAE,IAAI+B,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACK,IAAI,CAACL,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAAC9B,CAAC,EAAE6B,MAAM,CAAC;AAAE;AAC/Z,SAASF,gBAAgBA,CAACU,IAAI,EAAE;EAAE,IAAI,OAAOxG,MAAM,KAAK,WAAW,IAAIwG,IAAI,CAACxG,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIuG,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO5E,KAAK,CAAC0E,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASX,kBAAkBA,CAACxF,GAAG,EAAE;EAAE,IAAIuB,KAAK,CAACC,OAAO,CAACxB,GAAG,CAAC,EAAE,OAAO4F,iBAAiB,CAAC5F,GAAG,CAAC;AAAE;AAC1F,SAAS4F,iBAAiBA,CAAC5F,GAAG,EAAEoG,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGpG,GAAG,CAACqB,MAAM,EAAE+E,GAAG,GAAGpG,GAAG,CAACqB,MAAM;EAAE,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEoG,IAAI,GAAG,IAAI9E,KAAK,CAAC6E,GAAG,CAAC,EAAEnG,CAAC,GAAGmG,GAAG,EAAEnG,CAAC,EAAE,EAAEoG,IAAI,CAACpG,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;EAAE,OAAOoG,IAAI;AAAE;AAClL,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAI9D,IAAI,GAAGzB,MAAM,CAACyB,IAAI,CAAC6D,MAAM,CAAC;EAAE,IAAItF,MAAM,CAACoB,qBAAqB,EAAE;IAAE,IAAIoE,OAAO,GAAGxF,MAAM,CAACoB,qBAAqB,CAACkE,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAO1F,MAAM,CAAC2F,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAAC1D,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACvB,IAAI,CAACc,KAAK,CAACS,IAAI,EAAE+D,OAAO,CAAC;EAAE;EAAE,OAAO/D,IAAI;AAAE;AACpV,SAASmE,aAAaA,CAACjF,MAAM,EAAE;EAAE,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,SAAS,CAACR,MAAM,EAAEpB,CAAC,EAAE,EAAE;IAAE,IAAI6B,MAAM,GAAG,IAAI,IAAID,SAAS,CAAC5B,CAAC,CAAC,GAAG4B,SAAS,CAAC5B,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGqG,OAAO,CAACrF,MAAM,CAACa,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACgF,OAAO,CAAC,UAAU/E,GAAG,EAAE;MAAEgF,eAAe,CAACnF,MAAM,EAAEG,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGd,MAAM,CAAC+F,yBAAyB,GAAG/F,MAAM,CAACgG,gBAAgB,CAACrF,MAAM,EAAEX,MAAM,CAAC+F,yBAAyB,CAAClF,MAAM,CAAC,CAAC,GAAGwE,OAAO,CAACrF,MAAM,CAACa,MAAM,CAAC,CAAC,CAACgF,OAAO,CAAC,UAAU/E,GAAG,EAAE;MAAEd,MAAM,CAACmC,cAAc,CAACxB,MAAM,EAAEG,GAAG,EAAEd,MAAM,CAAC2F,wBAAwB,CAAC9E,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,MAAM;AAAE;AACzf,SAASmF,eAAeA,CAACrH,GAAG,EAAEqC,GAAG,EAAEX,KAAK,EAAE;EAAEW,GAAG,GAAGsB,cAAc,CAACtB,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIrC,GAAG,EAAE;IAAEuB,MAAM,CAACmC,cAAc,CAAC1D,GAAG,EAAEqC,GAAG,EAAE;MAAEX,KAAK,EAAEA,KAAK;MAAE6B,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEzD,GAAG,CAACqC,GAAG,CAAC,GAAGX,KAAK;EAAE;EAAE,OAAO1B,GAAG;AAAE;AAC3O,SAAS2D,cAAcA,CAAC6D,GAAG,EAAE;EAAE,IAAInF,GAAG,GAAGoF,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOzH,OAAO,CAACsC,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGqF,MAAM,CAACrF,GAAG,CAAC;AAAE;AAC5H,SAASoF,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI7H,OAAO,CAAC4H,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC1H,MAAM,CAAC6H,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACxG,IAAI,CAACsG,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI7H,OAAO,CAACiI,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIpH,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACgH,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,OAAOO,KAAK,IAAIC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,QAAQ,OAAO;AACrF,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,aAAa,EAAEC,SAAS,QAAQ,oBAAoB;AAC7D,SAASC,aAAa,EAAEC,eAAe,EAAEC,cAAc,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,oBAAoB;AAC3L,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,EAAEC,wBAAwB,QAAQ,kBAAkB;AACtE,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7G,SAASC,wBAAwB,EAAEC,yBAAyB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,4BAA4B,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,cAAc,QAAQ,oBAAoB;AACjd,SAASC,6BAA6B,QAAQ,uCAAuC;AACrF,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AACxD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,IAAIC,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;EACxBC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO;AACzB,CAAC;AACD,IAAIC,gBAAgB,GAAG;EACrBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;;AAED;AACA;AACA,IAAIC,OAAO,GAAGrE,MAAM,CAACsE,QAAQ,GAAGtE,MAAM,CAACsE,QAAQ,GAAGA,QAAQ;AAC1D,IAAIC,KAAK;AACT;AACA,OAAOC,qBAAqB,KAAK,UAAU,GAAGA,qBAAqB,GAAG,OAAOC,YAAY,KAAK,UAAU,GAAGA,YAAY,GAAGC,UAAU;AACpI,IAAIC,UAAU;AACd;AACA,OAAOC,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,GAAG,OAAOC,cAAc,KAAK,UAAU,GAAGA,cAAc,GAAGC,YAAY;AACxI,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvE,IAAIA,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOD,QAAQ,CAACb,CAAC;EACnB;EACA,IAAIc,MAAM,KAAK,UAAU,EAAE;IACzB,OAAOD,QAAQ,CAACZ,CAAC;EACnB;EACA,IAAIa,MAAM,KAAK,SAAS,EAAE;IACxB,OAAOD,QAAQ,CAACE,KAAK;EACvB;EACA,OAAOF,QAAQ,CAACG,MAAM;AACxB,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACH,MAAM,EAAEI,YAAY,EAAEC,WAAW,EAAEN,QAAQ,EAAE;EAClG,IAAIO,KAAK,GAAGF,YAAY,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;IAC5C,OAAOA,IAAI,IAAIA,IAAI,CAACC,KAAK,KAAKJ,WAAW;EAC3C,CAAC,CAAC;EACF,IAAIC,KAAK,EAAE;IACT,IAAIN,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLd,CAAC,EAAEoB,KAAK,CAACI,UAAU;QACnBvB,CAAC,EAAEY,QAAQ,CAACZ;MACd,CAAC;IACH;IACA,IAAIa,MAAM,KAAK,UAAU,EAAE;MACzB,OAAO;QACLd,CAAC,EAAEa,QAAQ,CAACb,CAAC;QACbC,CAAC,EAAEmB,KAAK,CAACI;MACX,CAAC;IACH;IACA,IAAIV,MAAM,KAAK,SAAS,EAAE;MACxB,IAAIW,MAAM,GAAGL,KAAK,CAACI,UAAU;MAC7B,IAAIE,OAAO,GAAGb,QAAQ,CAACG,MAAM;MAC7B,OAAOjG,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8F,QAAQ,CAAC,EAAEtB,gBAAgB,CAACsB,QAAQ,CAACc,EAAE,EAAEd,QAAQ,CAACe,EAAE,EAAEF,OAAO,EAAED,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChIV,KAAK,EAAEU,MAAM;QACbT,MAAM,EAAEU;MACV,CAAC,CAAC;IACJ;IACA,IAAIV,MAAM,GAAGI,KAAK,CAACI,UAAU;IAC7B,IAAIT,KAAK,GAAGF,QAAQ,CAACE,KAAK;IAC1B,OAAOhG,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8F,QAAQ,CAAC,EAAEtB,gBAAgB,CAACsB,QAAQ,CAACc,EAAE,EAAEd,QAAQ,CAACe,EAAE,EAAEZ,MAAM,EAAED,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9HA,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ;EACA,OAAOjB,gBAAgB;AACzB,CAAC;AACD,IAAI8B,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACjE,IAAIC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACtCC,cAAc,GAAGH,IAAI,CAACG,cAAc;IACpCC,YAAY,GAAGJ,IAAI,CAACI,YAAY;EAClC,IAAIC,SAAS,GAAG,CAACH,cAAc,IAAI,EAAE,EAAEI,MAAM,CAAC,UAAU1J,MAAM,EAAE2J,KAAK,EAAE;IACrE,IAAIC,QAAQ,GAAGD,KAAK,CAACrL,KAAK,CAAC6K,IAAI;IAC/B,IAAIS,QAAQ,IAAIA,QAAQ,CAAChN,MAAM,EAAE;MAC/B,OAAO,EAAE,CAACiN,MAAM,CAAC/I,kBAAkB,CAACd,MAAM,CAAC,EAAEc,kBAAkB,CAAC8I,QAAQ,CAAC,CAAC;IAC5E;IACA,OAAO5J,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EACN,IAAIyJ,SAAS,IAAIA,SAAS,CAAC7M,MAAM,GAAG,CAAC,EAAE;IACrC,OAAO6M,SAAS;EAClB;EACA,IAAIJ,IAAI,IAAIA,IAAI,CAAC/K,KAAK,IAAI+K,IAAI,CAAC/K,KAAK,CAAC6K,IAAI,IAAIE,IAAI,CAAC/K,KAAK,CAAC6K,IAAI,CAACvM,MAAM,GAAG,CAAC,EAAE;IACvE,OAAOyM,IAAI,CAAC/K,KAAK,CAAC6K,IAAI;EACxB;EACA,IAAIA,IAAI,IAAIA,IAAI,CAACvM,MAAM,IAAIwI,QAAQ,CAACmE,cAAc,CAAC,IAAInE,QAAQ,CAACoE,YAAY,CAAC,EAAE;IAC7E,OAAOL,IAAI,CAAC7H,KAAK,CAACiI,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACrD;EACA,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,uBAAuBA,CAACC,MAAM,EAAEC,iBAAiB,EAAEC,QAAQ,EAAE;EACpE,IAAIA,QAAQ,KAAK,QAAQ,IAAID,iBAAiB,KAAK,IAAI,IAAIlN,KAAK,CAACC,OAAO,CAACgN,MAAM,CAAC,EAAE;IAChF,IAAIG,WAAW,GAAGH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAC3E,IAAII,SAAS,GAAGJ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;;IAEzE;AACJ;AACA;AACA;IACI,IAAI,CAAC,CAACG,WAAW,IAAI,CAAC,CAACC,SAAS,IAAI/E,QAAQ,CAAC8E,WAAW,CAAC,IAAI9E,QAAQ,CAAC+E,SAAS,CAAC,EAAE;MAChF,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASC,0BAA0BA,CAACH,QAAQ,EAAE;EAC5C,OAAOA,QAAQ,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAGjH,SAAS;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIqH,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,SAAS,EAAE/B,WAAW,EAAEgC,WAAW,EAAE;EAC7F,IAAIlB,cAAc,GAAGgB,KAAK,CAAChB,cAAc;IACvCmB,WAAW,GAAGH,KAAK,CAACG,WAAW;EACjC,IAAIC,aAAa,GAAGxB,gBAAgB,CAACqB,SAAS,EAAED,KAAK,CAAC;EACtD,IAAI9B,WAAW,GAAG,CAAC,IAAI,CAACc,cAAc,IAAI,CAACA,cAAc,CAAC1M,MAAM,IAAI4L,WAAW,IAAIkC,aAAa,CAAC9N,MAAM,EAAE;IACvG,OAAO,IAAI;EACb;EACA;EACA,OAAO0M,cAAc,CAACI,MAAM,CAAC,UAAU1J,MAAM,EAAE2J,KAAK,EAAE;IACpD,IAAIgB,IAAI,GAAGhB,KAAK,CAACrL,KAAK,CAACqM,IAAI;IAC3B,IAAIA,IAAI,EAAE;MACR,OAAO3K,MAAM;IACf;IACA,IAAImJ,IAAI,GAAGQ,KAAK,CAACrL,KAAK,CAAC6K,IAAI;IAC3B,IAAIyB,OAAO;IACX,IAAIH,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE;MAC/D;MACA,IAAIC,OAAO,GAAG5B,IAAI,KAAKnG,SAAS,GAAG0H,aAAa,GAAGvB,IAAI;MACvDyB,OAAO,GAAGvF,gBAAgB,CAAC0F,OAAO,EAAEN,WAAW,CAACI,OAAO,EAAEL,WAAW,CAAC;IACvE,CAAC,MAAM;MACLI,OAAO,GAAGzB,IAAI,IAAIA,IAAI,CAACX,WAAW,CAAC,IAAIkC,aAAa,CAAClC,WAAW,CAAC;IACnE;IACA,IAAI,CAACoC,OAAO,EAAE;MACZ,OAAO5K,MAAM;IACf;IACA,OAAO,EAAE,CAAC6J,MAAM,CAAC/I,kBAAkB,CAACd,MAAM,CAAC,EAAE,CAACyG,cAAc,CAACkD,KAAK,EAAEiB,OAAO,CAAC,CAAC,CAAC;EAChF,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,cAAc,GAAG,SAASA,cAAcA,CAACV,KAAK,EAAEC,SAAS,EAAEpC,MAAM,EAAED,QAAQ,EAAE;EAC/E,IAAI+C,SAAS,GAAG/C,QAAQ,IAAI;IAC1Bb,CAAC,EAAEiD,KAAK,CAACY,MAAM;IACf5D,CAAC,EAAEgD,KAAK,CAACa;EACX,CAAC;EACD,IAAIC,GAAG,GAAGnD,mBAAmB,CAACgD,SAAS,EAAE9C,MAAM,CAAC;EAChD,IAAIkD,KAAK,GAAGf,KAAK,CAACgB,mBAAmB;IACnCC,IAAI,GAAGjB,KAAK,CAACG,WAAW;IACxBlC,YAAY,GAAG+B,KAAK,CAAC/B,YAAY;EACnC,IAAIC,WAAW,GAAGlD,wBAAwB,CAAC8F,GAAG,EAAEC,KAAK,EAAE9C,YAAY,EAAEgD,IAAI,CAAC;EAC1E,IAAI/C,WAAW,IAAI,CAAC,IAAID,YAAY,EAAE;IACpC,IAAIiC,WAAW,GAAGjC,YAAY,CAACC,WAAW,CAAC,IAAID,YAAY,CAACC,WAAW,CAAC,CAAC7L,KAAK;IAC9E,IAAI6O,aAAa,GAAGnB,iBAAiB,CAACC,KAAK,EAAEC,SAAS,EAAE/B,WAAW,EAAEgC,WAAW,CAAC;IACjF,IAAIiB,gBAAgB,GAAGnD,mBAAmB,CAACH,MAAM,EAAEkD,KAAK,EAAE7C,WAAW,EAAEyC,SAAS,CAAC;IACjF,OAAO;MACLS,kBAAkB,EAAElD,WAAW;MAC/BgC,WAAW,EAAEA,WAAW;MACxBgB,aAAa,EAAEA,aAAa;MAC5BC,gBAAgB,EAAEA;IACpB,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACrN,KAAK,EAAEsN,KAAK,EAAE;EACpE,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnBvC,cAAc,GAAGsC,KAAK,CAACtC,cAAc;IACrCW,QAAQ,GAAG2B,KAAK,CAAC3B,QAAQ;IACzB6B,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BxC,cAAc,GAAGqC,KAAK,CAACrC,cAAc;IACrCC,YAAY,GAAGoC,KAAK,CAACpC,YAAY;EACnC,IAAIrB,MAAM,GAAG7J,KAAK,CAAC6J,MAAM;IACvB6D,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;IACzBC,WAAW,GAAG3N,KAAK,CAAC2N,WAAW;EACjC,IAAIC,aAAa,GAAG/F,iBAAiB,CAACgC,MAAM,EAAE8B,QAAQ,CAAC;;EAEvD;EACA,IAAIkC,OAAO,GAAGN,IAAI,CAACnC,MAAM,CAAC,UAAU1J,MAAM,EAAE2J,KAAK,EAAE;IACjD,IAAIyC,oBAAoB;IACxB,IAAIC,YAAY,GAAG1C,KAAK,CAACrL,KAAK;MAC5BgO,IAAI,GAAGD,YAAY,CAACC,IAAI;MACxBzB,OAAO,GAAGwB,YAAY,CAACxB,OAAO;MAC9Bb,iBAAiB,GAAGqC,YAAY,CAACrC,iBAAiB;MAClDc,uBAAuB,GAAGuB,YAAY,CAACvB,uBAAuB;MAC9DyB,KAAK,GAAGF,YAAY,CAACE,KAAK;MAC1BlB,KAAK,GAAGgB,YAAY,CAAChB,KAAK;MAC1BmB,aAAa,GAAGH,YAAY,CAACG,aAAa;IAC5C,IAAIC,MAAM,GAAG9C,KAAK,CAACrL,KAAK,CAACwN,SAAS,CAAC;IACnC,IAAI9L,MAAM,CAACyM,MAAM,CAAC,EAAE;MAClB,OAAOzM,MAAM;IACf;IACA,IAAI0K,aAAa,GAAGxB,gBAAgB,CAAC5K,KAAK,CAAC6K,IAAI,EAAE;MAC/CG,cAAc,EAAEA,cAAc,CAACrH,MAAM,CAAC,UAAUoH,IAAI,EAAE;QACpD,OAAOA,IAAI,CAAC/K,KAAK,CAACwN,SAAS,CAAC,KAAKW,MAAM;MACzC,CAAC,CAAC;MACFlD,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF,IAAI7H,GAAG,GAAG+I,aAAa,CAAC9N,MAAM;IAC9B,IAAImN,MAAM,EAAE2C,eAAe,EAAEC,iBAAiB;;IAE9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI7C,uBAAuB,CAACH,KAAK,CAACrL,KAAK,CAACyL,MAAM,EAAEC,iBAAiB,EAAEsC,IAAI,CAAC,EAAE;MACxEvC,MAAM,GAAGxD,oBAAoB,CAACoD,KAAK,CAACrL,KAAK,CAACyL,MAAM,EAAE,IAAI,EAAEC,iBAAiB,CAAC;MAC1E;AACN;AACA;AACA;MACM,IAAIkC,aAAa,KAAKI,IAAI,KAAK,QAAQ,IAAIC,KAAK,KAAK,MAAM,CAAC,EAAE;QAC5DI,iBAAiB,GAAGrG,oBAAoB,CAACoE,aAAa,EAAEG,OAAO,EAAE,UAAU,CAAC;MAC9E;IACF;;IAEA;IACA,IAAI+B,aAAa,GAAGxC,0BAA0B,CAACkC,IAAI,CAAC;;IAEpD;IACA,IAAI,CAACvC,MAAM,IAAIA,MAAM,CAACnN,MAAM,KAAK,CAAC,EAAE;MAClC,IAAIiQ,mBAAmB;MACvB,IAAIC,WAAW,GAAG,CAACD,mBAAmB,GAAGlD,KAAK,CAACrL,KAAK,CAACyL,MAAM,MAAM,IAAI,IAAI8C,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGD,aAAa;MAC7I,IAAI/B,OAAO,EAAE;QACX;QACAd,MAAM,GAAGzD,oBAAoB,CAACoE,aAAa,EAAEG,OAAO,EAAEyB,IAAI,CAAC;QAC3D,IAAIA,IAAI,KAAK,UAAU,IAAIJ,aAAa,EAAE;UACxC;UACA,IAAIa,SAAS,GAAG7H,YAAY,CAAC6E,MAAM,CAAC;UACpC,IAAIe,uBAAuB,IAAIiC,SAAS,EAAE;YACxCL,eAAe,GAAG3C,MAAM;YACxB;YACAA,MAAM,GAAGrP,MAAM,CAAC,CAAC,EAAEiH,GAAG,CAAC;UACzB,CAAC,MAAM,IAAI,CAACmJ,uBAAuB,EAAE;YACnC;YACAf,MAAM,GAAGvD,yBAAyB,CAACsG,WAAW,EAAE/C,MAAM,EAAEJ,KAAK,CAAC,CAACD,MAAM,CAAC,UAAUsD,WAAW,EAAEvE,KAAK,EAAE;cAClG,OAAOuE,WAAW,CAAClP,OAAO,CAAC2K,KAAK,CAAC,IAAI,CAAC,GAAGuE,WAAW,GAAG,EAAE,CAACnD,MAAM,CAAC/I,kBAAkB,CAACkM,WAAW,CAAC,EAAE,CAACvE,KAAK,CAAC,CAAC;YAC5G,CAAC,EAAE,EAAE,CAAC;UACR;QACF,CAAC,MAAM,IAAI6D,IAAI,KAAK,UAAU,EAAE;UAC9B;UACA,IAAI,CAACxB,uBAAuB,EAAE;YAC5Bf,MAAM,GAAGvD,yBAAyB,CAACsG,WAAW,EAAE/C,MAAM,EAAEJ,KAAK,CAAC,CAACD,MAAM,CAAC,UAAUsD,WAAW,EAAEvE,KAAK,EAAE;cAClG,OAAOuE,WAAW,CAAClP,OAAO,CAAC2K,KAAK,CAAC,IAAI,CAAC,IAAIA,KAAK,KAAK,EAAE,IAAI9N,MAAM,CAAC8N,KAAK,CAAC,GAAGuE,WAAW,GAAG,EAAE,CAACnD,MAAM,CAAC/I,kBAAkB,CAACkM,WAAW,CAAC,EAAE,CAACvE,KAAK,CAAC,CAAC;YAC7I,CAAC,EAAE,EAAE,CAAC;UACR,CAAC,MAAM;YACL;YACAsB,MAAM,GAAGA,MAAM,CAAC9H,MAAM,CAAC,UAAUwG,KAAK,EAAE;cACtC,OAAOA,KAAK,KAAK,EAAE,IAAI,CAAC9N,MAAM,CAAC8N,KAAK,CAAC;YACvC,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAI6D,IAAI,KAAK,QAAQ,EAAE;UAC5B;UACA,IAAIW,eAAe,GAAGjH,oBAAoB,CAAC0E,aAAa,EAAEpB,cAAc,CAACrH,MAAM,CAAC,UAAUoH,IAAI,EAAE;YAC9F,OAAOA,IAAI,CAAC/K,KAAK,CAACwN,SAAS,CAAC,KAAKW,MAAM,KAAKD,aAAa,IAAI,CAACnD,IAAI,CAAC/K,KAAK,CAACqM,IAAI,CAAC;UAChF,CAAC,CAAC,EAAEE,OAAO,EAAEZ,QAAQ,EAAE9B,MAAM,CAAC;UAC9B,IAAI8E,eAAe,EAAE;YACnBlD,MAAM,GAAGkD,eAAe;UAC1B;QACF;QACA,IAAIf,aAAa,KAAKI,IAAI,KAAK,QAAQ,IAAIC,KAAK,KAAK,MAAM,CAAC,EAAE;UAC5DI,iBAAiB,GAAGrG,oBAAoB,CAACoE,aAAa,EAAEG,OAAO,EAAE,UAAU,CAAC;QAC9E;MACF,CAAC,MAAM,IAAIqB,aAAa,EAAE;QACxB;QACAnC,MAAM,GAAGrP,MAAM,CAAC,CAAC,EAAEiH,GAAG,CAAC;MACzB,CAAC,MAAM,IAAIoK,WAAW,IAAIA,WAAW,CAACU,MAAM,CAAC,IAAIV,WAAW,CAACU,MAAM,CAAC,CAACS,QAAQ,IAAIZ,IAAI,KAAK,QAAQ,EAAE;QAClG;QACAvC,MAAM,GAAGkC,WAAW,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG5F,sBAAsB,CAAC0F,WAAW,CAACU,MAAM,CAAC,CAACV,WAAW,EAAExC,cAAc,EAAEC,YAAY,CAAC;MACpI,CAAC,MAAM;QACLO,MAAM,GAAG3D,4BAA4B,CAACsE,aAAa,EAAEpB,cAAc,CAACrH,MAAM,CAAC,UAAUoH,IAAI,EAAE;UACzF,OAAOA,IAAI,CAAC/K,KAAK,CAACwN,SAAS,CAAC,KAAKW,MAAM,KAAKD,aAAa,IAAI,CAACnD,IAAI,CAAC/K,KAAK,CAACqM,IAAI,CAAC;QAChF,CAAC,CAAC,EAAE2B,IAAI,EAAEnE,MAAM,EAAE,IAAI,CAAC;MACzB;MACA,IAAImE,IAAI,KAAK,QAAQ,EAAE;QACrB;QACAvC,MAAM,GAAGrD,6BAA6B,CAACsF,QAAQ,EAAEjC,MAAM,EAAE0C,MAAM,EAAExC,QAAQ,EAAEoB,KAAK,CAAC;QACjF,IAAIyB,WAAW,EAAE;UACf/C,MAAM,GAAGxD,oBAAoB,CAACuG,WAAW,EAAE/C,MAAM,EAAEC,iBAAiB,CAAC;QACvE;MACF,CAAC,MAAM,IAAIsC,IAAI,KAAK,UAAU,IAAIQ,WAAW,EAAE;QAC7C,IAAIK,UAAU,GAAGL,WAAW;QAC5B,IAAIM,aAAa,GAAGrD,MAAM,CAACsD,KAAK,CAAC,UAAU5E,KAAK,EAAE;UAChD,OAAO0E,UAAU,CAACrP,OAAO,CAAC2K,KAAK,CAAC,IAAI,CAAC;QACvC,CAAC,CAAC;QACF,IAAI2E,aAAa,EAAE;UACjBrD,MAAM,GAAGoD,UAAU;QACrB;MACF;IACF;IACA,OAAO/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEsC,eAAe,CAAC,CAAC,CAAC,EAAEmK,MAAM,EAAErK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuH,KAAK,CAACrL,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAChI2L,QAAQ,EAAEA,QAAQ;MAClBF,MAAM,EAAEA,MAAM;MACd4C,iBAAiB,EAAEA,iBAAiB;MACpCD,eAAe,EAAEA,eAAe;MAChCY,cAAc,EAAE,CAAClB,oBAAoB,GAAGzC,KAAK,CAACrL,KAAK,CAACyL,MAAM,MAAM,IAAI,IAAIqC,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGQ,aAAa;MAC9IV,aAAa,EAAEA,aAAa;MAC5B/D,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOgE,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACjP,KAAK,EAAEkP,KAAK,EAAE;EAC/D,IAAIlE,cAAc,GAAGkE,KAAK,CAAClE,cAAc;IACvCmE,IAAI,GAAGD,KAAK,CAACC,IAAI;IACjBxD,QAAQ,GAAGuD,KAAK,CAACvD,QAAQ;IACzB6B,SAAS,GAAG0B,KAAK,CAAC1B,SAAS;IAC3BC,WAAW,GAAGyB,KAAK,CAACzB,WAAW;IAC/BxC,cAAc,GAAGiE,KAAK,CAACjE,cAAc;IACrCC,YAAY,GAAGgE,KAAK,CAAChE,YAAY;EACnC,IAAIrB,MAAM,GAAG7J,KAAK,CAAC6J,MAAM;IACvB6D,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;EAC3B,IAAItB,aAAa,GAAGxB,gBAAgB,CAAC5K,KAAK,CAAC6K,IAAI,EAAE;IAC/CG,cAAc,EAAEA,cAAc;IAC9BC,cAAc,EAAEA,cAAc;IAC9BC,YAAY,EAAEA;EAChB,CAAC,CAAC;EACF,IAAI7H,GAAG,GAAG+I,aAAa,CAAC9N,MAAM;EAC9B,IAAIsP,aAAa,GAAG/F,iBAAiB,CAACgC,MAAM,EAAE8B,QAAQ,CAAC;EACvD,IAAIrB,KAAK,GAAG,CAAC,CAAC;;EAEd;EACA;EACA;EACA;EACA,IAAIuD,OAAO,GAAG7C,cAAc,CAACI,MAAM,CAAC,UAAU1J,MAAM,EAAE2J,KAAK,EAAE;IAC3D,IAAI8C,MAAM,GAAG9C,KAAK,CAACrL,KAAK,CAACwN,SAAS,CAAC;IACnC,IAAIwB,cAAc,GAAGlD,0BAA0B,CAAC,QAAQ,CAAC;IACzD,IAAI,CAACpK,MAAM,CAACyM,MAAM,CAAC,EAAE;MACnB7D,KAAK,EAAE;MACP,IAAImB,MAAM;MACV,IAAImC,aAAa,EAAE;QACjBnC,MAAM,GAAGrP,MAAM,CAAC,CAAC,EAAEiH,GAAG,CAAC;MACzB,CAAC,MAAM,IAAIoK,WAAW,IAAIA,WAAW,CAACU,MAAM,CAAC,IAAIV,WAAW,CAACU,MAAM,CAAC,CAACS,QAAQ,EAAE;QAC7EnD,MAAM,GAAG1D,sBAAsB,CAAC0F,WAAW,CAACU,MAAM,CAAC,CAACV,WAAW,EAAExC,cAAc,EAAEC,YAAY,CAAC;QAC9FO,MAAM,GAAGrD,6BAA6B,CAACsF,QAAQ,EAAEjC,MAAM,EAAE0C,MAAM,EAAExC,QAAQ,CAAC;MAC5E,CAAC,MAAM;QACLF,MAAM,GAAGxD,oBAAoB,CAAC+G,cAAc,EAAElH,4BAA4B,CAACsE,aAAa,EAAEpB,cAAc,CAACrH,MAAM,CAAC,UAAUoH,IAAI,EAAE;UAC9H,OAAOA,IAAI,CAAC/K,KAAK,CAACwN,SAAS,CAAC,KAAKW,MAAM,IAAI,CAACpD,IAAI,CAAC/K,KAAK,CAACqM,IAAI;QAC7D,CAAC,CAAC,EAAE,QAAQ,EAAExC,MAAM,CAAC,EAAEsF,IAAI,CAACC,YAAY,CAAC1D,iBAAiB,CAAC;QAC3DD,MAAM,GAAGrD,6BAA6B,CAACsF,QAAQ,EAAEjC,MAAM,EAAE0C,MAAM,EAAExC,QAAQ,CAAC;MAC5E;MACA,OAAO7H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEsC,eAAe,CAAC,CAAC,CAAC,EAAEmK,MAAM,EAAErK,aAAa,CAACA,aAAa,CAAC;QAC1G6H,QAAQ,EAAEA;MACZ,CAAC,EAAEwD,IAAI,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACzB/C,IAAI,EAAE,IAAI;QACVgD,WAAW,EAAElT,IAAI,CAACwM,UAAU,EAAE,EAAE,CAAC4C,MAAM,CAACI,QAAQ,EAAE,GAAG,CAAC,CAACJ,MAAM,CAACjB,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;QAC/EmB,MAAM,EAAEA,MAAM;QACduD,cAAc,EAAEA,cAAc;QAC9BpB,aAAa,EAAEA,aAAa;QAC5B/D,MAAM,EAAEA;QACR;QACA;MACF,CAAC,CAAC,CAAC,CAAC;IACN;IAEA,OAAOnI,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOmM,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIyB,UAAU,GAAG,SAASA,UAAUA,CAACtP,KAAK,EAAEuP,KAAK,EAAE;EACjD,IAAIC,cAAc,GAAGD,KAAK,CAAC5D,QAAQ;IACjCA,QAAQ,GAAG6D,cAAc,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,cAAc;IAC/DC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBzE,cAAc,GAAGuE,KAAK,CAACvE,cAAc;IACrCyC,WAAW,GAAG8B,KAAK,CAAC9B,WAAW;IAC/BxC,cAAc,GAAGsE,KAAK,CAACtE,cAAc;IACrCC,YAAY,GAAGqE,KAAK,CAACrE,YAAY;EACnC,IAAIwC,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;EAC7B,IAAIF,SAAS,GAAG,EAAE,CAACjC,MAAM,CAACI,QAAQ,EAAE,IAAI,CAAC;EACzC;EACA,IAAI4B,IAAI,GAAGzH,aAAa,CAAC4H,QAAQ,EAAE+B,QAAQ,CAAC;EAC5C,IAAI5B,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIN,IAAI,IAAIA,IAAI,CAACjP,MAAM,EAAE;IACvBuP,OAAO,GAAGR,gBAAgB,CAACrN,KAAK,EAAE;MAChCuN,IAAI,EAAEA,IAAI;MACVvC,cAAc,EAAEA,cAAc;MAC9BW,QAAQ,EAAEA,QAAQ;MAClB6B,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA,WAAW;MACxBxC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIF,cAAc,IAAIA,cAAc,CAAC1M,MAAM,EAAE;IAClDuP,OAAO,GAAGoB,iBAAiB,CAACjP,KAAK,EAAE;MACjCmP,IAAI,EAAEM,QAAQ;MACdzE,cAAc,EAAEA,cAAc;MAC9BW,QAAQ,EAAEA,QAAQ;MAClB6B,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA,WAAW;MACxBxC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ;EACA,OAAO2C,OAAO;AAChB,CAAC;AACD,IAAI6B,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC7B,OAAO,EAAE;EAClE,IAAIZ,IAAI,GAAGtG,qBAAqB,CAACkH,OAAO,CAAC;EACzC,IAAI5D,YAAY,GAAG1C,cAAc,CAAC0F,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EACpD,OAAO;IACLhD,YAAY,EAAEA,YAAY;IAC1B+C,mBAAmB,EAAE9Q,OAAO,CAAC+N,YAAY,EAAE,UAAUlJ,CAAC,EAAE;MACtD,OAAOA,CAAC,CAACwJ,UAAU;IACrB,CAAC,CAAC;IACF4B,WAAW,EAAEc,IAAI;IACjB0C,mBAAmB,EAAEhI,iBAAiB,CAACsF,IAAI,EAAEhD,YAAY;EAC3D,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAI2F,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC5P,KAAK,EAAE;EAC1D,IAAI6P,gBAAgB,EAAEC,iBAAiB;EACvC,IAAIpC,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;IAC3BqC,kBAAkB,GAAG/P,KAAK,CAAC+P,kBAAkB;EAC/C,IAAIC,SAAS,GAAGjK,eAAe,CAAC2H,QAAQ,EAAElH,KAAK,CAAC;EAChD,IAAIyJ,UAAU,GAAGD,SAAS,IAAIA,SAAS,CAAChQ,KAAK,IAAIgQ,SAAS,CAAChQ,KAAK,CAACiQ,UAAU,IAAI,CAAC;EAChF,IAAIC,QAAQ,GAAG,CAACF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACH,gBAAgB,GAAGG,SAAS,CAAChQ,KAAK,MAAM,IAAI,IAAI6P,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACK,QAAQ,MAAMxL,SAAS,GAAGsL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACF,iBAAiB,GAAGE,SAAS,CAAChQ,KAAK,MAAM,IAAI,IAAI8P,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACI,QAAQ,GAAGlQ,KAAK,CAAC6K,IAAI,IAAI7K,KAAK,CAAC6K,IAAI,CAACvM,MAAM,GAAG,CAAC,IAAI,CAAC;EAC9Z,OAAO;IACLsO,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACT5B,cAAc,EAAEgF,UAAU;IAC1B/E,YAAY,EAAEgF,QAAQ;IACtB9C,kBAAkB,EAAE,CAAC,CAAC;IACtB+C,eAAe,EAAE,CAAC9T,MAAM,CAAC0T,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG;EACtE,CAAC;AACH,CAAC;AACD,IAAIK,mBAAmB,GAAG,SAASA,mBAAmBA,CAACpF,cAAc,EAAE;EACrE,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAAC1M,MAAM,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,OAAO0M,cAAc,CAACqF,IAAI,CAAC,UAAUtF,IAAI,EAAE;IACzC,IAAI9H,IAAI,GAAG+C,cAAc,CAAC+E,IAAI,IAAIA,IAAI,CAACiD,IAAI,CAAC;IAC5C,OAAO/K,IAAI,IAAIA,IAAI,CAACzD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;AACJ,CAAC;AACD,IAAI8Q,mBAAmB,GAAG,SAASA,mBAAmBA,CAACzG,MAAM,EAAE;EAC7D,IAAIA,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO;MACL0G,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAI3G,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO;MACL0G,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,IAAI3G,MAAM,KAAK,SAAS,EAAE;IACxB,OAAO;MACL0G,eAAe,EAAE,YAAY;MAC7BC,YAAY,EAAE;IAChB,CAAC;EACH;EACA,OAAO;IACLD,eAAe,EAAE,WAAW;IAC5BC,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,cAAc,EAAE;EACpE,IAAI3Q,KAAK,GAAG0Q,KAAK,CAAC1Q,KAAK;IACrBgL,cAAc,GAAG0F,KAAK,CAAC1F,cAAc;IACrC4F,cAAc,GAAGF,KAAK,CAACG,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;IAC1DE,cAAc,GAAGJ,KAAK,CAACK,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;EAC5D,IAAIE,KAAK,GAAGhR,KAAK,CAACgR,KAAK;IACrBC,MAAM,GAAGjR,KAAK,CAACiR,MAAM;IACrBvD,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;EAC3B,IAAIwD,MAAM,GAAGlR,KAAK,CAACkR,MAAM,IAAI,CAAC,CAAC;EAC/B,IAAIlB,SAAS,GAAGjK,eAAe,CAAC2H,QAAQ,EAAElH,KAAK,CAAC;EAChD,IAAI2K,UAAU,GAAGpL,eAAe,CAAC2H,QAAQ,EAAEnI,MAAM,CAAC;EAClD,IAAI6L,OAAO,GAAGlT,MAAM,CAACyB,IAAI,CAACoR,QAAQ,CAAC,CAAC3F,MAAM,CAAC,UAAU1J,MAAM,EAAE2P,EAAE,EAAE;IAC/D,IAAIlH,KAAK,GAAG4G,QAAQ,CAACM,EAAE,CAAC;IACxB,IAAIhC,WAAW,GAAGlF,KAAK,CAACkF,WAAW;IACnC,IAAI,CAAClF,KAAK,CAACmH,MAAM,IAAI,CAACnH,KAAK,CAACkC,IAAI,EAAE;MAChC,OAAOvI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEsC,eAAe,CAAC,CAAC,CAAC,EAAEqL,WAAW,EAAE3N,MAAM,CAAC2N,WAAW,CAAC,GAAGlF,KAAK,CAAC6G,KAAK,CAAC,CAAC;IAC1H;IACA,OAAOtP,MAAM;EACf,CAAC,EAAE;IACD6P,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAI,CAAC;IACtBC,KAAK,EAAEN,MAAM,CAACM,KAAK,IAAI;EACzB,CAAC,CAAC;EACF,IAAIC,OAAO,GAAGvT,MAAM,CAACyB,IAAI,CAACkR,QAAQ,CAAC,CAACzF,MAAM,CAAC,UAAU1J,MAAM,EAAE2P,EAAE,EAAE;IAC/D,IAAIlH,KAAK,GAAG0G,QAAQ,CAACQ,EAAE,CAAC;IACxB,IAAIhC,WAAW,GAAGlF,KAAK,CAACkF,WAAW;IACnC,IAAI,CAAClF,KAAK,CAACmH,MAAM,IAAI,CAACnH,KAAK,CAACkC,IAAI,EAAE;MAChC,OAAOvI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEsC,eAAe,CAAC,CAAC,CAAC,EAAEqL,WAAW,EAAElT,IAAI,CAACuF,MAAM,EAAE,EAAE,CAAC6J,MAAM,CAAC8D,WAAW,CAAC,CAAC,GAAGlF,KAAK,CAAC8G,MAAM,CAAC,CAAC;IAC5I;IACA,OAAOvP,MAAM;EACf,CAAC,EAAE;IACDgQ,GAAG,EAAER,MAAM,CAACQ,GAAG,IAAI,CAAC;IACpBC,MAAM,EAAET,MAAM,CAACS,MAAM,IAAI;EAC3B,CAAC,CAAC;EACF,IAAIC,MAAM,GAAG9N,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2N,OAAO,CAAC,EAAEL,OAAO,CAAC;EAC/D,IAAIS,WAAW,GAAGD,MAAM,CAACD,MAAM;EAC/B,IAAI3B,SAAS,EAAE;IACb4B,MAAM,CAACD,MAAM,IAAI3B,SAAS,CAAChQ,KAAK,CAACiR,MAAM,IAAIzK,KAAK,CAAC4I,YAAY,CAAC6B,MAAM;EACtE;EACA,IAAIE,UAAU,IAAIR,cAAc,EAAE;IAChCiB,MAAM,GAAGxK,oBAAoB,CAACwK,MAAM,EAAE5G,cAAc,EAAEhL,KAAK,EAAE2Q,cAAc,CAAC;EAC9E;EACA,OAAO7M,aAAa,CAACA,aAAa,CAAC;IACjC+N,WAAW,EAAEA;EACf,CAAC,EAAED,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IACdZ,KAAK,EAAEA,KAAK,GAAGY,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACJ,KAAK;IACzCP,MAAM,EAAEA,MAAM,GAAGW,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACD;EACvC,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIG,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,KAAK,EAAE;EAC7E,IAAIC,MAAM;EACV,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,qBAAqB,GAAGJ,KAAK,CAACK,uBAAuB;IACrDA,uBAAuB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,qBAAqB;IAC3FE,qBAAqB,GAAGN,KAAK,CAACO,yBAAyB;IACvDA,yBAAyB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAGA,qBAAqB;IAC/FE,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,aAAa,GAAGV,KAAK,CAACU,aAAa;IACnCrD,YAAY,GAAG2C,KAAK,CAAC3C,YAAY;EACnC,IAAIsD,cAAc,GAAG,SAASA,cAAcA,CAAC1S,KAAK,EAAE2S,YAAY,EAAE;IAChE,IAAI3H,cAAc,GAAG2H,YAAY,CAAC3H,cAAc;MAC9CyC,WAAW,GAAGkF,YAAY,CAAClF,WAAW;MACtCmE,MAAM,GAAGe,YAAY,CAACf,MAAM;MAC5BgB,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAChC3H,cAAc,GAAG0H,YAAY,CAAC1H,cAAc;MAC5CC,YAAY,GAAGyH,YAAY,CAACzH,YAAY;IAC1C,IAAI2H,OAAO,GAAG7S,KAAK,CAAC6S,OAAO;MACzBhJ,MAAM,GAAG7J,KAAK,CAAC6J,MAAM;MACrBiJ,MAAM,GAAG9S,KAAK,CAAC8S,MAAM;MACrBC,cAAc,GAAG/S,KAAK,CAAC+S,cAAc;MACrCC,gBAAgB,GAAGhT,KAAK,CAACiT,UAAU;IACrC,IAAIC,oBAAoB,GAAG5C,mBAAmB,CAACzG,MAAM,CAAC;MACpD0G,eAAe,GAAG2C,oBAAoB,CAAC3C,eAAe;MACtDC,YAAY,GAAG0C,oBAAoB,CAAC1C,YAAY;IAClD,IAAI2C,MAAM,GAAG/C,mBAAmB,CAACpF,cAAc,CAAC;IAChD,IAAIoI,QAAQ,GAAGD,MAAM,IAAIjM,cAAc,CAAC;MACtC2L,OAAO,EAAEA,OAAO;MAChBpF,WAAW,EAAEA;IACf,CAAC,CAAC;IACF,IAAI4F,cAAc,GAAG,EAAE;IACvBrI,cAAc,CAACjH,OAAO,CAAC,UAAUgH,IAAI,EAAET,KAAK,EAAE;MAC5C,IAAI8B,aAAa,GAAGxB,gBAAgB,CAAC5K,KAAK,CAAC6K,IAAI,EAAE;QAC/CI,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA;MAChB,CAAC,EAAEH,IAAI,CAAC;MACR,IAAIuI,WAAW,GAAGvI,IAAI,CAAC/K,KAAK;QAC1BuM,OAAO,GAAG+G,WAAW,CAAC/G,OAAO;QAC7BgH,eAAe,GAAGD,WAAW,CAACL,UAAU;MAC1C,IAAIO,aAAa,GAAGzI,IAAI,CAAC/K,KAAK,CAAC,EAAE,CAACuL,MAAM,CAACgF,eAAe,EAAE,IAAI,CAAC,CAAC;MAChE,IAAIkD,UAAU,GAAG1I,IAAI,CAAC/K,KAAK,CAAC,EAAE,CAACuL,MAAM,CAACiF,YAAY,EAAE,IAAI,CAAC,CAAC;MAC1D,IAAIkD,OAAO,GAAGnB,cAAc,CAACnH,MAAM,CAAC,UAAU1J,MAAM,EAAEyI,KAAK,EAAE;QAC3D,IAAIwJ,cAAc;QAClB,IAAI9F,OAAO,GAAG8E,YAAY,CAAC,EAAE,CAACpH,MAAM,CAACpB,KAAK,CAACwB,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAI0F,EAAE,GAAGtG,IAAI,CAAC/K,KAAK,CAAC,EAAE,CAACuL,MAAM,CAACpB,KAAK,CAACwB,QAAQ,EAAE,IAAI,CAAC,CAAC;QACpD,IAAIsB,IAAI,GAAGY,OAAO,IAAIA,OAAO,CAACwD,EAAE,CAAC;QACjC,OAAOvN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAGiS,cAAc,GAAG,CAAC,CAAC,EAAE3P,eAAe,CAAC2P,cAAc,EAAExJ,KAAK,CAACwB,QAAQ,EAAEsB,IAAI,CAAC,EAAEjJ,eAAe,CAAC2P,cAAc,EAAE,EAAE,CAACpI,MAAM,CAACpB,KAAK,CAACwB,QAAQ,EAAE,OAAO,CAAC,EAAEpE,cAAc,CAAC0F,IAAI,CAAC,CAAC,EAAE0G,cAAc,CAAC,CAAC;MAC9O,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,IAAIC,QAAQ,GAAGF,OAAO,CAAClD,YAAY,CAAC;MACpC,IAAIqD,SAAS,GAAGH,OAAO,CAAC,EAAE,CAACnI,MAAM,CAACiF,YAAY,EAAE,OAAO,CAAC,CAAC;MACzD,IAAIsD,WAAW,GAAGrG,WAAW,IAAIA,WAAW,CAAC+F,aAAa,CAAC,IAAI/F,WAAW,CAAC+F,aAAa,CAAC,CAAC5E,QAAQ,IAAInH,oBAAoB,CAACsD,IAAI,EAAE0C,WAAW,CAAC+F,aAAa,CAAC,CAAC/F,WAAW,CAAC;MACxK,IAAIsG,SAAS,GAAG/N,cAAc,CAAC+E,IAAI,CAACiD,IAAI,CAAC,CAACxO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;MAC7D,IAAIwU,QAAQ,GAAGrM,iBAAiB,CAACiM,QAAQ,EAAEC,SAAS,CAAC;MACrD,IAAII,WAAW,GAAG,EAAE;MACpB,IAAIF,SAAS,EAAE;QACb,IAAIG,KAAK,EAAEC,kBAAkB;QAC7B;QACA,IAAIlB,UAAU,GAAG5W,MAAM,CAACkX,eAAe,CAAC,GAAGP,gBAAgB,GAAGO,eAAe;QAC7E,IAAIa,WAAW,GAAG,CAACF,KAAK,GAAG,CAACC,kBAAkB,GAAGxM,iBAAiB,CAACiM,QAAQ,EAAEC,SAAS,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIM,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGlB,UAAU,MAAM,IAAI,IAAIiB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;QACpND,WAAW,GAAG9M,cAAc,CAAC;UAC3B2L,MAAM,EAAEA,MAAM;UACdC,cAAc,EAAEA,cAAc;UAC9BiB,QAAQ,EAAEI,WAAW,KAAKJ,QAAQ,GAAGI,WAAW,GAAGJ,QAAQ;UAC3DZ,QAAQ,EAAEA,QAAQ,CAACK,UAAU,CAAC;UAC9BR,UAAU,EAAEA;QACd,CAAC,CAAC;QACF,IAAImB,WAAW,KAAKJ,QAAQ,EAAE;UAC5BC,WAAW,GAAGA,WAAW,CAACI,GAAG,CAAC,UAAUvH,GAAG,EAAE;YAC3C,OAAOhJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgJ,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;cAC/CwH,QAAQ,EAAExQ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgJ,GAAG,CAACwH,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC3D1C,MAAM,EAAE9E,GAAG,CAACwH,QAAQ,CAAC1C,MAAM,GAAGwC,WAAW,GAAG;cAC9C,CAAC;YACH,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;MACA,IAAIG,UAAU,GAAGxJ,IAAI,IAAIA,IAAI,CAACiD,IAAI,IAAIjD,IAAI,CAACiD,IAAI,CAACwG,eAAe;MAC/D,IAAID,UAAU,EAAE;QACd,IAAIE,cAAc;QAClBpB,cAAc,CAACjV,IAAI,CAAC;UAClB4B,KAAK,EAAE8D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyQ,UAAU,CAACzQ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4P,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9FtH,aAAa,EAAEA,aAAa;YAC5BpM,KAAK,EAAEA,KAAK;YACZuM,OAAO,EAAEA,OAAO;YAChBxB,IAAI,EAAEA,IAAI;YACViJ,QAAQ,EAAEA,QAAQ;YAClBC,WAAW,EAAEA,WAAW;YACxBrC,MAAM,EAAEA,MAAM;YACdkC,WAAW,EAAEA,WAAW;YACxBjK,MAAM,EAAEA,MAAM;YACdoB,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGuJ,cAAc,GAAG;YAC1BzV,GAAG,EAAE+L,IAAI,CAAC/L,GAAG,IAAI,OAAO,CAACuM,MAAM,CAACjB,KAAK;UACvC,CAAC,EAAEtG,eAAe,CAACyQ,cAAc,EAAElE,eAAe,EAAEmD,OAAO,CAACnD,eAAe,CAAC,CAAC,EAAEvM,eAAe,CAACyQ,cAAc,EAAEjE,YAAY,EAAEkD,OAAO,CAAClD,YAAY,CAAC,CAAC,EAAExM,eAAe,CAACyQ,cAAc,EAAE,aAAa,EAAE7B,QAAQ,CAAC,EAAE6B,cAAc,CAAC,CAAC;UAC/NC,UAAU,EAAEzO,eAAe,CAAC8E,IAAI,EAAE/K,KAAK,CAAC0N,QAAQ,CAAC;UACjD3C,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOsI,cAAc;EACvB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIsB,yCAAyC,GAAG,SAASA,yCAAyCA,CAACC,KAAK,EAAEC,SAAS,EAAE;IACnH,IAAI7U,KAAK,GAAG4U,KAAK,CAAC5U,KAAK;MACrBiL,cAAc,GAAG2J,KAAK,CAAC3J,cAAc;MACrCC,YAAY,GAAG0J,KAAK,CAAC1J,YAAY;MACjC0H,QAAQ,GAAGgC,KAAK,CAAChC,QAAQ;IAC3B,IAAI,CAAC1M,mBAAmB,CAAC;MACvBlG,KAAK,EAAEA;IACT,CAAC,CAAC,EAAE;MACF,OAAO,IAAI;IACb;IACA,IAAI0N,QAAQ,GAAG1N,KAAK,CAAC0N,QAAQ;MAC3B7D,MAAM,GAAG7J,KAAK,CAAC6J,MAAM;MACrB8D,WAAW,GAAG3N,KAAK,CAAC2N,WAAW;MAC/B9C,IAAI,GAAG7K,KAAK,CAAC6K,IAAI;MACjBiK,iBAAiB,GAAG9U,KAAK,CAAC8U,iBAAiB;IAC7C,IAAIC,qBAAqB,GAAGzE,mBAAmB,CAACzG,MAAM,CAAC;MACrD0G,eAAe,GAAGwE,qBAAqB,CAACxE,eAAe;MACvDC,YAAY,GAAGuE,qBAAqB,CAACvE,YAAY;IACnD,IAAIxF,cAAc,GAAGlF,aAAa,CAAC4H,QAAQ,EAAEwE,cAAc,CAAC;IAC5D,IAAIzE,WAAW,GAAG7F,sBAAsB,CAACiD,IAAI,EAAEG,cAAc,EAAE,EAAE,CAACO,MAAM,CAACgF,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAChF,MAAM,CAACiF,YAAY,EAAE,IAAI,CAAC,EAAE7C,WAAW,EAAEmH,iBAAiB,CAAC;IAC/J,IAAIpB,OAAO,GAAGnB,cAAc,CAACnH,MAAM,CAAC,UAAU1J,MAAM,EAAEyI,KAAK,EAAE;MAC3D,IAAIlH,IAAI,GAAG,EAAE,CAACsI,MAAM,CAACpB,KAAK,CAACwB,QAAQ,EAAE,KAAK,CAAC;MAC3C,OAAO7H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEsC,eAAe,CAAC,CAAC,CAAC,EAAEf,IAAI,EAAEqM,UAAU,CAACtP,KAAK,EAAE8D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1Ia,cAAc,EAAEA,cAAc;QAC9ByC,WAAW,EAAEtD,KAAK,CAACwB,QAAQ,KAAK4E,eAAe,IAAI9C,WAAW;QAC9DxC,cAAc,EAAEA,cAAc;QAC9BC,YAAY,EAAEA;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI0G,MAAM,GAAGnB,eAAe,CAAC3M,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4P,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACzE1T,KAAK,EAAEA,KAAK;MACZgL,cAAc,EAAEA;IAClB,CAAC,CAAC,EAAE6J,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,UAAU,CAAC;IAC/E9W,MAAM,CAACyB,IAAI,CAAC+T,OAAO,CAAC,CAAC3P,OAAO,CAAC,UAAU/E,GAAG,EAAE;MAC1C0U,OAAO,CAAC1U,GAAG,CAAC,GAAGyT,aAAa,CAACzS,KAAK,EAAE0T,OAAO,CAAC1U,GAAG,CAAC,EAAE4S,MAAM,EAAE5S,GAAG,CAACiW,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAEhD,SAAS,CAAC;IAC9F,CAAC,CAAC;IACF,IAAIiD,WAAW,GAAGxB,OAAO,CAAC,EAAE,CAACnI,MAAM,CAACiF,YAAY,EAAE,KAAK,CAAC,CAAC;IACzD,IAAI2E,QAAQ,GAAGzF,qBAAqB,CAACwF,WAAW,CAAC;IACjD,IAAIE,uBAAuB,GAAG1C,cAAc,CAAC1S,KAAK,EAAE8D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4P,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MAChGzI,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1B0H,QAAQ,EAAEA,QAAQ;MAClB5H,cAAc,EAAEA,cAAc;MAC9ByC,WAAW,EAAEA,WAAW;MACxBmE,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC;IACH,OAAO9N,aAAa,CAACA,aAAa,CAAC;MACjCsR,uBAAuB,EAAEA,uBAAuB;MAChDpK,cAAc,EAAEA,cAAc;MAC9B4G,MAAM,EAAEA,MAAM;MACdnE,WAAW,EAAEA;IACf,CAAC,EAAE0H,QAAQ,CAAC,EAAEzB,OAAO,CAAC;EACxB,CAAC;EACD,OAAO1B,MAAM,GAAG,aAAa,UAAUqD,UAAU,EAAE;IACjD3U,SAAS,CAAC4U,uBAAuB,EAAED,UAAU,CAAC;IAC9C,IAAIE,MAAM,GAAGpU,YAAY,CAACmU,uBAAuB,CAAC;IAClD;;IAEA,SAASA,uBAAuBA,CAACE,MAAM,EAAE;MACvC,IAAIC,KAAK;MACT7V,eAAe,CAAC,IAAI,EAAE0V,uBAAuB,CAAC;MAC9CG,KAAK,GAAGF,MAAM,CAACvX,IAAI,CAAC,IAAI,EAAEwX,MAAM,CAAC;MACjCxR,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;QACzE,IAAI,CAACpZ,MAAM,CAACoZ,KAAK,CAACC,OAAO,CAAC,IAAInM,UAAU,EAAE;UACxCA,UAAU,CAACkM,KAAK,CAACC,OAAO,CAAC;QAC3B;QACAD,KAAK,CAACC,OAAO,GAAG,IAAI;MACtB,CAAC,CAAC;MACF1R,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,wBAAwB,EAAE,UAAUE,GAAG,EAAE;QACtF,IAAIA,GAAG,EAAE;UACP,IAAIC,WAAW,GAAGH,KAAK,CAACzJ,KAAK;YAC3Bf,cAAc,GAAG2K,WAAW,CAAC3K,cAAc;YAC3CC,YAAY,GAAG0K,WAAW,CAAC1K,YAAY;YACvC0H,QAAQ,GAAGgD,WAAW,CAAChD,QAAQ;UACjC6C,KAAK,CAACI,QAAQ,CAAC/R,aAAa,CAAC;YAC3BkR,UAAU,EAAEW;UACd,CAAC,EAAEhB,yCAAyC,CAAC;YAC3C3U,KAAK,EAAEyV,KAAK,CAACzV,KAAK;YAClBiL,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA,YAAY;YAC1B0H,QAAQ,EAAEA;UACZ,CAAC,EAAE9O,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2R,KAAK,CAACzJ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACnDgJ,UAAU,EAAEW;UACd,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;MACF,CAAC,CAAC;MACF3R,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,wBAAwB,EAAE,UAAUK,GAAG,EAAEC,OAAO,EAAElL,IAAI,EAAE;QACrG,IAAImL,MAAM,GAAGP,KAAK,CAACzV,KAAK,CAACgW,MAAM;QAC/B,IAAIA,MAAM,KAAKF,GAAG,IAAIC,OAAO,KAAKN,KAAK,CAACQ,aAAa,EAAE;UACrDR,KAAK,CAACS,YAAY,CAAC,CAAC;UACpBT,KAAK,CAACC,OAAO,GAAGvM,KAAK,IAAIA,KAAK,CAACsM,KAAK,CAACU,cAAc,CAACvX,IAAI,CAACoD,sBAAsB,CAACyT,KAAK,CAAC,EAAE5K,IAAI,CAAC,CAAC;QAChG;MACF,CAAC,CAAC;MACF7G,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,mBAAmB,EAAE,UAAUW,KAAK,EAAE;QACnF,IAAInG,UAAU,GAAGmG,KAAK,CAACnG,UAAU;UAC/BC,QAAQ,GAAGkG,KAAK,CAAClG,QAAQ;QAC3B;QACA,IAAID,UAAU,KAAKwF,KAAK,CAACzJ,KAAK,CAACf,cAAc,IAAIiF,QAAQ,KAAKuF,KAAK,CAACzJ,KAAK,CAACd,YAAY,EAAE;UACtF,IAAI0H,QAAQ,GAAG6C,KAAK,CAACzJ,KAAK,CAAC4G,QAAQ;UACnC6C,KAAK,CAACI,QAAQ,CAAC,YAAY;YACzB,OAAO/R,aAAa,CAAC;cACnBmH,cAAc,EAAEgF,UAAU;cAC1B/E,YAAY,EAAEgF;YAChB,CAAC,EAAEyE,yCAAyC,CAAC;cAC3C3U,KAAK,EAAEyV,KAAK,CAACzV,KAAK;cAClBiL,cAAc,EAAEgF,UAAU;cAC1B/E,YAAY,EAAEgF,QAAQ;cACtB0C,QAAQ,EAAEA;YACZ,CAAC,EAAE6C,KAAK,CAACzJ,KAAK,CAAC,CAAC;UAClB,CAAC,CAAC;UACFyJ,KAAK,CAACY,gBAAgB,CAAC;YACrBpL,cAAc,EAAEgF,UAAU;YAC1B/E,YAAY,EAAEgF;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACFlM,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUnT,CAAC,EAAE;QAC9E,IAAIgU,YAAY,GAAGb,KAAK,CAACzV,KAAK,CAACsW,YAAY;QAC3C,IAAIC,KAAK,GAAGd,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;QACjC,IAAIiU,KAAK,EAAE;UACT,IAAIE,UAAU,GAAG3S,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyS,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YAC3DpG,eAAe,EAAE;UACnB,CAAC,CAAC;UACFsF,KAAK,CAACI,QAAQ,CAACY,UAAU,CAAC;UAC1BhB,KAAK,CAACY,gBAAgB,CAACI,UAAU,CAAC;UAClC,IAAIza,WAAW,CAACsa,YAAY,CAAC,EAAE;YAC7BA,YAAY,CAACG,UAAU,EAAEnU,CAAC,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,yBAAyB,EAAE,UAAUnT,CAAC,EAAE;QACrF,IAAIoU,WAAW,GAAGjB,KAAK,CAACzV,KAAK,CAAC0W,WAAW;QACzC,IAAIH,KAAK,GAAGd,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;QACjC,IAAIqU,SAAS,GAAGJ,KAAK,GAAGzS,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyS,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAClEpG,eAAe,EAAE;QACnB,CAAC,CAAC,GAAG;UACHA,eAAe,EAAE;QACnB,CAAC;QACDsF,KAAK,CAACI,QAAQ,CAACc,SAAS,CAAC;QACzBlB,KAAK,CAACY,gBAAgB,CAACM,SAAS,CAAC;QACjC,IAAI3a,WAAW,CAAC0a,WAAW,CAAC,EAAE;UAC5BA,WAAW,CAACC,SAAS,EAAErU,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,sBAAsB,EAAE,UAAUmB,EAAE,EAAE;QACnFnB,KAAK,CAACI,QAAQ,CAAC,YAAY;UACzB,OAAO;YACL1F,eAAe,EAAE,IAAI;YACrB0G,UAAU,EAAED,EAAE;YACd1J,aAAa,EAAE0J,EAAE,CAACE,cAAc;YAChC3J,gBAAgB,EAAEyJ,EAAE,CAACG,eAAe,IAAI;cACtChO,CAAC,EAAE6N,EAAE,CAAClM,EAAE;cACR1B,CAAC,EAAE4N,EAAE,CAACjM;YACR;UACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;MACF3G,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;QACjFA,KAAK,CAACI,QAAQ,CAAC,YAAY;UACzB,OAAO;YACL1F,eAAe,EAAE;UACnB,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;MACFnM,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUnT,CAAC,EAAE;QAC7E,IAAIA,CAAC,IAAItG,WAAW,CAACsG,CAAC,CAAC0U,OAAO,CAAC,EAAE;UAC/B1U,CAAC,CAAC0U,OAAO,CAAC,CAAC;QACb;QACAvB,KAAK,CAACwB,uBAAuB,CAAC3U,CAAC,CAAC;MAClC,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUnT,CAAC,EAAE;QAC9E,IAAI4U,YAAY,GAAGzB,KAAK,CAACzV,KAAK,CAACkX,YAAY;QAC3C,IAAIP,SAAS,GAAG;UACdxG,eAAe,EAAE;QACnB,CAAC;QACDsF,KAAK,CAACI,QAAQ,CAACc,SAAS,CAAC;QACzBlB,KAAK,CAACY,gBAAgB,CAACM,SAAS,CAAC;QACjC,IAAI3a,WAAW,CAACkb,YAAY,CAAC,EAAE;UAC7BA,YAAY,CAACP,SAAS,EAAErU,CAAC,CAAC;QAC5B;QACAmT,KAAK,CAAC0B,oCAAoC,CAAC,CAAC;MAC9C,CAAC,CAAC;MACFnT,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUnT,CAAC,EAAE;QAC9E,IAAI8U,SAAS,GAAG/Q,mBAAmB,CAAC/D,CAAC,CAAC;QACtC,IAAI+U,KAAK,GAAGlb,IAAI,CAACsZ,KAAK,CAACzV,KAAK,EAAE,EAAE,CAACuL,MAAM,CAAC6L,SAAS,CAAC,CAAC;QACnD,IAAIA,SAAS,IAAIpb,WAAW,CAACqb,KAAK,CAAC,EAAE;UACnC,IAAId,KAAK;UACT,IAAI,YAAY,CAACpT,IAAI,CAACiU,SAAS,CAAC,EAAE;YAChCb,KAAK,GAAGd,KAAK,CAACe,YAAY,CAAClU,CAAC,CAACgV,cAAc,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,MAAM;YACLf,KAAK,GAAGd,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;UAC/B;UACA,IAAIiV,OAAO,GAAGF,KAAK;;UAEnB;UACA;UACAE,OAAO,CAAChB,KAAK,EAAEjU,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUnT,CAAC,EAAE;QACzE,IAAIkV,OAAO,GAAG/B,KAAK,CAACzV,KAAK,CAACwX,OAAO;QACjC,IAAIjB,KAAK,GAAGd,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;QACjC,IAAIiU,KAAK,EAAE;UACT,IAAIkB,WAAW,GAAG3T,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyS,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5DpG,eAAe,EAAE;UACnB,CAAC,CAAC;UACFsF,KAAK,CAACI,QAAQ,CAAC4B,WAAW,CAAC;UAC3BhC,KAAK,CAACY,gBAAgB,CAACoB,WAAW,CAAC;UACnC,IAAIzb,WAAW,CAACwb,OAAO,CAAC,EAAE;YACxBA,OAAO,CAACC,WAAW,EAAEnV,CAAC,CAAC;UACzB;QACF;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUnT,CAAC,EAAE;QAC7E,IAAIoV,WAAW,GAAGjC,KAAK,CAACzV,KAAK,CAAC0X,WAAW;QACzC,IAAI1b,WAAW,CAAC0b,WAAW,CAAC,EAAE;UAC5B,IAAIC,WAAW,GAAGlC,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;UACvCoV,WAAW,CAACC,WAAW,EAAErV,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,eAAe,EAAE,UAAUnT,CAAC,EAAE;QAC3E,IAAIsV,SAAS,GAAGnC,KAAK,CAACzV,KAAK,CAAC4X,SAAS;QACrC,IAAI5b,WAAW,CAAC4b,SAAS,CAAC,EAAE;UAC1B,IAAIC,WAAW,GAAGpC,KAAK,CAACe,YAAY,CAAClU,CAAC,CAAC;UACvCsV,SAAS,CAACC,WAAW,EAAEvV,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;MACF0B,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUnT,CAAC,EAAE;QAC7E,IAAIA,CAAC,CAACgV,cAAc,IAAI,IAAI,IAAIhV,CAAC,CAACgV,cAAc,CAAChZ,MAAM,GAAG,CAAC,EAAE;UAC3DmX,KAAK,CAACqC,eAAe,CAACxV,CAAC,CAACgV,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACFtT,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUnT,CAAC,EAAE;QAC9E,IAAIA,CAAC,CAACgV,cAAc,IAAI,IAAI,IAAIhV,CAAC,CAACgV,cAAc,CAAChZ,MAAM,GAAG,CAAC,EAAE;UAC3DmX,KAAK,CAACsC,eAAe,CAACzV,CAAC,CAACgV,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACFtT,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,gBAAgB,EAAE,UAAUnT,CAAC,EAAE;QAC5E,IAAIA,CAAC,CAACgV,cAAc,IAAI,IAAI,IAAIhV,CAAC,CAACgV,cAAc,CAAChZ,MAAM,GAAG,CAAC,EAAE;UAC3DmX,KAAK,CAACuC,aAAa,CAAC1V,CAAC,CAACgV,cAAc,CAAC,CAAC,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC;MACFtT,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,8BAA8B,EAAE,UAAUwC,MAAM,EAAE;QAC/F,IAAIrP,KAAK,GAAGqP,MAAM,CAACrP,KAAK;UACtBoI,KAAK,GAAGiH,MAAM,CAACjH,KAAK;UACpBC,MAAM,GAAGgH,MAAM,CAAChH,MAAM;UACtBW,MAAM,GAAGqG,MAAM,CAACrG,MAAM;QACxB,OAAOpK,oBAAoB,CAACrC,QAAQ,CAACrB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,aAAa,CAAC6I,YAAY,CAAC,EAAExG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1HmE,KAAK,EAAExF,cAAc,CAACqB,KAAK,EAAE,IAAI,CAAC;UAClCsP,OAAO,EAAE;YACPnP,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJgI,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV;QACF,CAAC,CAAC,CAAC,EAAEW,MAAM,CAACL,IAAI,EAAEK,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACZ,KAAK,CAAC;MAC/C,CAAC,CAAC;MACFhN,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,gCAAgC,EAAE,UAAU0C,MAAM,EAAE;QACjG,IAAItP,KAAK,GAAGsP,MAAM,CAACtP,KAAK;UACtBmI,KAAK,GAAGmH,MAAM,CAACnH,KAAK;UACpBC,MAAM,GAAGkH,MAAM,CAAClH,MAAM;UACtBW,MAAM,GAAGuG,MAAM,CAACvG,MAAM;QACxB,OAAOpK,oBAAoB,CAACrC,QAAQ,CAACrB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,aAAa,CAAC6I,YAAY,CAAC,EAAEvG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1HkE,KAAK,EAAExF,cAAc,CAACsB,KAAK,EAAE,IAAI,CAAC;UAClCqP,OAAO,EAAE;YACPnP,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJgI,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV;QACF,CAAC,CAAC,CAAC,EAAEW,MAAM,CAACF,GAAG,EAAEE,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACX,MAAM,CAAC;MAC9C,CAAC,CAAC;MACFjN,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,oBAAoB,EAAE,UAAUxI,IAAI,EAAE;QACnF,OAAO1F,cAAc,CAAC0F,IAAI,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;MACFjJ,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,cAAc,EAAE,UAAU2C,OAAO,EAAE;QAChF,IAAIC,YAAY,GAAG5C,KAAK,CAACzJ,KAAK;UAC5BmE,eAAe,GAAGkI,YAAY,CAAClI,eAAe;UAC9ChD,gBAAgB,GAAGkL,YAAY,CAAClL,gBAAgB;UAChDD,aAAa,GAAGmL,YAAY,CAACnL,aAAa;UAC1C0E,MAAM,GAAGyG,YAAY,CAACzG,MAAM;UAC5BxE,kBAAkB,GAAGiL,YAAY,CAACjL,kBAAkB;QACtD,IAAIkL,gBAAgB,GAAG7C,KAAK,CAAC8C,mBAAmB,CAAC,CAAC;QAClD,IAAI,CAACH,OAAO,IAAI,CAACA,OAAO,CAACpY,KAAK,CAACwY,MAAM,IAAI,CAACrI,eAAe,IAAI,CAAChD,gBAAgB,IAAI8E,SAAS,KAAK,cAAc,IAAIqG,gBAAgB,KAAK,MAAM,EAAE;UAC7I,OAAO,IAAI;QACb;QACA,IAAIzO,MAAM,GAAG4L,KAAK,CAACzV,KAAK,CAAC6J,MAAM;QAC/B,IAAI4O,SAAS;QACb,IAAIC,UAAU,GAAGlT,KAAK;QACtB,IAAIyM,SAAS,KAAK,cAAc,EAAE;UAChCwG,SAAS,GAAGtL,gBAAgB;UAC5BuL,UAAU,GAAGjT,KAAK;QACpB,CAAC,MAAM,IAAIwM,SAAS,KAAK,UAAU,EAAE;UACnCwG,SAAS,GAAGhD,KAAK,CAACkD,kBAAkB,CAAC,CAAC;UACtCD,UAAU,GAAG7S,SAAS;QACxB,CAAC,MAAM,IAAIgE,MAAM,KAAK,QAAQ,EAAE;UAC9B,IAAI+O,qBAAqB,GAAGnD,KAAK,CAACoD,eAAe,CAAC,CAAC;YACjDnO,EAAE,GAAGkO,qBAAqB,CAAClO,EAAE;YAC7BC,EAAE,GAAGiO,qBAAqB,CAACjO,EAAE;YAC7BZ,MAAM,GAAG6O,qBAAqB,CAAC7O,MAAM;YACrC+O,UAAU,GAAGF,qBAAqB,CAACE,UAAU;YAC7CC,QAAQ,GAAGH,qBAAqB,CAACG,QAAQ;UAC3CN,SAAS,GAAG;YACV/N,EAAE,EAAEA,EAAE;YACNC,EAAE,EAAEA,EAAE;YACNmO,UAAU,EAAEA,UAAU;YACtBC,QAAQ,EAAEA,QAAQ;YAClBC,WAAW,EAAEjP,MAAM;YACnBkP,WAAW,EAAElP;UACf,CAAC;UACD2O,UAAU,GAAGhT,MAAM;QACrB,CAAC,MAAM;UACL+S,SAAS,GAAG;YACVS,MAAM,EAAEzD,KAAK,CAACoD,eAAe,CAAC;UAChC,CAAC;UACDH,UAAU,GAAGlT,KAAK;QACpB;QACA,IAAIxG,GAAG,GAAGoZ,OAAO,CAACpZ,GAAG,IAAI,kBAAkB;QAC3C,IAAIma,WAAW,GAAGrV,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACxEsV,MAAM,EAAE,MAAM;UACdC,aAAa,EAAE;QACjB,CAAC,EAAEzH,MAAM,CAAC,EAAE6G,SAAS,CAAC,EAAEnS,WAAW,CAAC8R,OAAO,CAACpY,KAAK,CAACwY,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC9DlM,OAAO,EAAEY,aAAa;UACtBoM,YAAY,EAAElM,kBAAkB;UAChCpO,GAAG,EAAEA,GAAG;UACRua,SAAS,EAAE;QACb,CAAC,CAAC;QACF,OAAO,aAAavU,cAAc,CAACoT,OAAO,CAACpY,KAAK,CAACwY,MAAM,CAAC,GAAG,aAAazT,YAAY,CAACqT,OAAO,CAACpY,KAAK,CAACwY,MAAM,EAAEW,WAAW,CAAC,GAAG,aAAalU,aAAa,CAACyT,UAAU,EAAES,WAAW,CAAC;MAC/K,CAAC,CAAC;MACFnV,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QACvG,IAAIqB,QAAQ,GAAGxP,IAAI,CAACic,OAAO,EAAE,eAAe,CAAC;QAC7C,IAAIvK,OAAO,GAAG1R,IAAI,CAACsZ,KAAK,CAACzJ,KAAK,EAAE,EAAE,CAACT,MAAM,CAACI,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI8N,UAAU,GAAG5L,OAAO,IAAIA,OAAO,CAACuK,OAAO,CAACpY,KAAK,CAAC,EAAE,CAACuL,MAAM,CAACI,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QAC7E,OAAO,aAAa5G,YAAY,CAACqT,OAAO,EAAEtU,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2V,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;UACzFF,SAAS,EAAE5N,QAAQ;UACnB3M,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,EAAE,CAACuM,MAAM,CAACiO,WAAW,EAAE,GAAG,CAAC,CAACjO,MAAM,CAACjB,KAAK,CAAC;UAC7DyC,KAAK,EAAExF,cAAc,CAACkS,UAAU,EAAE,IAAI;QACxC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACFzV,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,aAAa,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QACnG,IAAIuG,QAAQ,GAAG4E,KAAK,CAACzJ,KAAK,CAAC6E,QAAQ;QACnC,IAAI6C,OAAO,GAAG7C,QAAQ,CAACuH,OAAO,CAACpY,KAAK,CAAC0Z,OAAO,CAAC;QAC7C,OAAOjE,KAAK,CAACkE,UAAU,CAACjG,OAAO,EAAE0E,OAAO,EAAEoB,WAAW,EAAElP,KAAK,CAAC;MAC/D,CAAC,CAAC;MACFtG,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,aAAa,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QACnG,IAAIyG,QAAQ,GAAG0E,KAAK,CAACzJ,KAAK,CAAC+E,QAAQ;QACnC,IAAI2C,OAAO,GAAG3C,QAAQ,CAACqH,OAAO,CAACpY,KAAK,CAAC4Z,OAAO,CAAC;QAC7C,OAAOnE,KAAK,CAACkE,UAAU,CAACjG,OAAO,EAAE0E,OAAO,EAAEoB,WAAW,EAAElP,KAAK,CAAC;MAC/D,CAAC,CAAC;MACFtG,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,YAAY,EAAE,UAAU2C,OAAO,EAAE;QAC9E,IAAIyB,YAAY,GAAGpE,KAAK,CAACzJ,KAAK;UAC5B6E,QAAQ,GAAGgJ,YAAY,CAAChJ,QAAQ;UAChCE,QAAQ,GAAG8I,YAAY,CAAC9I,QAAQ;UAChCa,MAAM,GAAGiI,YAAY,CAACjI,MAAM;QAC9B,IAAIkI,WAAW,GAAGrE,KAAK,CAACzV,KAAK;UAC3BgR,KAAK,GAAG8I,WAAW,CAAC9I,KAAK;UACzBC,MAAM,GAAG6I,WAAW,CAAC7I,MAAM;QAC7B,IAAIrI,KAAK,GAAGjC,qBAAqB,CAACkK,QAAQ,CAAC;QAC3C,IAAIkJ,qBAAqB,GAAGhe,KAAK,CAACgV,QAAQ,EAAE,UAAU9D,IAAI,EAAE;UAC1D,OAAOnR,MAAM,CAACmR,IAAI,CAACxB,MAAM,EAAExC,OAAO,CAAC;QACrC,CAAC,CAAC;QACF,IAAIJ,KAAK,GAAGkR,qBAAqB,IAAIpT,qBAAqB,CAACoK,QAAQ,CAAC;QACpE,IAAI/Q,KAAK,GAAGoY,OAAO,CAACpY,KAAK,IAAI,CAAC,CAAC;QAC/B,OAAO,aAAa+E,YAAY,CAACqT,OAAO,EAAE;UACxCpZ,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,MAAM;UAC1B+J,CAAC,EAAEjC,QAAQ,CAAC9G,KAAK,CAAC+I,CAAC,CAAC,GAAG/I,KAAK,CAAC+I,CAAC,GAAG6I,MAAM,CAACL,IAAI;UAC5CvI,CAAC,EAAElC,QAAQ,CAAC9G,KAAK,CAACgJ,CAAC,CAAC,GAAGhJ,KAAK,CAACgJ,CAAC,GAAG4I,MAAM,CAACF,GAAG;UAC3CV,KAAK,EAAElK,QAAQ,CAAC9G,KAAK,CAACgR,KAAK,CAAC,GAAGhR,KAAK,CAACgR,KAAK,GAAGY,MAAM,CAACZ,KAAK;UACzDC,MAAM,EAAEnK,QAAQ,CAAC9G,KAAK,CAACiR,MAAM,CAAC,GAAGjR,KAAK,CAACiR,MAAM,GAAGW,MAAM,CAACX,MAAM;UAC7DrI,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZ+I,MAAM,EAAEA,MAAM;UACdoI,UAAU,EAAEhJ,KAAK;UACjBiJ,WAAW,EAAEhJ,MAAM;UACnBiJ,4BAA4B,EAAEla,KAAK,CAACka,4BAA4B,IAAIzE,KAAK,CAACyE,4BAA4B;UACtGC,8BAA8B,EAAEna,KAAK,CAACma,8BAA8B,IAAI1E,KAAK,CAAC0E;QAChF,CAAC,CAAC;MACJ,CAAC,CAAC;MACFnW,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAU2C,OAAO,EAAE;QACnF,IAAIgC,cAAc,GAAGhC,OAAO,CAACpY,KAAK;UAChCqa,WAAW,GAAGD,cAAc,CAACC,WAAW;UACxCC,WAAW,GAAGF,cAAc,CAACE,WAAW;UACxCC,WAAW,GAAGH,cAAc,CAACG,WAAW;QAC1C,IAAIC,YAAY,GAAG/E,KAAK,CAACzJ,KAAK;UAC5ByO,aAAa,GAAGD,YAAY,CAACC,aAAa;UAC1CC,YAAY,GAAGF,YAAY,CAACE,YAAY;QAC1C,IAAIC,UAAU,GAAGhU,qBAAqB,CAAC8T,aAAa,CAAC;QACrD,IAAIG,SAAS,GAAGjU,qBAAqB,CAAC+T,YAAY,CAAC;QACnD,IAAIhQ,EAAE,GAAGkQ,SAAS,CAAClQ,EAAE;UACnBC,EAAE,GAAGiQ,SAAS,CAACjQ,EAAE;UACjBqO,WAAW,GAAG4B,SAAS,CAAC5B,WAAW;UACnCC,WAAW,GAAG2B,SAAS,CAAC3B,WAAW;QACrC,OAAO,aAAalU,YAAY,CAACqT,OAAO,EAAE;UACxCkC,WAAW,EAAE/d,QAAQ,CAAC+d,WAAW,CAAC,GAAGA,WAAW,GAAG/S,cAAc,CAACqT,SAAS,EAAE,IAAI,CAAC,CAACvG,GAAG,CAAC,UAAUlK,KAAK,EAAE;YACtG,OAAOA,KAAK,CAACI,UAAU;UACzB,CAAC,CAAC;UACFgQ,WAAW,EAAEhe,QAAQ,CAACge,WAAW,CAAC,GAAGA,WAAW,GAAGhT,cAAc,CAACoT,UAAU,EAAE,IAAI,CAAC,CAACtG,GAAG,CAAC,UAAUlK,KAAK,EAAE;YACvG,OAAOA,KAAK,CAACI,UAAU;UACzB,CAAC,CAAC;UACFG,EAAE,EAAEA,EAAE;UACNC,EAAE,EAAEA,EAAE;UACNqO,WAAW,EAAEA,WAAW;UACxBC,WAAW,EAAEA,WAAW;UACxBja,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,YAAY;UAChCqb,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;MACFrW,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,cAAc,EAAE,YAAY;QACzE,IAAIL,uBAAuB,GAAGK,KAAK,CAACzJ,KAAK,CAACoJ,uBAAuB;QACjE,IAAIyF,YAAY,GAAGpF,KAAK,CAACzV,KAAK;UAC5B0N,QAAQ,GAAGmN,YAAY,CAACnN,QAAQ;UAChCsD,KAAK,GAAG6J,YAAY,CAAC7J,KAAK;UAC1BC,MAAM,GAAG4J,YAAY,CAAC5J,MAAM;QAC9B,IAAIC,MAAM,GAAGuE,KAAK,CAACzV,KAAK,CAACkR,MAAM,IAAI,CAAC,CAAC;QACrC,IAAI4J,WAAW,GAAG9J,KAAK,IAAIE,MAAM,CAACK,IAAI,IAAI,CAAC,CAAC,IAAIL,MAAM,CAACM,KAAK,IAAI,CAAC,CAAC;QAClE,IAAIxR,KAAK,GAAGqH,cAAc,CAAC;UACzBqG,QAAQ,EAAEA,QAAQ;UAClB0H,uBAAuB,EAAEA,uBAAuB;UAChD0F,WAAW,EAAEA,WAAW;UACxBtI,aAAa,EAAEA;QACjB,CAAC,CAAC;QACF,IAAI,CAACxS,KAAK,EAAE;UACV,OAAO,IAAI;QACb;QACA,IAAI+K,IAAI,GAAG/K,KAAK,CAAC+K,IAAI;UACnBgQ,UAAU,GAAG5b,wBAAwB,CAACa,KAAK,EAAExD,SAAS,CAAC;QACzD,OAAO,aAAauI,YAAY,CAACgG,IAAI,EAAEjH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiX,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;UACtFf,UAAU,EAAEhJ,KAAK;UACjBiJ,WAAW,EAAEhJ,MAAM;UACnBC,MAAM,EAAEA,MAAM;UACd8J,GAAG,EAAE,SAASA,GAAGA,CAACC,MAAM,EAAE;YACxBxF,KAAK,CAACyF,cAAc,GAAGD,MAAM;UAC/B,CAAC;UACDE,YAAY,EAAE1F,KAAK,CAAC2F;QACtB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACFpX,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,eAAe,EAAE,YAAY;QAC1E,IAAI/H,QAAQ,GAAG+H,KAAK,CAACzV,KAAK,CAAC0N,QAAQ;QACnC,IAAI2N,WAAW,GAAGtV,eAAe,CAAC2H,QAAQ,EAAEpI,OAAO,CAAC;QACpD,IAAI,CAAC+V,WAAW,EAAE;UAChB,OAAO,IAAI;QACb;QACA,IAAIC,YAAY,GAAG7F,KAAK,CAACzJ,KAAK;UAC5BmE,eAAe,GAAGmL,YAAY,CAACnL,eAAe;UAC9ChD,gBAAgB,GAAGmO,YAAY,CAACnO,gBAAgB;UAChDD,aAAa,GAAGoO,YAAY,CAACpO,aAAa;UAC1ChB,WAAW,GAAGoP,YAAY,CAACpP,WAAW;UACtC0F,MAAM,GAAG0J,YAAY,CAAC1J,MAAM;QAC9B,OAAO,aAAa7M,YAAY,CAACsW,WAAW,EAAE;UAC5CnD,OAAO,EAAEpU,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8N,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACpD7I,CAAC,EAAE6I,MAAM,CAACL,IAAI;YACdvI,CAAC,EAAE4I,MAAM,CAACF;UACZ,CAAC,CAAC;UACF6J,MAAM,EAAEpL,eAAe;UACvBqL,KAAK,EAAEtP,WAAW;UAClBI,OAAO,EAAE6D,eAAe,GAAGjD,aAAa,GAAG,EAAE;UAC7C3C,UAAU,EAAE4C;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACFnJ,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,aAAa,EAAE,UAAU2C,OAAO,EAAE;QAC/E,IAAIqD,YAAY,GAAGhG,KAAK,CAACzV,KAAK;UAC5BkR,MAAM,GAAGuK,YAAY,CAACvK,MAAM;UAC5BrG,IAAI,GAAG4Q,YAAY,CAAC5Q,IAAI;QAC1B,IAAI6Q,YAAY,GAAGjG,KAAK,CAACzJ,KAAK;UAC5B4F,MAAM,GAAG8J,YAAY,CAAC9J,MAAM;UAC5B3G,cAAc,GAAGyQ,YAAY,CAACzQ,cAAc;UAC5CC,YAAY,GAAGwQ,YAAY,CAACxQ,YAAY;UACxC0H,QAAQ,GAAG8I,YAAY,CAAC9I,QAAQ;;QAElC;QACA,OAAO,aAAa7N,YAAY,CAACqT,OAAO,EAAE;UACxCpZ,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,iBAAiB;UACrC2c,QAAQ,EAAErU,oBAAoB,CAACmO,KAAK,CAACmG,iBAAiB,EAAE,IAAI,EAAExD,OAAO,CAACpY,KAAK,CAAC2b,QAAQ,CAAC;UACrF9Q,IAAI,EAAEA,IAAI;UACV9B,CAAC,EAAEjC,QAAQ,CAACsR,OAAO,CAACpY,KAAK,CAAC+I,CAAC,CAAC,GAAGqP,OAAO,CAACpY,KAAK,CAAC+I,CAAC,GAAG6I,MAAM,CAACL,IAAI;UAC5DvI,CAAC,EAAElC,QAAQ,CAACsR,OAAO,CAACpY,KAAK,CAACgJ,CAAC,CAAC,GAAGoP,OAAO,CAACpY,KAAK,CAACgJ,CAAC,GAAG4I,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACX,MAAM,GAAGW,MAAM,CAACC,WAAW,IAAIX,MAAM,CAACS,MAAM,IAAI,CAAC,CAAC;UACvHX,KAAK,EAAElK,QAAQ,CAACsR,OAAO,CAACpY,KAAK,CAACgR,KAAK,CAAC,GAAGoH,OAAO,CAACpY,KAAK,CAACgR,KAAK,GAAGY,MAAM,CAACZ,KAAK;UACzEf,UAAU,EAAEhF,cAAc;UAC1BiF,QAAQ,EAAEhF,YAAY;UACtB0H,QAAQ,EAAE,QAAQ,CAACrH,MAAM,CAACqH,QAAQ;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC;MACF5O,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,wBAAwB,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QAC9G,IAAI,CAAC8N,OAAO,EAAE;UACZ,OAAO,IAAI;QACb;QACA,IAAIyD,qBAAqB,GAAG7Z,sBAAsB,CAACyT,KAAK,CAAC;UACvDqG,UAAU,GAAGD,qBAAqB,CAACC,UAAU;QAC/C,IAAIC,YAAY,GAAGtG,KAAK,CAACzJ,KAAK;UAC5B6E,QAAQ,GAAGkL,YAAY,CAAClL,QAAQ;UAChCE,QAAQ,GAAGgL,YAAY,CAAChL,QAAQ;UAChCa,MAAM,GAAGmK,YAAY,CAACnK,MAAM;QAC9B,IAAIoK,eAAe,GAAG5D,OAAO,CAACpY,KAAK;UACjC0Z,OAAO,GAAGsC,eAAe,CAACtC,OAAO;UACjCE,OAAO,GAAGoC,eAAe,CAACpC,OAAO;QACnC,OAAO,aAAa7U,YAAY,CAACqT,OAAO,EAAE;UACxCpZ,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,EAAE,CAACuM,MAAM,CAACiO,WAAW,EAAE,GAAG,CAAC,CAACjO,MAAM,CAACjB,KAAK,CAAC;UAC7D1B,KAAK,EAAEiI,QAAQ,CAAC6I,OAAO,CAAC;UACxB7Q,KAAK,EAAEkI,QAAQ,CAAC6I,OAAO,CAAC;UACxB1B,OAAO,EAAE;YACPnP,CAAC,EAAE6I,MAAM,CAACL,IAAI;YACdvI,CAAC,EAAE4I,MAAM,CAACF,GAAG;YACbV,KAAK,EAAEY,MAAM,CAACZ,KAAK;YACnBC,MAAM,EAAEW,MAAM,CAACX;UACjB,CAAC;UACD6K,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACF9X,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,oBAAoB,EAAE,UAAUwG,MAAM,EAAE;QACrF,IAAIlR,IAAI,GAAGkR,MAAM,CAAClR,IAAI;UACpBmR,WAAW,GAAGD,MAAM,CAACC,WAAW;UAChCC,SAAS,GAAGF,MAAM,CAACE,SAAS;UAC5BzH,UAAU,GAAGuH,MAAM,CAACvH,UAAU;UAC9B0H,OAAO,GAAGH,MAAM,CAACG,OAAO;QAC1B,IAAI1a,MAAM,GAAG,EAAE;QACf,IAAI1C,GAAG,GAAG+L,IAAI,CAAC/K,KAAK,CAAChB,GAAG;QACxB,IAAIqd,gBAAgB,GAAGtR,IAAI,CAACA,IAAI,CAAC/K,KAAK;UACpCsc,SAAS,GAAGD,gBAAgB,CAACC,SAAS;UACtC/P,OAAO,GAAG8P,gBAAgB,CAAC9P,OAAO;QACpC,IAAIgQ,QAAQ,GAAGzY,aAAa,CAACA,aAAa,CAAC;UACzCwG,KAAK,EAAEoK,UAAU;UACjBnI,OAAO,EAAEA,OAAO;UAChB7B,EAAE,EAAEwR,WAAW,CAACnT,CAAC;UACjB4B,EAAE,EAAEuR,WAAW,CAAClT,CAAC;UACjBwT,CAAC,EAAE,CAAC;UACJC,IAAI,EAAExV,yBAAyB,CAAC8D,IAAI,CAACA,IAAI,CAAC;UAC1C2R,WAAW,EAAE,CAAC;UACdtD,MAAM,EAAE,MAAM;UACd9M,OAAO,EAAE4P,WAAW,CAAC5P,OAAO;UAC5BjO,KAAK,EAAE6d,WAAW,CAAC7d,KAAK;UACxBW,GAAG,EAAE,EAAE,CAACuM,MAAM,CAACvM,GAAG,EAAE,eAAe,CAAC,CAACuM,MAAM,CAACmJ,UAAU;QACxD,CAAC,EAAEpO,WAAW,CAACgW,SAAS,CAAC,CAAC,EAAE5T,kBAAkB,CAAC4T,SAAS,CAAC,CAAC;QAC1D5a,MAAM,CAACtD,IAAI,CAACkX,uBAAuB,CAACqH,eAAe,CAACL,SAAS,EAAEC,QAAQ,CAAC,CAAC;QACzE,IAAIJ,SAAS,EAAE;UACbza,MAAM,CAACtD,IAAI,CAACkX,uBAAuB,CAACqH,eAAe,CAACL,SAAS,EAAExY,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyY,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5G7R,EAAE,EAAEyR,SAAS,CAACpT,CAAC;YACf4B,EAAE,EAAEwR,SAAS,CAACnT,CAAC;YACfhK,GAAG,EAAE,EAAE,CAACuM,MAAM,CAACvM,GAAG,EAAE,aAAa,CAAC,CAACuM,MAAM,CAACmJ,UAAU;UACtD,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,MAAM,IAAI0H,OAAO,EAAE;UAClB1a,MAAM,CAACtD,IAAI,CAAC,IAAI,CAAC;QACnB;QACA,OAAOsD,MAAM;MACf,CAAC,CAAC;MACFsC,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,oBAAoB,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QAC1G,IAAIS,IAAI,GAAG0K,KAAK,CAACmH,gBAAgB,CAACxE,OAAO,EAAEoB,WAAW,EAAElP,KAAK,CAAC;QAC9D,IAAI,CAACS,IAAI,EAAE;UACT,OAAO,IAAI;QACb;QACA,IAAIuN,gBAAgB,GAAG7C,KAAK,CAAC8C,mBAAmB,CAAC,CAAC;QAClD,IAAIsE,YAAY,GAAGpH,KAAK,CAACzJ,KAAK;UAC5BmE,eAAe,GAAG0M,YAAY,CAAC1M,eAAe;UAC9ChE,WAAW,GAAG0Q,YAAY,CAAC1Q,WAAW;UACtCiB,kBAAkB,GAAGyP,YAAY,CAACzP,kBAAkB;UACpDlB,WAAW,GAAG2Q,YAAY,CAAC3Q,WAAW;QACxC,IAAIwB,QAAQ,GAAG+H,KAAK,CAACzV,KAAK,CAAC0N,QAAQ;QACnC,IAAI2N,WAAW,GAAGtV,eAAe,CAAC2H,QAAQ,EAAEpI,OAAO,CAAC;QACpD,IAAIwX,YAAY,GAAG/R,IAAI,CAAC/K,KAAK;UAC3BkZ,MAAM,GAAG4D,YAAY,CAAC5D,MAAM;UAC5BkD,OAAO,GAAGU,YAAY,CAACV,OAAO;UAC9BW,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAClC,IAAIC,iBAAiB,GAAGjS,IAAI,CAACA,IAAI,CAAC/K,KAAK;UACrCsc,SAAS,GAAGU,iBAAiB,CAACV,SAAS;UACvCjQ,IAAI,GAAG2Q,iBAAiB,CAAC3Q,IAAI;QAC/B,IAAI4Q,SAAS,GAAG,CAAC5Q,IAAI,IAAI8D,eAAe,IAAIkL,WAAW,IAAIiB,SAAS,IAAIlP,kBAAkB,IAAI,CAAC;QAC/F,IAAI8P,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI5E,gBAAgB,KAAK,MAAM,IAAI+C,WAAW,IAAIA,WAAW,CAACrb,KAAK,CAACmd,OAAO,KAAK,OAAO,EAAE;UACvFD,UAAU,GAAG;YACX1F,OAAO,EAAElQ,oBAAoB,CAACmO,KAAK,CAAC2H,oBAAoB,EAAE,IAAI,EAAEhF,OAAO,CAACpY,KAAK,CAACqd,OAAO;UACvF,CAAC;QACH,CAAC,MAAM,IAAI/E,gBAAgB,KAAK,MAAM,EAAE;UACtC4E,UAAU,GAAG;YACXhG,YAAY,EAAE5P,oBAAoB,CAACmO,KAAK,CAAC6H,oBAAoB,EAAE,IAAI,EAAElF,OAAO,CAACpY,KAAK,CAACkX,YAAY,CAAC;YAChGZ,YAAY,EAAEhP,oBAAoB,CAACmO,KAAK,CAAC2H,oBAAoB,EAAE,IAAI,EAAEhF,OAAO,CAACpY,KAAK,CAACsW,YAAY;UACjG,CAAC;QACH;QACA,IAAIiH,aAAa,GAAG,aAAaxY,YAAY,CAACqT,OAAO,EAAEtU,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiH,IAAI,CAAC/K,KAAK,CAAC,EAAEkd,UAAU,CAAC,CAAC;QAChH,SAASM,eAAeA,CAACrT,KAAK,EAAE;UAC9B;UACA,OAAO,OAAOgC,WAAW,CAACI,OAAO,KAAK,UAAU,GAAGJ,WAAW,CAACI,OAAO,CAACpC,KAAK,CAACmC,OAAO,CAAC,GAAG,IAAI;QAC9F;QACA,IAAI2Q,SAAS,EAAE;UACb,IAAIf,WAAW,EAAEC,SAAS;UAC1B,IAAIhQ,WAAW,CAACI,OAAO,IAAI,CAACJ,WAAW,CAACK,uBAAuB,EAAE;YAC/D;YACA,IAAIiR,YAAY,GAAG,OAAOtR,WAAW,CAACI,OAAO,KAAK,UAAU,GAAGiR,eAAe,GAAG,UAAU,CAACjS,MAAM,CAACY,WAAW,CAACI,OAAO,CAACxJ,QAAQ,CAAC,CAAC,CAAC;YAClImZ,WAAW,GAAGnV,gBAAgB,CAACmS,MAAM,EAAEuE,YAAY,EAAEvR,WAAW,CAAC;YACjEiQ,SAAS,GAAGC,OAAO,IAAIW,QAAQ,IAAIhW,gBAAgB,CAACgW,QAAQ,EAAEU,YAAY,EAAEvR,WAAW,CAAC;UAC1F,CAAC,MAAM;YACLgQ,WAAW,GAAGhD,MAAM,CAAC9L,kBAAkB,CAAC;YACxC+O,SAAS,GAAGC,OAAO,IAAIW,QAAQ,IAAIA,QAAQ,CAAC3P,kBAAkB,CAAC;UACjE;UACA,IAAI,CAAC/Q,MAAM,CAAC6f,WAAW,CAAC,EAAE;YACxB,OAAO,CAACqB,aAAa,CAAC,CAAChS,MAAM,CAAC/I,kBAAkB,CAACiT,KAAK,CAACiI,kBAAkB,CAAC;cACxE3S,IAAI,EAAEA,IAAI;cACVmR,WAAW,EAAEA,WAAW;cACxBC,SAAS,EAAEA,SAAS;cACpBzH,UAAU,EAAEtH,kBAAkB;cAC9BgP,OAAO,EAAEA;YACX,CAAC,CAAC,CAAC,CAAC;UACN;QACF;QACA,IAAIA,OAAO,EAAE;UACX,OAAO,CAACmB,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;QACpC;QACA,OAAO,CAACA,aAAa,EAAE,IAAI,CAAC;MAC9B,CAAC,CAAC;MACFvZ,eAAe,CAAChC,sBAAsB,CAACyT,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAU2C,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QACxG,OAAO,aAAavF,YAAY,CAACqT,OAAO,EAAEtU,aAAa,CAACA,aAAa,CAAC;UACpE9E,GAAG,EAAE,sBAAsB,CAACuM,MAAM,CAACjB,KAAK;QAC1C,CAAC,EAAEmL,KAAK,CAACzV,KAAK,CAAC,EAAEyV,KAAK,CAACzJ,KAAK,CAAC,CAAC;MAChC,CAAC,CAAC;MACFyJ,KAAK,CAACQ,aAAa,GAAG5Z,MAAM,CAACmZ,MAAM,CAACnE,EAAE,CAAC,GAAGxK,QAAQ,CAAC,UAAU,CAAC,GAAG2O,MAAM,CAACnE,EAAE;MAC1EoE,KAAK,CAACqG,UAAU,GAAG,EAAE,CAACvQ,MAAM,CAACkK,KAAK,CAACQ,aAAa,EAAE,OAAO,CAAC;MAC1D,IAAIT,MAAM,CAACmI,aAAa,EAAE;QACxBlI,KAAK,CAACwB,uBAAuB,GAAGhb,SAAS,CAACwZ,KAAK,CAACwB,uBAAuB,EAAEzB,MAAM,CAACmI,aAAa,CAAC;MAChG;MACAlI,KAAK,CAACzJ,KAAK,GAAG,CAAC,CAAC;MAChB,OAAOyJ,KAAK;IACd;IACAlV,YAAY,CAAC+U,uBAAuB,EAAE,CAAC;MACrCtW,GAAG,EAAE,mBAAmB;MACxBX,KAAK,EAAE,SAASuf,iBAAiBA,CAAA,EAAG;QAClC,IAAI,CAACvhB,MAAM,CAAC,IAAI,CAAC2D,KAAK,CAACgW,MAAM,CAAC,EAAE;UAC9B,IAAI,CAAC6H,WAAW,CAAC,CAAC;QACpB;MACF;IACF,CAAC,EAAE;MACD7e,GAAG,EAAE,oBAAoB;MACzBX,KAAK,EAAE,SAASyf,kBAAkBA,CAACC,SAAS,EAAE;QAC5C;QACA,IAAI1hB,MAAM,CAAC0hB,SAAS,CAAC/H,MAAM,CAAC,IAAI,CAAC3Z,MAAM,CAAC,IAAI,CAAC2D,KAAK,CAACgW,MAAM,CAAC,EAAE;UAC1D,IAAI,CAAC6H,WAAW,CAAC,CAAC;QACpB;QACA;QACA,IAAI,CAACxhB,MAAM,CAAC0hB,SAAS,CAAC/H,MAAM,CAAC,IAAI3Z,MAAM,CAAC,IAAI,CAAC2D,KAAK,CAACgW,MAAM,CAAC,EAAE;UAC1D,IAAI,CAACgI,cAAc,CAAC,CAAC;QACvB;MACF;IACF,CAAC,EAAE;MACDhf,GAAG,EAAE,sBAAsB;MAC3BX,KAAK,EAAE,SAAS4f,oBAAoBA,CAAA,EAAG;QACrC,IAAI,CAAC/H,YAAY,CAAC,CAAC;QACnB,IAAI,CAAC7Z,MAAM,CAAC,IAAI,CAAC2D,KAAK,CAACgW,MAAM,CAAC,EAAE;UAC9B,IAAI,CAACgI,cAAc,CAAC,CAAC;QACvB;QACA,IAAI,CAAC7G,oCAAoC,CAAC,CAAC;MAC7C;IACF,CAAC,EAAE;MACDnY,GAAG,EAAE,sCAAsC;MAC3CX,KAAK,EAAE,SAAS8Y,oCAAoCA,CAAA,EAAG;QACrD,IAAI,OAAO,IAAI,CAACF,uBAAuB,CAACiH,MAAM,KAAK,UAAU,EAAE;UAC7D,IAAI,CAACjH,uBAAuB,CAACiH,MAAM,CAAC,CAAC;QACvC;MACF;IACF,CAAC,EAAE;MACDlf,GAAG,EAAE,qBAAqB;MAC1BX,KAAK,EAAE,SAASka,mBAAmBA,CAAA,EAAG;QACpC,IAAI8C,WAAW,GAAGtV,eAAe,CAAC,IAAI,CAAC/F,KAAK,CAAC0N,QAAQ,EAAEpI,OAAO,CAAC;QAC/D,IAAI+V,WAAW,IAAI/e,UAAU,CAAC+e,WAAW,CAACrb,KAAK,CAACme,MAAM,CAAC,EAAE;UACvD,IAAIC,SAAS,GAAG/C,WAAW,CAACrb,KAAK,CAACme,MAAM,GAAG,MAAM,GAAG,MAAM;UAC1D,OAAO7L,yBAAyB,CAAC9S,OAAO,CAAC4e,SAAS,CAAC,IAAI,CAAC,GAAGA,SAAS,GAAGhM,uBAAuB;QAChG;QACA,OAAOA,uBAAuB;MAChC;;MAEA;AACN;AACA;AACA;AACA;IACI,CAAC,EAAE;MACDpT,GAAG,EAAE,cAAc;MACnBX,KAAK,EAAE,SAASmY,YAAYA,CAACa,KAAK,EAAE;QAClC,IAAI,CAAC,IAAI,CAACgH,SAAS,EAAE;UACnB,OAAO,IAAI;QACb;QACA,IAAIC,eAAe,GAAG7X,SAAS,CAAC,IAAI,CAAC4X,SAAS,CAAC;QAC/C,IAAI/b,CAAC,GAAGoE,wBAAwB,CAAC2Q,KAAK,EAAEiH,eAAe,CAAC;QACxD,IAAI1U,QAAQ,GAAG,IAAI,CAAC2U,OAAO,CAACjc,CAAC,CAACsK,MAAM,EAAEtK,CAAC,CAACuK,MAAM,CAAC;QAC/C,IAAI,CAACjD,QAAQ,EAAE;UACb,OAAO,IAAI;QACb;QACA,IAAI4U,YAAY,GAAG,IAAI,CAACxS,KAAK;UAC3B6E,QAAQ,GAAG2N,YAAY,CAAC3N,QAAQ;UAChCE,QAAQ,GAAGyN,YAAY,CAACzN,QAAQ;QAClC,IAAIuH,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACjD,IAAID,gBAAgB,KAAK,MAAM,IAAIzH,QAAQ,IAAIE,QAAQ,EAAE;UACvD,IAAI0N,MAAM,GAAG9X,qBAAqB,CAACkK,QAAQ,CAAC,CAAC5C,KAAK;UAClD,IAAIyQ,MAAM,GAAG/X,qBAAqB,CAACoK,QAAQ,CAAC,CAAC9C,KAAK;UAClD,IAAI0Q,MAAM,GAAGF,MAAM,IAAIA,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACG,MAAM,CAACtc,CAAC,CAACsK,MAAM,CAAC,GAAG,IAAI;UACrE,IAAIiS,MAAM,GAAGH,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACE,MAAM,CAACtc,CAAC,CAACuK,MAAM,CAAC,GAAG,IAAI;UACrE,OAAO/I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAExB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC7Cqc,MAAM,EAAEA,MAAM;YACdE,MAAM,EAAEA;UACV,CAAC,CAAC;QACJ;QACA,IAAIC,WAAW,GAAGpS,cAAc,CAAC,IAAI,CAACV,KAAK,EAAE,IAAI,CAAChM,KAAK,CAAC6K,IAAI,EAAE,IAAI,CAAC7K,KAAK,CAAC6J,MAAM,EAAED,QAAQ,CAAC;QAC1F,IAAIkV,WAAW,EAAE;UACf,OAAOhb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAExB,CAAC,CAAC,EAAEwc,WAAW,CAAC;QACzD;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD9f,GAAG,EAAE,oBAAoB;MACzBX,KAAK,EAAE,SAASsa,kBAAkBA,CAAA,EAAG;QACnC,IAAI9O,MAAM,GAAG,IAAI,CAAC7J,KAAK,CAAC6J,MAAM;QAC9B,IAAIkV,aAAa,GAAG,IAAI,CAAC/S,KAAK;UAC5BmB,gBAAgB,GAAG4R,aAAa,CAAC5R,gBAAgB;UACjDyE,MAAM,GAAGmN,aAAa,CAACnN,MAAM;UAC7BjC,mBAAmB,GAAGoP,aAAa,CAACpP,mBAAmB;QACzD,IAAIqP,QAAQ,GAAGrP,mBAAmB,GAAG,CAAC;QACtC,OAAO;UACLyJ,MAAM,EAAE,MAAM;UACdqD,IAAI,EAAE,MAAM;UACZ1T,CAAC,EAAEc,MAAM,KAAK,YAAY,GAAGsD,gBAAgB,CAACpE,CAAC,GAAGiW,QAAQ,GAAGpN,MAAM,CAACL,IAAI,GAAG,GAAG;UAC9EvI,CAAC,EAAEa,MAAM,KAAK,YAAY,GAAG+H,MAAM,CAACF,GAAG,GAAG,GAAG,GAAGvE,gBAAgB,CAACnE,CAAC,GAAGgW,QAAQ;UAC7EhO,KAAK,EAAEnH,MAAM,KAAK,YAAY,GAAG8F,mBAAmB,GAAGiC,MAAM,CAACZ,KAAK,GAAG,CAAC;UACvEC,MAAM,EAAEpH,MAAM,KAAK,YAAY,GAAG+H,MAAM,CAACX,MAAM,GAAG,CAAC,GAAGtB;QACxD,CAAC;MACH;IACF,CAAC,EAAE;MACD3Q,GAAG,EAAE,iBAAiB;MACtBX,KAAK,EAAE,SAASwa,eAAeA,CAAA,EAAG;QAChC,IAAIhP,MAAM,GAAG,IAAI,CAAC7J,KAAK,CAAC6J,MAAM;QAC9B,IAAIoV,aAAa,GAAG,IAAI,CAACjT,KAAK;UAC5BmB,gBAAgB,GAAG8R,aAAa,CAAC9R,gBAAgB;UACjDyE,MAAM,GAAGqN,aAAa,CAACrN,MAAM;QAC/B,IAAIsN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;QAClB,IAAIxV,MAAM,KAAK,YAAY,EAAE;UAC3BqV,EAAE,GAAG/R,gBAAgB,CAACpE,CAAC;UACvBqW,EAAE,GAAGF,EAAE;UACPC,EAAE,GAAGvN,MAAM,CAACF,GAAG;UACf2N,EAAE,GAAGzN,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACX,MAAM;QACjC,CAAC,MAAM,IAAIpH,MAAM,KAAK,UAAU,EAAE;UAChCsV,EAAE,GAAGhS,gBAAgB,CAACnE,CAAC;UACvBqW,EAAE,GAAGF,EAAE;UACPD,EAAE,GAAGtN,MAAM,CAACL,IAAI;UAChB6N,EAAE,GAAGxN,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACZ,KAAK;QACjC,CAAC,MAAM,IAAI,CAAC3U,MAAM,CAAC8Q,gBAAgB,CAACzC,EAAE,CAAC,IAAI,CAACrO,MAAM,CAAC8Q,gBAAgB,CAACxC,EAAE,CAAC,EAAE;UACvE,IAAId,MAAM,KAAK,SAAS,EAAE;YACxB,IAAIa,EAAE,GAAGyC,gBAAgB,CAACzC,EAAE;cAC1BC,EAAE,GAAGwC,gBAAgB,CAACxC,EAAE;cACxBqO,WAAW,GAAG7L,gBAAgB,CAAC6L,WAAW;cAC1CC,WAAW,GAAG9L,gBAAgB,CAAC8L,WAAW;cAC1CnP,KAAK,GAAGqD,gBAAgB,CAACrD,KAAK;YAChC,IAAIwV,UAAU,GAAGhX,gBAAgB,CAACoC,EAAE,EAAEC,EAAE,EAAEqO,WAAW,EAAElP,KAAK,CAAC;YAC7D,IAAIyV,UAAU,GAAGjX,gBAAgB,CAACoC,EAAE,EAAEC,EAAE,EAAEsO,WAAW,EAAEnP,KAAK,CAAC;YAC7DoV,EAAE,GAAGI,UAAU,CAACvW,CAAC;YACjBoW,EAAE,GAAGG,UAAU,CAACtW,CAAC;YACjBoW,EAAE,GAAGG,UAAU,CAACxW,CAAC;YACjBsW,EAAE,GAAGE,UAAU,CAACvW,CAAC;UACnB,CAAC,MAAM;YACL,IAAIwW,GAAG,GAAGrS,gBAAgB,CAACzC,EAAE;cAC3B+U,GAAG,GAAGtS,gBAAgB,CAACxC,EAAE;cACzBZ,MAAM,GAAGoD,gBAAgB,CAACpD,MAAM;cAChC+O,UAAU,GAAG3L,gBAAgB,CAAC2L,UAAU;cACxCC,QAAQ,GAAG5L,gBAAgB,CAAC4L,QAAQ;YACtC,IAAI2G,UAAU,GAAGpX,gBAAgB,CAACkX,GAAG,EAAEC,GAAG,EAAE1V,MAAM,EAAE+O,UAAU,CAAC;YAC/D,IAAI6G,QAAQ,GAAGrX,gBAAgB,CAACkX,GAAG,EAAEC,GAAG,EAAE1V,MAAM,EAAEgP,QAAQ,CAAC;YAC3D,OAAO;cACLG,MAAM,EAAE,CAACwG,UAAU,EAAEC,QAAQ,CAAC;cAC9BjV,EAAE,EAAE8U,GAAG;cACP7U,EAAE,EAAE8U,GAAG;cACP1V,MAAM,EAAEA,MAAM;cACd+O,UAAU,EAAEA,UAAU;cACtBC,QAAQ,EAAEA;YACZ,CAAC;UACH;QACF;QACA,OAAO,CAAC;UACNhQ,CAAC,EAAEmW,EAAE;UACLlW,CAAC,EAAEmW;QACL,CAAC,EAAE;UACDpW,CAAC,EAAEqW,EAAE;UACLpW,CAAC,EAAEqW;QACL,CAAC,CAAC;MACJ;IACF,CAAC,EAAE;MACDrgB,GAAG,EAAE,SAAS;MACdX,KAAK,EAAE,SAASkgB,OAAOA,CAACxV,CAAC,EAAEC,CAAC,EAAE;QAC5B,IAAIa,MAAM,GAAG,IAAI,CAAC7J,KAAK,CAAC6J,MAAM;QAC9B,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;UACpD,IAAI+H,MAAM,GAAG,IAAI,CAAC5F,KAAK,CAAC4F,MAAM;UAC9B,IAAIgO,SAAS,GAAG7W,CAAC,IAAI6I,MAAM,CAACL,IAAI,IAAIxI,CAAC,IAAI6I,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACZ,KAAK,IAAIhI,CAAC,IAAI4I,MAAM,CAACF,GAAG,IAAI1I,CAAC,IAAI4I,MAAM,CAACF,GAAG,GAAGE,MAAM,CAACX,MAAM;UACzH,OAAO2O,SAAS,GAAG;YACjB7W,CAAC,EAAEA,CAAC;YACJC,CAAC,EAAEA;UACL,CAAC,GAAG,IAAI;QACV;QACA,IAAI6W,aAAa,GAAG,IAAI,CAAC7T,KAAK;UAC5B0O,YAAY,GAAGmF,aAAa,CAACnF,YAAY;UACzCD,aAAa,GAAGoF,aAAa,CAACpF,aAAa;QAC7C,IAAIC,YAAY,IAAID,aAAa,EAAE;UACjC,IAAIG,SAAS,GAAGjU,qBAAqB,CAAC+T,YAAY,CAAC;UACnD,OAAOrS,eAAe,CAAC;YACrBU,CAAC,EAAEA,CAAC;YACJC,CAAC,EAAEA;UACL,CAAC,EAAE4R,SAAS,CAAC;QACf;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD5b,GAAG,EAAE,sBAAsB;MAC3BX,KAAK,EAAE,SAASyhB,oBAAoBA,CAAA,EAAG;QACrC,IAAIpS,QAAQ,GAAG,IAAI,CAAC1N,KAAK,CAAC0N,QAAQ;QAClC,IAAI4K,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACjD,IAAI8C,WAAW,GAAGtV,eAAe,CAAC2H,QAAQ,EAAEpI,OAAO,CAAC;QACpD,IAAIya,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI1E,WAAW,IAAI/C,gBAAgB,KAAK,MAAM,EAAE;UAC9C,IAAI+C,WAAW,CAACrb,KAAK,CAACmd,OAAO,KAAK,OAAO,EAAE;YACzC4C,aAAa,GAAG;cACdvI,OAAO,EAAE,IAAI,CAACwI;YAChB,CAAC;UACH,CAAC,MAAM;YACLD,aAAa,GAAG;cACdzJ,YAAY,EAAE,IAAI,CAAC2J,gBAAgB;cACnCvJ,WAAW,EAAE,IAAI,CAACoB,eAAe;cACjCZ,YAAY,EAAE,IAAI,CAACgJ,gBAAgB;cACnCC,WAAW,EAAE,IAAI,CAACC,eAAe;cACjCC,YAAY,EAAE,IAAI,CAACC,gBAAgB;cACnCC,UAAU,EAAE,IAAI,CAACC;YACnB,CAAC;UACH;QACF;QACA,IAAIC,WAAW,GAAG/X,kBAAkB,CAAC,IAAI,CAAC1I,KAAK,EAAE,IAAI,CAAC0gB,gBAAgB,CAAC;QACvE,OAAO5c,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2c,WAAW,CAAC,EAAEV,aAAa,CAAC;MACrE;;MAEA;IACF,CAAC,EAAE;MACD/gB,GAAG,EAAE,aAAa;MAClBX,KAAK,EAAE,SAASwf,WAAWA,CAAA,EAAG;QAC5BrV,WAAW,CAACmY,EAAE,CAAClY,UAAU,EAAE,IAAI,CAACmY,sBAAsB,CAAC;QACvD,IAAIpY,WAAW,CAACqY,eAAe,IAAIrY,WAAW,CAACsY,aAAa,EAAE;UAC5DtY,WAAW,CAACqY,eAAe,CAACrY,WAAW,CAACsY,aAAa,GAAG,CAAC,CAAC;QAC5D;MACF;IACF,CAAC,EAAE;MACD9hB,GAAG,EAAE,gBAAgB;MACrBX,KAAK,EAAE,SAAS2f,cAAcA,CAAA,EAAG;QAC/BxV,WAAW,CAACwV,cAAc,CAACvV,UAAU,EAAE,IAAI,CAACmY,sBAAsB,CAAC;QACnE,IAAIpY,WAAW,CAACqY,eAAe,IAAIrY,WAAW,CAACsY,aAAa,EAAE;UAC5DtY,WAAW,CAACqY,eAAe,CAACrY,WAAW,CAACsY,aAAa,GAAG,CAAC,CAAC;QAC5D;MACF;IACF,CAAC,EAAE;MACD9hB,GAAG,EAAE,kBAAkB;MACvBX,KAAK,EAAE,SAASgY,gBAAgBA,CAACxL,IAAI,EAAE;QACrC,IAAImL,MAAM,GAAG,IAAI,CAAChW,KAAK,CAACgW,MAAM;QAC9B,IAAI,CAAC3Z,MAAM,CAAC2Z,MAAM,CAAC,EAAE;UACnBxN,WAAW,CAACuY,IAAI,CAACtY,UAAU,EAAEuN,MAAM,EAAE,IAAI,CAACC,aAAa,EAAEpL,IAAI,CAAC;QAChE;MACF;IACF,CAAC,EAAE;MACD7L,GAAG,EAAE,gBAAgB;MACrBX,KAAK,EAAE,SAAS8X,cAAcA,CAACtL,IAAI,EAAE;QACnC,IAAImW,YAAY,GAAG,IAAI,CAAChhB,KAAK;UAC3B6J,MAAM,GAAGmX,YAAY,CAACnX,MAAM;UAC5BoX,UAAU,GAAGD,YAAY,CAACC,UAAU;QACtC,IAAIrO,QAAQ,GAAG,IAAI,CAAC5G,KAAK,CAAC4G,QAAQ;QAClC,IAAI3H,cAAc,GAAGJ,IAAI,CAACI,cAAc;UACtCC,YAAY,GAAGL,IAAI,CAACK,YAAY;QAClC,IAAI,CAAC7O,MAAM,CAACwO,IAAI,CAACI,cAAc,CAAC,IAAI,CAAC5O,MAAM,CAACwO,IAAI,CAACK,YAAY,CAAC,EAAE;UAC9D,IAAI,CAAC2K,QAAQ,CAAC/R,aAAa,CAAC;YAC1BmH,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA;UAChB,CAAC,EAAEyJ,yCAAyC,CAAC;YAC3C3U,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBiL,cAAc,EAAEA,cAAc;YAC9BC,YAAY,EAAEA,YAAY;YAC1B0H,QAAQ,EAAEA;UACZ,CAAC,EAAE,IAAI,CAAC5G,KAAK,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM,IAAI,CAAC3P,MAAM,CAACwO,IAAI,CAACuC,kBAAkB,CAAC,EAAE;UAC3C,IAAIR,MAAM,GAAG/B,IAAI,CAAC+B,MAAM;YACtBC,MAAM,GAAGhC,IAAI,CAACgC,MAAM;UACtB,IAAIO,kBAAkB,GAAGvC,IAAI,CAACuC,kBAAkB;UAChD,IAAI8T,aAAa,GAAG,IAAI,CAAClV,KAAK;YAC5B4F,MAAM,GAAGsP,aAAa,CAACtP,MAAM;YAC7B3H,YAAY,GAAGiX,aAAa,CAACjX,YAAY;UAC3C,IAAI,CAAC2H,MAAM,EAAE;YACX;UACF;UACA,IAAI,OAAOqP,UAAU,KAAK,UAAU,EAAE;YACpC;YACA7T,kBAAkB,GAAG6T,UAAU,CAAChX,YAAY,EAAEY,IAAI,CAAC;UACrD,CAAC,MAAM,IAAIoW,UAAU,KAAK,OAAO,EAAE;YACjC;YACA;YACA7T,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,IAAIlQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+M,YAAY,CAAC3L,MAAM,EAAEpB,CAAC,EAAE,EAAE;cAC5C,IAAI+M,YAAY,CAAC/M,CAAC,CAAC,CAACmB,KAAK,KAAKwM,IAAI,CAACqB,WAAW,EAAE;gBAC9CkB,kBAAkB,GAAGlQ,CAAC;gBACtB;cACF;YACF;UACF;UACA,IAAIgb,OAAO,GAAGpU,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8N,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACzD7I,CAAC,EAAE6I,MAAM,CAACL,IAAI;YACdvI,CAAC,EAAE4I,MAAM,CAACF;UACZ,CAAC,CAAC;UACF;UACA;UACA,IAAIyP,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACzU,MAAM,EAAEsL,OAAO,CAACnP,CAAC,GAAGmP,OAAO,CAAClH,KAAK,CAAC;UAChE,IAAIsQ,cAAc,GAAGF,IAAI,CAACC,GAAG,CAACxU,MAAM,EAAEqL,OAAO,CAAClP,CAAC,GAAGkP,OAAO,CAACjH,MAAM,CAAC;UACjE,IAAI/E,WAAW,GAAGjC,YAAY,CAACmD,kBAAkB,CAAC,IAAInD,YAAY,CAACmD,kBAAkB,CAAC,CAAC/O,KAAK;UAC5F,IAAI6O,aAAa,GAAGnB,iBAAiB,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAChM,KAAK,CAAC6K,IAAI,EAAEuC,kBAAkB,CAAC;UACtF,IAAID,gBAAgB,GAAGlD,YAAY,CAACmD,kBAAkB,CAAC,GAAG;YACxDrE,CAAC,EAAEc,MAAM,KAAK,YAAY,GAAGI,YAAY,CAACmD,kBAAkB,CAAC,CAAC7C,UAAU,GAAG4W,cAAc;YACzFnY,CAAC,EAAEa,MAAM,KAAK,YAAY,GAAGyX,cAAc,GAAGrX,YAAY,CAACmD,kBAAkB,CAAC,CAAC7C;UACjF,CAAC,GAAGzB,gBAAgB;UACpB,IAAI,CAAC+M,QAAQ,CAAC/R,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YACvDqB,WAAW,EAAEA,WAAW;YACxBiB,gBAAgB,EAAEA,gBAAgB;YAClCD,aAAa,EAAEA,aAAa;YAC5BE,kBAAkB,EAAEA;UACtB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACyI,QAAQ,CAAChL,IAAI,CAAC;QACrB;MACF;IACF,CAAC,EAAE;MACD7L,GAAG,EAAE,kBAAkB;MACvBX,KAAK,EAAE,SAASue,gBAAgBA,CAAC7R,IAAI,EAAEyO,WAAW,EAAE9E,UAAU,EAAE;QAC9D,IAAIU,uBAAuB,GAAG,IAAI,CAACpJ,KAAK,CAACoJ,uBAAuB;QAChE,KAAK,IAAIlY,CAAC,GAAG,CAAC,EAAEmG,GAAG,GAAG+R,uBAAuB,CAAC9W,MAAM,EAAEpB,CAAC,GAAGmG,GAAG,EAAEnG,CAAC,EAAE,EAAE;UAClE,IAAIiN,KAAK,GAAGiL,uBAAuB,CAAClY,CAAC,CAAC;UACtC,IAAIiN,KAAK,CAACY,IAAI,KAAKA,IAAI,IAAIZ,KAAK,CAACnK,KAAK,CAAChB,GAAG,KAAK+L,IAAI,CAAC/L,GAAG,IAAIwa,WAAW,KAAKxT,cAAc,CAACmE,KAAK,CAACY,IAAI,CAACiD,IAAI,CAAC,IAAI0G,UAAU,KAAKvK,KAAK,CAACuK,UAAU,EAAE;YAC7I,OAAOvK,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACDnL,GAAG,EAAE,YAAY;MACjBX,KAAK;MACL;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,SAASsb,UAAUA,CAAC4H,WAAW,EAAEnJ,OAAO,EAAEoB,WAAW,EAAElP,KAAK,EAAE;QAC5D,IAAIkX,YAAY,GAAG,IAAI,CAACxhB,KAAK;UAC3BgR,KAAK,GAAGwQ,YAAY,CAACxQ,KAAK;UAC1BC,MAAM,GAAGuQ,YAAY,CAACvQ,MAAM;QAC9B,OAAO,aAAapM,KAAK,CAACI,aAAa,CAACsB,aAAa,EAAE7H,QAAQ,CAAC,CAAC,CAAC,EAAE6iB,WAAW,EAAE;UAC/EhI,SAAS,EAAE,WAAW,CAAChO,MAAM,CAACgW,WAAW,CAAC5V,QAAQ,EAAE,GAAG,CAAC,CAACJ,MAAM,CAACgW,WAAW,CAAC5V,QAAQ,CAAC;UACrF3M,GAAG,EAAEoZ,OAAO,CAACpZ,GAAG,IAAI,EAAE,CAACuM,MAAM,CAACiO,WAAW,EAAE,GAAG,CAAC,CAACjO,MAAM,CAACjB,KAAK,CAAC;UAC7D4N,OAAO,EAAE;YACPnP,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJgI,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC;UACDwQ,cAAc,EAAE,IAAI,CAACC;QACvB,CAAC,CAAC,CAAC;MACL;;MAEA;AACN;AACA;AACA;AACA;IACI,CAAC,EAAE;MACD1iB,GAAG,EAAE,gBAAgB;MACrBX,KAAK,EAAE,SAASsjB,cAAcA,CAAA,EAAG;QAC/B,IAAI7F,UAAU,GAAG,IAAI,CAACA,UAAU;QAChC,IAAI8F,kBAAkB,GAAG,IAAI,CAAC5V,KAAK,CAAC4F,MAAM;UACxCL,IAAI,GAAGqQ,kBAAkB,CAACrQ,IAAI;UAC9BG,GAAG,GAAGkQ,kBAAkB,CAAClQ,GAAG;UAC5BT,MAAM,GAAG2Q,kBAAkB,CAAC3Q,MAAM;UAClCD,KAAK,GAAG4Q,kBAAkB,CAAC5Q,KAAK;QAClC,OAAO,aAAanM,KAAK,CAACI,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaJ,KAAK,CAACI,aAAa,CAAC,UAAU,EAAE;UACjGoM,EAAE,EAAEyK;QACN,CAAC,EAAE,aAAajX,KAAK,CAACI,aAAa,CAAC,MAAM,EAAE;UAC1C8D,CAAC,EAAEwI,IAAI;UACPvI,CAAC,EAAE0I,GAAG;UACNT,MAAM,EAAEA,MAAM;UACdD,KAAK,EAAEA;QACT,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC,EAAE;MACDhS,GAAG,EAAE,YAAY;MACjBX,KAAK,EAAE,SAASwjB,UAAUA,CAAA,EAAG;QAC3B,IAAIhR,QAAQ,GAAG,IAAI,CAAC7E,KAAK,CAAC6E,QAAQ;QAClC,OAAOA,QAAQ,GAAG3S,MAAM,CAACuO,OAAO,CAACoE,QAAQ,CAAC,CAACzF,MAAM,CAAC,UAAUzG,GAAG,EAAEmd,MAAM,EAAE;UACvE,IAAIC,MAAM,GAAG/kB,cAAc,CAAC8kB,MAAM,EAAE,CAAC,CAAC;YACpC3T,MAAM,GAAG4T,MAAM,CAAC,CAAC,CAAC;YAClBC,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC;UACvB,OAAOje,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEX,eAAe,CAAC,CAAC,CAAC,EAAEmK,MAAM,EAAE6T,SAAS,CAAC/T,KAAK,CAAC,CAAC;QAChG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;MACf;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,YAAY;MACjBX,KAAK,EAAE,SAAS4jB,UAAUA,CAAA,EAAG;QAC3B,IAAIlR,QAAQ,GAAG,IAAI,CAAC/E,KAAK,CAAC+E,QAAQ;QAClC,OAAOA,QAAQ,GAAG7S,MAAM,CAACuO,OAAO,CAACsE,QAAQ,CAAC,CAAC3F,MAAM,CAAC,UAAUzG,GAAG,EAAEud,MAAM,EAAE;UACvE,IAAIC,MAAM,GAAGnlB,cAAc,CAACklB,MAAM,EAAE,CAAC,CAAC;YACpC/T,MAAM,GAAGgU,MAAM,CAAC,CAAC,CAAC;YAClBH,SAAS,GAAGG,MAAM,CAAC,CAAC,CAAC;UACvB,OAAOre,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEX,eAAe,CAAC,CAAC,CAAC,EAAEmK,MAAM,EAAE6T,SAAS,CAAC/T,KAAK,CAAC,CAAC;QAChG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;MACf;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,mBAAmB;MACxBX,KAAK,EAAE,SAAS+jB,iBAAiBA,CAACjU,MAAM,EAAE;QACxC,IAAIkU,oBAAoB,EAAEC,qBAAqB;QAC/C,OAAO,CAACD,oBAAoB,GAAG,IAAI,CAACrW,KAAK,CAAC6E,QAAQ,MAAM,IAAI,IAAIwR,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,oBAAoB,CAAClU,MAAM,CAAC,MAAM,IAAI,IAAImU,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACrU,KAAK;MACvP;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,mBAAmB;MACxBX,KAAK,EAAE,SAASkkB,iBAAiBA,CAACpU,MAAM,EAAE;QACxC,IAAIqU,oBAAoB,EAAEC,qBAAqB;QAC/C,OAAO,CAACD,oBAAoB,GAAG,IAAI,CAACxW,KAAK,CAAC+E,QAAQ,MAAM,IAAI,IAAIyR,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,oBAAoB,CAACrU,MAAM,CAAC,MAAM,IAAI,IAAIsU,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACxU,KAAK;MACvP;IACF,CAAC,EAAE;MACDjP,GAAG,EAAE,aAAa;MAClBX,KAAK,EAAE,SAASqkB,WAAWA,CAACC,OAAO,EAAE;QACnC,IAAIvN,uBAAuB,GAAG,IAAI,CAACpJ,KAAK,CAACoJ,uBAAuB;QAChE,IAAIA,uBAAuB,IAAIA,uBAAuB,CAAC9W,MAAM,EAAE;UAC7D,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEmG,GAAG,GAAG+R,uBAAuB,CAAC9W,MAAM,EAAEpB,CAAC,GAAGmG,GAAG,EAAEnG,CAAC,EAAE,EAAE;YAClE,IAAIqgB,aAAa,GAAGnI,uBAAuB,CAAClY,CAAC,CAAC;YAC9C,IAAI8C,KAAK,GAAGud,aAAa,CAACvd,KAAK;cAC7B+K,IAAI,GAAGwS,aAAa,CAACxS,IAAI;YAC3B,IAAI6X,eAAe,GAAG5c,cAAc,CAAC+E,IAAI,CAACiD,IAAI,CAAC;YAC/C,IAAI4U,eAAe,KAAK,KAAK,EAAE;cAC7B,IAAIC,aAAa,GAAG,CAAC7iB,KAAK,CAAC6K,IAAI,IAAI,EAAE,EAAET,IAAI,CAAC,UAAUD,KAAK,EAAE;gBAC3D,OAAOvE,aAAa,CAAC+c,OAAO,EAAExY,KAAK,CAAC;cACtC,CAAC,CAAC;cACF,IAAI0Y,aAAa,EAAE;gBACjB,OAAO;kBACLtF,aAAa,EAAEA,aAAa;kBAC5BjR,OAAO,EAAEuW;gBACX,CAAC;cACH;YACF,CAAC,MAAM,IAAID,eAAe,KAAK,WAAW,EAAE;cAC1C,IAAIE,cAAc,GAAG,CAAC9iB,KAAK,CAAC6K,IAAI,IAAI,EAAE,EAAET,IAAI,CAAC,UAAUD,KAAK,EAAE;gBAC5D,OAAO9B,eAAe,CAACsa,OAAO,EAAExY,KAAK,CAAC;cACxC,CAAC,CAAC;cACF,IAAI2Y,cAAc,EAAE;gBAClB,OAAO;kBACLvF,aAAa,EAAEA,aAAa;kBAC5BjR,OAAO,EAAEwW;gBACX,CAAC;cACH;YACF;UACF;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC,EAAE;MACD9jB,GAAG,EAAE,QAAQ;MACbX,KAAK,EAAE,SAAS0kB,MAAMA,CAAA,EAAG;QACvB,IAAIC,MAAM,GAAG,IAAI;QACjB,IAAI,CAAC9c,mBAAmB,CAAC,IAAI,CAAC,EAAE;UAC9B,OAAO,IAAI;QACb;QACA,IAAI+c,YAAY,GAAG,IAAI,CAACjjB,KAAK;UAC3B0N,QAAQ,GAAGuV,YAAY,CAACvV,QAAQ;UAChC6L,SAAS,GAAG0J,YAAY,CAAC1J,SAAS;UAClCvI,KAAK,GAAGiS,YAAY,CAACjS,KAAK;UAC1BC,MAAM,GAAGgS,YAAY,CAAChS,MAAM;UAC5BiS,KAAK,GAAGD,YAAY,CAACC,KAAK;UAC1BC,OAAO,GAAGF,YAAY,CAACE,OAAO;UAC9BC,KAAK,GAAGH,YAAY,CAACG,KAAK;UAC1BC,IAAI,GAAGJ,YAAY,CAACI,IAAI;UACxBC,MAAM,GAAGnkB,wBAAwB,CAAC8jB,YAAY,EAAExmB,UAAU,CAAC;QAC7D,IAAI8mB,KAAK,GAAGjd,WAAW,CAACgd,MAAM,CAAC;QAC/B,IAAIjP,GAAG,GAAG;UACRmP,aAAa,EAAE;YACbjM,OAAO,EAAE,IAAI,CAACkM,UAAU;YACxBC,IAAI,EAAE;UACR,CAAC;UACDC,aAAa,EAAE;YACbpM,OAAO,EAAE,IAAI,CAACqM;UAChB,CAAC;UACDC,aAAa,EAAE;YACbtM,OAAO,EAAE,IAAI,CAACqM;UAChB,CAAC;UACDE,YAAY,EAAE;YACZvM,OAAO,EAAE,IAAI,CAACqM;UAChB,CAAC;UACDG,KAAK,EAAE;YACLxM,OAAO,EAAE,IAAI,CAACyM;UAChB,CAAC;UACDC,KAAK,EAAE;YACL1M,OAAO,EAAE,IAAI,CAAC2M;UAChB,CAAC;UACD1d,KAAK,EAAE;YACL+Q,OAAO,EAAE,IAAI,CAAC4M,WAAW;YACzBT,IAAI,EAAE;UACR,CAAC;UACDU,GAAG,EAAE;YACH7M,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDC,IAAI,EAAE;YACJ/M,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDE,IAAI,EAAE;YACJhN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDG,KAAK,EAAE;YACLjN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDI,SAAS,EAAE;YACTlN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDK,OAAO,EAAE;YACPnN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDM,GAAG,EAAE;YACHpN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACDO,MAAM,EAAE;YACNrN,OAAO,EAAE,IAAI,CAAC8M;UAChB,CAAC;UACD/e,OAAO,EAAE;YACPiS,OAAO,EAAE,IAAI,CAACsN,YAAY;YAC1BnB,IAAI,EAAE;UACR,CAAC;UACDoB,SAAS,EAAE;YACTvN,OAAO,EAAE,IAAI,CAACwN,eAAe;YAC7BrB,IAAI,EAAE;UACR,CAAC;UACDsB,cAAc,EAAE;YACdzN,OAAO,EAAE,IAAI,CAAC0N;UAChB,CAAC;UACDC,eAAe,EAAE;YACf3N,OAAO,EAAE,IAAI,CAAC0N;UAChB,CAAC;UACDE,UAAU,EAAE;YACV5N,OAAO,EAAE,IAAI,CAAC6N;UAChB;QACF,CAAC;;QAED;QACA,IAAIjC,OAAO,EAAE;UACX,OAAO,aAAate,KAAK,CAACI,aAAa,CAACG,OAAO,EAAE1G,QAAQ,CAAC,CAAC,CAAC,EAAE6kB,KAAK,EAAE;YACnEvS,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA,MAAM;YACdmS,KAAK,EAAEA,KAAK;YACZC,IAAI,EAAEA;UACR,CAAC,CAAC,EAAE,IAAI,CAAC1B,cAAc,CAAC,CAAC,EAAEvb,aAAa,CAACsH,QAAQ,EAAE2G,GAAG,CAAC,CAAC;QAC1D;QACA,IAAIgR,MAAM,GAAG,IAAI,CAACvF,oBAAoB,CAAC,CAAC;QACxC,OAAO,aAAajb,KAAK,CAACI,aAAa,CAAC,KAAK,EAAEvG,QAAQ,CAAC;UACtD6a,SAAS,EAAErU,UAAU,CAAC,kBAAkB,EAAEqU,SAAS,CAAC;UACpD2J,KAAK,EAAEpf,aAAa,CAAC;YACnBwQ,QAAQ,EAAE,UAAU;YACpBkE,MAAM,EAAE,SAAS;YACjBxH,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC,EAAEiS,KAAK;QACV,CAAC,EAAEmC,MAAM,EAAE;UACTrK,GAAG,EAAE,SAASA,GAAGA,CAACsK,IAAI,EAAE;YACtBtC,MAAM,CAAC3E,SAAS,GAAGiH,IAAI;UACzB,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC,EAAE,aAAa1gB,KAAK,CAACI,aAAa,CAACG,OAAO,EAAE1G,QAAQ,CAAC,CAAC,CAAC,EAAE6kB,KAAK,EAAE;UAChEvS,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdmS,KAAK,EAAEA,KAAK;UACZC,IAAI,EAAEA;QACR,CAAC,CAAC,EAAE,IAAI,CAAC1B,cAAc,CAAC,CAAC,EAAEvb,aAAa,CAACsH,QAAQ,EAAE2G,GAAG,CAAC,CAAC,EAAE,IAAI,CAACmR,YAAY,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;MACtG;IACF,CAAC,CAAC,CAAC;IACH,OAAOnQ,uBAAuB;EAChC,CAAC,CAACxQ,SAAS,CAAC,EAAEd,eAAe,CAACgO,MAAM,EAAE,aAAa,EAAEC,SAAS,CAAC,EAAEjO,eAAe,CAACgO,MAAM,EAAE,cAAc,EAAElO,aAAa,CAAC;IACrH+F,MAAM,EAAE,YAAY;IACpB8D,WAAW,EAAE,MAAM;IACnBoF,cAAc,EAAE,KAAK;IACrBD,MAAM,EAAE,CAAC;IACT5B,MAAM,EAAE;MACNQ,GAAG,EAAE,CAAC;MACNF,KAAK,EAAE,CAAC;MACRG,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAE;IACR,CAAC;IACDuD,iBAAiB,EAAE,KAAK;IACxBmM,UAAU,EAAE;EACd,CAAC,EAAE7R,YAAY,CAAC,CAAC,EAAEpL,eAAe,CAACgO,MAAM,EAAE,0BAA0B,EAAE,UAAU0T,SAAS,EAAE7Q,SAAS,EAAE;IACrG,IAAIhK,IAAI,GAAG6a,SAAS,CAAC7a,IAAI;MACvB6C,QAAQ,GAAGgY,SAAS,CAAChY,QAAQ;MAC7BsD,KAAK,GAAG0U,SAAS,CAAC1U,KAAK;MACvBC,MAAM,GAAGyU,SAAS,CAACzU,MAAM;MACzBpH,MAAM,GAAG6b,SAAS,CAAC7b,MAAM;MACzB8D,WAAW,GAAG+X,SAAS,CAAC/X,WAAW;MACnCuD,MAAM,GAAGwU,SAAS,CAACxU,MAAM;IAC3B,IAAI7U,MAAM,CAACwY,SAAS,CAACjC,QAAQ,CAAC,EAAE;MAC9B,IAAI+S,YAAY,GAAG/V,kBAAkB,CAAC8V,SAAS,CAAC;MAChD,OAAO5hB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6hB,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACtE/S,QAAQ,EAAE;MACZ,CAAC,EAAE+B,yCAAyC,CAAC7Q,aAAa,CAACA,aAAa,CAAC;QACvE9D,KAAK,EAAE0lB;MACT,CAAC,EAAEC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QACpB/S,QAAQ,EAAE;MACZ,CAAC,CAAC,EAAEiC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnB+Q,QAAQ,EAAE/a,IAAI;QACdgb,SAAS,EAAE7U,KAAK;QAChB8U,UAAU,EAAE7U,MAAM;QAClB8U,UAAU,EAAElc,MAAM;QAClBmc,eAAe,EAAErY,WAAW;QAC5BsY,UAAU,EAAE/U,MAAM;QAClBgV,YAAY,EAAExY;MAChB,CAAC,CAAC;IACJ;IACA,IAAI7C,IAAI,KAAKgK,SAAS,CAAC+Q,QAAQ,IAAI5U,KAAK,KAAK6D,SAAS,CAACgR,SAAS,IAAI5U,MAAM,KAAK4D,SAAS,CAACiR,UAAU,IAAIjc,MAAM,KAAKgL,SAAS,CAACkR,UAAU,IAAIpY,WAAW,KAAKkH,SAAS,CAACmR,eAAe,IAAI,CAACzd,YAAY,CAAC2I,MAAM,EAAE2D,SAAS,CAACoR,UAAU,CAAC,EAAE;MAClO,IAAIE,aAAa,GAAGvW,kBAAkB,CAAC8V,SAAS,CAAC;;MAEjD;MACA,IAAIU,iBAAiB,GAAG;QACtB;QACA;QACAxZ,MAAM,EAAEiI,SAAS,CAACjI,MAAM;QACxBC,MAAM,EAAEgI,SAAS,CAAChI,MAAM;QACxB;QACA;QACAsD,eAAe,EAAE0E,SAAS,CAAC1E;MAC7B,CAAC;MACD,IAAIkW,cAAc,GAAGviB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4I,cAAc,CAACmI,SAAS,EAAEhK,IAAI,EAAEhB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACjG+I,QAAQ,EAAEiC,SAAS,CAACjC,QAAQ,GAAG;MACjC,CAAC,CAAC;MACF,IAAI0T,QAAQ,GAAGxiB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqiB,aAAa,CAAC,EAAEC,iBAAiB,CAAC,EAAEC,cAAc,CAAC;MAChH,OAAOviB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwiB,QAAQ,CAAC,EAAE3R,yCAAyC,CAAC7Q,aAAa,CAAC;QACtH9D,KAAK,EAAE0lB;MACT,CAAC,EAAEY,QAAQ,CAAC,EAAEzR,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7B+Q,QAAQ,EAAE/a,IAAI;QACdgb,SAAS,EAAE7U,KAAK;QAChB8U,UAAU,EAAE7U,MAAM;QAClB8U,UAAU,EAAElc,MAAM;QAClBmc,eAAe,EAAErY,WAAW;QAC5BsY,UAAU,EAAE/U,MAAM;QAClBgV,YAAY,EAAExY;MAChB,CAAC,CAAC;IACJ;IACA,IAAI,CAACvH,eAAe,CAACuH,QAAQ,EAAEmH,SAAS,CAACqR,YAAY,CAAC,EAAE;MACtD;MACA,IAAIK,aAAa,GAAG,CAAClqB,MAAM,CAACwO,IAAI,CAAC;MACjC,IAAI2b,WAAW,GAAGD,aAAa,GAAG1R,SAAS,CAACjC,QAAQ,GAAGiC,SAAS,CAACjC,QAAQ,GAAG,CAAC;MAC7E,OAAO9O,aAAa,CAACA,aAAa,CAAC;QACjC8O,QAAQ,EAAE4T;MACZ,CAAC,EAAE7R,yCAAyC,CAAC7Q,aAAa,CAACA,aAAa,CAAC;QACvE9D,KAAK,EAAE0lB;MACT,CAAC,EAAE7Q,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACjBjC,QAAQ,EAAE4T;MACZ,CAAC,CAAC,EAAE3R,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnBqR,YAAY,EAAExY;MAChB,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb,CAAC,CAAC,EAAE1J,eAAe,CAACgO,MAAM,EAAE,iBAAiB,EAAE,UAAUyU,MAAM,EAAEzmB,KAAK,EAAE;IACtE,IAAI0mB,GAAG;IACP,IAAK,aAAa1hB,cAAc,CAACyhB,MAAM,CAAC,EAAE;MACxCC,GAAG,GAAG,aAAa3hB,YAAY,CAAC0hB,MAAM,EAAEzmB,KAAK,CAAC;IAChD,CAAC,MAAM,IAAIhE,WAAW,CAACyqB,MAAM,CAAC,EAAE;MAC9BC,GAAG,GAAGD,MAAM,CAACzmB,KAAK,CAAC;IACrB,CAAC,MAAM;MACL0mB,GAAG,GAAG,aAAa7hB,KAAK,CAACI,aAAa,CAACU,GAAG,EAAE3F,KAAK,CAAC;IACpD;IACA,OAAO,aAAa6E,KAAK,CAACI,aAAa,CAACI,KAAK,EAAE;MAC7CkU,SAAS,EAAE,qBAAqB;MAChCva,GAAG,EAAEgB,KAAK,CAAChB;IACb,CAAC,EAAE0nB,GAAG,CAAC;EACT,CAAC,CAAC,EAAE1U,MAAM;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}