{"ast": null, "code": "import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nexport default (function (callback, conditions) {\n  var mountRef = React.useRef(false);\n  React.useEffect(function () {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n});", "map": {"version": 3, "names": ["React", "callback", "conditions", "mountRef", "useRef", "useEffect", "current"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/hooks/useUpdatedEffect.js"], "sourcesContent": ["import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nexport default (function (callback, conditions) {\n  var mountRef = React.useRef(false);\n  React.useEffect(function () {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,gBAAgB,UAAUC,QAAQ,EAAEC,UAAU,EAAE;EAC9C,IAAIC,QAAQ,GAAGH,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC;EAClCJ,KAAK,CAACK,SAAS,CAAC,YAAY;IAC1B,IAAIF,QAAQ,CAACG,OAAO,EAAE;MACpBL,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLE,QAAQ,CAACG,OAAO,GAAG,IAAI;IACzB;EACF,CAAC,EAAEJ,UAAU,CAAC;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}