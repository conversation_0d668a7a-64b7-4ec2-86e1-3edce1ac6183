{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { placements } from \"rc-tooltip/es/placements\";\nvar autoAdjustOverflowEnabled = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar autoAdjustOverflowDisabled = {\n  adjustX: 0,\n  adjustY: 0\n};\nvar targetOffset = [0, 0];\nexport function getOverflowOptions(autoAdjustOverflow) {\n  if (typeof autoAdjustOverflow === 'boolean') {\n    return autoAdjustOverflow ? autoAdjustOverflowEnabled : autoAdjustOverflowDisabled;\n  }\n  return _extends(_extends({}, autoAdjustOverflowDisabled), autoAdjustOverflow);\n}\nexport default function getPlacements(config) {\n  var _config$arrowWidth = config.arrowWidth,\n    arrowWidth = _config$arrowWidth === void 0 ? 4 : _config$arrowWidth,\n    _config$horizontalArr = config.horizontalArrowShift,\n    horizontalArrowShift = _config$horizontalArr === void 0 ? 16 : _config$horizontalArr,\n    _config$verticalArrow = config.verticalArrowShift,\n    verticalArrowShift = _config$verticalArrow === void 0 ? 8 : _config$verticalArrow,\n    autoAdjustOverflow = config.autoAdjustOverflow,\n    arrowPointAtCenter = config.arrowPointAtCenter;\n  var placementMap = {\n    left: {\n      points: ['cr', 'cl'],\n      offset: [-4, 0]\n    },\n    right: {\n      points: ['cl', 'cr'],\n      offset: [4, 0]\n    },\n    top: {\n      points: ['bc', 'tc'],\n      offset: [0, -4]\n    },\n    bottom: {\n      points: ['tc', 'bc'],\n      offset: [0, 4]\n    },\n    topLeft: {\n      points: ['bl', 'tc'],\n      offset: [-(horizontalArrowShift + arrowWidth), -4]\n    },\n    leftTop: {\n      points: ['tr', 'cl'],\n      offset: [-4, -(verticalArrowShift + arrowWidth)]\n    },\n    topRight: {\n      points: ['br', 'tc'],\n      offset: [horizontalArrowShift + arrowWidth, -4]\n    },\n    rightTop: {\n      points: ['tl', 'cr'],\n      offset: [4, -(verticalArrowShift + arrowWidth)]\n    },\n    bottomRight: {\n      points: ['tr', 'bc'],\n      offset: [horizontalArrowShift + arrowWidth, 4]\n    },\n    rightBottom: {\n      points: ['bl', 'cr'],\n      offset: [4, verticalArrowShift + arrowWidth]\n    },\n    bottomLeft: {\n      points: ['tl', 'bc'],\n      offset: [-(horizontalArrowShift + arrowWidth), 4]\n    },\n    leftBottom: {\n      points: ['br', 'cl'],\n      offset: [-4, verticalArrowShift + arrowWidth]\n    }\n  };\n  Object.keys(placementMap).forEach(function (key) {\n    placementMap[key] = arrowPointAtCenter ? _extends(_extends({}, placementMap[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow),\n      targetOffset: targetOffset\n    }) : _extends(_extends({}, placements[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow)\n    });\n    placementMap[key].ignoreShake = true;\n  });\n  return placementMap;\n}", "map": {"version": 3, "names": ["_extends", "placements", "autoAdjustOverflowEnabled", "adjustX", "adjustY", "autoAdjustOverflowDisabled", "targetOffset", "getOverflowOptions", "autoAdjustOverflow", "getPlacements", "config", "_config$arrowWidth", "arrow<PERSON>idth", "_config$horizontalArr", "horizontalArrowShift", "_config$verticalArrow", "verticalArrowShift", "arrowPointAtCenter", "placementMap", "left", "points", "offset", "right", "top", "bottom", "topLeft", "leftTop", "topRight", "rightTop", "bottomRight", "rightBottom", "bottomLeft", "leftBottom", "Object", "keys", "for<PERSON>ach", "key", "overflow", "ignoreShake"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/placements.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { placements } from \"rc-tooltip/es/placements\";\nvar autoAdjustOverflowEnabled = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar autoAdjustOverflowDisabled = {\n  adjustX: 0,\n  adjustY: 0\n};\nvar targetOffset = [0, 0];\nexport function getOverflowOptions(autoAdjustOverflow) {\n  if (typeof autoAdjustOverflow === 'boolean') {\n    return autoAdjustOverflow ? autoAdjustOverflowEnabled : autoAdjustOverflowDisabled;\n  }\n  return _extends(_extends({}, autoAdjustOverflowDisabled), autoAdjustOverflow);\n}\nexport default function getPlacements(config) {\n  var _config$arrowWidth = config.arrowWidth,\n    arrowWidth = _config$arrowWidth === void 0 ? 4 : _config$arrowWidth,\n    _config$horizontalArr = config.horizontalArrowShift,\n    horizontalArrowShift = _config$horizontalArr === void 0 ? 16 : _config$horizontalArr,\n    _config$verticalArrow = config.verticalArrowShift,\n    verticalArrowShift = _config$verticalArrow === void 0 ? 8 : _config$verticalArrow,\n    autoAdjustOverflow = config.autoAdjustOverflow,\n    arrowPointAtCenter = config.arrowPointAtCenter;\n  var placementMap = {\n    left: {\n      points: ['cr', 'cl'],\n      offset: [-4, 0]\n    },\n    right: {\n      points: ['cl', 'cr'],\n      offset: [4, 0]\n    },\n    top: {\n      points: ['bc', 'tc'],\n      offset: [0, -4]\n    },\n    bottom: {\n      points: ['tc', 'bc'],\n      offset: [0, 4]\n    },\n    topLeft: {\n      points: ['bl', 'tc'],\n      offset: [-(horizontalArrowShift + arrowWidth), -4]\n    },\n    leftTop: {\n      points: ['tr', 'cl'],\n      offset: [-4, -(verticalArrowShift + arrowWidth)]\n    },\n    topRight: {\n      points: ['br', 'tc'],\n      offset: [horizontalArrowShift + arrowWidth, -4]\n    },\n    rightTop: {\n      points: ['tl', 'cr'],\n      offset: [4, -(verticalArrowShift + arrowWidth)]\n    },\n    bottomRight: {\n      points: ['tr', 'bc'],\n      offset: [horizontalArrowShift + arrowWidth, 4]\n    },\n    rightBottom: {\n      points: ['bl', 'cr'],\n      offset: [4, verticalArrowShift + arrowWidth]\n    },\n    bottomLeft: {\n      points: ['tl', 'bc'],\n      offset: [-(horizontalArrowShift + arrowWidth), 4]\n    },\n    leftBottom: {\n      points: ['br', 'cl'],\n      offset: [-4, verticalArrowShift + arrowWidth]\n    }\n  };\n  Object.keys(placementMap).forEach(function (key) {\n    placementMap[key] = arrowPointAtCenter ? _extends(_extends({}, placementMap[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow),\n      targetOffset: targetOffset\n    }) : _extends(_extends({}, placements[key]), {\n      overflow: getOverflowOptions(autoAdjustOverflow)\n    });\n    placementMap[key].ignoreShake = true;\n  });\n  return placementMap;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,IAAIC,yBAAyB,GAAG;EAC9BC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,0BAA0B,GAAG;EAC/BF,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,IAAIE,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,OAAO,SAASC,kBAAkBA,CAACC,kBAAkB,EAAE;EACrD,IAAI,OAAOA,kBAAkB,KAAK,SAAS,EAAE;IAC3C,OAAOA,kBAAkB,GAAGN,yBAAyB,GAAGG,0BAA0B;EACpF;EACA,OAAOL,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEK,0BAA0B,CAAC,EAAEG,kBAAkB,CAAC;AAC/E;AACA,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC5C,IAAIC,kBAAkB,GAAGD,MAAM,CAACE,UAAU;IACxCA,UAAU,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IACnEE,qBAAqB,GAAGH,MAAM,CAACI,oBAAoB;IACnDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IACpFE,qBAAqB,GAAGL,MAAM,CAACM,kBAAkB;IACjDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IACjFP,kBAAkB,GAAGE,MAAM,CAACF,kBAAkB;IAC9CS,kBAAkB,GAAGP,MAAM,CAACO,kBAAkB;EAChD,IAAIC,YAAY,GAAG;IACjBC,IAAI,EAAE;MACJC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC;IACDC,KAAK,EAAE;MACLF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;IACf,CAAC;IACDE,GAAG,EAAE;MACHH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC;IACDG,MAAM,EAAE;MACNJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;IACf,CAAC;IACDI,OAAO,EAAE;MACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,EAAEP,oBAAoB,GAAGF,UAAU,CAAC,EAAE,CAAC,CAAC;IACnD,CAAC;IACDc,OAAO,EAAE;MACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,EAAEL,kBAAkB,GAAGJ,UAAU,CAAC;IACjD,CAAC;IACDe,QAAQ,EAAE;MACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAACP,oBAAoB,GAAGF,UAAU,EAAE,CAAC,CAAC;IAChD,CAAC;IACDgB,QAAQ,EAAE;MACRR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAEL,kBAAkB,GAAGJ,UAAU,CAAC;IAChD,CAAC;IACDiB,WAAW,EAAE;MACXT,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAACP,oBAAoB,GAAGF,UAAU,EAAE,CAAC;IAC/C,CAAC;IACDkB,WAAW,EAAE;MACXV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,EAAEL,kBAAkB,GAAGJ,UAAU;IAC7C,CAAC;IACDmB,UAAU,EAAE;MACVX,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,EAAEP,oBAAoB,GAAGF,UAAU,CAAC,EAAE,CAAC;IAClD,CAAC;IACDoB,UAAU,EAAE;MACVZ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;MACpBC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAEL,kBAAkB,GAAGJ,UAAU;IAC9C;EACF,CAAC;EACDqB,MAAM,CAACC,IAAI,CAAChB,YAAY,CAAC,CAACiB,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC/ClB,YAAY,CAACkB,GAAG,CAAC,GAAGnB,kBAAkB,GAAGjB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkB,YAAY,CAACkB,GAAG,CAAC,CAAC,EAAE;MACjFC,QAAQ,EAAE9B,kBAAkB,CAACC,kBAAkB,CAAC;MAChDF,YAAY,EAAEA;IAChB,CAAC,CAAC,GAAGN,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEC,UAAU,CAACmC,GAAG,CAAC,CAAC,EAAE;MAC3CC,QAAQ,EAAE9B,kBAAkB,CAACC,kBAAkB;IACjD,CAAC,CAAC;IACFU,YAAY,CAACkB,GAAG,CAAC,CAACE,WAAW,GAAG,IAAI;EACtC,CAAC,CAAC;EACF,OAAOpB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}