{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ResizableTextArea from './ResizableTextArea';\nvar TextArea = /*#__PURE__*/function (_React$Component) {\n  _inherits(TextArea, _React$Component);\n  var _super = _createSuper(TextArea);\n  function TextArea(props) {\n    var _this;\n    _classCallCheck(this, TextArea);\n    _this = _super.call(this, props);\n    _this.resizableTextArea = void 0;\n    _this.focus = function () {\n      _this.resizableTextArea.textArea.focus();\n    };\n    _this.saveTextArea = function (resizableTextArea) {\n      _this.resizableTextArea = resizableTextArea;\n    };\n    _this.handleChange = function (e) {\n      var onChange = _this.props.onChange;\n      _this.setValue(e.target.value);\n      if (onChange) {\n        onChange(e);\n      }\n    };\n    _this.handleKeyDown = function (e) {\n      var _this$props = _this.props,\n        onPressEnter = _this$props.onPressEnter,\n        onKeyDown = _this$props.onKeyDown;\n      if (e.keyCode === 13 && onPressEnter) {\n        onPressEnter(e);\n      }\n      if (onKeyDown) {\n        onKeyDown(e);\n      }\n    };\n    var value = typeof props.value === 'undefined' || props.value === null ? props.defaultValue : props.value;\n    _this.state = {\n      value: value\n    };\n    return _this;\n  }\n  _createClass(TextArea, [{\n    key: \"setValue\",\n    value: function setValue(value, callback) {\n      if (!('value' in this.props)) {\n        this.setState({\n          value: value\n        }, callback);\n      }\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.resizableTextArea.textArea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, this.props, {\n        value: this.state.value,\n        onKeyDown: this.handleKeyDown,\n        onChange: this.handleChange,\n        ref: this.saveTextArea\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      if ('value' in nextProps) {\n        return {\n          value: nextProps.value\n        };\n      }\n      return null;\n    }\n  }]);\n  return TextArea;\n}(React.Component);\nexport { ResizableTextArea };\nexport default TextArea;", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "ResizableTextArea", "TextArea", "_React$Component", "_super", "props", "_this", "call", "resizableTextArea", "focus", "textArea", "saveTextArea", "handleChange", "e", "onChange", "setValue", "target", "value", "handleKeyDown", "_this$props", "onPressEnter", "onKeyDown", "keyCode", "defaultValue", "state", "key", "callback", "setState", "blur", "render", "createElement", "ref", "getDerivedStateFromProps", "nextProps", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-textarea/es/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport ResizableTextArea from './ResizableTextArea';\nvar TextArea = /*#__PURE__*/function (_React$Component) {\n  _inherits(TextArea, _React$Component);\n  var _super = _createSuper(TextArea);\n  function TextArea(props) {\n    var _this;\n    _classCallCheck(this, TextArea);\n    _this = _super.call(this, props);\n    _this.resizableTextArea = void 0;\n    _this.focus = function () {\n      _this.resizableTextArea.textArea.focus();\n    };\n    _this.saveTextArea = function (resizableTextArea) {\n      _this.resizableTextArea = resizableTextArea;\n    };\n    _this.handleChange = function (e) {\n      var onChange = _this.props.onChange;\n      _this.setValue(e.target.value);\n      if (onChange) {\n        onChange(e);\n      }\n    };\n    _this.handleKeyDown = function (e) {\n      var _this$props = _this.props,\n        onPressEnter = _this$props.onPressEnter,\n        onKeyDown = _this$props.onKeyDown;\n      if (e.keyCode === 13 && onPressEnter) {\n        onPressEnter(e);\n      }\n      if (onKeyDown) {\n        onKeyDown(e);\n      }\n    };\n    var value = typeof props.value === 'undefined' || props.value === null ? props.defaultValue : props.value;\n    _this.state = {\n      value: value\n    };\n    return _this;\n  }\n  _createClass(TextArea, [{\n    key: \"setValue\",\n    value: function setValue(value, callback) {\n      if (!('value' in this.props)) {\n        this.setState({\n          value: value\n        }, callback);\n      }\n    }\n  }, {\n    key: \"blur\",\n    value: function blur() {\n      this.resizableTextArea.textArea.blur();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, this.props, {\n        value: this.state.value,\n        onKeyDown: this.handleKeyDown,\n        onChange: this.handleChange,\n        ref: this.saveTextArea\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps) {\n      if ('value' in nextProps) {\n        return {\n          value: nextProps.value\n        };\n      }\n      return null;\n    }\n  }]);\n  return TextArea;\n}(React.Component);\nexport { ResizableTextArea };\nexport default TextArea;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACtDL,SAAS,CAACI,QAAQ,EAAEC,gBAAgB,CAAC;EACrC,IAAIC,MAAM,GAAGL,YAAY,CAACG,QAAQ,CAAC;EACnC,SAASA,QAAQA,CAACG,KAAK,EAAE;IACvB,IAAIC,KAAK;IACTV,eAAe,CAAC,IAAI,EAAEM,QAAQ,CAAC;IAC/BI,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;IAChCC,KAAK,CAACE,iBAAiB,GAAG,KAAK,CAAC;IAChCF,KAAK,CAACG,KAAK,GAAG,YAAY;MACxBH,KAAK,CAACE,iBAAiB,CAACE,QAAQ,CAACD,KAAK,CAAC,CAAC;IAC1C,CAAC;IACDH,KAAK,CAACK,YAAY,GAAG,UAAUH,iBAAiB,EAAE;MAChDF,KAAK,CAACE,iBAAiB,GAAGA,iBAAiB;IAC7C,CAAC;IACDF,KAAK,CAACM,YAAY,GAAG,UAAUC,CAAC,EAAE;MAChC,IAAIC,QAAQ,GAAGR,KAAK,CAACD,KAAK,CAACS,QAAQ;MACnCR,KAAK,CAACS,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC;MAC9B,IAAIH,QAAQ,EAAE;QACZA,QAAQ,CAACD,CAAC,CAAC;MACb;IACF,CAAC;IACDP,KAAK,CAACY,aAAa,GAAG,UAAUL,CAAC,EAAE;MACjC,IAAIM,WAAW,GAAGb,KAAK,CAACD,KAAK;QAC3Be,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,SAAS,GAAGF,WAAW,CAACE,SAAS;MACnC,IAAIR,CAAC,CAACS,OAAO,KAAK,EAAE,IAAIF,YAAY,EAAE;QACpCA,YAAY,CAACP,CAAC,CAAC;MACjB;MACA,IAAIQ,SAAS,EAAE;QACbA,SAAS,CAACR,CAAC,CAAC;MACd;IACF,CAAC;IACD,IAAII,KAAK,GAAG,OAAOZ,KAAK,CAACY,KAAK,KAAK,WAAW,IAAIZ,KAAK,CAACY,KAAK,KAAK,IAAI,GAAGZ,KAAK,CAACkB,YAAY,GAAGlB,KAAK,CAACY,KAAK;IACzGX,KAAK,CAACkB,KAAK,GAAG;MACZP,KAAK,EAAEA;IACT,CAAC;IACD,OAAOX,KAAK;EACd;EACAT,YAAY,CAACK,QAAQ,EAAE,CAAC;IACtBuB,GAAG,EAAE,UAAU;IACfR,KAAK,EAAE,SAASF,QAAQA,CAACE,KAAK,EAAES,QAAQ,EAAE;MACxC,IAAI,EAAE,OAAO,IAAI,IAAI,CAACrB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACsB,QAAQ,CAAC;UACZV,KAAK,EAAEA;QACT,CAAC,EAAES,QAAQ,CAAC;MACd;IACF;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,MAAM;IACXR,KAAK,EAAE,SAASW,IAAIA,CAAA,EAAG;MACrB,IAAI,CAACpB,iBAAiB,CAACE,QAAQ,CAACkB,IAAI,CAAC,CAAC;IACxC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbR,KAAK,EAAE,SAASY,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAAC7B,iBAAiB,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACU,KAAK,EAAE;QAClFY,KAAK,EAAE,IAAI,CAACO,KAAK,CAACP,KAAK;QACvBI,SAAS,EAAE,IAAI,CAACH,aAAa;QAC7BJ,QAAQ,EAAE,IAAI,CAACF,YAAY;QAC3BmB,GAAG,EAAE,IAAI,CAACpB;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,EAAE,CAAC;IACHc,GAAG,EAAE,0BAA0B;IAC/BR,KAAK,EAAE,SAASe,wBAAwBA,CAACC,SAAS,EAAE;MAClD,IAAI,OAAO,IAAIA,SAAS,EAAE;QACxB,OAAO;UACLhB,KAAK,EAAEgB,SAAS,CAAChB;QACnB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOf,QAAQ;AACjB,CAAC,CAACF,KAAK,CAACkC,SAAS,CAAC;AAClB,SAASjC,iBAAiB;AAC1B,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}