{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Checkbox from '../checkbox';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport TransButton from '../_util/transButton';\nvar ListItem = function ListItem(props) {\n  var _classNames;\n  var renderedText = props.renderedText,\n    renderedEl = props.renderedEl,\n    item = props.item,\n    checked = props.checked,\n    disabled = props.disabled,\n    prefixCls = props.prefixCls,\n    onClick = props.onClick,\n    onRemove = props.onRemove,\n    showRemove = props.showRemove;\n  var className = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item-disabled\"), disabled || item.disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item-checked\"), checked), _classNames));\n  var title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Transfer\",\n    defaultLocale: defaultLocale.Transfer\n  }, function (contextLocale) {\n    var liProps = {\n      className: className,\n      title: title\n    };\n    var labelNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-content-item-text\")\n    }, renderedEl);\n    // Show remove\n    if (showRemove) {\n      return /*#__PURE__*/React.createElement(\"li\", _extends({}, liProps), labelNode, /*#__PURE__*/React.createElement(TransButton, {\n        disabled: disabled || item.disabled,\n        className: \"\".concat(prefixCls, \"-content-item-remove\"),\n        \"aria-label\": contextLocale.remove,\n        onClick: function onClick() {\n          onRemove === null || onRemove === void 0 ? void 0 : onRemove(item);\n        }\n      }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n    }\n    // Default click to select\n    liProps.onClick = disabled || item.disabled ? undefined : function () {\n      return onClick(item);\n    };\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n      className: \"\".concat(prefixCls, \"-checkbox\"),\n      checked: checked,\n      disabled: disabled || item.disabled\n    }), labelNode);\n  });\n};\nexport default /*#__PURE__*/React.memo(ListItem);", "map": {"version": 3, "names": ["_extends", "_defineProperty", "DeleteOutlined", "classNames", "React", "Checkbox", "LocaleReceiver", "defaultLocale", "TransButton", "ListItem", "props", "_classNames", "renderedText", "renderedEl", "item", "checked", "disabled", "prefixCls", "onClick", "onRemove", "showRemove", "className", "concat", "title", "String", "createElement", "componentName", "Transfer", "contextLocale", "liProps", "labelNode", "remove", "undefined", "memo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/transfer/ListItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Checkbox from '../checkbox';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport defaultLocale from '../locale/default';\nimport TransButton from '../_util/transButton';\nvar ListItem = function ListItem(props) {\n  var _classNames;\n  var renderedText = props.renderedText,\n    renderedEl = props.renderedEl,\n    item = props.item,\n    checked = props.checked,\n    disabled = props.disabled,\n    prefixCls = props.prefixCls,\n    onClick = props.onClick,\n    onRemove = props.onRemove,\n    showRemove = props.showRemove;\n  var className = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item\"), true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item-disabled\"), disabled || item.disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-content-item-checked\"), checked), _classNames));\n  var title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"Transfer\",\n    defaultLocale: defaultLocale.Transfer\n  }, function (contextLocale) {\n    var liProps = {\n      className: className,\n      title: title\n    };\n    var labelNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-content-item-text\")\n    }, renderedEl);\n    // Show remove\n    if (showRemove) {\n      return /*#__PURE__*/React.createElement(\"li\", _extends({}, liProps), labelNode, /*#__PURE__*/React.createElement(TransButton, {\n        disabled: disabled || item.disabled,\n        className: \"\".concat(prefixCls, \"-content-item-remove\"),\n        \"aria-label\": contextLocale.remove,\n        onClick: function onClick() {\n          onRemove === null || onRemove === void 0 ? void 0 : onRemove(item);\n        }\n      }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n    }\n    // Default click to select\n    liProps.onClick = disabled || item.disabled ? undefined : function () {\n      return onClick(item);\n    };\n    return /*#__PURE__*/React.createElement(\"li\", _extends({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n      className: \"\".concat(prefixCls, \"-checkbox\"),\n      checked: checked,\n      disabled: disabled || item.disabled\n    }), labelNode);\n  });\n};\nexport default /*#__PURE__*/React.memo(ListItem);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,WAAW;EACf,IAAIC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACnCC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,OAAO,GAAGL,KAAK,CAACK,OAAO;IACvBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,UAAU,GAAGV,KAAK,CAACU,UAAU;EAC/B,IAAIC,SAAS,GAAGlB,UAAU,EAAEQ,WAAW,GAAG,CAAC,CAAC,EAAEV,eAAe,CAACU,WAAW,EAAE,EAAE,CAACW,MAAM,CAACL,SAAS,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,EAAEhB,eAAe,CAACU,WAAW,EAAE,EAAE,CAACW,MAAM,CAACL,SAAS,EAAE,wBAAwB,CAAC,EAAED,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAAC,EAAEf,eAAe,CAACU,WAAW,EAAE,EAAE,CAACW,MAAM,CAACL,SAAS,EAAE,uBAAuB,CAAC,EAAEF,OAAO,CAAC,EAAEJ,WAAW,CAAC,CAAC;EACrU,IAAIY,KAAK;EACT,IAAI,OAAOX,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACxEW,KAAK,GAAGC,MAAM,CAACZ,YAAY,CAAC;EAC9B;EACA,OAAO,aAAaR,KAAK,CAACqB,aAAa,CAACnB,cAAc,EAAE;IACtDoB,aAAa,EAAE,UAAU;IACzBnB,aAAa,EAAEA,aAAa,CAACoB;EAC/B,CAAC,EAAE,UAAUC,aAAa,EAAE;IAC1B,IAAIC,OAAO,GAAG;MACZR,SAAS,EAAEA,SAAS;MACpBE,KAAK,EAAEA;IACT,CAAC;IACD,IAAIO,SAAS,GAAG,aAAa1B,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;MACvDJ,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,oBAAoB;IACtD,CAAC,EAAEJ,UAAU,CAAC;IACd;IACA,IAAIO,UAAU,EAAE;MACd,OAAO,aAAahB,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,OAAO,CAAC,EAAEC,SAAS,EAAE,aAAa1B,KAAK,CAACqB,aAAa,CAACjB,WAAW,EAAE;QAC5HQ,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE,QAAQ;QACnCK,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,sBAAsB,CAAC;QACvD,YAAY,EAAEW,aAAa,CAACG,MAAM;QAClCb,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1BC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACL,IAAI,CAAC;QACpE;MACF,CAAC,EAAE,aAAaV,KAAK,CAACqB,aAAa,CAACvB,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D;IACA;IACA2B,OAAO,CAACX,OAAO,GAAGF,QAAQ,IAAIF,IAAI,CAACE,QAAQ,GAAGgB,SAAS,GAAG,YAAY;MACpE,OAAOd,OAAO,CAACJ,IAAI,CAAC;IACtB,CAAC;IACD,OAAO,aAAaV,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,OAAO,CAAC,EAAE,aAAazB,KAAK,CAACqB,aAAa,CAACpB,QAAQ,EAAE;MAC9GgB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,WAAW,CAAC;MAC5CF,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE;IAC7B,CAAC,CAAC,EAAEc,SAAS,CAAC;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,eAAe,aAAa1B,KAAK,CAAC6B,IAAI,CAACxB,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}