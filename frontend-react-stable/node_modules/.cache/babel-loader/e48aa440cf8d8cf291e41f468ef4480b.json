{"ast": null, "code": "/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\nexport default ColumnGroup;", "map": {"version": 3, "names": ["ColumnGroup", "_"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/ColumnGroup.js"], "sourcesContent": ["/* istanbul ignore next */\n/** This is a syntactic sugar for `columns` prop. So HOC will not work on this. */\n// eslint-disable-next-line no-unused-vars\nfunction ColumnGroup(_) {\n  return null;\n}\nexport default ColumnGroup;"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,CAAC,EAAE;EACtB,OAAO,IAAI;AACb;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}