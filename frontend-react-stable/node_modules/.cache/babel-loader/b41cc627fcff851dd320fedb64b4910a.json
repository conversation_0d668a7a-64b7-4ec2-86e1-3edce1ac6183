{"ast": null, "code": "// This icon file is generated automatically.\nvar FundViewOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M956 686.5l-.1-.1-.1-.1C911.7 593 843.4 545 752.5 545s-159.2 48.1-203.4 141.3v.1a42.92 42.92 0 000 36.4C593.3 816 661.6 864 752.5 864s159.2-48.1 203.4-141.3c5.4-11.5 5.4-24.8.1-36.2zM752.5 800c-62.1 0-107.4-30-141.1-95.5C645 639 690.4 609 752.5 609c62.1 0 107.4 30 141.1 95.5C860 770 814.6 800 752.5 800z\"\n      }\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M697 705a56 56 0 10112 0 56 56 0 10-112 0zM136 232h704v253h72V192c0-17.7-14.3-32-32-32H96c-17.7 0-32 14.3-32 32v520c0 17.7 14.3 32 32 32h352v-72H136V232z\"\n      }\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M724.9 338.1l-36.8-36.8a8.03 8.03 0 00-11.3 0L493 485.3l-86.1-86.2a8.03 8.03 0 00-11.3 0L251.3 543.4a8.03 8.03 0 000 11.3l36.8 36.8c3.1 3.1 8.2 3.1 11.3 0l101.8-101.8 86.1 86.2c3.1 3.1 8.2 3.1 11.3 0l226.3-226.5c3.2-3.1 3.2-8.2 0-11.3z\"\n      }\n    }]\n  },\n  \"name\": \"fund-view\",\n  \"theme\": \"outlined\"\n};\nexport default FundViewOutlined;", "map": {"version": 3, "names": ["FundViewOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/FundViewOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar FundViewOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M956 686.5l-.1-.1-.1-.1C911.7 593 843.4 545 752.5 545s-159.2 48.1-203.4 141.3v.1a42.92 42.92 0 000 36.4C593.3 816 661.6 864 752.5 864s159.2-48.1 203.4-141.3c5.4-11.5 5.4-24.8.1-36.2zM752.5 800c-62.1 0-107.4-30-141.1-95.5C645 639 690.4 609 752.5 609c62.1 0 107.4 30 141.1 95.5C860 770 814.6 800 752.5 800z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M697 705a56 56 0 10112 0 56 56 0 10-112 0zM136 232h704v253h72V192c0-17.7-14.3-32-32-32H96c-17.7 0-32 14.3-32 32v520c0 17.7 14.3 32 32 32h352v-72H136V232z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M724.9 338.1l-36.8-36.8a8.03 8.03 0 00-11.3 0L493 485.3l-86.1-86.2a8.03 8.03 0 00-11.3 0L251.3 543.4a8.03 8.03 0 000 11.3l36.8 36.8c3.1 3.1 8.2 3.1 11.3 0l101.8-101.8 86.1 86.2c3.1 3.1 8.2 3.1 11.3 0l226.3-226.5c3.2-3.1 3.2-8.2 0-11.3z\" } }] }, \"name\": \"fund-view\", \"theme\": \"outlined\" };\nexport default FundViewOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,gBAAgB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAmT;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA4J;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA8O;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,WAAW;EAAE,OAAO,EAAE;AAAW,CAAC;AACxiC,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}