{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = hasClass;\nfunction hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);else return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["exports", "__esModule", "default", "hasClass", "element", "className", "classList", "contains", "baseVal", "indexOf", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/dom-helpers/class/hasClass.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = hasClass;\n\nfunction hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);else return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}\n\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACpC,IAAID,OAAO,CAACE,SAAS,EAAE,OAAO,CAAC,CAACD,SAAS,IAAID,OAAO,CAACE,SAAS,CAACC,QAAQ,CAACF,SAAS,CAAC,CAAC,KAAK,OAAO,CAAC,GAAG,IAAID,OAAO,CAACC,SAAS,CAACG,OAAO,IAAIJ,OAAO,CAACC,SAAS,CAAC,GAAG,GAAG,EAAEI,OAAO,CAAC,GAAG,GAAGJ,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACrM;AAEAK,MAAM,CAACV,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}