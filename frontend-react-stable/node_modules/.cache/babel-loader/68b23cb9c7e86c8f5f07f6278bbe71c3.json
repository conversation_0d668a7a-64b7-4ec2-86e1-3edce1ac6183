{"ast": null, "code": "module.exports = function (module) {\n  if (!module.webpackPolyfill) {\n    module.deprecate = function () {};\n    module.paths = [];\n    // module.parent = undefined by default\n    if (!module.children) module.children = [];\n    Object.defineProperty(module, \"loaded\", {\n      enumerable: true,\n      get: function () {\n        return module.l;\n      }\n    });\n    Object.defineProperty(module, \"id\", {\n      enumerable: true,\n      get: function () {\n        return module.i;\n      }\n    });\n    module.webpackPolyfill = 1;\n  }\n  return module;\n};", "map": {"version": 3, "names": ["module", "exports", "webpackPolyfill", "deprecate", "paths", "children", "Object", "defineProperty", "enumerable", "get", "l", "i"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/webpack/buildin/module.js"], "sourcesContent": ["module.exports = function(module) {\n\tif (!module.webpackPolyfill) {\n\t\tmodule.deprecate = function() {};\n\t\tmodule.paths = [];\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,UAASD,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,CAACE,eAAe,EAAE;IAC5BF,MAAM,CAACG,SAAS,GAAG,YAAW,CAAC,CAAC;IAChCH,MAAM,CAACI,KAAK,GAAG,EAAE;IACjB;IACA,IAAI,CAACJ,MAAM,CAACK,QAAQ,EAAEL,MAAM,CAACK,QAAQ,GAAG,EAAE;IAC1CC,MAAM,CAACC,cAAc,CAACP,MAAM,EAAE,QAAQ,EAAE;MACvCQ,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACf,OAAOT,MAAM,CAACU,CAAC;MAChB;IACD,CAAC,CAAC;IACFJ,MAAM,CAACC,cAAc,CAACP,MAAM,EAAE,IAAI,EAAE;MACnCQ,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,SAAAA,CAAA,EAAW;QACf,OAAOT,MAAM,CAACW,CAAC;MAChB;IACD,CAAC,CAAC;IACFX,MAAM,CAACE,eAAe,GAAG,CAAC;EAC3B;EACA,OAAOF,MAAM;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}