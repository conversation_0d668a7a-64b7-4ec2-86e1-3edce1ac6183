{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.stepAfter = stepAfter;\nexports.stepBefore = stepBefore;\nfunction Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\nStep.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n      // falls through\n\n      default:\n        {\n          if (this._t <= 0) {\n            this._context.lineTo(this._x, y);\n            this._context.lineTo(x, y);\n          } else {\n            var x1 = this._x * (1 - this._t) + x * this._t;\n            this._context.lineTo(x1, this._y);\n            this._context.lineTo(x1, y);\n          }\n          break;\n        }\n    }\n    this._x = x, this._y = y;\n  }\n};\nfunction _default(context) {\n  return new Step(context, 0.5);\n}\nfunction stepBefore(context) {\n  return new Step(context, 0);\n}\nfunction stepAfter(context) {\n  return new Step(context, 1);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "stepAfter", "stepBefore", "Step", "context", "t", "_context", "_t", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_x", "_y", "_point", "lineEnd", "lineTo", "closePath", "point", "x", "y", "moveTo", "x1"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/curve/step.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nexports.stepAfter = stepAfter;\nexports.stepBefore = stepBefore;\n\nfunction Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n\n      case 1:\n        this._point = 2;\n      // falls through\n\n      default:\n        {\n          if (this._t <= 0) {\n            this._context.lineTo(this._x, y);\n\n            this._context.lineTo(x, y);\n          } else {\n            var x1 = this._x * (1 - this._t) + x * this._t;\n\n            this._context.lineTo(x1, this._y);\n\n            this._context.lineTo(x1, y);\n          }\n\n          break;\n        }\n    }\n\n    this._x = x, this._y = y;\n  }\n};\n\nfunction _default(context) {\n  return new Step(context, 0.5);\n}\n\nfunction stepBefore(context) {\n  return new Step(context, 0);\n}\n\nfunction stepAfter(context) {\n  return new Step(context, 1);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAC1BH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAC7BJ,OAAO,CAACK,UAAU,GAAGA,UAAU;AAE/B,SAASC,IAAIA,CAACC,OAAO,EAAEC,CAAC,EAAE;EACxB,IAAI,CAACC,QAAQ,GAAGF,OAAO;EACvB,IAAI,CAACG,EAAE,GAAGF,CAAC;AACb;AAEAF,IAAI,CAACK,SAAS,GAAG;EACfC,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAY;IACrB,IAAI,CAACC,EAAE,GAAG,IAAI,CAACC,EAAE,GAAGH,GAAG;IACvB,IAAI,CAACI,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAY;IACnB,IAAI,CAAC,GAAG,IAAI,CAACV,EAAE,IAAI,IAAI,CAACA,EAAE,GAAG,CAAC,IAAI,IAAI,CAACS,MAAM,KAAK,CAAC,EAAE,IAAI,CAACV,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACJ,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC;IAC3F,IAAI,IAAI,CAACL,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACM,MAAM,KAAK,CAAC,EAAE,IAAI,CAACV,QAAQ,CAACa,SAAS,CAAC,CAAC;IAClF,IAAI,IAAI,CAACT,KAAK,IAAI,CAAC,EAAE,IAAI,CAACH,EAAE,GAAG,CAAC,GAAG,IAAI,CAACA,EAAE,EAAE,IAAI,CAACG,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EACzE,CAAC;EACDU,KAAK,EAAE,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IACrBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IAEd,QAAQ,IAAI,CAACN,MAAM;MACjB,KAAK,CAAC;QACJ,IAAI,CAACA,MAAM,GAAG,CAAC;QACf,IAAI,CAACN,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAACY,MAAM,CAACG,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAAChB,QAAQ,CAACiB,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;QACpE;MAEF,KAAK,CAAC;QACJ,IAAI,CAACN,MAAM,GAAG,CAAC;MACjB;;MAEA;QACE;UACE,IAAI,IAAI,CAACT,EAAE,IAAI,CAAC,EAAE;YAChB,IAAI,CAACD,QAAQ,CAACY,MAAM,CAAC,IAAI,CAACJ,EAAE,EAAEQ,CAAC,CAAC;YAEhC,IAAI,CAAChB,QAAQ,CAACY,MAAM,CAACG,CAAC,EAAEC,CAAC,CAAC;UAC5B,CAAC,MAAM;YACL,IAAIE,EAAE,GAAG,IAAI,CAACV,EAAE,IAAI,CAAC,GAAG,IAAI,CAACP,EAAE,CAAC,GAAGc,CAAC,GAAG,IAAI,CAACd,EAAE;YAE9C,IAAI,CAACD,QAAQ,CAACY,MAAM,CAACM,EAAE,EAAE,IAAI,CAACT,EAAE,CAAC;YAEjC,IAAI,CAACT,QAAQ,CAACY,MAAM,CAACM,EAAE,EAAEF,CAAC,CAAC;UAC7B;UAEA;QACF;IACJ;IAEA,IAAI,CAACR,EAAE,GAAGO,CAAC,EAAE,IAAI,CAACN,EAAE,GAAGO,CAAC;EAC1B;AACF,CAAC;AAED,SAAStB,QAAQA,CAACI,OAAO,EAAE;EACzB,OAAO,IAAID,IAAI,CAACC,OAAO,EAAE,GAAG,CAAC;AAC/B;AAEA,SAASF,UAAUA,CAACE,OAAO,EAAE;EAC3B,OAAO,IAAID,IAAI,CAACC,OAAO,EAAE,CAAC,CAAC;AAC7B;AAEA,SAASH,SAASA,CAACG,OAAO,EAAE;EAC1B,OAAO,IAAID,IAAI,CAACC,OAAO,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}