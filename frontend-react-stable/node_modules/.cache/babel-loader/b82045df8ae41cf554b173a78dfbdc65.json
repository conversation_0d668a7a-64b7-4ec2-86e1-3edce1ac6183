{"ast": null, "code": "import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Empty = function Empty() {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('empty-img-default');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"184\",\n    height: \"152\",\n    viewBox: \"0 0 184 152\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(24 31.67)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"67.797\",\n    cy: \"106.89\",\n    rx: \"67.797\",\n    ry: \"12.668\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-1\"),\n    d: \"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-2\"),\n    d: \"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\",\n    transform: \"translate(13.56)\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-3\"),\n    d: \"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-4\"),\n    d: \"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\"\n  })), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-5\"),\n    d: \"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    transform: \"translate(149.65 15.383)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    cx: \"20.654\",\n    cy: \"3.167\",\n    rx: \"2.849\",\n    ry: \"2.815\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\"\n  }))));\n};\nexport default Empty;", "map": {"version": 3, "names": ["React", "ConfigContext", "Empty", "_React$useContext", "useContext", "getPrefixCls", "prefixCls", "createElement", "className", "width", "height", "viewBox", "xmlns", "fill", "fillRule", "transform", "concat", "cx", "cy", "rx", "ry", "d"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/empty/empty.js"], "sourcesContent": ["import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Empty = function Empty() {\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('empty-img-default');\n  return /*#__PURE__*/React.createElement(\"svg\", {\n    className: prefixCls,\n    width: \"184\",\n    height: \"152\",\n    viewBox: \"0 0 184 152\",\n    xmlns: \"http://www.w3.org/2000/svg\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(24 31.67)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    className: \"\".concat(prefixCls, \"-ellipse\"),\n    cx: \"67.797\",\n    cy: \"106.89\",\n    rx: \"67.797\",\n    ry: \"12.668\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-1\"),\n    d: \"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-2\"),\n    d: \"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z\",\n    transform: \"translate(13.56)\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-3\"),\n    d: \"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-4\"),\n    d: \"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z\"\n  })), /*#__PURE__*/React.createElement(\"path\", {\n    className: \"\".concat(prefixCls, \"-path-5\"),\n    d: \"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z\"\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    className: \"\".concat(prefixCls, \"-g\"),\n    transform: \"translate(149.65 15.383)\"\n  }, /*#__PURE__*/React.createElement(\"ellipse\", {\n    cx: \"20.654\",\n    cy: \"3.167\",\n    rx: \"2.849\",\n    ry: \"2.815\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z\"\n  }))));\n};\nexport default Empty;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAC3B,IAAIC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAACH,aAAa,CAAC;IACrDI,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIC,SAAS,GAAGD,YAAY,CAAC,mBAAmB,CAAC;EACjD,OAAO,aAAaL,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEF,SAAS;IACpBG,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE;EACT,CAAC,EAAE,aAAaZ,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE;IACvCM,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAad,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE;IACvCQ,SAAS,EAAE;EACb,CAAC,EAAE,aAAaf,KAAK,CAACO,aAAa,CAAC,SAAS,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC;IAC3CW,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IAC1Ce,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IAC1Ce,CAAC,EAAE,0IAA0I;IAC7IN,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAaf,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IAC1Ce,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IAC1Ce,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC5CC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,SAAS,CAAC;IAC1Ce,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE;IACxCC,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACV,SAAS,EAAE,IAAI,CAAC;IACrCS,SAAS,EAAE;EACb,CAAC,EAAE,aAAaf,KAAK,CAACO,aAAa,CAAC,SAAS,EAAE;IAC7CU,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE;EACN,CAAC,CAAC,EAAE,aAAapB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC3Cc,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,eAAenB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}