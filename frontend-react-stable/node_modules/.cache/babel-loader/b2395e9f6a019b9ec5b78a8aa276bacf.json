{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport defaultLocale from '../locale/default';\nvar runtimeLocale = _extends({}, defaultLocale.Modal);\nexport function changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    runtimeLocale = _extends(_extends({}, runtimeLocale), newLocale);\n  } else {\n    runtimeLocale = _extends({}, defaultLocale.Modal);\n  }\n}\nexport function getConfirmLocale() {\n  return runtimeLocale;\n}", "map": {"version": 3, "names": ["_extends", "defaultLocale", "runtimeLocale", "Modal", "changeConfirmLocale", "newLocale", "getConfirmLocale"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/modal/locale.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport defaultLocale from '../locale/default';\nvar runtimeLocale = _extends({}, defaultLocale.Modal);\nexport function changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    runtimeLocale = _extends(_extends({}, runtimeLocale), newLocale);\n  } else {\n    runtimeLocale = _extends({}, defaultLocale.Modal);\n  }\n}\nexport function getConfirmLocale() {\n  return runtimeLocale;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,IAAIC,aAAa,GAAGF,QAAQ,CAAC,CAAC,CAAC,EAAEC,aAAa,CAACE,KAAK,CAAC;AACrD,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EAC7C,IAAIA,SAAS,EAAE;IACbH,aAAa,GAAGF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEE,aAAa,CAAC,EAAEG,SAAS,CAAC;EAClE,CAAC,MAAM;IACLH,aAAa,GAAGF,QAAQ,CAAC,CAAC,CAAC,EAAEC,aAAa,CAACE,KAAK,CAAC;EACnD;AACF;AACA,OAAO,SAASG,gBAAgBA,CAAA,EAAG;EACjC,OAAOJ,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}