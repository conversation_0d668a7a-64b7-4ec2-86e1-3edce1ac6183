{"ast": null, "code": "var createPadding = require('./_createPadding'),\n  stringSize = require('./_stringSize'),\n  toInteger = require('./toInteger'),\n  toString = require('./toString');\n\n/**\n * Pads `string` on the left side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padStart('abc', 6);\n * // => '   abc'\n *\n * _.padStart('abc', 6, '_-');\n * // => '_-_abc'\n *\n * _.padStart('abc', 3);\n * // => 'abc'\n */\nfunction padStart(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n  var strLength = length ? stringSize(string) : 0;\n  return length && strLength < length ? createPadding(length - strLength, chars) + string : string;\n}\nmodule.exports = padStart;", "map": {"version": 3, "names": ["createPadding", "require", "stringSize", "toInteger", "toString", "padStart", "string", "length", "chars", "str<PERSON><PERSON><PERSON>", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/padStart.js"], "sourcesContent": ["var createPadding = require('./_createPadding'),\n    stringSize = require('./_stringSize'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Pads `string` on the left side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padStart('abc', 6);\n * // => '   abc'\n *\n * _.padStart('abc', 6, '_-');\n * // => '_-_abc'\n *\n * _.padStart('abc', 3);\n * // => 'abc'\n */\nfunction padStart(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n\n  var strLength = length ? stringSize(string) : 0;\n  return (length && strLength < length)\n    ? (createPadding(length - strLength, chars) + string)\n    : string;\n}\n\nmodule.exports = padStart;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EAC3CC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;EAClCG,QAAQ,GAAGH,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACvCF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzBC,MAAM,GAAGJ,SAAS,CAACI,MAAM,CAAC;EAE1B,IAAIE,SAAS,GAAGF,MAAM,GAAGL,UAAU,CAACI,MAAM,CAAC,GAAG,CAAC;EAC/C,OAAQC,MAAM,IAAIE,SAAS,GAAGF,MAAM,GAC/BP,aAAa,CAACO,MAAM,GAAGE,SAAS,EAAED,KAAK,CAAC,GAAGF,MAAM,GAClDA,MAAM;AACZ;AAEAI,MAAM,CAACC,OAAO,GAAGN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}