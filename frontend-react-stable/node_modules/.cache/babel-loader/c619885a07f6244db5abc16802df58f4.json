{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Notice from './Notice';\nexport default function useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(Notice, _extends({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n          if (index === -1) {\n            return [].concat(_toConsumableArray(originElements), [noticeEle]);\n          }\n          var cloneList = _toConsumableArray(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n      firstMount = false;\n    });\n  }\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_extends", "_slicedToArray", "React", "Notice", "useNotification", "notificationInstance", "createdRef", "useRef", "_React$useState", "useState", "_React$useState2", "elements", "setElements", "notify", "noticeProps", "firstMount", "add", "div", "props", "key", "current", "noticeEle", "createElement", "holder", "originElements", "index", "findIndex", "ele", "concat", "cloneList", "Fragment"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-notification/es/useNotification.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Notice from './Notice';\nexport default function useNotification(notificationInstance) {\n  var createdRef = React.useRef({});\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    elements = _React$useState2[0],\n    setElements = _React$useState2[1];\n  function notify(noticeProps) {\n    var firstMount = true;\n    notificationInstance.add(noticeProps, function (div, props) {\n      var key = props.key;\n      if (div && (!createdRef.current[key] || firstMount)) {\n        var noticeEle = /*#__PURE__*/React.createElement(Notice, _extends({}, props, {\n          holder: div\n        }));\n        createdRef.current[key] = noticeEle;\n        setElements(function (originElements) {\n          var index = originElements.findIndex(function (ele) {\n            return ele.key === props.key;\n          });\n          if (index === -1) {\n            return [].concat(_toConsumableArray(originElements), [noticeEle]);\n          }\n          var cloneList = _toConsumableArray(originElements);\n          cloneList[index] = noticeEle;\n          return cloneList;\n        });\n      }\n      firstMount = false;\n    });\n  }\n  return [notify, /*#__PURE__*/React.createElement(React.Fragment, null, elements)];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,eAAe,SAASC,eAAeA,CAACC,oBAAoB,EAAE;EAC5D,IAAIC,UAAU,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAAC,EAAE,CAAC;IACtCC,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,SAASG,MAAMA,CAACC,WAAW,EAAE;IAC3B,IAAIC,UAAU,GAAG,IAAI;IACrBV,oBAAoB,CAACW,GAAG,CAACF,WAAW,EAAE,UAAUG,GAAG,EAAEC,KAAK,EAAE;MAC1D,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;MACnB,IAAIF,GAAG,KAAK,CAACX,UAAU,CAACc,OAAO,CAACD,GAAG,CAAC,IAAIJ,UAAU,CAAC,EAAE;QACnD,IAAIM,SAAS,GAAG,aAAanB,KAAK,CAACoB,aAAa,CAACnB,MAAM,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;UAC3EK,MAAM,EAAEN;QACV,CAAC,CAAC,CAAC;QACHX,UAAU,CAACc,OAAO,CAACD,GAAG,CAAC,GAAGE,SAAS;QACnCT,WAAW,CAAC,UAAUY,cAAc,EAAE;UACpC,IAAIC,KAAK,GAAGD,cAAc,CAACE,SAAS,CAAC,UAAUC,GAAG,EAAE;YAClD,OAAOA,GAAG,CAACR,GAAG,KAAKD,KAAK,CAACC,GAAG;UAC9B,CAAC,CAAC;UACF,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,EAAE,CAACG,MAAM,CAAC7B,kBAAkB,CAACyB,cAAc,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;UACnE;UACA,IAAIQ,SAAS,GAAG9B,kBAAkB,CAACyB,cAAc,CAAC;UAClDK,SAAS,CAACJ,KAAK,CAAC,GAAGJ,SAAS;UAC5B,OAAOQ,SAAS;QAClB,CAAC,CAAC;MACJ;MACAd,UAAU,GAAG,KAAK;IACpB,CAAC,CAAC;EACJ;EACA,OAAO,CAACF,MAAM,EAAE,aAAaX,KAAK,CAACoB,aAAa,CAACpB,KAAK,CAAC4B,QAAQ,EAAE,IAAI,EAAEnB,QAAQ,CAAC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}