{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if ((typeof Reflect === \"undefined\" ? \"undefined\" : _typeof(Reflect)) === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) {\n    if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  }\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { throttleByAnimationFrameDecorator } from '../_util/throttleByAnimationFrame';\nimport { addObserveTarget, getFixedBottom, getFixedTop, getTargetRect, removeObserveTarget } from './utils';\nfunction getDefaultTarget() {\n  return typeof window !== 'undefined' ? window : null;\n}\nvar AffixStatus;\n(function (AffixStatus) {\n  AffixStatus[AffixStatus[\"None\"] = 0] = \"None\";\n  AffixStatus[AffixStatus[\"Prepare\"] = 1] = \"Prepare\";\n})(AffixStatus || (AffixStatus = {}));\nvar Affix = /*#__PURE__*/function (_React$Component) {\n  _inherits(Affix, _React$Component);\n  var _super = _createSuper(Affix);\n  function Affix() {\n    var _this;\n    _classCallCheck(this, Affix);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      status: AffixStatus.None,\n      lastAffix: false,\n      prevTarget: null\n    };\n    _this.getOffsetTop = function () {\n      var _this$props = _this.props,\n        offsetBottom = _this$props.offsetBottom,\n        offsetTop = _this$props.offsetTop;\n      return offsetBottom === undefined && offsetTop === undefined ? 0 : offsetTop;\n    };\n    _this.getOffsetBottom = function () {\n      return _this.props.offsetBottom;\n    };\n    _this.savePlaceholderNode = function (node) {\n      _this.placeholderNode = node;\n    };\n    _this.saveFixedNode = function (node) {\n      _this.fixedNode = node;\n    };\n    // =================== Measure ===================\n    _this.measure = function () {\n      var _this$state = _this.state,\n        status = _this$state.status,\n        lastAffix = _this$state.lastAffix;\n      var onChange = _this.props.onChange;\n      var targetFunc = _this.getTargetFunc();\n      if (status !== AffixStatus.Prepare || !_this.fixedNode || !_this.placeholderNode || !targetFunc) {\n        return;\n      }\n      var offsetTop = _this.getOffsetTop();\n      var offsetBottom = _this.getOffsetBottom();\n      var targetNode = targetFunc();\n      if (!targetNode) {\n        return;\n      }\n      var newState = {\n        status: AffixStatus.None\n      };\n      var targetRect = getTargetRect(targetNode);\n      var placeholderReact = getTargetRect(_this.placeholderNode);\n      var fixedTop = getFixedTop(placeholderReact, targetRect, offsetTop);\n      var fixedBottom = getFixedBottom(placeholderReact, targetRect, offsetBottom);\n      if (fixedTop !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          top: fixedTop,\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n      } else if (fixedBottom !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          bottom: fixedBottom,\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n      }\n      newState.lastAffix = !!newState.affixStyle;\n      if (onChange && lastAffix !== newState.lastAffix) {\n        onChange(newState.lastAffix);\n      }\n      _this.setState(newState);\n    };\n    // @ts-ignore TS6133\n    _this.prepareMeasure = function () {\n      // event param is used before. Keep compatible ts define here.\n      _this.setState({\n        status: AffixStatus.Prepare,\n        affixStyle: undefined,\n        placeholderStyle: undefined\n      });\n      // Test if `updatePosition` called\n      if (process.env.NODE_ENV === 'test') {\n        var onTestUpdatePosition = _this.props.onTestUpdatePosition;\n        onTestUpdatePosition === null || onTestUpdatePosition === void 0 ? void 0 : onTestUpdatePosition();\n      }\n    };\n    return _this;\n  }\n  _createClass(Affix, [{\n    key: \"getTargetFunc\",\n    value: function getTargetFunc() {\n      var getTargetContainer = this.context.getTargetContainer;\n      var target = this.props.target;\n      if (target !== undefined) {\n        return target;\n      }\n      return getTargetContainer !== null && getTargetContainer !== void 0 ? getTargetContainer : getDefaultTarget;\n    }\n    // Event handler\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      var targetFunc = this.getTargetFunc();\n      if (targetFunc) {\n        // [Legacy] Wait for parent component ref has its value.\n        // We should use target as directly element instead of function which makes element check hard.\n        this.timeout = setTimeout(function () {\n          addObserveTarget(targetFunc(), _this2);\n          // Mock Event object.\n          _this2.updatePosition();\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var prevTarget = this.state.prevTarget;\n      var targetFunc = this.getTargetFunc();\n      var newTarget = (targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc()) || null;\n      if (prevTarget !== newTarget) {\n        removeObserveTarget(this);\n        if (newTarget) {\n          addObserveTarget(newTarget, this);\n          // Mock Event object.\n          this.updatePosition();\n        }\n        // eslint-disable-next-line react/no-did-update-set-state\n        this.setState({\n          prevTarget: newTarget\n        });\n      }\n      if (prevProps.offsetTop !== this.props.offsetTop || prevProps.offsetBottom !== this.props.offsetBottom) {\n        this.updatePosition();\n      }\n      this.measure();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      clearTimeout(this.timeout);\n      removeObserveTarget(this);\n      this.updatePosition.cancel();\n      // https://github.com/ant-design/ant-design/issues/22683\n      this.lazyUpdatePosition.cancel();\n    }\n    // Handle realign logic\n  }, {\n    key: \"updatePosition\",\n    value: function updatePosition() {\n      this.prepareMeasure();\n    }\n  }, {\n    key: \"lazyUpdatePosition\",\n    value: function lazyUpdatePosition() {\n      var targetFunc = this.getTargetFunc();\n      var affixStyle = this.state.affixStyle;\n      // Check position change before measure to make Safari smooth\n      if (targetFunc && affixStyle) {\n        var offsetTop = this.getOffsetTop();\n        var offsetBottom = this.getOffsetBottom();\n        var targetNode = targetFunc();\n        if (targetNode && this.placeholderNode) {\n          var targetRect = getTargetRect(targetNode);\n          var placeholderReact = getTargetRect(this.placeholderNode);\n          var fixedTop = getFixedTop(placeholderReact, targetRect, offsetTop);\n          var fixedBottom = getFixedBottom(placeholderReact, targetRect, offsetBottom);\n          if (fixedTop !== undefined && affixStyle.top === fixedTop || fixedBottom !== undefined && affixStyle.bottom === fixedBottom) {\n            return;\n          }\n        }\n      }\n      // Directly call prepare measure since it's already throttled.\n      this.prepareMeasure();\n    }\n    // =================== Render ===================\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$state2 = this.state,\n        affixStyle = _this$state2.affixStyle,\n        placeholderStyle = _this$state2.placeholderStyle;\n      var _this$props2 = this.props,\n        affixPrefixCls = _this$props2.affixPrefixCls,\n        children = _this$props2.children;\n      var className = classNames(_defineProperty({}, affixPrefixCls, !!affixStyle));\n      var props = omit(this.props, ['prefixCls', 'offsetTop', 'offsetBottom', 'target', 'onChange', 'affixPrefixCls']);\n      // Omit this since `onTestUpdatePosition` only works on test.\n      if (process.env.NODE_ENV === 'test') {\n        props = omit(props, ['onTestUpdatePosition']);\n      }\n      return /*#__PURE__*/React.createElement(ResizeObserver, {\n        onResize: function onResize() {\n          _this3.updatePosition();\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", _extends({}, props, {\n        ref: this.savePlaceholderNode\n      }), affixStyle && /*#__PURE__*/React.createElement(\"div\", {\n        style: placeholderStyle,\n        \"aria-hidden\": \"true\"\n      }), /*#__PURE__*/React.createElement(\"div\", {\n        className: className,\n        ref: this.saveFixedNode,\n        style: affixStyle\n      }, /*#__PURE__*/React.createElement(ResizeObserver, {\n        onResize: function onResize() {\n          _this3.updatePosition();\n        }\n      }, children))));\n    }\n  }]);\n  return Affix;\n}(React.Component);\nAffix.contextType = ConfigContext;\n__decorate([throttleByAnimationFrameDecorator()], Affix.prototype, \"updatePosition\", null);\n__decorate([throttleByAnimationFrameDecorator()], Affix.prototype, \"lazyUpdatePosition\", null);\nvar AffixFC = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var affixPrefixCls = getPrefixCls('affix', customizePrefixCls);\n  var affixProps = _extends(_extends({}, props), {\n    affixPrefixCls: affixPrefixCls\n  });\n  return /*#__PURE__*/React.createElement(Affix, _extends({}, affixProps, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  AffixFC.displayName = 'Affix';\n}\nexport default AffixFC;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "_typeof", "__decorate", "decorators", "target", "key", "desc", "c", "arguments", "length", "r", "Object", "getOwnPropertyDescriptor", "d", "Reflect", "decorate", "i", "defineProperty", "classNames", "ResizeObserver", "omit", "React", "ConfigContext", "throttleByAnimationFrameDecorator", "addObserveTarget", "getFixedBottom", "getFixedTop", "getTargetRect", "removeObserveTarget", "getDefaultTarget", "window", "AffixStatus", "Affix", "_React$Component", "_super", "_this", "apply", "state", "status", "None", "lastAffix", "prevTarget", "getOffsetTop", "_this$props", "props", "offsetBottom", "offsetTop", "undefined", "getOffsetBottom", "savePlaceholderNode", "node", "placeholderNode", "saveFixedNode", "fixedNode", "measure", "_this$state", "onChange", "targetFunc", "getTargetFunc", "Prepare", "targetNode", "newState", "targetRect", "placeholder<PERSON><PERSON><PERSON>", "fixedTop", "fixedBottom", "affixStyle", "position", "top", "width", "height", "placeholder<PERSON><PERSON><PERSON>", "bottom", "setState", "prepareMeasure", "process", "env", "NODE_ENV", "onTestUpdatePosition", "value", "getTargetContainer", "context", "componentDidMount", "_this2", "timeout", "setTimeout", "updatePosition", "componentDidUpdate", "prevProps", "newTarget", "componentWillUnmount", "clearTimeout", "cancel", "lazyUpdatePosition", "render", "_this3", "_this$state2", "_this$props2", "affixPrefixCls", "children", "className", "createElement", "onResize", "ref", "style", "Component", "contextType", "prototype", "AffixFC", "forwardRef", "customizePrefixCls", "prefixCls", "_React$useContext", "useContext", "getPrefixCls", "affixProps", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/affix/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if ((typeof Reflect === \"undefined\" ? \"undefined\" : _typeof(Reflect)) === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) {\n    if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  }\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { throttleByAnimationFrameDecorator } from '../_util/throttleByAnimationFrame';\nimport { addObserveTarget, getFixedBottom, getFixedTop, getTargetRect, removeObserveTarget } from './utils';\nfunction getDefaultTarget() {\n  return typeof window !== 'undefined' ? window : null;\n}\nvar AffixStatus;\n(function (AffixStatus) {\n  AffixStatus[AffixStatus[\"None\"] = 0] = \"None\";\n  AffixStatus[AffixStatus[\"Prepare\"] = 1] = \"Prepare\";\n})(AffixStatus || (AffixStatus = {}));\nvar Affix = /*#__PURE__*/function (_React$Component) {\n  _inherits(Affix, _React$Component);\n  var _super = _createSuper(Affix);\n  function Affix() {\n    var _this;\n    _classCallCheck(this, Affix);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      status: AffixStatus.None,\n      lastAffix: false,\n      prevTarget: null\n    };\n    _this.getOffsetTop = function () {\n      var _this$props = _this.props,\n        offsetBottom = _this$props.offsetBottom,\n        offsetTop = _this$props.offsetTop;\n      return offsetBottom === undefined && offsetTop === undefined ? 0 : offsetTop;\n    };\n    _this.getOffsetBottom = function () {\n      return _this.props.offsetBottom;\n    };\n    _this.savePlaceholderNode = function (node) {\n      _this.placeholderNode = node;\n    };\n    _this.saveFixedNode = function (node) {\n      _this.fixedNode = node;\n    };\n    // =================== Measure ===================\n    _this.measure = function () {\n      var _this$state = _this.state,\n        status = _this$state.status,\n        lastAffix = _this$state.lastAffix;\n      var onChange = _this.props.onChange;\n      var targetFunc = _this.getTargetFunc();\n      if (status !== AffixStatus.Prepare || !_this.fixedNode || !_this.placeholderNode || !targetFunc) {\n        return;\n      }\n      var offsetTop = _this.getOffsetTop();\n      var offsetBottom = _this.getOffsetBottom();\n      var targetNode = targetFunc();\n      if (!targetNode) {\n        return;\n      }\n      var newState = {\n        status: AffixStatus.None\n      };\n      var targetRect = getTargetRect(targetNode);\n      var placeholderReact = getTargetRect(_this.placeholderNode);\n      var fixedTop = getFixedTop(placeholderReact, targetRect, offsetTop);\n      var fixedBottom = getFixedBottom(placeholderReact, targetRect, offsetBottom);\n      if (fixedTop !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          top: fixedTop,\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n      } else if (fixedBottom !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          bottom: fixedBottom,\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderReact.width,\n          height: placeholderReact.height\n        };\n      }\n      newState.lastAffix = !!newState.affixStyle;\n      if (onChange && lastAffix !== newState.lastAffix) {\n        onChange(newState.lastAffix);\n      }\n      _this.setState(newState);\n    };\n    // @ts-ignore TS6133\n    _this.prepareMeasure = function () {\n      // event param is used before. Keep compatible ts define here.\n      _this.setState({\n        status: AffixStatus.Prepare,\n        affixStyle: undefined,\n        placeholderStyle: undefined\n      });\n      // Test if `updatePosition` called\n      if (process.env.NODE_ENV === 'test') {\n        var onTestUpdatePosition = _this.props.onTestUpdatePosition;\n        onTestUpdatePosition === null || onTestUpdatePosition === void 0 ? void 0 : onTestUpdatePosition();\n      }\n    };\n    return _this;\n  }\n  _createClass(Affix, [{\n    key: \"getTargetFunc\",\n    value: function getTargetFunc() {\n      var getTargetContainer = this.context.getTargetContainer;\n      var target = this.props.target;\n      if (target !== undefined) {\n        return target;\n      }\n      return getTargetContainer !== null && getTargetContainer !== void 0 ? getTargetContainer : getDefaultTarget;\n    }\n    // Event handler\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this2 = this;\n      var targetFunc = this.getTargetFunc();\n      if (targetFunc) {\n        // [Legacy] Wait for parent component ref has its value.\n        // We should use target as directly element instead of function which makes element check hard.\n        this.timeout = setTimeout(function () {\n          addObserveTarget(targetFunc(), _this2);\n          // Mock Event object.\n          _this2.updatePosition();\n        });\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var prevTarget = this.state.prevTarget;\n      var targetFunc = this.getTargetFunc();\n      var newTarget = (targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc()) || null;\n      if (prevTarget !== newTarget) {\n        removeObserveTarget(this);\n        if (newTarget) {\n          addObserveTarget(newTarget, this);\n          // Mock Event object.\n          this.updatePosition();\n        }\n        // eslint-disable-next-line react/no-did-update-set-state\n        this.setState({\n          prevTarget: newTarget\n        });\n      }\n      if (prevProps.offsetTop !== this.props.offsetTop || prevProps.offsetBottom !== this.props.offsetBottom) {\n        this.updatePosition();\n      }\n      this.measure();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      clearTimeout(this.timeout);\n      removeObserveTarget(this);\n      this.updatePosition.cancel();\n      // https://github.com/ant-design/ant-design/issues/22683\n      this.lazyUpdatePosition.cancel();\n    }\n    // Handle realign logic\n  }, {\n    key: \"updatePosition\",\n    value: function updatePosition() {\n      this.prepareMeasure();\n    }\n  }, {\n    key: \"lazyUpdatePosition\",\n    value: function lazyUpdatePosition() {\n      var targetFunc = this.getTargetFunc();\n      var affixStyle = this.state.affixStyle;\n      // Check position change before measure to make Safari smooth\n      if (targetFunc && affixStyle) {\n        var offsetTop = this.getOffsetTop();\n        var offsetBottom = this.getOffsetBottom();\n        var targetNode = targetFunc();\n        if (targetNode && this.placeholderNode) {\n          var targetRect = getTargetRect(targetNode);\n          var placeholderReact = getTargetRect(this.placeholderNode);\n          var fixedTop = getFixedTop(placeholderReact, targetRect, offsetTop);\n          var fixedBottom = getFixedBottom(placeholderReact, targetRect, offsetBottom);\n          if (fixedTop !== undefined && affixStyle.top === fixedTop || fixedBottom !== undefined && affixStyle.bottom === fixedBottom) {\n            return;\n          }\n        }\n      }\n      // Directly call prepare measure since it's already throttled.\n      this.prepareMeasure();\n    }\n    // =================== Render ===================\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this3 = this;\n      var _this$state2 = this.state,\n        affixStyle = _this$state2.affixStyle,\n        placeholderStyle = _this$state2.placeholderStyle;\n      var _this$props2 = this.props,\n        affixPrefixCls = _this$props2.affixPrefixCls,\n        children = _this$props2.children;\n      var className = classNames(_defineProperty({}, affixPrefixCls, !!affixStyle));\n      var props = omit(this.props, ['prefixCls', 'offsetTop', 'offsetBottom', 'target', 'onChange', 'affixPrefixCls']);\n      // Omit this since `onTestUpdatePosition` only works on test.\n      if (process.env.NODE_ENV === 'test') {\n        props = omit(props, ['onTestUpdatePosition']);\n      }\n      return /*#__PURE__*/React.createElement(ResizeObserver, {\n        onResize: function onResize() {\n          _this3.updatePosition();\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", _extends({}, props, {\n        ref: this.savePlaceholderNode\n      }), affixStyle && /*#__PURE__*/React.createElement(\"div\", {\n        style: placeholderStyle,\n        \"aria-hidden\": \"true\"\n      }), /*#__PURE__*/React.createElement(\"div\", {\n        className: className,\n        ref: this.saveFixedNode,\n        style: affixStyle\n      }, /*#__PURE__*/React.createElement(ResizeObserver, {\n        onResize: function onResize() {\n          _this3.updatePosition();\n        }\n      }, children))));\n    }\n  }]);\n  return Affix;\n}(React.Component);\nAffix.contextType = ConfigContext;\n__decorate([throttleByAnimationFrameDecorator()], Affix.prototype, \"updatePosition\", null);\n__decorate([throttleByAnimationFrameDecorator()], Affix.prototype, \"lazyUpdatePosition\", null);\nvar AffixFC = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var customizePrefixCls = props.prefixCls;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var affixPrefixCls = getPrefixCls('affix', customizePrefixCls);\n  var affixProps = _extends(_extends({}, props), {\n    affixPrefixCls: affixPrefixCls\n  });\n  return /*#__PURE__*/React.createElement(Affix, _extends({}, affixProps, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  AffixFC.displayName = 'Affix';\n}\nexport default AffixFC;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,UAAU,GAAG,IAAI,IAAI,IAAI,CAACA,UAAU,IAAI,UAAUC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnF,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM;IACtBC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGH,MAAM,GAAGE,IAAI,KAAK,IAAI,GAAGA,IAAI,GAAGK,MAAM,CAACC,wBAAwB,CAACR,MAAM,EAAEC,GAAG,CAAC,GAAGC,IAAI;IAC/FO,CAAC;EACH,IAAI,CAAC,OAAOC,OAAO,KAAK,WAAW,GAAG,WAAW,GAAGb,OAAO,CAACa,OAAO,CAAC,MAAM,QAAQ,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,UAAU,EAAEL,CAAC,GAAGI,OAAO,CAACC,QAAQ,CAACZ,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,KAAK,KAAK,IAAIU,CAAC,GAAGb,UAAU,CAACM,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtO,IAAIH,CAAC,GAAGV,UAAU,CAACa,CAAC,CAAC,EAAEN,CAAC,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACH,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGM,CAAC,CAACT,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,GAAGG,CAAC,CAACT,MAAM,EAAEC,GAAG,CAAC,KAAKK,CAAC;EAC7F;EACA,OAAOH,CAAC,GAAG,CAAC,IAAIG,CAAC,IAAIC,MAAM,CAACM,cAAc,CAACb,MAAM,EAAEC,GAAG,EAAEK,CAAC,CAAC,EAAEA,CAAC;AAC/D,CAAC;AACD,OAAOQ,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iCAAiC,QAAQ,mCAAmC;AACrF,SAASC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,SAAS;AAC3G,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;AACtD;AACA,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACtBA,WAAW,CAACA,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7CA,WAAW,CAACA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;AACrD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnDlC,SAAS,CAACiC,KAAK,EAAEC,gBAAgB,CAAC;EAClC,IAAIC,MAAM,GAAGlC,YAAY,CAACgC,KAAK,CAAC;EAChC,SAASA,KAAKA,CAAA,EAAG;IACf,IAAIG,KAAK;IACTtC,eAAe,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC5BG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAE5B,SAAS,CAAC;IACrC2B,KAAK,CAACE,KAAK,GAAG;MACZC,MAAM,EAAEP,WAAW,CAACQ,IAAI;MACxBC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE;IACd,CAAC;IACDN,KAAK,CAACO,YAAY,GAAG,YAAY;MAC/B,IAAIC,WAAW,GAAGR,KAAK,CAACS,KAAK;QAC3BC,YAAY,GAAGF,WAAW,CAACE,YAAY;QACvCC,SAAS,GAAGH,WAAW,CAACG,SAAS;MACnC,OAAOD,YAAY,KAAKE,SAAS,IAAID,SAAS,KAAKC,SAAS,GAAG,CAAC,GAAGD,SAAS;IAC9E,CAAC;IACDX,KAAK,CAACa,eAAe,GAAG,YAAY;MAClC,OAAOb,KAAK,CAACS,KAAK,CAACC,YAAY;IACjC,CAAC;IACDV,KAAK,CAACc,mBAAmB,GAAG,UAAUC,IAAI,EAAE;MAC1Cf,KAAK,CAACgB,eAAe,GAAGD,IAAI;IAC9B,CAAC;IACDf,KAAK,CAACiB,aAAa,GAAG,UAAUF,IAAI,EAAE;MACpCf,KAAK,CAACkB,SAAS,GAAGH,IAAI;IACxB,CAAC;IACD;IACAf,KAAK,CAACmB,OAAO,GAAG,YAAY;MAC1B,IAAIC,WAAW,GAAGpB,KAAK,CAACE,KAAK;QAC3BC,MAAM,GAAGiB,WAAW,CAACjB,MAAM;QAC3BE,SAAS,GAAGe,WAAW,CAACf,SAAS;MACnC,IAAIgB,QAAQ,GAAGrB,KAAK,CAACS,KAAK,CAACY,QAAQ;MACnC,IAAIC,UAAU,GAAGtB,KAAK,CAACuB,aAAa,CAAC,CAAC;MACtC,IAAIpB,MAAM,KAAKP,WAAW,CAAC4B,OAAO,IAAI,CAACxB,KAAK,CAACkB,SAAS,IAAI,CAAClB,KAAK,CAACgB,eAAe,IAAI,CAACM,UAAU,EAAE;QAC/F;MACF;MACA,IAAIX,SAAS,GAAGX,KAAK,CAACO,YAAY,CAAC,CAAC;MACpC,IAAIG,YAAY,GAAGV,KAAK,CAACa,eAAe,CAAC,CAAC;MAC1C,IAAIY,UAAU,GAAGH,UAAU,CAAC,CAAC;MAC7B,IAAI,CAACG,UAAU,EAAE;QACf;MACF;MACA,IAAIC,QAAQ,GAAG;QACbvB,MAAM,EAAEP,WAAW,CAACQ;MACtB,CAAC;MACD,IAAIuB,UAAU,GAAGnC,aAAa,CAACiC,UAAU,CAAC;MAC1C,IAAIG,gBAAgB,GAAGpC,aAAa,CAACQ,KAAK,CAACgB,eAAe,CAAC;MAC3D,IAAIa,QAAQ,GAAGtC,WAAW,CAACqC,gBAAgB,EAAED,UAAU,EAAEhB,SAAS,CAAC;MACnE,IAAImB,WAAW,GAAGxC,cAAc,CAACsC,gBAAgB,EAAED,UAAU,EAAEjB,YAAY,CAAC;MAC5E,IAAImB,QAAQ,KAAKjB,SAAS,EAAE;QAC1Bc,QAAQ,CAACK,UAAU,GAAG;UACpBC,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAEJ,QAAQ;UACbK,KAAK,EAAEN,gBAAgB,CAACM,KAAK;UAC7BC,MAAM,EAAEP,gBAAgB,CAACO;QAC3B,CAAC;QACDT,QAAQ,CAACU,gBAAgB,GAAG;UAC1BF,KAAK,EAAEN,gBAAgB,CAACM,KAAK;UAC7BC,MAAM,EAAEP,gBAAgB,CAACO;QAC3B,CAAC;MACH,CAAC,MAAM,IAAIL,WAAW,KAAKlB,SAAS,EAAE;QACpCc,QAAQ,CAACK,UAAU,GAAG;UACpBC,QAAQ,EAAE,OAAO;UACjBK,MAAM,EAAEP,WAAW;UACnBI,KAAK,EAAEN,gBAAgB,CAACM,KAAK;UAC7BC,MAAM,EAAEP,gBAAgB,CAACO;QAC3B,CAAC;QACDT,QAAQ,CAACU,gBAAgB,GAAG;UAC1BF,KAAK,EAAEN,gBAAgB,CAACM,KAAK;UAC7BC,MAAM,EAAEP,gBAAgB,CAACO;QAC3B,CAAC;MACH;MACAT,QAAQ,CAACrB,SAAS,GAAG,CAAC,CAACqB,QAAQ,CAACK,UAAU;MAC1C,IAAIV,QAAQ,IAAIhB,SAAS,KAAKqB,QAAQ,CAACrB,SAAS,EAAE;QAChDgB,QAAQ,CAACK,QAAQ,CAACrB,SAAS,CAAC;MAC9B;MACAL,KAAK,CAACsC,QAAQ,CAACZ,QAAQ,CAAC;IAC1B,CAAC;IACD;IACA1B,KAAK,CAACuC,cAAc,GAAG,YAAY;MACjC;MACAvC,KAAK,CAACsC,QAAQ,CAAC;QACbnC,MAAM,EAAEP,WAAW,CAAC4B,OAAO;QAC3BO,UAAU,EAAEnB,SAAS;QACrBwB,gBAAgB,EAAExB;MACpB,CAAC,CAAC;MACF;MACA,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAIC,oBAAoB,GAAG3C,KAAK,CAACS,KAAK,CAACkC,oBAAoB;QAC3DA,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC,CAAC;MACpG;IACF,CAAC;IACD,OAAO3C,KAAK;EACd;EACArC,YAAY,CAACkC,KAAK,EAAE,CAAC;IACnB3B,GAAG,EAAE,eAAe;IACpB0E,KAAK,EAAE,SAASrB,aAAaA,CAAA,EAAG;MAC9B,IAAIsB,kBAAkB,GAAG,IAAI,CAACC,OAAO,CAACD,kBAAkB;MACxD,IAAI5E,MAAM,GAAG,IAAI,CAACwC,KAAK,CAACxC,MAAM;MAC9B,IAAIA,MAAM,KAAK2C,SAAS,EAAE;QACxB,OAAO3C,MAAM;MACf;MACA,OAAO4E,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGnD,gBAAgB;IAC7G;IACA;EACF,CAAC,EAAE;IACDxB,GAAG,EAAE,mBAAmB;IACxB0E,KAAK,EAAE,SAASG,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI1B,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACrC,IAAID,UAAU,EAAE;QACd;QACA;QACA,IAAI,CAAC2B,OAAO,GAAGC,UAAU,CAAC,YAAY;UACpC7D,gBAAgB,CAACiC,UAAU,CAAC,CAAC,EAAE0B,MAAM,CAAC;UACtC;UACAA,MAAM,CAACG,cAAc,CAAC,CAAC;QACzB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDjF,GAAG,EAAE,oBAAoB;IACzB0E,KAAK,EAAE,SAASQ,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAI/C,UAAU,GAAG,IAAI,CAACJ,KAAK,CAACI,UAAU;MACtC,IAAIgB,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACrC,IAAI+B,SAAS,GAAG,CAAChC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC,CAAC,KAAK,IAAI;MAC9F,IAAIhB,UAAU,KAAKgD,SAAS,EAAE;QAC5B7D,mBAAmB,CAAC,IAAI,CAAC;QACzB,IAAI6D,SAAS,EAAE;UACbjE,gBAAgB,CAACiE,SAAS,EAAE,IAAI,CAAC;UACjC;UACA,IAAI,CAACH,cAAc,CAAC,CAAC;QACvB;QACA;QACA,IAAI,CAACb,QAAQ,CAAC;UACZhC,UAAU,EAAEgD;QACd,CAAC,CAAC;MACJ;MACA,IAAID,SAAS,CAAC1C,SAAS,KAAK,IAAI,CAACF,KAAK,CAACE,SAAS,IAAI0C,SAAS,CAAC3C,YAAY,KAAK,IAAI,CAACD,KAAK,CAACC,YAAY,EAAE;QACtG,IAAI,CAACyC,cAAc,CAAC,CAAC;MACvB;MACA,IAAI,CAAChC,OAAO,CAAC,CAAC;IAChB;EACF,CAAC,EAAE;IACDjD,GAAG,EAAE,sBAAsB;IAC3B0E,KAAK,EAAE,SAASW,oBAAoBA,CAAA,EAAG;MACrCC,YAAY,CAAC,IAAI,CAACP,OAAO,CAAC;MAC1BxD,mBAAmB,CAAC,IAAI,CAAC;MACzB,IAAI,CAAC0D,cAAc,CAACM,MAAM,CAAC,CAAC;MAC5B;MACA,IAAI,CAACC,kBAAkB,CAACD,MAAM,CAAC,CAAC;IAClC;IACA;EACF,CAAC,EAAE;IACDvF,GAAG,EAAE,gBAAgB;IACrB0E,KAAK,EAAE,SAASO,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAACZ,cAAc,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,oBAAoB;IACzB0E,KAAK,EAAE,SAASc,kBAAkBA,CAAA,EAAG;MACnC,IAAIpC,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MACrC,IAAIQ,UAAU,GAAG,IAAI,CAAC7B,KAAK,CAAC6B,UAAU;MACtC;MACA,IAAIT,UAAU,IAAIS,UAAU,EAAE;QAC5B,IAAIpB,SAAS,GAAG,IAAI,CAACJ,YAAY,CAAC,CAAC;QACnC,IAAIG,YAAY,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC;QACzC,IAAIY,UAAU,GAAGH,UAAU,CAAC,CAAC;QAC7B,IAAIG,UAAU,IAAI,IAAI,CAACT,eAAe,EAAE;UACtC,IAAIW,UAAU,GAAGnC,aAAa,CAACiC,UAAU,CAAC;UAC1C,IAAIG,gBAAgB,GAAGpC,aAAa,CAAC,IAAI,CAACwB,eAAe,CAAC;UAC1D,IAAIa,QAAQ,GAAGtC,WAAW,CAACqC,gBAAgB,EAAED,UAAU,EAAEhB,SAAS,CAAC;UACnE,IAAImB,WAAW,GAAGxC,cAAc,CAACsC,gBAAgB,EAAED,UAAU,EAAEjB,YAAY,CAAC;UAC5E,IAAImB,QAAQ,KAAKjB,SAAS,IAAImB,UAAU,CAACE,GAAG,KAAKJ,QAAQ,IAAIC,WAAW,KAAKlB,SAAS,IAAImB,UAAU,CAACM,MAAM,KAAKP,WAAW,EAAE;YAC3H;UACF;QACF;MACF;MACA;MACA,IAAI,CAACS,cAAc,CAAC,CAAC;IACvB;IACA;EACF,CAAC,EAAE;IACDrE,GAAG,EAAE,QAAQ;IACb0E,KAAK,EAAE,SAASe,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC3D,KAAK;QAC3B6B,UAAU,GAAG8B,YAAY,CAAC9B,UAAU;QACpCK,gBAAgB,GAAGyB,YAAY,CAACzB,gBAAgB;MAClD,IAAI0B,YAAY,GAAG,IAAI,CAACrD,KAAK;QAC3BsD,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;MAClC,IAAIC,SAAS,GAAGlF,UAAU,CAACtB,eAAe,CAAC,CAAC,CAAC,EAAEsG,cAAc,EAAE,CAAC,CAAChC,UAAU,CAAC,CAAC;MAC7E,IAAItB,KAAK,GAAGxB,IAAI,CAAC,IAAI,CAACwB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;MAChH;MACA,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;QACnCjC,KAAK,GAAGxB,IAAI,CAACwB,KAAK,EAAE,CAAC,sBAAsB,CAAC,CAAC;MAC/C;MACA,OAAO,aAAavB,KAAK,CAACgF,aAAa,CAAClF,cAAc,EAAE;QACtDmF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC5BP,MAAM,CAACT,cAAc,CAAC,CAAC;QACzB;MACF,CAAC,EAAE,aAAajE,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE1G,QAAQ,CAAC,CAAC,CAAC,EAAEiD,KAAK,EAAE;QAC7D2D,GAAG,EAAE,IAAI,CAACtD;MACZ,CAAC,CAAC,EAAEiB,UAAU,IAAI,aAAa7C,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QACxDG,KAAK,EAAEjC,gBAAgB;QACvB,aAAa,EAAE;MACjB,CAAC,CAAC,EAAE,aAAalD,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QAC1CD,SAAS,EAAEA,SAAS;QACpBG,GAAG,EAAE,IAAI,CAACnD,aAAa;QACvBoD,KAAK,EAAEtC;MACT,CAAC,EAAE,aAAa7C,KAAK,CAACgF,aAAa,CAAClF,cAAc,EAAE;QAClDmF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC5BP,MAAM,CAACT,cAAc,CAAC,CAAC;QACzB;MACF,CAAC,EAAEa,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAOnE,KAAK;AACd,CAAC,CAACX,KAAK,CAACoF,SAAS,CAAC;AAClBzE,KAAK,CAAC0E,WAAW,GAAGpF,aAAa;AACjCpB,UAAU,CAAC,CAACqB,iCAAiC,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC2E,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC;AAC1FzG,UAAU,CAAC,CAACqB,iCAAiC,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC2E,SAAS,EAAE,oBAAoB,EAAE,IAAI,CAAC;AAC9F,IAAIC,OAAO,GAAG,aAAavF,KAAK,CAACwF,UAAU,CAAC,UAAUjE,KAAK,EAAE2D,GAAG,EAAE;EAChE,IAAIO,kBAAkB,GAAGlE,KAAK,CAACmE,SAAS;EACxC,IAAIC,iBAAiB,GAAG3F,KAAK,CAAC4F,UAAU,CAAC3F,aAAa,CAAC;IACrD4F,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIhB,cAAc,GAAGgB,YAAY,CAAC,OAAO,EAAEJ,kBAAkB,CAAC;EAC9D,IAAIK,UAAU,GAAGxH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiD,KAAK,CAAC,EAAE;IAC7CsD,cAAc,EAAEA;EAClB,CAAC,CAAC;EACF,OAAO,aAAa7E,KAAK,CAACgF,aAAa,CAACrE,KAAK,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEwH,UAAU,EAAE;IACtEZ,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC+B,OAAO,CAACQ,WAAW,GAAG,OAAO;AAC/B;AACA,eAAeR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}