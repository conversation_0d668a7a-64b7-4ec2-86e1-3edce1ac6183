{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nvar EmptyList = [];\nexport var PathRegisterContext = /*#__PURE__*/React.createContext(null);\nexport function useMeasure() {\n  return React.useContext(PathRegisterContext);\n} // ========================= Path Tracker ==========================\n\nexport var PathTrackerContext = /*#__PURE__*/React.createContext(EmptyList);\nexport function useFullPath(eventKey) {\n  var parentKeyPath = React.useContext(PathTrackerContext);\n  return React.useMemo(function () {\n    return eventKey !== undefined ? [].concat(_toConsumableArray(parentKeyPath), [eventKey]) : parentKeyPath;\n  }, [parentKeyPath, eventKey]);\n}\nexport var PathUserContext = /*#__PURE__*/React.createContext(null);", "map": {"version": 3, "names": ["_toConsumableArray", "React", "EmptyList", "PathRegisterContext", "createContext", "useMeasure", "useContext", "PathTrackerContext", "useFullPath", "eventKey", "parent<PERSON><PERSON><PERSON><PERSON>", "useMemo", "undefined", "concat", "PathUserContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/context/PathContext.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nvar EmptyList = [];\nexport var PathRegisterContext = /*#__PURE__*/React.createContext(null);\nexport function useMeasure() {\n  return React.useContext(PathRegisterContext);\n} // ========================= Path Tracker ==========================\n\nexport var PathTrackerContext = /*#__PURE__*/React.createContext(EmptyList);\nexport function useFullPath(eventKey) {\n  var parentKeyPath = React.useContext(PathTrackerContext);\n  return React.useMemo(function () {\n    return eventKey !== undefined ? [].concat(_toConsumableArray(parentKeyPath), [eventKey]) : parentKeyPath;\n  }, [parentKeyPath, eventKey]);\n}\nexport var PathUserContext = /*#__PURE__*/React.createContext(null);"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,SAAS,GAAG,EAAE;AAClB,OAAO,IAAIC,mBAAmB,GAAG,aAAaF,KAAK,CAACG,aAAa,CAAC,IAAI,CAAC;AACvE,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,OAAOJ,KAAK,CAACK,UAAU,CAACH,mBAAmB,CAAC;AAC9C,CAAC,CAAC;;AAEF,OAAO,IAAII,kBAAkB,GAAG,aAAaN,KAAK,CAACG,aAAa,CAACF,SAAS,CAAC;AAC3E,OAAO,SAASM,WAAWA,CAACC,QAAQ,EAAE;EACpC,IAAIC,aAAa,GAAGT,KAAK,CAACK,UAAU,CAACC,kBAAkB,CAAC;EACxD,OAAON,KAAK,CAACU,OAAO,CAAC,YAAY;IAC/B,OAAOF,QAAQ,KAAKG,SAAS,GAAG,EAAE,CAACC,MAAM,CAACb,kBAAkB,CAACU,aAAa,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC,GAAGC,aAAa;EAC1G,CAAC,EAAE,CAACA,aAAa,EAAED,QAAQ,CAAC,CAAC;AAC/B;AACA,OAAO,IAAIK,eAAe,GAAG,aAAab,KAAK,CAACG,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}