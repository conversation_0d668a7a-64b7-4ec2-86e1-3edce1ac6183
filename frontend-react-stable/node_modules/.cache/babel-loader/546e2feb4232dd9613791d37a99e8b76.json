{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar Record;\n(function (Record) {\n  Record[Record[\"None\"] = 0] = \"None\";\n  Record[Record[\"Start\"] = 1] = \"Start\";\n  Record[Record[\"End\"] = 2] = \"End\";\n})(Record || (Record = {}));\nfunction traverseNodesKey(treeData, callback) {\n  function processNode(dataNode) {\n    var key = dataNode.key,\n      children = dataNode.children;\n    if (callback(key, dataNode) !== false) {\n      traverseNodesKey(children || [], callback);\n    }\n  }\n  treeData.forEach(processNode);\n}\n/** 计算选中范围，只考虑expanded情况以优化性能 */\nexport function calcRangeKeys(_ref) {\n  var treeData = _ref.treeData,\n    expandedKeys = _ref.expandedKeys,\n    startKey = _ref.startKey,\n    endKey = _ref.endKey;\n  var keys = [];\n  var record = Record.None;\n  if (startKey && startKey === endKey) {\n    return [startKey];\n  }\n  if (!startKey || !endKey) {\n    return [];\n  }\n  function matchKey(key) {\n    return key === startKey || key === endKey;\n  }\n  traverseNodesKey(treeData, function (key) {\n    if (record === Record.End) {\n      return false;\n    }\n    if (matchKey(key)) {\n      // Match test\n      keys.push(key);\n      if (record === Record.None) {\n        record = Record.Start;\n      } else if (record === Record.Start) {\n        record = Record.End;\n        return false;\n      }\n    } else if (record === Record.Start) {\n      // Append selection\n      keys.push(key);\n    }\n    return expandedKeys.includes(key);\n  });\n  return keys;\n}\nexport function convertDirectoryKeysToNodes(treeData, keys) {\n  var restKeys = _toConsumableArray(keys);\n  var nodes = [];\n  traverseNodesKey(treeData, function (key, node) {\n    var index = restKeys.indexOf(key);\n    if (index !== -1) {\n      nodes.push(node);\n      restKeys.splice(index, 1);\n    }\n    return !!restKeys.length;\n  });\n  return nodes;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "Record", "traverseNodesKey", "treeData", "callback", "processNode", "dataNode", "key", "children", "for<PERSON>ach", "calcRangeKeys", "_ref", "expandedKeys", "startKey", "<PERSON><PERSON><PERSON>", "keys", "record", "None", "matchKey", "End", "push", "Start", "includes", "convertDirectoryKeysToNodes", "restKeys", "nodes", "node", "index", "indexOf", "splice", "length"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tree/utils/dictUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar Record;\n(function (Record) {\n  Record[Record[\"None\"] = 0] = \"None\";\n  Record[Record[\"Start\"] = 1] = \"Start\";\n  Record[Record[\"End\"] = 2] = \"End\";\n})(Record || (Record = {}));\nfunction traverseNodesKey(treeData, callback) {\n  function processNode(dataNode) {\n    var key = dataNode.key,\n      children = dataNode.children;\n    if (callback(key, dataNode) !== false) {\n      traverseNodesKey(children || [], callback);\n    }\n  }\n  treeData.forEach(processNode);\n}\n/** 计算选中范围，只考虑expanded情况以优化性能 */\nexport function calcRangeKeys(_ref) {\n  var treeData = _ref.treeData,\n    expandedKeys = _ref.expandedKeys,\n    startKey = _ref.startKey,\n    endKey = _ref.endKey;\n  var keys = [];\n  var record = Record.None;\n  if (startKey && startKey === endKey) {\n    return [startKey];\n  }\n  if (!startKey || !endKey) {\n    return [];\n  }\n  function matchKey(key) {\n    return key === startKey || key === endKey;\n  }\n  traverseNodesKey(treeData, function (key) {\n    if (record === Record.End) {\n      return false;\n    }\n    if (matchKey(key)) {\n      // Match test\n      keys.push(key);\n      if (record === Record.None) {\n        record = Record.Start;\n      } else if (record === Record.Start) {\n        record = Record.End;\n        return false;\n      }\n    } else if (record === Record.Start) {\n      // Append selection\n      keys.push(key);\n    }\n    return expandedKeys.includes(key);\n  });\n  return keys;\n}\nexport function convertDirectoryKeysToNodes(treeData, keys) {\n  var restKeys = _toConsumableArray(keys);\n  var nodes = [];\n  traverseNodesKey(treeData, function (key, node) {\n    var index = restKeys.indexOf(key);\n    if (index !== -1) {\n      nodes.push(node);\n      restKeys.splice(index, 1);\n    }\n    return !!restKeys.length;\n  });\n  return nodes;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,MAAM;AACV,CAAC,UAAUA,MAAM,EAAE;EACjBA,MAAM,CAACA,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnCA,MAAM,CAACA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACrCA,MAAM,CAACA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACnC,CAAC,EAAEA,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC5C,SAASC,WAAWA,CAACC,QAAQ,EAAE;IAC7B,IAAIC,GAAG,GAAGD,QAAQ,CAACC,GAAG;MACpBC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;IAC9B,IAAIJ,QAAQ,CAACG,GAAG,EAAED,QAAQ,CAAC,KAAK,KAAK,EAAE;MACrCJ,gBAAgB,CAACM,QAAQ,IAAI,EAAE,EAAEJ,QAAQ,CAAC;IAC5C;EACF;EACAD,QAAQ,CAACM,OAAO,CAACJ,WAAW,CAAC;AAC/B;AACA;AACA,OAAO,SAASK,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIR,QAAQ,GAAGQ,IAAI,CAACR,QAAQ;IAC1BS,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,MAAM,GAAGf,MAAM,CAACgB,IAAI;EACxB,IAAIJ,QAAQ,IAAIA,QAAQ,KAAKC,MAAM,EAAE;IACnC,OAAO,CAACD,QAAQ,CAAC;EACnB;EACA,IAAI,CAACA,QAAQ,IAAI,CAACC,MAAM,EAAE;IACxB,OAAO,EAAE;EACX;EACA,SAASI,QAAQA,CAACX,GAAG,EAAE;IACrB,OAAOA,GAAG,KAAKM,QAAQ,IAAIN,GAAG,KAAKO,MAAM;EAC3C;EACAZ,gBAAgB,CAACC,QAAQ,EAAE,UAAUI,GAAG,EAAE;IACxC,IAAIS,MAAM,KAAKf,MAAM,CAACkB,GAAG,EAAE;MACzB,OAAO,KAAK;IACd;IACA,IAAID,QAAQ,CAACX,GAAG,CAAC,EAAE;MACjB;MACAQ,IAAI,CAACK,IAAI,CAACb,GAAG,CAAC;MACd,IAAIS,MAAM,KAAKf,MAAM,CAACgB,IAAI,EAAE;QAC1BD,MAAM,GAAGf,MAAM,CAACoB,KAAK;MACvB,CAAC,MAAM,IAAIL,MAAM,KAAKf,MAAM,CAACoB,KAAK,EAAE;QAClCL,MAAM,GAAGf,MAAM,CAACkB,GAAG;QACnB,OAAO,KAAK;MACd;IACF,CAAC,MAAM,IAAIH,MAAM,KAAKf,MAAM,CAACoB,KAAK,EAAE;MAClC;MACAN,IAAI,CAACK,IAAI,CAACb,GAAG,CAAC;IAChB;IACA,OAAOK,YAAY,CAACU,QAAQ,CAACf,GAAG,CAAC;EACnC,CAAC,CAAC;EACF,OAAOQ,IAAI;AACb;AACA,OAAO,SAASQ,2BAA2BA,CAACpB,QAAQ,EAAEY,IAAI,EAAE;EAC1D,IAAIS,QAAQ,GAAGxB,kBAAkB,CAACe,IAAI,CAAC;EACvC,IAAIU,KAAK,GAAG,EAAE;EACdvB,gBAAgB,CAACC,QAAQ,EAAE,UAAUI,GAAG,EAAEmB,IAAI,EAAE;IAC9C,IAAIC,KAAK,GAAGH,QAAQ,CAACI,OAAO,CAACrB,GAAG,CAAC;IACjC,IAAIoB,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBF,KAAK,CAACL,IAAI,CAACM,IAAI,CAAC;MAChBF,QAAQ,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3B;IACA,OAAO,CAAC,CAACH,QAAQ,CAACM,MAAM;EAC1B,CAAC,CAAC;EACF,OAAOL,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}