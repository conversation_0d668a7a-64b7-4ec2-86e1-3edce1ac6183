{"ast": null, "code": "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\nmodule.exports = trimmedEndIndex;", "map": {"version": 3, "names": ["reWhitespace", "trimmedEndIndex", "string", "index", "length", "test", "char<PERSON>t", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_trimmedEndIndex.js"], "sourcesContent": ["/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nmodule.exports = trimmedEndIndex;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG,IAAI;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAIC,KAAK,GAAGD,MAAM,CAACE,MAAM;EAEzB,OAAOD,KAAK,EAAE,IAAIH,YAAY,CAACK,IAAI,CAACH,MAAM,CAACI,MAAM,CAACH,KAAK,CAAC,CAAC,EAAE,CAAC;EAC5D,OAAOA,KAAK;AACd;AAEAI,MAAM,CAACC,OAAO,GAAGP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script"}