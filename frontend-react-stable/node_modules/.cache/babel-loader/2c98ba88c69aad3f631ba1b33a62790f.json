{"ast": null, "code": "var baseIsArguments = require('./_baseIsArguments'),\n  isObjectLike = require('./isObjectLike');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function () {\n  return arguments;\n}()) ? baseIsArguments : function (value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') && !propertyIsEnumerable.call(value, 'callee');\n};\nmodule.exports = isArguments;", "map": {"version": 3, "names": ["baseIsArguments", "require", "isObjectLike", "objectProto", "Object", "prototype", "hasOwnProperty", "propertyIsEnumerable", "isArguments", "arguments", "value", "call", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/isArguments.js"], "sourcesContent": ["var baseIsArguments = require('./_baseIsArguments'),\n    isObjectLike = require('./isObjectLike');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\nmodule.exports = isArguments;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAC/CC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA,IAAIC,oBAAoB,GAAGJ,WAAW,CAACI,oBAAoB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAGR,eAAe,CAAC,YAAW;EAAE,OAAOS,SAAS;AAAE,CAAC,CAAC,CAAC,CAAC,GAAGT,eAAe,GAAG,UAASU,KAAK,EAAE;EACxG,OAAOR,YAAY,CAACQ,KAAK,CAAC,IAAIJ,cAAc,CAACK,IAAI,CAACD,KAAK,EAAE,QAAQ,CAAC,IAChE,CAACH,oBAAoB,CAACI,IAAI,CAACD,KAAK,EAAE,QAAQ,CAAC;AAC/C,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAGL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script"}