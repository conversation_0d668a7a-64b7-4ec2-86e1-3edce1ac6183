{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nvar FormItemInput = function FormItemInput(props) {\n  var prefixCls = props.prefixCls,\n    status = props.status,\n    wrapperCol = props.wrapperCol,\n    children = props.children,\n    errors = props.errors,\n    warnings = props.warnings,\n    formItemRender = props._internalItemRender,\n    extra = props.extra,\n    help = props.help,\n    fieldId = props.fieldId,\n    marginBottom = props.marginBottom,\n    onErrorVisibleChanged = props.onErrorVisibleChanged;\n  var baseClassName = \"\".concat(prefixCls, \"-item\");\n  var formContext = React.useContext(FormContext);\n  var mergedWrapperCol = wrapperCol || formContext.wrapperCol || {};\n  var className = classNames(\"\".concat(baseClassName, \"-control\"), mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  var subFormContext = React.useMemo(function () {\n    return _extends({}, formContext);\n  }, [formContext]);\n  delete subFormContext.labelCol;\n  delete subFormContext.wrapperCol;\n  var inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input-content\")\n  }, children));\n  var formItemContext = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: status\n    };\n  }, [prefixCls, status]);\n  var errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexWrap: 'nowrap'\n    }\n  }, /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: \"\".concat(baseClassName, \"-explain-connected\"),\n    onVisibleChanged: onErrorVisibleChanged\n  })), !!marginBottom && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: 0,\n      height: marginBottom\n    }\n  })) : null;\n  var extraProps = {};\n  if (fieldId) {\n    extraProps.id = \"\".concat(fieldId, \"_extra\");\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  var extraDom = extra ? /*#__PURE__*/React.createElement(\"div\", _extends({}, extraProps, {\n    className: \"\".concat(baseClassName, \"-extra\")\n  }), extra) : null;\n  var dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : /*#__PURE__*/React.createElement(React.Fragment, null, inputDom, errorListDom, extraDom);\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, _extends({}, mergedWrapperCol, {\n    className: className\n  }), dom));\n};\nexport default FormItemInput;", "map": {"version": 3, "names": ["_extends", "classNames", "React", "Col", "FormContext", "FormItemPrefixContext", "ErrorList", "FormItemInput", "props", "prefixCls", "status", "wrapperCol", "children", "errors", "warnings", "formItemRender", "_internalItemRender", "extra", "help", "fieldId", "marginBottom", "onErrorVisibleChanged", "baseClassName", "concat", "formContext", "useContext", "mergedWrapperCol", "className", "subFormContext", "useMemo", "labelCol", "inputDom", "createElement", "formItemContext", "errorListDom", "length", "style", "display", "flexWrap", "Provider", "value", "helpStatus", "onVisibleChanged", "width", "height", "extraProps", "id", "extraDom", "dom", "mark", "render", "input", "errorList", "Fragment"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/FormItemInput.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nvar FormItemInput = function FormItemInput(props) {\n  var prefixCls = props.prefixCls,\n    status = props.status,\n    wrapperCol = props.wrapperCol,\n    children = props.children,\n    errors = props.errors,\n    warnings = props.warnings,\n    formItemRender = props._internalItemRender,\n    extra = props.extra,\n    help = props.help,\n    fieldId = props.fieldId,\n    marginBottom = props.marginBottom,\n    onErrorVisibleChanged = props.onErrorVisibleChanged;\n  var baseClassName = \"\".concat(prefixCls, \"-item\");\n  var formContext = React.useContext(FormContext);\n  var mergedWrapperCol = wrapperCol || formContext.wrapperCol || {};\n  var className = classNames(\"\".concat(baseClassName, \"-control\"), mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  var subFormContext = React.useMemo(function () {\n    return _extends({}, formContext);\n  }, [formContext]);\n  delete subFormContext.labelCol;\n  delete subFormContext.wrapperCol;\n  var inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input-content\")\n  }, children));\n  var formItemContext = React.useMemo(function () {\n    return {\n      prefixCls: prefixCls,\n      status: status\n    };\n  }, [prefixCls, status]);\n  var errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexWrap: 'nowrap'\n    }\n  }, /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: \"\".concat(baseClassName, \"-explain-connected\"),\n    onVisibleChanged: onErrorVisibleChanged\n  })), !!marginBottom && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: 0,\n      height: marginBottom\n    }\n  })) : null;\n  var extraProps = {};\n  if (fieldId) {\n    extraProps.id = \"\".concat(fieldId, \"_extra\");\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  var extraDom = extra ? /*#__PURE__*/React.createElement(\"div\", _extends({}, extraProps, {\n    className: \"\".concat(baseClassName, \"-extra\")\n  }), extra) : null;\n  var dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : /*#__PURE__*/React.createElement(React.Fragment, null, inputDom, errorListDom, extraDom);\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, _extends({}, mergedWrapperCol, {\n    className: className\n  }), dom));\n};\nexport default FormItemInput;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,WAAW;AAC9D,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,cAAc,GAAGP,KAAK,CAACQ,mBAAmB;IAC1CC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,YAAY,GAAGZ,KAAK,CAACY,YAAY;IACjCC,qBAAqB,GAAGb,KAAK,CAACa,qBAAqB;EACrD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACd,SAAS,EAAE,OAAO,CAAC;EACjD,IAAIe,WAAW,GAAGtB,KAAK,CAACuB,UAAU,CAACrB,WAAW,CAAC;EAC/C,IAAIsB,gBAAgB,GAAGf,UAAU,IAAIa,WAAW,CAACb,UAAU,IAAI,CAAC,CAAC;EACjE,IAAIgB,SAAS,GAAG1B,UAAU,CAAC,EAAE,CAACsB,MAAM,CAACD,aAAa,EAAE,UAAU,CAAC,EAAEI,gBAAgB,CAACC,SAAS,CAAC;EAC5F;EACA,IAAIC,cAAc,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,YAAY;IAC7C,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAEwB,WAAW,CAAC;EAClC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACjB,OAAOI,cAAc,CAACE,QAAQ;EAC9B,OAAOF,cAAc,CAACjB,UAAU;EAChC,IAAIoB,QAAQ,GAAG,aAAa7B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACrDL,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACD,aAAa,EAAE,gBAAgB;EACtD,CAAC,EAAE,aAAapB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACzCL,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACD,aAAa,EAAE,wBAAwB;EAC9D,CAAC,EAAEV,QAAQ,CAAC,CAAC;EACb,IAAIqB,eAAe,GAAG/B,KAAK,CAAC2B,OAAO,CAAC,YAAY;IAC9C,OAAO;MACLpB,SAAS,EAAEA,SAAS;MACpBC,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,EAAE,CAACD,SAAS,EAAEC,MAAM,CAAC,CAAC;EACvB,IAAIwB,YAAY,GAAGd,YAAY,KAAK,IAAI,IAAIP,MAAM,CAACsB,MAAM,IAAIrB,QAAQ,CAACqB,MAAM,GAAG,aAAajC,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACrHI,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,aAAapC,KAAK,CAAC8B,aAAa,CAAC3B,qBAAqB,CAACkC,QAAQ,EAAE;IAClEC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAa/B,KAAK,CAAC8B,aAAa,CAAC1B,SAAS,EAAE;IAC7Ca,OAAO,EAAEA,OAAO;IAChBN,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBI,IAAI,EAAEA,IAAI;IACVuB,UAAU,EAAE/B,MAAM;IAClBiB,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACD,aAAa,EAAE,oBAAoB,CAAC;IACzDoB,gBAAgB,EAAErB;EACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAACD,YAAY,IAAI,aAAalB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7DI,KAAK,EAAE;MACLO,KAAK,EAAE,CAAC;MACRC,MAAM,EAAExB;IACV;EACF,CAAC,CAAC,CAAC,GAAG,IAAI;EACV,IAAIyB,UAAU,GAAG,CAAC,CAAC;EACnB,IAAI1B,OAAO,EAAE;IACX0B,UAAU,CAACC,EAAE,GAAG,EAAE,CAACvB,MAAM,CAACJ,OAAO,EAAE,QAAQ,CAAC;EAC9C;EACA;EACA;EACA,IAAI4B,QAAQ,GAAG9B,KAAK,GAAG,aAAaf,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAE6C,UAAU,EAAE;IACtFlB,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACD,aAAa,EAAE,QAAQ;EAC9C,CAAC,CAAC,EAAEL,KAAK,CAAC,GAAG,IAAI;EACjB,IAAI+B,GAAG,GAAGjC,cAAc,IAAIA,cAAc,CAACkC,IAAI,KAAK,kBAAkB,IAAIlC,cAAc,CAACmC,MAAM,GAAGnC,cAAc,CAACmC,MAAM,CAAC1C,KAAK,EAAE;IAC7H2C,KAAK,EAAEpB,QAAQ;IACfqB,SAAS,EAAElB,YAAY;IACvBjB,KAAK,EAAE8B;EACT,CAAC,CAAC,GAAG,aAAa7C,KAAK,CAAC8B,aAAa,CAAC9B,KAAK,CAACmD,QAAQ,EAAE,IAAI,EAAEtB,QAAQ,EAAEG,YAAY,EAAEa,QAAQ,CAAC;EAC7F,OAAO,aAAa7C,KAAK,CAAC8B,aAAa,CAAC5B,WAAW,CAACmC,QAAQ,EAAE;IAC5DC,KAAK,EAAEZ;EACT,CAAC,EAAE,aAAa1B,KAAK,CAAC8B,aAAa,CAAC7B,GAAG,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAE0B,gBAAgB,EAAE;IACtEC,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEqB,GAAG,CAAC,CAAC;AACX,CAAC;AACD,eAAezC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}