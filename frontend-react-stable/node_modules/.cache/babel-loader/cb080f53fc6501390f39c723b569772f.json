{"ast": null, "code": "import * as React from 'react';\nimport { MenuContext } from '../context/MenuContext';\nexport default function useActive(eventKey, disabled, onMouseEnter, onMouseLeave) {\n  var _React$useContext = React.useContext(MenuContext),\n    activeKey = _React$useContext.activeKey,\n    onActive = _React$useContext.onActive,\n    onInactive = _React$useContext.onInactive;\n  var ret = {\n    active: activeKey === eventKey\n  }; // Skip when disabled\n\n  if (!disabled) {\n    ret.onMouseEnter = function (domEvent) {\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onActive(eventKey);\n    };\n    ret.onMouseLeave = function (domEvent) {\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onInactive(eventKey);\n    };\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["React", "MenuContext", "useActive", "eventKey", "disabled", "onMouseEnter", "onMouseLeave", "_React$useContext", "useContext", "active<PERSON><PERSON>", "onActive", "onInactive", "ret", "active", "domEvent", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/hooks/useActive.js"], "sourcesContent": ["import * as React from 'react';\nimport { MenuContext } from '../context/MenuContext';\nexport default function useActive(eventKey, disabled, onMouseEnter, onMouseLeave) {\n  var _React$useContext = React.useContext(MenuContext),\n      activeKey = _React$useContext.activeKey,\n      onActive = _React$useContext.onActive,\n      onInactive = _React$useContext.onInactive;\n\n  var ret = {\n    active: activeKey === eventKey\n  }; // Skip when disabled\n\n  if (!disabled) {\n    ret.onMouseEnter = function (domEvent) {\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onActive(eventKey);\n    };\n\n    ret.onMouseLeave = function (domEvent) {\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        key: eventKey,\n        domEvent: domEvent\n      });\n      onInactive(eventKey);\n    };\n  }\n\n  return ret;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,EAAE;EAChF,IAAIC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU,CAACP,WAAW,CAAC;IACjDQ,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,QAAQ,GAAGH,iBAAiB,CAACG,QAAQ;IACrCC,UAAU,GAAGJ,iBAAiB,CAACI,UAAU;EAE7C,IAAIC,GAAG,GAAG;IACRC,MAAM,EAAEJ,SAAS,KAAKN;EACxB,CAAC,CAAC,CAAC;;EAEH,IAAI,CAACC,QAAQ,EAAE;IACbQ,GAAG,CAACP,YAAY,GAAG,UAAUS,QAAQ,EAAE;MACrCT,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvEU,GAAG,EAAEZ,QAAQ;QACbW,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFJ,QAAQ,CAACP,QAAQ,CAAC;IACpB,CAAC;IAEDS,GAAG,CAACN,YAAY,GAAG,UAAUQ,QAAQ,EAAE;MACrCR,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvES,GAAG,EAAEZ,QAAQ;QACbW,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACFH,UAAU,CAACR,QAAQ,CAAC;IACtB,CAAC;EACH;EAEA,OAAOS,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}