{"ast": null, "code": "var arrayFilter = require('./_arrayFilter'),\n  stubArray = require('./stubArray');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function (object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function (symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\nmodule.exports = getSymbols;", "map": {"version": 3, "names": ["arrayFilter", "require", "stubArray", "objectProto", "Object", "prototype", "propertyIsEnumerable", "nativeGetSymbols", "getOwnPropertySymbols", "getSymbols", "object", "symbol", "call", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_getSymbols.js"], "sourcesContent": ["var arrayFilter = require('./_arrayFilter'),\n    stubArray = require('./stubArray');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function(symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\n\nmodule.exports = getSymbols;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,SAAS,GAAGD,OAAO,CAAC,aAAa,CAAC;;AAEtC;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACG,oBAAoB;;AAE3D;AACA,IAAIC,gBAAgB,GAAGH,MAAM,CAACI,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,CAACF,gBAAgB,GAAGL,SAAS,GAAG,UAASQ,MAAM,EAAE;EAChE,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,EAAE;EACX;EACAA,MAAM,GAAGN,MAAM,CAACM,MAAM,CAAC;EACvB,OAAOV,WAAW,CAACO,gBAAgB,CAACG,MAAM,CAAC,EAAE,UAASC,MAAM,EAAE;IAC5D,OAAOL,oBAAoB,CAACM,IAAI,CAACF,MAAM,EAAEC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAGL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script"}