{"ast": null, "code": "/**\n * Get index with specific start index one by one. e.g.\n * min: 3, max: 9, start: 6\n *\n * Return index is:\n * [0]: 6\n * [1]: 7\n * [2]: 5\n * [3]: 8\n * [4]: 4\n * [5]: 9\n * [6]: 3\n */\nexport function getIndexByStartLoc(min, max, start, index) {\n  var beforeCount = start - min;\n  var afterCount = max - start;\n  var balanceCount = Math.min(beforeCount, afterCount) * 2;\n\n  // Balance\n  if (index <= balanceCount) {\n    var stepIndex = Math.floor(index / 2);\n    if (index % 2) {\n      return start + stepIndex + 1;\n    }\n    return start - stepIndex;\n  }\n\n  // One is out of range\n  if (beforeCount > afterCount) {\n    return start - (index - afterCount);\n  }\n  return start + (index - beforeCount);\n}\n\n/**\n * We assume that 2 list has only 1 item diff and others keeping the order.\n * So we can use dichotomy algorithm to find changed one.\n */\nexport function findListDiffIndex(originList, targetList, getKey) {\n  var originLen = originList.length;\n  var targetLen = targetList.length;\n  var shortList;\n  var longList;\n  if (originLen === 0 && targetLen === 0) {\n    return null;\n  }\n  if (originLen < targetLen) {\n    shortList = originList;\n    longList = targetList;\n  } else {\n    shortList = targetList;\n    longList = originList;\n  }\n  var notExistKey = {\n    __EMPTY_ITEM__: true\n  };\n  function getItemKey(item) {\n    if (item !== undefined) {\n      return getKey(item);\n    }\n    return notExistKey;\n  }\n\n  // Loop to find diff one\n  var diffIndex = null;\n  var multiple = Math.abs(originLen - targetLen) !== 1;\n  for (var i = 0; i < longList.length; i += 1) {\n    var shortKey = getItemKey(shortList[i]);\n    var longKey = getItemKey(longList[i]);\n    if (shortKey !== longKey) {\n      diffIndex = i;\n      multiple = multiple || shortKey !== getItemKey(longList[i + 1]);\n      break;\n    }\n  }\n  return diffIndex === null ? null : {\n    index: diffIndex,\n    multiple: multiple\n  };\n}", "map": {"version": 3, "names": ["getIndexByStartLoc", "min", "max", "start", "index", "beforeCount", "afterCount", "balanceCount", "Math", "stepIndex", "floor", "findListDiffIndex", "originList", "targetList", "<PERSON><PERSON><PERSON>", "originLen", "length", "targetLen", "shortList", "longList", "notExistKey", "__EMPTY_ITEM__", "getItemKey", "item", "undefined", "diffIndex", "multiple", "abs", "i", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-virtual-list/es/utils/algorithmUtil.js"], "sourcesContent": ["/**\n * Get index with specific start index one by one. e.g.\n * min: 3, max: 9, start: 6\n *\n * Return index is:\n * [0]: 6\n * [1]: 7\n * [2]: 5\n * [3]: 8\n * [4]: 4\n * [5]: 9\n * [6]: 3\n */\nexport function getIndexByStartLoc(min, max, start, index) {\n  var beforeCount = start - min;\n  var afterCount = max - start;\n  var balanceCount = Math.min(beforeCount, afterCount) * 2;\n\n  // Balance\n  if (index <= balanceCount) {\n    var stepIndex = Math.floor(index / 2);\n    if (index % 2) {\n      return start + stepIndex + 1;\n    }\n    return start - stepIndex;\n  }\n\n  // One is out of range\n  if (beforeCount > afterCount) {\n    return start - (index - afterCount);\n  }\n  return start + (index - beforeCount);\n}\n\n/**\n * We assume that 2 list has only 1 item diff and others keeping the order.\n * So we can use dichotomy algorithm to find changed one.\n */\nexport function findListDiffIndex(originList, targetList, getKey) {\n  var originLen = originList.length;\n  var targetLen = targetList.length;\n  var shortList;\n  var longList;\n  if (originLen === 0 && targetLen === 0) {\n    return null;\n  }\n  if (originLen < targetLen) {\n    shortList = originList;\n    longList = targetList;\n  } else {\n    shortList = targetList;\n    longList = originList;\n  }\n  var notExistKey = {\n    __EMPTY_ITEM__: true\n  };\n  function getItemKey(item) {\n    if (item !== undefined) {\n      return getKey(item);\n    }\n    return notExistKey;\n  }\n\n  // Loop to find diff one\n  var diffIndex = null;\n  var multiple = Math.abs(originLen - targetLen) !== 1;\n  for (var i = 0; i < longList.length; i += 1) {\n    var shortKey = getItemKey(shortList[i]);\n    var longKey = getItemKey(longList[i]);\n    if (shortKey !== longKey) {\n      diffIndex = i;\n      multiple = multiple || shortKey !== getItemKey(longList[i + 1]);\n      break;\n    }\n  }\n  return diffIndex === null ? null : {\n    index: diffIndex,\n    multiple: multiple\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,kBAAkBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACzD,IAAIC,WAAW,GAAGF,KAAK,GAAGF,GAAG;EAC7B,IAAIK,UAAU,GAAGJ,GAAG,GAAGC,KAAK;EAC5B,IAAII,YAAY,GAAGC,IAAI,CAACP,GAAG,CAACI,WAAW,EAAEC,UAAU,CAAC,GAAG,CAAC;;EAExD;EACA,IAAIF,KAAK,IAAIG,YAAY,EAAE;IACzB,IAAIE,SAAS,GAAGD,IAAI,CAACE,KAAK,CAACN,KAAK,GAAG,CAAC,CAAC;IACrC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAOD,KAAK,GAAGM,SAAS,GAAG,CAAC;IAC9B;IACA,OAAON,KAAK,GAAGM,SAAS;EAC1B;;EAEA;EACA,IAAIJ,WAAW,GAAGC,UAAU,EAAE;IAC5B,OAAOH,KAAK,IAAIC,KAAK,GAAGE,UAAU,CAAC;EACrC;EACA,OAAOH,KAAK,IAAIC,KAAK,GAAGC,WAAW,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASM,iBAAiBA,CAACC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAE;EAChE,IAAIC,SAAS,GAAGH,UAAU,CAACI,MAAM;EACjC,IAAIC,SAAS,GAAGJ,UAAU,CAACG,MAAM;EACjC,IAAIE,SAAS;EACb,IAAIC,QAAQ;EACZ,IAAIJ,SAAS,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EACA,IAAIF,SAAS,GAAGE,SAAS,EAAE;IACzBC,SAAS,GAAGN,UAAU;IACtBO,QAAQ,GAAGN,UAAU;EACvB,CAAC,MAAM;IACLK,SAAS,GAAGL,UAAU;IACtBM,QAAQ,GAAGP,UAAU;EACvB;EACA,IAAIQ,WAAW,GAAG;IAChBC,cAAc,EAAE;EAClB,CAAC;EACD,SAASC,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACtB,OAAOV,MAAM,CAACS,IAAI,CAAC;IACrB;IACA,OAAOH,WAAW;EACpB;;EAEA;EACA,IAAIK,SAAS,GAAG,IAAI;EACpB,IAAIC,QAAQ,GAAGlB,IAAI,CAACmB,GAAG,CAACZ,SAAS,GAAGE,SAAS,CAAC,KAAK,CAAC;EACpD,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,QAAQ,CAACH,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAIC,QAAQ,GAAGP,UAAU,CAACJ,SAAS,CAACU,CAAC,CAAC,CAAC;IACvC,IAAIE,OAAO,GAAGR,UAAU,CAACH,QAAQ,CAACS,CAAC,CAAC,CAAC;IACrC,IAAIC,QAAQ,KAAKC,OAAO,EAAE;MACxBL,SAAS,GAAGG,CAAC;MACbF,QAAQ,GAAGA,QAAQ,IAAIG,QAAQ,KAAKP,UAAU,CAACH,QAAQ,CAACS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/D;IACF;EACF;EACA,OAAOH,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;IACjCrB,KAAK,EAAEqB,SAAS;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}