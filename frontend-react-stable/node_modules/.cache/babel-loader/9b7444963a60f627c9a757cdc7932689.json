{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport MonthHeader from './MonthHeader';\nimport MonthBody, { MONTH_COL_COUNT } from './MonthBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nfunction MonthPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-month-panel\");\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff * MONTH_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange('date', value || viewDate);\n        }\n      });\n    }\n  };\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(MonthHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(MonthBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      _onSelect(date, 'mouse');\n      onPanelChange('date', date);\n    }\n  })));\n}\nexport default MonthPanel;", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON><PERSON>", "MonthBody", "MONTH_COL_COUNT", "createKeyDownHandler", "MonthPanel", "props", "prefixCls", "operationRef", "onViewDateChange", "generateConfig", "value", "viewDate", "onPanelChange", "_onSelect", "onSelect", "panelPrefixCls", "concat", "current", "onKeyDown", "event", "onLeftRight", "diff", "addMonth", "onCtrlLeftRight", "addYear", "onUpDown", "onEnter", "onYearChange", "newDate", "createElement", "className", "onPrevYear", "onNextYear", "onYearClick", "date"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/MonthPanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport MonthHeader from './MonthHeader';\nimport MonthBody, { MONTH_COL_COUNT } from './MonthBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nfunction MonthPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-month-panel\");\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff * MONTH_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange('date', value || viewDate);\n        }\n      });\n    }\n  };\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(MonthHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(MonthBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      _onSelect(date, 'mouse');\n      onPanelChange('date', date);\n    }\n  })));\n}\nexport default MonthPanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,IAAIC,eAAe,QAAQ,aAAa;AACxD,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;IACzCC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,SAAS,GAAGR,KAAK,CAACS,QAAQ;EAC5B,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,cAAc,CAAC;EACzD;EACAC,YAAY,CAACU,OAAO,GAAG;IACrBC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;MACnC,OAAOhB,oBAAoB,CAACgB,KAAK,EAAE;QACjCC,WAAW,EAAE,SAASA,WAAWA,CAACC,IAAI,EAAE;UACtCR,SAAS,CAACJ,cAAc,CAACa,QAAQ,CAACZ,KAAK,IAAIC,QAAQ,EAAEU,IAAI,CAAC,EAAE,KAAK,CAAC;QACpE,CAAC;QACDE,eAAe,EAAE,SAASA,eAAeA,CAACF,IAAI,EAAE;UAC9CR,SAAS,CAACJ,cAAc,CAACe,OAAO,CAACd,KAAK,IAAIC,QAAQ,EAAEU,IAAI,CAAC,EAAE,KAAK,CAAC;QACnE,CAAC;QACDI,QAAQ,EAAE,SAASA,QAAQA,CAACJ,IAAI,EAAE;UAChCR,SAAS,CAACJ,cAAc,CAACa,QAAQ,CAACZ,KAAK,IAAIC,QAAQ,EAAEU,IAAI,GAAGnB,eAAe,CAAC,EAAE,KAAK,CAAC;QACtF,CAAC;QACDwB,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1Bd,aAAa,CAAC,MAAM,EAAEF,KAAK,IAAIC,QAAQ,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACA,IAAIgB,YAAY,GAAG,SAASA,YAAYA,CAACN,IAAI,EAAE;IAC7C,IAAIO,OAAO,GAAGnB,cAAc,CAACe,OAAO,CAACb,QAAQ,EAAEU,IAAI,CAAC;IACpDb,gBAAgB,CAACoB,OAAO,CAAC;IACzBhB,aAAa,CAAC,IAAI,EAAEgB,OAAO,CAAC;EAC9B,CAAC;EACD,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEf;EACb,CAAC,EAAE,aAAahB,KAAK,CAAC8B,aAAa,CAAC7B,WAAW,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IACnEC,SAAS,EAAEA,SAAS;IACpByB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCJ,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACDK,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCL,YAAY,CAAC,CAAC,CAAC;IACjB,CAAC;IACDM,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClCrB,aAAa,CAAC,MAAM,EAAED,QAAQ,CAAC;IACjC;EACF,CAAC,CAAC,CAAC,EAAE,aAAaZ,KAAK,CAAC8B,aAAa,CAAC5B,SAAS,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IACnEC,SAAS,EAAEA,SAAS;IACpBQ,QAAQ,EAAE,SAASA,QAAQA,CAACoB,IAAI,EAAE;MAChCrB,SAAS,CAACqB,IAAI,EAAE,OAAO,CAAC;MACxBtB,aAAa,CAAC,MAAM,EAAEsB,IAAI,CAAC;IAC7B;EACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAe9B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}