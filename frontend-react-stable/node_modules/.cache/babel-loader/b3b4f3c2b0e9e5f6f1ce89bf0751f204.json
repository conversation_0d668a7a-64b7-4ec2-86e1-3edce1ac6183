{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport DatePicker from '../date-picker';\nimport warning from '../_util/warning';\nvar InternalTimePicker = DatePicker.TimePicker,\n  InternalRangePicker = DatePicker.RangePicker;\nvar RangePicker = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var dropdownClassName = props.dropdownClassName,\n    popupClassName = props.popupClassName;\n  return /*#__PURE__*/React.createElement(InternalRangePicker, _extends({}, props, {\n    dropdownClassName: popupClassName || dropdownClassName,\n    picker: \"time\",\n    mode: undefined,\n    ref: ref\n  }));\n});\nvar TimePicker = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var addon = _a.addon,\n    renderExtraFooter = _a.renderExtraFooter,\n    popupClassName = _a.popupClassName,\n    dropdownClassName = _a.dropdownClassName,\n    restProps = __rest(_a, [\"addon\", \"renderExtraFooter\", \"popupClassName\", \"dropdownClassName\"]);\n  var internalRenderExtraFooter = React.useMemo(function () {\n    if (renderExtraFooter) {\n      return renderExtraFooter;\n    }\n    if (addon) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'TimePicker', '`addon` is deprecated. Please use `renderExtraFooter` instead.') : void 0;\n      return addon;\n    }\n    return undefined;\n  }, [addon, renderExtraFooter]);\n  process.env.NODE_ENV !== \"production\" ? warning(!dropdownClassName, 'TimePicker', '`dropdownClassName` is deprecated which will be removed in next major version. Please use `popupClassName` instead.') : void 0;\n  return /*#__PURE__*/React.createElement(InternalTimePicker, _extends({\n    dropdownClassName: popupClassName || dropdownClassName\n  }, restProps, {\n    mode: undefined,\n    ref: ref,\n    renderExtraFooter: internalRenderExtraFooter\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  TimePicker.displayName = 'TimePicker';\n}\nTimePicker.RangePicker = RangePicker;\nexport default TimePicker;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "DatePicker", "warning", "InternalTimePicker", "TimePicker", "InternalRangePicker", "RangePicker", "forwardRef", "props", "ref", "dropdownClassName", "popupClassName", "createElement", "picker", "mode", "undefined", "_a", "addon", "renderExtraFooter", "restProps", "internalRenderExtraFooter", "useMemo", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/time-picker/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport DatePicker from '../date-picker';\nimport warning from '../_util/warning';\nvar InternalTimePicker = DatePicker.TimePicker,\n  InternalRangePicker = DatePicker.RangePicker;\nvar RangePicker = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var dropdownClassName = props.dropdownClassName,\n    popupClassName = props.popupClassName;\n  return /*#__PURE__*/React.createElement(InternalRangePicker, _extends({}, props, {\n    dropdownClassName: popupClassName || dropdownClassName,\n    picker: \"time\",\n    mode: undefined,\n    ref: ref\n  }));\n});\nvar TimePicker = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var addon = _a.addon,\n    renderExtraFooter = _a.renderExtraFooter,\n    popupClassName = _a.popupClassName,\n    dropdownClassName = _a.dropdownClassName,\n    restProps = __rest(_a, [\"addon\", \"renderExtraFooter\", \"popupClassName\", \"dropdownClassName\"]);\n  var internalRenderExtraFooter = React.useMemo(function () {\n    if (renderExtraFooter) {\n      return renderExtraFooter;\n    }\n    if (addon) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'TimePicker', '`addon` is deprecated. Please use `renderExtraFooter` instead.') : void 0;\n      return addon;\n    }\n    return undefined;\n  }, [addon, renderExtraFooter]);\n  process.env.NODE_ENV !== \"production\" ? warning(!dropdownClassName, 'TimePicker', '`dropdownClassName` is deprecated which will be removed in next major version. Please use `popupClassName` instead.') : void 0;\n  return /*#__PURE__*/React.createElement(InternalTimePicker, _extends({\n    dropdownClassName: popupClassName || dropdownClassName\n  }, restProps, {\n    mode: undefined,\n    ref: ref,\n    renderExtraFooter: internalRenderExtraFooter\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  TimePicker.displayName = 'TimePicker';\n}\nTimePicker.RangePicker = RangePicker;\nexport default TimePicker;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,kBAAkB,GAAGF,UAAU,CAACG,UAAU;EAC5CC,mBAAmB,GAAGJ,UAAU,CAACK,WAAW;AAC9C,IAAIA,WAAW,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIC,iBAAiB,GAAGF,KAAK,CAACE,iBAAiB;IAC7CC,cAAc,GAAGH,KAAK,CAACG,cAAc;EACvC,OAAO,aAAaX,KAAK,CAACY,aAAa,CAACP,mBAAmB,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IAC/EE,iBAAiB,EAAEC,cAAc,IAAID,iBAAiB;IACtDG,MAAM,EAAE,MAAM;IACdC,IAAI,EAAEC,SAAS;IACfN,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIL,UAAU,GAAG,aAAaJ,KAAK,CAACO,UAAU,CAAC,UAAUS,EAAE,EAAEP,GAAG,EAAE;EAChE,IAAIQ,KAAK,GAAGD,EAAE,CAACC,KAAK;IAClBC,iBAAiB,GAAGF,EAAE,CAACE,iBAAiB;IACxCP,cAAc,GAAGK,EAAE,CAACL,cAAc;IAClCD,iBAAiB,GAAGM,EAAE,CAACN,iBAAiB;IACxCS,SAAS,GAAGjC,MAAM,CAAC8B,EAAE,EAAE,CAAC,OAAO,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;EAC/F,IAAII,yBAAyB,GAAGpB,KAAK,CAACqB,OAAO,CAAC,YAAY;IACxD,IAAIH,iBAAiB,EAAE;MACrB,OAAOA,iBAAiB;IAC1B;IACA,IAAID,KAAK,EAAE;MACTK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,gEAAgE,CAAC,GAAG,KAAK,CAAC;MAC/I,OAAOe,KAAK;IACd;IACA,OAAOF,SAAS;EAClB,CAAC,EAAE,CAACE,KAAK,EAAEC,iBAAiB,CAAC,CAAC;EAC9BI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,OAAO,CAAC,CAACQ,iBAAiB,EAAE,YAAY,EAAE,qHAAqH,CAAC,GAAG,KAAK,CAAC;EACjN,OAAO,aAAaV,KAAK,CAACY,aAAa,CAACT,kBAAkB,EAAElB,QAAQ,CAAC;IACnEyB,iBAAiB,EAAEC,cAAc,IAAID;EACvC,CAAC,EAAES,SAAS,EAAE;IACZL,IAAI,EAAEC,SAAS;IACfN,GAAG,EAAEA,GAAG;IACRS,iBAAiB,EAAEE;EACrB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCpB,UAAU,CAACqB,WAAW,GAAG,YAAY;AACvC;AACArB,UAAU,CAACE,WAAW,GAAGA,WAAW;AACpC,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}