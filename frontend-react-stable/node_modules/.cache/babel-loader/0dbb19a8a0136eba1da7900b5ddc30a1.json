{"ast": null, "code": "// This icon file is generated automatically.\nvar BuildOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M916 210H376c-17.7 0-32 14.3-32 32v236H108c-17.7 0-32 14.3-32 32v272c0 17.7 14.3 32 32 32h540c17.7 0 32-14.3 32-32V546h236c17.7 0 32-14.3 32-32V242c0-17.7-14.3-32-32-32zm-504 68h200v200H412V278zm-68 468H144V546h200v200zm268 0H412V546h200v200zm268-268H680V278h200v200z\"\n      }\n    }]\n  },\n  \"name\": \"build\",\n  \"theme\": \"outlined\"\n};\nexport default BuildOutlined;", "map": {"version": 3, "names": ["BuildOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/BuildOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar BuildOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M916 210H376c-17.7 0-32 14.3-32 32v236H108c-17.7 0-32 14.3-32 32v272c0 17.7 14.3 32 32 32h540c17.7 0 32-14.3 32-32V546h236c17.7 0 32-14.3 32-32V242c0-17.7-14.3-32-32-32zm-504 68h200v200H412V278zm-68 468H144V546h200v200zm268 0H412V546h200v200zm268-268H680V278h200v200z\" } }] }, \"name\": \"build\", \"theme\": \"outlined\" };\nexport default BuildOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA8Q;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,OAAO;EAAE,OAAO,EAAE;AAAW,CAAC;AACvd,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}