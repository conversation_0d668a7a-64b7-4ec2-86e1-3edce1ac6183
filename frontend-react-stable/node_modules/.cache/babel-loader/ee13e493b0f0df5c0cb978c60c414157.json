{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react'; // recursion (flat tree structure)\n\nfunction flatRecord(record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var arr = [];\n  arr.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      var tempArr = flatRecord(record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n      arr.push.apply(arr, _toConsumableArray(tempArr));\n    }\n  }\n  return arr;\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\n\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.size) {\n      var temp = []; // collect flattened record\n\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n        temp.push.apply(temp, _toConsumableArray(flatRecord(record, 0, childrenColumnName, expandedKeys, getRowKey, i)));\n      }\n      return temp;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "flatRecord", "record", "indent", "childrenColumnName", "expandedKeys", "getRowKey", "index", "arr", "push", "key", "expanded", "has", "Array", "isArray", "i", "length", "tempArr", "apply", "useFlattenRecords", "data", "useMemo", "size", "temp", "map", "item"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/hooks/useFlattenRecords.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react'; // recursion (flat tree structure)\n\nfunction flatRecord(record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var arr = [];\n  arr.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      var tempArr = flatRecord(record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n      arr.push.apply(arr, _toConsumableArray(tempArr));\n    }\n  }\n\n  return arr;\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\n\n\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.size) {\n      var temp = []; // collect flattened record\n\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n        temp.push.apply(temp, _toConsumableArray(flatRecord(record, 0, childrenColumnName, expandedKeys, getRowKey, i)));\n      }\n\n      return temp;\n    }\n\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO,CAAC,CAAC;;AAEhC,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACtF,IAAIC,GAAG,GAAG,EAAE;EACZA,GAAG,CAACC,IAAI,CAAC;IACPP,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdI,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,IAAIG,GAAG,GAAGJ,SAAS,CAACJ,MAAM,CAAC;EAC3B,IAAIS,QAAQ,GAAGN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACO,GAAG,CAACF,GAAG,CAAC;EAEhG,IAAIR,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACZ,MAAM,CAACE,kBAAkB,CAAC,CAAC,IAAIO,QAAQ,EAAE;IACnE;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACE,kBAAkB,CAAC,CAACY,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7D,IAAIE,OAAO,GAAGhB,UAAU,CAACC,MAAM,CAACE,kBAAkB,CAAC,CAACW,CAAC,CAAC,EAAEZ,MAAM,GAAG,CAAC,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC;MACnHP,GAAG,CAACC,IAAI,CAACS,KAAK,CAACV,GAAG,EAAET,kBAAkB,CAACkB,OAAO,CAAC,CAAC;IAClD;EACF;EAEA,OAAOT,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,eAAe,SAASW,iBAAiBA,CAACC,IAAI,EAAEhB,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAC3F,IAAIE,GAAG,GAAGR,KAAK,CAACqB,OAAO,CAAC,YAAY;IAClC,IAAIhB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACiB,IAAI,EAAE;MACjF,IAAIC,IAAI,GAAG,EAAE,CAAC,CAAC;;MAEf,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIK,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACJ,MAAM,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrF,IAAIb,MAAM,GAAGkB,IAAI,CAACL,CAAC,CAAC;QACpBQ,IAAI,CAACd,IAAI,CAACS,KAAK,CAACK,IAAI,EAAExB,kBAAkB,CAACE,UAAU,CAACC,MAAM,EAAE,CAAC,EAAEE,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC,CAAC,CAAC;MAClH;MAEA,OAAOQ,IAAI;IACb;IAEA,OAAOH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAElB,KAAK,EAAE;MACjF,OAAO;QACLL,MAAM,EAAEuB,IAAI;QACZtB,MAAM,EAAE,CAAC;QACTI,KAAK,EAAEA;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACa,IAAI,EAAEhB,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,CAAC,CAAC;EACvD,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}