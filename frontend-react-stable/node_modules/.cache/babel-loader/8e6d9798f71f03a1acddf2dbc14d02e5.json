{"ast": null, "code": "import Group from './group';\nimport InternalRadio from './radio';\nimport Button from './radioButton';\nexport { Button, Group };\nvar Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nRadio.__ANT_RADIO = true;\nexport default Radio;", "map": {"version": 3, "names": ["Group", "InternalRadio", "<PERSON><PERSON>", "Radio", "__ANT_RADIO"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/radio/index.js"], "sourcesContent": ["import Group from './group';\nimport InternalRadio from './radio';\nimport Button from './radioButton';\nexport { Button, Group };\nvar Radio = InternalRadio;\nRadio.Button = Button;\nRadio.Group = Group;\nRadio.__ANT_RADIO = true;\nexport default Radio;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASA,MAAM,EAAEF,KAAK;AACtB,IAAIG,KAAK,GAAGF,aAAa;AACzBE,KAAK,CAACD,MAAM,GAAGA,MAAM;AACrBC,KAAK,CAACH,KAAK,GAAGA,KAAK;AACnBG,KAAK,CAACC,WAAW,GAAG,IAAI;AACxB,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}