{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\nimport classNames from 'classnames';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { fillRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport { Context } from \"./context\";\nimport DomWrapper from \"./DomWrapper\";\nimport useStatus from \"./hooks/useStatus\";\nimport { isActive } from \"./hooks/useStepQueue\";\nimport { STATUS_NONE, STEP_PREPARE, STEP_START } from \"./interface\";\nimport { getTransitionName, supportTransition } from \"./util/motion\";\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nexport function genCSSMotion(config) {\n  var transitionSupport = config;\n  if (_typeof(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = React.useContext(Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = useRef();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = useRef();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : findDOMNode(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = useStatus(supportMotion, visible, getDomElement, props),\n      _useStatus2 = _slicedToArray(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = React.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = React.useCallback(function (node) {\n      nodeRef.current = node;\n      fillRef(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = _objectSpread(_objectSpread({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children(_objectSpread({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if (isActive(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = getTransitionName(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n        className: classNames(getTransitionName(motionName, status), _defineProperty(_defineProperty({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if (/*#__PURE__*/React.isValidElement(motionChildren) && supportRef(motionChildren)) {\n      var originNodeRef = getNodeRef(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/React.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/React.createElement(DomWrapper, {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\nexport default genCSSMotion(supportTransition);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "_typeof", "classNames", "findDOMNode", "fillRef", "getNodeRef", "supportRef", "React", "useRef", "Context", "DomWrapper", "useStatus", "isActive", "STATUS_NONE", "STEP_PREPARE", "STEP_START", "getTransitionName", "supportTransition", "genCSSMotion", "config", "transitionSupport", "isSupportTransition", "props", "contextMotion", "motionName", "CSSMotion", "forwardRef", "ref", "_props$visible", "visible", "_props$removeOnLeave", "removeOnLeave", "forceRender", "children", "leavedClassName", "eventProps", "_React$useContext", "useContext", "motion", "supportMotion", "nodeRef", "wrapperNodeRef", "getDomElement", "current", "HTMLElement", "e", "_useStatus", "_useStatus2", "status", "statusStep", "statusStyle", "mergedVisible", "renderedRef", "setNodeRef", "useCallback", "node", "motion<PERSON><PERSON><PERSON><PERSON>", "mergedProps", "className", "style", "display", "statusSuffix", "motionCls", "concat", "isValidElement", "originNodeRef", "cloneElement", "createElement", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/CSSMotion.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\nimport classNames from 'classnames';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { fillRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport { Context } from \"./context\";\nimport DomWrapper from \"./DomWrapper\";\nimport useStatus from \"./hooks/useStatus\";\nimport { isActive } from \"./hooks/useStepQueue\";\nimport { STATUS_NONE, STEP_PREPARE, STEP_START } from \"./interface\";\nimport { getTransitionName, supportTransition } from \"./util/motion\";\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nexport function genCSSMotion(config) {\n  var transitionSupport = config;\n  if (_typeof(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = React.useContext(Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = useRef();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = useRef();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : findDOMNode(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = useStatus(supportMotion, visible, getDomElement, props),\n      _useStatus2 = _slicedToArray(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = React.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = React.useCallback(function (node) {\n      nodeRef.current = node;\n      fillRef(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = _objectSpread(_objectSpread({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children(_objectSpread({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if (isActive(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = getTransitionName(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n        className: classNames(getTransitionName(motionName, status), _defineProperty(_defineProperty({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/React.isValidElement(motionChildren) && supportRef(motionChildren)) {\n      var originNodeRef = getNodeRef(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/React.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/React.createElement(DomWrapper, {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\nexport default genCSSMotion(supportTransition);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,EAAEC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AAChE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,WAAW,EAAEC,YAAY,EAAEC,UAAU,QAAQ,aAAa;AACnE,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,eAAe;AACpE;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,IAAIC,iBAAiB,GAAGD,MAAM;EAC9B,IAAIlB,OAAO,CAACkB,MAAM,CAAC,KAAK,QAAQ,EAAE;IAChCC,iBAAiB,GAAGD,MAAM,CAACC,iBAAiB;EAC9C;EACA,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,aAAa,EAAE;IACjD,OAAO,CAAC,EAAED,KAAK,CAACE,UAAU,IAAIJ,iBAAiB,IAAIG,aAAa,KAAK,KAAK,CAAC;EAC7E;EACA,IAAIE,SAAS,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,UAAUJ,KAAK,EAAEK,GAAG,EAAE;IAClE,IAAIC,cAAc,GAAGN,KAAK,CAACO,OAAO;MAChCA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;MAC3DE,oBAAoB,GAAGR,KAAK,CAACS,aAAa;MAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;MAC7EE,WAAW,GAAGV,KAAK,CAACU,WAAW;MAC/BC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;MACzBT,UAAU,GAAGF,KAAK,CAACE,UAAU;MAC7BU,eAAe,GAAGZ,KAAK,CAACY,eAAe;MACvCC,UAAU,GAAGb,KAAK,CAACa,UAAU;IAC/B,IAAIC,iBAAiB,GAAG7B,KAAK,CAAC8B,UAAU,CAAC5B,OAAO,CAAC;MAC/Cc,aAAa,GAAGa,iBAAiB,CAACE,MAAM;IAC1C,IAAIC,aAAa,GAAGlB,mBAAmB,CAACC,KAAK,EAAEC,aAAa,CAAC;;IAE7D;IACA,IAAIiB,OAAO,GAAGhC,MAAM,CAAC,CAAC;IACtB;IACA,IAAIiC,cAAc,GAAGjC,MAAM,CAAC,CAAC;IAC7B,SAASkC,aAAaA,CAAA,EAAG;MACvB,IAAI;QACF;QACA;QACA;QACA;QACA,OAAOF,OAAO,CAACG,OAAO,YAAYC,WAAW,GAAGJ,OAAO,CAACG,OAAO,GAAGxC,WAAW,CAACsC,cAAc,CAACE,OAAO,CAAC;MACvG,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV;QACA,OAAO,IAAI;MACb;IACF;IACA,IAAIC,UAAU,GAAGnC,SAAS,CAAC4B,aAAa,EAAEV,OAAO,EAAEa,aAAa,EAAEpB,KAAK,CAAC;MACtEyB,WAAW,GAAG/C,cAAc,CAAC8C,UAAU,EAAE,CAAC,CAAC;MAC3CE,MAAM,GAAGD,WAAW,CAAC,CAAC,CAAC;MACvBE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC3BG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;MAC5BI,aAAa,GAAGJ,WAAW,CAAC,CAAC,CAAC;;IAEhC;IACA;IACA,IAAIK,WAAW,GAAG7C,KAAK,CAACC,MAAM,CAAC2C,aAAa,CAAC;IAC7C,IAAIA,aAAa,EAAE;MACjBC,WAAW,CAACT,OAAO,GAAG,IAAI;IAC5B;;IAEA;IACA,IAAIU,UAAU,GAAG9C,KAAK,CAAC+C,WAAW,CAAC,UAAUC,IAAI,EAAE;MACjDf,OAAO,CAACG,OAAO,GAAGY,IAAI;MACtBnD,OAAO,CAACuB,GAAG,EAAE4B,IAAI,CAAC;IACpB,CAAC,EAAE,CAAC5B,GAAG,CAAC,CAAC;;IAET;IACA,IAAI6B,cAAc;IAClB,IAAIC,WAAW,GAAG1D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MACjEN,OAAO,EAAEA;IACX,CAAC,CAAC;IACF,IAAI,CAACI,QAAQ,EAAE;MACb;MACAuB,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM,IAAIR,MAAM,KAAKnC,WAAW,EAAE;MACjC;MACA,IAAIsC,aAAa,EAAE;QACjBK,cAAc,GAAGvB,QAAQ,CAAClC,aAAa,CAAC,CAAC,CAAC,EAAE0D,WAAW,CAAC,EAAEJ,UAAU,CAAC;MACvE,CAAC,MAAM,IAAI,CAACtB,aAAa,IAAIqB,WAAW,CAACT,OAAO,IAAIT,eAAe,EAAE;QACnEsB,cAAc,GAAGvB,QAAQ,CAAClC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1EC,SAAS,EAAExB;QACb,CAAC,CAAC,EAAEmB,UAAU,CAAC;MACjB,CAAC,MAAM,IAAIrB,WAAW,IAAI,CAACD,aAAa,IAAI,CAACG,eAAe,EAAE;QAC5DsB,cAAc,GAAGvB,QAAQ,CAAClC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1EE,KAAK,EAAE;YACLC,OAAO,EAAE;UACX;QACF,CAAC,CAAC,EAAEP,UAAU,CAAC;MACjB,CAAC,MAAM;QACLG,cAAc,GAAG,IAAI;MACvB;IACF,CAAC,MAAM;MACL;MACA,IAAIK,YAAY;MAChB,IAAIZ,UAAU,KAAKnC,YAAY,EAAE;QAC/B+C,YAAY,GAAG,SAAS;MAC1B,CAAC,MAAM,IAAIjD,QAAQ,CAACqC,UAAU,CAAC,EAAE;QAC/BY,YAAY,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAIZ,UAAU,KAAKlC,UAAU,EAAE;QACpC8C,YAAY,GAAG,OAAO;MACxB;MACA,IAAIC,SAAS,GAAG9C,iBAAiB,CAACQ,UAAU,EAAE,EAAE,CAACuC,MAAM,CAACf,MAAM,EAAE,GAAG,CAAC,CAACe,MAAM,CAACF,YAAY,CAAC,CAAC;MAC1FL,cAAc,GAAGvB,QAAQ,CAAClC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1EC,SAAS,EAAExD,UAAU,CAACc,iBAAiB,CAACQ,UAAU,EAAEwB,MAAM,CAAC,EAAElD,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEgE,SAAS,EAAEA,SAAS,IAAID,YAAY,CAAC,EAAErC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,CAAC,CAAC;QACpLmC,KAAK,EAAET;MACT,CAAC,CAAC,EAAEG,UAAU,CAAC;IACjB;;IAEA;IACA,IAAK,aAAa9C,KAAK,CAACyD,cAAc,CAACR,cAAc,CAAC,IAAIlD,UAAU,CAACkD,cAAc,CAAC,EAAE;MACpF,IAAIS,aAAa,GAAG5D,UAAU,CAACmD,cAAc,CAAC;MAC9C,IAAI,CAACS,aAAa,EAAE;QAClBT,cAAc,GAAG,aAAajD,KAAK,CAAC2D,YAAY,CAACV,cAAc,EAAE;UAC/D7B,GAAG,EAAE0B;QACP,CAAC,CAAC;MACJ;IACF;IACA,OAAO,aAAa9C,KAAK,CAAC4D,aAAa,CAACzD,UAAU,EAAE;MAClDiB,GAAG,EAAEc;IACP,CAAC,EAAEe,cAAc,CAAC;EACpB,CAAC,CAAC;EACF/B,SAAS,CAAC2C,WAAW,GAAG,WAAW;EACnC,OAAO3C,SAAS;AAClB;AACA,eAAeP,YAAY,CAACD,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}