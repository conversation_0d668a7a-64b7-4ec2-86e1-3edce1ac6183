{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nvar TransBtn = function TransBtn(_ref) {\n  var className = _ref.className,\n    customizeIcon = _ref.customizeIcon,\n    customizeIconProps = _ref.customizeIconProps,\n    _onMouseDown = _ref.onMouseDown,\n    onClick = _ref.onClick,\n    children = _ref.children;\n  var icon;\n  if (typeof customizeIcon === 'function') {\n    icon = customizeIcon(customizeIconProps);\n  } else {\n    icon = customizeIcon;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    onMouseDown: function onMouseDown(event) {\n      event.preventDefault();\n      if (_onMouseDown) {\n        _onMouseDown(event);\n      }\n    },\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none'\n    },\n    unselectable: \"on\",\n    onClick: onClick,\n    \"aria-hidden\": true\n  }, icon !== undefined ? icon : /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(className.split(/\\s+/).map(function (cls) {\n      return \"\".concat(cls, \"-icon\");\n    }))\n  }, children));\n};\nexport default TransBtn;", "map": {"version": 3, "names": ["React", "classNames", "TransBtn", "_ref", "className", "customizeIcon", "customizeIconProps", "_onMouseDown", "onMouseDown", "onClick", "children", "icon", "createElement", "event", "preventDefault", "style", "userSelect", "WebkitUserSelect", "unselectable", "undefined", "split", "map", "cls", "concat"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/TransBtn.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nvar TransBtn = function TransBtn(_ref) {\n  var className = _ref.className,\n    customizeIcon = _ref.customizeIcon,\n    customizeIconProps = _ref.customizeIconProps,\n    _onMouseDown = _ref.onMouseDown,\n    onClick = _ref.onClick,\n    children = _ref.children;\n  var icon;\n  if (typeof customizeIcon === 'function') {\n    icon = customizeIcon(customizeIconProps);\n  } else {\n    icon = customizeIcon;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    onMouseDown: function onMouseDown(event) {\n      event.preventDefault();\n      if (_onMouseDown) {\n        _onMouseDown(event);\n      }\n    },\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none'\n    },\n    unselectable: \"on\",\n    onClick: onClick,\n    \"aria-hidden\": true\n  }, icon !== undefined ? icon : /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(className.split(/\\s+/).map(function (cls) {\n      return \"\".concat(cls, \"-icon\");\n    }))\n  }, children));\n};\nexport default TransBtn;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,kBAAkB,GAAGH,IAAI,CAACG,kBAAkB;IAC5CC,YAAY,GAAGJ,IAAI,CAACK,WAAW;IAC/BC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;EAC1B,IAAIC,IAAI;EACR,IAAI,OAAON,aAAa,KAAK,UAAU,EAAE;IACvCM,IAAI,GAAGN,aAAa,CAACC,kBAAkB,CAAC;EAC1C,CAAC,MAAM;IACLK,IAAI,GAAGN,aAAa;EACtB;EACA,OAAO,aAAaL,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IAC9CR,SAAS,EAAEA,SAAS;IACpBI,WAAW,EAAE,SAASA,WAAWA,CAACK,KAAK,EAAE;MACvCA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAIP,YAAY,EAAE;QAChBA,YAAY,CAACM,KAAK,CAAC;MACrB;IACF,CAAC;IACDE,KAAK,EAAE;MACLC,UAAU,EAAE,MAAM;MAClBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE,IAAI;IAClBT,OAAO,EAAEA,OAAO;IAChB,aAAa,EAAE;EACjB,CAAC,EAAEE,IAAI,KAAKQ,SAAS,GAAGR,IAAI,GAAG,aAAaX,KAAK,CAACY,aAAa,CAAC,MAAM,EAAE;IACtER,SAAS,EAAEH,UAAU,CAACG,SAAS,CAACgB,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC9D,OAAO,EAAE,CAACC,MAAM,CAACD,GAAG,EAAE,OAAO,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,EAAEZ,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}