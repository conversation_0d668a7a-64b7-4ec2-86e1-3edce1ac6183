{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport { responsiveArray } from '../_util/responsiveObserve';\nimport warning from '../_util/warning';\nimport SizeContext from './SizeContext';\nvar InternalAvatar = function InternalAvatar(props, ref) {\n  var _classNames, _classNames2;\n  var groupSize = React.useContext(SizeContext);\n  var _React$useState = React.useState(1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    scale = _React$useState2[0],\n    setScale = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  var _React$useState5 = React.useState(true),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isImgExist = _React$useState6[0],\n    setIsImgExist = _React$useState6[1];\n  var avatarNodeRef = React.useRef(null);\n  var avatarChildrenRef = React.useRef(null);\n  var avatarNodeMergeRef = composeRef(ref, avatarNodeRef);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var setScaleParam = function setScaleParam() {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    var childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    var nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      var _props$gap = props.gap,\n        gap = _props$gap === void 0 ? 4 : _props$gap;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n  React.useEffect(function () {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(function () {\n    setScaleParam();\n  }, [props.gap]);\n  var handleImgLoadError = function handleImgLoadError() {\n    var onError = props.onError;\n    var errorFlag = onError ? onError() : undefined;\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  var customizePrefixCls = props.prefixCls,\n    _props$shape = props.shape,\n    shape = _props$shape === void 0 ? 'circle' : _props$shape,\n    _props$size = props.size,\n    customSize = _props$size === void 0 ? 'default' : _props$size,\n    src = props.src,\n    srcSet = props.srcSet,\n    icon = props.icon,\n    className = props.className,\n    alt = props.alt,\n    draggable = props.draggable,\n    children = props.children,\n    crossOrigin = props.crossOrigin,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  var size = customSize === 'default' ? groupSize : customSize;\n  var needResponsive = Object.keys(_typeof(size) === 'object' ? size || {} : {}).some(function (key) {\n    return ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key);\n  });\n  var screens = useBreakpoint(needResponsive);\n  var responsiveSizeStyle = React.useMemo(function () {\n    if (_typeof(size) !== 'object') {\n      return {};\n    }\n    var currentBreakpoint = responsiveArray.find(function (screen) {\n      return screens[screen];\n    });\n    var currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      lineHeight: \"\".concat(currentSize, \"px\"),\n      fontSize: icon ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'Avatar', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\")) : void 0;\n  var prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  var classString = classNames(prefixCls, sizeCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-\").concat(shape), !!shape), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-image\"), hasImageElement || src && isImgExist), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-icon\"), !!icon), _classNames2), className);\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\"),\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  var childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    var transformString = \"scale(\".concat(scale, \") translateX(-50%)\");\n    var childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    var sizeChildrenStyle = typeof size === 'number' ? {\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      ref: avatarChildrenRef,\n      style: _extends(_extends({}, sizeChildrenStyle), childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n  delete others.onError;\n  delete others.gap;\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, others, {\n    style: _extends(_extends(_extends({}, sizeStyle), responsiveSizeStyle), others.style),\n    className: classString,\n    ref: avatarNodeMergeRef\n  }), childrenToRender);\n};\nvar Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "ResizeObserver", "composeRef", "React", "ConfigContext", "useBreakpoint", "responsiveArray", "warning", "SizeContext", "InternalAvatar", "props", "ref", "_classNames", "_classNames2", "groupSize", "useContext", "_React$useState", "useState", "_React$useState2", "scale", "setScale", "_React$useState3", "_React$useState4", "mounted", "setMounted", "_React$useState5", "_React$useState6", "isImgExist", "setIsImgExist", "avatarNodeRef", "useRef", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarNodeMergeRef", "_React$useContext", "getPrefixCls", "setScaleParam", "current", "children<PERSON><PERSON>th", "offsetWidth", "nodeWidth", "_props$gap", "gap", "useEffect", "src", "handleImgLoadError", "onError", "errorFlag", "undefined", "customizePrefixCls", "prefixCls", "_props$shape", "shape", "_props$size", "size", "customSize", "srcSet", "icon", "className", "alt", "draggable", "children", "crossOrigin", "others", "needResponsive", "keys", "some", "key", "includes", "screens", "responsiveSizeStyle", "useMemo", "currentBreakpoint", "find", "screen", "currentSize", "width", "height", "lineHeight", "concat", "fontSize", "process", "env", "NODE_ENV", "sizeCls", "hasImageElement", "isValidElement", "classString", "sizeStyle", "children<PERSON><PERSON><PERSON><PERSON>", "createElement", "transformString", "childrenStyle", "msTransform", "WebkitTransform", "transform", "sizeChildrenStyle", "onResize", "style", "opacity", "Avatar", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/avatar/avatar.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport { responsiveArray } from '../_util/responsiveObserve';\nimport warning from '../_util/warning';\nimport SizeContext from './SizeContext';\nvar InternalAvatar = function InternalAvatar(props, ref) {\n  var _classNames, _classNames2;\n  var groupSize = React.useContext(SizeContext);\n  var _React$useState = React.useState(1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    scale = _React$useState2[0],\n    setScale = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    mounted = _React$useState4[0],\n    setMounted = _React$useState4[1];\n  var _React$useState5 = React.useState(true),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isImgExist = _React$useState6[0],\n    setIsImgExist = _React$useState6[1];\n  var avatarNodeRef = React.useRef(null);\n  var avatarChildrenRef = React.useRef(null);\n  var avatarNodeMergeRef = composeRef(ref, avatarNodeRef);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var setScaleParam = function setScaleParam() {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    var childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    var nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      var _props$gap = props.gap,\n        gap = _props$gap === void 0 ? 4 : _props$gap;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(function () {\n    setMounted(true);\n  }, []);\n  React.useEffect(function () {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(function () {\n    setScaleParam();\n  }, [props.gap]);\n  var handleImgLoadError = function handleImgLoadError() {\n    var onError = props.onError;\n    var errorFlag = onError ? onError() : undefined;\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  var customizePrefixCls = props.prefixCls,\n    _props$shape = props.shape,\n    shape = _props$shape === void 0 ? 'circle' : _props$shape,\n    _props$size = props.size,\n    customSize = _props$size === void 0 ? 'default' : _props$size,\n    src = props.src,\n    srcSet = props.srcSet,\n    icon = props.icon,\n    className = props.className,\n    alt = props.alt,\n    draggable = props.draggable,\n    children = props.children,\n    crossOrigin = props.crossOrigin,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  var size = customSize === 'default' ? groupSize : customSize;\n  var needResponsive = Object.keys(_typeof(size) === 'object' ? size || {} : {}).some(function (key) {\n    return ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key);\n  });\n  var screens = useBreakpoint(needResponsive);\n  var responsiveSizeStyle = React.useMemo(function () {\n    if (_typeof(size) !== 'object') {\n      return {};\n    }\n    var currentBreakpoint = responsiveArray.find(function (screen) {\n      return screens[screen];\n    });\n    var currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      lineHeight: \"\".concat(currentSize, \"px\"),\n      fontSize: icon ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'Avatar', \"`icon` is using ReactNode instead of string naming in v4. Please check `\".concat(icon, \"` at https://ant.design/components/icon\")) : void 0;\n  var prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  var sizeCls = classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), size === 'small'), _classNames));\n  var hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  var classString = classNames(prefixCls, sizeCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-\").concat(shape), !!shape), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-image\"), hasImageElement || src && isImgExist), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-icon\"), !!icon), _classNames2), className);\n  var sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    lineHeight: \"\".concat(size, \"px\"),\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  var childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    var transformString = \"scale(\".concat(scale, \") translateX(-50%)\");\n    var childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    var sizeChildrenStyle = typeof size === 'number' ? {\n      lineHeight: \"\".concat(size, \"px\")\n    } : {};\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      ref: avatarChildrenRef,\n      style: _extends(_extends({}, sizeChildrenStyle), childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-string\"),\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n  delete others.onError;\n  delete others.gap;\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, others, {\n    style: _extends(_extends(_extends({}, sizeStyle), responsiveSizeStyle), others.style),\n    className: classString,\n    ref: avatarNodeMergeRef\n  }), childrenToRender);\n};\nvar Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,IAAIC,WAAW,EAAEC,YAAY;EAC7B,IAAIC,SAAS,GAAGX,KAAK,CAACY,UAAU,CAACP,WAAW,CAAC;EAC7C,IAAIQ,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAGjC,cAAc,CAAC+B,eAAe,EAAE,CAAC,CAAC;IACrDG,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,gBAAgB,GAAGlB,KAAK,CAACc,QAAQ,CAAC,KAAK,CAAC;IAC1CK,gBAAgB,GAAGrC,cAAc,CAACoC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAGtB,KAAK,CAACc,QAAQ,CAAC,IAAI,CAAC;IACzCS,gBAAgB,GAAGzC,cAAc,CAACwC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,aAAa,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIC,iBAAiB,GAAG5B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAIE,kBAAkB,GAAG9B,UAAU,CAACS,GAAG,EAAEkB,aAAa,CAAC;EACvD,IAAII,iBAAiB,GAAG9B,KAAK,CAACY,UAAU,CAACX,aAAa,CAAC;IACrD8B,YAAY,GAAGD,iBAAiB,CAACC,YAAY;EAC/C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,IAAI,CAACJ,iBAAiB,CAACK,OAAO,IAAI,CAACP,aAAa,CAACO,OAAO,EAAE;MACxD;IACF;IACA,IAAIC,aAAa,GAAGN,iBAAiB,CAACK,OAAO,CAACE,WAAW,CAAC,CAAC;IAC3D,IAAIC,SAAS,GAAGV,aAAa,CAACO,OAAO,CAACE,WAAW;IACjD;IACA,IAAID,aAAa,KAAK,CAAC,IAAIE,SAAS,KAAK,CAAC,EAAE;MAC1C,IAAIC,UAAU,GAAG9B,KAAK,CAAC+B,GAAG;QACxBA,GAAG,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;MAC9C,IAAIC,GAAG,GAAG,CAAC,GAAGF,SAAS,EAAE;QACvBnB,QAAQ,CAACmB,SAAS,GAAGE,GAAG,GAAG,CAAC,GAAGJ,aAAa,GAAG,CAACE,SAAS,GAAGE,GAAG,GAAG,CAAC,IAAIJ,aAAa,GAAG,CAAC,CAAC;MAC3F;IACF;EACF,CAAC;EACDlC,KAAK,CAACuC,SAAS,CAAC,YAAY;IAC1BlB,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNrB,KAAK,CAACuC,SAAS,CAAC,YAAY;IAC1Bd,aAAa,CAAC,IAAI,CAAC;IACnBR,QAAQ,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAACV,KAAK,CAACiC,GAAG,CAAC,CAAC;EACfxC,KAAK,CAACuC,SAAS,CAAC,YAAY;IAC1BP,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACzB,KAAK,CAAC+B,GAAG,CAAC,CAAC;EACf,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIC,OAAO,GAAGnC,KAAK,CAACmC,OAAO;IAC3B,IAAIC,SAAS,GAAGD,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAGE,SAAS;IAC/C,IAAID,SAAS,KAAK,KAAK,EAAE;MACvBlB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EACD,IAAIoB,kBAAkB,GAAGtC,KAAK,CAACuC,SAAS;IACtCC,YAAY,GAAGxC,KAAK,CAACyC,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,YAAY;IACzDE,WAAW,GAAG1C,KAAK,CAAC2C,IAAI;IACxBC,UAAU,GAAGF,WAAW,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,WAAW;IAC7DT,GAAG,GAAGjC,KAAK,CAACiC,GAAG;IACfY,MAAM,GAAG7C,KAAK,CAAC6C,MAAM;IACrBC,IAAI,GAAG9C,KAAK,CAAC8C,IAAI;IACjBC,SAAS,GAAG/C,KAAK,CAAC+C,SAAS;IAC3BC,GAAG,GAAGhD,KAAK,CAACgD,GAAG;IACfC,SAAS,GAAGjD,KAAK,CAACiD,SAAS;IAC3BC,QAAQ,GAAGlD,KAAK,CAACkD,QAAQ;IACzBC,WAAW,GAAGnD,KAAK,CAACmD,WAAW;IAC/BC,MAAM,GAAG5E,MAAM,CAACwB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;EAC7I,IAAI2C,IAAI,GAAGC,UAAU,KAAK,SAAS,GAAGxC,SAAS,GAAGwC,UAAU;EAC5D,IAAIS,cAAc,GAAGxE,MAAM,CAACyE,IAAI,CAAChF,OAAO,CAACqE,IAAI,CAAC,KAAK,QAAQ,GAAGA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACY,IAAI,CAAC,UAAUC,GAAG,EAAE;IACjG,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC;EAC5D,CAAC,CAAC;EACF,IAAIE,OAAO,GAAG/D,aAAa,CAAC0D,cAAc,CAAC;EAC3C,IAAIM,mBAAmB,GAAGlE,KAAK,CAACmE,OAAO,CAAC,YAAY;IAClD,IAAItF,OAAO,CAACqE,IAAI,CAAC,KAAK,QAAQ,EAAE;MAC9B,OAAO,CAAC,CAAC;IACX;IACA,IAAIkB,iBAAiB,GAAGjE,eAAe,CAACkE,IAAI,CAAC,UAAUC,MAAM,EAAE;MAC7D,OAAOL,OAAO,CAACK,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,IAAIC,WAAW,GAAGrB,IAAI,CAACkB,iBAAiB,CAAC;IACzC,OAAOG,WAAW,GAAG;MACnBC,KAAK,EAAED,WAAW;MAClBE,MAAM,EAAEF,WAAW;MACnBG,UAAU,EAAE,EAAE,CAACC,MAAM,CAACJ,WAAW,EAAE,IAAI,CAAC;MACxCK,QAAQ,EAAEvB,IAAI,GAAGkB,WAAW,GAAG,CAAC,GAAG;IACrC,CAAC,GAAG,CAAC,CAAC;EACR,CAAC,EAAE,CAACN,OAAO,EAAEf,IAAI,CAAC,CAAC;EACnB2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3E,OAAO,CAAC,EAAE,OAAOiD,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC1D,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,0EAA0E,CAACgF,MAAM,CAACtB,IAAI,EAAE,yCAAyC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtP,IAAIP,SAAS,GAAGf,YAAY,CAAC,QAAQ,EAAEc,kBAAkB,CAAC;EAC1D,IAAImC,OAAO,GAAGnF,UAAU,EAAEY,WAAW,GAAG,CAAC,CAAC,EAAE7B,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC7B,SAAS,EAAE,KAAK,CAAC,EAAEI,IAAI,KAAK,OAAO,CAAC,EAAEtE,eAAe,CAAC6B,WAAW,EAAE,EAAE,CAACkE,MAAM,CAAC7B,SAAS,EAAE,KAAK,CAAC,EAAEI,IAAI,KAAK,OAAO,CAAC,EAAEzC,WAAW,CAAC,CAAC;EACnN,IAAIwE,eAAe,GAAG,aAAajF,KAAK,CAACkF,cAAc,CAAC1C,GAAG,CAAC;EAC5D,IAAI2C,WAAW,GAAGtF,UAAU,CAACiD,SAAS,EAAEkC,OAAO,GAAGtE,YAAY,GAAG,CAAC,CAAC,EAAE9B,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAACiE,MAAM,CAAC7B,SAAS,EAAE,GAAG,CAAC,CAAC6B,MAAM,CAAC3B,KAAK,CAAC,EAAE,CAAC,CAACA,KAAK,CAAC,EAAEpE,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAACiE,MAAM,CAAC7B,SAAS,EAAE,QAAQ,CAAC,EAAEmC,eAAe,IAAIzC,GAAG,IAAIhB,UAAU,CAAC,EAAE5C,eAAe,CAAC8B,YAAY,EAAE,EAAE,CAACiE,MAAM,CAAC7B,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAACO,IAAI,CAAC,EAAE3C,YAAY,GAAG4C,SAAS,CAAC;EAC1V,IAAI8B,SAAS,GAAG,OAAOlC,IAAI,KAAK,QAAQ,GAAG;IACzCsB,KAAK,EAAEtB,IAAI;IACXuB,MAAM,EAAEvB,IAAI;IACZwB,UAAU,EAAE,EAAE,CAACC,MAAM,CAACzB,IAAI,EAAE,IAAI,CAAC;IACjC0B,QAAQ,EAAEvB,IAAI,GAAGH,IAAI,GAAG,CAAC,GAAG;EAC9B,CAAC,GAAG,CAAC,CAAC;EACN,IAAImC,gBAAgB;EACpB,IAAI,OAAO7C,GAAG,KAAK,QAAQ,IAAIhB,UAAU,EAAE;IACzC6D,gBAAgB,GAAG,aAAarF,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;MACzD9C,GAAG,EAAEA,GAAG;MACRgB,SAAS,EAAEA,SAAS;MACpBJ,MAAM,EAAEA,MAAM;MACdV,OAAO,EAAED,kBAAkB;MAC3Bc,GAAG,EAAEA,GAAG;MACRG,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIuB,eAAe,EAAE;IAC1BI,gBAAgB,GAAG7C,GAAG;EACxB,CAAC,MAAM,IAAIa,IAAI,EAAE;IACfgC,gBAAgB,GAAGhC,IAAI;EACzB,CAAC,MAAM,IAAIjC,OAAO,IAAIJ,KAAK,KAAK,CAAC,EAAE;IACjC,IAAIuE,eAAe,GAAG,QAAQ,CAACZ,MAAM,CAAC3D,KAAK,EAAE,oBAAoB,CAAC;IAClE,IAAIwE,aAAa,GAAG;MAClBC,WAAW,EAAEF,eAAe;MAC5BG,eAAe,EAAEH,eAAe;MAChCI,SAAS,EAAEJ;IACb,CAAC;IACD,IAAIK,iBAAiB,GAAG,OAAO1C,IAAI,KAAK,QAAQ,GAAG;MACjDwB,UAAU,EAAE,EAAE,CAACC,MAAM,CAACzB,IAAI,EAAE,IAAI;IAClC,CAAC,GAAG,CAAC,CAAC;IACNmC,gBAAgB,GAAG,aAAarF,KAAK,CAACsF,aAAa,CAACxF,cAAc,EAAE;MAClE+F,QAAQ,EAAE7D;IACZ,CAAC,EAAE,aAAahC,KAAK,CAACsF,aAAa,CAAC,MAAM,EAAE;MAC1ChC,SAAS,EAAE,EAAE,CAACqB,MAAM,CAAC7B,SAAS,EAAE,SAAS,CAAC;MAC1CtC,GAAG,EAAEoB,iBAAiB;MACtBkE,KAAK,EAAEnH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiH,iBAAiB,CAAC,EAAEJ,aAAa;IAChE,CAAC,EAAE/B,QAAQ,CAAC,CAAC;EACf,CAAC,MAAM;IACL4B,gBAAgB,GAAG,aAAarF,KAAK,CAACsF,aAAa,CAAC,MAAM,EAAE;MAC1DhC,SAAS,EAAE,EAAE,CAACqB,MAAM,CAAC7B,SAAS,EAAE,SAAS,CAAC;MAC1CgD,KAAK,EAAE;QACLC,OAAO,EAAE;MACX,CAAC;MACDvF,GAAG,EAAEoB;IACP,CAAC,EAAE6B,QAAQ,CAAC;EACd;EACA;EACA;EACA,OAAOE,MAAM,CAACjB,OAAO;EACrB,OAAOiB,MAAM,CAACrB,GAAG;EACjB,OAAO,aAAatC,KAAK,CAACsF,aAAa,CAAC,MAAM,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAEgF,MAAM,EAAE;IACnEmC,KAAK,EAAEnH,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyG,SAAS,CAAC,EAAElB,mBAAmB,CAAC,EAAEP,MAAM,CAACmC,KAAK,CAAC;IACrFxC,SAAS,EAAE6B,WAAW;IACtB3E,GAAG,EAAEqB;EACP,CAAC,CAAC,EAAEwD,gBAAgB,CAAC;AACvB,CAAC;AACD,IAAIW,MAAM,GAAG,aAAahG,KAAK,CAACiG,UAAU,CAAC3F,cAAc,CAAC;AAC1D,IAAIuE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCiB,MAAM,CAACE,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}