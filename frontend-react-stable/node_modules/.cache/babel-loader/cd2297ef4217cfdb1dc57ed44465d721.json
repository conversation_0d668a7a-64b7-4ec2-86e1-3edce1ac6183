{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport NotificationOutlinedSvg from \"@ant-design/icons-svg/es/asn/NotificationOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar NotificationOutlined = function NotificationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: NotificationOutlinedSvg\n  }));\n};\nNotificationOutlined.displayName = 'NotificationOutlined';\nexport default /*#__PURE__*/React.forwardRef(NotificationOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "NotificationOutlinedSvg", "AntdIcon", "NotificationOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/NotificationOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport NotificationOutlinedSvg from \"@ant-design/icons-svg/es/asn/NotificationOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar NotificationOutlined = function NotificationOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: NotificationOutlinedSvg\n  }));\n};\nNotificationOutlined.displayName = 'NotificationOutlined';\nexport default /*#__PURE__*/React.forwardRef(NotificationOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,uBAAuB,MAAM,mDAAmD;AACvF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,oBAAoB,CAACK,WAAW,GAAG,sBAAsB;AACzD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}