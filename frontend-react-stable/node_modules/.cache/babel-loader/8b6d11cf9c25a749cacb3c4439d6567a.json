{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = groupSort;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _group = _interopRequireWildcard(require(\"./group.js\"));\nvar _sort = _interopRequireDefault(require(\"./sort.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function (nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n    return {\n      default: obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj.default = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction groupSort(values, reduce, key) {\n  return (reduce.length !== 2 ? (0, _sort.default)((0, _group.rollup)(values, reduce, key), (_ref, _ref2) => {\n    let [ak, av] = _ref;\n    let [bk, bv] = _ref2;\n    return (0, _ascending.default)(av, bv) || (0, _ascending.default)(ak, bk);\n  }) : (0, _sort.default)((0, _group.default)(values, key), (_ref3, _ref4) => {\n    let [ak, av] = _ref3;\n    let [bk, bv] = _ref4;\n    return reduce(av, bv) || (0, _ascending.default)(ak, bk);\n  })).map(_ref5 => {\n    let [key] = _ref5;\n    return key;\n  });\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "groupSort", "_ascending", "_interopRequireDefault", "require", "_group", "_interopRequireWildcard", "_sort", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "values", "reduce", "length", "rollup", "_ref", "_ref2", "ak", "av", "bk", "bv", "_ref3", "_ref4", "map", "_ref5"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/groupSort.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = groupSort;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _group = _interopRequireWildcard(require(\"./group.js\"));\n\nvar _sort = _interopRequireDefault(require(\"./sort.js\"));\n\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\n\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction groupSort(values, reduce, key) {\n  return (reduce.length !== 2 ? (0, _sort.default)((0, _group.rollup)(values, reduce, key), ([ak, av], [bk, bv]) => (0, _ascending.default)(av, bv) || (0, _ascending.default)(ak, bk)) : (0, _sort.default)((0, _group.default)(values, key), ([ak, av], [bk, bv]) => reduce(av, bv) || (0, _ascending.default)(ak, bk))).map(([key]) => key);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,SAAS;AAE3B,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,MAAM,GAAGC,uBAAuB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAE3D,IAAIG,KAAK,GAAGJ,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,SAASI,wBAAwBA,CAACC,WAAW,EAAE;EAAE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;EAAE,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,WAAW,EAAE;IAAE,OAAOA,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;EAAE,CAAC,EAAEF,WAAW,CAAC;AAAE;AAEtT,SAASH,uBAAuBA,CAACO,GAAG,EAAEJ,WAAW,EAAE;EAAE,IAAI,CAACA,WAAW,IAAII,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE;EAAE,IAAIA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAE,OAAO;MAAEb,OAAO,EAAEa;IAAI,CAAC;EAAE;EAAE,IAAIE,KAAK,GAAGP,wBAAwB,CAACC,WAAW,CAAC;EAAE,IAAIM,KAAK,IAAIA,KAAK,CAACC,GAAG,CAACH,GAAG,CAAC,EAAE;IAAE,OAAOE,KAAK,CAACE,GAAG,CAACJ,GAAG,CAAC;EAAE;EAAE,IAAIK,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,qBAAqB,GAAGvB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACwB,wBAAwB;EAAE,KAAK,IAAIC,GAAG,IAAIR,GAAG,EAAE;IAAE,IAAIQ,GAAG,KAAK,SAAS,IAAIzB,MAAM,CAAC0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,GAAG,EAAEQ,GAAG,CAAC,EAAE;MAAE,IAAII,IAAI,GAAGN,qBAAqB,GAAGvB,MAAM,CAACwB,wBAAwB,CAACP,GAAG,EAAEQ,GAAG,CAAC,GAAG,IAAI;MAAE,IAAII,IAAI,KAAKA,IAAI,CAACR,GAAG,IAAIQ,IAAI,CAACC,GAAG,CAAC,EAAE;QAAE9B,MAAM,CAACC,cAAc,CAACqB,MAAM,EAAEG,GAAG,EAAEI,IAAI,CAAC;MAAE,CAAC,MAAM;QAAEP,MAAM,CAACG,GAAG,CAAC,GAAGR,GAAG,CAACQ,GAAG,CAAC;MAAE;IAAE;EAAE;EAAEH,MAAM,CAAClB,OAAO,GAAGa,GAAG;EAAE,IAAIE,KAAK,EAAE;IAAEA,KAAK,CAACW,GAAG,CAACb,GAAG,EAAEK,MAAM,CAAC;EAAE;EAAE,OAAOA,MAAM;AAAE;AAEnyB,SAASf,sBAAsBA,CAACU,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEb,OAAO,EAAEa;EAAI,CAAC;AAAE;AAE9F,SAASZ,SAASA,CAAC0B,MAAM,EAAEC,MAAM,EAAEP,GAAG,EAAE;EACtC,OAAO,CAACO,MAAM,CAACC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,EAAEtB,KAAK,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEK,MAAM,CAACyB,MAAM,EAAEH,MAAM,EAAEC,MAAM,EAAEP,GAAG,CAAC,EAAE,CAAAU,IAAA,EAAAC,KAAA;IAAA,IAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAAH,IAAA;IAAA,IAAE,CAACI,EAAE,EAAEC,EAAE,CAAC,GAAAJ,KAAA;IAAA,OAAK,CAAC,CAAC,EAAE9B,UAAU,CAACF,OAAO,EAAEkC,EAAE,EAAEE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAElC,UAAU,CAACF,OAAO,EAAEiC,EAAE,EAAEE,EAAE,CAAC;EAAA,EAAC,GAAG,CAAC,CAAC,EAAE5B,KAAK,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEK,MAAM,CAACL,OAAO,EAAE2B,MAAM,EAAEN,GAAG,CAAC,EAAE,CAAAgB,KAAA,EAAAC,KAAA;IAAA,IAAC,CAACL,EAAE,EAAEC,EAAE,CAAC,GAAAG,KAAA;IAAA,IAAE,CAACF,EAAE,EAAEC,EAAE,CAAC,GAAAE,KAAA;IAAA,OAAKV,MAAM,CAACM,EAAE,EAAEE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAElC,UAAU,CAACF,OAAO,EAAEiC,EAAE,EAAEE,EAAE,CAAC;EAAA,EAAC,EAAEI,GAAG,CAACC,KAAA;IAAA,IAAC,CAACnB,GAAG,CAAC,GAAAmB,KAAA;IAAA,OAAKnB,GAAG;EAAA,EAAC;AAC9U", "ignoreList": []}, "metadata": {}, "sourceType": "script"}