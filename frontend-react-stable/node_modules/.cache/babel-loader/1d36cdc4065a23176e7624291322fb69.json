{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\nfunction isThenable(thing) {\n  return !!(thing && !!thing.then);\n}\nvar ActionButton = function ActionButton(props) {\n  var clickedRef = React.useRef(false);\n  var ref = React.useRef();\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loading = _useState2[0],\n    setLoading = _useState2[1];\n  var close = props.close;\n  var onInternalClose = function onInternalClose() {\n    close === null || close === void 0 ? void 0 : close.apply(void 0, arguments);\n  };\n  React.useEffect(function () {\n    var timeoutId;\n    if (props.autoFocus) {\n      var $this = ref.current;\n      timeoutId = setTimeout(function () {\n        return $this.focus();\n      });\n    }\n    return function () {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  var handlePromiseOnOk = function handlePromiseOnOk(returnValueOfOnOk) {\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      onInternalClose.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, function (e) {\n      // Emit error when catch promise reject\n      // eslint-disable-next-line no-console\n      console.error(e);\n      // See: https://github.com/ant-design/ant-design/issues/6183\n      setLoading(false, true);\n      clickedRef.current = false;\n    });\n  };\n  var onClick = function onClick(e) {\n    var actionFn = props.actionFn;\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      onInternalClose();\n      return;\n    }\n    var returnValueOfOnOk;\n    if (props.emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (props.quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        onInternalClose(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close);\n      // https://github.com/ant-design/ant-design/issues/23358\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!returnValueOfOnOk) {\n        onInternalClose();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  var type = props.type,\n    children = props.children,\n    prefixCls = props.prefixCls,\n    buttonProps = props.buttonProps;\n  return /*#__PURE__*/React.createElement(Button, _extends({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: ref\n  }), children);\n};\nexport default ActionButton;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "useState", "React", "<PERSON><PERSON>", "convertLegacyProps", "isThenable", "thing", "then", "ActionButton", "props", "clickedRef", "useRef", "ref", "_useState", "_useState2", "loading", "setLoading", "close", "onInternalClose", "apply", "arguments", "useEffect", "timeoutId", "autoFocus", "$this", "current", "setTimeout", "focus", "clearTimeout", "handlePromiseOnOk", "returnValueOfOnOk", "e", "console", "error", "onClick", "actionFn", "emitEvent", "quitOnNullishReturnValue", "length", "type", "children", "prefixCls", "buttonProps", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/ActionButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/button';\nfunction isThenable(thing) {\n  return !!(thing && !!thing.then);\n}\nvar ActionButton = function ActionButton(props) {\n  var clickedRef = React.useRef(false);\n  var ref = React.useRef();\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    loading = _useState2[0],\n    setLoading = _useState2[1];\n  var close = props.close;\n  var onInternalClose = function onInternalClose() {\n    close === null || close === void 0 ? void 0 : close.apply(void 0, arguments);\n  };\n  React.useEffect(function () {\n    var timeoutId;\n    if (props.autoFocus) {\n      var $this = ref.current;\n      timeoutId = setTimeout(function () {\n        return $this.focus();\n      });\n    }\n    return function () {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  var handlePromiseOnOk = function handlePromiseOnOk(returnValueOfOnOk) {\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      onInternalClose.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, function (e) {\n      // Emit error when catch promise reject\n      // eslint-disable-next-line no-console\n      console.error(e);\n      // See: https://github.com/ant-design/ant-design/issues/6183\n      setLoading(false, true);\n      clickedRef.current = false;\n    });\n  };\n  var onClick = function onClick(e) {\n    var actionFn = props.actionFn;\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      onInternalClose();\n      return;\n    }\n    var returnValueOfOnOk;\n    if (props.emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (props.quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        onInternalClose(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close);\n      // https://github.com/ant-design/ant-design/issues/23358\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!returnValueOfOnOk) {\n        onInternalClose();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  var type = props.type,\n    children = props.children,\n    prefixCls = props.prefixCls,\n    buttonProps = props.buttonProps;\n  return /*#__PURE__*/React.createElement(Button, _extends({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: ref\n  }), children);\n};\nexport default ActionButton;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAAC,EAAEA,KAAK,IAAI,CAAC,CAACA,KAAK,CAACC,IAAI,CAAC;AAClC;AACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC9C,IAAIC,UAAU,GAAGR,KAAK,CAACS,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIC,GAAG,GAAGV,KAAK,CAACS,MAAM,CAAC,CAAC;EACxB,IAAIE,SAAS,GAAGZ,QAAQ,CAAC,KAAK,CAAC;IAC7Ba,UAAU,GAAGd,cAAc,CAACa,SAAS,EAAE,CAAC,CAAC;IACzCE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC5B,IAAIG,KAAK,GAAGR,KAAK,CAACQ,KAAK;EACvB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/CD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;EAC9E,CAAC;EACDlB,KAAK,CAACmB,SAAS,CAAC,YAAY;IAC1B,IAAIC,SAAS;IACb,IAAIb,KAAK,CAACc,SAAS,EAAE;MACnB,IAAIC,KAAK,GAAGZ,GAAG,CAACa,OAAO;MACvBH,SAAS,GAAGI,UAAU,CAAC,YAAY;QACjC,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;IACA,OAAO,YAAY;MACjB,IAAIL,SAAS,EAAE;QACbM,YAAY,CAACN,SAAS,CAAC;MACzB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIO,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,iBAAiB,EAAE;IACpE,IAAI,CAACzB,UAAU,CAACyB,iBAAiB,CAAC,EAAE;MAClC;IACF;IACAd,UAAU,CAAC,IAAI,CAAC;IAChBc,iBAAiB,CAACvB,IAAI,CAAC,YAAY;MACjCS,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBE,eAAe,CAACC,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACxCV,UAAU,CAACe,OAAO,GAAG,KAAK;IAC5B,CAAC,EAAE,UAAUM,CAAC,EAAE;MACd;MACA;MACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAChB;MACAf,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBN,UAAU,CAACe,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC;EACD,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACH,CAAC,EAAE;IAChC,IAAII,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;IAC7B,IAAIzB,UAAU,CAACe,OAAO,EAAE;MACtB;IACF;IACAf,UAAU,CAACe,OAAO,GAAG,IAAI;IACzB,IAAI,CAACU,QAAQ,EAAE;MACbjB,eAAe,CAAC,CAAC;MACjB;IACF;IACA,IAAIY,iBAAiB;IACrB,IAAIrB,KAAK,CAAC2B,SAAS,EAAE;MACnBN,iBAAiB,GAAGK,QAAQ,CAACJ,CAAC,CAAC;MAC/B,IAAItB,KAAK,CAAC4B,wBAAwB,IAAI,CAAChC,UAAU,CAACyB,iBAAiB,CAAC,EAAE;QACpEpB,UAAU,CAACe,OAAO,GAAG,KAAK;QAC1BP,eAAe,CAACa,CAAC,CAAC;QAClB;MACF;IACF,CAAC,MAAM,IAAII,QAAQ,CAACG,MAAM,EAAE;MAC1BR,iBAAiB,GAAGK,QAAQ,CAAClB,KAAK,CAAC;MACnC;MACAP,UAAU,CAACe,OAAO,GAAG,KAAK;IAC5B,CAAC,MAAM;MACLK,iBAAiB,GAAGK,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAACL,iBAAiB,EAAE;QACtBZ,eAAe,CAAC,CAAC;QACjB;MACF;IACF;IACAW,iBAAiB,CAACC,iBAAiB,CAAC;EACtC,CAAC;EACD,IAAIS,IAAI,GAAG9B,KAAK,CAAC8B,IAAI;IACnBC,QAAQ,GAAG/B,KAAK,CAAC+B,QAAQ;IACzBC,SAAS,GAAGhC,KAAK,CAACgC,SAAS;IAC3BC,WAAW,GAAGjC,KAAK,CAACiC,WAAW;EACjC,OAAO,aAAaxC,KAAK,CAACyC,aAAa,CAACxC,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEK,kBAAkB,CAACmC,IAAI,CAAC,EAAE;IACrFL,OAAO,EAAEA,OAAO;IAChBnB,OAAO,EAAEA,OAAO;IAChB0B,SAAS,EAAEA;EACb,CAAC,EAAEC,WAAW,EAAE;IACd9B,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE4B,QAAQ,CAAC;AACf,CAAC;AACD,eAAehC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}