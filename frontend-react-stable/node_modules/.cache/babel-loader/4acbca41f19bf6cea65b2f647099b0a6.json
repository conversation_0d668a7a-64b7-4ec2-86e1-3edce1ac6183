{"ast": null, "code": "export function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  var mergedValue = value || 'value';\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: mergedValue,\n    key: mergedValue,\n    children: children || 'children'\n  };\n}\nexport function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n}\n/** Loop fetch all the keys exist in the tree */\n\nexport function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  }\n  dig(treeData);\n  return keys;\n}\nexport function isNil(val) {\n  return val === null || val === undefined;\n}", "map": {"version": 3, "names": ["toArray", "value", "Array", "isArray", "undefined", "fillFieldNames", "fieldNames", "_ref", "label", "children", "mergedValue", "_title", "key", "isCheckDisabled", "node", "disabled", "disableCheckbox", "checkable", "getAllKeys", "treeData", "keys", "dig", "list", "for<PERSON>ach", "item", "push", "isNil", "val"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/utils/valueUtil.js"], "sourcesContent": ["export function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return value !== undefined ? [value] : [];\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n      label = _ref.label,\n      value = _ref.value,\n      children = _ref.children;\n\n  var mergedValue = value || 'value';\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: mergedValue,\n    key: mergedValue,\n    children: children || 'children'\n  };\n}\nexport function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n}\n/** Loop fetch all the keys exist in the tree */\n\nexport function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n\n  function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  }\n\n  dig(treeData);\n  return keys;\n}\nexport function isNil(val) {\n  return val === null || val === undefined;\n}"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EAEA,OAAOA,KAAK,KAAKG,SAAS,GAAG,CAACH,KAAK,CAAC,GAAG,EAAE;AAC3C;AACA,OAAO,SAASI,cAAcA,CAACC,UAAU,EAAE;EACzC,IAAIC,IAAI,GAAGD,UAAU,IAAI,CAAC,CAAC;IACvBE,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAClBQ,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAE5B,IAAIC,WAAW,GAAGT,KAAK,IAAI,OAAO;EAClC,OAAO;IACLU,MAAM,EAAEH,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;IAC5CP,KAAK,EAAES,WAAW;IAClBE,GAAG,EAAEF,WAAW;IAChBD,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC;AACH;AACA,OAAO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,CAACA,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,eAAe,IAAIF,IAAI,CAACG,SAAS,KAAK,KAAK;AACnF;AACA;;AAEA,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEb,UAAU,EAAE;EAC/C,IAAIc,IAAI,GAAG,EAAE;EAEb,SAASC,GAAGA,CAACC,IAAI,EAAE;IACjBA,IAAI,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3B,IAAIf,QAAQ,GAAGe,IAAI,CAAClB,UAAU,CAACG,QAAQ,CAAC;MAExC,IAAIA,QAAQ,EAAE;QACZW,IAAI,CAACK,IAAI,CAACD,IAAI,CAAClB,UAAU,CAACL,KAAK,CAAC,CAAC;QACjCoB,GAAG,CAACZ,QAAQ,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEAY,GAAG,CAACF,QAAQ,CAAC;EACb,OAAOC,IAAI;AACb;AACA,OAAO,SAASM,KAAKA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKvB,SAAS;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}