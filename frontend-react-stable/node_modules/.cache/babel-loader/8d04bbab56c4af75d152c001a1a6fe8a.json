{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonInput = function SkeletonInput(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    block = props.block,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-input\"),\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonInput;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "omit", "React", "ConfigContext", "Element", "SkeletonInput", "props", "_classNames", "customizePrefixCls", "prefixCls", "className", "active", "block", "_props$size", "size", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Input.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonInput = function SkeletonInput(props) {\n  var _classNames;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    block = props.block,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-block\"), block), _classNames), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-input\"),\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonInput;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,WAAW;EACf,IAAIC,kBAAkB,GAAGF,KAAK,CAACG,SAAS;IACtCC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,WAAW,GAAGP,KAAK,CAACQ,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,WAAW;EACzD,IAAIE,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACb,aAAa,CAAC;IACrDc,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIR,SAAS,GAAGQ,YAAY,CAAC,UAAU,EAAET,kBAAkB,CAAC;EAC5D,IAAIU,UAAU,GAAGjB,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,IAAIa,GAAG,GAAGnB,UAAU,CAACS,SAAS,EAAE,EAAE,CAACW,MAAM,CAACX,SAAS,EAAE,UAAU,CAAC,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACa,MAAM,CAACX,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAEZ,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACa,MAAM,CAACX,SAAS,EAAE,QAAQ,CAAC,EAAEG,KAAK,CAAC,EAAEL,WAAW,GAAGG,SAAS,CAAC;EACzP,OAAO,aAAaR,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CX,SAAS,EAAES;EACb,CAAC,EAAE,aAAajB,KAAK,CAACmB,aAAa,CAACjB,OAAO,EAAEN,QAAQ,CAAC;IACpDW,SAAS,EAAE,EAAE,CAACW,MAAM,CAACX,SAAS,EAAE,QAAQ,CAAC;IACzCK,IAAI,EAAEA;EACR,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}