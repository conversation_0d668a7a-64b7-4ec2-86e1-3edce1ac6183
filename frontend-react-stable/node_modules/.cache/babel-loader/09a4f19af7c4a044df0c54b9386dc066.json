{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = reduce;\nfunction reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done,\n    next,\n    index = -1;\n  if (arguments.length < 3) {\n    ({\n      done,\n      value\n    } = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while ({\n    done,\n    value: next\n  } = iterator.next(), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "reduce", "values", "reducer", "TypeError", "iterator", "Symbol", "done", "next", "index", "arguments", "length"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/reduce.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = reduce;\n\nfunction reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done,\n      next,\n      index = -1;\n\n  if (arguments.length < 3) {\n    ({\n      done,\n      value\n    } = iterator.next());\n    if (done) return;\n    ++index;\n  }\n\n  while (({\n    done,\n    value: next\n  } = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n\n  return value;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAEJ,KAAK,EAAE;EACtC,IAAI,OAAOI,OAAO,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,2BAA2B,CAAC;EACnF,MAAMC,QAAQ,GAAGH,MAAM,CAACI,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;EAC1C,IAAIE,IAAI;IACJC,IAAI;IACJC,KAAK,GAAG,CAAC,CAAC;EAEd,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,CAAC;MACCJ,IAAI;MACJR;IACF,CAAC,GAAGM,QAAQ,CAACG,IAAI,CAAC,CAAC;IACnB,IAAID,IAAI,EAAE;IACV,EAAEE,KAAK;EACT;EAEA,OAAQ;IACNF,IAAI;IACJR,KAAK,EAAES;EACT,CAAC,GAAGH,QAAQ,CAACG,IAAI,CAAC,CAAC,EAAG,CAACD,IAAI,EAAE;IAC3BR,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAES,IAAI,EAAE,EAAEC,KAAK,EAAEP,MAAM,CAAC;EAC/C;EAEA,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}