{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _formatDecimal = _interopRequireDefault(require(\"./formatDecimal.js\"));\nvar _formatPrefixAuto = _interopRequireDefault(require(\"./formatPrefixAuto.js\"));\nvar _formatRounded = _interopRequireDefault(require(\"./formatRounded.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar _default = {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": x => Math.round(x).toString(2),\n  \"c\": x => x + \"\",\n  \"d\": _formatDecimal.default,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": x => Math.round(x).toString(8),\n  \"p\": (x, p) => (0, _formatRounded.default)(x * 100, p),\n  \"r\": _formatRounded.default,\n  \"s\": _formatPrefixAuto.default,\n  \"X\": x => Math.round(x).toString(16).toUpperCase(),\n  \"x\": x => Math.round(x).toString(16)\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_formatDecimal", "_interopRequireDefault", "require", "_formatPrefixAuto", "_formatRounded", "obj", "__esModule", "_default", "%", "x", "p", "toFixed", "Math", "round", "toString", "e", "toExponential", "f", "g", "toPrecision", "toUpperCase"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatTypes.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _formatDecimal = _interopRequireDefault(require(\"./formatDecimal.js\"));\n\nvar _formatPrefixAuto = _interopRequireDefault(require(\"./formatPrefixAuto.js\"));\n\nvar _formatRounded = _interopRequireDefault(require(\"./formatRounded.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar _default = {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": x => Math.round(x).toString(2),\n  \"c\": x => x + \"\",\n  \"d\": _formatDecimal.default,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": x => Math.round(x).toString(8),\n  \"p\": (x, p) => (0, _formatRounded.default)(x * 100, p),\n  \"r\": _formatRounded.default,\n  \"s\": _formatPrefixAuto.default,\n  \"X\": x => Math.round(x).toString(16).toUpperCase(),\n  \"x\": x => Math.round(x).toString(16)\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,cAAc,GAAGC,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE1E,IAAIC,iBAAiB,GAAGF,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAEhF,IAAIE,cAAc,GAAGH,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAE1E,SAASD,sBAAsBA,CAACI,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,IAAIE,QAAQ,GAAG;EACb,GAAG,EAAEC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,GAAG,GAAG,EAAEE,OAAO,CAACD,CAAC,CAAC;EACnC,GAAG,EAAED,CAAC,IAAIG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;EACnC,GAAG,EAAEL,CAAC,IAAIA,CAAC,GAAG,EAAE;EAChB,GAAG,EAAET,cAAc,CAACD,OAAO;EAC3B,GAAG,EAAEgB,CAACN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACO,aAAa,CAACN,CAAC,CAAC;EACjC,GAAG,EAAEO,CAACR,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,OAAO,CAACD,CAAC,CAAC;EAC3B,GAAG,EAAEQ,CAACT,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACU,WAAW,CAACT,CAAC,CAAC;EAC/B,GAAG,EAAED,CAAC,IAAIG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;EACnC,GAAG,EAAEJ,CAACD,CAAC,EAAEC,CAAC,KAAK,CAAC,CAAC,EAAEN,cAAc,CAACL,OAAO,EAAEU,CAAC,GAAG,GAAG,EAAEC,CAAC,CAAC;EACtD,GAAG,EAAEN,cAAc,CAACL,OAAO;EAC3B,GAAG,EAAEI,iBAAiB,CAACJ,OAAO;EAC9B,GAAG,EAAEU,CAAC,IAAIG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC,CAACM,WAAW,CAAC,CAAC;EAClD,GAAG,EAAEX,CAAC,IAAIG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,CAACK,QAAQ,CAAC,EAAE;AACrC,CAAC;AACDjB,OAAO,CAACE,OAAO,GAAGQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}