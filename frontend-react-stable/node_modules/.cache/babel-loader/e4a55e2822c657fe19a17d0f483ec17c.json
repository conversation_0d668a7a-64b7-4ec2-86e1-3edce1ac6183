{"ast": null, "code": "import _isEqual from \"lodash/isEqual\";\nimport _isNaN from \"lodash/isNaN\";\nimport _max from \"lodash/max\";\nimport _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nimport _isNil from \"lodash/isNil\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\", \"ref\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Area\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport Animate from 'react-smooth';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { isNumber, uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps } from '../util/ReactUtils';\nexport var Area = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Area, _PureComponent);\n  var _super = _createSuper(Area);\n  function Area() {\n    var _this;\n    _classCallCheck(this, Area);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: true\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-area-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Area, [{\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (isAnimationActive && !isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        dot = _this$props.dot,\n        points = _this$props.points,\n        dataKey = _this$props.dataKey;\n      var areaProps = filterProps(this.props);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, areaProps), customDotProps), {}, {\n          dataKey: dataKey,\n          cx: entry.x,\n          cy: entry.y,\n          index: i,\n          value: entry.value,\n          payload: entry.payload\n        });\n        return Area.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-area-dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderHorizontalRect\",\n    value: function renderHorizontalRect(alpha) {\n      var _this$props2 = this.props,\n        baseLine = _this$props2.baseLine,\n        points = _this$props2.points,\n        strokeWidth = _this$props2.strokeWidth;\n      var startX = points[0].x;\n      var endX = points[points.length - 1].x;\n      var width = alpha * Math.abs(startX - endX);\n      var maxY = _max(points.map(function (entry) {\n        return entry.y || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxY = Math.max(baseLine, maxY);\n      } else if (baseLine && _isArray(baseLine) && baseLine.length) {\n        maxY = Math.max(_max(baseLine.map(function (entry) {\n          return entry.y || 0;\n        })), maxY);\n      }\n      if (isNumber(maxY)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: startX < endX ? startX : startX - width,\n          y: 0,\n          width: width,\n          height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderVerticalRect\",\n    value: function renderVerticalRect(alpha) {\n      var _this$props3 = this.props,\n        baseLine = _this$props3.baseLine,\n        points = _this$props3.points,\n        strokeWidth = _this$props3.strokeWidth;\n      var startY = points[0].y;\n      var endY = points[points.length - 1].y;\n      var height = alpha * Math.abs(startY - endY);\n      var maxX = _max(points.map(function (entry) {\n        return entry.x || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxX = Math.max(baseLine, maxX);\n      } else if (baseLine && _isArray(baseLine) && baseLine.length) {\n        maxX = Math.max(_max(baseLine.map(function (entry) {\n          return entry.x || 0;\n        })), maxX);\n      }\n      if (isNumber(maxX)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: 0,\n          y: startY < endY ? startY : startY - height,\n          width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n          height: Math.floor(height)\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderClipRect\",\n    value: function renderClipRect(alpha) {\n      var layout = this.props.layout;\n      if (layout === 'vertical') {\n        return this.renderVerticalRect(alpha);\n      }\n      return this.renderHorizontalRect(alpha);\n    }\n  }, {\n    key: \"renderAreaStatically\",\n    value: function renderAreaStatically(points, baseLine, needClip, clipPathId) {\n      var _this$props4 = this.props,\n        layout = _this$props4.layout,\n        type = _this$props4.type,\n        stroke = _this$props4.stroke,\n        connectNulls = _this$props4.connectNulls,\n        isRange = _this$props4.isRange,\n        ref = _this$props4.ref,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      return /*#__PURE__*/React.createElement(Layer, {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n        points: points,\n        connectNulls: connectNulls,\n        type: type,\n        baseLine: baseLine,\n        layout: layout,\n        stroke: \"none\",\n        className: \"recharts-area-area\"\n      })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: points\n      })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: baseLine\n      })));\n    }\n  }, {\n    key: \"renderAreaWithAnimation\",\n    value: function renderAreaWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        baseLine = _this$props5.baseLine,\n        isAnimationActive = _this$props5.isAnimationActive,\n        animationBegin = _this$props5.animationBegin,\n        animationDuration = _this$props5.animationDuration,\n        animationEasing = _this$props5.animationEasing,\n        animationId = _this$props5.animationId;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        prevBaseLine = _this$state.prevBaseLine;\n      // const clipPathId = _.isNil(id) ? this.id : id;\n\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"area-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          // update animtaion\n          var stepPoints = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n            return entry;\n          });\n          var stepBaseLine;\n          if (isNumber(baseLine) && typeof baseLine === 'number') {\n            var interpolator = interpolateNumber(prevBaseLine, baseLine);\n            stepBaseLine = interpolator(t);\n          } else if (_isNil(baseLine) || _isNaN(baseLine)) {\n            var _interpolator = interpolateNumber(prevBaseLine, 0);\n            stepBaseLine = _interpolator(t);\n          } else {\n            stepBaseLine = baseLine.map(function (entry, index) {\n              var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n              if (prevBaseLine[prevPointIndex]) {\n                var prev = prevBaseLine[prevPointIndex];\n                var interpolatorX = interpolateNumber(prev.x, entry.x);\n                var interpolatorY = interpolateNumber(prev.y, entry.y);\n                return _objectSpread(_objectSpread({}, entry), {}, {\n                  x: interpolatorX(t),\n                  y: interpolatorY(t)\n                });\n              }\n              return entry;\n            });\n          }\n          return _this2.renderAreaStatically(stepPoints, stepBaseLine, needClip, clipPathId);\n        }\n        return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: \"animationClipPath-\".concat(clipPathId)\n        }, _this2.renderClipRect(t))), /*#__PURE__*/React.createElement(Layer, {\n          clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n        }, _this2.renderAreaStatically(points, baseLine, needClip, clipPathId)));\n      });\n    }\n  }, {\n    key: \"renderArea\",\n    value: function renderArea(needClip, clipPathId) {\n      var _this$props6 = this.props,\n        points = _this$props6.points,\n        baseLine = _this$props6.baseLine,\n        isAnimationActive = _this$props6.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        prevBaseLine = _this$state2.prevBaseLine,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !_isEqual(prevPoints, points) || !_isEqual(prevBaseLine, baseLine))) {\n        return this.renderAreaWithAnimation(needClip, clipPathId);\n      }\n      return this.renderAreaStatically(points, baseLine, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props7 = this.props,\n        hide = _this$props7.hide,\n        dot = _this$props7.dot,\n        points = _this$props7.points,\n        className = _this$props7.className,\n        top = _this$props7.top,\n        left = _this$props7.left,\n        xAxis = _this$props7.xAxis,\n        yAxis = _this$props7.yAxis,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        isAnimationActive = _this$props7.isAnimationActive,\n        id = _this$props7.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = classNames('recharts-area', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: Math.floor(height)\n      }))) : null, !hasSinglePoint ? this.renderArea(needClip, clipPathId) : null, (dot || hasSinglePoint) && this.renderDots(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine,\n          prevPoints: prevState.curPoints,\n          prevBaseLine: prevState.curBaseLine\n        };\n      }\n      if (nextProps.points !== prevState.curPoints || nextProps.baseLine !== prevState.curBaseLine) {\n        return {\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine\n        };\n      }\n      return null;\n    }\n  }]);\n  return Area;\n}(PureComponent);\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", {\n  stroke: '#3182bd',\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'line',\n  connectNulls: false,\n  // points of area\n  points: [],\n  dot: false,\n  activeDot: true,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n});\n_defineProperty(Area, \"getBaseValue\", function (props, item, xAxis, yAxis) {\n  var layout = props.layout,\n    chartBaseValue = props.baseValue;\n  var itemBaseValue = item.props.baseValue;\n\n  // The baseValue can be defined both on the AreaChart as well as on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue) && typeof baseValue === 'number') {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var max = Math.max(domain[0], domain[1]);\n    var min = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return min;\n    }\n    if (baseValue === 'dataMax') {\n      return max;\n    }\n    return max < 0 ? max : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n});\n_defineProperty(Area, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    bandSize = _ref2.bandSize,\n    dataKey = _ref2.dataKey,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var layout = props.layout;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = Area.getBaseValue(props, item, xAxis, yAxis);\n  var isRange = false;\n  var points = displayedData.map(function (entry, index) {\n    var originalValue = getValueByDataKey(entry, dataKey);\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = originalValue;\n      if (!_isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = _isNil(value[1]) || hasStack && _isNil(originalValue);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(function (entry) {\n      if (layout === 'horizontal') {\n        return {\n          x: entry.x,\n          y: !_isNil(_get(entry, 'value[0]')) && !_isNil(_get(entry, 'y')) ? yAxis.scale(_get(entry, 'value[0]')) : null\n        };\n      }\n      return {\n        x: !_isNil(_get(entry, 'value[0]')) ? xAxis.scale(_get(entry, 'value[0]')) : null,\n        y: entry.y\n      };\n    });\n  } else if (layout === 'horizontal') {\n    baseLine = yAxis.scale(baseValue);\n  } else {\n    baseLine = xAxis.scale(baseValue);\n  }\n  return _objectSpread({\n    points: points,\n    baseLine: baseLine,\n    layout: layout,\n    isRange: isRange\n  }, offset);\n});\n_defineProperty(Area, \"renderDotItem\", function (option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (_isFunction(option)) {\n    dotItem = option(props);\n  } else {\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: \"recharts-area-dot\"\n    }));\n  }\n  return dotItem;\n});", "map": {"version": 3, "names": ["_isEqual", "_isNaN", "_max", "_isFunction", "_get", "_isNil", "_isArray", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "_extends", "assign", "bind", "arguments", "hasOwnProperty", "apply", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "classNames", "Animate", "Curve", "Dot", "Layer", "LabelList", "Global", "isNumber", "uniqueId", "interpolateNumber", "getCateCoordinateOfLine", "getValueByDataKey", "filterProps", "Area", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "onAnimationEnd", "setState", "onAnimationStart", "renderDots", "needClip", "clipPathId", "isAnimationActive", "state", "_this$props", "dot", "points", "dataKey", "areaProps", "customDotProps", "dots", "map", "entry", "dotProps", "r", "cx", "x", "cy", "y", "index", "payload", "renderDotItem", "dotsProps", "clipPath", "createElement", "className", "renderHorizontalRect", "alpha", "_this$props2", "baseLine", "strokeWidth", "startX", "endX", "width", "Math", "abs", "maxY", "max", "height", "floor", "parseInt", "renderVerticalRect", "_this$props3", "startY", "endY", "maxX", "renderClipRect", "layout", "renderAreaStatically", "_this$props4", "type", "stroke", "connectNulls", "isRange", "ref", "others", "fill", "renderAreaWithAnimation", "_this2", "_this$props5", "animationBegin", "animationDuration", "animationEasing", "animationId", "_this$state", "prevPoints", "prevBaseLine", "begin", "duration", "isActive", "easing", "from", "t", "to", "handleAnimationEnd", "handleAnimationStart", "_ref", "prevPointsDiffFactor", "stepPoints", "prevPointIndex", "prev", "interpolatorX", "interpolatorY", "stepBaseLine", "interpolator", "_interpolator", "id", "renderArea", "_this$props6", "_this$state2", "totalLength", "render", "_this$props7", "hide", "top", "left", "xAxis", "yAxis", "hasSinglePoint", "layerClass", "allowDataOverflow", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "curBaseLine", "fillOpacity", "xAxisId", "yAxisId", "legendType", "activeDot", "isSsr", "item", "chartBaseValue", "baseValue", "itemBaseValue", "numericAxis", "domain", "scale", "min", "_ref2", "xAxisTicks", "yAxisTicks", "bandSize", "stackedData", "dataStartIndex", "displayedData", "offset", "hasStack", "getBaseValue", "originalValue", "isBreakPoint", "axis", "ticks", "option", "dotItem", "isValidElement", "cloneElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/Area.js"], "sourcesContent": ["import _isEqual from \"lodash/isEqual\";\nimport _isNaN from \"lodash/isNaN\";\nimport _max from \"lodash/max\";\nimport _isFunction from \"lodash/isFunction\";\nimport _get from \"lodash/get\";\nimport _isNil from \"lodash/isNil\";\nimport _isArray from \"lodash/isArray\";\nvar _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\", \"ref\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Area\n */\nimport React, { PureComponent } from 'react';\nimport classNames from 'classnames';\nimport Animate from 'react-smooth';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { isNumber, uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps } from '../util/ReactUtils';\nexport var Area = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Area, _PureComponent);\n  var _super = _createSuper(Area);\n  function Area() {\n    var _this;\n    _classCallCheck(this, Area);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isAnimationFinished: true\n    });\n    _defineProperty(_assertThisInitialized(_this), \"id\", uniqueId('recharts-area-'));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Area, [{\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      if (isAnimationActive && !isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        dot = _this$props.dot,\n        points = _this$props.points,\n        dataKey = _this$props.dataKey;\n      var areaProps = filterProps(this.props);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, areaProps), customDotProps), {}, {\n          dataKey: dataKey,\n          cx: entry.x,\n          cy: entry.y,\n          index: i,\n          value: entry.value,\n          payload: entry.payload\n        });\n        return Area.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-area-dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderHorizontalRect\",\n    value: function renderHorizontalRect(alpha) {\n      var _this$props2 = this.props,\n        baseLine = _this$props2.baseLine,\n        points = _this$props2.points,\n        strokeWidth = _this$props2.strokeWidth;\n      var startX = points[0].x;\n      var endX = points[points.length - 1].x;\n      var width = alpha * Math.abs(startX - endX);\n      var maxY = _max(points.map(function (entry) {\n        return entry.y || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxY = Math.max(baseLine, maxY);\n      } else if (baseLine && _isArray(baseLine) && baseLine.length) {\n        maxY = Math.max(_max(baseLine.map(function (entry) {\n          return entry.y || 0;\n        })), maxY);\n      }\n      if (isNumber(maxY)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: startX < endX ? startX : startX - width,\n          y: 0,\n          width: width,\n          height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderVerticalRect\",\n    value: function renderVerticalRect(alpha) {\n      var _this$props3 = this.props,\n        baseLine = _this$props3.baseLine,\n        points = _this$props3.points,\n        strokeWidth = _this$props3.strokeWidth;\n      var startY = points[0].y;\n      var endY = points[points.length - 1].y;\n      var height = alpha * Math.abs(startY - endY);\n      var maxX = _max(points.map(function (entry) {\n        return entry.x || 0;\n      }));\n      if (isNumber(baseLine) && typeof baseLine === 'number') {\n        maxX = Math.max(baseLine, maxX);\n      } else if (baseLine && _isArray(baseLine) && baseLine.length) {\n        maxX = Math.max(_max(baseLine.map(function (entry) {\n          return entry.x || 0;\n        })), maxX);\n      }\n      if (isNumber(maxX)) {\n        return /*#__PURE__*/React.createElement(\"rect\", {\n          x: 0,\n          y: startY < endY ? startY : startY - height,\n          width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n          height: Math.floor(height)\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderClipRect\",\n    value: function renderClipRect(alpha) {\n      var layout = this.props.layout;\n      if (layout === 'vertical') {\n        return this.renderVerticalRect(alpha);\n      }\n      return this.renderHorizontalRect(alpha);\n    }\n  }, {\n    key: \"renderAreaStatically\",\n    value: function renderAreaStatically(points, baseLine, needClip, clipPathId) {\n      var _this$props4 = this.props,\n        layout = _this$props4.layout,\n        type = _this$props4.type,\n        stroke = _this$props4.stroke,\n        connectNulls = _this$props4.connectNulls,\n        isRange = _this$props4.isRange,\n        ref = _this$props4.ref,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      return /*#__PURE__*/React.createElement(Layer, {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n        points: points,\n        connectNulls: connectNulls,\n        type: type,\n        baseLine: baseLine,\n        layout: layout,\n        stroke: \"none\",\n        className: \"recharts-area-area\"\n      })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: points\n      })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(this.props), {\n        className: \"recharts-area-curve\",\n        layout: layout,\n        type: type,\n        connectNulls: connectNulls,\n        fill: \"none\",\n        points: baseLine\n      })));\n    }\n  }, {\n    key: \"renderAreaWithAnimation\",\n    value: function renderAreaWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        baseLine = _this$props5.baseLine,\n        isAnimationActive = _this$props5.isAnimationActive,\n        animationBegin = _this$props5.animationBegin,\n        animationDuration = _this$props5.animationDuration,\n        animationEasing = _this$props5.animationEasing,\n        animationId = _this$props5.animationId;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        prevBaseLine = _this$state.prevBaseLine;\n      // const clipPathId = _.isNil(id) ? this.id : id;\n\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"area-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          // update animtaion\n          var stepPoints = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n            return entry;\n          });\n          var stepBaseLine;\n          if (isNumber(baseLine) && typeof baseLine === 'number') {\n            var interpolator = interpolateNumber(prevBaseLine, baseLine);\n            stepBaseLine = interpolator(t);\n          } else if (_isNil(baseLine) || _isNaN(baseLine)) {\n            var _interpolator = interpolateNumber(prevBaseLine, 0);\n            stepBaseLine = _interpolator(t);\n          } else {\n            stepBaseLine = baseLine.map(function (entry, index) {\n              var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n              if (prevBaseLine[prevPointIndex]) {\n                var prev = prevBaseLine[prevPointIndex];\n                var interpolatorX = interpolateNumber(prev.x, entry.x);\n                var interpolatorY = interpolateNumber(prev.y, entry.y);\n                return _objectSpread(_objectSpread({}, entry), {}, {\n                  x: interpolatorX(t),\n                  y: interpolatorY(t)\n                });\n              }\n              return entry;\n            });\n          }\n          return _this2.renderAreaStatically(stepPoints, stepBaseLine, needClip, clipPathId);\n        }\n        return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n          id: \"animationClipPath-\".concat(clipPathId)\n        }, _this2.renderClipRect(t))), /*#__PURE__*/React.createElement(Layer, {\n          clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n        }, _this2.renderAreaStatically(points, baseLine, needClip, clipPathId)));\n      });\n    }\n  }, {\n    key: \"renderArea\",\n    value: function renderArea(needClip, clipPathId) {\n      var _this$props6 = this.props,\n        points = _this$props6.points,\n        baseLine = _this$props6.baseLine,\n        isAnimationActive = _this$props6.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        prevBaseLine = _this$state2.prevBaseLine,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !_isEqual(prevPoints, points) || !_isEqual(prevBaseLine, baseLine))) {\n        return this.renderAreaWithAnimation(needClip, clipPathId);\n      }\n      return this.renderAreaStatically(points, baseLine, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props7 = this.props,\n        hide = _this$props7.hide,\n        dot = _this$props7.dot,\n        points = _this$props7.points,\n        className = _this$props7.className,\n        top = _this$props7.top,\n        left = _this$props7.left,\n        xAxis = _this$props7.xAxis,\n        yAxis = _this$props7.yAxis,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        isAnimationActive = _this$props7.isAnimationActive,\n        id = _this$props7.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = classNames('recharts-area', className);\n      var needClip = xAxis && xAxis.allowDataOverflow || yAxis && yAxis.allowDataOverflow;\n      var clipPathId = _isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClip ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left,\n        y: top,\n        width: width,\n        height: Math.floor(height)\n      }))) : null, !hasSinglePoint ? this.renderArea(needClip, clipPathId) : null, (dot || hasSinglePoint) && this.renderDots(needClip, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine,\n          prevPoints: prevState.curPoints,\n          prevBaseLine: prevState.curBaseLine\n        };\n      }\n      if (nextProps.points !== prevState.curPoints || nextProps.baseLine !== prevState.curBaseLine) {\n        return {\n          curPoints: nextProps.points,\n          curBaseLine: nextProps.baseLine\n        };\n      }\n      return null;\n    }\n  }]);\n  return Area;\n}(PureComponent);\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", {\n  stroke: '#3182bd',\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  xAxisId: 0,\n  yAxisId: 0,\n  legendType: 'line',\n  connectNulls: false,\n  // points of area\n  points: [],\n  dot: false,\n  activeDot: true,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n});\n_defineProperty(Area, \"getBaseValue\", function (props, item, xAxis, yAxis) {\n  var layout = props.layout,\n    chartBaseValue = props.baseValue;\n  var itemBaseValue = item.props.baseValue;\n\n  // The baseValue can be defined both on the AreaChart as well as on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue) && typeof baseValue === 'number') {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var max = Math.max(domain[0], domain[1]);\n    var min = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return min;\n    }\n    if (baseValue === 'dataMax') {\n      return max;\n    }\n    return max < 0 ? max : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n});\n_defineProperty(Area, \"getComposedData\", function (_ref2) {\n  var props = _ref2.props,\n    item = _ref2.item,\n    xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    bandSize = _ref2.bandSize,\n    dataKey = _ref2.dataKey,\n    stackedData = _ref2.stackedData,\n    dataStartIndex = _ref2.dataStartIndex,\n    displayedData = _ref2.displayedData,\n    offset = _ref2.offset;\n  var layout = props.layout;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = Area.getBaseValue(props, item, xAxis, yAxis);\n  var isRange = false;\n  var points = displayedData.map(function (entry, index) {\n    var originalValue = getValueByDataKey(entry, dataKey);\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = originalValue;\n      if (!_isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = _isNil(value[1]) || hasStack && _isNil(originalValue);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(function (entry) {\n      if (layout === 'horizontal') {\n        return {\n          x: entry.x,\n          y: !_isNil(_get(entry, 'value[0]')) && !_isNil(_get(entry, 'y')) ? yAxis.scale(_get(entry, 'value[0]')) : null\n        };\n      }\n      return {\n        x: !_isNil(_get(entry, 'value[0]')) ? xAxis.scale(_get(entry, 'value[0]')) : null,\n        y: entry.y\n      };\n    });\n  } else if (layout === 'horizontal') {\n    baseLine = yAxis.scale(baseValue);\n  } else {\n    baseLine = xAxis.scale(baseValue);\n  }\n  return _objectSpread({\n    points: points,\n    baseLine: baseLine,\n    layout: layout,\n    isRange: isRange\n  }, offset);\n});\n_defineProperty(Area, \"renderDotItem\", function (option, props) {\n  var dotItem;\n  if ( /*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (_isFunction(option)) {\n    dotItem = option(props);\n  } else {\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: \"recharts-area-dot\"\n    }));\n  }\n  return dotItem;\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,IAAIC,SAAS,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC;AAC9E,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT,SAASa,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGT,MAAM,CAACU,MAAM,GAAGV,MAAM,CAACU,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUf,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGkB,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACqB,cAAc,CAACP,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOa,QAAQ,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AAAE;AAClV,SAASG,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIT,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACQ,MAAM,CAAC;EAAE,IAAIhB,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIiB,OAAO,GAAGlB,MAAM,CAACC,qBAAqB,CAACe,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOpB,MAAM,CAACqB,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEd,IAAI,CAACe,IAAI,CAACT,KAAK,CAACN,IAAI,EAAEU,OAAO,CAAC;EAAE;EAAE,OAAOV,IAAI;AAAE;AACpV,SAASgB,aAAaA,CAAC5B,MAAM,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACT,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIL,MAAM,GAAG,IAAI,IAAIkB,SAAS,CAACb,CAAC,CAAC,GAAGa,SAAS,CAACb,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAE4B,eAAe,CAAC9B,MAAM,EAAEE,GAAG,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAAC2B,yBAAyB,GAAG3B,MAAM,CAAC4B,gBAAgB,CAAChC,MAAM,EAAEI,MAAM,CAAC2B,yBAAyB,CAACjC,MAAM,CAAC,CAAC,GAAGqB,OAAO,CAACf,MAAM,CAACN,MAAM,CAAC,CAAC,CAAC+B,OAAO,CAAC,UAAU3B,GAAG,EAAE;MAAEE,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAEE,GAAG,EAAEE,MAAM,CAACqB,wBAAwB,CAAC3B,MAAM,EAAEI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AACzf,SAASkC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACtC,MAAM,EAAEuC,KAAK,EAAE;EAAE,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAChC,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIqC,UAAU,GAAGD,KAAK,CAACpC,CAAC,CAAC;IAAEqC,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEtC,MAAM,CAAC6B,cAAc,CAACjC,MAAM,EAAE2C,cAAc,CAACH,UAAU,CAACtC,GAAG,CAAC,EAAEsC,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAACxC,SAAS,EAAEiD,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAE1C,MAAM,CAAC6B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAACpD,SAAS,GAAGQ,MAAM,CAAC8C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACrD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEwD,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAErC,MAAM,CAAC6B,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGhD,MAAM,CAACmD,cAAc,GAAGnD,MAAM,CAACmD,cAAc,CAACxC,IAAI,CAAC,CAAC,GAAG,SAASqC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACpE,WAAW;MAAEqE,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE9C,SAAS,EAAEiD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC5C,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAAE;IAAE,OAAOoD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAE3D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI2B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAAC9E,SAAS,CAAC+E,OAAO,CAACjE,IAAI,CAACwD,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAG3D,MAAM,CAACmD,cAAc,GAAGnD,MAAM,CAACyE,cAAc,CAAC9D,IAAI,CAAC,CAAC,GAAG,SAASgD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAIpD,MAAM,CAACyE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAACtC,GAAG,EAAEU,GAAG,EAAEiD,KAAK,EAAE;EAAEjD,GAAG,GAAGyC,cAAc,CAACzC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIV,GAAG,EAAE;IAAEY,MAAM,CAAC6B,cAAc,CAACzC,GAAG,EAAEU,GAAG,EAAE;MAAEiD,KAAK,EAAEA,KAAK;MAAEzB,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAElD,GAAG,CAACU,GAAG,CAAC,GAAGiD,KAAK;EAAE;EAAE,OAAO3D,GAAG;AAAE;AAC3O,SAASmD,cAAcA,CAACmC,GAAG,EAAE;EAAE,IAAI5E,GAAG,GAAG6E,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOvF,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAG8E,MAAM,CAAC9E,GAAG,CAAC;AAAE;AAC5H,SAAS6E,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI3F,OAAO,CAAC0F,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACxF,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACzE,IAAI,CAACuE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI3F,OAAO,CAAC+F,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIjD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC6C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,cAAc;AAClC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AACzE,SAASC,uBAAuB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC/E,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,IAAIC,IAAI,GAAG,aAAa,UAAUC,cAAc,EAAE;EACvDzD,SAAS,CAACwD,IAAI,EAAEC,cAAc,CAAC;EAC/B,IAAIC,MAAM,GAAGhD,YAAY,CAAC8C,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTxE,eAAe,CAAC,IAAI,EAAEqE,IAAI,CAAC;IAC3B,KAAK,IAAII,IAAI,GAAG3F,SAAS,CAACT,MAAM,EAAEqG,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAG9F,SAAS,CAAC8F,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAAC/F,IAAI,CAACQ,KAAK,CAACuF,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtD9E,eAAe,CAACwC,sBAAsB,CAACoC,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACFlF,eAAe,CAACwC,sBAAsB,CAACoC,KAAK,CAAC,EAAE,IAAI,EAAER,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAChFpE,eAAe,CAACwC,sBAAsB,CAACoC,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIO,cAAc,GAAGP,KAAK,CAACnE,KAAK,CAAC0E,cAAc;MAC/CP,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI9H,WAAW,CAAC+H,cAAc,CAAC,EAAE;QAC/BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFnF,eAAe,CAACwC,sBAAsB,CAACoC,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjF,IAAIS,gBAAgB,GAAGT,KAAK,CAACnE,KAAK,CAAC4E,gBAAgB;MACnDT,KAAK,CAACQ,QAAQ,CAAC;QACbF,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI9H,WAAW,CAACiI,gBAAgB,CAAC,EAAE;QACjCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOT,KAAK;EACd;EACA9D,YAAY,CAAC2D,IAAI,EAAE,CAAC;IAClBrG,GAAG,EAAE,YAAY;IACjBiD,KAAK,EAAE,SAASiE,UAAUA,CAACC,QAAQ,EAAEC,UAAU,EAAE;MAC/C,IAAIC,iBAAiB,GAAG,IAAI,CAAChF,KAAK,CAACgF,iBAAiB;MACpD,IAAIP,mBAAmB,GAAG,IAAI,CAACQ,KAAK,CAACR,mBAAmB;MACxD,IAAIO,iBAAiB,IAAI,CAACP,mBAAmB,EAAE;QAC7C,OAAO,IAAI;MACb;MACA,IAAIS,WAAW,GAAG,IAAI,CAAClF,KAAK;QAC1BmF,GAAG,GAAGD,WAAW,CAACC,GAAG;QACrBC,MAAM,GAAGF,WAAW,CAACE,MAAM;QAC3BC,OAAO,GAAGH,WAAW,CAACG,OAAO;MAC/B,IAAIC,SAAS,GAAGvB,WAAW,CAAC,IAAI,CAAC/D,KAAK,CAAC;MACvC,IAAIuF,cAAc,GAAGxB,WAAW,CAACoB,GAAG,EAAE,IAAI,CAAC;MAC3C,IAAIK,IAAI,GAAGJ,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE9H,CAAC,EAAE;QACxC,IAAI+H,QAAQ,GAAGtG,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACvD1B,GAAG,EAAE,MAAM,CAAC6G,MAAM,CAAC5G,CAAC,CAAC;UACrBgI,CAAC,EAAE;QACL,CAAC,EAAEN,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UAClCF,OAAO,EAAEA,OAAO;UAChBQ,EAAE,EAAEH,KAAK,CAACI,CAAC;UACXC,EAAE,EAAEL,KAAK,CAACM,CAAC;UACXC,KAAK,EAAErI,CAAC;UACRgD,KAAK,EAAE8E,KAAK,CAAC9E,KAAK;UAClBsF,OAAO,EAAER,KAAK,CAACQ;QACjB,CAAC,CAAC;QACF,OAAOlC,IAAI,CAACmC,aAAa,CAAChB,GAAG,EAAEQ,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF,IAAIS,SAAS,GAAG;QACdC,QAAQ,EAAEvB,QAAQ,GAAG,gBAAgB,CAACN,MAAM,CAACO,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAa9B,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAEjF,QAAQ,CAAC;QACtDiI,SAAS,EAAE;MACb,CAAC,EAAEH,SAAS,CAAC,EAAEZ,IAAI,CAAC;IACtB;EACF,CAAC,EAAE;IACD7H,GAAG,EAAE,sBAAsB;IAC3BiD,KAAK,EAAE,SAAS4F,oBAAoBA,CAACC,KAAK,EAAE;MAC1C,IAAIC,YAAY,GAAG,IAAI,CAAC1G,KAAK;QAC3B2G,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCvB,MAAM,GAAGsB,YAAY,CAACtB,MAAM;QAC5BwB,WAAW,GAAGF,YAAY,CAACE,WAAW;MACxC,IAAIC,MAAM,GAAGzB,MAAM,CAAC,CAAC,CAAC,CAACU,CAAC;MACxB,IAAIgB,IAAI,GAAG1B,MAAM,CAACA,MAAM,CAACpH,MAAM,GAAG,CAAC,CAAC,CAAC8H,CAAC;MACtC,IAAIiB,KAAK,GAAGN,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACJ,MAAM,GAAGC,IAAI,CAAC;MAC3C,IAAII,IAAI,GAAGxK,IAAI,CAAC0I,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC1C,OAAOA,KAAK,CAACM,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAItC,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACtDO,IAAI,GAAGF,IAAI,CAACG,GAAG,CAACR,QAAQ,EAAEO,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIP,QAAQ,IAAI7J,QAAQ,CAAC6J,QAAQ,CAAC,IAAIA,QAAQ,CAAC3I,MAAM,EAAE;QAC5DkJ,IAAI,GAAGF,IAAI,CAACG,GAAG,CAACzK,IAAI,CAACiK,QAAQ,CAAClB,GAAG,CAAC,UAAUC,KAAK,EAAE;UACjD,OAAOA,KAAK,CAACM,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,EAAEkB,IAAI,CAAC;MACZ;MACA,IAAIxD,QAAQ,CAACwD,IAAI,CAAC,EAAE;QAClB,OAAO,aAAajE,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;UAC9CR,CAAC,EAAEe,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGE,KAAK;UAC1Cf,CAAC,EAAE,CAAC;UACJe,KAAK,EAAEA,KAAK;UACZK,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAACH,IAAI,IAAIN,WAAW,GAAGU,QAAQ,CAAC,EAAE,CAAC9C,MAAM,CAACoC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QACpF,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjJ,GAAG,EAAE,oBAAoB;IACzBiD,KAAK,EAAE,SAAS2G,kBAAkBA,CAACd,KAAK,EAAE;MACxC,IAAIe,YAAY,GAAG,IAAI,CAACxH,KAAK;QAC3B2G,QAAQ,GAAGa,YAAY,CAACb,QAAQ;QAChCvB,MAAM,GAAGoC,YAAY,CAACpC,MAAM;QAC5BwB,WAAW,GAAGY,YAAY,CAACZ,WAAW;MACxC,IAAIa,MAAM,GAAGrC,MAAM,CAAC,CAAC,CAAC,CAACY,CAAC;MACxB,IAAI0B,IAAI,GAAGtC,MAAM,CAACA,MAAM,CAACpH,MAAM,GAAG,CAAC,CAAC,CAACgI,CAAC;MACtC,IAAIoB,MAAM,GAAGX,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACQ,MAAM,GAAGC,IAAI,CAAC;MAC5C,IAAIC,IAAI,GAAGjL,IAAI,CAAC0I,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC1C,OAAOA,KAAK,CAACI,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,CAAC;MACH,IAAIpC,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QACtDgB,IAAI,GAAGX,IAAI,CAACG,GAAG,CAACR,QAAQ,EAAEgB,IAAI,CAAC;MACjC,CAAC,MAAM,IAAIhB,QAAQ,IAAI7J,QAAQ,CAAC6J,QAAQ,CAAC,IAAIA,QAAQ,CAAC3I,MAAM,EAAE;QAC5D2J,IAAI,GAAGX,IAAI,CAACG,GAAG,CAACzK,IAAI,CAACiK,QAAQ,CAAClB,GAAG,CAAC,UAAUC,KAAK,EAAE;UACjD,OAAOA,KAAK,CAACI,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC;MACZ;MACA,IAAIjE,QAAQ,CAACiE,IAAI,CAAC,EAAE;QAClB,OAAO,aAAa1E,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;UAC9CR,CAAC,EAAE,CAAC;UACJE,CAAC,EAAEyB,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGL,MAAM;UAC3CL,KAAK,EAAEY,IAAI,IAAIf,WAAW,GAAGU,QAAQ,CAAC,EAAE,CAAC9C,MAAM,CAACoC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;UACtEQ,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAACD,MAAM;QAC3B,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDzJ,GAAG,EAAE,gBAAgB;IACrBiD,KAAK,EAAE,SAASgH,cAAcA,CAACnB,KAAK,EAAE;MACpC,IAAIoB,MAAM,GAAG,IAAI,CAAC7H,KAAK,CAAC6H,MAAM;MAC9B,IAAIA,MAAM,KAAK,UAAU,EAAE;QACzB,OAAO,IAAI,CAACN,kBAAkB,CAACd,KAAK,CAAC;MACvC;MACA,OAAO,IAAI,CAACD,oBAAoB,CAACC,KAAK,CAAC;IACzC;EACF,CAAC,EAAE;IACD9I,GAAG,EAAE,sBAAsB;IAC3BiD,KAAK,EAAE,SAASkH,oBAAoBA,CAAC1C,MAAM,EAAEuB,QAAQ,EAAE7B,QAAQ,EAAEC,UAAU,EAAE;MAC3E,IAAIgD,YAAY,GAAG,IAAI,CAAC/H,KAAK;QAC3B6H,MAAM,GAAGE,YAAY,CAACF,MAAM;QAC5BG,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5BC,YAAY,GAAGH,YAAY,CAACG,YAAY;QACxCC,OAAO,GAAGJ,YAAY,CAACI,OAAO;QAC9BC,GAAG,GAAGL,YAAY,CAACK,GAAG;QACtBC,MAAM,GAAG/K,wBAAwB,CAACyK,YAAY,EAAEhL,SAAS,CAAC;MAC5D,OAAO,aAAakG,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE;QAC7C8C,QAAQ,EAAEvB,QAAQ,GAAG,gBAAgB,CAACN,MAAM,CAACO,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAE,aAAa9B,KAAK,CAACqD,aAAa,CAACjD,KAAK,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEyF,WAAW,CAACsE,MAAM,EAAE,IAAI,CAAC,EAAE;QACjFjD,MAAM,EAAEA,MAAM;QACd8C,YAAY,EAAEA,YAAY;QAC1BF,IAAI,EAAEA,IAAI;QACVrB,QAAQ,EAAEA,QAAQ;QAClBkB,MAAM,EAAEA,MAAM;QACdI,MAAM,EAAE,MAAM;QACd1B,SAAS,EAAE;MACb,CAAC,CAAC,CAAC,EAAE0B,MAAM,KAAK,MAAM,IAAI,aAAahF,KAAK,CAACqD,aAAa,CAACjD,KAAK,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEyF,WAAW,CAAC,IAAI,CAAC/D,KAAK,CAAC,EAAE;QACtGuG,SAAS,EAAE,qBAAqB;QAChCsB,MAAM,EAAEA,MAAM;QACdG,IAAI,EAAEA,IAAI;QACVE,YAAY,EAAEA,YAAY;QAC1BI,IAAI,EAAE,MAAM;QACZlD,MAAM,EAAEA;MACV,CAAC,CAAC,CAAC,EAAE6C,MAAM,KAAK,MAAM,IAAIE,OAAO,IAAI,aAAalF,KAAK,CAACqD,aAAa,CAACjD,KAAK,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEyF,WAAW,CAAC,IAAI,CAAC/D,KAAK,CAAC,EAAE;QACjHuG,SAAS,EAAE,qBAAqB;QAChCsB,MAAM,EAAEA,MAAM;QACdG,IAAI,EAAEA,IAAI;QACVE,YAAY,EAAEA,YAAY;QAC1BI,IAAI,EAAE,MAAM;QACZlD,MAAM,EAAEuB;MACV,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,EAAE;IACDhJ,GAAG,EAAE,yBAAyB;IAC9BiD,KAAK,EAAE,SAAS2H,uBAAuBA,CAACzD,QAAQ,EAAEC,UAAU,EAAE;MAC5D,IAAIyD,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACzI,KAAK;QAC3BoF,MAAM,GAAGqD,YAAY,CAACrD,MAAM;QAC5BuB,QAAQ,GAAG8B,YAAY,CAAC9B,QAAQ;QAChC3B,iBAAiB,GAAGyD,YAAY,CAACzD,iBAAiB;QAClD0D,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,eAAe,GAAGH,YAAY,CAACG,eAAe;QAC9CC,WAAW,GAAGJ,YAAY,CAACI,WAAW;MACxC,IAAIC,WAAW,GAAG,IAAI,CAAC7D,KAAK;QAC1B8D,UAAU,GAAGD,WAAW,CAACC,UAAU;QACnCC,YAAY,GAAGF,WAAW,CAACE,YAAY;MACzC;;MAEA,OAAO,aAAa/F,KAAK,CAACqD,aAAa,CAAClD,OAAO,EAAE;QAC/C6F,KAAK,EAAEP,cAAc;QACrBQ,QAAQ,EAAEP,iBAAiB;QAC3BQ,QAAQ,EAAEnE,iBAAiB;QAC3BoE,MAAM,EAAER,eAAe;QACvBS,IAAI,EAAE;UACJC,CAAC,EAAE;QACL,CAAC;QACDC,EAAE,EAAE;UACFD,CAAC,EAAE;QACL,CAAC;QACD3L,GAAG,EAAE,OAAO,CAAC6G,MAAM,CAACqE,WAAW,CAAC;QAChCnE,cAAc,EAAE,IAAI,CAAC8E,kBAAkB;QACvC5E,gBAAgB,EAAE,IAAI,CAAC6E;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIJ,CAAC,GAAGI,IAAI,CAACJ,CAAC;QACd,IAAIP,UAAU,EAAE;UACd,IAAIY,oBAAoB,GAAGZ,UAAU,CAAC/K,MAAM,GAAGoH,MAAM,CAACpH,MAAM;UAC5D;UACA,IAAI4L,UAAU,GAAGxE,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAEO,KAAK,EAAE;YAClD,IAAI4D,cAAc,GAAG7C,IAAI,CAACK,KAAK,CAACpB,KAAK,GAAG0D,oBAAoB,CAAC;YAC7D,IAAIZ,UAAU,CAACc,cAAc,CAAC,EAAE;cAC9B,IAAIC,IAAI,GAAGf,UAAU,CAACc,cAAc,CAAC;cACrC,IAAIE,aAAa,GAAGnG,iBAAiB,CAACkG,IAAI,CAAChE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC;cACtD,IAAIkE,aAAa,GAAGpG,iBAAiB,CAACkG,IAAI,CAAC9D,CAAC,EAAEN,KAAK,CAACM,CAAC,CAAC;cACtD,OAAO3G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDI,CAAC,EAAEiE,aAAa,CAACT,CAAC,CAAC;gBACnBtD,CAAC,EAAEgE,aAAa,CAACV,CAAC;cACpB,CAAC,CAAC;YACJ;YACA,OAAO5D,KAAK;UACd,CAAC,CAAC;UACF,IAAIuE,YAAY;UAChB,IAAIvG,QAAQ,CAACiD,QAAQ,CAAC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;YACtD,IAAIuD,YAAY,GAAGtG,iBAAiB,CAACoF,YAAY,EAAErC,QAAQ,CAAC;YAC5DsD,YAAY,GAAGC,YAAY,CAACZ,CAAC,CAAC;UAChC,CAAC,MAAM,IAAIzM,MAAM,CAAC8J,QAAQ,CAAC,IAAIlK,MAAM,CAACkK,QAAQ,CAAC,EAAE;YAC/C,IAAIwD,aAAa,GAAGvG,iBAAiB,CAACoF,YAAY,EAAE,CAAC,CAAC;YACtDiB,YAAY,GAAGE,aAAa,CAACb,CAAC,CAAC;UACjC,CAAC,MAAM;YACLW,YAAY,GAAGtD,QAAQ,CAAClB,GAAG,CAAC,UAAUC,KAAK,EAAEO,KAAK,EAAE;cAClD,IAAI4D,cAAc,GAAG7C,IAAI,CAACK,KAAK,CAACpB,KAAK,GAAG0D,oBAAoB,CAAC;cAC7D,IAAIX,YAAY,CAACa,cAAc,CAAC,EAAE;gBAChC,IAAIC,IAAI,GAAGd,YAAY,CAACa,cAAc,CAAC;gBACvC,IAAIE,aAAa,GAAGnG,iBAAiB,CAACkG,IAAI,CAAChE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC;gBACtD,IAAIkE,aAAa,GAAGpG,iBAAiB,CAACkG,IAAI,CAAC9D,CAAC,EAAEN,KAAK,CAACM,CAAC,CAAC;gBACtD,OAAO3G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;kBACjDI,CAAC,EAAEiE,aAAa,CAACT,CAAC,CAAC;kBACnBtD,CAAC,EAAEgE,aAAa,CAACV,CAAC;gBACpB,CAAC,CAAC;cACJ;cACA,OAAO5D,KAAK;YACd,CAAC,CAAC;UACJ;UACA,OAAO8C,MAAM,CAACV,oBAAoB,CAAC8B,UAAU,EAAEK,YAAY,EAAEnF,QAAQ,EAAEC,UAAU,CAAC;QACpF;QACA,OAAO,aAAa9B,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE,IAAI,EAAE,aAAaN,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarD,KAAK,CAACqD,aAAa,CAAC,UAAU,EAAE;UAC/I8D,EAAE,EAAE,oBAAoB,CAAC5F,MAAM,CAACO,UAAU;QAC5C,CAAC,EAAEyD,MAAM,CAACZ,cAAc,CAAC0B,CAAC,CAAC,CAAC,CAAC,EAAE,aAAarG,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE;UACrE8C,QAAQ,EAAE,yBAAyB,CAAC7B,MAAM,CAACO,UAAU,EAAE,GAAG;QAC5D,CAAC,EAAEyD,MAAM,CAACV,oBAAoB,CAAC1C,MAAM,EAAEuB,QAAQ,EAAE7B,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,YAAY;IACjBiD,KAAK,EAAE,SAASyJ,UAAUA,CAACvF,QAAQ,EAAEC,UAAU,EAAE;MAC/C,IAAIuF,YAAY,GAAG,IAAI,CAACtK,KAAK;QAC3BoF,MAAM,GAAGkF,YAAY,CAAClF,MAAM;QAC5BuB,QAAQ,GAAG2D,YAAY,CAAC3D,QAAQ;QAChC3B,iBAAiB,GAAGsF,YAAY,CAACtF,iBAAiB;MACpD,IAAIuF,YAAY,GAAG,IAAI,CAACtF,KAAK;QAC3B8D,UAAU,GAAGwB,YAAY,CAACxB,UAAU;QACpCC,YAAY,GAAGuB,YAAY,CAACvB,YAAY;QACxCwB,WAAW,GAAGD,YAAY,CAACC,WAAW;MACxC,IAAIxF,iBAAiB,IAAII,MAAM,IAAIA,MAAM,CAACpH,MAAM,KAAK,CAAC+K,UAAU,IAAIyB,WAAW,GAAG,CAAC,IAAI,CAAChO,QAAQ,CAACuM,UAAU,EAAE3D,MAAM,CAAC,IAAI,CAAC5I,QAAQ,CAACwM,YAAY,EAAErC,QAAQ,CAAC,CAAC,EAAE;QAC1J,OAAO,IAAI,CAAC4B,uBAAuB,CAACzD,QAAQ,EAAEC,UAAU,CAAC;MAC3D;MACA,OAAO,IAAI,CAAC+C,oBAAoB,CAAC1C,MAAM,EAAEuB,QAAQ,EAAE7B,QAAQ,EAAEC,UAAU,CAAC;IAC1E;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,QAAQ;IACbiD,KAAK,EAAE,SAAS6J,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC1K,KAAK;QAC3B2K,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBxF,GAAG,GAAGuF,YAAY,CAACvF,GAAG;QACtBC,MAAM,GAAGsF,YAAY,CAACtF,MAAM;QAC5BmB,SAAS,GAAGmE,YAAY,CAACnE,SAAS;QAClCqE,GAAG,GAAGF,YAAY,CAACE,GAAG;QACtBC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBC,KAAK,GAAGJ,YAAY,CAACI,KAAK;QAC1BC,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1BhE,KAAK,GAAG2D,YAAY,CAAC3D,KAAK;QAC1BK,MAAM,GAAGsD,YAAY,CAACtD,MAAM;QAC5BpC,iBAAiB,GAAG0F,YAAY,CAAC1F,iBAAiB;QAClDoF,EAAE,GAAGM,YAAY,CAACN,EAAE;MACtB,IAAIO,IAAI,IAAI,CAACvF,MAAM,IAAI,CAACA,MAAM,CAACpH,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAIyG,mBAAmB,GAAG,IAAI,CAACQ,KAAK,CAACR,mBAAmB;MACxD,IAAIuG,cAAc,GAAG5F,MAAM,CAACpH,MAAM,KAAK,CAAC;MACxC,IAAIiN,UAAU,GAAG9H,UAAU,CAAC,eAAe,EAAEoD,SAAS,CAAC;MACvD,IAAIzB,QAAQ,GAAGgG,KAAK,IAAIA,KAAK,CAACI,iBAAiB,IAAIH,KAAK,IAAIA,KAAK,CAACG,iBAAiB;MACnF,IAAInG,UAAU,GAAGlI,MAAM,CAACuN,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MAC1C,OAAO,aAAanH,KAAK,CAACqD,aAAa,CAAC/C,KAAK,EAAE;QAC7CgD,SAAS,EAAE0E;MACb,CAAC,EAAEnG,QAAQ,GAAG,aAAa7B,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarD,KAAK,CAACqD,aAAa,CAAC,UAAU,EAAE;QACxG8D,EAAE,EAAE,WAAW,CAAC5F,MAAM,CAACO,UAAU;MACnC,CAAC,EAAE,aAAa9B,KAAK,CAACqD,aAAa,CAAC,MAAM,EAAE;QAC1CR,CAAC,EAAE+E,IAAI;QACP7E,CAAC,EAAE4E,GAAG;QACN7D,KAAK,EAAEA,KAAK;QACZK,MAAM,EAAEJ,IAAI,CAACK,KAAK,CAACD,MAAM;MAC3B,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC4D,cAAc,GAAG,IAAI,CAACX,UAAU,CAACvF,QAAQ,EAAEC,UAAU,CAAC,GAAG,IAAI,EAAE,CAACI,GAAG,IAAI6F,cAAc,KAAK,IAAI,CAACnG,UAAU,CAACC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,CAACC,iBAAiB,IAAIP,mBAAmB,KAAKjB,SAAS,CAAC2H,kBAAkB,CAAC,IAAI,CAACnL,KAAK,EAAEoF,MAAM,CAAC,CAAC;IACjP;EACF,CAAC,CAAC,EAAE,CAAC;IACHzH,GAAG,EAAE,0BAA0B;IAC/BiD,KAAK,EAAE,SAASwK,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAACxC,WAAW,KAAKyC,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAACxC,WAAW;UACtC2C,SAAS,EAAEH,SAAS,CAACjG,MAAM;UAC3BqG,WAAW,EAAEJ,SAAS,CAAC1E,QAAQ;UAC/BoC,UAAU,EAAEuC,SAAS,CAACE,SAAS;UAC/BxC,YAAY,EAAEsC,SAAS,CAACG;QAC1B,CAAC;MACH;MACA,IAAIJ,SAAS,CAACjG,MAAM,KAAKkG,SAAS,CAACE,SAAS,IAAIH,SAAS,CAAC1E,QAAQ,KAAK2E,SAAS,CAACG,WAAW,EAAE;QAC5F,OAAO;UACLD,SAAS,EAAEH,SAAS,CAACjG,MAAM;UAC3BqG,WAAW,EAAEJ,SAAS,CAAC1E;QACzB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO3C,IAAI;AACb,CAAC,CAACd,aAAa,CAAC;AAChB3D,eAAe,CAACyE,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5CzE,eAAe,CAACyE,IAAI,EAAE,cAAc,EAAE;EACpCiE,MAAM,EAAE,SAAS;EACjBK,IAAI,EAAE,SAAS;EACfoD,WAAW,EAAE,GAAG;EAChBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,MAAM;EAClB3D,YAAY,EAAE,KAAK;EACnB;EACA9C,MAAM,EAAE,EAAE;EACVD,GAAG,EAAE,KAAK;EACV2G,SAAS,EAAE,IAAI;EACfnB,IAAI,EAAE,KAAK;EACX3F,iBAAiB,EAAE,CAACvB,MAAM,CAACsI,KAAK;EAChCrD,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACFrJ,eAAe,CAACyE,IAAI,EAAE,cAAc,EAAE,UAAUhE,KAAK,EAAEgM,IAAI,EAAElB,KAAK,EAAEC,KAAK,EAAE;EACzE,IAAIlD,MAAM,GAAG7H,KAAK,CAAC6H,MAAM;IACvBoE,cAAc,GAAGjM,KAAK,CAACkM,SAAS;EAClC,IAAIC,aAAa,GAAGH,IAAI,CAAChM,KAAK,CAACkM,SAAS;;EAExC;EACA;EACA,IAAIA,SAAS,GAAGC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGF,cAAc;EACnG,IAAIvI,QAAQ,CAACwI,SAAS,CAAC,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxD,OAAOA,SAAS;EAClB;EACA,IAAIE,WAAW,GAAGvE,MAAM,KAAK,YAAY,GAAGkD,KAAK,GAAGD,KAAK;EACzD,IAAIuB,MAAM,GAAGD,WAAW,CAACE,KAAK,CAACD,MAAM,CAAC,CAAC;EACvC,IAAID,WAAW,CAACpE,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAIb,GAAG,GAAGH,IAAI,CAACG,GAAG,CAACkF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIE,GAAG,GAAGvF,IAAI,CAACuF,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAIH,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOK,GAAG;IACZ;IACA,IAAIL,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAO/E,GAAG;IACZ;IACA,OAAOA,GAAG,GAAG,CAAC,GAAGA,GAAG,GAAGH,IAAI,CAACG,GAAG,CAACH,IAAI,CAACuF,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACpE;EACA,IAAIH,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOG,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIH,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOG,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,OAAOA,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF9M,eAAe,CAACyE,IAAI,EAAE,iBAAiB,EAAE,UAAUwI,KAAK,EAAE;EACxD,IAAIxM,KAAK,GAAGwM,KAAK,CAACxM,KAAK;IACrBgM,IAAI,GAAGQ,KAAK,CAACR,IAAI;IACjBlB,KAAK,GAAG0B,KAAK,CAAC1B,KAAK;IACnBC,KAAK,GAAGyB,KAAK,CAACzB,KAAK;IACnB0B,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBtH,OAAO,GAAGmH,KAAK,CAACnH,OAAO;IACvBuH,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,MAAM,GAAGP,KAAK,CAACO,MAAM;EACvB,IAAIlF,MAAM,GAAG7H,KAAK,CAAC6H,MAAM;EACzB,IAAImF,QAAQ,GAAGJ,WAAW,IAAIA,WAAW,CAAC5O,MAAM;EAChD,IAAIkO,SAAS,GAAGlI,IAAI,CAACiJ,YAAY,CAACjN,KAAK,EAAEgM,IAAI,EAAElB,KAAK,EAAEC,KAAK,CAAC;EAC5D,IAAI5C,OAAO,GAAG,KAAK;EACnB,IAAI/C,MAAM,GAAG0H,aAAa,CAACrH,GAAG,CAAC,UAAUC,KAAK,EAAEO,KAAK,EAAE;IACrD,IAAIiH,aAAa,GAAGpJ,iBAAiB,CAAC4B,KAAK,EAAEL,OAAO,CAAC;IACrD,IAAIzE,KAAK;IACT,IAAIoM,QAAQ,EAAE;MACZpM,KAAK,GAAGgM,WAAW,CAACC,cAAc,GAAG5G,KAAK,CAAC;IAC7C,CAAC,MAAM;MACLrF,KAAK,GAAGsM,aAAa;MACrB,IAAI,CAACpQ,QAAQ,CAAC8D,KAAK,CAAC,EAAE;QACpBA,KAAK,GAAG,CAACsL,SAAS,EAAEtL,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLuH,OAAO,GAAG,IAAI;MAChB;IACF;IACA,IAAIgF,YAAY,GAAGtQ,MAAM,CAAC+D,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIoM,QAAQ,IAAInQ,MAAM,CAACqQ,aAAa,CAAC;IACxE,IAAIrF,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACL/B,CAAC,EAAEjC,uBAAuB,CAAC;UACzBuJ,IAAI,EAAEtC,KAAK;UACXuC,KAAK,EAAEZ,UAAU;UACjBE,QAAQ,EAAEA,QAAQ;UAClBjH,KAAK,EAAEA,KAAK;UACZO,KAAK,EAAEA;QACT,CAAC,CAAC;QACFD,CAAC,EAAEmH,YAAY,GAAG,IAAI,GAAGpC,KAAK,CAACuB,KAAK,CAAC1L,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9CA,KAAK,EAAEA,KAAK;QACZsF,OAAO,EAAER;MACX,CAAC;IACH;IACA,OAAO;MACLI,CAAC,EAAEqH,YAAY,GAAG,IAAI,GAAGrC,KAAK,CAACwB,KAAK,CAAC1L,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9CoF,CAAC,EAAEnC,uBAAuB,CAAC;QACzBuJ,IAAI,EAAErC,KAAK;QACXsC,KAAK,EAAEX,UAAU;QACjBC,QAAQ,EAAEA,QAAQ;QAClBjH,KAAK,EAAEA,KAAK;QACZO,KAAK,EAAEA;MACT,CAAC,CAAC;MACFrF,KAAK,EAAEA,KAAK;MACZsF,OAAO,EAAER;IACX,CAAC;EACH,CAAC,CAAC;EACF,IAAIiB,QAAQ;EACZ,IAAIqG,QAAQ,IAAI7E,OAAO,EAAE;IACvBxB,QAAQ,GAAGvB,MAAM,CAACK,GAAG,CAAC,UAAUC,KAAK,EAAE;MACrC,IAAImC,MAAM,KAAK,YAAY,EAAE;QAC3B,OAAO;UACL/B,CAAC,EAAEJ,KAAK,CAACI,CAAC;UACVE,CAAC,EAAE,CAACnJ,MAAM,CAACD,IAAI,CAAC8I,KAAK,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC7I,MAAM,CAACD,IAAI,CAAC8I,KAAK,EAAE,GAAG,CAAC,CAAC,GAAGqF,KAAK,CAACuB,KAAK,CAAC1P,IAAI,CAAC8I,KAAK,EAAE,UAAU,CAAC,CAAC,GAAG;QAC5G,CAAC;MACH;MACA,OAAO;QACLI,CAAC,EAAE,CAACjJ,MAAM,CAACD,IAAI,CAAC8I,KAAK,EAAE,UAAU,CAAC,CAAC,GAAGoF,KAAK,CAACwB,KAAK,CAAC1P,IAAI,CAAC8I,KAAK,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI;QACjFM,CAAC,EAAEN,KAAK,CAACM;MACX,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI6B,MAAM,KAAK,YAAY,EAAE;IAClClB,QAAQ,GAAGoE,KAAK,CAACuB,KAAK,CAACJ,SAAS,CAAC;EACnC,CAAC,MAAM;IACLvF,QAAQ,GAAGmE,KAAK,CAACwB,KAAK,CAACJ,SAAS,CAAC;EACnC;EACA,OAAO7M,aAAa,CAAC;IACnB+F,MAAM,EAAEA,MAAM;IACduB,QAAQ,EAAEA,QAAQ;IAClBkB,MAAM,EAAEA,MAAM;IACdM,OAAO,EAAEA;EACX,CAAC,EAAE4E,MAAM,CAAC;AACZ,CAAC,CAAC;AACFxN,eAAe,CAACyE,IAAI,EAAE,eAAe,EAAE,UAAUsJ,MAAM,EAAEtN,KAAK,EAAE;EAC9D,IAAIuN,OAAO;EACX,IAAK,aAAatK,KAAK,CAACuK,cAAc,CAACF,MAAM,CAAC,EAAE;IAC9CC,OAAO,GAAG,aAAatK,KAAK,CAACwK,YAAY,CAACH,MAAM,EAAEtN,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAIrD,WAAW,CAAC2Q,MAAM,CAAC,EAAE;IAC9BC,OAAO,GAAGD,MAAM,CAACtN,KAAK,CAAC;EACzB,CAAC,MAAM;IACLuN,OAAO,GAAG,aAAatK,KAAK,CAACqD,aAAa,CAAChD,GAAG,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAE0B,KAAK,EAAE;MAClEuG,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOgH,OAAO;AAChB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}