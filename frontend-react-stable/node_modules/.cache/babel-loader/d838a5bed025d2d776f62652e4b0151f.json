{"ast": null, "code": "export function setTime(generateConfig, date, hour, minute, second) {\n  var nextTime = generateConfig.setHour(date, hour);\n  nextTime = generateConfig.setMinute(nextTime, minute);\n  nextTime = generateConfig.setSecond(nextTime, second);\n  return nextTime;\n}\nexport function setDateTime(generateConfig, date, defaultDate) {\n  if (!defaultDate) {\n    return date;\n  }\n  var newDate = date;\n  newDate = generateConfig.setHour(newDate, generateConfig.getHour(defaultDate));\n  newDate = generateConfig.setMinute(newDate, generateConfig.getMinute(defaultDate));\n  newDate = generateConfig.setSecond(newDate, generateConfig.getSecond(defaultDate));\n  return newDate;\n}\nexport function getLowerBoundTime(hour, minute, second, hourStep, minuteStep, secondStep) {\n  var lowerBoundHour = Math.floor(hour / hourStep) * hourStep;\n  if (lowerBoundHour < hour) {\n    return [lowerBoundHour, 60 - minuteStep, 60 - secondStep];\n  }\n  var lowerBoundMinute = Math.floor(minute / minuteStep) * minuteStep;\n  if (lowerBoundMinute < minute) {\n    return [lowerBoundHour, lowerBoundMinute, 60 - secondStep];\n  }\n  var lowerBoundSecond = Math.floor(second / secondStep) * secondStep;\n  return [lowerBoundHour, lowerBoundMinute, lowerBoundSecond];\n}\nexport function getLastDay(generateConfig, date) {\n  var year = generateConfig.getYear(date);\n  var month = generateConfig.getMonth(date) + 1;\n  var endDate = generateConfig.getEndDate(generateConfig.getFixedDate(\"\".concat(year, \"-\").concat(month, \"-01\")));\n  var lastDay = generateConfig.getDate(endDate);\n  var monthShow = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n  return \"\".concat(year, \"-\").concat(monthShow, \"-\").concat(lastDay);\n}", "map": {"version": 3, "names": ["setTime", "generateConfig", "date", "hour", "minute", "second", "nextTime", "setHour", "setMinute", "setSecond", "setDateTime", "defaultDate", "newDate", "getHour", "getMinute", "getSecond", "getLowerBoundTime", "hourStep", "minuteStep", "secondStep", "lowerBoundHour", "Math", "floor", "lowerBoundMinute", "lowerBoundSecond", "getLastDay", "year", "getYear", "month", "getMonth", "endDate", "getEndDate", "getFixedDate", "concat", "lastDay", "getDate", "monthShow"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/utils/timeUtil.js"], "sourcesContent": ["export function setTime(generateConfig, date, hour, minute, second) {\n  var nextTime = generateConfig.setHour(date, hour);\n  nextTime = generateConfig.setMinute(nextTime, minute);\n  nextTime = generateConfig.setSecond(nextTime, second);\n  return nextTime;\n}\nexport function setDateTime(generateConfig, date, defaultDate) {\n  if (!defaultDate) {\n    return date;\n  }\n  var newDate = date;\n  newDate = generateConfig.setHour(newDate, generateConfig.getHour(defaultDate));\n  newDate = generateConfig.setMinute(newDate, generateConfig.getMinute(defaultDate));\n  newDate = generateConfig.setSecond(newDate, generateConfig.getSecond(defaultDate));\n  return newDate;\n}\nexport function getLowerBoundTime(hour, minute, second, hourStep, minuteStep, secondStep) {\n  var lowerBoundHour = Math.floor(hour / hourStep) * hourStep;\n  if (lowerBoundHour < hour) {\n    return [lowerBoundHour, 60 - minuteStep, 60 - secondStep];\n  }\n  var lowerBoundMinute = Math.floor(minute / minuteStep) * minuteStep;\n  if (lowerBoundMinute < minute) {\n    return [lowerBoundHour, lowerBoundMinute, 60 - secondStep];\n  }\n  var lowerBoundSecond = Math.floor(second / secondStep) * secondStep;\n  return [lowerBoundHour, lowerBoundMinute, lowerBoundSecond];\n}\nexport function getLastDay(generateConfig, date) {\n  var year = generateConfig.getYear(date);\n  var month = generateConfig.getMonth(date) + 1;\n  var endDate = generateConfig.getEndDate(generateConfig.getFixedDate(\"\".concat(year, \"-\").concat(month, \"-01\")));\n  var lastDay = generateConfig.getDate(endDate);\n  var monthShow = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n  return \"\".concat(year, \"-\").concat(monthShow, \"-\").concat(lastDay);\n}"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,cAAc,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAClE,IAAIC,QAAQ,GAAGL,cAAc,CAACM,OAAO,CAACL,IAAI,EAAEC,IAAI,CAAC;EACjDG,QAAQ,GAAGL,cAAc,CAACO,SAAS,CAACF,QAAQ,EAAEF,MAAM,CAAC;EACrDE,QAAQ,GAAGL,cAAc,CAACQ,SAAS,CAACH,QAAQ,EAAED,MAAM,CAAC;EACrD,OAAOC,QAAQ;AACjB;AACA,OAAO,SAASI,WAAWA,CAACT,cAAc,EAAEC,IAAI,EAAES,WAAW,EAAE;EAC7D,IAAI,CAACA,WAAW,EAAE;IAChB,OAAOT,IAAI;EACb;EACA,IAAIU,OAAO,GAAGV,IAAI;EAClBU,OAAO,GAAGX,cAAc,CAACM,OAAO,CAACK,OAAO,EAAEX,cAAc,CAACY,OAAO,CAACF,WAAW,CAAC,CAAC;EAC9EC,OAAO,GAAGX,cAAc,CAACO,SAAS,CAACI,OAAO,EAAEX,cAAc,CAACa,SAAS,CAACH,WAAW,CAAC,CAAC;EAClFC,OAAO,GAAGX,cAAc,CAACQ,SAAS,CAACG,OAAO,EAAEX,cAAc,CAACc,SAAS,CAACJ,WAAW,CAAC,CAAC;EAClF,OAAOC,OAAO;AAChB;AACA,OAAO,SAASI,iBAAiBA,CAACb,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEY,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACxF,IAAIC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACnB,IAAI,GAAGc,QAAQ,CAAC,GAAGA,QAAQ;EAC3D,IAAIG,cAAc,GAAGjB,IAAI,EAAE;IACzB,OAAO,CAACiB,cAAc,EAAE,EAAE,GAAGF,UAAU,EAAE,EAAE,GAAGC,UAAU,CAAC;EAC3D;EACA,IAAII,gBAAgB,GAAGF,IAAI,CAACC,KAAK,CAAClB,MAAM,GAAGc,UAAU,CAAC,GAAGA,UAAU;EACnE,IAAIK,gBAAgB,GAAGnB,MAAM,EAAE;IAC7B,OAAO,CAACgB,cAAc,EAAEG,gBAAgB,EAAE,EAAE,GAAGJ,UAAU,CAAC;EAC5D;EACA,IAAIK,gBAAgB,GAAGH,IAAI,CAACC,KAAK,CAACjB,MAAM,GAAGc,UAAU,CAAC,GAAGA,UAAU;EACnE,OAAO,CAACC,cAAc,EAAEG,gBAAgB,EAAEC,gBAAgB,CAAC;AAC7D;AACA,OAAO,SAASC,UAAUA,CAACxB,cAAc,EAAEC,IAAI,EAAE;EAC/C,IAAIwB,IAAI,GAAGzB,cAAc,CAAC0B,OAAO,CAACzB,IAAI,CAAC;EACvC,IAAI0B,KAAK,GAAG3B,cAAc,CAAC4B,QAAQ,CAAC3B,IAAI,CAAC,GAAG,CAAC;EAC7C,IAAI4B,OAAO,GAAG7B,cAAc,CAAC8B,UAAU,CAAC9B,cAAc,CAAC+B,YAAY,CAAC,EAAE,CAACC,MAAM,CAACP,IAAI,EAAE,GAAG,CAAC,CAACO,MAAM,CAACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;EAC/G,IAAIM,OAAO,GAAGjC,cAAc,CAACkC,OAAO,CAACL,OAAO,CAAC;EAC7C,IAAIM,SAAS,GAAGR,KAAK,GAAG,EAAE,GAAG,GAAG,CAACK,MAAM,CAACL,KAAK,CAAC,GAAG,EAAE,CAACK,MAAM,CAACL,KAAK,CAAC;EACjE,OAAO,EAAE,CAACK,MAAM,CAACP,IAAI,EAAE,GAAG,CAAC,CAACO,MAAM,CAACG,SAAS,EAAE,GAAG,CAAC,CAACH,MAAM,CAACC,OAAO,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}