{"ast": null, "code": "/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar OptGroup = function OptGroup() {\n  return null;\n};\nOptGroup.isSelectOptGroup = true;\nexport default OptGroup;", "map": {"version": 3, "names": ["OptGroup", "isSelectOptGroup"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/OptGroup.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar OptGroup = function OptGroup() {\n  return null;\n};\nOptGroup.isSelectOptGroup = true;\nexport default OptGroup;"], "mappings": "AAAA;;AAEA;AACA,IAAIA,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;EACjC,OAAO,IAAI;AACb,CAAC;AACDA,QAAQ,CAACC,gBAAgB,GAAG,IAAI;AAChC,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}