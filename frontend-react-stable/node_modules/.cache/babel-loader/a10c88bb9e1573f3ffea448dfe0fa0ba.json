{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={sidebarCollapsed:false,theme:'light',loading:false,notifications:[]};const uiSlice=createSlice({name:'ui',initialState,reducers:{toggleSidebar:state=>{state.sidebarCollapsed=!state.sidebarCollapsed;},setSidebarCollapsed:(state,action)=>{state.sidebarCollapsed=action.payload;},setTheme:(state,action)=>{state.theme=action.payload;},setLoading:(state,action)=>{state.loading=action.payload;},addNotification:(state,action)=>{const notification={...action.payload,id:Date.now().toString(),timestamp:Date.now()};state.notifications.unshift(notification);// 保持最多10个通知\nif(state.notifications.length>10){state.notifications=state.notifications.slice(0,10);}},removeNotification:(state,action)=>{state.notifications=state.notifications.filter(notification=>notification.id!==action.payload);},clearNotifications:state=>{state.notifications=[];}}});export const{toggleSidebar,setSidebarCollapsed,setTheme,setLoading,addNotification,removeNotification,clearNotifications}=uiSlice.actions;export default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarCollapsed", "theme", "loading", "notifications", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarCollapsed", "action", "payload", "setTheme", "setLoading", "addNotification", "notification", "id", "Date", "now", "toString", "timestamp", "unshift", "length", "slice", "removeNotification", "filter", "clearNotifications", "actions", "reducer"], "sources": ["/home/<USER>/frontend-react-stable/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface UIState {\n  sidebarCollapsed: boolean;\n  theme: 'light' | 'dark';\n  loading: boolean;\n  notifications: Notification[];\n}\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: number;\n}\n\nconst initialState: UIState = {\n  sidebarCollapsed: false,\n  theme: 'light',\n  loading: false,\n  notifications: [],\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {\n      state.sidebarCollapsed = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: Date.now(),\n      };\n      state.notifications.unshift(notification);\n      // 保持最多10个通知\n      if (state.notifications.length > 10) {\n        state.notifications = state.notifications.slice(0, 10);\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(\n        (notification) => notification.id !== action.payload\n      );\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarCollapsed,\n  setTheme,\n  setLoading,\n  addNotification,\n  removeNotification,\n  clearNotifications,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,OAASA,WAAW,KAAuB,kBAAkB,CAiB7D,KAAM,CAAAC,YAAqB,CAAG,CAC5BC,gBAAgB,CAAE,KAAK,CACvBC,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,KAAK,CACdC,aAAa,CAAE,EACjB,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGN,WAAW,CAAC,CAC1BO,IAAI,CAAE,IAAI,CACVN,YAAY,CACZO,QAAQ,CAAE,CACRC,aAAa,CAAGC,KAAK,EAAK,CACxBA,KAAK,CAACR,gBAAgB,CAAG,CAACQ,KAAK,CAACR,gBAAgB,CAClD,CAAC,CACDS,mBAAmB,CAAEA,CAACD,KAAK,CAAEE,MAA8B,GAAK,CAC9DF,KAAK,CAACR,gBAAgB,CAAGU,MAAM,CAACC,OAAO,CACzC,CAAC,CACDC,QAAQ,CAAEA,CAACJ,KAAK,CAAEE,MAAuC,GAAK,CAC5DF,KAAK,CAACP,KAAK,CAAGS,MAAM,CAACC,OAAO,CAC9B,CAAC,CACDE,UAAU,CAAEA,CAACL,KAAK,CAAEE,MAA8B,GAAK,CACrDF,KAAK,CAACN,OAAO,CAAGQ,MAAM,CAACC,OAAO,CAChC,CAAC,CACDG,eAAe,CAAEA,CAACN,KAAK,CAAEE,MAA6D,GAAK,CACzF,KAAM,CAAAK,YAA0B,CAAG,CACjC,GAAGL,MAAM,CAACC,OAAO,CACjBK,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBC,SAAS,CAAEH,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CACDV,KAAK,CAACL,aAAa,CAACkB,OAAO,CAACN,YAAY,CAAC,CACzC;AACA,GAAIP,KAAK,CAACL,aAAa,CAACmB,MAAM,CAAG,EAAE,CAAE,CACnCd,KAAK,CAACL,aAAa,CAAGK,KAAK,CAACL,aAAa,CAACoB,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CACxD,CACF,CAAC,CACDC,kBAAkB,CAAEA,CAAChB,KAAK,CAAEE,MAA6B,GAAK,CAC5DF,KAAK,CAACL,aAAa,CAAGK,KAAK,CAACL,aAAa,CAACsB,MAAM,CAC7CV,YAAY,EAAKA,YAAY,CAACC,EAAE,GAAKN,MAAM,CAACC,OAC/C,CAAC,CACH,CAAC,CACDe,kBAAkB,CAAGlB,KAAK,EAAK,CAC7BA,KAAK,CAACL,aAAa,CAAG,EAAE,CAC1B,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CACXI,aAAa,CACbE,mBAAmB,CACnBG,QAAQ,CACRC,UAAU,CACVC,eAAe,CACfU,kBAAkB,CAClBE,kBACF,CAAC,CAAGtB,OAAO,CAACuB,OAAO,CAEnB,cAAe,CAAAvB,OAAO,CAACwB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}