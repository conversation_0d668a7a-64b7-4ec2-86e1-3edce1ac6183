{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport GifOutlinedSvg from \"@ant-design/icons-svg/es/asn/GifOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar GifOutlined = function GifOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: GifOutlinedSvg\n  }));\n};\nGifOutlined.displayName = 'GifOutlined';\nexport default /*#__PURE__*/React.forwardRef(GifOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "GifOutlinedSvg", "AntdIcon", "GifOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/GifOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport GifOutlinedSvg from \"@ant-design/icons-svg/es/asn/GifOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar GifOutlined = function GifOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: GifOutlinedSvg\n  }));\n};\nGifOutlined.displayName = 'GifOutlined';\nexport default /*#__PURE__*/React.forwardRef(GifOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,WAAW,CAACK,WAAW,GAAG,aAAa;AACvC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}