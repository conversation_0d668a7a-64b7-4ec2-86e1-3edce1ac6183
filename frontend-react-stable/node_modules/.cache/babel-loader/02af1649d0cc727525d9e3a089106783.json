{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = superset;\nfunction superset(values, other) {\n  const iterator = values[Symbol.iterator](),\n    set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "superset", "values", "other", "iterator", "Symbol", "set", "Set", "o", "io", "intern", "has", "done", "next", "ivalue", "add", "is", "valueOf"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/superset.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = superset;\n\nfunction superset(values, other) {\n  const iterator = values[Symbol.iterator](),\n        set = new Set();\n\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n\n    while (({\n      value,\n      done\n    } = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;IACpCE,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EAErB,KAAK,MAAMC,CAAC,IAAIL,KAAK,EAAE;IACrB,MAAMM,EAAE,GAAGC,MAAM,CAACF,CAAC,CAAC;IACpB,IAAIF,GAAG,CAACK,GAAG,CAACF,EAAE,CAAC,EAAE;IACjB,IAAIV,KAAK,EAAEa,IAAI;IAEf,OAAQ;MACNb,KAAK;MACLa;IACF,CAAC,GAAGR,QAAQ,CAACS,IAAI,CAAC,CAAC,EAAG;MACpB,IAAID,IAAI,EAAE,OAAO,KAAK;MACtB,MAAME,MAAM,GAAGJ,MAAM,CAACX,KAAK,CAAC;MAC5BO,GAAG,CAACS,GAAG,CAACD,MAAM,CAAC;MACf,IAAIlB,MAAM,CAACoB,EAAE,CAACP,EAAE,EAAEK,MAAM,CAAC,EAAE;IAC7B;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASJ,MAAMA,CAACX,KAAK,EAAE;EACrB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACkB,OAAO,CAAC,CAAC,GAAGlB,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "script"}