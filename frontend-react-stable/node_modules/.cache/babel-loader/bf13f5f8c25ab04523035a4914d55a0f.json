{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"component\", \"viewBox\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport { svgBaseProps, warning, useInsertStyles } from '../utils';\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    Component = props.component,\n    viewBox = props.viewBox,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  warning(Boolean(Component || children), 'Should have `component` prop or `children`.');\n  useInsertStyles();\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, className);\n  var svgClassString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin));\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var innerSvgProps = _objectSpread(_objectSpread({}, svgBaseProps), {}, {\n    className: svgClassString,\n    style: svgStyle,\n    viewBox: viewBox\n  });\n  if (!viewBox) {\n    delete innerSvgProps.viewBox;\n  }\n  // component > children\n  var renderInnerNode = function renderInnerNode() {\n    if (Component) {\n      return /*#__PURE__*/React.createElement(Component, _objectSpread({}, innerSvgProps), children);\n    }\n    if (children) {\n      warning(Boolean(viewBox) || React.Children.count(children) === 1 && /*#__PURE__*/React.isValidElement(children) && React.Children.only(children).type === 'use', 'Make sure that you provide correct `viewBox`' + ' prop (default `0 0 1024 1024`) to the icon.');\n      return /*#__PURE__*/React.createElement(\"svg\", _objectSpread(_objectSpread({}, innerSvgProps), {}, {\n        viewBox: viewBox\n      }), children);\n    }\n    return null;\n  };\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\"\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), renderInnerNode());\n});\nIcon.displayName = 'AntdIcon';\nexport default Icon;", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_objectWithoutProperties", "_excluded", "React", "classNames", "Context", "svgBaseProps", "warning", "useInsertStyles", "Icon", "forwardRef", "props", "ref", "className", "Component", "component", "viewBox", "spin", "rotate", "tabIndex", "onClick", "children", "restProps", "Boolean", "_React$useContext", "useContext", "_React$useContext$pre", "prefixCls", "rootClassName", "classString", "svgClassString", "concat", "svgStyle", "msTransform", "transform", "undefined", "innerSvgProps", "style", "renderInnerNode", "createElement", "Children", "count", "isValidElement", "only", "type", "iconTabIndex", "role", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/components/Icon.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"component\", \"viewBox\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Context from './Context';\nimport { svgBaseProps, warning, useInsertStyles } from '../utils';\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    Component = props.component,\n    viewBox = props.viewBox,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  warning(Boolean(Component || children), 'Should have `component` prop or `children`.');\n  useInsertStyles();\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, className);\n  var svgClassString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin));\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var innerSvgProps = _objectSpread(_objectSpread({}, svgBaseProps), {}, {\n    className: svgClassString,\n    style: svgStyle,\n    viewBox: viewBox\n  });\n  if (!viewBox) {\n    delete innerSvgProps.viewBox;\n  }\n  // component > children\n  var renderInnerNode = function renderInnerNode() {\n    if (Component) {\n      return /*#__PURE__*/React.createElement(Component, _objectSpread({}, innerSvgProps), children);\n    }\n    if (children) {\n      warning(Boolean(viewBox) || React.Children.count(children) === 1 && /*#__PURE__*/React.isValidElement(children) && React.Children.only(children).type === 'use', 'Make sure that you provide correct `viewBox`' + ' prop (default `0 0 1024 1024`) to the icon.');\n      return /*#__PURE__*/React.createElement(\"svg\", _objectSpread(_objectSpread({}, innerSvgProps), {}, {\n        viewBox: viewBox\n      }), children);\n    }\n    return null;\n  };\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _objectSpread(_objectSpread({\n    role: \"img\"\n  }, restProps), {}, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), renderInnerNode());\n});\nIcon.displayName = 'AntdIcon';\nexport default Icon;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;AAC1G,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,YAAY,EAAEC,OAAO,EAAEC,eAAe,QAAQ,UAAU;AACjE,IAAIC,IAAI,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,SAAS,GAAGH,KAAK,CAACI,SAAS;IAC3BC,OAAO,GAAGL,KAAK,CAACK,OAAO;IACvBC,IAAI,GAAGN,KAAK,CAACM,IAAI;IACjBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,SAAS,GAAGrB,wBAAwB,CAACU,KAAK,EAAET,SAAS,CAAC;EACxDK,OAAO,CAACgB,OAAO,CAACT,SAAS,IAAIO,QAAQ,CAAC,EAAE,6CAA6C,CAAC;EACtFb,eAAe,CAAC,CAAC;EACjB,IAAIgB,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACpB,OAAO,CAAC;IAC/CqB,qBAAqB,GAAGF,iBAAiB,CAACG,SAAS;IACnDA,SAAS,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,qBAAqB;IAChFE,aAAa,GAAGJ,iBAAiB,CAACI,aAAa;EACjD,IAAIC,WAAW,GAAGzB,UAAU,CAACwB,aAAa,EAAED,SAAS,EAAEd,SAAS,CAAC;EACjE,IAAIiB,cAAc,GAAG1B,UAAU,CAACJ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+B,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAACV,IAAI,CAAC,CAAC;EAC3F,IAAIe,QAAQ,GAAGd,MAAM,GAAG;IACtBe,WAAW,EAAE,SAAS,CAACF,MAAM,CAACb,MAAM,EAAE,MAAM,CAAC;IAC7CgB,SAAS,EAAE,SAAS,CAACH,MAAM,CAACb,MAAM,EAAE,MAAM;EAC5C,CAAC,GAAGiB,SAAS;EACb,IAAIC,aAAa,GAAGrC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEO,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;IACrEO,SAAS,EAAEiB,cAAc;IACzBO,KAAK,EAAEL,QAAQ;IACfhB,OAAO,EAAEA;EACX,CAAC,CAAC;EACF,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOoB,aAAa,CAACpB,OAAO;EAC9B;EACA;EACA,IAAIsB,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIxB,SAAS,EAAE;MACb,OAAO,aAAaX,KAAK,CAACoC,aAAa,CAACzB,SAAS,EAAEf,aAAa,CAAC,CAAC,CAAC,EAAEqC,aAAa,CAAC,EAAEf,QAAQ,CAAC;IAChG;IACA,IAAIA,QAAQ,EAAE;MACZd,OAAO,CAACgB,OAAO,CAACP,OAAO,CAAC,IAAIb,KAAK,CAACqC,QAAQ,CAACC,KAAK,CAACpB,QAAQ,CAAC,KAAK,CAAC,IAAI,aAAalB,KAAK,CAACuC,cAAc,CAACrB,QAAQ,CAAC,IAAIlB,KAAK,CAACqC,QAAQ,CAACG,IAAI,CAACtB,QAAQ,CAAC,CAACuB,IAAI,KAAK,KAAK,EAAE,8CAA8C,GAAG,8CAA8C,CAAC;MACjQ,OAAO,aAAazC,KAAK,CAACoC,aAAa,CAAC,KAAK,EAAExC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqC,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;QACjGpB,OAAO,EAAEA;MACX,CAAC,CAAC,EAAEK,QAAQ,CAAC;IACf;IACA,OAAO,IAAI;EACb,CAAC;EACD,IAAIwB,YAAY,GAAG1B,QAAQ;EAC3B,IAAI0B,YAAY,KAAKV,SAAS,IAAIf,OAAO,EAAE;IACzCyB,YAAY,GAAG,CAAC,CAAC;EACnB;EACA,OAAO,aAAa1C,KAAK,CAACoC,aAAa,CAAC,MAAM,EAAExC,aAAa,CAACA,aAAa,CAAC;IAC1E+C,IAAI,EAAE;EACR,CAAC,EAAExB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;IACjBV,GAAG,EAAEA,GAAG;IACRO,QAAQ,EAAE0B,YAAY;IACtBzB,OAAO,EAAEA,OAAO;IAChBP,SAAS,EAAEgB;EACb,CAAC,CAAC,EAAES,eAAe,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AACF7B,IAAI,CAACsC,WAAW,GAAG,UAAU;AAC7B,eAAetC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}