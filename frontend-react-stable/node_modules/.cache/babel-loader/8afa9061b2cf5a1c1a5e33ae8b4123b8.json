{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport TableContext from '../context/TableContext';\nimport ExpandedRow from './ExpandedRow';\nimport BodyContext from '../context/BodyContext';\nimport { getColumnsKey } from '../utils/valueUtil';\nimport ResizeContext from '../context/ResizeContext';\nimport BodyRow from './BodyRow';\nimport useFlattenRecords from '../hooks/useFlattenRecords';\nimport HoverContext from '../context/HoverContext';\nimport PerfContext from '../context/PerfContext';\nimport MeasureRow from './MeasureRow';\nfunction Body(_ref) {\n  var data = _ref.data,\n    getRowKey = _ref.getRowKey,\n    measureColumnWidth = _ref.measureColumnWidth,\n    expandedKeys = _ref.expandedKeys,\n    onRow = _ref.onRow,\n    rowExpandable = _ref.rowExpandable,\n    emptyNode = _ref.emptyNode,\n    childrenColumnName = _ref.childrenColumnName;\n  var _React$useContext = React.useContext(ResizeContext),\n    onColumnResize = _React$useContext.onColumnResize;\n  var _React$useContext2 = React.useContext(TableContext),\n    prefixCls = _React$useContext2.prefixCls,\n    getComponent = _React$useContext2.getComponent;\n  var _React$useContext3 = React.useContext(BodyContext),\n    flattenColumns = _React$useContext3.flattenColumns;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey); // =================== Performance ====================\n\n  var perfRef = React.useRef({\n    renderWithProps: false\n  }); // ====================== Hover =======================\n\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    startRow = _React$useState2[0],\n    setStartRow = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    endRow = _React$useState4[0],\n    setEndRow = _React$useState4[1];\n  var onHover = React.useCallback(function (start, end) {\n    setStartRow(start);\n    setEndRow(end);\n  }, []); // ====================== Render ======================\n\n  var bodyNode = React.useMemo(function () {\n    var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n    var trComponent = getComponent(['body', 'row'], 'tr');\n    var tdComponent = getComponent(['body', 'cell'], 'td');\n    var rows;\n    if (data.length) {\n      rows = flattenData.map(function (item, idx) {\n        var record = item.record,\n          indent = item.indent,\n          renderIndex = item.index;\n        var key = getRowKey(record, idx);\n        return /*#__PURE__*/React.createElement(BodyRow, {\n          key: key,\n          rowKey: key,\n          record: record,\n          recordKey: key,\n          index: idx,\n          renderIndex: renderIndex,\n          rowComponent: trComponent,\n          cellComponent: tdComponent,\n          expandedKeys: expandedKeys,\n          onRow: onRow,\n          getRowKey: getRowKey,\n          rowExpandable: rowExpandable,\n          childrenColumnName: childrenColumnName,\n          indent: indent\n        });\n      });\n    } else {\n      rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n        expanded: true,\n        className: \"\".concat(prefixCls, \"-placeholder\"),\n        prefixCls: prefixCls,\n        component: trComponent,\n        cellComponent: tdComponent,\n        colSpan: flattenColumns.length,\n        isEmpty: true\n      }, emptyNode);\n    }\n    var columnsKey = getColumnsKey(flattenColumns);\n    return /*#__PURE__*/React.createElement(WrapperComponent, {\n      className: \"\".concat(prefixCls, \"-tbody\")\n    }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n      prefixCls: prefixCls,\n      columnsKey: columnsKey,\n      onColumnResize: onColumnResize\n    }), rows);\n  }, [data, prefixCls, onRow, measureColumnWidth, expandedKeys, getRowKey, getComponent, emptyNode, flattenColumns, childrenColumnName, onColumnResize, rowExpandable, flattenData]);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(HoverContext.Provider, {\n    value: {\n      startRow: startRow,\n      endRow: endRow,\n      onHover: onHover\n    }\n  }, bodyNode));\n}\nvar MemoBody = /*#__PURE__*/React.memo(Body);\nMemoBody.displayName = 'Body';\nexport default MemoBody;", "map": {"version": 3, "names": ["_slicedToArray", "React", "TableContext", "ExpandedRow", "BodyContext", "getColumnsKey", "ResizeContext", "BodyRow", "useFlattenRecords", "HoverContext", "PerfContext", "MeasureRow", "Body", "_ref", "data", "getRowKey", "measureColumnWidth", "expandedKeys", "onRow", "rowExpandable", "emptyNode", "childrenColumnName", "_React$useContext", "useContext", "onColumnResize", "_React$useContext2", "prefixCls", "getComponent", "_React$useContext3", "flattenColumns", "flattenData", "perfRef", "useRef", "renderWithProps", "_React$useState", "useState", "_React$useState2", "startRow", "setStartRow", "_React$useState3", "_React$useState4", "endRow", "setEndRow", "onHover", "useCallback", "start", "end", "bodyNode", "useMemo", "WrapperComponent", "trComponent", "tdComponent", "rows", "length", "map", "item", "idx", "record", "indent", "renderIndex", "index", "key", "createElement", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ey", "rowComponent", "cellComponent", "expanded", "className", "concat", "component", "colSpan", "isEmpty", "columnsKey", "Provider", "value", "current", "MemoBody", "memo", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Body/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport TableContext from '../context/TableContext';\nimport ExpandedRow from './ExpandedRow';\nimport BodyContext from '../context/BodyContext';\nimport { getColumnsKey } from '../utils/valueUtil';\nimport ResizeContext from '../context/ResizeContext';\nimport BodyRow from './BodyRow';\nimport useFlattenRecords from '../hooks/useFlattenRecords';\nimport HoverContext from '../context/HoverContext';\nimport PerfContext from '../context/PerfContext';\nimport MeasureRow from './MeasureRow';\n\nfunction Body(_ref) {\n  var data = _ref.data,\n      getRowKey = _ref.getRowKey,\n      measureColumnWidth = _ref.measureColumnWidth,\n      expandedKeys = _ref.expandedKeys,\n      onRow = _ref.onRow,\n      rowExpandable = _ref.rowExpandable,\n      emptyNode = _ref.emptyNode,\n      childrenColumnName = _ref.childrenColumnName;\n\n  var _React$useContext = React.useContext(ResizeContext),\n      onColumnResize = _React$useContext.onColumnResize;\n\n  var _React$useContext2 = React.useContext(TableContext),\n      prefixCls = _React$useContext2.prefixCls,\n      getComponent = _React$useContext2.getComponent;\n\n  var _React$useContext3 = React.useContext(BodyContext),\n      flattenColumns = _React$useContext3.flattenColumns;\n\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey); // =================== Performance ====================\n\n  var perfRef = React.useRef({\n    renderWithProps: false\n  }); // ====================== Hover =======================\n\n  var _React$useState = React.useState(-1),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      startRow = _React$useState2[0],\n      setStartRow = _React$useState2[1];\n\n  var _React$useState3 = React.useState(-1),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      endRow = _React$useState4[0],\n      setEndRow = _React$useState4[1];\n\n  var onHover = React.useCallback(function (start, end) {\n    setStartRow(start);\n    setEndRow(end);\n  }, []); // ====================== Render ======================\n\n  var bodyNode = React.useMemo(function () {\n    var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n    var trComponent = getComponent(['body', 'row'], 'tr');\n    var tdComponent = getComponent(['body', 'cell'], 'td');\n    var rows;\n\n    if (data.length) {\n      rows = flattenData.map(function (item, idx) {\n        var record = item.record,\n            indent = item.indent,\n            renderIndex = item.index;\n        var key = getRowKey(record, idx);\n        return /*#__PURE__*/React.createElement(BodyRow, {\n          key: key,\n          rowKey: key,\n          record: record,\n          recordKey: key,\n          index: idx,\n          renderIndex: renderIndex,\n          rowComponent: trComponent,\n          cellComponent: tdComponent,\n          expandedKeys: expandedKeys,\n          onRow: onRow,\n          getRowKey: getRowKey,\n          rowExpandable: rowExpandable,\n          childrenColumnName: childrenColumnName,\n          indent: indent\n        });\n      });\n    } else {\n      rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n        expanded: true,\n        className: \"\".concat(prefixCls, \"-placeholder\"),\n        prefixCls: prefixCls,\n        component: trComponent,\n        cellComponent: tdComponent,\n        colSpan: flattenColumns.length,\n        isEmpty: true\n      }, emptyNode);\n    }\n\n    var columnsKey = getColumnsKey(flattenColumns);\n    return /*#__PURE__*/React.createElement(WrapperComponent, {\n      className: \"\".concat(prefixCls, \"-tbody\")\n    }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n      prefixCls: prefixCls,\n      columnsKey: columnsKey,\n      onColumnResize: onColumnResize\n    }), rows);\n  }, [data, prefixCls, onRow, measureColumnWidth, expandedKeys, getRowKey, getComponent, emptyNode, flattenColumns, childrenColumnName, onColumnResize, rowExpandable, flattenData]);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(HoverContext.Provider, {\n    value: {\n      startRow: startRow,\n      endRow: endRow,\n      onHover: onHover\n    }\n  }, bodyNode));\n}\n\nvar MemoBody = /*#__PURE__*/React.memo(Body);\nMemoBody.displayName = 'Body';\nexport default MemoBody;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,UAAU,MAAM,cAAc;AAErC,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,kBAAkB,GAAGH,IAAI,CAACG,kBAAkB;IAC5CC,YAAY,GAAGJ,IAAI,CAACI,YAAY;IAChCC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,aAAa,GAAGN,IAAI,CAACM,aAAa;IAClCC,SAAS,GAAGP,IAAI,CAACO,SAAS;IAC1BC,kBAAkB,GAAGR,IAAI,CAACQ,kBAAkB;EAEhD,IAAIC,iBAAiB,GAAGrB,KAAK,CAACsB,UAAU,CAACjB,aAAa,CAAC;IACnDkB,cAAc,GAAGF,iBAAiB,CAACE,cAAc;EAErD,IAAIC,kBAAkB,GAAGxB,KAAK,CAACsB,UAAU,CAACrB,YAAY,CAAC;IACnDwB,SAAS,GAAGD,kBAAkB,CAACC,SAAS;IACxCC,YAAY,GAAGF,kBAAkB,CAACE,YAAY;EAElD,IAAIC,kBAAkB,GAAG3B,KAAK,CAACsB,UAAU,CAACnB,WAAW,CAAC;IAClDyB,cAAc,GAAGD,kBAAkB,CAACC,cAAc;EAEtD,IAAIC,WAAW,GAAGtB,iBAAiB,CAACM,IAAI,EAAEO,kBAAkB,EAAEJ,YAAY,EAAEF,SAAS,CAAC,CAAC,CAAC;;EAExF,IAAIgB,OAAO,GAAG9B,KAAK,CAAC+B,MAAM,CAAC;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,eAAe,GAAGjC,KAAK,CAACkC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCC,gBAAgB,GAAGpC,cAAc,CAACkC,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAErC,IAAIG,gBAAgB,GAAGtC,KAAK,CAACkC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrCK,gBAAgB,GAAGxC,cAAc,CAACuC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEnC,IAAIG,OAAO,GAAG1C,KAAK,CAAC2C,WAAW,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACpDR,WAAW,CAACO,KAAK,CAAC;IAClBH,SAAS,CAACI,GAAG,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,IAAIC,QAAQ,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,YAAY;IACvC,IAAIC,gBAAgB,GAAGtB,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;IACjE,IAAIuB,WAAW,GAAGvB,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD,IAAIwB,WAAW,GAAGxB,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;IACtD,IAAIyB,IAAI;IAER,IAAItC,IAAI,CAACuC,MAAM,EAAE;MACfD,IAAI,GAAGtB,WAAW,CAACwB,GAAG,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;QAC1C,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;UACpBC,MAAM,GAAGH,IAAI,CAACG,MAAM;UACpBC,WAAW,GAAGJ,IAAI,CAACK,KAAK;QAC5B,IAAIC,GAAG,GAAG9C,SAAS,CAAC0C,MAAM,EAAED,GAAG,CAAC;QAChC,OAAO,aAAavD,KAAK,CAAC6D,aAAa,CAACvD,OAAO,EAAE;UAC/CsD,GAAG,EAAEA,GAAG;UACRE,MAAM,EAAEF,GAAG;UACXJ,MAAM,EAAEA,MAAM;UACdO,SAAS,EAAEH,GAAG;UACdD,KAAK,EAAEJ,GAAG;UACVG,WAAW,EAAEA,WAAW;UACxBM,YAAY,EAAEf,WAAW;UACzBgB,aAAa,EAAEf,WAAW;UAC1BlC,YAAY,EAAEA,YAAY;UAC1BC,KAAK,EAAEA,KAAK;UACZH,SAAS,EAAEA,SAAS;UACpBI,aAAa,EAAEA,aAAa;UAC5BE,kBAAkB,EAAEA,kBAAkB;UACtCqC,MAAM,EAAEA;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLN,IAAI,GAAG,aAAanD,KAAK,CAAC6D,aAAa,CAAC3D,WAAW,EAAE;QACnDgE,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC3C,SAAS,EAAE,cAAc,CAAC;QAC/CA,SAAS,EAAEA,SAAS;QACpB4C,SAAS,EAAEpB,WAAW;QACtBgB,aAAa,EAAEf,WAAW;QAC1BoB,OAAO,EAAE1C,cAAc,CAACwB,MAAM;QAC9BmB,OAAO,EAAE;MACX,CAAC,EAAEpD,SAAS,CAAC;IACf;IAEA,IAAIqD,UAAU,GAAGpE,aAAa,CAACwB,cAAc,CAAC;IAC9C,OAAO,aAAa5B,KAAK,CAAC6D,aAAa,CAACb,gBAAgB,EAAE;MACxDmB,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC3C,SAAS,EAAE,QAAQ;IAC1C,CAAC,EAAEV,kBAAkB,IAAI,aAAaf,KAAK,CAAC6D,aAAa,CAACnD,UAAU,EAAE;MACpEe,SAAS,EAAEA,SAAS;MACpB+C,UAAU,EAAEA,UAAU;MACtBjD,cAAc,EAAEA;IAClB,CAAC,CAAC,EAAE4B,IAAI,CAAC;EACX,CAAC,EAAE,CAACtC,IAAI,EAAEY,SAAS,EAAER,KAAK,EAAEF,kBAAkB,EAAEC,YAAY,EAAEF,SAAS,EAAEY,YAAY,EAAEP,SAAS,EAAES,cAAc,EAAER,kBAAkB,EAAEG,cAAc,EAAEL,aAAa,EAAEW,WAAW,CAAC,CAAC;EAClL,OAAO,aAAa7B,KAAK,CAAC6D,aAAa,CAACpD,WAAW,CAACgE,QAAQ,EAAE;IAC5DC,KAAK,EAAE5C,OAAO,CAAC6C;EACjB,CAAC,EAAE,aAAa3E,KAAK,CAAC6D,aAAa,CAACrD,YAAY,CAACiE,QAAQ,EAAE;IACzDC,KAAK,EAAE;MACLtC,QAAQ,EAAEA,QAAQ;MAClBI,MAAM,EAAEA,MAAM;MACdE,OAAO,EAAEA;IACX;EACF,CAAC,EAAEI,QAAQ,CAAC,CAAC;AACf;AAEA,IAAI8B,QAAQ,GAAG,aAAa5E,KAAK,CAAC6E,IAAI,CAAClE,IAAI,CAAC;AAC5CiE,QAAQ,CAACE,WAAW,GAAG,MAAM;AAC7B,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}