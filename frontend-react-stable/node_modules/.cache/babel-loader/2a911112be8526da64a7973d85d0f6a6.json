{"ast": null, "code": "import React,{useEffect}from'react';import{Layout,Menu,Button,Avatar,Dropdown,Space,Typography}from'antd';import'./MainLayout.css';import'./MenuFix.css';import{MenuFoldOutlined,MenuUnfoldOutlined,<PERSON><PERSON><PERSON>Outlined,ExperimentOutlined,AimOutlined,DatabaseOutlined,FileTextOutlined,SearchOutlined,UserOutlined,LogoutOutlined,SettingOutlined,ClockCircleOutlined}from'@ant-design/icons';import{useNavigate,useLocation}from'react-router-dom';import{useSelector,useDispatch}from'react-redux';import{logout,restoreAuth}from'../../store/slices/authSlice';import{toggleSidebar}from'../../store/slices/uiSlice';// import TaskStatusIndicator from '../TaskStatusIndicator';\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{<PERSON><PERSON>,Sider,Content}=Layout;const{Title}=Typography;const MainLayout=_ref=>{let{children}=_ref;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const{user}=useSelector(state=>state.auth);const{sidebarCollapsed}=useSelector(state=>state.ui);useEffect(()=>{// 恢复登录状态\ndispatch(restoreAuth());},[dispatch]);const menuItems=[{key:'/data-cleaning',icon:/*#__PURE__*/_jsx(BarChartOutlined,{}),label:'流量分析'},{key:'/model-training',icon:/*#__PURE__*/_jsx(ExperimentOutlined,{}),label:'模型训练'},{key:'/model-prediction',icon:/*#__PURE__*/_jsx(AimOutlined,{}),label:'模型预测'},{key:'/model-registry',icon:/*#__PURE__*/_jsx(DatabaseOutlined,{}),label:'模型仓库'},{key:'/clean-template',icon:/*#__PURE__*/_jsx(FileTextOutlined,{}),label:'清洗模板'},{key:'/data-query',icon:/*#__PURE__*/_jsx(SearchOutlined,{}),label:'数据查询'},{key:'/task-manager',icon:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),label:'任务管理'},{key:'/user-management',icon:/*#__PURE__*/_jsx(UserOutlined,{}),label:'用户管理'}];const handleMenuClick=key=>{navigate(key);};const handleLogout=()=>{dispatch(logout());navigate('/login');};const userMenuItems=[{key:'profile',icon:/*#__PURE__*/_jsx(UserOutlined,{}),label:'个人信息'},{key:'settings',icon:/*#__PURE__*/_jsx(SettingOutlined,{}),label:'设置'},{type:'divider'},{key:'logout',icon:/*#__PURE__*/_jsx(LogoutOutlined,{}),label:'退出登录',onClick:handleLogout}];return/*#__PURE__*/_jsxs(Layout,{style:{minHeight:'100vh'},children:[/*#__PURE__*/_jsxs(Sider,{trigger:null,collapsible:true,collapsed:sidebarCollapsed,width:200,style:{background:'#fff',boxShadow:'2px 0 8px rgba(0,0,0,0.1)',position:'fixed',left:0,top:64,bottom:0,zIndex:999,overflow:'hidden',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Menu,{mode:\"inline\",selectedKeys:[location.pathname],items:menuItems,onClick:_ref2=>{let{key}=_ref2;return handleMenuClick(key);},style:{border:'none',flex:1},className:sidebarCollapsed?'menu-collapsed':'menu-expanded'}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'16px',borderTop:'1px solid #f0f0f0',textAlign:'center'},children:/*#__PURE__*/_jsx(Button,{type:\"text\",icon:sidebarCollapsed?/*#__PURE__*/_jsx(MenuUnfoldOutlined,{}):/*#__PURE__*/_jsx(MenuFoldOutlined,{}),onClick:()=>dispatch(toggleSidebar()),className:\"sidebar-toggle-btn\",style:{fontSize:'16px',width:'100%',height:40,display:'flex',alignItems:'center',justifyContent:'center'},title:sidebarCollapsed?'展开菜单':'收起菜单'})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `})]}),/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsxs(Header,{style:{padding:'0 24px 0 24px',background:'#fff',display:'flex',alignItems:'center',justifyContent:'space-between',boxShadow:'0 2px 8px rgba(0,0,0,0.1)',position:'fixed',top:0,right:0,left:0,zIndex:1001,height:'64px'},children:[/*#__PURE__*/_jsx(Title,{level:3,style:{margin:0,color:'#000000',fontSize:'20px',fontWeight:600,letterSpacing:'0.5px',textShadow:'0 1px 2px rgba(0, 0, 0, 0.1)'},children:\"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsx(Space,{children:/*#__PURE__*/_jsx(Dropdown,{menu:{items:userMenuItems},placement:\"bottomRight\",arrow:true,children:/*#__PURE__*/_jsxs(Space,{style:{cursor:'pointer'},children:[/*#__PURE__*/_jsx(Avatar,{icon:/*#__PURE__*/_jsx(UserOutlined,{})}),/*#__PURE__*/_jsx(\"span\",{children:user===null||user===void 0?void 0:user.username})]})})})]}),/*#__PURE__*/_jsx(Content,{style:{margin:`88px 16px 24px ${(sidebarCollapsed?80:200)+16}px`,// 顶部留出Header的空间，左侧留出Sider的空间\npadding:24,background:'#fff',borderRadius:'8px',minHeight:'calc(100vh - 112px)',overflow:'hidden',// 确保子元素不会超出圆角边界\ntransition:'margin-left 0.2s'},children:children})]})]});};export default MainLayout;", "map": {"version": 3, "names": ["React", "useEffect", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "Dropdown", "Space", "Typography", "MenuFoldOutlined", "MenuUnfoldOutlined", "BarChartOutlined", "ExperimentOutlined", "AimOutlined", "DatabaseOutlined", "FileTextOutlined", "SearchOutlined", "UserOutlined", "LogoutOutlined", "SettingOutlined", "ClockCircleOutlined", "useNavigate", "useLocation", "useSelector", "useDispatch", "logout", "restoreAuth", "toggleSidebar", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "<PERSON><PERSON>", "Content", "Title", "MainLayout", "_ref", "children", "navigate", "location", "dispatch", "user", "state", "auth", "sidebarCollapsed", "ui", "menuItems", "key", "icon", "label", "handleMenuClick", "handleLogout", "userMenuItems", "type", "onClick", "style", "minHeight", "trigger", "collapsible", "collapsed", "width", "background", "boxShadow", "position", "left", "top", "bottom", "zIndex", "overflow", "display", "flexDirection", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "_ref2", "border", "flex", "className", "padding", "borderTop", "textAlign", "fontSize", "height", "alignItems", "justifyContent", "title", "right", "level", "margin", "color", "fontWeight", "letterSpacing", "textShadow", "menu", "placement", "arrow", "cursor", "username", "borderRadius", "transition"], "sources": ["/home/<USER>/frontend-react-stable/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  ExperimentOutlined,\n  AimOutlined,\n  DatabaseOutlined,\n  FileTextOutlined,\n  SearchOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  ClockCircleOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\n// import TaskStatusIndicator from '../TaskStatusIndicator';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const { user } = useSelector((state: RootState) => state.auth);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n\n\n\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n\n\n  const menuItems = [\n    {\n      key: '/data-cleaning',\n      icon: <BarChartOutlined />,\n      label: '流量分析',\n    },\n    {\n      key: '/model-training',\n      icon: <ExperimentOutlined />,\n      label: '模型训练',\n    },\n    {\n      key: '/model-prediction',\n      icon: <AimOutlined />,\n      label: '模型预测',\n    },\n    {\n      key: '/model-registry',\n      icon: <DatabaseOutlined />,\n      label: '模型仓库',\n    },\n    {\n      key: '/clean-template',\n      icon: <FileTextOutlined />,\n      label: '清洗模板',\n    },\n    {\n      key: '/data-query',\n      icon: <SearchOutlined />,\n      label: '数据查询',\n    },\n    {\n      key: '/task-manager',\n      icon: <ClockCircleOutlined />,\n      label: '任务管理',\n    },\n    {\n      key: '/user-management',\n      icon: <UserOutlined />,\n      label: '用户管理',\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={200}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          left: 0,\n          top: 64,\n          bottom: 0,\n          zIndex: 999,\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n        }}\n      >\n\n\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          style={{\n            border: 'none',\n            flex: 1,\n          }}\n          className={sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'}\n        />\n\n        {/* 回缩按钮移到侧边栏底部 */}\n        <div style={{\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center',\n        }}>\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => dispatch(toggleSidebar())}\n            className=\"sidebar-toggle-btn\"\n            style={{\n              fontSize: '16px',\n              width: '100%',\n              height: 40,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n            title={sidebarCollapsed ? '展开菜单' : '收起菜单'}\n          />\n        </div>\n\n        {/* 强制应用选中项样式 */}\n        <style>{`\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `}</style>\n\n      </Sider>\n\n      <Layout>\n        <Header style={{\n          padding: '0 24px 0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px',\n        }}>\n          <Title level={3} style={{\n            margin: 0,\n            color: '#000000',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n          }}>\n            AI智能清洗策略系统\n          </Title>\n\n          <Space>\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>{user?.username}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`, // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden', // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,IAAI,CAAEC,MAAM,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,UAAU,KAAQ,MAAM,CAChF,MAAO,kBAAkB,CACzB,MAAO,eAAe,CACtB,OACEC,gBAAgB,CAChBC,kBAAkB,CAClBC,gBAAgB,CAChBC,kBAAkB,CAClBC,WAAW,CACXC,gBAAgB,CAChBC,gBAAgB,CAChBC,cAAc,CACdC,YAAY,CACZC,cAAc,CACdC,eAAe,CACfC,mBAAmB,KACd,mBAAmB,CAC1B,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CAEtD,OAASC,MAAM,CAAEC,WAAW,KAAQ,8BAA8B,CAClE,OAASC,aAAa,KAAQ,4BAA4B,CAC1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEA,KAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGhC,MAAM,CACzC,KAAM,CAAEiC,KAAM,CAAC,CAAG3B,UAAU,CAM5B,KAAM,CAAA4B,UAAqC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACzD,KAAM,CAAAE,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAEkB,IAAK,CAAC,CAAGnB,WAAW,CAAEoB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAC9D,KAAM,CAAEC,gBAAiB,CAAC,CAAGtB,WAAW,CAAEoB,KAAgB,EAAKA,KAAK,CAACG,EAAE,CAAC,CAIxE7C,SAAS,CAAC,IAAM,CACd;AACAwC,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC,CACzB,CAAC,CAAE,CAACe,QAAQ,CAAC,CAAC,CAId,KAAM,CAAAM,SAAS,CAAG,CAChB,CACEC,GAAG,CAAE,gBAAgB,CACrBC,IAAI,cAAEpB,IAAA,CAAClB,gBAAgB,GAAE,CAAC,CAC1BuC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,iBAAiB,CACtBC,IAAI,cAAEpB,IAAA,CAACjB,kBAAkB,GAAE,CAAC,CAC5BsC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,mBAAmB,CACxBC,IAAI,cAAEpB,IAAA,CAAChB,WAAW,GAAE,CAAC,CACrBqC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,iBAAiB,CACtBC,IAAI,cAAEpB,IAAA,CAACf,gBAAgB,GAAE,CAAC,CAC1BoC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,iBAAiB,CACtBC,IAAI,cAAEpB,IAAA,CAACd,gBAAgB,GAAE,CAAC,CAC1BmC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,aAAa,CAClBC,IAAI,cAAEpB,IAAA,CAACb,cAAc,GAAE,CAAC,CACxBkC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,eAAe,CACpBC,IAAI,cAAEpB,IAAA,CAACT,mBAAmB,GAAE,CAAC,CAC7B8B,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,kBAAkB,CACvBC,IAAI,cAAEpB,IAAA,CAACZ,YAAY,GAAE,CAAC,CACtBiC,KAAK,CAAE,MACT,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAIH,GAAW,EAAK,CACvCT,QAAQ,CAACS,GAAG,CAAC,CACf,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzBX,QAAQ,CAAChB,MAAM,CAAC,CAAC,CAAC,CAClBc,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAED,KAAM,CAAAc,aAAa,CAAG,CACpB,CACEL,GAAG,CAAE,SAAS,CACdC,IAAI,cAAEpB,IAAA,CAACZ,YAAY,GAAE,CAAC,CACtBiC,KAAK,CAAE,MACT,CAAC,CACD,CACEF,GAAG,CAAE,UAAU,CACfC,IAAI,cAAEpB,IAAA,CAACV,eAAe,GAAE,CAAC,CACzB+B,KAAK,CAAE,IACT,CAAC,CACD,CACEI,IAAI,CAAE,SACR,CAAC,CACD,CACEN,GAAG,CAAE,QAAQ,CACbC,IAAI,cAAEpB,IAAA,CAACX,cAAc,GAAE,CAAC,CACxBgC,KAAK,CAAE,MAAM,CACbK,OAAO,CAAEH,YACX,CAAC,CACF,CAED,mBACErB,KAAA,CAAC7B,MAAM,EAACsD,KAAK,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAnB,QAAA,eACpCP,KAAA,CAACE,KAAK,EACJyB,OAAO,CAAE,IAAK,CACdC,WAAW,MACXC,SAAS,CAAEf,gBAAiB,CAC5BgB,KAAK,CAAE,GAAI,CACXL,KAAK,CAAE,CACLM,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,2BAA2B,CACtCC,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,CAAC,CACPC,GAAG,CAAE,EAAE,CACPC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,GAAG,CACXC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAjC,QAAA,eAIFT,IAAA,CAAC1B,IAAI,EACHqE,IAAI,CAAC,QAAQ,CACbC,YAAY,CAAE,CAACjC,QAAQ,CAACkC,QAAQ,CAAE,CAClCC,KAAK,CAAE5B,SAAU,CACjBQ,OAAO,CAAEqB,KAAA,MAAC,CAAE5B,GAAI,CAAC,CAAA4B,KAAA,OAAK,CAAAzB,eAAe,CAACH,GAAG,CAAC,EAAC,CAC3CQ,KAAK,CAAE,CACLqB,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,CACR,CAAE,CACFC,SAAS,CAAElC,gBAAgB,CAAG,gBAAgB,CAAG,eAAgB,CAClE,CAAC,cAGFhB,IAAA,QAAK2B,KAAK,CAAE,CACVwB,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,mBAAmB,CAC9BC,SAAS,CAAE,QACb,CAAE,CAAA5C,QAAA,cACAT,IAAA,CAACzB,MAAM,EACLkD,IAAI,CAAC,MAAM,CACXL,IAAI,CAAEJ,gBAAgB,cAAGhB,IAAA,CAACnB,kBAAkB,GAAE,CAAC,cAAGmB,IAAA,CAACpB,gBAAgB,GAAE,CAAE,CACvE8C,OAAO,CAAEA,CAAA,GAAMd,QAAQ,CAACd,aAAa,CAAC,CAAC,CAAE,CACzCoD,SAAS,CAAC,oBAAoB,CAC9BvB,KAAK,CAAE,CACL2B,QAAQ,CAAE,MAAM,CAChBtB,KAAK,CAAE,MAAM,CACbuB,MAAM,CAAE,EAAE,CACVd,OAAO,CAAE,MAAM,CACfe,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CACFC,KAAK,CAAE1C,gBAAgB,CAAG,MAAM,CAAG,MAAO,CAC3C,CAAC,CACC,CAAC,cAGNhB,IAAA,UAAAS,QAAA,CAAQ;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAQ,CAAC,EAEL,CAAC,cAERP,KAAA,CAAC7B,MAAM,EAAAoC,QAAA,eACLP,KAAA,CAACC,MAAM,EAACwB,KAAK,CAAE,CACbwB,OAAO,CAAE,eAAe,CACxBlB,UAAU,CAAE,MAAM,CAClBQ,OAAO,CAAE,MAAM,CACfe,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BvB,SAAS,CAAE,2BAA2B,CACtCC,QAAQ,CAAE,OAAO,CACjBE,GAAG,CAAE,CAAC,CACNsB,KAAK,CAAE,CAAC,CACRvB,IAAI,CAAE,CAAC,CACPG,MAAM,CAAE,IAAI,CACZgB,MAAM,CAAE,MACV,CAAE,CAAA9C,QAAA,eACAT,IAAA,CAACM,KAAK,EAACsD,KAAK,CAAE,CAAE,CAACjC,KAAK,CAAE,CACtBkC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,SAAS,CAChBR,QAAQ,CAAE,MAAM,CAChBS,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,OAAO,CACtBC,UAAU,CAAE,8BACd,CAAE,CAAAxD,QAAA,CAAC,oDAEH,CAAO,CAAC,cAERT,IAAA,CAACtB,KAAK,EAAA+B,QAAA,cACJT,IAAA,CAACvB,QAAQ,EACPyF,IAAI,CAAE,CAAEpB,KAAK,CAAEtB,aAAc,CAAE,CAC/B2C,SAAS,CAAC,aAAa,CACvBC,KAAK,MAAA3D,QAAA,cAELP,KAAA,CAACxB,KAAK,EAACiD,KAAK,CAAE,CAAE0C,MAAM,CAAE,SAAU,CAAE,CAAA5D,QAAA,eAClCT,IAAA,CAACxB,MAAM,EAAC4C,IAAI,cAAEpB,IAAA,CAACZ,YAAY,GAAE,CAAE,CAAE,CAAC,cAClCY,IAAA,SAAAS,QAAA,CAAOI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEyD,QAAQ,CAAO,CAAC,EACxB,CAAC,CACA,CAAC,CACN,CAAC,EACF,CAAC,cAETtE,IAAA,CAACK,OAAO,EAACsB,KAAK,CAAE,CACdkC,MAAM,CAAE,kBAAkB,CAAC7C,gBAAgB,CAAG,EAAE,CAAG,GAAG,EAAI,EAAE,IAAI,CAAE;AAClEmC,OAAO,CAAE,EAAE,CACXlB,UAAU,CAAE,MAAM,CAClBsC,YAAY,CAAE,KAAK,CACnB3C,SAAS,CAAE,qBAAqB,CAChCY,QAAQ,CAAE,QAAQ,CAAE;AACpBgC,UAAU,CAAE,kBACd,CAAE,CAAA/D,QAAA,CACCA,QAAQ,CACF,CAAC,EACJ,CAAC,EACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}