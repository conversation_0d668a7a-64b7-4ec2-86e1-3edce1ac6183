{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, InputNumber, Slider, Checkbox, Progress, Alert, Row, Col, Statistic } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay = ({\n  resultKey,\n  result\n}) => {\n  var _result$train_losses, _result$val_losses;\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"middle\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u534F\\u8BAE:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 17\n          }, this), \" \", selectedProt]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 17\n          }, this), \" \", selectedDatatype]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BAD\\u7EC3\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D4B\\u8BD5\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"R\\xB2 \\u5206\\u6570\",\n            value: result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A',\n            precision: 4,\n            valueStyle: {\n              color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C\",\n            value: result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A',\n            precision: 2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u4F7F\\u7528\\u7387\",\n            value: result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), result.train_losses && result.val_losses && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u8BAD\\u7EC3\\u635F\\u5931\\u66F2\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.train_losses.map((trainLoss, index) => ({\n                epoch: index + 1,\n                训练损失: trainLoss,\n                验证损失: result.val_losses[index] || null\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"epoch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u8BAD\\u7EC3\\u635F\\u5931\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9A8C\\u8BC1\\u635F\\u5931\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \", result.train_losses.length, \" epochs | \\u6700\\u7EC8\\u8BAD\\u7EC3\\u635F\\u5931: \", (_result$train_losses = result.train_losses[result.train_losses.length - 1]) === null || _result$train_losses === void 0 ? void 0 : _result$train_losses.toFixed(6), \" | \\u6700\\u7EC8\\u9A8C\\u8BC1\\u635F\\u5931: \", (_result$val_losses = result.val_losses[result.val_losses.length - 1]) === null || _result$val_losses === void 0 ? void 0 : _result$val_losses.toFixed(6)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B9E\\u9645\\u503C vs \\u9884\\u6D4B\\u503C\\u5BF9\\u6BD4\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.y_test_actual.map((actual, index) => ({\n                index: index + 1,\n                实际值: actual,\n                预测值: result.y_pred[index]\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"index\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u5B9E\\u9645\\u503C\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9884\\u6D4B\\u503C\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u663E\\u793A\\u6240\\u6709 \", result.y_test_actual.length, \" \\u4E2A\\u6D4B\\u8BD5\\u6837\\u672C\\u7684\\u9884\\u6D4B\\u5BF9\\u6BD4 | R\\xB2 \\u5206\\u6570: \", result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A', \" | \\u5EFA\\u8BAE\\u9608\\u503C: \", result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), result.model_save_path && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6A21\\u578B\\u6587\\u4EF6\\u4FE1\\u606F\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 20\n            }, this), \" \", result.model_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 17\n          }, this), result.scaler_y_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u51C6\\u5316\\u5668\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 22\n            }, this), \" \", result.scaler_y_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 19\n          }, this), result.params_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u53C2\\u6570\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 22\n            }, this), \" \", result.params_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 19\n          }, this), result.test_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6D4B\\u8BD5\\u6570\\u636E\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 22\n            }, this), \" \", result.test_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 19\n          }, this), result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 22\n            }, this), \" \", result.static_anomaly_threshold.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 19\n          }, this), result.finished_time && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 22\n            }, this), \" \", result.finished_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 19\n          }, this), result.duration_seconds && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u8017\\u65F6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 22\n            }, this), \" \", result.duration_seconds.toFixed(2), \" \\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_c = TrainingResultDisplay;\nconst ModelTrainingPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState(null);\n  const [selectedResultKey, setSelectedResultKey] = useState('');\n\n  // 任务管理\n  const {\n    submitTrainingTask,\n    getCompletedTasksByType\n  } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = prots => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot]) {\n        newDatatypes[prot] = [datatypeOptions[prot][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n    const hasValidDatatypes = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        const formData = new FormData();\n        if (dataSource === 'upload') {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedFile);\n        }\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 提交异步任务\n        const taskId = await submitTrainingTask(formData);\n        if (taskId) {\n          message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setTraining(false);\n          setProgress(0);\n        }\n        return; // 异步模式下直接返回\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n    } catch (error) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        var _error$response$data2, _error$response$data3;\n        if ((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.detail) {\n          errorMessage = error.response.data.detail;\n        } else if ((_error$response$data3 = error.response.data) !== null && _error$response$data3 !== void 0 && _error$response$data3.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: filesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedFile,\n                onChange: setSelectedFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            value: selectedProts,\n            onChange: handleProtocolChange,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n              value: prot,\n              children: prot\n            }, prot, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), selectedProts.map(prot => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: selectedDatatypes[prot] || [],\n            onChange: datatypes => handleDatatypeChange(prot, datatypes),\n            style: {\n              marginTop: 8\n            },\n            children: (datatypeOptions[prot] || []).map(datatype => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: datatype,\n              children: datatype\n            }, datatype, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this)]\n        }, prot, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 11\n      }, this),\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u57FA\\u7840\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5B66\\u4E60\\u7387\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: learningRate,\n                    onChange: value => setLearningRate(value || 0.0001),\n                    min: 0.0001,\n                    max: 1,\n                    step: 0.0001,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"0.0001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: batchSize,\n                    onChange: value => setBatchSize(value || 64),\n                    min: 1,\n                    max: 512,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: epochs,\n                    onChange: value => setEpochs(value || 100),\n                    min: 1,\n                    max: 1000,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: sequenceLength,\n                    onChange: value => setSequenceLength(value || 10),\n                    min: 1,\n                    max: 100,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: hiddenSize,\n                    onChange: value => setHiddenSize(value || 50),\n                    min: 10,\n                    max: 512,\n                    step: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5C42\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: numLayers,\n                    onChange: value => setNumLayers(value || 2),\n                    min: 1,\n                    max: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  align: \"middle\",\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"Dropout \\u6982\\u7387\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 813,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    style: {\n                      textAlign: 'right'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      code: true,\n                      children: dropout\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                  value: dropout,\n                  onChange: setDropout,\n                  min: 0,\n                  max: 0.9,\n                  step: 0.05,\n                  marks: {\n                    0: '0',\n                    0.2: '0.2',\n                    0.5: '0.5',\n                    0.9: '0.9'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              value: outputFolder,\n              onChange: e => setOutputFolder(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              size: \"large\",\n              prefix: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncTraining,\n            onChange: e => setUseAsyncTraining(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u8BAD\\u7EC3\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this), useAsyncTraining && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartTraining,\n        loading: training,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: training ? '正在训练...' : '开始训练预测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 9\n      }, this), training && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6240\\u6709\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 19\n            }, this), trainingResults.result_path && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u7ED3\\u679C\\u5DF2\\u66F4\\u65B0\\u81F3:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 24\n              }, this), \" \", trainingResults.result_path]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 21\n            }, this), Object.entries(trainingResults.results).map(([key, result]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u534F\\u8BAE\\u4E0E\\u6570\\u636E\\u7C7B\\u578B:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 26\n                }, this), \" \", key]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6A21\\u578B\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.model_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6807\\u51C6\\u5316\\u5668\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.scaler_y_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u67E5\\u770B\\u6A21\\u578B\\u8BAD\\u7EC3\\u53CA\\u7279\\u5F81\\u9884\\u6D4B\\u7ED3\\u679C\",\n        className: \"function-card\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedResultKey,\n              onChange: setSelectedResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(trainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), selectedResultKey && trainingResults.results[selectedResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedResultKey,\n            result: trainingResults.results[selectedResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 959,\n        columnNumber: 9\n      }, this), asyncTrainingResults && asyncTrainingResults.results && Object.keys(asyncTrainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n        className: \"function-card\",\n        style: {\n          marginTop: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\",\n          description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u4E0D\\u540C\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u7684\\u8BAD\\u7EC3\\u6548\\u679C\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAsyncResultKey,\n              onChange: setSelectedAsyncResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(asyncTrainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 13\n          }, this), selectedAsyncResultKey && asyncTrainingResults.results[selectedAsyncResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedAsyncResultKey,\n            result: asyncTrainingResults.results[selectedAsyncResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 909,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 554,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelTrainingPage, \"moAIehbZlEHRT4TxF8hWgLsJt4U=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelTrainingPage;\nexport default ModelTrainingPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TrainingResultDisplay\");\n$RefreshReg$(_c2, \"ModelTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "InboxOutlined", "PlayCircleOutlined", "SettingOutlined", "ExperimentOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelTrainingAPI", "useTaskManager", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "TrainingResultDisplay", "<PERSON><PERSON><PERSON>", "result", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "children", "direction", "size", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "title", "value", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "color", "static_anomaly_threshold", "marginTop", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_memory", "gpu_utilization_percent", "gpu_utilization", "train_losses", "val_losses", "strong", "height", "data", "map", "trainLoss", "index", "epoch", "训练损失", "验证损失", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "length", "y_test_actual", "y_pred", "actual", "实际值", "预测值", "model_save_path", "description", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "showIcon", "_c", "ModelTrainingPage", "_s", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "submitTrainingTask", "getCompletedTasksByType", "useAsyncTraining", "setUseAsyncTraining", "asyncTrainingResults", "setAsyncTrainingResults", "selectedAsyncResult<PERSON>ey", "setSelectedAsyncResultKey", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedTrainingTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "results", "Object", "keys", "latestTask", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "handleStartTraining", "hasValidDatatypes", "some", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "toString", "success", "progressInterval", "setInterval", "clearInterval", "trainModel", "localTrainingData", "csv_dir", "selected_file", "selected_prots", "selected_datatypes", "learning_rate", "batch_size", "sequence_length", "hidden_size", "num_layers", "output_folder", "trainModelLocal", "console", "log", "result_path", "errorMessage", "_error$response$data2", "_error$response$data3", "statusText", "status", "isFormValid", "level", "fontSize", "fontWeight", "marginBottom", "className", "Group", "e", "target", "compact", "display", "placeholder", "flex", "onClick", "loading", "disabled", "marginLeft", "spinning", "file", "mode", "datatype", "align", "min", "max", "step", "textAlign", "code", "marks", "prefix", "icon", "percent", "entries", "key", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"建议清洗阈值\"\n              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              precision={2}\n            />\n          </Col>\n        </Row>\n\n        <Row gutter={16} style={{ marginTop: 16 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"CPU使用率\"\n              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"内存使用\"\n              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU内存\"\n              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU使用率\"\n              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 任务管理\n  const { submitTrainingTask, getCompletedTasksByType } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState<any>(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState<string>('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload') {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedFile);\n        }\n\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 提交异步任务\n        const taskId = await submitTrainingTask(formData);\n\n        if (taskId) {\n          message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setTraining(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /home/<USER>\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={filesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card\n        title={\n          <Space>\n            <SettingOutlined />\n            <span>训练参数配置</span>\n          </Space>\n        }\n        className=\"function-card\"\n      >\n        <Row gutter={[24, 24]}>\n          {/* 基础参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <ExperimentOutlined />\n                  <Text strong>基础参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>学习率：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.0001)}\n                      min={0.0001}\n                      max={1}\n                      step={0.0001}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0001\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>批量大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 64)}\n                      min={1}\n                      max={512}\n                      style={{ width: '100%' }}\n                      placeholder=\"64\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>训练轮数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      min={1}\n                      max={1000}\n                      style={{ width: '100%' }}\n                      placeholder=\"100\"\n                    />\n                  </Col>\n                </Row>\n              </Space>\n            </Card>\n          </Col>\n\n          {/* 模型参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <SettingOutlined />\n                  <Text strong>模型参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>序列长度：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>隐藏层大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      min={10}\n                      max={512}\n                      step={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"50\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>层数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      min={1}\n                      max={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"2\"\n                    />\n                  </Col>\n                </Row>\n                <div style={{ width: '100%' }}>\n                  <Row align=\"middle\" style={{ marginBottom: 8 }}>\n                    <Col span={12}>\n                      <Text strong>Dropout 概率：</Text>\n                    </Col>\n                    <Col span={12} style={{ textAlign: 'right' }}>\n                      <Text code>{dropout}</Text>\n                    </Col>\n                  </Row>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    marks={{\n                      0: '0',\n                      0.2: '0.2',\n                      0.5: '0.5',\n                      0.9: '0.9'\n                    }}\n                  />\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 模型保存路径 */}\n        <Row style={{ marginTop: 24 }}>\n          <Col span={24}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <InboxOutlined />\n                  <Text strong>模型保存路径</Text>\n                </Space>\n              }\n            >\n              <Input\n                value={outputFolder}\n                onChange={(e) => setOutputFolder(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                size=\"large\"\n                prefix={<InboxOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 训练模式选择 */}\n      <Card className=\"function-card\" title=\"训练模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={useAsyncTraining}\n              onChange={(e) => setUseAsyncTraining(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value={true}>\n                <Space>\n                  异步训练（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步训练\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待训练完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncTraining && (\n            <Alert\n              message=\"异步训练模式\"\n              description={\n                <div>\n                  训练任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果 */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 异步训练结果展示 */}\n      {asyncTrainingResults && asyncTrainingResults.results && Object.keys(asyncTrainingResults.results).length > 0 && (\n        <Card title=\"异步训练结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步训练已完成\"\n            description=\"以下是后台训练任务的结果，您可以查看不同协议和数据类型的训练效果。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedAsyncResultKey}\n                onChange={setSelectedAsyncResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(asyncTrainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedAsyncResultKey && asyncTrainingResults.results[selectedAsyncResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedAsyncResultKey}\n                result={asyncTrainingResults.results[selectedAsyncResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,mBAAmB;AAC1G,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG9B,UAAU;AAClC,MAAM;EAAE+B;AAAQ,CAAC,GAAGnC,MAAM;AAC1B,MAAM;EAAEoC;AAAO,CAAC,GAAGlC,MAAM;;AAEzB;AACA,MAAMmC,qBAAmE,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAA,IAAAC,oBAAA,EAAAC,kBAAA;EACrG,MAAM,CAACC,YAAY,EAAEC,gBAAgB,CAAC,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EAEhE,oBACEZ,OAAA;IAAAa,QAAA,eACEb,OAAA,CAAC3B,KAAK;MAACyC,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACjEb,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAAAW,QAAA,gBAACb,OAAA;YAAAa,QAAA,EAAQ;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACX,YAAY;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDrB,OAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNrB,OAAA,CAACE,IAAI;UAAAW,QAAA,gBAACb,OAAA;YAAAa,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,gBAAgB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENrB,OAAA,CAAClB,GAAG;QAACwC,MAAM,EAAE,EAAG;QAAAT,QAAA,gBACdb,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACmB,WAAW,GAAG,GAAGnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAMnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACoB,UAAU,GAAG,GAAGpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,MAAMpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,oBAAO;YACbC,KAAK,EAAElB,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAO;YACjGE,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEC,KAAK,EAAE1B,MAAM,CAACqB,QAAQ,GAAG,GAAG,IAAIrB,MAAM,CAACuB,EAAE,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM;YAC5FE,SAAS,EAAE;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrB,OAAA,CAAClB,GAAG;QAACwC,MAAM,EAAE,EAAG;QAACN,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACxCb,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC6B,WAAW,GAAG,GAAG7B,MAAM,CAAC6B,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAM;YACxEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAElB,MAAM,CAAC8B,SAAS,GAAG,GAAG9B,MAAM,CAAC8B,SAAS,CAACR,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAM;YACtEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAElB,MAAM,CAAC+B,aAAa,GAAG,GAAG/B,MAAM,CAAC+B,aAAa,CAACT,OAAO,CAAC,CAAC,CAAC,KAAK,GAAItB,MAAM,CAACgC,UAAU,GAAG,GAAGhC,MAAM,CAACgC,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAO;YAC3IG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXb,OAAA,CAAChB,SAAS;YACRwC,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAACiC,uBAAuB,GAAG,GAAGjC,MAAM,CAACiC,uBAAuB,CAACX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAItB,MAAM,CAACkC,eAAe,GAAG,GAAGlC,MAAM,CAACkC,eAAe,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAO;YACrKG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELd,MAAM,CAACmC,YAAY,IAAInC,MAAM,CAACoC,UAAU,iBACvC3C,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAAC0C,MAAM;UAAA/B,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BrB,OAAA;UAAKgB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCb,OAAA,CAACJ,mBAAmB;YAACqB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cb,OAAA,CAACX,SAAS;cACRyD,IAAI,EAAEvC,MAAM,CAACmC,YAAY,CAACK,GAAG,CAAC,CAACC,SAAiB,EAAEC,KAAa,MAAM;gBACnEC,KAAK,EAAED,KAAK,GAAG,CAAC;gBAChBE,IAAI,EAAEH,SAAS;gBACfI,IAAI,EAAE7C,MAAM,CAACoC,UAAU,CAACM,KAAK,CAAC,IAAI;cACpC,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDb,OAAA,CAACP,aAAa;gBAACiE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCrB,OAAA,CAACT,KAAK;gBAACoE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBrB,OAAA,CAACR,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTrB,OAAA,CAACN,OAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrB,OAAA,CAACL,MAAM;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVrB,OAAA,CAACV,IAAI;gBACHsE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFrB,OAAA,CAACV,IAAI;gBACHsE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrB,OAAA;UAAKgB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bb,OAAA,CAACE,IAAI;YAAC0D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,4BACf,EAACN,MAAM,CAACmC,YAAY,CAACsB,MAAM,EAAC,kDAC1B,GAAAxD,oBAAA,GAACD,MAAM,CAACmC,YAAY,CAACnC,MAAM,CAACmC,YAAY,CAACsB,MAAM,GAAG,CAAC,CAAC,cAAAxD,oBAAA,uBAAnDA,oBAAA,CAAqDqB,OAAO,CAAC,CAAC,CAAC,EAAC,2CACjE,GAAApB,kBAAA,GAACF,MAAM,CAACoC,UAAU,CAACpC,MAAM,CAACoC,UAAU,CAACqB,MAAM,GAAG,CAAC,CAAC,cAAAvD,kBAAA,uBAA/CA,kBAAA,CAAiDoB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAd,MAAM,CAAC0D,aAAa,IAAI1D,MAAM,CAAC2D,MAAM,IAAI3D,MAAM,CAAC0D,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvEhE,OAAA;QAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;UAAC0C,MAAM;UAAA/B,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCrB,OAAA;UAAKgB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCb,OAAA,CAACJ,mBAAmB;YAACqB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cb,OAAA,CAACX,SAAS;cACRyD,IAAI,EAAEvC,MAAM,CAAC0D,aAAa,CAAClB,GAAG,CAAC,CAACoB,MAAc,EAAElB,KAAa,MAAM;gBACjEA,KAAK,EAAEA,KAAK,GAAG,CAAC;gBAChBmB,GAAG,EAAED,MAAM;gBACXE,GAAG,EAAE9D,MAAM,CAAC2D,MAAM,CAACjB,KAAK;cAC1B,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDb,OAAA,CAACP,aAAa;gBAACiE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCrB,OAAA,CAACT,KAAK;gBAACoE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBrB,OAAA,CAACR,KAAK;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTrB,OAAA,CAACN,OAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrB,OAAA,CAACL,MAAM;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVrB,OAAA,CAACV,IAAI;gBACHsE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFrB,OAAA,CAACV,IAAI;gBACHsE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrB,OAAA;UAAKgB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bb,OAAA,CAACE,IAAI;YAAC0D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,2BAChB,EAACN,MAAM,CAAC0D,aAAa,CAACD,MAAM,EAAC,sFAC3B,EAACzD,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM,EAAC,+BAC5F,EAACtB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAIAd,MAAM,CAAC+D,eAAe,iBACrBtE,OAAA,CAACnB,KAAK;QACJN,OAAO,EAAC,sCAAQ;QAChBgG,WAAW,eACTvE,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC+D,eAAe;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvDd,MAAM,CAACiE,kBAAkB,iBACxBxE,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACiE,kBAAkB;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC7D,EACAd,MAAM,CAACkE,gBAAgB,iBACtBzE,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACkE,gBAAgB;UAAA;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAACmE,cAAc,iBACpB1E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACmE,cAAc;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAAC2B,wBAAwB,iBAC9BlC,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5E,EACAd,MAAM,CAACoE,aAAa,iBACnB3E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACoE,aAAa;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACtD,EACAd,MAAM,CAACqE,gBAAgB,iBACtB5E,OAAA;YAAAa,QAAA,gBAAGb,OAAA;cAAAa,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACqE,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDuC,IAAI,EAAC,MAAM;QACXiB,QAAQ;MAAA;QAAA3D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACyD,EAAA,GAtMIzE,qBAAmE;AAwMzE,MAAM0E,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtH,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAACuH,YAAY,EAAEC,eAAe,CAAC,GAAGxH,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAACyH,MAAM,EAAEC,SAAS,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2H,cAAc,EAAEC,iBAAiB,CAAC,GAAG5H,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC6H,YAAY,EAAEC,eAAe,CAAC,GAAG9H,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACiI,aAAa,EAAEC,gBAAgB,CAAC,GAAGlI,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAACmI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpI,QAAQ,CAA4B;IACpFqI,GAAG,EAAE,CAAC,aAAa;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvI,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAACwI,SAAS,EAAEC,YAAY,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0I,MAAM,EAAEC,SAAS,CAAC,GAAG3I,QAAQ,CAAC,GAAG,CAAC;EACzC,MAAM,CAAC4I,cAAc,EAAEC,iBAAiB,CAAC,GAAG7I,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8I,UAAU,EAAEC,aAAa,CAAC,GAAG/I,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgJ,SAAS,EAAEC,YAAY,CAAC,GAAGjJ,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkJ,OAAO,EAAEC,UAAU,CAAC,GAAGnJ,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACoJ,YAAY,EAAEC,eAAe,CAAC,GAAGrJ,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAACsJ,QAAQ,EAAEC,WAAW,CAAC,GAAGvJ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwJ,QAAQ,EAAEC,WAAW,CAAC,GAAGzJ,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC0J,eAAe,EAAEC,kBAAkB,CAAC,GAAG3J,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAAC4J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7J,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAM;IAAE8J,kBAAkB;IAAEC;EAAwB,CAAC,GAAG7H,cAAc,CAAC,CAAC;EACxE,MAAM,CAAC8H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM,CAACkK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnK,QAAQ,CAAM,IAAI,CAAC;EAC3E,MAAM,CAACoK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrK,QAAQ,CAAS,EAAE,CAAC;EAChF,MAAM,CAACsK,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvK,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAMwK,sBAAsB,GAAGT,uBAAuB,CAAC,UAAU,CAAC;;EAElE;EACA,MAAMU,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,sBAAsB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IACjF,IAAIC,YAAY,IAAIA,YAAY,CAAChI,MAAM,IAAIgI,YAAY,CAAChI,MAAM,CAACoI,OAAO,EAAE;MACtEZ,uBAAuB,CAACQ,YAAY,CAAChI,MAAM,CAAC;MAC5C0H,yBAAyB,CAACW,MAAM,CAACC,IAAI,CAACN,YAAY,CAAChI,MAAM,CAACoI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA9K,SAAS,CAAC,MAAM;IACd,IAAIuK,sBAAsB,CAACpE,MAAM,GAAG,CAAC,IAAI,CAACkE,mBAAmB,EAAE;MAC7D,MAAMY,UAAU,GAAGV,sBAAsB,CAACA,sBAAsB,CAACpE,MAAM,GAAG,CAAC,CAAC;MAC5EqE,qBAAqB,CAACS,UAAU,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,sBAAsB,EAAEF,mBAAmB,CAAC,CAAC;;EAEjD;EACA,MAAMa,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtB/C,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjEgD,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC9D,MAAM,EAAE;IAEbO,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMwD,QAAQ,GAAG,MAAMvJ,gBAAgB,CAACwJ,YAAY,CAAChE,MAAM,CAAC;MAC5DG,iBAAiB,CAAC4D,QAAQ,CAACtG,IAAI,CAACwG,KAAK,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBlL,OAAO,CAACgL,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1G,IAAI,cAAA2G,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzDlE,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA/H,SAAS,CAAC,MAAM;IACd,IAAIoH,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAACrB,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAM2F,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BT,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMU,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC1E,UAAU,EAAEI,MAAM,CAAC,CAAC;;EAExB;EACA,MAAMyE,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACrG,MAAM,GAAG,CAAC,EAAE;QAC5BoB,eAAe,CAACgF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLjF,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMkF,oBAAoB,GAAIC,KAAe,IAAK;IAChDzE,gBAAgB,CAACyE,KAAK,CAAC;IACvB;IACA,MAAMC,YAAY,GAAG;MAAE,GAAGzE;IAAkB,CAAC;IAC7CwE,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpB,IAAI,CAACF,YAAY,CAACE,IAAI,CAAC,IAAI1B,eAAe,CAAC0B,IAAI,CAAiC,EAAE;QAChFF,YAAY,CAACE,IAAI,CAAC,GAAG,CAAC1B,eAAe,CAAC0B,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC;MACjF;IACF,CAAC,CAAC;IACF;IACA9B,MAAM,CAACC,IAAI,CAAC2B,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACD,IAAI,CAAC,EAAE;QACzB,OAAOF,YAAY,CAACE,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;IACF1E,oBAAoB,CAACwE,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtE9E,oBAAoB,CAAC+E,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IAAI/F,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;MAC5C5G,OAAO,CAACgL,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAItE,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,YAAY,CAAC,EAAE;MACxDlH,OAAO,CAACgL,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAI1D,aAAa,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAC9BzF,OAAO,CAACgL,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,MAAM0B,iBAAiB,GAAGpF,aAAa,CAACqF,IAAI,CAACR,IAAI,IAC/C3E,iBAAiB,CAAC2E,IAAI,CAAC,IAAI3E,iBAAiB,CAAC2E,IAAI,CAAC,CAAC1G,MAAM,GAAG,CAC9D,CAAC;IAED,IAAI,CAACiH,iBAAiB,EAAE;MACtB1M,OAAO,CAACgL,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF;IAEApC,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;IAExB,IAAI;MACF,IAAIG,gBAAgB,EAAE;QACpB;QACA,MAAMuD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAInG,UAAU,KAAK,QAAQ,EAAE;UAC3BkG,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElG,YAAY,CAACmG,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhG,MAAM,CAAC;UAClC8F,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE5F,YAAY,CAAC;QAChD;QAEA0F,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC3F,aAAa,CAAC,CAAC;QAChEsF,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACzF,iBAAiB,CAAC,CAAC;QACxEoF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnF,YAAY,CAACuF,QAAQ,CAAC,CAAC,CAAC;QACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEjF,SAAS,CAACqF,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/E,MAAM,CAACmF,QAAQ,CAAC,CAAC,CAAC;QAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE7E,cAAc,CAACiF,QAAQ,CAAC,CAAC,CAAC;QAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE3E,UAAU,CAAC+E,QAAQ,CAAC,CAAC,CAAC;QACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEzE,SAAS,CAAC6E,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvE,OAAO,CAAC2E,QAAQ,CAAC,CAAC,CAAC;QAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAErE,YAAY,CAAC;;QAE9C;QACA,MAAMsB,MAAM,GAAG,MAAMZ,kBAAkB,CAACyD,QAAQ,CAAC;QAEjD,IAAI7C,MAAM,EAAE;UACV/J,OAAO,CAACmN,OAAO,CAAC,gCAAgC,CAAC;UACjD;UACAvE,WAAW,CAAC,KAAK,CAAC;UAClBE,WAAW,CAAC,CAAC,CAAC;QAChB;QAEA,OAAO,CAAC;MACV;;MAEA;MACA,IAAI+B,QAAQ;MAEZ,IAAInE,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMkG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElG,YAAY,CAACmG,aAAa,CAAC;QACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAAC3F,aAAa,CAAC,CAAC;QAChEsF,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAACzF,iBAAiB,CAAC,CAAC;QACxEoF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnF,YAAY,CAACuF,QAAQ,CAAC,CAAC,CAAC;QACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEjF,SAAS,CAACqF,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE/E,MAAM,CAACmF,QAAQ,CAAC,CAAC,CAAC;QAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE7E,cAAc,CAACiF,QAAQ,CAAC,CAAC,CAAC;QAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE3E,UAAU,CAAC+E,QAAQ,CAAC,CAAC,CAAC;QACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEzE,SAAS,CAAC6E,QAAQ,CAAC,CAAC,CAAC;QACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEvE,OAAO,CAAC2E,QAAQ,CAAC,CAAC,CAAC;QAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAErE,YAAY,CAAC;;QAE9C;QACA,MAAM2E,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCvE,WAAW,CAAE0D,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdc,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOZ,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAER3B,QAAQ,GAAG,MAAMvJ,gBAAgB,CAACiM,UAAU,CAACX,QAAQ,CAAC;QACtDU,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMI,iBAAiB,GAAG;UACxBC,OAAO,EAAE3G,MAAM;UACf4G,aAAa,EAAExG,YAAY;UAC3ByG,cAAc,EAAErG,aAAa;UAC7BsG,kBAAkB,EAAEpG,iBAAiB;UACrCqG,aAAa,EAAElG,YAAY;UAC3BmG,UAAU,EAAEjG,SAAS;UACrBE,MAAM,EAAEA,MAAM;UACdgG,eAAe,EAAE9F,cAAc;UAC/B+F,WAAW,EAAE7F,UAAU;UACvB8F,UAAU,EAAE5F,SAAS;UACrBE,OAAO,EAAEA,OAAO;UAChB2F,aAAa,EAAEzF;QACjB,CAAC;;QAED;QACA,MAAM2E,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCvE,WAAW,CAAE0D,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdc,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOZ,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAER3B,QAAQ,GAAG,MAAMvJ,gBAAgB,CAAC6M,eAAe,CAACX,iBAAiB,CAAC;QACpEF,aAAa,CAACF,gBAAgB,CAAC;MACjC;MAEAtE,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACAsF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAExD,QAAQ,CAACtG,IAAI,CAAC;MAErCyE,kBAAkB,CAAC6B,QAAQ,CAACtG,IAAI,CAAC;;MAEjC;MACA,IAAIsG,QAAQ,CAACtG,IAAI,CAAC6F,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAACtG,IAAI,CAAC6F,OAAO,CAAC,CAAC3E,MAAM,GAAG,CAAC,EAAE;QAC1EyD,oBAAoB,CAACmB,MAAM,CAACC,IAAI,CAACO,QAAQ,CAACtG,IAAI,CAAC6F,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;MAEApK,OAAO,CAACmN,OAAO,CAAC,SAAS,CAAC;;MAE1B;MACA,IAAItC,QAAQ,CAACtG,IAAI,CAAC+J,WAAW,EAAE;QAC7BtO,OAAO,CAAC6L,IAAI,CAAC,WAAWhB,QAAQ,CAACtG,IAAI,CAAC+J,WAAW,EAAE,CAAC;MACtD;IAEF,CAAC,CAAC,OAAOtD,KAAU,EAAE;MACnBoD,OAAO,CAACpD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BoD,OAAO,CAACpD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACH,QAAQ,CAAC;;MAEtC;MACA,IAAI0D,YAAY,GAAG,QAAQ;MAC3B,IAAIvD,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAA2D,qBAAA,EAAAC,qBAAA;QAClB,KAAAD,qBAAA,GAAIxD,KAAK,CAACH,QAAQ,CAACtG,IAAI,cAAAiK,qBAAA,eAAnBA,qBAAA,CAAqBrD,MAAM,EAAE;UAC/BoD,YAAY,GAAGvD,KAAK,CAACH,QAAQ,CAACtG,IAAI,CAAC4G,MAAM;QAC3C,CAAC,MAAM,KAAAsD,qBAAA,GAAIzD,KAAK,CAACH,QAAQ,CAACtG,IAAI,cAAAkK,qBAAA,eAAnBA,qBAAA,CAAqBzO,OAAO,EAAE;UACvCuO,YAAY,GAAGvD,KAAK,CAACH,QAAQ,CAACtG,IAAI,CAACvE,OAAO;QAC5C,CAAC,MAAM,IAAIgL,KAAK,CAACH,QAAQ,CAAC6D,UAAU,EAAE;UACpCH,YAAY,GAAG,SAASvD,KAAK,CAACH,QAAQ,CAAC8D,MAAM,IAAI3D,KAAK,CAACH,QAAQ,CAAC6D,UAAU,EAAE;QAC9E;MACF,CAAC,MAAM,IAAI1D,KAAK,CAAChL,OAAO,EAAE;QACxBuO,YAAY,GAAGvD,KAAK,CAAChL,OAAO;MAC9B;MAEAA,OAAO,CAACgL,KAAK,CAACuD,YAAY,CAAC;IAC7B,CAAC,SAAS;MACR3F,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMgG,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIlI,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOE,YAAY,IAAIU,aAAa,CAAC7B,MAAM,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAOqB,MAAM,IAAII,YAAY,IAAII,aAAa,CAAC7B,MAAM,GAAG,CAAC;IAC3D;EACF,CAAC;EAED,oBACEhE,OAAA;IAAAa,QAAA,gBACEb,OAAA,CAACC,KAAK;MAACmN,KAAK,EAAE,CAAE;MAACpM,KAAK,EAAE;QAAEqM,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAA1M,QAAA,EAAC;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACrGrB,OAAA,CAACE,IAAI;MAAC0D,IAAI,EAAC,WAAW;MAAA/C,QAAA,EAAC;IAEvB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPrB,OAAA,CAAC1B,OAAO;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXrB,OAAA,CAAClC,IAAI;MAAC0D,KAAK,EAAC,oBAAK;MAACgM,SAAS,EAAC,eAAe;MAAA3M,QAAA,eACzCb,OAAA,CAAC3B,KAAK;QAACyC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAAC0C,MAAM;YAAA/B,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BrB,OAAA,CAACjC,KAAK,CAAC0P,KAAK;YACVhM,KAAK,EAAEwD,UAAW;YAClBkF,QAAQ,EAAGuD,CAAC,IAAKxI,aAAa,CAACwI,CAAC,CAACC,MAAM,CAAClM,KAAK,CAAE;YAC/CT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBb,OAAA,CAACjC,KAAK;cAAC0D,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCrB,OAAA,CAACjC,KAAK;cAAC0D,KAAK,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL4D,UAAU,KAAK,QAAQ,iBACtBjF,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAAC0C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBrB,OAAA,CAACG,OAAO;YAAA,GAAK2J,WAAW;YAAE9I,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAChDb,OAAA;cAAGwN,SAAS,EAAC,sBAAsB;cAAA3M,QAAA,eACjCb,OAAA,CAACf,aAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJrB,OAAA;cAAGwN,SAAS,EAAC,iBAAiB;cAAA3M,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDrB,OAAA;cAAGwN,SAAS,EAAC,iBAAiB;cAAA3M,QAAA,EAAC;YAE/B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGA4D,UAAU,KAAK,OAAO,iBACrBjF,OAAA,CAAC3B,KAAK;UAACyC,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBACnDb,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAAC0C,MAAM;cAAA/B,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BrB,OAAA,CAAC/B,KAAK,CAACwP,KAAK;cAACG,OAAO;cAAC5M,KAAK,EAAE;gBAAEmB,SAAS,EAAE,CAAC;gBAAE0L,OAAO,EAAE;cAAO,CAAE;cAAAhN,QAAA,gBAC5Db,OAAA,CAAC/B,KAAK;gBACJwD,KAAK,EAAE4D,MAAO;gBACd8E,QAAQ,EAAGuD,CAAC,IAAKpI,SAAS,CAACoI,CAAC,CAACC,MAAM,CAAClM,KAAK,CAAE;gBAC3CqM,WAAW,EAAC,4BAAkB;gBAC9B9M,KAAK,EAAE;kBAAE+M,IAAI,EAAE;gBAAE;cAAE;gBAAA7M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFrB,OAAA,CAAC7B,MAAM;gBACLyF,IAAI,EAAC,SAAS;gBACdoK,OAAO,EAAE7E,aAAc;gBACvB8E,OAAO,EAAEtI,YAAa;gBACtBuI,QAAQ,EAAE,CAAC7I,MAAO;gBAClBrE,KAAK,EAAE;kBAAEmN,UAAU,EAAE;gBAAE,CAAE;gBAAAtN,QAAA,EAC1B;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENrB,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAAC0C,MAAM;cAAA/B,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBrB,OAAA,CAACxB,IAAI;cAAC4P,QAAQ,EAAEzI,YAAa;cAAA9E,QAAA,eAC3Bb,OAAA,CAAC9B,MAAM;gBACLuD,KAAK,EAAEgE,YAAa;gBACpB0E,QAAQ,EAAEzE,eAAgB;gBAC1BoI,WAAW,EAAC,mCAAU;gBACtB9M,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEkB,SAAS,EAAE;gBAAE,CAAE;gBACvC8L,OAAO,EAAEtI,YAAa;gBAAA9E,QAAA,EAErB0E,cAAc,CAACxC,GAAG,CAAEsL,IAAI,iBACvBrO,OAAA,CAACI,MAAM;kBAAYqB,KAAK,EAAE4M,IAAK;kBAAAxN,QAAA,EAC5BwN;gBAAI,GADMA,IAAI;kBAAAnN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAClC,IAAI;MAAC0D,KAAK,EAAC,wDAAW;MAACgM,SAAS,EAAC,eAAe;MAAA3M,QAAA,eAC/Cb,OAAA,CAAC3B,KAAK;QAACyC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAAC0C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBrB,OAAA,CAAC9B,MAAM;YACLoQ,IAAI,EAAC,UAAU;YACf7M,KAAK,EAAEoE,aAAc;YACrBsE,QAAQ,EAAEG,oBAAqB;YAC/BwD,WAAW,EAAC,gCAAO;YACnB9M,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEtCkI,eAAe,CAAChG,GAAG,CAAE2H,IAAI,iBACxB1K,OAAA,CAACI,MAAM;cAAYqB,KAAK,EAAEiJ,IAAK;cAAA7J,QAAA,EAC5B6J;YAAI,GADMA,IAAI;cAAAxJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELwE,aAAa,CAAC9C,GAAG,CAAE2H,IAAI,iBACtB1K,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAAC0C,MAAM;YAAA/B,QAAA,GAAE6J,IAAI,EAAC,iCAAM;UAAA;YAAAxJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCrB,OAAA,CAACrB,QAAQ,CAAC8O,KAAK;YACbhM,KAAK,EAAEsE,iBAAiB,CAAC2E,IAAI,CAAC,IAAI,EAAG;YACrCP,QAAQ,EAAGW,SAAS,IAAKF,oBAAoB,CAACF,IAAI,EAAEI,SAAqB,CAAE;YAC3E9J,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEvB,CAACmI,eAAe,CAAC0B,IAAI,CAAiC,IAAI,EAAE,EAAE3H,GAAG,CAAEwL,QAAQ,iBAC1EvO,OAAA,CAACrB,QAAQ;cAAgB8C,KAAK,EAAE8M,QAAS;cAAA1N,QAAA,EACtC0N;YAAQ,GADIA,QAAQ;cAAArN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA,GAZTqJ,IAAI;UAAAxJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAClC,IAAI;MACH0D,KAAK,eACHxB,OAAA,CAAC3B,KAAK;QAAAwC,QAAA,gBACJb,OAAA,CAACb,eAAe;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnBrB,OAAA;UAAAa,QAAA,EAAM;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACR;MACDmM,SAAS,EAAC,eAAe;MAAA3M,QAAA,gBAEzBb,OAAA,CAAClB,GAAG;QAACwC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAEpBb,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZb,OAAA,CAAClC,IAAI;YACHiD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHxB,OAAA,CAAC3B,KAAK;cAAAwC,QAAA,gBACJb,OAAA,CAACZ,kBAAkB;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBrB,OAAA,CAACE,IAAI;gBAAC0C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDb,OAAA,CAAC3B,KAAK;cAACyC,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEb,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAEyE,YAAa;oBACpBiE,QAAQ,EAAG1I,KAAK,IAAK0E,eAAe,CAAC1E,KAAK,IAAI,MAAM,CAAE;oBACtDgN,GAAG,EAAE,MAAO;oBACZC,GAAG,EAAE,CAAE;oBACPC,IAAI,EAAE,MAAO;oBACb3N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAQ;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAE2E,SAAU;oBACjB+D,QAAQ,EAAG1I,KAAK,IAAK4E,YAAY,CAAC5E,KAAK,IAAI,EAAE,CAAE;oBAC/CgN,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACT1N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAI;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAE6E,MAAO;oBACd6D,QAAQ,EAAG1I,KAAK,IAAK8E,SAAS,CAAC9E,KAAK,IAAI,GAAG,CAAE;oBAC7CgN,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACV1N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAK;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNrB,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZb,OAAA,CAAClC,IAAI;YACHiD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHxB,OAAA,CAAC3B,KAAK;cAAAwC,QAAA,gBACJb,OAAA,CAACb,eAAe;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBrB,OAAA,CAACE,IAAI;gBAAC0C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDb,OAAA,CAAC3B,KAAK;cAACyC,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEb,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAE+E,cAAe;oBACtB2D,QAAQ,EAAG1I,KAAK,IAAKgF,iBAAiB,CAAChF,KAAK,IAAI,EAAE,CAAE;oBACpDgN,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACT1N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAI;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAEiF,UAAW;oBAClByD,QAAQ,EAAG1I,KAAK,IAAKkF,aAAa,CAAClF,KAAK,IAAI,EAAE,CAAE;oBAChDgN,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTC,IAAI,EAAE,EAAG;oBACT3N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAI;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAAClB,GAAG;gBAAC0P,KAAK,EAAC,QAAQ;gBAAA3N,QAAA,gBACjBb,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;oBAAC0C,MAAM;oBAAA/B,QAAA,EAAC;kBAAG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACNrB,OAAA,CAACjB,GAAG;kBAACwC,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZb,OAAA,CAACvB,WAAW;oBACVgD,KAAK,EAAEmF,SAAU;oBACjBuD,QAAQ,EAAG1I,KAAK,IAAKoF,YAAY,CAACpF,KAAK,IAAI,CAAC,CAAE;oBAC9CgN,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACR1N,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6M,WAAW,EAAC;kBAAG;oBAAA5M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA;gBAAKgB,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,gBAC5Bb,OAAA,CAAClB,GAAG;kBAAC0P,KAAK,EAAC,QAAQ;kBAACxN,KAAK,EAAE;oBAAEuM,YAAY,EAAE;kBAAE,CAAE;kBAAA1M,QAAA,gBAC7Cb,OAAA,CAACjB,GAAG;oBAACwC,IAAI,EAAE,EAAG;oBAAAV,QAAA,eACZb,OAAA,CAACE,IAAI;sBAAC0C,MAAM;sBAAA/B,QAAA,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNrB,OAAA,CAACjB,GAAG;oBAACwC,IAAI,EAAE,EAAG;oBAACP,KAAK,EAAE;sBAAE4N,SAAS,EAAE;oBAAQ,CAAE;oBAAA/N,QAAA,eAC3Cb,OAAA,CAACE,IAAI;sBAAC2O,IAAI;sBAAAhO,QAAA,EAAEiG;oBAAO;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrB,OAAA,CAACtB,MAAM;kBACL+C,KAAK,EAAEqF,OAAQ;kBACfqD,QAAQ,EAAEpD,UAAW;kBACrB0H,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,IAAK;kBACXG,KAAK,EAAE;oBACL,CAAC,EAAE,GAAG;oBACN,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE;kBACP;gBAAE;kBAAA5N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrB,OAAA,CAAClB,GAAG;QAACkC,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bb,OAAA,CAACjB,GAAG;UAACwC,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZb,OAAA,CAAClC,IAAI;YACHiD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHxB,OAAA,CAAC3B,KAAK;cAAAwC,QAAA,gBACJb,OAAA,CAACf,aAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBrB,OAAA,CAACE,IAAI;gBAAC0C,MAAM;gBAAA/B,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACR;YAAAR,QAAA,eAEDb,OAAA,CAAC/B,KAAK;cACJwD,KAAK,EAAEuF,YAAa;cACpBmD,QAAQ,EAAGuD,CAAC,IAAKzG,eAAe,CAACyG,CAAC,CAACC,MAAM,CAAClM,KAAK,CAAE;cACjDqM,WAAW,EAAC,4BAAkB;cAC9B/M,IAAI,EAAC,OAAO;cACZgO,MAAM,eAAE/O,OAAA,CAACf,aAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPrB,OAAA,CAAClC,IAAI;MAAC0P,SAAS,EAAC,eAAe;MAAChM,KAAK,EAAC,0BAAM;MAAAX,QAAA,eAC1Cb,OAAA,CAAC3B,KAAK;QAACyC,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjEb,OAAA;UAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;YAAC0C,MAAM;YAAA/B,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BrB,OAAA,CAACjC,KAAK,CAAC0P,KAAK;YACVhM,KAAK,EAAEmG,gBAAiB;YACxBuC,QAAQ,EAAGuD,CAAC,IAAK7F,mBAAmB,CAAC6F,CAAC,CAACC,MAAM,CAAClM,KAAK,CAAE;YACrDT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBb,OAAA,CAACjC,KAAK;cAAC0D,KAAK,EAAE,IAAK;cAAAZ,QAAA,eACjBb,OAAA,CAAC3B,KAAK;gBAAAwC,QAAA,GAAC,kDAEL,eAAAb,OAAA,CAACE,IAAI;kBAAC0D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqM,QAAQ,EAAE;kBAAG,CAAE;kBAAAxM,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRrB,OAAA,CAACjC,KAAK;cAAC0D,KAAK,EAAE,KAAM;cAAAZ,QAAA,eAClBb,OAAA,CAAC3B,KAAK;gBAAAwC,QAAA,GAAC,0BAEL,eAAAb,OAAA,CAACE,IAAI;kBAAC0D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqM,QAAQ,EAAE;kBAAG,CAAE;kBAAAxM,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELuG,gBAAgB,iBACf5H,OAAA,CAACnB,KAAK;UACJN,OAAO,EAAC,sCAAQ;UAChBgG,WAAW,eACTvE,OAAA;YAAAa,QAAA,GAAK,8JAEH,eAAAb,OAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAArB,OAAA;cAAAa,QAAA,EAAQ;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDuC,IAAI,EAAC,MAAM;UACXiB,QAAQ;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrB,OAAA,CAAClC,IAAI;MAAC0P,SAAS,EAAC,eAAe;MAAA3M,QAAA,gBAC7Bb,OAAA,CAAC7B,MAAM;QACLyF,IAAI,EAAC,SAAS;QACd7C,IAAI,EAAC,OAAO;QACZiO,IAAI,eAAEhP,OAAA,CAACd,kBAAkB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B2M,OAAO,EAAEhD,mBAAoB;QAC7BiD,OAAO,EAAE/G,QAAS;QAClBgH,QAAQ,EAAE,CAACf,WAAW,CAAC,CAAE;QACzBK,SAAS,EAAC,eAAe;QAAA3M,QAAA,EAExBqG,QAAQ,GAAG,SAAS,GAAG;MAAQ;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAGR6F,QAAQ,iBACPlH,OAAA;QAAKwN,SAAS,EAAC,kBAAkB;QAAA3M,QAAA,gBAC/Bb,OAAA,CAACE,IAAI;UAAAW,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBrB,OAAA,CAACpB,QAAQ;UAACqQ,OAAO,EAAE7H,QAAS;UAAC8F,MAAM,EAAC;QAAQ;UAAAhM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAGAiG,eAAe,IAAIA,eAAe,CAACqB,OAAO,iBACzC3I,OAAA;QAAKgB,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bb,OAAA,CAACnB,KAAK;UACJN,OAAO,EAAC,0BAAM;UACdgG,WAAW,eACTvE,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAAa,QAAA,EAAG;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACfiG,eAAe,CAACuF,WAAW,iBAC1B7M,OAAA;cAAAa,QAAA,gBAAGb,OAAA;gBAAAa,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACiG,eAAe,CAACuF,WAAW;YAAA;cAAA3L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7D,EACAuH,MAAM,CAACsG,OAAO,CAAC5H,eAAe,CAACqB,OAAO,CAAC,CAAC5F,GAAG,CAAC,CAAC,CAACoM,GAAG,EAAE5O,MAAM,CAAgB,kBACxEP,OAAA;cAAegB,KAAK,EAAE;gBAAEmB,SAAS,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCb,OAAA;gBAAAa,QAAA,gBAAGb,OAAA;kBAAAa,QAAA,EAAQ;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC8N,GAAG;cAAA;gBAAAjO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCrB,OAAA;gBAAAa,QAAA,GAAG,wCAAQ,EAACN,MAAM,CAAC+D,eAAe;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCrB,OAAA;gBAAAa,QAAA,GAAG,oDAAU,EAACN,MAAM,CAACiE,kBAAkB;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHpC8N,GAAG;cAAAjO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;UACDuC,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAG;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGFiG,eAAe,IAAIA,eAAe,CAACqB,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACvB,eAAe,CAACqB,OAAO,CAAC,CAAC3E,MAAM,GAAG,CAAC,iBAC5FhE,OAAA,CAAClC,IAAI;QAAC0D,KAAK,EAAC,gFAAe;QAACgM,SAAS,EAAC,eAAe;QAAA3M,QAAA,eACnDb,OAAA,CAAC3B,KAAK;UAACyC,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAChEb,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAAC0C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCrB,OAAA,CAAC9B,MAAM;cACLuD,KAAK,EAAE+F,iBAAkB;cACzB2C,QAAQ,EAAE1C,oBAAqB;cAC/BzG,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvC2L,WAAW,EAAC,8DAAY;cAAAjN,QAAA,EAEvB+H,MAAM,CAACC,IAAI,CAACvB,eAAe,CAACqB,OAAO,CAAC,CAAC5F,GAAG,CAAEoM,GAAG,iBAC5CnP,OAAA,CAACI,MAAM;gBAAWqB,KAAK,EAAE0N,GAAI;gBAAAtO,QAAA,EAC1BsO;cAAG,GADOA,GAAG;gBAAAjO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELmG,iBAAiB,IAAIF,eAAe,CAACqB,OAAO,CAACnB,iBAAiB,CAAC,iBAC9DxH,OAAA,CAACK,qBAAqB;YACpBC,SAAS,EAAEkH,iBAAkB;YAC7BjH,MAAM,EAAE+G,eAAe,CAACqB,OAAO,CAACnB,iBAAiB;UAAE;YAAAtG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP,EAGAyG,oBAAoB,IAAIA,oBAAoB,CAACa,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACf,oBAAoB,CAACa,OAAO,CAAC,CAAC3E,MAAM,GAAG,CAAC,iBAC3GhE,OAAA,CAAClC,IAAI;QAAC0D,KAAK,EAAC,sCAAQ;QAACgM,SAAS,EAAC,eAAe;QAACxM,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACtEb,OAAA,CAACnB,KAAK;UACJN,OAAO,EAAC,4CAAS;UACjBgG,WAAW,EAAC,wMAAmC;UAC/CX,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAEuM,YAAY,EAAE;UAAG;QAAE;UAAArM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFrB,OAAA,CAAC3B,KAAK;UAACyC,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAChEb,OAAA;YAAAa,QAAA,gBACEb,OAAA,CAACE,IAAI;cAAC0C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCrB,OAAA,CAAC9B,MAAM;cACLuD,KAAK,EAAEuG,sBAAuB;cAC9BmC,QAAQ,EAAElC,yBAA0B;cACpCjH,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvC2L,WAAW,EAAC,8DAAY;cAAAjN,QAAA,EAEvB+H,MAAM,CAACC,IAAI,CAACf,oBAAoB,CAACa,OAAO,CAAC,CAAC5F,GAAG,CAAEoM,GAAG,iBACjDnP,OAAA,CAACI,MAAM;gBAAWqB,KAAK,EAAE0N,GAAI;gBAAAtO,QAAA,EAC1BsO;cAAG,GADOA,GAAG;gBAAAjO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL2G,sBAAsB,IAAIF,oBAAoB,CAACa,OAAO,CAACX,sBAAsB,CAAC,iBAC7EhI,OAAA,CAACK,qBAAqB;YACpBC,SAAS,EAAE0H,sBAAuB;YAClCzH,MAAM,EAAEuH,oBAAoB,CAACa,OAAO,CAACX,sBAAsB;UAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC2D,EAAA,CAxxBID,iBAA2B;EAAA,QA+ByBjF,cAAc;AAAA;AAAAsP,GAAA,GA/BlErK,iBAA2B;AA0xBjC,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAsK,GAAA;AAAAC,YAAA,CAAAvK,EAAA;AAAAuK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}