{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"onVisibleChange\", \"getContainer\", \"current\", \"countRender\"];\nimport * as React from 'react';\nimport { useState } from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Preview from './Preview';\n/* istanbul ignore next */\n\nexport var context = /*#__PURE__*/React.createContext({\n  previewUrls: new Map(),\n  setPreviewUrls: function setPreviewUrls() {\n    return null;\n  },\n  current: null,\n  setCurrent: function setCurrent() {\n    return null;\n  },\n  setShowPreview: function setShowPreview() {\n    return null;\n  },\n  setMousePosition: function setMousePosition() {\n    return null;\n  },\n  registerImage: function registerImage() {\n    return function () {\n      return null;\n    };\n  },\n  rootClassName: ''\n});\nvar Provider = context.Provider;\nvar Group = function Group(_ref) {\n  var _ref$previewPrefixCls = _ref.previewPrefixCls,\n    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,\n    children = _ref.children,\n    _ref$icons = _ref.icons,\n    icons = _ref$icons === void 0 ? {} : _ref$icons,\n    preview = _ref.preview;\n  var _ref2 = _typeof(preview) === 'object' ? preview : {},\n    _ref2$visible = _ref2.visible,\n    previewVisible = _ref2$visible === void 0 ? undefined : _ref2$visible,\n    _ref2$onVisibleChange = _ref2.onVisibleChange,\n    onPreviewVisibleChange = _ref2$onVisibleChange === void 0 ? undefined : _ref2$onVisibleChange,\n    _ref2$getContainer = _ref2.getContainer,\n    getContainer = _ref2$getContainer === void 0 ? undefined : _ref2$getContainer,\n    _ref2$current = _ref2.current,\n    currentIndex = _ref2$current === void 0 ? 0 : _ref2$current,\n    _ref2$countRender = _ref2.countRender,\n    countRender = _ref2$countRender === void 0 ? undefined : _ref2$countRender,\n    dialogProps = _objectWithoutProperties(_ref2, _excluded);\n  var _useState = useState(new Map()),\n    _useState2 = _slicedToArray(_useState, 2),\n    previewUrls = _useState2[0],\n    setPreviewUrls = _useState2[1];\n  var _useState3 = useState(),\n    _useState4 = _slicedToArray(_useState3, 2),\n    current = _useState4[0],\n    setCurrent = _useState4[1];\n  var _useMergedState = useMergedState(!!previewVisible, {\n      value: previewVisible,\n      onChange: onPreviewVisibleChange\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    isShowPreview = _useMergedState2[0],\n    setShowPreview = _useMergedState2[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    mousePosition = _useState6[0],\n    setMousePosition = _useState6[1];\n  var isControlled = previewVisible !== undefined;\n  var previewUrlsKeys = Array.from(previewUrls.keys());\n  var currentControlledKey = previewUrlsKeys[currentIndex];\n  var canPreviewUrls = new Map(Array.from(previewUrls).filter(function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 2),\n      canPreview = _ref4[1].canPreview;\n    return !!canPreview;\n  }).map(function (_ref5) {\n    var _ref6 = _slicedToArray(_ref5, 2),\n      id = _ref6[0],\n      url = _ref6[1].url;\n    return [id, url];\n  }));\n  var registerImage = function registerImage(id, url) {\n    var canPreview = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    var unRegister = function unRegister() {\n      setPreviewUrls(function (oldPreviewUrls) {\n        var clonePreviewUrls = new Map(oldPreviewUrls);\n        var deleteResult = clonePreviewUrls.delete(id);\n        return deleteResult ? clonePreviewUrls : oldPreviewUrls;\n      });\n    };\n    setPreviewUrls(function (oldPreviewUrls) {\n      return new Map(oldPreviewUrls).set(id, {\n        url: url,\n        canPreview: canPreview\n      });\n    });\n    return unRegister;\n  };\n  var onPreviewClose = function onPreviewClose(e) {\n    e.stopPropagation();\n    setShowPreview(false);\n    setMousePosition(null);\n  };\n  React.useEffect(function () {\n    setCurrent(currentControlledKey);\n  }, [currentControlledKey]);\n  React.useEffect(function () {\n    if (!isShowPreview && isControlled) {\n      setCurrent(currentControlledKey);\n    }\n  }, [currentControlledKey, isControlled, isShowPreview]);\n  return /*#__PURE__*/React.createElement(Provider, {\n    value: {\n      isPreviewGroup: true,\n      previewUrls: canPreviewUrls,\n      setPreviewUrls: setPreviewUrls,\n      current: current,\n      setCurrent: setCurrent,\n      setShowPreview: setShowPreview,\n      setMousePosition: setMousePosition,\n      registerImage: registerImage\n    }\n  }, children, /*#__PURE__*/React.createElement(Preview, _extends({\n    \"aria-hidden\": !isShowPreview,\n    visible: isShowPreview,\n    prefixCls: previewPrefixCls,\n    onClose: onPreviewClose,\n    mousePosition: mousePosition,\n    src: canPreviewUrls.get(current),\n    icons: icons,\n    getContainer: getContainer,\n    countRender: countRender\n  }, dialogProps)));\n};\nexport default Group;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_typeof", "_objectWithoutProperties", "_excluded", "React", "useState", "useMergedState", "Preview", "context", "createContext", "previewUrls", "Map", "setPreviewUrls", "current", "setCurrent", "setShowPreview", "setMousePosition", "registerImage", "rootClassName", "Provider", "Group", "_ref", "_ref$previewPrefixCls", "previewPrefixCls", "children", "_ref$icons", "icons", "preview", "_ref2", "_ref2$visible", "visible", "previewVisible", "undefined", "_ref2$onVisibleChange", "onVisibleChange", "onPreviewVisibleChange", "_ref2$getContainer", "getContainer", "_ref2$current", "currentIndex", "_ref2$countRender", "countRender", "dialogProps", "_useState", "_useState2", "_useState3", "_useState4", "_useMergedState", "value", "onChange", "_useMergedState2", "isShowPreview", "_useState5", "_useState6", "mousePosition", "isControlled", "previewUrlsKeys", "Array", "from", "keys", "currentControlledKey", "canPreviewUrls", "filter", "_ref3", "_ref4", "canPreview", "map", "_ref5", "_ref6", "id", "url", "arguments", "length", "unRegister", "oldPreviewUrls", "clonePreviewUrls", "deleteResult", "delete", "set", "onPreviewClose", "e", "stopPropagation", "useEffect", "createElement", "isPreviewGroup", "prefixCls", "onClose", "src", "get"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-image/es/PreviewGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"onVisibleChange\", \"getContainer\", \"current\", \"countRender\"];\nimport * as React from 'react';\nimport { useState } from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Preview from './Preview';\n/* istanbul ignore next */\n\nexport var context = /*#__PURE__*/React.createContext({\n  previewUrls: new Map(),\n  setPreviewUrls: function setPreviewUrls() {\n    return null;\n  },\n  current: null,\n  setCurrent: function setCurrent() {\n    return null;\n  },\n  setShowPreview: function setShowPreview() {\n    return null;\n  },\n  setMousePosition: function setMousePosition() {\n    return null;\n  },\n  registerImage: function registerImage() {\n    return function () {\n      return null;\n    };\n  },\n  rootClassName: ''\n});\nvar Provider = context.Provider;\n\nvar Group = function Group(_ref) {\n  var _ref$previewPrefixCls = _ref.previewPrefixCls,\n      previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,\n      children = _ref.children,\n      _ref$icons = _ref.icons,\n      icons = _ref$icons === void 0 ? {} : _ref$icons,\n      preview = _ref.preview;\n\n  var _ref2 = _typeof(preview) === 'object' ? preview : {},\n      _ref2$visible = _ref2.visible,\n      previewVisible = _ref2$visible === void 0 ? undefined : _ref2$visible,\n      _ref2$onVisibleChange = _ref2.onVisibleChange,\n      onPreviewVisibleChange = _ref2$onVisibleChange === void 0 ? undefined : _ref2$onVisibleChange,\n      _ref2$getContainer = _ref2.getContainer,\n      getContainer = _ref2$getContainer === void 0 ? undefined : _ref2$getContainer,\n      _ref2$current = _ref2.current,\n      currentIndex = _ref2$current === void 0 ? 0 : _ref2$current,\n      _ref2$countRender = _ref2.countRender,\n      countRender = _ref2$countRender === void 0 ? undefined : _ref2$countRender,\n      dialogProps = _objectWithoutProperties(_ref2, _excluded);\n\n  var _useState = useState(new Map()),\n      _useState2 = _slicedToArray(_useState, 2),\n      previewUrls = _useState2[0],\n      setPreviewUrls = _useState2[1];\n\n  var _useState3 = useState(),\n      _useState4 = _slicedToArray(_useState3, 2),\n      current = _useState4[0],\n      setCurrent = _useState4[1];\n\n  var _useMergedState = useMergedState(!!previewVisible, {\n    value: previewVisible,\n    onChange: onPreviewVisibleChange\n  }),\n      _useMergedState2 = _slicedToArray(_useMergedState, 2),\n      isShowPreview = _useMergedState2[0],\n      setShowPreview = _useMergedState2[1];\n\n  var _useState5 = useState(null),\n      _useState6 = _slicedToArray(_useState5, 2),\n      mousePosition = _useState6[0],\n      setMousePosition = _useState6[1];\n\n  var isControlled = previewVisible !== undefined;\n  var previewUrlsKeys = Array.from(previewUrls.keys());\n  var currentControlledKey = previewUrlsKeys[currentIndex];\n  var canPreviewUrls = new Map(Array.from(previewUrls).filter(function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 2),\n        canPreview = _ref4[1].canPreview;\n\n    return !!canPreview;\n  }).map(function (_ref5) {\n    var _ref6 = _slicedToArray(_ref5, 2),\n        id = _ref6[0],\n        url = _ref6[1].url;\n\n    return [id, url];\n  }));\n\n  var registerImage = function registerImage(id, url) {\n    var canPreview = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n\n    var unRegister = function unRegister() {\n      setPreviewUrls(function (oldPreviewUrls) {\n        var clonePreviewUrls = new Map(oldPreviewUrls);\n        var deleteResult = clonePreviewUrls.delete(id);\n        return deleteResult ? clonePreviewUrls : oldPreviewUrls;\n      });\n    };\n\n    setPreviewUrls(function (oldPreviewUrls) {\n      return new Map(oldPreviewUrls).set(id, {\n        url: url,\n        canPreview: canPreview\n      });\n    });\n    return unRegister;\n  };\n\n  var onPreviewClose = function onPreviewClose(e) {\n    e.stopPropagation();\n    setShowPreview(false);\n    setMousePosition(null);\n  };\n\n  React.useEffect(function () {\n    setCurrent(currentControlledKey);\n  }, [currentControlledKey]);\n  React.useEffect(function () {\n    if (!isShowPreview && isControlled) {\n      setCurrent(currentControlledKey);\n    }\n  }, [currentControlledKey, isControlled, isShowPreview]);\n  return /*#__PURE__*/React.createElement(Provider, {\n    value: {\n      isPreviewGroup: true,\n      previewUrls: canPreviewUrls,\n      setPreviewUrls: setPreviewUrls,\n      current: current,\n      setCurrent: setCurrent,\n      setShowPreview: setShowPreview,\n      setMousePosition: setMousePosition,\n      registerImage: registerImage\n    }\n  }, children, /*#__PURE__*/React.createElement(Preview, _extends({\n    \"aria-hidden\": !isShowPreview,\n    visible: isShowPreview,\n    prefixCls: previewPrefixCls,\n    onClose: onPreviewClose,\n    mousePosition: mousePosition,\n    src: canPreviewUrls.get(current),\n    icons: icons,\n    getContainer: getContainer,\n    countRender: countRender\n  }, dialogProps)));\n};\n\nexport default Group;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,SAAS,EAAE,iBAAiB,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,CAAC;AACxF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,WAAW;AAC/B;;AAEA,OAAO,IAAIC,OAAO,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC;EACpDC,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;EACtBC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,OAAO,IAAI;EACb,CAAC;EACDC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,OAAO,IAAI;EACb,CAAC;EACDC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,OAAO,IAAI;EACb,CAAC;EACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,OAAO,IAAI;EACb,CAAC;EACDC,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;IACtC,OAAO,YAAY;MACjB,OAAO,IAAI;IACb,CAAC;EACH,CAAC;EACDC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGX,OAAO,CAACW,QAAQ;AAE/B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,IAAIC,qBAAqB,GAAGD,IAAI,CAACE,gBAAgB;IAC7CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAGA,qBAAqB;IAChGE,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,UAAU,GAAGJ,IAAI,CAACK,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,UAAU;IAC/CE,OAAO,GAAGN,IAAI,CAACM,OAAO;EAE1B,IAAIC,KAAK,GAAG3B,OAAO,CAAC0B,OAAO,CAAC,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC,CAAC;IACpDE,aAAa,GAAGD,KAAK,CAACE,OAAO;IAC7BC,cAAc,GAAGF,aAAa,KAAK,KAAK,CAAC,GAAGG,SAAS,GAAGH,aAAa;IACrEI,qBAAqB,GAAGL,KAAK,CAACM,eAAe;IAC7CC,sBAAsB,GAAGF,qBAAqB,KAAK,KAAK,CAAC,GAAGD,SAAS,GAAGC,qBAAqB;IAC7FG,kBAAkB,GAAGR,KAAK,CAACS,YAAY;IACvCA,YAAY,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAGJ,SAAS,GAAGI,kBAAkB;IAC7EE,aAAa,GAAGV,KAAK,CAACf,OAAO;IAC7B0B,YAAY,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,aAAa;IAC3DE,iBAAiB,GAAGZ,KAAK,CAACa,WAAW;IACrCA,WAAW,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGR,SAAS,GAAGQ,iBAAiB;IAC1EE,WAAW,GAAGxC,wBAAwB,CAAC0B,KAAK,EAAEzB,SAAS,CAAC;EAE5D,IAAIwC,SAAS,GAAGtC,QAAQ,CAAC,IAAIM,GAAG,CAAC,CAAC,CAAC;IAC/BiC,UAAU,GAAG5C,cAAc,CAAC2C,SAAS,EAAE,CAAC,CAAC;IACzCjC,WAAW,GAAGkC,UAAU,CAAC,CAAC,CAAC;IAC3BhC,cAAc,GAAGgC,UAAU,CAAC,CAAC,CAAC;EAElC,IAAIC,UAAU,GAAGxC,QAAQ,CAAC,CAAC;IACvByC,UAAU,GAAG9C,cAAc,CAAC6C,UAAU,EAAE,CAAC,CAAC;IAC1ChC,OAAO,GAAGiC,UAAU,CAAC,CAAC,CAAC;IACvBhC,UAAU,GAAGgC,UAAU,CAAC,CAAC,CAAC;EAE9B,IAAIC,eAAe,GAAGzC,cAAc,CAAC,CAAC,CAACyB,cAAc,EAAE;MACrDiB,KAAK,EAAEjB,cAAc;MACrBkB,QAAQ,EAAEd;IACZ,CAAC,CAAC;IACEe,gBAAgB,GAAGlD,cAAc,CAAC+C,eAAe,EAAE,CAAC,CAAC;IACrDI,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCnC,cAAc,GAAGmC,gBAAgB,CAAC,CAAC,CAAC;EAExC,IAAIE,UAAU,GAAG/C,QAAQ,CAAC,IAAI,CAAC;IAC3BgD,UAAU,GAAGrD,cAAc,CAACoD,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BrC,gBAAgB,GAAGqC,UAAU,CAAC,CAAC,CAAC;EAEpC,IAAIE,YAAY,GAAGxB,cAAc,KAAKC,SAAS;EAC/C,IAAIwB,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAChD,WAAW,CAACiD,IAAI,CAAC,CAAC,CAAC;EACpD,IAAIC,oBAAoB,GAAGJ,eAAe,CAACjB,YAAY,CAAC;EACxD,IAAIsB,cAAc,GAAG,IAAIlD,GAAG,CAAC8C,KAAK,CAACC,IAAI,CAAChD,WAAW,CAAC,CAACoD,MAAM,CAAC,UAAUC,KAAK,EAAE;IAC3E,IAAIC,KAAK,GAAGhE,cAAc,CAAC+D,KAAK,EAAE,CAAC,CAAC;MAChCE,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU;IAEpC,OAAO,CAAC,CAACA,UAAU;EACrB,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAE;IACtB,IAAIC,KAAK,GAAGpE,cAAc,CAACmE,KAAK,EAAE,CAAC,CAAC;MAChCE,EAAE,GAAGD,KAAK,CAAC,CAAC,CAAC;MACbE,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACE,GAAG;IAEtB,OAAO,CAACD,EAAE,EAAEC,GAAG,CAAC;EAClB,CAAC,CAAC,CAAC;EAEH,IAAIrD,aAAa,GAAG,SAASA,aAAaA,CAACoD,EAAE,EAAEC,GAAG,EAAE;IAClD,IAAIL,UAAU,GAAGM,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKvC,SAAS,GAAGuC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAEzF,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;MACrC7D,cAAc,CAAC,UAAU8D,cAAc,EAAE;QACvC,IAAIC,gBAAgB,GAAG,IAAIhE,GAAG,CAAC+D,cAAc,CAAC;QAC9C,IAAIE,YAAY,GAAGD,gBAAgB,CAACE,MAAM,CAACR,EAAE,CAAC;QAC9C,OAAOO,YAAY,GAAGD,gBAAgB,GAAGD,cAAc;MACzD,CAAC,CAAC;IACJ,CAAC;IAED9D,cAAc,CAAC,UAAU8D,cAAc,EAAE;MACvC,OAAO,IAAI/D,GAAG,CAAC+D,cAAc,CAAC,CAACI,GAAG,CAACT,EAAE,EAAE;QACrCC,GAAG,EAAEA,GAAG;QACRL,UAAU,EAAEA;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOQ,UAAU;EACnB,CAAC;EAED,IAAIM,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;IAC9CA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBlE,cAAc,CAAC,KAAK,CAAC;IACrBC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAEDZ,KAAK,CAAC8E,SAAS,CAAC,YAAY;IAC1BpE,UAAU,CAAC8C,oBAAoB,CAAC;EAClC,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1BxD,KAAK,CAAC8E,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC/B,aAAa,IAAII,YAAY,EAAE;MAClCzC,UAAU,CAAC8C,oBAAoB,CAAC;IAClC;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEL,YAAY,EAAEJ,aAAa,CAAC,CAAC;EACvD,OAAO,aAAa/C,KAAK,CAAC+E,aAAa,CAAChE,QAAQ,EAAE;IAChD6B,KAAK,EAAE;MACLoC,cAAc,EAAE,IAAI;MACpB1E,WAAW,EAAEmD,cAAc;MAC3BjD,cAAc,EAAEA,cAAc;MAC9BC,OAAO,EAAEA,OAAO;MAChBC,UAAU,EAAEA,UAAU;MACtBC,cAAc,EAAEA,cAAc;MAC9BC,gBAAgB,EAAEA,gBAAgB;MAClCC,aAAa,EAAEA;IACjB;EACF,CAAC,EAAEO,QAAQ,EAAE,aAAapB,KAAK,CAAC+E,aAAa,CAAC5E,OAAO,EAAER,QAAQ,CAAC;IAC9D,aAAa,EAAE,CAACoD,aAAa;IAC7BrB,OAAO,EAAEqB,aAAa;IACtBkC,SAAS,EAAE9D,gBAAgB;IAC3B+D,OAAO,EAAEP,cAAc;IACvBzB,aAAa,EAAEA,aAAa;IAC5BiC,GAAG,EAAE1B,cAAc,CAAC2B,GAAG,CAAC3E,OAAO,CAAC;IAChCa,KAAK,EAAEA,KAAK;IACZW,YAAY,EAAEA,YAAY;IAC1BI,WAAW,EAAEA;EACf,CAAC,EAAEC,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,eAAetB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}