{"ast": null, "code": "import InternalAvatar from './avatar';\nimport Group from './group';\nexport { Group };\nvar Avatar = InternalAvatar;\nAvatar.Group = Group;\nexport default Avatar;", "map": {"version": 3, "names": ["InternalAvatar", "Group", "Avatar"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/avatar/index.js"], "sourcesContent": ["import InternalAvatar from './avatar';\nimport Group from './group';\nexport { Group };\nvar Avatar = InternalAvatar;\nAvatar.Group = Group;\nexport default Avatar;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,UAAU;AACrC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASA,KAAK;AACd,IAAIC,MAAM,GAAGF,cAAc;AAC3BE,MAAM,CAACD,KAAK,GAAGA,KAAK;AACpB,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}