{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = filter;\nfunction filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "filter", "values", "test", "TypeError", "array", "index", "push"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/filter.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = filter;\n\nfunction filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n\n  return array;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,SAASA,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC5B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,MAAMP,KAAK,IAAIG,MAAM,EAAE;IAC1B,IAAIC,IAAI,CAACJ,KAAK,EAAE,EAAEO,KAAK,EAAEJ,MAAM,CAAC,EAAE;MAChCG,KAAK,CAACE,IAAI,CAACR,KAAK,CAAC;IACnB;EACF;EAEA,OAAOM,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}