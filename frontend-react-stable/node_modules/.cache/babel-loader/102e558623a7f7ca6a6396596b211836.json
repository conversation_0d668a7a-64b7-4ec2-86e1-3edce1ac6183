{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport ArrowLeftOutlined from \"@ant-design/icons/es/icons/ArrowLeftOutlined\";\nimport ArrowRightOutlined from \"@ant-design/icons/es/icons/ArrowRightOutlined\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport Avatar from '../avatar';\nimport Breadcrumb from '../breadcrumb';\nimport { ConfigConsumer } from '../config-provider';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport Space from '../space';\nimport TransButton from '../_util/transButton';\nvar renderBack = function renderBack(prefixCls, backIcon, onBack) {\n  if (!backIcon || !onBack) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"PageHeader\"\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-back\")\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      onClick: function onClick(e) {\n        onBack === null || onBack === void 0 ? void 0 : onBack(e);\n      },\n      className: \"\".concat(prefixCls, \"-back-button\"),\n      \"aria-label\": contextLocale.back\n    }, backIcon));\n  });\n};\nvar renderBreadcrumb = function renderBreadcrumb(breadcrumb) {\n  return /*#__PURE__*/React.createElement(Breadcrumb, _extends({}, breadcrumb));\n};\nvar getBackIcon = function getBackIcon(props) {\n  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'ltr';\n  if (props.backIcon !== undefined) {\n    return props.backIcon;\n  }\n  return direction === 'rtl' ? /*#__PURE__*/React.createElement(ArrowRightOutlined, null) : /*#__PURE__*/React.createElement(ArrowLeftOutlined, null);\n};\nvar renderTitle = function renderTitle(prefixCls, props) {\n  var direction = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ltr';\n  var title = props.title,\n    avatar = props.avatar,\n    subTitle = props.subTitle,\n    tags = props.tags,\n    extra = props.extra,\n    onBack = props.onBack;\n  var headingPrefixCls = \"\".concat(prefixCls, \"-heading\");\n  var hasHeading = title || subTitle || tags || extra;\n  // If there is nothing, return a null\n  if (!hasHeading) {\n    return null;\n  }\n  var backIcon = getBackIcon(props, direction);\n  var backIconDom = renderBack(prefixCls, backIcon, onBack);\n  var hasTitle = backIconDom || avatar || hasHeading;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: headingPrefixCls\n  }, hasTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(headingPrefixCls, \"-left\")\n  }, backIconDom, avatar && /*#__PURE__*/React.createElement(Avatar, _extends({}, avatar)), title && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-sub-title\"),\n    title: typeof subTitle === 'string' ? subTitle : undefined\n  }, subTitle), tags && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-tags\")\n  }, tags)), extra && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-extra\")\n  }, /*#__PURE__*/React.createElement(Space, null, extra)));\n};\nvar renderFooter = function renderFooter(prefixCls, footer) {\n  if (footer) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  return null;\n};\nvar renderChildren = function renderChildren(prefixCls, children) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children);\n};\nvar PageHeader = function PageHeader(props) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    compact = _useState2[0],\n    updateCompact = _useState2[1];\n  var onResize = function onResize(_ref) {\n    var width = _ref.width;\n    updateCompact(width < 768, true);\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref2) {\n    var _classNames;\n    var getPrefixCls = _ref2.getPrefixCls,\n      pageHeader = _ref2.pageHeader,\n      direction = _ref2.direction;\n    var _a;\n    var customizePrefixCls = props.prefixCls,\n      style = props.style,\n      footer = props.footer,\n      children = props.children,\n      breadcrumb = props.breadcrumb,\n      breadcrumbRender = props.breadcrumbRender,\n      customizeClassName = props.className;\n    var ghost = true;\n    // Use `ghost` from `props` or from `ConfigProvider` instead.\n    if ('ghost' in props) {\n      ghost = props.ghost;\n    } else if (pageHeader && 'ghost' in pageHeader) {\n      ghost = pageHeader.ghost;\n    }\n    var prefixCls = getPrefixCls('page-header', customizePrefixCls);\n    var getDefaultBreadcrumbDom = function getDefaultBreadcrumbDom() {\n      if (breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.routes) {\n        return renderBreadcrumb(breadcrumb);\n      }\n      return null;\n    };\n    var defaultBreadcrumbDom = getDefaultBreadcrumbDom();\n    var isBreadcrumbComponent = breadcrumb && 'props' in breadcrumb;\n    // support breadcrumbRender function\n    var breadcrumbRenderDomFromProps = (_a = breadcrumbRender === null || breadcrumbRender === void 0 ? void 0 : breadcrumbRender(props, defaultBreadcrumbDom)) !== null && _a !== void 0 ? _a : defaultBreadcrumbDom;\n    var breadcrumbDom = isBreadcrumbComponent ? breadcrumb : breadcrumbRenderDomFromProps;\n    var className = classNames(prefixCls, customizeClassName, (_classNames = {\n      'has-breadcrumb': !!breadcrumbDom,\n      'has-footer': !!footer\n    }, _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), ghost), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), compact), _classNames));\n    return /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onResize\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: className,\n      style: style\n    }, breadcrumbDom, renderTitle(prefixCls, props, direction), children && renderChildren(prefixCls, children), renderFooter(prefixCls, footer)));\n  });\n};\nexport default PageHeader;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "_extends", "ArrowLeftOutlined", "ArrowRightOutlined", "classNames", "ResizeObserver", "useState", "React", "Avatar", "Breadcrumb", "ConfigConsumer", "LocaleReceiver", "Space", "TransButton", "renderBack", "prefixCls", "backIcon", "onBack", "createElement", "componentName", "contextLocale", "className", "concat", "onClick", "e", "back", "renderBreadcrumb", "breadcrumb", "getBackIcon", "props", "direction", "arguments", "length", "undefined", "renderTitle", "title", "avatar", "subTitle", "tags", "extra", "headingPrefixCls", "hasHeading", "backIconDom", "hasTitle", "renderFooter", "footer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "<PERSON><PERSON><PERSON><PERSON>", "_useState", "_useState2", "compact", "updateCompact", "onResize", "_ref", "width", "_ref2", "_classNames", "getPrefixCls", "pageHeader", "_a", "customizePrefixCls", "style", "breadcrumbRender", "customizeClassName", "ghost", "getDefaultBreadcrumbDom", "routes", "defaultBreadcrumbDom", "isBreadcrumbComponent", "breadcrumbRenderDomFromProps", "breadcrumbDom"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/page-header/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport ArrowLeftOutlined from \"@ant-design/icons/es/icons/ArrowLeftOutlined\";\nimport ArrowRightOutlined from \"@ant-design/icons/es/icons/ArrowRightOutlined\";\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport Avatar from '../avatar';\nimport Breadcrumb from '../breadcrumb';\nimport { ConfigConsumer } from '../config-provider';\nimport LocaleReceiver from '../locale-provider/LocaleReceiver';\nimport Space from '../space';\nimport TransButton from '../_util/transButton';\nvar renderBack = function renderBack(prefixCls, backIcon, onBack) {\n  if (!backIcon || !onBack) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LocaleReceiver, {\n    componentName: \"PageHeader\"\n  }, function (contextLocale) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-back\")\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      onClick: function onClick(e) {\n        onBack === null || onBack === void 0 ? void 0 : onBack(e);\n      },\n      className: \"\".concat(prefixCls, \"-back-button\"),\n      \"aria-label\": contextLocale.back\n    }, backIcon));\n  });\n};\nvar renderBreadcrumb = function renderBreadcrumb(breadcrumb) {\n  return /*#__PURE__*/React.createElement(Breadcrumb, _extends({}, breadcrumb));\n};\nvar getBackIcon = function getBackIcon(props) {\n  var direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'ltr';\n  if (props.backIcon !== undefined) {\n    return props.backIcon;\n  }\n  return direction === 'rtl' ? /*#__PURE__*/React.createElement(ArrowRightOutlined, null) : /*#__PURE__*/React.createElement(ArrowLeftOutlined, null);\n};\nvar renderTitle = function renderTitle(prefixCls, props) {\n  var direction = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'ltr';\n  var title = props.title,\n    avatar = props.avatar,\n    subTitle = props.subTitle,\n    tags = props.tags,\n    extra = props.extra,\n    onBack = props.onBack;\n  var headingPrefixCls = \"\".concat(prefixCls, \"-heading\");\n  var hasHeading = title || subTitle || tags || extra;\n  // If there is nothing, return a null\n  if (!hasHeading) {\n    return null;\n  }\n  var backIcon = getBackIcon(props, direction);\n  var backIconDom = renderBack(prefixCls, backIcon, onBack);\n  var hasTitle = backIconDom || avatar || hasHeading;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: headingPrefixCls\n  }, hasTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(headingPrefixCls, \"-left\")\n  }, backIconDom, avatar && /*#__PURE__*/React.createElement(Avatar, _extends({}, avatar)), title && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-title\"),\n    title: typeof title === 'string' ? title : undefined\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-sub-title\"),\n    title: typeof subTitle === 'string' ? subTitle : undefined\n  }, subTitle), tags && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-tags\")\n  }, tags)), extra && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(headingPrefixCls, \"-extra\")\n  }, /*#__PURE__*/React.createElement(Space, null, extra)));\n};\nvar renderFooter = function renderFooter(prefixCls, footer) {\n  if (footer) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, footer);\n  }\n  return null;\n};\nvar renderChildren = function renderChildren(prefixCls, children) {\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, children);\n};\nvar PageHeader = function PageHeader(props) {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    compact = _useState2[0],\n    updateCompact = _useState2[1];\n  var onResize = function onResize(_ref) {\n    var width = _ref.width;\n    updateCompact(width < 768, true);\n  };\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref2) {\n    var _classNames;\n    var getPrefixCls = _ref2.getPrefixCls,\n      pageHeader = _ref2.pageHeader,\n      direction = _ref2.direction;\n    var _a;\n    var customizePrefixCls = props.prefixCls,\n      style = props.style,\n      footer = props.footer,\n      children = props.children,\n      breadcrumb = props.breadcrumb,\n      breadcrumbRender = props.breadcrumbRender,\n      customizeClassName = props.className;\n    var ghost = true;\n    // Use `ghost` from `props` or from `ConfigProvider` instead.\n    if ('ghost' in props) {\n      ghost = props.ghost;\n    } else if (pageHeader && 'ghost' in pageHeader) {\n      ghost = pageHeader.ghost;\n    }\n    var prefixCls = getPrefixCls('page-header', customizePrefixCls);\n    var getDefaultBreadcrumbDom = function getDefaultBreadcrumbDom() {\n      if (breadcrumb === null || breadcrumb === void 0 ? void 0 : breadcrumb.routes) {\n        return renderBreadcrumb(breadcrumb);\n      }\n      return null;\n    };\n    var defaultBreadcrumbDom = getDefaultBreadcrumbDom();\n    var isBreadcrumbComponent = breadcrumb && 'props' in breadcrumb;\n    // support breadcrumbRender function\n    var breadcrumbRenderDomFromProps = (_a = breadcrumbRender === null || breadcrumbRender === void 0 ? void 0 : breadcrumbRender(props, defaultBreadcrumbDom)) !== null && _a !== void 0 ? _a : defaultBreadcrumbDom;\n    var breadcrumbDom = isBreadcrumbComponent ? breadcrumb : breadcrumbRenderDomFromProps;\n    var className = classNames(prefixCls, customizeClassName, (_classNames = {\n      'has-breadcrumb': !!breadcrumbDom,\n      'has-footer': !!footer\n    }, _defineProperty(_classNames, \"\".concat(prefixCls, \"-ghost\"), ghost), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), compact), _classNames));\n    return /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onResize\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: className,\n      style: style\n    }, breadcrumbDom, renderTitle(prefixCls, props, direction), children && renderChildren(prefixCls, children), renderFooter(prefixCls, footer)));\n  });\n};\nexport default PageHeader;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAChE,IAAI,CAACD,QAAQ,IAAI,CAACC,MAAM,EAAE;IACxB,OAAO,IAAI;EACb;EACA,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACP,cAAc,EAAE;IACtDQ,aAAa,EAAE;EACjB,CAAC,EAAE,UAAUC,aAAa,EAAE;IAC1B,OAAO,aAAab,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;MAC7CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,OAAO;IACzC,CAAC,EAAE,aAAaR,KAAK,CAACW,aAAa,CAACL,WAAW,EAAE;MAC/CU,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3BP,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,CAAC,CAAC;MAC3D,CAAC;MACDH,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,cAAc,CAAC;MAC/C,YAAY,EAAEK,aAAa,CAACK;IAC9B,CAAC,EAAET,QAAQ,CAAC,CAAC;EACf,CAAC,CAAC;AACJ,CAAC;AACD,IAAIU,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAE;EAC3D,OAAO,aAAapB,KAAK,CAACW,aAAa,CAACT,UAAU,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAE0B,UAAU,CAAC,CAAC;AAC/E,CAAC;AACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAIF,KAAK,CAACb,QAAQ,KAAKiB,SAAS,EAAE;IAChC,OAAOJ,KAAK,CAACb,QAAQ;EACvB;EACA,OAAOc,SAAS,KAAK,KAAK,GAAG,aAAavB,KAAK,CAACW,aAAa,CAACf,kBAAkB,EAAE,IAAI,CAAC,GAAG,aAAaI,KAAK,CAACW,aAAa,CAAChB,iBAAiB,EAAE,IAAI,CAAC;AACrJ,CAAC;AACD,IAAIgC,WAAW,GAAG,SAASA,WAAWA,CAACnB,SAAS,EAAEc,KAAK,EAAE;EACvD,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAII,KAAK,GAAGN,KAAK,CAACM,KAAK;IACrBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBtB,MAAM,GAAGY,KAAK,CAACZ,MAAM;EACvB,IAAIuB,gBAAgB,GAAG,EAAE,CAAClB,MAAM,CAACP,SAAS,EAAE,UAAU,CAAC;EACvD,IAAI0B,UAAU,GAAGN,KAAK,IAAIE,QAAQ,IAAIC,IAAI,IAAIC,KAAK;EACnD;EACA,IAAI,CAACE,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAIzB,QAAQ,GAAGY,WAAW,CAACC,KAAK,EAAEC,SAAS,CAAC;EAC5C,IAAIY,WAAW,GAAG5B,UAAU,CAACC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,CAAC;EACzD,IAAI0B,QAAQ,GAAGD,WAAW,IAAIN,MAAM,IAAIK,UAAU;EAClD,OAAO,aAAalC,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAC7CG,SAAS,EAAEmB;EACb,CAAC,EAAEG,QAAQ,IAAI,aAAapC,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACrDG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACkB,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAEE,WAAW,EAAEN,MAAM,IAAI,aAAa7B,KAAK,CAACW,aAAa,CAACV,MAAM,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEmC,MAAM,CAAC,CAAC,EAAED,KAAK,IAAI,aAAa5B,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC1IG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACkB,gBAAgB,EAAE,QAAQ,CAAC;IAChDL,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGF;EAC7C,CAAC,EAAEE,KAAK,CAAC,EAAEE,QAAQ,IAAI,aAAa9B,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC9DG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACkB,gBAAgB,EAAE,YAAY,CAAC;IACpDL,KAAK,EAAE,OAAOE,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGJ;EACnD,CAAC,EAAEI,QAAQ,CAAC,EAAEC,IAAI,IAAI,aAAa/B,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC7DG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACkB,gBAAgB,EAAE,OAAO;EAChD,CAAC,EAAEF,IAAI,CAAC,CAAC,EAAEC,KAAK,IAAI,aAAahC,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAC3DG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACkB,gBAAgB,EAAE,QAAQ;EACjD,CAAC,EAAE,aAAajC,KAAK,CAACW,aAAa,CAACN,KAAK,EAAE,IAAI,EAAE2B,KAAK,CAAC,CAAC,CAAC;AAC3D,CAAC;AACD,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAAC7B,SAAS,EAAE8B,MAAM,EAAE;EAC1D,IAAIA,MAAM,EAAE;IACV,OAAO,aAAatC,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;MAC7CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,SAAS;IAC3C,CAAC,EAAE8B,MAAM,CAAC;EACZ;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAC/B,SAAS,EAAEgC,QAAQ,EAAE;EAChE,OAAO,aAAaxC,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAC7CG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEgC,QAAQ,CAAC;AACd,CAAC;AACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACnB,KAAK,EAAE;EAC1C,IAAIoB,SAAS,GAAG3C,QAAQ,CAAC,KAAK,CAAC;IAC7B4C,UAAU,GAAGlD,cAAc,CAACiD,SAAS,EAAE,CAAC,CAAC;IACzCE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtBH,aAAa,CAACG,KAAK,GAAG,GAAG,EAAE,IAAI,CAAC;EAClC,CAAC;EACD,OAAO,aAAahD,KAAK,CAACW,aAAa,CAACR,cAAc,EAAE,IAAI,EAAE,UAAU8C,KAAK,EAAE;IAC7E,IAAIC,WAAW;IACf,IAAIC,YAAY,GAAGF,KAAK,CAACE,YAAY;MACnCC,UAAU,GAAGH,KAAK,CAACG,UAAU;MAC7B7B,SAAS,GAAG0B,KAAK,CAAC1B,SAAS;IAC7B,IAAI8B,EAAE;IACN,IAAIC,kBAAkB,GAAGhC,KAAK,CAACd,SAAS;MACtC+C,KAAK,GAAGjC,KAAK,CAACiC,KAAK;MACnBjB,MAAM,GAAGhB,KAAK,CAACgB,MAAM;MACrBE,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;MACzBpB,UAAU,GAAGE,KAAK,CAACF,UAAU;MAC7BoC,gBAAgB,GAAGlC,KAAK,CAACkC,gBAAgB;MACzCC,kBAAkB,GAAGnC,KAAK,CAACR,SAAS;IACtC,IAAI4C,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,OAAO,IAAIpC,KAAK,EAAE;MACpBoC,KAAK,GAAGpC,KAAK,CAACoC,KAAK;IACrB,CAAC,MAAM,IAAIN,UAAU,IAAI,OAAO,IAAIA,UAAU,EAAE;MAC9CM,KAAK,GAAGN,UAAU,CAACM,KAAK;IAC1B;IACA,IAAIlD,SAAS,GAAG2C,YAAY,CAAC,aAAa,EAAEG,kBAAkB,CAAC;IAC/D,IAAIK,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;MAC/D,IAAIvC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACwC,MAAM,EAAE;QAC7E,OAAOzC,gBAAgB,CAACC,UAAU,CAAC;MACrC;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAIyC,oBAAoB,GAAGF,uBAAuB,CAAC,CAAC;IACpD,IAAIG,qBAAqB,GAAG1C,UAAU,IAAI,OAAO,IAAIA,UAAU;IAC/D;IACA,IAAI2C,4BAA4B,GAAG,CAACV,EAAE,GAAGG,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAClC,KAAK,EAAEuC,oBAAoB,CAAC,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGQ,oBAAoB;IACjN,IAAIG,aAAa,GAAGF,qBAAqB,GAAG1C,UAAU,GAAG2C,4BAA4B;IACrF,IAAIjD,SAAS,GAAGjB,UAAU,CAACW,SAAS,EAAEiD,kBAAkB,GAAGP,WAAW,GAAG;MACvE,gBAAgB,EAAE,CAAC,CAACc,aAAa;MACjC,YAAY,EAAE,CAAC,CAAC1B;IAClB,CAAC,EAAE9C,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACnC,MAAM,CAACP,SAAS,EAAE,QAAQ,CAAC,EAAEkD,KAAK,CAAC,EAAElE,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACnC,MAAM,CAACP,SAAS,EAAE,MAAM,CAAC,EAAEe,SAAS,KAAK,KAAK,CAAC,EAAE/B,eAAe,CAAC0D,WAAW,EAAE,EAAE,CAACnC,MAAM,CAACP,SAAS,EAAE,UAAU,CAAC,EAAEoC,OAAO,CAAC,EAAEM,WAAW,CAAC,CAAC;IAC/O,OAAO,aAAalD,KAAK,CAACW,aAAa,CAACb,cAAc,EAAE;MACtDgD,QAAQ,EAAEA;IACZ,CAAC,EAAE,aAAa9C,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;MACzCG,SAAS,EAAEA,SAAS;MACpByC,KAAK,EAAEA;IACT,CAAC,EAAES,aAAa,EAAErC,WAAW,CAACnB,SAAS,EAAEc,KAAK,EAAEC,SAAS,CAAC,EAAEiB,QAAQ,IAAID,cAAc,CAAC/B,SAAS,EAAEgC,QAAQ,CAAC,EAAEH,YAAY,CAAC7B,SAAS,EAAE8B,MAAM,CAAC,CAAC,CAAC;EAChJ,CAAC,CAAC;AACJ,CAAC;AACD,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}