{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  var _selectedTask$result$;\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = taskId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 13\n      }, this),\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 13\n      }, this),\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      style: {\n        marginBottom: '24px',\n        display: 'block'\n      },\n      children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",\n            children: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true,\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            danger: true,\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 23\n            }, this),\n            disabled: completedTasks.length === 0,\n            onClick: handleClearCompleted,\n            children: \"\\u6E05\\u7A7A\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: completedTasks,\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(MinusCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleDeleteSingleTask(task.task_id),\n                children: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.includes('_') ? `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` : `${task.task_id.substring(0, 8)}...`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), selectedTask.status === TASK_STATUS.COMPLETED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: 100,\n              size: \"small\",\n              status: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.FAILED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this) : selectedTask.status === TASK_STATUS.CANCELLED ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress || 0,\n              size: \"small\",\n              status: \"exception\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this) : selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6267\\u884C\\u65F6\\u957F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: (_selectedTask$result => {\n                // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                if (selectedTask.started_at && selectedTask.completed_at) {\n                  const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒`;\n                }\n                // 如果任务正在运行，且有开始时间\n                else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                  const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                  return `${duration}秒 (进行中)`;\n                }\n                // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                else if (selectedTask.status === TASK_STATUS.COMPLETED && (_selectedTask$result = selectedTask.result) !== null && _selectedTask$result !== void 0 && _selectedTask$result.duration_seconds) {\n                  return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                }\n                // 如果任务已完成但没有时间信息，显示已完成\n                else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                  return '已完成';\n                }\n                // 如果任务失败或取消，显示相应状态\n                else if (selectedTask.status === TASK_STATUS.FAILED) {\n                  return '执行失败';\n                } else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                  return '已取消';\n                }\n                // 其他情况显示等待开始\n                else {\n                  return '等待开始';\n                }\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), selectedTask.current_step && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u5F53\\u524D\\u6B65\\u9AA4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.current_step\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 17\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 17\n          }, this), selectedTask.params && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u53C2\\u6570:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 12,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 200,\n                overflow: 'auto',\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.params, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'training' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 23\n              }, this), selectedTask.result.results && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"purple\",\n                children: [\"\\u6A21\\u578B\\u6570\\u91CF: \", Object.keys(selectedTask.result.results).length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u8BAD\\u7EC3\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6A21\\u578B\\u8BAD\\u7EC3\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 17\n          }, this), selectedTask.result && selectedTask.task_type === 'prediction' && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9884\\u6D4B\\u5B8C\\u6210:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 46\n                }, this),\n                children: \"\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this), selectedTask.result.anomaly_count !== undefined && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"red\",\n                children: [\"\\u5F02\\u5E38\\u6570\\u91CF: \", selectedTask.result.anomaly_count]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 23\n              }, this), ((_selectedTask$result$ = selectedTask.result.predictions) === null || _selectedTask$result$ === void 0 ? void 0 : _selectedTask$result$.length) && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: [\"\\u9884\\u6D4B\\u70B9\\u6570: \", selectedTask.result.predictions.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 23\n              }, this), selectedTask.result.duration_seconds && /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"orange\",\n                children: [\"\\u8017\\u65F6: \", Math.round(selectedTask.result.duration_seconds), \"\\u79D2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\uD83D\\uDCA1 \\u9884\\u6D4B\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u5F02\\u5E38\\u68C0\\u6D4B\\\"\\u9875\\u9762\\u67E5\\u770B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"r5mtZqJgQ/WOUShIGG9323lPtOs=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "MinusCircleOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "_selectedTask$result$", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "deleteSingleTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleDeleteSingleTask", "taskId", "confirm", "title", "content", "substring", "icon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "okText", "okType", "cancelText", "onOk", "handleViewTaskDetail", "task", "handleCancelTask", "console", "handleClearCompleted", "completedCount", "length", "info", "getTaskStatusIcon", "status", "RUNNING", "spin", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "children", "formatTime", "timeString", "Date", "toLocaleString", "level", "fontSize", "fontWeight", "marginBottom", "type", "display", "justifyContent", "onClick", "gutter", "span", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "slice", "created_at", "progress", "undefined", "marginTop", "percent", "size", "disabled", "updated_at", "open", "onCancel", "footer", "width", "_selectedTask$result", "started_at", "completed_at", "duration", "Math", "round", "getTime", "result", "duration_seconds", "current_step", "params", "background", "padding", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "wrap", "results", "Object", "keys", "anomaly_count", "predictions", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>任务管理</Title>\n      <Text type=\"secondary\" style={{ marginBottom: '24px', display: 'block' }}>\n        查看和管理异步训练、预测任务的状态和结果\n      </Text>\n\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'flex-end' }}>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.status === TASK_STATUS.COMPLETED ? (\n                  <Progress percent={100} size=\"small\" status=\"success\" />\n                ) : selectedTask.status === TASK_STATUS.FAILED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>执行时长:</Text>\n                <br />\n                <Text>\n                  {(() => {\n                    // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                    if (selectedTask.started_at && selectedTask.completed_at) {\n                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒`;\n                    }\n                    // 如果任务正在运行，且有开始时间\n                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒 (进行中)`;\n                    }\n                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {\n                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                    }\n                    // 如果任务已完成但没有时间信息，显示已完成\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                      return '已完成';\n                    }\n                    // 如果任务失败或取消，显示相应状态\n                    else if (selectedTask.status === TASK_STATUS.FAILED) {\n                      return '执行失败';\n                    }\n                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                      return '已取消';\n                    }\n                    // 其他情况显示等待开始\n                    else {\n                      return '等待开始';\n                    }\n                  })()}\n                </Text>\n              </Col>\n              {selectedTask.current_step && (\n                <Col span={24}>\n                  <Text strong>当前步骤:</Text>\n                  <br />\n                  <Text>{selectedTask.current_step}</Text>\n                </Col>\n              )}\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.params && (\n                <Col span={24}>\n                  <Text strong>任务参数:</Text>\n                  <br />\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 12,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 200,\n                    overflow: 'auto',\n                    marginTop: 8\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'training' && (\n                <Col span={24}>\n                  <Text strong>训练完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      训练已完成\n                    </Tag>\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"blue\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                    {selectedTask.result.results && (\n                      <Tag color=\"purple\">\n                        模型数量: {Object.keys(selectedTask.result.results).length}\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 训练结果详情请前往\"模型训练\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'prediction' && (\n                <Col span={24}>\n                  <Text strong>预测完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      预测已完成\n                    </Tag>\n                    {selectedTask.result.anomaly_count !== undefined && (\n                      <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                    )}\n                    {selectedTask.result.predictions?.length && (\n                      <Tag color=\"blue\">预测点数: {selectedTask.result.predictions.length}</Tag>\n                    )}\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 预测结果详情请前往\"异常检测\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGzB,UAAU;AAElC,MAAM0B,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,gBAAgB;IAChBC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGnB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACdwC,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACAzC,SAAS,CAAC,MAAM;IACd,MAAMqD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCd,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMe,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACb,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCJ,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMK,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF5B,OAAO,CAAC8C,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMS,sBAAsB,GAAIC,MAAc,IAAK;IACjDnD,KAAK,CAACoD,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,WAAWH,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ;MAClDC,IAAI,eAAEtC,OAAA,CAACH,mBAAmB;QAAC0C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1DC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,MAAMlB,OAAO,GAAG,MAAMhB,gBAAgB,CAACmB,MAAM,CAAC;QAC9C,IAAIH,OAAO,EAAE;UACX;UACA,MAAMF,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAIC,IAAU,IAAK;IAC3C/B,eAAe,CAAC+B,IAAI,CAAC;IACrB7B,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM8B,gBAAgB,GAAG,MAAOlB,MAAc,IAAK;IACjD,IAAI;MACF,MAAMpB,UAAU,CAACoB,MAAM,CAAC;MACxB,MAAMtB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,cAAc,GAAG7C,cAAc,CAAC8C,MAAM;IAC5C,IAAID,cAAc,KAAK,CAAC,EAAE;MACxBtE,OAAO,CAACwE,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA1E,KAAK,CAACoD,OAAO,CAAC;MACZC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,WAAWkB,cAAc,oBAAoB;MACtDhB,IAAI,eAAEtC,OAAA,CAACN,cAAc;QAAC6C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrDC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMjC,mBAAmB,CAAC,CAAC;UAC3B;UACA,MAAMa,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;QACJ,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAKzC,WAAW,CAAC0C,OAAO;QACtB,oBAAO3D,OAAA,CAACZ,YAAY;UAACwE,IAAI;UAACrB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK3B,WAAW,CAAC4C,SAAS;QACxB,oBAAO7D,OAAA,CAACX,mBAAmB;UAACkD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK3B,WAAW,CAAC6C,MAAM;QACrB,oBAAO9D,OAAA,CAACV,mBAAmB;UAACiD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK3B,WAAW,CAAC8C,SAAS;QACxB,oBAAO/D,OAAA,CAACT,YAAY;UAACgD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAO5C,OAAA,CAACR,mBAAmB;UAAC+C,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAIN,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAKzC,WAAW,CAAC0C,OAAO;QACtB,oBAAO3D,OAAA,CAACpB,GAAG;UAAC4D,KAAK,EAAC,YAAY;UAACF,IAAI,eAAEtC,OAAA,CAACZ,YAAY;YAACwE,IAAI;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK3B,WAAW,CAAC4C,SAAS;QACxB,oBAAO7D,OAAA,CAACpB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtC,OAAA,CAACX,mBAAmB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK3B,WAAW,CAAC6C,MAAM;QACrB,oBAAO9D,OAAA,CAACpB,GAAG;UAAC4D,KAAK,EAAC,OAAO;UAACF,IAAI,eAAEtC,OAAA,CAACV,mBAAmB;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK3B,WAAW,CAAC8C,SAAS;QACxB,oBAAO/D,OAAA,CAACpB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtC,OAAA,CAACT,YAAY;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAO5C,OAAA,CAACpB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACF,IAAI,eAAEtC,OAAA,CAACR,mBAAmB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAqB,QAAA,EAAC;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;EAED,oBACErE,OAAA;IAAAiE,QAAA,gBACEjE,OAAA,CAACG,KAAK;MAACmE,KAAK,EAAE,CAAE;MAAC/B,KAAK,EAAE;QAAEgC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAR,QAAA,EAAC;IAAI;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAChG5C,OAAA,CAACI,IAAI;MAACsE,IAAI,EAAC,WAAW;MAACnC,KAAK,EAAE;QAAEkC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE;MAAQ,CAAE;MAAAV,QAAA,EAAC;IAE1E;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP5C,OAAA;MAAKuC,KAAK,EAAE;QAAEkC,YAAY,EAAE,MAAM;QAAEE,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAX,QAAA,eAChFjE,OAAA,CAACvB,MAAM;QACLiG,IAAI,EAAC,SAAS;QACdpC,IAAI,eAAEtC,OAAA,CAACL,cAAc;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBiC,OAAO,EAAElD,aAAc;QACvBjB,OAAO,EAAEY,UAAW;QAAA2C,QAAA,EACrB;MAED;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN5C,OAAA,CAAC5B,IAAI;MAACmE,KAAK,EAAE;QAAEkC,YAAY,EAAE;MAAO,CAAE;MAAAR,QAAA,eACpCjE,OAAA,CAAC3B,GAAG;QAACyG,MAAM,EAAE,EAAG;QAAAb,QAAA,gBACdjE,OAAA,CAAC1B,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXjE,OAAA,CAACzB,SAAS;YACR4D,KAAK,EAAC,gCAAO;YACb6C,KAAK,EAAExE,YAAY,CAAC+C,MAAO;YAC3B0B,MAAM,eAAEjF,OAAA,CAACZ,YAAY;cAACwE,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5C,OAAA,CAAC1B,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXjE,OAAA,CAACzB,SAAS;YACR4D,KAAK,EAAC,gCAAO;YACb6C,KAAK,EAAEvE,cAAc,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAKzC,WAAW,CAAC4C,SAAS,CAAC,CAACN,MAAO;YAC7E0B,MAAM,eAAEjF,OAAA,CAACX,mBAAmB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5C,OAAA,CAAC1B,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXjE,OAAA,CAACzB,SAAS;YACR4D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAEvE,cAAc,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,CAAC,CAACP,MAAO;YAC1E0B,MAAM,eAAEjF,OAAA,CAACV,mBAAmB;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCsC,UAAU,EAAE;cAAE1C,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5C,OAAA,CAAC1B,GAAG;UAACyG,IAAI,EAAE,CAAE;UAAAd,QAAA,eACXjE,OAAA,CAACzB,SAAS;YACR4D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAExE,YAAY,CAAC+C,MAAM,GAAG9C,cAAc,CAAC8C,MAAO;YACnD0B,MAAM,eAAEjF,OAAA,CAACJ,kBAAkB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP5C,OAAA,CAAC3B,GAAG;MAACyG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAb,QAAA,gBAEpBjE,OAAA,CAAC1B,GAAG;QAACyG,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZjE,OAAA,CAAC5B,IAAI;UACH+D,KAAK,eACHnC,OAAA,CAACtB,KAAK;YAAAuF,QAAA,gBACJjE,OAAA,CAACZ,YAAY;cAACwE,IAAI;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAErB,eAAA5C,OAAA,CAACpB,GAAG;cAAC4D,KAAK,EAAC,YAAY;cAAAyB,QAAA,EAAEzD,YAAY,CAAC+C;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACDyC,KAAK,eACHrF,OAAA,CAACd,OAAO;YAACiD,KAAK,EAAC,gCAAO;YAAA8B,QAAA,eACpBjE,OAAA,CAACZ,YAAY;cAACwE,IAAI;cAACrB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACV;UAAAqB,QAAA,eAEDjE,OAAA,CAACf,IAAI;YAACqG,QAAQ,EAAE5E,OAAQ;YAAAuD,QAAA,eACtBjE,OAAA,CAACxB,IAAI;cACH+G,UAAU,EAAE/E,YAAa;cACzBgF,MAAM,EAAE;gBAAEC,SAAS,eAAEzF,OAAA,CAACjB,KAAK;kBAAC2G,WAAW,EAAC;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxD+C,UAAU,EAAGzC,IAAI,iBACflD,OAAA,CAACxB,IAAI,CAACoH,IAAI;gBACRC,OAAO,EAAE,cACP7F,OAAA,CAACvB,MAAM;kBACLiG,IAAI,EAAC,MAAM;kBACXpC,IAAI,eAAEtC,OAAA,CAACP,WAAW;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBiC,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACC,IAAI,CAAE;kBAAAe,QAAA,EAC3C;gBAED;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5C,OAAA,CAACb,UAAU;kBACTgD,KAAK,EAAC,oEAAa;kBACnB2D,SAAS,EAAEA,CAAA,KAAM3C,gBAAgB,CAACD,IAAI,CAAC6C,OAAO,CAAE;kBAChDlD,MAAM,EAAC,cAAI;kBACXE,UAAU,EAAC,cAAI;kBAAAkB,QAAA,eAEfjE,OAAA,CAACvB,MAAM;oBACLiG,IAAI,EAAC,MAAM;oBACXsB,MAAM;oBACN1D,IAAI,eAAEtC,OAAA,CAACT,YAAY;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAqB,QAAA,EACxB;kBAED;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAqB,QAAA,eAEFjE,OAAA,CAACxB,IAAI,CAACoH,IAAI,CAACK,IAAI;kBACbC,MAAM,EAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;kBACvCvB,KAAK,eACHnC,OAAA,CAACtB,KAAK;oBAAAuF,QAAA,gBACJjE,OAAA,CAACI,IAAI;sBAAC+F,MAAM;sBAAAlC,QAAA,EAAEjD,cAAc,CAACkC,IAAI,CAACkD,SAAS;oBAAC;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACD8C,WAAW,eACT1F,OAAA;oBAAAiE,QAAA,gBACEjE,OAAA,CAACI,IAAI;sBAACsE,IAAI,EAAC,WAAW;sBAAC2B,QAAQ,EAAE;wBAAEC,IAAI,EAAEpD,IAAI,CAAC6C;sBAAQ,CAAE;sBAAA9B,QAAA,GAAC,MACnD,EAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAAC6C,OAAO,CAAC1D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAElC,CAAC,eACP5C,OAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;sBAACsE,IAAI,EAAC,WAAW;sBAAAT,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAACwD,UAAU,CAAC;oBAAA;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEM,IAAI,CAAClE,OAAO,iBACXgB,OAAA,CAAAE,SAAA;sBAAA+D,QAAA,gBACEjE,OAAA;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;wBAACsE,IAAI,EAAC,WAAW;wBAAAT,QAAA,GAAC,gBAAI,EAACf,IAAI,CAAClE,OAAO;sBAAA;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAM,IAAI,CAACyD,QAAQ,KAAKC,SAAS,iBAC1B5G,OAAA;sBAAKuC,KAAK,EAAE;wBAAEsE,SAAS,EAAE;sBAAE,CAAE;sBAAA5C,QAAA,eAC3BjE,OAAA,CAACnB,QAAQ;wBAACiI,OAAO,EAAE5D,IAAI,CAACyD,QAAS;wBAACI,IAAI,EAAC;sBAAO;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5C,OAAA,CAAC1B,GAAG;QAACyG,IAAI,EAAE,EAAG;QAAAd,QAAA,eACZjE,OAAA,CAAC5B,IAAI;UACH+D,KAAK,eACHnC,OAAA,CAACtB,KAAK;YAAAuF,QAAA,gBACJjE,OAAA,CAACX,mBAAmB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAA5C,OAAA,CAACpB,GAAG;cAAC4D,KAAK,EAAC,SAAS;cAAAyB,QAAA,EAAExD,cAAc,CAAC8C;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACDyC,KAAK,eACHrF,OAAA,CAACvB,MAAM;YACLiG,IAAI,EAAC,SAAS;YACdsB,MAAM;YACNe,IAAI,EAAC,OAAO;YACZzE,IAAI,eAAEtC,OAAA,CAACN,cAAc;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBoE,QAAQ,EAAEvG,cAAc,CAAC8C,MAAM,KAAK,CAAE;YACtCsB,OAAO,EAAExB,oBAAqB;YAAAY,QAAA,EAC/B;UAED;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAqB,QAAA,eAEDjE,OAAA,CAACxB,IAAI;YACH+G,UAAU,EAAE9E,cAAe;YAC3B+E,MAAM,EAAE;cACNC,SAAS,eACPzF,OAAA,CAACjB,KAAK;gBACJ2G,WAAW,eACT1F,OAAA;kBAAAiE,QAAA,gBACEjE,OAAA;oBAAAiE,QAAA,EAAK;kBAAQ;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnB5C,OAAA,CAACI,IAAI;oBAACsE,IAAI,EAAC,WAAW;oBAACnC,KAAK,EAAE;sBAAEgC,QAAQ,EAAE;oBAAO,CAAE;oBAAAN,QAAA,EAAC;kBAEpD;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACF+C,UAAU,EAAGzC,IAAI,iBACflD,OAAA,CAACxB,IAAI,CAACoH,IAAI;cACRC,OAAO,EAAE,cACP7F,OAAA,CAACvB,MAAM;gBACLiG,IAAI,EAAC,MAAM;gBACXpC,IAAI,eAAEtC,OAAA,CAACP,WAAW;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBiC,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACC,IAAI,CAAE;gBAAAe,QAAA,EAC3C;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5C,OAAA,CAACvB,MAAM;gBACLiG,IAAI,EAAC,MAAM;gBACXsB,MAAM;gBACN1D,IAAI,eAAEtC,OAAA,CAACH,mBAAmB;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC9BiC,OAAO,EAAEA,CAAA,KAAM7C,sBAAsB,CAACkB,IAAI,CAAC6C,OAAO,CAAE;gBAAA9B,QAAA,EACrD;cAED;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAqB,QAAA,eAEFjE,OAAA,CAACxB,IAAI,CAACoH,IAAI,CAACK,IAAI;gBACbC,MAAM,EAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE;gBACvCvB,KAAK,eACHnC,OAAA,CAACtB,KAAK;kBAAAuF,QAAA,gBACJjE,OAAA,CAACI,IAAI;oBAAC+F,MAAM;oBAAAlC,QAAA,EAAEjD,cAAc,CAACkC,IAAI,CAACkD,SAAS;kBAAC;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDoB,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACD8C,WAAW,eACT1F,OAAA;kBAAAiE,QAAA,gBACEjE,OAAA,CAACI,IAAI;oBAACsE,IAAI,EAAC,WAAW;oBAAC2B,QAAQ,EAAE;sBAAEC,IAAI,EAAEpD,IAAI,CAAC6C;oBAAQ,CAAE;oBAAA9B,QAAA,GAAC,MACnD,EAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GACzF,GAAGa,IAAI,CAAC6C,OAAO,CAAC1D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAElC,CAAC,eACP5C,OAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;oBAACsE,IAAI,EAAC,WAAW;oBAAAT,QAAA,GAAC,4BAAM,EAACC,UAAU,CAAChB,IAAI,CAAC+D,UAAU,CAAC;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEM,IAAI,CAACQ,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,IAAIZ,IAAI,CAACnB,KAAK,iBAC/C/B,OAAA,CAAAE,SAAA;oBAAA+D,QAAA,gBACEjE,OAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;sBAACsE,IAAI,EAAC,QAAQ;sBAAAT,QAAA,GAAC,gBAAI,EAACf,IAAI,CAACnB,KAAK;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA,CAAClB,KAAK;MACJqD,KAAK,EAAC,0BAAM;MACZ+E,IAAI,EAAE9F,iBAAkB;MACxB+F,QAAQ,EAAEA,CAAA,KAAM9F,oBAAoB,CAAC,KAAK,CAAE;MAC5C+F,MAAM,EAAE,cACNpH,OAAA,CAACvB,MAAM;QAAaoG,OAAO,EAAEA,CAAA,KAAMxD,oBAAoB,CAAC,KAAK,CAAE;QAAA4C,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFyE,KAAK,EAAE,GAAI;MAAApD,QAAA,EAEV/C,YAAY,iBACXlB,OAAA;QAAAiE,QAAA,eACEjE,OAAA,CAAC3B,GAAG;UAACyG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAb,QAAA,gBACpBjE,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAACiG,QAAQ;cAAApC,QAAA,EAAE/C,YAAY,CAAC6E;YAAO;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EAAEjD,cAAc,CAACE,YAAY,CAACkF,SAAS;YAAC;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLoB,gBAAgB,CAAC9C,YAAY,CAACwC,MAAM,CAAC;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL1B,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC4C,SAAS,gBAC5C7D,OAAA,CAACnB,QAAQ;cAACiI,OAAO,EAAE,GAAI;cAACC,IAAI,EAAC,OAAO;cAACrD,MAAM,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACtD1B,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,gBAC5C9D,OAAA,CAACnB,QAAQ;cAACiI,OAAO,EAAE5F,YAAY,CAACyF,QAAQ,IAAI,CAAE;cAACI,IAAI,EAAC,OAAO;cAACrD,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E1B,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC8C,SAAS,gBAC/C/D,OAAA,CAACnB,QAAQ;cAACiI,OAAO,EAAE5F,YAAY,CAACyF,QAAQ,IAAI,CAAE;cAACI,IAAI,EAAC,OAAO;cAACrD,MAAM,EAAC;YAAW;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC/E1B,YAAY,CAACyF,QAAQ,KAAKC,SAAS,gBACrC5G,OAAA,CAACnB,QAAQ;cAACiI,OAAO,EAAE5F,YAAY,CAACyF,QAAS;cAACI,IAAI,EAAC;YAAO;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD5C,OAAA,CAACI,IAAI;cAACsE,IAAI,EAAC,WAAW;cAAAT,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EAAEC,UAAU,CAAChD,YAAY,CAACwF,UAAU;YAAC;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EAAEC,UAAU,CAAChD,YAAY,CAAC+F,UAAU;YAAC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN5C,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EACF,CAACqD,oBAAA,IAAM;gBACN;gBACA,IAAIpG,YAAY,CAACqG,UAAU,IAAIrG,YAAY,CAACsG,YAAY,EAAE;kBACxD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIvD,IAAI,CAAClD,YAAY,CAACsG,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAIxD,IAAI,CAAClD,YAAY,CAACqG,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACjI,OAAO,GAAGH,QAAQ,GAAG;gBACvB;gBACA;gBAAA,KACK,IAAIvG,YAAY,CAACqG,UAAU,IAAIrG,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC0C,OAAO,EAAE;kBAC/E,MAAM8D,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIvD,IAAI,CAAC,CAAC,CAACwD,OAAO,CAAC,CAAC,GAAG,IAAIxD,IAAI,CAAClD,YAAY,CAACqG,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;kBACxG,OAAO,GAAGH,QAAQ,SAAS;gBAC7B;gBACA;gBAAA,KACK,IAAIvG,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC4C,SAAS,KAAAyD,oBAAA,GAAIpG,YAAY,CAAC2G,MAAM,cAAAP,oBAAA,eAAnBA,oBAAA,CAAqBQ,gBAAgB,EAAE;kBAC/F,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACzG,YAAY,CAAC2G,MAAM,CAACC,gBAAgB,CAAC,GAAG;gBAC/D;gBACA;gBAAA,KACK,IAAI5G,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC4C,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK,IAAI3C,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC6C,MAAM,EAAE;kBACnD,OAAO,MAAM;gBACf,CAAC,MACI,IAAI5C,YAAY,CAACwC,MAAM,KAAKzC,WAAW,CAAC8C,SAAS,EAAE;kBACtD,OAAO,KAAK;gBACd;gBACA;gBAAA,KACK;kBACH,OAAO,MAAM;gBACf;cACF,CAAC,EAAE;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL1B,YAAY,CAAC6G,YAAY,iBACxB/H,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EAAE/C,YAAY,CAAC6G;YAAY;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACN,EACA1B,YAAY,CAAClC,OAAO,iBACnBgB,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAAA6D,QAAA,EAAE/C,YAAY,CAAClC;YAAO;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA1B,YAAY,CAACa,KAAK,iBACjB/B,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACI,IAAI;cAACsE,IAAI,EAAC,QAAQ;cAAAT,QAAA,EAAE/C,YAAY,CAACa;YAAK;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA1B,YAAY,CAAC8G,MAAM,iBAClBhI,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA;cAAKuC,KAAK,EAAE;gBACV0F,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,CAAC;gBACf5D,QAAQ,EAAE,EAAE;gBACZ6D,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE,MAAM;gBAChBxB,SAAS,EAAE;cACb,CAAE;cAAA5C,QAAA,eACAjE,OAAA;gBAAAiE,QAAA,EAAMqE,IAAI,CAACC,SAAS,CAACrH,YAAY,CAAC8G,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA1B,YAAY,CAAC2G,MAAM,IAAI3G,YAAY,CAACkF,SAAS,KAAK,UAAU,iBAC3DpG,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACtB,KAAK;cAAC8J,IAAI;cAACjG,KAAK,EAAE;gBAAEsE,SAAS,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAClCjE,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAEtC,OAAA,CAACX,mBAAmB;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL1B,YAAY,CAAC2G,MAAM,CAACC,gBAAgB,iBACnC9H,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,gBACZ,EAACyD,IAAI,CAACC,KAAK,CAACzG,YAAY,CAAC2G,MAAM,CAACC,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACA1B,YAAY,CAAC2G,MAAM,CAACY,OAAO,iBAC1BzI,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,4BACZ,EAACyE,MAAM,CAACC,IAAI,CAACzH,YAAY,CAAC2G,MAAM,CAACY,OAAO,CAAC,CAAClF,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5C,OAAA;cAAKuC,KAAK,EAAE;gBAAEsE,SAAS,EAAE;cAAE,CAAE;cAAA5C,QAAA,eAC3BjE,OAAA,CAACI,IAAI;gBAACsE,IAAI,EAAC,WAAW;gBAAAT,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACA1B,YAAY,CAAC2G,MAAM,IAAI3G,YAAY,CAACkF,SAAS,KAAK,YAAY,iBAC7DpG,OAAA,CAAC1B,GAAG;YAACyG,IAAI,EAAE,EAAG;YAAAd,QAAA,gBACZjE,OAAA,CAACI,IAAI;cAAC+F,MAAM;cAAAlC,QAAA,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB5C,OAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5C,OAAA,CAACtB,KAAK;cAAC8J,IAAI;cAACjG,KAAK,EAAE;gBAAEsE,SAAS,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAClCjE,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,OAAO;gBAACF,IAAI,eAAEtC,OAAA,CAACX,mBAAmB;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAqB,QAAA,EAAC;cAElD;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL1B,YAAY,CAAC2G,MAAM,CAACe,aAAa,KAAKhC,SAAS,iBAC9C5G,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,KAAK;gBAAAyB,QAAA,GAAC,4BAAM,EAAC/C,YAAY,CAAC2G,MAAM,CAACe,aAAa;cAAA;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAChE,EACA,EAAArC,qBAAA,GAAAW,YAAY,CAAC2G,MAAM,CAACgB,WAAW,cAAAtI,qBAAA,uBAA/BA,qBAAA,CAAiCgD,MAAM,kBACtCvD,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,MAAM;gBAAAyB,QAAA,GAAC,4BAAM,EAAC/C,YAAY,CAAC2G,MAAM,CAACgB,WAAW,CAACtF,MAAM;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACtE,EACA1B,YAAY,CAAC2G,MAAM,CAACC,gBAAgB,iBACnC9H,OAAA,CAACpB,GAAG;gBAAC4D,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,GAAC,gBACd,EAACyD,IAAI,CAACC,KAAK,CAACzG,YAAY,CAAC2G,MAAM,CAACC,gBAAgB,CAAC,EAAC,QACxD;cAAA;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACR5C,OAAA;cAAKuC,KAAK,EAAE;gBAAEsE,SAAS,EAAE;cAAE,CAAE;cAAA5C,QAAA,eAC3BjE,OAAA,CAACI,IAAI;gBAACsE,IAAI,EAAC,WAAW;gBAAAT,QAAA,EAAC;cAEvB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtC,EAAA,CAlkBID,eAAyB;EAAA,QAYzBP,cAAc;AAAA;AAAAgJ,EAAA,GAZdzI,eAAyB;AAokB/B,eAAeA,eAAe;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}