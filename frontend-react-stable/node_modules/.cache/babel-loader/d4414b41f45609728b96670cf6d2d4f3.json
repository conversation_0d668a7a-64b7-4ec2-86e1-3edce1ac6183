{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Select from '../select';\nvar MiniSelect = function MiniSelect(props) {\n  return /*#__PURE__*/React.createElement(Select, _extends({}, props, {\n    size: \"small\"\n  }));\n};\nvar MiddleSelect = function MiddleSelect(props) {\n  return /*#__PURE__*/React.createElement(Select, _extends({}, props, {\n    size: \"middle\"\n  }));\n};\nMiniSelect.Option = Select.Option;\nMiddleSelect.Option = Select.Option;\nexport { MiniSelect, MiddleSelect };", "map": {"version": 3, "names": ["_extends", "React", "Select", "MiniSelect", "props", "createElement", "size", "MiddleSelect", "Option"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/pagination/Select.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Select from '../select';\nvar MiniSelect = function MiniSelect(props) {\n  return /*#__PURE__*/React.createElement(Select, _extends({}, props, {\n    size: \"small\"\n  }));\n};\nvar MiddleSelect = function MiddleSelect(props) {\n  return /*#__PURE__*/React.createElement(Select, _extends({}, props, {\n    size: \"middle\"\n  }));\n};\nMiniSelect.Option = Select.Option;\nMiddleSelect.Option = Select.Option;\nexport { MiniSelect, MiddleSelect };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,MAAM,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEI,KAAK,EAAE;IAClEE,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACH,KAAK,EAAE;EAC9C,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,MAAM,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEI,KAAK,EAAE;IAClEE,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDH,UAAU,CAACK,MAAM,GAAGN,MAAM,CAACM,MAAM;AACjCD,YAAY,CAACC,MAAM,GAAGN,MAAM,CAACM,MAAM;AACnC,SAASL,UAAU,EAAEI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}