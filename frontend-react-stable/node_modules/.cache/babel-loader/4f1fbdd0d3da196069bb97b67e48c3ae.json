{"ast": null, "code": "var getNative = require('./_getNative');\nvar defineProperty = function () {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}();\nmodule.exports = defineProperty;", "map": {"version": 3, "names": ["getNative", "require", "defineProperty", "func", "Object", "e", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_defineProperty.js"], "sourcesContent": ["var getNative = require('./_getNative');\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nmodule.exports = defineProperty;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEvC,IAAIC,cAAc,GAAI,YAAW;EAC/B,IAAI;IACF,IAAIC,IAAI,GAAGH,SAAS,CAACI,MAAM,EAAE,gBAAgB,CAAC;IAC9CD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChB,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAE;AAEJC,MAAM,CAACC,OAAO,GAAGL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script"}