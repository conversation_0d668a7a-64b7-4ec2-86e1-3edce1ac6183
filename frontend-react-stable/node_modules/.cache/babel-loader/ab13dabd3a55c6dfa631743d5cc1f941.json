{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Radio,Upload,Input,Select,Button,Typography,Space,Divider,message,Spin,InputNumber,Slider,Checkbox,Progress,Alert,Row,Col,Statistic}from'antd';import{InboxOutlined,PlayCircleOutlined,SettingOutlined,ExperimentOutlined}from'@ant-design/icons';import{LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend,ResponsiveContainer}from'recharts';import{modelTrainingAPI}from'../services/api';import useTaskManager from'../hooks/useTaskManager';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Dragger}=Upload;const{Option}=Select;// 训练结果展示组件\nconst TrainingResultDisplay=_ref=>{var _result$train_losses,_result$val_losses;let{resultKey,result}=_ref;const[selectedProt,selectedDatatype]=resultKey.split('_',2);return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u534F\\u8BAE:\"}),\" \",selectedProt]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6570\\u636E\\u7C7B\\u578B:\"}),\" \",selectedDatatype]})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8BAD\\u7EC3\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",value:result.train_shape?`${result.train_shape[0]} × ${result.train_shape[1]}`:'N/A'})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6D4B\\u8BD5\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",value:result.test_shape?`${result.test_shape[0]} × ${result.test_shape[1]}`:'N/A'})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"R\\xB2 \\u5206\\u6570\",value:result.r2_score?result.r2_score.toFixed(4):result.r2?result.r2.toFixed(4):'N/A',precision:4,valueStyle:{color:result.r2_score>0.8||result.r2>0.8?'#3f8600':'#cf1322'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C\",value:result.static_anomaly_threshold?result.static_anomaly_threshold.toFixed(2):'N/A',precision:2})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginTop:16},children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"CPU\\u4F7F\\u7528\\u7387\",value:result.cpu_percent?`${result.cpu_percent.toFixed(2)}%`:'N/A',valueStyle:{color:'#1890ff'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5185\\u5B58\\u4F7F\\u7528\",value:result.memory_mb?`${result.memory_mb.toFixed(2)} MB`:'N/A',valueStyle:{color:'#1890ff'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u5185\\u5B58\",value:result.gpu_memory_mb?`${result.gpu_memory_mb.toFixed(2)} MB`:result.gpu_memory?`${result.gpu_memory.toFixed(2)} MB`:'N/A',valueStyle:{color:'#1890ff'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u4F7F\\u7528\\u7387\",value:result.gpu_utilization_percent?`${result.gpu_utilization_percent.toFixed(2)}%`:result.gpu_utilization?`${result.gpu_utilization.toFixed(2)}%`:'N/A',valueStyle:{color:'#1890ff'}})})]}),result.train_losses&&result.val_losses&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BAD\\u7EC3\\u635F\\u5931\\u66F2\\u7EBF\"}),/*#__PURE__*/_jsx(\"div\",{style:{height:300,marginTop:8},children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:\"100%\",children:/*#__PURE__*/_jsxs(LineChart,{data:result.train_losses.map((trainLoss,index)=>({epoch:index+1,训练损失:trainLoss,验证损失:result.val_losses[index]||null})),margin:{top:5,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"epoch\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"\\u8BAD\\u7EC3\\u635F\\u5931\",stroke:\"#8884d8\",strokeWidth:2,dot:false}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"\\u9A8C\\u8BC1\\u635F\\u5931\",stroke:\"#82ca9d\",strokeWidth:2,dot:false})]})})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \",result.train_losses.length,\" epochs | \\u6700\\u7EC8\\u8BAD\\u7EC3\\u635F\\u5931: \",(_result$train_losses=result.train_losses[result.train_losses.length-1])===null||_result$train_losses===void 0?void 0:_result$train_losses.toFixed(6),\" | \\u6700\\u7EC8\\u9A8C\\u8BC1\\u635F\\u5931: \",(_result$val_losses=result.val_losses[result.val_losses.length-1])===null||_result$val_losses===void 0?void 0:_result$val_losses.toFixed(6)]})})]}),result.y_test_actual&&result.y_pred&&result.y_test_actual.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5B9E\\u9645\\u503C vs \\u9884\\u6D4B\\u503C\\u5BF9\\u6BD4\\u56FE\"}),/*#__PURE__*/_jsx(\"div\",{style:{height:300,marginTop:8},children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:\"100%\",children:/*#__PURE__*/_jsxs(LineChart,{data:result.y_test_actual.map((actual,index)=>({index:index+1,实际值:actual,预测值:result.y_pred[index]})),margin:{top:5,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"index\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"\\u5B9E\\u9645\\u503C\",stroke:\"#8884d8\",strokeWidth:2,dot:false}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"\\u9884\\u6D4B\\u503C\",stroke:\"#82ca9d\",strokeWidth:2,dot:false})]})})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u663E\\u793A\\u6240\\u6709 \",result.y_test_actual.length,\" \\u4E2A\\u6D4B\\u8BD5\\u6837\\u672C\\u7684\\u9884\\u6D4B\\u5BF9\\u6BD4 | R\\xB2 \\u5206\\u6570: \",result.r2_score?result.r2_score.toFixed(4):result.r2?result.r2.toFixed(4):'N/A',\" | \\u5EFA\\u8BAE\\u9608\\u503C: \",result.static_anomaly_threshold?result.static_anomaly_threshold.toFixed(2):'N/A']})})]}),result.model_save_path&&/*#__PURE__*/_jsx(Alert,{message:\"\\u6A21\\u578B\\u6587\\u4EF6\\u4FE1\\u606F\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84:\"}),\" \",result.model_save_path]}),result.scaler_y_save_path&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6807\\u51C6\\u5316\\u5668\\u4FDD\\u5B58\\u8DEF\\u5F84:\"}),\" \",result.scaler_y_save_path]}),result.params_save_path&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u53C2\\u6570\\u4FDD\\u5B58\\u8DEF\\u5F84:\"}),\" \",result.params_save_path]}),result.test_save_path&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6D4B\\u8BD5\\u6570\\u636E\\u4FDD\\u5B58\\u8DEF\\u5F84:\"}),\" \",result.test_save_path]}),result.static_anomaly_threshold&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C:\"}),\" \",result.static_anomaly_threshold.toFixed(2)]}),result.finished_time&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u65F6\\u95F4:\"}),\" \",result.finished_time]}),result.duration_seconds&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BAD\\u7EC3\\u8017\\u65F6:\"}),\" \",result.duration_seconds.toFixed(2),\" \\u79D2\"]})]}),type:\"info\",showIcon:true})]})});};const ModelTrainingPage=()=>{const[dataSource,setDataSource]=useState('upload');const[uploadedFile,setUploadedFile]=useState(null);const[csvDir,setCsvDir]=useState('');const[availableFiles,setAvailableFiles]=useState([]);const[selectedFile,setSelectedFile]=useState('');const[filesLoading,setFilesLoading]=useState(false);// 协议和数据类型选择（与Streamlit版本一致）\nconst[selectedProts,setSelectedProts]=useState(['TCP']);const[selectedDatatypes,setSelectedDatatypes]=useState({TCP:['spt_sip_dip']});// 训练参数\nconst[learningRate,setLearningRate]=useState(0.0001);const[batchSize,setBatchSize]=useState(64);const[epochs,setEpochs]=useState(100);const[sequenceLength,setSequenceLength]=useState(10);const[hiddenSize,setHiddenSize]=useState(50);const[numLayers,setNumLayers]=useState(2);const[dropout,setDropout]=useState(0.2);const[outputFolder,setOutputFolder]=useState('');// 训练状态\nconst[training,setTraining]=useState(false);const[progress,setProgress]=useState(0);const[trainingResults,setTrainingResults]=useState(null);const[selectedResultKey,setSelectedResultKey]=useState('');// 任务管理\nconst{submitTrainingTask}=useTaskManager();const[useAsyncTraining,setUseAsyncTraining]=useState(true);// 默认使用异步训练\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions=['TCP','UDP','ICMP'];const datatypeOptions={TCP:['spt_sip_dip','dpt_sip_dip','len_dpt_syn','seq_ack_dip'],UDP:['spt_sip_dip','dpt_sip_dip'],ICMP:['dip']};// 获取CSV文件列表\nconst fetchCsvFiles=async()=>{if(!csvDir)return;setFilesLoading(true);try{const response=await modelTrainingAPI.listCsvFiles(csvDir);setAvailableFiles(response.data.files||[]);}catch(error){var _error$response,_error$response$data;message.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'获取文件列表失败');setAvailableFiles([]);}finally{setFilesLoading(false);}};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(dataSource==='local'&&csvDir&&csvDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchCsvFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[dataSource,csvDir]);// 文件上传配置\nconst uploadProps={name:'file',multiple:false,accept:'.csv',beforeUpload:()=>false,onChange:info=>{if(info.fileList.length>0){setUploadedFile(info.fileList[0]);}else{setUploadedFile(null);}}};// 协议选择变化处理（与Streamlit版本一致）\nconst handleProtocolChange=prots=>{setSelectedProts(prots);// 为新选择的协议添加默认数据类型\nconst newDatatypes={...selectedDatatypes};prots.forEach(prot=>{if(!newDatatypes[prot]&&datatypeOptions[prot]){newDatatypes[prot]=[datatypeOptions[prot][0]];}});// 移除未选择协议的数据类型\nObject.keys(newDatatypes).forEach(prot=>{if(!prots.includes(prot)){delete newDatatypes[prot];}});setSelectedDatatypes(newDatatypes);};// 数据类型选择变化处理\nconst handleDatatypeChange=(protocol,datatypes)=>{setSelectedDatatypes(prev=>({...prev,[protocol]:datatypes}));};// 开始训练\nconst handleStartTraining=async()=>{// 验证输入\nif(dataSource==='upload'&&!uploadedFile){message.error('请上传CSV文件');return;}if(dataSource==='local'&&(!csvDir||!selectedFile)){message.error('请选择CSV文件');return;}if(selectedProts.length===0){message.error('请至少选择一种协议');return;}const hasValidDatatypes=selectedProts.some(prot=>selectedDatatypes[prot]&&selectedDatatypes[prot].length>0);if(!hasValidDatatypes){message.error('请为每个协议至少选择一种数据类型');return;}setTraining(true);setProgress(0);setTrainingResults(null);setSelectedResultKey('');try{if(useAsyncTraining){// 异步训练模式\nconst formData=new FormData();if(dataSource==='upload'){formData.append('file',uploadedFile.originFileObj);}else{formData.append('csv_dir',csvDir);formData.append('selected_file',selectedFile);}formData.append('selected_prots',JSON.stringify(selectedProts));formData.append('selected_datatypes',JSON.stringify(selectedDatatypes));formData.append('learning_rate',learningRate.toString());formData.append('batch_size',batchSize.toString());formData.append('epochs',epochs.toString());formData.append('sequence_length',sequenceLength.toString());formData.append('hidden_size',hiddenSize.toString());formData.append('num_layers',numLayers.toString());formData.append('dropout',dropout.toString());formData.append('output_folder',outputFolder);// 提交异步任务\nconst taskId=await submitTrainingTask(formData);if(taskId){message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');// 重置状态\nsetTraining(false);setProgress(0);}return;// 异步模式下直接返回\n}// 同步训练模式（保留原有逻辑）\nlet response;if(dataSource==='upload'){const formData=new FormData();formData.append('file',uploadedFile.originFileObj);formData.append('selected_prots',JSON.stringify(selectedProts));formData.append('selected_datatypes',JSON.stringify(selectedDatatypes));formData.append('learning_rate',learningRate.toString());formData.append('batch_size',batchSize.toString());formData.append('epochs',epochs.toString());formData.append('sequence_length',sequenceLength.toString());formData.append('hidden_size',hiddenSize.toString());formData.append('num_layers',numLayers.toString());formData.append('dropout',dropout.toString());formData.append('output_folder',outputFolder);// 模拟进度更新\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+5;});},1000);response=await modelTrainingAPI.trainModel(formData);clearInterval(progressInterval);}else{// 本地文件训练逻辑\nconst localTrainingData={csv_dir:csvDir,selected_file:selectedFile,selected_prots:selectedProts,selected_datatypes:selectedDatatypes,learning_rate:learningRate,batch_size:batchSize,epochs:epochs,sequence_length:sequenceLength,hidden_size:hiddenSize,num_layers:numLayers,dropout:dropout,output_folder:outputFolder};// 模拟进度更新\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+5;});},1000);response=await modelTrainingAPI.trainModelLocal(localTrainingData);clearInterval(progressInterval);}setProgress(100);// 添加调试信息\nconsole.log('训练响应数据:',response.data);setTrainingResults(response.data);// 设置默认选择第一个结果\nif(response.data.results&&Object.keys(response.data.results).length>0){setSelectedResultKey(Object.keys(response.data.results)[0]);}message.success('模型训练完成！');// 显示训练结果路径信息\nif(response.data.result_path){message.info(`结果已保存至: ${response.data.result_path}`);}}catch(error){console.error('训练错误详情:',error);console.error('错误响应:',error.response);// 更详细的错误信息\nlet errorMessage='模型训练失败';if(error.response){var _error$response$data2,_error$response$data3;if((_error$response$data2=error.response.data)!==null&&_error$response$data2!==void 0&&_error$response$data2.detail){errorMessage=error.response.data.detail;}else if((_error$response$data3=error.response.data)!==null&&_error$response$data3!==void 0&&_error$response$data3.message){errorMessage=error.response.data.message;}else if(error.response.statusText){errorMessage=`请求失败: ${error.response.status} ${error.response.statusText}`;}}else if(error.message){errorMessage=error.message;}message.error(errorMessage);}finally{setTraining(false);}};const isFormValid=()=>{if(dataSource==='upload'){return uploadedFile&&selectedProts.length>0;}else{return csvDir&&selectedFile&&selectedProts.length>0;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6570\\u636E\\u6E90\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:dataSource,onChange:e=>setDataSource(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"upload\",children:\"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Radio,{value:\"local\",children:\"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"})]})]}),dataSource==='upload'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsxs(Dragger,{...uploadProps,style:{marginTop:8},children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(InboxOutlined,{})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"})]})]}),dataSource==='local'&&/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:csvDir,onChange:e=>setCsvDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /home/<USER>\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchCsvFiles,loading:filesLoading,disabled:!csvDir,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:filesLoading,children:/*#__PURE__*/_jsx(Select,{value:selectedFile,onChange:setSelectedFile,placeholder:\"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:filesLoading,children:availableFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedProts,onChange:handleProtocolChange,placeholder:\"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",style:{width:'100%',marginTop:8},children:protocolOptions.map(prot=>/*#__PURE__*/_jsx(Option,{value:prot,children:prot},prot))})]}),selectedProts.map(prot=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{strong:true,children:[prot,\" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]}),/*#__PURE__*/_jsx(Checkbox.Group,{value:selectedDatatypes[prot]||[],onChange:datatypes=>handleDatatypeChange(prot,datatypes),style:{marginTop:8},children:(datatypeOptions[prot]||[]).map(datatype=>/*#__PURE__*/_jsx(Checkbox,{value:datatype,children:datatype},datatype))})]},prot))]})}),/*#__PURE__*/_jsxs(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(SettingOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\"})]}),className:\"function-card\",children:[/*#__PURE__*/_jsxs(Row,{gutter:[24,24],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{size:\"small\",title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(ExperimentOutlined,{}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u57FA\\u7840\\u53C2\\u6570\"})]}),children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5B66\\u4E60\\u7387\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:learningRate,onChange:value=>setLearningRate(value||0.0001),min:0.0001,max:1,step:0.0001,style:{width:'100%'},placeholder:\"0.0001\"})})]}),/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:batchSize,onChange:value=>setBatchSize(value||64),min:1,max:512,style:{width:'100%'},placeholder:\"64\"})})]}),/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:epochs,onChange:value=>setEpochs(value||100),min:1,max:1000,style:{width:'100%'},placeholder:\"100\"})})]})]})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{size:\"small\",title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(SettingOutlined,{}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6A21\\u578B\\u53C2\\u6570\"})]}),children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:sequenceLength,onChange:value=>setSequenceLength(value||10),min:1,max:100,style:{width:'100%'},placeholder:\"10\"})})]}),/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:hiddenSize,onChange:value=>setHiddenSize(value||50),min:10,max:512,step:10,style:{width:'100%'},placeholder:\"50\"})})]}),/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{span:10,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5C42\\u6570\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:14,children:/*#__PURE__*/_jsx(InputNumber,{value:numLayers,onChange:value=>setNumLayers(value||2),min:1,max:10,style:{width:'100%'},placeholder:\"2\"})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%'},children:[/*#__PURE__*/_jsxs(Row,{align:\"middle\",style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Text,{strong:true,children:\"Dropout \\u6982\\u7387\\uFF1A\"})}),/*#__PURE__*/_jsx(Col,{span:12,style:{textAlign:'right'},children:/*#__PURE__*/_jsx(Text,{code:true,children:dropout})})]}),/*#__PURE__*/_jsx(Slider,{value:dropout,onChange:setDropout,min:0,max:0.9,step:0.05,marks:{0:'0',0.2:'0.2',0.5:'0.5',0.9:'0.9'}})]})]})})})]}),/*#__PURE__*/_jsx(Row,{style:{marginTop:24},children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Card,{size:\"small\",title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(InboxOutlined,{}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\"})]}),children:/*#__PURE__*/_jsx(Input,{value:outputFolder,onChange:e=>setOutputFolder(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",size:\"large\",prefix:/*#__PURE__*/_jsx(InboxOutlined,{})})})})})]}),/*#__PURE__*/_jsx(Card,{className:\"function-card\",title:\"\\u8BAD\\u7EC3\\u6A21\\u5F0F\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u8BAD\\u7EC3\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:useAsyncTraining,onChange:e=>setUseAsyncTraining(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:true,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\uFF08\\u63A8\\u8350\\uFF09\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"})]})}),/*#__PURE__*/_jsx(Radio,{value:false,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u540C\\u6B65\\u8BAD\\u7EC3\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u7B49\\u5F85\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"})]})})]})]}),useAsyncTraining&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u6A21\\u5F0F\",description:\"\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728\\u4EFB\\u52A1\\u7BA1\\u7406\\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\",type:\"info\",showIcon:true})]})}),/*#__PURE__*/_jsxs(Card,{className:\"function-card\",children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleStartTraining,loading:training,disabled:!isFormValid(),className:\"action-button\",children:training?'正在训练...':'开始训练预测'}),training&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsx(Text,{children:\"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:progress,status:\"active\"})]}),trainingResults&&trainingResults.results&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:24},children:/*#__PURE__*/_jsx(Alert,{message:\"\\u8BAD\\u7EC3\\u5B8C\\u6210\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u6240\\u6709\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF01\"}),trainingResults.result_path&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u7ED3\\u679C\\u5DF2\\u66F4\\u65B0\\u81F3:\"}),\" \",trainingResults.result_path]}),Object.entries(trainingResults.results).map(_ref2=>{let[key,result]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:8},children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u534F\\u8BAE\\u4E0E\\u6570\\u636E\\u7C7B\\u578B:\"}),\" \",key]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u6A21\\u578B\\u5DF2\\u4FDD\\u5B58\\u81F3: \",result.model_save_path]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u6807\\u51C6\\u5316\\u5668\\u5DF2\\u4FDD\\u5B58\\u81F3: \",result.scaler_y_save_path]})]},key);})]}),type:\"success\",showIcon:true,style:{marginTop:16}})}),trainingResults&&trainingResults.results&&Object.keys(trainingResults.results).length>0&&/*#__PURE__*/_jsx(Card,{title:\"\\u67E5\\u770B\\u6A21\\u578B\\u8BAD\\u7EC3\\u53CA\\u7279\\u5F81\\u9884\\u6D4B\\u7ED3\\u679C\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{value:selectedResultKey,onChange:setSelectedResultKey,style:{width:'100%',marginTop:8},placeholder:\"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",children:Object.keys(trainingResults.results).map(key=>/*#__PURE__*/_jsx(Option,{value:key,children:key},key))})]}),selectedResultKey&&trainingResults.results[selectedResultKey]&&/*#__PURE__*/_jsx(TrainingResultDisplay,{resultKey:selectedResultKey,result:trainingResults.results[selectedResultKey]})]})})]})]});};export default ModelTrainingPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "InboxOutlined", "PlayCircleOutlined", "SettingOutlined", "ExperimentOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelTrainingAPI", "useTaskManager", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "<PERSON><PERSON>", "Option", "TrainingResultDisplay", "_ref", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "result", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "children", "direction", "size", "style", "width", "gutter", "span", "title", "value", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "color", "static_anomaly_threshold", "marginTop", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_memory", "gpu_utilization_percent", "gpu_utilization", "train_losses", "val_losses", "strong", "height", "data", "map", "trainLoss", "index", "epoch", "训练损失", "验证损失", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "length", "y_test_actual", "y_pred", "actual", "实际值", "预测值", "model_save_path", "description", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "showIcon", "ModelTrainingPage", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "outputFolder", "setOutputFolder", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "submitTrainingTask", "useAsyncTraining", "setUseAsyncTraining", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "Object", "keys", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "handleStartTraining", "hasValidDatatypes", "some", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "toString", "taskId", "success", "progressInterval", "setInterval", "clearInterval", "trainModel", "localTrainingData", "csv_dir", "selected_file", "selected_prots", "selected_datatypes", "learning_rate", "batch_size", "sequence_length", "hidden_size", "num_layers", "output_folder", "trainModelLocal", "console", "log", "results", "result_path", "errorMessage", "_error$response$data2", "_error$response$data3", "statusText", "status", "isFormValid", "level", "fontSize", "fontWeight", "marginBottom", "className", "Group", "e", "target", "compact", "display", "placeholder", "flex", "onClick", "loading", "disabled", "marginLeft", "spinning", "file", "mode", "datatype", "align", "min", "max", "step", "textAlign", "code", "marks", "prefix", "icon", "percent", "entries", "_ref2", "key"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"建议清洗阈值\"\n              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              precision={2}\n            />\n          </Col>\n        </Row>\n\n        <Row gutter={16} style={{ marginTop: 16 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"CPU使用率\"\n              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"内存使用\"\n              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU内存\"\n              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU使用率\"\n              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 任务管理\n  const { submitTrainingTask } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload') {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedFile);\n        }\n\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 提交异步任务\n        const taskId = await submitTrainingTask(formData);\n\n        if (taskId) {\n          message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setTraining(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /home/<USER>\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={filesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card\n        title={\n          <Space>\n            <SettingOutlined />\n            <span>训练参数配置</span>\n          </Space>\n        }\n        className=\"function-card\"\n      >\n        <Row gutter={[24, 24]}>\n          {/* 基础参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <ExperimentOutlined />\n                  <Text strong>基础参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>学习率：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.0001)}\n                      min={0.0001}\n                      max={1}\n                      step={0.0001}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0001\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>批量大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 64)}\n                      min={1}\n                      max={512}\n                      style={{ width: '100%' }}\n                      placeholder=\"64\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>训练轮数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      min={1}\n                      max={1000}\n                      style={{ width: '100%' }}\n                      placeholder=\"100\"\n                    />\n                  </Col>\n                </Row>\n              </Space>\n            </Card>\n          </Col>\n\n          {/* 模型参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <SettingOutlined />\n                  <Text strong>模型参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>序列长度：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>隐藏层大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      min={10}\n                      max={512}\n                      step={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"50\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>层数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      min={1}\n                      max={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"2\"\n                    />\n                  </Col>\n                </Row>\n                <div style={{ width: '100%' }}>\n                  <Row align=\"middle\" style={{ marginBottom: 8 }}>\n                    <Col span={12}>\n                      <Text strong>Dropout 概率：</Text>\n                    </Col>\n                    <Col span={12} style={{ textAlign: 'right' }}>\n                      <Text code>{dropout}</Text>\n                    </Col>\n                  </Row>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    marks={{\n                      0: '0',\n                      0.2: '0.2',\n                      0.5: '0.5',\n                      0.9: '0.9'\n                    }}\n                  />\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 模型保存路径 */}\n        <Row style={{ marginTop: 24 }}>\n          <Col span={24}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <InboxOutlined />\n                  <Text strong>模型保存路径</Text>\n                </Space>\n              }\n            >\n              <Input\n                value={outputFolder}\n                onChange={(e) => setOutputFolder(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                size=\"large\"\n                prefix={<InboxOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 训练模式选择 */}\n      <Card className=\"function-card\" title=\"训练模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={useAsyncTraining}\n              onChange={(e) => setUseAsyncTraining(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value={true}>\n                <Space>\n                  异步训练（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步训练\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待训练完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncTraining && (\n            <Alert\n              message=\"异步训练模式\"\n              description=\"训练任务将在后台运行，您可以继续使用系统的其他功能。任务完成后会收到通知，可在任务管理中查看进度和结果。\"\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果 */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,QAAQ,CACRC,QAAQ,CACRC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,SAAS,KACJ,MAAM,CACb,OAASC,aAAa,CAAEC,kBAAkB,CAAEC,eAAe,CAAEC,kBAAkB,KAAQ,mBAAmB,CAC1G,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,gBAAgB,KAAQ,iBAAiB,CAClD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGhC,UAAU,CAClC,KAAM,CAAEiC,OAAQ,CAAC,CAAGrC,MAAM,CAC1B,KAAM,CAAEsC,MAAO,CAAC,CAAGpC,MAAM,CAEzB;AACA,KAAM,CAAAqC,qBAAmE,CAAGC,IAAA,EAA2B,KAAAC,oBAAA,CAAAC,kBAAA,IAA1B,CAAEC,SAAS,CAAEC,MAAO,CAAC,CAAAJ,IAAA,CAChG,KAAM,CAACK,YAAY,CAAEC,gBAAgB,CAAC,CAAGH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAE,CAAC,CAAC,CAEhE,mBACEf,IAAA,QAAAgB,QAAA,cACEd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACjEd,KAAA,QAAAc,QAAA,eACEd,KAAA,CAACE,IAAI,EAAAY,QAAA,eAAChB,IAAA,WAAAgB,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACH,YAAY,EAAO,CAAC,cAChDb,IAAA,QAAK,CAAC,cACNE,KAAA,CAACE,IAAI,EAAAY,QAAA,eAAChB,IAAA,WAAAgB,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACF,gBAAgB,EAAO,CAAC,EACnD,CAAC,cAENZ,KAAA,CAACpB,GAAG,EAACuC,MAAM,CAAE,EAAG,CAAAL,QAAA,eACdhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,4CAAS,CACfC,KAAK,CAAEZ,MAAM,CAACa,WAAW,CAAG,GAAGb,MAAM,CAACa,WAAW,CAAC,CAAC,CAAC,MAAMb,MAAM,CAACa,WAAW,CAAC,CAAC,CAAC,EAAE,CAAG,KAAM,CAC3F,CAAC,CACC,CAAC,cACNzB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,4CAAS,CACfC,KAAK,CAAEZ,MAAM,CAACc,UAAU,CAAG,GAAGd,MAAM,CAACc,UAAU,CAAC,CAAC,CAAC,MAAMd,MAAM,CAACc,UAAU,CAAC,CAAC,CAAC,EAAE,CAAG,KAAM,CACxF,CAAC,CACC,CAAC,cACN1B,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,oBAAO,CACbC,KAAK,CAAEZ,MAAM,CAACe,QAAQ,CAAGf,MAAM,CAACe,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAIhB,MAAM,CAACiB,EAAE,CAAGjB,MAAM,CAACiB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,KAAO,CACjGE,SAAS,CAAE,CAAE,CACbC,UAAU,CAAE,CAAEC,KAAK,CAAEpB,MAAM,CAACe,QAAQ,CAAG,GAAG,EAAIf,MAAM,CAACiB,EAAE,CAAG,GAAG,CAAG,SAAS,CAAG,SAAU,CAAE,CACzF,CAAC,CACC,CAAC,cACN7B,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,sCAAQ,CACdC,KAAK,CAAEZ,MAAM,CAACqB,wBAAwB,CAAGrB,MAAM,CAACqB,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,CAAG,KAAM,CAC5FE,SAAS,CAAE,CAAE,CACd,CAAC,CACC,CAAC,EACH,CAAC,cAEN5B,KAAA,CAACpB,GAAG,EAACuC,MAAM,CAAE,EAAG,CAACF,KAAK,CAAE,CAAEe,SAAS,CAAE,EAAG,CAAE,CAAAlB,QAAA,eACxChB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,uBAAQ,CACdC,KAAK,CAAEZ,MAAM,CAACuB,WAAW,CAAG,GAAGvB,MAAM,CAACuB,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC,GAAG,CAAG,KAAM,CACxEG,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhC,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAEZ,MAAM,CAACwB,SAAS,CAAG,GAAGxB,MAAM,CAACwB,SAAS,CAACR,OAAO,CAAC,CAAC,CAAC,KAAK,CAAG,KAAM,CACtEG,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhC,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,iBAAO,CACbC,KAAK,CAAEZ,MAAM,CAACyB,aAAa,CAAG,GAAGzB,MAAM,CAACyB,aAAa,CAACT,OAAO,CAAC,CAAC,CAAC,KAAK,CAAIhB,MAAM,CAAC0B,UAAU,CAAG,GAAG1B,MAAM,CAAC0B,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,KAAK,CAAG,KAAO,CAC3IG,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNhC,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,CAAE,CAAAN,QAAA,cACXhB,IAAA,CAAChB,SAAS,EACRuC,KAAK,CAAC,uBAAQ,CACdC,KAAK,CAAEZ,MAAM,CAAC2B,uBAAuB,CAAG,GAAG3B,MAAM,CAAC2B,uBAAuB,CAACX,OAAO,CAAC,CAAC,CAAC,GAAG,CAAIhB,MAAM,CAAC4B,eAAe,CAAG,GAAG5B,MAAM,CAAC4B,eAAe,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAG,KAAO,CACrKG,UAAU,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,EACH,CAAC,CAELpB,MAAM,CAAC6B,YAAY,EAAI7B,MAAM,CAAC8B,UAAU,eACvCxC,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC1BhB,IAAA,QAAKmB,KAAK,CAAE,CAAEyB,MAAM,CAAE,GAAG,CAAEV,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACxChB,IAAA,CAACJ,mBAAmB,EAACwB,KAAK,CAAC,MAAM,CAACwB,MAAM,CAAC,MAAM,CAAA5B,QAAA,cAC7Cd,KAAA,CAACb,SAAS,EACRwD,IAAI,CAAEjC,MAAM,CAAC6B,YAAY,CAACK,GAAG,CAAC,CAACC,SAAiB,CAAEC,KAAa,IAAM,CACnEC,KAAK,CAAED,KAAK,CAAG,CAAC,CAChBE,IAAI,CAAEH,SAAS,CACfI,IAAI,CAAEvC,MAAM,CAAC8B,UAAU,CAACM,KAAK,CAAC,EAAI,IACpC,CAAC,CAAC,CAAE,CACJI,MAAM,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAAxC,QAAA,eAEnDhB,IAAA,CAACP,aAAa,EAACgE,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCzD,IAAA,CAACT,KAAK,EAACmE,OAAO,CAAC,OAAO,CAAE,CAAC,cACzB1D,IAAA,CAACR,KAAK,GAAE,CAAC,cACTQ,IAAA,CAACN,OAAO,GAAE,CAAC,cACXM,IAAA,CAACL,MAAM,GAAE,CAAC,cACVK,IAAA,CAACV,IAAI,EACHqE,IAAI,CAAC,UAAU,CACfD,OAAO,CAAC,0BAAM,CACdE,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfC,GAAG,CAAE,KAAM,CACZ,CAAC,cACF9D,IAAA,CAACV,IAAI,EACHqE,IAAI,CAAC,UAAU,CACfD,OAAO,CAAC,0BAAM,CACdE,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfC,GAAG,CAAE,KAAM,CACZ,CAAC,EACO,CAAC,CACO,CAAC,CACnB,CAAC,cACN9D,IAAA,QAAKmB,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,cAC3Bd,KAAA,CAACE,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAA3C,QAAA,EAAC,4BACf,CAACJ,MAAM,CAAC6B,YAAY,CAACsB,MAAM,CAAC,kDAC1B,EAAAtD,oBAAA,CAACG,MAAM,CAAC6B,YAAY,CAAC7B,MAAM,CAAC6B,YAAY,CAACsB,MAAM,CAAG,CAAC,CAAC,UAAAtD,oBAAA,iBAAnDA,oBAAA,CAAqDmB,OAAO,CAAC,CAAC,CAAC,CAAC,2CACjE,EAAAlB,kBAAA,CAACE,MAAM,CAAC8B,UAAU,CAAC9B,MAAM,CAAC8B,UAAU,CAACqB,MAAM,CAAG,CAAC,CAAC,UAAArD,kBAAA,iBAA/CA,kBAAA,CAAiDkB,OAAO,CAAC,CAAC,CAAC,EAChE,CAAC,CACJ,CAAC,EACH,CACN,CAEAhB,MAAM,CAACoD,aAAa,EAAIpD,MAAM,CAACqD,MAAM,EAAIrD,MAAM,CAACoD,aAAa,CAACD,MAAM,CAAG,CAAC,eACvE7D,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,4DAAa,CAAM,CAAC,cACjChB,IAAA,QAAKmB,KAAK,CAAE,CAAEyB,MAAM,CAAE,GAAG,CAAEV,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACxChB,IAAA,CAACJ,mBAAmB,EAACwB,KAAK,CAAC,MAAM,CAACwB,MAAM,CAAC,MAAM,CAAA5B,QAAA,cAC7Cd,KAAA,CAACb,SAAS,EACRwD,IAAI,CAAEjC,MAAM,CAACoD,aAAa,CAAClB,GAAG,CAAC,CAACoB,MAAc,CAAElB,KAAa,IAAM,CACjEA,KAAK,CAAEA,KAAK,CAAG,CAAC,CAChBmB,GAAG,CAAED,MAAM,CACXE,GAAG,CAAExD,MAAM,CAACqD,MAAM,CAACjB,KAAK,CAC1B,CAAC,CAAC,CAAE,CACJI,MAAM,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAAxC,QAAA,eAEnDhB,IAAA,CAACP,aAAa,EAACgE,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCzD,IAAA,CAACT,KAAK,EAACmE,OAAO,CAAC,OAAO,CAAE,CAAC,cACzB1D,IAAA,CAACR,KAAK,GAAE,CAAC,cACTQ,IAAA,CAACN,OAAO,GAAE,CAAC,cACXM,IAAA,CAACL,MAAM,GAAE,CAAC,cACVK,IAAA,CAACV,IAAI,EACHqE,IAAI,CAAC,UAAU,CACfD,OAAO,CAAC,oBAAK,CACbE,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfC,GAAG,CAAE,KAAM,CACZ,CAAC,cACF9D,IAAA,CAACV,IAAI,EACHqE,IAAI,CAAC,UAAU,CACfD,OAAO,CAAC,oBAAK,CACbE,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfC,GAAG,CAAE,KAAM,CACZ,CAAC,EACO,CAAC,CACO,CAAC,CACnB,CAAC,cACN9D,IAAA,QAAKmB,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,cAC3Bd,KAAA,CAACE,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAA3C,QAAA,EAAC,2BAChB,CAACJ,MAAM,CAACoD,aAAa,CAACD,MAAM,CAAC,sFAC3B,CAACnD,MAAM,CAACe,QAAQ,CAAGf,MAAM,CAACe,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAIhB,MAAM,CAACiB,EAAE,CAAGjB,MAAM,CAACiB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,KAAM,CAAC,+BAC5F,CAAChB,MAAM,CAACqB,wBAAwB,CAAGrB,MAAM,CAACqB,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,EACvF,CAAC,CACJ,CAAC,EACH,CACN,CAIAhB,MAAM,CAACyD,eAAe,eACrBrE,IAAA,CAACnB,KAAK,EACJN,OAAO,CAAC,sCAAQ,CAChB+F,WAAW,cACTpE,KAAA,QAAAc,QAAA,eACEd,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAACyD,eAAe,EAAI,CAAC,CACvDzD,MAAM,CAAC2D,kBAAkB,eACxBrE,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,mDAAS,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAAC2D,kBAAkB,EAAI,CAC7D,CACA3D,MAAM,CAAC4D,gBAAgB,eACtBtE,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAAC4D,gBAAgB,EAAI,CACzD,CACA5D,MAAM,CAAC6D,cAAc,eACpBvE,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,mDAAS,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAAC6D,cAAc,EAAI,CACzD,CACA7D,MAAM,CAACqB,wBAAwB,eAC9B/B,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAACqB,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,EAAI,CAC5E,CACAhB,MAAM,CAAC8D,aAAa,eACnBxE,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAAC8D,aAAa,EAAI,CACtD,CACA9D,MAAM,CAAC+D,gBAAgB,eACtBzE,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACJ,MAAM,CAAC+D,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAC,CAAC,SAAE,EAAG,CACpE,EACE,CACN,CACD+B,IAAI,CAAC,MAAM,CACXiB,QAAQ,MACT,CACF,EACI,CAAC,CACL,CAAC,CAEV,CAAC,CAED,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGnH,QAAQ,CAAqB,QAAQ,CAAC,CAC1E,KAAM,CAACoH,YAAY,CAAEC,eAAe,CAAC,CAAGrH,QAAQ,CAAM,IAAI,CAAC,CAC3D,KAAM,CAACsH,MAAM,CAAEC,SAAS,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACwH,cAAc,CAAEC,iBAAiB,CAAC,CAAGzH,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAAC0H,YAAY,CAAEC,eAAe,CAAC,CAAG3H,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC4H,YAAY,CAAEC,eAAe,CAAC,CAAG7H,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAC8H,aAAa,CAAEC,gBAAgB,CAAC,CAAG/H,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC,CACrE,KAAM,CAACgI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjI,QAAQ,CAA4B,CACpFkI,GAAG,CAAE,CAAC,aAAa,CACrB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGpI,QAAQ,CAAC,MAAM,CAAC,CACxD,KAAM,CAACqI,SAAS,CAAEC,YAAY,CAAC,CAAGtI,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACuI,MAAM,CAAEC,SAAS,CAAC,CAAGxI,QAAQ,CAAC,GAAG,CAAC,CACzC,KAAM,CAACyI,cAAc,CAAEC,iBAAiB,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC2I,UAAU,CAAEC,aAAa,CAAC,CAAG5I,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6I,SAAS,CAAEC,YAAY,CAAC,CAAG9I,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAAC+I,OAAO,CAAEC,UAAU,CAAC,CAAGhJ,QAAQ,CAAC,GAAG,CAAC,CAC3C,KAAM,CAACiJ,YAAY,CAAEC,eAAe,CAAC,CAAGlJ,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAACmJ,QAAQ,CAAEC,WAAW,CAAC,CAAGpJ,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACqJ,QAAQ,CAAEC,WAAW,CAAC,CAAGtJ,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACuJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGxJ,QAAQ,CAAM,IAAI,CAAC,CACjE,KAAM,CAACyJ,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1J,QAAQ,CAAS,EAAE,CAAC,CAEtE;AACA,KAAM,CAAE2J,kBAAmB,CAAC,CAAGzH,cAAc,CAAC,CAAC,CAC/C,KAAM,CAAC0H,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7J,QAAQ,CAAC,IAAI,CAAC,CAAE;AAEhE;AACA,KAAM,CAAA8J,eAAe,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CAC9C,KAAM,CAAAC,eAAe,CAAG,CACtB7B,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CACjE8B,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAC,CACnCC,IAAI,CAAE,CAAC,KAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAAC5C,MAAM,CAAE,OAEbO,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAAsC,QAAQ,CAAG,KAAM,CAAAlI,gBAAgB,CAACmI,YAAY,CAAC9C,MAAM,CAAC,CAC5DG,iBAAiB,CAAC0C,QAAQ,CAAClF,IAAI,CAACoF,KAAK,EAAI,EAAE,CAAC,CAC9C,CAAE,MAAOC,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnB7J,OAAO,CAAC2J,KAAK,CAAC,EAAAC,eAAA,CAAAD,KAAK,CAACH,QAAQ,UAAAI,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBtF,IAAI,UAAAuF,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,UAAU,CAAC,CACzDhD,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,OAAS,CACRI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA5H,SAAS,CAAC,IAAM,CACd,GAAIiH,UAAU,GAAK,OAAO,EAAII,MAAM,EAAIA,MAAM,CAACnB,MAAM,CAAG,CAAC,CAAE,CAAE;AAC3D,KAAM,CAAAuE,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BT,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMU,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAACxD,UAAU,CAAEI,MAAM,CAAC,CAAC,CAExB;AACA,KAAM,CAAAuD,WAAW,CAAG,CAClBC,IAAI,CAAE,MAAM,CACZC,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAEA,CAAA,GAAM,KAAK,CACzBC,QAAQ,CAAGC,IAAS,EAAK,CACvB,GAAIA,IAAI,CAACC,QAAQ,CAACjF,MAAM,CAAG,CAAC,CAAE,CAC5BkB,eAAe,CAAC8D,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC,IAAM,CACL/D,eAAe,CAAC,IAAI,CAAC,CACvB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAgE,oBAAoB,CAAIC,KAAe,EAAK,CAChDvD,gBAAgB,CAACuD,KAAK,CAAC,CACvB;AACA,KAAM,CAAAC,YAAY,CAAG,CAAE,GAAGvD,iBAAkB,CAAC,CAC7CsD,KAAK,CAACE,OAAO,CAACC,IAAI,EAAI,CACpB,GAAI,CAACF,YAAY,CAACE,IAAI,CAAC,EAAI1B,eAAe,CAAC0B,IAAI,CAAiC,CAAE,CAChFF,YAAY,CAACE,IAAI,CAAC,CAAG,CAAC1B,eAAe,CAAC0B,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC,CACjF,CACF,CAAC,CAAC,CACF;AACAC,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,EAAI,CACxC,GAAI,CAACH,KAAK,CAACM,QAAQ,CAACH,IAAI,CAAC,CAAE,CACzB,MAAO,CAAAF,YAAY,CAACE,IAAI,CAAC,CAC3B,CACF,CAAC,CAAC,CACFxD,oBAAoB,CAACsD,YAAY,CAAC,CACpC,CAAC,CAED;AACA,KAAM,CAAAM,oBAAoB,CAAGA,CAACC,QAAgB,CAAEC,SAAmB,GAAK,CACtE9D,oBAAoB,CAAC+D,IAAI,GAAK,CAC5B,GAAGA,IAAI,CACP,CAACF,QAAQ,EAAGC,SACd,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC;AACA,GAAI/E,UAAU,GAAK,QAAQ,EAAI,CAACE,YAAY,CAAE,CAC5CzG,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA,GAAIpD,UAAU,GAAK,OAAO,GAAK,CAACI,MAAM,EAAI,CAACI,YAAY,CAAC,CAAE,CACxD/G,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA,GAAIxC,aAAa,CAAC3B,MAAM,GAAK,CAAC,CAAE,CAC9BxF,OAAO,CAAC2J,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA,KAAM,CAAA4B,iBAAiB,CAAGpE,aAAa,CAACqE,IAAI,CAACV,IAAI,EAC/CzD,iBAAiB,CAACyD,IAAI,CAAC,EAAIzD,iBAAiB,CAACyD,IAAI,CAAC,CAACtF,MAAM,CAAG,CAC9D,CAAC,CAED,GAAI,CAAC+F,iBAAiB,CAAE,CACtBvL,OAAO,CAAC2J,KAAK,CAAC,kBAAkB,CAAC,CACjC,OACF,CAEAlB,WAAW,CAAC,IAAI,CAAC,CACjBE,WAAW,CAAC,CAAC,CAAC,CACdE,kBAAkB,CAAC,IAAI,CAAC,CACxBE,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAI,CACF,GAAIE,gBAAgB,CAAE,CACpB;AACA,KAAM,CAAAwC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B,GAAInF,UAAU,GAAK,QAAQ,CAAE,CAC3BkF,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAElF,YAAY,CAACmF,aAAa,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEhF,MAAM,CAAC,CAClC8E,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE5E,YAAY,CAAC,CAChD,CAEA0E,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEE,IAAI,CAACC,SAAS,CAAC3E,aAAa,CAAC,CAAC,CAChEsE,QAAQ,CAACE,MAAM,CAAC,oBAAoB,CAAEE,IAAI,CAACC,SAAS,CAACzE,iBAAiB,CAAC,CAAC,CACxEoE,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEnE,YAAY,CAACuE,QAAQ,CAAC,CAAC,CAAC,CACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEjE,SAAS,CAACqE,QAAQ,CAAC,CAAC,CAAC,CACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAE/D,MAAM,CAACmE,QAAQ,CAAC,CAAC,CAAC,CAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAE7D,cAAc,CAACiE,QAAQ,CAAC,CAAC,CAAC,CAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE3D,UAAU,CAAC+D,QAAQ,CAAC,CAAC,CAAC,CACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEzD,SAAS,CAAC6D,QAAQ,CAAC,CAAC,CAAC,CACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEvD,OAAO,CAAC2D,QAAQ,CAAC,CAAC,CAAC,CAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAErD,YAAY,CAAC,CAE9C;AACA,KAAM,CAAA0D,MAAM,CAAG,KAAM,CAAAhD,kBAAkB,CAACyC,QAAQ,CAAC,CAEjD,GAAIO,MAAM,CAAE,CACVhM,OAAO,CAACiM,OAAO,CAAC,gCAAgC,CAAC,CACjD;AACAxD,WAAW,CAAC,KAAK,CAAC,CAClBE,WAAW,CAAC,CAAC,CAAC,CAChB,CAEA,OAAQ;AACV,CAEA;AACA,GAAI,CAAAa,QAAQ,CAEZ,GAAIjD,UAAU,GAAK,QAAQ,CAAE,CAC3B,KAAM,CAAAkF,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAElF,YAAY,CAACmF,aAAa,CAAC,CACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEE,IAAI,CAACC,SAAS,CAAC3E,aAAa,CAAC,CAAC,CAChEsE,QAAQ,CAACE,MAAM,CAAC,oBAAoB,CAAEE,IAAI,CAACC,SAAS,CAACzE,iBAAiB,CAAC,CAAC,CACxEoE,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEnE,YAAY,CAACuE,QAAQ,CAAC,CAAC,CAAC,CACzDN,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEjE,SAAS,CAACqE,QAAQ,CAAC,CAAC,CAAC,CACnDN,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAE/D,MAAM,CAACmE,QAAQ,CAAC,CAAC,CAAC,CAC5CN,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAE7D,cAAc,CAACiE,QAAQ,CAAC,CAAC,CAAC,CAC7DN,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE3D,UAAU,CAAC+D,QAAQ,CAAC,CAAC,CAAC,CACrDN,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEzD,SAAS,CAAC6D,QAAQ,CAAC,CAAC,CAAC,CACnDN,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEvD,OAAO,CAAC2D,QAAQ,CAAC,CAAC,CAAC,CAC9CN,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAErD,YAAY,CAAC,CAE9C;AACA,KAAM,CAAA4D,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCxD,WAAW,CAAE0C,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACde,aAAa,CAACF,gBAAgB,CAAC,CAC/B,MAAO,CAAAb,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,CAAC,CACjB,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CAER7B,QAAQ,CAAG,KAAM,CAAAlI,gBAAgB,CAAC+K,UAAU,CAACZ,QAAQ,CAAC,CACtDW,aAAa,CAACF,gBAAgB,CAAC,CACjC,CAAC,IAAM,CACL;AACA,KAAM,CAAAI,iBAAiB,CAAG,CACxBC,OAAO,CAAE5F,MAAM,CACf6F,aAAa,CAAEzF,YAAY,CAC3B0F,cAAc,CAAEtF,aAAa,CAC7BuF,kBAAkB,CAAErF,iBAAiB,CACrCsF,aAAa,CAAEnF,YAAY,CAC3BoF,UAAU,CAAElF,SAAS,CACrBE,MAAM,CAAEA,MAAM,CACdiF,eAAe,CAAE/E,cAAc,CAC/BgF,WAAW,CAAE9E,UAAU,CACvB+E,UAAU,CAAE7E,SAAS,CACrBE,OAAO,CAAEA,OAAO,CAChB4E,aAAa,CAAE1E,YACjB,CAAC,CAED;AACA,KAAM,CAAA4D,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCxD,WAAW,CAAE0C,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACde,aAAa,CAACF,gBAAgB,CAAC,CAC/B,MAAO,CAAAb,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,CAAC,CACjB,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CAER7B,QAAQ,CAAG,KAAM,CAAAlI,gBAAgB,CAAC2L,eAAe,CAACX,iBAAiB,CAAC,CACpEF,aAAa,CAACF,gBAAgB,CAAC,CACjC,CAEAvD,WAAW,CAAC,GAAG,CAAC,CAEhB;AACAuE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE3D,QAAQ,CAAClF,IAAI,CAAC,CAErCuE,kBAAkB,CAACW,QAAQ,CAAClF,IAAI,CAAC,CAEjC;AACA,GAAIkF,QAAQ,CAAClF,IAAI,CAAC8I,OAAO,EAAIrC,MAAM,CAACC,IAAI,CAACxB,QAAQ,CAAClF,IAAI,CAAC8I,OAAO,CAAC,CAAC5H,MAAM,CAAG,CAAC,CAAE,CAC1EuD,oBAAoB,CAACgC,MAAM,CAACC,IAAI,CAACxB,QAAQ,CAAClF,IAAI,CAAC8I,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7D,CAEApN,OAAO,CAACiM,OAAO,CAAC,SAAS,CAAC,CAE1B;AACA,GAAIzC,QAAQ,CAAClF,IAAI,CAAC+I,WAAW,CAAE,CAC7BrN,OAAO,CAACwK,IAAI,CAAC,WAAWhB,QAAQ,CAAClF,IAAI,CAAC+I,WAAW,EAAE,CAAC,CACtD,CAEF,CAAE,MAAO1D,KAAU,CAAE,CACnBuD,OAAO,CAACvD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BuD,OAAO,CAACvD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAACH,QAAQ,CAAC,CAEtC;AACA,GAAI,CAAA8D,YAAY,CAAG,QAAQ,CAC3B,GAAI3D,KAAK,CAACH,QAAQ,CAAE,KAAA+D,qBAAA,CAAAC,qBAAA,CAClB,IAAAD,qBAAA,CAAI5D,KAAK,CAACH,QAAQ,CAAClF,IAAI,UAAAiJ,qBAAA,WAAnBA,qBAAA,CAAqBzD,MAAM,CAAE,CAC/BwD,YAAY,CAAG3D,KAAK,CAACH,QAAQ,CAAClF,IAAI,CAACwF,MAAM,CAC3C,CAAC,IAAM,KAAA0D,qBAAA,CAAI7D,KAAK,CAACH,QAAQ,CAAClF,IAAI,UAAAkJ,qBAAA,WAAnBA,qBAAA,CAAqBxN,OAAO,CAAE,CACvCsN,YAAY,CAAG3D,KAAK,CAACH,QAAQ,CAAClF,IAAI,CAACtE,OAAO,CAC5C,CAAC,IAAM,IAAI2J,KAAK,CAACH,QAAQ,CAACiE,UAAU,CAAE,CACpCH,YAAY,CAAG,SAAS3D,KAAK,CAACH,QAAQ,CAACkE,MAAM,IAAI/D,KAAK,CAACH,QAAQ,CAACiE,UAAU,EAAE,CAC9E,CACF,CAAC,IAAM,IAAI9D,KAAK,CAAC3J,OAAO,CAAE,CACxBsN,YAAY,CAAG3D,KAAK,CAAC3J,OAAO,CAC9B,CAEAA,OAAO,CAAC2J,KAAK,CAAC2D,YAAY,CAAC,CAC7B,CAAC,OAAS,CACR7E,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAkF,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIpH,UAAU,GAAK,QAAQ,CAAE,CAC3B,MAAO,CAAAE,YAAY,EAAIU,aAAa,CAAC3B,MAAM,CAAG,CAAC,CACjD,CAAC,IAAM,CACL,MAAO,CAAAmB,MAAM,EAAII,YAAY,EAAII,aAAa,CAAC3B,MAAM,CAAG,CAAC,CAC3D,CACF,CAAC,CAED,mBACE7D,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACG,KAAK,EAACgM,KAAK,CAAE,CAAE,CAAChL,KAAK,CAAE,CAAEiL,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAtL,QAAA,CAAC,wDAAS,CAAO,CAAC,cACrGhB,IAAA,CAACI,IAAI,EAACuD,IAAI,CAAC,WAAW,CAAA3C,QAAA,CAAC,yOAEvB,CAAM,CAAC,cAEPhB,IAAA,CAAC1B,OAAO,GAAE,CAAC,cAGX0B,IAAA,CAAClC,IAAI,EAACyD,KAAK,CAAC,oBAAK,CAACgL,SAAS,CAAC,eAAe,CAAAvL,QAAA,cACzCd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAChEd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC1Bd,KAAA,CAACnC,KAAK,CAACyO,KAAK,EACVhL,KAAK,CAAEsD,UAAW,CAClBgE,QAAQ,CAAG2D,CAAC,EAAK1H,aAAa,CAAC0H,CAAC,CAACC,MAAM,CAAClL,KAAK,CAAE,CAC/CL,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAExBhB,IAAA,CAACjC,KAAK,EAACyD,KAAK,CAAC,QAAQ,CAAAR,QAAA,CAAC,6BAAO,CAAO,CAAC,cACrChB,IAAA,CAACjC,KAAK,EAACyD,KAAK,CAAC,OAAO,CAAAR,QAAA,CAAC,yCAAS,CAAO,CAAC,EAC3B,CAAC,EACX,CAAC,CAGL8D,UAAU,GAAK,QAAQ,eACtB5E,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBd,KAAA,CAACG,OAAO,KAAKoI,WAAW,CAAEtH,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAChDhB,IAAA,MAAGuM,SAAS,CAAC,sBAAsB,CAAAvL,QAAA,cACjChB,IAAA,CAACf,aAAa,GAAE,CAAC,CAChB,CAAC,cACJe,IAAA,MAAGuM,SAAS,CAAC,iBAAiB,CAAAvL,QAAA,CAAC,mFAAgB,CAAG,CAAC,cACnDhB,IAAA,MAAGuM,SAAS,CAAC,iBAAiB,CAAAvL,QAAA,CAAC,uEAE/B,CAAG,CAAC,EACG,CAAC,EACP,CACN,CAGA8D,UAAU,GAAK,OAAO,eACrB5E,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACE,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACnDd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5Bd,KAAA,CAACjC,KAAK,CAACuO,KAAK,EAACG,OAAO,MAACxL,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAC,CAAE0K,OAAO,CAAE,MAAO,CAAE,CAAA5L,QAAA,eAC5DhB,IAAA,CAAC/B,KAAK,EACJuD,KAAK,CAAE0D,MAAO,CACd4D,QAAQ,CAAG2D,CAAC,EAAKtH,SAAS,CAACsH,CAAC,CAACC,MAAM,CAAClL,KAAK,CAAE,CAC3CqL,WAAW,CAAC,4BAAkB,CAC9B1L,KAAK,CAAE,CAAE2L,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACF9M,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,SAAS,CACdoJ,OAAO,CAAEjF,aAAc,CACvBkF,OAAO,CAAExH,YAAa,CACtByH,QAAQ,CAAE,CAAC/H,MAAO,CAClB/D,KAAK,CAAE,CAAE+L,UAAU,CAAE,CAAE,CAAE,CAAAlM,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBhB,IAAA,CAACxB,IAAI,EAAC2O,QAAQ,CAAE3H,YAAa,CAAAxE,QAAA,cAC3BhB,IAAA,CAAC9B,MAAM,EACLsD,KAAK,CAAE8D,YAAa,CACpBwD,QAAQ,CAAEvD,eAAgB,CAC1BsH,WAAW,CAAC,mCAAU,CACtB1L,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEc,SAAS,CAAE,CAAE,CAAE,CACvC8K,OAAO,CAAExH,YAAa,CAAAxE,QAAA,CAErBoE,cAAc,CAACtC,GAAG,CAAEsK,IAAI,eACvBpN,IAAA,CAACM,MAAM,EAAYkB,KAAK,CAAE4L,IAAK,CAAApM,QAAA,CAC5BoM,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CAAC,EACD,CACR,EACI,CAAC,CACJ,CAAC,cAGPpN,IAAA,CAAClC,IAAI,EAACyD,KAAK,CAAC,wDAAW,CAACgL,SAAS,CAAC,eAAe,CAAAvL,QAAA,cAC/Cd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAChEd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBhB,IAAA,CAAC9B,MAAM,EACLmP,IAAI,CAAC,UAAU,CACf7L,KAAK,CAAEkE,aAAc,CACrBoD,QAAQ,CAAEG,oBAAqB,CAC/B4D,WAAW,CAAC,gCAAO,CACnB1L,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEc,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,CAEtC0G,eAAe,CAAC5E,GAAG,CAAEuG,IAAI,eACxBrJ,IAAA,CAACM,MAAM,EAAYkB,KAAK,CAAE6H,IAAK,CAAArI,QAAA,CAC5BqI,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAEL3D,aAAa,CAAC5C,GAAG,CAAEuG,IAAI,eACtBnJ,KAAA,QAAAc,QAAA,eACEd,KAAA,CAACE,IAAI,EAACuC,MAAM,MAAA3B,QAAA,EAAEqI,IAAI,CAAC,iCAAM,EAAM,CAAC,cAChCrJ,IAAA,CAACrB,QAAQ,CAAC6N,KAAK,EACbhL,KAAK,CAAEoE,iBAAiB,CAACyD,IAAI,CAAC,EAAI,EAAG,CACrCP,QAAQ,CAAGa,SAAS,EAAKF,oBAAoB,CAACJ,IAAI,CAAEM,SAAqB,CAAE,CAC3ExI,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,CAEvB,CAAC2G,eAAe,CAAC0B,IAAI,CAAiC,EAAI,EAAE,EAAEvG,GAAG,CAAEwK,QAAQ,eAC1EtN,IAAA,CAACrB,QAAQ,EAAgB6C,KAAK,CAAE8L,QAAS,CAAAtM,QAAA,CACtCsM,QAAQ,EADIA,QAEL,CACX,CAAC,CACY,CAAC,GAZTjE,IAaL,CACN,CAAC,EACG,CAAC,CACJ,CAAC,cAGPnJ,KAAA,CAACpC,IAAI,EACHyD,KAAK,cACHrB,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,eACJhB,IAAA,CAACb,eAAe,GAAE,CAAC,cACnBa,IAAA,SAAAgB,QAAA,CAAM,sCAAM,CAAM,CAAC,EACd,CACR,CACDuL,SAAS,CAAC,eAAe,CAAAvL,QAAA,eAEzBd,KAAA,CAACpB,GAAG,EAACuC,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAL,QAAA,eAEpBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAAClC,IAAI,EACHoD,IAAI,CAAC,OAAO,CACZK,KAAK,cACHrB,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,eACJhB,IAAA,CAACZ,kBAAkB,GAAE,CAAC,cACtBY,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,0BAAI,CAAM,CAAC,EACnB,CACR,CAAAA,QAAA,cAEDd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACjEd,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,0BAAI,CAAM,CAAC,CACrB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAEuE,YAAa,CACpB+C,QAAQ,CAAGtH,KAAK,EAAKwE,eAAe,CAACxE,KAAK,EAAI,MAAM,CAAE,CACtDgM,GAAG,CAAE,MAAO,CACZC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,MAAO,CACbvM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,QAAQ,CACrB,CAAC,CACC,CAAC,EACH,CAAC,cACN3M,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,CACtB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAEyE,SAAU,CACjB6C,QAAQ,CAAGtH,KAAK,EAAK0E,YAAY,CAAC1E,KAAK,EAAI,EAAE,CAAE,CAC/CgM,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTtM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,IAAI,CACjB,CAAC,CACC,CAAC,EACH,CAAC,cACN3M,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,CACtB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAE2E,MAAO,CACd2C,QAAQ,CAAGtH,KAAK,EAAK4E,SAAS,CAAC5E,KAAK,EAAI,GAAG,CAAE,CAC7CgM,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,IAAK,CACVtM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,KAAK,CAClB,CAAC,CACC,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,CACJ,CAAC,cAGN7M,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAAClC,IAAI,EACHoD,IAAI,CAAC,OAAO,CACZK,KAAK,cACHrB,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,eACJhB,IAAA,CAACb,eAAe,GAAE,CAAC,cACnBa,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,0BAAI,CAAM,CAAC,EACnB,CACR,CAAAA,QAAA,cAEDd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACjEd,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,gCAAK,CAAM,CAAC,CACtB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAE6E,cAAe,CACtByC,QAAQ,CAAGtH,KAAK,EAAK8E,iBAAiB,CAAC9E,KAAK,EAAI,EAAE,CAAE,CACpDgM,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTtM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,IAAI,CACjB,CAAC,CACC,CAAC,EACH,CAAC,cACN3M,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,sCAAM,CAAM,CAAC,CACvB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAE+E,UAAW,CAClBuC,QAAQ,CAAGtH,KAAK,EAAKgF,aAAa,CAAChF,KAAK,EAAI,EAAE,CAAE,CAChDgM,GAAG,CAAE,EAAG,CACRC,GAAG,CAAE,GAAI,CACTC,IAAI,CAAE,EAAG,CACTvM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,IAAI,CACjB,CAAC,CACC,CAAC,EACH,CAAC,cACN3M,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAAAvM,QAAA,eACjBhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,oBAAG,CAAM,CAAC,CACpB,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACvB,WAAW,EACV+C,KAAK,CAAEiF,SAAU,CACjBqC,QAAQ,CAAGtH,KAAK,EAAKkF,YAAY,CAAClF,KAAK,EAAI,CAAC,CAAE,CAC9CgM,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRtM,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzByL,WAAW,CAAC,GAAG,CAChB,CAAC,CACC,CAAC,EACH,CAAC,cACN3M,KAAA,QAAKiB,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAC5Bd,KAAA,CAACpB,GAAG,EAACyO,KAAK,CAAC,QAAQ,CAACpM,KAAK,CAAE,CAAEmL,YAAY,CAAE,CAAE,CAAE,CAAAtL,QAAA,eAC7ChB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,4BAAW,CAAM,CAAC,CAC5B,CAAC,cACNhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAACH,KAAK,CAAE,CAAEwM,SAAS,CAAE,OAAQ,CAAE,CAAA3M,QAAA,cAC3ChB,IAAA,CAACI,IAAI,EAACwN,IAAI,MAAA5M,QAAA,CAAE2F,OAAO,CAAO,CAAC,CACxB,CAAC,EACH,CAAC,cACN3G,IAAA,CAACtB,MAAM,EACL8C,KAAK,CAAEmF,OAAQ,CACfmC,QAAQ,CAAElC,UAAW,CACrB4G,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTC,IAAI,CAAE,IAAK,CACXG,KAAK,CAAE,CACL,CAAC,CAAE,GAAG,CACN,GAAG,CAAE,KAAK,CACV,GAAG,CAAE,KAAK,CACV,GAAG,CAAE,KACP,CAAE,CACH,CAAC,EACC,CAAC,EACD,CAAC,CACJ,CAAC,CACJ,CAAC,EACH,CAAC,cAGN7N,IAAA,CAAClB,GAAG,EAACqC,KAAK,CAAE,CAAEe,SAAS,CAAE,EAAG,CAAE,CAAAlB,QAAA,cAC5BhB,IAAA,CAACjB,GAAG,EAACuC,IAAI,CAAE,EAAG,CAAAN,QAAA,cACZhB,IAAA,CAAClC,IAAI,EACHoD,IAAI,CAAC,OAAO,CACZK,KAAK,cACHrB,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,eACJhB,IAAA,CAACf,aAAa,GAAE,CAAC,cACjBe,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,sCAAM,CAAM,CAAC,EACrB,CACR,CAAAA,QAAA,cAEDhB,IAAA,CAAC/B,KAAK,EACJuD,KAAK,CAAEqF,YAAa,CACpBiC,QAAQ,CAAG2D,CAAC,EAAK3F,eAAe,CAAC2F,CAAC,CAACC,MAAM,CAAClL,KAAK,CAAE,CACjDqL,WAAW,CAAC,4BAAkB,CAC9B3L,IAAI,CAAC,OAAO,CACZ4M,MAAM,cAAE9N,IAAA,CAACf,aAAa,GAAE,CAAE,CAC3B,CAAC,CACE,CAAC,CACJ,CAAC,CACH,CAAC,EACF,CAAC,cAGPe,IAAA,CAAClC,IAAI,EAACyO,SAAS,CAAC,eAAe,CAAChL,KAAK,CAAC,0BAAM,CAAAP,QAAA,cAC1Cd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,QAAQ,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eACjEd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3Bd,KAAA,CAACnC,KAAK,CAACyO,KAAK,EACVhL,KAAK,CAAEgG,gBAAiB,CACxBsB,QAAQ,CAAG2D,CAAC,EAAKhF,mBAAmB,CAACgF,CAAC,CAACC,MAAM,CAAClL,KAAK,CAAE,CACrDL,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAExBhB,IAAA,CAACjC,KAAK,EAACyD,KAAK,CAAE,IAAK,CAAAR,QAAA,cACjBd,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,EAAC,kDAEL,cAAAhB,IAAA,CAACI,IAAI,EAACuD,IAAI,CAAC,WAAW,CAACxC,KAAK,CAAE,CAAEiL,QAAQ,CAAE,EAAG,CAAE,CAAApL,QAAA,CAAC,4EAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,cACRhB,IAAA,CAACjC,KAAK,EAACyD,KAAK,CAAE,KAAM,CAAAR,QAAA,cAClBd,KAAA,CAAC7B,KAAK,EAAA2C,QAAA,EAAC,0BAEL,cAAAhB,IAAA,CAACI,IAAI,EAACuD,IAAI,CAAC,WAAW,CAACxC,KAAK,CAAE,CAAEiL,QAAQ,CAAE,EAAG,CAAE,CAAApL,QAAA,CAAC,0GAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,EACG,CAAC,EACX,CAAC,CAELwG,gBAAgB,eACfxH,IAAA,CAACnB,KAAK,EACJN,OAAO,CAAC,sCAAQ,CAChB+F,WAAW,CAAC,0TAAsD,CAClEX,IAAI,CAAC,MAAM,CACXiB,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,cAGP1E,KAAA,CAACpC,IAAI,EAACyO,SAAS,CAAC,eAAe,CAAAvL,QAAA,eAC7BhB,IAAA,CAAC7B,MAAM,EACLwF,IAAI,CAAC,SAAS,CACdzC,IAAI,CAAC,OAAO,CACZ6M,IAAI,cAAE/N,IAAA,CAACd,kBAAkB,GAAE,CAAE,CAC7B6N,OAAO,CAAElD,mBAAoB,CAC7BmD,OAAO,CAAEjG,QAAS,CAClBkG,QAAQ,CAAE,CAACf,WAAW,CAAC,CAAE,CACzBK,SAAS,CAAC,eAAe,CAAAvL,QAAA,CAExB+F,QAAQ,CAAG,SAAS,CAAG,QAAQ,CAC1B,CAAC,CAGRA,QAAQ,eACP7G,KAAA,QAAKqM,SAAS,CAAC,kBAAkB,CAAAvL,QAAA,eAC/BhB,IAAA,CAACI,IAAI,EAAAY,QAAA,CAAC,gCAAK,CAAM,CAAC,cAClBhB,IAAA,CAACpB,QAAQ,EAACoP,OAAO,CAAE/G,QAAS,CAACgF,MAAM,CAAC,QAAQ,CAAE,CAAC,EAC5C,CACN,CAGA9E,eAAe,EAAIA,eAAe,CAACwE,OAAO,eACzC3L,IAAA,QAAKmB,KAAK,CAAE,CAAEe,SAAS,CAAE,EAAG,CAAE,CAAAlB,QAAA,cAC5BhB,IAAA,CAACnB,KAAK,EACJN,OAAO,CAAC,0BAAM,CACd+F,WAAW,cACTpE,KAAA,QAAAc,QAAA,eACEhB,IAAA,MAAAgB,QAAA,CAAG,wDAAS,CAAG,CAAC,CACfmG,eAAe,CAACyE,WAAW,eAC1B1L,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACmG,eAAe,CAACyE,WAAW,EAAI,CAC7D,CACAtC,MAAM,CAAC2E,OAAO,CAAC9G,eAAe,CAACwE,OAAO,CAAC,CAAC7I,GAAG,CAACoL,KAAA,MAAC,CAACC,GAAG,CAAEvN,MAAM,CAAgB,CAAAsN,KAAA,oBACxEhO,KAAA,QAAeiB,KAAK,CAAE,CAAEe,SAAS,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACrCd,KAAA,MAAAc,QAAA,eAAGhB,IAAA,WAAAgB,QAAA,CAAQ,6CAAQ,CAAQ,CAAC,IAAC,CAACmN,GAAG,EAAI,CAAC,cACtCjO,KAAA,MAAAc,QAAA,EAAG,wCAAQ,CAACJ,MAAM,CAACyD,eAAe,EAAI,CAAC,cACvCnE,KAAA,MAAAc,QAAA,EAAG,oDAAU,CAACJ,MAAM,CAAC2D,kBAAkB,EAAI,CAAC,GAHpC4J,GAIL,CAAC,EACP,CAAC,EACC,CACN,CACDxK,IAAI,CAAC,SAAS,CACdiB,QAAQ,MACRzD,KAAK,CAAE,CAAEe,SAAS,CAAE,EAAG,CAAE,CAC1B,CAAC,CACC,CACN,CAGFiF,eAAe,EAAIA,eAAe,CAACwE,OAAO,EAAIrC,MAAM,CAACC,IAAI,CAACpC,eAAe,CAACwE,OAAO,CAAC,CAAC5H,MAAM,CAAG,CAAC,eAC5F/D,IAAA,CAAClC,IAAI,EAACyD,KAAK,CAAC,gFAAe,CAACgL,SAAS,CAAC,eAAe,CAAAvL,QAAA,cACnDd,KAAA,CAAC7B,KAAK,EAAC4C,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACC,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAJ,QAAA,eAChEd,KAAA,QAAAc,QAAA,eACEhB,IAAA,CAACI,IAAI,EAACuC,MAAM,MAAA3B,QAAA,CAAC,sFAAc,CAAM,CAAC,cAClChB,IAAA,CAAC9B,MAAM,EACLsD,KAAK,CAAE6F,iBAAkB,CACzByB,QAAQ,CAAExB,oBAAqB,CAC/BnG,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEc,SAAS,CAAE,CAAE,CAAE,CACvC2K,WAAW,CAAC,8DAAY,CAAA7L,QAAA,CAEvBsI,MAAM,CAACC,IAAI,CAACpC,eAAe,CAACwE,OAAO,CAAC,CAAC7I,GAAG,CAAEqL,GAAG,eAC5CnO,IAAA,CAACM,MAAM,EAAWkB,KAAK,CAAE2M,GAAI,CAAAnN,QAAA,CAC1BmN,GAAG,EADOA,GAEL,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAEL9G,iBAAiB,EAAIF,eAAe,CAACwE,OAAO,CAACtE,iBAAiB,CAAC,eAC9DrH,IAAA,CAACO,qBAAqB,EACpBI,SAAS,CAAE0G,iBAAkB,CAC7BzG,MAAM,CAAEuG,eAAe,CAACwE,OAAO,CAACtE,iBAAiB,CAAE,CACpD,CACF,EACI,CAAC,CACJ,CACP,EACK,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}