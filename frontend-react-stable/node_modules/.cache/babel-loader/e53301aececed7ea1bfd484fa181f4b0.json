{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = least;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction least(values) {\n  let compare = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _ascending.default;\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? (0, _ascending.default)(value, minValue) < 0 : (0, _ascending.default)(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "least", "_ascending", "_interopRequireDefault", "require", "obj", "__esModule", "values", "compare", "arguments", "length", "undefined", "min", "defined", "minValue", "element"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/least.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = least;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction least(values, compare = _ascending.default) {\n  let min;\n  let defined = false;\n\n  if (compare.length === 1) {\n    let minValue;\n\n    for (const element of values) {\n      const value = compare(element);\n\n      if (defined ? (0, _ascending.default)(value, minValue) < 0 : (0, _ascending.default)(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, min) < 0 : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n\n  return min;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,KAAKA,CAACM,MAAM,EAAgC;EAAA,IAA9BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGP,UAAU,CAACF,OAAO;EACjD,IAAIY,GAAG;EACP,IAAIC,OAAO,GAAG,KAAK;EAEnB,IAAIL,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,IAAII,QAAQ;IAEZ,KAAK,MAAMC,OAAO,IAAIR,MAAM,EAAE;MAC5B,MAAMR,KAAK,GAAGS,OAAO,CAACO,OAAO,CAAC;MAE9B,IAAIF,OAAO,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACF,OAAO,EAAED,KAAK,EAAEe,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEZ,UAAU,CAACF,OAAO,EAAED,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACxGa,GAAG,GAAGG,OAAO;QACbD,QAAQ,GAAGf,KAAK;QAChBc,OAAO,GAAG,IAAI;MAChB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMd,KAAK,IAAIQ,MAAM,EAAE;MAC1B,IAAIM,OAAO,GAAGL,OAAO,CAACT,KAAK,EAAEa,GAAG,CAAC,GAAG,CAAC,GAAGJ,OAAO,CAACT,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACnEa,GAAG,GAAGb,KAAK;QACXc,OAAO,GAAG,IAAI;MAChB;IACF;EACF;EAEA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}