{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Typography, Card, Alert, Form, Input, Button, message, Space, Collapse, Table, Tag, Row, Col } from 'antd';\nimport { LockOutlined, UserAddOutlined, UserOutlined, EyeInvisibleOutlined, EyeTwoTone, TeamOutlined, SafetyOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Panel\n} = Collapse;\n// const { confirm } = Modal;\n\nconst UserManagementPage = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const currentUser = (user === null || user === void 0 ? void 0 : user.username) || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username, userData]) => ({\n          username,\n          is_admin: username === 'admin',\n          created_time: userData.created_time || '',\n          last_login: userData.last_login || ''\n        }));\n        setUsers(userList);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async values => {\n    if (!token) return;\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async values => {\n    if (!token) return;\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_, value) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 12) {\n      return Promise.reject(new Error('密码长度必须至少12位'));\n    }\n    if (!/[A-Z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个大写字母'));\n    }\n    if (!/[a-z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个小写字母'));\n    }\n    if (!/\\d/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个数字'));\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个特殊字符'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_, value) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    render: username => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '角色',\n    dataIndex: 'is_admin',\n    key: 'is_admin',\n    render: isAdmin => /*#__PURE__*/_jsxDEV(Tag, {\n      color: isAdmin ? 'red' : 'blue',\n      children: isAdmin ? '管理员' : '普通用户'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_time',\n    key: 'created_time',\n    render: time => time ? new Date(time).toLocaleString() : 'N/A'\n  }, {\n    title: '最后登录',\n    dataIndex: 'last_login',\n    key: 'last_login',\n    render: time => time ? new Date(time).toLocaleString() : '从未登录'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), \"\\u7528\\u6237\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4FEE\\u6539\\u5BC6\\u7801\\u3001\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u7B49\\u7528\\u6237\\u7BA1\\u7406\\u529F\\u80FD\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this),\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5BC6\\u7801\\u5B89\\u5168\\u63D0\\u793A\",\n            description: \"\\u4E3A\\u4E86\\u8D26\\u6237\\u5B89\\u5168\\uFF0C\\u5EFA\\u8BAE\\u5B9A\\u671F\\u4FEE\\u6539\\u5BC6\\u7801\\u3002\\u65B0\\u5BC6\\u7801\\u5E94\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF0C\\u957F\\u5EA6\\u81F3\\u5C116\\u4F4D\\u3002\",\n            type: \"info\",\n            showIcon: true,\n            style: {\n              marginBottom: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: changePasswordForm,\n            layout: \"vertical\",\n            onFinish: handleChangePassword,\n            style: {\n              maxWidth: 600\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5F53\\u524D\\u7528\\u6237\",\n              name: \"current_user\",\n              initialValue: currentUser,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 27\n                }, this),\n                disabled: true,\n                value: currentUser\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u539F\\u5BC6\\u7801\",\n              name: \"old_password\",\n              rules: [{\n                required: true,\n                message: '请输入原密码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u539F\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u65B0\\u5BC6\\u7801\",\n              name: \"new_password\",\n              rules: [{\n                validator: validatePassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C1112\\u4F4D\\uFF0C\\u5305\\u542B\\u5927\\u5C0F\\u5199\\u5B57\\u6BCD\\u3001\\u6570\\u5B57\\u548C\\u7279\\u6B8A\\u5B57\\u7B26\\uFF09\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\",\n              name: \"confirm_password\",\n              rules: [{\n                validator: validateConfirmPassword\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 55\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 25\n                }, this),\n                size: \"large\",\n                children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), isAdmin && /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"red\",\n              children: \"\\u7BA1\\u7406\\u5458\\u4E13\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this),\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Collapse, {\n            defaultActiveKey: ['1'],\n            ghost: true,\n            children: [/*#__PURE__*/_jsxDEV(Panel, {\n              header: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: addUserForm,\n                layout: \"vertical\",\n                onFinish: handleAddUser,\n                style: {\n                  maxWidth: 600\n                },\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u65B0\\u7528\\u6237\\u540D\",\n                  name: \"new_username\",\n                  rules: [{\n                    required: true,\n                    message: '请输入用户名'\n                  }, {\n                    min: 3,\n                    message: '用户名至少3位'\n                  }, {\n                    pattern: /^[a-zA-Z0-9_]+$/,\n                    message: '用户名只能包含字母、数字和下划线'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u540D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                  name: \"new_user_password\",\n                  rules: [{\n                    validator: validatePassword\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",\n                    iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 78\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u786E\\u8BA4\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                  name: \"confirm_user_password\",\n                  rules: [{\n                    validator: validateConfirmPassword\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                    prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 33\n                    }, this),\n                    placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u7528\\u6237\\u5BC6\\u7801\",\n                    iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 61\n                    }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 78\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    htmlType: \"submit\",\n                    loading: loading,\n                    icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 31\n                    }, this),\n                    size: \"large\",\n                    children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)\n            }, \"1\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u7528\\u6237\\u5217\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: [users.length, \" \\u4E2A\\u7528\\u6237\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 16\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 29\n                  }, this),\n                  onClick: fetchUsers,\n                  loading: loading,\n                  children: \"\\u5237\\u65B0\\u7528\\u6237\\u5217\\u8868\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Table, {\n                columns: userColumns,\n                dataSource: users,\n                rowKey: \"username\",\n                pagination: {\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: total => `共 ${total} 个用户`\n                },\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, \"2\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), !isAdmin && /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6743\\u9650\\u63D0\\u793A\",\n          description: \"\\u60A8\\u5F53\\u524D\\u662F\\u666E\\u901A\\u7528\\u6237\\uFF0C\\u53EA\\u80FD\\u4FEE\\u6539\\u81EA\\u5DF1\\u7684\\u5BC6\\u7801\\u3002\\u5982\\u9700\\u6DFB\\u52A0\\u65B0\\u7528\\u6237\\u6216\\u67E5\\u770B\\u7528\\u6237\\u5217\\u8868\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u3002\",\n          type: \"warning\",\n          showIcon: true,\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagementPage, \"kvxNulDRolVLK02a1i70i9o5XOY=\", false, function () {\n  return [Form.useForm, Form.useForm, useSelector];\n});\n_c = UserManagementPage;\nexport default UserManagementPage;\nvar _c;\n$RefreshReg$(_c, \"UserManagementPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "<PERSON><PERSON>", "Form", "Input", "<PERSON><PERSON>", "message", "Space", "Collapse", "Table", "Tag", "Row", "Col", "LockOutlined", "UserAddOutlined", "UserOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "TeamOutlined", "SafetyOutlined", "ExclamationCircleOutlined", "useSelector", "authAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "Panel", "UserManagementPage", "_s", "loading", "setLoading", "users", "setUsers", "changePasswordForm", "useForm", "addUserForm", "user", "token", "state", "auth", "currentUser", "username", "isAdmin", "fetchUsers", "response", "getUsers", "data", "userList", "Object", "entries", "map", "userData", "is_admin", "created_time", "last_login", "error", "_error$response", "_error$response$data", "console", "detail", "handleChangePassword", "values", "changePassword", "old_password", "new_password", "confirm_password", "success", "resetFields", "_error$response2", "_error$response2$data", "handleAddUser", "addUser", "new_username", "new_user_password", "confirm_user_password", "_error$response3", "_error$response3$data", "validatePassword", "_", "value", "Promise", "reject", "Error", "length", "test", "resolve", "validateConfirmPassword", "form", "passwordField", "password", "getFieldValue", "userColumns", "title", "dataIndex", "key", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "color", "time", "Date", "toLocaleString", "level", "style", "fontSize", "fontWeight", "marginBottom", "type", "gutter", "marginTop", "span", "size", "description", "showIcon", "layout", "onFinish", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "label", "name", "initialValue", "prefix", "disabled", "rules", "required", "Password", "placeholder", "iconRender", "visible", "validator", "htmlType", "icon", "defaultActiveKey", "ghost", "header", "min", "pattern", "onClick", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showTotal", "total", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/UserManagementPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n  Alert,\n  Form,\n  Input,\n  Button,\n  message,\n  Space,\n\n  Collapse,\n  Table,\n  Tag,\n\n  Row,\n  Col\n} from 'antd';\nimport {\n  LockOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  TeamOutlined,\n  SafetyOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { authAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Panel } = Collapse;\n// const { confirm } = Modal;\n\ninterface User {\n  username: string;\n  is_admin: boolean;\n  created_time?: string;\n  last_login?: string;\n}\n\nconst UserManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState<User[]>([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const { user, token } = useSelector((state: RootState) => state.auth);\n  const currentUser = user?.username || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username, userData]: [string, any]) => ({\n          username,\n          is_admin: username === 'admin',\n          created_time: userData.created_time || '',\n          last_login: userData.last_login || '',\n        }));\n        setUsers(userList);\n      }\n    } catch (error: any) {\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error: any) {\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_: any, value: string) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 12) {\n      return Promise.reject(new Error('密码长度必须至少12位'));\n    }\n    if (!/[A-Z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个大写字母'));\n    }\n    if (!/[a-z]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个小写字母'));\n    }\n    if (!/\\d/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个数字'));\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(value)) {\n      return Promise.reject(new Error('密码必须包含至少一个特殊字符'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_: any, value: string) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (username: string) => (\n        <Space>\n          <UserOutlined />\n          <Text strong>{username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'is_admin',\n      key: 'is_admin',\n      render: (isAdmin: boolean) => (\n        <Tag color={isAdmin ? 'red' : 'blue'}>\n          {isAdmin ? '管理员' : '普通用户'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      render: (time: string) => time ? new Date(time).toLocaleString() : '从未登录',\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>\n        <Space>\n          <TeamOutlined />\n          用户管理\n        </Space>\n      </Title>\n      <Text type=\"secondary\">\n        修改密码、添加新用户等用户管理功能。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* 修改密码 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <LockOutlined />\n                <span>修改密码</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Alert\n              message=\"密码安全提示\"\n              description=\"为了账户安全，建议定期修改密码。新密码应包含字母和数字，长度至少6位。\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n\n            <Form\n              form={changePasswordForm}\n              layout=\"vertical\"\n              onFinish={handleChangePassword}\n              style={{ maxWidth: 600 }}\n            >\n              <Form.Item\n                label=\"当前用户\"\n                name=\"current_user\"\n                initialValue={currentUser}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  disabled\n                  value={currentUser}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"原密码\"\n                name=\"old_password\"\n                rules={[{ required: true, message: '请输入原密码' }]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"请输入原密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"新密码\"\n                name=\"new_password\"\n                rules={[{ validator: validatePassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请输入新密码（至少12位，包含大小写字母、数字和特殊字符）\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"确认新密码\"\n                name=\"confirm_password\"\n                rules={[{ validator: validateConfirmPassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请再次输入新密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LockOutlined />}\n                  size=\"large\"\n                >\n                  修改密码\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 用户管理（仅管理员） */}\n        {isAdmin && (\n          <Col span={24}>\n            <Card\n              title={\n                <Space>\n                  <TeamOutlined />\n                  <span>用户管理</span>\n                  <Tag color=\"red\">管理员专用</Tag>\n                </Space>\n              }\n              size=\"small\"\n            >\n              <Collapse defaultActiveKey={['1']} ghost>\n                <Panel\n                  header={\n                    <Space>\n                      <UserAddOutlined />\n                      <span>添加新用户</span>\n                    </Space>\n                  }\n                  key=\"1\"\n                >\n                  <Form\n                    form={addUserForm}\n                    layout=\"vertical\"\n                    onFinish={handleAddUser}\n                    style={{ maxWidth: 600 }}\n                  >\n                    <Form.Item\n                      label=\"新用户名\"\n                      name=\"new_username\"\n                      rules={[\n                        { required: true, message: '请输入用户名' },\n                        { min: 3, message: '用户名至少3位' },\n                        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }\n                      ]}\n                    >\n                      <Input\n                        prefix={<UserOutlined />}\n                        placeholder=\"请输入新用户名\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"新用户密码\"\n                      name=\"new_user_password\"\n                      rules={[{ validator: validatePassword }]}\n                    >\n                      <Input.Password\n                        prefix={<LockOutlined />}\n                        placeholder=\"请输入新用户密码（至少6位，包含字母和数字）\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"确认新用户密码\"\n                      name=\"confirm_user_password\"\n                      rules={[{ validator: validateConfirmPassword }]}\n                    >\n                      <Input.Password\n                        prefix={<SafetyOutlined />}\n                        placeholder=\"请再次输入新用户密码\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        icon={<UserAddOutlined />}\n                        size=\"large\"\n                      >\n                        添加用户\n                      </Button>\n                    </Form.Item>\n                  </Form>\n                </Panel>\n\n                <Panel\n                  header={\n                    <Space>\n                      <TeamOutlined />\n                      <span>用户列表</span>\n                      <Tag color=\"blue\">{users.length} 个用户</Tag>\n                    </Space>\n                  }\n                  key=\"2\"\n                >\n                  <div style={{ marginBottom: 16 }}>\n                    <Button\n                      icon={<TeamOutlined />}\n                      onClick={fetchUsers}\n                      loading={loading}\n                    >\n                      刷新用户列表\n                    </Button>\n                  </div>\n\n                  <Table\n                    columns={userColumns}\n                    dataSource={users}\n                    rowKey=\"username\"\n                    pagination={{\n                      pageSize: 10,\n                      showSizeChanger: true,\n                      showTotal: (total) => `共 ${total} 个用户`,\n                    }}\n                    size=\"small\"\n                  />\n                </Panel>\n              </Collapse>\n            </Card>\n          </Col>\n        )}\n\n        {/* 非管理员提示 */}\n        {!isAdmin && (\n          <Col span={24}>\n            <Alert\n              message=\"权限提示\"\n              description=\"您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。\"\n              type=\"warning\"\n              showIcon\n              icon={<ExclamationCircleOutlined />}\n            />\n          </Col>\n        )}\n      </Row>\n    </div>\n  );\n};\n\nexport default UserManagementPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EAELC,QAAQ,EACRC,KAAK,EACLC,GAAG,EAEHC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SACEC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,EACVC,YAAY,EACZC,cAAc,EACdC,yBAAyB,QACpB,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG1B,UAAU;AAClC,MAAM;EAAE2B;AAAM,CAAC,GAAGnB,QAAQ;AAC1B;;AASA,MAAMoB,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACqC,kBAAkB,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC3C,MAAM,CAACC,WAAW,CAAC,GAAGjC,IAAI,CAACgC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAM;IAAEE,IAAI;IAAEC;EAAM,CAAC,GAAGjB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACrE,MAAMC,WAAW,GAAG,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,QAAQ,KAAI,EAAE;EACxC,MAAMC,OAAO,GAAGF,WAAW,KAAK,OAAO;;EAEvC;EACA,MAAMG,UAAU,GAAG7C,WAAW,CAAC,YAAY;IACzC,IAAI,CAACuC,KAAK,EAAE;IAEZ,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMvB,OAAO,CAACwB,QAAQ,CAACR,KAAK,CAAC;MAC9C,IAAIO,QAAQ,CAACE,IAAI,EAAE;QACjB;QACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,CAACI,GAAG,CAAC,CAAC,CAACT,QAAQ,EAAEU,QAAQ,CAAgB,MAAM;UAC3FV,QAAQ;UACRW,QAAQ,EAAEX,QAAQ,KAAK,OAAO;UAC9BY,YAAY,EAAEF,QAAQ,CAACE,YAAY,IAAI,EAAE;UACzCC,UAAU,EAAEH,QAAQ,CAACG,UAAU,IAAI;QACrC,CAAC,CAAC,CAAC;QACHtB,QAAQ,CAACe,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOQ,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjClD,OAAO,CAACkD,KAAK,CAAC,eAAe,EAAAC,eAAA,GAAAD,KAAK,CAACX,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAIJ,KAAK,CAAClD,OAAO,EAAE,CAAC;IAC/E;EACF,CAAC,EAAE,CAACgC,KAAK,CAAC,CAAC;;EAEX;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI6C,OAAO,IAAIL,KAAK,EAAE;MACpBM,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACD,OAAO,EAAEL,KAAK,EAAEM,UAAU,CAAC,CAAC;;EAEhC;EACA,MAAMiB,oBAAoB,GAAG,MAAOC,MAAW,IAAK;IAClD,IAAI,CAACxB,KAAK,EAAE;IAEZP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMvB,OAAO,CAACyC,cAAc,CAAC;QAC5CrB,QAAQ,EAAED,WAAW;QACrBuB,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCC,YAAY,EAAEH,MAAM,CAACG,YAAY;QACjCC,gBAAgB,EAAEJ,MAAM,CAACI;MAC3B,CAAC,EAAE5B,KAAK,CAAC;MAET,IAAIO,QAAQ,CAACE,IAAI,CAACzC,OAAO,EAAE;QACzBA,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;QAC3BjC,kBAAkB,CAACkC,WAAW,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAa,gBAAA,EAAAC,qBAAA;MACnBX,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,aAAa,EAAAa,gBAAA,GAAAb,KAAK,CAACX,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtB,IAAI,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAIJ,KAAK,CAAClD,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACRyB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwC,aAAa,GAAG,MAAOT,MAAW,IAAK;IAC3C,IAAI,CAACxB,KAAK,EAAE;IAEZP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMvB,OAAO,CAACkD,OAAO,CAAC;QACrC9B,QAAQ,EAAED,WAAW;QACrBgC,YAAY,EAAEX,MAAM,CAACW,YAAY;QACjCC,iBAAiB,EAAEZ,MAAM,CAACY,iBAAiB;QAC3CC,qBAAqB,EAAEb,MAAM,CAACa;MAChC,CAAC,EAAErC,KAAK,CAAC;MAET,IAAIO,QAAQ,CAACE,IAAI,CAACzC,OAAO,EAAE;QACzBA,OAAO,CAAC6D,OAAO,CAAC,UAAU,CAAC;QAC3B/B,WAAW,CAACgC,WAAW,CAAC,CAAC;QACzBxB,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnBlB,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,aAAa,EAAAoB,gBAAA,GAAApB,KAAK,CAACX,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAIJ,KAAK,CAAClD,OAAO,EAAE,CAAC;IAC7E,CAAC,SAAS;MACRyB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,gBAAgB,GAAGA,CAACC,CAAM,EAAEC,KAAa,KAAK;IAClD,IAAI,CAACA,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,CAACI,MAAM,GAAG,EAAE,EAAE;MACrB,OAAOH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD;IACA,IAAI,CAAC,OAAO,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACxB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,IAAI,CAAC,OAAO,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACxB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,IAAI,CAAC,IAAI,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACrB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,cAAc,CAAC,CAAC;IAClD;IACA,IAAI,CAAC,wBAAwB,CAACE,IAAI,CAACL,KAAK,CAAC,EAAE;MACzC,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAACR,CAAM,EAAEC,KAAa,KAAK;IACzD,MAAMQ,IAAI,GAAGtD,kBAAkB,IAAIE,WAAW;IAC9C,MAAMqD,aAAa,GAAGD,IAAI,KAAKtD,kBAAkB,GAAG,cAAc,GAAG,mBAAmB;IACxF,MAAMwD,QAAQ,GAAGF,IAAI,CAACG,aAAa,CAACF,aAAa,CAAC;IAElD,IAAI,CAACT,KAAK,EAAE;MACV,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C;IACA,IAAIH,KAAK,KAAKU,QAAQ,EAAE;MACtB,OAAOT,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;IAChD;IACA,OAAOF,OAAO,CAACK,OAAO,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMM,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGtD,QAAgB,iBACvBlB,OAAA,CAACjB,KAAK;MAAA0F,QAAA,gBACJzE,OAAA,CAACT,YAAY;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChB7E,OAAA,CAACE,IAAI;QAAC4E,MAAM;QAAAL,QAAA,EAAEvD;MAAQ;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAEX,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGrD,OAAgB,iBACvBnB,OAAA,CAACd,GAAG;MAAC6F,KAAK,EAAE5D,OAAO,GAAG,KAAK,GAAG,MAAO;MAAAsD,QAAA,EAClCtD,OAAO,GAAG,KAAK,GAAG;IAAM;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB;EAET,CAAC,EACD;IACER,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGQ,IAAY,IAAKA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG;EACrE,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGQ,IAAY,IAAKA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG;EACrE,CAAC,CACF;EAED,oBACElF,OAAA;IAAAyE,QAAA,gBACEzE,OAAA,CAACC,KAAK;MAACkF,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAd,QAAA,eACjFzE,OAAA,CAACjB,KAAK;QAAA0F,QAAA,gBACJzE,OAAA,CAACN,YAAY;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACR7E,OAAA,CAACE,IAAI;MAACsF,IAAI,EAAC,WAAW;MAAAf,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP7E,OAAA,CAACb,GAAG;MAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACL,KAAK,EAAE;QAAEM,SAAS,EAAE;MAAG,CAAE;MAAAjB,QAAA,gBAE9CzE,OAAA,CAACZ,GAAG;QAACuG,IAAI,EAAE,EAAG;QAAAlB,QAAA,eACZzE,OAAA,CAACvB,IAAI;UACH4F,KAAK,eACHrE,OAAA,CAACjB,KAAK;YAAA0F,QAAA,gBACJzE,OAAA,CAACX,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChB7E,OAAA;cAAAyE,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CACR;UACDe,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBAEZzE,OAAA,CAACtB,KAAK;YACJI,OAAO,EAAC,sCAAQ;YAChB+G,WAAW,EAAC,+MAAqC;YACjDL,IAAI,EAAC,MAAM;YACXM,QAAQ;YACRV,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAG;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEF7E,OAAA,CAACrB,IAAI;YACHqF,IAAI,EAAEtD,kBAAmB;YACzBqF,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAE3D,oBAAqB;YAC/B+C,KAAK,EAAE;cAAEa,QAAQ,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEzBzE,OAAA,CAACrB,IAAI,CAACuH,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZC,IAAI,EAAC,cAAc;cACnBC,YAAY,EAAEpF,WAAY;cAAAwD,QAAA,eAE1BzE,OAAA,CAACpB,KAAK;gBACJ0H,MAAM,eAAEtG,OAAA,CAACT,YAAY;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB0B,QAAQ;gBACR/C,KAAK,EAAEvC;cAAY;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3H,OAAO,EAAE;cAAS,CAAC,CAAE;cAAA2F,QAAA,eAE/CzE,OAAA,CAACpB,KAAK,CAAC8H,QAAQ;gBACbJ,MAAM,eAAEtG,OAAA,CAACX,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzB8B,WAAW,EAAC,sCAAQ;gBACpBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7G,OAAA,CAACP,UAAU;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,oBAAoB;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXC,IAAI,EAAC,cAAc;cACnBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAExD;cAAiB,CAAC,CAAE;cAAAmB,QAAA,eAEzCzE,OAAA,CAACpB,KAAK,CAAC8H,QAAQ;gBACbJ,MAAM,eAAEtG,OAAA,CAACL,cAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B8B,WAAW,EAAC,sKAA+B;gBAC3CC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7G,OAAA,CAACP,UAAU;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,oBAAoB;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbC,IAAI,EAAC,kBAAkB;cACvBI,KAAK,EAAE,CAAC;gBAAEM,SAAS,EAAE/C;cAAwB,CAAC,CAAE;cAAAU,QAAA,eAEhDzE,OAAA,CAACpB,KAAK,CAAC8H,QAAQ;gBACbJ,MAAM,eAAEtG,OAAA,CAACL,cAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3B8B,WAAW,EAAC,kDAAU;gBACtBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7G,OAAA,CAACP,UAAU;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,oBAAoB;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;cAAAzB,QAAA,eACRzE,OAAA,CAACnB,MAAM;gBACL2G,IAAI,EAAC,SAAS;gBACduB,QAAQ,EAAC,QAAQ;gBACjBzG,OAAO,EAAEA,OAAQ;gBACjB0G,IAAI,eAAEhH,OAAA,CAACX,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBe,IAAI,EAAC,OAAO;gBAAAnB,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGL1D,OAAO,iBACNnB,OAAA,CAACZ,GAAG;QAACuG,IAAI,EAAE,EAAG;QAAAlB,QAAA,eACZzE,OAAA,CAACvB,IAAI;UACH4F,KAAK,eACHrE,OAAA,CAACjB,KAAK;YAAA0F,QAAA,gBACJzE,OAAA,CAACN,YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChB7E,OAAA;cAAAyE,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB7E,OAAA,CAACd,GAAG;cAAC6F,KAAK,EAAC,KAAK;cAAAN,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CACR;UACDe,IAAI,EAAC,OAAO;UAAAnB,QAAA,eAEZzE,OAAA,CAAChB,QAAQ;YAACiI,gBAAgB,EAAE,CAAC,GAAG,CAAE;YAACC,KAAK;YAAAzC,QAAA,gBACtCzE,OAAA,CAACG,KAAK;cACJgH,MAAM,eACJnH,OAAA,CAACjB,KAAK;gBAAA0F,QAAA,gBACJzE,OAAA,CAACV,eAAe;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnB7E,OAAA;kBAAAyE,QAAA,EAAM;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACR;cAAAJ,QAAA,eAGDzE,OAAA,CAACrB,IAAI;gBACHqF,IAAI,EAAEpD,WAAY;gBAClBmF,MAAM,EAAC,UAAU;gBACjBC,QAAQ,EAAEjD,aAAc;gBACxBqC,KAAK,EAAE;kBAAEa,QAAQ,EAAE;gBAAI,CAAE;gBAAAxB,QAAA,gBAEzBzE,OAAA,CAACrB,IAAI,CAACuH,IAAI;kBACRC,KAAK,EAAC,0BAAM;kBACZC,IAAI,EAAC,cAAc;kBACnBI,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAE3H,OAAO,EAAE;kBAAS,CAAC,EACrC;oBAAEsI,GAAG,EAAE,CAAC;oBAAEtI,OAAO,EAAE;kBAAU,CAAC,EAC9B;oBAAEuI,OAAO,EAAE,iBAAiB;oBAAEvI,OAAO,EAAE;kBAAmB,CAAC,CAC3D;kBAAA2F,QAAA,eAEFzE,OAAA,CAACpB,KAAK;oBACJ0H,MAAM,eAAEtG,OAAA,CAACT,YAAY;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB8B,WAAW,EAAC;kBAAS;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;kBACRC,KAAK,EAAC,gCAAO;kBACbC,IAAI,EAAC,mBAAmB;kBACxBI,KAAK,EAAE,CAAC;oBAAEM,SAAS,EAAExD;kBAAiB,CAAC,CAAE;kBAAAmB,QAAA,eAEzCzE,OAAA,CAACpB,KAAK,CAAC8H,QAAQ;oBACbJ,MAAM,eAAEtG,OAAA,CAACX,YAAY;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB8B,WAAW,EAAC,iIAAwB;oBACpCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7G,OAAA,CAACP,UAAU;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,oBAAoB;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;kBACRC,KAAK,EAAC,4CAAS;kBACfC,IAAI,EAAC,uBAAuB;kBAC5BI,KAAK,EAAE,CAAC;oBAAEM,SAAS,EAAE/C;kBAAwB,CAAC,CAAE;kBAAAU,QAAA,eAEhDzE,OAAA,CAACpB,KAAK,CAAC8H,QAAQ;oBACbJ,MAAM,eAAEtG,OAAA,CAACL,cAAc;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC3B8B,WAAW,EAAC,8DAAY;oBACxBC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7G,OAAA,CAACP,UAAU;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACR,oBAAoB;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eAEZ7E,OAAA,CAACrB,IAAI,CAACuH,IAAI;kBAAAzB,QAAA,eACRzE,OAAA,CAACnB,MAAM;oBACL2G,IAAI,EAAC,SAAS;oBACduB,QAAQ,EAAC,QAAQ;oBACjBzG,OAAO,EAAEA,OAAQ;oBACjB0G,IAAI,eAAEhH,OAAA,CAACV,eAAe;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1Be,IAAI,EAAC,OAAO;oBAAAnB,QAAA,EACb;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GA1DH,GAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DF,CAAC,eAER7E,OAAA,CAACG,KAAK;cACJgH,MAAM,eACJnH,OAAA,CAACjB,KAAK;gBAAA0F,QAAA,gBACJzE,OAAA,CAACN,YAAY;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChB7E,OAAA;kBAAAyE,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjB7E,OAAA,CAACd,GAAG;kBAAC6F,KAAK,EAAC,MAAM;kBAAAN,QAAA,GAAEjE,KAAK,CAACoD,MAAM,EAAC,qBAAI;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CACR;cAAAJ,QAAA,gBAGDzE,OAAA;gBAAKoF,KAAK,EAAE;kBAAEG,YAAY,EAAE;gBAAG,CAAE;gBAAAd,QAAA,eAC/BzE,OAAA,CAACnB,MAAM;kBACLmI,IAAI,eAAEhH,OAAA,CAACN,YAAY;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvByC,OAAO,EAAElG,UAAW;kBACpBd,OAAO,EAAEA,OAAQ;kBAAAmE,QAAA,EAClB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7E,OAAA,CAACf,KAAK;gBACJsI,OAAO,EAAEnD,WAAY;gBACrBoD,UAAU,EAAEhH,KAAM;gBAClBiH,MAAM,EAAC,UAAU;gBACjBC,UAAU,EAAE;kBACVC,QAAQ,EAAE,EAAE;kBACZC,eAAe,EAAE,IAAI;kBACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;gBAClC,CAAE;gBACFlC,IAAI,EAAC;cAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA,GAtBE,GAAG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA,CAAC1D,OAAO,iBACPnB,OAAA,CAACZ,GAAG;QAACuG,IAAI,EAAE,EAAG;QAAAlB,QAAA,eACZzE,OAAA,CAACtB,KAAK;UACJI,OAAO,EAAC,0BAAM;UACd+G,WAAW,EAAC,wPAA2C;UACvDL,IAAI,EAAC,SAAS;UACdM,QAAQ;UACRkB,IAAI,eAAEhH,OAAA,CAACJ,yBAAyB;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CAjZID,kBAA4B;EAAA,QAGHzB,IAAI,CAACgC,OAAO,EACnBhC,IAAI,CAACgC,OAAO,EAGVd,WAAW;AAAA;AAAAkI,EAAA,GAP/B3H,kBAA4B;AAmZlC,eAAeA,kBAAkB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}