{"ast": null, "code": "import Col from './col';\nimport useInternalBreakpoint from './hooks/useBreakpoint';\nimport Row from './row';\n// Do not export params\nfunction useBreakpoint() {\n  return useInternalBreakpoint();\n}\nexport { Row, Col };\nexport default {\n  useBreakpoint: useBreakpoint\n};", "map": {"version": 3, "names": ["Col", "useInternalBreakpoint", "Row", "useBreakpoint"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/grid/index.js"], "sourcesContent": ["import Col from './col';\nimport useInternalBreakpoint from './hooks/useBreakpoint';\nimport Row from './row';\n// Do not export params\nfunction useBreakpoint() {\n  return useInternalBreakpoint();\n}\nexport { Row, Col };\nexport default {\n  useBreakpoint: useBreakpoint\n};"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,OAAOC,qBAAqB,MAAM,uBAAuB;AACzD,OAAOC,GAAG,MAAM,OAAO;AACvB;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,OAAOF,qBAAqB,CAAC,CAAC;AAChC;AACA,SAASC,GAAG,EAAEF,GAAG;AACjB,eAAe;EACbG,aAAa,EAAEA;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}