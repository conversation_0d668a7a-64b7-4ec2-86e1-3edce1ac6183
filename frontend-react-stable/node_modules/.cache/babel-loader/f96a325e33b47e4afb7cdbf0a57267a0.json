{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, _ref2) {\n  let {\n    areStatesEqual,\n    areOwnPropsEqual,\n    areStatePropsEqual\n  } = _ref2;\n  let hasRunAtLeastOnce = false;\n  let state;\n  let ownProps;\n  let stateProps;\n  let dispatchProps;\n  let mergedProps;\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewState() {\n    const nextStateProps = mapStateToProps(state, ownProps);\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n}\n// TODO: Add more comments\n// The selector returned by selectorFactory will memoize its results,\n// allowing connect's shouldComponentUpdate to return false if final\n// props have not changed.\nexport default function finalPropsSelectorFactory(dispatch, _ref) {\n  let {\n      initMapStateToProps,\n      initMapDispatchToProps,\n      initMergeProps\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const mapStateToProps = initMapStateToProps(dispatch, options);\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  const mergeProps = initMergeProps(dispatch, options);\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n  }\n  return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "verifySubselectors", "pureFinalPropsSelectorFactory", "mapStateToProps", "mapDispatchToProps", "mergeProps", "dispatch", "_ref2", "areStatesEqual", "areOwnPropsEqual", "areStatePropsEqual", "hasRunAtLeastOnce", "state", "ownProps", "stateProps", "dispatchProps", "mergedProps", "handleFirstCall", "firstState", "firstOwnProps", "handleNewPropsAndNewState", "dependsOnOwnProps", "handleNewProps", "handleNewState", "nextStateProps", "statePropsChanged", "handleSubsequentCalls", "nextState", "nextOwnProps", "propsChanged", "stateChanged", "pureFinalPropsSelector", "finalPropsSelectorFactory", "_ref", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "options", "process", "env", "NODE_ENV"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/connect/selectorFactory.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, {\n  areStatesEqual,\n  areOwnPropsEqual,\n  areStatePropsEqual\n}) {\n  let hasRunAtLeastOnce = false;\n  let state;\n  let ownProps;\n  let stateProps;\n  let dispatchProps;\n  let mergedProps;\n\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewState() {\n    const nextStateProps = mapStateToProps(state, ownProps);\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n}\n// TODO: Add more comments\n// The selector returned by selectorFactory will memoize its results,\n// allowing connect's shouldComponentUpdate to return false if final\n// props have not changed.\nexport default function finalPropsSelectorFactory(dispatch, _ref) {\n  let {\n    initMapStateToProps,\n    initMapDispatchToProps,\n    initMergeProps\n  } = _ref,\n      options = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  const mapStateToProps = initMapStateToProps(dispatch, options);\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  const mergeProps = initMergeProps(dispatch, options);\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n  }\n\n  return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,gBAAgB,CAAC;AACrF,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAO,SAASC,6BAA6BA,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,EAAAC,KAAA,EAIpG;EAAA,IAJsG;IACvGC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC,GAAAH,KAAA;EACC,IAAII,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIC,UAAU;EACd,IAAIC,aAAa;EACjB,IAAIC,WAAW;EAEf,SAASC,eAAeA,CAACC,UAAU,EAAEC,aAAa,EAAE;IAClDP,KAAK,GAAGM,UAAU;IAClBL,QAAQ,GAAGM,aAAa;IACxBL,UAAU,GAAGX,eAAe,CAACS,KAAK,EAAEC,QAAQ,CAAC;IAC7CE,aAAa,GAAGX,kBAAkB,CAACE,QAAQ,EAAEO,QAAQ,CAAC;IACtDG,WAAW,GAAGX,UAAU,CAACS,UAAU,EAAEC,aAAa,EAAEF,QAAQ,CAAC;IAC7DF,iBAAiB,GAAG,IAAI;IACxB,OAAOK,WAAW;EACpB;EAEA,SAASI,yBAAyBA,CAAA,EAAG;IACnCN,UAAU,GAAGX,eAAe,CAACS,KAAK,EAAEC,QAAQ,CAAC;IAC7C,IAAIT,kBAAkB,CAACiB,iBAAiB,EAAEN,aAAa,GAAGX,kBAAkB,CAACE,QAAQ,EAAEO,QAAQ,CAAC;IAChGG,WAAW,GAAGX,UAAU,CAACS,UAAU,EAAEC,aAAa,EAAEF,QAAQ,CAAC;IAC7D,OAAOG,WAAW;EACpB;EAEA,SAASM,cAAcA,CAAA,EAAG;IACxB,IAAInB,eAAe,CAACkB,iBAAiB,EAAEP,UAAU,GAAGX,eAAe,CAACS,KAAK,EAAEC,QAAQ,CAAC;IACpF,IAAIT,kBAAkB,CAACiB,iBAAiB,EAAEN,aAAa,GAAGX,kBAAkB,CAACE,QAAQ,EAAEO,QAAQ,CAAC;IAChGG,WAAW,GAAGX,UAAU,CAACS,UAAU,EAAEC,aAAa,EAAEF,QAAQ,CAAC;IAC7D,OAAOG,WAAW;EACpB;EAEA,SAASO,cAAcA,CAAA,EAAG;IACxB,MAAMC,cAAc,GAAGrB,eAAe,CAACS,KAAK,EAAEC,QAAQ,CAAC;IACvD,MAAMY,iBAAiB,GAAG,CAACf,kBAAkB,CAACc,cAAc,EAAEV,UAAU,CAAC;IACzEA,UAAU,GAAGU,cAAc;IAC3B,IAAIC,iBAAiB,EAAET,WAAW,GAAGX,UAAU,CAACS,UAAU,EAAEC,aAAa,EAAEF,QAAQ,CAAC;IACpF,OAAOG,WAAW;EACpB;EAEA,SAASU,qBAAqBA,CAACC,SAAS,EAAEC,YAAY,EAAE;IACtD,MAAMC,YAAY,GAAG,CAACpB,gBAAgB,CAACmB,YAAY,EAAEf,QAAQ,CAAC;IAC9D,MAAMiB,YAAY,GAAG,CAACtB,cAAc,CAACmB,SAAS,EAAEf,KAAK,EAAEgB,YAAY,EAAEf,QAAQ,CAAC;IAC9ED,KAAK,GAAGe,SAAS;IACjBd,QAAQ,GAAGe,YAAY;IACvB,IAAIC,YAAY,IAAIC,YAAY,EAAE,OAAOV,yBAAyB,CAAC,CAAC;IACpE,IAAIS,YAAY,EAAE,OAAOP,cAAc,CAAC,CAAC;IACzC,IAAIQ,YAAY,EAAE,OAAOP,cAAc,CAAC,CAAC;IACzC,OAAOP,WAAW;EACpB;EAEA,OAAO,SAASe,sBAAsBA,CAACJ,SAAS,EAAEC,YAAY,EAAE;IAC9D,OAAOjB,iBAAiB,GAAGe,qBAAqB,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,eAAe,CAACU,SAAS,EAAEC,YAAY,CAAC;EACtH,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,eAAe,SAASI,yBAAyBA,CAAC1B,QAAQ,EAAE2B,IAAI,EAAE;EAChE,IAAI;MACFC,mBAAmB;MACnBC,sBAAsB;MACtBC;IACF,CAAC,GAAGH,IAAI;IACJI,OAAO,GAAGtC,6BAA6B,CAACkC,IAAI,EAAEjC,SAAS,CAAC;EAE5D,MAAMG,eAAe,GAAG+B,mBAAmB,CAAC5B,QAAQ,EAAE+B,OAAO,CAAC;EAC9D,MAAMjC,kBAAkB,GAAG+B,sBAAsB,CAAC7B,QAAQ,EAAE+B,OAAO,CAAC;EACpE,MAAMhC,UAAU,GAAG+B,cAAc,CAAC9B,QAAQ,EAAE+B,OAAO,CAAC;EAEpD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCvC,kBAAkB,CAACE,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,CAAC;EACrE;EAEA,OAAOH,6BAA6B,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,EAAE+B,OAAO,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module"}