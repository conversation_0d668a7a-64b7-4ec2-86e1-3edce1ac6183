{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { message, notification } from 'antd';\nimport taskApi, { TASK_STATUS, TASK_TYPE } from '../services/taskApi';\nexport const useTaskManager = () => {\n  _s();\n  const [tasks, setTasks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [runningTasks, setRunningTasks] = useState([]);\n  const [completedTasks, setCompletedTasks] = useState([]);\n  const [initialized, setInitialized] = useState(false);\n  const pollingIntervals = useRef(new Map()); // 存储轮询定时器\n\n  /**\n   * 获取所有任务\n   */\n  const fetchAllTasks = useCallback(async (showError = true) => {\n    try {\n      setLoading(true);\n      const response = await taskApi.getAllTasks();\n      if (response.success) {\n        setTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取任务列表失败:', error);\n      if (showError) {\n        message.error('获取任务列表失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * 获取正在运行的任务\n   */\n  const fetchRunningTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getRunningTasks();\n      if (response.success) {\n        setRunningTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取运行中任务失败:', error);\n      if (showError) {\n        message.error('获取运行中任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 获取已完成的任务\n   */\n  const fetchCompletedTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getCompletedTasks();\n      if (response.success) {\n        setCompletedTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取已完成任务失败:', error);\n      if (showError) {\n        message.error('获取已完成任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 删除单个已完成的任务\n   */\n  const deleteSingleTask = useCallback(async taskId => {\n    try {\n      const response = await taskApi.deleteSingleTask(taskId);\n      if (response.success) {\n        // 从已完成任务列表中移除\n        setCompletedTasks(prev => prev.filter(task => task.task_id !== taskId));\n        message.success(`已删除任务: ${taskId.substring(0, 8)}...`);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除任务失败:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '删除任务失败';\n      message.error(errorMessage);\n      return false;\n    }\n  }, []);\n\n  /**\n   * 清空所有已完成的任务\n   */\n  const clearCompletedTasks = useCallback(async () => {\n    try {\n      const response = await taskApi.clearCompletedTasks();\n      if (response.success) {\n        setCompletedTasks([]);\n        message.success(`已清空 ${response.cleared_count} 个完成的任务`);\n      }\n    } catch (error) {\n      console.error('清空已完成任务失败:', error);\n      message.error('清空已完成任务失败');\n    }\n  }, []);\n\n  /**\n   * 启动任务轮询\n   */\n  const startPolling = useCallback((taskId, onProgress) => {\n    // 如果已经在轮询，先清除\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId));\n    }\n    const interval = setInterval(async () => {\n      try {\n        const response = await taskApi.getTaskStatus(taskId);\n        const task = response.task;\n        if (!task) return;\n\n        // 更新任务列表中的对应任务\n        setTasks(prevTasks => {\n          const index = prevTasks.findIndex(t => t.task_id === taskId);\n          if (index >= 0) {\n            const newTasks = [...prevTasks];\n            newTasks[index] = task;\n            return newTasks;\n          } else {\n            return [...prevTasks, task];\n          }\n        });\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 如果任务完成，停止轮询并发送通知\n        if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED].includes(task.status)) {\n          clearInterval(pollingIntervals.current.get(taskId));\n          pollingIntervals.current.delete(taskId);\n\n          // 发送通知\n          if (task.status === TASK_STATUS.COMPLETED) {\n            notification.success({\n              message: '🎉 任务完成',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已成功完成！点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细结果`,\n              duration: 10,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: {\n                cursor: 'pointer'\n              }\n            });\n          } else if (task.status === TASK_STATUS.FAILED) {\n            notification.error({\n              message: '❌ 任务失败',\n              description: `${taskApi.formatTaskType(task.task_type)}任务执行失败: ${task.error || '未知错误'}。点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细错误信息`,\n              duration: 15,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: {\n                cursor: 'pointer'\n              }\n            });\n          } else if (task.status === TASK_STATUS.CANCELLED) {\n            notification.warning({\n              message: '⚠️ 任务已取消',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已被取消`,\n              duration: 5\n            });\n          }\n\n          // 更新运行中任务列表和已完成任务列表\n          fetchRunningTasks();\n          fetchCompletedTasks();\n        }\n      } catch (error) {\n        console.error(`轮询任务 ${taskId} 状态失败:`, error);\n        clearInterval(pollingIntervals.current.get(taskId));\n        pollingIntervals.current.delete(taskId);\n      }\n    }, 2000);\n    pollingIntervals.current.set(taskId, interval);\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  /**\n   * 停止任务轮询\n   */\n  const stopPolling = useCallback(taskId => {\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId));\n      pollingIntervals.current.delete(taskId);\n    }\n  }, []);\n\n  /**\n   * 初始化任务管理器（延迟初始化）\n   */\n  const initializeTaskManager = useCallback(async () => {\n    if (initialized) return;\n    try {\n      // 尝试获取运行中任务\n      await fetchRunningTasks(false);\n\n      // 为已存在的运行中任务启动轮询\n      const response = await taskApi.getRunningTasks();\n      if (response.success && response.tasks) {\n        response.tasks.forEach(task => {\n          startPolling(task.task_id);\n        });\n      }\n      setInitialized(true);\n    } catch (error) {\n      console.warn('初始化任务管理失败:', error);\n      // 静默失败，不影响用户体验\n    }\n  }, [initialized, fetchRunningTasks, startPolling]);\n\n  /**\n   * 提交训练任务\n   */\n  const submitTrainingTask = useCallback(async formData => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n      const response = await taskApi.startTrainingAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 训练任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n          return taskId;\n        }\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('启动训练任务失败:', error);\n      message.error('启动训练任务失败: ' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 提交预测任务\n   */\n  const submitPredictionTask = useCallback(async formData => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n      const response = await taskApi.startPredictionAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 预测任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n          return taskId;\n        }\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('启动预测任务失败:', error);\n      message.error('启动预测任务失败: ' + (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 取消任务\n   */\n  const cancelTask = useCallback(async taskId => {\n    try {\n      const response = await taskApi.cancelTask(taskId);\n      if (response.success) {\n        message.success('任务已取消');\n\n        // 停止轮询\n        stopPolling(taskId);\n\n        // 刷新任务列表\n        fetchAllTasks();\n        fetchRunningTasks();\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('取消任务失败:', error);\n      message.error('取消任务失败: ' + (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message));\n    }\n  }, [stopPolling, fetchAllTasks, fetchRunningTasks]);\n\n  /**\n   * 获取任务详情\n   */\n  const getTaskDetail = useCallback(async taskId => {\n    try {\n      const response = await taskApi.getTaskStatus(taskId);\n      return response.task || null;\n    } catch (error) {\n      console.error('获取任务详情失败:', error);\n      message.error('获取任务详情失败');\n      return null;\n    }\n  }, []);\n\n  // 组件挂载时初始化，获取已完成任务\n  useEffect(() => {\n    // 初始化时获取已完成任务\n    fetchCompletedTasks();\n\n    // 清理函数：清除所有轮询\n    return () => {\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const intervals = pollingIntervals.current;\n      intervals.forEach(interval => clearInterval(interval));\n      intervals.clear();\n    };\n  }, [fetchCompletedTasks]);\n  return {\n    // 状态\n    tasks,\n    runningTasks,\n    completedTasks,\n    loading,\n    // 方法\n    fetchAllTasks,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    clearCompletedTasks,\n    submitTrainingTask,\n    submitPredictionTask,\n    cancelTask,\n    getTaskDetail,\n    startPolling,\n    stopPolling,\n    // 结果获取方法\n    getTaskResult: useCallback(taskId => {\n      const task = completedTasks.find(t => t.task_id === taskId);\n      return (task === null || task === void 0 ? void 0 : task.result) || null;\n    }, [completedTasks]),\n    getCompletedTasksByType: useCallback(taskType => {\n      return completedTasks.filter(task => task.task_type === taskType && task.status === TASK_STATUS.COMPLETED && task.result);\n    }, [completedTasks]),\n    // 工具方法\n    formatTaskStatus: taskApi.formatTaskStatus,\n    formatTaskType: taskApi.formatTaskType,\n    getTaskStatusColor: taskApi.getTaskStatusColor,\n    calculateTaskDuration: taskApi.calculateTaskDuration,\n    // 常量\n    TASK_STATUS,\n    TASK_TYPE\n  };\n};\n_s(useTaskManager, \"Hb5jWfc0iUqnLlW8cpHXHnMqJ5Y=\");\nexport default useTaskManager;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "message", "notification", "taskApi", "TASK_STATUS", "TASK_TYPE", "useTaskManager", "_s", "tasks", "setTasks", "loading", "setLoading", "runningTasks", "setRunningTasks", "completedTasks", "setCompletedTasks", "initialized", "setInitialized", "pollingIntervals", "Map", "fetchAllTasks", "showError", "response", "getAllTasks", "success", "error", "console", "fetchRunningTasks", "getRunningTasks", "fetchCompletedTasks", "getCompletedTasks", "deleteSingleTask", "taskId", "prev", "filter", "task", "task_id", "substring", "_error$response", "_error$response$data", "errorMessage", "data", "detail", "clearCompletedTasks", "cleared_count", "startPolling", "onProgress", "current", "has", "clearInterval", "get", "interval", "setInterval", "getTaskStatus", "prevTasks", "index", "findIndex", "t", "newTasks", "COMPLETED", "FAILED", "CANCELLED", "includes", "status", "delete", "description", "formatTaskType", "task_type", "duration", "onClick", "window", "location", "hash", "style", "cursor", "warning", "set", "stopPolling", "initializeTaskManager", "for<PERSON>ach", "warn", "submitTrainingTask", "formData", "startTrainingAsync", "_error$response2", "_error$response2$data", "submitPredictionTask", "startPredictionAsync", "_error$response3", "_error$response3$data", "cancelTask", "_error$response4", "_error$response4$data", "getTaskDetail", "intervals", "clear", "getTaskResult", "find", "result", "getCompletedTasksByType", "taskType", "formatTaskStatus", "getTaskStatusColor", "calculateTaskDuration"], "sources": ["/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts"], "sourcesContent": ["/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { message, notification } from 'antd';\nimport taskApi, {\n  TASK_STATUS,\n  TASK_TYPE,\n  Task\n} from '../services/taskApi';\n\nexport const useTaskManager = () => {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [runningTasks, setRunningTasks] = useState<Task[]>([]);\n  const [completedTasks, setCompletedTasks] = useState<Task[]>([]);\n  const [initialized, setInitialized] = useState(false);\n  const pollingIntervals = useRef(new Map<string, NodeJS.Timeout>()); // 存储轮询定时器\n\n  /**\n   * 获取所有任务\n   */\n  const fetchAllTasks = useCallback(async (showError = true) => {\n    try {\n      setLoading(true);\n      const response = await taskApi.getAllTasks();\n      if (response.success) {\n        setTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取任务列表失败:', error);\n      if (showError) {\n        message.error('获取任务列表失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * 获取正在运行的任务\n   */\n  const fetchRunningTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getRunningTasks();\n      if (response.success) {\n        setRunningTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取运行中任务失败:', error);\n      if (showError) {\n        message.error('获取运行中任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 获取已完成的任务\n   */\n  const fetchCompletedTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getCompletedTasks();\n      if (response.success) {\n        setCompletedTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取已完成任务失败:', error);\n      if (showError) {\n        message.error('获取已完成任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 删除单个已完成的任务\n   */\n  const deleteSingleTask = useCallback(async (taskId: string): Promise<boolean> => {\n    try {\n      const response = await taskApi.deleteSingleTask(taskId);\n      if (response.success) {\n        // 从已完成任务列表中移除\n        setCompletedTasks(prev => prev.filter(task => task.task_id !== taskId));\n        message.success(`已删除任务: ${taskId.substring(0, 8)}...`);\n        return true;\n      }\n      return false;\n    } catch (error: any) {\n      console.error('删除任务失败:', error);\n      const errorMessage = error.response?.data?.detail || '删除任务失败';\n      message.error(errorMessage);\n      return false;\n    }\n  }, []);\n\n  /**\n   * 清空所有已完成的任务\n   */\n  const clearCompletedTasks = useCallback(async () => {\n    try {\n      const response = await taskApi.clearCompletedTasks();\n      if (response.success) {\n        setCompletedTasks([]);\n        message.success(`已清空 ${response.cleared_count} 个完成的任务`);\n      }\n    } catch (error) {\n      console.error('清空已完成任务失败:', error);\n      message.error('清空已完成任务失败');\n    }\n  }, []);\n\n  /**\n   * 启动任务轮询\n   */\n  const startPolling = useCallback((taskId: string, onProgress?: (task: Task) => void) => {\n    // 如果已经在轮询，先清除\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const response = await taskApi.getTaskStatus(taskId);\n        const task = response.task;\n\n        if (!task) return;\n\n        // 更新任务列表中的对应任务\n        setTasks(prevTasks => {\n          const index = prevTasks.findIndex(t => t.task_id === taskId);\n          if (index >= 0) {\n            const newTasks = [...prevTasks];\n            newTasks[index] = task;\n            return newTasks;\n          } else {\n            return [...prevTasks, task];\n          }\n        });\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 如果任务完成，停止轮询并发送通知\n        if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED].includes(task.status as any)) {\n          clearInterval(pollingIntervals.current.get(taskId)!);\n          pollingIntervals.current.delete(taskId);\n\n          // 发送通知\n          if (task.status === TASK_STATUS.COMPLETED) {\n            notification.success({\n              message: '🎉 任务完成',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已成功完成！点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细结果`,\n              duration: 10,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.FAILED) {\n            notification.error({\n              message: '❌ 任务失败',\n              description: `${taskApi.formatTaskType(task.task_type)}任务执行失败: ${task.error || '未知错误'}。点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细错误信息`,\n              duration: 15,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.CANCELLED) {\n            notification.warning({\n              message: '⚠️ 任务已取消',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已被取消`,\n              duration: 5,\n            });\n          }\n\n          // 更新运行中任务列表和已完成任务列表\n          fetchRunningTasks();\n          fetchCompletedTasks();\n        }\n      } catch (error) {\n        console.error(`轮询任务 ${taskId} 状态失败:`, error);\n        clearInterval(pollingIntervals.current.get(taskId)!);\n        pollingIntervals.current.delete(taskId);\n      }\n    }, 2000);\n\n    pollingIntervals.current.set(taskId, interval);\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  /**\n   * 停止任务轮询\n   */\n  const stopPolling = useCallback((taskId: string) => {\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n      pollingIntervals.current.delete(taskId);\n    }\n  }, []);\n\n  /**\n   * 初始化任务管理器（延迟初始化）\n   */\n  const initializeTaskManager = useCallback(async () => {\n    if (initialized) return;\n\n    try {\n      // 尝试获取运行中任务\n      await fetchRunningTasks(false);\n\n      // 为已存在的运行中任务启动轮询\n      const response = await taskApi.getRunningTasks();\n      if (response.success && response.tasks) {\n        response.tasks.forEach(task => {\n          startPolling(task.task_id);\n        });\n      }\n\n      setInitialized(true);\n    } catch (error) {\n      console.warn('初始化任务管理失败:', error);\n      // 静默失败，不影响用户体验\n    }\n  }, [initialized, fetchRunningTasks, startPolling]);\n\n  /**\n   * 提交训练任务\n   */\n  const submitTrainingTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startTrainingAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 训练任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动训练任务失败:', error);\n      message.error('启动训练任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 提交预测任务\n   */\n  const submitPredictionTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startPredictionAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 预测任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动预测任务失败:', error);\n      message.error('启动预测任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 取消任务\n   */\n  const cancelTask = useCallback(async (taskId: string) => {\n    try {\n      const response = await taskApi.cancelTask(taskId);\n      if (response.success) {\n        message.success('任务已取消');\n        \n        // 停止轮询\n        stopPolling(taskId);\n        \n        // 刷新任务列表\n        fetchAllTasks();\n        fetchRunningTasks();\n      }\n    } catch (error: any) {\n      console.error('取消任务失败:', error);\n      message.error('取消任务失败: ' + (error.response?.data?.detail || error.message));\n    }\n  }, [stopPolling, fetchAllTasks, fetchRunningTasks]);\n\n  /**\n   * 获取任务详情\n   */\n  const getTaskDetail = useCallback(async (taskId: string): Promise<Task | null> => {\n    try {\n      const response = await taskApi.getTaskStatus(taskId);\n      return response.task || null;\n    } catch (error) {\n      console.error('获取任务详情失败:', error);\n      message.error('获取任务详情失败');\n      return null;\n    }\n  }, []);\n\n  // 组件挂载时初始化，获取已完成任务\n  useEffect(() => {\n    // 初始化时获取已完成任务\n    fetchCompletedTasks();\n\n    // 清理函数：清除所有轮询\n    return () => {\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const intervals = pollingIntervals.current;\n      intervals.forEach(interval => clearInterval(interval));\n      intervals.clear();\n    };\n  }, [fetchCompletedTasks]);\n\n  return {\n    // 状态\n    tasks,\n    runningTasks,\n    completedTasks,\n    loading,\n\n    // 方法\n    fetchAllTasks,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    clearCompletedTasks,\n    submitTrainingTask,\n    submitPredictionTask,\n    cancelTask,\n    getTaskDetail,\n    startPolling,\n    stopPolling,\n\n    // 结果获取方法\n    getTaskResult: useCallback((taskId: string) => {\n      const task = completedTasks.find(t => t.task_id === taskId);\n      return task?.result || null;\n    }, [completedTasks]),\n\n    getCompletedTasksByType: useCallback((taskType: 'training' | 'prediction') => {\n      return completedTasks.filter(task =>\n        task.task_type === taskType &&\n        task.status === TASK_STATUS.COMPLETED &&\n        task.result\n      );\n    }, [completedTasks]),\n\n    // 工具方法\n    formatTaskStatus: taskApi.formatTaskStatus,\n    formatTaskType: taskApi.formatTaskType,\n    getTaskStatusColor: taskApi.getTaskStatusColor,\n    calculateTaskDuration: taskApi.calculateTaskDuration,\n\n    // 常量\n    TASK_STATUS,\n    TASK_TYPE\n  };\n};\n\nexport default useTaskManager;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAChE,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAC5C,OAAOC,OAAO,IACZC,WAAW,EACXC,SAAS,QAEJ,qBAAqB;AAE5B,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMqB,gBAAgB,GAAGlB,MAAM,CAAC,IAAImB,GAAG,CAAyB,CAAC,CAAC,CAAC,CAAC;;EAEpE;AACF;AACA;EACE,MAAMC,aAAa,GAAGrB,WAAW,CAAC,OAAOsB,SAAS,GAAG,IAAI,KAAK;IAC5D,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMnB,OAAO,CAACoB,WAAW,CAAC,CAAC;MAC5C,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBf,QAAQ,CAACa,QAAQ,CAACd,KAAK,IAAI,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIJ,SAAS,EAAE;QACbpB,OAAO,CAACwB,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMgB,iBAAiB,GAAG5B,WAAW,CAAC,OAAOsB,SAAS,GAAG,KAAK,KAAK;IACjE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,OAAO,CAACyB,eAAe,CAAC,CAAC;MAChD,IAAIN,QAAQ,CAACE,OAAO,EAAE;QACpBX,eAAe,CAACS,QAAQ,CAACd,KAAK,IAAI,EAAE,CAAC;MACvC;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAIJ,SAAS,EAAE;QACbpB,OAAO,CAACwB,KAAK,CAAC,WAAW,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMI,mBAAmB,GAAG9B,WAAW,CAAC,OAAOsB,SAAS,GAAG,KAAK,KAAK;IACnE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,OAAO,CAAC2B,iBAAiB,CAAC,CAAC;MAClD,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBT,iBAAiB,CAACO,QAAQ,CAACd,KAAK,IAAI,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAIJ,SAAS,EAAE;QACbpB,OAAO,CAACwB,KAAK,CAAC,WAAW,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMM,gBAAgB,GAAGhC,WAAW,CAAC,MAAOiC,MAAc,IAAuB;IAC/E,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMnB,OAAO,CAAC4B,gBAAgB,CAACC,MAAM,CAAC;MACvD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAT,iBAAiB,CAACkB,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC,CAAC;QACvE/B,OAAO,CAACuB,OAAO,CAAC,UAAUQ,MAAM,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;QACtD,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACnBb,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMe,YAAY,GAAG,EAAAF,eAAA,GAAAb,KAAK,CAACH,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAI,QAAQ;MAC7DzC,OAAO,CAACwB,KAAK,CAACe,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMG,mBAAmB,GAAG5C,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMuB,QAAQ,GAAG,MAAMnB,OAAO,CAACwC,mBAAmB,CAAC,CAAC;MACpD,IAAIrB,QAAQ,CAACE,OAAO,EAAE;QACpBT,iBAAiB,CAAC,EAAE,CAAC;QACrBd,OAAO,CAACuB,OAAO,CAAC,OAAOF,QAAQ,CAACsB,aAAa,SAAS,CAAC;MACzD;IACF,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCxB,OAAO,CAACwB,KAAK,CAAC,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMoB,YAAY,GAAG9C,WAAW,CAAC,CAACiC,MAAc,EAAEc,UAAiC,KAAK;IACtF;IACA,IAAI5B,gBAAgB,CAAC6B,OAAO,CAACC,GAAG,CAAChB,MAAM,CAAC,EAAE;MACxCiB,aAAa,CAAC/B,gBAAgB,CAAC6B,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC;IACtD;IAEA,MAAMmB,QAAQ,GAAGC,WAAW,CAAC,YAAY;MACvC,IAAI;QACF,MAAM9B,QAAQ,GAAG,MAAMnB,OAAO,CAACkD,aAAa,CAACrB,MAAM,CAAC;QACpD,MAAMG,IAAI,GAAGb,QAAQ,CAACa,IAAI;QAE1B,IAAI,CAACA,IAAI,EAAE;;QAEX;QACA1B,QAAQ,CAAC6C,SAAS,IAAI;UACpB,MAAMC,KAAK,GAAGD,SAAS,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACrB,OAAO,KAAKJ,MAAM,CAAC;UAC5D,IAAIuB,KAAK,IAAI,CAAC,EAAE;YACd,MAAMG,QAAQ,GAAG,CAAC,GAAGJ,SAAS,CAAC;YAC/BI,QAAQ,CAACH,KAAK,CAAC,GAAGpB,IAAI;YACtB,OAAOuB,QAAQ;UACjB,CAAC,MAAM;YACL,OAAO,CAAC,GAAGJ,SAAS,EAAEnB,IAAI,CAAC;UAC7B;QACF,CAAC,CAAC;;QAEF;QACA,IAAIW,UAAU,EAAE;UACdA,UAAU,CAACX,IAAI,CAAC;QAClB;;QAEA;QACA,IAAI,CAAC/B,WAAW,CAACuD,SAAS,EAAEvD,WAAW,CAACwD,MAAM,EAAExD,WAAW,CAACyD,SAAS,CAAC,CAACC,QAAQ,CAAC3B,IAAI,CAAC4B,MAAa,CAAC,EAAE;UACnGd,aAAa,CAAC/B,gBAAgB,CAAC6B,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC;UACpDd,gBAAgB,CAAC6B,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC;;UAEvC;UACA,IAAIG,IAAI,CAAC4B,MAAM,KAAK3D,WAAW,CAACuD,SAAS,EAAE;YACzCzD,YAAY,CAACsB,OAAO,CAAC;cACnBvB,OAAO,EAAE,SAAS;cAClBgE,WAAW,EAAE,GAAG9D,OAAO,CAAC+D,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,sCAAsC;cAC5FC,QAAQ,EAAE,EAAE;cACZC,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgB;cACzC,CAAC;cACDC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAU;YAC7B,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIvC,IAAI,CAAC4B,MAAM,KAAK3D,WAAW,CAACwD,MAAM,EAAE;YAC7C1D,YAAY,CAACuB,KAAK,CAAC;cACjBxB,OAAO,EAAE,QAAQ;cACjBgE,WAAW,EAAE,GAAG9D,OAAO,CAAC+D,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,WAAWhC,IAAI,CAACV,KAAK,IAAI,MAAM,iCAAiC;cACtH2C,QAAQ,EAAE,EAAE;cACZC,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgB;cACzC,CAAC;cACDC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAU;YAC7B,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIvC,IAAI,CAAC4B,MAAM,KAAK3D,WAAW,CAACyD,SAAS,EAAE;YAChD3D,YAAY,CAACyE,OAAO,CAAC;cACnB1E,OAAO,EAAE,UAAU;cACnBgE,WAAW,EAAE,GAAG9D,OAAO,CAAC+D,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,QAAQ;cAC9DC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;;UAEA;UACAzC,iBAAiB,CAAC,CAAC;UACnBE,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQO,MAAM,QAAQ,EAAEP,KAAK,CAAC;QAC5CwB,aAAa,CAAC/B,gBAAgB,CAAC6B,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC;QACpDd,gBAAgB,CAAC6B,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC;MACzC;IACF,CAAC,EAAE,IAAI,CAAC;IAERd,gBAAgB,CAAC6B,OAAO,CAAC6B,GAAG,CAAC5C,MAAM,EAAEmB,QAAQ,CAAC;EAChD,CAAC,EAAE,CAACxB,iBAAiB,EAAEE,mBAAmB,CAAC,CAAC;;EAE5C;AACF;AACA;EACE,MAAMgD,WAAW,GAAG9E,WAAW,CAAEiC,MAAc,IAAK;IAClD,IAAId,gBAAgB,CAAC6B,OAAO,CAACC,GAAG,CAAChB,MAAM,CAAC,EAAE;MACxCiB,aAAa,CAAC/B,gBAAgB,CAAC6B,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC;MACpDd,gBAAgB,CAAC6B,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC;IACzC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM8C,qBAAqB,GAAG/E,WAAW,CAAC,YAAY;IACpD,IAAIiB,WAAW,EAAE;IAEjB,IAAI;MACF;MACA,MAAMW,iBAAiB,CAAC,KAAK,CAAC;;MAE9B;MACA,MAAML,QAAQ,GAAG,MAAMnB,OAAO,CAACyB,eAAe,CAAC,CAAC;MAChD,IAAIN,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACd,KAAK,EAAE;QACtCc,QAAQ,CAACd,KAAK,CAACuE,OAAO,CAAC5C,IAAI,IAAI;UAC7BU,YAAY,CAACV,IAAI,CAACC,OAAO,CAAC;QAC5B,CAAC,CAAC;MACJ;MAEAnB,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACsD,IAAI,CAAC,YAAY,EAAEvD,KAAK,CAAC;MACjC;IACF;EACF,CAAC,EAAE,CAACT,WAAW,EAAEW,iBAAiB,EAAEkB,YAAY,CAAC,CAAC;;EAElD;AACF;AACA;EACE,MAAMoC,kBAAkB,GAAGlF,WAAW,CAAC,MAAOmF,QAAkB,IAAkC;IAChG,IAAI;MACF;MACA,MAAMJ,qBAAqB,CAAC,CAAC;MAE7B,MAAMxD,QAAQ,GAAG,MAAMnB,OAAO,CAACgF,kBAAkB,CAACD,QAAQ,CAAC;MAC3D,IAAI5D,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMQ,MAAM,GAAGV,QAAQ,CAACc,OAAO;QAC/B,IAAIJ,MAAM,EAAE;UACV/B,OAAO,CAACuB,OAAO,CAAC,qCAAqC,EAAE,CAAC,CAAC;;UAEzD;UACAqB,YAAY,CAACb,MAAM,CAAC;;UAEpB;UACAL,iBAAiB,CAAC,KAAK,CAAC;UAExB,OAAOK,MAAM;QACf;MACF;IACF,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MACnB3D,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxB,OAAO,CAACwB,KAAK,CAAC,YAAY,IAAI,EAAA2D,gBAAA,GAAA3D,KAAK,CAACH,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsB3C,MAAM,KAAIjB,KAAK,CAACxB,OAAO,CAAC,CAAC;MAC7E,MAAMwB,KAAK;IACb;EACF,CAAC,EAAE,CAACqD,qBAAqB,EAAEjC,YAAY,EAAElB,iBAAiB,CAAC,CAAC;;EAE5D;AACF;AACA;EACE,MAAM2D,oBAAoB,GAAGvF,WAAW,CAAC,MAAOmF,QAAkB,IAAkC;IAClG,IAAI;MACF;MACA,MAAMJ,qBAAqB,CAAC,CAAC;MAE7B,MAAMxD,QAAQ,GAAG,MAAMnB,OAAO,CAACoF,oBAAoB,CAACL,QAAQ,CAAC;MAC7D,IAAI5D,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMQ,MAAM,GAAGV,QAAQ,CAACc,OAAO;QAC/B,IAAIJ,MAAM,EAAE;UACV/B,OAAO,CAACuB,OAAO,CAAC,qCAAqC,EAAE,CAAC,CAAC;;UAEzD;UACAqB,YAAY,CAACb,MAAM,CAAC;;UAEpB;UACAL,iBAAiB,CAAC,KAAK,CAAC;UAExB,OAAOK,MAAM;QACf;MACF;IACF,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACnB/D,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxB,OAAO,CAACwB,KAAK,CAAC,YAAY,IAAI,EAAA+D,gBAAA,GAAA/D,KAAK,CAACH,QAAQ,cAAAkE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/C,IAAI,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsB/C,MAAM,KAAIjB,KAAK,CAACxB,OAAO,CAAC,CAAC;MAC7E,MAAMwB,KAAK;IACb;EACF,CAAC,EAAE,CAACqD,qBAAqB,EAAEjC,YAAY,EAAElB,iBAAiB,CAAC,CAAC;;EAE5D;AACF;AACA;EACE,MAAM+D,UAAU,GAAG3F,WAAW,CAAC,MAAOiC,MAAc,IAAK;IACvD,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMnB,OAAO,CAACuF,UAAU,CAAC1D,MAAM,CAAC;MACjD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBvB,OAAO,CAACuB,OAAO,CAAC,OAAO,CAAC;;QAExB;QACAqD,WAAW,CAAC7C,MAAM,CAAC;;QAEnB;QACAZ,aAAa,CAAC,CAAC;QACfO,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC,CAAC,OAAOF,KAAU,EAAE;MAAA,IAAAkE,gBAAA,EAAAC,qBAAA;MACnBlE,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxB,OAAO,CAACwB,KAAK,CAAC,UAAU,IAAI,EAAAkE,gBAAA,GAAAlE,KAAK,CAACH,QAAQ,cAAAqE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsBlD,MAAM,KAAIjB,KAAK,CAACxB,OAAO,CAAC,CAAC;IAC7E;EACF,CAAC,EAAE,CAAC4E,WAAW,EAAEzD,aAAa,EAAEO,iBAAiB,CAAC,CAAC;;EAEnD;AACF;AACA;EACE,MAAMkE,aAAa,GAAG9F,WAAW,CAAC,MAAOiC,MAAc,IAA2B;IAChF,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMnB,OAAO,CAACkD,aAAa,CAACrB,MAAM,CAAC;MACpD,OAAOV,QAAQ,CAACa,IAAI,IAAI,IAAI;IAC9B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxB,OAAO,CAACwB,KAAK,CAAC,UAAU,CAAC;MACzB,OAAO,IAAI;IACb;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3B,SAAS,CAAC,MAAM;IACd;IACA+B,mBAAmB,CAAC,CAAC;;IAErB;IACA,OAAO,MAAM;MACX;MACA,MAAMiE,SAAS,GAAG5E,gBAAgB,CAAC6B,OAAO;MAC1C+C,SAAS,CAACf,OAAO,CAAC5B,QAAQ,IAAIF,aAAa,CAACE,QAAQ,CAAC,CAAC;MACtD2C,SAAS,CAACC,KAAK,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,CAAClE,mBAAmB,CAAC,CAAC;EAEzB,OAAO;IACL;IACArB,KAAK;IACLI,YAAY;IACZE,cAAc;IACdJ,OAAO;IAEP;IACAU,aAAa;IACbO,iBAAiB;IACjBE,mBAAmB;IACnBc,mBAAmB;IACnBsC,kBAAkB;IAClBK,oBAAoB;IACpBI,UAAU;IACVG,aAAa;IACbhD,YAAY;IACZgC,WAAW;IAEX;IACAmB,aAAa,EAAEjG,WAAW,CAAEiC,MAAc,IAAK;MAC7C,MAAMG,IAAI,GAAGrB,cAAc,CAACmF,IAAI,CAACxC,CAAC,IAAIA,CAAC,CAACrB,OAAO,KAAKJ,MAAM,CAAC;MAC3D,OAAO,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,MAAM,KAAI,IAAI;IAC7B,CAAC,EAAE,CAACpF,cAAc,CAAC,CAAC;IAEpBqF,uBAAuB,EAAEpG,WAAW,CAAEqG,QAAmC,IAAK;MAC5E,OAAOtF,cAAc,CAACoB,MAAM,CAACC,IAAI,IAC/BA,IAAI,CAACgC,SAAS,KAAKiC,QAAQ,IAC3BjE,IAAI,CAAC4B,MAAM,KAAK3D,WAAW,CAACuD,SAAS,IACrCxB,IAAI,CAAC+D,MACP,CAAC;IACH,CAAC,EAAE,CAACpF,cAAc,CAAC,CAAC;IAEpB;IACAuF,gBAAgB,EAAElG,OAAO,CAACkG,gBAAgB;IAC1CnC,cAAc,EAAE/D,OAAO,CAAC+D,cAAc;IACtCoC,kBAAkB,EAAEnG,OAAO,CAACmG,kBAAkB;IAC9CC,qBAAqB,EAAEpG,OAAO,CAACoG,qBAAqB;IAEpD;IACAnG,WAAW;IACXC;EACF,CAAC;AACH,CAAC;AAACE,EAAA,CAlXWD,cAAc;AAoX3B,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}