{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Alert, Progress, Typography, Space, Divider, message, Spin } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst DataCleaningPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('local');\n  const [processingMode, setProcessingMode] = useState('single');\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState({});\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt'));\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false,\n    // 阻止自动上传\n    onChange: info => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: e => {\n      console.log('Dropped files', e.dataTransfer.files);\n    }\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n    try {\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach(file => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir\n        });\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6D41\\u91CF\\u6570\\u636E\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\u6216\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u4E2D\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\uFF0C\\u5206\\u6790\\u540E\\u751F\\u6210CSV\\u6587\\u4EF6\\u5E76\\u4FDD\\u5B58\\u5230\\u6307\\u5B9A\\u76EE\\u5F55\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u5206\\u6790\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6D41\\u91CF\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636ETXT\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u672C\\u5730\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: folderPath,\n                onChange: e => setFolderPath(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/aizhinengqingxicepingdaliu\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchLocalFiles,\n                loading: filesLoading,\n                disabled: !folderPath,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                value: selectedFiles,\n                onChange: setSelectedFiles,\n                placeholder: \"\\u8BF7\\u9009\\u62E9TXT\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301\\u5355\\u4E2A\\u6216\\u6279\\u91CF\\u4E0A\\u4F20TXT\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"CSV\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: outputDir,\n            onChange: e => setOutputDir(e.target.value),\n            placeholder: \"\\u4F8B\\u5982: /data/output\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 19\n          }, this),\n          onClick: handleCleanData,\n          loading: loading,\n          disabled: !isFormValid(),\n          className: \"action-button\",\n          children: loading ? '正在处理...' : '执行流量分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u5904\\u7406\\u8FDB\\u5EA6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: progress,\n            status: \"active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5904\\u7406\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u5904\\u7406\\u7ED3\\u679C\\uFF1A\", result.message]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), result.output_file && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u8F93\\u51FA\\u6587\\u4EF6\\uFF1A\", result.output_file]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 42\n            }, this), result.processed_files && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u5904\\u7406\\u7684\\u6587\\u4EF6\\u6570\\uFF1A\", result.processed_files]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 46\n            }, this), result.total_rows && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u603B\\u884C\\u6570\\uFF1A\", result.total_rows]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s(DataCleaningPage, \"l9ctDTHEUY1ai+u030csZppwOz4=\");\n_c = DataCleaningPage;\nexport default DataCleaningPage;\nvar _c;\n$RefreshReg$(_c, \"DataCleaningPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "Progress", "Typography", "Space", "Divider", "message", "Spin", "InboxOutlined", "PlayCircleOutlined", "dataCleaningAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "DataCleaningPage", "_s", "dataSource", "setDataSource", "processingMode", "setProcessingMode", "uploadedFiles", "setUploadedFiles", "folderPath", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "loading", "setLoading", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "batchTasks", "setBatchTasks", "batchLoading", "setBatchLoading", "batchProgress", "setBatchProgress", "fetchLocalFiles", "response", "listFiles", "files", "data", "txtFiles", "filter", "file", "toLowerCase", "endsWith", "length", "success", "warning", "error", "_error$response", "_error$response$data", "detail", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "console", "log", "dataTransfer", "handleCleanData", "formData", "FormData", "for<PERSON>ach", "append", "originFileObj", "progressInterval", "setInterval", "prev", "clearInterval", "cleanData", "cleanDataLocal", "folder_path", "selected_files", "output_dir", "_error$response2", "_error$response2$data", "isFormValid", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "title", "className", "direction", "size", "width", "strong", "Group", "value", "target", "marginTop", "compact", "display", "placeholder", "flex", "onClick", "disabled", "marginLeft", "spinning", "mode", "map", "icon", "percent", "status", "description", "output_file", "processed_files", "total_rows", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [processingMode, setProcessingMode] = useState<'single' | 'batch'>('single');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState<Array<{\n    id: string;\n    customer: string;\n    inputDir: string;\n    outputDir: string;\n    fileCount?: number;\n  }>>([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState<{[key: string]: number}>({});\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地目录文件</Radio>\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n          {/* 结果展示 */}\n          {result && (\n            <Alert\n              message=\"处理完成\"\n              description={\n                <div>\n                  <p>处理结果：{result.message}</p>\n                  {result.output_file && <p>输出文件：{result.output_file}</p>}\n                  {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                  {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,eAAe,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGX,UAAU;AAClC,MAAM;EAAEY;AAAQ,CAAC,GAAGlB,MAAM;AAC1B,MAAM;EAAEmB;AAAO,CAAC,GAAGjB,MAAM;AAEzB,MAAMkB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAqB,QAAQ,CAAC;EAClF,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAM,IAAI,CAAC;;EAE/C;EACA,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAMxC,EAAE,CAAC;EACP,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAE/E;EACA,MAAMsD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACtB,UAAU,EAAE;IAEjBW,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMtC,eAAe,CAACuC,SAAS,CAACxB,UAAU,CAAC;MAC5D,MAAMyB,KAAK,GAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,IAAI,EAAE;MACvCtB,iBAAiB,CAACsB,KAAK,CAAC;;MAExB;MACA,MAAME,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAAEC,IAAY,IACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC;MAED,IAAIJ,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAE;QACvB3B,gBAAgB,CAACsB,QAAQ,CAAC;QAC1B9C,OAAO,CAACoD,OAAO,CAAC,SAASN,QAAQ,CAACK,MAAM,SAAS,CAAC;MACpD,CAAC,MAAM;QACL3B,gBAAgB,CAAC,EAAE,CAAC;QACpB,IAAIoB,KAAK,CAACO,MAAM,GAAG,CAAC,EAAE;UACpBnD,OAAO,CAACqD,OAAO,CAAC,cAAc,CAAC;QACjC;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBxD,OAAO,CAACsD,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACZ,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzDnC,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRM,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIyB,UAAU,KAAK,OAAO,IAAIM,UAAU,IAAIA,UAAU,CAACgC,MAAM,GAAG,CAAC,EAAE;MAAE;MACnE,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMmB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC7C,UAAU,EAAEM,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM0C,WAAW,GAAG;IAClBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IAAE;IAC3BC,QAAQ,EAAGC,IAAS,IAAK;MACvBjD,gBAAgB,CAACiD,IAAI,CAACC,QAAQ,CAAC;IACjC,CAAC;IACDC,MAAM,EAAGC,CAAM,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,CAAC,CAACG,YAAY,CAAC7B,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC;IACA,IAAI7D,UAAU,KAAK,QAAQ,IAAII,aAAa,CAACkC,MAAM,KAAK,CAAC,EAAE;MACzDnD,OAAO,CAACsD,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAIzC,UAAU,KAAK,OAAO,KAAK,CAACM,UAAU,IAAII,aAAa,CAAC4B,MAAM,KAAK,CAAC,CAAC,EAAE;MACzEnD,OAAO,CAACsD,KAAK,CAAC,mBAAmB,CAAC;MAClC;IACF;IAEA1B,UAAU,CAAC,IAAI,CAAC;IAChBI,WAAW,CAAC,CAAC,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,IAAIQ,QAAQ;MAEZ,IAAI7B,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAM8D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/B3D,aAAa,CAAC4D,OAAO,CAAE7B,IAAI,IAAK;UAC9B2B,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAE9B,IAAI,CAAC+B,aAAa,CAAC;QAC9C,CAAC,CAAC;QACFJ,QAAQ,CAACG,MAAM,CAAC,YAAY,EAAErD,SAAS,CAAC;;QAExC;QACA,MAAMuD,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCjD,WAAW,CAAEkD,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdC,aAAa,CAACH,gBAAgB,CAAC;cAC/B,OAAOE,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPxC,QAAQ,GAAG,MAAMtC,eAAe,CAACgF,SAAS,CAACT,QAAQ,CAAC;QACpDQ,aAAa,CAACH,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMA,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzCjD,WAAW,CAAEkD,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdC,aAAa,CAACH,gBAAgB,CAAC;cAC/B,OAAOE,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPxC,QAAQ,GAAG,MAAMtC,eAAe,CAACiF,cAAc,CAAC;UAC9CC,WAAW,EAAEnE,UAAU;UACvBoE,cAAc,EAAEhE,aAAa;UAC7BiE,UAAU,EAAE/D;QACd,CAAC,CAAC;QACF0D,aAAa,CAACH,gBAAgB,CAAC;MACjC;MAEAhD,WAAW,CAAC,GAAG,CAAC;MAChBE,SAAS,CAACQ,QAAQ,CAACG,IAAI,CAAC;MACxB7C,OAAO,CAACoD,OAAO,CAAC,SAAS,CAAC;IAE5B,CAAC,CAAC,OAAOE,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnB1F,OAAO,CAACsD,KAAK,CAAC,EAAAmC,gBAAA,GAAAnC,KAAK,CAACZ,QAAQ,cAAA+C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBjC,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI9E,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOI,aAAa,CAACkC,MAAM,GAAG,CAAC;IACjC,CAAC,MAAM;MACL,OAAOhC,UAAU,IAAII,aAAa,CAAC4B,MAAM,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,oBACE7C,OAAA;IAAAsF,QAAA,gBACEtF,OAAA,CAACC,KAAK;MAACsF,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAL,QAAA,EAAC;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClG/F,OAAA,CAACE,IAAI;MAAC8F,IAAI,EAAC,WAAW;MAAAV,QAAA,EAAC;IAEvB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEP/F,OAAA,CAACP,OAAO;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEX/F,OAAA,CAACjB,IAAI;MAACkH,KAAK,EAAC,0BAAM;MAACC,SAAS,EAAC,eAAe;MAAAZ,QAAA,eAC1CtF,OAAA,CAACR,KAAK;QAAC2G,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACZ,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAEhEtF,OAAA;UAAAsF,QAAA,gBACEtF,OAAA,CAACE,IAAI;YAACoG,MAAM;YAAAhB,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5B/F,OAAA,CAAChB,KAAK,CAACuH,KAAK;YACVC,KAAK,EAAEjG,UAAW;YAClBqD,QAAQ,EAAGI,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;YAC/ChB,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAApB,QAAA,gBAExBtF,OAAA,CAAChB,KAAK;cAACwH,KAAK,EAAC,OAAO;cAAAlB,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrC/F,OAAA,CAAChB,KAAK;cAACwH,KAAK,EAAC,QAAQ;cAAAlB,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLxF,UAAU,KAAK,OAAO,iBACrBP,OAAA,CAACR,KAAK;UAAC2G,SAAS,EAAC,UAAU;UAACX,KAAK,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAf,QAAA,gBACnDtF,OAAA;YAAAsF,QAAA,gBACEtF,OAAA,CAACE,IAAI;cAACoG,MAAM;cAAAhB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3B/F,OAAA,CAACd,KAAK,CAACqH,KAAK;cAACI,OAAO;cAACnB,KAAK,EAAE;gBAAEkB,SAAS,EAAE,CAAC;gBAAEE,OAAO,EAAE;cAAO,CAAE;cAAAtB,QAAA,gBAC5DtF,OAAA,CAACd,KAAK;gBACJsH,KAAK,EAAE3F,UAAW;gBAClB+C,QAAQ,EAAGI,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;gBAC/CK,WAAW,EAAC,gDAAsC;gBAClDrB,KAAK,EAAE;kBAAEsB,IAAI,EAAE;gBAAE;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACF/F,OAAA,CAACZ,MAAM;gBACL4G,IAAI,EAAC,SAAS;gBACde,OAAO,EAAE5E,eAAgB;gBACzBd,OAAO,EAAEE,YAAa;gBACtByF,QAAQ,EAAE,CAACnG,UAAW;gBACtB2E,KAAK,EAAE;kBAAEyB,UAAU,EAAE;gBAAE,CAAE;gBAAA3B,QAAA,EAC1B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEN/F,OAAA;YAAAsF,QAAA,gBACEtF,OAAA,CAACE,IAAI;cAACoG,MAAM;cAAAhB,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB/F,OAAA,CAACL,IAAI;cAACuH,QAAQ,EAAE3F,YAAa;cAAA+D,QAAA,eAC3BtF,OAAA,CAACb,MAAM;gBACLgI,IAAI,EAAC,UAAU;gBACfX,KAAK,EAAEvF,aAAc;gBACrB2C,QAAQ,EAAE1C,gBAAiB;gBAC3B2F,WAAW,EAAC,mCAAU;gBACtBrB,KAAK,EAAE;kBAAEa,KAAK,EAAE,MAAM;kBAAEK,SAAS,EAAE;gBAAE,CAAE;gBACvCrF,OAAO,EAAEE,YAAa;gBAAA+D,QAAA,EAErBvE,cAAc,CAACqG,GAAG,CAAE1E,IAAI,iBACvB1C,OAAA,CAACI,MAAM;kBAAYoG,KAAK,EAAE9D,IAAK;kBAAA4C,QAAA,EAC5B5C;gBAAI,GADMA,IAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGAxF,UAAU,KAAK,QAAQ,iBACtBP,OAAA;UAAAsF,QAAA,gBACEtF,OAAA,CAACE,IAAI;YAACoG,MAAM;YAAAhB,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzB/F,OAAA,CAACG,OAAO;YAAA,GAAKoD,WAAW;YAAEiC,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAApB,QAAA,gBAChDtF,OAAA;cAAGkG,SAAS,EAAC,sBAAsB;cAAAZ,QAAA,eACjCtF,OAAA,CAACJ,aAAa;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJ/F,OAAA;cAAGkG,SAAS,EAAC,iBAAiB;cAAAZ,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChD/F,OAAA;cAAGkG,SAAS,EAAC,iBAAiB;cAAAZ,QAAA,EAAC;YAE/B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,eAGD/F,OAAA;UAAAsF,QAAA,gBACEtF,OAAA,CAACE,IAAI;YAACoG,MAAM;YAAAhB,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5B/F,OAAA,CAACd,KAAK;YACJsH,KAAK,EAAErF,SAAU;YACjByC,QAAQ,EAAGI,CAAC,IAAK5C,YAAY,CAAC4C,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;YAC9CK,WAAW,EAAC,4BAAkB;YAC9BrB,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/F,OAAA,CAACZ,MAAM;UACL4G,IAAI,EAAC,SAAS;UACdI,IAAI,EAAC,OAAO;UACZiB,IAAI,eAAErH,OAAA,CAACH,kBAAkB;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BgB,OAAO,EAAE3C,eAAgB;UACzB/C,OAAO,EAAEA,OAAQ;UACjB2F,QAAQ,EAAE,CAAC3B,WAAW,CAAC,CAAE;UACzBa,SAAS,EAAC,eAAe;UAAAZ,QAAA,EAExBjE,OAAO,GAAG,SAAS,GAAG;QAAQ;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAGR1E,OAAO,iBACNrB,OAAA;UAAKkG,SAAS,EAAC,kBAAkB;UAAAZ,QAAA,gBAC/BtF,OAAA,CAACE,IAAI;YAAAoF,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClB/F,OAAA,CAACV,QAAQ;YAACgI,OAAO,EAAE7F,QAAS;YAAC8F,MAAM,EAAC;UAAQ;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN,EAGApE,MAAM,iBACL3B,OAAA,CAACX,KAAK;UACJK,OAAO,EAAC,0BAAM;UACd8H,WAAW,eACTxH,OAAA;YAAAsF,QAAA,gBACEtF,OAAA;cAAAsF,QAAA,GAAG,gCAAK,EAAC3D,MAAM,CAACjC,OAAO;YAAA;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC3BpE,MAAM,CAAC8F,WAAW,iBAAIzH,OAAA;cAAAsF,QAAA,GAAG,gCAAK,EAAC3D,MAAM,CAAC8F,WAAW;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtDpE,MAAM,CAAC+F,eAAe,iBAAI1H,OAAA;cAAAsF,QAAA,GAAG,4CAAO,EAAC3D,MAAM,CAAC+F,eAAe;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChEpE,MAAM,CAACgG,UAAU,iBAAI3H,OAAA;cAAAsF,QAAA,GAAG,0BAAI,EAAC3D,MAAM,CAACgG,UAAU;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN;UACDC,IAAI,EAAC,SAAS;UACd4B,QAAQ;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzF,EAAA,CA5SID,gBAA0B;AAAAwH,EAAA,GAA1BxH,gBAA0B;AA8ShC,eAAeA,gBAAgB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}