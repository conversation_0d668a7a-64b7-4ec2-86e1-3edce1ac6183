{"ast": null, "code": "var toString = {}.toString;\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "map": {"version": 3, "names": ["toString", "module", "exports", "Array", "isArray", "arr", "call"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/buffer/node_modules/isarray/index.js"], "sourcesContent": ["var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,CAAC,CAAC,CAACA,QAAQ;AAE1BC,MAAM,CAACC,OAAO,GAAGC,KAAK,CAACC,OAAO,IAAI,UAAUC,GAAG,EAAE;EAC/C,OAAOL,QAAQ,CAACM,IAAI,CAACD,GAAG,CAAC,IAAI,gBAAgB;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}