{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\nmodule.exports = ReactPropTypesSecret;", "map": {"version": 3, "names": ["ReactPropTypesSecret", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/prop-types/lib/ReactPropTypesSecret.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,oBAAoB,GAAG,8CAA8C;AAEzEC,MAAM,CAACC,OAAO,GAAGF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}