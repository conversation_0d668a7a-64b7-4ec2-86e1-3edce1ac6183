{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Adder = void 0;\nexports.fcumsum = fcumsum;\nexports.fsum = fsum;\n\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n,\n      x,\n      y,\n      lo,\n      hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\nexports.Adder = Adder;\nfunction fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\nfunction fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined ? v => adder.add(+v || 0) : v => adder.add(+valueof(v, ++index, values) || 0));\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "<PERSON><PERSON>", "fcumsum", "fsum", "constructor", "_partials", "Float64Array", "_n", "add", "x", "p", "i", "j", "y", "hi", "lo", "Math", "abs", "valueOf", "n", "values", "valueof", "adder", "undefined", "index", "from", "v"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/fsum.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Adder = void 0;\nexports.fcumsum = fcumsum;\nexports.fsum = fsum;\n\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n            hi = x + y,\n            lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n\n  valueOf() {\n    const p = this._partials;\n    let n = this._n,\n        x,\n        y,\n        lo,\n        hi = 0;\n\n    if (n > 0) {\n      hi = p[--n];\n\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n\n      if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n\n    return hi;\n  }\n\n}\n\nexports.Adder = Adder;\n\nfunction fsum(values, valueof) {\n  const adder = new Adder();\n\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n\n  return +adder;\n}\n\nfunction fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined ? v => adder.add(+v || 0) : v => adder.add(+valueof(v, ++index, values) || 0));\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAG,KAAK,CAAC;AACtBF,OAAO,CAACG,OAAO,GAAGA,OAAO;AACzBH,OAAO,CAACI,IAAI,GAAGA,IAAI;;AAEnB;AACA,MAAMF,KAAK,CAAC;EACVG,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,IAAIC,YAAY,CAAC,EAAE,CAAC;IACrC,IAAI,CAACC,EAAE,GAAG,CAAC;EACb;EAEAC,GAAGA,CAACC,CAAC,EAAE;IACL,MAAMC,CAAC,GAAG,IAAI,CAACL,SAAS;IACxB,IAAIM,CAAC,GAAG,CAAC;IAET,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACL,EAAE,IAAIK,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;QACRE,EAAE,GAAGL,CAAC,GAAGI,CAAC;QACVE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACR,CAAC,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACJ,CAAC,CAAC,GAAGJ,CAAC,IAAIK,EAAE,GAAGD,CAAC,CAAC,GAAGA,CAAC,IAAIC,EAAE,GAAGL,CAAC,CAAC;MAClE,IAAIM,EAAE,EAAEL,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGI,EAAE;MACnBN,CAAC,GAAGK,EAAE;IACR;IAEAJ,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC;IACR,IAAI,CAACF,EAAE,GAAGI,CAAC,GAAG,CAAC;IACf,OAAO,IAAI;EACb;EAEAO,OAAOA,CAAA,EAAG;IACR,MAAMR,CAAC,GAAG,IAAI,CAACL,SAAS;IACxB,IAAIc,CAAC,GAAG,IAAI,CAACZ,EAAE;MACXE,CAAC;MACDI,CAAC;MACDE,EAAE;MACFD,EAAE,GAAG,CAAC;IAEV,IAAIK,CAAC,GAAG,CAAC,EAAE;MACTL,EAAE,GAAGJ,CAAC,CAAC,EAAES,CAAC,CAAC;MAEX,OAAOA,CAAC,GAAG,CAAC,EAAE;QACZV,CAAC,GAAGK,EAAE;QACND,CAAC,GAAGH,CAAC,CAAC,EAAES,CAAC,CAAC;QACVL,EAAE,GAAGL,CAAC,GAAGI,CAAC;QACVE,EAAE,GAAGF,CAAC,IAAIC,EAAE,GAAGL,CAAC,CAAC;QACjB,IAAIM,EAAE,EAAE;MACV;MAEA,IAAII,CAAC,GAAG,CAAC,KAAKJ,EAAE,GAAG,CAAC,IAAIL,CAAC,CAACS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIJ,EAAE,GAAG,CAAC,IAAIL,CAAC,CAACS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;QAC/DN,CAAC,GAAGE,EAAE,GAAG,CAAC;QACVN,CAAC,GAAGK,EAAE,GAAGD,CAAC;QACV,IAAIA,CAAC,IAAIJ,CAAC,GAAGK,EAAE,EAAEA,EAAE,GAAGL,CAAC;MACzB;IACF;IAEA,OAAOK,EAAE;EACX;AAEF;AAEAf,OAAO,CAACE,KAAK,GAAGA,KAAK;AAErB,SAASE,IAAIA,CAACiB,MAAM,EAAEC,OAAO,EAAE;EAC7B,MAAMC,KAAK,GAAG,IAAIrB,KAAK,CAAC,CAAC;EAEzB,IAAIoB,OAAO,KAAKE,SAAS,EAAE;IACzB,KAAK,IAAIvB,KAAK,IAAIoB,MAAM,EAAE;MACxB,IAAIpB,KAAK,GAAG,CAACA,KAAK,EAAE;QAClBsB,KAAK,CAACd,GAAG,CAACR,KAAK,CAAC;MAClB;IACF;EACF,CAAC,MAAM;IACL,IAAIwB,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIxB,KAAK,IAAIoB,MAAM,EAAE;MACxB,IAAIpB,KAAK,GAAG,CAACqB,OAAO,CAACrB,KAAK,EAAE,EAAEwB,KAAK,EAAEJ,MAAM,CAAC,EAAE;QAC5CE,KAAK,CAACd,GAAG,CAACR,KAAK,CAAC;MAClB;IACF;EACF;EAEA,OAAO,CAACsB,KAAK;AACf;AAEA,SAASpB,OAAOA,CAACkB,MAAM,EAAEC,OAAO,EAAE;EAChC,MAAMC,KAAK,GAAG,IAAIrB,KAAK,CAAC,CAAC;EACzB,IAAIuB,KAAK,GAAG,CAAC,CAAC;EACd,OAAOlB,YAAY,CAACmB,IAAI,CAACL,MAAM,EAAEC,OAAO,KAAKE,SAAS,GAAGG,CAAC,IAAIJ,KAAK,CAACd,GAAG,CAAC,CAACkB,CAAC,IAAI,CAAC,CAAC,GAAGA,CAAC,IAAIJ,KAAK,CAACd,GAAG,CAAC,CAACa,OAAO,CAACK,CAAC,EAAE,EAAEF,KAAK,EAAEJ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACvI", "ignoreList": []}, "metadata": {}, "sourceType": "script"}