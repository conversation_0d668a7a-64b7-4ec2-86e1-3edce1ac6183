{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = extent;\nfunction extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "extent", "values", "valueof", "min", "max", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/extent.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = extent;\n\nfunction extent(values, valueof) {\n  let min;\n  let max;\n\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n\n  return [min, max];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC/B,IAAIC,GAAG;EACP,IAAIC,GAAG;EAEP,IAAIF,OAAO,KAAKG,SAAS,EAAE;IACzB,KAAK,MAAMP,KAAK,IAAIG,MAAM,EAAE;MAC1B,IAAIH,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIK,GAAG,KAAKE,SAAS,EAAE;UACrB,IAAIP,KAAK,IAAIA,KAAK,EAAEK,GAAG,GAAGC,GAAG,GAAGN,KAAK;QACvC,CAAC,MAAM;UACL,IAAIK,GAAG,GAAGL,KAAK,EAAEK,GAAG,GAAGL,KAAK;UAC5B,IAAIM,GAAG,GAAGN,KAAK,EAAEM,GAAG,GAAGN,KAAK;QAC9B;MACF;IACF;EACF,CAAC,MAAM;IACL,IAAIQ,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIR,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEQ,KAAK,EAAEL,MAAM,CAAC,KAAK,IAAI,EAAE;QACrD,IAAIE,GAAG,KAAKE,SAAS,EAAE;UACrB,IAAIP,KAAK,IAAIA,KAAK,EAAEK,GAAG,GAAGC,GAAG,GAAGN,KAAK;QACvC,CAAC,MAAM;UACL,IAAIK,GAAG,GAAGL,KAAK,EAAEK,GAAG,GAAGL,KAAK;UAC5B,IAAIM,GAAG,GAAGN,KAAK,EAAEM,GAAG,GAAGN,KAAK;QAC9B;MACF;IACF;EACF;EAEA,OAAO,CAACK,GAAG,EAAEC,GAAG,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}