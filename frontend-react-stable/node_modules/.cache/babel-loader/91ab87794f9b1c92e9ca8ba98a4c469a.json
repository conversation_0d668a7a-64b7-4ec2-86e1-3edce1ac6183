{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nexports.__esModule = true;\nexports.default = addClass;\nvar _hasClass = _interopRequireDefault(require(\"./hasClass\"));\nfunction addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!(0, _hasClass.default)(element, className)) if (typeof element.className === 'string') element.className = element.className + ' ' + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + ' ' + className);\n}\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "exports", "__esModule", "default", "addClass", "_hasClass", "element", "className", "classList", "add", "setAttribute", "baseVal", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/dom-helpers/class/addClass.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nexports.__esModule = true;\nexports.default = addClass;\n\nvar _hasClass = _interopRequireDefault(require(\"./hasClass\"));\n\nfunction addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!(0, _hasClass.default)(element, className)) if (typeof element.className === 'string') element.className = element.className + ' ' + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + ' ' + className);\n}\n\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC;AAEpFC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE7D,SAASI,QAAQA,CAACE,OAAO,EAAEC,SAAS,EAAE;EACpC,IAAID,OAAO,CAACE,SAAS,EAAEF,OAAO,CAACE,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEF,SAAS,CAACF,OAAO,EAAEG,OAAO,EAAEC,SAAS,CAAC,EAAE,IAAI,OAAOD,OAAO,CAACC,SAAS,KAAK,QAAQ,EAAED,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,GAAG,GAAG,GAAGA,SAAS,CAAC,KAAKD,OAAO,CAACI,YAAY,CAAC,OAAO,EAAE,CAACJ,OAAO,CAACC,SAAS,IAAID,OAAO,CAACC,SAAS,CAACI,OAAO,IAAI,EAAE,IAAI,GAAG,GAAGJ,SAAS,CAAC;AAC/T;AAEAK,MAAM,CAACX,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}