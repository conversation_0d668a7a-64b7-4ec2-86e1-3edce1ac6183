{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport { INTERNAL_COL_DEFINE } from 'rc-table';\nimport { arrAdd, arrDel } from \"rc-tree/es/util\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { useCallback, useMemo, useState } from 'react';\nimport Checkbox from '../../checkbox';\nimport Dropdown from '../../dropdown';\nimport Radio from '../../radio';\nimport warning from '../../_util/warning';\n// TODO: warning if use ajax!!!\nexport var SELECTION_COLUMN = {};\nexport var SELECTION_ALL = 'SELECT_ALL';\nexport var SELECTION_INVERT = 'SELECT_INVERT';\nexport var SELECTION_NONE = 'SELECT_NONE';\nvar EMPTY_LIST = [];\nfunction flattenData(data, childrenColumnName) {\n  var list = [];\n  (data || []).forEach(function (record) {\n    list.push(record);\n    if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n      list = [].concat(_toConsumableArray(list), _toConsumableArray(flattenData(record[childrenColumnName], childrenColumnName)));\n    }\n  });\n  return list;\n}\nexport default function useSelection(rowSelection, config) {\n  var _ref = rowSelection || {},\n    preserveSelectedRowKeys = _ref.preserveSelectedRowKeys,\n    selectedRowKeys = _ref.selectedRowKeys,\n    defaultSelectedRowKeys = _ref.defaultSelectedRowKeys,\n    getCheckboxProps = _ref.getCheckboxProps,\n    onSelectionChange = _ref.onChange,\n    onSelect = _ref.onSelect,\n    onSelectAll = _ref.onSelectAll,\n    onSelectInvert = _ref.onSelectInvert,\n    onSelectNone = _ref.onSelectNone,\n    onSelectMultiple = _ref.onSelectMultiple,\n    selectionColWidth = _ref.columnWidth,\n    selectionType = _ref.type,\n    selections = _ref.selections,\n    fixed = _ref.fixed,\n    customizeRenderCell = _ref.renderCell,\n    hideSelectAll = _ref.hideSelectAll,\n    _ref$checkStrictly = _ref.checkStrictly,\n    checkStrictly = _ref$checkStrictly === void 0 ? true : _ref$checkStrictly;\n  var prefixCls = config.prefixCls,\n    data = config.data,\n    pageData = config.pageData,\n    getRecordByKey = config.getRecordByKey,\n    getRowKey = config.getRowKey,\n    expandType = config.expandType,\n    childrenColumnName = config.childrenColumnName,\n    tableLocale = config.locale,\n    getPopupContainer = config.getPopupContainer;\n  // ========================= Keys =========================\n  var _useMergedState = useMergedState(selectedRowKeys || defaultSelectedRowKeys || EMPTY_LIST, {\n      value: selectedRowKeys\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSelectedKeys = _useMergedState2[0],\n    setMergedSelectedKeys = _useMergedState2[1];\n  // ======================== Caches ========================\n  var preserveRecordsRef = React.useRef(new Map());\n  var updatePreserveRecordsCache = useCallback(function (keys) {\n    if (preserveSelectedRowKeys) {\n      var newCache = new Map();\n      // Keep key if mark as preserveSelectedRowKeys\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (!record && preserveRecordsRef.current.has(key)) {\n          record = preserveRecordsRef.current.get(key);\n        }\n        newCache.set(key, record);\n      });\n      // Refresh to new cache\n      preserveRecordsRef.current = newCache;\n    }\n  }, [getRecordByKey, preserveSelectedRowKeys]);\n  // Update cache with selectedKeys\n  React.useEffect(function () {\n    updatePreserveRecordsCache(mergedSelectedKeys);\n  }, [mergedSelectedKeys]);\n  var _useMemo = useMemo(function () {\n      return checkStrictly ? {\n        keyEntities: null\n      } : convertDataToEntities(data, {\n        externalGetKey: getRowKey,\n        childrenPropName: childrenColumnName\n      });\n    }, [data, getRowKey, checkStrictly, childrenColumnName]),\n    keyEntities = _useMemo.keyEntities;\n  // Get flatten data\n  var flattedData = useMemo(function () {\n    return flattenData(pageData, childrenColumnName);\n  }, [pageData, childrenColumnName]);\n  // Get all checkbox props\n  var checkboxPropsMap = useMemo(function () {\n    var map = new Map();\n    flattedData.forEach(function (record, index) {\n      var key = getRowKey(record, index);\n      var checkboxProps = (getCheckboxProps ? getCheckboxProps(record) : null) || {};\n      map.set(key, checkboxProps);\n      process.env.NODE_ENV !== \"production\" ? warning(!('checked' in checkboxProps || 'defaultChecked' in checkboxProps), 'Table', 'Do not set `checked` or `defaultChecked` in `getCheckboxProps`. Please use `selectedRowKeys` instead.') : void 0;\n    });\n    return map;\n  }, [flattedData, getRowKey, getCheckboxProps]);\n  var isCheckboxDisabled = useCallback(function (r) {\n    var _a;\n    return !!((_a = checkboxPropsMap.get(getRowKey(r))) === null || _a === void 0 ? void 0 : _a.disabled);\n  }, [checkboxPropsMap, getRowKey]);\n  var _useMemo2 = useMemo(function () {\n      if (checkStrictly) {\n        return [mergedSelectedKeys || [], []];\n      }\n      var _conductCheck = conductCheck(mergedSelectedKeys, true, keyEntities, isCheckboxDisabled),\n        checkedKeys = _conductCheck.checkedKeys,\n        halfCheckedKeys = _conductCheck.halfCheckedKeys;\n      return [checkedKeys || [], halfCheckedKeys];\n    }, [mergedSelectedKeys, checkStrictly, keyEntities, isCheckboxDisabled]),\n    _useMemo3 = _slicedToArray(_useMemo2, 2),\n    derivedSelectedKeys = _useMemo3[0],\n    derivedHalfSelectedKeys = _useMemo3[1];\n  var derivedSelectedKeySet = useMemo(function () {\n    var keys = selectionType === 'radio' ? derivedSelectedKeys.slice(0, 1) : derivedSelectedKeys;\n    return new Set(keys);\n  }, [derivedSelectedKeys, selectionType]);\n  var derivedHalfSelectedKeySet = useMemo(function () {\n    return selectionType === 'radio' ? new Set() : new Set(derivedHalfSelectedKeys);\n  }, [derivedHalfSelectedKeys, selectionType]);\n  // Save last selected key to enable range selection\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    lastSelectedKey = _useState2[0],\n    setLastSelectedKey = _useState2[1];\n  // Reset if rowSelection reset\n  React.useEffect(function () {\n    if (!rowSelection) {\n      setMergedSelectedKeys(EMPTY_LIST);\n    }\n  }, [!!rowSelection]);\n  var setSelectedKeys = useCallback(function (keys, method) {\n    var availableKeys;\n    var records;\n    updatePreserveRecordsCache(keys);\n    if (preserveSelectedRowKeys) {\n      availableKeys = keys;\n      records = keys.map(function (key) {\n        return preserveRecordsRef.current.get(key);\n      });\n    } else {\n      // Filter key which not exist in the `dataSource`\n      availableKeys = [];\n      records = [];\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (record !== undefined) {\n          availableKeys.push(key);\n          records.push(record);\n        }\n      });\n    }\n    setMergedSelectedKeys(availableKeys);\n    onSelectionChange === null || onSelectionChange === void 0 ? void 0 : onSelectionChange(availableKeys, records, {\n      type: method\n    });\n  }, [setMergedSelectedKeys, getRecordByKey, onSelectionChange, preserveSelectedRowKeys]);\n  // ====================== Selections ======================\n  // Trigger single `onSelect` event\n  var triggerSingleSelection = useCallback(function (key, selected, keys, event) {\n    if (onSelect) {\n      var rows = keys.map(function (k) {\n        return getRecordByKey(k);\n      });\n      onSelect(getRecordByKey(key), selected, rows, event);\n    }\n    setSelectedKeys(keys, 'single');\n  }, [onSelect, getRecordByKey, setSelectedKeys]);\n  var mergedSelections = useMemo(function () {\n    if (!selections || hideSelectAll) {\n      return null;\n    }\n    var selectionList = selections === true ? [SELECTION_ALL, SELECTION_INVERT, SELECTION_NONE] : selections;\n    return selectionList.map(function (selection) {\n      if (selection === SELECTION_ALL) {\n        return {\n          key: 'all',\n          text: tableLocale.selectionAll,\n          onSelect: function onSelect() {\n            setSelectedKeys(data.map(function (record, index) {\n              return getRowKey(record, index);\n            }).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return !(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled) || derivedSelectedKeySet.has(key);\n            }), 'all');\n          }\n        };\n      }\n      if (selection === SELECTION_INVERT) {\n        return {\n          key: 'invert',\n          text: tableLocale.selectInvert,\n          onSelect: function onSelect() {\n            var keySet = new Set(derivedSelectedKeySet);\n            pageData.forEach(function (record, index) {\n              var key = getRowKey(record, index);\n              var checkProps = checkboxPropsMap.get(key);\n              if (!(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled)) {\n                if (keySet.has(key)) {\n                  keySet[\"delete\"](key);\n                } else {\n                  keySet.add(key);\n                }\n              }\n            });\n            var keys = Array.from(keySet);\n            if (onSelectInvert) {\n              process.env.NODE_ENV !== \"production\" ? warning(false, 'Table', '`onSelectInvert` will be removed in future. Please use `onChange` instead.') : void 0;\n              onSelectInvert(keys);\n            }\n            setSelectedKeys(keys, 'invert');\n          }\n        };\n      }\n      if (selection === SELECTION_NONE) {\n        return {\n          key: 'none',\n          text: tableLocale.selectNone,\n          onSelect: function onSelect() {\n            onSelectNone === null || onSelectNone === void 0 ? void 0 : onSelectNone();\n            setSelectedKeys(Array.from(derivedSelectedKeySet).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled;\n            }), 'none');\n          }\n        };\n      }\n      return selection;\n    }).map(function (selection) {\n      return _extends(_extends({}, selection), {\n        onSelect: function onSelect() {\n          var _a2;\n          var _a;\n          for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n            rest[_key] = arguments[_key];\n          }\n          (_a = selection.onSelect) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [selection].concat(rest));\n          setLastSelectedKey(null);\n        }\n      });\n    });\n  }, [selections, derivedSelectedKeySet, pageData, getRowKey, onSelectInvert, setSelectedKeys]);\n  // ======================= Columns ========================\n  var transformColumns = useCallback(function (columns) {\n    var _a;\n    // >>>>>>>>>>> Skip if not exists `rowSelection`\n    if (!rowSelection) {\n      process.env.NODE_ENV !== \"production\" ? warning(!columns.includes(SELECTION_COLUMN), 'Table', '`rowSelection` is not config but `SELECTION_COLUMN` exists in the `columns`.') : void 0;\n      return columns.filter(function (col) {\n        return col !== SELECTION_COLUMN;\n      });\n    }\n    // >>>>>>>>>>> Support selection\n    var cloneColumns = _toConsumableArray(columns);\n    var keySet = new Set(derivedSelectedKeySet);\n    // Record key only need check with enabled\n    var recordKeys = flattedData.map(getRowKey).filter(function (key) {\n      return !checkboxPropsMap.get(key).disabled;\n    });\n    var checkedCurrentAll = recordKeys.every(function (key) {\n      return keySet.has(key);\n    });\n    var checkedCurrentSome = recordKeys.some(function (key) {\n      return keySet.has(key);\n    });\n    var onSelectAllChange = function onSelectAllChange() {\n      var changeKeys = [];\n      if (checkedCurrentAll) {\n        recordKeys.forEach(function (key) {\n          keySet[\"delete\"](key);\n          changeKeys.push(key);\n        });\n      } else {\n        recordKeys.forEach(function (key) {\n          if (!keySet.has(key)) {\n            keySet.add(key);\n            changeKeys.push(key);\n          }\n        });\n      }\n      var keys = Array.from(keySet);\n      onSelectAll === null || onSelectAll === void 0 ? void 0 : onSelectAll(!checkedCurrentAll, keys.map(function (k) {\n        return getRecordByKey(k);\n      }), changeKeys.map(function (k) {\n        return getRecordByKey(k);\n      }));\n      setSelectedKeys(keys, 'all');\n      setLastSelectedKey(null);\n    };\n    // ===================== Render =====================\n    // Title Cell\n    var title;\n    if (selectionType !== 'radio') {\n      var customizeSelections;\n      if (mergedSelections) {\n        var menu = {\n          getPopupContainer: getPopupContainer,\n          items: mergedSelections.map(function (selection, index) {\n            var key = selection.key,\n              text = selection.text,\n              onSelectionClick = selection.onSelect;\n            return {\n              key: key || index,\n              onClick: function onClick() {\n                onSelectionClick === null || onSelectionClick === void 0 ? void 0 : onSelectionClick(recordKeys);\n              },\n              label: text\n            };\n          })\n        };\n        customizeSelections = /*#__PURE__*/React.createElement(\"div\", {\n          className: \"\".concat(prefixCls, \"-selection-extra\")\n        }, /*#__PURE__*/React.createElement(Dropdown, {\n          menu: menu,\n          getPopupContainer: getPopupContainer\n        }, /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(DownOutlined, null))));\n      }\n      var allDisabledData = flattedData.map(function (record, index) {\n        var key = getRowKey(record, index);\n        var checkboxProps = checkboxPropsMap.get(key) || {};\n        return _extends({\n          checked: keySet.has(key)\n        }, checkboxProps);\n      }).filter(function (_ref2) {\n        var disabled = _ref2.disabled;\n        return disabled;\n      });\n      var allDisabled = !!allDisabledData.length && allDisabledData.length === flattedData.length;\n      var allDisabledAndChecked = allDisabled && allDisabledData.every(function (_ref3) {\n        var checked = _ref3.checked;\n        return checked;\n      });\n      var allDisabledSomeChecked = allDisabled && allDisabledData.some(function (_ref4) {\n        var checked = _ref4.checked;\n        return checked;\n      });\n      title = !hideSelectAll && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-selection\")\n      }, /*#__PURE__*/React.createElement(Checkbox, {\n        checked: !allDisabled ? !!flattedData.length && checkedCurrentAll : allDisabledAndChecked,\n        indeterminate: !allDisabled ? !checkedCurrentAll && checkedCurrentSome : !allDisabledAndChecked && allDisabledSomeChecked,\n        onChange: onSelectAllChange,\n        disabled: flattedData.length === 0 || allDisabled,\n        skipGroup: true\n      }), customizeSelections);\n    }\n    // Body Cell\n    var renderCell;\n    if (selectionType === 'radio') {\n      renderCell = function renderCell(_, record, index) {\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        return {\n          node: /*#__PURE__*/React.createElement(Radio, _extends({}, checkboxPropsMap.get(key), {\n            checked: checked,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(event) {\n              if (!keySet.has(key)) {\n                triggerSingleSelection(key, true, [key], event.nativeEvent);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    } else {\n      renderCell = function renderCell(_, record, index) {\n        var _a;\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        var indeterminate = derivedHalfSelectedKeySet.has(key);\n        var checkboxProps = checkboxPropsMap.get(key);\n        var mergedIndeterminate;\n        if (expandType === 'nest') {\n          mergedIndeterminate = indeterminate;\n          process.env.NODE_ENV !== \"production\" ? warning(typeof (checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== 'boolean', 'Table', 'set `indeterminate` using `rowSelection.getCheckboxProps` is not allowed with tree structured dataSource.') : void 0;\n        } else {\n          mergedIndeterminate = (_a = checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== null && _a !== void 0 ? _a : indeterminate;\n        }\n        // Record checked\n        return {\n          node: /*#__PURE__*/React.createElement(Checkbox, _extends({}, checkboxProps, {\n            indeterminate: mergedIndeterminate,\n            checked: checked,\n            skipGroup: true,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(_ref5) {\n              var nativeEvent = _ref5.nativeEvent;\n              var shiftKey = nativeEvent.shiftKey;\n              var startIndex = -1;\n              var endIndex = -1;\n              // Get range of this\n              if (shiftKey && checkStrictly) {\n                var pointKeys = new Set([lastSelectedKey, key]);\n                recordKeys.some(function (recordKey, recordIndex) {\n                  if (pointKeys.has(recordKey)) {\n                    if (startIndex === -1) {\n                      startIndex = recordIndex;\n                    } else {\n                      endIndex = recordIndex;\n                      return true;\n                    }\n                  }\n                  return false;\n                });\n              }\n              if (endIndex !== -1 && startIndex !== endIndex && checkStrictly) {\n                // Batch update selections\n                var rangeKeys = recordKeys.slice(startIndex, endIndex + 1);\n                var changedKeys = [];\n                if (checked) {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet[\"delete\"](recordKey);\n                    }\n                  });\n                } else {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (!keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet.add(recordKey);\n                    }\n                  });\n                }\n                var keys = Array.from(keySet);\n                onSelectMultiple === null || onSelectMultiple === void 0 ? void 0 : onSelectMultiple(!checked, keys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }), changedKeys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }));\n                setSelectedKeys(keys, 'multiple');\n              } else {\n                // Single record selected\n                var originCheckedKeys = derivedSelectedKeys;\n                if (checkStrictly) {\n                  var checkedKeys = checked ? arrDel(originCheckedKeys, key) : arrAdd(originCheckedKeys, key);\n                  triggerSingleSelection(key, !checked, checkedKeys, nativeEvent);\n                } else {\n                  // Always fill first\n                  var result = conductCheck([].concat(_toConsumableArray(originCheckedKeys), [key]), true, keyEntities, isCheckboxDisabled);\n                  var _checkedKeys = result.checkedKeys,\n                    halfCheckedKeys = result.halfCheckedKeys;\n                  var nextCheckedKeys = _checkedKeys;\n                  // If remove, we do it again to correction\n                  if (checked) {\n                    var tempKeySet = new Set(_checkedKeys);\n                    tempKeySet[\"delete\"](key);\n                    nextCheckedKeys = conductCheck(Array.from(tempKeySet), {\n                      checked: false,\n                      halfCheckedKeys: halfCheckedKeys\n                    }, keyEntities, isCheckboxDisabled).checkedKeys;\n                  }\n                  triggerSingleSelection(key, !checked, nextCheckedKeys, nativeEvent);\n                }\n              }\n              if (checked) {\n                setLastSelectedKey(null);\n              } else {\n                setLastSelectedKey(key);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    }\n    var renderSelectionCell = function renderSelectionCell(_, record, index) {\n      var _renderCell = renderCell(_, record, index),\n        node = _renderCell.node,\n        checked = _renderCell.checked;\n      if (customizeRenderCell) {\n        return customizeRenderCell(checked, record, index, node);\n      }\n      return node;\n    };\n    // Insert selection column if not exist\n    if (!cloneColumns.includes(SELECTION_COLUMN)) {\n      // Always after expand icon\n      if (cloneColumns.findIndex(function (col) {\n        var _a;\n        return ((_a = col[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN';\n      }) === 0) {\n        var _cloneColumns = cloneColumns,\n          _cloneColumns2 = _toArray(_cloneColumns),\n          expandColumn = _cloneColumns2[0],\n          restColumns = _cloneColumns2.slice(1);\n        cloneColumns = [expandColumn, SELECTION_COLUMN].concat(_toConsumableArray(restColumns));\n      } else {\n        // Normal insert at first column\n        cloneColumns = [SELECTION_COLUMN].concat(_toConsumableArray(cloneColumns));\n      }\n    }\n    // Deduplicate selection column\n    var selectionColumnIndex = cloneColumns.indexOf(SELECTION_COLUMN);\n    process.env.NODE_ENV !== \"production\" ? warning(cloneColumns.filter(function (col) {\n      return col === SELECTION_COLUMN;\n    }).length <= 1, 'Table', 'Multiple `SELECTION_COLUMN` exist in `columns`.') : void 0;\n    cloneColumns = cloneColumns.filter(function (column, index) {\n      return column !== SELECTION_COLUMN || index === selectionColumnIndex;\n    });\n    // Fixed column logic\n    var prevCol = cloneColumns[selectionColumnIndex - 1];\n    var nextCol = cloneColumns[selectionColumnIndex + 1];\n    var mergedFixed = fixed;\n    if (mergedFixed === undefined) {\n      if ((nextCol === null || nextCol === void 0 ? void 0 : nextCol.fixed) !== undefined) {\n        mergedFixed = nextCol.fixed;\n      } else if ((prevCol === null || prevCol === void 0 ? void 0 : prevCol.fixed) !== undefined) {\n        mergedFixed = prevCol.fixed;\n      }\n    }\n    if (mergedFixed && prevCol && ((_a = prevCol[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN' && prevCol.fixed === undefined) {\n      prevCol.fixed = mergedFixed;\n    }\n    // Replace with real selection column\n    var selectionColumn = _defineProperty({\n      fixed: mergedFixed,\n      width: selectionColWidth,\n      className: \"\".concat(prefixCls, \"-selection-column\"),\n      title: rowSelection.columnTitle || title,\n      render: renderSelectionCell\n    }, INTERNAL_COL_DEFINE, {\n      className: \"\".concat(prefixCls, \"-selection-col\")\n    });\n    return cloneColumns.map(function (col) {\n      return col === SELECTION_COLUMN ? selectionColumn : col;\n    });\n  }, [getRowKey, flattedData, rowSelection, derivedSelectedKeys, derivedSelectedKeySet, derivedHalfSelectedKeySet, selectionColWidth, mergedSelections, expandType, lastSelectedKey, checkboxPropsMap, onSelectMultiple, triggerSingleSelection, isCheckboxDisabled]);\n  return [transformColumns, derivedSelectedKeySet];\n}", "map": {"version": 3, "names": ["_defineProperty", "_toArray", "_extends", "_slicedToArray", "_toConsumableArray", "_typeof", "DownOutlined", "INTERNAL_COL_DEFINE", "arrAdd", "arr<PERSON><PERSON>", "conduct<PERSON>heck", "convertDataToEntities", "useMergedState", "React", "useCallback", "useMemo", "useState", "Checkbox", "Dropdown", "Radio", "warning", "SELECTION_COLUMN", "SELECTION_ALL", "SELECTION_INVERT", "SELECTION_NONE", "EMPTY_LIST", "flattenData", "data", "childrenColumnName", "list", "for<PERSON>ach", "record", "push", "concat", "useSelection", "rowSelection", "config", "_ref", "preserveSelectedRowKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultSelectedRowKeys", "getCheckboxProps", "onSelectionChange", "onChange", "onSelect", "onSelectAll", "onSelectInvert", "onSelectNone", "onSelectMultiple", "selection<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnWidth", "selectionType", "type", "selections", "fixed", "customizeRenderCell", "renderCell", "hideSelectAll", "_ref$checkStrictly", "checkStrictly", "prefixCls", "pageData", "getRecordByKey", "getRowKey", "expandType", "tableLocale", "locale", "getPopupContainer", "_useMergedState", "value", "_useMergedState2", "mergedSelectedKeys", "setMergedSelectedKeys", "preserveRecordsRef", "useRef", "Map", "updatePreserveRecordsCache", "keys", "newCache", "key", "current", "has", "get", "set", "useEffect", "_useMemo", "keyEntities", "externalGetKey", "childrenPropName", "flattedData", "checkboxPropsMap", "map", "index", "checkboxProps", "process", "env", "NODE_ENV", "isCheckboxDisabled", "r", "_a", "disabled", "_useMemo2", "_conductCheck", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "_useMemo3", "derivedSelectedKeys", "derivedHalfSelectedKeys", "derivedSelectedKeySet", "slice", "Set", "derivedHalfSelectedKeySet", "_useState", "_useState2", "lastSelectedKey", "setLastSelectedKey", "setSelectedKeys", "method", "availableKeys", "records", "undefined", "triggerSingleSelection", "selected", "event", "rows", "k", "mergedSelections", "selectionList", "selection", "text", "selectionAll", "filter", "checkProps", "selectInvert", "keySet", "add", "Array", "from", "selectNone", "_a2", "_len", "arguments", "length", "rest", "_key", "call", "apply", "transformColumns", "columns", "includes", "col", "cloneColumns", "recordKeys", "checkedCurrentAll", "every", "checkedCurrentSome", "some", "onSelectAllChange", "changeKeys", "title", "customizeSelections", "menu", "items", "onSelectionClick", "onClick", "label", "createElement", "className", "allDisabledData", "checked", "_ref2", "allDisabled", "allDisabledAndChecked", "_ref3", "allDisabledSomeChecked", "_ref4", "indeterminate", "skipGroup", "_", "node", "e", "stopPropagation", "nativeEvent", "mergedIndeterminate", "_ref5", "shift<PERSON>ey", "startIndex", "endIndex", "point<PERSON>eys", "<PERSON><PERSON>ey", "recordIndex", "rangeKeys", "changed<PERSON><PERSON><PERSON>", "originCheckedKeys", "result", "_checked<PERSON><PERSON><PERSON>", "next<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tempKeySet", "renderSelectionCell", "_renderCell", "findIndex", "columnType", "_cloneColumns", "_cloneColumns2", "expandColumn", "restColumns", "selectionColumnIndex", "indexOf", "column", "prevCol", "nextCol", "mergedFixed", "selectionColumn", "width", "columnTitle", "render"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/hooks/useSelection.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport { INTERNAL_COL_DEFINE } from 'rc-table';\nimport { arrAdd, arrDel } from \"rc-tree/es/util\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { useCallback, useMemo, useState } from 'react';\nimport Checkbox from '../../checkbox';\nimport Dropdown from '../../dropdown';\nimport Radio from '../../radio';\nimport warning from '../../_util/warning';\n// TODO: warning if use ajax!!!\nexport var SELECTION_COLUMN = {};\nexport var SELECTION_ALL = 'SELECT_ALL';\nexport var SELECTION_INVERT = 'SELECT_INVERT';\nexport var SELECTION_NONE = 'SELECT_NONE';\nvar EMPTY_LIST = [];\nfunction flattenData(data, childrenColumnName) {\n  var list = [];\n  (data || []).forEach(function (record) {\n    list.push(record);\n    if (record && _typeof(record) === 'object' && childrenColumnName in record) {\n      list = [].concat(_toConsumableArray(list), _toConsumableArray(flattenData(record[childrenColumnName], childrenColumnName)));\n    }\n  });\n  return list;\n}\nexport default function useSelection(rowSelection, config) {\n  var _ref = rowSelection || {},\n    preserveSelectedRowKeys = _ref.preserveSelectedRowKeys,\n    selectedRowKeys = _ref.selectedRowKeys,\n    defaultSelectedRowKeys = _ref.defaultSelectedRowKeys,\n    getCheckboxProps = _ref.getCheckboxProps,\n    onSelectionChange = _ref.onChange,\n    onSelect = _ref.onSelect,\n    onSelectAll = _ref.onSelectAll,\n    onSelectInvert = _ref.onSelectInvert,\n    onSelectNone = _ref.onSelectNone,\n    onSelectMultiple = _ref.onSelectMultiple,\n    selectionColWidth = _ref.columnWidth,\n    selectionType = _ref.type,\n    selections = _ref.selections,\n    fixed = _ref.fixed,\n    customizeRenderCell = _ref.renderCell,\n    hideSelectAll = _ref.hideSelectAll,\n    _ref$checkStrictly = _ref.checkStrictly,\n    checkStrictly = _ref$checkStrictly === void 0 ? true : _ref$checkStrictly;\n  var prefixCls = config.prefixCls,\n    data = config.data,\n    pageData = config.pageData,\n    getRecordByKey = config.getRecordByKey,\n    getRowKey = config.getRowKey,\n    expandType = config.expandType,\n    childrenColumnName = config.childrenColumnName,\n    tableLocale = config.locale,\n    getPopupContainer = config.getPopupContainer;\n  // ========================= Keys =========================\n  var _useMergedState = useMergedState(selectedRowKeys || defaultSelectedRowKeys || EMPTY_LIST, {\n      value: selectedRowKeys\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSelectedKeys = _useMergedState2[0],\n    setMergedSelectedKeys = _useMergedState2[1];\n  // ======================== Caches ========================\n  var preserveRecordsRef = React.useRef(new Map());\n  var updatePreserveRecordsCache = useCallback(function (keys) {\n    if (preserveSelectedRowKeys) {\n      var newCache = new Map();\n      // Keep key if mark as preserveSelectedRowKeys\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (!record && preserveRecordsRef.current.has(key)) {\n          record = preserveRecordsRef.current.get(key);\n        }\n        newCache.set(key, record);\n      });\n      // Refresh to new cache\n      preserveRecordsRef.current = newCache;\n    }\n  }, [getRecordByKey, preserveSelectedRowKeys]);\n  // Update cache with selectedKeys\n  React.useEffect(function () {\n    updatePreserveRecordsCache(mergedSelectedKeys);\n  }, [mergedSelectedKeys]);\n  var _useMemo = useMemo(function () {\n      return checkStrictly ? {\n        keyEntities: null\n      } : convertDataToEntities(data, {\n        externalGetKey: getRowKey,\n        childrenPropName: childrenColumnName\n      });\n    }, [data, getRowKey, checkStrictly, childrenColumnName]),\n    keyEntities = _useMemo.keyEntities;\n  // Get flatten data\n  var flattedData = useMemo(function () {\n    return flattenData(pageData, childrenColumnName);\n  }, [pageData, childrenColumnName]);\n  // Get all checkbox props\n  var checkboxPropsMap = useMemo(function () {\n    var map = new Map();\n    flattedData.forEach(function (record, index) {\n      var key = getRowKey(record, index);\n      var checkboxProps = (getCheckboxProps ? getCheckboxProps(record) : null) || {};\n      map.set(key, checkboxProps);\n      process.env.NODE_ENV !== \"production\" ? warning(!('checked' in checkboxProps || 'defaultChecked' in checkboxProps), 'Table', 'Do not set `checked` or `defaultChecked` in `getCheckboxProps`. Please use `selectedRowKeys` instead.') : void 0;\n    });\n    return map;\n  }, [flattedData, getRowKey, getCheckboxProps]);\n  var isCheckboxDisabled = useCallback(function (r) {\n    var _a;\n    return !!((_a = checkboxPropsMap.get(getRowKey(r))) === null || _a === void 0 ? void 0 : _a.disabled);\n  }, [checkboxPropsMap, getRowKey]);\n  var _useMemo2 = useMemo(function () {\n      if (checkStrictly) {\n        return [mergedSelectedKeys || [], []];\n      }\n      var _conductCheck = conductCheck(mergedSelectedKeys, true, keyEntities, isCheckboxDisabled),\n        checkedKeys = _conductCheck.checkedKeys,\n        halfCheckedKeys = _conductCheck.halfCheckedKeys;\n      return [checkedKeys || [], halfCheckedKeys];\n    }, [mergedSelectedKeys, checkStrictly, keyEntities, isCheckboxDisabled]),\n    _useMemo3 = _slicedToArray(_useMemo2, 2),\n    derivedSelectedKeys = _useMemo3[0],\n    derivedHalfSelectedKeys = _useMemo3[1];\n  var derivedSelectedKeySet = useMemo(function () {\n    var keys = selectionType === 'radio' ? derivedSelectedKeys.slice(0, 1) : derivedSelectedKeys;\n    return new Set(keys);\n  }, [derivedSelectedKeys, selectionType]);\n  var derivedHalfSelectedKeySet = useMemo(function () {\n    return selectionType === 'radio' ? new Set() : new Set(derivedHalfSelectedKeys);\n  }, [derivedHalfSelectedKeys, selectionType]);\n  // Save last selected key to enable range selection\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    lastSelectedKey = _useState2[0],\n    setLastSelectedKey = _useState2[1];\n  // Reset if rowSelection reset\n  React.useEffect(function () {\n    if (!rowSelection) {\n      setMergedSelectedKeys(EMPTY_LIST);\n    }\n  }, [!!rowSelection]);\n  var setSelectedKeys = useCallback(function (keys, method) {\n    var availableKeys;\n    var records;\n    updatePreserveRecordsCache(keys);\n    if (preserveSelectedRowKeys) {\n      availableKeys = keys;\n      records = keys.map(function (key) {\n        return preserveRecordsRef.current.get(key);\n      });\n    } else {\n      // Filter key which not exist in the `dataSource`\n      availableKeys = [];\n      records = [];\n      keys.forEach(function (key) {\n        var record = getRecordByKey(key);\n        if (record !== undefined) {\n          availableKeys.push(key);\n          records.push(record);\n        }\n      });\n    }\n    setMergedSelectedKeys(availableKeys);\n    onSelectionChange === null || onSelectionChange === void 0 ? void 0 : onSelectionChange(availableKeys, records, {\n      type: method\n    });\n  }, [setMergedSelectedKeys, getRecordByKey, onSelectionChange, preserveSelectedRowKeys]);\n  // ====================== Selections ======================\n  // Trigger single `onSelect` event\n  var triggerSingleSelection = useCallback(function (key, selected, keys, event) {\n    if (onSelect) {\n      var rows = keys.map(function (k) {\n        return getRecordByKey(k);\n      });\n      onSelect(getRecordByKey(key), selected, rows, event);\n    }\n    setSelectedKeys(keys, 'single');\n  }, [onSelect, getRecordByKey, setSelectedKeys]);\n  var mergedSelections = useMemo(function () {\n    if (!selections || hideSelectAll) {\n      return null;\n    }\n    var selectionList = selections === true ? [SELECTION_ALL, SELECTION_INVERT, SELECTION_NONE] : selections;\n    return selectionList.map(function (selection) {\n      if (selection === SELECTION_ALL) {\n        return {\n          key: 'all',\n          text: tableLocale.selectionAll,\n          onSelect: function onSelect() {\n            setSelectedKeys(data.map(function (record, index) {\n              return getRowKey(record, index);\n            }).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return !(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled) || derivedSelectedKeySet.has(key);\n            }), 'all');\n          }\n        };\n      }\n      if (selection === SELECTION_INVERT) {\n        return {\n          key: 'invert',\n          text: tableLocale.selectInvert,\n          onSelect: function onSelect() {\n            var keySet = new Set(derivedSelectedKeySet);\n            pageData.forEach(function (record, index) {\n              var key = getRowKey(record, index);\n              var checkProps = checkboxPropsMap.get(key);\n              if (!(checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled)) {\n                if (keySet.has(key)) {\n                  keySet[\"delete\"](key);\n                } else {\n                  keySet.add(key);\n                }\n              }\n            });\n            var keys = Array.from(keySet);\n            if (onSelectInvert) {\n              process.env.NODE_ENV !== \"production\" ? warning(false, 'Table', '`onSelectInvert` will be removed in future. Please use `onChange` instead.') : void 0;\n              onSelectInvert(keys);\n            }\n            setSelectedKeys(keys, 'invert');\n          }\n        };\n      }\n      if (selection === SELECTION_NONE) {\n        return {\n          key: 'none',\n          text: tableLocale.selectNone,\n          onSelect: function onSelect() {\n            onSelectNone === null || onSelectNone === void 0 ? void 0 : onSelectNone();\n            setSelectedKeys(Array.from(derivedSelectedKeySet).filter(function (key) {\n              var checkProps = checkboxPropsMap.get(key);\n              return checkProps === null || checkProps === void 0 ? void 0 : checkProps.disabled;\n            }), 'none');\n          }\n        };\n      }\n      return selection;\n    }).map(function (selection) {\n      return _extends(_extends({}, selection), {\n        onSelect: function onSelect() {\n          var _a2;\n          var _a;\n          for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n            rest[_key] = arguments[_key];\n          }\n          (_a = selection.onSelect) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [selection].concat(rest));\n          setLastSelectedKey(null);\n        }\n      });\n    });\n  }, [selections, derivedSelectedKeySet, pageData, getRowKey, onSelectInvert, setSelectedKeys]);\n  // ======================= Columns ========================\n  var transformColumns = useCallback(function (columns) {\n    var _a;\n    // >>>>>>>>>>> Skip if not exists `rowSelection`\n    if (!rowSelection) {\n      process.env.NODE_ENV !== \"production\" ? warning(!columns.includes(SELECTION_COLUMN), 'Table', '`rowSelection` is not config but `SELECTION_COLUMN` exists in the `columns`.') : void 0;\n      return columns.filter(function (col) {\n        return col !== SELECTION_COLUMN;\n      });\n    }\n    // >>>>>>>>>>> Support selection\n    var cloneColumns = _toConsumableArray(columns);\n    var keySet = new Set(derivedSelectedKeySet);\n    // Record key only need check with enabled\n    var recordKeys = flattedData.map(getRowKey).filter(function (key) {\n      return !checkboxPropsMap.get(key).disabled;\n    });\n    var checkedCurrentAll = recordKeys.every(function (key) {\n      return keySet.has(key);\n    });\n    var checkedCurrentSome = recordKeys.some(function (key) {\n      return keySet.has(key);\n    });\n    var onSelectAllChange = function onSelectAllChange() {\n      var changeKeys = [];\n      if (checkedCurrentAll) {\n        recordKeys.forEach(function (key) {\n          keySet[\"delete\"](key);\n          changeKeys.push(key);\n        });\n      } else {\n        recordKeys.forEach(function (key) {\n          if (!keySet.has(key)) {\n            keySet.add(key);\n            changeKeys.push(key);\n          }\n        });\n      }\n      var keys = Array.from(keySet);\n      onSelectAll === null || onSelectAll === void 0 ? void 0 : onSelectAll(!checkedCurrentAll, keys.map(function (k) {\n        return getRecordByKey(k);\n      }), changeKeys.map(function (k) {\n        return getRecordByKey(k);\n      }));\n      setSelectedKeys(keys, 'all');\n      setLastSelectedKey(null);\n    };\n    // ===================== Render =====================\n    // Title Cell\n    var title;\n    if (selectionType !== 'radio') {\n      var customizeSelections;\n      if (mergedSelections) {\n        var menu = {\n          getPopupContainer: getPopupContainer,\n          items: mergedSelections.map(function (selection, index) {\n            var key = selection.key,\n              text = selection.text,\n              onSelectionClick = selection.onSelect;\n            return {\n              key: key || index,\n              onClick: function onClick() {\n                onSelectionClick === null || onSelectionClick === void 0 ? void 0 : onSelectionClick(recordKeys);\n              },\n              label: text\n            };\n          })\n        };\n        customizeSelections = /*#__PURE__*/React.createElement(\"div\", {\n          className: \"\".concat(prefixCls, \"-selection-extra\")\n        }, /*#__PURE__*/React.createElement(Dropdown, {\n          menu: menu,\n          getPopupContainer: getPopupContainer\n        }, /*#__PURE__*/React.createElement(\"span\", null, /*#__PURE__*/React.createElement(DownOutlined, null))));\n      }\n      var allDisabledData = flattedData.map(function (record, index) {\n        var key = getRowKey(record, index);\n        var checkboxProps = checkboxPropsMap.get(key) || {};\n        return _extends({\n          checked: keySet.has(key)\n        }, checkboxProps);\n      }).filter(function (_ref2) {\n        var disabled = _ref2.disabled;\n        return disabled;\n      });\n      var allDisabled = !!allDisabledData.length && allDisabledData.length === flattedData.length;\n      var allDisabledAndChecked = allDisabled && allDisabledData.every(function (_ref3) {\n        var checked = _ref3.checked;\n        return checked;\n      });\n      var allDisabledSomeChecked = allDisabled && allDisabledData.some(function (_ref4) {\n        var checked = _ref4.checked;\n        return checked;\n      });\n      title = !hideSelectAll && /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-selection\")\n      }, /*#__PURE__*/React.createElement(Checkbox, {\n        checked: !allDisabled ? !!flattedData.length && checkedCurrentAll : allDisabledAndChecked,\n        indeterminate: !allDisabled ? !checkedCurrentAll && checkedCurrentSome : !allDisabledAndChecked && allDisabledSomeChecked,\n        onChange: onSelectAllChange,\n        disabled: flattedData.length === 0 || allDisabled,\n        skipGroup: true\n      }), customizeSelections);\n    }\n    // Body Cell\n    var renderCell;\n    if (selectionType === 'radio') {\n      renderCell = function renderCell(_, record, index) {\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        return {\n          node: /*#__PURE__*/React.createElement(Radio, _extends({}, checkboxPropsMap.get(key), {\n            checked: checked,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(event) {\n              if (!keySet.has(key)) {\n                triggerSingleSelection(key, true, [key], event.nativeEvent);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    } else {\n      renderCell = function renderCell(_, record, index) {\n        var _a;\n        var key = getRowKey(record, index);\n        var checked = keySet.has(key);\n        var indeterminate = derivedHalfSelectedKeySet.has(key);\n        var checkboxProps = checkboxPropsMap.get(key);\n        var mergedIndeterminate;\n        if (expandType === 'nest') {\n          mergedIndeterminate = indeterminate;\n          process.env.NODE_ENV !== \"production\" ? warning(typeof (checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== 'boolean', 'Table', 'set `indeterminate` using `rowSelection.getCheckboxProps` is not allowed with tree structured dataSource.') : void 0;\n        } else {\n          mergedIndeterminate = (_a = checkboxProps === null || checkboxProps === void 0 ? void 0 : checkboxProps.indeterminate) !== null && _a !== void 0 ? _a : indeterminate;\n        }\n        // Record checked\n        return {\n          node: /*#__PURE__*/React.createElement(Checkbox, _extends({}, checkboxProps, {\n            indeterminate: mergedIndeterminate,\n            checked: checked,\n            skipGroup: true,\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            },\n            onChange: function onChange(_ref5) {\n              var nativeEvent = _ref5.nativeEvent;\n              var shiftKey = nativeEvent.shiftKey;\n              var startIndex = -1;\n              var endIndex = -1;\n              // Get range of this\n              if (shiftKey && checkStrictly) {\n                var pointKeys = new Set([lastSelectedKey, key]);\n                recordKeys.some(function (recordKey, recordIndex) {\n                  if (pointKeys.has(recordKey)) {\n                    if (startIndex === -1) {\n                      startIndex = recordIndex;\n                    } else {\n                      endIndex = recordIndex;\n                      return true;\n                    }\n                  }\n                  return false;\n                });\n              }\n              if (endIndex !== -1 && startIndex !== endIndex && checkStrictly) {\n                // Batch update selections\n                var rangeKeys = recordKeys.slice(startIndex, endIndex + 1);\n                var changedKeys = [];\n                if (checked) {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet[\"delete\"](recordKey);\n                    }\n                  });\n                } else {\n                  rangeKeys.forEach(function (recordKey) {\n                    if (!keySet.has(recordKey)) {\n                      changedKeys.push(recordKey);\n                      keySet.add(recordKey);\n                    }\n                  });\n                }\n                var keys = Array.from(keySet);\n                onSelectMultiple === null || onSelectMultiple === void 0 ? void 0 : onSelectMultiple(!checked, keys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }), changedKeys.map(function (recordKey) {\n                  return getRecordByKey(recordKey);\n                }));\n                setSelectedKeys(keys, 'multiple');\n              } else {\n                // Single record selected\n                var originCheckedKeys = derivedSelectedKeys;\n                if (checkStrictly) {\n                  var checkedKeys = checked ? arrDel(originCheckedKeys, key) : arrAdd(originCheckedKeys, key);\n                  triggerSingleSelection(key, !checked, checkedKeys, nativeEvent);\n                } else {\n                  // Always fill first\n                  var result = conductCheck([].concat(_toConsumableArray(originCheckedKeys), [key]), true, keyEntities, isCheckboxDisabled);\n                  var _checkedKeys = result.checkedKeys,\n                    halfCheckedKeys = result.halfCheckedKeys;\n                  var nextCheckedKeys = _checkedKeys;\n                  // If remove, we do it again to correction\n                  if (checked) {\n                    var tempKeySet = new Set(_checkedKeys);\n                    tempKeySet[\"delete\"](key);\n                    nextCheckedKeys = conductCheck(Array.from(tempKeySet), {\n                      checked: false,\n                      halfCheckedKeys: halfCheckedKeys\n                    }, keyEntities, isCheckboxDisabled).checkedKeys;\n                  }\n                  triggerSingleSelection(key, !checked, nextCheckedKeys, nativeEvent);\n                }\n              }\n              if (checked) {\n                setLastSelectedKey(null);\n              } else {\n                setLastSelectedKey(key);\n              }\n            }\n          })),\n          checked: checked\n        };\n      };\n    }\n    var renderSelectionCell = function renderSelectionCell(_, record, index) {\n      var _renderCell = renderCell(_, record, index),\n        node = _renderCell.node,\n        checked = _renderCell.checked;\n      if (customizeRenderCell) {\n        return customizeRenderCell(checked, record, index, node);\n      }\n      return node;\n    };\n    // Insert selection column if not exist\n    if (!cloneColumns.includes(SELECTION_COLUMN)) {\n      // Always after expand icon\n      if (cloneColumns.findIndex(function (col) {\n        var _a;\n        return ((_a = col[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN';\n      }) === 0) {\n        var _cloneColumns = cloneColumns,\n          _cloneColumns2 = _toArray(_cloneColumns),\n          expandColumn = _cloneColumns2[0],\n          restColumns = _cloneColumns2.slice(1);\n        cloneColumns = [expandColumn, SELECTION_COLUMN].concat(_toConsumableArray(restColumns));\n      } else {\n        // Normal insert at first column\n        cloneColumns = [SELECTION_COLUMN].concat(_toConsumableArray(cloneColumns));\n      }\n    }\n    // Deduplicate selection column\n    var selectionColumnIndex = cloneColumns.indexOf(SELECTION_COLUMN);\n    process.env.NODE_ENV !== \"production\" ? warning(cloneColumns.filter(function (col) {\n      return col === SELECTION_COLUMN;\n    }).length <= 1, 'Table', 'Multiple `SELECTION_COLUMN` exist in `columns`.') : void 0;\n    cloneColumns = cloneColumns.filter(function (column, index) {\n      return column !== SELECTION_COLUMN || index === selectionColumnIndex;\n    });\n    // Fixed column logic\n    var prevCol = cloneColumns[selectionColumnIndex - 1];\n    var nextCol = cloneColumns[selectionColumnIndex + 1];\n    var mergedFixed = fixed;\n    if (mergedFixed === undefined) {\n      if ((nextCol === null || nextCol === void 0 ? void 0 : nextCol.fixed) !== undefined) {\n        mergedFixed = nextCol.fixed;\n      } else if ((prevCol === null || prevCol === void 0 ? void 0 : prevCol.fixed) !== undefined) {\n        mergedFixed = prevCol.fixed;\n      }\n    }\n    if (mergedFixed && prevCol && ((_a = prevCol[INTERNAL_COL_DEFINE]) === null || _a === void 0 ? void 0 : _a.columnType) === 'EXPAND_COLUMN' && prevCol.fixed === undefined) {\n      prevCol.fixed = mergedFixed;\n    }\n    // Replace with real selection column\n    var selectionColumn = _defineProperty({\n      fixed: mergedFixed,\n      width: selectionColWidth,\n      className: \"\".concat(prefixCls, \"-selection-column\"),\n      title: rowSelection.columnTitle || title,\n      render: renderSelectionCell\n    }, INTERNAL_COL_DEFINE, {\n      className: \"\".concat(prefixCls, \"-selection-col\")\n    });\n    return cloneColumns.map(function (col) {\n      return col === SELECTION_COLUMN ? selectionColumn : col;\n    });\n  }, [getRowKey, flattedData, rowSelection, derivedSelectedKeys, derivedSelectedKeySet, derivedHalfSelectedKeySet, selectionColWidth, mergedSelections, expandType, lastSelectedKey, checkboxPropsMap, onSelectMultiple, triggerSingleSelection, isCheckboxDisabled]);\n  return [transformColumns, derivedSelectedKeySet];\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,MAAM,EAAEC,MAAM,QAAQ,iBAAiB;AAChD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACtD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,OAAO,MAAM,qBAAqB;AACzC;AACA,OAAO,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AAChC,OAAO,IAAIC,aAAa,GAAG,YAAY;AACvC,OAAO,IAAIC,gBAAgB,GAAG,eAAe;AAC7C,OAAO,IAAIC,cAAc,GAAG,aAAa;AACzC,IAAIC,UAAU,GAAG,EAAE;AACnB,SAASC,WAAWA,CAACC,IAAI,EAAEC,kBAAkB,EAAE;EAC7C,IAAIC,IAAI,GAAG,EAAE;EACb,CAACF,IAAI,IAAI,EAAE,EAAEG,OAAO,CAAC,UAAUC,MAAM,EAAE;IACrCF,IAAI,CAACG,IAAI,CAACD,MAAM,CAAC;IACjB,IAAIA,MAAM,IAAI1B,OAAO,CAAC0B,MAAM,CAAC,KAAK,QAAQ,IAAIH,kBAAkB,IAAIG,MAAM,EAAE;MAC1EF,IAAI,GAAG,EAAE,CAACI,MAAM,CAAC7B,kBAAkB,CAACyB,IAAI,CAAC,EAAEzB,kBAAkB,CAACsB,WAAW,CAACK,MAAM,CAACH,kBAAkB,CAAC,EAAEA,kBAAkB,CAAC,CAAC,CAAC;IAC7H;EACF,CAAC,CAAC;EACF,OAAOC,IAAI;AACb;AACA,eAAe,SAASK,YAAYA,CAACC,YAAY,EAAEC,MAAM,EAAE;EACzD,IAAIC,IAAI,GAAGF,YAAY,IAAI,CAAC,CAAC;IAC3BG,uBAAuB,GAAGD,IAAI,CAACC,uBAAuB;IACtDC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,sBAAsB,GAAGH,IAAI,CAACG,sBAAsB;IACpDC,gBAAgB,GAAGJ,IAAI,CAACI,gBAAgB;IACxCC,iBAAiB,GAAGL,IAAI,CAACM,QAAQ;IACjCC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,WAAW,GAAGR,IAAI,CAACQ,WAAW;IAC9BC,cAAc,GAAGT,IAAI,CAACS,cAAc;IACpCC,YAAY,GAAGV,IAAI,CAACU,YAAY;IAChCC,gBAAgB,GAAGX,IAAI,CAACW,gBAAgB;IACxCC,iBAAiB,GAAGZ,IAAI,CAACa,WAAW;IACpCC,aAAa,GAAGd,IAAI,CAACe,IAAI;IACzBC,UAAU,GAAGhB,IAAI,CAACgB,UAAU;IAC5BC,KAAK,GAAGjB,IAAI,CAACiB,KAAK;IAClBC,mBAAmB,GAAGlB,IAAI,CAACmB,UAAU;IACrCC,aAAa,GAAGpB,IAAI,CAACoB,aAAa;IAClCC,kBAAkB,GAAGrB,IAAI,CAACsB,aAAa;IACvCA,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,kBAAkB;EAC3E,IAAIE,SAAS,GAAGxB,MAAM,CAACwB,SAAS;IAC9BjC,IAAI,GAAGS,MAAM,CAACT,IAAI;IAClBkC,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ;IAC1BC,cAAc,GAAG1B,MAAM,CAAC0B,cAAc;IACtCC,SAAS,GAAG3B,MAAM,CAAC2B,SAAS;IAC5BC,UAAU,GAAG5B,MAAM,CAAC4B,UAAU;IAC9BpC,kBAAkB,GAAGQ,MAAM,CAACR,kBAAkB;IAC9CqC,WAAW,GAAG7B,MAAM,CAAC8B,MAAM;IAC3BC,iBAAiB,GAAG/B,MAAM,CAAC+B,iBAAiB;EAC9C;EACA,IAAIC,eAAe,GAAGxD,cAAc,CAAC2B,eAAe,IAAIC,sBAAsB,IAAIf,UAAU,EAAE;MAC1F4C,KAAK,EAAE9B;IACT,CAAC,CAAC;IACF+B,gBAAgB,GAAGnE,cAAc,CAACiE,eAAe,EAAE,CAAC,CAAC;IACrDG,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C;EACA,IAAIG,kBAAkB,GAAG5D,KAAK,CAAC6D,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAChD,IAAIC,0BAA0B,GAAG9D,WAAW,CAAC,UAAU+D,IAAI,EAAE;IAC3D,IAAIvC,uBAAuB,EAAE;MAC3B,IAAIwC,QAAQ,GAAG,IAAIH,GAAG,CAAC,CAAC;MACxB;MACAE,IAAI,CAAC/C,OAAO,CAAC,UAAUiD,GAAG,EAAE;QAC1B,IAAIhD,MAAM,GAAG+B,cAAc,CAACiB,GAAG,CAAC;QAChC,IAAI,CAAChD,MAAM,IAAI0C,kBAAkB,CAACO,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;UAClDhD,MAAM,GAAG0C,kBAAkB,CAACO,OAAO,CAACE,GAAG,CAACH,GAAG,CAAC;QAC9C;QACAD,QAAQ,CAACK,GAAG,CAACJ,GAAG,EAAEhD,MAAM,CAAC;MAC3B,CAAC,CAAC;MACF;MACA0C,kBAAkB,CAACO,OAAO,GAAGF,QAAQ;IACvC;EACF,CAAC,EAAE,CAAChB,cAAc,EAAExB,uBAAuB,CAAC,CAAC;EAC7C;EACAzB,KAAK,CAACuE,SAAS,CAAC,YAAY;IAC1BR,0BAA0B,CAACL,kBAAkB,CAAC;EAChD,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACxB,IAAIc,QAAQ,GAAGtE,OAAO,CAAC,YAAY;MAC/B,OAAO4C,aAAa,GAAG;QACrB2B,WAAW,EAAE;MACf,CAAC,GAAG3E,qBAAqB,CAACgB,IAAI,EAAE;QAC9B4D,cAAc,EAAExB,SAAS;QACzByB,gBAAgB,EAAE5D;MACpB,CAAC,CAAC;IACJ,CAAC,EAAE,CAACD,IAAI,EAAEoC,SAAS,EAAEJ,aAAa,EAAE/B,kBAAkB,CAAC,CAAC;IACxD0D,WAAW,GAAGD,QAAQ,CAACC,WAAW;EACpC;EACA,IAAIG,WAAW,GAAG1E,OAAO,CAAC,YAAY;IACpC,OAAOW,WAAW,CAACmC,QAAQ,EAAEjC,kBAAkB,CAAC;EAClD,CAAC,EAAE,CAACiC,QAAQ,EAAEjC,kBAAkB,CAAC,CAAC;EAClC;EACA,IAAI8D,gBAAgB,GAAG3E,OAAO,CAAC,YAAY;IACzC,IAAI4E,GAAG,GAAG,IAAIhB,GAAG,CAAC,CAAC;IACnBc,WAAW,CAAC3D,OAAO,CAAC,UAAUC,MAAM,EAAE6D,KAAK,EAAE;MAC3C,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;MAClC,IAAIC,aAAa,GAAG,CAACpD,gBAAgB,GAAGA,gBAAgB,CAACV,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;MAC9E4D,GAAG,CAACR,GAAG,CAACJ,GAAG,EAAEc,aAAa,CAAC;MAC3BC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5E,OAAO,CAAC,EAAE,SAAS,IAAIyE,aAAa,IAAI,gBAAgB,IAAIA,aAAa,CAAC,EAAE,OAAO,EAAE,uGAAuG,CAAC,GAAG,KAAK,CAAC;IAChP,CAAC,CAAC;IACF,OAAOF,GAAG;EACZ,CAAC,EAAE,CAACF,WAAW,EAAE1B,SAAS,EAAEtB,gBAAgB,CAAC,CAAC;EAC9C,IAAIwD,kBAAkB,GAAGnF,WAAW,CAAC,UAAUoF,CAAC,EAAE;IAChD,IAAIC,EAAE;IACN,OAAO,CAAC,EAAE,CAACA,EAAE,GAAGT,gBAAgB,CAACR,GAAG,CAACnB,SAAS,CAACmC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,CAAC;EACvG,CAAC,EAAE,CAACV,gBAAgB,EAAE3B,SAAS,CAAC,CAAC;EACjC,IAAIsC,SAAS,GAAGtF,OAAO,CAAC,YAAY;MAChC,IAAI4C,aAAa,EAAE;QACjB,OAAO,CAACY,kBAAkB,IAAI,EAAE,EAAE,EAAE,CAAC;MACvC;MACA,IAAI+B,aAAa,GAAG5F,YAAY,CAAC6D,kBAAkB,EAAE,IAAI,EAAEe,WAAW,EAAEW,kBAAkB,CAAC;QACzFM,WAAW,GAAGD,aAAa,CAACC,WAAW;QACvCC,eAAe,GAAGF,aAAa,CAACE,eAAe;MACjD,OAAO,CAACD,WAAW,IAAI,EAAE,EAAEC,eAAe,CAAC;IAC7C,CAAC,EAAE,CAACjC,kBAAkB,EAAEZ,aAAa,EAAE2B,WAAW,EAAEW,kBAAkB,CAAC,CAAC;IACxEQ,SAAS,GAAGtG,cAAc,CAACkG,SAAS,EAAE,CAAC,CAAC;IACxCK,mBAAmB,GAAGD,SAAS,CAAC,CAAC,CAAC;IAClCE,uBAAuB,GAAGF,SAAS,CAAC,CAAC,CAAC;EACxC,IAAIG,qBAAqB,GAAG7F,OAAO,CAAC,YAAY;IAC9C,IAAI8D,IAAI,GAAG1B,aAAa,KAAK,OAAO,GAAGuD,mBAAmB,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGH,mBAAmB;IAC5F,OAAO,IAAII,GAAG,CAACjC,IAAI,CAAC;EACtB,CAAC,EAAE,CAAC6B,mBAAmB,EAAEvD,aAAa,CAAC,CAAC;EACxC,IAAI4D,yBAAyB,GAAGhG,OAAO,CAAC,YAAY;IAClD,OAAOoC,aAAa,KAAK,OAAO,GAAG,IAAI2D,GAAG,CAAC,CAAC,GAAG,IAAIA,GAAG,CAACH,uBAAuB,CAAC;EACjF,CAAC,EAAE,CAACA,uBAAuB,EAAExD,aAAa,CAAC,CAAC;EAC5C;EACA,IAAI6D,SAAS,GAAGhG,QAAQ,CAAC,IAAI,CAAC;IAC5BiG,UAAU,GAAG9G,cAAc,CAAC6G,SAAS,EAAE,CAAC,CAAC;IACzCE,eAAe,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/BE,kBAAkB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACpC;EACApG,KAAK,CAACuE,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACjD,YAAY,EAAE;MACjBqC,qBAAqB,CAAC/C,UAAU,CAAC;IACnC;EACF,CAAC,EAAE,CAAC,CAAC,CAACU,YAAY,CAAC,CAAC;EACpB,IAAIiF,eAAe,GAAGtG,WAAW,CAAC,UAAU+D,IAAI,EAAEwC,MAAM,EAAE;IACxD,IAAIC,aAAa;IACjB,IAAIC,OAAO;IACX3C,0BAA0B,CAACC,IAAI,CAAC;IAChC,IAAIvC,uBAAuB,EAAE;MAC3BgF,aAAa,GAAGzC,IAAI;MACpB0C,OAAO,GAAG1C,IAAI,CAACc,GAAG,CAAC,UAAUZ,GAAG,EAAE;QAChC,OAAON,kBAAkB,CAACO,OAAO,CAACE,GAAG,CAACH,GAAG,CAAC;MAC5C,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAuC,aAAa,GAAG,EAAE;MAClBC,OAAO,GAAG,EAAE;MACZ1C,IAAI,CAAC/C,OAAO,CAAC,UAAUiD,GAAG,EAAE;QAC1B,IAAIhD,MAAM,GAAG+B,cAAc,CAACiB,GAAG,CAAC;QAChC,IAAIhD,MAAM,KAAKyF,SAAS,EAAE;UACxBF,aAAa,CAACtF,IAAI,CAAC+C,GAAG,CAAC;UACvBwC,OAAO,CAACvF,IAAI,CAACD,MAAM,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;IACAyC,qBAAqB,CAAC8C,aAAa,CAAC;IACpC5E,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC4E,aAAa,EAAEC,OAAO,EAAE;MAC9GnE,IAAI,EAAEiE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7C,qBAAqB,EAAEV,cAAc,EAAEpB,iBAAiB,EAAEJ,uBAAuB,CAAC,CAAC;EACvF;EACA;EACA,IAAImF,sBAAsB,GAAG3G,WAAW,CAAC,UAAUiE,GAAG,EAAE2C,QAAQ,EAAE7C,IAAI,EAAE8C,KAAK,EAAE;IAC7E,IAAI/E,QAAQ,EAAE;MACZ,IAAIgF,IAAI,GAAG/C,IAAI,CAACc,GAAG,CAAC,UAAUkC,CAAC,EAAE;QAC/B,OAAO/D,cAAc,CAAC+D,CAAC,CAAC;MAC1B,CAAC,CAAC;MACFjF,QAAQ,CAACkB,cAAc,CAACiB,GAAG,CAAC,EAAE2C,QAAQ,EAAEE,IAAI,EAAED,KAAK,CAAC;IACtD;IACAP,eAAe,CAACvC,IAAI,EAAE,QAAQ,CAAC;EACjC,CAAC,EAAE,CAACjC,QAAQ,EAAEkB,cAAc,EAAEsD,eAAe,CAAC,CAAC;EAC/C,IAAIU,gBAAgB,GAAG/G,OAAO,CAAC,YAAY;IACzC,IAAI,CAACsC,UAAU,IAAII,aAAa,EAAE;MAChC,OAAO,IAAI;IACb;IACA,IAAIsE,aAAa,GAAG1E,UAAU,KAAK,IAAI,GAAG,CAAC/B,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,CAAC,GAAG6B,UAAU;IACxG,OAAO0E,aAAa,CAACpC,GAAG,CAAC,UAAUqC,SAAS,EAAE;MAC5C,IAAIA,SAAS,KAAK1G,aAAa,EAAE;QAC/B,OAAO;UACLyD,GAAG,EAAE,KAAK;UACVkD,IAAI,EAAEhE,WAAW,CAACiE,YAAY;UAC9BtF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5BwE,eAAe,CAACzF,IAAI,CAACgE,GAAG,CAAC,UAAU5D,MAAM,EAAE6D,KAAK,EAAE;cAChD,OAAO7B,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;YACjC,CAAC,CAAC,CAACuC,MAAM,CAAC,UAAUpD,GAAG,EAAE;cACvB,IAAIqD,UAAU,GAAG1C,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAC1C,OAAO,EAAEqD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChC,QAAQ,CAAC,IAAIQ,qBAAqB,CAAC3B,GAAG,CAACF,GAAG,CAAC;YACzH,CAAC,CAAC,EAAE,KAAK,CAAC;UACZ;QACF,CAAC;MACH;MACA,IAAIiD,SAAS,KAAKzG,gBAAgB,EAAE;QAClC,OAAO;UACLwD,GAAG,EAAE,QAAQ;UACbkD,IAAI,EAAEhE,WAAW,CAACoE,YAAY;UAC9BzF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5B,IAAI0F,MAAM,GAAG,IAAIxB,GAAG,CAACF,qBAAqB,CAAC;YAC3C/C,QAAQ,CAAC/B,OAAO,CAAC,UAAUC,MAAM,EAAE6D,KAAK,EAAE;cACxC,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;cAClC,IAAIwC,UAAU,GAAG1C,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAC1C,IAAI,EAAEqD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChC,QAAQ,CAAC,EAAE;gBAClF,IAAIkC,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC,EAAE;kBACnBuD,MAAM,CAAC,QAAQ,CAAC,CAACvD,GAAG,CAAC;gBACvB,CAAC,MAAM;kBACLuD,MAAM,CAACC,GAAG,CAACxD,GAAG,CAAC;gBACjB;cACF;YACF,CAAC,CAAC;YACF,IAAIF,IAAI,GAAG2D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;YAC7B,IAAIxF,cAAc,EAAE;cAClBgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5E,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,4EAA4E,CAAC,GAAG,KAAK,CAAC;cACtJ0B,cAAc,CAAC+B,IAAI,CAAC;YACtB;YACAuC,eAAe,CAACvC,IAAI,EAAE,QAAQ,CAAC;UACjC;QACF,CAAC;MACH;MACA,IAAImD,SAAS,KAAKxG,cAAc,EAAE;QAChC,OAAO;UACLuD,GAAG,EAAE,MAAM;UACXkD,IAAI,EAAEhE,WAAW,CAACyE,UAAU;UAC5B9F,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5BG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC;YAC1EqE,eAAe,CAACoB,KAAK,CAACC,IAAI,CAAC7B,qBAAqB,CAAC,CAACuB,MAAM,CAAC,UAAUpD,GAAG,EAAE;cACtE,IAAIqD,UAAU,GAAG1C,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;cAC1C,OAAOqD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAChC,QAAQ;YACpF,CAAC,CAAC,EAAE,MAAM,CAAC;UACb;QACF,CAAC;MACH;MACA,OAAO4B,SAAS;IAClB,CAAC,CAAC,CAACrC,GAAG,CAAC,UAAUqC,SAAS,EAAE;MAC1B,OAAO9H,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8H,SAAS,CAAC,EAAE;QACvCpF,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC5B,IAAI+F,GAAG;UACP,IAAIxC,EAAE;UACN,KAAK,IAAIyC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIP,KAAK,CAACI,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;YACvFD,IAAI,CAACC,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;UAC9B;UACA,CAAC7C,EAAE,GAAG6B,SAAS,CAACpF,QAAQ,MAAM,IAAI,IAAIuD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACwC,GAAG,GAAGxC,EAAE,EAAE8C,IAAI,CAACC,KAAK,CAACP,GAAG,EAAE,CAACX,SAAS,CAAC,CAAC/F,MAAM,CAAC8G,IAAI,CAAC,CAAC;UACnH5B,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9D,UAAU,EAAEuD,qBAAqB,EAAE/C,QAAQ,EAAEE,SAAS,EAAEjB,cAAc,EAAEsE,eAAe,CAAC,CAAC;EAC7F;EACA,IAAI+B,gBAAgB,GAAGrI,WAAW,CAAC,UAAUsI,OAAO,EAAE;IACpD,IAAIjD,EAAE;IACN;IACA,IAAI,CAAChE,YAAY,EAAE;MACjB2D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5E,OAAO,CAAC,CAACgI,OAAO,CAACC,QAAQ,CAAChI,gBAAgB,CAAC,EAAE,OAAO,EAAE,8EAA8E,CAAC,GAAG,KAAK,CAAC;MACtL,OAAO+H,OAAO,CAACjB,MAAM,CAAC,UAAUmB,GAAG,EAAE;QACnC,OAAOA,GAAG,KAAKjI,gBAAgB;MACjC,CAAC,CAAC;IACJ;IACA;IACA,IAAIkI,YAAY,GAAGnJ,kBAAkB,CAACgJ,OAAO,CAAC;IAC9C,IAAId,MAAM,GAAG,IAAIxB,GAAG,CAACF,qBAAqB,CAAC;IAC3C;IACA,IAAI4C,UAAU,GAAG/D,WAAW,CAACE,GAAG,CAAC5B,SAAS,CAAC,CAACoE,MAAM,CAAC,UAAUpD,GAAG,EAAE;MAChE,OAAO,CAACW,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,CAACqB,QAAQ;IAC5C,CAAC,CAAC;IACF,IAAIqD,iBAAiB,GAAGD,UAAU,CAACE,KAAK,CAAC,UAAU3E,GAAG,EAAE;MACtD,OAAOuD,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,IAAI4E,kBAAkB,GAAGH,UAAU,CAACI,IAAI,CAAC,UAAU7E,GAAG,EAAE;MACtD,OAAOuD,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,IAAI8E,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;MACnD,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIL,iBAAiB,EAAE;QACrBD,UAAU,CAAC1H,OAAO,CAAC,UAAUiD,GAAG,EAAE;UAChCuD,MAAM,CAAC,QAAQ,CAAC,CAACvD,GAAG,CAAC;UACrB+E,UAAU,CAAC9H,IAAI,CAAC+C,GAAG,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLyE,UAAU,CAAC1H,OAAO,CAAC,UAAUiD,GAAG,EAAE;UAChC,IAAI,CAACuD,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC,EAAE;YACpBuD,MAAM,CAACC,GAAG,CAACxD,GAAG,CAAC;YACf+E,UAAU,CAAC9H,IAAI,CAAC+C,GAAG,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;MACA,IAAIF,IAAI,GAAG2D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;MAC7BzF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC4G,iBAAiB,EAAE5E,IAAI,CAACc,GAAG,CAAC,UAAUkC,CAAC,EAAE;QAC9G,OAAO/D,cAAc,CAAC+D,CAAC,CAAC;MAC1B,CAAC,CAAC,EAAEiC,UAAU,CAACnE,GAAG,CAAC,UAAUkC,CAAC,EAAE;QAC9B,OAAO/D,cAAc,CAAC+D,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC;MACHT,eAAe,CAACvC,IAAI,EAAE,KAAK,CAAC;MAC5BsC,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC;IACD;IACA;IACA,IAAI4C,KAAK;IACT,IAAI5G,aAAa,KAAK,OAAO,EAAE;MAC7B,IAAI6G,mBAAmB;MACvB,IAAIlC,gBAAgB,EAAE;QACpB,IAAImC,IAAI,GAAG;UACT9F,iBAAiB,EAAEA,iBAAiB;UACpC+F,KAAK,EAAEpC,gBAAgB,CAACnC,GAAG,CAAC,UAAUqC,SAAS,EAAEpC,KAAK,EAAE;YACtD,IAAIb,GAAG,GAAGiD,SAAS,CAACjD,GAAG;cACrBkD,IAAI,GAAGD,SAAS,CAACC,IAAI;cACrBkC,gBAAgB,GAAGnC,SAAS,CAACpF,QAAQ;YACvC,OAAO;cACLmC,GAAG,EAAEA,GAAG,IAAIa,KAAK;cACjBwE,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;gBAC1BD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACX,UAAU,CAAC;cAClG,CAAC;cACDa,KAAK,EAAEpC;YACT,CAAC;UACH,CAAC;QACH,CAAC;QACD+B,mBAAmB,GAAG,aAAanJ,KAAK,CAACyJ,aAAa,CAAC,KAAK,EAAE;UAC5DC,SAAS,EAAE,EAAE,CAACtI,MAAM,CAAC2B,SAAS,EAAE,kBAAkB;QACpD,CAAC,EAAE,aAAa/C,KAAK,CAACyJ,aAAa,CAACpJ,QAAQ,EAAE;UAC5C+I,IAAI,EAAEA,IAAI;UACV9F,iBAAiB,EAAEA;QACrB,CAAC,EAAE,aAAatD,KAAK,CAACyJ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAazJ,KAAK,CAACyJ,aAAa,CAAChK,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC3G;MACA,IAAIkK,eAAe,GAAG/E,WAAW,CAACE,GAAG,CAAC,UAAU5D,MAAM,EAAE6D,KAAK,EAAE;QAC7D,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAIC,aAAa,GAAGH,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,OAAO7E,QAAQ,CAAC;UACduK,OAAO,EAAEnC,MAAM,CAACrD,GAAG,CAACF,GAAG;QACzB,CAAC,EAAEc,aAAa,CAAC;MACnB,CAAC,CAAC,CAACsC,MAAM,CAAC,UAAUuC,KAAK,EAAE;QACzB,IAAItE,QAAQ,GAAGsE,KAAK,CAACtE,QAAQ;QAC7B,OAAOA,QAAQ;MACjB,CAAC,CAAC;MACF,IAAIuE,WAAW,GAAG,CAAC,CAACH,eAAe,CAAC1B,MAAM,IAAI0B,eAAe,CAAC1B,MAAM,KAAKrD,WAAW,CAACqD,MAAM;MAC3F,IAAI8B,qBAAqB,GAAGD,WAAW,IAAIH,eAAe,CAACd,KAAK,CAAC,UAAUmB,KAAK,EAAE;QAChF,IAAIJ,OAAO,GAAGI,KAAK,CAACJ,OAAO;QAC3B,OAAOA,OAAO;MAChB,CAAC,CAAC;MACF,IAAIK,sBAAsB,GAAGH,WAAW,IAAIH,eAAe,CAACZ,IAAI,CAAC,UAAUmB,KAAK,EAAE;QAChF,IAAIN,OAAO,GAAGM,KAAK,CAACN,OAAO;QAC3B,OAAOA,OAAO;MAChB,CAAC,CAAC;MACFV,KAAK,GAAG,CAACtG,aAAa,IAAI,aAAa5C,KAAK,CAACyJ,aAAa,CAAC,KAAK,EAAE;QAChEC,SAAS,EAAE,EAAE,CAACtI,MAAM,CAAC2B,SAAS,EAAE,YAAY;MAC9C,CAAC,EAAE,aAAa/C,KAAK,CAACyJ,aAAa,CAACrJ,QAAQ,EAAE;QAC5CwJ,OAAO,EAAE,CAACE,WAAW,GAAG,CAAC,CAAClF,WAAW,CAACqD,MAAM,IAAIW,iBAAiB,GAAGmB,qBAAqB;QACzFI,aAAa,EAAE,CAACL,WAAW,GAAG,CAAClB,iBAAiB,IAAIE,kBAAkB,GAAG,CAACiB,qBAAqB,IAAIE,sBAAsB;QACzHnI,QAAQ,EAAEkH,iBAAiB;QAC3BzD,QAAQ,EAAEX,WAAW,CAACqD,MAAM,KAAK,CAAC,IAAI6B,WAAW;QACjDM,SAAS,EAAE;MACb,CAAC,CAAC,EAAEjB,mBAAmB,CAAC;IAC1B;IACA;IACA,IAAIxG,UAAU;IACd,IAAIL,aAAa,KAAK,OAAO,EAAE;MAC7BK,UAAU,GAAG,SAASA,UAAUA,CAAC0H,CAAC,EAAEnJ,MAAM,EAAE6D,KAAK,EAAE;QACjD,IAAIb,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAI6E,OAAO,GAAGnC,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC;QAC7B,OAAO;UACLoG,IAAI,EAAE,aAAatK,KAAK,CAACyJ,aAAa,CAACnJ,KAAK,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAEwF,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC,EAAE;YACpF0F,OAAO,EAAEA,OAAO;YAChBL,OAAO,EAAE,SAASA,OAAOA,CAACgB,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B,CAAC;YACD1I,QAAQ,EAAE,SAASA,QAAQA,CAACgF,KAAK,EAAE;cACjC,IAAI,CAACW,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC,EAAE;gBACpB0C,sBAAsB,CAAC1C,GAAG,EAAE,IAAI,EAAE,CAACA,GAAG,CAAC,EAAE4C,KAAK,CAAC2D,WAAW,CAAC;cAC7D;YACF;UACF,CAAC,CAAC,CAAC;UACHb,OAAO,EAAEA;QACX,CAAC;MACH,CAAC;IACH,CAAC,MAAM;MACLjH,UAAU,GAAG,SAASA,UAAUA,CAAC0H,CAAC,EAAEnJ,MAAM,EAAE6D,KAAK,EAAE;QACjD,IAAIO,EAAE;QACN,IAAIpB,GAAG,GAAGhB,SAAS,CAAChC,MAAM,EAAE6D,KAAK,CAAC;QAClC,IAAI6E,OAAO,GAAGnC,MAAM,CAACrD,GAAG,CAACF,GAAG,CAAC;QAC7B,IAAIiG,aAAa,GAAGjE,yBAAyB,CAAC9B,GAAG,CAACF,GAAG,CAAC;QACtD,IAAIc,aAAa,GAAGH,gBAAgB,CAACR,GAAG,CAACH,GAAG,CAAC;QAC7C,IAAIwG,mBAAmB;QACvB,IAAIvH,UAAU,KAAK,MAAM,EAAE;UACzBuH,mBAAmB,GAAGP,aAAa;UACnClF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5E,OAAO,CAAC,QAAQyE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACmF,aAAa,CAAC,KAAK,SAAS,EAAE,OAAO,EAAE,2GAA2G,CAAC,GAAG,KAAK,CAAC;QAClS,CAAC,MAAM;UACLO,mBAAmB,GAAG,CAACpF,EAAE,GAAGN,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACmF,aAAa,MAAM,IAAI,IAAI7E,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG6E,aAAa;QACvK;QACA;QACA,OAAO;UACLG,IAAI,EAAE,aAAatK,KAAK,CAACyJ,aAAa,CAACrJ,QAAQ,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAE2F,aAAa,EAAE;YAC3EmF,aAAa,EAAEO,mBAAmB;YAClCd,OAAO,EAAEA,OAAO;YAChBQ,SAAS,EAAE,IAAI;YACfb,OAAO,EAAE,SAASA,OAAOA,CAACgB,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B,CAAC;YACD1I,QAAQ,EAAE,SAASA,QAAQA,CAAC6I,KAAK,EAAE;cACjC,IAAIF,WAAW,GAAGE,KAAK,CAACF,WAAW;cACnC,IAAIG,QAAQ,GAAGH,WAAW,CAACG,QAAQ;cACnC,IAAIC,UAAU,GAAG,CAAC,CAAC;cACnB,IAAIC,QAAQ,GAAG,CAAC,CAAC;cACjB;cACA,IAAIF,QAAQ,IAAI9H,aAAa,EAAE;gBAC7B,IAAIiI,SAAS,GAAG,IAAI9E,GAAG,CAAC,CAACI,eAAe,EAAEnC,GAAG,CAAC,CAAC;gBAC/CyE,UAAU,CAACI,IAAI,CAAC,UAAUiC,SAAS,EAAEC,WAAW,EAAE;kBAChD,IAAIF,SAAS,CAAC3G,GAAG,CAAC4G,SAAS,CAAC,EAAE;oBAC5B,IAAIH,UAAU,KAAK,CAAC,CAAC,EAAE;sBACrBA,UAAU,GAAGI,WAAW;oBAC1B,CAAC,MAAM;sBACLH,QAAQ,GAAGG,WAAW;sBACtB,OAAO,IAAI;oBACb;kBACF;kBACA,OAAO,KAAK;gBACd,CAAC,CAAC;cACJ;cACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAID,UAAU,KAAKC,QAAQ,IAAIhI,aAAa,EAAE;gBAC/D;gBACA,IAAIoI,SAAS,GAAGvC,UAAU,CAAC3C,KAAK,CAAC6E,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC;gBAC1D,IAAIK,WAAW,GAAG,EAAE;gBACpB,IAAIvB,OAAO,EAAE;kBACXsB,SAAS,CAACjK,OAAO,CAAC,UAAU+J,SAAS,EAAE;oBACrC,IAAIvD,MAAM,CAACrD,GAAG,CAAC4G,SAAS,CAAC,EAAE;sBACzBG,WAAW,CAAChK,IAAI,CAAC6J,SAAS,CAAC;sBAC3BvD,MAAM,CAAC,QAAQ,CAAC,CAACuD,SAAS,CAAC;oBAC7B;kBACF,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACLE,SAAS,CAACjK,OAAO,CAAC,UAAU+J,SAAS,EAAE;oBACrC,IAAI,CAACvD,MAAM,CAACrD,GAAG,CAAC4G,SAAS,CAAC,EAAE;sBAC1BG,WAAW,CAAChK,IAAI,CAAC6J,SAAS,CAAC;sBAC3BvD,MAAM,CAACC,GAAG,CAACsD,SAAS,CAAC;oBACvB;kBACF,CAAC,CAAC;gBACJ;gBACA,IAAIhH,IAAI,GAAG2D,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;gBAC7BtF,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC,CAACyH,OAAO,EAAE5F,IAAI,CAACc,GAAG,CAAC,UAAUkG,SAAS,EAAE;kBAC3H,OAAO/H,cAAc,CAAC+H,SAAS,CAAC;gBAClC,CAAC,CAAC,EAAEG,WAAW,CAACrG,GAAG,CAAC,UAAUkG,SAAS,EAAE;kBACvC,OAAO/H,cAAc,CAAC+H,SAAS,CAAC;gBAClC,CAAC,CAAC,CAAC;gBACHzE,eAAe,CAACvC,IAAI,EAAE,UAAU,CAAC;cACnC,CAAC,MAAM;gBACL;gBACA,IAAIoH,iBAAiB,GAAGvF,mBAAmB;gBAC3C,IAAI/C,aAAa,EAAE;kBACjB,IAAI4C,WAAW,GAAGkE,OAAO,GAAGhK,MAAM,CAACwL,iBAAiB,EAAElH,GAAG,CAAC,GAAGvE,MAAM,CAACyL,iBAAiB,EAAElH,GAAG,CAAC;kBAC3F0C,sBAAsB,CAAC1C,GAAG,EAAE,CAAC0F,OAAO,EAAElE,WAAW,EAAE+E,WAAW,CAAC;gBACjE,CAAC,MAAM;kBACL;kBACA,IAAIY,MAAM,GAAGxL,YAAY,CAAC,EAAE,CAACuB,MAAM,CAAC7B,kBAAkB,CAAC6L,iBAAiB,CAAC,EAAE,CAAClH,GAAG,CAAC,CAAC,EAAE,IAAI,EAAEO,WAAW,EAAEW,kBAAkB,CAAC;kBACzH,IAAIkG,YAAY,GAAGD,MAAM,CAAC3F,WAAW;oBACnCC,eAAe,GAAG0F,MAAM,CAAC1F,eAAe;kBAC1C,IAAI4F,eAAe,GAAGD,YAAY;kBAClC;kBACA,IAAI1B,OAAO,EAAE;oBACX,IAAI4B,UAAU,GAAG,IAAIvF,GAAG,CAACqF,YAAY,CAAC;oBACtCE,UAAU,CAAC,QAAQ,CAAC,CAACtH,GAAG,CAAC;oBACzBqH,eAAe,GAAG1L,YAAY,CAAC8H,KAAK,CAACC,IAAI,CAAC4D,UAAU,CAAC,EAAE;sBACrD5B,OAAO,EAAE,KAAK;sBACdjE,eAAe,EAAEA;oBACnB,CAAC,EAAElB,WAAW,EAAEW,kBAAkB,CAAC,CAACM,WAAW;kBACjD;kBACAkB,sBAAsB,CAAC1C,GAAG,EAAE,CAAC0F,OAAO,EAAE2B,eAAe,EAAEd,WAAW,CAAC;gBACrE;cACF;cACA,IAAIb,OAAO,EAAE;gBACXtD,kBAAkB,CAAC,IAAI,CAAC;cAC1B,CAAC,MAAM;gBACLA,kBAAkB,CAACpC,GAAG,CAAC;cACzB;YACF;UACF,CAAC,CAAC,CAAC;UACH0F,OAAO,EAAEA;QACX,CAAC;MACH,CAAC;IACH;IACA,IAAI6B,mBAAmB,GAAG,SAASA,mBAAmBA,CAACpB,CAAC,EAAEnJ,MAAM,EAAE6D,KAAK,EAAE;MACvE,IAAI2G,WAAW,GAAG/I,UAAU,CAAC0H,CAAC,EAAEnJ,MAAM,EAAE6D,KAAK,CAAC;QAC5CuF,IAAI,GAAGoB,WAAW,CAACpB,IAAI;QACvBV,OAAO,GAAG8B,WAAW,CAAC9B,OAAO;MAC/B,IAAIlH,mBAAmB,EAAE;QACvB,OAAOA,mBAAmB,CAACkH,OAAO,EAAE1I,MAAM,EAAE6D,KAAK,EAAEuF,IAAI,CAAC;MAC1D;MACA,OAAOA,IAAI;IACb,CAAC;IACD;IACA,IAAI,CAAC5B,YAAY,CAACF,QAAQ,CAAChI,gBAAgB,CAAC,EAAE;MAC5C;MACA,IAAIkI,YAAY,CAACiD,SAAS,CAAC,UAAUlD,GAAG,EAAE;QACxC,IAAInD,EAAE;QACN,OAAO,CAAC,CAACA,EAAE,GAAGmD,GAAG,CAAC/I,mBAAmB,CAAC,MAAM,IAAI,IAAI4F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsG,UAAU,MAAM,eAAe;MACjH,CAAC,CAAC,KAAK,CAAC,EAAE;QACR,IAAIC,aAAa,GAAGnD,YAAY;UAC9BoD,cAAc,GAAG1M,QAAQ,CAACyM,aAAa,CAAC;UACxCE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;UAChCE,WAAW,GAAGF,cAAc,CAAC9F,KAAK,CAAC,CAAC,CAAC;QACvC0C,YAAY,GAAG,CAACqD,YAAY,EAAEvL,gBAAgB,CAAC,CAACY,MAAM,CAAC7B,kBAAkB,CAACyM,WAAW,CAAC,CAAC;MACzF,CAAC,MAAM;QACL;QACAtD,YAAY,GAAG,CAAClI,gBAAgB,CAAC,CAACY,MAAM,CAAC7B,kBAAkB,CAACmJ,YAAY,CAAC,CAAC;MAC5E;IACF;IACA;IACA,IAAIuD,oBAAoB,GAAGvD,YAAY,CAACwD,OAAO,CAAC1L,gBAAgB,CAAC;IACjEyE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5E,OAAO,CAACmI,YAAY,CAACpB,MAAM,CAAC,UAAUmB,GAAG,EAAE;MACjF,OAAOA,GAAG,KAAKjI,gBAAgB;IACjC,CAAC,CAAC,CAACyH,MAAM,IAAI,CAAC,EAAE,OAAO,EAAE,iDAAiD,CAAC,GAAG,KAAK,CAAC;IACpFS,YAAY,GAAGA,YAAY,CAACpB,MAAM,CAAC,UAAU6E,MAAM,EAAEpH,KAAK,EAAE;MAC1D,OAAOoH,MAAM,KAAK3L,gBAAgB,IAAIuE,KAAK,KAAKkH,oBAAoB;IACtE,CAAC,CAAC;IACF;IACA,IAAIG,OAAO,GAAG1D,YAAY,CAACuD,oBAAoB,GAAG,CAAC,CAAC;IACpD,IAAII,OAAO,GAAG3D,YAAY,CAACuD,oBAAoB,GAAG,CAAC,CAAC;IACpD,IAAIK,WAAW,GAAG7J,KAAK;IACvB,IAAI6J,WAAW,KAAK3F,SAAS,EAAE;MAC7B,IAAI,CAAC0F,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC5J,KAAK,MAAMkE,SAAS,EAAE;QACnF2F,WAAW,GAAGD,OAAO,CAAC5J,KAAK;MAC7B,CAAC,MAAM,IAAI,CAAC2J,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC3J,KAAK,MAAMkE,SAAS,EAAE;QAC1F2F,WAAW,GAAGF,OAAO,CAAC3J,KAAK;MAC7B;IACF;IACA,IAAI6J,WAAW,IAAIF,OAAO,IAAI,CAAC,CAAC9G,EAAE,GAAG8G,OAAO,CAAC1M,mBAAmB,CAAC,MAAM,IAAI,IAAI4F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsG,UAAU,MAAM,eAAe,IAAIQ,OAAO,CAAC3J,KAAK,KAAKkE,SAAS,EAAE;MACzKyF,OAAO,CAAC3J,KAAK,GAAG6J,WAAW;IAC7B;IACA;IACA,IAAIC,eAAe,GAAGpN,eAAe,CAAC;MACpCsD,KAAK,EAAE6J,WAAW;MAClBE,KAAK,EAAEpK,iBAAiB;MACxBsH,SAAS,EAAE,EAAE,CAACtI,MAAM,CAAC2B,SAAS,EAAE,mBAAmB,CAAC;MACpDmG,KAAK,EAAE5H,YAAY,CAACmL,WAAW,IAAIvD,KAAK;MACxCwD,MAAM,EAAEjB;IACV,CAAC,EAAE/L,mBAAmB,EAAE;MACtBgK,SAAS,EAAE,EAAE,CAACtI,MAAM,CAAC2B,SAAS,EAAE,gBAAgB;IAClD,CAAC,CAAC;IACF,OAAO2F,YAAY,CAAC5D,GAAG,CAAC,UAAU2D,GAAG,EAAE;MACrC,OAAOA,GAAG,KAAKjI,gBAAgB,GAAG+L,eAAe,GAAG9D,GAAG;IACzD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvF,SAAS,EAAE0B,WAAW,EAAEtD,YAAY,EAAEuE,mBAAmB,EAAEE,qBAAqB,EAAEG,yBAAyB,EAAE9D,iBAAiB,EAAE6E,gBAAgB,EAAE9D,UAAU,EAAEkD,eAAe,EAAExB,gBAAgB,EAAE1C,gBAAgB,EAAEyE,sBAAsB,EAAExB,kBAAkB,CAAC,CAAC;EACnQ,OAAO,CAACkD,gBAAgB,EAAEvC,qBAAqB,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}