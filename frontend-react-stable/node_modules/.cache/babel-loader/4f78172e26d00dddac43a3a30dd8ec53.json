{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport Alert from '.';\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inherits(ErrorBoundary, _React$Component);\n  var _super = _createSuper(ErrorBoundary);\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error: error,\n        info: info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        message = _this$props.message,\n        description = _this$props.description,\n        children = _this$props.children;\n      var _this$state = this.state,\n        error = _this$state.error,\n        info = _this$state.info;\n      var componentStack = info && info.componentStack ? info.componentStack : null;\n      var errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      var errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", null, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n  return ErrorBoundary;\n}(React.Component);\nexport { ErrorBoundary as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_inherits", "_createSuper", "React", "<PERSON><PERSON>", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_super", "_this", "apply", "arguments", "state", "error", "undefined", "info", "componentStack", "key", "value", "componentDidCatch", "setState", "render", "_this$props", "props", "message", "description", "children", "_this$state", "errorMessage", "toString", "errorDescription", "createElement", "type", "Component", "default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/alert/ErrorBoundary.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport Alert from '.';\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inherits(ErrorBoundary, _React$Component);\n  var _super = _createSuper(ErrorBoundary);\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    _this = _super.apply(this, arguments);\n    _this.state = {\n      error: undefined,\n      info: {\n        componentStack: ''\n      }\n    };\n    return _this;\n  }\n  _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, info) {\n      this.setState({\n        error: error,\n        info: info\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        message = _this$props.message,\n        description = _this$props.description,\n        children = _this$props.children;\n      var _this$state = this.state,\n        error = _this$state.error,\n        info = _this$state.info;\n      var componentStack = info && info.componentStack ? info.componentStack : null;\n      var errorMessage = typeof message === 'undefined' ? (error || '').toString() : message;\n      var errorDescription = typeof description === 'undefined' ? componentStack : description;\n      if (error) {\n        return /*#__PURE__*/React.createElement(Alert, {\n          type: \"error\",\n          message: errorMessage,\n          description: /*#__PURE__*/React.createElement(\"pre\", null, errorDescription)\n        });\n      }\n      return children;\n    }\n  }]);\n  return ErrorBoundary;\n}(React.Component);\nexport { ErrorBoundary as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,KAAK,MAAM,GAAG;AACrB,IAAIC,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DL,SAAS,CAACI,aAAa,EAAEC,gBAAgB,CAAC;EAC1C,IAAIC,MAAM,GAAGL,YAAY,CAACG,aAAa,CAAC;EACxC,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAIG,KAAK;IACTT,eAAe,CAAC,IAAI,EAAEM,aAAa,CAAC;IACpCG,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACrCF,KAAK,CAACG,KAAK,GAAG;MACZC,KAAK,EAAEC,SAAS;MAChBC,IAAI,EAAE;QACJC,cAAc,EAAE;MAClB;IACF,CAAC;IACD,OAAOP,KAAK;EACd;EACAR,YAAY,CAACK,aAAa,EAAE,CAAC;IAC3BW,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,SAASC,iBAAiBA,CAACN,KAAK,EAAEE,IAAI,EAAE;MAC7C,IAAI,CAACK,QAAQ,CAAC;QACZP,KAAK,EAAEA,KAAK;QACZE,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACC,KAAK;QAC1BC,OAAO,GAAGF,WAAW,CAACE,OAAO;QAC7BC,WAAW,GAAGH,WAAW,CAACG,WAAW;QACrCC,QAAQ,GAAGJ,WAAW,CAACI,QAAQ;MACjC,IAAIC,WAAW,GAAG,IAAI,CAACf,KAAK;QAC1BC,KAAK,GAAGc,WAAW,CAACd,KAAK;QACzBE,IAAI,GAAGY,WAAW,CAACZ,IAAI;MACzB,IAAIC,cAAc,GAAGD,IAAI,IAAIA,IAAI,CAACC,cAAc,GAAGD,IAAI,CAACC,cAAc,GAAG,IAAI;MAC7E,IAAIY,YAAY,GAAG,OAAOJ,OAAO,KAAK,WAAW,GAAG,CAACX,KAAK,IAAI,EAAE,EAAEgB,QAAQ,CAAC,CAAC,GAAGL,OAAO;MACtF,IAAIM,gBAAgB,GAAG,OAAOL,WAAW,KAAK,WAAW,GAAGT,cAAc,GAAGS,WAAW;MACxF,IAAIZ,KAAK,EAAE;QACT,OAAO,aAAaT,KAAK,CAAC2B,aAAa,CAAC1B,KAAK,EAAE;UAC7C2B,IAAI,EAAE,OAAO;UACbR,OAAO,EAAEI,YAAY;UACrBH,WAAW,EAAE,aAAarB,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE,IAAI,EAAED,gBAAgB;QAC7E,CAAC,CAAC;MACJ;MACA,OAAOJ,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAOpB,aAAa;AACtB,CAAC,CAACF,KAAK,CAAC6B,SAAS,CAAC;AAClB,SAAS3B,aAAa,IAAI4B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}