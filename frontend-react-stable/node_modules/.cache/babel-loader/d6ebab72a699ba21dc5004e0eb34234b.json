{"ast": null, "code": "var baseIsMap = require('./_baseIsMap'),\n  baseUnary = require('./_baseUnary'),\n  nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsMap = nodeUtil && nodeUtil.isMap;\n\n/**\n * Checks if `value` is classified as a `Map` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n * @example\n *\n * _.isMap(new Map);\n * // => true\n *\n * _.isMap(new WeakMap);\n * // => false\n */\nvar isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;\nmodule.exports = isMap;", "map": {"version": 3, "names": ["baseIsMap", "require", "baseUnary", "nodeUtil", "nodeIsMap", "isMap", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/isMap.js"], "sourcesContent": ["var baseIsMap = require('./_baseIsMap'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsMap = nodeUtil && nodeUtil.isMap;\n\n/**\n * Checks if `value` is classified as a `Map` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n * @example\n *\n * _.isMap(new Map);\n * // => true\n *\n * _.isMap(new WeakMap);\n * // => false\n */\nvar isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;\n\nmodule.exports = isMap;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA,IAAIG,SAAS,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,KAAK;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,KAAK,GAAGD,SAAS,GAAGF,SAAS,CAACE,SAAS,CAAC,GAAGJ,SAAS;AAExDM,MAAM,CAACC,OAAO,GAAGF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}