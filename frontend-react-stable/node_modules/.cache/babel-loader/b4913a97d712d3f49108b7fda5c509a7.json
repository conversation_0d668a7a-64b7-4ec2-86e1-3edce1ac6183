{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Alert, Progress, Typography, Space, Divider, message, Spin } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst DataCleaningPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('local');\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState(null);\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt'));\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false,\n    // 阻止自动上传\n    onChange: info => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: e => {\n      console.log('Dropped files', e.dataTransfer.files);\n    }\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n    try {\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach(file => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir\n        });\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6D41\\u91CF\\u6570\\u636E\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\u6216\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u4E2D\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\uFF0C\\u5206\\u6790\\u540E\\u751F\\u6210CSV\\u6587\\u4EF6\\u5E76\\u4FDD\\u5B58\\u5230\\u6307\\u5B9A\\u76EE\\u5F55\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u5206\\u6790\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6D41\\u91CF\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636ETXT\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u672C\\u5730\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: folderPath,\n                onChange: e => setFolderPath(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/aizhinengqingxicepingdaliu\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchLocalFiles,\n                loading: filesLoading,\n                disabled: !folderPath,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                value: selectedFiles,\n                onChange: setSelectedFiles,\n                placeholder: \"\\u8BF7\\u9009\\u62E9TXT\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301\\u5355\\u4E2A\\u6216\\u6279\\u91CF\\u4E0A\\u4F20TXT\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"CSV\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: outputDir,\n            onChange: e => setOutputDir(e.target.value),\n            placeholder: \"\\u4F8B\\u5982: /data/output\",\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 19\n          }, this),\n          onClick: handleCleanData,\n          loading: loading,\n          disabled: !isFormValid(),\n          className: \"action-button\",\n          children: loading ? '正在处理...' : '执行流量分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u5904\\u7406\\u8FDB\\u5EA6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: progress,\n            status: \"active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5904\\u7406\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u5904\\u7406\\u7ED3\\u679C\\uFF1A\", result.message]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), result.output_file && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u8F93\\u51FA\\u6587\\u4EF6\\uFF1A\", result.output_file]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 42\n            }, this), result.processed_files && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u5904\\u7406\\u7684\\u6587\\u4EF6\\u6570\\uFF1A\", result.processed_files]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 46\n            }, this), result.total_rows && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\u603B\\u884C\\u6570\\uFF1A\", result.total_rows]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(DataCleaningPage, \"tx59Mh08tsySU4MHovFmgBfYpQE=\");\n_c = DataCleaningPage;\nexport default DataCleaningPage;\nvar _c;\n$RefreshReg$(_c, \"DataCleaningPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "Progress", "Typography", "Space", "Divider", "message", "Spin", "InboxOutlined", "PlayCircleOutlined", "dataCleaningAPI", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "DataCleaningPage", "_s", "dataSource", "setDataSource", "uploadedFiles", "setUploadedFiles", "folderPath", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "loading", "setLoading", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "fetchLocalFiles", "response", "listFiles", "files", "data", "txtFiles", "filter", "file", "toLowerCase", "endsWith", "length", "success", "warning", "error", "_error$response", "_error$response$data", "detail", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "console", "log", "dataTransfer", "handleCleanData", "formData", "FormData", "for<PERSON>ach", "append", "originFileObj", "progressInterval", "setInterval", "prev", "clearInterval", "cleanData", "cleanDataLocal", "folder_path", "selected_files", "output_dir", "_error$response2", "_error$response2$data", "isFormValid", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "title", "className", "direction", "size", "width", "strong", "Group", "value", "target", "marginTop", "compact", "display", "placeholder", "flex", "onClick", "disabled", "marginLeft", "spinning", "mode", "map", "icon", "percent", "status", "description", "output_file", "processed_files", "total_rows", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地目录文件</Radio>\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n          {/* 结果展示 */}\n          {result && (\n            <Alert\n              message=\"处理完成\"\n              description={\n                <div>\n                  <p>处理结果：{result.message}</p>\n                  {result.output_file && <p>输出文件：{result.output_file}</p>}\n                  {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                  {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,eAAe,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGX,UAAU;AAClC,MAAM;EAAEY;AAAQ,CAAC,GAAGlB,MAAM;AAC1B,MAAM;EAAEmB;AAAO,CAAC,GAAGjB,MAAM;AAEzB,MAAMkB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAM,IAAI,CAAC;;EAE/C;EACA,MAAM8C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAChB,UAAU,EAAE;IAEjBW,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM9B,eAAe,CAAC+B,SAAS,CAAClB,UAAU,CAAC;MAC5D,MAAMmB,KAAK,GAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,IAAI,EAAE;MACvChB,iBAAiB,CAACgB,KAAK,CAAC;;MAExB;MACA,MAAME,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAAEC,IAAY,IACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC;MAED,IAAIJ,QAAQ,CAACK,MAAM,GAAG,CAAC,EAAE;QACvBrB,gBAAgB,CAACgB,QAAQ,CAAC;QAC1BtC,OAAO,CAAC4C,OAAO,CAAC,SAASN,QAAQ,CAACK,MAAM,SAAS,CAAC;MACpD,CAAC,MAAM;QACLrB,gBAAgB,CAAC,EAAE,CAAC;QACpB,IAAIc,KAAK,CAACO,MAAM,GAAG,CAAC,EAAE;UACpB3C,OAAO,CAAC6C,OAAO,CAAC,cAAc,CAAC;QACjC;MACF;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBhD,OAAO,CAAC8C,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACZ,QAAQ,cAAAa,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzD7B,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRM,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIyB,UAAU,KAAK,OAAO,IAAII,UAAU,IAAIA,UAAU,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAAE;MACnE,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMmB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACrC,UAAU,EAAEI,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMoC,WAAW,GAAG;IAClBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IAAE;IAC3BC,QAAQ,EAAGC,IAAS,IAAK;MACvB3C,gBAAgB,CAAC2C,IAAI,CAACC,QAAQ,CAAC;IACjC,CAAC;IACDC,MAAM,EAAGC,CAAM,IAAK;MAClBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,CAAC,CAACG,YAAY,CAAC7B,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC;IACA,IAAIrD,UAAU,KAAK,QAAQ,IAAIE,aAAa,CAAC4B,MAAM,KAAK,CAAC,EAAE;MACzD3C,OAAO,CAAC8C,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAIjC,UAAU,KAAK,OAAO,KAAK,CAACI,UAAU,IAAII,aAAa,CAACsB,MAAM,KAAK,CAAC,CAAC,EAAE;MACzE3C,OAAO,CAAC8C,KAAK,CAAC,mBAAmB,CAAC;MAClC;IACF;IAEApB,UAAU,CAAC,IAAI,CAAC;IAChBI,WAAW,CAAC,CAAC,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,IAAIE,QAAQ;MAEZ,IAAIrB,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMsD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BrD,aAAa,CAACsD,OAAO,CAAE7B,IAAI,IAAK;UAC9B2B,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAE9B,IAAI,CAAC+B,aAAa,CAAC;QAC9C,CAAC,CAAC;QACFJ,QAAQ,CAACG,MAAM,CAAC,YAAY,EAAE/C,SAAS,CAAC;;QAExC;QACA,MAAMiD,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC3C,WAAW,CAAE4C,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdC,aAAa,CAACH,gBAAgB,CAAC;cAC/B,OAAOE,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPxC,QAAQ,GAAG,MAAM9B,eAAe,CAACwE,SAAS,CAACT,QAAQ,CAAC;QACpDQ,aAAa,CAACH,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMA,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC3C,WAAW,CAAE4C,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdC,aAAa,CAACH,gBAAgB,CAAC;cAC/B,OAAOE,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPxC,QAAQ,GAAG,MAAM9B,eAAe,CAACyE,cAAc,CAAC;UAC9CC,WAAW,EAAE7D,UAAU;UACvB8D,cAAc,EAAE1D,aAAa;UAC7B2D,UAAU,EAAEzD;QACd,CAAC,CAAC;QACFoD,aAAa,CAACH,gBAAgB,CAAC;MACjC;MAEA1C,WAAW,CAAC,GAAG,CAAC;MAChBE,SAAS,CAACE,QAAQ,CAACG,IAAI,CAAC;MACxBrC,OAAO,CAAC4C,OAAO,CAAC,SAAS,CAAC;IAE5B,CAAC,CAAC,OAAOE,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnBlF,OAAO,CAAC8C,KAAK,CAAC,EAAAmC,gBAAA,GAAAnC,KAAK,CAACZ,QAAQ,cAAA+C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5C,IAAI,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBjC,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyD,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAItE,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOE,aAAa,CAAC4B,MAAM,GAAG,CAAC;IACjC,CAAC,MAAM;MACL,OAAO1B,UAAU,IAAII,aAAa,CAACsB,MAAM,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,oBACErC,OAAA;IAAA8E,QAAA,gBACE9E,OAAA,CAACC,KAAK;MAAC8E,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAL,QAAA,EAAC;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClGvF,OAAA,CAACE,IAAI;MAACsF,IAAI,EAAC,WAAW;MAAAV,QAAA,EAAC;IAEvB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPvF,OAAA,CAACP,OAAO;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXvF,OAAA,CAACjB,IAAI;MAAC0G,KAAK,EAAC,0BAAM;MAACC,SAAS,EAAC,eAAe;MAAAZ,QAAA,eAC1C9E,OAAA,CAACR,KAAK;QAACmG,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACZ,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAEhE9E,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACE,IAAI;YAAC4F,MAAM;YAAAhB,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5BvF,OAAA,CAAChB,KAAK,CAAC+G,KAAK;YACVC,KAAK,EAAEzF,UAAW;YAClB6C,QAAQ,EAAGI,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;YAC/ChB,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAApB,QAAA,gBAExB9E,OAAA,CAAChB,KAAK;cAACgH,KAAK,EAAC,OAAO;cAAAlB,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCvF,OAAA,CAAChB,KAAK;cAACgH,KAAK,EAAC,QAAQ;cAAAlB,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLhF,UAAU,KAAK,OAAO,iBACrBP,OAAA,CAACR,KAAK;UAACmG,SAAS,EAAC,UAAU;UAACX,KAAK,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAf,QAAA,gBACnD9E,OAAA;YAAA8E,QAAA,gBACE9E,OAAA,CAACE,IAAI;cAAC4F,MAAM;cAAAhB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3BvF,OAAA,CAACd,KAAK,CAAC6G,KAAK;cAACI,OAAO;cAACnB,KAAK,EAAE;gBAAEkB,SAAS,EAAE,CAAC;gBAAEE,OAAO,EAAE;cAAO,CAAE;cAAAtB,QAAA,gBAC5D9E,OAAA,CAACd,KAAK;gBACJ8G,KAAK,EAAErF,UAAW;gBAClByC,QAAQ,EAAGI,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;gBAC/CK,WAAW,EAAC,gDAAsC;gBAClDrB,KAAK,EAAE;kBAAEsB,IAAI,EAAE;gBAAE;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFvF,OAAA,CAACZ,MAAM;gBACLoG,IAAI,EAAC,SAAS;gBACde,OAAO,EAAE5E,eAAgB;gBACzBR,OAAO,EAAEE,YAAa;gBACtBmF,QAAQ,EAAE,CAAC7F,UAAW;gBACtBqE,KAAK,EAAE;kBAAEyB,UAAU,EAAE;gBAAE,CAAE;gBAAA3B,QAAA,EAC1B;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENvF,OAAA;YAAA8E,QAAA,gBACE9E,OAAA,CAACE,IAAI;cAAC4F,MAAM;cAAAhB,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBvF,OAAA,CAACL,IAAI;cAAC+G,QAAQ,EAAErF,YAAa;cAAAyD,QAAA,eAC3B9E,OAAA,CAACb,MAAM;gBACLwH,IAAI,EAAC,UAAU;gBACfX,KAAK,EAAEjF,aAAc;gBACrBqC,QAAQ,EAAEpC,gBAAiB;gBAC3BqF,WAAW,EAAC,mCAAU;gBACtBrB,KAAK,EAAE;kBAAEa,KAAK,EAAE,MAAM;kBAAEK,SAAS,EAAE;gBAAE,CAAE;gBACvC/E,OAAO,EAAEE,YAAa;gBAAAyD,QAAA,EAErBjE,cAAc,CAAC+F,GAAG,CAAE1E,IAAI,iBACvBlC,OAAA,CAACI,MAAM;kBAAY4F,KAAK,EAAE9D,IAAK;kBAAA4C,QAAA,EAC5B5C;gBAAI,GADMA,IAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGAhF,UAAU,KAAK,QAAQ,iBACtBP,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACE,IAAI;YAAC4F,MAAM;YAAAhB,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvF,OAAA,CAACG,OAAO;YAAA,GAAK4C,WAAW;YAAEiC,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAApB,QAAA,gBAChD9E,OAAA;cAAG0F,SAAS,EAAC,sBAAsB;cAAAZ,QAAA,eACjC9E,OAAA,CAACJ,aAAa;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJvF,OAAA;cAAG0F,SAAS,EAAC,iBAAiB;cAAAZ,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChDvF,OAAA;cAAG0F,SAAS,EAAC,iBAAiB;cAAAZ,QAAA,EAAC;YAE/B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,eAGDvF,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACE,IAAI;YAAC4F,MAAM;YAAAhB,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5BvF,OAAA,CAACd,KAAK;YACJ8G,KAAK,EAAE/E,SAAU;YACjBmC,QAAQ,EAAGI,CAAC,IAAKtC,YAAY,CAACsC,CAAC,CAACyC,MAAM,CAACD,KAAK,CAAE;YAC9CK,WAAW,EAAC,4BAAkB;YAC9BrB,KAAK,EAAE;cAAEkB,SAAS,EAAE;YAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNvF,OAAA,CAACZ,MAAM;UACLoG,IAAI,EAAC,SAAS;UACdI,IAAI,EAAC,OAAO;UACZiB,IAAI,eAAE7G,OAAA,CAACH,kBAAkB;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BgB,OAAO,EAAE3C,eAAgB;UACzBzC,OAAO,EAAEA,OAAQ;UACjBqF,QAAQ,EAAE,CAAC3B,WAAW,CAAC,CAAE;UACzBa,SAAS,EAAC,eAAe;UAAAZ,QAAA,EAExB3D,OAAO,GAAG,SAAS,GAAG;QAAQ;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAGRpE,OAAO,iBACNnB,OAAA;UAAK0F,SAAS,EAAC,kBAAkB;UAAAZ,QAAA,gBAC/B9E,OAAA,CAACE,IAAI;YAAA4E,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClBvF,OAAA,CAACV,QAAQ;YAACwH,OAAO,EAAEvF,QAAS;YAACwF,MAAM,EAAC;UAAQ;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN,EAGA9D,MAAM,iBACLzB,OAAA,CAACX,KAAK;UACJK,OAAO,EAAC,0BAAM;UACdsH,WAAW,eACThH,OAAA;YAAA8E,QAAA,gBACE9E,OAAA;cAAA8E,QAAA,GAAG,gCAAK,EAACrD,MAAM,CAAC/B,OAAO;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC3B9D,MAAM,CAACwF,WAAW,iBAAIjH,OAAA;cAAA8E,QAAA,GAAG,gCAAK,EAACrD,MAAM,CAACwF,WAAW;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtD9D,MAAM,CAACyF,eAAe,iBAAIlH,OAAA;cAAA8E,QAAA,GAAG,4CAAO,EAACrD,MAAM,CAACyF,eAAe;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChE9D,MAAM,CAAC0F,UAAU,iBAAInH,OAAA;cAAA8E,QAAA,GAAG,0BAAI,EAACrD,MAAM,CAAC0F,UAAU;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN;UACDC,IAAI,EAAC,SAAS;UACd4B,QAAQ;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjF,EAAA,CAhSID,gBAA0B;AAAAgH,EAAA,GAA1BhH,gBAA0B;AAkShC,eAAeA,gBAAgB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}