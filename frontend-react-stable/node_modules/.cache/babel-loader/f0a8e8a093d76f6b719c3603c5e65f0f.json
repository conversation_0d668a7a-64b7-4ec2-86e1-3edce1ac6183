{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Tag from '../tag';\nexport default function PickerTag(props) {\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    color: \"blue\"\n  }, props));\n}", "map": {"version": 3, "names": ["_extends", "React", "Tag", "PickerTag", "props", "createElement", "color"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/date-picker/PickerTag.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Tag from '../tag';\nexport default function PickerTag(props) {\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    color: \"blue\"\n  }, props));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,QAAQ;AACxB,eAAe,SAASC,SAASA,CAACC,KAAK,EAAE;EACvC,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,GAAG,EAAEF,QAAQ,CAAC;IACpDM,KAAK,EAAE;EACT,CAAC,EAAEF,KAAK,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}