{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Row,Col,Statistic,List,Button,Space,Typography,Tag,Progress,Modal,Empty,message,Spin,Tooltip,Popconfirm}from'antd';import{SyncOutlined,CheckCircleOutlined,CloseCircleOutlined,StopOutlined,ClockCircleOutlined,EyeOutlined,DeleteOutlined,ReloadOutlined,PlayCircleOutlined,MinusCircleOutlined}from'@ant-design/icons';import useTaskManager from'../hooks/useTaskManager';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const TaskManagerPage=()=>{var _selectedTask$result$;const{runningTasks,completedTasks,loading,fetchRunningTasks,fetchCompletedTasks,cancelTask,deleteSingleTask,clearCompletedTasks,formatTaskType,TASK_STATUS}=useTaskManager();const[selectedTask,setSelectedTask]=useState(null);const[taskDetailVisible,setTaskDetailVisible]=useState(false);const[refreshing,setRefreshing]=useState(false);// 页面加载时获取任务数据\nuseEffect(()=>{fetchRunningTasks();fetchCompletedTasks();},[fetchRunningTasks,fetchCompletedTasks]);// 自动刷新运行中的任务\nuseEffect(()=>{const interval=setInterval(()=>{fetchRunningTasks(false);// 静默刷新\n},5000);// 每5秒刷新一次\nreturn()=>clearInterval(interval);},[fetchRunningTasks]);// 手动刷新\nconst handleRefresh=async()=>{setRefreshing(true);try{await Promise.all([fetchRunningTasks(),fetchCompletedTasks()]);message.success('刷新成功');}catch(error){message.error('刷新失败');}finally{setRefreshing(false);}};// 删除单个任务\nconst handleDeleteSingleTask=taskId=>{Modal.confirm({title:'确认删除',content:`确定要删除任务 ${taskId.substring(0,8)}... 吗？`,icon:/*#__PURE__*/_jsx(MinusCircleOutlined,{style:{color:'#ff4d4f'}}),okText:'删除',okType:'danger',cancelText:'取消',onOk:async()=>{const success=await deleteSingleTask(taskId);if(success){// 刷新任务列表\nawait Promise.all([fetchRunningTasks(),fetchCompletedTasks()]);}}});};// 查看任务详情\nconst handleViewTaskDetail=task=>{setSelectedTask(task);setTaskDetailVisible(true);};// 取消任务\nconst handleCancelTask=async taskId=>{try{await cancelTask(taskId);await fetchRunningTasks();// 刷新运行中任务列表\n}catch(error){console.error('取消任务失败:',error);}};// 清空所有已完成任务\nconst handleClearCompleted=()=>{const completedCount=completedTasks.length;if(completedCount===0){message.info('没有已完成的任务需要清空');return;}Modal.confirm({title:'确认清空所有已完成任务',content:`确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,icon:/*#__PURE__*/_jsx(DeleteOutlined,{style:{color:'#ff4d4f'}}),okText:'清空',okType:'danger',cancelText:'取消',onOk:async()=>{try{await clearCompletedTasks();// 刷新任务列表\nawait Promise.all([fetchRunningTasks(),fetchCompletedTasks()]);}catch(error){console.error('清空任务失败:',error);}}});};// 获取任务状态图标\nconst getTaskStatusIcon=status=>{switch(status){case TASK_STATUS.RUNNING:return/*#__PURE__*/_jsx(SyncOutlined,{spin:true,style:{color:'#1890ff'}});case TASK_STATUS.COMPLETED:return/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'#52c41a'}});case TASK_STATUS.FAILED:return/*#__PURE__*/_jsx(CloseCircleOutlined,{style:{color:'#ff4d4f'}});case TASK_STATUS.CANCELLED:return/*#__PURE__*/_jsx(StopOutlined,{style:{color:'#faad14'}});default:return/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{color:'#d9d9d9'}});}};// 获取任务状态标签\nconst getTaskStatusTag=status=>{switch(status){case TASK_STATUS.RUNNING:return/*#__PURE__*/_jsx(Tag,{color:\"processing\",icon:/*#__PURE__*/_jsx(SyncOutlined,{spin:true}),children:\"\\u8FD0\\u884C\\u4E2D\"});case TASK_STATUS.COMPLETED:return/*#__PURE__*/_jsx(Tag,{color:\"success\",icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),children:\"\\u5DF2\\u5B8C\\u6210\"});case TASK_STATUS.FAILED:return/*#__PURE__*/_jsx(Tag,{color:\"error\",icon:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),children:\"\\u5931\\u8D25\"});case TASK_STATUS.CANCELLED:return/*#__PURE__*/_jsx(Tag,{color:\"warning\",icon:/*#__PURE__*/_jsx(StopOutlined,{}),children:\"\\u5DF2\\u53D6\\u6D88\"});default:return/*#__PURE__*/_jsx(Tag,{color:\"default\",icon:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),children:\"\\u7B49\\u5F85\\u4E2D\"});}};// 格式化时间\nconst formatTime=timeString=>{if(!timeString)return'未知';return new Date(timeString).toLocaleString('zh-CN');};return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:2,children:\"\\u4EFB\\u52A1\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"})]}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:handleRefresh,loading:refreshing,children:\"\\u5237\\u65B0\"})]}),/*#__PURE__*/_jsx(Card,{style:{marginBottom:'24px'},children:/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",value:runningTasks.length,prefix:/*#__PURE__*/_jsx(SyncOutlined,{spin:true}),valueStyle:{color:'#1890ff'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",value:completedTasks.filter(t=>t.status===TASK_STATUS.COMPLETED).length,prefix:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),valueStyle:{color:'#52c41a'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5931\\u8D25\\u4EFB\\u52A1\",value:completedTasks.filter(t=>t.status===TASK_STATUS.FAILED).length,prefix:/*#__PURE__*/_jsx(CloseCircleOutlined,{}),valueStyle:{color:'#ff4d4f'}})}),/*#__PURE__*/_jsx(Col,{span:6,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u4EFB\\u52A1\\u6570\",value:runningTasks.length+completedTasks.length,prefix:/*#__PURE__*/_jsx(PlayCircleOutlined,{})})})]})}),/*#__PURE__*/_jsxs(Row,{gutter:[24,24],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(SyncOutlined,{spin:true}),\"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",/*#__PURE__*/_jsx(Tag,{color:\"processing\",children:runningTasks.length})]}),extra:/*#__PURE__*/_jsx(Tooltip,{title:\"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",children:/*#__PURE__*/_jsx(SyncOutlined,{spin:true,style:{color:'#1890ff'}})}),children:/*#__PURE__*/_jsx(Spin,{spinning:loading,children:/*#__PURE__*/_jsx(List,{dataSource:runningTasks,locale:{emptyText:/*#__PURE__*/_jsx(Empty,{description:\"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"})},renderItem:task=>/*#__PURE__*/_jsx(List.Item,{actions:[/*#__PURE__*/_jsx(Button,{type:\"link\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewTaskDetail(task),children:\"\\u8BE6\\u60C5\"}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",onConfirm:()=>handleCancelTask(task.task_id),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{type:\"link\",danger:true,icon:/*#__PURE__*/_jsx(StopOutlined,{}),children:\"\\u53D6\\u6D88\"})})],children:/*#__PURE__*/_jsx(List.Item.Meta,{avatar:getTaskStatusIcon(task.status),title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:formatTaskType(task.task_type)}),getTaskStatusTag(task.status)]}),description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",copyable:{text:task.task_id},children:[\"ID: \",task.task_id.includes('_')?`${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0,8)}`:`${task.task_id.substring(0,8)}...`]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u5F00\\u59CB\\u65F6\\u95F4: \",formatTime(task.created_at)]}),task.message&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u72B6\\u6001: \",task.message]})]}),task.progress!==undefined&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Progress,{percent:task.progress,size:\"small\"})})]})})})})})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(CheckCircleOutlined,{}),\"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",/*#__PURE__*/_jsx(Tag,{color:\"success\",children:completedTasks.length})]}),extra:/*#__PURE__*/_jsx(Button,{type:\"primary\",danger:true,size:\"small\",icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),disabled:completedTasks.length===0,onClick:handleClearCompleted,children:\"\\u6E05\\u7A7A\\u5168\\u90E8\"}),children:/*#__PURE__*/_jsx(List,{dataSource:completedTasks,locale:{emptyText:/*#__PURE__*/_jsx(Empty,{description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{children:\"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"})]})})},renderItem:task=>/*#__PURE__*/_jsx(List.Item,{actions:[/*#__PURE__*/_jsx(Button,{type:\"link\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>handleViewTaskDetail(task),children:\"\\u8BE6\\u60C5\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",danger:true,icon:/*#__PURE__*/_jsx(MinusCircleOutlined,{}),onClick:()=>handleDeleteSingleTask(task.task_id),children:\"\\u5220\\u9664\"})],children:/*#__PURE__*/_jsx(List.Item.Meta,{avatar:getTaskStatusIcon(task.status),title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:formatTaskType(task.task_type)}),getTaskStatusTag(task.status)]}),description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",copyable:{text:task.task_id},children:[\"ID: \",task.task_id.includes('_')?`${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0,8)}`:`${task.task_id.substring(0,8)}...`]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u5B8C\\u6210\\u65F6\\u95F4: \",formatTime(task.updated_at)]}),task.status===TASK_STATUS.FAILED&&task.error&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"danger\",children:[\"\\u9519\\u8BEF: \",task.error]})]})]})})})})})})]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u4EFB\\u52A1\\u8BE6\\u60C5\",open:taskDetailVisible,onCancel:()=>setTaskDetailVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setTaskDetailVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedTask&&/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EFB\\u52A1ID:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{copyable:true,children:selectedTask.task_id})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EFB\\u52A1\\u7C7B\\u578B:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:formatTaskType(selectedTask.task_type)})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(\"br\",{}),getTaskStatusTag(selectedTask.status)]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8FDB\\u5EA6:\"}),/*#__PURE__*/_jsx(\"br\",{}),selectedTask.status===TASK_STATUS.COMPLETED?/*#__PURE__*/_jsx(Progress,{percent:100,size:\"small\",status:\"success\"}):selectedTask.status===TASK_STATUS.FAILED?/*#__PURE__*/_jsx(Progress,{percent:selectedTask.progress||0,size:\"small\",status:\"exception\"}):selectedTask.status===TASK_STATUS.CANCELLED?/*#__PURE__*/_jsx(Progress,{percent:selectedTask.progress||0,size:\"small\",status:\"exception\"}):selectedTask.progress!==undefined?/*#__PURE__*/_jsx(Progress,{percent:selectedTask.progress,size:\"small\"}):/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u521B\\u5EFA\\u65F6\\u95F4:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:formatTime(selectedTask.created_at)})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u66F4\\u65B0\\u65F6\\u95F4:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:formatTime(selectedTask.updated_at)})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6267\\u884C\\u65F6\\u957F:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:(_selectedTask$result=>{// 如果任务已完成、失败或取消，且有开始时间和完成时间\nif(selectedTask.started_at&&selectedTask.completed_at){const duration=Math.round((new Date(selectedTask.completed_at).getTime()-new Date(selectedTask.started_at).getTime())/1000);return`${duration}秒`;}// 如果任务正在运行，且有开始时间\nelse if(selectedTask.started_at&&selectedTask.status===TASK_STATUS.RUNNING){const duration=Math.round((new Date().getTime()-new Date(selectedTask.started_at).getTime())/1000);return`${duration}秒 (进行中)`;}// 如果任务已完成但没有开始时间，尝试从结果中获取时长\nelse if(selectedTask.status===TASK_STATUS.COMPLETED&&(_selectedTask$result=selectedTask.result)!==null&&_selectedTask$result!==void 0&&_selectedTask$result.duration_seconds){return`${Math.round(selectedTask.result.duration_seconds)}秒`;}// 如果任务已完成但没有时间信息，显示已完成\nelse if(selectedTask.status===TASK_STATUS.COMPLETED){return'已完成';}// 如果任务失败或取消，显示相应状态\nelse if(selectedTask.status===TASK_STATUS.FAILED){return'执行失败';}else if(selectedTask.status===TASK_STATUS.CANCELLED){return'已取消';}// 其他情况显示等待开始\nelse{return'等待开始';}})()})]}),selectedTask.current_step&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u5F53\\u524D\\u6B65\\u9AA4:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:selectedTask.current_step})]}),selectedTask.message&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6D88\\u606F:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{children:selectedTask.message})]}),selectedTask.error&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9519\\u8BEF\\u4FE1\\u606F:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Text,{type:\"danger\",children:selectedTask.error})]}),selectedTask.params&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4EFB\\u52A1\\u53C2\\u6570:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"div\",{style:{background:'#f5f5f5',padding:12,borderRadius:4,fontSize:12,maxHeight:200,overflow:'auto',marginTop:8},children:/*#__PURE__*/_jsx(\"pre\",{children:JSON.stringify(selectedTask.params,null,2)})})]}),selectedTask.result&&selectedTask.task_type==='training'&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BAD\\u7EC3\\u5B8C\\u6210:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Space,{wrap:true,style:{marginTop:8},children:[/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),children:\"\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\"}),selectedTask.result.duration_seconds&&/*#__PURE__*/_jsxs(Tag,{color:\"blue\",children:[\"\\u8017\\u65F6: \",Math.round(selectedTask.result.duration_seconds),\"\\u79D2\"]}),selectedTask.result.results&&/*#__PURE__*/_jsxs(Tag,{color:\"purple\",children:[\"\\u6A21\\u578B\\u6570\\u91CF: \",Object.keys(selectedTask.result.results).length]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\uD83D\\uDCA1 \\u8BAD\\u7EC3\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u6A21\\u578B\\u8BAD\\u7EC3\\\"\\u9875\\u9762\\u67E5\\u770B\"})})]}),selectedTask.result&&selectedTask.task_type==='prediction'&&/*#__PURE__*/_jsxs(Col,{span:24,children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u5B8C\\u6210:\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Space,{wrap:true,style:{marginTop:8},children:[/*#__PURE__*/_jsx(Tag,{color:\"green\",icon:/*#__PURE__*/_jsx(CheckCircleOutlined,{}),children:\"\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\"}),selectedTask.result.anomaly_count!==undefined&&/*#__PURE__*/_jsxs(Tag,{color:\"red\",children:[\"\\u5F02\\u5E38\\u6570\\u91CF: \",selectedTask.result.anomaly_count]}),((_selectedTask$result$=selectedTask.result.predictions)===null||_selectedTask$result$===void 0?void 0:_selectedTask$result$.length)&&/*#__PURE__*/_jsxs(Tag,{color:\"blue\",children:[\"\\u9884\\u6D4B\\u70B9\\u6570: \",selectedTask.result.predictions.length]}),selectedTask.result.duration_seconds&&/*#__PURE__*/_jsxs(Tag,{color:\"orange\",children:[\"\\u8017\\u65F6: \",Math.round(selectedTask.result.duration_seconds),\"\\u79D2\"]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\uD83D\\uDCA1 \\u9884\\u6D4B\\u7ED3\\u679C\\u8BE6\\u60C5\\u8BF7\\u524D\\u5F80\\\"\\u5F02\\u5E38\\u68C0\\u6D4B\\\"\\u9875\\u9762\\u67E5\\u770B\"})})]})]})})})]});};export default TaskManagerPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "MinusCircleOutlined", "useTaskManager", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_selectedTask$result$", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "deleteSingleTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleDeleteSingleTask", "taskId", "confirm", "title", "content", "substring", "icon", "style", "color", "okText", "okType", "cancelText", "onOk", "handleViewTaskDetail", "task", "handleCancelTask", "console", "handleClearCompleted", "completedCount", "length", "info", "getTaskStatusIcon", "status", "RUNNING", "spin", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "children", "formatTime", "timeString", "Date", "toLocaleString", "padding", "marginBottom", "display", "justifyContent", "alignItems", "level", "type", "onClick", "gutter", "span", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "includes", "split", "slice", "created_at", "progress", "undefined", "marginTop", "percent", "size", "disabled", "fontSize", "updated_at", "open", "onCancel", "footer", "width", "_selectedTask$result", "started_at", "completed_at", "duration", "Math", "round", "getTime", "result", "duration_seconds", "current_step", "params", "background", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "wrap", "results", "Object", "keys", "anomaly_count", "predictions"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>任务管理</Title>\n          <Text type=\"secondary\">\n            查看和管理异步训练、预测任务的状态和结果\n          </Text>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.status === TASK_STATUS.COMPLETED ? (\n                  <Progress percent={100} size=\"small\" status=\"success\" />\n                ) : selectedTask.status === TASK_STATUS.FAILED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>执行时长:</Text>\n                <br />\n                <Text>\n                  {(() => {\n                    // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                    if (selectedTask.started_at && selectedTask.completed_at) {\n                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒`;\n                    }\n                    // 如果任务正在运行，且有开始时间\n                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒 (进行中)`;\n                    }\n                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {\n                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                    }\n                    // 如果任务已完成但没有时间信息，显示已完成\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                      return '已完成';\n                    }\n                    // 如果任务失败或取消，显示相应状态\n                    else if (selectedTask.status === TASK_STATUS.FAILED) {\n                      return '执行失败';\n                    }\n                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                      return '已取消';\n                    }\n                    // 其他情况显示等待开始\n                    else {\n                      return '等待开始';\n                    }\n                  })()}\n                </Text>\n              </Col>\n              {selectedTask.current_step && (\n                <Col span={24}>\n                  <Text strong>当前步骤:</Text>\n                  <br />\n                  <Text>{selectedTask.current_step}</Text>\n                </Col>\n              )}\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.params && (\n                <Col span={24}>\n                  <Text strong>任务参数:</Text>\n                  <br />\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 12,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 200,\n                    overflow: 'auto',\n                    marginTop: 8\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'training' && (\n                <Col span={24}>\n                  <Text strong>训练完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      训练已完成\n                    </Tag>\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"blue\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                    {selectedTask.result.results && (\n                      <Tag color=\"purple\">\n                        模型数量: {Object.keys(selectedTask.result.results).length}\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 训练结果详情请前往\"模型训练\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'prediction' && (\n                <Col span={24}>\n                  <Text strong>预测完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      预测已完成\n                    </Tag>\n                    {selectedTask.result.anomaly_count !== undefined && (\n                      <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                    )}\n                    {selectedTask.result.predictions?.length && (\n                      <Tag color=\"blue\">预测点数: {selectedTask.result.predictions.length}</Tag>\n                    )}\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 预测结果详情请前往\"异常检测\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,IAAI,CACJC,MAAM,CACNC,KAAK,CACLC,UAAU,CACVC,GAAG,CACHC,QAAQ,CACRC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,IAAI,CACJC,OAAO,CACPC,UAAU,KACL,MAAM,CACb,OACEC,YAAY,CACZC,mBAAmB,CACnBC,mBAAmB,CACnBC,YAAY,CACZC,mBAAmB,CACnBC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,kBAAkB,CAClBC,mBAAmB,KACd,mBAAmB,CAC1B,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGrD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG3B,UAAU,CAElC,KAAM,CAAA4B,eAAyB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACtC,KAAM,CACJC,YAAY,CACZC,cAAc,CACdC,OAAO,CACPC,iBAAiB,CACjBC,mBAAmB,CACnBC,UAAU,CACVC,gBAAgB,CAChBC,mBAAmB,CACnBC,cAAc,CACdC,WACF,CAAC,CAAGpB,cAAc,CAAC,CAAC,CAEpB,KAAM,CAACqB,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAACmD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACqD,UAAU,CAAEC,aAAa,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACAC,SAAS,CAAC,IAAM,CACdyC,iBAAiB,CAAC,CAAC,CACnBC,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACD,iBAAiB,CAAEC,mBAAmB,CAAC,CAAC,CAE5C;AACA1C,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsD,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCd,iBAAiB,CAAC,KAAK,CAAC,CAAE;AAC5B,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMe,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACb,iBAAiB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAgB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCJ,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,KAAM,CAAAK,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,CACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC,CACF7B,OAAO,CAAC+C,OAAO,CAAC,MAAM,CAAC,CACzB,CAAE,MAAOC,KAAK,CAAE,CACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC,CACvB,CAAC,OAAS,CACRR,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED;AACA,KAAM,CAAAS,sBAAsB,CAAIC,MAAc,EAAK,CACjDpD,KAAK,CAACqD,OAAO,CAAC,CACZC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,WAAWH,MAAM,CAACI,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,QAAQ,CAClDC,IAAI,cAAEvC,IAAA,CAACH,mBAAmB,EAAC2C,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC1DC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,QAAQ,CAChBC,UAAU,CAAE,IAAI,CAChBC,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,KAAM,CAAAd,OAAO,CAAG,KAAM,CAAAhB,gBAAgB,CAACmB,MAAM,CAAC,CAC9C,GAAIH,OAAO,CAAE,CACX;AACA,KAAM,CAAAF,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,CACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC,CACJ,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAiC,oBAAoB,CAAIC,IAAU,EAAK,CAC3C3B,eAAe,CAAC2B,IAAI,CAAC,CACrBzB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA0B,gBAAgB,CAAG,KAAO,CAAAd,MAAc,EAAK,CACjD,GAAI,CACF,KAAM,CAAApB,UAAU,CAACoB,MAAM,CAAC,CACxB,KAAM,CAAAtB,iBAAiB,CAAC,CAAC,CAAE;AAC7B,CAAE,MAAOoB,KAAK,CAAE,CACdiB,OAAO,CAACjB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAkB,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,cAAc,CAAGzC,cAAc,CAAC0C,MAAM,CAC5C,GAAID,cAAc,GAAK,CAAC,CAAE,CACxBnE,OAAO,CAACqE,IAAI,CAAC,cAAc,CAAC,CAC5B,OACF,CAEAvE,KAAK,CAACqD,OAAO,CAAC,CACZC,KAAK,CAAE,aAAa,CACpBC,OAAO,CAAE,WAAWc,cAAc,oBAAoB,CACtDZ,IAAI,cAAEvC,IAAA,CAACN,cAAc,EAAC8C,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACrDC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,QAAQ,CAChBC,UAAU,CAAE,IAAI,CAChBC,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CACF,KAAM,CAAA7B,mBAAmB,CAAC,CAAC,CAC3B;AACA,KAAM,CAAAa,OAAO,CAACC,GAAG,CAAC,CAChBlB,iBAAiB,CAAC,CAAC,CACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC,CACJ,CAAE,MAAOmB,KAAK,CAAE,CACdiB,OAAO,CAACjB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAsB,iBAAiB,CAAIC,MAAc,EAAK,CAC5C,OAAQA,MAAM,EACZ,IAAK,CAAArC,WAAW,CAACsC,OAAO,CACtB,mBAAOxD,IAAA,CAACZ,YAAY,EAACqE,IAAI,MAACjB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC3D,IAAK,CAAAvB,WAAW,CAACwC,SAAS,CACxB,mBAAO1D,IAAA,CAACX,mBAAmB,EAACmD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC7D,IAAK,CAAAvB,WAAW,CAACyC,MAAM,CACrB,mBAAO3D,IAAA,CAACV,mBAAmB,EAACkD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC7D,IAAK,CAAAvB,WAAW,CAAC0C,SAAS,CACxB,mBAAO5D,IAAA,CAACT,YAAY,EAACiD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACtD,QACE,mBAAOzC,IAAA,CAACR,mBAAmB,EAACgD,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/D,CACF,CAAC,CAED;AACA,KAAM,CAAAoB,gBAAgB,CAAIN,MAAc,EAAK,CAC3C,OAAQA,MAAM,EACZ,IAAK,CAAArC,WAAW,CAACsC,OAAO,CACtB,mBAAOxD,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,YAAY,CAACF,IAAI,cAAEvC,IAAA,CAACZ,YAAY,EAACqE,IAAI,MAAE,CAAE,CAAAK,QAAA,CAAC,oBAAG,CAAK,CAAC,CACvE,IAAK,CAAA5C,WAAW,CAACwC,SAAS,CACxB,mBAAO1D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,SAAS,CAACF,IAAI,cAAEvC,IAAA,CAACX,mBAAmB,GAAE,CAAE,CAAAyE,QAAA,CAAC,oBAAG,CAAK,CAAC,CACtE,IAAK,CAAA5C,WAAW,CAACyC,MAAM,CACrB,mBAAO3D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,OAAO,CAACF,IAAI,cAAEvC,IAAA,CAACV,mBAAmB,GAAE,CAAE,CAAAwE,QAAA,CAAC,cAAE,CAAK,CAAC,CACnE,IAAK,CAAA5C,WAAW,CAAC0C,SAAS,CACxB,mBAAO5D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,SAAS,CAACF,IAAI,cAAEvC,IAAA,CAACT,YAAY,GAAE,CAAE,CAAAuE,QAAA,CAAC,oBAAG,CAAK,CAAC,CAC/D,QACE,mBAAO9D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,SAAS,CAACF,IAAI,cAAEvC,IAAA,CAACR,mBAAmB,GAAE,CAAE,CAAAsE,QAAA,CAAC,oBAAG,CAAK,CAAC,CACxE,CACF,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAIC,UAAmB,EAAK,CAC1C,GAAI,CAACA,UAAU,CAAE,MAAO,IAAI,CAC5B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,CACrD,CAAC,CAED,mBACEhE,KAAA,QAAKsC,KAAK,CAAE,CAAE2B,OAAO,CAAE,MAAO,CAAE,CAAAL,QAAA,eAC9B5D,KAAA,QAAKsC,KAAK,CAAE,CAAE4B,YAAY,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAT,QAAA,eAC3G5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,CAACK,KAAK,EAACmE,KAAK,CAAE,CAAE,CAAAV,QAAA,CAAC,0BAAI,CAAO,CAAC,cAC7B9D,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,0HAEvB,CAAM,CAAC,EACJ,CAAC,cACN9D,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,SAAS,CACdlC,IAAI,cAAEvC,IAAA,CAACL,cAAc,GAAE,CAAE,CACzB+E,OAAO,CAAE9C,aAAc,CACvBjB,OAAO,CAAEY,UAAW,CAAAuC,QAAA,CACrB,cAED,CAAQ,CAAC,EACN,CAAC,cAGN9D,IAAA,CAAC5B,IAAI,EAACoE,KAAK,CAAE,CAAE4B,YAAY,CAAE,MAAO,CAAE,CAAAN,QAAA,cACpC5D,KAAA,CAAC7B,GAAG,EAACsG,MAAM,CAAE,EAAG,CAAAb,QAAA,eACd9D,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,CAAE,CAAAd,QAAA,cACX9D,IAAA,CAACzB,SAAS,EACR6D,KAAK,CAAC,gCAAO,CACbyC,KAAK,CAAEpE,YAAY,CAAC2C,MAAO,CAC3B0B,MAAM,cAAE9E,IAAA,CAACZ,YAAY,EAACqE,IAAI,MAAE,CAAE,CAC9BsB,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNzC,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,CAAE,CAAAd,QAAA,cACX9D,IAAA,CAACzB,SAAS,EACR6D,KAAK,CAAC,gCAAO,CACbyC,KAAK,CAAEnE,cAAc,CAACsE,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1B,MAAM,GAAKrC,WAAW,CAACwC,SAAS,CAAC,CAACN,MAAO,CAC7E0B,MAAM,cAAE9E,IAAA,CAACX,mBAAmB,GAAE,CAAE,CAChC0F,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNzC,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,CAAE,CAAAd,QAAA,cACX9D,IAAA,CAACzB,SAAS,EACR6D,KAAK,CAAC,0BAAM,CACZyC,KAAK,CAAEnE,cAAc,CAACsE,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1B,MAAM,GAAKrC,WAAW,CAACyC,MAAM,CAAC,CAACP,MAAO,CAC1E0B,MAAM,cAAE9E,IAAA,CAACV,mBAAmB,GAAE,CAAE,CAChCyF,UAAU,CAAE,CAAEtC,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNzC,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,CAAE,CAAAd,QAAA,cACX9D,IAAA,CAACzB,SAAS,EACR6D,KAAK,CAAC,0BAAM,CACZyC,KAAK,CAAEpE,YAAY,CAAC2C,MAAM,CAAG1C,cAAc,CAAC0C,MAAO,CACnD0B,MAAM,cAAE9E,IAAA,CAACJ,kBAAkB,GAAE,CAAE,CAChC,CAAC,CACC,CAAC,EACH,CAAC,CACF,CAAC,cAEPM,KAAA,CAAC7B,GAAG,EAACsG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAb,QAAA,eAEpB9D,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,cACZ9D,IAAA,CAAC5B,IAAI,EACHgE,KAAK,cACHlC,KAAA,CAACxB,KAAK,EAAAoF,QAAA,eACJ9D,IAAA,CAACZ,YAAY,EAACqE,IAAI,MAAE,CAAC,iCAErB,cAAAzD,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,YAAY,CAAAqB,QAAA,CAAErD,YAAY,CAAC2C,MAAM,CAAM,CAAC,EAC9C,CACR,CACD8B,KAAK,cACHlF,IAAA,CAACd,OAAO,EAACkD,KAAK,CAAC,gCAAO,CAAA0B,QAAA,cACpB9D,IAAA,CAACZ,YAAY,EAACqE,IAAI,MAACjB,KAAK,CAAE,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC3C,CACV,CAAAqB,QAAA,cAED9D,IAAA,CAACf,IAAI,EAACkG,QAAQ,CAAExE,OAAQ,CAAAmD,QAAA,cACtB9D,IAAA,CAACxB,IAAI,EACH4G,UAAU,CAAE3E,YAAa,CACzB4E,MAAM,CAAE,CAAEC,SAAS,cAAEtF,IAAA,CAACjB,KAAK,EAACwG,WAAW,CAAC,kDAAU,CAAE,CAAE,CAAE,CACxDC,UAAU,CAAGzC,IAAI,eACf/C,IAAA,CAACxB,IAAI,CAACiH,IAAI,EACRC,OAAO,CAAE,cACP1F,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,MAAM,CACXlC,IAAI,cAAEvC,IAAA,CAACP,WAAW,GAAE,CAAE,CACtBiF,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAACC,IAAI,CAAE,CAAAe,QAAA,CAC3C,cAED,CAAQ,CAAC,cACT9D,IAAA,CAACb,UAAU,EACTiD,KAAK,CAAC,oEAAa,CACnBuD,SAAS,CAAEA,CAAA,GAAM3C,gBAAgB,CAACD,IAAI,CAAC6C,OAAO,CAAE,CAChDlD,MAAM,CAAC,cAAI,CACXE,UAAU,CAAC,cAAI,CAAAkB,QAAA,cAEf9D,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,MAAM,CACXoB,MAAM,MACNtD,IAAI,cAAEvC,IAAA,CAACT,YAAY,GAAE,CAAE,CAAAuE,QAAA,CACxB,cAED,CAAQ,CAAC,CACC,CAAC,CACb,CAAAA,QAAA,cAEF9D,IAAA,CAACxB,IAAI,CAACiH,IAAI,CAACK,IAAI,EACbC,MAAM,CAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE,CACvCnB,KAAK,cACHlC,KAAA,CAACxB,KAAK,EAAAoF,QAAA,eACJ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAE7C,cAAc,CAAC8B,IAAI,CAACkD,SAAS,CAAC,CAAO,CAAC,CACnDpC,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC,EACzB,CACR,CACDgC,WAAW,cACTrF,KAAA,QAAA4D,QAAA,eACE5D,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,WAAW,CAACyB,QAAQ,CAAE,CAAEC,IAAI,CAAEpD,IAAI,CAAC6C,OAAQ,CAAE,CAAA9B,QAAA,EAAC,MACnD,CAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAChE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE,CACzF,GAAGS,IAAI,CAAC6C,OAAO,CAACtD,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,KAAK,EAElC,CAAC,cACPtC,IAAA,QAAK,CAAC,cACNE,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,EAAC,4BAAM,CAACC,UAAU,CAAChB,IAAI,CAACwD,UAAU,CAAC,EAAO,CAAC,CAChExD,IAAI,CAAC/D,OAAO,eACXkB,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE9D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,EAAC,gBAAI,CAACf,IAAI,CAAC/D,OAAO,EAAO,CAAC,EAChD,CACH,CACA+D,IAAI,CAACyD,QAAQ,GAAKC,SAAS,eAC1BzG,IAAA,QAAKwC,KAAK,CAAE,CAAEkE,SAAS,CAAE,CAAE,CAAE,CAAA5C,QAAA,cAC3B9D,IAAA,CAACnB,QAAQ,EAAC8H,OAAO,CAAE5D,IAAI,CAACyD,QAAS,CAACI,IAAI,CAAC,OAAO,CAAE,CAAC,CAC9C,CACN,EACE,CACN,CACF,CAAC,CACO,CACX,CACH,CAAC,CACE,CAAC,CACH,CAAC,CACJ,CAAC,cAGN5G,IAAA,CAAC1B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,cACZ9D,IAAA,CAAC5B,IAAI,EACHgE,KAAK,cACHlC,KAAA,CAACxB,KAAK,EAAAoF,QAAA,eACJ9D,IAAA,CAACX,mBAAmB,GAAE,CAAC,iCAEvB,cAAAW,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,SAAS,CAAAqB,QAAA,CAAEpD,cAAc,CAAC0C,MAAM,CAAM,CAAC,EAC7C,CACR,CACD8B,KAAK,cACHlF,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,SAAS,CACdoB,MAAM,MACNe,IAAI,CAAC,OAAO,CACZrE,IAAI,cAAEvC,IAAA,CAACN,cAAc,GAAE,CAAE,CACzBmH,QAAQ,CAAEnG,cAAc,CAAC0C,MAAM,GAAK,CAAE,CACtCsB,OAAO,CAAExB,oBAAqB,CAAAY,QAAA,CAC/B,0BAED,CAAQ,CACT,CAAAA,QAAA,cAED9D,IAAA,CAACxB,IAAI,EACH4G,UAAU,CAAE1E,cAAe,CAC3B2E,MAAM,CAAE,CACNC,SAAS,cACPtF,IAAA,CAACjB,KAAK,EACJwG,WAAW,cACTrF,KAAA,QAAA4D,QAAA,eACE9D,IAAA,QAAA8D,QAAA,CAAK,kDAAQ,CAAK,CAAC,cACnB9D,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,WAAW,CAACjC,KAAK,CAAE,CAAEsE,QAAQ,CAAE,MAAO,CAAE,CAAAhD,QAAA,CAAC,oHAEpD,CAAM,CAAC,EACJ,CACN,CACF,CAEL,CAAE,CACF0B,UAAU,CAAGzC,IAAI,eACf/C,IAAA,CAACxB,IAAI,CAACiH,IAAI,EACRC,OAAO,CAAE,cACP1F,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,MAAM,CACXlC,IAAI,cAAEvC,IAAA,CAACP,WAAW,GAAE,CAAE,CACtBiF,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAACC,IAAI,CAAE,CAAAe,QAAA,CAC3C,cAED,CAAQ,CAAC,cACT9D,IAAA,CAACvB,MAAM,EACLgG,IAAI,CAAC,MAAM,CACXoB,MAAM,MACNtD,IAAI,cAAEvC,IAAA,CAACH,mBAAmB,GAAE,CAAE,CAC9B6E,OAAO,CAAEA,CAAA,GAAMzC,sBAAsB,CAACc,IAAI,CAAC6C,OAAO,CAAE,CAAA9B,QAAA,CACrD,cAED,CAAQ,CAAC,CACT,CAAAA,QAAA,cAEF9D,IAAA,CAACxB,IAAI,CAACiH,IAAI,CAACK,IAAI,EACbC,MAAM,CAAEzC,iBAAiB,CAACP,IAAI,CAACQ,MAAM,CAAE,CACvCnB,KAAK,cACHlC,KAAA,CAACxB,KAAK,EAAAoF,QAAA,eACJ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAE7C,cAAc,CAAC8B,IAAI,CAACkD,SAAS,CAAC,CAAO,CAAC,CACnDpC,gBAAgB,CAACd,IAAI,CAACQ,MAAM,CAAC,EACzB,CACR,CACDgC,WAAW,cACTrF,KAAA,QAAA4D,QAAA,eACE5D,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,WAAW,CAACyB,QAAQ,CAAE,CAAEC,IAAI,CAAEpD,IAAI,CAAC6C,OAAQ,CAAE,CAAA9B,QAAA,EAAC,MACnD,CAACf,IAAI,CAAC6C,OAAO,CAACQ,QAAQ,CAAC,GAAG,CAAC,CAC7B,GAAGrD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMtD,IAAI,CAAC6C,OAAO,CAACS,KAAK,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAChE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE,CACzF,GAAGS,IAAI,CAAC6C,OAAO,CAACtD,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,KAAK,EAElC,CAAC,cACPtC,IAAA,QAAK,CAAC,cACNE,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,EAAC,4BAAM,CAACC,UAAU,CAAChB,IAAI,CAACgE,UAAU,CAAC,EAAO,CAAC,CAChEhE,IAAI,CAACQ,MAAM,GAAKrC,WAAW,CAACyC,MAAM,EAAIZ,IAAI,CAACf,KAAK,eAC/C9B,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE9D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACI,IAAI,EAACmE,IAAI,CAAC,QAAQ,CAAAX,QAAA,EAAC,gBAAI,CAACf,IAAI,CAACf,KAAK,EAAO,CAAC,EAC3C,CACH,EACE,CACN,CACF,CAAC,CACO,CACX,CACH,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGNhC,IAAA,CAAClB,KAAK,EACJsD,KAAK,CAAC,0BAAM,CACZ4E,IAAI,CAAE3F,iBAAkB,CACxB4F,QAAQ,CAAEA,CAAA,GAAM3F,oBAAoB,CAAC,KAAK,CAAE,CAC5C4F,MAAM,CAAE,cACNlH,IAAA,CAACvB,MAAM,EAAaiG,OAAO,CAAEA,CAAA,GAAMpD,oBAAoB,CAAC,KAAK,CAAE,CAAAwC,QAAA,CAAC,cAEhE,EAFY,OAEJ,CAAC,CACT,CACFqD,KAAK,CAAE,GAAI,CAAArD,QAAA,CAEV3C,YAAY,eACXnB,IAAA,QAAA8D,QAAA,cACE5D,KAAA,CAAC7B,GAAG,EAACsG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAb,QAAA,eACpB5D,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,iBAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAC4F,QAAQ,MAAApC,QAAA,CAAE3C,YAAY,CAACyE,OAAO,CAAO,CAAC,EACzC,CAAC,cACN1F,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CAAE7C,cAAc,CAACE,YAAY,CAAC8E,SAAS,CAAC,CAAO,CAAC,EAClD,CAAC,cACN/F,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,eAAG,CAAM,CAAC,cACvB9D,IAAA,QAAK,CAAC,CACL6D,gBAAgB,CAAC1C,YAAY,CAACoC,MAAM,CAAC,EACnC,CAAC,cACNrD,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,eAAG,CAAM,CAAC,cACvB9D,IAAA,QAAK,CAAC,CACLmB,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACwC,SAAS,cAC5C1D,IAAA,CAACnB,QAAQ,EAAC8H,OAAO,CAAE,GAAI,CAACC,IAAI,CAAC,OAAO,CAACrD,MAAM,CAAC,SAAS,CAAE,CAAC,CACtDpC,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACyC,MAAM,cAC5C3D,IAAA,CAACnB,QAAQ,EAAC8H,OAAO,CAAExF,YAAY,CAACqF,QAAQ,EAAI,CAAE,CAACI,IAAI,CAAC,OAAO,CAACrD,MAAM,CAAC,WAAW,CAAE,CAAC,CAC/EpC,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAAC0C,SAAS,cAC/C5D,IAAA,CAACnB,QAAQ,EAAC8H,OAAO,CAAExF,YAAY,CAACqF,QAAQ,EAAI,CAAE,CAACI,IAAI,CAAC,OAAO,CAACrD,MAAM,CAAC,WAAW,CAAE,CAAC,CAC/EpC,YAAY,CAACqF,QAAQ,GAAKC,SAAS,cACrCzG,IAAA,CAACnB,QAAQ,EAAC8H,OAAO,CAAExF,YAAY,CAACqF,QAAS,CAACI,IAAI,CAAC,OAAO,CAAE,CAAC,cAEzD5G,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,gCAAK,CAAM,CACnC,EACE,CAAC,cACN5D,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CAAEC,UAAU,CAAC5C,YAAY,CAACoF,UAAU,CAAC,CAAO,CAAC,EAC/C,CAAC,cACNrG,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CAAEC,UAAU,CAAC5C,YAAY,CAAC4F,UAAU,CAAC,CAAO,CAAC,EAC/C,CAAC,cACN7G,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CACF,CAACsD,oBAAA,EAAM,CACN;AACA,GAAIjG,YAAY,CAACkG,UAAU,EAAIlG,YAAY,CAACmG,YAAY,CAAE,CACxD,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,GAAI,CAAAxD,IAAI,CAAC9C,YAAY,CAACmG,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAzD,IAAI,CAAC9C,YAAY,CAACkG,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CACjI,MAAO,GAAGH,QAAQ,GAAG,CACvB,CACA;AAAA,IACK,IAAIpG,YAAY,CAACkG,UAAU,EAAIlG,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACsC,OAAO,CAAE,CAC/E,KAAM,CAAA+D,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAAC,GAAI,CAAAxD,IAAI,CAAC,CAAC,CAACyD,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAzD,IAAI,CAAC9C,YAAY,CAACkG,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CACxG,MAAO,GAAGH,QAAQ,SAAS,CAC7B,CACA;AAAA,IACK,IAAIpG,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACwC,SAAS,GAAA0D,oBAAA,CAAIjG,YAAY,CAACwG,MAAM,UAAAP,oBAAA,WAAnBA,oBAAA,CAAqBQ,gBAAgB,CAAE,CAC/F,MAAO,GAAGJ,IAAI,CAACC,KAAK,CAACtG,YAAY,CAACwG,MAAM,CAACC,gBAAgB,CAAC,GAAG,CAC/D,CACA;AAAA,IACK,IAAIzG,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACwC,SAAS,CAAE,CACtD,MAAO,KAAK,CACd,CACA;AAAA,IACK,IAAIvC,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAACyC,MAAM,CAAE,CACnD,MAAO,MAAM,CACf,CAAC,IACI,IAAIxC,YAAY,CAACoC,MAAM,GAAKrC,WAAW,CAAC0C,SAAS,CAAE,CACtD,MAAO,KAAK,CACd,CACA;AAAA,IACK,CACH,MAAO,MAAM,CACf,CACF,CAAC,EAAE,CAAC,CACA,CAAC,EACJ,CAAC,CACLzC,YAAY,CAAC0G,YAAY,eACxB3H,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CAAE3C,YAAY,CAAC0G,YAAY,CAAO,CAAC,EACrC,CACN,CACA1G,YAAY,CAACnC,OAAO,eACnBkB,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,eAAG,CAAM,CAAC,cACvB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAAAwD,QAAA,CAAE3C,YAAY,CAACnC,OAAO,CAAO,CAAC,EAChC,CACN,CACAmC,YAAY,CAACa,KAAK,eACjB9B,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,QAAQ,CAAAX,QAAA,CAAE3C,YAAY,CAACa,KAAK,CAAO,CAAC,EAC5C,CACN,CACAb,YAAY,CAAC2G,MAAM,eAClB5H,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNA,IAAA,QAAKwC,KAAK,CAAE,CACVuF,UAAU,CAAE,SAAS,CACrB5D,OAAO,CAAE,EAAE,CACX6D,YAAY,CAAE,CAAC,CACflB,QAAQ,CAAE,EAAE,CACZmB,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,MAAM,CAChBxB,SAAS,CAAE,CACb,CAAE,CAAA5C,QAAA,cACA9D,IAAA,QAAA8D,QAAA,CAAMqE,IAAI,CAACC,SAAS,CAACjH,YAAY,CAAC2G,MAAM,CAAE,IAAI,CAAE,CAAC,CAAC,CAAM,CAAC,CACtD,CAAC,EACH,CACN,CACA3G,YAAY,CAACwG,MAAM,EAAIxG,YAAY,CAAC8E,SAAS,GAAK,UAAU,eAC3D/F,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACxB,KAAK,EAAC2J,IAAI,MAAC7F,KAAK,CAAE,CAAEkE,SAAS,CAAE,CAAE,CAAE,CAAA5C,QAAA,eAClC9D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,OAAO,CAACF,IAAI,cAAEvC,IAAA,CAACX,mBAAmB,GAAE,CAAE,CAAAyE,QAAA,CAAC,gCAElD,CAAK,CAAC,CACL3C,YAAY,CAACwG,MAAM,CAACC,gBAAgB,eACnC1H,KAAA,CAACtB,GAAG,EAAC6D,KAAK,CAAC,MAAM,CAAAqB,QAAA,EAAC,gBACZ,CAAC0D,IAAI,CAACC,KAAK,CAACtG,YAAY,CAACwG,MAAM,CAACC,gBAAgB,CAAC,CAAC,QACxD,EAAK,CACN,CACAzG,YAAY,CAACwG,MAAM,CAACW,OAAO,eAC1BpI,KAAA,CAACtB,GAAG,EAAC6D,KAAK,CAAC,QAAQ,CAAAqB,QAAA,EAAC,4BACZ,CAACyE,MAAM,CAACC,IAAI,CAACrH,YAAY,CAACwG,MAAM,CAACW,OAAO,CAAC,CAAClF,MAAM,EACnD,CACN,EACI,CAAC,cACRpD,IAAA,QAAKwC,KAAK,CAAE,CAAEkE,SAAS,CAAE,CAAE,CAAE,CAAA5C,QAAA,cAC3B9D,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,yHAEvB,CAAM,CAAC,CACJ,CAAC,EACH,CACN,CACA3C,YAAY,CAACwG,MAAM,EAAIxG,YAAY,CAAC8E,SAAS,GAAK,YAAY,eAC7D/F,KAAA,CAAC5B,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAd,QAAA,eACZ9D,IAAA,CAACM,IAAI,EAAC0F,MAAM,MAAAlC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzB9D,IAAA,QAAK,CAAC,cACNE,KAAA,CAACxB,KAAK,EAAC2J,IAAI,MAAC7F,KAAK,CAAE,CAAEkE,SAAS,CAAE,CAAE,CAAE,CAAA5C,QAAA,eAClC9D,IAAA,CAACpB,GAAG,EAAC6D,KAAK,CAAC,OAAO,CAACF,IAAI,cAAEvC,IAAA,CAACX,mBAAmB,GAAE,CAAE,CAAAyE,QAAA,CAAC,gCAElD,CAAK,CAAC,CACL3C,YAAY,CAACwG,MAAM,CAACc,aAAa,GAAKhC,SAAS,eAC9CvG,KAAA,CAACtB,GAAG,EAAC6D,KAAK,CAAC,KAAK,CAAAqB,QAAA,EAAC,4BAAM,CAAC3C,YAAY,CAACwG,MAAM,CAACc,aAAa,EAAM,CAChE,CACA,EAAAjI,qBAAA,CAAAW,YAAY,CAACwG,MAAM,CAACe,WAAW,UAAAlI,qBAAA,iBAA/BA,qBAAA,CAAiC4C,MAAM,gBACtClD,KAAA,CAACtB,GAAG,EAAC6D,KAAK,CAAC,MAAM,CAAAqB,QAAA,EAAC,4BAAM,CAAC3C,YAAY,CAACwG,MAAM,CAACe,WAAW,CAACtF,MAAM,EAAM,CACtE,CACAjC,YAAY,CAACwG,MAAM,CAACC,gBAAgB,eACnC1H,KAAA,CAACtB,GAAG,EAAC6D,KAAK,CAAC,QAAQ,CAAAqB,QAAA,EAAC,gBACd,CAAC0D,IAAI,CAACC,KAAK,CAACtG,YAAY,CAACwG,MAAM,CAACC,gBAAgB,CAAC,CAAC,QACxD,EAAK,CACN,EACI,CAAC,cACR5H,IAAA,QAAKwC,KAAK,CAAE,CAAEkE,SAAS,CAAE,CAAE,CAAE,CAAA5C,QAAA,cAC3B9D,IAAA,CAACM,IAAI,EAACmE,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,yHAEvB,CAAM,CAAC,CACJ,CAAC,EACH,CACN,EACE,CAAC,CACH,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}