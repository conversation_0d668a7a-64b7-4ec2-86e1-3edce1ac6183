{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PropertySafetyFilledSvg from \"@ant-design/icons-svg/es/asn/PropertySafetyFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar PropertySafetyFilled = function PropertySafetyFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PropertySafetyFilledSvg\n  }));\n};\nPropertySafetyFilled.displayName = 'PropertySafetyFilled';\nexport default /*#__PURE__*/React.forwardRef(PropertySafetyFilled);", "map": {"version": 3, "names": ["_objectSpread", "React", "PropertySafetyFilledSvg", "AntdIcon", "PropertySafetyFilled", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/PropertySafetyFilled.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PropertySafetyFilledSvg from \"@ant-design/icons-svg/es/asn/PropertySafetyFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar PropertySafetyFilled = function PropertySafetyFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PropertySafetyFilledSvg\n  }));\n};\nPropertySafetyFilled.displayName = 'PropertySafetyFilled';\nexport default /*#__PURE__*/React.forwardRef(PropertySafetyFilled);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,uBAAuB,MAAM,mDAAmD;AACvF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,oBAAoB,CAACK,WAAW,GAAG,sBAAsB;AACzD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}