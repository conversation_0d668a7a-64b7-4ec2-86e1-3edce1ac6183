{"ast": null, "code": "export function supportBigInt() {\n  return typeof BigInt === 'function';\n}", "map": {"version": 3, "names": ["supportBigInt", "BigInt"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-input-number/es/utils/supportUtil.js"], "sourcesContent": ["export function supportBigInt() {\n  return typeof BigInt === 'function';\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAAA,EAAG;EAC9B,OAAO,OAAOC,MAAM,KAAK,UAAU;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}