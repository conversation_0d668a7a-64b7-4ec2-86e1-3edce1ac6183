{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PieChartTwoToneSvg from \"@ant-design/icons-svg/es/asn/PieChartTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar PieChartTwoTone = function PieChartTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PieChartTwoToneSvg\n  }));\n};\nPieChartTwoTone.displayName = 'PieChartTwoTone';\nexport default /*#__PURE__*/React.forwardRef(PieChartTwoTone);", "map": {"version": 3, "names": ["_objectSpread", "React", "PieChartTwoToneSvg", "AntdIcon", "PieChartTwoTone", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/PieChartTwoTone.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PieChartTwoToneSvg from \"@ant-design/icons-svg/es/asn/PieChartTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar PieChartTwoTone = function PieChartTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PieChartTwoToneSvg\n  }));\n};\nPieChartTwoTone.displayName = 'PieChartTwoTone';\nexport default /*#__PURE__*/React.forwardRef(PieChartTwoTone);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,eAAe,CAACK,WAAW,GAAG,iBAAiB;AAC/C,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}