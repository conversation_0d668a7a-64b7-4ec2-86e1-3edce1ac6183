{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport TrademarkCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/TrademarkCircleTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar TrademarkCircleTwoTone = function TrademarkCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: TrademarkCircleTwoToneSvg\n  }));\n};\nTrademarkCircleTwoTone.displayName = 'TrademarkCircleTwoTone';\nexport default /*#__PURE__*/React.forwardRef(TrademarkCircleTwoTone);", "map": {"version": 3, "names": ["_objectSpread", "React", "TrademarkCircleTwoToneSvg", "AntdIcon", "TrademarkCircleTwoTone", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/TrademarkCircleTwoTone.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport TrademarkCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/TrademarkCircleTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar TrademarkCircleTwoTone = function TrademarkCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: TrademarkCircleTwoToneSvg\n  }));\n};\nTrademarkCircleTwoTone.displayName = 'TrademarkCircleTwoTone';\nexport default /*#__PURE__*/React.forwardRef(TrademarkCircleTwoTone);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,sBAAsB,CAACK,WAAW,GAAG,wBAAwB;AAC7D,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}