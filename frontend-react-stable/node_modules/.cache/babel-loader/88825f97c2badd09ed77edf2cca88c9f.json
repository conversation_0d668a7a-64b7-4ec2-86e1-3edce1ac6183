{"ast": null, "code": "var createPadding = require('./_createPadding'),\n  stringSize = require('./_stringSize'),\n  toInteger = require('./toInteger'),\n  toString = require('./toString');\n\n/**\n * Pads `string` on the right side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padEnd('abc', 6);\n * // => 'abc   '\n *\n * _.padEnd('abc', 6, '_-');\n * // => 'abc_-_'\n *\n * _.padEnd('abc', 3);\n * // => 'abc'\n */\nfunction padEnd(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n  var strLength = length ? stringSize(string) : 0;\n  return length && strLength < length ? string + createPadding(length - strLength, chars) : string;\n}\nmodule.exports = padEnd;", "map": {"version": 3, "names": ["createPadding", "require", "stringSize", "toInteger", "toString", "padEnd", "string", "length", "chars", "str<PERSON><PERSON><PERSON>", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/padEnd.js"], "sourcesContent": ["var createPadding = require('./_createPadding'),\n    stringSize = require('./_stringSize'),\n    toInteger = require('./toInteger'),\n    toString = require('./toString');\n\n/**\n * Pads `string` on the right side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padEnd('abc', 6);\n * // => 'abc   '\n *\n * _.padEnd('abc', 6, '_-');\n * // => 'abc_-_'\n *\n * _.padEnd('abc', 3);\n * // => 'abc'\n */\nfunction padEnd(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n\n  var strLength = length ? stringSize(string) : 0;\n  return (length && strLength < length)\n    ? (string + createPadding(length - strLength, chars))\n    : string;\n}\n\nmodule.exports = padEnd;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EAC3CC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;EAClCG,QAAQ,GAAGH,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACrCF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzBC,MAAM,GAAGJ,SAAS,CAACI,MAAM,CAAC;EAE1B,IAAIE,SAAS,GAAGF,MAAM,GAAGL,UAAU,CAACI,MAAM,CAAC,GAAG,CAAC;EAC/C,OAAQC,MAAM,IAAIE,SAAS,GAAGF,MAAM,GAC/BD,MAAM,GAAGN,aAAa,CAACO,MAAM,GAAGE,SAAS,EAAED,KAAK,CAAC,GAClDF,MAAM;AACZ;AAEAI,MAAM,CAACC,OAAO,GAAGN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script"}