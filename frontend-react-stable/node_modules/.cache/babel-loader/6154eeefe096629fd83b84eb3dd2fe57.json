{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom';import{Provider}from'react-redux';import{BrowserRouter}from'react-router-dom';import{ConfigProvider}from'antd';import zhCN from'antd/lib/locale/zh_CN';import'antd/dist/antd.css';// 导入Ant Design样式\nimport{store}from'./store/store';import App from'./App';import'./index.css';import{jsx as _jsx}from\"react/jsx-runtime\";ReactDOM.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(Provider,{store:store,children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(ConfigProvider,{locale:zhCN,children:/*#__PURE__*/_jsx(App,{})})})})}),document.getElementById('root'));", "map": {"version": 3, "names": ["React", "ReactDOM", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "store", "App", "jsx", "_jsx", "render", "StrictMode", "children", "locale", "document", "getElementById"], "sources": ["/home/<USER>/frontend-react-stable/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport 'antd/dist/antd.css'; // 导入Ant Design样式\nimport { store } from './store/store';\nimport App from './App';\nimport './index.css';\n\nReactDOM.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <BrowserRouter>\n        <ConfigProvider locale={zhCN}>\n          <App />\n        </ConfigProvider>\n      </BrowserRouter>\n    </Provider>\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,WAAW,CAChC,OAASC,QAAQ,KAAQ,aAAa,CACtC,OAASC,aAAa,KAAQ,kBAAkB,CAChD,OAASC,cAAc,KAAQ,MAAM,CACrC,MAAO,CAAAC,IAAI,KAAM,uBAAuB,CACxC,MAAO,oBAAoB,CAAE;AAC7B,OAASC,KAAK,KAAQ,eAAe,CACrC,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAErBR,QAAQ,CAACS,MAAM,cACbD,IAAA,CAACT,KAAK,CAACW,UAAU,EAAAC,QAAA,cACfH,IAAA,CAACP,QAAQ,EAACI,KAAK,CAAEA,KAAM,CAAAM,QAAA,cACrBH,IAAA,CAACN,aAAa,EAAAS,QAAA,cACZH,IAAA,CAACL,cAAc,EAACS,MAAM,CAAER,IAAK,CAAAO,QAAA,cAC3BH,IAAA,CAACF,GAAG,GAAE,CAAC,CACO,CAAC,CACJ,CAAC,CACR,CAAC,CACK,CAAC,CACnBO,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}