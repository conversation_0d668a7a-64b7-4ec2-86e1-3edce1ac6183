{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nvar Meta = function Meta(props) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      avatar = props.avatar,\n      title = props.title,\n      description = props.description,\n      others = __rest(props, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n    var prefixCls = getPrefixCls('card', customizePrefixCls);\n    var classString = classNames(\"\".concat(prefixCls, \"-meta\"), className);\n    var avatarDom = avatar ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-avatar\")\n    }, avatar) : null;\n    var titleDom = title ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-title\")\n    }, title) : null;\n    var descriptionDom = description ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-description\")\n    }, description) : null;\n    var MetaDetail = titleDom || descriptionDom ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-detail\")\n    }, titleDom, descriptionDom) : null;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n      className: classString\n    }), avatarDom, MetaDetail);\n  });\n};\nexport default Meta;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "ConfigConsumer", "Meta", "props", "createElement", "_ref", "getPrefixCls", "customizePrefixCls", "prefixCls", "className", "avatar", "title", "description", "others", "classString", "concat", "avatarDom", "titleDom", "descriptionDom", "MetaDetail"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/card/Meta.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigConsumer } from '../config-provider';\nvar Meta = function Meta(props) {\n  return /*#__PURE__*/React.createElement(ConfigConsumer, null, function (_ref) {\n    var getPrefixCls = _ref.getPrefixCls;\n    var customizePrefixCls = props.prefixCls,\n      className = props.className,\n      avatar = props.avatar,\n      title = props.title,\n      description = props.description,\n      others = __rest(props, [\"prefixCls\", \"className\", \"avatar\", \"title\", \"description\"]);\n    var prefixCls = getPrefixCls('card', customizePrefixCls);\n    var classString = classNames(\"\".concat(prefixCls, \"-meta\"), className);\n    var avatarDom = avatar ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-avatar\")\n    }, avatar) : null;\n    var titleDom = title ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-title\")\n    }, title) : null;\n    var descriptionDom = description ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-description\")\n    }, description) : null;\n    var MetaDetail = titleDom || descriptionDom ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-meta-detail\")\n    }, titleDom, descriptionDom) : null;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, others, {\n      className: classString\n    }), avatarDom, MetaDetail);\n  });\n};\nexport default Meta;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaH,KAAK,CAACI,aAAa,CAACH,cAAc,EAAE,IAAI,EAAE,UAAUI,IAAI,EAAE;IAC5E,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IACpC,IAAIC,kBAAkB,GAAGJ,KAAK,CAACK,SAAS;MACtCC,SAAS,GAAGN,KAAK,CAACM,SAAS;MAC3BC,MAAM,GAAGP,KAAK,CAACO,MAAM;MACrBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;MACnBC,WAAW,GAAGT,KAAK,CAACS,WAAW;MAC/BC,MAAM,GAAG5B,MAAM,CAACkB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IACtF,IAAIK,SAAS,GAAGF,YAAY,CAAC,MAAM,EAAEC,kBAAkB,CAAC;IACxD,IAAIO,WAAW,GAAGf,UAAU,CAAC,EAAE,CAACgB,MAAM,CAACP,SAAS,EAAE,OAAO,CAAC,EAAEC,SAAS,CAAC;IACtE,IAAIO,SAAS,GAAGN,MAAM,GAAG,aAAaV,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;MAC/DK,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,cAAc;IAChD,CAAC,EAAEE,MAAM,CAAC,GAAG,IAAI;IACjB,IAAIO,QAAQ,GAAGN,KAAK,GAAG,aAAaX,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;MAC7DK,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,aAAa;IAC/C,CAAC,EAAEG,KAAK,CAAC,GAAG,IAAI;IAChB,IAAIO,cAAc,GAAGN,WAAW,GAAG,aAAaZ,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;MACzEK,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,mBAAmB;IACrD,CAAC,EAAEI,WAAW,CAAC,GAAG,IAAI;IACtB,IAAIO,UAAU,GAAGF,QAAQ,IAAIC,cAAc,GAAG,aAAalB,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;MACpFK,SAAS,EAAE,EAAE,CAACM,MAAM,CAACP,SAAS,EAAE,cAAc;IAChD,CAAC,EAAES,QAAQ,EAAEC,cAAc,CAAC,GAAG,IAAI;IACnC,OAAO,aAAalB,KAAK,CAACI,aAAa,CAAC,KAAK,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,MAAM,EAAE;MAClEJ,SAAS,EAAEK;IACb,CAAC,CAAC,EAAEE,SAAS,EAAEG,UAAU,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;AACD,eAAejB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}