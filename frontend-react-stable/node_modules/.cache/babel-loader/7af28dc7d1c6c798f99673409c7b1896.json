{"ast": null, "code": "// `victory-vendor/d3-shape` (CommonJS)\n// See upstream license: https://github.com/d3/d3-shape/blob/main/LICENSE\n//\n// This file only exists for tooling that doesn't work yet with package.json:exports\n// by proxying through the CommonJS version.\nmodule.exports = require(\"./lib/d3-shape\");", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/d3-shape.js"], "sourcesContent": ["\n// `victory-vendor/d3-shape` (CommonJS)\n// See upstream license: https://github.com/d3/d3-shape/blob/main/LICENSE\n//\n// This file only exists for tooling that doesn't work yet with package.json:exports\n// by proxying through the CommonJS version.\nmodule.exports = require(\"./lib/d3-shape\");\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}