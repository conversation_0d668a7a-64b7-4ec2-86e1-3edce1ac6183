{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FormatPainterOutlinedSvg from \"@ant-design/icons-svg/es/asn/FormatPainterOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FormatPainterOutlined = function FormatPainterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FormatPainterOutlinedSvg\n  }));\n};\nFormatPainterOutlined.displayName = 'FormatPainterOutlined';\nexport default /*#__PURE__*/React.forwardRef(FormatPainterOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "FormatPainterOutlinedSvg", "AntdIcon", "FormatPainterOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/FormatPainterOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FormatPainterOutlinedSvg from \"@ant-design/icons-svg/es/asn/FormatPainterOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FormatPainterOutlined = function FormatPainterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FormatPainterOutlinedSvg\n  }));\n};\nFormatPainterOutlined.displayName = 'FormatPainterOutlined';\nexport default /*#__PURE__*/React.forwardRef(FormatPainterOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,qBAAqB,CAACK,WAAW,GAAG,uBAAuB;AAC3D,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}