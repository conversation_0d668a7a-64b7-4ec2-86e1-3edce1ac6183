{"ast": null, "code": "import React,{useEffect}from'react';import{Form,Input,But<PERSON>,Card,Typography,Alert,Space}from'antd';import{UserOutlined,LockOutlined}from'@ant-design/icons';import{useDispatch,useSelector}from'react-redux';import{useNavigate}from'react-router-dom';import{loginAsync,clearError}from'../store/slices/authSlice';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const LoginPage=()=>{const[form]=Form.useForm();const dispatch=useDispatch();const navigate=useNavigate();const{loading,error,isAuthenticated}=useSelector(state=>state.auth);useEffect(()=>{if(isAuthenticated){navigate('/');}},[isAuthenticated,navigate]);useEffect(()=>{// 清除之前的错误信息\nreturn()=>{dispatch(clearError());};},[dispatch]);const handleSubmit=async values=>{try{await dispatch(loginAsync(values)).unwrap();// 登录成功后会通过useEffect自动跳转\n}catch(error){// 错误已经在store中处理\n}};return/*#__PURE__*/_jsx(\"div\",{style:{minHeight:'100vh',background:'linear-gradient(to bottom, #e6f3ff, #ffffff)',display:'flex',alignItems:'center',justifyContent:'center',padding:'20px'},children:/*#__PURE__*/_jsx(Card,{style:{width:'100%',maxWidth:400,boxShadow:'0 4px 16px rgba(0, 0, 0, 0.1)',borderRadius:'8px'},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%',textAlign:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:2,style:{color:'#1890ff',marginBottom:8},children:\"AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u57FA\\u4E8E\\u6DF1\\u5EA6\\u5B66\\u4E60\\u7684\\u7F51\\u7EDC\\u6D41\\u91CF\\u5F02\\u5E38\\u68C0\\u6D4B\\u548C\\u6E05\\u6D17\\u7CFB\\u7EDF\"})]}),error&&/*#__PURE__*/_jsx(Alert,{message:\"\\u767B\\u5F55\\u5931\\u8D25\",description:error,type:\"error\",showIcon:true,closable:true,onClose:()=>dispatch(clearError())}),/*#__PURE__*/_jsxs(Form,{form:form,name:\"login\",onFinish:handleSubmit,autoComplete:\"off\",size:\"large\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"username\",rules:[{required:true,message:'请输入用户名!'},{min:2,message:'用户名至少2个字符!'}],children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(UserOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",autoComplete:\"username\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"password\",rules:[{required:true,message:'请输入密码!'},{min:6,message:'密码至少6个字符!'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{}),placeholder:\"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",autoComplete:\"current-password\"})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",loading:loading,style:{width:'100%'},children:loading?'登录中...':'登录'})})]}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center'},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:'12px'},children:\"\\xA9 2024 AI\\u667A\\u80FD\\u6E05\\u6D17\\u7B56\\u7565\\u7CFB\\u7EDF. All rights reserved.\"})})]})})});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useEffect", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "<PERSON><PERSON>", "Space", "UserOutlined", "LockOutlined", "useDispatch", "useSelector", "useNavigate", "loginAsync", "clearError", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "LoginPage", "form", "useForm", "dispatch", "navigate", "loading", "error", "isAuthenticated", "state", "auth", "handleSubmit", "values", "unwrap", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "children", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "borderRadius", "direction", "size", "textAlign", "level", "color", "marginBottom", "type", "message", "description", "showIcon", "closable", "onClose", "name", "onFinish", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "htmlType", "fontSize"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Alert, Space } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { RootState } from '../store/store';\nimport { loginAsync, clearError } from '../store/slices/authSlice';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  \n  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n\n  useEffect(() => {\n    // 清除之前的错误信息\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    try {\n      await dispatch(loginAsync(values)).unwrap();\n      // 登录成功后会通过useEffect自动跳转\n    } catch (error) {\n      // 错误已经在store中处理\n    }\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(to bottom, #e6f3ff, #ffffff)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: 400,\n          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',\n          borderRadius: '8px',\n        }}\n      >\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%', textAlign: 'center' }}>\n          <div>\n            <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>\n              AI智能清洗策略系统\n            </Title>\n            <Text type=\"secondary\">\n              基于深度学习的网络流量异常检测和清洗系统\n            </Text>\n          </div>\n\n          {error && (\n            <Alert\n              message=\"登录失败\"\n              description={error}\n              type=\"error\"\n              showIcon\n              closable\n              onClose={() => dispatch(clearError())}\n            />\n          )}\n\n          <Form\n            form={form}\n            name=\"login\"\n            onFinish={handleSubmit}\n            autoComplete=\"off\"\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[\n                { required: true, message: '请输入用户名!' },\n                { min: 2, message: '用户名至少2个字符!' },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n                autoComplete=\"username\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码!' },\n                { min: 6, message: '密码至少6个字符!' },\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请输入密码\"\n                autoComplete=\"current-password\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                {loading ? '登录中...' : '登录'}\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <div style={{ textAlign: 'center' }}>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              © 2024 AI智能清洗策略系统. All rights reserved.\n            </Text>\n          </div>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,KAAK,CAAEC,KAAK,KAAQ,MAAM,CAC1E,OAASC,YAAY,CAAEC,YAAY,KAAQ,mBAAmB,CAC9D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,OAASC,UAAU,CAAEC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGf,UAAU,CAOlC,KAAM,CAAAgB,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,IAAI,CAAC,CAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC,CAC7B,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAEc,OAAO,CAAEC,KAAK,CAAEC,eAAgB,CAAC,CAAGjB,WAAW,CAAEkB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAEzF9B,SAAS,CAAC,IAAM,CACd,GAAI4B,eAAe,CAAE,CACnBH,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CAAE,CAACG,eAAe,CAAEH,QAAQ,CAAC,CAAC,CAE/BzB,SAAS,CAAC,IAAM,CACd;AACA,MAAO,IAAM,CACXwB,QAAQ,CAACV,UAAU,CAAC,CAAC,CAAC,CACxB,CAAC,CACH,CAAC,CAAE,CAACU,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAO,YAAY,CAAG,KAAO,CAAAC,MAAuB,EAAK,CACtD,GAAI,CACF,KAAM,CAAAR,QAAQ,CAACX,UAAU,CAACmB,MAAM,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAC3C;AACF,CAAE,MAAON,KAAK,CAAE,CACd;AAAA,CAEJ,CAAC,CAED,mBACEX,IAAA,QAAKkB,KAAK,CAAE,CACVC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,8CAA8C,CAC1DC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,OAAO,CAAE,MACX,CAAE,CAAAC,QAAA,cACAzB,IAAA,CAACZ,IAAI,EACH8B,KAAK,CAAE,CACLQ,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,GAAG,CACbC,SAAS,CAAE,+BAA+B,CAC1CC,YAAY,CAAE,KAChB,CAAE,CAAAJ,QAAA,cAEFvB,KAAA,CAACX,KAAK,EAACuC,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACb,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAM,CAAEM,SAAS,CAAE,QAAS,CAAE,CAAAP,QAAA,eACrFvB,KAAA,QAAAuB,QAAA,eACEzB,IAAA,CAACG,KAAK,EAAC8B,KAAK,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,KAAK,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAV,QAAA,CAAC,oDAE/D,CAAO,CAAC,cACRzB,IAAA,CAACI,IAAI,EAACgC,IAAI,CAAC,WAAW,CAAAX,QAAA,CAAC,0HAEvB,CAAM,CAAC,EACJ,CAAC,CAELd,KAAK,eACJX,IAAA,CAACV,KAAK,EACJ+C,OAAO,CAAC,0BAAM,CACdC,WAAW,CAAE3B,KAAM,CACnByB,IAAI,CAAC,OAAO,CACZG,QAAQ,MACRC,QAAQ,MACRC,OAAO,CAAEA,CAAA,GAAMjC,QAAQ,CAACV,UAAU,CAAC,CAAC,CAAE,CACvC,CACF,cAEDI,KAAA,CAACjB,IAAI,EACHqB,IAAI,CAAEA,IAAK,CACXoC,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE5B,YAAa,CACvB6B,YAAY,CAAC,KAAK,CAClBb,IAAI,CAAC,OAAO,CAAAN,QAAA,eAEZzB,IAAA,CAACf,IAAI,CAAC4D,IAAI,EACRH,IAAI,CAAC,UAAU,CACfI,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEV,OAAO,CAAE,SAAU,CAAC,CACtC,CAAEW,GAAG,CAAE,CAAC,CAAEX,OAAO,CAAE,YAAa,CAAC,CACjC,CAAAZ,QAAA,cAEFzB,IAAA,CAACd,KAAK,EACJ+D,MAAM,cAAEjD,IAAA,CAACR,YAAY,GAAE,CAAE,CACzB0D,WAAW,CAAC,sCAAQ,CACpBN,YAAY,CAAC,UAAU,CACxB,CAAC,CACO,CAAC,cAEZ5C,IAAA,CAACf,IAAI,CAAC4D,IAAI,EACRH,IAAI,CAAC,UAAU,CACfI,KAAK,CAAE,CACL,CAAEC,QAAQ,CAAE,IAAI,CAAEV,OAAO,CAAE,QAAS,CAAC,CACrC,CAAEW,GAAG,CAAE,CAAC,CAAEX,OAAO,CAAE,WAAY,CAAC,CAChC,CAAAZ,QAAA,cAEFzB,IAAA,CAACd,KAAK,CAACiE,QAAQ,EACbF,MAAM,cAAEjD,IAAA,CAACP,YAAY,GAAE,CAAE,CACzByD,WAAW,CAAC,gCAAO,CACnBN,YAAY,CAAC,kBAAkB,CAChC,CAAC,CACO,CAAC,cAEZ5C,IAAA,CAACf,IAAI,CAAC4D,IAAI,EAAApB,QAAA,cACRzB,IAAA,CAACb,MAAM,EACLiD,IAAI,CAAC,SAAS,CACdgB,QAAQ,CAAC,QAAQ,CACjB1C,OAAO,CAAEA,OAAQ,CACjBQ,KAAK,CAAE,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAAAD,QAAA,CAExBf,OAAO,CAAG,QAAQ,CAAG,IAAI,CACpB,CAAC,CACA,CAAC,EACR,CAAC,cAEPV,IAAA,QAAKkB,KAAK,CAAE,CAAEc,SAAS,CAAE,QAAS,CAAE,CAAAP,QAAA,cAClCzB,IAAA,CAACI,IAAI,EAACgC,IAAI,CAAC,WAAW,CAAClB,KAAK,CAAE,CAAEmC,QAAQ,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAAC,oFAEpD,CAAM,CAAC,CACJ,CAAC,EACD,CAAC,CACJ,CAAC,CACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}