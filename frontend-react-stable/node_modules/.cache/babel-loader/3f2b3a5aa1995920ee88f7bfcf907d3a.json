{"ast": null, "code": "// This icon file is generated automatically.\nvar FolderViewOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"style\",\n        \"attrs\": {}\n      }]\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M309.1 554.3a42.92 42.92 0 000 36.4C353.3 684 421.6 732 512.5 732s159.2-48.1 203.4-141.3c5.4-11.5 5.4-24.8.1-36.3l-.1-.1-.1-.1C671.7 461 603.4 413 512.5 413s-159.2 48.1-203.4 141.3zM512.5 477c62.1 0 107.4 30 141.1 95.5C620 638 574.6 668 512.5 668s-107.4-30-141.1-95.5c33.7-65.5 79-95.5 141.1-95.5z\"\n      }\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M457 573a56 56 0 10112 0 56 56 0 10-112 0z\"\n      }\n    }, {\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z\"\n      }\n    }]\n  },\n  \"name\": \"folder-view\",\n  \"theme\": \"outlined\"\n};\nexport default FolderViewOutlined;", "map": {"version": 3, "names": ["FolderViewOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/FolderViewOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar FolderViewOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M309.1 554.3a42.92 42.92 0 000 36.4C353.3 684 421.6 732 512.5 732s159.2-48.1 203.4-141.3c5.4-11.5 5.4-24.8.1-36.3l-.1-.1-.1-.1C671.7 461 603.4 413 512.5 413s-159.2 48.1-203.4 141.3zM512.5 477c62.1 0 107.4 30 141.1 95.5C620 638 574.6 668 512.5 668s-107.4-30-141.1-95.5c33.7-65.5 79-95.5 141.1-95.5z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M457 573a56 56 0 10112 0 56 56 0 10-112 0z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z\" } }] }, \"name\": \"folder-view\", \"theme\": \"outlined\" };\nexport default FolderViewOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,OAAO;QAAE,OAAO,EAAE,CAAC;MAAE,CAAC;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA4S;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA6C;IAAE,CAAC,EAAE;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAuM;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,aAAa;EAAE,OAAO,EAAE;AAAW,CAAC;AAC/4B,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}