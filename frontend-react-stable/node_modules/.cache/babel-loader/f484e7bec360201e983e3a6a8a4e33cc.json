{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open,\n    notFoundContent = _useBaseProps.notFoundContent;\n  var _React$useContext = React.useContext(TreeSelectContext),\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    treeData = _React$useContext.treeData,\n    fieldNames = _React$useContext.fieldNames,\n    onSelect = _React$useContext.onSelect,\n    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n    treeExpandAction = _React$useContext.treeExpandAction;\n  var _React$useContext2 = React.useContext(LegacyContext),\n    checkable = _React$useContext2.checkable,\n    checkedKeys = _React$useContext2.checkedKeys,\n    halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n    treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n    onTreeExpand = _React$useContext2.onTreeExpand,\n    treeIcon = _React$useContext2.treeIcon,\n    showTreeIcon = _React$useContext2.showTreeIcon,\n    switcherIcon = _React$useContext2.switcherIcon,\n    treeLine = _React$useContext2.treeLine,\n    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n    loadData = _React$useContext2.loadData,\n    treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n    treeMotion = _React$useContext2.treeMotion,\n    onTreeLoad = _React$useContext2.onTreeLoad,\n    keyEntities = _React$useContext2.keyEntities;\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  }, [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  }); // ========================== Values ==========================\n\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]); // ========================== Scroll ==========================\n\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 ? void 0 : _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n  }, [open]); // ========================== Search ==========================\n\n  var lowerSearchValue = String(searchValue).toLowerCase();\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  }; // =========================== Keys ===========================\n\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedKeys = _React$useState2[0],\n    setExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    searchExpandedKeys = _React$useState4[0],\n    setSearchExpandedKeys = _React$useState4[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n  }, [searchValue]);\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  }; // ========================== Events ==========================\n\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  }; // ========================= Keyboard =========================\n\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    activeKey = _React$useState6[0],\n    setActiveKey = _React$useState6[1];\n  var activeEntity = keyEntities[activeKey];\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 ? void 0 : _treeRef$current3.onKeyDown(event);\n            break;\n          // >>> Select item\n\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                  selectable = _ref.selectable,\n                  value = _ref.value;\n                if (selectable !== false) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n              break;\n            }\n          // >>> Close\n\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  }); // ========================== Render ==========================\n\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: searchValue ? null : loadData,\n    motion: treeMotion,\n    activeKey: activeKey // We handle keys by out instead tree self\n    ,\n\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction\n  })));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_slicedToArray", "React", "KeyCode", "useMemo", "useBaseProps", "Tree", "LegacyContext", "TreeSelectContext", "getAllKeys", "isCheckDisabled", "HIDDEN_STYLE", "width", "height", "display", "overflow", "opacity", "border", "padding", "margin", "OptionList", "_", "ref", "_useBaseProps", "prefixCls", "multiple", "searchValue", "toggle<PERSON><PERSON>", "open", "notFoundContent", "_React$useContext", "useContext", "virtual", "listHeight", "listItemHeight", "treeData", "fieldNames", "onSelect", "dropdownMatchSelectWidth", "treeExpandAction", "_React$useContext2", "checkable", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "treeExpandedKeys", "treeDefaultExpandAll", "treeDefaultExpandedKeys", "onTreeExpand", "treeIcon", "showTreeIcon", "switcherIcon", "treeLine", "treeNodeFilterProp", "loadData", "treeLoaded<PERSON><PERSON>s", "treeMotion", "onTreeLoad", "keyEntities", "treeRef", "useRef", "memoTreeData", "prev", "next", "mergedCheckedKeys", "checked", "halfChecked", "useEffect", "length", "_treeRef$current", "current", "scrollTo", "key", "lowerSearchValue", "String", "toLowerCase", "filterTreeNode", "treeNode", "includes", "_React$useState", "useState", "_React$useState2", "expandedKeys", "setExpandedKeys", "_React$useState3", "_React$useState4", "searchExpandedKeys", "setSearchExpandedKeys", "mergedExpandedKeys", "onInternalExpand", "keys", "onListMouseDown", "event", "preventDefault", "onInternalSelect", "__", "info", "node", "selected", "_React$useState5", "_React$useState6", "active<PERSON><PERSON>", "setActiveKey", "activeEntity", "useImperativeHandle", "_treeRef$current2", "onKeyDown", "_treeRef$current3", "which", "UP", "DOWN", "LEFT", "RIGHT", "ENTER", "_ref", "selectable", "value", "ESC", "onKeyUp", "createElement", "role", "className", "concat", "onMouseDown", "treeProps", "loadedKeys", "style", "focusable", "itemHeight", "icon", "showIcon", "showLine", "motion", "checkStrictly", "<PERSON><PERSON><PERSON><PERSON>", "defaultExpandAll", "onActiveChange", "onCheck", "onExpand", "onLoad", "expandAction", "RefOptionList", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree-select/es/OptionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\n\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n      prefixCls = _useBaseProps.prefixCls,\n      multiple = _useBaseProps.multiple,\n      searchValue = _useBaseProps.searchValue,\n      toggleOpen = _useBaseProps.toggleOpen,\n      open = _useBaseProps.open,\n      notFoundContent = _useBaseProps.notFoundContent;\n\n  var _React$useContext = React.useContext(TreeSelectContext),\n      virtual = _React$useContext.virtual,\n      listHeight = _React$useContext.listHeight,\n      listItemHeight = _React$useContext.listItemHeight,\n      treeData = _React$useContext.treeData,\n      fieldNames = _React$useContext.fieldNames,\n      onSelect = _React$useContext.onSelect,\n      dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n      treeExpandAction = _React$useContext.treeExpandAction;\n\n  var _React$useContext2 = React.useContext(LegacyContext),\n      checkable = _React$useContext2.checkable,\n      checkedKeys = _React$useContext2.checkedKeys,\n      halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n      treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n      treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n      treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n      onTreeExpand = _React$useContext2.onTreeExpand,\n      treeIcon = _React$useContext2.treeIcon,\n      showTreeIcon = _React$useContext2.showTreeIcon,\n      switcherIcon = _React$useContext2.switcherIcon,\n      treeLine = _React$useContext2.treeLine,\n      treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n      loadData = _React$useContext2.loadData,\n      treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n      treeMotion = _React$useContext2.treeMotion,\n      onTreeLoad = _React$useContext2.onTreeLoad,\n      keyEntities = _React$useContext2.keyEntities;\n\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  }, [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  }); // ========================== Values ==========================\n\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]); // ========================== Scroll ==========================\n\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 ? void 0 : _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n  }, [open]); // ========================== Search ==========================\n\n  var lowerSearchValue = String(searchValue).toLowerCase();\n\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  }; // =========================== Keys ===========================\n\n\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      expandedKeys = _React$useState2[0],\n      setExpandedKeys = _React$useState2[1];\n\n  var _React$useState3 = React.useState(null),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      searchExpandedKeys = _React$useState4[0],\n      setSearchExpandedKeys = _React$useState4[1];\n\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n  }, [searchValue]);\n\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  }; // ========================== Events ==========================\n\n\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  }; // ========================= Keyboard =========================\n\n\n  var _React$useState5 = React.useState(null),\n      _React$useState6 = _slicedToArray(_React$useState5, 2),\n      activeKey = _React$useState6[0],\n      setActiveKey = _React$useState6[1];\n\n  var activeEntity = keyEntities[activeKey];\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n\n        var which = event.which;\n\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 ? void 0 : _treeRef$current3.onKeyDown(event);\n            break;\n          // >>> Select item\n\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                    selectable = _ref.selectable,\n                    value = _ref.value;\n\n                if (selectable !== false) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n\n              break;\n            }\n          // >>> Close\n\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  }); // ========================== Render ==========================\n\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: searchValue ? null : loadData,\n    motion: treeMotion,\n    activeKey: activeKey // We handle keys by out instead tree self\n    ,\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction\n  })));\n};\n\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,UAAU,EAAEC,eAAe,QAAQ,mBAAmB;AAC/D,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE;AACV,CAAC;AAED,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC3C,IAAIC,aAAa,GAAGlB,YAAY,CAAC,CAAC;IAC9BmB,SAAS,GAAGD,aAAa,CAACC,SAAS;IACnCC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;IACjCC,WAAW,GAAGH,aAAa,CAACG,WAAW;IACvCC,UAAU,GAAGJ,aAAa,CAACI,UAAU;IACrCC,IAAI,GAAGL,aAAa,CAACK,IAAI;IACzBC,eAAe,GAAGN,aAAa,CAACM,eAAe;EAEnD,IAAIC,iBAAiB,GAAG5B,KAAK,CAAC6B,UAAU,CAACvB,iBAAiB,CAAC;IACvDwB,OAAO,GAAGF,iBAAiB,CAACE,OAAO;IACnCC,UAAU,GAAGH,iBAAiB,CAACG,UAAU;IACzCC,cAAc,GAAGJ,iBAAiB,CAACI,cAAc;IACjDC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,UAAU,GAAGN,iBAAiB,CAACM,UAAU;IACzCC,QAAQ,GAAGP,iBAAiB,CAACO,QAAQ;IACrCC,wBAAwB,GAAGR,iBAAiB,CAACQ,wBAAwB;IACrEC,gBAAgB,GAAGT,iBAAiB,CAACS,gBAAgB;EAEzD,IAAIC,kBAAkB,GAAGtC,KAAK,CAAC6B,UAAU,CAACxB,aAAa,CAAC;IACpDkC,SAAS,GAAGD,kBAAkB,CAACC,SAAS;IACxCC,WAAW,GAAGF,kBAAkB,CAACE,WAAW;IAC5CC,eAAe,GAAGH,kBAAkB,CAACG,eAAe;IACpDC,gBAAgB,GAAGJ,kBAAkB,CAACI,gBAAgB;IACtDC,oBAAoB,GAAGL,kBAAkB,CAACK,oBAAoB;IAC9DC,uBAAuB,GAAGN,kBAAkB,CAACM,uBAAuB;IACpEC,YAAY,GAAGP,kBAAkB,CAACO,YAAY;IAC9CC,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ;IACtCC,YAAY,GAAGT,kBAAkB,CAACS,YAAY;IAC9CC,YAAY,GAAGV,kBAAkB,CAACU,YAAY;IAC9CC,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ;IACtCC,kBAAkB,GAAGZ,kBAAkB,CAACY,kBAAkB;IAC1DC,QAAQ,GAAGb,kBAAkB,CAACa,QAAQ;IACtCC,cAAc,GAAGd,kBAAkB,CAACc,cAAc;IAClDC,UAAU,GAAGf,kBAAkB,CAACe,UAAU;IAC1CC,UAAU,GAAGhB,kBAAkB,CAACgB,UAAU;IAC1CC,WAAW,GAAGjB,kBAAkB,CAACiB,WAAW;EAEhD,IAAIC,OAAO,GAAGxD,KAAK,CAACyD,MAAM,CAAC,CAAC;EAC5B,IAAIC,YAAY,GAAGxD,OAAO,CAAC,YAAY;IACrC,OAAO+B,QAAQ;EACjB,CAAC,EAAE,CAACP,IAAI,EAAEO,QAAQ,CAAC,EAAE,UAAU0B,IAAI,EAAEC,IAAI,EAAE;IACzC,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,iBAAiB,GAAG7D,KAAK,CAACE,OAAO,CAAC,YAAY;IAChD,IAAI,CAACqC,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA,OAAO;MACLuB,OAAO,EAAEtB,WAAW;MACpBuB,WAAW,EAAEtB;IACf,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,EAAEC,WAAW,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE/CzC,KAAK,CAACgE,SAAS,CAAC,YAAY;IAC1B;IACA,IAAItC,IAAI,IAAI,CAACH,QAAQ,IAAIiB,WAAW,CAACyB,MAAM,EAAE;MAC3C,IAAIC,gBAAgB;MAEpB,CAACA,gBAAgB,GAAGV,OAAO,CAACW,OAAO,MAAM,IAAI,IAAID,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACE,QAAQ,CAAC;QAChHC,GAAG,EAAE7B,WAAW,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,IAAI4C,gBAAgB,GAAGC,MAAM,CAAC/C,WAAW,CAAC,CAACgD,WAAW,CAAC,CAAC;EAExD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,QAAQ,EAAE;IACrD,IAAI,CAACJ,gBAAgB,EAAE;MACrB,OAAO,KAAK;IACd;IAEA,OAAOC,MAAM,CAACG,QAAQ,CAACxB,kBAAkB,CAAC,CAAC,CAACsB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACL,gBAAgB,CAAC;EACtF,CAAC,CAAC,CAAC;;EAGH,IAAIM,eAAe,GAAG5E,KAAK,CAAC6E,QAAQ,CAACjC,uBAAuB,CAAC;IACzDkC,gBAAgB,GAAG/E,cAAc,CAAC6E,eAAe,EAAE,CAAC,CAAC;IACrDG,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC,IAAIG,gBAAgB,GAAGjF,KAAK,CAAC6E,QAAQ,CAAC,IAAI,CAAC;IACvCK,gBAAgB,GAAGnF,cAAc,CAACkF,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE/C,IAAIG,kBAAkB,GAAGrF,KAAK,CAACE,OAAO,CAAC,YAAY;IACjD,IAAIwC,gBAAgB,EAAE;MACpB,OAAO5C,kBAAkB,CAAC4C,gBAAgB,CAAC;IAC7C;IAEA,OAAOlB,WAAW,GAAG2D,kBAAkB,GAAGJ,YAAY;EACxD,CAAC,EAAE,CAACA,YAAY,EAAEI,kBAAkB,EAAEzC,gBAAgB,EAAElB,WAAW,CAAC,CAAC;EACrExB,KAAK,CAACgE,SAAS,CAAC,YAAY;IAC1B,IAAIxC,WAAW,EAAE;MACf4D,qBAAqB,CAAC7E,UAAU,CAAC0B,QAAQ,EAAEC,UAAU,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAACV,WAAW,CAAC,CAAC;EAEjB,IAAI8D,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IACrDP,eAAe,CAACO,IAAI,CAAC;IACrBH,qBAAqB,CAACG,IAAI,CAAC;IAE3B,IAAI1C,YAAY,EAAE;MAChBA,YAAY,CAAC0C,IAAI,CAAC;IACpB;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;IACpDA,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB,CAAC;EAED,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACzD,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAEpB,IAAIvD,SAAS,IAAI/B,eAAe,CAACsF,IAAI,CAAC,EAAE;MACtC;IACF;IAEA3D,QAAQ,CAAC2D,IAAI,CAACzB,GAAG,EAAE;MACjB0B,QAAQ,EAAE,CAACvD,WAAW,CAACmC,QAAQ,CAACmB,IAAI,CAACzB,GAAG;IAC1C,CAAC,CAAC;IAEF,IAAI,CAAC9C,QAAQ,EAAE;MACbE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIuE,gBAAgB,GAAGhG,KAAK,CAAC6E,QAAQ,CAAC,IAAI,CAAC;IACvCoB,gBAAgB,GAAGlG,cAAc,CAACiG,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEtC,IAAIG,YAAY,GAAG7C,WAAW,CAAC2C,SAAS,CAAC;EACzClG,KAAK,CAACqG,mBAAmB,CAACjF,GAAG,EAAE,YAAY;IACzC,IAAIkF,iBAAiB;IAErB,OAAO;MACLlC,QAAQ,EAAE,CAACkC,iBAAiB,GAAG9C,OAAO,CAACW,OAAO,MAAM,IAAI,IAAImC,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAClC,QAAQ;MAC9HmC,SAAS,EAAE,SAASA,SAASA,CAACd,KAAK,EAAE;QACnC,IAAIe,iBAAiB;QAErB,IAAIC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;QAEvB,QAAQA,KAAK;UACX;UACA,KAAKxG,OAAO,CAACyG,EAAE;UACf,KAAKzG,OAAO,CAAC0G,IAAI;UACjB,KAAK1G,OAAO,CAAC2G,IAAI;UACjB,KAAK3G,OAAO,CAAC4G,KAAK;YAChB,CAACL,iBAAiB,GAAGhD,OAAO,CAACW,OAAO,MAAM,IAAI,IAAIqC,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACD,SAAS,CAACd,KAAK,CAAC;YAC5H;UACF;;UAEA,KAAKxF,OAAO,CAAC6G,KAAK;YAChB;cACE,IAAIV,YAAY,EAAE;gBAChB,IAAIW,IAAI,GAAG,CAACX,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACN,IAAI,KAAK,CAAC,CAAC;kBAC5FkB,UAAU,GAAGD,IAAI,CAACC,UAAU;kBAC5BC,KAAK,GAAGF,IAAI,CAACE,KAAK;gBAEtB,IAAID,UAAU,KAAK,KAAK,EAAE;kBACxBrB,gBAAgB,CAAC,IAAI,EAAE;oBACrBG,IAAI,EAAE;sBACJzB,GAAG,EAAE6B;oBACP,CAAC;oBACDH,QAAQ,EAAE,CAACvD,WAAW,CAACmC,QAAQ,CAACsC,KAAK;kBACvC,CAAC,CAAC;gBACJ;cACF;cAEA;YACF;UACF;;UAEA,KAAKhH,OAAO,CAACiH,GAAG;YACd;cACEzF,UAAU,CAAC,KAAK,CAAC;YACnB;QACJ;MACF,CAAC;MACD0F,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;IAC/B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIzD,YAAY,CAACO,MAAM,KAAK,CAAC,EAAE;IAC7B,OAAO,aAAajE,KAAK,CAACoH,aAAa,CAAC,KAAK,EAAE;MAC7CC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACjG,SAAS,EAAE,QAAQ,CAAC;MACzCkG,WAAW,EAAEhC;IACf,CAAC,EAAE7D,eAAe,CAAC;EACrB;EAEA,IAAI8F,SAAS,GAAG;IACdvF,UAAU,EAAEA;EACd,CAAC;EAED,IAAIkB,cAAc,EAAE;IAClBqE,SAAS,CAACC,UAAU,GAAGtE,cAAc;EACvC;EAEA,IAAIiC,kBAAkB,EAAE;IACtBoC,SAAS,CAAC1C,YAAY,GAAGM,kBAAkB;EAC7C;EAEA,OAAO,aAAarF,KAAK,CAACoH,aAAa,CAAC,KAAK,EAAE;IAC7CI,WAAW,EAAEhC;EACf,CAAC,EAAEY,YAAY,IAAI1E,IAAI,IAAI,aAAa1B,KAAK,CAACoH,aAAa,CAAC,MAAM,EAAE;IAClEO,KAAK,EAAElH,YAAY;IACnB,WAAW,EAAE;EACf,CAAC,EAAE2F,YAAY,CAACN,IAAI,CAACmB,KAAK,CAAC,EAAE,aAAajH,KAAK,CAACoH,aAAa,CAAChH,IAAI,EAAEP,QAAQ,CAAC;IAC3EuB,GAAG,EAAEoC,OAAO;IACZoE,SAAS,EAAE,KAAK;IAChBtG,SAAS,EAAE,EAAE,CAACiG,MAAM,CAACjG,SAAS,EAAE,OAAO,CAAC;IACxCW,QAAQ,EAAEyB,YAAY;IACtB/C,MAAM,EAAEoB,UAAU;IAClB8F,UAAU,EAAE7F,cAAc;IAC1BF,OAAO,EAAEA,OAAO,KAAK,KAAK,IAAIM,wBAAwB,KAAK,KAAK;IAChEb,QAAQ,EAAEA,QAAQ;IAClBuG,IAAI,EAAEhF,QAAQ;IACdiF,QAAQ,EAAEhF,YAAY;IACtBC,YAAY,EAAEA,YAAY;IAC1BgF,QAAQ,EAAE/E,QAAQ;IAClBE,QAAQ,EAAE3B,WAAW,GAAG,IAAI,GAAG2B,QAAQ;IACvC8E,MAAM,EAAE5E,UAAU;IAClB6C,SAAS,EAAEA,SAAS,CAAC;IAAA;;IAErB3D,SAAS,EAAEA,SAAS;IACpB2F,aAAa,EAAE,IAAI;IACnB1F,WAAW,EAAEqB,iBAAiB;IAC9BsE,YAAY,EAAE,CAAC5F,SAAS,GAAGC,WAAW,GAAG,EAAE;IAC3C4F,gBAAgB,EAAEzF;EACpB,CAAC,EAAE8E,SAAS,EAAE;IACZ;IACAY,cAAc,EAAElC,YAAY;IAC5BhE,QAAQ,EAAEwD,gBAAgB;IAC1B2C,OAAO,EAAE3C,gBAAgB;IACzB4C,QAAQ,EAAEjD,gBAAgB;IAC1BkD,MAAM,EAAElF,UAAU;IAClBmB,cAAc,EAAEA,cAAc;IAC9BgE,YAAY,EAAEpG;EAChB,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAED,IAAIqG,aAAa,GAAG,aAAa1I,KAAK,CAAC2I,UAAU,CAACzH,UAAU,CAAC;AAC7DwH,aAAa,CAACE,WAAW,GAAG,YAAY;AACxC,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}