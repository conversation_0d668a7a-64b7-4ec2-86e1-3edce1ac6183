{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Alert, Progress, Typography, Space, Divider, message, Spin, Row, Col } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst DataCleaningPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('local');\n  const [processingMode, setProcessingMode] = useState('single');\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState({});\n\n  // 批量任务管理函数\n  const addBatchTask = () => {\n    const newTask = {\n      id: Date.now().toString(),\n      inputDir: '',\n      outputDir: '',\n      fileCount: 0\n    };\n    setBatchTasks([...batchTasks, newTask]);\n  };\n  const updateBatchTask = (id, field, value) => {\n    setBatchTasks(batchTasks.map(task => task.id === id ? {\n      ...task,\n      [field]: value\n    } : task));\n  };\n  const removeBatchTask = id => {\n    setBatchTasks(batchTasks.filter(task => task.id !== id));\n  };\n  const validateBatchTask = async task => {\n    try {\n      const response = await dataCleaningAPI.listFiles(task.inputDir);\n      const files = response.data.files || [];\n      const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt'));\n      updateBatchTask(task.id, 'fileCount', txtFiles.length.toString());\n      return txtFiles.length > 0;\n    } catch (error) {\n      message.error(`验证目录 ${task.inputDir} 失败`);\n      return false;\n    }\n  };\n  const startBatchAnalysis = async () => {\n    // 验证所有任务\n    const validTasks = [];\n    for (let i = 0; i < batchTasks.length; i++) {\n      const task = batchTasks[i];\n      const taskName = `任务${i + 1}`;\n      if (!task.inputDir || !task.outputDir) {\n        message.error(`请完善 \"${taskName}\" 的配置`);\n        return;\n      }\n      const isValid = await validateBatchTask(task);\n      if (isValid) {\n        validTasks.push({\n          ...task,\n          taskName\n        });\n      }\n    }\n    if (validTasks.length === 0) {\n      message.error('没有有效的批量任务');\n      return;\n    }\n    setBatchLoading(true);\n    try {\n      // 调用批量分析API\n      const response = await dataCleaningAPI.batchAnalyze({\n        tasks: validTasks.map((task, index) => ({\n          customer: `任务${index + 1}`,\n          input_dir: task.inputDir,\n          output_dir: task.outputDir\n        }))\n      });\n      if (response.data.success) {\n        const batchId = response.data.batch_id;\n        message.success(`批量任务已启动，任务ID: ${batchId}`);\n\n        // 开始监控批量任务进度\n        monitorBatchProgress(batchId);\n      } else {\n        message.error('批量任务启动失败');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '批量分析失败');\n    } finally {\n      setBatchLoading(false);\n    }\n  };\n  const monitorBatchProgress = async batchId => {\n    const maxAttempts = 60; // 10分钟监控\n    let attempt = 0;\n    const checkProgress = async () => {\n      try {\n        const response = await dataCleaningAPI.getBatchStatus(batchId);\n        console.log('批量任务状态响应:', response.data); // 添加调试日志\n\n        if (response.data && response.data.success) {\n          const {\n            status,\n            progress,\n            current_step,\n            error\n          } = response.data;\n          console.log(`批量任务状态: ${status}, 进度: ${progress}%`); // 添加调试日志\n\n          // 更新进度状态\n          setBatchProgress(prev => ({\n            ...prev,\n            [batchId]: progress || 0\n          }));\n          if (status === 'completed') {\n            message.success('批量分析完成！');\n            setBatchLoading(false);\n            console.log('批量任务成功完成');\n            return;\n          } else if (status === 'failed') {\n            const errorMsg = error || '未知错误';\n            message.error(`批量分析失败: ${errorMsg}`);\n            setBatchLoading(false);\n            console.error('批量任务失败:', errorMsg);\n            return;\n          }\n\n          // 如果任务还在进行中，继续监控\n          if (attempt < maxAttempts && (status === 'running' || status === 'pending' || !status)) {\n            attempt++;\n            setTimeout(checkProgress, 5000); // 5秒后再次检查\n          } else if (attempt >= maxAttempts) {\n            // 超时时不显示失败，而是提示用户手动检查\n            message.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');\n            setBatchLoading(false);\n            console.warn('批量任务监控超时');\n          }\n        } else {\n          // API响应格式问题，但不立即判定为失败\n          console.warn('批量任务状态API响应格式异常:', response.data);\n          if (attempt < maxAttempts) {\n            attempt++;\n            setTimeout(checkProgress, 5000);\n          } else {\n            message.warning('无法获取批量任务状态，请手动检查任务结果');\n            setBatchLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('监控批量任务进度失败:', error);\n\n        // 网络错误或API错误，增加重试次数\n        if (attempt < 10) {\n          // 增加重试次数到10次\n          attempt++;\n          console.log(`批量任务监控重试 ${attempt}/10`);\n          setTimeout(checkProgress, 5000);\n        } else {\n          // 多次重试后仍失败，提示用户但不判定任务失败\n          message.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');\n          setBatchLoading(false);\n          console.error('批量任务监控最终失败');\n        }\n      }\n    };\n\n    // 开始监控\n    checkProgress();\n  };\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt'));\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false,\n    // 阻止自动上传\n    onChange: info => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: e => {\n      console.log('Dropped files', e.dataTransfer.files);\n    }\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n    try {\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach(file => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir\n        });\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6D41\\u91CF\\u6570\\u636E\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\u6216\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u4E2D\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\uFF0C\\u5206\\u6790\\u540E\\u751F\\u6210CSV\\u6587\\u4EF6\\u5E76\\u4FDD\\u5B58\\u5230\\u6307\\u5B9A\\u76EE\\u5F55\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u5206\\u6790\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u5904\\u7406\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: processingMode,\n            onChange: e => setProcessingMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u76EE\\u5F55\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"batch\",\n              children: \"\\u6279\\u91CF\\u76EE\\u5F55\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), processingMode === 'single' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6D41\\u91CF\\u6570\\u636E\\u6E90\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n              value: dataSource,\n              onChange: e => setDataSource(e.target.value),\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Radio, {\n                value: \"local\",\n                children: \"\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Radio, {\n                value: \"upload\",\n                children: \"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636ETXT\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 11\n          }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u672C\\u5730\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n                compact: true,\n                style: {\n                  marginTop: 8,\n                  display: 'flex'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  value: folderPath,\n                  onChange: e => setFolderPath(e.target.value),\n                  placeholder: \"\\u4F8B\\u5982: /data/aizhinengqingxicepingdaliu\",\n                  style: {\n                    flex: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  onClick: fetchLocalFiles,\n                  loading: filesLoading,\n                  disabled: !folderPath,\n                  style: {\n                    marginLeft: 8\n                  },\n                  children: \"\\u5237\\u65B0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                spinning: filesLoading,\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  mode: \"multiple\",\n                  value: selectedFiles,\n                  onChange: setSelectedFiles,\n                  placeholder: \"\\u8BF7\\u9009\\u62E9TXT\\u6587\\u4EF6\",\n                  style: {\n                    width: '100%',\n                    marginTop: 8\n                  },\n                  loading: filesLoading,\n                  children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                    value: file,\n                    children: file\n                  }, file, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n              ...uploadProps,\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"ant-upload-drag-icon\",\n                children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"ant-upload-text\",\n                children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"ant-upload-hint\",\n                children: \"\\u652F\\u6301\\u5355\\u4E2A\\u6216\\u6279\\u91CF\\u4E0A\\u4F20TXT\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              value: outputDir,\n              onChange: e => setOutputDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              style: {\n                marginTop: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 19\n            }, this),\n            onClick: handleCleanData,\n            loading: loading,\n            disabled: !isFormValid(),\n            className: \"action-button\",\n            children: loading ? '正在处理...' : '执行流量分析'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 11\n          }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-section\",\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u5904\\u7406\\u8FDB\\u5EA6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: progress,\n              status: \"active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), result && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u5904\\u7406\\u5B8C\\u6210\",\n            description: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u5904\\u7406\\u7ED3\\u679C\\uFF1A\", result.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 23\n              }, this), result.output_file && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u8F93\\u51FA\\u6587\\u4EF6\\uFF1A\", result.output_file]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 46\n              }, this), result.processed_files && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u5904\\u7406\\u7684\\u6587\\u4EF6\\u6570\\uFF1A\", result.processed_files]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 50\n              }, this), result.total_rows && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u603B\\u884C\\u6570\\uFF1A\", result.total_rows]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 21\n            }, this),\n            type: \"success\",\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), processingMode === 'batch' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6279\\u91CF\\u4EFB\\u52A1\\u914D\\u7F6E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: [batchTasks.map((task, index) => /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                style: {\n                  marginBottom: 16\n                },\n                title: `任务 ${index + 1}`,\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"text\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 33\n                  }, this),\n                  onClick: () => removeBatchTask(task.id)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 25\n                }, this),\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"\\u8F93\\u5165\\u76EE\\u5F55\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Input, {\n                      value: task.inputDir,\n                      onChange: e => updateBatchTask(task.id, 'inputDir', e.target.value),\n                      placeholder: \"\\u4F8B\\u5982\\uFF1A/data/input\",\n                      style: {\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Input, {\n                      value: task.outputDir,\n                      onChange: e => updateBatchTask(task.id, 'outputDir', e.target.value),\n                      placeholder: \"\\u4F8B\\u5982\\uFF1A/data/output\",\n                      style: {\n                        marginTop: 4\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 23\n                }, this), task.fileCount !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u68C0\\u6D4B\\u5230 \", task.fileCount, \" \\u4E2ATXT\\u6587\\u4EF6\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 25\n                }, this)]\n              }, task.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"dashed\",\n                icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 27\n                }, this),\n                onClick: addBatchTask,\n                style: {\n                  width: '100%',\n                  marginBottom: 16\n                },\n                children: \"\\u6DFB\\u52A0\\u4EFB\\u52A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                size: \"large\",\n                loading: batchLoading,\n                disabled: batchTasks.length === 0,\n                onClick: startBatchAnalysis,\n                style: {\n                  width: '100%'\n                },\n                children: batchLoading ? '批量分析中...' : '开始批量分析'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this), batchLoading && Object.keys(batchProgress).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6279\\u91CF\\u4EFB\\u52A1\\u8FDB\\u5EA6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this), Object.entries(batchProgress).map(([taskId, progress]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: progress,\n                    status: progress === 100 ? 'success' : 'active',\n                    format: percent => `${percent}%`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 27\n                  }, this)\n                }, taskId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 352,\n    columnNumber: 5\n  }, this);\n};\n_s(DataCleaningPage, \"l9ctDTHEUY1ai+u030csZppwOz4=\");\n_c = DataCleaningPage;\nexport default DataCleaningPage;\nvar _c;\n$RefreshReg$(_c, \"DataCleaningPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "Progress", "Typography", "Space", "Divider", "message", "Spin", "Row", "Col", "InboxOutlined", "PlayCircleOutlined", "DeleteOutlined", "PlusOutlined", "dataCleaningAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "DataCleaningPage", "_s", "dataSource", "setDataSource", "processingMode", "setProcessingMode", "uploadedFiles", "setUploadedFiles", "folderPath", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "loading", "setLoading", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "batchTasks", "setBatchTasks", "batchLoading", "setBatchLoading", "batchProgress", "setBatchProgress", "addBatchTask", "newTask", "id", "Date", "now", "toString", "inputDir", "fileCount", "updateBatchTask", "field", "value", "map", "task", "removeBatchTask", "filter", "validateBatchTask", "response", "listFiles", "files", "data", "txtFiles", "file", "toLowerCase", "endsWith", "length", "error", "startBatchAnalysis", "validTasks", "i", "taskName", "<PERSON><PERSON><PERSON><PERSON>", "push", "batchAnalyze", "tasks", "index", "customer", "input_dir", "output_dir", "success", "batchId", "batch_id", "monitorBatchProgress", "_error$response", "_error$response$data", "detail", "maxAttempts", "attempt", "checkProgress", "getBatchStatus", "console", "log", "status", "current_step", "prev", "errorMsg", "setTimeout", "warning", "warn", "fetchLocalFiles", "_error$response2", "_error$response2$data", "timer", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "dataTransfer", "handleCleanData", "formData", "FormData", "for<PERSON>ach", "append", "originFileObj", "progressInterval", "setInterval", "clearInterval", "cleanData", "cleanDataLocal", "folder_path", "selected_files", "_error$response3", "_error$response3$data", "isFormValid", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "title", "className", "direction", "size", "width", "strong", "Group", "target", "marginTop", "compact", "display", "placeholder", "flex", "onClick", "disabled", "marginLeft", "spinning", "mode", "icon", "percent", "description", "output_file", "processed_files", "total_rows", "showIcon", "extra", "danger", "gutter", "span", "undefined", "Object", "keys", "entries", "taskId", "format", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Row,\n  Col,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [processingMode, setProcessingMode] = useState<'single' | 'batch'>('single');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState<Array<{\n    id: string;\n    inputDir: string;\n    outputDir: string;\n    fileCount?: number;\n  }>>([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState<{[key: string]: number}>({});\n\n  // 批量任务管理函数\n  const addBatchTask = () => {\n    const newTask = {\n      id: Date.now().toString(),\n      inputDir: '',\n      outputDir: '',\n      fileCount: 0\n    };\n    setBatchTasks([...batchTasks, newTask]);\n  };\n\n  const updateBatchTask = (id: string, field: string, value: string) => {\n    setBatchTasks(batchTasks.map(task =>\n      task.id === id ? { ...task, [field]: value } : task\n    ));\n  };\n\n  const removeBatchTask = (id: string) => {\n    setBatchTasks(batchTasks.filter(task => task.id !== id));\n  };\n\n  const validateBatchTask = async (task: any) => {\n    try {\n      const response = await dataCleaningAPI.listFiles(task.inputDir);\n      const files = response.data.files || [];\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      updateBatchTask(task.id, 'fileCount', txtFiles.length.toString());\n      return txtFiles.length > 0;\n    } catch (error) {\n      message.error(`验证目录 ${task.inputDir} 失败`);\n      return false;\n    }\n  };\n\n  const startBatchAnalysis = async () => {\n    // 验证所有任务\n    const validTasks = [];\n    for (let i = 0; i < batchTasks.length; i++) {\n      const task = batchTasks[i];\n      const taskName = `任务${i + 1}`;\n\n      if (!task.inputDir || !task.outputDir) {\n        message.error(`请完善 \"${taskName}\" 的配置`);\n        return;\n      }\n\n      const isValid = await validateBatchTask(task);\n      if (isValid) {\n        validTasks.push({...task, taskName});\n      }\n    }\n\n    if (validTasks.length === 0) {\n      message.error('没有有效的批量任务');\n      return;\n    }\n\n    setBatchLoading(true);\n\n    try {\n      // 调用批量分析API\n      const response = await dataCleaningAPI.batchAnalyze({\n        tasks: validTasks.map((task, index) => ({\n          customer: `任务${index + 1}`,\n          input_dir: task.inputDir,\n          output_dir: task.outputDir\n        }))\n      });\n\n      if (response.data.success) {\n        const batchId = response.data.batch_id;\n        message.success(`批量任务已启动，任务ID: ${batchId}`);\n\n        // 开始监控批量任务进度\n        monitorBatchProgress(batchId);\n      } else {\n        message.error('批量任务启动失败');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '批量分析失败');\n    } finally {\n      setBatchLoading(false);\n    }\n  };\n\n  const monitorBatchProgress = async (batchId: string) => {\n    const maxAttempts = 60; // 10分钟监控\n    let attempt = 0;\n\n    const checkProgress = async () => {\n      try {\n        const response = await dataCleaningAPI.getBatchStatus(batchId);\n\n        console.log('批量任务状态响应:', response.data); // 添加调试日志\n\n        if (response.data && response.data.success) {\n          const { status, progress, current_step, error } = response.data;\n\n          console.log(`批量任务状态: ${status}, 进度: ${progress}%`); // 添加调试日志\n\n          // 更新进度状态\n          setBatchProgress(prev => ({\n            ...prev,\n            [batchId]: progress || 0\n          }));\n\n          if (status === 'completed') {\n            message.success('批量分析完成！');\n            setBatchLoading(false);\n            console.log('批量任务成功完成');\n            return;\n          } else if (status === 'failed') {\n            const errorMsg = error || '未知错误';\n            message.error(`批量分析失败: ${errorMsg}`);\n            setBatchLoading(false);\n            console.error('批量任务失败:', errorMsg);\n            return;\n          }\n\n          // 如果任务还在进行中，继续监控\n          if (attempt < maxAttempts && (status === 'running' || status === 'pending' || !status)) {\n            attempt++;\n            setTimeout(checkProgress, 5000); // 5秒后再次检查\n          } else if (attempt >= maxAttempts) {\n            // 超时时不显示失败，而是提示用户手动检查\n            message.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');\n            setBatchLoading(false);\n            console.warn('批量任务监控超时');\n          }\n        } else {\n          // API响应格式问题，但不立即判定为失败\n          console.warn('批量任务状态API响应格式异常:', response.data);\n          if (attempt < maxAttempts) {\n            attempt++;\n            setTimeout(checkProgress, 5000);\n          } else {\n            message.warning('无法获取批量任务状态，请手动检查任务结果');\n            setBatchLoading(false);\n          }\n        }\n      } catch (error: any) {\n        console.error('监控批量任务进度失败:', error);\n\n        // 网络错误或API错误，增加重试次数\n        if (attempt < 10) { // 增加重试次数到10次\n          attempt++;\n          console.log(`批量任务监控重试 ${attempt}/10`);\n          setTimeout(checkProgress, 5000);\n        } else {\n          // 多次重试后仍失败，提示用户但不判定任务失败\n          message.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');\n          setBatchLoading(false);\n          console.error('批量任务监控最终失败');\n        }\n      }\n    };\n\n    // 开始监控\n    checkProgress();\n  };\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 处理模式选择 */}\n          <div>\n            <Text strong>处理模式：</Text>\n            <Radio.Group\n              value={processingMode}\n              onChange={(e) => setProcessingMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个目录分析</Radio>\n              <Radio value=\"batch\">批量目录分析</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 单个目录模式 */}\n          {processingMode === 'single' && (\n            <>\n              {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地目录文件</Radio>\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n              {/* 结果展示 */}\n              {result && (\n                <Alert\n                  message=\"处理完成\"\n                  description={\n                    <div>\n                      <p>处理结果：{result.message}</p>\n                      {result.output_file && <p>输出文件：{result.output_file}</p>}\n                      {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                      {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n              )}\n            </>\n          )}\n\n          {/* 批量目录模式 */}\n          {processingMode === 'batch' && (\n            <>\n              <div>\n                <Text strong>批量任务配置：</Text>\n                <div style={{ marginTop: 16 }}>\n                  {batchTasks.map((task, index) => (\n                    <Card\n                      key={task.id}\n                      size=\"small\"\n                      style={{ marginBottom: 16 }}\n                      title={`任务 ${index + 1}`}\n                      extra={\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          onClick={() => removeBatchTask(task.id)}\n                        />\n                      }\n                    >\n                      <Row gutter={16}>\n                        <Col span={12}>\n                          <Text strong>输入目录：</Text>\n                          <Input\n                            value={task.inputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'inputDir', e.target.value)}\n                            placeholder=\"例如：/data/input\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                        <Col span={12}>\n                          <Text strong>输出目录：</Text>\n                          <Input\n                            value={task.outputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'outputDir', e.target.value)}\n                            placeholder=\"例如：/data/output\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                      </Row>\n                      {task.fileCount !== undefined && (\n                        <div style={{ marginTop: 8 }}>\n                          <Text type=\"secondary\">\n                            检测到 {task.fileCount} 个TXT文件\n                          </Text>\n                        </div>\n                      )}\n                    </Card>\n                  ))}\n\n                  <Button\n                    type=\"dashed\"\n                    icon={<PlusOutlined />}\n                    onClick={addBatchTask}\n                    style={{ width: '100%', marginBottom: 16 }}\n                  >\n                    添加任务\n                  </Button>\n\n                  <Button\n                    type=\"primary\"\n                    size=\"large\"\n                    loading={batchLoading}\n                    disabled={batchTasks.length === 0}\n                    onClick={startBatchAnalysis}\n                    style={{ width: '100%' }}\n                  >\n                    {batchLoading ? '批量分析中...' : '开始批量分析'}\n                  </Button>\n\n                  {/* 批量任务进度显示 */}\n                  {batchLoading && Object.keys(batchProgress).length > 0 && (\n                    <div style={{ marginTop: 16 }}>\n                      <Text strong>批量任务进度：</Text>\n                      {Object.entries(batchProgress).map(([taskId, progress]) => (\n                        <div key={taskId} style={{ marginTop: 8 }}>\n                          <Progress\n                            percent={progress}\n                            status={progress === 100 ? 'success' : 'active'}\n                            format={(percent) => `${percent}%`}\n                          />\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </>\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,mBAAmB;AACnG,SAASC,eAAe,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGjB,UAAU;AAClC,MAAM;EAAEkB;AAAQ,CAAC,GAAGxB,MAAM;AAC1B,MAAM;EAAEyB;AAAO,CAAC,GAAGvB,MAAM;AAEzB,MAAMwB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAqB,QAAQ,CAAC;EAClF,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAM,IAAI,CAAC;;EAE/C;EACA,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAKxC,EAAE,CAAC;EACP,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAA0B,CAAC,CAAC,CAAC;;EAE/E;EACA,MAAM4D,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAG;MACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,QAAQ,EAAE,EAAE;MACZtB,SAAS,EAAE,EAAE;MACbuB,SAAS,EAAE;IACb,CAAC;IACDZ,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAEO,OAAO,CAAC,CAAC;EACzC,CAAC;EAED,MAAMO,eAAe,GAAGA,CAACN,EAAU,EAAEO,KAAa,EAAEC,KAAa,KAAK;IACpEf,aAAa,CAACD,UAAU,CAACiB,GAAG,CAACC,IAAI,IAC/BA,IAAI,CAACV,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGU,IAAI;MAAE,CAACH,KAAK,GAAGC;IAAM,CAAC,GAAGE,IACjD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,eAAe,GAAIX,EAAU,IAAK;IACtCP,aAAa,CAACD,UAAU,CAACoB,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACV,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMa,iBAAiB,GAAG,MAAOH,IAAS,IAAK;IAC7C,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMvD,eAAe,CAACwD,SAAS,CAACL,IAAI,CAACN,QAAQ,CAAC;MAC/D,MAAMY,KAAK,GAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,IAAI,EAAE;MACvC,MAAME,QAAQ,GAAGF,KAAK,CAACJ,MAAM,CAAEO,IAAY,IACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC;MAEDf,eAAe,CAACI,IAAI,CAACV,EAAE,EAAE,WAAW,EAAEkB,QAAQ,CAACI,MAAM,CAACnB,QAAQ,CAAC,CAAC,CAAC;MACjE,OAAOe,QAAQ,CAACI,MAAM,GAAG,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,QAAQb,IAAI,CAACN,QAAQ,KAAK,CAAC;MACzC,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC;IACA,MAAMC,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlC,UAAU,CAAC8B,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC1C,MAAMhB,IAAI,GAAGlB,UAAU,CAACkC,CAAC,CAAC;MAC1B,MAAMC,QAAQ,GAAG,KAAKD,CAAC,GAAG,CAAC,EAAE;MAE7B,IAAI,CAAChB,IAAI,CAACN,QAAQ,IAAI,CAACM,IAAI,CAAC5B,SAAS,EAAE;QACrC/B,OAAO,CAACwE,KAAK,CAAC,QAAQI,QAAQ,OAAO,CAAC;QACtC;MACF;MAEA,MAAMC,OAAO,GAAG,MAAMf,iBAAiB,CAACH,IAAI,CAAC;MAC7C,IAAIkB,OAAO,EAAE;QACXH,UAAU,CAACI,IAAI,CAAC;UAAC,GAAGnB,IAAI;UAAEiB;QAAQ,CAAC,CAAC;MACtC;IACF;IAEA,IAAIF,UAAU,CAACH,MAAM,KAAK,CAAC,EAAE;MAC3BvE,OAAO,CAACwE,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA5B,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMmB,QAAQ,GAAG,MAAMvD,eAAe,CAACuE,YAAY,CAAC;QAClDC,KAAK,EAAEN,UAAU,CAAChB,GAAG,CAAC,CAACC,IAAI,EAAEsB,KAAK,MAAM;UACtCC,QAAQ,EAAE,KAAKD,KAAK,GAAG,CAAC,EAAE;UAC1BE,SAAS,EAAExB,IAAI,CAACN,QAAQ;UACxB+B,UAAU,EAAEzB,IAAI,CAAC5B;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAIgC,QAAQ,CAACG,IAAI,CAACmB,OAAO,EAAE;QACzB,MAAMC,OAAO,GAAGvB,QAAQ,CAACG,IAAI,CAACqB,QAAQ;QACtCvF,OAAO,CAACqF,OAAO,CAAC,iBAAiBC,OAAO,EAAE,CAAC;;QAE3C;QACAE,oBAAoB,CAACF,OAAO,CAAC;MAC/B,CAAC,MAAM;QACLtF,OAAO,CAACwE,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAiB,eAAA,EAAAC,oBAAA;MACnB1F,OAAO,CAACwE,KAAK,CAAC,EAAAiB,eAAA,GAAAjB,KAAK,CAACT,QAAQ,cAAA0B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvB,IAAI,cAAAwB,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACR/C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4C,oBAAoB,GAAG,MAAOF,OAAe,IAAK;IACtD,MAAMM,WAAW,GAAG,EAAE,CAAC,CAAC;IACxB,IAAIC,OAAO,GAAG,CAAC;IAEf,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAM/B,QAAQ,GAAG,MAAMvD,eAAe,CAACuF,cAAc,CAACT,OAAO,CAAC;QAE9DU,OAAO,CAACC,GAAG,CAAC,WAAW,EAAElC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC;;QAEzC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACmB,OAAO,EAAE;UAC1C,MAAM;YAAEa,MAAM;YAAE7D,QAAQ;YAAE8D,YAAY;YAAE3B;UAAM,CAAC,GAAGT,QAAQ,CAACG,IAAI;UAE/D8B,OAAO,CAACC,GAAG,CAAC,WAAWC,MAAM,SAAS7D,QAAQ,GAAG,CAAC,CAAC,CAAC;;UAEpD;UACAS,gBAAgB,CAACsD,IAAI,KAAK;YACxB,GAAGA,IAAI;YACP,CAACd,OAAO,GAAGjD,QAAQ,IAAI;UACzB,CAAC,CAAC,CAAC;UAEH,IAAI6D,MAAM,KAAK,WAAW,EAAE;YAC1BlG,OAAO,CAACqF,OAAO,CAAC,SAAS,CAAC;YAC1BzC,eAAe,CAAC,KAAK,CAAC;YACtBoD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;YACvB;UACF,CAAC,MAAM,IAAIC,MAAM,KAAK,QAAQ,EAAE;YAC9B,MAAMG,QAAQ,GAAG7B,KAAK,IAAI,MAAM;YAChCxE,OAAO,CAACwE,KAAK,CAAC,WAAW6B,QAAQ,EAAE,CAAC;YACpCzD,eAAe,CAAC,KAAK,CAAC;YACtBoD,OAAO,CAACxB,KAAK,CAAC,SAAS,EAAE6B,QAAQ,CAAC;YAClC;UACF;;UAEA;UACA,IAAIR,OAAO,GAAGD,WAAW,KAAKM,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,IAAI,CAACA,MAAM,CAAC,EAAE;YACtFL,OAAO,EAAE;YACTS,UAAU,CAACR,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;UACnC,CAAC,MAAM,IAAID,OAAO,IAAID,WAAW,EAAE;YACjC;YACA5F,OAAO,CAACuG,OAAO,CAAC,+BAA+B,CAAC;YAChD3D,eAAe,CAAC,KAAK,CAAC;YACtBoD,OAAO,CAACQ,IAAI,CAAC,UAAU,CAAC;UAC1B;QACF,CAAC,MAAM;UACL;UACAR,OAAO,CAACQ,IAAI,CAAC,kBAAkB,EAAEzC,QAAQ,CAACG,IAAI,CAAC;UAC/C,IAAI2B,OAAO,GAAGD,WAAW,EAAE;YACzBC,OAAO,EAAE;YACTS,UAAU,CAACR,aAAa,EAAE,IAAI,CAAC;UACjC,CAAC,MAAM;YACL9F,OAAO,CAACuG,OAAO,CAAC,sBAAsB,CAAC;YACvC3D,eAAe,CAAC,KAAK,CAAC;UACxB;QACF;MACF,CAAC,CAAC,OAAO4B,KAAU,EAAE;QACnBwB,OAAO,CAACxB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;;QAEnC;QACA,IAAIqB,OAAO,GAAG,EAAE,EAAE;UAAE;UAClBA,OAAO,EAAE;UACTG,OAAO,CAACC,GAAG,CAAC,YAAYJ,OAAO,KAAK,CAAC;UACrCS,UAAU,CAACR,aAAa,EAAE,IAAI,CAAC;QACjC,CAAC,MAAM;UACL;UACA9F,OAAO,CAACuG,OAAO,CAAC,iCAAiC,CAAC;UAClD3D,eAAe,CAAC,KAAK,CAAC;UACtBoD,OAAO,CAACxB,KAAK,CAAC,YAAY,CAAC;QAC7B;MACF;IACF,CAAC;;IAED;IACAsB,aAAa,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAMW,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAChF,UAAU,EAAE;IAEjBW,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMvD,eAAe,CAACwD,SAAS,CAACvC,UAAU,CAAC;MAC5D,MAAMwC,KAAK,GAAGF,QAAQ,CAACG,IAAI,CAACD,KAAK,IAAI,EAAE;MACvCrC,iBAAiB,CAACqC,KAAK,CAAC;;MAExB;MACA,MAAME,QAAQ,GAAGF,KAAK,CAACJ,MAAM,CAAEO,IAAY,IACzCA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CACpC,CAAC;MAED,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;QACvBzC,gBAAgB,CAACqC,QAAQ,CAAC;QAC1BnE,OAAO,CAACqF,OAAO,CAAC,SAASlB,QAAQ,CAACI,MAAM,SAAS,CAAC;MACpD,CAAC,MAAM;QACLzC,gBAAgB,CAAC,EAAE,CAAC;QACpB,IAAImC,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;UACpBvE,OAAO,CAACuG,OAAO,CAAC,cAAc,CAAC;QACjC;MACF;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACnB3G,OAAO,CAACwE,KAAK,CAAC,EAAAkC,gBAAA,GAAAlC,KAAK,CAACT,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBhB,MAAM,KAAI,UAAU,CAAC;MACzD/D,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACRM,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAhD,SAAS,CAAC,MAAM;IACd,IAAI+B,UAAU,KAAK,OAAO,IAAIM,UAAU,IAAIA,UAAU,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAAE;MACnE,MAAMqC,KAAK,GAAGN,UAAU,CAAC,MAAM;QAC7BG,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMI,YAAY,CAACD,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACzF,UAAU,EAAEM,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMqF,WAAW,GAAG;IAClBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IAAE;IAC3BC,QAAQ,EAAGC,IAAS,IAAK;MACvB5F,gBAAgB,CAAC4F,IAAI,CAACC,QAAQ,CAAC;IACjC,CAAC;IACDC,MAAM,EAAGC,CAAM,IAAK;MAClBvB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEsB,CAAC,CAACC,YAAY,CAACvD,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMwD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC;IACA,IAAItG,UAAU,KAAK,QAAQ,IAAII,aAAa,CAACgD,MAAM,KAAK,CAAC,EAAE;MACzDvE,OAAO,CAACwE,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAIrD,UAAU,KAAK,OAAO,KAAK,CAACM,UAAU,IAAII,aAAa,CAAC0C,MAAM,KAAK,CAAC,CAAC,EAAE;MACzEvE,OAAO,CAACwE,KAAK,CAAC,mBAAmB,CAAC;MAClC;IACF;IAEAtC,UAAU,CAAC,IAAI,CAAC;IAChBI,WAAW,CAAC,CAAC,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,IAAIuB,QAAQ;MAEZ,IAAI5C,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMuG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BpG,aAAa,CAACqG,OAAO,CAAExD,IAAI,IAAK;UAC9BsD,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAEzD,IAAI,CAAC0D,aAAa,CAAC;QAC9C,CAAC,CAAC;QACFJ,QAAQ,CAACG,MAAM,CAAC,YAAY,EAAE9F,SAAS,CAAC;;QAExC;QACA,MAAMgG,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC1F,WAAW,CAAE8D,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACd6B,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAO3B,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPrC,QAAQ,GAAG,MAAMvD,eAAe,CAAC0H,SAAS,CAACR,QAAQ,CAAC;QACpDO,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMA,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC1F,WAAW,CAAE8D,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACd6B,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAO3B,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,EAAE;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;QAEPrC,QAAQ,GAAG,MAAMvD,eAAe,CAAC2H,cAAc,CAAC;UAC9CC,WAAW,EAAE3G,UAAU;UACvB4G,cAAc,EAAExG,aAAa;UAC7BuD,UAAU,EAAErD;QACd,CAAC,CAAC;QACFkG,aAAa,CAACF,gBAAgB,CAAC;MACjC;MAEAzF,WAAW,CAAC,GAAG,CAAC;MAChBE,SAAS,CAACuB,QAAQ,CAACG,IAAI,CAAC;MACxBlE,OAAO,CAACqF,OAAO,CAAC,SAAS,CAAC;IAE5B,CAAC,CAAC,OAAOb,KAAU,EAAE;MAAA,IAAA8D,gBAAA,EAAAC,qBAAA;MACnBvI,OAAO,CAACwE,KAAK,CAAC,EAAA8D,gBAAA,GAAA9D,KAAK,CAACT,QAAQ,cAAAuE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpE,IAAI,cAAAqE,qBAAA,uBAApBA,qBAAA,CAAsB5C,MAAM,KAAI,QAAQ,CAAC;IACzD,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIrH,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOI,aAAa,CAACgD,MAAM,GAAG,CAAC;IACjC,CAAC,MAAM;MACL,OAAO9C,UAAU,IAAII,aAAa,CAAC0C,MAAM,GAAG,CAAC;IAC/C;EACF,CAAC;EAED,oBACE7D,OAAA;IAAA+H,QAAA,gBACE/H,OAAA,CAACG,KAAK;MAAC6H,KAAK,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAAL,QAAA,EAAC;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAClGxI,OAAA,CAACI,IAAI;MAACqI,IAAI,EAAC,WAAW;MAAAV,QAAA,EAAC;IAEvB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxI,OAAA,CAACX,OAAO;MAAAgJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXxI,OAAA,CAACrB,IAAI;MAAC+J,KAAK,EAAC,0BAAM;MAACC,SAAS,EAAC,eAAe;MAAAZ,QAAA,eAC1C/H,OAAA,CAACZ,KAAK;QAACwJ,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACZ,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,gBAEhE/H,OAAA;UAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;YAAC2I,MAAM;YAAAhB,QAAA,EAAC;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxI,OAAA,CAACpB,KAAK,CAACoK,KAAK;YACVjG,KAAK,EAAEpC,cAAe;YACtB8F,QAAQ,EAAGI,CAAC,IAAKjG,iBAAiB,CAACiG,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;YACnDkF,KAAK,EAAE;cAAEiB,SAAS,EAAE;YAAE,CAAE;YAAAnB,QAAA,gBAExB/H,OAAA,CAACpB,KAAK;cAACmE,KAAK,EAAC,QAAQ;cAAAgF,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxI,OAAA,CAACpB,KAAK;cAACmE,KAAK,EAAC,OAAO;cAAAgF,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL7H,cAAc,KAAK,QAAQ,iBAC1BX,OAAA,CAAAE,SAAA;UAAA6H,QAAA,gBAEF/H,OAAA;YAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;cAAC2I,MAAM;cAAAhB,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxI,OAAA,CAACpB,KAAK,CAACoK,KAAK;cACVjG,KAAK,EAAEtC,UAAW;cAClBgG,QAAQ,EAAGI,CAAC,IAAKnG,aAAa,CAACmG,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;cAC/CkF,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAAnB,QAAA,gBAExB/H,OAAA,CAACpB,KAAK;gBAACmE,KAAK,EAAC,OAAO;gBAAAgF,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrCxI,OAAA,CAACpB,KAAK;gBAACmE,KAAK,EAAC,QAAQ;gBAAAgF,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAGL/H,UAAU,KAAK,OAAO,iBACrBT,OAAA,CAACZ,KAAK;YAACwJ,SAAS,EAAC,UAAU;YAACX,KAAK,EAAE;cAAEa,KAAK,EAAE;YAAO,CAAE;YAAAf,QAAA,gBACnD/H,OAAA;cAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;gBAAC2I,MAAM;gBAAAhB,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3BxI,OAAA,CAAClB,KAAK,CAACkK,KAAK;gBAACG,OAAO;gBAAClB,KAAK,EAAE;kBAAEiB,SAAS,EAAE,CAAC;kBAAEE,OAAO,EAAE;gBAAO,CAAE;gBAAArB,QAAA,gBAC5D/H,OAAA,CAAClB,KAAK;kBACJiE,KAAK,EAAEhC,UAAW;kBAClB0F,QAAQ,EAAGI,CAAC,IAAK7F,aAAa,CAAC6F,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;kBAC/CsG,WAAW,EAAC,gDAAsC;kBAClDpB,KAAK,EAAE;oBAAEqB,IAAI,EAAE;kBAAE;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFxI,OAAA,CAAChB,MAAM;kBACLyJ,IAAI,EAAC,SAAS;kBACdc,OAAO,EAAExD,eAAgB;kBACzBxE,OAAO,EAAEE,YAAa;kBACtB+H,QAAQ,EAAE,CAACzI,UAAW;kBACtBkH,KAAK,EAAE;oBAAEwB,UAAU,EAAE;kBAAE,CAAE;kBAAA1B,QAAA,EAC1B;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAENxI,OAAA;cAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;gBAAC2I,MAAM;gBAAAhB,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBxI,OAAA,CAACT,IAAI;gBAACmK,QAAQ,EAAEjI,YAAa;gBAAAsG,QAAA,eAC3B/H,OAAA,CAACjB,MAAM;kBACL4K,IAAI,EAAC,UAAU;kBACf5G,KAAK,EAAE5B,aAAc;kBACrBsF,QAAQ,EAAErF,gBAAiB;kBAC3BiI,WAAW,EAAC,mCAAU;kBACtBpB,KAAK,EAAE;oBAAEa,KAAK,EAAE,MAAM;oBAAEI,SAAS,EAAE;kBAAE,CAAE;kBACvC3H,OAAO,EAAEE,YAAa;kBAAAsG,QAAA,EAErB9G,cAAc,CAAC+B,GAAG,CAAEU,IAAI,iBACvB1D,OAAA,CAACM,MAAM;oBAAYyC,KAAK,EAAEW,IAAK;oBAAAqE,QAAA,EAC5BrE;kBAAI,GADMA,IAAI;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAGA/H,UAAU,KAAK,QAAQ,iBACtBT,OAAA;YAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;cAAC2I,MAAM;cAAAhB,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBxI,OAAA,CAACK,OAAO;cAAA,GAAK+F,WAAW;cAAE6B,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAAnB,QAAA,gBAChD/H,OAAA;gBAAG2I,SAAS,EAAC,sBAAsB;gBAAAZ,QAAA,eACjC/H,OAAA,CAACN,aAAa;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACJxI,OAAA;gBAAG2I,SAAS,EAAC,iBAAiB;gBAAAZ,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDxI,OAAA;gBAAG2I,SAAS,EAAC,iBAAiB;gBAAAZ,QAAA,EAAC;cAE/B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,eAGDxI,OAAA;YAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;cAAC2I,MAAM;cAAAhB,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxI,OAAA,CAAClB,KAAK;cACJiE,KAAK,EAAE1B,SAAU;cACjBoF,QAAQ,EAAGI,CAAC,IAAKvF,YAAY,CAACuF,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;cAC9CsG,WAAW,EAAC,4BAAkB;cAC9BpB,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxI,OAAA,CAAChB,MAAM;YACLyJ,IAAI,EAAC,SAAS;YACdI,IAAI,EAAC,OAAO;YACZe,IAAI,eAAE5J,OAAA,CAACL,kBAAkB;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7Be,OAAO,EAAExC,eAAgB;YACzBxF,OAAO,EAAEA,OAAQ;YACjBiI,QAAQ,EAAE,CAAC1B,WAAW,CAAC,CAAE;YACzBa,SAAS,EAAC,eAAe;YAAAZ,QAAA,EAExBxG,OAAO,GAAG,SAAS,GAAG;UAAQ;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,EAGRjH,OAAO,iBACNvB,OAAA;YAAK2I,SAAS,EAAC,kBAAkB;YAAAZ,QAAA,gBAC/B/H,OAAA,CAACI,IAAI;cAAA2H,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBxI,OAAA,CAACd,QAAQ;cAAC2K,OAAO,EAAElI,QAAS;cAAC6D,MAAM,EAAC;YAAQ;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EAGI3G,MAAM,iBACL7B,OAAA,CAACf,KAAK;YACJK,OAAO,EAAC,0BAAM;YACdwK,WAAW,eACT9J,OAAA;cAAA+H,QAAA,gBACE/H,OAAA;gBAAA+H,QAAA,GAAG,gCAAK,EAAClG,MAAM,CAACvC,OAAO;cAAA;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC3B3G,MAAM,CAACkI,WAAW,iBAAI/J,OAAA;gBAAA+H,QAAA,GAAG,gCAAK,EAAClG,MAAM,CAACkI,WAAW;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACtD3G,MAAM,CAACmI,eAAe,iBAAIhK,OAAA;gBAAA+H,QAAA,GAAG,4CAAO,EAAClG,MAAM,CAACmI,eAAe;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAChE3G,MAAM,CAACoI,UAAU,iBAAIjK,OAAA;gBAAA+H,QAAA,GAAG,0BAAI,EAAClG,MAAM,CAACoI,UAAU;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CACN;YACDC,IAAI,EAAC,SAAS;YACdyB,QAAQ;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACF;QAAA,eACD,CACH,EAGA7H,cAAc,KAAK,OAAO,iBACzBX,OAAA,CAAAE,SAAA;UAAA6H,QAAA,eACE/H,OAAA;YAAA+H,QAAA,gBACE/H,OAAA,CAACI,IAAI;cAAC2I,MAAM;cAAAhB,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3BxI,OAAA;cAAKiI,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAG,CAAE;cAAAnB,QAAA,GAC3BhG,UAAU,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEsB,KAAK,kBAC1BvE,OAAA,CAACrB,IAAI;gBAEHkK,IAAI,EAAC,OAAO;gBACZZ,KAAK,EAAE;kBAAEG,YAAY,EAAE;gBAAG,CAAE;gBAC5BM,KAAK,EAAE,MAAMnE,KAAK,GAAG,CAAC,EAAG;gBACzB4F,KAAK,eACHnK,OAAA,CAAChB,MAAM;kBACLyJ,IAAI,EAAC,MAAM;kBACX2B,MAAM;kBACNR,IAAI,eAAE5J,OAAA,CAACJ,cAAc;oBAAAyI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBe,OAAO,EAAEA,CAAA,KAAMrG,eAAe,CAACD,IAAI,CAACV,EAAE;gBAAE;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CACF;gBAAAT,QAAA,gBAED/H,OAAA,CAACR,GAAG;kBAAC6K,MAAM,EAAE,EAAG;kBAAAtC,QAAA,gBACd/H,OAAA,CAACP,GAAG;oBAAC6K,IAAI,EAAE,EAAG;oBAAAvC,QAAA,gBACZ/H,OAAA,CAACI,IAAI;sBAAC2I,MAAM;sBAAAhB,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzBxI,OAAA,CAAClB,KAAK;sBACJiE,KAAK,EAAEE,IAAI,CAACN,QAAS;sBACrB8D,QAAQ,EAAGI,CAAC,IAAKhE,eAAe,CAACI,IAAI,CAACV,EAAE,EAAE,UAAU,EAAEsE,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;sBACtEsG,WAAW,EAAC,+BAAgB;sBAC5BpB,KAAK,EAAE;wBAAEiB,SAAS,EAAE;sBAAE;oBAAE;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNxI,OAAA,CAACP,GAAG;oBAAC6K,IAAI,EAAE,EAAG;oBAAAvC,QAAA,gBACZ/H,OAAA,CAACI,IAAI;sBAAC2I,MAAM;sBAAAhB,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzBxI,OAAA,CAAClB,KAAK;sBACJiE,KAAK,EAAEE,IAAI,CAAC5B,SAAU;sBACtBoF,QAAQ,EAAGI,CAAC,IAAKhE,eAAe,CAACI,IAAI,CAACV,EAAE,EAAE,WAAW,EAAEsE,CAAC,CAACoC,MAAM,CAAClG,KAAK,CAAE;sBACvEsG,WAAW,EAAC,gCAAiB;sBAC7BpB,KAAK,EAAE;wBAAEiB,SAAS,EAAE;sBAAE;oBAAE;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLvF,IAAI,CAACL,SAAS,KAAK2H,SAAS,iBAC3BvK,OAAA;kBAAKiI,KAAK,EAAE;oBAAEiB,SAAS,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,eAC3B/H,OAAA,CAACI,IAAI;oBAACqI,IAAI,EAAC,WAAW;oBAAAV,QAAA,GAAC,qBACjB,EAAC9E,IAAI,CAACL,SAAS,EAAC,wBACtB;kBAAA;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN;cAAA,GAvCIvF,IAAI,CAACV,EAAE;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwCR,CACP,CAAC,eAEFxI,OAAA,CAAChB,MAAM;gBACLyJ,IAAI,EAAC,QAAQ;gBACbmB,IAAI,eAAE5J,OAAA,CAACH,YAAY;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBe,OAAO,EAAElH,YAAa;gBACtB4F,KAAK,EAAE;kBAAEa,KAAK,EAAE,MAAM;kBAAEV,YAAY,EAAE;gBAAG,CAAE;gBAAAL,QAAA,EAC5C;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETxI,OAAA,CAAChB,MAAM;gBACLyJ,IAAI,EAAC,SAAS;gBACdI,IAAI,EAAC,OAAO;gBACZtH,OAAO,EAAEU,YAAa;gBACtBuH,QAAQ,EAAEzH,UAAU,CAAC8B,MAAM,KAAK,CAAE;gBAClC0F,OAAO,EAAExF,kBAAmB;gBAC5BkE,KAAK,EAAE;kBAAEa,KAAK,EAAE;gBAAO,CAAE;gBAAAf,QAAA,EAExB9F,YAAY,GAAG,UAAU,GAAG;cAAQ;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EAGRvG,YAAY,IAAIuI,MAAM,CAACC,IAAI,CAACtI,aAAa,CAAC,CAAC0B,MAAM,GAAG,CAAC,iBACpD7D,OAAA;gBAAKiI,KAAK,EAAE;kBAAEiB,SAAS,EAAE;gBAAG,CAAE;gBAAAnB,QAAA,gBAC5B/H,OAAA,CAACI,IAAI;kBAAC2I,MAAM;kBAAAhB,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC1BgC,MAAM,CAACE,OAAO,CAACvI,aAAa,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC2H,MAAM,EAAEhJ,QAAQ,CAAC,kBACpD3B,OAAA;kBAAkBiI,KAAK,EAAE;oBAAEiB,SAAS,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,eACxC/H,OAAA,CAACd,QAAQ;oBACP2K,OAAO,EAAElI,QAAS;oBAClB6D,MAAM,EAAE7D,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG,QAAS;oBAChDiJ,MAAM,EAAGf,OAAO,IAAK,GAAGA,OAAO;kBAAI;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC,GALMmC,MAAM;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMX,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,gBACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChI,EAAA,CA5jBID,gBAA0B;AAAAsK,EAAA,GAA1BtK,gBAA0B;AA8jBhC,eAAeA,gBAAgB;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}