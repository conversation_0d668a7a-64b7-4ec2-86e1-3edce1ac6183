{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonAvatar = function SkeletonAvatar(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    _props$shape = props.shape,\n    shape = _props$shape === void 0 ? 'circle' : _props$shape,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls', 'className']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-avatar\"),\n    shape: shape,\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonAvatar;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "omit", "React", "ConfigContext", "Element", "SkeletonAvatar", "props", "customizePrefixCls", "prefixCls", "className", "active", "_props$shape", "shape", "_props$size", "size", "_React$useContext", "useContext", "getPrefixCls", "otherProps", "cls", "concat", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Avatar.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport Element from './Element';\nvar SkeletonAvatar = function SkeletonAvatar(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    active = props.active,\n    _props$shape = props.shape,\n    shape = _props$shape === void 0 ? 'circle' : _props$shape,\n    _props$size = props.size,\n    size = _props$size === void 0 ? 'default' : _props$size;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var otherProps = omit(props, ['prefixCls', 'className']);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(Element, _extends({\n    prefixCls: \"\".concat(prefixCls, \"-avatar\"),\n    shape: shape,\n    size: size\n  }, otherProps)));\n};\nexport default SkeletonAvatar;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,YAAY,GAAGL,KAAK,CAACM,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,YAAY;IACzDE,WAAW,GAAGP,KAAK,CAACQ,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,WAAW;EACzD,IAAIE,iBAAiB,GAAGb,KAAK,CAACc,UAAU,CAACb,aAAa,CAAC;IACrDc,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIT,SAAS,GAAGS,YAAY,CAAC,UAAU,EAAEV,kBAAkB,CAAC;EAC5D,IAAIW,UAAU,GAAGjB,IAAI,CAACK,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EACxD,IAAIa,GAAG,GAAGnB,UAAU,CAACQ,SAAS,EAAE,EAAE,CAACY,MAAM,CAACZ,SAAS,EAAE,UAAU,CAAC,EAAET,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC,EAAEE,MAAM,CAAC,EAAED,SAAS,CAAC;EAC1I,OAAO,aAAaP,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7CZ,SAAS,EAAEU;EACb,CAAC,EAAE,aAAajB,KAAK,CAACmB,aAAa,CAACjB,OAAO,EAAEN,QAAQ,CAAC;IACpDU,SAAS,EAAE,EAAE,CAACY,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC;IAC1CI,KAAK,EAAEA,KAAK;IACZE,IAAI,EAAEA;EACR,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}