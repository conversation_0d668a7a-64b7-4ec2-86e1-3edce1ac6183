{"ast": null, "code": "/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react'; // We will never use default, here only to fix TypeScript warning\n\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;", "map": {"version": 3, "names": ["React", "MentionsContext", "createContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-mentions/es/MentionsContext.js"], "sourcesContent": ["/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react'; // We will never use default, here only to fix TypeScript warning\n\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO,CAAC,CAAC;;AAEhC,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}