{"ast": null, "code": "var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n    index = assocIndexOf(data, key);\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\nmodule.exports = listCacheSet;", "map": {"version": 3, "names": ["assocIndexOf", "require", "listCacheSet", "key", "value", "data", "__data__", "index", "size", "push", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_listCacheSet.js"], "sourcesContent": ["var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\nmodule.exports = listCacheSet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAChC,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;IACpBC,KAAK,GAAGP,YAAY,CAACK,IAAI,EAAEF,GAAG,CAAC;EAEnC,IAAII,KAAK,GAAG,CAAC,EAAE;IACb,EAAE,IAAI,CAACC,IAAI;IACXH,IAAI,CAACI,IAAI,CAAC,CAACN,GAAG,EAAEC,KAAK,CAAC,CAAC;EACzB,CAAC,MAAM;IACLC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGH,KAAK;EACxB;EACA,OAAO,IAAI;AACb;AAEAM,MAAM,CAACC,OAAO,GAAGT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}