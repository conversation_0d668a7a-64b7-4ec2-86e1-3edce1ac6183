{"ast": null, "code": "import * as React from 'react';\nvar ExpandedRowContext = /*#__PURE__*/React.createContext(null);\nexport default ExpandedRowContext;", "map": {"version": 3, "names": ["React", "ExpandedRowContext", "createContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/context/ExpandedRowContext.js"], "sourcesContent": ["import * as React from 'react';\nvar ExpandedRowContext = /*#__PURE__*/React.createContext(null);\nexport default ExpandedRowContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC/D,eAAeD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}