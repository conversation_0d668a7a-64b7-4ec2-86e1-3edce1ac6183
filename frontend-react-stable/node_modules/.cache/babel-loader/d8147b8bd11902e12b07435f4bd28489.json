{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _index = require(\"../../../lib-vendor/d3-path/src/index.js\");\nvar _array = _interopRequireDefault(require(\"./array.js\"));\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nvar _linear = _interopRequireDefault(require(\"./curve/linear.js\"));\nvar _line = _interopRequireDefault(require(\"./line.js\"));\nvar _point = require(\"./point.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _default(x0, y0, y1) {\n  var x1 = null,\n    defined = (0, _constant.default)(true),\n    context = null,\n    curve = _linear.default,\n    output = null;\n  x0 = typeof x0 === \"function\" ? x0 : x0 === undefined ? _point.x : (0, _constant.default)(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : y0 === undefined ? (0, _constant.default)(0) : (0, _constant.default)(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : y1 === undefined ? _point.y : (0, _constant.default)(+y1);\n  function area(data) {\n    var i,\n      j,\n      k,\n      n = (data = (0, _array.default)(data)).length,\n      d,\n      defined0 = false,\n      buffer,\n      x0z = new Array(n),\n      y0z = new Array(n);\n    if (context == null) output = curve(buffer = (0, _index.path)());\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n  function arealine() {\n    return (0, _line.default)().defined(defined).curve(curve).context(context);\n  }\n  area.x = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), x1 = null, area) : x0;\n  };\n  area.x0 = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : x0;\n  };\n  area.x1 = function (_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : x1;\n  };\n  area.y = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), y1 = null, area) : y0;\n  };\n  area.y0 = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : y0;\n  };\n  area.y1 = function (_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : y1;\n  };\n  area.lineX0 = area.lineY0 = function () {\n    return arealine().x(x0).y(y0);\n  };\n  area.lineY1 = function () {\n    return arealine().x(x0).y(y1);\n  };\n  area.lineX1 = function () {\n    return arealine().x(x1).y(y0);\n  };\n  area.defined = function (_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : (0, _constant.default)(!!_), area) : defined;\n  };\n  area.curve = function (_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n  area.context = function (_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n  return area;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_index", "require", "_array", "_interopRequireDefault", "_constant", "_linear", "_line", "_point", "obj", "__esModule", "x0", "y0", "y1", "x1", "defined", "context", "curve", "output", "undefined", "x", "y", "area", "data", "i", "j", "k", "n", "length", "d", "defined0", "buffer", "x0z", "Array", "y0z", "path", "areaStart", "lineStart", "lineEnd", "point", "areaEnd", "arealine", "_", "arguments", "lineX0", "lineY0", "lineY1", "lineX1"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/area.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _index = require(\"../../../lib-vendor/d3-path/src/index.js\");\n\nvar _array = _interopRequireDefault(require(\"./array.js\"));\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nvar _linear = _interopRequireDefault(require(\"./curve/linear.js\"));\n\nvar _line = _interopRequireDefault(require(\"./line.js\"));\n\nvar _point = require(\"./point.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _default(x0, y0, y1) {\n  var x1 = null,\n      defined = (0, _constant.default)(true),\n      context = null,\n      curve = _linear.default,\n      output = null;\n  x0 = typeof x0 === \"function\" ? x0 : x0 === undefined ? _point.x : (0, _constant.default)(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : y0 === undefined ? (0, _constant.default)(0) : (0, _constant.default)(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : y1 === undefined ? _point.y : (0, _constant.default)(+y1);\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = (data = (0, _array.default)(data)).length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n    if (context == null) output = curve(buffer = (0, _index.path)());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return (0, _line.default)().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : x0;\n  };\n\n  area.x1 = function (_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : x1;\n  };\n\n  area.y = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : y0;\n  };\n\n  area.y1 = function (_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : (0, _constant.default)(+_), area) : y1;\n  };\n\n  area.lineX0 = area.lineY0 = function () {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function () {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function () {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function (_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : (0, _constant.default)(!!_), area) : defined;\n  };\n\n  area.curve = function (_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function (_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,MAAM,GAAGC,OAAO,CAAC,0CAA0C,CAAC;AAEhE,IAAIC,MAAM,GAAGC,sBAAsB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,IAAIG,SAAS,GAAGD,sBAAsB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAII,OAAO,GAAGF,sBAAsB,CAACF,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAElE,IAAIK,KAAK,GAAGH,sBAAsB,CAACF,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,IAAIM,MAAM,GAAGN,OAAO,CAAC,YAAY,CAAC;AAElC,SAASE,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,SAAST,QAAQA,CAACW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5B,IAAIC,EAAE,GAAG,IAAI;IACTC,OAAO,GAAG,CAAC,CAAC,EAAEV,SAAS,CAACN,OAAO,EAAE,IAAI,CAAC;IACtCiB,OAAO,GAAG,IAAI;IACdC,KAAK,GAAGX,OAAO,CAACP,OAAO;IACvBmB,MAAM,GAAG,IAAI;EACjBP,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAGA,EAAE,KAAKQ,SAAS,GAAGX,MAAM,CAACY,CAAC,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACN,OAAO,EAAE,CAACY,EAAE,CAAC;EAC9FC,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAGA,EAAE,KAAKO,SAAS,GAAG,CAAC,CAAC,EAAEd,SAAS,CAACN,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEM,SAAS,CAACN,OAAO,EAAE,CAACa,EAAE,CAAC;EAC/GC,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAGA,EAAE,KAAKM,SAAS,GAAGX,MAAM,CAACa,CAAC,GAAG,CAAC,CAAC,EAAEhB,SAAS,CAACN,OAAO,EAAE,CAACc,EAAE,CAAC;EAE9F,SAASS,IAAIA,CAACC,IAAI,EAAE;IAClB,IAAIC,CAAC;MACDC,CAAC;MACDC,CAAC;MACDC,CAAC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC,EAAEpB,MAAM,CAACJ,OAAO,EAAEwB,IAAI,CAAC,EAAEK,MAAM;MAC7CC,CAAC;MACDC,QAAQ,GAAG,KAAK;MAChBC,MAAM;MACNC,GAAG,GAAG,IAAIC,KAAK,CAACN,CAAC,CAAC;MAClBO,GAAG,GAAG,IAAID,KAAK,CAACN,CAAC,CAAC;IACtB,IAAIX,OAAO,IAAI,IAAI,EAAEE,MAAM,GAAGD,KAAK,CAACc,MAAM,GAAG,CAAC,CAAC,EAAE9B,MAAM,CAACkC,IAAI,EAAE,CAAC,CAAC;IAEhE,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACvB,IAAI,EAAEA,CAAC,GAAGG,CAAC,IAAIZ,OAAO,CAACc,CAAC,GAAGN,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,CAAC,KAAKO,QAAQ,EAAE;QAC1D,IAAIA,QAAQ,GAAG,CAACA,QAAQ,EAAE;UACxBL,CAAC,GAAGD,CAAC;UACLN,MAAM,CAACkB,SAAS,CAAC,CAAC;UAClBlB,MAAM,CAACmB,SAAS,CAAC,CAAC;QACpB,CAAC,MAAM;UACLnB,MAAM,CAACoB,OAAO,CAAC,CAAC;UAChBpB,MAAM,CAACmB,SAAS,CAAC,CAAC;UAElB,KAAKX,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAID,CAAC,EAAE,EAAEC,CAAC,EAAE;YAC3BR,MAAM,CAACqB,KAAK,CAACP,GAAG,CAACN,CAAC,CAAC,EAAEQ,GAAG,CAACR,CAAC,CAAC,CAAC;UAC9B;UAEAR,MAAM,CAACoB,OAAO,CAAC,CAAC;UAChBpB,MAAM,CAACsB,OAAO,CAAC,CAAC;QAClB;MACF;MAEA,IAAIV,QAAQ,EAAE;QACZE,GAAG,CAACR,CAAC,CAAC,GAAG,CAACb,EAAE,CAACkB,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,EAAEW,GAAG,CAACV,CAAC,CAAC,GAAG,CAACZ,EAAE,CAACiB,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC;QAClDL,MAAM,CAACqB,KAAK,CAACzB,EAAE,GAAG,CAACA,EAAE,CAACe,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGS,GAAG,CAACR,CAAC,CAAC,EAAEX,EAAE,GAAG,CAACA,EAAE,CAACgB,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGW,GAAG,CAACV,CAAC,CAAC,CAAC;MAC5E;IACF;IAEA,IAAIO,MAAM,EAAE,OAAOb,MAAM,GAAG,IAAI,EAAEa,MAAM,GAAG,EAAE,IAAI,IAAI;EACvD;EAEA,SAASU,QAAQA,CAAA,EAAG;IAClB,OAAO,CAAC,CAAC,EAAElC,KAAK,CAACR,OAAO,EAAE,CAAC,CAACgB,OAAO,CAACA,OAAO,CAAC,CAACE,KAAK,CAACA,KAAK,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;EAC5E;EAEAM,IAAI,CAACF,CAAC,GAAG,UAAUsB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACf,MAAM,IAAIjB,EAAE,GAAG,OAAO+B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAE5B,EAAE,GAAG,IAAI,EAAEQ,IAAI,IAAIX,EAAE;EACjH,CAAC;EAEDW,IAAI,CAACX,EAAE,GAAG,UAAU+B,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACf,MAAM,IAAIjB,EAAE,GAAG,OAAO+B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAEpB,IAAI,IAAIX,EAAE;EACtG,CAAC;EAEDW,IAAI,CAACR,EAAE,GAAG,UAAU4B,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACf,MAAM,IAAId,EAAE,GAAG4B,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAEpB,IAAI,IAAIR,EAAE;EACzH,CAAC;EAEDQ,IAAI,CAACD,CAAC,GAAG,UAAUqB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACf,MAAM,IAAIhB,EAAE,GAAG,OAAO8B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAE7B,EAAE,GAAG,IAAI,EAAES,IAAI,IAAIV,EAAE;EACjH,CAAC;EAEDU,IAAI,CAACV,EAAE,GAAG,UAAU8B,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACf,MAAM,IAAIhB,EAAE,GAAG,OAAO8B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAEpB,IAAI,IAAIV,EAAE;EACtG,CAAC;EAEDU,IAAI,CAACT,EAAE,GAAG,UAAU6B,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACf,MAAM,IAAIf,EAAE,GAAG6B,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC2C,CAAC,CAAC,EAAEpB,IAAI,IAAIT,EAAE;EACzH,CAAC;EAEDS,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACuB,MAAM,GAAG,YAAY;IACtC,OAAOJ,QAAQ,CAAC,CAAC,CAACrB,CAAC,CAACT,EAAE,CAAC,CAACU,CAAC,CAACT,EAAE,CAAC;EAC/B,CAAC;EAEDU,IAAI,CAACwB,MAAM,GAAG,YAAY;IACxB,OAAOL,QAAQ,CAAC,CAAC,CAACrB,CAAC,CAACT,EAAE,CAAC,CAACU,CAAC,CAACR,EAAE,CAAC;EAC/B,CAAC;EAEDS,IAAI,CAACyB,MAAM,GAAG,YAAY;IACxB,OAAON,QAAQ,CAAC,CAAC,CAACrB,CAAC,CAACN,EAAE,CAAC,CAACO,CAAC,CAACT,EAAE,CAAC;EAC/B,CAAC;EAEDU,IAAI,CAACP,OAAO,GAAG,UAAU2B,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACf,MAAM,IAAIb,OAAO,GAAG,OAAO2B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG,CAAC,CAAC,EAAErC,SAAS,CAACN,OAAO,EAAE,CAAC,CAAC2C,CAAC,CAAC,EAAEpB,IAAI,IAAIP,OAAO;EACjH,CAAC;EAEDO,IAAI,CAACL,KAAK,GAAG,UAAUyB,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACf,MAAM,IAAIX,KAAK,GAAGyB,CAAC,EAAE1B,OAAO,IAAI,IAAI,KAAKE,MAAM,GAAGD,KAAK,CAACD,OAAO,CAAC,CAAC,EAAEM,IAAI,IAAIL,KAAK;EACnG,CAAC;EAEDK,IAAI,CAACN,OAAO,GAAG,UAAU0B,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACf,MAAM,IAAIc,CAAC,IAAI,IAAI,GAAG1B,OAAO,GAAGE,MAAM,GAAG,IAAI,GAAGA,MAAM,GAAGD,KAAK,CAACD,OAAO,GAAG0B,CAAC,CAAC,EAAEpB,IAAI,IAAIN,OAAO;EAC/G,CAAC;EAED,OAAOM,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script"}