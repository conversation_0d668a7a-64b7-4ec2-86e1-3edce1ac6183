{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemInputContext } from '../form/context';\nvar Group = function Group(props) {\n  var _classNames;\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    direction = _useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('input-group', customizePrefixCls);\n  var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), props.size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), props.size === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), props.compact), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var formItemContext = useContext(FormItemInputContext);\n  var groupFormItemContext = useMemo(function () {\n    return _extends(_extends({}, formItemContext), {\n      isFormItemInput: false\n    });\n  }, [formItemContext]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: cls,\n    style: props.style,\n    onMouseEnter: props.onMouseEnter,\n    onMouseLeave: props.onMouseLeave,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: groupFormItemContext\n  }, props.children));\n};\nexport default Group;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "classNames", "React", "useContext", "useMemo", "ConfigContext", "FormItemInputContext", "Group", "props", "_classNames", "_useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$className", "className", "cls", "concat", "size", "compact", "formItemContext", "groupFormItemContext", "isFormItemInput", "createElement", "style", "onMouseEnter", "onMouseLeave", "onFocus", "onBlur", "Provider", "value", "children"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/Group.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { FormItemInputContext } from '../form/context';\nvar Group = function Group(props) {\n  var _classNames;\n  var _useContext = useContext(ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    direction = _useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className;\n  var prefixCls = getPrefixCls('input-group', customizePrefixCls);\n  var cls = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-lg\"), props.size === 'large'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-sm\"), props.size === 'small'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-compact\"), props.compact), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _classNames), className);\n  var formItemContext = useContext(FormItemInputContext);\n  var groupFormItemContext = useMemo(function () {\n    return _extends(_extends({}, formItemContext), {\n      isFormItemInput: false\n    });\n  }, [formItemContext]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: cls,\n    style: props.style,\n    onMouseEnter: props.onMouseEnter,\n    onMouseLeave: props.onMouseLeave,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: groupFormItemContext\n  }, props.children));\n};\nexport default Group;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,WAAW;EACf,IAAIC,WAAW,GAAGP,UAAU,CAACE,aAAa,CAAC;IACzCM,YAAY,GAAGD,WAAW,CAACC,YAAY;IACvCC,SAAS,GAAGF,WAAW,CAACE,SAAS;EACnC,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;IACtCC,gBAAgB,GAAGP,KAAK,CAACQ,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;EACjE,IAAID,SAAS,GAAGH,YAAY,CAAC,aAAa,EAAEE,kBAAkB,CAAC;EAC/D,IAAII,GAAG,GAAGhB,UAAU,CAACa,SAAS,GAAGL,WAAW,GAAG,CAAC,CAAC,EAAET,eAAe,CAACS,WAAW,EAAE,EAAE,CAACS,MAAM,CAACJ,SAAS,EAAE,KAAK,CAAC,EAAEN,KAAK,CAACW,IAAI,KAAK,OAAO,CAAC,EAAEnB,eAAe,CAACS,WAAW,EAAE,EAAE,CAACS,MAAM,CAACJ,SAAS,EAAE,KAAK,CAAC,EAAEN,KAAK,CAACW,IAAI,KAAK,OAAO,CAAC,EAAEnB,eAAe,CAACS,WAAW,EAAE,EAAE,CAACS,MAAM,CAACJ,SAAS,EAAE,UAAU,CAAC,EAAEN,KAAK,CAACY,OAAO,CAAC,EAAEpB,eAAe,CAACS,WAAW,EAAE,EAAE,CAACS,MAAM,CAACJ,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAEH,WAAW,GAAGO,SAAS,CAAC;EACjZ,IAAIK,eAAe,GAAGlB,UAAU,CAACG,oBAAoB,CAAC;EACtD,IAAIgB,oBAAoB,GAAGlB,OAAO,CAAC,YAAY;IAC7C,OAAOL,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,eAAe,CAAC,EAAE;MAC7CE,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,eAAe,CAAC,CAAC;EACrB,OAAO,aAAanB,KAAK,CAACsB,aAAa,CAAC,MAAM,EAAE;IAC9CR,SAAS,EAAEC,GAAG;IACdQ,KAAK,EAAEjB,KAAK,CAACiB,KAAK;IAClBC,YAAY,EAAElB,KAAK,CAACkB,YAAY;IAChCC,YAAY,EAAEnB,KAAK,CAACmB,YAAY;IAChCC,OAAO,EAAEpB,KAAK,CAACoB,OAAO;IACtBC,MAAM,EAAErB,KAAK,CAACqB;EAChB,CAAC,EAAE,aAAa3B,KAAK,CAACsB,aAAa,CAAClB,oBAAoB,CAACwB,QAAQ,EAAE;IACjEC,KAAK,EAAET;EACT,CAAC,EAAEd,KAAK,CAACwB,QAAQ,CAAC,CAAC;AACrB,CAAC;AACD,eAAezB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}