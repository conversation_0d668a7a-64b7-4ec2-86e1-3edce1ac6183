{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport Dot<PERSON>hartOutlined from \"@ant-design/icons/es/icons/DotChartOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar SkeletonNode = function SkeletonNode(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    active = props.active,\n    children = props.children;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  var content = children !== null && children !== void 0 ? children : /*#__PURE__*/React.createElement(DotChartOutlined, null);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, content));\n};\nexport default SkeletonNode;", "map": {"version": 3, "names": ["_defineProperty", "DotChartOutlined", "classNames", "React", "ConfigContext", "SkeletonNode", "props", "customizePrefixCls", "prefixCls", "className", "style", "active", "children", "_React$useContext", "useContext", "getPrefixCls", "cls", "concat", "content", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Node.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport Dot<PERSON>hartOutlined from \"@ant-design/icons/es/icons/DotChartOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar SkeletonNode = function SkeletonNode(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    active = props.active,\n    children = props.children;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  var content = children !== null && children !== void 0 ? children : /*#__PURE__*/React.createElement(DotChartOutlined, null);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, content));\n};\nexport default SkeletonNode;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC9C,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;EAC3B,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,aAAa,CAAC;IACrDW,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIP,SAAS,GAAGO,YAAY,CAAC,UAAU,EAAER,kBAAkB,CAAC;EAC5D,IAAIS,GAAG,GAAGd,UAAU,CAACM,SAAS,EAAE,EAAE,CAACS,MAAM,CAACT,SAAS,EAAE,UAAU,CAAC,EAAER,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACiB,MAAM,CAACT,SAAS,EAAE,SAAS,CAAC,EAAEG,MAAM,CAAC,EAAEF,SAAS,CAAC;EAC1I,IAAIS,OAAO,GAAGN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,aAAaT,KAAK,CAACgB,aAAa,CAAClB,gBAAgB,EAAE,IAAI,CAAC;EAC5H,OAAO,aAAaE,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7CV,SAAS,EAAEO;EACb,CAAC,EAAE,aAAab,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACzCV,SAAS,EAAEP,UAAU,CAAC,EAAE,CAACe,MAAM,CAACT,SAAS,EAAE,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAChEC,KAAK,EAAEA;EACT,CAAC,EAAEQ,OAAO,CAAC,CAAC;AACd,CAAC;AACD,eAAeb,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}