{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = rank;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _sort = require(\"./sort.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction rank(values) {\n  let valueof = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _ascending.default;\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending.default;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  Uint32Array.from(V, (_, i) => i).sort(valueof === _ascending.default ? (i, j) => (0, _sort.ascendingDefined)(V[i], V[j]) : (0, _sort.compareDefined)(compareIndex)).forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "rank", "_ascending", "_interopRequireDefault", "require", "_sort", "obj", "__esModule", "values", "valueof", "arguments", "length", "undefined", "Symbol", "iterator", "TypeError", "V", "Array", "from", "R", "Float64Array", "map", "compareIndex", "i", "j", "k", "r", "Uint32Array", "_", "sort", "ascendingDefined", "compareDefined", "for<PERSON>ach", "c", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/rank.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = rank;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _sort = require(\"./sort.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction rank(values, valueof = _ascending.default) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending.default;\n\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n\n  let k, r;\n  Uint32Array.from(V, (_, i) => i).sort(valueof === _ascending.default ? (i, j) => (0, _sort.ascendingDefined)(V[i], V[j]) : (0, _sort.compareDefined)(compareIndex)).forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAEtB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,KAAK,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,IAAIA,CAACO,MAAM,EAAgC;EAAA,IAA9BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGR,UAAU,CAACF,OAAO;EAChD,IAAI,OAAOQ,MAAM,CAACK,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAIC,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACV,MAAM,CAAC;EAC1B,MAAMW,CAAC,GAAG,IAAIC,YAAY,CAACJ,CAAC,CAACL,MAAM,CAAC;EACpC,IAAIF,OAAO,CAACE,MAAM,KAAK,CAAC,EAAEK,CAAC,GAAGA,CAAC,CAACK,GAAG,CAACZ,OAAO,CAAC,EAAEA,OAAO,GAAGP,UAAU,CAACF,OAAO;EAE1E,MAAMsB,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKf,OAAO,CAACO,CAAC,CAACO,CAAC,CAAC,EAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC;EAElD,IAAIC,CAAC,EAAEC,CAAC;EACRC,WAAW,CAACT,IAAI,CAACF,CAAC,EAAE,CAACY,CAAC,EAAEL,CAAC,KAAKA,CAAC,CAAC,CAACM,IAAI,CAACpB,OAAO,KAAKP,UAAU,CAACF,OAAO,GAAG,CAACuB,CAAC,EAAEC,CAAC,KAAK,CAAC,CAAC,EAAEnB,KAAK,CAACyB,gBAAgB,EAAEd,CAAC,CAACO,CAAC,CAAC,EAAEP,CAAC,CAACQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEnB,KAAK,CAAC0B,cAAc,EAAET,YAAY,CAAC,CAAC,CAACU,OAAO,CAAC,CAACR,CAAC,EAAED,CAAC,KAAK;IACpL,MAAMU,CAAC,GAAGX,YAAY,CAACE,CAAC,EAAEC,CAAC,KAAKb,SAAS,GAAGY,CAAC,GAAGC,CAAC,CAAC;IAElD,IAAIQ,CAAC,IAAI,CAAC,EAAE;MACV,IAAIR,CAAC,KAAKb,SAAS,IAAIqB,CAAC,GAAG,CAAC,EAAER,CAAC,GAAGD,CAAC,EAAEE,CAAC,GAAGH,CAAC;MAC1CJ,CAAC,CAACK,CAAC,CAAC,GAAGE,CAAC;IACV,CAAC,MAAM;MACLP,CAAC,CAACK,CAAC,CAAC,GAAGU,GAAG;IACZ;EACF,CAAC,CAAC;EACF,OAAOf,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}