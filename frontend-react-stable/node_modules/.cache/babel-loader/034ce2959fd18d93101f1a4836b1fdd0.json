{"ast": null, "code": "var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport var placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport var placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport default placements;", "map": {"version": 3, "names": ["autoAdjustOverflow", "adjustX", "adjustY", "placements", "topLeft", "points", "overflow", "offset", "bottomLeft", "leftTop", "rightTop", "placementsRtl"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/placements.js"], "sourcesContent": ["var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport var placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport var placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport default placements;"], "mappings": "AAAA,IAAIA,kBAAkB,GAAG;EACvBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EACDC,UAAU,EAAE;IACVH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDE,OAAO,EAAE;IACPJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC;EACDG,QAAQ,EAAE;IACRL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf;AACF,CAAC;AACD,OAAO,IAAII,aAAa,GAAG;EACzBP,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EACDC,UAAU,EAAE;IACVH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf,CAAC;EACDG,QAAQ,EAAE;IACRL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC;EACDE,OAAO,EAAE;IACPJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN,kBAAkB;IAC5BO,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;EACf;AACF,CAAC;AACD,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}