{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nimport { getMenuId } from '../context/IdContext'; // destruct to reduce minify size\n\nvar LEFT = KeyCode.LEFT,\n  RIGHT = KeyCode.RIGHT,\n  UP = KeyCode.UP,\n  DOWN = KeyCode.DOWN,\n  ENTER = KeyCode.ENTER,\n  ESC = KeyCode.ESC,\n  HOME = KeyCode.HOME,\n  END = KeyCode.END;\nvar ArrowKeys = [UP, DOWN, LEFT, RIGHT];\nfunction getOffset(mode, isRootLevel, isRtl, which) {\n  var _inline, _horizontal, _vertical, _offsets;\n  var prev = 'prev';\n  var next = 'next';\n  var children = 'children';\n  var parent = 'parent'; // Inline enter is special that we use unique operation\n\n  if (mode === 'inline' && which === ENTER) {\n    return {\n      inlineTrigger: true\n    };\n  }\n  var inline = (_inline = {}, _defineProperty(_inline, UP, prev), _defineProperty(_inline, DOWN, next), _inline);\n  var horizontal = (_horizontal = {}, _defineProperty(_horizontal, LEFT, isRtl ? next : prev), _defineProperty(_horizontal, RIGHT, isRtl ? prev : next), _defineProperty(_horizontal, DOWN, children), _defineProperty(_horizontal, ENTER, children), _horizontal);\n  var vertical = (_vertical = {}, _defineProperty(_vertical, UP, prev), _defineProperty(_vertical, DOWN, next), _defineProperty(_vertical, ENTER, children), _defineProperty(_vertical, ESC, parent), _defineProperty(_vertical, LEFT, isRtl ? children : parent), _defineProperty(_vertical, RIGHT, isRtl ? parent : children), _vertical);\n  var offsets = {\n    inline: inline,\n    horizontal: horizontal,\n    vertical: vertical,\n    inlineSub: inline,\n    horizontalSub: vertical,\n    verticalSub: vertical\n  };\n  var type = (_offsets = offsets[\"\".concat(mode).concat(isRootLevel ? '' : 'Sub')]) === null || _offsets === void 0 ? void 0 : _offsets[which];\n  switch (type) {\n    case prev:\n      return {\n        offset: -1,\n        sibling: true\n      };\n    case next:\n      return {\n        offset: 1,\n        sibling: true\n      };\n    case parent:\n      return {\n        offset: -1,\n        sibling: false\n      };\n    case children:\n      return {\n        offset: 1,\n        sibling: false\n      };\n    default:\n      return null;\n  }\n}\nfunction findContainerUL(element) {\n  var current = element;\n  while (current) {\n    if (current.getAttribute('data-menu-list')) {\n      return current;\n    }\n    current = current.parentElement;\n  } // Normally should not reach this line\n\n  /* istanbul ignore next */\n\n  return null;\n}\n/**\n * Find focused element within element set provided\n */\n\nfunction getFocusElement(activeElement, elements) {\n  var current = activeElement || document.activeElement;\n  while (current) {\n    if (elements.has(current)) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n/**\n * Get focusable elements from the element set under provided container\n */\n\nfunction getFocusableElements(container, elements) {\n  var list = getFocusNodeList(container, true);\n  return list.filter(function (ele) {\n    return elements.has(ele);\n  });\n}\nfunction getNextFocusElement(parentQueryContainer, elements, focusMenuElement) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n\n  // Key on the menu item will not get validate parent container\n  if (!parentQueryContainer) {\n    return null;\n  } // List current level menu item elements\n\n  var sameLevelFocusableMenuElementList = getFocusableElements(parentQueryContainer, elements); // Find next focus index\n\n  var count = sameLevelFocusableMenuElementList.length;\n  var focusIndex = sameLevelFocusableMenuElementList.findIndex(function (ele) {\n    return focusMenuElement === ele;\n  });\n  if (offset < 0) {\n    if (focusIndex === -1) {\n      focusIndex = count - 1;\n    } else {\n      focusIndex -= 1;\n    }\n  } else if (offset > 0) {\n    focusIndex += 1;\n  }\n  focusIndex = (focusIndex + count) % count; // Focus menu item\n\n  return sameLevelFocusableMenuElementList[focusIndex];\n}\nexport default function useAccessibility(mode, activeKey, isRtl, id, containerRef, getKeys, getKeyPath, triggerActiveKey, triggerAccessibilityOpen, originOnKeyDown) {\n  var rafRef = React.useRef();\n  var activeRef = React.useRef();\n  activeRef.current = activeKey;\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(rafRef.current);\n  };\n  React.useEffect(function () {\n    return function () {\n      cleanRaf();\n    };\n  }, []);\n  return function (e) {\n    var which = e.which;\n    if ([].concat(ArrowKeys, [ENTER, ESC, HOME, END]).includes(which)) {\n      // Convert key to elements\n      var elements;\n      var key2element;\n      var element2key; // >>> Wrap as function since we use raf for some case\n\n      var refreshElements = function refreshElements() {\n        elements = new Set();\n        key2element = new Map();\n        element2key = new Map();\n        var keys = getKeys();\n        keys.forEach(function (key) {\n          var element = document.querySelector(\"[data-menu-id='\".concat(getMenuId(id, key), \"']\"));\n          if (element) {\n            elements.add(element);\n            element2key.set(element, key);\n            key2element.set(key, element);\n          }\n        });\n        return elements;\n      };\n      refreshElements(); // First we should find current focused MenuItem/SubMenu element\n\n      var activeElement = key2element.get(activeKey);\n      var focusMenuElement = getFocusElement(activeElement, elements);\n      var focusMenuKey = element2key.get(focusMenuElement);\n      var offsetObj = getOffset(mode, getKeyPath(focusMenuKey, true).length === 1, isRtl, which); // Some mode do not have fully arrow operation like inline\n\n      if (!offsetObj && which !== HOME && which !== END) {\n        return;\n      } // Arrow prevent default to avoid page scroll\n\n      if (ArrowKeys.includes(which) || [HOME, END].includes(which)) {\n        e.preventDefault();\n      }\n      var tryFocus = function tryFocus(menuElement) {\n        if (menuElement) {\n          var focusTargetElement = menuElement; // Focus to link instead of menu item if possible\n\n          var link = menuElement.querySelector('a');\n          if (link === null || link === void 0 ? void 0 : link.getAttribute('href')) {\n            focusTargetElement = link;\n          }\n          var targetKey = element2key.get(menuElement);\n          triggerActiveKey(targetKey);\n          /**\n           * Do not `useEffect` here since `tryFocus` may trigger async\n           * which makes React sync update the `activeKey`\n           * that force render before `useRef` set the next activeKey\n           */\n\n          cleanRaf();\n          rafRef.current = raf(function () {\n            if (activeRef.current === targetKey) {\n              focusTargetElement.focus();\n            }\n          });\n        }\n      };\n      if ([HOME, END].includes(which) || offsetObj.sibling || !focusMenuElement) {\n        // ========================== Sibling ==========================\n        // Find walkable focus menu element container\n        var parentQueryContainer;\n        if (!focusMenuElement || mode === 'inline') {\n          parentQueryContainer = containerRef.current;\n        } else {\n          parentQueryContainer = findContainerUL(focusMenuElement);\n        } // Get next focus element\n\n        var targetElement;\n        var focusableElements = getFocusableElements(parentQueryContainer, elements);\n        if (which === HOME) {\n          targetElement = focusableElements[0];\n        } else if (which === END) {\n          targetElement = focusableElements[focusableElements.length - 1];\n        } else {\n          targetElement = getNextFocusElement(parentQueryContainer, elements, focusMenuElement, offsetObj.offset);\n        } // Focus menu item\n\n        tryFocus(targetElement); // ======================= InlineTrigger =======================\n      } else if (offsetObj.inlineTrigger) {\n        // Inline trigger no need switch to sub menu item\n        triggerAccessibilityOpen(focusMenuKey); // =========================== Level ===========================\n      } else if (offsetObj.offset > 0) {\n        triggerAccessibilityOpen(focusMenuKey, true);\n        cleanRaf();\n        rafRef.current = raf(function () {\n          // Async should resync elements\n          refreshElements();\n          var controlId = focusMenuElement.getAttribute('aria-controls');\n          var subQueryContainer = document.getElementById(controlId); // Get sub focusable menu item\n\n          var targetElement = getNextFocusElement(subQueryContainer, elements); // Focus menu item\n\n          tryFocus(targetElement);\n        }, 5);\n      } else if (offsetObj.offset < 0) {\n        var keyPath = getKeyPath(focusMenuKey, true);\n        var parentKey = keyPath[keyPath.length - 2];\n        var parentMenuElement = key2element.get(parentKey); // Focus menu item\n\n        triggerAccessibilityOpen(parentKey, false);\n        tryFocus(parentMenuElement);\n      }\n    } // Pass origin key down event\n\n    originOnKeyDown === null || originOnKeyDown === void 0 ? void 0 : originOnKeyDown(e);\n  };\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "KeyCode", "raf", "getFocusNodeList", "getMenuId", "LEFT", "RIGHT", "UP", "DOWN", "ENTER", "ESC", "HOME", "END", "ArrowKeys", "getOffset", "mode", "isRootLevel", "isRtl", "which", "_inline", "_horizontal", "_vertical", "_offsets", "prev", "next", "children", "parent", "inlineTrigger", "inline", "horizontal", "vertical", "offsets", "inlineSub", "horizontalSub", "verticalSub", "type", "concat", "offset", "sibling", "findContainerUL", "element", "current", "getAttribute", "parentElement", "getFocusElement", "activeElement", "elements", "document", "has", "getFocusableElements", "container", "list", "filter", "ele", "getNextFocusElement", "parentQueryContainer", "focusMenuElement", "arguments", "length", "undefined", "sameLevelFocusableMenuElementList", "count", "focusIndex", "findIndex", "useAccessibility", "active<PERSON><PERSON>", "id", "containerRef", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerActiveKey", "triggerAccessibilityOpen", "originOnKeyDown", "rafRef", "useRef", "activeRef", "cleanRaf", "cancel", "useEffect", "e", "includes", "key2element", "element2key", "refreshElements", "Set", "Map", "keys", "for<PERSON>ach", "key", "querySelector", "add", "set", "get", "focusMenuKey", "offsetObj", "preventDefault", "tryFocus", "menuElement", "focusTargetElement", "link", "<PERSON><PERSON><PERSON>", "focus", "targetElement", "focusableElements", "controlId", "subQueryContainer", "getElementById", "keyP<PERSON>", "parent<PERSON><PERSON>", "parentMenuElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/hooks/useAccessibility.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { getFocusNodeList } from \"rc-util/es/Dom/focus\";\nimport { getMenuId } from '../context/IdContext'; // destruct to reduce minify size\n\nvar LEFT = KeyCode.LEFT,\n    RIGHT = KeyCode.RIGHT,\n    UP = KeyCode.UP,\n    DOWN = KeyCode.DOWN,\n    ENTER = KeyCode.ENTER,\n    ESC = KeyCode.ESC,\n    HOME = KeyCode.HOME,\n    END = KeyCode.END;\nvar ArrowKeys = [UP, DOWN, LEFT, RIGHT];\n\nfunction getOffset(mode, isRootLevel, isRtl, which) {\n  var _inline, _horizontal, _vertical, _offsets;\n\n  var prev = 'prev';\n  var next = 'next';\n  var children = 'children';\n  var parent = 'parent'; // Inline enter is special that we use unique operation\n\n  if (mode === 'inline' && which === ENTER) {\n    return {\n      inlineTrigger: true\n    };\n  }\n\n  var inline = (_inline = {}, _defineProperty(_inline, UP, prev), _defineProperty(_inline, DOWN, next), _inline);\n  var horizontal = (_horizontal = {}, _defineProperty(_horizontal, LEFT, isRtl ? next : prev), _defineProperty(_horizontal, RIGHT, isRtl ? prev : next), _defineProperty(_horizontal, DOWN, children), _defineProperty(_horizontal, ENTER, children), _horizontal);\n  var vertical = (_vertical = {}, _defineProperty(_vertical, UP, prev), _defineProperty(_vertical, DOWN, next), _defineProperty(_vertical, ENTER, children), _defineProperty(_vertical, ESC, parent), _defineProperty(_vertical, LEFT, isRtl ? children : parent), _defineProperty(_vertical, RIGHT, isRtl ? parent : children), _vertical);\n  var offsets = {\n    inline: inline,\n    horizontal: horizontal,\n    vertical: vertical,\n    inlineSub: inline,\n    horizontalSub: vertical,\n    verticalSub: vertical\n  };\n  var type = (_offsets = offsets[\"\".concat(mode).concat(isRootLevel ? '' : 'Sub')]) === null || _offsets === void 0 ? void 0 : _offsets[which];\n\n  switch (type) {\n    case prev:\n      return {\n        offset: -1,\n        sibling: true\n      };\n\n    case next:\n      return {\n        offset: 1,\n        sibling: true\n      };\n\n    case parent:\n      return {\n        offset: -1,\n        sibling: false\n      };\n\n    case children:\n      return {\n        offset: 1,\n        sibling: false\n      };\n\n    default:\n      return null;\n  }\n}\n\nfunction findContainerUL(element) {\n  var current = element;\n\n  while (current) {\n    if (current.getAttribute('data-menu-list')) {\n      return current;\n    }\n\n    current = current.parentElement;\n  } // Normally should not reach this line\n\n  /* istanbul ignore next */\n\n\n  return null;\n}\n/**\n * Find focused element within element set provided\n */\n\n\nfunction getFocusElement(activeElement, elements) {\n  var current = activeElement || document.activeElement;\n\n  while (current) {\n    if (elements.has(current)) {\n      return current;\n    }\n\n    current = current.parentElement;\n  }\n\n  return null;\n}\n/**\n * Get focusable elements from the element set under provided container\n */\n\n\nfunction getFocusableElements(container, elements) {\n  var list = getFocusNodeList(container, true);\n  return list.filter(function (ele) {\n    return elements.has(ele);\n  });\n}\n\nfunction getNextFocusElement(parentQueryContainer, elements, focusMenuElement) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n\n  // Key on the menu item will not get validate parent container\n  if (!parentQueryContainer) {\n    return null;\n  } // List current level menu item elements\n\n\n  var sameLevelFocusableMenuElementList = getFocusableElements(parentQueryContainer, elements); // Find next focus index\n\n  var count = sameLevelFocusableMenuElementList.length;\n  var focusIndex = sameLevelFocusableMenuElementList.findIndex(function (ele) {\n    return focusMenuElement === ele;\n  });\n\n  if (offset < 0) {\n    if (focusIndex === -1) {\n      focusIndex = count - 1;\n    } else {\n      focusIndex -= 1;\n    }\n  } else if (offset > 0) {\n    focusIndex += 1;\n  }\n\n  focusIndex = (focusIndex + count) % count; // Focus menu item\n\n  return sameLevelFocusableMenuElementList[focusIndex];\n}\n\nexport default function useAccessibility(mode, activeKey, isRtl, id, containerRef, getKeys, getKeyPath, triggerActiveKey, triggerAccessibilityOpen, originOnKeyDown) {\n  var rafRef = React.useRef();\n  var activeRef = React.useRef();\n  activeRef.current = activeKey;\n\n  var cleanRaf = function cleanRaf() {\n    raf.cancel(rafRef.current);\n  };\n\n  React.useEffect(function () {\n    return function () {\n      cleanRaf();\n    };\n  }, []);\n  return function (e) {\n    var which = e.which;\n\n    if ([].concat(ArrowKeys, [ENTER, ESC, HOME, END]).includes(which)) {\n      // Convert key to elements\n      var elements;\n      var key2element;\n      var element2key; // >>> Wrap as function since we use raf for some case\n\n      var refreshElements = function refreshElements() {\n        elements = new Set();\n        key2element = new Map();\n        element2key = new Map();\n        var keys = getKeys();\n        keys.forEach(function (key) {\n          var element = document.querySelector(\"[data-menu-id='\".concat(getMenuId(id, key), \"']\"));\n\n          if (element) {\n            elements.add(element);\n            element2key.set(element, key);\n            key2element.set(key, element);\n          }\n        });\n        return elements;\n      };\n\n      refreshElements(); // First we should find current focused MenuItem/SubMenu element\n\n      var activeElement = key2element.get(activeKey);\n      var focusMenuElement = getFocusElement(activeElement, elements);\n      var focusMenuKey = element2key.get(focusMenuElement);\n      var offsetObj = getOffset(mode, getKeyPath(focusMenuKey, true).length === 1, isRtl, which); // Some mode do not have fully arrow operation like inline\n\n      if (!offsetObj && which !== HOME && which !== END) {\n        return;\n      } // Arrow prevent default to avoid page scroll\n\n\n      if (ArrowKeys.includes(which) || [HOME, END].includes(which)) {\n        e.preventDefault();\n      }\n\n      var tryFocus = function tryFocus(menuElement) {\n        if (menuElement) {\n          var focusTargetElement = menuElement; // Focus to link instead of menu item if possible\n\n          var link = menuElement.querySelector('a');\n\n          if (link === null || link === void 0 ? void 0 : link.getAttribute('href')) {\n            focusTargetElement = link;\n          }\n\n          var targetKey = element2key.get(menuElement);\n          triggerActiveKey(targetKey);\n          /**\n           * Do not `useEffect` here since `tryFocus` may trigger async\n           * which makes React sync update the `activeKey`\n           * that force render before `useRef` set the next activeKey\n           */\n\n          cleanRaf();\n          rafRef.current = raf(function () {\n            if (activeRef.current === targetKey) {\n              focusTargetElement.focus();\n            }\n          });\n        }\n      };\n\n      if ([HOME, END].includes(which) || offsetObj.sibling || !focusMenuElement) {\n        // ========================== Sibling ==========================\n        // Find walkable focus menu element container\n        var parentQueryContainer;\n\n        if (!focusMenuElement || mode === 'inline') {\n          parentQueryContainer = containerRef.current;\n        } else {\n          parentQueryContainer = findContainerUL(focusMenuElement);\n        } // Get next focus element\n\n\n        var targetElement;\n        var focusableElements = getFocusableElements(parentQueryContainer, elements);\n\n        if (which === HOME) {\n          targetElement = focusableElements[0];\n        } else if (which === END) {\n          targetElement = focusableElements[focusableElements.length - 1];\n        } else {\n          targetElement = getNextFocusElement(parentQueryContainer, elements, focusMenuElement, offsetObj.offset);\n        } // Focus menu item\n\n\n        tryFocus(targetElement); // ======================= InlineTrigger =======================\n      } else if (offsetObj.inlineTrigger) {\n        // Inline trigger no need switch to sub menu item\n        triggerAccessibilityOpen(focusMenuKey); // =========================== Level ===========================\n      } else if (offsetObj.offset > 0) {\n        triggerAccessibilityOpen(focusMenuKey, true);\n        cleanRaf();\n        rafRef.current = raf(function () {\n          // Async should resync elements\n          refreshElements();\n          var controlId = focusMenuElement.getAttribute('aria-controls');\n          var subQueryContainer = document.getElementById(controlId); // Get sub focusable menu item\n\n          var targetElement = getNextFocusElement(subQueryContainer, elements); // Focus menu item\n\n          tryFocus(targetElement);\n        }, 5);\n      } else if (offsetObj.offset < 0) {\n        var keyPath = getKeyPath(focusMenuKey, true);\n        var parentKey = keyPath[keyPath.length - 2];\n        var parentMenuElement = key2element.get(parentKey); // Focus menu item\n\n        triggerAccessibilityOpen(parentKey, false);\n        tryFocus(parentMenuElement);\n      }\n    } // Pass origin key down event\n\n\n    originOnKeyDown === null || originOnKeyDown === void 0 ? void 0 : originOnKeyDown(e);\n  };\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,SAAS,QAAQ,sBAAsB,CAAC,CAAC;;AAElD,IAAIC,IAAI,GAAGJ,OAAO,CAACI,IAAI;EACnBC,KAAK,GAAGL,OAAO,CAACK,KAAK;EACrBC,EAAE,GAAGN,OAAO,CAACM,EAAE;EACfC,IAAI,GAAGP,OAAO,CAACO,IAAI;EACnBC,KAAK,GAAGR,OAAO,CAACQ,KAAK;EACrBC,GAAG,GAAGT,OAAO,CAACS,GAAG;EACjBC,IAAI,GAAGV,OAAO,CAACU,IAAI;EACnBC,GAAG,GAAGX,OAAO,CAACW,GAAG;AACrB,IAAIC,SAAS,GAAG,CAACN,EAAE,EAAEC,IAAI,EAAEH,IAAI,EAAEC,KAAK,CAAC;AAEvC,SAASQ,SAASA,CAACC,IAAI,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAClD,IAAIC,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ;EAE7C,IAAIC,IAAI,GAAG,MAAM;EACjB,IAAIC,IAAI,GAAG,MAAM;EACjB,IAAIC,QAAQ,GAAG,UAAU;EACzB,IAAIC,MAAM,GAAG,QAAQ,CAAC,CAAC;;EAEvB,IAAIX,IAAI,KAAK,QAAQ,IAAIG,KAAK,KAAKT,KAAK,EAAE;IACxC,OAAO;MACLkB,aAAa,EAAE;IACjB,CAAC;EACH;EAEA,IAAIC,MAAM,IAAIT,OAAO,GAAG,CAAC,CAAC,EAAEpB,eAAe,CAACoB,OAAO,EAAEZ,EAAE,EAAEgB,IAAI,CAAC,EAAExB,eAAe,CAACoB,OAAO,EAAEX,IAAI,EAAEgB,IAAI,CAAC,EAAEL,OAAO,CAAC;EAC9G,IAAIU,UAAU,IAAIT,WAAW,GAAG,CAAC,CAAC,EAAErB,eAAe,CAACqB,WAAW,EAAEf,IAAI,EAAEY,KAAK,GAAGO,IAAI,GAAGD,IAAI,CAAC,EAAExB,eAAe,CAACqB,WAAW,EAAEd,KAAK,EAAEW,KAAK,GAAGM,IAAI,GAAGC,IAAI,CAAC,EAAEzB,eAAe,CAACqB,WAAW,EAAEZ,IAAI,EAAEiB,QAAQ,CAAC,EAAE1B,eAAe,CAACqB,WAAW,EAAEX,KAAK,EAAEgB,QAAQ,CAAC,EAAEL,WAAW,CAAC;EAChQ,IAAIU,QAAQ,IAAIT,SAAS,GAAG,CAAC,CAAC,EAAEtB,eAAe,CAACsB,SAAS,EAAEd,EAAE,EAAEgB,IAAI,CAAC,EAAExB,eAAe,CAACsB,SAAS,EAAEb,IAAI,EAAEgB,IAAI,CAAC,EAAEzB,eAAe,CAACsB,SAAS,EAAEZ,KAAK,EAAEgB,QAAQ,CAAC,EAAE1B,eAAe,CAACsB,SAAS,EAAEX,GAAG,EAAEgB,MAAM,CAAC,EAAE3B,eAAe,CAACsB,SAAS,EAAEhB,IAAI,EAAEY,KAAK,GAAGQ,QAAQ,GAAGC,MAAM,CAAC,EAAE3B,eAAe,CAACsB,SAAS,EAAEf,KAAK,EAAEW,KAAK,GAAGS,MAAM,GAAGD,QAAQ,CAAC,EAAEJ,SAAS,CAAC;EACzU,IAAIU,OAAO,GAAG;IACZH,MAAM,EAAEA,MAAM;IACdC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAEA,QAAQ;IAClBE,SAAS,EAAEJ,MAAM;IACjBK,aAAa,EAAEH,QAAQ;IACvBI,WAAW,EAAEJ;EACf,CAAC;EACD,IAAIK,IAAI,GAAG,CAACb,QAAQ,GAAGS,OAAO,CAAC,EAAE,CAACK,MAAM,CAACrB,IAAI,CAAC,CAACqB,MAAM,CAACpB,WAAW,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,MAAM,IAAI,IAAIM,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACJ,KAAK,CAAC;EAE5I,QAAQiB,IAAI;IACV,KAAKZ,IAAI;MACP,OAAO;QACLc,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKd,IAAI;MACP,OAAO;QACLa,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKZ,MAAM;MACT,OAAO;QACLW,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKb,QAAQ;MACX,OAAO;QACLY,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;IAEH;MACE,OAAO,IAAI;EACf;AACF;AAEA,SAASC,eAAeA,CAACC,OAAO,EAAE;EAChC,IAAIC,OAAO,GAAGD,OAAO;EAErB,OAAOC,OAAO,EAAE;IACd,IAAIA,OAAO,CAACC,YAAY,CAAC,gBAAgB,CAAC,EAAE;MAC1C,OAAOD,OAAO;IAChB;IAEAA,OAAO,GAAGA,OAAO,CAACE,aAAa;EACjC,CAAC,CAAC;;EAEF;;EAGA,OAAO,IAAI;AACb;AACA;AACA;AACA;;AAGA,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAE;EAChD,IAAIL,OAAO,GAAGI,aAAa,IAAIE,QAAQ,CAACF,aAAa;EAErD,OAAOJ,OAAO,EAAE;IACd,IAAIK,QAAQ,CAACE,GAAG,CAACP,OAAO,CAAC,EAAE;MACzB,OAAOA,OAAO;IAChB;IAEAA,OAAO,GAAGA,OAAO,CAACE,aAAa;EACjC;EAEA,OAAO,IAAI;AACb;AACA;AACA;AACA;;AAGA,SAASM,oBAAoBA,CAACC,SAAS,EAAEJ,QAAQ,EAAE;EACjD,IAAIK,IAAI,GAAGhD,gBAAgB,CAAC+C,SAAS,EAAE,IAAI,CAAC;EAC5C,OAAOC,IAAI,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;IAChC,OAAOP,QAAQ,CAACE,GAAG,CAACK,GAAG,CAAC;EAC1B,CAAC,CAAC;AACJ;AAEA,SAASC,mBAAmBA,CAACC,oBAAoB,EAAET,QAAQ,EAAEU,gBAAgB,EAAE;EAC7E,IAAInB,MAAM,GAAGoB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;;EAElF;EACA,IAAI,CAACF,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIK,iCAAiC,GAAGX,oBAAoB,CAACM,oBAAoB,EAAET,QAAQ,CAAC,CAAC,CAAC;;EAE9F,IAAIe,KAAK,GAAGD,iCAAiC,CAACF,MAAM;EACpD,IAAII,UAAU,GAAGF,iCAAiC,CAACG,SAAS,CAAC,UAAUV,GAAG,EAAE;IAC1E,OAAOG,gBAAgB,KAAKH,GAAG;EACjC,CAAC,CAAC;EAEF,IAAIhB,MAAM,GAAG,CAAC,EAAE;IACd,IAAIyB,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBA,UAAU,GAAGD,KAAK,GAAG,CAAC;IACxB,CAAC,MAAM;MACLC,UAAU,IAAI,CAAC;IACjB;EACF,CAAC,MAAM,IAAIzB,MAAM,GAAG,CAAC,EAAE;IACrByB,UAAU,IAAI,CAAC;EACjB;EAEAA,UAAU,GAAG,CAACA,UAAU,GAAGD,KAAK,IAAIA,KAAK,CAAC,CAAC;;EAE3C,OAAOD,iCAAiC,CAACE,UAAU,CAAC;AACtD;AAEA,eAAe,SAASE,gBAAgBA,CAACjD,IAAI,EAAEkD,SAAS,EAAEhD,KAAK,EAAEiD,EAAE,EAAEC,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,eAAe,EAAE;EACnK,IAAIC,MAAM,GAAGzE,KAAK,CAAC0E,MAAM,CAAC,CAAC;EAC3B,IAAIC,SAAS,GAAG3E,KAAK,CAAC0E,MAAM,CAAC,CAAC;EAC9BC,SAAS,CAAClC,OAAO,GAAGwB,SAAS;EAE7B,IAAIW,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC1E,GAAG,CAAC2E,MAAM,CAACJ,MAAM,CAAChC,OAAO,CAAC;EAC5B,CAAC;EAEDzC,KAAK,CAAC8E,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBF,QAAQ,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,UAAUG,CAAC,EAAE;IAClB,IAAI7D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK;IAEnB,IAAI,EAAE,CAACkB,MAAM,CAACvB,SAAS,EAAE,CAACJ,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,CAAC,CAAC,CAACoE,QAAQ,CAAC9D,KAAK,CAAC,EAAE;MACjE;MACA,IAAI4B,QAAQ;MACZ,IAAImC,WAAW;MACf,IAAIC,WAAW,CAAC,CAAC;;MAEjB,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;QAC/CrC,QAAQ,GAAG,IAAIsC,GAAG,CAAC,CAAC;QACpBH,WAAW,GAAG,IAAII,GAAG,CAAC,CAAC;QACvBH,WAAW,GAAG,IAAIG,GAAG,CAAC,CAAC;QACvB,IAAIC,IAAI,GAAGlB,OAAO,CAAC,CAAC;QACpBkB,IAAI,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;UAC1B,IAAIhD,OAAO,GAAGO,QAAQ,CAAC0C,aAAa,CAAC,iBAAiB,CAACrD,MAAM,CAAChC,SAAS,CAAC8D,EAAE,EAAEsB,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;UAExF,IAAIhD,OAAO,EAAE;YACXM,QAAQ,CAAC4C,GAAG,CAAClD,OAAO,CAAC;YACrB0C,WAAW,CAACS,GAAG,CAACnD,OAAO,EAAEgD,GAAG,CAAC;YAC7BP,WAAW,CAACU,GAAG,CAACH,GAAG,EAAEhD,OAAO,CAAC;UAC/B;QACF,CAAC,CAAC;QACF,OAAOM,QAAQ;MACjB,CAAC;MAEDqC,eAAe,CAAC,CAAC,CAAC,CAAC;;MAEnB,IAAItC,aAAa,GAAGoC,WAAW,CAACW,GAAG,CAAC3B,SAAS,CAAC;MAC9C,IAAIT,gBAAgB,GAAGZ,eAAe,CAACC,aAAa,EAAEC,QAAQ,CAAC;MAC/D,IAAI+C,YAAY,GAAGX,WAAW,CAACU,GAAG,CAACpC,gBAAgB,CAAC;MACpD,IAAIsC,SAAS,GAAGhF,SAAS,CAACC,IAAI,EAAEsD,UAAU,CAACwB,YAAY,EAAE,IAAI,CAAC,CAACnC,MAAM,KAAK,CAAC,EAAEzC,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;;MAE5F,IAAI,CAAC4E,SAAS,IAAI5E,KAAK,KAAKP,IAAI,IAAIO,KAAK,KAAKN,GAAG,EAAE;QACjD;MACF,CAAC,CAAC;;MAGF,IAAIC,SAAS,CAACmE,QAAQ,CAAC9D,KAAK,CAAC,IAAI,CAACP,IAAI,EAAEC,GAAG,CAAC,CAACoE,QAAQ,CAAC9D,KAAK,CAAC,EAAE;QAC5D6D,CAAC,CAACgB,cAAc,CAAC,CAAC;MACpB;MAEA,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,WAAW,EAAE;QAC5C,IAAIA,WAAW,EAAE;UACf,IAAIC,kBAAkB,GAAGD,WAAW,CAAC,CAAC;;UAEtC,IAAIE,IAAI,GAAGF,WAAW,CAACR,aAAa,CAAC,GAAG,CAAC;UAEzC,IAAIU,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACzD,YAAY,CAAC,MAAM,CAAC,EAAE;YACzEwD,kBAAkB,GAAGC,IAAI;UAC3B;UAEA,IAAIC,SAAS,GAAGlB,WAAW,CAACU,GAAG,CAACK,WAAW,CAAC;UAC5C3B,gBAAgB,CAAC8B,SAAS,CAAC;UAC3B;AACV;AACA;AACA;AACA;;UAEUxB,QAAQ,CAAC,CAAC;UACVH,MAAM,CAAChC,OAAO,GAAGvC,GAAG,CAAC,YAAY;YAC/B,IAAIyE,SAAS,CAAClC,OAAO,KAAK2D,SAAS,EAAE;cACnCF,kBAAkB,CAACG,KAAK,CAAC,CAAC;YAC5B;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MAED,IAAI,CAAC1F,IAAI,EAAEC,GAAG,CAAC,CAACoE,QAAQ,CAAC9D,KAAK,CAAC,IAAI4E,SAAS,CAACxD,OAAO,IAAI,CAACkB,gBAAgB,EAAE;QACzE;QACA;QACA,IAAID,oBAAoB;QAExB,IAAI,CAACC,gBAAgB,IAAIzC,IAAI,KAAK,QAAQ,EAAE;UAC1CwC,oBAAoB,GAAGY,YAAY,CAAC1B,OAAO;QAC7C,CAAC,MAAM;UACLc,oBAAoB,GAAGhB,eAAe,CAACiB,gBAAgB,CAAC;QAC1D,CAAC,CAAC;;QAGF,IAAI8C,aAAa;QACjB,IAAIC,iBAAiB,GAAGtD,oBAAoB,CAACM,oBAAoB,EAAET,QAAQ,CAAC;QAE5E,IAAI5B,KAAK,KAAKP,IAAI,EAAE;UAClB2F,aAAa,GAAGC,iBAAiB,CAAC,CAAC,CAAC;QACtC,CAAC,MAAM,IAAIrF,KAAK,KAAKN,GAAG,EAAE;UACxB0F,aAAa,GAAGC,iBAAiB,CAACA,iBAAiB,CAAC7C,MAAM,GAAG,CAAC,CAAC;QACjE,CAAC,MAAM;UACL4C,aAAa,GAAGhD,mBAAmB,CAACC,oBAAoB,EAAET,QAAQ,EAAEU,gBAAgB,EAAEsC,SAAS,CAACzD,MAAM,CAAC;QACzG,CAAC,CAAC;;QAGF2D,QAAQ,CAACM,aAAa,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM,IAAIR,SAAS,CAACnE,aAAa,EAAE;QAClC;QACA4C,wBAAwB,CAACsB,YAAY,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM,IAAIC,SAAS,CAACzD,MAAM,GAAG,CAAC,EAAE;QAC/BkC,wBAAwB,CAACsB,YAAY,EAAE,IAAI,CAAC;QAC5CjB,QAAQ,CAAC,CAAC;QACVH,MAAM,CAAChC,OAAO,GAAGvC,GAAG,CAAC,YAAY;UAC/B;UACAiF,eAAe,CAAC,CAAC;UACjB,IAAIqB,SAAS,GAAGhD,gBAAgB,CAACd,YAAY,CAAC,eAAe,CAAC;UAC9D,IAAI+D,iBAAiB,GAAG1D,QAAQ,CAAC2D,cAAc,CAACF,SAAS,CAAC,CAAC,CAAC;;UAE5D,IAAIF,aAAa,GAAGhD,mBAAmB,CAACmD,iBAAiB,EAAE3D,QAAQ,CAAC,CAAC,CAAC;;UAEtEkD,QAAQ,CAACM,aAAa,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,MAAM,IAAIR,SAAS,CAACzD,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAIsE,OAAO,GAAGtC,UAAU,CAACwB,YAAY,EAAE,IAAI,CAAC;QAC5C,IAAIe,SAAS,GAAGD,OAAO,CAACA,OAAO,CAACjD,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAImD,iBAAiB,GAAG5B,WAAW,CAACW,GAAG,CAACgB,SAAS,CAAC,CAAC,CAAC;;QAEpDrC,wBAAwB,CAACqC,SAAS,EAAE,KAAK,CAAC;QAC1CZ,QAAQ,CAACa,iBAAiB,CAAC;MAC7B;IACF,CAAC,CAAC;;IAGFrC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACO,CAAC,CAAC;EACtF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}