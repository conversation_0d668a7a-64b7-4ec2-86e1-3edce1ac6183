{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"columnType\"];\nimport * as React from 'react';\nimport { INTERNAL_COL_DEFINE } from './utils/legacyUtil';\nfunction ColGroup(_ref) {\n  var colWidths = _ref.colWidths,\n    columns = _ref.columns,\n    columCount = _ref.columCount;\n  var cols = [];\n  var len = columCount || columns.length; // Only insert col with width & additional props\n  // Skip if rest col do not have any useful info\n\n  var mustInsert = false;\n  for (var i = len - 1; i >= 0; i -= 1) {\n    var width = colWidths[i];\n    var column = columns && columns[i];\n    var additionalProps = column && column[INTERNAL_COL_DEFINE];\n    if (width || additionalProps || mustInsert) {\n      var _ref2 = additionalProps || {},\n        columnType = _ref2.columnType,\n        restAdditionalProps = _objectWithoutProperties(_ref2, _excluded);\n      cols.unshift(/*#__PURE__*/React.createElement(\"col\", _extends({\n        key: i,\n        style: {\n          width: width\n        }\n      }, restAdditionalProps)));\n      mustInsert = true;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"colgroup\", null, cols);\n}\nexport default ColGroup;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "React", "INTERNAL_COL_DEFINE", "ColGroup", "_ref", "col<PERSON><PERSON><PERSON>", "columns", "columCount", "cols", "len", "length", "mustInsert", "i", "width", "column", "additionalProps", "_ref2", "columnType", "restAdditionalProps", "unshift", "createElement", "key", "style"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/ColGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"columnType\"];\nimport * as React from 'react';\nimport { INTERNAL_COL_DEFINE } from './utils/legacyUtil';\n\nfunction ColGroup(_ref) {\n  var colWidths = _ref.colWidths,\n      columns = _ref.columns,\n      columCount = _ref.columCount;\n  var cols = [];\n  var len = columCount || columns.length; // Only insert col with width & additional props\n  // Skip if rest col do not have any useful info\n\n  var mustInsert = false;\n\n  for (var i = len - 1; i >= 0; i -= 1) {\n    var width = colWidths[i];\n    var column = columns && columns[i];\n    var additionalProps = column && column[INTERNAL_COL_DEFINE];\n\n    if (width || additionalProps || mustInsert) {\n      var _ref2 = additionalProps || {},\n          columnType = _ref2.columnType,\n          restAdditionalProps = _objectWithoutProperties(_ref2, _excluded);\n\n      cols.unshift( /*#__PURE__*/React.createElement(\"col\", _extends({\n        key: i,\n        style: {\n          width: width\n        }\n      }, restAdditionalProps)));\n      mustInsert = true;\n    }\n  }\n\n  return /*#__PURE__*/React.createElement(\"colgroup\", null, cols);\n}\n\nexport default ColGroup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,YAAY,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,oBAAoB;AAExD,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAChC,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,GAAG,GAAGF,UAAU,IAAID,OAAO,CAACI,MAAM,CAAC,CAAC;EACxC;;EAEA,IAAIC,UAAU,GAAG,KAAK;EAEtB,KAAK,IAAIC,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACpC,IAAIC,KAAK,GAAGR,SAAS,CAACO,CAAC,CAAC;IACxB,IAAIE,MAAM,GAAGR,OAAO,IAAIA,OAAO,CAACM,CAAC,CAAC;IAClC,IAAIG,eAAe,GAAGD,MAAM,IAAIA,MAAM,CAACZ,mBAAmB,CAAC;IAE3D,IAAIW,KAAK,IAAIE,eAAe,IAAIJ,UAAU,EAAE;MAC1C,IAAIK,KAAK,GAAGD,eAAe,IAAI,CAAC,CAAC;QAC7BE,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,mBAAmB,GAAGnB,wBAAwB,CAACiB,KAAK,EAAEhB,SAAS,CAAC;MAEpEQ,IAAI,CAACW,OAAO,CAAE,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAEtB,QAAQ,CAAC;QAC7DuB,GAAG,EAAET,CAAC;QACNU,KAAK,EAAE;UACLT,KAAK,EAAEA;QACT;MACF,CAAC,EAAEK,mBAAmB,CAAC,CAAC,CAAC;MACzBP,UAAU,GAAG,IAAI;IACnB;EACF;EAEA,OAAO,aAAaV,KAAK,CAACmB,aAAa,CAAC,UAAU,EAAE,IAAI,EAAEZ,IAAI,CAAC;AACjE;AAEA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}