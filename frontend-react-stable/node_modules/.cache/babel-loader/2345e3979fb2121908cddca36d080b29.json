{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\n/** @internal Only used for Dropdown component. Do not use this in your production. */\nvar OverrideContext = /*#__PURE__*/React.createContext(null);\n/** @internal Only used for Dropdown component. Do not use this in your production. */\nexport var OverrideProvider = function OverrideProvider(_a) {\n  var children = _a.children,\n    restProps = __rest(_a, [\"children\"]);\n  var override = React.useContext(OverrideContext);\n  var context = React.useMemo(function () {\n    return _extends(_extends({}, override), restProps);\n  }, [override, restProps.prefixCls,\n  // restProps.expandIcon, Not mark as deps since this is a ReactNode\n  restProps.mode, restProps.selectable\n  // restProps.validator, Not mark as deps since this is a function\n  ]);\n  return /*#__PURE__*/React.createElement(OverrideContext.Provider, {\n    value: context\n  }, children);\n};\nexport default OverrideContext;", "map": {"version": 3, "names": ["_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "OverrideContext", "createContext", "OverrideProvider", "_a", "children", "restProps", "override", "useContext", "context", "useMemo", "prefixCls", "mode", "selectable", "createElement", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/menu/OverrideContext.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\n/** @internal Only used for Dropdown component. Do not use this in your production. */\nvar OverrideContext = /*#__PURE__*/React.createContext(null);\n/** @internal Only used for Dropdown component. Do not use this in your production. */\nexport var OverrideProvider = function OverrideProvider(_a) {\n  var children = _a.children,\n    restProps = __rest(_a, [\"children\"]);\n  var override = React.useContext(OverrideContext);\n  var context = React.useMemo(function () {\n    return _extends(_extends({}, override), restProps);\n  }, [override, restProps.prefixCls,\n  // restProps.expandIcon, Not mark as deps since this is a ReactNode\n  restProps.mode, restProps.selectable\n  // restProps.validator, Not mark as deps since this is a function\n  ]);\n\n  return /*#__PURE__*/React.createElement(OverrideContext.Provider, {\n    value: context\n  }, children);\n};\nexport default OverrideContext;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B;AACA,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,EAAE,EAAE;EAC1D,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;IACxBC,SAAS,GAAGpB,MAAM,CAACkB,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACtC,IAAIG,QAAQ,GAAGP,KAAK,CAACQ,UAAU,CAACP,eAAe,CAAC;EAChD,IAAIQ,OAAO,GAAGT,KAAK,CAACU,OAAO,CAAC,YAAY;IACtC,OAAOzB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,QAAQ,CAAC,EAAED,SAAS,CAAC;EACpD,CAAC,EAAE,CAACC,QAAQ,EAAED,SAAS,CAACK,SAAS;EACjC;EACAL,SAAS,CAACM,IAAI,EAAEN,SAAS,CAACO;EAC1B;EAAA,CACC,CAAC;EAEF,OAAO,aAAab,KAAK,CAACc,aAAa,CAACb,eAAe,CAACc,QAAQ,EAAE;IAChEC,KAAK,EAAEP;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd,CAAC;AACD,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}