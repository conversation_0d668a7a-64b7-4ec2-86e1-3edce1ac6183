{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Typo<PERSON>,Card,Alert,Tabs,Button,Table,Space,message,Modal,Descriptions,Statistic,Row,Col,Input,Tag,Popconfirm,Spin}from'antd';import{ReloadOutlined,EyeOutlined,DeleteOutlined,SearchOutlined,Bar<PERSON><PERSON>Outlined,DatabaseOutlined,TrophyOutlined}from'@ant-design/icons';import{modelRegistryAPI}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{TabPane}=Tabs;const ModelRegistryPage=()=>{var _statistics$best_mode,_selectedModel$file_p,_selectedModel$file_p2,_selectedModel$file_p3,_selectedModel$file_p4,_selectedModel$traini,_selectedModel$traini2,_selectedModel$traini3,_selectedModel$cleani,_selectedModel$model_,_selectedModel$model_2,_selectedModel$model_3,_selectedModel$model_4,_selectedModel$model_5;const[loading,setLoading]=useState(false);const[models,setModels]=useState([]);const[statistics,setStatistics]=useState(null);const[selectedModel,setSelectedModel]=useState(null);const[detailModalVisible,setDetailModalVisible]=useState(false);const[searchText,setSearchText]=useState('');const[activeTab,setActiveTab]=useState('1');// 获取模型列表\nconst fetchModels=async()=>{setLoading(true);try{const response=await modelRegistryAPI.listModels();console.log('模型列表响应:',response.data);// 添加调试日志\nif(response.data.success){setModels(response.data.models||[]);if(response.data.total_count>0){message.success(`📊 共找到 ${response.data.total_count} 个模型`);}}else{console.error('模型列表API返回失败:',response.data);message.error('获取模型列表失败');}}catch(error){var _error$response,_error$response$data;console.error('获取模型列表失败:',error);message.error(`❌ 获取模型列表失败: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||error.message}`);}finally{setLoading(false);}};// 获取统计信息\nconst fetchStatistics=async()=>{try{const response=await modelRegistryAPI.getStatistics();console.log('统计信息响应:',response.data);// 添加调试日志\nif(response.data.success){setStatistics(response.data.statistics);}else{console.error('统计信息API返回失败:',response.data);message.error('获取统计信息失败');}}catch(error){var _error$response2,_error$response2$data;console.error('获取统计信息失败:',error);message.error(`❌ 获取统计信息失败: ${((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message}`);}};// 获取模型详情\nconst fetchModelDetail=async modelId=>{try{const response=await modelRegistryAPI.getModelDetail(modelId);if(response.data.success){setSelectedModel(response.data.model);setDetailModalVisible(true);}}catch(error){var _error$response3,_error$response3$data;console.error('获取模型详情失败:',error);message.error(`❌ 获取模型详情失败: ${((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message}`);}};// 删除模型\nconst deleteModel=async modelId=>{try{const response=await modelRegistryAPI.deleteModel(modelId);if(response.data.success){message.success('✅ 模型删除成功');fetchModels();// 重新获取列表\n}}catch(error){var _error$response4,_error$response4$data;console.error('删除模型失败:',error);message.error(`❌ 删除模型失败: ${((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message}`);}};useEffect(()=>{fetchModels();fetchStatistics();},[]);// 模型列表表格列定义\nconst columns=[{title:'模型ID',dataIndex:'model_id',key:'model_id',width:200,filteredValue:searchText?[searchText]:null,onFilter:(value,record)=>record.model_id.toLowerCase().includes(value.toLowerCase())||record.protocol.toLowerCase().includes(value.toLowerCase())||record.datatype.toLowerCase().includes(value.toLowerCase()),render:text=>/*#__PURE__*/_jsx(Text,{code:true,style:{fontSize:'12px'},children:text})},{title:'协议类型',dataIndex:'protocol',key:'protocol',width:100,render:protocol=>/*#__PURE__*/_jsx(Tag,{color:protocol==='TCP'?'blue':protocol==='UDP'?'green':'orange',children:protocol})},{title:'数据类型',dataIndex:'datatype',key:'datatype',width:120,render:datatype=>/*#__PURE__*/_jsx(Tag,{color:\"purple\",children:datatype})},{title:'R² 分数',dataIndex:'r2_score',key:'r2_score',width:120,sorter:(a,b)=>a.r2_score-b.r2_score,render:score=>/*#__PURE__*/_jsx(Text,{strong:true,style:{color:score>0.8?'#52c41a':score>0.6?'#faad14':'#ff4d4f'},children:score.toFixed(4)})},{title:'训练时长',dataIndex:'training_duration',key:'training_duration',width:120,render:duration=>`${duration?duration.toFixed(2):'N/A'}s`},{title:'创建时间',dataIndex:'created_time',key:'created_time',width:180,sorter:(a,b)=>new Date(a.created_time).getTime()-new Date(b.created_time).getTime(),render:time=>new Date(time).toLocaleString()},{title:'操作',key:'actions',width:150,render:(_,record)=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>fetchModelDetail(record.model_id),children:\"\\u8BE6\\u60C5\"}),/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u8BA4\\u5220\\u9664\",description:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u6A21\\u578B\\u5417\\uFF1F\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\u3002\",onConfirm:()=>deleteModel(record.model_id),okText:\"\\u786E\\u8BA4\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{type:\"primary\",danger:true,size:\"small\",icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),children:\"\\u5220\\u9664\"})})]})}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6A21\\u578B\\u4ED3\\u5E93\\u7BA1\\u7406\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u96C6\\u4E2D\\u5C55\\u793A\\u3001\\u7BA1\\u7406\\u548C\\u67E5\\u770B\\u6240\\u6709\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6A21\\u578B\\u4FE1\\u606F\\u3002\"}),/*#__PURE__*/_jsxs(Tabs,{activeKey:activeTab,onChange:setActiveTab,style:{marginTop:24},children:[/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(DatabaseOutlined,{}),\"\\u6A21\\u578B\\u5217\\u8868\"]}),children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:16,display:'flex',justifyContent:'space-between',alignItems:'center'},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchModels,loading:loading,children:\"\\u5237\\u65B0\\u5217\\u8868\"}),/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u6A21\\u578BID\\u3001\\u534F\\u8BAE\\u6216\\u6570\\u636E\\u7C7B\\u578B\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),value:searchText,onChange:e=>setSearchText(e.target.value),style:{width:300},allowClear:true})]})}),models.length===0&&!loading?/*#__PURE__*/_jsx(Alert,{message:\"\\u6682\\u65E0\\u6A21\\u578B\",description:\"\\uD83D\\uDD0D \\u6682\\u65E0\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6A21\\u578B\\uFF0C\\u8BF7\\u5148\\u8FDB\\u884C\\u6A21\\u578B\\u8BAD\\u7EC3\\u3002\",type:\"info\",showIcon:true}):/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:models,rowKey:\"model_id\",loading:loading,pagination:{pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:total=>`共 ${total} 个模型`},scroll:{x:1200}})]})},\"1\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(BarChartOutlined,{}),\"\\u7EDF\\u8BA1\\u4FE1\\u606F\"]}),children:statistics?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u6A21\\u578B\\u6570\",value:statistics.total_models,prefix:/*#__PURE__*/_jsx(DatabaseOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E73\\u5747 R\\xB2 \\u5206\\u6570\",value:statistics.avg_r2_score,precision:4,prefix:/*#__PURE__*/_jsx(BarChartOutlined,{}),valueStyle:{color:'#52c41a'}})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6700\\u4F73\\u6A21\\u578B R\\xB2\",value:((_statistics$best_mode=statistics.best_model)===null||_statistics$best_mode===void 0?void 0:_statistics$best_mode.r2_score)||0,precision:4,prefix:/*#__PURE__*/_jsx(TrophyOutlined,{}),valueStyle:{color:'#faad14'}})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u534F\\u8BAE\\u5206\\u5E03\",size:\"small\",children:[statistics.protocols&&Object.entries(statistics.protocols).map(_ref=>{let[protocol,count]=_ref;return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Tag,{color:protocol==='TCP'?'blue':protocol==='UDP'?'green':'orange',children:protocol}),/*#__PURE__*/_jsxs(\"span\",{style:{marginLeft:8},children:[count,\" \\u4E2A\\u6A21\\u578B\"]})]},protocol);}),(!statistics.protocols||Object.keys(statistics.protocols).length===0)&&/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6682\\u65E0\\u6570\\u636E\"})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u6570\\u636E\\u7C7B\\u578B\\u5206\\u5E03\",size:\"small\",children:[statistics.datatypes&&Object.entries(statistics.datatypes).map(_ref2=>{let[datatype,count]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:8},children:[/*#__PURE__*/_jsx(Tag,{color:\"purple\",children:datatype}),/*#__PURE__*/_jsxs(\"span\",{style:{marginLeft:8},children:[count,\" \\u4E2A\\u6A21\\u578B\"]})]},datatype);}),(!statistics.datatypes||Object.keys(statistics.datatypes).length===0)&&/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6682\\u65E0\\u6570\\u636E\"})]})})]}),statistics.best_model&&/*#__PURE__*/_jsx(Card,{title:\"\\u6700\\u4F73\\u6A21\\u578B\\u4FE1\\u606F\",style:{marginTop:16},size:\"small\",children:/*#__PURE__*/_jsxs(Descriptions,{column:2,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6A21\\u578BID\",children:/*#__PURE__*/_jsx(Text,{code:true,children:statistics.best_model.model_id})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"R\\xB2 \\u5206\\u6570\",children:/*#__PURE__*/_jsx(Text,{strong:true,style:{color:'#faad14'},children:statistics.best_model.r2_score.toFixed(4)})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u534F\\u8BAE\\u7C7B\\u578B\",children:/*#__PURE__*/_jsx(Tag,{color:statistics.best_model.protocol==='TCP'?'blue':statistics.best_model.protocol==='UDP'?'green':'orange',children:statistics.best_model.protocol})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6570\\u636E\\u7C7B\\u578B\",children:/*#__PURE__*/_jsx(Tag,{color:\"purple\",children:statistics.best_model.datatype})})]})})]}):/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',marginTop:16},children:/*#__PURE__*/_jsx(Text,{children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u7EDF\\u8BA1\\u4FE1\\u606F...\"})})]})},\"2\")]}),/*#__PURE__*/_jsx(Modal,{title:\"\\u6A21\\u578B\\u8BE6\\u7EC6\\u4FE1\\u606F\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:800,children:selectedModel&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u57FA\\u672C\\u4FE1\\u606F\",bordered:true,column:2,size:\"small\",children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6A21\\u578BID\",span:2,children:/*#__PURE__*/_jsx(Text,{code:true,children:selectedModel.model_id})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u534F\\u8BAE\\u7C7B\\u578B\",children:/*#__PURE__*/_jsx(Tag,{color:selectedModel.protocol==='TCP'?'blue':selectedModel.protocol==='UDP'?'green':'orange',children:selectedModel.protocol})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6570\\u636E\\u7C7B\\u578B\",children:/*#__PURE__*/_jsx(Tag,{color:\"purple\",children:selectedModel.datatype})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"R\\xB2 \\u5206\\u6570\",children:/*#__PURE__*/_jsx(Text,{strong:true,style:{color:selectedModel.r2_score>0.8?'#52c41a':selectedModel.r2_score>0.6?'#faad14':'#ff4d4f'},children:selectedModel.r2_score.toFixed(4)})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BAD\\u7EC3\\u5F00\\u59CB\\u65F6\\u95F4\",children:selectedModel.training_time?new Date(selectedModel.training_time).toLocaleString():'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u521B\\u5EFA\\u65F6\\u95F4\",span:2,children:new Date(selectedModel.created_time).toLocaleString()})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u6A21\\u578B\\u6587\\u4EF6\",bordered:true,column:1,size:\"small\",style:{marginTop:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6A21\\u578B\\u6587\\u4EF6\",children:/*#__PURE__*/_jsx(Text,{code:true,children:((_selectedModel$file_p=selectedModel.file_paths)===null||_selectedModel$file_p===void 0?void 0:_selectedModel$file_p.model_path)||'N/A'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u53C2\\u6570\\u6587\\u4EF6\",children:/*#__PURE__*/_jsx(Text,{code:true,children:((_selectedModel$file_p2=selectedModel.file_paths)===null||_selectedModel$file_p2===void 0?void 0:_selectedModel$file_p2.params_path)||'N/A'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",children:/*#__PURE__*/_jsx(Text,{code:true,children:((_selectedModel$file_p3=selectedModel.file_paths)===null||_selectedModel$file_p3===void 0?void 0:_selectedModel$file_p3.scaler_path)||'N/A'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6D4B\\u8BD5\\u6570\\u636E\\u6587\\u4EF6\",children:/*#__PURE__*/_jsx(Text,{code:true,children:((_selectedModel$file_p4=selectedModel.file_paths)===null||_selectedModel$file_p4===void 0?void 0:_selectedModel$file_p4.test_data_path)||'N/A'})})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u8BAD\\u7EC3\\u53C2\\u6570\",bordered:true,column:2,size:\"small\",style:{marginTop:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5B66\\u4E60\\u7387\",children:((_selectedModel$traini=selectedModel.training_params)===null||_selectedModel$traini===void 0?void 0:_selectedModel$traini.learning_rate)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6279\\u6B21\\u5927\\u5C0F\",children:((_selectedModel$traini2=selectedModel.training_params)===null||_selectedModel$traini2===void 0?void 0:_selectedModel$traini2.batch_size)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BAD\\u7EC3\\u8F6E\\u6570\",children:((_selectedModel$traini3=selectedModel.training_params)===null||_selectedModel$traini3===void 0?void 0:_selectedModel$traini3.epochs)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6E05\\u6D17\\u9608\\u503C\",children:((_selectedModel$cleani=selectedModel.cleaning_threshold)===null||_selectedModel$cleani===void 0?void 0:_selectedModel$cleani.toFixed(4))||'N/A'})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u6A21\\u578B\\u67B6\\u6784\",bordered:true,column:2,size:\"small\",style:{marginTop:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6A21\\u578B\\u7C7B\\u578B\",children:((_selectedModel$model_=selectedModel.model_architecture)===null||_selectedModel$model_===void 0?void 0:_selectedModel$model_.type)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\",children:((_selectedModel$model_2=selectedModel.model_architecture)===null||_selectedModel$model_2===void 0?void 0:_selectedModel$model_2.hidden_size)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7F51\\u7EDC\\u5C42\\u6570\",children:((_selectedModel$model_3=selectedModel.model_architecture)===null||_selectedModel$model_3===void 0?void 0:_selectedModel$model_3.num_layers)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5E8F\\u5217\\u957F\\u5EA6\",children:((_selectedModel$model_4=selectedModel.model_architecture)===null||_selectedModel$model_4===void 0?void 0:_selectedModel$model_4.sequence_length)||'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"Dropout\\u7387\",span:2,children:((_selectedModel$model_5=selectedModel.model_architecture)===null||_selectedModel$model_5===void 0?void 0:_selectedModel$model_5.dropout)||'N/A'})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u6570\\u636E\\u4FE1\\u606F\",bordered:true,column:2,size:\"small\",style:{marginTop:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6570\\u636E\\u6587\\u4EF6\",span:2,children:/*#__PURE__*/_jsx(Text,{code:true,children:selectedModel.source_data||'N/A'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BAD\\u7EC3\\u6570\\u636E\\u5F62\\u72B6\",children:selectedModel.train_shape?`[${selectedModel.train_shape.join(', ')}]`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6D4B\\u8BD5\\u6570\\u636E\\u5F62\\u72B6\",children:selectedModel.test_shape?`[${selectedModel.test_shape.join(', ')}]`:'N/A'})]}),/*#__PURE__*/_jsxs(Descriptions,{title:\"\\u8D44\\u6E90\\u4F7F\\u7528\",bordered:true,column:2,size:\"small\",style:{marginTop:16},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8BAD\\u7EC3\\u65F6\\u957F\",children:selectedModel.training_duration?`${selectedModel.training_duration.toFixed(2)}s`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"CPU\\u4F7F\\u7528\\u7387\",children:selectedModel.cpu_usage?`${selectedModel.cpu_usage.toFixed(2)}%`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u5185\\u5B58\\u4F7F\\u7528\",children:selectedModel.memory_usage?`${selectedModel.memory_usage.toFixed(2)}MB`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"GPU\\u5185\\u5B58\",children:selectedModel.gpu_memory?`${selectedModel.gpu_memory.toFixed(2)}MB`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"GPU\\u5229\\u7528\\u7387\",span:2,children:selectedModel.gpu_utilization?`${selectedModel.gpu_utilization.toFixed(2)}%`:'N/A'})]})]})})]});};export default ModelRegistryPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "Card", "<PERSON><PERSON>", "Tabs", "<PERSON><PERSON>", "Table", "Space", "message", "Modal", "Descriptions", "Statistic", "Row", "Col", "Input", "Tag", "Popconfirm", "Spin", "ReloadOutlined", "EyeOutlined", "DeleteOutlined", "SearchOutlined", "BarChartOutlined", "DatabaseOutlined", "TrophyOutlined", "modelRegistryAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "TabPane", "ModelRegistryPage", "_statistics$best_mode", "_selectedModel$file_p", "_selectedModel$file_p2", "_selectedModel$file_p3", "_selectedModel$file_p4", "_selectedModel$traini", "_selectedModel$traini2", "_selectedModel$traini3", "_selectedModel$cleani", "_selectedModel$model_", "_selectedModel$model_2", "_selectedModel$model_3", "_selectedModel$model_4", "_selectedModel$model_5", "loading", "setLoading", "models", "setModels", "statistics", "setStatistics", "selected<PERSON><PERSON>l", "setSelectedModel", "detailModalVisible", "setDetailModalVisible", "searchText", "setSearchText", "activeTab", "setActiveTab", "fetchModels", "response", "listModels", "console", "log", "data", "success", "total_count", "error", "_error$response", "_error$response$data", "detail", "fetchStatistics", "getStatistics", "_error$response2", "_error$response2$data", "fetchModelDetail", "modelId", "getModelDetail", "model", "_error$response3", "_error$response3$data", "deleteModel", "_error$response4", "_error$response4$data", "columns", "title", "dataIndex", "key", "width", "filteredValue", "onFilter", "value", "record", "model_id", "toLowerCase", "includes", "protocol", "datatype", "render", "text", "code", "style", "fontSize", "children", "color", "sorter", "a", "b", "r2_score", "score", "strong", "toFixed", "duration", "Date", "created_time", "getTime", "time", "toLocaleString", "_", "type", "size", "icon", "onClick", "description", "onConfirm", "okText", "cancelText", "danger", "level", "fontWeight", "marginBottom", "active<PERSON><PERSON>", "onChange", "marginTop", "tab", "display", "justifyContent", "alignItems", "placeholder", "prefix", "e", "target", "allowClear", "length", "showIcon", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "gutter", "span", "total_models", "valueStyle", "avg_r2_score", "precision", "best_model", "protocols", "Object", "entries", "map", "_ref", "count", "marginLeft", "keys", "datatypes", "_ref2", "column", "<PERSON><PERSON>", "label", "textAlign", "open", "onCancel", "footer", "bordered", "training_time", "file_paths", "model_path", "params_path", "scaler_path", "test_data_path", "training_params", "learning_rate", "batch_size", "epochs", "cleaning_threshold", "model_architecture", "hidden_size", "num_layers", "sequence_length", "dropout", "source_data", "train_shape", "join", "test_shape", "training_duration", "cpu_usage", "memory_usage", "gpu_memory", "gpu_utilization"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelRegistryPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>ton,\n  Table,\n  Space,\n  message,\n  Modal,\n  Descriptions,\n  Statistic,\n  Row,\n  Col,\n  Input,\n  Tag,\n  Popconfirm,\n  Spin\n} from 'antd';\nimport {\n  ReloadOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  DatabaseOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport { modelRegistryAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\ninterface ModelInfo {\n  model_id: string;\n  protocol: string;\n  datatype: string;\n  r2_score: number;\n  training_time: string;\n  created_time: string;\n  source_data: string;\n  cleaning_threshold: number;\n  training_duration: number;\n  cpu_usage: number;\n  memory_usage: number;\n  gpu_memory: number;\n  gpu_utilization: number;\n  file_paths: {\n    model_path: string;\n    params_path: string;\n    scaler_path: string;\n    test_data_path: string;\n  };\n  training_params: {\n    learning_rate: number;\n    batch_size: number;\n    epochs: number;\n  };\n  model_architecture: {\n    type: string;\n    hidden_size: number;\n    num_layers: number;\n    sequence_length: number;\n    dropout: number;\n  };\n  train_shape: number[];\n  test_shape: number[];\n}\n\ninterface Statistics {\n  total_models: number;\n  avg_r2_score: number;\n  best_model: {\n    model_id: string;\n    r2_score: number;\n    protocol: string;\n    datatype: string;\n  } | null;\n  protocols: { [key: string]: number };\n  datatypes: { [key: string]: number };\n}\n\nconst ModelRegistryPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState<ModelInfo[]>([]);\n  const [statistics, setStatistics] = useState<Statistics | null>(null);\n  const [selectedModel, setSelectedModel] = useState<ModelInfo | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [activeTab, setActiveTab] = useState('1');\n\n  // 获取模型列表\n  const fetchModels = async () => {\n    setLoading(true);\n    try {\n      const response = await modelRegistryAPI.listModels();\n      console.log('模型列表响应:', response.data); // 添加调试日志\n      if (response.data.success) {\n        setModels(response.data.models || []);\n        if (response.data.total_count > 0) {\n          message.success(`📊 共找到 ${response.data.total_count} 个模型`);\n        }\n      } else {\n        console.error('模型列表API返回失败:', response.data);\n        message.error('获取模型列表失败');\n      }\n    } catch (error: any) {\n      console.error('获取模型列表失败:', error);\n      message.error(`❌ 获取模型列表失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      const response = await modelRegistryAPI.getStatistics();\n      console.log('统计信息响应:', response.data); // 添加调试日志\n      if (response.data.success) {\n        setStatistics(response.data.statistics);\n      } else {\n        console.error('统计信息API返回失败:', response.data);\n        message.error('获取统计信息失败');\n      }\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n      message.error(`❌ 获取统计信息失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 获取模型详情\n  const fetchModelDetail = async (modelId: string) => {\n    try {\n      const response = await modelRegistryAPI.getModelDetail(modelId);\n      if (response.data.success) {\n        setSelectedModel(response.data.model);\n        setDetailModalVisible(true);\n      }\n    } catch (error: any) {\n      console.error('获取模型详情失败:', error);\n      message.error(`❌ 获取模型详情失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 删除模型\n  const deleteModel = async (modelId: string) => {\n    try {\n      const response = await modelRegistryAPI.deleteModel(modelId);\n      if (response.data.success) {\n        message.success('✅ 模型删除成功');\n        fetchModels(); // 重新获取列表\n      }\n    } catch (error: any) {\n      console.error('删除模型失败:', error);\n      message.error(`❌ 删除模型失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  useEffect(() => {\n    fetchModels();\n    fetchStatistics();\n  }, []);\n\n  // 模型列表表格列定义\n  const columns = [\n    {\n      title: '模型ID',\n      dataIndex: 'model_id',\n      key: 'model_id',\n      width: 200,\n      filteredValue: searchText ? [searchText] : null,\n      onFilter: (value: any, record: ModelInfo) =>\n        record.model_id.toLowerCase().includes(value.toLowerCase()) ||\n        record.protocol.toLowerCase().includes(value.toLowerCase()) ||\n        record.datatype.toLowerCase().includes(value.toLowerCase()),\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>{text}</Text>\n      ),\n    },\n    {\n      title: '协议类型',\n      dataIndex: 'protocol',\n      key: 'protocol',\n      width: 100,\n      render: (protocol: string) => (\n        <Tag color={protocol === 'TCP' ? 'blue' : protocol === 'UDP' ? 'green' : 'orange'}>\n          {protocol}\n        </Tag>\n      ),\n    },\n    {\n      title: '数据类型',\n      dataIndex: 'datatype',\n      key: 'datatype',\n      width: 120,\n      render: (datatype: string) => (\n        <Tag color=\"purple\">{datatype}</Tag>\n      ),\n    },\n    {\n      title: 'R² 分数',\n      dataIndex: 'r2_score',\n      key: 'r2_score',\n      width: 120,\n      sorter: (a: ModelInfo, b: ModelInfo) => a.r2_score - b.r2_score,\n      render: (score: number) => (\n        <Text strong style={{ color: score > 0.8 ? '#52c41a' : score > 0.6 ? '#faad14' : '#ff4d4f' }}>\n          {score.toFixed(4)}\n        </Text>\n      ),\n    },\n    {\n      title: '训练时长',\n      dataIndex: 'training_duration',\n      key: 'training_duration',\n      width: 120,\n      render: (duration: number) => `${duration ? duration.toFixed(2) : 'N/A'}s`,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      width: 180,\n      sorter: (a: ModelInfo, b: ModelInfo) => new Date(a.created_time).getTime() - new Date(b.created_time).getTime(),\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 150,\n      render: (_, record: ModelInfo) => (\n        <Space>\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => fetchModelDetail(record.model_id)}\n          >\n            详情\n          </Button>\n          <Popconfirm\n            title=\"确认删除\"\n            description=\"确定要删除这个模型吗？此操作不可恢复。\"\n            onConfirm={() => deleteModel(record.model_id)}\n            okText=\"确认\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"primary\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型仓库管理</Title>\n      <Text type=\"secondary\">\n        集中展示、管理和查看所有已训练的模型信息。\n      </Text>\n\n      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: 24 }}>\n        <TabPane tab={<span><DatabaseOutlined />模型列表</span>} key=\"1\">\n          <Card>\n            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <Space>\n                <Button\n                  type=\"primary\"\n                  icon={<ReloadOutlined />}\n                  onClick={fetchModels}\n                  loading={loading}\n                >\n                  刷新列表\n                </Button>\n                <Input\n                  placeholder=\"搜索模型ID、协议或数据类型\"\n                  prefix={<SearchOutlined />}\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  style={{ width: 300 }}\n                  allowClear\n                />\n              </Space>\n            </div>\n\n            {models.length === 0 && !loading ? (\n              <Alert\n                message=\"暂无模型\"\n                description=\"🔍 暂无已训练的模型，请先进行模型训练。\"\n                type=\"info\"\n                showIcon\n              />\n            ) : (\n              <Table\n                columns={columns}\n                dataSource={models}\n                rowKey=\"model_id\"\n                loading={loading}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 个模型`,\n                }}\n                scroll={{ x: 1200 }}\n              />\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><BarChartOutlined />统计信息</span>} key=\"2\">\n          {statistics ? (\n            <div>\n              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"总模型数\"\n                      value={statistics.total_models}\n                      prefix={<DatabaseOutlined />}\n                      valueStyle={{ color: '#1890ff' }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"平均 R² 分数\"\n                      value={statistics.avg_r2_score}\n                      precision={4}\n                      prefix={<BarChartOutlined />}\n                      valueStyle={{ color: '#52c41a' }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"最佳模型 R²\"\n                      value={statistics.best_model?.r2_score || 0}\n                      precision={4}\n                      prefix={<TrophyOutlined />}\n                      valueStyle={{ color: '#faad14' }}\n                    />\n                  </Card>\n                </Col>\n              </Row>\n\n              <Row gutter={[16, 16]}>\n                <Col span={12}>\n                  <Card title=\"协议分布\" size=\"small\">\n                    {statistics.protocols && Object.entries(statistics.protocols).map(([protocol, count]) => (\n                      <div key={protocol} style={{ marginBottom: 8 }}>\n                        <Tag color={protocol === 'TCP' ? 'blue' : protocol === 'UDP' ? 'green' : 'orange'}>\n                          {protocol}\n                        </Tag>\n                        <span style={{ marginLeft: 8 }}>{count} 个模型</span>\n                      </div>\n                    ))}\n                    {(!statistics.protocols || Object.keys(statistics.protocols).length === 0) && (\n                      <Text type=\"secondary\">暂无数据</Text>\n                    )}\n                  </Card>\n                </Col>\n                <Col span={12}>\n                  <Card title=\"数据类型分布\" size=\"small\">\n                    {statistics.datatypes && Object.entries(statistics.datatypes).map(([datatype, count]) => (\n                      <div key={datatype} style={{ marginBottom: 8 }}>\n                        <Tag color=\"purple\">{datatype}</Tag>\n                        <span style={{ marginLeft: 8 }}>{count} 个模型</span>\n                      </div>\n                    ))}\n                    {(!statistics.datatypes || Object.keys(statistics.datatypes).length === 0) && (\n                      <Text type=\"secondary\">暂无数据</Text>\n                    )}\n                  </Card>\n                </Col>\n              </Row>\n\n              {statistics.best_model && (\n                <Card title=\"最佳模型信息\" style={{ marginTop: 16 }} size=\"small\">\n                  <Descriptions column={2} size=\"small\">\n                    <Descriptions.Item label=\"模型ID\">\n                      <Text code>{statistics.best_model.model_id}</Text>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"R² 分数\">\n                      <Text strong style={{ color: '#faad14' }}>\n                        {statistics.best_model.r2_score.toFixed(4)}\n                      </Text>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"协议类型\">\n                      <Tag color={statistics.best_model.protocol === 'TCP' ? 'blue' :\n                                  statistics.best_model.protocol === 'UDP' ? 'green' : 'orange'}>\n                        {statistics.best_model.protocol}\n                      </Tag>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"数据类型\">\n                      <Tag color=\"purple\">{statistics.best_model.datatype}</Tag>\n                    </Descriptions.Item>\n                  </Descriptions>\n                </Card>\n              )}\n            </div>\n          ) : (\n            <Card>\n              <Spin size=\"large\" />\n              <div style={{ textAlign: 'center', marginTop: 16 }}>\n                <Text>正在加载统计信息...</Text>\n              </div>\n            </Card>\n          )}\n        </TabPane>\n      </Tabs>\n\n      {/* 模型详情弹窗 */}\n      <Modal\n        title=\"模型详细信息\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedModel && (\n          <div>\n            <Descriptions title=\"基本信息\" bordered column={2} size=\"small\">\n              <Descriptions.Item label=\"模型ID\" span={2}>\n                <Text code>{selectedModel.model_id}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"协议类型\">\n                <Tag color={selectedModel.protocol === 'TCP' ? 'blue' :\n                            selectedModel.protocol === 'UDP' ? 'green' : 'orange'}>\n                  {selectedModel.protocol}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"数据类型\">\n                <Tag color=\"purple\">{selectedModel.datatype}</Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"R² 分数\">\n                <Text strong style={{ color: selectedModel.r2_score > 0.8 ? '#52c41a' :\n                                           selectedModel.r2_score > 0.6 ? '#faad14' : '#ff4d4f' }}>\n                  {selectedModel.r2_score.toFixed(4)}\n                </Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练开始时间\">\n                {selectedModel.training_time ? new Date(selectedModel.training_time).toLocaleString() : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\" span={2}>\n                {new Date(selectedModel.created_time).toLocaleString()}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"模型文件\" bordered column={1} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"模型文件\">\n                <Text code>{selectedModel.file_paths?.model_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"参数文件\">\n                <Text code>{selectedModel.file_paths?.params_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"标准化器文件\">\n                <Text code>{selectedModel.file_paths?.scaler_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"测试数据文件\">\n                <Text code>{selectedModel.file_paths?.test_data_path || 'N/A'}</Text>\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"训练参数\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"学习率\">\n                {selectedModel.training_params?.learning_rate || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"批次大小\">\n                {selectedModel.training_params?.batch_size || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练轮数\">\n                {selectedModel.training_params?.epochs || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"清洗阈值\">\n                {selectedModel.cleaning_threshold?.toFixed(4) || 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"模型架构\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"模型类型\">\n                {selectedModel.model_architecture?.type || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"隐藏层大小\">\n                {selectedModel.model_architecture?.hidden_size || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"网络层数\">\n                {selectedModel.model_architecture?.num_layers || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"序列长度\">\n                {selectedModel.model_architecture?.sequence_length || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Dropout率\" span={2}>\n                {selectedModel.model_architecture?.dropout || 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"数据信息\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"数据文件\" span={2}>\n                <Text code>{selectedModel.source_data || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练数据形状\">\n                {selectedModel.train_shape ? `[${selectedModel.train_shape.join(', ')}]` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"测试数据形状\">\n                {selectedModel.test_shape ? `[${selectedModel.test_shape.join(', ')}]` : 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"资源使用\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"训练时长\">\n                {selectedModel.training_duration ? `${selectedModel.training_duration.toFixed(2)}s` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"CPU使用率\">\n                {selectedModel.cpu_usage ? `${selectedModel.cpu_usage.toFixed(2)}%` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"内存使用\">\n                {selectedModel.memory_usage ? `${selectedModel.memory_usage.toFixed(2)}MB` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"GPU内存\">\n                {selectedModel.gpu_memory ? `${selectedModel.gpu_memory.toFixed(2)}MB` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"GPU利用率\" span={2}>\n                {selectedModel.gpu_utilization ? `${selectedModel.gpu_utilization.toFixed(2)}%` : 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default ModelRegistryPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,UAAU,CACVC,IAAI,CACJC,KAAK,CACLC,IAAI,CACJC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,KAAK,CACLC,YAAY,CACZC,SAAS,CACTC,GAAG,CACHC,GAAG,CACHC,KAAK,CACLC,GAAG,CACHC,UAAU,CACVC,IAAI,KACC,MAAM,CACb,OACEC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,gBAAgB,CAChBC,gBAAgB,CAChBC,cAAc,KACT,mBAAmB,CAC1B,OAASC,gBAAgB,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG9B,UAAU,CAClC,KAAM,CAAE+B,OAAQ,CAAC,CAAG5B,IAAI,CAmDxB,KAAM,CAAA6B,iBAA2B,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACxC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmD,MAAM,CAAEC,SAAS,CAAC,CAAGpD,QAAQ,CAAc,EAAE,CAAC,CACrD,KAAM,CAACqD,UAAU,CAAEC,aAAa,CAAC,CAAGtD,QAAQ,CAAoB,IAAI,CAAC,CACrE,KAAM,CAACuD,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAAmB,IAAI,CAAC,CAC1E,KAAM,CAACyD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC2D,UAAU,CAAEC,aAAa,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6D,SAAS,CAAEC,YAAY,CAAC,CAAG9D,QAAQ,CAAC,GAAG,CAAC,CAE/C;AACA,KAAM,CAAA+D,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9Bb,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAtC,gBAAgB,CAACuC,UAAU,CAAC,CAAC,CACpDC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEH,QAAQ,CAACI,IAAI,CAAC,CAAE;AACvC,GAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAE,CACzBjB,SAAS,CAACY,QAAQ,CAACI,IAAI,CAACjB,MAAM,EAAI,EAAE,CAAC,CACrC,GAAIa,QAAQ,CAACI,IAAI,CAACE,WAAW,CAAG,CAAC,CAAE,CACjC7D,OAAO,CAAC4D,OAAO,CAAC,UAAUL,QAAQ,CAACI,IAAI,CAACE,WAAW,MAAM,CAAC,CAC5D,CACF,CAAC,IAAM,CACLJ,OAAO,CAACK,KAAK,CAAC,cAAc,CAAEP,QAAQ,CAACI,IAAI,CAAC,CAC5C3D,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAE,MAAOA,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnBP,OAAO,CAACK,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC9D,OAAO,CAAC8D,KAAK,CAAC,eAAe,EAAAC,eAAA,CAAAD,KAAK,CAACP,QAAQ,UAAAQ,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBJ,IAAI,UAAAK,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAIH,KAAK,CAAC9D,OAAO,EAAE,CAAC,CAC/E,CAAC,OAAS,CACRyC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAtC,gBAAgB,CAACkD,aAAa,CAAC,CAAC,CACvDV,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEH,QAAQ,CAACI,IAAI,CAAC,CAAE;AACvC,GAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAE,CACzBf,aAAa,CAACU,QAAQ,CAACI,IAAI,CAACf,UAAU,CAAC,CACzC,CAAC,IAAM,CACLa,OAAO,CAACK,KAAK,CAAC,cAAc,CAAEP,QAAQ,CAACI,IAAI,CAAC,CAC5C3D,OAAO,CAAC8D,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAE,MAAOA,KAAU,CAAE,KAAAM,gBAAA,CAAAC,qBAAA,CACnBZ,OAAO,CAACK,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC9D,OAAO,CAAC8D,KAAK,CAAC,eAAe,EAAAM,gBAAA,CAAAN,KAAK,CAACP,QAAQ,UAAAa,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBT,IAAI,UAAAU,qBAAA,iBAApBA,qBAAA,CAAsBJ,MAAM,GAAIH,KAAK,CAAC9D,OAAO,EAAE,CAAC,CAC/E,CACF,CAAC,CAED;AACA,KAAM,CAAAsE,gBAAgB,CAAG,KAAO,CAAAC,OAAe,EAAK,CAClD,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAtC,gBAAgB,CAACuD,cAAc,CAACD,OAAO,CAAC,CAC/D,GAAIhB,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAE,CACzBb,gBAAgB,CAACQ,QAAQ,CAACI,IAAI,CAACc,KAAK,CAAC,CACrCxB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CAAE,MAAOa,KAAU,CAAE,KAAAY,gBAAA,CAAAC,qBAAA,CACnBlB,OAAO,CAACK,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC9D,OAAO,CAAC8D,KAAK,CAAC,eAAe,EAAAY,gBAAA,CAAAZ,KAAK,CAACP,QAAQ,UAAAmB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBf,IAAI,UAAAgB,qBAAA,iBAApBA,qBAAA,CAAsBV,MAAM,GAAIH,KAAK,CAAC9D,OAAO,EAAE,CAAC,CAC/E,CACF,CAAC,CAED;AACA,KAAM,CAAA4E,WAAW,CAAG,KAAO,CAAAL,OAAe,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAtC,gBAAgB,CAAC2D,WAAW,CAACL,OAAO,CAAC,CAC5D,GAAIhB,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAE,CACzB5D,OAAO,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAC3BN,WAAW,CAAC,CAAC,CAAE;AACjB,CACF,CAAE,MAAOQ,KAAU,CAAE,KAAAe,gBAAA,CAAAC,qBAAA,CACnBrB,OAAO,CAACK,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B9D,OAAO,CAAC8D,KAAK,CAAC,aAAa,EAAAe,gBAAA,CAAAf,KAAK,CAACP,QAAQ,UAAAsB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBlB,IAAI,UAAAmB,qBAAA,iBAApBA,qBAAA,CAAsBb,MAAM,GAAIH,KAAK,CAAC9D,OAAO,EAAE,CAAC,CAC7E,CACF,CAAC,CAEDR,SAAS,CAAC,IAAM,CACd8D,WAAW,CAAC,CAAC,CACbY,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAa,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVC,aAAa,CAAElC,UAAU,CAAG,CAACA,UAAU,CAAC,CAAG,IAAI,CAC/CmC,QAAQ,CAAEA,CAACC,KAAU,CAAEC,MAAiB,GACtCA,MAAM,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EAC3DF,MAAM,CAACI,QAAQ,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EAC3DF,MAAM,CAACK,QAAQ,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,CAC7DI,MAAM,CAAGC,IAAY,eACnB3E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAEJ,IAAI,CAAO,CAExD,CAAC,CACD,CACEd,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVU,MAAM,CAAGF,QAAgB,eACvBxE,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAER,QAAQ,GAAK,KAAK,CAAG,MAAM,CAAGA,QAAQ,GAAK,KAAK,CAAG,OAAO,CAAG,QAAS,CAAAO,QAAA,CAC/EP,QAAQ,CACN,CAET,CAAC,CACD,CACEX,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACVU,MAAM,CAAGD,QAAgB,eACvBzE,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAC,QAAQ,CAAAD,QAAA,CAAEN,QAAQ,CAAM,CAEvC,CAAC,CACD,CACEZ,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,KAAK,CAAE,GAAG,CACViB,MAAM,CAAEA,CAACC,CAAY,CAAEC,CAAY,GAAKD,CAAC,CAACE,QAAQ,CAAGD,CAAC,CAACC,QAAQ,CAC/DV,MAAM,CAAGW,KAAa,eACpBrF,IAAA,CAACI,IAAI,EAACkF,MAAM,MAACT,KAAK,CAAE,CAAEG,KAAK,CAAEK,KAAK,CAAG,GAAG,CAAG,SAAS,CAAGA,KAAK,CAAG,GAAG,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAN,QAAA,CAC1FM,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CACb,CAEV,CAAC,CACD,CACE1B,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,mBAAmB,CAC9BC,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,GAAG,CACVU,MAAM,CAAGc,QAAgB,EAAK,GAAGA,QAAQ,CAAGA,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,GACzE,CAAC,CACD,CACE1B,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,cAAc,CACzBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,GAAG,CACViB,MAAM,CAAEA,CAACC,CAAY,CAAEC,CAAY,GAAK,GAAI,CAAAM,IAAI,CAACP,CAAC,CAACQ,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAG,GAAI,CAAAF,IAAI,CAACN,CAAC,CAACO,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAC/GjB,MAAM,CAAGkB,IAAY,EAAK,GAAI,CAAAH,IAAI,CAACG,IAAI,CAAC,CAACC,cAAc,CAAC,CAC1D,CAAC,CACD,CACEhC,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,GAAG,CACVU,MAAM,CAAEA,CAACoB,CAAC,CAAE1B,MAAiB,gBAC3BlE,KAAA,CAACtB,KAAK,EAAAmG,QAAA,eACJ/E,IAAA,CAACtB,MAAM,EACLqH,IAAI,CAAC,SAAS,CACdC,IAAI,CAAC,OAAO,CACZC,IAAI,cAAEjG,IAAA,CAACR,WAAW,GAAE,CAAE,CACtB0G,OAAO,CAAEA,CAAA,GAAM/C,gBAAgB,CAACiB,MAAM,CAACC,QAAQ,CAAE,CAAAU,QAAA,CAClD,cAED,CAAQ,CAAC,cACT/E,IAAA,CAACX,UAAU,EACTwE,KAAK,CAAC,0BAAM,CACZsC,WAAW,CAAC,oHAAqB,CACjCC,SAAS,CAAEA,CAAA,GAAM3C,WAAW,CAACW,MAAM,CAACC,QAAQ,CAAE,CAC9CgC,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAvB,QAAA,cAEf/E,IAAA,CAACtB,MAAM,EACLqH,IAAI,CAAC,SAAS,CACdQ,MAAM,MACNP,IAAI,CAAC,OAAO,CACZC,IAAI,cAAEjG,IAAA,CAACP,cAAc,GAAE,CAAE,CAAAsF,QAAA,CAC1B,cAED,CAAQ,CAAC,CACC,CAAC,EACR,CAEX,CAAC,CACF,CAED,mBACE7E,KAAA,QAAA6E,QAAA,eACE/E,IAAA,CAACG,KAAK,EAACqG,KAAK,CAAE,CAAE,CAAC3B,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAE2B,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAA3B,QAAA,CAAC,sCAAM,CAAO,CAAC,cAClG/E,IAAA,CAACI,IAAI,EAAC2F,IAAI,CAAC,WAAW,CAAAhB,QAAA,CAAC,gIAEvB,CAAM,CAAC,cAEP7E,KAAA,CAACzB,IAAI,EAACkI,SAAS,CAAE1E,SAAU,CAAC2E,QAAQ,CAAE1E,YAAa,CAAC2C,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eAC3E/E,IAAA,CAACK,OAAO,EAACyG,GAAG,cAAE5G,KAAA,SAAA6E,QAAA,eAAM/E,IAAA,CAACJ,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAmF,QAAA,cAClD7E,KAAA,CAAC3B,IAAI,EAAAwG,QAAA,eACH/E,IAAA,QAAK6E,KAAK,CAAE,CAAE6B,YAAY,CAAE,EAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAlC,QAAA,cACvG7E,KAAA,CAACtB,KAAK,EAAAmG,QAAA,eACJ/E,IAAA,CAACtB,MAAM,EACLqH,IAAI,CAAC,SAAS,CACdE,IAAI,cAAEjG,IAAA,CAACT,cAAc,GAAE,CAAE,CACzB2G,OAAO,CAAE/D,WAAY,CACrBd,OAAO,CAAEA,OAAQ,CAAA0D,QAAA,CAClB,0BAED,CAAQ,CAAC,cACT/E,IAAA,CAACb,KAAK,EACJ+H,WAAW,CAAC,4EAAgB,CAC5BC,MAAM,cAAEnH,IAAA,CAACN,cAAc,GAAE,CAAE,CAC3ByE,KAAK,CAAEpC,UAAW,CAClB6E,QAAQ,CAAGQ,CAAC,EAAKpF,aAAa,CAACoF,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE,CAC/CU,KAAK,CAAE,CAAEb,KAAK,CAAE,GAAI,CAAE,CACtBsD,UAAU,MACX,CAAC,EACG,CAAC,CACL,CAAC,CAEL/F,MAAM,CAACgG,MAAM,GAAK,CAAC,EAAI,CAAClG,OAAO,cAC9BrB,IAAA,CAACxB,KAAK,EACJK,OAAO,CAAC,0BAAM,CACdsH,WAAW,CAAC,2HAAuB,CACnCJ,IAAI,CAAC,MAAM,CACXyB,QAAQ,MACT,CAAC,cAEFxH,IAAA,CAACrB,KAAK,EACJiF,OAAO,CAAEA,OAAQ,CACjB6D,UAAU,CAAElG,MAAO,CACnBmG,MAAM,CAAC,UAAU,CACjBrG,OAAO,CAAEA,OAAQ,CACjBsG,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGC,KAAK,EAAK,KAAKA,KAAK,MAClC,CAAE,CACFC,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACrB,CACF,EACG,CAAC,EA7CgD,GA8ChD,CAAC,cAEVlI,IAAA,CAACK,OAAO,EAACyG,GAAG,cAAE5G,KAAA,SAAA6E,QAAA,eAAM/E,IAAA,CAACL,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAoF,QAAA,CACjDtD,UAAU,cACTvB,KAAA,QAAA6E,QAAA,eACE7E,KAAA,CAACjB,GAAG,EAACkJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtD,KAAK,CAAE,CAAE6B,YAAY,CAAE,EAAG,CAAE,CAAA3B,QAAA,eACjD/E,IAAA,CAACd,GAAG,EAACkJ,IAAI,CAAE,CAAE,CAAArD,QAAA,cACX/E,IAAA,CAACzB,IAAI,EAAAwG,QAAA,cACH/E,IAAA,CAAChB,SAAS,EACR6E,KAAK,CAAC,0BAAM,CACZM,KAAK,CAAE1C,UAAU,CAAC4G,YAAa,CAC/BlB,MAAM,cAAEnH,IAAA,CAACJ,gBAAgB,GAAE,CAAE,CAC7B0I,UAAU,CAAE,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAACd,GAAG,EAACkJ,IAAI,CAAE,CAAE,CAAArD,QAAA,cACX/E,IAAA,CAACzB,IAAI,EAAAwG,QAAA,cACH/E,IAAA,CAAChB,SAAS,EACR6E,KAAK,CAAC,iCAAU,CAChBM,KAAK,CAAE1C,UAAU,CAAC8G,YAAa,CAC/BC,SAAS,CAAE,CAAE,CACbrB,MAAM,cAAEnH,IAAA,CAACL,gBAAgB,GAAE,CAAE,CAC7B2I,UAAU,CAAE,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACNhF,IAAA,CAACd,GAAG,EAACkJ,IAAI,CAAE,CAAE,CAAArD,QAAA,cACX/E,IAAA,CAACzB,IAAI,EAAAwG,QAAA,cACH/E,IAAA,CAAChB,SAAS,EACR6E,KAAK,CAAC,gCAAS,CACfM,KAAK,CAAE,EAAA5D,qBAAA,CAAAkB,UAAU,CAACgH,UAAU,UAAAlI,qBAAA,iBAArBA,qBAAA,CAAuB6E,QAAQ,GAAI,CAAE,CAC5CoD,SAAS,CAAE,CAAE,CACbrB,MAAM,cAAEnH,IAAA,CAACH,cAAc,GAAE,CAAE,CAC3ByI,UAAU,CAAE,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAEN9E,KAAA,CAACjB,GAAG,EAACkJ,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAApD,QAAA,eACpB/E,IAAA,CAACd,GAAG,EAACkJ,IAAI,CAAE,EAAG,CAAArD,QAAA,cACZ7E,KAAA,CAAC3B,IAAI,EAACsF,KAAK,CAAC,0BAAM,CAACmC,IAAI,CAAC,OAAO,CAAAjB,QAAA,EAC5BtD,UAAU,CAACiH,SAAS,EAAIC,MAAM,CAACC,OAAO,CAACnH,UAAU,CAACiH,SAAS,CAAC,CAACG,GAAG,CAACC,IAAA,MAAC,CAACtE,QAAQ,CAAEuE,KAAK,CAAC,CAAAD,IAAA,oBAClF5I,KAAA,QAAoB2E,KAAK,CAAE,CAAE6B,YAAY,CAAE,CAAE,CAAE,CAAA3B,QAAA,eAC7C/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAER,QAAQ,GAAK,KAAK,CAAG,MAAM,CAAGA,QAAQ,GAAK,KAAK,CAAG,OAAO,CAAG,QAAS,CAAAO,QAAA,CAC/EP,QAAQ,CACN,CAAC,cACNtE,KAAA,SAAM2E,KAAK,CAAE,CAAEmE,UAAU,CAAE,CAAE,CAAE,CAAAjE,QAAA,EAAEgE,KAAK,CAAC,qBAAI,EAAM,CAAC,GAJ1CvE,QAKL,CAAC,EACP,CAAC,CACD,CAAC,CAAC/C,UAAU,CAACiH,SAAS,EAAIC,MAAM,CAACM,IAAI,CAACxH,UAAU,CAACiH,SAAS,CAAC,CAACnB,MAAM,GAAK,CAAC,gBACvEvH,IAAA,CAACI,IAAI,EAAC2F,IAAI,CAAC,WAAW,CAAAhB,QAAA,CAAC,0BAAI,CAAM,CAClC,EACG,CAAC,CACJ,CAAC,cACN/E,IAAA,CAACd,GAAG,EAACkJ,IAAI,CAAE,EAAG,CAAArD,QAAA,cACZ7E,KAAA,CAAC3B,IAAI,EAACsF,KAAK,CAAC,sCAAQ,CAACmC,IAAI,CAAC,OAAO,CAAAjB,QAAA,EAC9BtD,UAAU,CAACyH,SAAS,EAAIP,MAAM,CAACC,OAAO,CAACnH,UAAU,CAACyH,SAAS,CAAC,CAACL,GAAG,CAACM,KAAA,MAAC,CAAC1E,QAAQ,CAAEsE,KAAK,CAAC,CAAAI,KAAA,oBAClFjJ,KAAA,QAAoB2E,KAAK,CAAE,CAAE6B,YAAY,CAAE,CAAE,CAAE,CAAA3B,QAAA,eAC7C/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAC,QAAQ,CAAAD,QAAA,CAAEN,QAAQ,CAAM,CAAC,cACpCvE,KAAA,SAAM2E,KAAK,CAAE,CAAEmE,UAAU,CAAE,CAAE,CAAE,CAAAjE,QAAA,EAAEgE,KAAK,CAAC,qBAAI,EAAM,CAAC,GAF1CtE,QAGL,CAAC,EACP,CAAC,CACD,CAAC,CAAChD,UAAU,CAACyH,SAAS,EAAIP,MAAM,CAACM,IAAI,CAACxH,UAAU,CAACyH,SAAS,CAAC,CAAC3B,MAAM,GAAK,CAAC,gBACvEvH,IAAA,CAACI,IAAI,EAAC2F,IAAI,CAAC,WAAW,CAAAhB,QAAA,CAAC,0BAAI,CAAM,CAClC,EACG,CAAC,CACJ,CAAC,EACH,CAAC,CAELtD,UAAU,CAACgH,UAAU,eACpBzI,IAAA,CAACzB,IAAI,EAACsF,KAAK,CAAC,sCAAQ,CAACgB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAACb,IAAI,CAAC,OAAO,CAAAjB,QAAA,cACzD7E,KAAA,CAACnB,YAAY,EAACqK,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAAAjB,QAAA,eACnC/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,gBAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAEtD,UAAU,CAACgH,UAAU,CAACpE,QAAQ,CAAO,CAAC,CACjC,CAAC,cACpBrE,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,oBAAO,CAAAvE,QAAA,cAC9B/E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAACT,KAAK,CAAE,CAAEG,KAAK,CAAE,SAAU,CAAE,CAAAD,QAAA,CACtCtD,UAAU,CAACgH,UAAU,CAACrD,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,CACtC,CAAC,CACU,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAEvD,UAAU,CAACgH,UAAU,CAACjE,QAAQ,GAAK,KAAK,CAAG,MAAM,CACjD/C,UAAU,CAACgH,UAAU,CAACjE,QAAQ,GAAK,KAAK,CAAG,OAAO,CAAG,QAAS,CAAAO,QAAA,CACvEtD,UAAU,CAACgH,UAAU,CAACjE,QAAQ,CAC5B,CAAC,CACW,CAAC,cACpBxE,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAC,QAAQ,CAAAD,QAAA,CAAEtD,UAAU,CAACgH,UAAU,CAAChE,QAAQ,CAAM,CAAC,CACzC,CAAC,EACR,CAAC,CACX,CACP,EACE,CAAC,cAENvE,KAAA,CAAC3B,IAAI,EAAAwG,QAAA,eACH/E,IAAA,CAACV,IAAI,EAAC0G,IAAI,CAAC,OAAO,CAAE,CAAC,cACrBhG,IAAA,QAAK6E,KAAK,CAAE,CAAE0E,SAAS,CAAE,QAAQ,CAAE1C,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,cACjD/E,IAAA,CAACI,IAAI,EAAA2E,QAAA,CAAC,qDAAW,CAAM,CAAC,CACrB,CAAC,EACF,CACP,EApGsD,GAqGhD,CAAC,EACN,CAAC,cAGP/E,IAAA,CAAClB,KAAK,EACJ+E,KAAK,CAAC,sCAAQ,CACd2F,IAAI,CAAE3H,kBAAmB,CACzB4H,QAAQ,CAAEA,CAAA,GAAM3H,qBAAqB,CAAC,KAAK,CAAE,CAC7C4H,MAAM,CAAE,cACN1J,IAAA,CAACtB,MAAM,EAAawH,OAAO,CAAEA,CAAA,GAAMpE,qBAAqB,CAAC,KAAK,CAAE,CAAAiD,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,CACT,CACFf,KAAK,CAAE,GAAI,CAAAe,QAAA,CAEVpD,aAAa,eACZzB,KAAA,QAAA6E,QAAA,eACE7E,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAAAjB,QAAA,eACzD/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,gBAAM,CAAClB,IAAI,CAAE,CAAE,CAAArD,QAAA,cACtC/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAEpD,aAAa,CAAC0C,QAAQ,CAAO,CAAC,CACzB,CAAC,cACpBrE,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAErD,aAAa,CAAC6C,QAAQ,GAAK,KAAK,CAAG,MAAM,CACzC7C,aAAa,CAAC6C,QAAQ,GAAK,KAAK,CAAG,OAAO,CAAG,QAAS,CAAAO,QAAA,CAC/DpD,aAAa,CAAC6C,QAAQ,CACpB,CAAC,CACW,CAAC,cACpBxE,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACZ,GAAG,EAAC4F,KAAK,CAAC,QAAQ,CAAAD,QAAA,CAAEpD,aAAa,CAAC8C,QAAQ,CAAM,CAAC,CACjC,CAAC,cACpBzE,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,oBAAO,CAAAvE,QAAA,cAC9B/E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAACT,KAAK,CAAE,CAAEG,KAAK,CAAErD,aAAa,CAACyD,QAAQ,CAAG,GAAG,CAAG,SAAS,CAC1CzD,aAAa,CAACyD,QAAQ,CAAG,GAAG,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAL,QAAA,CAC/EpD,aAAa,CAACyD,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,CACU,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,sCAAQ,CAAAvE,QAAA,CAC9BpD,aAAa,CAACiI,aAAa,CAAG,GAAI,CAAAnE,IAAI,CAAC9D,aAAa,CAACiI,aAAa,CAAC,CAAC/D,cAAc,CAAC,CAAC,CAAG,KAAK,CAC5E,CAAC,cACpB7F,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAClB,IAAI,CAAE,CAAE,CAAArD,QAAA,CACrC,GAAI,CAAAU,IAAI,CAAC9D,aAAa,CAAC+D,YAAY,CAAC,CAACG,cAAc,CAAC,CAAC,CACrC,CAAC,EACR,CAAC,cAEf3F,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAACnB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACnF/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAE,EAAAvE,qBAAA,CAAAmB,aAAa,CAACkI,UAAU,UAAArJ,qBAAA,iBAAxBA,qBAAA,CAA0BsJ,UAAU,GAAI,KAAK,CAAO,CAAC,CAChD,CAAC,cACpB9J,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,cAC7B/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAE,EAAAtE,sBAAA,CAAAkB,aAAa,CAACkI,UAAU,UAAApJ,sBAAA,iBAAxBA,sBAAA,CAA0BsJ,WAAW,GAAI,KAAK,CAAO,CAAC,CACjD,CAAC,cACpB/J,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,sCAAQ,CAAAvE,QAAA,cAC/B/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAE,EAAArE,sBAAA,CAAAiB,aAAa,CAACkI,UAAU,UAAAnJ,sBAAA,iBAAxBA,sBAAA,CAA0BsJ,WAAW,GAAI,KAAK,CAAO,CAAC,CACjD,CAAC,cACpBhK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,sCAAQ,CAAAvE,QAAA,cAC/B/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAE,EAAApE,sBAAA,CAAAgB,aAAa,CAACkI,UAAU,UAAAlJ,sBAAA,iBAAxBA,sBAAA,CAA0BsJ,cAAc,GAAI,KAAK,CAAO,CAAC,CACpD,CAAC,EACR,CAAC,cAEf/J,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAACnB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACnF/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,oBAAK,CAAAvE,QAAA,CAC3B,EAAAnE,qBAAA,CAAAe,aAAa,CAACuI,eAAe,UAAAtJ,qBAAA,iBAA7BA,qBAAA,CAA+BuJ,aAAa,GAAI,KAAK,CACrC,CAAC,cACpBnK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAAlE,sBAAA,CAAAc,aAAa,CAACuI,eAAe,UAAArJ,sBAAA,iBAA7BA,sBAAA,CAA+BuJ,UAAU,GAAI,KAAK,CAClC,CAAC,cACpBpK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAAjE,sBAAA,CAAAa,aAAa,CAACuI,eAAe,UAAApJ,sBAAA,iBAA7BA,sBAAA,CAA+BuJ,MAAM,GAAI,KAAK,CAC9B,CAAC,cACpBrK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAAhE,qBAAA,CAAAY,aAAa,CAAC2I,kBAAkB,UAAAvJ,qBAAA,iBAAhCA,qBAAA,CAAkCwE,OAAO,CAAC,CAAC,CAAC,GAAI,KAAK,CACrC,CAAC,EACR,CAAC,cAEfrF,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAACnB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACnF/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAA/D,qBAAA,CAAAW,aAAa,CAAC4I,kBAAkB,UAAAvJ,qBAAA,iBAAhCA,qBAAA,CAAkC+E,IAAI,GAAI,KAAK,CAC/B,CAAC,cACpB/F,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,gCAAO,CAAAvE,QAAA,CAC7B,EAAA9D,sBAAA,CAAAU,aAAa,CAAC4I,kBAAkB,UAAAtJ,sBAAA,iBAAhCA,sBAAA,CAAkCuJ,WAAW,GAAI,KAAK,CACtC,CAAC,cACpBxK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAA7D,sBAAA,CAAAS,aAAa,CAAC4I,kBAAkB,UAAArJ,sBAAA,iBAAhCA,sBAAA,CAAkCuJ,UAAU,GAAI,KAAK,CACrC,CAAC,cACpBzK,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5B,EAAA5D,sBAAA,CAAAQ,aAAa,CAAC4I,kBAAkB,UAAApJ,sBAAA,iBAAhCA,sBAAA,CAAkCuJ,eAAe,GAAI,KAAK,CAC1C,CAAC,cACpB1K,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,eAAU,CAAClB,IAAI,CAAE,CAAE,CAAArD,QAAA,CACzC,EAAA3D,sBAAA,CAAAO,aAAa,CAAC4I,kBAAkB,UAAAnJ,sBAAA,iBAAhCA,sBAAA,CAAkCuJ,OAAO,GAAI,KAAK,CAClC,CAAC,EACR,CAAC,cAEfzK,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAACnB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACnF/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAClB,IAAI,CAAE,CAAE,CAAArD,QAAA,cACtC/E,IAAA,CAACI,IAAI,EAACwE,IAAI,MAAAG,QAAA,CAAEpD,aAAa,CAACiJ,WAAW,EAAI,KAAK,CAAO,CAAC,CACrC,CAAC,cACpB5K,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,sCAAQ,CAAAvE,QAAA,CAC9BpD,aAAa,CAACkJ,WAAW,CAAG,IAAIlJ,aAAa,CAACkJ,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,KAAK,CAC/D,CAAC,cACpB9K,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,sCAAQ,CAAAvE,QAAA,CAC9BpD,aAAa,CAACoJ,UAAU,CAAG,IAAIpJ,aAAa,CAACoJ,UAAU,CAACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,KAAK,CAC7D,CAAC,EACR,CAAC,cAEf5K,KAAA,CAACnB,YAAY,EAAC8E,KAAK,CAAC,0BAAM,CAAC8F,QAAQ,MAACP,MAAM,CAAE,CAAE,CAACpD,IAAI,CAAC,OAAO,CAACnB,KAAK,CAAE,CAAEgC,SAAS,CAAE,EAAG,CAAE,CAAA9B,QAAA,eACnF/E,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5BpD,aAAa,CAACqJ,iBAAiB,CAAG,GAAGrJ,aAAa,CAACqJ,iBAAiB,CAACzF,OAAO,CAAC,CAAC,CAAC,GAAG,CAAG,KAAK,CAC1E,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,uBAAQ,CAAAvE,QAAA,CAC9BpD,aAAa,CAACsJ,SAAS,CAAG,GAAGtJ,aAAa,CAACsJ,SAAS,CAAC1F,OAAO,CAAC,CAAC,CAAC,GAAG,CAAG,KAAK,CAC1D,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,0BAAM,CAAAvE,QAAA,CAC5BpD,aAAa,CAACuJ,YAAY,CAAG,GAAGvJ,aAAa,CAACuJ,YAAY,CAAC3F,OAAO,CAAC,CAAC,CAAC,IAAI,CAAG,KAAK,CACjE,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,iBAAO,CAAAvE,QAAA,CAC7BpD,aAAa,CAACwJ,UAAU,CAAG,GAAGxJ,aAAa,CAACwJ,UAAU,CAAC5F,OAAO,CAAC,CAAC,CAAC,IAAI,CAAG,KAAK,CAC7D,CAAC,cACpBvF,IAAA,CAACjB,YAAY,CAACsK,IAAI,EAACC,KAAK,CAAC,uBAAQ,CAAClB,IAAI,CAAE,CAAE,CAAArD,QAAA,CACvCpD,aAAa,CAACyJ,eAAe,CAAG,GAAGzJ,aAAa,CAACyJ,eAAe,CAAC7F,OAAO,CAAC,CAAC,CAAC,GAAG,CAAG,KAAK,CACtE,CAAC,EACR,CAAC,EACZ,CACN,CACI,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}