{"ast": null, "code": "var MapCache = require('./_MapCache'),\n  setCacheAdd = require('./_setCacheAdd'),\n  setCacheHas = require('./_setCacheHas');\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n    length = values == null ? 0 : values.length;\n  this.__data__ = new MapCache();\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\nmodule.exports = SetCache;", "map": {"version": 3, "names": ["MapCache", "require", "setCacheAdd", "setCacheHas", "<PERSON><PERSON><PERSON>", "values", "index", "length", "__data__", "add", "prototype", "push", "has", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_SetCache.js"], "sourcesContent": ["var MapCache = require('./_MapCache'),\n    setCacheAdd = require('./_setCacheAdd'),\n    setCacheHas = require('./_setCacheHas');\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n\n  this.__data__ = new MapCache;\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n\nmodule.exports = SetCache;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EACvCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,MAAM,IAAI,IAAI,GAAG,CAAC,GAAGA,MAAM,CAACE,MAAM;EAE/C,IAAI,CAACC,QAAQ,GAAG,IAAIR,QAAQ,CAAD,CAAC;EAC5B,OAAO,EAAEM,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAI,CAACE,GAAG,CAACJ,MAAM,CAACC,KAAK,CAAC,CAAC;EACzB;AACF;;AAEA;AACAF,QAAQ,CAACM,SAAS,CAACD,GAAG,GAAGL,QAAQ,CAACM,SAAS,CAACC,IAAI,GAAGT,WAAW;AAC9DE,QAAQ,CAACM,SAAS,CAACE,GAAG,GAAGT,WAAW;AAEpCU,MAAM,CAACC,OAAO,GAAGV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}