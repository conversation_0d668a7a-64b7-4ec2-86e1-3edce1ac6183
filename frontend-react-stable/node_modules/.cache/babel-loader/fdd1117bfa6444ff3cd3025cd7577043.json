{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = sequentialQuantile;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nfunction sequentialQuantile() {\n  var domain = [],\n    interpolator = _continuous.identity;\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator(((0, _index.bisect)(domain, x, 1) - 1) / (domain.length - 1));\n  }\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(_index.ascending);\n    return scale;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  scale.range = function () {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n  scale.quantiles = function (n) {\n    return Array.from({\n      length: n + 1\n    }, (_, i) => (0, _index.quantile)(domain, i / n));\n  };\n  scale.copy = function () {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n  return _init.initInterpolator.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "sequentialQuantile", "_index", "require", "_continuous", "_init", "domain", "interpolator", "identity", "scale", "x", "isNaN", "bisect", "length", "_", "arguments", "slice", "d", "push", "sort", "ascending", "range", "map", "i", "quantiles", "n", "Array", "from", "quantile", "copy", "initInterpolator", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/sequentialQuantile.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = sequentialQuantile;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction sequentialQuantile() {\n  var domain = [],\n      interpolator = _continuous.identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator(((0, _index.bisect)(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n\n    domain.sort(_index.ascending);\n    return scale;\n  };\n\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function () {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function (n) {\n    return Array.from({\n      length: n + 1\n    }, (_, i) => (0, _index.quantile)(domain, i / n));\n  };\n\n  scale.copy = function () {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return _init.initInterpolator.apply(scale, arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,kBAAkB;AAEpC,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASF,kBAAkBA,CAAA,EAAG;EAC5B,IAAIK,MAAM,GAAG,EAAE;IACXC,YAAY,GAAGH,WAAW,CAACI,QAAQ;EAEvC,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIA,CAAC,IAAI,IAAI,IAAI,CAACC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,OAAOH,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACU,MAAM,EAAEN,MAAM,EAAEI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAKJ,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC,CAAC;EACpH;EAEAJ,KAAK,CAACH,MAAM,GAAG,UAAUQ,CAAC,EAAE;IAC1B,IAAI,CAACC,SAAS,CAACF,MAAM,EAAE,OAAOP,MAAM,CAACU,KAAK,CAAC,CAAC;IAC5CV,MAAM,GAAG,EAAE;IAEX,KAAK,IAAIW,CAAC,IAAIH,CAAC,EAAE,IAAIG,CAAC,IAAI,IAAI,IAAI,CAACN,KAAK,CAACM,CAAC,GAAG,CAACA,CAAC,CAAC,EAAEX,MAAM,CAACY,IAAI,CAACD,CAAC,CAAC;IAEhEX,MAAM,CAACa,IAAI,CAACjB,MAAM,CAACkB,SAAS,CAAC;IAC7B,OAAOX,KAAK;EACd,CAAC;EAEDA,KAAK,CAACF,YAAY,GAAG,UAAUO,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACF,MAAM,IAAIN,YAAY,GAAGO,CAAC,EAAEL,KAAK,IAAIF,YAAY;EACpE,CAAC;EAEDE,KAAK,CAACY,KAAK,GAAG,YAAY;IACxB,OAAOf,MAAM,CAACgB,GAAG,CAAC,CAACL,CAAC,EAAEM,CAAC,KAAKhB,YAAY,CAACgB,CAAC,IAAIjB,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,CAAC;EAEDJ,KAAK,CAACe,SAAS,GAAG,UAAUC,CAAC,EAAE;IAC7B,OAAOC,KAAK,CAACC,IAAI,CAAC;MAChBd,MAAM,EAAEY,CAAC,GAAG;IACd,CAAC,EAAE,CAACX,CAAC,EAAES,CAAC,KAAK,CAAC,CAAC,EAAErB,MAAM,CAAC0B,QAAQ,EAAEtB,MAAM,EAAEiB,CAAC,GAAGE,CAAC,CAAC,CAAC;EACnD,CAAC;EAEDhB,KAAK,CAACoB,IAAI,GAAG,YAAY;IACvB,OAAO5B,kBAAkB,CAACM,YAAY,CAAC,CAACD,MAAM,CAACA,MAAM,CAAC;EACxD,CAAC;EAED,OAAOD,KAAK,CAACyB,gBAAgB,CAACC,KAAK,CAACtB,KAAK,EAAEM,SAAS,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}