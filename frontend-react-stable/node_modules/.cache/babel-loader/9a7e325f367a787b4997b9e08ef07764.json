{"ast": null, "code": "import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar BreadcrumbSeparator = function BreadcrumbSeparator(_ref) {\n  var children = _ref.children;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('breadcrumb');\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-separator\")\n  }, children || '/');\n};\nBreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;\nexport default BreadcrumbSeparator;", "map": {"version": 3, "names": ["React", "ConfigContext", "BreadcrumbSeparator", "_ref", "children", "_React$useContext", "useContext", "getPrefixCls", "prefixCls", "createElement", "className", "concat", "__ANT_BREADCRUMB_SEPARATOR"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/breadcrumb/BreadcrumbSeparator.js"], "sourcesContent": ["import * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar BreadcrumbSeparator = function BreadcrumbSeparator(_ref) {\n  var children = _ref.children;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('breadcrumb');\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-separator\")\n  }, children || '/');\n};\nBreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;\nexport default BreadcrumbSeparator;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAC3D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,IAAIC,iBAAiB,GAAGL,KAAK,CAACM,UAAU,CAACL,aAAa,CAAC;IACrDM,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIC,SAAS,GAAGD,YAAY,CAAC,YAAY,CAAC;EAC1C,OAAO,aAAaP,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACH,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAEJ,QAAQ,IAAI,GAAG,CAAC;AACrB,CAAC;AACDF,mBAAmB,CAACU,0BAA0B,GAAG,IAAI;AACrD,eAAeV,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}