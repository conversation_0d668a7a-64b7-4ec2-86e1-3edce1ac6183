{"ast": null, "code": "// =============================== Motion ===============================\nexport function getMotionName(prefixCls, transitionName, animationName) {\n  var motionName = transitionName;\n  if (!motionName && animationName) {\n    motionName = \"\".concat(prefixCls, \"-\").concat(animationName);\n  }\n  return motionName;\n}\n// =============================== Offset ===============================\nfunction getScroll(w, top) {\n  var ret = w[\"page\".concat(top ? 'Y' : 'X', \"Offset\")];\n  var method = \"scroll\".concat(top ? 'Top' : 'Left');\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nexport function offset(el) {\n  var rect = el.getBoundingClientRect();\n  var pos = {\n    left: rect.left,\n    top: rect.top\n  };\n  var doc = el.ownerDocument;\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScroll(w);\n  pos.top += getScroll(w, true);\n  return pos;\n}", "map": {"version": 3, "names": ["getMotionName", "prefixCls", "transitionName", "animationName", "motionName", "concat", "getScroll", "w", "top", "ret", "method", "d", "document", "documentElement", "body", "offset", "el", "rect", "getBoundingClientRect", "pos", "left", "doc", "ownerDocument", "defaultView", "parentWindow"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dialog/es/util.js"], "sourcesContent": ["// =============================== Motion ===============================\nexport function getMotionName(prefixCls, transitionName, animationName) {\n  var motionName = transitionName;\n  if (!motionName && animationName) {\n    motionName = \"\".concat(prefixCls, \"-\").concat(animationName);\n  }\n  return motionName;\n}\n// =============================== Offset ===============================\nfunction getScroll(w, top) {\n  var ret = w[\"page\".concat(top ? 'Y' : 'X', \"Offset\")];\n  var method = \"scroll\".concat(top ? 'Top' : 'Left');\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nexport function offset(el) {\n  var rect = el.getBoundingClientRect();\n  var pos = {\n    left: rect.left,\n    top: rect.top\n  };\n  var doc = el.ownerDocument;\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScroll(w);\n  pos.top += getScroll(w, true);\n  return pos;\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAACC,SAAS,EAAEC,cAAc,EAAEC,aAAa,EAAE;EACtE,IAAIC,UAAU,GAAGF,cAAc;EAC/B,IAAI,CAACE,UAAU,IAAID,aAAa,EAAE;IAChCC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,GAAG,CAAC,CAACI,MAAM,CAACF,aAAa,CAAC;EAC9D;EACA,OAAOC,UAAU;AACnB;AACA;AACA,SAASE,SAASA,CAACC,CAAC,EAAEC,GAAG,EAAE;EACzB,IAAIC,GAAG,GAAGF,CAAC,CAAC,MAAM,CAACF,MAAM,CAACG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC;EACrD,IAAIE,MAAM,GAAG,QAAQ,CAACL,MAAM,CAACG,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;EAClD,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAIE,CAAC,GAAGJ,CAAC,CAACK,QAAQ;IAClBH,GAAG,GAAGE,CAAC,CAACE,eAAe,CAACH,MAAM,CAAC;IAC/B,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3BA,GAAG,GAAGE,CAAC,CAACG,IAAI,CAACJ,MAAM,CAAC;IACtB;EACF;EACA,OAAOD,GAAG;AACZ;AACA,OAAO,SAASM,MAAMA,CAACC,EAAE,EAAE;EACzB,IAAIC,IAAI,GAAGD,EAAE,CAACE,qBAAqB,CAAC,CAAC;EACrC,IAAIC,GAAG,GAAG;IACRC,IAAI,EAAEH,IAAI,CAACG,IAAI;IACfZ,GAAG,EAAES,IAAI,CAACT;EACZ,CAAC;EACD,IAAIa,GAAG,GAAGL,EAAE,CAACM,aAAa;EAC1B,IAAIf,CAAC,GAAGc,GAAG,CAACE,WAAW,IAAIF,GAAG,CAACG,YAAY;EAC3CL,GAAG,CAACC,IAAI,IAAId,SAAS,CAACC,CAAC,CAAC;EACxBY,GAAG,CAACX,GAAG,IAAIF,SAAS,CAACC,CAAC,EAAE,IAAI,CAAC;EAC7B,OAAOY,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}