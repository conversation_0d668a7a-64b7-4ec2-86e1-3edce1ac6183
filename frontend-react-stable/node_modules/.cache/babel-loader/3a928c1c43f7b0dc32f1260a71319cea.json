{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport raf from \"rc-util/es/raf\";\nexport function throttleByAnimationFrame(fn) {\n  var requestId;\n  var later = function later(args) {\n    return function () {\n      requestId = null;\n      fn.apply(void 0, _toConsumableArray(args));\n    };\n  };\n  var throttled = function throttled() {\n    if (requestId == null) {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      requestId = raf(later(args));\n    }\n  };\n  throttled.cancel = function () {\n    raf.cancel(requestId);\n    requestId = null;\n  };\n  return throttled;\n}\nexport function throttleByAnimationFrameDecorator() {\n  return function throttle(target, key, descriptor) {\n    var fn = descriptor.value;\n    var definingProperty = false;\n    return {\n      configurable: true,\n      get: function get() {\n        // In IE11 calling Object.defineProperty has a side-effect of evaluating the\n        // getter for the property which is being replaced. This causes infinite\n        // recursion and an \"Out of stack space\" error.\n        // eslint-disable-next-line no-prototype-builtins\n        if (definingProperty || this === target.prototype || this.hasOwnProperty(key)) {\n          /* istanbul ignore next */\n          return fn;\n        }\n        var boundFn = throttleByAnimationFrame(fn.bind(this));\n        definingProperty = true;\n        Object.defineProperty(this, key, {\n          value: boundFn,\n          configurable: true,\n          writable: true\n        });\n        definingProperty = false;\n        return boundFn;\n      }\n    };\n  };\n}", "map": {"version": 3, "names": ["_toConsumableArray", "raf", "throttleByAnimationFrame", "fn", "requestId", "later", "args", "apply", "throttled", "_len", "arguments", "length", "Array", "_key", "cancel", "throttleByAnimationFrameDecorator", "throttle", "target", "key", "descriptor", "value", "definingProperty", "configurable", "get", "prototype", "hasOwnProperty", "boundFn", "bind", "Object", "defineProperty", "writable"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/throttleByAnimationFrame.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport raf from \"rc-util/es/raf\";\nexport function throttleByAnimationFrame(fn) {\n  var requestId;\n  var later = function later(args) {\n    return function () {\n      requestId = null;\n      fn.apply(void 0, _toConsumableArray(args));\n    };\n  };\n  var throttled = function throttled() {\n    if (requestId == null) {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      requestId = raf(later(args));\n    }\n  };\n  throttled.cancel = function () {\n    raf.cancel(requestId);\n    requestId = null;\n  };\n  return throttled;\n}\nexport function throttleByAnimationFrameDecorator() {\n  return function throttle(target, key, descriptor) {\n    var fn = descriptor.value;\n    var definingProperty = false;\n    return {\n      configurable: true,\n      get: function get() {\n        // In IE11 calling Object.defineProperty has a side-effect of evaluating the\n        // getter for the property which is being replaced. This causes infinite\n        // recursion and an \"Out of stack space\" error.\n        // eslint-disable-next-line no-prototype-builtins\n        if (definingProperty || this === target.prototype || this.hasOwnProperty(key)) {\n          /* istanbul ignore next */\n          return fn;\n        }\n        var boundFn = throttleByAnimationFrame(fn.bind(this));\n        definingProperty = true;\n        Object.defineProperty(this, key, {\n          value: boundFn,\n          configurable: true,\n          writable: true\n        });\n        definingProperty = false;\n        return boundFn;\n      }\n    };\n  };\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,SAASC,wBAAwBA,CAACC,EAAE,EAAE;EAC3C,IAAIC,SAAS;EACb,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;IAC/B,OAAO,YAAY;MACjBF,SAAS,GAAG,IAAI;MAChBD,EAAE,CAACI,KAAK,CAAC,KAAK,CAAC,EAAEP,kBAAkB,CAACM,IAAI,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC;EACD,IAAIE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIJ,SAAS,IAAI,IAAI,EAAE;MACrB,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEL,IAAI,GAAG,IAAIM,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFP,IAAI,CAACO,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MACAT,SAAS,GAAGH,GAAG,CAACI,KAAK,CAACC,IAAI,CAAC,CAAC;IAC9B;EACF,CAAC;EACDE,SAAS,CAACM,MAAM,GAAG,YAAY;IAC7Bb,GAAG,CAACa,MAAM,CAACV,SAAS,CAAC;IACrBA,SAAS,GAAG,IAAI;EAClB,CAAC;EACD,OAAOI,SAAS;AAClB;AACA,OAAO,SAASO,iCAAiCA,CAAA,EAAG;EAClD,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAE;IAChD,IAAIhB,EAAE,GAAGgB,UAAU,CAACC,KAAK;IACzB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,OAAO;MACLC,YAAY,EAAE,IAAI;MAClBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB;QACA;QACA;QACA;QACA,IAAIF,gBAAgB,IAAI,IAAI,KAAKJ,MAAM,CAACO,SAAS,IAAI,IAAI,CAACC,cAAc,CAACP,GAAG,CAAC,EAAE;UAC7E;UACA,OAAOf,EAAE;QACX;QACA,IAAIuB,OAAO,GAAGxB,wBAAwB,CAACC,EAAE,CAACwB,IAAI,CAAC,IAAI,CAAC,CAAC;QACrDN,gBAAgB,GAAG,IAAI;QACvBO,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEX,GAAG,EAAE;UAC/BE,KAAK,EAAEM,OAAO;UACdJ,YAAY,EAAE,IAAI;UAClBQ,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFT,gBAAgB,GAAG,KAAK;QACxB,OAAOK,OAAO;MAChB;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}