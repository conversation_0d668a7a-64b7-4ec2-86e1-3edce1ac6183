{"ast": null, "code": "import _isNil from \"lodash/isNil\";\nimport _isFunction from \"lodash/isFunction\";\nimport _uniqBy from \"lodash/uniqBy\";\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/**\n * @fileOverview Tooltip\n */\nimport React, { PureComponent } from 'react';\nimport { translateStyle } from 'react-smooth';\nimport classNames from 'classnames';\nimport { DefaultTooltipContent } from './DefaultTooltipContent';\nimport { Global } from '../util/Global';\nimport { isNumber } from '../util/DataUtils';\nvar CLS_PREFIX = 'recharts-tooltip-wrapper';\nvar EPS = 1;\nfunction defaultUniqBy(entry) {\n  return entry.dataKey;\n}\nfunction getUniqPayload(option, payload) {\n  if (option === true) {\n    return _uniqBy(payload, defaultUniqBy);\n  }\n  if (_isFunction(option)) {\n    return _uniqBy(payload, option);\n  }\n  return payload;\n}\nfunction renderContent(content, props) {\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (_isFunction(content)) {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  return /*#__PURE__*/React.createElement(DefaultTooltipContent, props);\n}\nexport var Tooltip = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Tooltip, _PureComponent);\n  var _super = _createSuper(Tooltip);\n  function Tooltip() {\n    var _this;\n    _classCallCheck(this, Tooltip);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      boxWidth: -1,\n      boxHeight: -1,\n      dismissed: false,\n      dismissedAtCoordinate: {\n        x: 0,\n        y: 0\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getTranslate\", function (_ref) {\n      var key = _ref.key,\n        tooltipDimension = _ref.tooltipDimension,\n        viewBoxDimension = _ref.viewBoxDimension;\n      var _this$props = _this.props,\n        allowEscapeViewBox = _this$props.allowEscapeViewBox,\n        reverseDirection = _this$props.reverseDirection,\n        coordinate = _this$props.coordinate,\n        offset = _this$props.offset,\n        position = _this$props.position,\n        viewBox = _this$props.viewBox;\n      if (position && isNumber(position[key])) {\n        return position[key];\n      }\n      var negative = coordinate[key] - tooltipDimension - offset;\n      var positive = coordinate[key] + offset;\n      if (allowEscapeViewBox[key]) {\n        return reverseDirection[key] ? negative : positive;\n      }\n      if (reverseDirection[key]) {\n        var _tooltipBoundary = negative;\n        var _viewBoxBoundary = viewBox[key];\n        if (_tooltipBoundary < _viewBoxBoundary) {\n          return Math.max(positive, viewBox[key]);\n        }\n        return Math.max(negative, viewBox[key]);\n      }\n      var tooltipBoundary = positive + tooltipDimension;\n      var viewBoxBoundary = viewBox[key] + viewBoxDimension;\n      if (tooltipBoundary > viewBoxBoundary) {\n        return Math.max(negative, viewBox[key]);\n      }\n      return Math.max(positive, viewBox[key]);\n    });\n    return _this;\n  }\n  _createClass(Tooltip, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var _this$state = this.state,\n        boxWidth = _this$state.boxWidth,\n        boxHeight = _this$state.boxHeight,\n        dismissed = _this$state.dismissed;\n      if (dismissed) {\n        this.wrapperNode.blur();\n        if (this.props.coordinate.x !== this.state.dismissedAtCoordinate.x || this.props.coordinate.y !== this.state.dismissedAtCoordinate.y) {\n          this.setState({\n            dismissed: false\n          });\n        }\n      } else {\n        this.wrapperNode.focus({\n          preventScroll: true\n        });\n      }\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var box = this.wrapperNode.getBoundingClientRect();\n        if (Math.abs(box.width - boxWidth) > EPS || Math.abs(box.height - boxHeight) > EPS) {\n          this.setState({\n            boxWidth: box.width,\n            boxHeight: box.height\n          });\n        }\n      } else if (boxWidth !== -1 || boxHeight !== -1) {\n        this.setState({\n          boxWidth: -1,\n          boxHeight: -1\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n        _this2 = this;\n      var _this$props2 = this.props,\n        payload = _this$props2.payload,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        filterNull = _this$props2.filterNull,\n        payloadUniqBy = _this$props2.payloadUniqBy;\n      var finalPayload = getUniqPayload(payloadUniqBy, filterNull && payload && payload.length ? payload.filter(function (entry) {\n        return !_isNil(entry.value);\n      }) : payload);\n      var hasPayload = finalPayload && finalPayload.length;\n      var _this$props3 = this.props,\n        content = _this$props3.content,\n        viewBox = _this$props3.viewBox,\n        coordinate = _this$props3.coordinate,\n        position = _this$props3.position,\n        active = _this$props3.active,\n        wrapperStyle = _this$props3.wrapperStyle;\n      var outerStyle = _objectSpread({\n        pointerEvents: 'none',\n        visibility: !this.state.dismissed && active && hasPayload ? 'visible' : 'hidden',\n        position: 'absolute',\n        top: 0,\n        left: 0\n      }, wrapperStyle);\n      var translateX, translateY;\n      if (position && isNumber(position.x) && isNumber(position.y)) {\n        translateX = position.x;\n        translateY = position.y;\n      } else {\n        var _this$state2 = this.state,\n          boxWidth = _this$state2.boxWidth,\n          boxHeight = _this$state2.boxHeight;\n        if (boxWidth > 0 && boxHeight > 0 && coordinate) {\n          translateX = this.getTranslate({\n            key: 'x',\n            tooltipDimension: boxWidth,\n            viewBoxDimension: viewBox.width\n          });\n          translateY = this.getTranslate({\n            key: 'y',\n            tooltipDimension: boxHeight,\n            viewBoxDimension: viewBox.height\n          });\n        } else {\n          outerStyle.visibility = 'hidden';\n        }\n      }\n      outerStyle = _objectSpread(_objectSpread({}, translateStyle({\n        transform: this.props.useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n      })), outerStyle);\n      if (isAnimationActive && active) {\n        outerStyle = _objectSpread(_objectSpread({}, translateStyle({\n          transition: \"transform \".concat(animationDuration, \"ms \").concat(animationEasing)\n        })), outerStyle);\n      }\n      var cls = classNames(CLS_PREFIX, (_classNames = {}, _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-right\"), isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX >= coordinate.x), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-left\"), isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX < coordinate.x), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-bottom\"), isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY >= coordinate.y), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-top\"), isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY < coordinate.y), _classNames));\n      return (/*#__PURE__*/\n        // ESLint is disabled to allow listening to the `Escape` key. Refer to\n        // https://github.com/recharts/recharts/pull/2925\n        // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions\n        React.createElement(\"div\", {\n          tabIndex: -1,\n          role: \"dialog\",\n          onKeyDown: function onKeyDown(event) {\n            if (event.key === 'Escape') {\n              _this2.setState({\n                dismissed: true,\n                dismissedAtCoordinate: _objectSpread(_objectSpread({}, _this2.state.dismissedAtCoordinate), {}, {\n                  x: _this2.props.coordinate.x,\n                  y: _this2.props.coordinate.y\n                })\n              });\n            }\n          },\n          className: cls,\n          style: outerStyle,\n          ref: function ref(node) {\n            _this2.wrapperNode = node;\n          }\n        }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n          payload: finalPayload\n        })))\n      );\n    }\n  }]);\n  return Tooltip;\n}(PureComponent);\n_defineProperty(Tooltip, \"displayName\", 'Tooltip');\n_defineProperty(Tooltip, \"defaultProps\", {\n  active: false,\n  allowEscapeViewBox: {\n    x: false,\n    y: false\n  },\n  reverseDirection: {\n    x: false,\n    y: false\n  },\n  offset: 10,\n  viewBox: {\n    x1: 0,\n    x2: 0,\n    y1: 0,\n    y2: 0\n  },\n  coordinate: {\n    x: 0,\n    y: 0\n  },\n  cursorStyle: {},\n  separator: ' : ',\n  wrapperStyle: {},\n  contentStyle: {},\n  itemStyle: {},\n  labelStyle: {},\n  cursor: true,\n  trigger: 'hover',\n  isAnimationActive: !Global.isSsr,\n  animationEasing: 'ease',\n  animationDuration: 400,\n  filterNull: true,\n  useTranslate3d: false\n});", "map": {"version": 3, "names": ["_isNil", "_isFunction", "_uniqBy", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "bind", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "PureComponent", "translateStyle", "classNames", "DefaultTooltipContent", "Global", "isNumber", "CLS_PREFIX", "EPS", "defaultUniqBy", "entry", "dataKey", "getUniqPayload", "option", "payload", "renderContent", "content", "isValidElement", "cloneElement", "createElement", "<PERSON><PERSON><PERSON>", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "boxWidth", "boxHeight", "dismissed", "dismissedAtCoordinate", "x", "y", "_ref", "tooltipDimension", "viewBoxDimension", "_this$props", "allowEscapeViewBox", "reverseDirection", "coordinate", "offset", "position", "viewBox", "negative", "positive", "_tooltipBoundary", "_viewBoxBoundary", "Math", "max", "tooltipBoundary", "viewBoxBoundary", "componentDidMount", "updateBBox", "componentDidUpdate", "_this$state", "state", "wrapperNode", "blur", "setState", "focus", "preventScroll", "getBoundingClientRect", "box", "abs", "width", "height", "render", "_classNames", "_this2", "_this$props2", "isAnimationActive", "animationDuration", "animationEasing", "filterNull", "payloadUniqBy", "finalPayload", "hasPayload", "_this$props3", "active", "wrapperStyle", "outerStyle", "pointerEvents", "visibility", "top", "left", "translateX", "translateY", "_this$state2", "getTranslate", "transform", "useTranslate3d", "transition", "cls", "tabIndex", "role", "onKeyDown", "event", "className", "style", "ref", "node", "x1", "x2", "y1", "y2", "cursorStyle", "separator", "contentStyle", "itemStyle", "labelStyle", "cursor", "trigger", "isSsr"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/Tooltip.js"], "sourcesContent": ["import _isNil from \"lodash/isNil\";\nimport _isFunction from \"lodash/isFunction\";\nimport _uniqBy from \"lodash/uniqBy\";\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * @fileOverview Tooltip\n */\nimport React, { PureComponent } from 'react';\nimport { translateStyle } from 'react-smooth';\nimport classNames from 'classnames';\nimport { DefaultTooltipContent } from './DefaultTooltipContent';\nimport { Global } from '../util/Global';\nimport { isNumber } from '../util/DataUtils';\nvar CLS_PREFIX = 'recharts-tooltip-wrapper';\nvar EPS = 1;\nfunction defaultUniqBy(entry) {\n  return entry.dataKey;\n}\nfunction getUniqPayload(option, payload) {\n  if (option === true) {\n    return _uniqBy(payload, defaultUniqBy);\n  }\n  if (_isFunction(option)) {\n    return _uniqBy(payload, option);\n  }\n  return payload;\n}\nfunction renderContent(content, props) {\n  if ( /*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (_isFunction(content)) {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  return /*#__PURE__*/React.createElement(DefaultTooltipContent, props);\n}\nexport var Tooltip = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Tooltip, _PureComponent);\n  var _super = _createSuper(Tooltip);\n  function Tooltip() {\n    var _this;\n    _classCallCheck(this, Tooltip);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      boxWidth: -1,\n      boxHeight: -1,\n      dismissed: false,\n      dismissedAtCoordinate: {\n        x: 0,\n        y: 0\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getTranslate\", function (_ref) {\n      var key = _ref.key,\n        tooltipDimension = _ref.tooltipDimension,\n        viewBoxDimension = _ref.viewBoxDimension;\n      var _this$props = _this.props,\n        allowEscapeViewBox = _this$props.allowEscapeViewBox,\n        reverseDirection = _this$props.reverseDirection,\n        coordinate = _this$props.coordinate,\n        offset = _this$props.offset,\n        position = _this$props.position,\n        viewBox = _this$props.viewBox;\n      if (position && isNumber(position[key])) {\n        return position[key];\n      }\n      var negative = coordinate[key] - tooltipDimension - offset;\n      var positive = coordinate[key] + offset;\n      if (allowEscapeViewBox[key]) {\n        return reverseDirection[key] ? negative : positive;\n      }\n      if (reverseDirection[key]) {\n        var _tooltipBoundary = negative;\n        var _viewBoxBoundary = viewBox[key];\n        if (_tooltipBoundary < _viewBoxBoundary) {\n          return Math.max(positive, viewBox[key]);\n        }\n        return Math.max(negative, viewBox[key]);\n      }\n      var tooltipBoundary = positive + tooltipDimension;\n      var viewBoxBoundary = viewBox[key] + viewBoxDimension;\n      if (tooltipBoundary > viewBoxBoundary) {\n        return Math.max(negative, viewBox[key]);\n      }\n      return Math.max(positive, viewBox[key]);\n    });\n    return _this;\n  }\n  _createClass(Tooltip, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var _this$state = this.state,\n        boxWidth = _this$state.boxWidth,\n        boxHeight = _this$state.boxHeight,\n        dismissed = _this$state.dismissed;\n      if (dismissed) {\n        this.wrapperNode.blur();\n        if (this.props.coordinate.x !== this.state.dismissedAtCoordinate.x || this.props.coordinate.y !== this.state.dismissedAtCoordinate.y) {\n          this.setState({\n            dismissed: false\n          });\n        }\n      } else {\n        this.wrapperNode.focus({\n          preventScroll: true\n        });\n      }\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var box = this.wrapperNode.getBoundingClientRect();\n        if (Math.abs(box.width - boxWidth) > EPS || Math.abs(box.height - boxHeight) > EPS) {\n          this.setState({\n            boxWidth: box.width,\n            boxHeight: box.height\n          });\n        }\n      } else if (boxWidth !== -1 || boxHeight !== -1) {\n        this.setState({\n          boxWidth: -1,\n          boxHeight: -1\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n        _this2 = this;\n      var _this$props2 = this.props,\n        payload = _this$props2.payload,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        filterNull = _this$props2.filterNull,\n        payloadUniqBy = _this$props2.payloadUniqBy;\n      var finalPayload = getUniqPayload(payloadUniqBy, filterNull && payload && payload.length ? payload.filter(function (entry) {\n        return !_isNil(entry.value);\n      }) : payload);\n      var hasPayload = finalPayload && finalPayload.length;\n      var _this$props3 = this.props,\n        content = _this$props3.content,\n        viewBox = _this$props3.viewBox,\n        coordinate = _this$props3.coordinate,\n        position = _this$props3.position,\n        active = _this$props3.active,\n        wrapperStyle = _this$props3.wrapperStyle;\n      var outerStyle = _objectSpread({\n        pointerEvents: 'none',\n        visibility: !this.state.dismissed && active && hasPayload ? 'visible' : 'hidden',\n        position: 'absolute',\n        top: 0,\n        left: 0\n      }, wrapperStyle);\n      var translateX, translateY;\n      if (position && isNumber(position.x) && isNumber(position.y)) {\n        translateX = position.x;\n        translateY = position.y;\n      } else {\n        var _this$state2 = this.state,\n          boxWidth = _this$state2.boxWidth,\n          boxHeight = _this$state2.boxHeight;\n        if (boxWidth > 0 && boxHeight > 0 && coordinate) {\n          translateX = this.getTranslate({\n            key: 'x',\n            tooltipDimension: boxWidth,\n            viewBoxDimension: viewBox.width\n          });\n          translateY = this.getTranslate({\n            key: 'y',\n            tooltipDimension: boxHeight,\n            viewBoxDimension: viewBox.height\n          });\n        } else {\n          outerStyle.visibility = 'hidden';\n        }\n      }\n      outerStyle = _objectSpread(_objectSpread({}, translateStyle({\n        transform: this.props.useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n      })), outerStyle);\n      if (isAnimationActive && active) {\n        outerStyle = _objectSpread(_objectSpread({}, translateStyle({\n          transition: \"transform \".concat(animationDuration, \"ms \").concat(animationEasing)\n        })), outerStyle);\n      }\n      var cls = classNames(CLS_PREFIX, (_classNames = {}, _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-right\"), isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX >= coordinate.x), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-left\"), isNumber(translateX) && coordinate && isNumber(coordinate.x) && translateX < coordinate.x), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-bottom\"), isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY >= coordinate.y), _defineProperty(_classNames, \"\".concat(CLS_PREFIX, \"-top\"), isNumber(translateY) && coordinate && isNumber(coordinate.y) && translateY < coordinate.y), _classNames));\n      return (\n        /*#__PURE__*/\n        // ESLint is disabled to allow listening to the `Escape` key. Refer to\n        // https://github.com/recharts/recharts/pull/2925\n        // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions\n        React.createElement(\"div\", {\n          tabIndex: -1,\n          role: \"dialog\",\n          onKeyDown: function onKeyDown(event) {\n            if (event.key === 'Escape') {\n              _this2.setState({\n                dismissed: true,\n                dismissedAtCoordinate: _objectSpread(_objectSpread({}, _this2.state.dismissedAtCoordinate), {}, {\n                  x: _this2.props.coordinate.x,\n                  y: _this2.props.coordinate.y\n                })\n              });\n            }\n          },\n          className: cls,\n          style: outerStyle,\n          ref: function ref(node) {\n            _this2.wrapperNode = node;\n          }\n        }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n          payload: finalPayload\n        })))\n      );\n    }\n  }]);\n  return Tooltip;\n}(PureComponent);\n_defineProperty(Tooltip, \"displayName\", 'Tooltip');\n_defineProperty(Tooltip, \"defaultProps\", {\n  active: false,\n  allowEscapeViewBox: {\n    x: false,\n    y: false\n  },\n  reverseDirection: {\n    x: false,\n    y: false\n  },\n  offset: 10,\n  viewBox: {\n    x1: 0,\n    x2: 0,\n    y1: 0,\n    y2: 0\n  },\n  coordinate: {\n    x: 0,\n    y: 0\n  },\n  cursorStyle: {},\n  separator: ' : ',\n  wrapperStyle: {},\n  contentStyle: {},\n  itemStyle: {},\n  labelStyle: {},\n  cursor: true,\n  trigger: 'hover',\n  isAnimationActive: !Global.isSsr,\n  animationEasing: 'ease',\n  animationDuration: 400,\n  filterNull: true,\n  useTranslate3d: false\n});"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AACzf,SAASW,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACf,MAAM,EAAEgB,KAAK,EAAE;EAAE,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIgB,UAAU,GAAGD,KAAK,CAACf,CAAC,CAAC;IAAEgB,UAAU,CAACrB,UAAU,GAAGqB,UAAU,CAACrB,UAAU,IAAI,KAAK;IAAEqB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE7B,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEoB,cAAc,CAACH,UAAU,CAACX,GAAG,CAAC,EAAEW,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAAC5B,SAAS,EAAEqC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAEjC,MAAM,CAACoB,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIZ,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEW,QAAQ,CAACxC,SAAS,GAAGK,MAAM,CAACqC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACzC,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE4C,KAAK,EAAEH,QAAQ;MAAEN,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE5B,MAAM,CAACoB,cAAc,CAACe,QAAQ,EAAE,WAAW,EAAE;IAAEN,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIO,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGvC,MAAM,CAAC0C,cAAc,GAAG1C,MAAM,CAAC0C,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASJ,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASI,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACzD,WAAW;MAAE0D,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEtC,SAAS,EAAEyC,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC1C,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;IAAE;IAAE,OAAO4C,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKpE,OAAO,CAACoE,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIlC,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACF,IAAI,CAAC;AAAE;AAC/R,SAASE,sBAAsBA,CAACF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACM,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACpE,SAAS,CAACqE,OAAO,CAACN,IAAI,CAACJ,OAAO,CAACC,SAAS,CAACQ,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASd,eAAeA,CAACX,CAAC,EAAE;EAAEW,eAAe,GAAGnD,MAAM,CAAC0C,cAAc,GAAG1C,MAAM,CAACkE,cAAc,CAACvB,IAAI,CAAC,CAAC,GAAG,SAASQ,eAAeA,CAACX,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACI,SAAS,IAAI5C,MAAM,CAACkE,cAAc,CAAC1B,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOW,eAAe,CAACX,CAAC,CAAC;AAAE;AACnN,SAASvB,eAAeA,CAAC1B,GAAG,EAAEyB,GAAG,EAAEsB,KAAK,EAAE;EAAEtB,GAAG,GAAGc,cAAc,CAACd,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIzB,GAAG,EAAE;IAAES,MAAM,CAACoB,cAAc,CAAC7B,GAAG,EAAEyB,GAAG,EAAE;MAAEsB,KAAK,EAAEA,KAAK;MAAEhC,UAAU,EAAE,IAAI;MAAEsB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEtC,GAAG,CAACyB,GAAG,CAAC,GAAGsB,KAAK;EAAE;EAAE,OAAO/C,GAAG;AAAE;AAC3O,SAASuC,cAAcA,CAACqC,GAAG,EAAE;EAAE,IAAInD,GAAG,GAAGoD,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO7E,OAAO,CAAC0B,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGqD,MAAM,CAACrD,GAAG,CAAC;AAAE;AAC5H,SAASoD,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIjF,OAAO,CAACgF,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC9E,MAAM,CAACiF,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACd,IAAI,CAACY,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIjF,OAAO,CAACqF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAInD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC+C,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X;AACA;AACA;AACA,OAAOO,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,IAAIC,UAAU,GAAG,0BAA0B;AAC3C,IAAIC,GAAG,GAAG,CAAC;AACX,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,OAAO;AACtB;AACA,SAASC,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvC,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOrG,OAAO,CAACsG,OAAO,EAAEL,aAAa,CAAC;EACxC;EACA,IAAIlG,WAAW,CAACsG,MAAM,CAAC,EAAE;IACvB,OAAOrG,OAAO,CAACsG,OAAO,EAAED,MAAM,CAAC;EACjC;EACA,OAAOC,OAAO;AAChB;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEnE,KAAK,EAAE;EACrC,IAAK,aAAamD,KAAK,CAACiB,cAAc,CAACD,OAAO,CAAC,EAAE;IAC/C,OAAO,aAAahB,KAAK,CAACkB,YAAY,CAACF,OAAO,EAAEnE,KAAK,CAAC;EACxD;EACA,IAAItC,WAAW,CAACyG,OAAO,CAAC,EAAE;IACxB,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAACH,OAAO,EAAEnE,KAAK,CAAC;EACzD;EACA,OAAO,aAAamD,KAAK,CAACmB,aAAa,CAACf,qBAAqB,EAAEvD,KAAK,CAAC;AACvE;AACA,OAAO,IAAIuE,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC1DhE,SAAS,CAAC+D,OAAO,EAAEC,cAAc,CAAC;EAClC,IAAIC,MAAM,GAAGtD,YAAY,CAACoD,OAAO,CAAC;EAClC,SAASA,OAAOA,CAAA,EAAG;IACjB,IAAIG,KAAK;IACT/E,eAAe,CAAC,IAAI,EAAE4E,OAAO,CAAC;IAC9B,KAAK,IAAII,IAAI,GAAGzF,SAAS,CAACC,MAAM,EAAEyF,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAG5F,SAAS,CAAC4F,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAACzC,IAAI,CAAClD,KAAK,CAAC2F,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDrF,eAAe,CAAC0C,sBAAsB,CAACyC,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDM,QAAQ,EAAE,CAAC,CAAC;MACZC,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,qBAAqB,EAAE;QACrBC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL;IACF,CAAC,CAAC;IACF9F,eAAe,CAAC0C,sBAAsB,CAACyC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUY,IAAI,EAAE;MAC7E,IAAIhG,GAAG,GAAGgG,IAAI,CAAChG,GAAG;QAChBiG,gBAAgB,GAAGD,IAAI,CAACC,gBAAgB;QACxCC,gBAAgB,GAAGF,IAAI,CAACE,gBAAgB;MAC1C,IAAIC,WAAW,GAAGf,KAAK,CAAC1E,KAAK;QAC3B0F,kBAAkB,GAAGD,WAAW,CAACC,kBAAkB;QACnDC,gBAAgB,GAAGF,WAAW,CAACE,gBAAgB;QAC/CC,UAAU,GAAGH,WAAW,CAACG,UAAU;QACnCC,MAAM,GAAGJ,WAAW,CAACI,MAAM;QAC3BC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;QAC/BC,OAAO,GAAGN,WAAW,CAACM,OAAO;MAC/B,IAAID,QAAQ,IAAIrC,QAAQ,CAACqC,QAAQ,CAACxG,GAAG,CAAC,CAAC,EAAE;QACvC,OAAOwG,QAAQ,CAACxG,GAAG,CAAC;MACtB;MACA,IAAI0G,QAAQ,GAAGJ,UAAU,CAACtG,GAAG,CAAC,GAAGiG,gBAAgB,GAAGM,MAAM;MAC1D,IAAII,QAAQ,GAAGL,UAAU,CAACtG,GAAG,CAAC,GAAGuG,MAAM;MACvC,IAAIH,kBAAkB,CAACpG,GAAG,CAAC,EAAE;QAC3B,OAAOqG,gBAAgB,CAACrG,GAAG,CAAC,GAAG0G,QAAQ,GAAGC,QAAQ;MACpD;MACA,IAAIN,gBAAgB,CAACrG,GAAG,CAAC,EAAE;QACzB,IAAI4G,gBAAgB,GAAGF,QAAQ;QAC/B,IAAIG,gBAAgB,GAAGJ,OAAO,CAACzG,GAAG,CAAC;QACnC,IAAI4G,gBAAgB,GAAGC,gBAAgB,EAAE;UACvC,OAAOC,IAAI,CAACC,GAAG,CAACJ,QAAQ,EAAEF,OAAO,CAACzG,GAAG,CAAC,CAAC;QACzC;QACA,OAAO8G,IAAI,CAACC,GAAG,CAACL,QAAQ,EAAED,OAAO,CAACzG,GAAG,CAAC,CAAC;MACzC;MACA,IAAIgH,eAAe,GAAGL,QAAQ,GAAGV,gBAAgB;MACjD,IAAIgB,eAAe,GAAGR,OAAO,CAACzG,GAAG,CAAC,GAAGkG,gBAAgB;MACrD,IAAIc,eAAe,GAAGC,eAAe,EAAE;QACrC,OAAOH,IAAI,CAACC,GAAG,CAACL,QAAQ,EAAED,OAAO,CAACzG,GAAG,CAAC,CAAC;MACzC;MACA,OAAO8G,IAAI,CAACC,GAAG,CAACJ,QAAQ,EAAEF,OAAO,CAACzG,GAAG,CAAC,CAAC;IACzC,CAAC,CAAC;IACF,OAAOoF,KAAK;EACd;EACArE,YAAY,CAACkE,OAAO,EAAE,CAAC;IACrBjF,GAAG,EAAE,mBAAmB;IACxBsB,KAAK,EAAE,SAAS4F,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDnH,GAAG,EAAE,oBAAoB;IACzBsB,KAAK,EAAE,SAAS8F,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDnH,GAAG,EAAE,YAAY;IACjBsB,KAAK,EAAE,SAAS6F,UAAUA,CAAA,EAAG;MAC3B,IAAIE,WAAW,GAAG,IAAI,CAACC,KAAK;QAC1B5B,QAAQ,GAAG2B,WAAW,CAAC3B,QAAQ;QAC/BC,SAAS,GAAG0B,WAAW,CAAC1B,SAAS;QACjCC,SAAS,GAAGyB,WAAW,CAACzB,SAAS;MACnC,IAAIA,SAAS,EAAE;QACb,IAAI,CAAC2B,WAAW,CAACC,IAAI,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC9G,KAAK,CAAC4F,UAAU,CAACR,CAAC,KAAK,IAAI,CAACwB,KAAK,CAACzB,qBAAqB,CAACC,CAAC,IAAI,IAAI,CAACpF,KAAK,CAAC4F,UAAU,CAACP,CAAC,KAAK,IAAI,CAACuB,KAAK,CAACzB,qBAAqB,CAACE,CAAC,EAAE;UACpI,IAAI,CAAC0B,QAAQ,CAAC;YACZ7B,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAI,CAAC2B,WAAW,CAACG,KAAK,CAAC;UACrBC,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;MACA,IAAI,IAAI,CAACJ,WAAW,IAAI,IAAI,CAACA,WAAW,CAACK,qBAAqB,EAAE;QAC9D,IAAIC,GAAG,GAAG,IAAI,CAACN,WAAW,CAACK,qBAAqB,CAAC,CAAC;QAClD,IAAId,IAAI,CAACgB,GAAG,CAACD,GAAG,CAACE,KAAK,GAAGrC,QAAQ,CAAC,GAAGrB,GAAG,IAAIyC,IAAI,CAACgB,GAAG,CAACD,GAAG,CAACG,MAAM,GAAGrC,SAAS,CAAC,GAAGtB,GAAG,EAAE;UAClF,IAAI,CAACoD,QAAQ,CAAC;YACZ/B,QAAQ,EAAEmC,GAAG,CAACE,KAAK;YACnBpC,SAAS,EAAEkC,GAAG,CAACG;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAItC,QAAQ,KAAK,CAAC,CAAC,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QAC9C,IAAI,CAAC8B,QAAQ,CAAC;UACZ/B,QAAQ,EAAE,CAAC,CAAC;UACZC,SAAS,EAAE,CAAC;QACd,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACD3F,GAAG,EAAE,QAAQ;IACbsB,KAAK,EAAE,SAAS2G,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;QACbC,MAAM,GAAG,IAAI;MACf,IAAIC,YAAY,GAAG,IAAI,CAAC1H,KAAK;QAC3BiE,OAAO,GAAGyD,YAAY,CAACzD,OAAO;QAC9B0D,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;QAClDC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;QAClDC,eAAe,GAAGH,YAAY,CAACG,eAAe;QAC9CC,UAAU,GAAGJ,YAAY,CAACI,UAAU;QACpCC,aAAa,GAAGL,YAAY,CAACK,aAAa;MAC5C,IAAIC,YAAY,GAAGjE,cAAc,CAACgE,aAAa,EAAED,UAAU,IAAI7D,OAAO,IAAIA,OAAO,CAAC9E,MAAM,GAAG8E,OAAO,CAACxF,MAAM,CAAC,UAAUoF,KAAK,EAAE;QACzH,OAAO,CAACpG,MAAM,CAACoG,KAAK,CAACjD,KAAK,CAAC;MAC7B,CAAC,CAAC,GAAGqD,OAAO,CAAC;MACb,IAAIgE,UAAU,GAAGD,YAAY,IAAIA,YAAY,CAAC7I,MAAM;MACpD,IAAI+I,YAAY,GAAG,IAAI,CAAClI,KAAK;QAC3BmE,OAAO,GAAG+D,YAAY,CAAC/D,OAAO;QAC9B4B,OAAO,GAAGmC,YAAY,CAACnC,OAAO;QAC9BH,UAAU,GAAGsC,YAAY,CAACtC,UAAU;QACpCE,QAAQ,GAAGoC,YAAY,CAACpC,QAAQ;QAChCqC,MAAM,GAAGD,YAAY,CAACC,MAAM;QAC5BC,YAAY,GAAGF,YAAY,CAACE,YAAY;MAC1C,IAAIC,UAAU,GAAGtJ,aAAa,CAAC;QAC7BuJ,aAAa,EAAE,MAAM;QACrBC,UAAU,EAAE,CAAC,IAAI,CAAC3B,KAAK,CAAC1B,SAAS,IAAIiD,MAAM,IAAIF,UAAU,GAAG,SAAS,GAAG,QAAQ;QAChFnC,QAAQ,EAAE,UAAU;QACpB0C,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE;MACR,CAAC,EAAEL,YAAY,CAAC;MAChB,IAAIM,UAAU,EAAEC,UAAU;MAC1B,IAAI7C,QAAQ,IAAIrC,QAAQ,CAACqC,QAAQ,CAACV,CAAC,CAAC,IAAI3B,QAAQ,CAACqC,QAAQ,CAACT,CAAC,CAAC,EAAE;QAC5DqD,UAAU,GAAG5C,QAAQ,CAACV,CAAC;QACvBuD,UAAU,GAAG7C,QAAQ,CAACT,CAAC;MACzB,CAAC,MAAM;QACL,IAAIuD,YAAY,GAAG,IAAI,CAAChC,KAAK;UAC3B5B,QAAQ,GAAG4D,YAAY,CAAC5D,QAAQ;UAChCC,SAAS,GAAG2D,YAAY,CAAC3D,SAAS;QACpC,IAAID,QAAQ,GAAG,CAAC,IAAIC,SAAS,GAAG,CAAC,IAAIW,UAAU,EAAE;UAC/C8C,UAAU,GAAG,IAAI,CAACG,YAAY,CAAC;YAC7BvJ,GAAG,EAAE,GAAG;YACRiG,gBAAgB,EAAEP,QAAQ;YAC1BQ,gBAAgB,EAAEO,OAAO,CAACsB;UAC5B,CAAC,CAAC;UACFsB,UAAU,GAAG,IAAI,CAACE,YAAY,CAAC;YAC7BvJ,GAAG,EAAE,GAAG;YACRiG,gBAAgB,EAAEN,SAAS;YAC3BO,gBAAgB,EAAEO,OAAO,CAACuB;UAC5B,CAAC,CAAC;QACJ,CAAC,MAAM;UACLe,UAAU,CAACE,UAAU,GAAG,QAAQ;QAClC;MACF;MACAF,UAAU,GAAGtJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,cAAc,CAAC;QAC1DyF,SAAS,EAAE,IAAI,CAAC9I,KAAK,CAAC+I,cAAc,GAAG,cAAc,CAAChE,MAAM,CAAC2D,UAAU,EAAE,MAAM,CAAC,CAAC3D,MAAM,CAAC4D,UAAU,EAAE,QAAQ,CAAC,GAAG,YAAY,CAAC5D,MAAM,CAAC2D,UAAU,EAAE,MAAM,CAAC,CAAC3D,MAAM,CAAC4D,UAAU,EAAE,KAAK;MAClL,CAAC,CAAC,CAAC,EAAEN,UAAU,CAAC;MAChB,IAAIV,iBAAiB,IAAIQ,MAAM,EAAE;QAC/BE,UAAU,GAAGtJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsE,cAAc,CAAC;UAC1D2F,UAAU,EAAE,YAAY,CAACjE,MAAM,CAAC6C,iBAAiB,EAAE,KAAK,CAAC,CAAC7C,MAAM,CAAC8C,eAAe;QAClF,CAAC,CAAC,CAAC,EAAEQ,UAAU,CAAC;MAClB;MACA,IAAIY,GAAG,GAAG3F,UAAU,CAACI,UAAU,GAAG8D,WAAW,GAAG,CAAC,CAAC,EAAEjI,eAAe,CAACiI,WAAW,EAAE,EAAE,CAACzC,MAAM,CAACrB,UAAU,EAAE,QAAQ,CAAC,EAAED,QAAQ,CAACiF,UAAU,CAAC,IAAI9C,UAAU,IAAInC,QAAQ,CAACmC,UAAU,CAACR,CAAC,CAAC,IAAIsD,UAAU,IAAI9C,UAAU,CAACR,CAAC,CAAC,EAAE7F,eAAe,CAACiI,WAAW,EAAE,EAAE,CAACzC,MAAM,CAACrB,UAAU,EAAE,OAAO,CAAC,EAAED,QAAQ,CAACiF,UAAU,CAAC,IAAI9C,UAAU,IAAInC,QAAQ,CAACmC,UAAU,CAACR,CAAC,CAAC,IAAIsD,UAAU,GAAG9C,UAAU,CAACR,CAAC,CAAC,EAAE7F,eAAe,CAACiI,WAAW,EAAE,EAAE,CAACzC,MAAM,CAACrB,UAAU,EAAE,SAAS,CAAC,EAAED,QAAQ,CAACkF,UAAU,CAAC,IAAI/C,UAAU,IAAInC,QAAQ,CAACmC,UAAU,CAACP,CAAC,CAAC,IAAIsD,UAAU,IAAI/C,UAAU,CAACP,CAAC,CAAC,EAAE9F,eAAe,CAACiI,WAAW,EAAE,EAAE,CAACzC,MAAM,CAACrB,UAAU,EAAE,MAAM,CAAC,EAAED,QAAQ,CAACkF,UAAU,CAAC,IAAI/C,UAAU,IAAInC,QAAQ,CAACmC,UAAU,CAACP,CAAC,CAAC,IAAIsD,UAAU,GAAG/C,UAAU,CAACP,CAAC,CAAC,EAAEmC,WAAW,CAAC,CAAC;MACzqB,QACE;QACA;QACA;QACA;QACArE,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;UACzB4E,QAAQ,EAAE,CAAC,CAAC;UACZC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;YACnC,IAAIA,KAAK,CAAC/J,GAAG,KAAK,QAAQ,EAAE;cAC1BmI,MAAM,CAACV,QAAQ,CAAC;gBACd7B,SAAS,EAAE,IAAI;gBACfC,qBAAqB,EAAEpG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,MAAM,CAACb,KAAK,CAACzB,qBAAqB,CAAC,EAAE,CAAC,CAAC,EAAE;kBAC9FC,CAAC,EAAEqC,MAAM,CAACzH,KAAK,CAAC4F,UAAU,CAACR,CAAC;kBAC5BC,CAAC,EAAEoC,MAAM,CAACzH,KAAK,CAAC4F,UAAU,CAACP;gBAC7B,CAAC;cACH,CAAC,CAAC;YACJ;UACF,CAAC;UACDiE,SAAS,EAAEL,GAAG;UACdM,KAAK,EAAElB,UAAU;UACjBmB,GAAG,EAAE,SAASA,GAAGA,CAACC,IAAI,EAAE;YACtBhC,MAAM,CAACZ,WAAW,GAAG4C,IAAI;UAC3B;QACF,CAAC,EAAEvF,aAAa,CAACC,OAAO,EAAEpF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACzEiE,OAAO,EAAE+D;QACX,CAAC,CAAC,CAAC;MAAC;IAER;EACF,CAAC,CAAC,CAAC;EACH,OAAOzD,OAAO;AAChB,CAAC,CAACnB,aAAa,CAAC;AAChB7D,eAAe,CAACgF,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClDhF,eAAe,CAACgF,OAAO,EAAE,cAAc,EAAE;EACvC4D,MAAM,EAAE,KAAK;EACbzC,kBAAkB,EAAE;IAClBN,CAAC,EAAE,KAAK;IACRC,CAAC,EAAE;EACL,CAAC;EACDM,gBAAgB,EAAE;IAChBP,CAAC,EAAE,KAAK;IACRC,CAAC,EAAE;EACL,CAAC;EACDQ,MAAM,EAAE,EAAE;EACVE,OAAO,EAAE;IACP2D,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE;EACN,CAAC;EACDjE,UAAU,EAAE;IACVR,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EACDyE,WAAW,EAAE,CAAC,CAAC;EACfC,SAAS,EAAE,KAAK;EAChB3B,YAAY,EAAE,CAAC,CAAC;EAChB4B,YAAY,EAAE,CAAC,CAAC;EAChBC,SAAS,EAAE,CAAC,CAAC;EACbC,UAAU,EAAE,CAAC,CAAC;EACdC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,OAAO;EAChBzC,iBAAiB,EAAE,CAACnE,MAAM,CAAC6G,KAAK;EAChCxC,eAAe,EAAE,MAAM;EACvBD,iBAAiB,EAAE,GAAG;EACtBE,UAAU,EAAE,IAAI;EAChBiB,cAAc,EAAE;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}