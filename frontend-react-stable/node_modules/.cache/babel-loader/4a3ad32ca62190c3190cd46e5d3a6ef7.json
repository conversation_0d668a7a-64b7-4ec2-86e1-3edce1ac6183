{"ast": null, "code": "import * as React from 'react';\nexport var IdContext = /*#__PURE__*/React.createContext(null);\nexport function getMenuId(uuid, eventKey) {\n  if (uuid === undefined) {\n    return null;\n  }\n  return \"\".concat(uuid, \"-\").concat(eventKey);\n}\n/**\n * Get `data-menu-id`\n */\n\nexport function useMenuId(eventKey) {\n  var id = React.useContext(IdContext);\n  return getMenuId(id, eventKey);\n}", "map": {"version": 3, "names": ["React", "IdContext", "createContext", "getMenuId", "uuid", "eventKey", "undefined", "concat", "useMenuId", "id", "useContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/context/IdContext.js"], "sourcesContent": ["import * as React from 'react';\nexport var IdContext = /*#__PURE__*/React.createContext(null);\nexport function getMenuId(uuid, eventKey) {\n  if (uuid === undefined) {\n    return null;\n  }\n\n  return \"\".concat(uuid, \"-\").concat(eventKey);\n}\n/**\n * Get `data-menu-id`\n */\n\nexport function useMenuId(eventKey) {\n  var id = React.useContext(IdContext);\n  return getMenuId(id, eventKey);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,SAAS,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC7D,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACxC,IAAID,IAAI,KAAKE,SAAS,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,OAAO,EAAE,CAACC,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,QAAQ,CAAC;AAC9C;AACA;AACA;AACA;;AAEA,OAAO,SAASG,SAASA,CAACH,QAAQ,EAAE;EAClC,IAAII,EAAE,GAAGT,KAAK,CAACU,UAAU,CAACT,SAAS,CAAC;EACpC,OAAOE,SAAS,CAACM,EAAE,EAAEJ,QAAQ,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}