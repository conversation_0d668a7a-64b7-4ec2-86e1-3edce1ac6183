{"ast": null, "code": "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as ReactDOM from 'react-dom';\n// Let compiler not to search module usage\nvar fullClone = _objectSpread({}, ReactDOM);\nvar version = fullClone.version,\n  reactRender = fullClone.render,\n  unmountComponentAtNode = fullClone.unmountComponentAtNode;\nvar createRoot;\ntry {\n  var mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18) {\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {\n  // Do nothing;\n}\nfunction toggleWarning(skip) {\n  var __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = fullClone.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && _typeof(__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nvar MARK = '__rc_react_root__';\n\n// ========================== Render ==========================\n\nfunction modernRender(node, container) {\n  toggleWarning(true);\n  var root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nfunction legacyRender(node, container) {\n  reactRender === null || reactRender === void 0 || reactRender(node, container);\n}\n\n/** @private Test usage. Not work in prod */\nexport function _r(node, container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyRender(node, container);\n  }\n}\nexport function render(node, container) {\n  if (createRoot) {\n    modernRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n}\n\n// ========================= Unmount ==========================\nfunction modernUnmount(_x) {\n  return _modernUnmount.apply(this, arguments);\n}\nfunction _modernUnmount() {\n  _modernUnmount = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(container) {\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          return _context.abrupt(\"return\", Promise.resolve().then(function () {\n            var _container$MARK;\n            (_container$MARK = container[MARK]) === null || _container$MARK === void 0 || _container$MARK.unmount();\n            delete container[MARK];\n          }));\n        case 1:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return _modernUnmount.apply(this, arguments);\n}\nfunction legacyUnmount(container) {\n  unmountComponentAtNode(container);\n}\n\n/** @private Test usage. Not work in prod */\nexport function _u(container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyUnmount(container);\n  }\n}\nexport function unmount(_x2) {\n  return _unmount.apply(this, arguments);\n}\nfunction _unmount() {\n  _unmount = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(container) {\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          if (!(createRoot !== undefined)) {\n            _context2.next = 2;\n            break;\n          }\n          return _context2.abrupt(\"return\", modernUnmount(container));\n        case 2:\n          legacyUnmount(container);\n        case 3:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return _unmount.apply(this, arguments);\n}", "map": {"version": 3, "names": ["_regeneratorRuntime", "_asyncToGenerator", "_typeof", "_objectSpread", "ReactDOM", "fullClone", "version", "reactRender", "render", "unmountComponentAtNode", "createRoot", "mainVersion", "Number", "split", "e", "toggle<PERSON><PERSON>ning", "skip", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "usingClientEntryPoint", "MARK", "modernRender", "node", "container", "root", "legacyRender", "_r", "process", "env", "NODE_ENV", "modernUnmount", "_x", "_modernUnmount", "apply", "arguments", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "Promise", "resolve", "then", "_container$MARK", "unmount", "stop", "legacyUnmount", "_u", "_x2", "_unmount", "_callee2", "_callee2$", "_context2", "undefined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/React/render.js"], "sourcesContent": ["import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as ReactDOM from 'react-dom';\n// Let compiler not to search module usage\nvar fullClone = _objectSpread({}, ReactDOM);\nvar version = fullClone.version,\n  reactRender = fullClone.render,\n  unmountComponentAtNode = fullClone.unmountComponentAtNode;\nvar createRoot;\ntry {\n  var mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18) {\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {\n  // Do nothing;\n}\nfunction toggleWarning(skip) {\n  var __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = fullClone.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && _typeof(__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nvar MARK = '__rc_react_root__';\n\n// ========================== Render ==========================\n\nfunction modernRender(node, container) {\n  toggleWarning(true);\n  var root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nfunction legacyRender(node, container) {\n  reactRender === null || reactRender === void 0 || reactRender(node, container);\n}\n\n/** @private Test usage. Not work in prod */\nexport function _r(node, container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyRender(node, container);\n  }\n}\nexport function render(node, container) {\n  if (createRoot) {\n    modernRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n}\n\n// ========================= Unmount ==========================\nfunction modernUnmount(_x) {\n  return _modernUnmount.apply(this, arguments);\n}\nfunction _modernUnmount() {\n  _modernUnmount = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(container) {\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          return _context.abrupt(\"return\", Promise.resolve().then(function () {\n            var _container$MARK;\n            (_container$MARK = container[MARK]) === null || _container$MARK === void 0 || _container$MARK.unmount();\n            delete container[MARK];\n          }));\n        case 1:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return _modernUnmount.apply(this, arguments);\n}\nfunction legacyUnmount(container) {\n  unmountComponentAtNode(container);\n}\n\n/** @private Test usage. Not work in prod */\nexport function _u(container) {\n  if (process.env.NODE_ENV !== 'production') {\n    return legacyUnmount(container);\n  }\n}\nexport function unmount(_x2) {\n  return _unmount.apply(this, arguments);\n}\nfunction _unmount() {\n  _unmount = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(container) {\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          if (!(createRoot !== undefined)) {\n            _context2.next = 2;\n            break;\n          }\n          return _context2.abrupt(\"return\", modernUnmount(container));\n        case 2:\n          legacyUnmount(container);\n        case 3:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return _unmount.apply(this, arguments);\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC;AACA,IAAIC,SAAS,GAAGF,aAAa,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;AAC3C,IAAIE,OAAO,GAAGD,SAAS,CAACC,OAAO;EAC7BC,WAAW,GAAGF,SAAS,CAACG,MAAM;EAC9BC,sBAAsB,GAAGJ,SAAS,CAACI,sBAAsB;AAC3D,IAAIC,UAAU;AACd,IAAI;EACF,IAAIC,WAAW,GAAGC,MAAM,CAAC,CAACN,OAAO,IAAI,EAAE,EAAEO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,IAAIF,WAAW,IAAI,EAAE,EAAE;IACrBD,UAAU,GAAGL,SAAS,CAACK,UAAU;EACnC;AACF,CAAC,CAAC,OAAOI,CAAC,EAAE;EACV;AAAA;AAEF,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,kDAAkD,GAAGZ,SAAS,CAACY,kDAAkD;EACrH,IAAIA,kDAAkD,IAAIf,OAAO,CAACe,kDAAkD,CAAC,KAAK,QAAQ,EAAE;IAClIA,kDAAkD,CAACC,qBAAqB,GAAGF,IAAI;EACjF;AACF;AACA,IAAIG,IAAI,GAAG,mBAAmB;;AAE9B;;AAEA,SAASC,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACrCP,aAAa,CAAC,IAAI,CAAC;EACnB,IAAIQ,IAAI,GAAGD,SAAS,CAACH,IAAI,CAAC,IAAIT,UAAU,CAACY,SAAS,CAAC;EACnDP,aAAa,CAAC,KAAK,CAAC;EACpBQ,IAAI,CAACf,MAAM,CAACa,IAAI,CAAC;EACjBC,SAAS,CAACH,IAAI,CAAC,GAAGI,IAAI;AACxB;AACA,SAASC,YAAYA,CAACH,IAAI,EAAEC,SAAS,EAAE;EACrCf,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACc,IAAI,EAAEC,SAAS,CAAC;AAChF;;AAEA;AACA,OAAO,SAASG,EAAEA,CAACJ,IAAI,EAAEC,SAAS,EAAE;EAClC,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOJ,YAAY,CAACH,IAAI,EAAEC,SAAS,CAAC;EACtC;AACF;AACA,OAAO,SAASd,MAAMA,CAACa,IAAI,EAAEC,SAAS,EAAE;EACtC,IAAIZ,UAAU,EAAE;IACdU,YAAY,CAACC,IAAI,EAAEC,SAAS,CAAC;IAC7B;EACF;EACAE,YAAY,CAACH,IAAI,EAAEC,SAAS,CAAC;AAC/B;;AAEA;AACA,SAASO,aAAaA,CAACC,EAAE,EAAE;EACzB,OAAOC,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAC9C;AACA,SAASF,cAAcA,CAAA,EAAG;EACxBA,cAAc,GAAG9B,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACkC,IAAI,CAAC,SAASC,OAAOA,CAACb,SAAS,EAAE;IACtG,OAAOtB,mBAAmB,CAAC,CAAC,CAACoC,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;MAC5D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;QAC7C,KAAK,CAAC;UACJ,OAAOF,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAEC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;YAClE,IAAIC,eAAe;YACnB,CAACA,eAAe,GAAGvB,SAAS,CAACH,IAAI,CAAC,MAAM,IAAI,IAAI0B,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACC,OAAO,CAAC,CAAC;YACvG,OAAOxB,SAAS,CAACH,IAAI,CAAC;UACxB,CAAC,CAAC,CAAC;QACL,KAAK,CAAC;QACN,KAAK,KAAK;UACR,OAAOmB,QAAQ,CAACS,IAAI,CAAC,CAAC;MAC1B;IACF,CAAC,EAAEZ,OAAO,CAAC;EACb,CAAC,CAAC,CAAC;EACH,OAAOJ,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAC9C;AACA,SAASe,aAAaA,CAAC1B,SAAS,EAAE;EAChCb,sBAAsB,CAACa,SAAS,CAAC;AACnC;;AAEA;AACA,OAAO,SAAS2B,EAAEA,CAAC3B,SAAS,EAAE;EAC5B,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOoB,aAAa,CAAC1B,SAAS,CAAC;EACjC;AACF;AACA,OAAO,SAASwB,OAAOA,CAACI,GAAG,EAAE;EAC3B,OAAOC,QAAQ,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACxC;AACA,SAASkB,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGlD,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACkC,IAAI,CAAC,SAASkB,QAAQA,CAAC9B,SAAS,EAAE;IACjG,OAAOtB,mBAAmB,CAAC,CAAC,CAACoC,IAAI,CAAC,SAASiB,SAASA,CAACC,SAAS,EAAE;MAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACf,IAAI,GAAGe,SAAS,CAACd,IAAI;QAC/C,KAAK,CAAC;UACJ,IAAI,EAAE9B,UAAU,KAAK6C,SAAS,CAAC,EAAE;YAC/BD,SAAS,CAACd,IAAI,GAAG,CAAC;YAClB;UACF;UACA,OAAOc,SAAS,CAACb,MAAM,CAAC,QAAQ,EAAEZ,aAAa,CAACP,SAAS,CAAC,CAAC;QAC7D,KAAK,CAAC;UACJ0B,aAAa,CAAC1B,SAAS,CAAC;QAC1B,KAAK,CAAC;QACN,KAAK,KAAK;UACR,OAAOgC,SAAS,CAACP,IAAI,CAAC,CAAC;MAC3B;IACF,CAAC,EAAEK,QAAQ,CAAC;EACd,CAAC,CAAC,CAAC;EACH,OAAOD,QAAQ,CAACnB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}