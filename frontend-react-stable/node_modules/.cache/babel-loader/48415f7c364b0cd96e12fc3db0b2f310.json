{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Removed props:\n *  - childrenProps\n */\nimport { alignElement, alignPoint } from 'dom-align';\nimport isEqual from \"rc-util/es/isEqual\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport React from 'react';\nimport useBuffer from \"./hooks/useBuffer\";\nimport { isSamePoint, monitorResize, restoreFocus } from \"./util\";\nfunction getElement(func) {\n  if (typeof func !== 'function') return null;\n  return func();\n}\nfunction getPoint(point) {\n  if (_typeof(point) !== 'object' || !point) return null;\n  return point;\n}\nvar Align = function Align(_ref, ref) {\n  var children = _ref.children,\n    disabled = _ref.disabled,\n    target = _ref.target,\n    align = _ref.align,\n    onAlign = _ref.onAlign,\n    monitorWindowResize = _ref.monitorWindowResize,\n    _ref$monitorBufferTim = _ref.monitorBufferTime,\n    monitorBufferTime = _ref$monitorBufferTim === void 0 ? 0 : _ref$monitorBufferTim;\n  var cacheRef = React.useRef({});\n  /** Popup node ref */\n\n  var nodeRef = React.useRef();\n  var childNode = React.Children.only(children); // ===================== Align ======================\n  // We save the props here to avoid closure makes props ood\n\n  var forceAlignPropsRef = React.useRef({});\n  forceAlignPropsRef.current.disabled = disabled;\n  forceAlignPropsRef.current.target = target;\n  forceAlignPropsRef.current.align = align;\n  forceAlignPropsRef.current.onAlign = onAlign;\n  var _useBuffer = useBuffer(function () {\n      var _forceAlignPropsRef$c = forceAlignPropsRef.current,\n        latestDisabled = _forceAlignPropsRef$c.disabled,\n        latestTarget = _forceAlignPropsRef$c.target,\n        latestAlign = _forceAlignPropsRef$c.align,\n        latestOnAlign = _forceAlignPropsRef$c.onAlign;\n      var source = nodeRef.current;\n      if (!latestDisabled && latestTarget && source) {\n        var _result;\n        var _element = getElement(latestTarget);\n        var _point = getPoint(latestTarget);\n        cacheRef.current.element = _element;\n        cacheRef.current.point = _point;\n        cacheRef.current.align = latestAlign; // IE lose focus after element realign\n        // We should record activeElement and restore later\n\n        var _document = document,\n          activeElement = _document.activeElement; // We only align when element is visible\n\n        if (_element && isVisible(_element)) {\n          _result = alignElement(source, _element, latestAlign);\n        } else if (_point) {\n          _result = alignPoint(source, _point, latestAlign);\n        }\n        restoreFocus(activeElement, source);\n        if (latestOnAlign && _result) {\n          latestOnAlign(source, _result);\n        }\n        return true;\n      }\n      return false;\n    }, monitorBufferTime),\n    _useBuffer2 = _slicedToArray(_useBuffer, 2),\n    _forceAlign = _useBuffer2[0],\n    cancelForceAlign = _useBuffer2[1]; // ===================== Effect =====================\n  // Handle props change\n\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    element = _React$useState2[0],\n    setElement = _React$useState2[1];\n  var _React$useState3 = React.useState(),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    point = _React$useState4[0],\n    setPoint = _React$useState4[1];\n  useLayoutEffect(function () {\n    setElement(getElement(target));\n    setPoint(getPoint(target));\n  });\n  React.useEffect(function () {\n    if (cacheRef.current.element !== element || !isSamePoint(cacheRef.current.point, point) || !isEqual(cacheRef.current.align, align)) {\n      _forceAlign();\n    }\n  }); // Watch popup element resize\n\n  React.useEffect(function () {\n    var cancelFn = monitorResize(nodeRef.current, _forceAlign);\n    return cancelFn;\n  }, [nodeRef.current]); // Watch target element resize\n\n  React.useEffect(function () {\n    var cancelFn = monitorResize(element, _forceAlign);\n    return cancelFn;\n  }, [element]); // Listen for disabled change\n\n  React.useEffect(function () {\n    if (!disabled) {\n      _forceAlign();\n    } else {\n      cancelForceAlign();\n    }\n  }, [disabled]); // Listen for window resize\n\n  React.useEffect(function () {\n    if (monitorWindowResize) {\n      var cancelFn = addEventListener(window, 'resize', _forceAlign);\n      return cancelFn.remove;\n    }\n  }, [monitorWindowResize]); // Clear all if unmount\n\n  React.useEffect(function () {\n    return function () {\n      cancelForceAlign();\n    };\n  }, []); // ====================== Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {\n        return _forceAlign(true);\n      }\n    };\n  }); // ===================== Render =====================\n\n  if (/*#__PURE__*/React.isValidElement(childNode)) {\n    childNode = /*#__PURE__*/React.cloneElement(childNode, {\n      ref: composeRef(childNode.ref, nodeRef)\n    });\n  }\n  return childNode;\n};\nvar RcAlign = /*#__PURE__*/React.forwardRef(Align);\nRcAlign.displayName = 'Align';\nexport default RcAlign;", "map": {"version": 3, "names": ["_slicedToArray", "_typeof", "alignElement", "alignPoint", "isEqual", "addEventListener", "isVisible", "useLayoutEffect", "composeRef", "React", "useBuffer", "isSamePoint", "monitorResize", "restoreFocus", "getElement", "func", "getPoint", "point", "Align", "_ref", "ref", "children", "disabled", "target", "align", "onAlign", "monitorWindowResize", "_ref$monitorBufferTim", "monitorBufferTime", "cacheRef", "useRef", "nodeRef", "childNode", "Children", "only", "forceAlignPropsRef", "current", "_useBuffer", "_forceAlignPropsRef$c", "latestDisabled", "latestTarget", "latestAlign", "latestOnAlign", "source", "_result", "_element", "_point", "element", "_document", "document", "activeElement", "_useBuffer2", "_forceAlign", "cancelForceAlign", "_React$useState", "useState", "_React$useState2", "setElement", "_React$useState3", "_React$useState4", "setPoint", "useEffect", "cancelFn", "window", "remove", "useImperativeHandle", "forceAlign", "isValidElement", "cloneElement", "RcAlign", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-align/es/Align.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n\n/**\n * Removed props:\n *  - childrenProps\n */\nimport { alignElement, alignPoint } from 'dom-align';\nimport isEqual from \"rc-util/es/isEqual\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport React from 'react';\nimport useBuffer from \"./hooks/useBuffer\";\nimport { isSamePoint, monitorResize, restoreFocus } from \"./util\";\n\nfunction getElement(func) {\n  if (typeof func !== 'function') return null;\n  return func();\n}\n\nfunction getPoint(point) {\n  if (_typeof(point) !== 'object' || !point) return null;\n  return point;\n}\n\nvar Align = function Align(_ref, ref) {\n  var children = _ref.children,\n      disabled = _ref.disabled,\n      target = _ref.target,\n      align = _ref.align,\n      onAlign = _ref.onAlign,\n      monitorWindowResize = _ref.monitorWindowResize,\n      _ref$monitorBufferTim = _ref.monitorBufferTime,\n      monitorBufferTime = _ref$monitorBufferTim === void 0 ? 0 : _ref$monitorBufferTim;\n  var cacheRef = React.useRef({});\n  /** Popup node ref */\n\n  var nodeRef = React.useRef();\n  var childNode = React.Children.only(children); // ===================== Align ======================\n  // We save the props here to avoid closure makes props ood\n\n  var forceAlignPropsRef = React.useRef({});\n  forceAlignPropsRef.current.disabled = disabled;\n  forceAlignPropsRef.current.target = target;\n  forceAlignPropsRef.current.align = align;\n  forceAlignPropsRef.current.onAlign = onAlign;\n\n  var _useBuffer = useBuffer(function () {\n    var _forceAlignPropsRef$c = forceAlignPropsRef.current,\n        latestDisabled = _forceAlignPropsRef$c.disabled,\n        latestTarget = _forceAlignPropsRef$c.target,\n        latestAlign = _forceAlignPropsRef$c.align,\n        latestOnAlign = _forceAlignPropsRef$c.onAlign;\n    var source = nodeRef.current;\n\n    if (!latestDisabled && latestTarget && source) {\n      var _result;\n\n      var _element = getElement(latestTarget);\n\n      var _point = getPoint(latestTarget);\n\n      cacheRef.current.element = _element;\n      cacheRef.current.point = _point;\n      cacheRef.current.align = latestAlign; // IE lose focus after element realign\n      // We should record activeElement and restore later\n\n      var _document = document,\n          activeElement = _document.activeElement; // We only align when element is visible\n\n      if (_element && isVisible(_element)) {\n        _result = alignElement(source, _element, latestAlign);\n      } else if (_point) {\n        _result = alignPoint(source, _point, latestAlign);\n      }\n\n      restoreFocus(activeElement, source);\n\n      if (latestOnAlign && _result) {\n        latestOnAlign(source, _result);\n      }\n\n      return true;\n    }\n\n    return false;\n  }, monitorBufferTime),\n      _useBuffer2 = _slicedToArray(_useBuffer, 2),\n      _forceAlign = _useBuffer2[0],\n      cancelForceAlign = _useBuffer2[1]; // ===================== Effect =====================\n  // Handle props change\n\n\n  var _React$useState = React.useState(),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      element = _React$useState2[0],\n      setElement = _React$useState2[1];\n\n  var _React$useState3 = React.useState(),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      point = _React$useState4[0],\n      setPoint = _React$useState4[1];\n\n  useLayoutEffect(function () {\n    setElement(getElement(target));\n    setPoint(getPoint(target));\n  });\n  React.useEffect(function () {\n    if (cacheRef.current.element !== element || !isSamePoint(cacheRef.current.point, point) || !isEqual(cacheRef.current.align, align)) {\n      _forceAlign();\n    }\n  }); // Watch popup element resize\n\n  React.useEffect(function () {\n    var cancelFn = monitorResize(nodeRef.current, _forceAlign);\n    return cancelFn;\n  }, [nodeRef.current]); // Watch target element resize\n\n  React.useEffect(function () {\n    var cancelFn = monitorResize(element, _forceAlign);\n    return cancelFn;\n  }, [element]); // Listen for disabled change\n\n  React.useEffect(function () {\n    if (!disabled) {\n      _forceAlign();\n    } else {\n      cancelForceAlign();\n    }\n  }, [disabled]); // Listen for window resize\n\n  React.useEffect(function () {\n    if (monitorWindowResize) {\n      var cancelFn = addEventListener(window, 'resize', _forceAlign);\n      return cancelFn.remove;\n    }\n  }, [monitorWindowResize]); // Clear all if unmount\n\n  React.useEffect(function () {\n    return function () {\n      cancelForceAlign();\n    };\n  }, []); // ====================== Ref =======================\n\n  React.useImperativeHandle(ref, function () {\n    return {\n      forceAlign: function forceAlign() {\n        return _forceAlign(true);\n      }\n    };\n  }); // ===================== Render =====================\n\n  if ( /*#__PURE__*/React.isValidElement(childNode)) {\n    childNode = /*#__PURE__*/React.cloneElement(childNode, {\n      ref: composeRef(childNode.ref, nodeRef)\n    });\n  }\n\n  return childNode;\n};\n\nvar RcAlign = /*#__PURE__*/React.forwardRef(Align);\nRcAlign.displayName = 'Align';\nexport default RcAlign;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;;AAEvD;AACA;AACA;AACA;AACA,SAASC,YAAY,EAAEC,UAAU,QAAQ,WAAW;AACpD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,WAAW,EAAEC,aAAa,EAAEC,YAAY,QAAQ,QAAQ;AAEjE,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,OAAO,IAAI;EAC3C,OAAOA,IAAI,CAAC,CAAC;AACf;AAEA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIhB,OAAO,CAACgB,KAAK,CAAC,KAAK,QAAQ,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACtD,OAAOA,KAAK;AACd;AAEA,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACpC,IAAIC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,mBAAmB,GAAGP,IAAI,CAACO,mBAAmB;IAC9CC,qBAAqB,GAAGR,IAAI,CAACS,iBAAiB;IAC9CA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;EACpF,IAAIE,QAAQ,GAAGpB,KAAK,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,IAAIC,OAAO,GAAGtB,KAAK,CAACqB,MAAM,CAAC,CAAC;EAC5B,IAAIE,SAAS,GAAGvB,KAAK,CAACwB,QAAQ,CAACC,IAAI,CAACb,QAAQ,CAAC,CAAC,CAAC;EAC/C;;EAEA,IAAIc,kBAAkB,GAAG1B,KAAK,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC;EACzCK,kBAAkB,CAACC,OAAO,CAACd,QAAQ,GAAGA,QAAQ;EAC9Ca,kBAAkB,CAACC,OAAO,CAACb,MAAM,GAAGA,MAAM;EAC1CY,kBAAkB,CAACC,OAAO,CAACZ,KAAK,GAAGA,KAAK;EACxCW,kBAAkB,CAACC,OAAO,CAACX,OAAO,GAAGA,OAAO;EAE5C,IAAIY,UAAU,GAAG3B,SAAS,CAAC,YAAY;MACrC,IAAI4B,qBAAqB,GAAGH,kBAAkB,CAACC,OAAO;QAClDG,cAAc,GAAGD,qBAAqB,CAAChB,QAAQ;QAC/CkB,YAAY,GAAGF,qBAAqB,CAACf,MAAM;QAC3CkB,WAAW,GAAGH,qBAAqB,CAACd,KAAK;QACzCkB,aAAa,GAAGJ,qBAAqB,CAACb,OAAO;MACjD,IAAIkB,MAAM,GAAGZ,OAAO,CAACK,OAAO;MAE5B,IAAI,CAACG,cAAc,IAAIC,YAAY,IAAIG,MAAM,EAAE;QAC7C,IAAIC,OAAO;QAEX,IAAIC,QAAQ,GAAG/B,UAAU,CAAC0B,YAAY,CAAC;QAEvC,IAAIM,MAAM,GAAG9B,QAAQ,CAACwB,YAAY,CAAC;QAEnCX,QAAQ,CAACO,OAAO,CAACW,OAAO,GAAGF,QAAQ;QACnChB,QAAQ,CAACO,OAAO,CAACnB,KAAK,GAAG6B,MAAM;QAC/BjB,QAAQ,CAACO,OAAO,CAACZ,KAAK,GAAGiB,WAAW,CAAC,CAAC;QACtC;;QAEA,IAAIO,SAAS,GAAGC,QAAQ;UACpBC,aAAa,GAAGF,SAAS,CAACE,aAAa,CAAC,CAAC;;QAE7C,IAAIL,QAAQ,IAAIvC,SAAS,CAACuC,QAAQ,CAAC,EAAE;UACnCD,OAAO,GAAG1C,YAAY,CAACyC,MAAM,EAAEE,QAAQ,EAAEJ,WAAW,CAAC;QACvD,CAAC,MAAM,IAAIK,MAAM,EAAE;UACjBF,OAAO,GAAGzC,UAAU,CAACwC,MAAM,EAAEG,MAAM,EAAEL,WAAW,CAAC;QACnD;QAEA5B,YAAY,CAACqC,aAAa,EAAEP,MAAM,CAAC;QAEnC,IAAID,aAAa,IAAIE,OAAO,EAAE;UAC5BF,aAAa,CAACC,MAAM,EAAEC,OAAO,CAAC;QAChC;QAEA,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,EAAEhB,iBAAiB,CAAC;IACjBuB,WAAW,GAAGnD,cAAc,CAACqC,UAAU,EAAE,CAAC,CAAC;IAC3Ce,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC5BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC;;EAGA,IAAIG,eAAe,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,CAAC;IAClCC,gBAAgB,GAAGxD,cAAc,CAACsD,eAAe,EAAE,CAAC,CAAC;IACrDP,OAAO,GAAGS,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAEpC,IAAIE,gBAAgB,GAAGjD,KAAK,CAAC8C,QAAQ,CAAC,CAAC;IACnCI,gBAAgB,GAAG3D,cAAc,CAAC0D,gBAAgB,EAAE,CAAC,CAAC;IACtDzC,KAAK,GAAG0C,gBAAgB,CAAC,CAAC,CAAC;IAC3BC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAElCpD,eAAe,CAAC,YAAY;IAC1BkD,UAAU,CAAC3C,UAAU,CAACS,MAAM,CAAC,CAAC;IAC9BqC,QAAQ,CAAC5C,QAAQ,CAACO,MAAM,CAAC,CAAC;EAC5B,CAAC,CAAC;EACFd,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAIhC,QAAQ,CAACO,OAAO,CAACW,OAAO,KAAKA,OAAO,IAAI,CAACpC,WAAW,CAACkB,QAAQ,CAACO,OAAO,CAACnB,KAAK,EAAEA,KAAK,CAAC,IAAI,CAACb,OAAO,CAACyB,QAAQ,CAACO,OAAO,CAACZ,KAAK,EAAEA,KAAK,CAAC,EAAE;MAClI4B,WAAW,CAAC,CAAC;IACf;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ3C,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAIC,QAAQ,GAAGlD,aAAa,CAACmB,OAAO,CAACK,OAAO,EAAEgB,WAAW,CAAC;IAC1D,OAAOU,QAAQ;EACjB,CAAC,EAAE,CAAC/B,OAAO,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEvB3B,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAIC,QAAQ,GAAGlD,aAAa,CAACmC,OAAO,EAAEK,WAAW,CAAC;IAClD,OAAOU,QAAQ;EACjB,CAAC,EAAE,CAACf,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEftC,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACvC,QAAQ,EAAE;MACb8B,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhBb,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,IAAInC,mBAAmB,EAAE;MACvB,IAAIoC,QAAQ,GAAGzD,gBAAgB,CAAC0D,MAAM,EAAE,QAAQ,EAAEX,WAAW,CAAC;MAC9D,OAAOU,QAAQ,CAACE,MAAM;IACxB;EACF,CAAC,EAAE,CAACtC,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAE3BjB,KAAK,CAACoD,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBR,gBAAgB,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER5C,KAAK,CAACwD,mBAAmB,CAAC7C,GAAG,EAAE,YAAY;IACzC,OAAO;MACL8C,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOd,WAAW,CAAC,IAAI,CAAC;MAC1B;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAK,aAAa3C,KAAK,CAAC0D,cAAc,CAACnC,SAAS,CAAC,EAAE;IACjDA,SAAS,GAAG,aAAavB,KAAK,CAAC2D,YAAY,CAACpC,SAAS,EAAE;MACrDZ,GAAG,EAAEZ,UAAU,CAACwB,SAAS,CAACZ,GAAG,EAAEW,OAAO;IACxC,CAAC,CAAC;EACJ;EAEA,OAAOC,SAAS;AAClB,CAAC;AAED,IAAIqC,OAAO,GAAG,aAAa5D,KAAK,CAAC6D,UAAU,CAACpD,KAAK,CAAC;AAClDmD,OAAO,CAACE,WAAW,GAAG,OAAO;AAC7B,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}