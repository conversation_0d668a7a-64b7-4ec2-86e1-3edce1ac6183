{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { FormContext } from '../context';\nexport default function useItemRef() {\n  var _React$useContext = React.useContext(FormContext),\n    itemRef = _React$useContext.itemRef;\n  var cacheRef = React.useRef({});\n  function getRef(name, children) {\n    var childrenRef = children && _typeof(children) === 'object' && children.ref;\n    var nameStr = name.join('_');\n    if (cacheRef.current.name !== nameStr || cacheRef.current.originRef !== childrenRef) {\n      cacheRef.current.name = nameStr;\n      cacheRef.current.originRef = childrenRef;\n      cacheRef.current.ref = composeRef(itemRef(name), childrenRef);\n    }\n    return cacheRef.current.ref;\n  }\n  return getRef;\n}", "map": {"version": 3, "names": ["_typeof", "composeRef", "React", "FormContext", "useItemRef", "_React$useContext", "useContext", "itemRef", "cacheRef", "useRef", "getRef", "name", "children", "childrenRef", "ref", "nameStr", "join", "current", "originRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/form/hooks/useItemRef.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { FormContext } from '../context';\nexport default function useItemRef() {\n  var _React$useContext = React.useContext(FormContext),\n    itemRef = _React$useContext.itemRef;\n  var cacheRef = React.useRef({});\n  function getRef(name, children) {\n    var childrenRef = children && _typeof(children) === 'object' && children.ref;\n    var nameStr = name.join('_');\n    if (cacheRef.current.name !== nameStr || cacheRef.current.originRef !== childrenRef) {\n      cacheRef.current.name = nameStr;\n      cacheRef.current.originRef = childrenRef;\n      cacheRef.current.ref = composeRef(itemRef(name), childrenRef);\n    }\n    return cacheRef.current.ref;\n  }\n  return getRef;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,YAAY;AACxC,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,IAAIC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAACH,WAAW,CAAC;IACnDI,OAAO,GAAGF,iBAAiB,CAACE,OAAO;EACrC,IAAIC,QAAQ,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,SAASC,MAAMA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC9B,IAAIC,WAAW,GAAGD,QAAQ,IAAIZ,OAAO,CAACY,QAAQ,CAAC,KAAK,QAAQ,IAAIA,QAAQ,CAACE,GAAG;IAC5E,IAAIC,OAAO,GAAGJ,IAAI,CAACK,IAAI,CAAC,GAAG,CAAC;IAC5B,IAAIR,QAAQ,CAACS,OAAO,CAACN,IAAI,KAAKI,OAAO,IAAIP,QAAQ,CAACS,OAAO,CAACC,SAAS,KAAKL,WAAW,EAAE;MACnFL,QAAQ,CAACS,OAAO,CAACN,IAAI,GAAGI,OAAO;MAC/BP,QAAQ,CAACS,OAAO,CAACC,SAAS,GAAGL,WAAW;MACxCL,QAAQ,CAACS,OAAO,CAACH,GAAG,GAAGb,UAAU,CAACM,OAAO,CAACI,IAAI,CAAC,EAAEE,WAAW,CAAC;IAC/D;IACA,OAAOL,QAAQ,CAACS,OAAO,CAACH,GAAG;EAC7B;EACA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}