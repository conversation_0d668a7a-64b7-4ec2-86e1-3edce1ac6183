{"ast": null, "code": "export function findExpandedKeys() {\n  var prev = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var next = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var prevLen = prev.length;\n  var nextLen = next.length;\n  if (Math.abs(prevLen - nextLen) !== 1) {\n    return {\n      add: false,\n      key: null\n    };\n  }\n  function find(shorter, longer) {\n    var cache = new Map();\n    shorter.forEach(function (key) {\n      cache.set(key, true);\n    });\n    var keys = longer.filter(function (key) {\n      return !cache.has(key);\n    });\n    return keys.length === 1 ? keys[0] : null;\n  }\n  if (prevLen < nextLen) {\n    return {\n      add: true,\n      key: find(prev, next)\n    };\n  }\n  return {\n    add: false,\n    key: find(next, prev)\n  };\n}\nexport function getExpandRange(shorter, longer, key) {\n  var shorterStartIndex = shorter.findIndex(function (data) {\n    return data.key === key;\n  });\n  var shorterEndNode = shorter[shorterStartIndex + 1];\n  var longerStartIndex = longer.findIndex(function (data) {\n    return data.key === key;\n  });\n  if (shorterEndNode) {\n    var longerEndIndex = longer.findIndex(function (data) {\n      return data.key === shorterEndNode.key;\n    });\n    return longer.slice(longerStartIndex + 1, longerEndIndex);\n  }\n  return longer.slice(longerStartIndex + 1);\n}", "map": {"version": 3, "names": ["findExpandedKeys", "prev", "arguments", "length", "undefined", "next", "prevLen", "nextLen", "Math", "abs", "add", "key", "find", "shorter", "longer", "cache", "Map", "for<PERSON>ach", "set", "keys", "filter", "has", "getExpandRange", "shorterStartIndex", "findIndex", "data", "shorterEndNode", "longerStartIndex", "longerEndIndex", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/utils/diffUtil.js"], "sourcesContent": ["export function findExpandedKeys() {\n  var prev = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var next = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var prevLen = prev.length;\n  var nextLen = next.length;\n  if (Math.abs(prevLen - nextLen) !== 1) {\n    return {\n      add: false,\n      key: null\n    };\n  }\n  function find(shorter, longer) {\n    var cache = new Map();\n    shorter.forEach(function (key) {\n      cache.set(key, true);\n    });\n    var keys = longer.filter(function (key) {\n      return !cache.has(key);\n    });\n    return keys.length === 1 ? keys[0] : null;\n  }\n  if (prevLen < nextLen) {\n    return {\n      add: true,\n      key: find(prev, next)\n    };\n  }\n  return {\n    add: false,\n    key: find(next, prev)\n  };\n}\nexport function getExpandRange(shorter, longer, key) {\n  var shorterStartIndex = shorter.findIndex(function (data) {\n    return data.key === key;\n  });\n  var shorterEndNode = shorter[shorterStartIndex + 1];\n  var longerStartIndex = longer.findIndex(function (data) {\n    return data.key === key;\n  });\n  if (shorterEndNode) {\n    var longerEndIndex = longer.findIndex(function (data) {\n      return data.key === shorterEndNode.key;\n    });\n    return longer.slice(longerStartIndex + 1, longerEndIndex);\n  }\n  return longer.slice(longerStartIndex + 1);\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAAA,EAAG;EACjC,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACjF,IAAIG,IAAI,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACjF,IAAII,OAAO,GAAGL,IAAI,CAACE,MAAM;EACzB,IAAII,OAAO,GAAGF,IAAI,CAACF,MAAM;EACzB,IAAIK,IAAI,CAACC,GAAG,CAACH,OAAO,GAAGC,OAAO,CAAC,KAAK,CAAC,EAAE;IACrC,OAAO;MACLG,GAAG,EAAE,KAAK;MACVC,GAAG,EAAE;IACP,CAAC;EACH;EACA,SAASC,IAAIA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAC7B,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrBH,OAAO,CAACI,OAAO,CAAC,UAAUN,GAAG,EAAE;MAC7BI,KAAK,CAACG,GAAG,CAACP,GAAG,EAAE,IAAI,CAAC;IACtB,CAAC,CAAC;IACF,IAAIQ,IAAI,GAAGL,MAAM,CAACM,MAAM,CAAC,UAAUT,GAAG,EAAE;MACtC,OAAO,CAACI,KAAK,CAACM,GAAG,CAACV,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,OAAOQ,IAAI,CAAChB,MAAM,KAAK,CAAC,GAAGgB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;EAC3C;EACA,IAAIb,OAAO,GAAGC,OAAO,EAAE;IACrB,OAAO;MACLG,GAAG,EAAE,IAAI;MACTC,GAAG,EAAEC,IAAI,CAACX,IAAI,EAAEI,IAAI;IACtB,CAAC;EACH;EACA,OAAO;IACLK,GAAG,EAAE,KAAK;IACVC,GAAG,EAAEC,IAAI,CAACP,IAAI,EAAEJ,IAAI;EACtB,CAAC;AACH;AACA,OAAO,SAASqB,cAAcA,CAACT,OAAO,EAAEC,MAAM,EAAEH,GAAG,EAAE;EACnD,IAAIY,iBAAiB,GAAGV,OAAO,CAACW,SAAS,CAAC,UAAUC,IAAI,EAAE;IACxD,OAAOA,IAAI,CAACd,GAAG,KAAKA,GAAG;EACzB,CAAC,CAAC;EACF,IAAIe,cAAc,GAAGb,OAAO,CAACU,iBAAiB,GAAG,CAAC,CAAC;EACnD,IAAII,gBAAgB,GAAGb,MAAM,CAACU,SAAS,CAAC,UAAUC,IAAI,EAAE;IACtD,OAAOA,IAAI,CAACd,GAAG,KAAKA,GAAG;EACzB,CAAC,CAAC;EACF,IAAIe,cAAc,EAAE;IAClB,IAAIE,cAAc,GAAGd,MAAM,CAACU,SAAS,CAAC,UAAUC,IAAI,EAAE;MACpD,OAAOA,IAAI,CAACd,GAAG,KAAKe,cAAc,CAACf,GAAG;IACxC,CAAC,CAAC;IACF,OAAOG,MAAM,CAACe,KAAK,CAACF,gBAAgB,GAAG,CAAC,EAAEC,cAAc,CAAC;EAC3D;EACA,OAAOd,MAAM,CAACe,KAAK,CAACF,gBAAgB,GAAG,CAAC,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}