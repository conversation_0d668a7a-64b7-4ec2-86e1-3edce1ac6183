{"ast": null, "code": "/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\nmodule.exports = identity;", "map": {"version": 3, "names": ["identity", "value", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/identity.js"], "sourcesContent": ["/**\n * This method returns the first argument it receives.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {*} value Any value.\n * @returns {*} Returns `value`.\n * @example\n *\n * var object = { 'a': 1 };\n *\n * console.log(_.identity(object) === object);\n * // => true\n */\nfunction identity(value) {\n  return value;\n}\n\nmodule.exports = identity;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK;AACd;AAEAC,MAAM,CAACC,OAAO,GAAGH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}