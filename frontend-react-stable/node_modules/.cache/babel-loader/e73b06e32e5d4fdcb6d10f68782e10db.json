{"ast": null, "code": "export function createInvalidArgFactory(arg, name) {\n  return (dispatch, options) => {\n    throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n  };\n}", "map": {"version": 3, "names": ["createInvalidArgFactory", "arg", "name", "dispatch", "options", "Error", "wrappedComponentName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-redux/es/connect/invalidArgFactory.js"], "sourcesContent": ["export function createInvalidArgFactory(arg, name) {\n  return (dispatch, options) => {\n    throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,uBAAuBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACjD,OAAO,CAACC,QAAQ,EAAEC,OAAO,KAAK;IAC5B,MAAM,IAAIC,KAAK,CAAC,yBAAyB,OAAOJ,GAAG,QAAQC,IAAI,uCAAuCE,OAAO,CAACE,oBAAoB,GAAG,CAAC;EACxI,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}