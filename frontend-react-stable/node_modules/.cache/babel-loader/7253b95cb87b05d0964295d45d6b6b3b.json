{"ast": null, "code": "var Hash = require('./_Hash'),\n  ListCache = require('./_ListCache'),\n  Map = require('./_Map');\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash(),\n    'map': new (Map || ListCache)(),\n    'string': new Hash()\n  };\n}\nmodule.exports = mapCacheClear;", "map": {"version": 3, "names": ["Hash", "require", "ListCache", "Map", "mapCacheClear", "size", "__data__", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_mapCacheClear.js"], "sourcesContent": ["var Hash = require('./_Hash'),\n    ListCache = require('./_ListCache'),\n    Map = require('./_Map');\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\nmodule.exports = mapCacheClear;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;EACzBC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,GAAG,GAAGF,OAAO,CAAC,QAAQ,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAAA,EAAG;EACvB,IAAI,CAACC,IAAI,GAAG,CAAC;EACb,IAAI,CAACC,QAAQ,GAAG;IACd,MAAM,EAAE,IAAIN,IAAI,CAAD,CAAC;IAChB,KAAK,EAAE,KAAKG,GAAG,IAAID,SAAS,GAAC;IAC7B,QAAQ,EAAE,IAAIF,IAAI,CAAD;EACnB,CAAC;AACH;AAEAO,MAAM,CAACC,OAAO,GAAGJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script"}