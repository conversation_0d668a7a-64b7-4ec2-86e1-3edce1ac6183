{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport TagFilledSvg from \"@ant-design/icons-svg/es/asn/TagFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar TagFilled = function TagFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: TagFilledSvg\n  }));\n};\nTagFilled.displayName = 'TagFilled';\nexport default /*#__PURE__*/React.forwardRef(TagFilled);", "map": {"version": 3, "names": ["_objectSpread", "React", "TagFilledSvg", "AntdIcon", "TagFilled", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/TagFilled.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport TagFilledSvg from \"@ant-design/icons-svg/es/asn/TagFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar TagFilled = function TagFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: TagFilledSvg\n  }));\n};\nTagFilled.displayName = 'TagFilled';\nexport default /*#__PURE__*/React.forwardRef(TagFilled);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,SAAS,CAACK,WAAW,GAAG,WAAW;AACnC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}