{"ast": null, "code": "import * as React from 'react';\nvar SizeContext = /*#__PURE__*/React.createContext('default');\nexport var SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n    size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\nexport default SizeContext;", "map": {"version": 3, "names": ["React", "SizeContext", "createContext", "SizeContextProvider", "_ref", "children", "size", "createElement", "Consumer", "originSize", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/avatar/SizeContext.js"], "sourcesContent": ["import * as React from 'react';\nvar SizeContext = /*#__PURE__*/React.createContext('default');\nexport var SizeContextProvider = function SizeContextProvider(_ref) {\n  var children = _ref.children,\n    size = _ref.size;\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (originSize) {\n    return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n      value: size || originSize\n    }, children);\n  });\n};\nexport default SizeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,SAAS,CAAC;AAC7D,OAAO,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAClE,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,IAAI,GAAGF,IAAI,CAACE,IAAI;EAClB,OAAO,aAAaN,KAAK,CAACO,aAAa,CAACN,WAAW,CAACO,QAAQ,EAAE,IAAI,EAAE,UAAUC,UAAU,EAAE;IACxF,OAAO,aAAaT,KAAK,CAACO,aAAa,CAACN,WAAW,CAACS,QAAQ,EAAE;MAC5DC,KAAK,EAAEL,IAAI,IAAIG;IACjB,CAAC,EAAEJ,QAAQ,CAAC;EACd,CAAC,CAAC;AACJ,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}