{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, InputNumber, Slider, Checkbox, Progress, Alert, Row, Col, Statistic, Tabs } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { startMultiTrainingAsync } from '../services/taskApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\n\n// 数据源类型定义\n\n// 训练结果展示组件\nconst TrainingResultDisplay = ({\n  resultKey,\n  result\n}) => {\n  var _result$train_losses, _result$val_losses;\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"middle\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u534F\\u8BAE:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 17\n          }, this), \" \", selectedProt]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this), \" \", selectedDatatype]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BAD\\u7EC3\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D4B\\u8BD5\\u96C6\\u6570\\u636E\\u5F62\\u72B6\",\n            value: result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"R\\xB2 \\u5206\\u6570\",\n            value: result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A',\n            precision: 4,\n            valueStyle: {\n              color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C\",\n            value: result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A',\n            precision: 2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u4F7F\\u7528\\u7387\",\n            value: result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A',\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), result.train_losses && result.val_losses && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u8BAD\\u7EC3\\u635F\\u5931\\u66F2\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.train_losses.map((trainLoss, index) => ({\n                epoch: index + 1,\n                训练损失: trainLoss,\n                验证损失: result.val_losses[index] || null\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"epoch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u8BAD\\u7EC3\\u635F\\u5931\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9A8C\\u8BC1\\u635F\\u5931\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u8BAD\\u7EC3\\u8F6E\\u6570: \", result.train_losses.length, \" epochs | \\u6700\\u7EC8\\u8BAD\\u7EC3\\u635F\\u5931: \", (_result$train_losses = result.train_losses[result.train_losses.length - 1]) === null || _result$train_losses === void 0 ? void 0 : _result$train_losses.toFixed(6), \" | \\u6700\\u7EC8\\u9A8C\\u8BC1\\u635F\\u5931: \", (_result$val_losses = result.val_losses[result.val_losses.length - 1]) === null || _result$val_losses === void 0 ? void 0 : _result$val_losses.toFixed(6)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this), result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B9E\\u9645\\u503C vs \\u9884\\u6D4B\\u503C\\u5BF9\\u6BD4\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: 300,\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: result.y_test_actual.map((actual, index) => ({\n                index: index + 1,\n                实际值: actual,\n                预测值: result.y_pred[index]\n              })),\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"index\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u5B9E\\u9645\\u503C\",\n                stroke: \"#8884d8\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"\\u9884\\u6D4B\\u503C\",\n                stroke: \"#82ca9d\",\n                strokeWidth: 2,\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u663E\\u793A\\u6240\\u6709 \", result.y_test_actual.length, \" \\u4E2A\\u6D4B\\u8BD5\\u6837\\u672C\\u7684\\u9884\\u6D4B\\u5BF9\\u6BD4 | R\\xB2 \\u5206\\u6570: \", result.r2_score ? result.r2_score.toFixed(4) : result.r2 ? result.r2.toFixed(4) : 'N/A', \" | \\u5EFA\\u8BAE\\u9608\\u503C: \", result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this), result.model_save_path && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6A21\\u578B\\u6587\\u4EF6\\u4FE1\\u606F\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 20\n            }, this), \" \", result.model_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 17\n          }, this), result.scaler_y_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u51C6\\u5316\\u5668\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 22\n            }, this), \" \", result.scaler_y_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 19\n          }, this), result.params_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u53C2\\u6570\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 22\n            }, this), \" \", result.params_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 19\n          }, this), result.test_save_path && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6D4B\\u8BD5\\u6570\\u636E\\u4FDD\\u5B58\\u8DEF\\u5F84:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 22\n            }, this), \" \", result.test_save_path]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 19\n          }, this), result.static_anomaly_threshold && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EFA\\u8BAE\\u6E05\\u6D17\\u9608\\u503C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 22\n            }, this), \" \", result.static_anomaly_threshold.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 19\n          }, this), result.finished_time && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 22\n            }, this), \" \", result.finished_time]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 19\n          }, this), result.duration_seconds && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BAD\\u7EC3\\u8017\\u65F6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 22\n            }, this), \" \", result.duration_seconds.toFixed(2), \" \\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_c = TrainingResultDisplay;\nconst ModelTrainingPage = () => {\n  _s();\n  // 训练模式：single（单文件）或 multi（多文件）\n  const [trainingMode, setTrainingMode] = useState('single');\n\n  // 单文件模式的状态（保持向后兼容）\n  const [dataSource, setDataSource] = useState('local');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState([]);\n  const [selectedFile, setSelectedFile] = useState('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 多文件模式的状态\n  const [dataSources, setDataSources] = useState([{\n    id: '1',\n    type: 'local',\n    outputFolder: '/data/output',\n    enabled: true,\n    availableFiles: [],\n    filesLoading: false\n  }]);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState(null);\n  const [selectedResultKey, setSelectedResultKey] = useState('');\n\n  // 任务管理\n  const {\n    submitTrainingTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 多数据源管理函数\n  const addDataSource = () => {\n    const newSource = {\n      id: Date.now().toString(),\n      type: 'local',\n      outputFolder: `/data/output/model_${dataSources.length + 1}`,\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    };\n    setDataSources([...dataSources, newSource]);\n  };\n  const removeDataSource = id => {\n    setDataSources(dataSources.filter(source => source.id !== id));\n  };\n  const updateDataSource = (id, updates) => {\n    setDataSources(dataSources.map(source => source.id === id ? {\n      ...source,\n      ...updates\n    } : source));\n  };\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表（单文件模式）\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 获取多数据源的CSV文件列表\n  const fetchCsvFilesForSource = async (sourceId, csvDir) => {\n    if (!csvDir) return;\n    updateDataSource(sourceId, {\n      filesLoading: true\n    });\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      updateDataSource(sourceId, {\n        availableFiles: response.data.files || [],\n        filesLoading: false\n      });\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取文件列表失败');\n      updateDataSource(sourceId, {\n        availableFiles: [],\n        filesLoading: false\n      });\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置（单文件模式）\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 多数据源文件上传配置\n  const getUploadPropsForSource = sourceId => ({\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        updateDataSource(sourceId, {\n          file: info.fileList[0]\n        });\n      } else {\n        updateDataSource(sourceId, {\n          file: undefined\n        });\n      }\n    }\n  });\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = prots => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = {\n      ...selectedDatatypes\n    };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot]) {\n        newDatatypes[prot] = [datatypeOptions[prot][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol, datatypes) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证多文件训练输入\n  const validateMultiTraining = () => {\n    // 检查是否至少有一个有效的数据源\n    const validSources = dataSources.filter(source => {\n      if (source.type === 'upload') {\n        return source.file && source.outputFolder;\n      } else {\n        return source.csvDir && source.selectedFile && source.outputFolder;\n      }\n    });\n    if (validSources.length === 0) {\n      message.error('请至少配置一个有效的数据源');\n      return false;\n    }\n\n    // 检查输出路径是否重复\n    const outputPaths = validSources.map(s => s.outputFolder);\n    const uniquePaths = new Set(outputPaths);\n    if (outputPaths.length !== uniquePaths.size) {\n      message.error('模型保存路径不能重复');\n      return false;\n    }\n    return true;\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 根据训练模式进行不同的验证\n    if (trainingMode === 'multi') {\n      if (!validateMultiTraining()) {\n        return;\n      }\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload' && !uploadedFile) {\n        message.error('请上传CSV文件');\n        return;\n      }\n      if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n        message.error('请选择CSV文件');\n        return;\n      }\n    }\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n    const hasValidDatatypes = selectedProts.some(prot => selectedDatatypes[prot] && selectedDatatypes[prot].length > 0);\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        if (trainingMode === 'multi') {\n          // 多文件异步训练\n          const formData = new FormData();\n\n          // 添加文件\n          dataSources.forEach((source, index) => {\n            if (source.type === 'upload' && source.file) {\n              formData.append('files', source.file.originFileObj);\n            }\n          });\n\n          // 添加数据源配置\n          formData.append('data_sources', JSON.stringify(dataSources));\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交多文件异步任务\n          const taskId = await startMultiTrainingAsync(formData);\n          if (taskId) {\n            message.success(`多文件训练任务已启动（共${dataSources.length}个数据源），您可以继续使用其他功能，任务完成后会收到通知`);\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n          return; // 异步模式下直接返回\n        } else {\n          // 单文件异步训练（保持原有逻辑）\n          const formData = new FormData();\n          if (dataSource === 'upload') {\n            formData.append('file', uploadedFile.originFileObj);\n          } else {\n            formData.append('csv_dir', csvDir);\n            formData.append('selected_file', selectedFile);\n          }\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('output_folder', outputFolder);\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交异步任务\n          const taskId = await submitTrainingTask(formData);\n          if (taskId) {\n            message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n          return; // 异步模式下直接返回\n        }\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder,\n          auto_generate_template: autoGenerateTemplate\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress(prev => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n    } catch (error) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        var _error$response$data2, _error$response$data3;\n        if ((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.detail) {\n          errorMessage = error.response.data.detail;\n        } else if ((_error$response$data3 = error.response.data) !== null && _error$response$data3 !== void 0 && _error$response$data3.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u8BAD\\u7EC3\\u4E0E\\u7279\\u5F81\\u9884\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u4E0A\\u4F20\\u6216\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF0C\\u914D\\u7F6E\\u8BAD\\u7EC3\\u53C2\\u6570\\uFF0C\\u6839\\u636E\\u591A\\u7EF4\\u7279\\u5F81\\u8BAD\\u7EC3\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\uFF0C\\u5E76\\u8FDB\\u884C\\u7279\\u5F81\\u9884\\u6D4B\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8BAD\\u7EC3\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /home/<USER>\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: filesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: filesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedFile,\n                onChange: setSelectedFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: filesLoading,\n                children: availableFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u534F\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            value: selectedProts,\n            onChange: handleProtocolChange,\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\",\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n              value: prot,\n              children: prot\n            }, prot, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this), selectedProts.map(prot => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [prot, \" \\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: selectedDatatypes[prot] || [],\n            onChange: datatypes => handleDatatypeChange(prot, datatypes),\n            style: {\n              marginTop: 8\n            },\n            children: (datatypeOptions[prot] || []).map(datatype => /*#__PURE__*/_jsxDEV(Checkbox, {\n              value: datatype,\n              children: datatype\n            }, datatype, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 15\n          }, this)]\n        }, prot, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BAD\\u7EC3\\u53C2\\u6570\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this),\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(ExperimentOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u57FA\\u7840\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5B66\\u4E60\\u7387\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: learningRate,\n                    onChange: value => setLearningRate(value || 0.0001),\n                    min: 0.0001,\n                    max: 1,\n                    step: 0.0001,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"0.0001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u6279\\u91CF\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: batchSize,\n                    onChange: value => setBatchSize(value || 64),\n                    min: 1,\n                    max: 512,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u8BAD\\u7EC3\\u8F6E\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: epochs,\n                    onChange: value => setEpochs(value || 100),\n                    min: 1,\n                    max: 1000,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u53C2\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"middle\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5E8F\\u5217\\u957F\\u5EA6\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: sequenceLength,\n                    onChange: value => setSequenceLength(value || 10),\n                    min: 1,\n                    max: 100,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9690\\u85CF\\u5C42\\u5927\\u5C0F\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: hiddenSize,\n                    onChange: value => setHiddenSize(value || 50),\n                    min: 10,\n                    max: 512,\n                    step: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"50\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                align: \"middle\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 10,\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u5C42\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 14,\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    value: numLayers,\n                    onChange: value => setNumLayers(value || 2),\n                    min: 1,\n                    max: 10,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  align: \"middle\",\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: \"Dropout \\u6982\\u7387\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 12,\n                    style: {\n                      textAlign: 'right'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Text, {\n                      code: true,\n                      children: dropout\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 987,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                  value: dropout,\n                  onChange: setDropout,\n                  min: 0,\n                  max: 0.9,\n                  step: 0.05,\n                  marks: {\n                    0: '0',\n                    0.2: '0.2',\n                    0.5: '0.5',\n                    0.9: '0.9'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6A21\\u578B\\u4FDD\\u5B58\\u8DEF\\u5F84\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 17\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              value: outputFolder,\n              onChange: e => setOutputFolder(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              size: \"large\",\n              prefix: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1010,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: autoGenerateTemplate,\n              onChange: e => setAutoGenerateTemplate(e.target.checked),\n              children: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: \"\\u9009\\u62E9\\u6B64\\u9009\\u9879\\u540E\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u5728\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\u540E\\u81EA\\u52A8\\u8C03\\u7528\\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u529F\\u80FD\\uFF0C \\u6839\\u636E\\u8BAD\\u7EC3\\u7ED3\\u679C\\u4E2D\\u7684\\u9608\\u503C\\u4FE1\\u606F\\u751F\\u6210\\u76F8\\u5E94\\u7684\\u6E05\\u6D17\\u6A21\\u677F\\u6587\\u4EF6\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1035,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1034,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncTraining,\n            onChange: e => setUseAsyncTraining(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u8BAD\\u7EC3\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u8BAD\\u7EC3\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1059,\n          columnNumber: 11\n        }, this), useAsyncTraining && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1058,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1107,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartTraining,\n        loading: training,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: training ? '正在训练...' : '开始训练预测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1104,\n        columnNumber: 9\n      }, this), training && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u8BAD\\u7EC3\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8BAD\\u7EC3\\u5B8C\\u6210\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u6240\\u6709\\u6A21\\u578B\\u8BAD\\u7EC3\\u5B8C\\u6210\\uFF01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 19\n            }, this), trainingResults.result_path && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u7ED3\\u679C\\u5DF2\\u66F4\\u65B0\\u81F3:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1133,\n                columnNumber: 24\n              }, this), \" \", trainingResults.result_path]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 21\n            }, this), trainingResults.template_info && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                padding: 8,\n                backgroundColor: '#f6ffed',\n                border: '1px solid #b7eb8f',\n                borderRadius: 4\n              },\n              children: trainingResults.template_info.template_generated ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#52c41a',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1139,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1140,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.template_path]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.updated_thresholds]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1141,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#ff4d4f',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 30\n                  }, this), \" \", trainingResults.template_info.error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 21\n            }, this), Object.entries(trainingResults.results).map(([key, result]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u534F\\u8BAE\\u4E0E\\u6570\\u636E\\u7C7B\\u578B:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1153,\n                  columnNumber: 26\n                }, this), \" \", key]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6A21\\u578B\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.model_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6807\\u51C6\\u5316\\u5668\\u5DF2\\u4FDD\\u5B58\\u81F3: \", result.scaler_y_save_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 23\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1152,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 17\n          }, this),\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1127,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1126,\n        columnNumber: 11\n      }, this), trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u67E5\\u770B\\u6A21\\u578B\\u8BAD\\u7EC3\\u53CA\\u7279\\u5F81\\u9884\\u6D4B\\u7ED3\\u679C\",\n        className: \"function-card\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedResultKey,\n              onChange: setSelectedResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(trainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1180,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 13\n          }, this), selectedResultKey && trainingResults.results[selectedResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedResultKey,\n            result: trainingResults.results[selectedResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1169,\n        columnNumber: 9\n      }, this), completedTrainingTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u7ED3\\u679C\",\n        className: \"function-card\",\n        style: {\n          marginTop: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u5DF2\\u5B8C\\u6210\",\n          description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u8BAD\\u7EC3\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u4E0D\\u540C\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\u7684\\u8BAD\\u7EC3\\u6548\\u679C\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8BAD\\u7EC3\\u4EFB\\u52A1\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAsyncTaskId,\n              onChange: handleAsyncTaskSelect,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u8BAD\\u7EC3\\u4EFB\\u52A1\",\n              children: completedTrainingTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n                value: task.task_id,\n                children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n              }, task.task_id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1209,\n            columnNumber: 13\n          }, this), asyncTrainingResults && asyncTrainingResults.template_info && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8,\n              padding: 8,\n              backgroundColor: '#f6ffed',\n              border: '1px solid #b7eb8f',\n              borderRadius: 4\n            },\n            children: asyncTrainingResults.template_info.template_generated ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#52c41a',\n                  fontWeight: 'bold'\n                },\n                children: \"\\u2705 \\u6E05\\u6D17\\u6A21\\u677F\\u5DF2\\u81EA\\u52A8\\u751F\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u6A21\\u677F\\u8DEF\\u5F84:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.template_path]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u66F4\\u65B0\\u9608\\u503C\\u6570\\u91CF:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.updated_thresholds]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1232,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#ff4d4f',\n                  fontWeight: 'bold'\n                },\n                children: \"\\u274C \\u6E05\\u6D17\\u6A21\\u677F\\u751F\\u6210\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1239,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1240,\n                  columnNumber: 24\n                }, this), \" \", asyncTrainingResults.template_info.error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1238,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 15\n          }, this), asyncTrainingResults && asyncTrainingResults.results && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedAsyncResultKey,\n              onChange: setSelectedAsyncResultKey,\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\",\n              children: Object.keys(asyncTrainingResults.results).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                value: key,\n                children: key\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 15\n          }, this), selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && /*#__PURE__*/_jsxDEV(TrainingResultDisplay, {\n            resultKey: selectedAsyncResultKey,\n            result: asyncTrainingResults.results[selectedAsyncResultKey]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 725,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelTrainingPage, \"fP8YY1tbXOt//aSiF+ikfSm6+Q4=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelTrainingPage;\nexport default ModelTrainingPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TrainingResultDisplay\");\n$RefreshReg$(_c2, \"ModelTrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "InputNumber", "Slide<PERSON>", "Checkbox", "Progress", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Tabs", "InboxOutlined", "PlayCircleOutlined", "SettingOutlined", "ExperimentOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelTrainingAPI", "useTaskManager", "startMultiTrainingAsync", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "Option", "TabPane", "TrainingResultDisplay", "<PERSON><PERSON><PERSON>", "result", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "children", "direction", "size", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "span", "title", "value", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "color", "static_anomaly_threshold", "marginTop", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_memory", "gpu_utilization_percent", "gpu_utilization", "train_losses", "val_losses", "strong", "height", "data", "map", "trainLoss", "index", "epoch", "训练损失", "验证损失", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "length", "y_test_actual", "y_pred", "actual", "实际值", "预测值", "model_save_path", "description", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "showIcon", "_c", "ModelTrainingPage", "_s", "trainingMode", "setTrainingMode", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableFiles", "setAvailableFiles", "selectedFile", "setSelectedFile", "filesLoading", "setFilesLoading", "dataSources", "setDataSources", "id", "outputFolder", "enabled", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "epochs", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "dropout", "setDropout", "setOutputFolder", "autoGenerateTemplate", "setAutoGenerateTemplate", "training", "setTraining", "progress", "setProgress", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "submitTrainingTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncTraining", "setUseAsyncTraining", "addDataSource", "newSource", "Date", "now", "toString", "removeDataSource", "filter", "source", "updateDataSource", "updates", "asyncTrainingResults", "setAsyncTrainingResults", "selectedAsyncResult<PERSON>ey", "setSelectedAsyncResultKey", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedTrainingTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "results", "Object", "keys", "latestTask", "protocolOptions", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchCsvFilesForSource", "sourceId", "_error$response2", "_error$response2$data", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "getUploadPropsForSource", "file", "undefined", "handleProtocolChange", "prots", "newDatatypes", "for<PERSON>ach", "prot", "includes", "handleDatatypeChange", "protocol", "datatypes", "prev", "validateMultiTraining", "validSources", "outputPaths", "s", "uniquePaths", "Set", "handleStartTraining", "hasValidDatatypes", "some", "formData", "FormData", "append", "originFileObj", "JSON", "stringify", "success", "progressInterval", "setInterval", "clearInterval", "trainModel", "localTrainingData", "csv_dir", "selected_file", "selected_prots", "selected_datatypes", "learning_rate", "batch_size", "sequence_length", "hidden_size", "num_layers", "output_folder", "auto_generate_template", "trainModelLocal", "console", "log", "result_path", "errorMessage", "_error$response$data2", "_error$response$data3", "statusText", "status", "isFormValid", "level", "fontSize", "fontWeight", "marginBottom", "className", "Group", "e", "target", "compact", "display", "placeholder", "flex", "onClick", "loading", "disabled", "marginLeft", "spinning", "mode", "datatype", "align", "min", "max", "step", "textAlign", "code", "marks", "prefix", "checked", "icon", "percent", "template_info", "padding", "backgroundColor", "border", "borderRadius", "template_generated", "template_path", "updated_thresholds", "entries", "key", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelTrainingPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Tabs,\n  Switch,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined, PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { startMultiTrainingAsync } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\n// 数据源类型定义\ninterface TrainingDataSource {\n  id: string;\n  type: 'upload' | 'local';\n  file?: any;\n  csvDir?: string;\n  selectedFile?: string;\n  outputFolder: string;\n  enabled: boolean;\n  availableFiles?: string[];\n  filesLoading?: boolean;\n}\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"建议清洗阈值\"\n              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              precision={2}\n            />\n          </Col>\n        </Row>\n\n        <Row gutter={16} style={{ marginTop: 16 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"CPU使用率\"\n              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"内存使用\"\n              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU内存\"\n              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU使用率\"\n              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  // 训练模式：single（单文件）或 multi（多文件）\n  const [trainingMode, setTrainingMode] = useState<'single' | 'multi'>('single');\n\n  // 单文件模式的状态（保持向后兼容）\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 多文件模式的状态\n  const [dataSources, setDataSources] = useState<TrainingDataSource[]>([\n    {\n      id: '1',\n      type: 'local',\n      outputFolder: '/data/output',\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    }\n  ]);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n  const [autoGenerateTemplate, setAutoGenerateTemplate] = useState(false); // 新增：是否自动生成清洗模板\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 任务管理\n  const { submitTrainingTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 多数据源管理函数\n  const addDataSource = () => {\n    const newSource: TrainingDataSource = {\n      id: Date.now().toString(),\n      type: 'local',\n      outputFolder: `/data/output/model_${dataSources.length + 1}`,\n      enabled: true,\n      availableFiles: [],\n      filesLoading: false\n    };\n    setDataSources([...dataSources, newSource]);\n  };\n\n  const removeDataSource = (id: string) => {\n    setDataSources(dataSources.filter(source => source.id !== id));\n  };\n\n  const updateDataSource = (id: string, updates: Partial<TrainingDataSource>) => {\n    setDataSources(dataSources.map(source =>\n      source.id === id ? { ...source, ...updates } : source\n    ));\n  };\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState<any>(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState<string>('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表（单文件模式）\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 获取多数据源的CSV文件列表\n  const fetchCsvFilesForSource = async (sourceId: string, csvDir: string) => {\n    if (!csvDir) return;\n\n    updateDataSource(sourceId, { filesLoading: true });\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      updateDataSource(sourceId, {\n        availableFiles: response.data.files || [],\n        filesLoading: false\n      });\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      updateDataSource(sourceId, {\n        availableFiles: [],\n        filesLoading: false\n      });\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置（单文件模式）\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 多数据源文件上传配置\n  const getUploadPropsForSource = (sourceId: string) => ({\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        updateDataSource(sourceId, { file: info.fileList[0] });\n      } else {\n        updateDataSource(sourceId, { file: undefined });\n      }\n    },\n  });\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 验证多文件训练输入\n  const validateMultiTraining = (): boolean => {\n    // 检查是否至少有一个有效的数据源\n    const validSources = dataSources.filter(source => {\n      if (source.type === 'upload') {\n        return source.file && source.outputFolder;\n      } else {\n        return source.csvDir && source.selectedFile && source.outputFolder;\n      }\n    });\n\n    if (validSources.length === 0) {\n      message.error('请至少配置一个有效的数据源');\n      return false;\n    }\n\n    // 检查输出路径是否重复\n    const outputPaths = validSources.map(s => s.outputFolder);\n    const uniquePaths = new Set(outputPaths);\n    if (outputPaths.length !== uniquePaths.size) {\n      message.error('模型保存路径不能重复');\n      return false;\n    }\n\n    return true;\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 根据训练模式进行不同的验证\n    if (trainingMode === 'multi') {\n      if (!validateMultiTraining()) {\n        return;\n      }\n    } else {\n      // 单文件模式验证（保持原有逻辑）\n      if (dataSource === 'upload' && !uploadedFile) {\n        message.error('请上传CSV文件');\n        return;\n      }\n\n      if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n        message.error('请选择CSV文件');\n        return;\n      }\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        if (trainingMode === 'multi') {\n          // 多文件异步训练\n          const formData = new FormData();\n\n          // 添加文件\n          dataSources.forEach((source, index) => {\n            if (source.type === 'upload' && source.file) {\n              formData.append('files', source.file.originFileObj);\n            }\n          });\n\n          // 添加数据源配置\n          formData.append('data_sources', JSON.stringify(dataSources));\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交多文件异步任务\n          const taskId = await startMultiTrainingAsync(formData);\n\n          if (taskId) {\n            message.success(`多文件训练任务已启动（共${dataSources.length}个数据源），您可以继续使用其他功能，任务完成后会收到通知`);\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n\n          return; // 异步模式下直接返回\n        } else {\n          // 单文件异步训练（保持原有逻辑）\n          const formData = new FormData();\n\n          if (dataSource === 'upload') {\n            formData.append('file', uploadedFile.originFileObj);\n          } else {\n            formData.append('csv_dir', csvDir);\n            formData.append('selected_file', selectedFile);\n          }\n\n          formData.append('selected_prots', JSON.stringify(selectedProts));\n          formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n          formData.append('learning_rate', learningRate.toString());\n          formData.append('batch_size', batchSize.toString());\n          formData.append('epochs', epochs.toString());\n          formData.append('sequence_length', sequenceLength.toString());\n          formData.append('hidden_size', hiddenSize.toString());\n          formData.append('num_layers', numLayers.toString());\n          formData.append('dropout', dropout.toString());\n          formData.append('output_folder', outputFolder);\n          formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n          // 提交异步任务\n          const taskId = await submitTrainingTask(formData);\n\n          if (taskId) {\n            message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n            // 重置状态\n            setTraining(false);\n            setProgress(0);\n          }\n\n          return; // 异步模式下直接返回\n        }\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n        formData.append('auto_generate_template', autoGenerateTemplate.toString());\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder,\n          auto_generate_template: autoGenerateTemplate\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /home/<USER>\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={filesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card\n        title={\n          <Space>\n            <SettingOutlined />\n            <span>训练参数配置</span>\n          </Space>\n        }\n        className=\"function-card\"\n      >\n        <Row gutter={[24, 24]}>\n          {/* 基础参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <ExperimentOutlined />\n                  <Text strong>基础参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>学习率：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.0001)}\n                      min={0.0001}\n                      max={1}\n                      step={0.0001}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0001\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>批量大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 64)}\n                      min={1}\n                      max={512}\n                      style={{ width: '100%' }}\n                      placeholder=\"64\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>训练轮数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      min={1}\n                      max={1000}\n                      style={{ width: '100%' }}\n                      placeholder=\"100\"\n                    />\n                  </Col>\n                </Row>\n              </Space>\n            </Card>\n          </Col>\n\n          {/* 模型参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <SettingOutlined />\n                  <Text strong>模型参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>序列长度：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>隐藏层大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      min={10}\n                      max={512}\n                      step={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"50\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>层数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      min={1}\n                      max={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"2\"\n                    />\n                  </Col>\n                </Row>\n                <div style={{ width: '100%' }}>\n                  <Row align=\"middle\" style={{ marginBottom: 8 }}>\n                    <Col span={12}>\n                      <Text strong>Dropout 概率：</Text>\n                    </Col>\n                    <Col span={12} style={{ textAlign: 'right' }}>\n                      <Text code>{dropout}</Text>\n                    </Col>\n                  </Row>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    marks={{\n                      0: '0',\n                      0.2: '0.2',\n                      0.5: '0.5',\n                      0.9: '0.9'\n                    }}\n                  />\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 模型保存路径 */}\n        <Row style={{ marginTop: 24 }}>\n          <Col span={24}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <InboxOutlined />\n                  <Text strong>模型保存路径</Text>\n                </Space>\n              }\n            >\n              <Input\n                value={outputFolder}\n                onChange={(e) => setOutputFolder(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                size=\"large\"\n                prefix={<InboxOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 清洗模板生成选项 */}\n      <Card className=\"function-card\" title=\"清洗模板生成\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>自动生成清洗模板：</Text>\n            <div style={{ marginTop: 8 }}>\n              <Checkbox\n                checked={autoGenerateTemplate}\n                onChange={(e) => setAutoGenerateTemplate(e.target.checked)}\n              >\n                训练完成后自动生成清洗模板\n              </Checkbox>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                选择此选项后，系统将在模型训练完成后自动调用清洗模板生成功能，\n                根据训练结果中的阈值信息生成相应的清洗模板文件。\n              </Text>\n            </div>\n          </div>\n        </Space>\n      </Card>\n\n      {/* 训练模式选择 */}\n      <Card className=\"function-card\" title=\"训练模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={useAsyncTraining}\n              onChange={(e) => setUseAsyncTraining(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value={true}>\n                <Space>\n                  异步训练（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步训练\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待训练完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncTraining && (\n            <Alert\n              message=\"异步训练模式\"\n              description={\n                <div>\n                  训练任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {trainingResults.template_info && (\n                    <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>\n                      {trainingResults.template_info.template_generated ? (\n                        <div>\n                          <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>\n                          <p><strong>模板路径:</strong> {trainingResults.template_info.template_path}</p>\n                          <p><strong>更新阈值数量:</strong> {trainingResults.template_info.updated_thresholds}</p>\n                        </div>\n                      ) : (\n                        <div>\n                          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>\n                          <p><strong>错误信息:</strong> {trainingResults.template_info.error}</p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果 */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 异步训练结果展示 */}\n      {completedTrainingTasks.length > 0 && (\n        <Card title=\"异步训练结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步训练已完成\"\n            description=\"以下是后台训练任务的结果，您可以查看不同协议和数据类型的训练效果。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择训练任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的训练任务\"\n              >\n                {completedTrainingTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 模板生成信息显示 */}\n            {asyncTrainingResults && asyncTrainingResults.template_info && (\n              <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 4 }}>\n                {asyncTrainingResults.template_info.template_generated ? (\n                  <div>\n                    <p style={{ color: '#52c41a', fontWeight: 'bold' }}>✅ 清洗模板已自动生成</p>\n                    <p><strong>模板路径:</strong> {asyncTrainingResults.template_info.template_path}</p>\n                    <p><strong>更新阈值数量:</strong> {asyncTrainingResults.template_info.updated_thresholds}</p>\n                  </div>\n                ) : (\n                  <div>\n                    <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>❌ 清洗模板生成失败</p>\n                    <p><strong>错误信息:</strong> {asyncTrainingResults.template_info.error}</p>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* 协议和数据类型选择器 */}\n            {asyncTrainingResults && asyncTrainingResults.results && (\n              <div>\n                <Text strong>选择要查看的协议和数据类型：</Text>\n                <Select\n                  value={selectedAsyncResultKey}\n                  onChange={setSelectedAsyncResultKey}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"请选择协议和数据类型\"\n                >\n                  {Object.keys(asyncTrainingResults.results).map((key) => (\n                    <Option key={key} value={key}>\n                      {key}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n            )}\n\n            {/* 结果展示 */}\n            {selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedAsyncResultKey}\n                result={asyncTrainingResults.results[selectedAsyncResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,QAEC,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,kBAAkB,QAAsD,mBAAmB;AACxJ,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,uBAAuB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGhC,UAAU;AAClC,MAAM;EAAEiC;AAAQ,CAAC,GAAGrC,MAAM;AAC1B,MAAM;EAAEsC;AAAO,CAAC,GAAGpC,MAAM;AACzB,MAAM;EAAEqC;AAAQ,CAAC,GAAGtB,IAAI;;AAExB;;AAaA;AACA,MAAMuB,qBAAmE,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAA,IAAAC,oBAAA,EAAAC,kBAAA;EACrG,MAAM,CAACC,YAAY,EAAEC,gBAAgB,CAAC,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EAEhE,oBACEb,OAAA;IAAAc,QAAA,eACEd,OAAA,CAAC7B,KAAK;MAAC4C,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,gBACjEd,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAAY,QAAA,gBAACd,OAAA;YAAAc,QAAA,EAAQ;UAAG;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACX,YAAY;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDtB,OAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtB,OAAA,CAACE,IAAI;UAAAY,QAAA,gBAACd,OAAA;YAAAc,QAAA,EAAQ;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,gBAAgB;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENtB,OAAA,CAACpB,GAAG;QAAC2C,MAAM,EAAE,EAAG;QAAAT,QAAA,gBACdd,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACmB,WAAW,GAAG,GAAGnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,MAAMnB,MAAM,CAACmB,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,4CAAS;YACfC,KAAK,EAAElB,MAAM,CAACoB,UAAU,GAAG,GAAGpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,MAAMpB,MAAM,CAACoB,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,oBAAO;YACbC,KAAK,EAAElB,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAO;YACjGE,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEC,KAAK,EAAE1B,MAAM,CAACqB,QAAQ,GAAG,GAAG,IAAIrB,MAAM,CAACuB,EAAE,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM;YAC5FE,SAAS,EAAE;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA,CAACpB,GAAG;QAAC2C,MAAM,EAAE,EAAG;QAACN,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACxCd,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAAC6B,WAAW,GAAG,GAAG7B,MAAM,CAAC6B,WAAW,CAACP,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAM;YACxEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAElB,MAAM,CAAC8B,SAAS,GAAG,GAAG9B,MAAM,CAAC8B,SAAS,CAACR,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAM;YACtEG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAElB,MAAM,CAAC+B,aAAa,GAAG,GAAG/B,MAAM,CAAC+B,aAAa,CAACT,OAAO,CAAC,CAAC,CAAC,KAAK,GAAItB,MAAM,CAACgC,UAAU,GAAG,GAAGhC,MAAM,CAACgC,UAAU,CAACV,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,KAAO;YAC3IG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,CAAE;UAAAV,QAAA,eACXd,OAAA,CAAClB,SAAS;YACR2C,KAAK,EAAC,uBAAQ;YACdC,KAAK,EAAElB,MAAM,CAACiC,uBAAuB,GAAG,GAAGjC,MAAM,CAACiC,uBAAuB,CAACX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAItB,MAAM,CAACkC,eAAe,GAAG,GAAGlC,MAAM,CAACkC,eAAe,CAACZ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,KAAO;YACrKG,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELd,MAAM,CAACmC,YAAY,IAAInC,MAAM,CAACoC,UAAU,iBACvC5C,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAC2C,MAAM;UAAA/B,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BtB,OAAA;UAAKiB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCd,OAAA,CAACL,mBAAmB;YAACuB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cd,OAAA,CAACZ,SAAS;cACR2D,IAAI,EAAEvC,MAAM,CAACmC,YAAY,CAACK,GAAG,CAAC,CAACC,SAAiB,EAAEC,KAAa,MAAM;gBACnEC,KAAK,EAAED,KAAK,GAAG,CAAC;gBAChBE,IAAI,EAAEH,SAAS;gBACfI,IAAI,EAAE7C,MAAM,CAACoC,UAAU,CAACM,KAAK,CAAC,IAAI;cACpC,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDd,OAAA,CAACR,aAAa;gBAACmE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCtB,OAAA,CAACV,KAAK;gBAACsE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBtB,OAAA,CAACT,KAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTtB,OAAA,CAACP,OAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXtB,OAAA,CAACN,MAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,0BAAM;gBACdE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNtB,OAAA;UAAKiB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;YAAC2D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,4BACf,EAACN,MAAM,CAACmC,YAAY,CAACsB,MAAM,EAAC,kDAC1B,GAAAxD,oBAAA,GAACD,MAAM,CAACmC,YAAY,CAACnC,MAAM,CAACmC,YAAY,CAACsB,MAAM,GAAG,CAAC,CAAC,cAAAxD,oBAAA,uBAAnDA,oBAAA,CAAqDqB,OAAO,CAAC,CAAC,CAAC,EAAC,2CACjE,GAAApB,kBAAA,GAACF,MAAM,CAACoC,UAAU,CAACpC,MAAM,CAACoC,UAAU,CAACqB,MAAM,GAAG,CAAC,CAAC,cAAAvD,kBAAA,uBAA/CA,kBAAA,CAAiDoB,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAd,MAAM,CAAC0D,aAAa,IAAI1D,MAAM,CAAC2D,MAAM,IAAI3D,MAAM,CAAC0D,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvEjE,OAAA;QAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;UAAC2C,MAAM;UAAA/B,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCtB,OAAA;UAAKiB,KAAK,EAAE;YAAE6B,MAAM,EAAE,GAAG;YAAEV,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACxCd,OAAA,CAACL,mBAAmB;YAACuB,KAAK,EAAC,MAAM;YAAC4B,MAAM,EAAC,MAAM;YAAAhC,QAAA,eAC7Cd,OAAA,CAACZ,SAAS;cACR2D,IAAI,EAAEvC,MAAM,CAAC0D,aAAa,CAAClB,GAAG,CAAC,CAACoB,MAAc,EAAElB,KAAa,MAAM;gBACjEA,KAAK,EAAEA,KAAK,GAAG,CAAC;gBAChBmB,GAAG,EAAED,MAAM;gBACXE,GAAG,EAAE9D,MAAM,CAAC2D,MAAM,CAACjB,KAAK;cAC1B,CAAC,CAAC,CAAE;cACJI,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBAEnDd,OAAA,CAACR,aAAa;gBAACmE,eAAe,EAAC;cAAK;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCtB,OAAA,CAACV,KAAK;gBAACsE,OAAO,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBtB,OAAA,CAACT,KAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTtB,OAAA,CAACP,OAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXtB,OAAA,CAACN,MAAM;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFtB,OAAA,CAACX,IAAI;gBACHwE,IAAI,EAAC,UAAU;gBACfD,OAAO,EAAC,oBAAK;gBACbE,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAE,CAAE;gBACfC,GAAG,EAAE;cAAM;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNtB,OAAA;UAAKiB,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAE,CAAE;UAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;YAAC2D,IAAI,EAAC,WAAW;YAAA/C,QAAA,GAAC,2BAChB,EAACN,MAAM,CAAC0D,aAAa,CAACD,MAAM,EAAC,sFAC3B,EAACzD,MAAM,CAACqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAItB,MAAM,CAACuB,EAAE,GAAGvB,MAAM,CAACuB,EAAE,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAM,EAAC,+BAC5F,EAACtB,MAAM,CAAC2B,wBAAwB,GAAG3B,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAIAd,MAAM,CAAC+D,eAAe,iBACrBvE,OAAA,CAACrB,KAAK;QACJN,OAAO,EAAC,sCAAQ;QAChBmG,WAAW,eACTxE,OAAA;UAAAc,QAAA,gBACEd,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC+D,eAAe;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACvDd,MAAM,CAACiE,kBAAkB,iBACxBzE,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACiE,kBAAkB;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC7D,EACAd,MAAM,CAACkE,gBAAgB,iBACtB1E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACkE,gBAAgB;UAAA;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAACmE,cAAc,iBACpB3E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACmE,cAAc;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACzD,EACAd,MAAM,CAAC2B,wBAAwB,iBAC9BnC,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAAC2B,wBAAwB,CAACL,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5E,EACAd,MAAM,CAACoE,aAAa,iBACnB5E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACoE,aAAa;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACtD,EACAd,MAAM,CAACqE,gBAAgB,iBACtB7E,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACd,MAAM,CAACqE,gBAAgB,CAAC/C,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDuC,IAAI,EAAC,MAAM;QACXiB,QAAQ;MAAA;QAAA3D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACyD,EAAA,GAtMIzE,qBAAmE;AAwMzE,MAAM0E,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzH,QAAQ,CAAqB,QAAQ,CAAC;;EAE9E;EACA,MAAM,CAAC0H,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAAqB,OAAO,CAAC;EACzE,MAAM,CAAC4H,YAAY,EAAEC,eAAe,CAAC,GAAG7H,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC8H,MAAM,EAAEC,SAAS,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACkI,YAAY,EAAEC,eAAe,CAAC,GAAGnI,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACoI,YAAY,EAAEC,eAAe,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACsI,WAAW,EAAEC,cAAc,CAAC,GAAGvI,QAAQ,CAAuB,CACnE;IACEwI,EAAE,EAAE,GAAG;IACPrC,IAAI,EAAE,OAAO;IACbsC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,IAAI;IACbV,cAAc,EAAE,EAAE;IAClBI,YAAY,EAAE;EAChB,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAG5I,QAAQ,CAAW,CAAC,KAAK,CAAC,CAAC;EACrE,MAAM,CAAC6I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9I,QAAQ,CAA4B;IACpF+I,GAAG,EAAE,CAAC,aAAa;EACrB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjJ,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAACkJ,SAAS,EAAEC,YAAY,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoJ,MAAM,EAAEC,SAAS,CAAC,GAAGrJ,QAAQ,CAAC,GAAG,CAAC;EACzC,MAAM,CAACsJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGvJ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACwJ,UAAU,EAAEC,aAAa,CAAC,GAAGzJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0J,SAAS,EAAEC,YAAY,CAAC,GAAG3J,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4J,OAAO,EAAEC,UAAU,CAAC,GAAG7J,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACyI,YAAY,EAAEqB,eAAe,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAM,CAACiK,QAAQ,EAAEC,WAAW,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmK,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqK,eAAe,EAAEC,kBAAkB,CAAC,GAAGtK,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAACuK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxK,QAAQ,CAAS,EAAE,CAAC;;EAEtE;EACA,MAAM;IAAEyK,kBAAkB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAGxI,cAAc,CAAC,CAAC;EAC7F,MAAM,CAACyI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7K,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAM8K,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,SAA6B,GAAG;MACpCvC,EAAE,EAAEwC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB/E,IAAI,EAAE,OAAO;MACbsC,YAAY,EAAE,sBAAsBH,WAAW,CAAC/B,MAAM,GAAG,CAAC,EAAE;MAC5DmC,OAAO,EAAE,IAAI;MACbV,cAAc,EAAE,EAAE;MAClBI,YAAY,EAAE;IAChB,CAAC;IACDG,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEyC,SAAS,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMI,gBAAgB,GAAI3C,EAAU,IAAK;IACvCD,cAAc,CAACD,WAAW,CAAC8C,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7C,EAAE,KAAKA,EAAE,CAAC,CAAC;EAChE,CAAC;EAED,MAAM8C,gBAAgB,GAAGA,CAAC9C,EAAU,EAAE+C,OAAoC,KAAK;IAC7EhD,cAAc,CAACD,WAAW,CAAChD,GAAG,CAAC+F,MAAM,IACnCA,MAAM,CAAC7C,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAG6C,MAAM;MAAE,GAAGE;IAAQ,CAAC,GAAGF,MACjD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM,CAACG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzL,QAAQ,CAAM,IAAI,CAAC;EAC3E,MAAM,CAAC0L,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3L,QAAQ,CAAS,EAAE,CAAC;EAChF,MAAM,CAAC4L,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7L,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAM8L,sBAAsB,GAAGpB,uBAAuB,CAAC,UAAU,CAAC;;EAElE;EACA,MAAMqB,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,sBAAsB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IACjF,IAAIC,YAAY,IAAIA,YAAY,CAACnJ,MAAM,IAAImJ,YAAY,CAACnJ,MAAM,CAACuJ,OAAO,EAAE;MACtEZ,uBAAuB,CAACQ,YAAY,CAACnJ,MAAM,CAAC;MAC5C6I,yBAAyB,CAACW,MAAM,CAACC,IAAI,CAACN,YAAY,CAACnJ,MAAM,CAACuJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACApM,SAAS,CAAC,MAAM;IACd0K,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA1K,SAAS,CAAC,MAAM;IACd,IAAI6L,sBAAsB,CAACvF,MAAM,GAAG,CAAC,IAAI,CAACqF,mBAAmB,EAAE;MAC7D,MAAMY,UAAU,GAAGV,sBAAsB,CAACA,sBAAsB,CAACvF,MAAM,GAAG,CAAC,CAAC;MAC5EwF,qBAAqB,CAACS,UAAU,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,sBAAsB,EAAEF,mBAAmB,CAAC,CAAC;;EAEjD;EACA,MAAMa,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;EAC9C,MAAMC,eAAe,GAAG;IACtB3D,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IACjE4D,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;IACnCC,IAAI,EAAE,CAAC,KAAK;EACd,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC/E,MAAM,EAAE;IAEbO,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMyE,QAAQ,GAAG,MAAM5K,gBAAgB,CAAC6K,YAAY,CAACjF,MAAM,CAAC;MAC5DG,iBAAiB,CAAC6E,QAAQ,CAACzH,IAAI,CAAC2H,KAAK,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBxM,OAAO,CAACsM,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB7H,IAAI,cAAA8H,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,UAAU,CAAC;MACzDnF,iBAAiB,CAAC,EAAE,CAAC;IACvB,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgF,sBAAsB,GAAG,MAAAA,CAAOC,QAAgB,EAAExF,MAAc,KAAK;IACzE,IAAI,CAACA,MAAM,EAAE;IAEbwD,gBAAgB,CAACgC,QAAQ,EAAE;MAAElF,YAAY,EAAE;IAAK,CAAC,CAAC;IAClD,IAAI;MACF,MAAM0E,QAAQ,GAAG,MAAM5K,gBAAgB,CAAC6K,YAAY,CAACjF,MAAM,CAAC;MAC5DwD,gBAAgB,CAACgC,QAAQ,EAAE;QACzBtF,cAAc,EAAE8E,QAAQ,CAACzH,IAAI,CAAC2H,KAAK,IAAI,EAAE;QACzC5E,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO6E,KAAU,EAAE;MAAA,IAAAM,gBAAA,EAAAC,qBAAA;MACnB7M,OAAO,CAACsM,KAAK,CAAC,EAAAM,gBAAA,GAAAN,KAAK,CAACH,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlI,IAAI,cAAAmI,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,UAAU,CAAC;MACzD9B,gBAAgB,CAACgC,QAAQ,EAAE;QACzBtF,cAAc,EAAE,EAAE;QAClBI,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAnI,SAAS,CAAC,MAAM;IACd,IAAIyH,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAACvB,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAMkH,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7Bb,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMc,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC/F,UAAU,EAAEI,MAAM,CAAC,CAAC;;EAExB;EACA,MAAM8F,WAAW,GAAG;IAClBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAAC5H,MAAM,GAAG,CAAC,EAAE;QAC5BsB,eAAe,CAACqG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLtG,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMuG,uBAAuB,GAAId,QAAgB,KAAM;IACrDO,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAAC5H,MAAM,GAAG,CAAC,EAAE;QAC5B+E,gBAAgB,CAACgC,QAAQ,EAAE;UAAEe,IAAI,EAAEH,IAAI,CAACC,QAAQ,CAAC,CAAC;QAAE,CAAC,CAAC;MACxD,CAAC,MAAM;QACL7C,gBAAgB,CAACgC,QAAQ,EAAE;UAAEe,IAAI,EAAEC;QAAU,CAAC,CAAC;MACjD;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAIC,KAAe,IAAK;IAChD5F,gBAAgB,CAAC4F,KAAK,CAAC;IACvB;IACA,MAAMC,YAAY,GAAG;MAAE,GAAG5F;IAAkB,CAAC;IAC7C2F,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpB,IAAI,CAACF,YAAY,CAACE,IAAI,CAAC,IAAIjC,eAAe,CAACiC,IAAI,CAAiC,EAAE;QAChFF,YAAY,CAACE,IAAI,CAAC,GAAG,CAACjC,eAAe,CAACiC,IAAI,CAAiC,CAAC,CAAC,CAAC,CAAC;MACjF;IACF,CAAC,CAAC;IACF;IACArC,MAAM,CAACC,IAAI,CAACkC,YAAY,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACxC,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACD,IAAI,CAAC,EAAE;QACzB,OAAOF,YAAY,CAACE,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;IACF7F,oBAAoB,CAAC2F,YAAY,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAACC,QAAgB,EAAEC,SAAmB,KAAK;IACtEjG,oBAAoB,CAACkG,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACF,QAAQ,GAAGC;IACd,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAe;IAC3C;IACA,MAAMC,YAAY,GAAG5G,WAAW,CAAC8C,MAAM,CAACC,MAAM,IAAI;MAChD,IAAIA,MAAM,CAAClF,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOkF,MAAM,CAACgD,IAAI,IAAIhD,MAAM,CAAC5C,YAAY;MAC3C,CAAC,MAAM;QACL,OAAO4C,MAAM,CAACvD,MAAM,IAAIuD,MAAM,CAACnD,YAAY,IAAImD,MAAM,CAAC5C,YAAY;MACpE;IACF,CAAC,CAAC;IAEF,IAAIyG,YAAY,CAAC3I,MAAM,KAAK,CAAC,EAAE;MAC7B5F,OAAO,CAACsM,KAAK,CAAC,eAAe,CAAC;MAC9B,OAAO,KAAK;IACd;;IAEA;IACA,MAAMkC,WAAW,GAAGD,YAAY,CAAC5J,GAAG,CAAC8J,CAAC,IAAIA,CAAC,CAAC3G,YAAY,CAAC;IACzD,MAAM4G,WAAW,GAAG,IAAIC,GAAG,CAACH,WAAW,CAAC;IACxC,IAAIA,WAAW,CAAC5I,MAAM,KAAK8I,WAAW,CAAC/L,IAAI,EAAE;MAC3C3C,OAAO,CAACsM,KAAK,CAAC,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMsC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC;IACA,IAAI/H,YAAY,KAAK,OAAO,EAAE;MAC5B,IAAI,CAACyH,qBAAqB,CAAC,CAAC,EAAE;QAC5B;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAIvH,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;QAC5CjH,OAAO,CAACsM,KAAK,CAAC,UAAU,CAAC;QACzB;MACF;MAEA,IAAIvF,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,YAAY,CAAC,EAAE;QACxDvH,OAAO,CAACsM,KAAK,CAAC,UAAU,CAAC;QACzB;MACF;IACF;IAEA,IAAItE,aAAa,CAACpC,MAAM,KAAK,CAAC,EAAE;MAC9B5F,OAAO,CAACsM,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,MAAMuC,iBAAiB,GAAG7G,aAAa,CAAC8G,IAAI,CAACd,IAAI,IAC/C9F,iBAAiB,CAAC8F,IAAI,CAAC,IAAI9F,iBAAiB,CAAC8F,IAAI,CAAC,CAACpI,MAAM,GAAG,CAC9D,CAAC;IAED,IAAI,CAACiJ,iBAAiB,EAAE;MACtB7O,OAAO,CAACsM,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF;IAEA/C,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,CAAC,CAAC;IACdE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;IAExB,IAAI;MACF,IAAII,gBAAgB,EAAE;QACpB;QACA,IAAIpD,YAAY,KAAK,OAAO,EAAE;UAC5B;UACA,MAAMkI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;UAE/B;UACArH,WAAW,CAACoG,OAAO,CAAC,CAACrD,MAAM,EAAE7F,KAAK,KAAK;YACrC,IAAI6F,MAAM,CAAClF,IAAI,KAAK,QAAQ,IAAIkF,MAAM,CAACgD,IAAI,EAAE;cAC3CqB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEvE,MAAM,CAACgD,IAAI,CAACwB,aAAa,CAAC;YACrD;UACF,CAAC,CAAC;;UAEF;UACAH,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEE,IAAI,CAACC,SAAS,CAACzH,WAAW,CAAC,CAAC;UAC5DoH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACpH,aAAa,CAAC,CAAC;UAChE+G,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAAClH,iBAAiB,CAAC,CAAC;UACxE6G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE5G,YAAY,CAACkC,QAAQ,CAAC,CAAC,CAAC;UACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1G,SAAS,CAACgC,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAExG,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;UAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEtG,cAAc,CAAC4B,QAAQ,CAAC,CAAC,CAAC;UAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEpG,UAAU,CAAC0B,QAAQ,CAAC,CAAC,CAAC;UACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAElG,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhG,OAAO,CAACsB,QAAQ,CAAC,CAAC,CAAC;UAC9CwE,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE7F,oBAAoB,CAACmB,QAAQ,CAAC,CAAC,CAAC;;UAE1E;UACA,MAAMc,MAAM,GAAG,MAAM5J,uBAAuB,CAACsN,QAAQ,CAAC;UAEtD,IAAI1D,MAAM,EAAE;YACVrL,OAAO,CAACqP,OAAO,CAAC,eAAe1H,WAAW,CAAC/B,MAAM,8BAA8B,CAAC;YAChF;YACA2D,WAAW,CAAC,KAAK,CAAC;YAClBE,WAAW,CAAC,CAAC,CAAC;UAChB;UAEA,OAAO,CAAC;QACV,CAAC,MAAM;UACL;UACA,MAAMsF,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAE/B,IAAIjI,UAAU,KAAK,QAAQ,EAAE;YAC3BgI,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhI,YAAY,CAACiI,aAAa,CAAC;UACrD,CAAC,MAAM;YACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE9H,MAAM,CAAC;YAClC4H,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE1H,YAAY,CAAC;UAChD;UAEAwH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACpH,aAAa,CAAC,CAAC;UAChE+G,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAAClH,iBAAiB,CAAC,CAAC;UACxE6G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE5G,YAAY,CAACkC,QAAQ,CAAC,CAAC,CAAC;UACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1G,SAAS,CAACgC,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAExG,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;UAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEtG,cAAc,CAAC4B,QAAQ,CAAC,CAAC,CAAC;UAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEpG,UAAU,CAAC0B,QAAQ,CAAC,CAAC,CAAC;UACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAElG,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAAC;UACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhG,OAAO,CAACsB,QAAQ,CAAC,CAAC,CAAC;UAC9CwE,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnH,YAAY,CAAC;UAC9CiH,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE7F,oBAAoB,CAACmB,QAAQ,CAAC,CAAC,CAAC;;UAE1E;UACA,MAAMc,MAAM,GAAG,MAAMvB,kBAAkB,CAACiF,QAAQ,CAAC;UAEjD,IAAI1D,MAAM,EAAE;YACVrL,OAAO,CAACqP,OAAO,CAAC,gCAAgC,CAAC;YACjD;YACA9F,WAAW,CAAC,KAAK,CAAC;YAClBE,WAAW,CAAC,CAAC,CAAC;UAChB;UAEA,OAAO,CAAC;QACV;MACF;;MAEA;MACA,IAAI0C,QAAQ;MAEZ,IAAIpF,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMgI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhI,YAAY,CAACiI,aAAa,CAAC;QACnDH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEE,IAAI,CAACC,SAAS,CAACpH,aAAa,CAAC,CAAC;QAChE+G,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEE,IAAI,CAACC,SAAS,CAAClH,iBAAiB,CAAC,CAAC;QACxE6G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE5G,YAAY,CAACkC,QAAQ,CAAC,CAAC,CAAC;QACzDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1G,SAAS,CAACgC,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAExG,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;QAC5CwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEtG,cAAc,CAAC4B,QAAQ,CAAC,CAAC,CAAC;QAC7DwE,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEpG,UAAU,CAAC0B,QAAQ,CAAC,CAAC,CAAC;QACrDwE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAElG,SAAS,CAACwB,QAAQ,CAAC,CAAC,CAAC;QACnDwE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhG,OAAO,CAACsB,QAAQ,CAAC,CAAC,CAAC;QAC9CwE,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnH,YAAY,CAAC;QAC9CiH,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAE7F,oBAAoB,CAACmB,QAAQ,CAAC,CAAC,CAAC;;QAE1E;QACA,MAAM+E,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC9F,WAAW,CAAE4E,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdmB,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOjB,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERlC,QAAQ,GAAG,MAAM5K,gBAAgB,CAACkO,UAAU,CAACV,QAAQ,CAAC;QACtDS,aAAa,CAACF,gBAAgB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMI,iBAAiB,GAAG;UACxBC,OAAO,EAAExI,MAAM;UACfyI,aAAa,EAAErI,YAAY;UAC3BsI,cAAc,EAAE7H,aAAa;UAC7B8H,kBAAkB,EAAE5H,iBAAiB;UACrC6H,aAAa,EAAE1H,YAAY;UAC3B2H,UAAU,EAAEzH,SAAS;UACrBE,MAAM,EAAEA,MAAM;UACdwH,eAAe,EAAEtH,cAAc;UAC/BuH,WAAW,EAAErH,UAAU;UACvBsH,UAAU,EAAEpH,SAAS;UACrBE,OAAO,EAAEA,OAAO;UAChBmH,aAAa,EAAEtI,YAAY;UAC3BuI,sBAAsB,EAAEjH;QAC1B,CAAC;;QAED;QACA,MAAMkG,gBAAgB,GAAGC,WAAW,CAAC,MAAM;UACzC9F,WAAW,CAAE4E,IAAI,IAAK;YACpB,IAAIA,IAAI,IAAI,EAAE,EAAE;cACdmB,aAAa,CAACF,gBAAgB,CAAC;cAC/B,OAAOjB,IAAI;YACb;YACA,OAAOA,IAAI,GAAG,CAAC;UACjB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;QAERlC,QAAQ,GAAG,MAAM5K,gBAAgB,CAAC+O,eAAe,CAACZ,iBAAiB,CAAC;QACpEF,aAAa,CAACF,gBAAgB,CAAC;MACjC;MAEA7F,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACA8G,OAAO,CAACC,GAAG,CAAC,SAAS,EAAErE,QAAQ,CAACzH,IAAI,CAAC;MAErCiF,kBAAkB,CAACwC,QAAQ,CAACzH,IAAI,CAAC;;MAEjC;MACA,IAAIyH,QAAQ,CAACzH,IAAI,CAACgH,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACO,QAAQ,CAACzH,IAAI,CAACgH,OAAO,CAAC,CAAC9F,MAAM,GAAG,CAAC,EAAE;QAC1EiE,oBAAoB,CAAC8B,MAAM,CAACC,IAAI,CAACO,QAAQ,CAACzH,IAAI,CAACgH,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;MAEA1L,OAAO,CAACqP,OAAO,CAAC,SAAS,CAAC;;MAE1B;MACA,IAAIlD,QAAQ,CAACzH,IAAI,CAAC+L,WAAW,EAAE;QAC7BzQ,OAAO,CAACuN,IAAI,CAAC,WAAWpB,QAAQ,CAACzH,IAAI,CAAC+L,WAAW,EAAE,CAAC;MACtD;IAEF,CAAC,CAAC,OAAOnE,KAAU,EAAE;MACnBiE,OAAO,CAACjE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BiE,OAAO,CAACjE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACH,QAAQ,CAAC;;MAEtC;MACA,IAAIuE,YAAY,GAAG,QAAQ;MAC3B,IAAIpE,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAwE,qBAAA,EAAAC,qBAAA;QAClB,KAAAD,qBAAA,GAAIrE,KAAK,CAACH,QAAQ,CAACzH,IAAI,cAAAiM,qBAAA,eAAnBA,qBAAA,CAAqBlE,MAAM,EAAE;UAC/BiE,YAAY,GAAGpE,KAAK,CAACH,QAAQ,CAACzH,IAAI,CAAC+H,MAAM;QAC3C,CAAC,MAAM,KAAAmE,qBAAA,GAAItE,KAAK,CAACH,QAAQ,CAACzH,IAAI,cAAAkM,qBAAA,eAAnBA,qBAAA,CAAqB5Q,OAAO,EAAE;UACvC0Q,YAAY,GAAGpE,KAAK,CAACH,QAAQ,CAACzH,IAAI,CAAC1E,OAAO;QAC5C,CAAC,MAAM,IAAIsM,KAAK,CAACH,QAAQ,CAAC0E,UAAU,EAAE;UACpCH,YAAY,GAAG,SAASpE,KAAK,CAACH,QAAQ,CAAC2E,MAAM,IAAIxE,KAAK,CAACH,QAAQ,CAAC0E,UAAU,EAAE;QAC9E;MACF,CAAC,MAAM,IAAIvE,KAAK,CAACtM,OAAO,EAAE;QACxB0Q,YAAY,GAAGpE,KAAK,CAACtM,OAAO;MAC9B;MAEAA,OAAO,CAACsM,KAAK,CAACoE,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRnH,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMwH,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIhK,UAAU,KAAK,QAAQ,EAAE;MAC3B,OAAOE,YAAY,IAAIe,aAAa,CAACpC,MAAM,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,OAAOuB,MAAM,IAAII,YAAY,IAAIS,aAAa,CAACpC,MAAM,GAAG,CAAC;IAC3D;EACF,CAAC;EAED,oBACEjE,OAAA;IAAAc,QAAA,gBACEd,OAAA,CAACC,KAAK;MAACoP,KAAK,EAAE,CAAE;MAACpO,KAAK,EAAE;QAAEqO,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAA1O,QAAA,EAAC;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACrGtB,OAAA,CAACE,IAAI;MAAC2D,IAAI,EAAC,WAAW;MAAA/C,QAAA,EAAC;IAEvB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPtB,OAAA,CAAC5B,OAAO;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXtB,OAAA,CAACpC,IAAI;MAAC6D,KAAK,EAAC,oBAAK;MAACgO,SAAS,EAAC,eAAe;MAAA3O,QAAA,eACzCd,OAAA,CAAC7B,KAAK;QAAC4C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BtB,OAAA,CAACnC,KAAK,CAAC6R,KAAK;YACVhO,KAAK,EAAE0D,UAAW;YAClBuG,QAAQ,EAAGgE,CAAC,IAAKtK,aAAa,CAACsK,CAAC,CAACC,MAAM,CAAClO,KAAK,CAAE;YAC/CT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBd,OAAA,CAACnC,KAAK;cAAC6D,KAAK,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCtB,OAAA,CAACnC,KAAK;cAAC6D,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL8D,UAAU,KAAK,OAAO,iBACrBpF,OAAA,CAAC7B,KAAK;UAAC4C,SAAS,EAAC,UAAU;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBACnDd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BtB,OAAA,CAACjC,KAAK,CAAC2R,KAAK;cAACG,OAAO;cAAC5O,KAAK,EAAE;gBAAEmB,SAAS,EAAE,CAAC;gBAAE0N,OAAO,EAAE;cAAO,CAAE;cAAAhP,QAAA,gBAC5Dd,OAAA,CAACjC,KAAK;gBACJ2D,KAAK,EAAE8D,MAAO;gBACdmG,QAAQ,EAAGgE,CAAC,IAAKlK,SAAS,CAACkK,CAAC,CAACC,MAAM,CAAClO,KAAK,CAAE;gBAC3CqO,WAAW,EAAC,4BAAkB;gBAC9B9O,KAAK,EAAE;kBAAE+O,IAAI,EAAE;gBAAE;cAAE;gBAAA7O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFtB,OAAA,CAAC/B,MAAM;gBACL4F,IAAI,EAAC,SAAS;gBACdoM,OAAO,EAAE1F,aAAc;gBACvB2F,OAAO,EAAEpK,YAAa;gBACtBqK,QAAQ,EAAE,CAAC3K,MAAO;gBAClBvE,KAAK,EAAE;kBAAEmP,UAAU,EAAE;gBAAE,CAAE;gBAAAtP,QAAA,EAC1B;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENtB,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBtB,OAAA,CAAC1B,IAAI;cAAC+R,QAAQ,EAAEvK,YAAa;cAAAhF,QAAA,eAC3Bd,OAAA,CAAChC,MAAM;gBACL0D,KAAK,EAAEkE,YAAa;gBACpB+F,QAAQ,EAAE9F,eAAgB;gBAC1BkK,WAAW,EAAC,mCAAU;gBACtB9O,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEkB,SAAS,EAAE;gBAAE,CAAE;gBACvC8N,OAAO,EAAEpK,YAAa;gBAAAhF,QAAA,EAErB4E,cAAc,CAAC1C,GAAG,CAAE+I,IAAI,iBACvB/L,OAAA,CAACI,MAAM;kBAAYsB,KAAK,EAAEqK,IAAK;kBAAAjL,QAAA,EAC5BiL;gBAAI,GADMA,IAAI;kBAAA5K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGA8D,UAAU,KAAK,QAAQ,iBACtBpF,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBtB,OAAA,CAACG,OAAO;YAAA,GAAKmL,WAAW;YAAErK,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAChDd,OAAA;cAAGyP,SAAS,EAAC,sBAAsB;cAAA3O,QAAA,eACjCd,OAAA,CAAChB,aAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJtB,OAAA;cAAGyP,SAAS,EAAC,iBAAiB;cAAA3O,QAAA,EAAC;YAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDtB,OAAA;cAAGyP,SAAS,EAAC,iBAAiB;cAAA3O,QAAA,EAAC;YAE/B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACpC,IAAI;MAAC6D,KAAK,EAAC,wDAAW;MAACgO,SAAS,EAAC,eAAe;MAAA3O,QAAA,eAC/Cd,OAAA,CAAC7B,KAAK;QAAC4C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBtB,OAAA,CAAChC,MAAM;YACLsS,IAAI,EAAC,UAAU;YACf5O,KAAK,EAAE2E,aAAc;YACrBsF,QAAQ,EAAEM,oBAAqB;YAC/B8D,WAAW,EAAC,gCAAO;YACnB9O,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEkB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEtCqJ,eAAe,CAACnH,GAAG,CAAEqJ,IAAI,iBACxBrM,OAAA,CAACI,MAAM;cAAYsB,KAAK,EAAE2K,IAAK;cAAAvL,QAAA,EAC5BuL;YAAI,GADMA,IAAI;cAAAlL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAET,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL+E,aAAa,CAACrD,GAAG,CAAEqJ,IAAI,iBACtBrM,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,GAAEuL,IAAI,EAAC,iCAAM;UAAA;YAAAlL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCtB,OAAA,CAACvB,QAAQ,CAACiR,KAAK;YACbhO,KAAK,EAAE6E,iBAAiB,CAAC8F,IAAI,CAAC,IAAI,EAAG;YACrCV,QAAQ,EAAGc,SAAS,IAAKF,oBAAoB,CAACF,IAAI,EAAEI,SAAqB,CAAE;YAC3ExL,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,EAEvB,CAACsJ,eAAe,CAACiC,IAAI,CAAiC,IAAI,EAAE,EAAErJ,GAAG,CAAEuN,QAAQ,iBAC1EvQ,OAAA,CAACvB,QAAQ;cAAgBiD,KAAK,EAAE6O,QAAS;cAAAzP,QAAA,EACtCyP;YAAQ,GADIA,QAAQ;cAAApP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC;QAAA,GAZT+K,IAAI;UAAAlL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaT,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACpC,IAAI;MACH6D,KAAK,eACHzB,OAAA,CAAC7B,KAAK;QAAA2C,QAAA,gBACJd,OAAA,CAACd,eAAe;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnBtB,OAAA;UAAAc,QAAA,EAAM;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACR;MACDmO,SAAS,EAAC,eAAe;MAAA3O,QAAA,gBAEzBd,OAAA,CAACpB,GAAG;QAAC2C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAT,QAAA,gBAEpBd,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACpC,IAAI;YACHoD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAC7B,KAAK;cAAA2C,QAAA,gBACJd,OAAA,CAACb,kBAAkB;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAAC7B,KAAK;cAAC4C,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEd,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAEgF,YAAa;oBACpBiF,QAAQ,EAAGjK,KAAK,IAAKiF,eAAe,CAACjF,KAAK,IAAI,MAAM,CAAE;oBACtD+O,GAAG,EAAE,MAAO;oBACZC,GAAG,EAAE,CAAE;oBACPC,IAAI,EAAE,MAAO;oBACb1P,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAQ;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAEkF,SAAU;oBACjB+E,QAAQ,EAAGjK,KAAK,IAAKmF,YAAY,CAACnF,KAAK,IAAI,EAAE,CAAE;oBAC/C+O,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACTzP,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAI;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAEoF,MAAO;oBACd6E,QAAQ,EAAGjK,KAAK,IAAKqF,SAAS,CAACrF,KAAK,IAAI,GAAG,CAAE;oBAC7C+O,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,IAAK;oBACVzP,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAK;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNtB,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACpC,IAAI;YACHoD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAC7B,KAAK;cAAA2C,QAAA,gBACJd,OAAA,CAACd,eAAe;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAAC7B,KAAK;cAAC4C,SAAS,EAAC,UAAU;cAACC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAJ,QAAA,gBACjEd,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAEsF,cAAe;oBACtB2E,QAAQ,EAAGjK,KAAK,IAAKuF,iBAAiB,CAACvF,KAAK,IAAI,EAAE,CAAE;oBACpD+O,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,GAAI;oBACTzP,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAI;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAEwF,UAAW;oBAClByE,QAAQ,EAAGjK,KAAK,IAAKyF,aAAa,CAACzF,KAAK,IAAI,EAAE,CAAE;oBAChD+O,GAAG,EAAE,EAAG;oBACRC,GAAG,EAAE,GAAI;oBACTC,IAAI,EAAE,EAAG;oBACT1P,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAI;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA,CAACpB,GAAG;gBAAC4R,KAAK,EAAC,QAAQ;gBAAA1P,QAAA,gBACjBd,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;oBAAC2C,MAAM;oBAAA/B,QAAA,EAAC;kBAAG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACNtB,OAAA,CAACnB,GAAG;kBAAC2C,IAAI,EAAE,EAAG;kBAAAV,QAAA,eACZd,OAAA,CAACzB,WAAW;oBACVmD,KAAK,EAAE0F,SAAU;oBACjBuE,QAAQ,EAAGjK,KAAK,IAAK2F,YAAY,CAAC3F,KAAK,IAAI,CAAC,CAAE;oBAC9C+O,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,EAAG;oBACRzP,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzB6O,WAAW,EAAC;kBAAG;oBAAA5O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtB,OAAA;gBAAKiB,KAAK,EAAE;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,gBAC5Bd,OAAA,CAACpB,GAAG;kBAAC4R,KAAK,EAAC,QAAQ;kBAACvP,KAAK,EAAE;oBAAEuO,YAAY,EAAE;kBAAE,CAAE;kBAAA1O,QAAA,gBAC7Cd,OAAA,CAACnB,GAAG;oBAAC2C,IAAI,EAAE,EAAG;oBAAAV,QAAA,eACZd,OAAA,CAACE,IAAI;sBAAC2C,MAAM;sBAAA/B,QAAA,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtB,OAAA,CAACnB,GAAG;oBAAC2C,IAAI,EAAE,EAAG;oBAACP,KAAK,EAAE;sBAAE2P,SAAS,EAAE;oBAAQ,CAAE;oBAAA9P,QAAA,eAC3Cd,OAAA,CAACE,IAAI;sBAAC2Q,IAAI;sBAAA/P,QAAA,EAAEwG;oBAAO;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtB,OAAA,CAACxB,MAAM;kBACLkD,KAAK,EAAE4F,OAAQ;kBACfqE,QAAQ,EAAEpE,UAAW;kBACrBkJ,GAAG,EAAE,CAAE;kBACPC,GAAG,EAAE,GAAI;kBACTC,IAAI,EAAE,IAAK;kBACXG,KAAK,EAAE;oBACL,CAAC,EAAE,GAAG;oBACN,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE;kBACP;gBAAE;kBAAA3P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA,CAACpB,GAAG;QAACqC,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bd,OAAA,CAACnB,GAAG;UAAC2C,IAAI,EAAE,EAAG;UAAAV,QAAA,eACZd,OAAA,CAACpC,IAAI;YACHoD,IAAI,EAAC,OAAO;YACZS,KAAK,eACHzB,OAAA,CAAC7B,KAAK;cAAA2C,QAAA,gBACJd,OAAA,CAAChB,aAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBtB,OAAA,CAACE,IAAI;gBAAC2C,MAAM;gBAAA/B,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACR;YAAAR,QAAA,eAEDd,OAAA,CAACjC,KAAK;cACJ2D,KAAK,EAAEyE,YAAa;cACpBwF,QAAQ,EAAGgE,CAAC,IAAKnI,eAAe,CAACmI,CAAC,CAACC,MAAM,CAAClO,KAAK,CAAE;cACjDqO,WAAW,EAAC,4BAAkB;cAC9B/O,IAAI,EAAC,OAAO;cACZ+P,MAAM,eAAE/Q,OAAA,CAAChB,aAAa;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPtB,OAAA,CAACpC,IAAI;MAAC6R,SAAS,EAAC,eAAe;MAAChO,KAAK,EAAC,sCAAQ;MAAAX,QAAA,eAC5Cd,OAAA,CAAC7B,KAAK;QAAC4C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,eACjEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BtB,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,eAC3Bd,OAAA,CAACvB,QAAQ;cACPuS,OAAO,EAAEvJ,oBAAqB;cAC9BkE,QAAQ,EAAGgE,CAAC,IAAKjI,uBAAuB,CAACiI,CAAC,CAACC,MAAM,CAACoB,OAAO,CAAE;cAAAlQ,QAAA,EAC5D;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNtB,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,eAC3Bd,OAAA,CAACE,IAAI;cAAC2D,IAAI,EAAC,WAAW;cAAC5C,KAAK,EAAE;gBAAEqO,QAAQ,EAAE;cAAO,CAAE;cAAAxO,QAAA,EAAC;YAGpD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACpC,IAAI;MAAC6R,SAAS,EAAC,eAAe;MAAChO,KAAK,EAAC,0BAAM;MAAAX,QAAA,eAC1Cd,OAAA,CAAC7B,KAAK;QAAC4C,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBACjEd,OAAA;UAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;YAAC2C,MAAM;YAAA/B,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BtB,OAAA,CAACnC,KAAK,CAAC6R,KAAK;YACVhO,KAAK,EAAE4G,gBAAiB;YACxBqD,QAAQ,EAAGgE,CAAC,IAAKpH,mBAAmB,CAACoH,CAAC,CAACC,MAAM,CAAClO,KAAK,CAAE;YACrDT,KAAK,EAAE;cAAEmB,SAAS,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBAExBd,OAAA,CAACnC,KAAK;cAAC6D,KAAK,EAAE,IAAK;cAAAZ,QAAA,eACjBd,OAAA,CAAC7B,KAAK;gBAAA2C,QAAA,GAAC,kDAEL,eAAAd,OAAA,CAACE,IAAI;kBAAC2D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqO,QAAQ,EAAE;kBAAG,CAAE;kBAAAxO,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRtB,OAAA,CAACnC,KAAK;cAAC6D,KAAK,EAAE,KAAM;cAAAZ,QAAA,eAClBd,OAAA,CAAC7B,KAAK;gBAAA2C,QAAA,GAAC,0BAEL,eAAAd,OAAA,CAACE,IAAI;kBAAC2D,IAAI,EAAC,WAAW;kBAAC5C,KAAK,EAAE;oBAAEqO,QAAQ,EAAE;kBAAG,CAAE;kBAAAxO,QAAA,EAAC;gBAEhD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELgH,gBAAgB,iBACftI,OAAA,CAACrB,KAAK;UACJN,OAAO,EAAC,sCAAQ;UAChBmG,WAAW,eACTxE,OAAA;YAAAc,QAAA,GAAK,8JAEH,eAAAd,OAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAtB,OAAA;cAAAc,QAAA,EAAQ;YAAY;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDuC,IAAI,EAAC,MAAM;UACXiB,QAAQ;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPtB,OAAA,CAACpC,IAAI;MAAC6R,SAAS,EAAC,eAAe;MAAA3O,QAAA,gBAC7Bd,OAAA,CAAC/B,MAAM;QACL4F,IAAI,EAAC,SAAS;QACd7C,IAAI,EAAC,OAAO;QACZiQ,IAAI,eAAEjR,OAAA,CAACf,kBAAkB;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7B2O,OAAO,EAAEhD,mBAAoB;QAC7BiD,OAAO,EAAEvI,QAAS;QAClBwI,QAAQ,EAAE,CAACf,WAAW,CAAC,CAAE;QACzBK,SAAS,EAAC,eAAe;QAAA3O,QAAA,EAExB6G,QAAQ,GAAG,SAAS,GAAG;MAAQ;QAAAxG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAGRqG,QAAQ,iBACP3H,OAAA;QAAKyP,SAAS,EAAC,kBAAkB;QAAA3O,QAAA,gBAC/Bd,OAAA,CAACE,IAAI;UAAAY,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBtB,OAAA,CAACtB,QAAQ;UAACwS,OAAO,EAAErJ,QAAS;UAACsH,MAAM,EAAC;QAAQ;UAAAhO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN,EAGAyG,eAAe,IAAIA,eAAe,CAACgC,OAAO,iBACzC/J,OAAA;QAAKiB,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC5Bd,OAAA,CAACrB,KAAK;UACJN,OAAO,EAAC,0BAAM;UACdmG,WAAW,eACTxE,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAG;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACfyG,eAAe,CAAC+G,WAAW,iBAC1B9O,OAAA;cAAAc,QAAA,gBAAGd,OAAA;gBAAAc,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAAC+G,WAAW;YAAA;cAAA3N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7D,EACAyG,eAAe,CAACoJ,aAAa,iBAC5BnR,OAAA;cAAKiB,KAAK,EAAE;gBAAEmB,SAAS,EAAE,CAAC;gBAAEgP,OAAO,EAAE,CAAC;gBAAEC,eAAe,EAAE,SAAS;gBAAEC,MAAM,EAAE,mBAAmB;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAzQ,QAAA,EAChHiH,eAAe,CAACoJ,aAAa,CAACK,kBAAkB,gBAC/CxR,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAGiB,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAEqN,UAAU,EAAE;kBAAO,CAAE;kBAAAzO,QAAA,EAAC;gBAAW;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAACoJ,aAAa,CAACM,aAAa;gBAAA;kBAAAtQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3EtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAACoJ,aAAa,CAACO,kBAAkB;gBAAA;kBAAAvQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,gBAENtB,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAGiB,KAAK,EAAE;oBAAEiB,KAAK,EAAE,SAAS;oBAAEqN,UAAU,EAAE;kBAAO,CAAE;kBAAAzO,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtB,OAAA;kBAAAc,QAAA,gBAAGd,OAAA;oBAAAc,QAAA,EAAQ;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACyG,eAAe,CAACoJ,aAAa,CAACxG,KAAK;gBAAA;kBAAAxJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EACA0I,MAAM,CAAC2H,OAAO,CAAC5J,eAAe,CAACgC,OAAO,CAAC,CAAC/G,GAAG,CAAC,CAAC,CAAC4O,GAAG,EAAEpR,MAAM,CAAgB,kBACxER,OAAA;cAAeiB,KAAK,EAAE;gBAAEmB,SAAS,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCd,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACsQ,GAAG;cAAA;gBAAAzQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCtB,OAAA;gBAAAc,QAAA,GAAG,wCAAQ,EAACN,MAAM,CAAC+D,eAAe;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCtB,OAAA;gBAAAc,QAAA,GAAG,oDAAU,EAACN,MAAM,CAACiE,kBAAkB;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHpCsQ,GAAG;cAAAzQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;UACDuC,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAG;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGFyG,eAAe,IAAIA,eAAe,CAACgC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAAClC,eAAe,CAACgC,OAAO,CAAC,CAAC9F,MAAM,GAAG,CAAC,iBAC5FjE,OAAA,CAACpC,IAAI;QAAC6D,KAAK,EAAC,gFAAe;QAACgO,SAAS,EAAC,eAAe;QAAA3O,QAAA,eACnDd,OAAA,CAAC7B,KAAK;UAAC4C,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAChEd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCtB,OAAA,CAAChC,MAAM;cACL0D,KAAK,EAAEuG,iBAAkB;cACzB0D,QAAQ,EAAEzD,oBAAqB;cAC/BjH,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvC2N,WAAW,EAAC,8DAAY;cAAAjP,QAAA,EAEvBkJ,MAAM,CAACC,IAAI,CAAClC,eAAe,CAACgC,OAAO,CAAC,CAAC/G,GAAG,CAAE4O,GAAG,iBAC5C5R,OAAA,CAACI,MAAM;gBAAWsB,KAAK,EAAEkQ,GAAI;gBAAA9Q,QAAA,EAC1B8Q;cAAG,GADOA,GAAG;gBAAAzQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL2G,iBAAiB,IAAIF,eAAe,CAACgC,OAAO,CAAC9B,iBAAiB,CAAC,iBAC9DjI,OAAA,CAACM,qBAAqB;YACpBC,SAAS,EAAE0H,iBAAkB;YAC7BzH,MAAM,EAAEuH,eAAe,CAACgC,OAAO,CAAC9B,iBAAiB;UAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP,EAGAkI,sBAAsB,CAACvF,MAAM,GAAG,CAAC,iBAChCjE,OAAA,CAACpC,IAAI;QAAC6D,KAAK,EAAC,sCAAQ;QAACgO,SAAS,EAAC,eAAe;QAACxO,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBACtEd,OAAA,CAACrB,KAAK;UACJN,OAAO,EAAC,4CAAS;UACjBmG,WAAW,EAAC,wMAAmC;UAC/CX,IAAI,EAAC,SAAS;UACdiB,QAAQ;UACR7D,KAAK,EAAE;YAAEuO,YAAY,EAAE;UAAG;QAAE;UAAArO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFtB,OAAA,CAAC7B,KAAK;UAAC4C,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,gBAEhEd,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3BtB,OAAA,CAAChC,MAAM;cACL0D,KAAK,EAAE4H,mBAAoB;cAC3BqC,QAAQ,EAAElC,qBAAsB;cAChCxI,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvC2N,WAAW,EAAC,oEAAa;cAAAjP,QAAA,EAExB0I,sBAAsB,CAACxG,GAAG,CAAE6G,IAAI,iBAC/B7J,OAAA,CAACI,MAAM;gBAAoBsB,KAAK,EAAEmI,IAAI,CAACC,OAAQ;gBAAAhJ,QAAA,EAC5C+I,IAAI,CAACC,OAAO,CAACwC,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAGzC,IAAI,CAACC,OAAO,CAACjJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI6H,IAAI,CAACmB,IAAI,CAACgI,UAAU,IAAIhI,IAAI,CAACiI,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAMlI,IAAI,CAACC,OAAO,CAACkI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAItJ,IAAI,CAACmB,IAAI,CAACgI,UAAU,IAAIhI,IAAI,CAACiI,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;cAAG,GAHjGlI,IAAI,CAACC,OAAO;gBAAA3I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL4H,oBAAoB,IAAIA,oBAAoB,CAACiI,aAAa,iBACzDnR,OAAA;YAAKiB,KAAK,EAAE;cAAEmB,SAAS,EAAE,CAAC;cAAEgP,OAAO,EAAE,CAAC;cAAEC,eAAe,EAAE,SAAS;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAzQ,QAAA,EAChHoI,oBAAoB,CAACiI,aAAa,CAACK,kBAAkB,gBACpDxR,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAGiB,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAEqN,UAAU,EAAE;gBAAO,CAAE;gBAAAzO,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnEtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC4H,oBAAoB,CAACiI,aAAa,CAACM,aAAa;cAAA;gBAAAtQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC4H,oBAAoB,CAACiI,aAAa,CAACO,kBAAkB;cAAA;gBAAAvQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,gBAENtB,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAGiB,KAAK,EAAE;kBAAEiB,KAAK,EAAE,SAAS;kBAAEqN,UAAU,EAAE;gBAAO,CAAE;gBAAAzO,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEtB,OAAA;gBAAAc,QAAA,gBAAGd,OAAA;kBAAAc,QAAA,EAAQ;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC4H,oBAAoB,CAACiI,aAAa,CAACxG,KAAK;cAAA;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA4H,oBAAoB,IAAIA,oBAAoB,CAACa,OAAO,iBACnD/J,OAAA;YAAAc,QAAA,gBACEd,OAAA,CAACE,IAAI;cAAC2C,MAAM;cAAA/B,QAAA,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCtB,OAAA,CAAChC,MAAM;cACL0D,KAAK,EAAE0H,sBAAuB;cAC9BuC,QAAQ,EAAEtC,yBAA0B;cACpCpI,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAAE,CAAE;cACvC2N,WAAW,EAAC,8DAAY;cAAAjP,QAAA,EAEvBkJ,MAAM,CAACC,IAAI,CAACf,oBAAoB,CAACa,OAAO,CAAC,CAAC/G,GAAG,CAAE4O,GAAG,iBACjD5R,OAAA,CAACI,MAAM;gBAAWsB,KAAK,EAAEkQ,GAAI;gBAAA9Q,QAAA,EAC1B8Q;cAAG,GADOA,GAAG;gBAAAzQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA8H,sBAAsB,IAAIF,oBAAoB,IAAIA,oBAAoB,CAACa,OAAO,CAACX,sBAAsB,CAAC,iBACrGpJ,OAAA,CAACM,qBAAqB;YACpBC,SAAS,EAAE6I,sBAAuB;YAClC5I,MAAM,EAAE0I,oBAAoB,CAACa,OAAO,CAACX,sBAAsB;UAAE;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC2D,EAAA,CApgCID,iBAA2B;EAAA,QAgD8CnF,cAAc;AAAA;AAAAoS,GAAA,GAhDvFjN,iBAA2B;AAsgCjC,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAkN,GAAA;AAAAC,YAAA,CAAAnN,EAAA;AAAAmN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}