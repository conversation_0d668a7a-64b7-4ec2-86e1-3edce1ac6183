{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdSturges;\nvar _count = _interopRequireDefault(require(\"../count.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction thresholdSturges(values) {\n  return Math.ceil(Math.log((0, _count.default)(values)) / Math.LN2) + 1;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "thresholdSturges", "_count", "_interopRequireDefault", "require", "obj", "__esModule", "values", "Math", "ceil", "log", "LN2"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/threshold/sturges.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = thresholdSturges;\n\nvar _count = _interopRequireDefault(require(\"../count.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction thresholdSturges(values) {\n  return Math.ceil(Math.log((0, _count.default)(values)) / Math.LN2) + 1;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,gBAAgB;AAElC,IAAIC,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE3D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,gBAAgBA,CAACM,MAAM,EAAE;EAChC,OAAOC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAAER,MAAM,CAACF,OAAO,EAAEO,MAAM,CAAC,CAAC,GAAGC,IAAI,CAACG,GAAG,CAAC,GAAG,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "script"}