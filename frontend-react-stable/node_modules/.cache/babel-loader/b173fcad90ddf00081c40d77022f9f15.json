{"ast": null, "code": "import InternalCard from './Card';\nimport Grid from './Grid';\nimport Meta from './Meta';\nvar Card = InternalCard;\nCard.Grid = Grid;\nCard.Meta = Meta;\nexport default Card;", "map": {"version": 3, "names": ["InternalCard", "Grid", "Meta", "Card"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/card/index.js"], "sourcesContent": ["import InternalCard from './Card';\nimport Grid from './Grid';\nimport Meta from './Meta';\nvar Card = InternalCard;\nCard.Grid = Grid;\nCard.Meta = Meta;\nexport default Card;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,QAAQ;AACjC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,IAAI,GAAGH,YAAY;AACvBG,IAAI,CAACF,IAAI,GAAGA,IAAI;AAChBE,IAAI,CAACD,IAAI,GAAGA,IAAI;AAChB,eAAeC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}