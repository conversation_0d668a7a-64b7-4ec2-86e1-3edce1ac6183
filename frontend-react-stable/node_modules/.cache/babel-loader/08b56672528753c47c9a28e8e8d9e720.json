{"ast": null, "code": "import confirm, { modalGlobalConfig, withConfirm, withError, withInfo, withSuccess, withWarn } from './confirm';\nimport destroyFns from './destroyFns';\nimport OriginModal from './Modal';\nimport useModal from './useModal';\nfunction modalWarn(props) {\n  return confirm(withWarn(props));\n}\nvar Modal = OriginModal;\nModal.useModal = useModal;\nModal.info = function infoFn(props) {\n  return confirm(withInfo(props));\n};\nModal.success = function successFn(props) {\n  return confirm(withSuccess(props));\n};\nModal.error = function errorFn(props) {\n  return confirm(withError(props));\n};\nModal.warning = modalWarn;\nModal.warn = modalWarn;\nModal.confirm = function confirmFn(props) {\n  return confirm(withConfirm(props));\n};\nModal.destroyAll = function destroyAllFn() {\n  while (destroyFns.length) {\n    var close = destroyFns.pop();\n    if (close) {\n      close();\n    }\n  }\n};\nModal.config = modalGlobalConfig;\nexport default Modal;", "map": {"version": 3, "names": ["confirm", "modalGlobalConfig", "withConfirm", "with<PERSON><PERSON><PERSON>", "withInfo", "withSuccess", "with<PERSON><PERSON><PERSON>", "destroyFns", "OriginModal", "useModal", "modalWarn", "props", "Modal", "info", "infoFn", "success", "successFn", "error", "errorFn", "warning", "warn", "confirmFn", "destroyAll", "destroyAllFn", "length", "close", "pop", "config"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/modal/index.js"], "sourcesContent": ["import confirm, { modalGlobalConfig, withConfirm, withError, withInfo, withSuccess, withWarn } from './confirm';\nimport destroyFns from './destroyFns';\nimport OriginModal from './Modal';\nimport useModal from './useModal';\nfunction modalWarn(props) {\n  return confirm(withWarn(props));\n}\nvar Modal = OriginModal;\nModal.useModal = useModal;\nModal.info = function infoFn(props) {\n  return confirm(withInfo(props));\n};\nModal.success = function successFn(props) {\n  return confirm(withSuccess(props));\n};\nModal.error = function errorFn(props) {\n  return confirm(withError(props));\n};\nModal.warning = modalWarn;\nModal.warn = modalWarn;\nModal.confirm = function confirmFn(props) {\n  return confirm(withConfirm(props));\n};\nModal.destroyAll = function destroyAllFn() {\n  while (destroyFns.length) {\n    var close = destroyFns.pop();\n    if (close) {\n      close();\n    }\n  }\n};\nModal.config = modalGlobalConfig;\nexport default Modal;"], "mappings": "AAAA,OAAOA,OAAO,IAAIC,iBAAiB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,WAAW;AAC/G,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,SAAS;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOX,OAAO,CAACM,QAAQ,CAACK,KAAK,CAAC,CAAC;AACjC;AACA,IAAIC,KAAK,GAAGJ,WAAW;AACvBI,KAAK,CAACH,QAAQ,GAAGA,QAAQ;AACzBG,KAAK,CAACC,IAAI,GAAG,SAASC,MAAMA,CAACH,KAAK,EAAE;EAClC,OAAOX,OAAO,CAACI,QAAQ,CAACO,KAAK,CAAC,CAAC;AACjC,CAAC;AACDC,KAAK,CAACG,OAAO,GAAG,SAASC,SAASA,CAACL,KAAK,EAAE;EACxC,OAAOX,OAAO,CAACK,WAAW,CAACM,KAAK,CAAC,CAAC;AACpC,CAAC;AACDC,KAAK,CAACK,KAAK,GAAG,SAASC,OAAOA,CAACP,KAAK,EAAE;EACpC,OAAOX,OAAO,CAACG,SAAS,CAACQ,KAAK,CAAC,CAAC;AAClC,CAAC;AACDC,KAAK,CAACO,OAAO,GAAGT,SAAS;AACzBE,KAAK,CAACQ,IAAI,GAAGV,SAAS;AACtBE,KAAK,CAACZ,OAAO,GAAG,SAASqB,SAASA,CAACV,KAAK,EAAE;EACxC,OAAOX,OAAO,CAACE,WAAW,CAACS,KAAK,CAAC,CAAC;AACpC,CAAC;AACDC,KAAK,CAACU,UAAU,GAAG,SAASC,YAAYA,CAAA,EAAG;EACzC,OAAOhB,UAAU,CAACiB,MAAM,EAAE;IACxB,IAAIC,KAAK,GAAGlB,UAAU,CAACmB,GAAG,CAAC,CAAC;IAC5B,IAAID,KAAK,EAAE;MACTA,KAAK,CAAC,CAAC;IACT;EACF;AACF,CAAC;AACDb,KAAK,CAACe,MAAM,GAAG1B,iBAAiB;AAChC,eAAeW,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}