{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CloudSyncOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloudSyncOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar CloudSyncOutlined = function CloudSyncOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CloudSyncOutlinedSvg\n  }));\n};\nCloudSyncOutlined.displayName = 'CloudSyncOutlined';\nexport default /*#__PURE__*/React.forwardRef(CloudSyncOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "CloudSyncOutlinedSvg", "AntdIcon", "CloudSyncOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/CloudSyncOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CloudSyncOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloudSyncOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar CloudSyncOutlined = function CloudSyncOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CloudSyncOutlinedSvg\n  }));\n};\nCloudSyncOutlined.displayName = 'CloudSyncOutlined';\nexport default /*#__PURE__*/React.forwardRef(CloudSyncOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACnD,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}