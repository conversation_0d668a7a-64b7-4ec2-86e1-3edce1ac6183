{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport YearHeader from './YearHeader';\nimport YearBody, { YEAR_COL_COUNT } from './YearBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nexport var YEAR_DECADE_COUNT = 10;\nfunction YearPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    sourceMode = props.sourceMode,\n    _onSelect = props.onSelect,\n    onPanelChange = props.onPanelChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-year-panel\");\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_DECADE_COUNT), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange(sourceMode === 'date' ? 'date' : 'month', value || viewDate);\n        }\n      });\n    }\n  };\n  // ==================== View Operation ====================\n  var onDecadeChange = function onDecadeChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff * 10);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(YearHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevDecade: function onPrevDecade() {\n      onDecadeChange(-1);\n    },\n    onNextDecade: function onNextDecade() {\n      onDecadeChange(1);\n    },\n    onDecadeClick: function onDecadeClick() {\n      onPanelChange('decade', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(YearBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      onPanelChange(sourceMode === 'date' ? 'date' : 'month', date);\n      _onSelect(date, 'mouse');\n    }\n  })));\n}\nexport default YearPanel;", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON><PERSON>", "YearBody", "YEAR_COL_COUNT", "createKeyDownHandler", "YEAR_DECADE_COUNT", "YearPanel", "props", "prefixCls", "operationRef", "onViewDateChange", "generateConfig", "value", "viewDate", "sourceMode", "_onSelect", "onSelect", "onPanelChange", "panelPrefixCls", "concat", "current", "onKeyDown", "event", "onLeftRight", "diff", "addYear", "onCtrlLeftRight", "onUpDown", "onEnter", "onDecadeChange", "newDate", "createElement", "className", "onPrevDecade", "onNextDecade", "onDecadeClick", "date"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/YearPanel/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport YearHeader from './YearHeader';\nimport YearBody, { YEAR_COL_COUNT } from './YearBody';\nimport { createKeyDownHandler } from '../../utils/uiUtil';\nexport var YEAR_DECADE_COUNT = 10;\nfunction YearPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    sourceMode = props.sourceMode,\n    _onSelect = props.onSelect,\n    onPanelChange = props.onPanelChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-year-panel\");\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_DECADE_COUNT), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange(sourceMode === 'date' ? 'date' : 'month', value || viewDate);\n        }\n      });\n    }\n  };\n  // ==================== View Operation ====================\n  var onDecadeChange = function onDecadeChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff * 10);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(YearHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevDecade: function onPrevDecade() {\n      onDecadeChange(-1);\n    },\n    onNextDecade: function onNextDecade() {\n      onDecadeChange(1);\n    },\n    onDecadeClick: function onDecadeClick() {\n      onPanelChange('decade', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(YearBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      onPanelChange(sourceMode === 'date' ? 'date' : 'month', date);\n      _onSelect(date, 'mouse');\n    }\n  })));\n}\nexport default YearPanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,QAAQ,IAAIC,cAAc,QAAQ,YAAY;AACrD,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,OAAO,IAAIC,iBAAiB,GAAG,EAAE;AACjC,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;IACzCC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,SAAS,GAAGR,KAAK,CAACS,QAAQ;IAC1BC,aAAa,GAAGV,KAAK,CAACU,aAAa;EACrC,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACX,SAAS,EAAE,aAAa,CAAC;EACxD;EACAC,YAAY,CAACW,OAAO,GAAG;IACrBC,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;MACnC,OAAOlB,oBAAoB,CAACkB,KAAK,EAAE;QACjCC,WAAW,EAAE,SAASA,WAAWA,CAACC,IAAI,EAAE;UACtCT,SAAS,CAACJ,cAAc,CAACc,OAAO,CAACb,KAAK,IAAIC,QAAQ,EAAEW,IAAI,CAAC,EAAE,KAAK,CAAC;QACnE,CAAC;QACDE,eAAe,EAAE,SAASA,eAAeA,CAACF,IAAI,EAAE;UAC9CT,SAAS,CAACJ,cAAc,CAACc,OAAO,CAACb,KAAK,IAAIC,QAAQ,EAAEW,IAAI,GAAGnB,iBAAiB,CAAC,EAAE,KAAK,CAAC;QACvF,CAAC;QACDsB,QAAQ,EAAE,SAASA,QAAQA,CAACH,IAAI,EAAE;UAChCT,SAAS,CAACJ,cAAc,CAACc,OAAO,CAACb,KAAK,IAAIC,QAAQ,EAAEW,IAAI,GAAGrB,cAAc,CAAC,EAAE,KAAK,CAAC;QACpF,CAAC;QACDyB,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1BX,aAAa,CAACH,UAAU,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,EAAEF,KAAK,IAAIC,QAAQ,CAAC;QAC5E;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACA,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACL,IAAI,EAAE;IACjD,IAAIM,OAAO,GAAGnB,cAAc,CAACc,OAAO,CAACZ,QAAQ,EAAEW,IAAI,GAAG,EAAE,CAAC;IACzDd,gBAAgB,CAACoB,OAAO,CAAC;IACzBb,aAAa,CAAC,IAAI,EAAEa,OAAO,CAAC;EAC9B,CAAC;EACD,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEd;EACb,CAAC,EAAE,aAAalB,KAAK,CAAC+B,aAAa,CAAC9B,UAAU,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IAClEC,SAAS,EAAEA,SAAS;IACpByB,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;MACpCJ,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACDK,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;MACpCL,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC;IACDM,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtClB,aAAa,CAAC,QAAQ,EAAEJ,QAAQ,CAAC;IACnC;EACF,CAAC,CAAC,CAAC,EAAE,aAAab,KAAK,CAAC+B,aAAa,CAAC7B,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IAClEC,SAAS,EAAEA,SAAS;IACpBQ,QAAQ,EAAE,SAASA,QAAQA,CAACoB,IAAI,EAAE;MAChCnB,aAAa,CAACH,UAAU,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,EAAEsB,IAAI,CAAC;MAC7DrB,SAAS,CAACqB,IAAI,EAAE,OAAO,CAAC;IAC1B;EACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAe9B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}