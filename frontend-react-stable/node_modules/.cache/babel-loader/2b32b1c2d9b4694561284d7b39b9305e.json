{"ast": null, "code": "import momentGenerateConfig from \"rc-picker/es/generate/moment\";\nimport generateCalendar from './generateCalendar';\nvar Calendar = generateCalendar(momentGenerateConfig);\nexport default Calendar;", "map": {"version": 3, "names": ["momentGenerateConfig", "generateCalendar", "Calendar"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/calendar/index.js"], "sourcesContent": ["import momentGenerateConfig from \"rc-picker/es/generate/moment\";\nimport generateCalendar from './generateCalendar';\nvar Calendar = generateCalendar(momentGenerateConfig);\nexport default Calendar;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,IAAIC,QAAQ,GAAGD,gBAAgB,CAACD,oBAAoB,CAAC;AACrD,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}