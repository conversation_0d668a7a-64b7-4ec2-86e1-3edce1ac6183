{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"style\", \"className\", \"title\", \"eventKey\", \"warnKey\", \"disabled\", \"internalPopupClose\", \"children\", \"itemIcon\", \"expandIcon\", \"popupClassName\", \"popupOffset\", \"onClick\", \"onMouseEnter\", \"onMouseLeave\", \"onTitleClick\", \"onTitleMouseEnter\", \"onTitleMouseLeave\"],\n  _excluded2 = [\"active\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport warning from \"rc-util/es/warning\";\nimport SubMenuList from './SubMenuList';\nimport { parseChildren } from '../utils/nodeUtil';\nimport MenuContextProvider, { MenuContext } from '../context/MenuContext';\nimport useMemoCallback from '../hooks/useMemoCallback';\nimport PopupTrigger from './PopupTrigger';\nimport Icon from '../Icon';\nimport useActive from '../hooks/useActive';\nimport { warnItemProp } from '../utils/warnUtil';\nimport useDirectionStyle from '../hooks/useDirectionStyle';\nimport InlineSubMenuList from './InlineSubMenuList';\nimport { PathTrackerContext, PathUserContext, useFullPath, useMeasure } from '../context/PathContext';\nimport { useMenuId } from '../context/IdContext';\nimport PrivateContext from '../context/PrivateContext';\nvar InternalSubMenu = function InternalSubMenu(props) {\n  var _classNames;\n  var style = props.style,\n    className = props.className,\n    title = props.title,\n    eventKey = props.eventKey,\n    warnKey = props.warnKey,\n    disabled = props.disabled,\n    internalPopupClose = props.internalPopupClose,\n    children = props.children,\n    itemIcon = props.itemIcon,\n    expandIcon = props.expandIcon,\n    popupClassName = props.popupClassName,\n    popupOffset = props.popupOffset,\n    onClick = props.onClick,\n    onMouseEnter = props.onMouseEnter,\n    onMouseLeave = props.onMouseLeave,\n    onTitleClick = props.onTitleClick,\n    onTitleMouseEnter = props.onTitleMouseEnter,\n    onTitleMouseLeave = props.onTitleMouseLeave,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var domDataId = useMenuId(eventKey);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    mode = _React$useContext.mode,\n    openKeys = _React$useContext.openKeys,\n    contextDisabled = _React$useContext.disabled,\n    overflowDisabled = _React$useContext.overflowDisabled,\n    activeKey = _React$useContext.activeKey,\n    selectedKeys = _React$useContext.selectedKeys,\n    contextItemIcon = _React$useContext.itemIcon,\n    contextExpandIcon = _React$useContext.expandIcon,\n    onItemClick = _React$useContext.onItemClick,\n    onOpenChange = _React$useContext.onOpenChange,\n    onActive = _React$useContext.onActive;\n  var _React$useContext2 = React.useContext(PrivateContext),\n    _internalRenderSubMenuItem = _React$useContext2._internalRenderSubMenuItem;\n  var _React$useContext3 = React.useContext(PathUserContext),\n    isSubPathKey = _React$useContext3.isSubPathKey;\n  var connectedPath = useFullPath();\n  var subMenuPrefixCls = \"\".concat(prefixCls, \"-submenu\");\n  var mergedDisabled = contextDisabled || disabled;\n  var elementRef = React.useRef();\n  var popupRef = React.useRef(); // ================================ Warn ================================\n\n  if (process.env.NODE_ENV !== 'production' && warnKey) {\n    warning(false, 'SubMenu should not leave undefined `key`.');\n  } // ================================ Icon ================================\n\n  var mergedItemIcon = itemIcon || contextItemIcon;\n  var mergedExpandIcon = expandIcon || contextExpandIcon; // ================================ Open ================================\n\n  var originOpen = openKeys.includes(eventKey);\n  var open = !overflowDisabled && originOpen; // =============================== Select ===============================\n\n  var childrenSelected = isSubPathKey(selectedKeys, eventKey); // =============================== Active ===============================\n\n  var _useActive = useActive(eventKey, mergedDisabled, onTitleMouseEnter, onTitleMouseLeave),\n    active = _useActive.active,\n    activeProps = _objectWithoutProperties(_useActive, _excluded2); // Fallback of active check to avoid hover on menu title or disabled item\n\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    childrenActive = _React$useState2[0],\n    setChildrenActive = _React$useState2[1];\n  var triggerChildrenActive = function triggerChildrenActive(newActive) {\n    if (!mergedDisabled) {\n      setChildrenActive(newActive);\n    }\n  };\n  var onInternalMouseEnter = function onInternalMouseEnter(domEvent) {\n    triggerChildrenActive(true);\n    onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n  var onInternalMouseLeave = function onInternalMouseLeave(domEvent) {\n    triggerChildrenActive(false);\n    onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n  var mergedActive = React.useMemo(function () {\n    if (active) {\n      return active;\n    }\n    if (mode !== 'inline') {\n      return childrenActive || isSubPathKey([activeKey], eventKey);\n    }\n    return false;\n  }, [mode, active, activeKey, childrenActive, eventKey, isSubPathKey]); // ========================== DirectionStyle ==========================\n\n  var directionStyle = useDirectionStyle(connectedPath.length); // =============================== Events ===============================\n  // >>>> Title click\n\n  var onInternalTitleClick = function onInternalTitleClick(e) {\n    // Skip if disabled\n    if (mergedDisabled) {\n      return;\n    }\n    onTitleClick === null || onTitleClick === void 0 ? void 0 : onTitleClick({\n      key: eventKey,\n      domEvent: e\n    }); // Trigger open by click when mode is `inline`\n\n    if (mode === 'inline') {\n      onOpenChange(eventKey, !originOpen);\n    }\n  }; // >>>> Context for children click\n\n  var onMergedItemClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(warnItemProp(info));\n    onItemClick(info);\n  }); // >>>>> Visible change\n\n  var onPopupVisibleChange = function onPopupVisibleChange(newVisible) {\n    if (mode !== 'inline') {\n      onOpenChange(eventKey, newVisible);\n    }\n  };\n  /**\n   * Used for accessibility. Helper will focus element without key board.\n   * We should manually trigger an active\n   */\n\n  var onInternalFocus = function onInternalFocus() {\n    onActive(eventKey);\n  }; // =============================== Render ===============================\n\n  var popupId = domDataId && \"\".concat(domDataId, \"-popup\"); // >>>>> Title\n\n  var titleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"menuitem\",\n    style: directionStyle,\n    className: \"\".concat(subMenuPrefixCls, \"-title\"),\n    tabIndex: mergedDisabled ? null : -1,\n    ref: elementRef,\n    title: typeof title === 'string' ? title : null,\n    \"data-menu-id\": overflowDisabled && domDataId ? null : domDataId,\n    \"aria-expanded\": open,\n    \"aria-haspopup\": true,\n    \"aria-controls\": popupId,\n    \"aria-disabled\": mergedDisabled,\n    onClick: onInternalTitleClick,\n    onFocus: onInternalFocus\n  }, activeProps), title, /*#__PURE__*/React.createElement(Icon, {\n    icon: mode !== 'horizontal' ? mergedExpandIcon : null,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      isOpen: open,\n      // [Legacy] Not sure why need this mark\n      isSubMenu: true\n    })\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"\".concat(subMenuPrefixCls, \"-arrow\")\n  }))); // Cache mode if it change to `inline` which do not have popup motion\n\n  var triggerModeRef = React.useRef(mode);\n  if (mode !== 'inline') {\n    triggerModeRef.current = connectedPath.length > 1 ? 'vertical' : mode;\n  }\n  if (!overflowDisabled) {\n    var triggerMode = triggerModeRef.current; // Still wrap with Trigger here since we need avoid react re-mount dom node\n    // Which makes motion failed\n\n    titleNode = /*#__PURE__*/React.createElement(PopupTrigger, {\n      mode: triggerMode,\n      prefixCls: subMenuPrefixCls,\n      visible: !internalPopupClose && open && mode !== 'inline',\n      popupClassName: popupClassName,\n      popupOffset: popupOffset,\n      popup: /*#__PURE__*/React.createElement(MenuContextProvider // Special handle of horizontal mode\n      , {\n        mode: triggerMode === 'horizontal' ? 'vertical' : triggerMode\n      }, /*#__PURE__*/React.createElement(SubMenuList, {\n        id: popupId,\n        ref: popupRef\n      }, children)),\n      disabled: mergedDisabled,\n      onVisibleChange: onPopupVisibleChange\n    }, titleNode);\n  } // >>>>> List node\n\n  var listNode = /*#__PURE__*/React.createElement(Overflow.Item, _extends({\n    role: \"none\"\n  }, restProps, {\n    component: \"li\",\n    style: style,\n    className: classNames(subMenuPrefixCls, \"\".concat(subMenuPrefixCls, \"-\").concat(mode), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-open\"), open), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-active\"), mergedActive), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-selected\"), childrenSelected), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-disabled\"), mergedDisabled), _classNames)),\n    onMouseEnter: onInternalMouseEnter,\n    onMouseLeave: onInternalMouseLeave\n  }), titleNode, !overflowDisabled && /*#__PURE__*/React.createElement(InlineSubMenuList, {\n    id: popupId,\n    open: open,\n    keyPath: connectedPath\n  }, children));\n  if (_internalRenderSubMenuItem) {\n    listNode = _internalRenderSubMenuItem(listNode, props, {\n      selected: childrenSelected,\n      active: mergedActive,\n      open: open,\n      disabled: mergedDisabled\n    });\n  } // >>>>> Render\n\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    onItemClick: onMergedItemClick,\n    mode: mode === 'horizontal' ? 'vertical' : mode,\n    itemIcon: mergedItemIcon,\n    expandIcon: mergedExpandIcon\n  }, listNode);\n};\nexport default function SubMenu(props) {\n  var eventKey = props.eventKey,\n    children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath); // ==================== Record KeyPath ====================\n\n  var measure = useMeasure(); // eslint-disable-next-line consistent-return\n\n  React.useEffect(function () {\n    if (measure) {\n      measure.registerPath(eventKey, connectedKeyPath);\n      return function () {\n        measure.unregisterPath(eventKey, connectedKeyPath);\n      };\n    }\n  }, [connectedKeyPath]);\n  var renderNode; // ======================== Render ========================\n\n  if (measure) {\n    renderNode = childList;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(InternalSubMenu, props, childList);\n  }\n  return /*#__PURE__*/React.createElement(PathTrackerContext.Provider, {\n    value: connectedKeyPath\n  }, renderNode);\n}", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "classNames", "Overflow", "warning", "SubMenuList", "parse<PERSON><PERSON><PERSON>n", "MenuContextProvider", "MenuContext", "useMemoCallback", "PopupTrigger", "Icon", "useActive", "warnItemProp", "useDirectionStyle", "InlineSubMenuList", "PathTrackerContext", "PathUserContext", "useFullPath", "useMeasure", "useMenuId", "PrivateContext", "InternalSubMenu", "props", "_classNames", "style", "className", "title", "eventKey", "<PERSON><PERSON><PERSON>", "disabled", "internalPopupClose", "children", "itemIcon", "expandIcon", "popupClassName", "popupOffset", "onClick", "onMouseEnter", "onMouseLeave", "onTitleClick", "onTitleMouseEnter", "onTitleMouseLeave", "restProps", "domDataId", "_React$useContext", "useContext", "prefixCls", "mode", "openKeys", "contextDisabled", "overflowDisabled", "active<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "contextItemIcon", "contextExpandIcon", "onItemClick", "onOpenChange", "onActive", "_React$useContext2", "_internalRenderSubMenuItem", "_React$useContext3", "isSubPath<PERSON>ey", "connectedPath", "subMenuPrefixCls", "concat", "mergedDisabled", "elementRef", "useRef", "popupRef", "process", "env", "NODE_ENV", "mergedItemIcon", "mergedExpandIcon", "originOpen", "includes", "open", "childrenSelected", "_useActive", "active", "activeProps", "_React$useState", "useState", "_React$useState2", "childrenActive", "setChildrenActive", "triggerChildrenActive", "newActive", "onInternalMouseEnter", "domEvent", "key", "onInternalMouseLeave", "mergedActive", "useMemo", "directionStyle", "length", "onInternalTitleClick", "e", "onMergedItemClick", "info", "onPopupVisibleChange", "newVisible", "onInternalFocus", "popupId", "titleNode", "createElement", "role", "tabIndex", "ref", "onFocus", "icon", "isOpen", "isSubMenu", "triggerModeRef", "current", "triggerMode", "visible", "popup", "id", "onVisibleChange", "listNode", "<PERSON><PERSON>", "component", "keyP<PERSON>", "selected", "SubMenu", "connectedKeyPath", "childList", "measure", "useEffect", "registerPath", "unregisterPath", "renderNode", "Provider", "value"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/SubMenu/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"style\", \"className\", \"title\", \"eventKey\", \"warnKey\", \"disabled\", \"internalPopupClose\", \"children\", \"itemIcon\", \"expandIcon\", \"popupClassName\", \"popupOffset\", \"onClick\", \"onMouseEnter\", \"onMouseLeave\", \"onTitleClick\", \"onTitleMouseEnter\", \"onTitleMouseLeave\"],\n    _excluded2 = [\"active\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Overflow from 'rc-overflow';\nimport warning from \"rc-util/es/warning\";\nimport SubMenuList from './SubMenuList';\nimport { parseChildren } from '../utils/nodeUtil';\nimport MenuContextProvider, { MenuContext } from '../context/MenuContext';\nimport useMemoCallback from '../hooks/useMemoCallback';\nimport PopupTrigger from './PopupTrigger';\nimport Icon from '../Icon';\nimport useActive from '../hooks/useActive';\nimport { warnItemProp } from '../utils/warnUtil';\nimport useDirectionStyle from '../hooks/useDirectionStyle';\nimport InlineSubMenuList from './InlineSubMenuList';\nimport { PathTrackerContext, PathUserContext, useFullPath, useMeasure } from '../context/PathContext';\nimport { useMenuId } from '../context/IdContext';\nimport PrivateContext from '../context/PrivateContext';\n\nvar InternalSubMenu = function InternalSubMenu(props) {\n  var _classNames;\n\n  var style = props.style,\n      className = props.className,\n      title = props.title,\n      eventKey = props.eventKey,\n      warnKey = props.warnKey,\n      disabled = props.disabled,\n      internalPopupClose = props.internalPopupClose,\n      children = props.children,\n      itemIcon = props.itemIcon,\n      expandIcon = props.expandIcon,\n      popupClassName = props.popupClassName,\n      popupOffset = props.popupOffset,\n      onClick = props.onClick,\n      onMouseEnter = props.onMouseEnter,\n      onMouseLeave = props.onMouseLeave,\n      onTitleClick = props.onTitleClick,\n      onTitleMouseEnter = props.onTitleMouseEnter,\n      onTitleMouseLeave = props.onTitleMouseLeave,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n  var domDataId = useMenuId(eventKey);\n\n  var _React$useContext = React.useContext(MenuContext),\n      prefixCls = _React$useContext.prefixCls,\n      mode = _React$useContext.mode,\n      openKeys = _React$useContext.openKeys,\n      contextDisabled = _React$useContext.disabled,\n      overflowDisabled = _React$useContext.overflowDisabled,\n      activeKey = _React$useContext.activeKey,\n      selectedKeys = _React$useContext.selectedKeys,\n      contextItemIcon = _React$useContext.itemIcon,\n      contextExpandIcon = _React$useContext.expandIcon,\n      onItemClick = _React$useContext.onItemClick,\n      onOpenChange = _React$useContext.onOpenChange,\n      onActive = _React$useContext.onActive;\n\n  var _React$useContext2 = React.useContext(PrivateContext),\n      _internalRenderSubMenuItem = _React$useContext2._internalRenderSubMenuItem;\n\n  var _React$useContext3 = React.useContext(PathUserContext),\n      isSubPathKey = _React$useContext3.isSubPathKey;\n\n  var connectedPath = useFullPath();\n  var subMenuPrefixCls = \"\".concat(prefixCls, \"-submenu\");\n  var mergedDisabled = contextDisabled || disabled;\n  var elementRef = React.useRef();\n  var popupRef = React.useRef(); // ================================ Warn ================================\n\n  if (process.env.NODE_ENV !== 'production' && warnKey) {\n    warning(false, 'SubMenu should not leave undefined `key`.');\n  } // ================================ Icon ================================\n\n\n  var mergedItemIcon = itemIcon || contextItemIcon;\n  var mergedExpandIcon = expandIcon || contextExpandIcon; // ================================ Open ================================\n\n  var originOpen = openKeys.includes(eventKey);\n  var open = !overflowDisabled && originOpen; // =============================== Select ===============================\n\n  var childrenSelected = isSubPathKey(selectedKeys, eventKey); // =============================== Active ===============================\n\n  var _useActive = useActive(eventKey, mergedDisabled, onTitleMouseEnter, onTitleMouseLeave),\n      active = _useActive.active,\n      activeProps = _objectWithoutProperties(_useActive, _excluded2); // Fallback of active check to avoid hover on menu title or disabled item\n\n\n  var _React$useState = React.useState(false),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      childrenActive = _React$useState2[0],\n      setChildrenActive = _React$useState2[1];\n\n  var triggerChildrenActive = function triggerChildrenActive(newActive) {\n    if (!mergedDisabled) {\n      setChildrenActive(newActive);\n    }\n  };\n\n  var onInternalMouseEnter = function onInternalMouseEnter(domEvent) {\n    triggerChildrenActive(true);\n    onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n\n  var onInternalMouseLeave = function onInternalMouseLeave(domEvent) {\n    triggerChildrenActive(false);\n    onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n      key: eventKey,\n      domEvent: domEvent\n    });\n  };\n\n  var mergedActive = React.useMemo(function () {\n    if (active) {\n      return active;\n    }\n\n    if (mode !== 'inline') {\n      return childrenActive || isSubPathKey([activeKey], eventKey);\n    }\n\n    return false;\n  }, [mode, active, activeKey, childrenActive, eventKey, isSubPathKey]); // ========================== DirectionStyle ==========================\n\n  var directionStyle = useDirectionStyle(connectedPath.length); // =============================== Events ===============================\n  // >>>> Title click\n\n  var onInternalTitleClick = function onInternalTitleClick(e) {\n    // Skip if disabled\n    if (mergedDisabled) {\n      return;\n    }\n\n    onTitleClick === null || onTitleClick === void 0 ? void 0 : onTitleClick({\n      key: eventKey,\n      domEvent: e\n    }); // Trigger open by click when mode is `inline`\n\n    if (mode === 'inline') {\n      onOpenChange(eventKey, !originOpen);\n    }\n  }; // >>>> Context for children click\n\n\n  var onMergedItemClick = useMemoCallback(function (info) {\n    onClick === null || onClick === void 0 ? void 0 : onClick(warnItemProp(info));\n    onItemClick(info);\n  }); // >>>>> Visible change\n\n  var onPopupVisibleChange = function onPopupVisibleChange(newVisible) {\n    if (mode !== 'inline') {\n      onOpenChange(eventKey, newVisible);\n    }\n  };\n  /**\n   * Used for accessibility. Helper will focus element without key board.\n   * We should manually trigger an active\n   */\n\n\n  var onInternalFocus = function onInternalFocus() {\n    onActive(eventKey);\n  }; // =============================== Render ===============================\n\n\n  var popupId = domDataId && \"\".concat(domDataId, \"-popup\"); // >>>>> Title\n\n  var titleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"menuitem\",\n    style: directionStyle,\n    className: \"\".concat(subMenuPrefixCls, \"-title\"),\n    tabIndex: mergedDisabled ? null : -1,\n    ref: elementRef,\n    title: typeof title === 'string' ? title : null,\n    \"data-menu-id\": overflowDisabled && domDataId ? null : domDataId,\n    \"aria-expanded\": open,\n    \"aria-haspopup\": true,\n    \"aria-controls\": popupId,\n    \"aria-disabled\": mergedDisabled,\n    onClick: onInternalTitleClick,\n    onFocus: onInternalFocus\n  }, activeProps), title, /*#__PURE__*/React.createElement(Icon, {\n    icon: mode !== 'horizontal' ? mergedExpandIcon : null,\n    props: _objectSpread(_objectSpread({}, props), {}, {\n      isOpen: open,\n      // [Legacy] Not sure why need this mark\n      isSubMenu: true\n    })\n  }, /*#__PURE__*/React.createElement(\"i\", {\n    className: \"\".concat(subMenuPrefixCls, \"-arrow\")\n  }))); // Cache mode if it change to `inline` which do not have popup motion\n\n  var triggerModeRef = React.useRef(mode);\n\n  if (mode !== 'inline') {\n    triggerModeRef.current = connectedPath.length > 1 ? 'vertical' : mode;\n  }\n\n  if (!overflowDisabled) {\n    var triggerMode = triggerModeRef.current; // Still wrap with Trigger here since we need avoid react re-mount dom node\n    // Which makes motion failed\n\n    titleNode = /*#__PURE__*/React.createElement(PopupTrigger, {\n      mode: triggerMode,\n      prefixCls: subMenuPrefixCls,\n      visible: !internalPopupClose && open && mode !== 'inline',\n      popupClassName: popupClassName,\n      popupOffset: popupOffset,\n      popup: /*#__PURE__*/React.createElement(MenuContextProvider // Special handle of horizontal mode\n      , {\n        mode: triggerMode === 'horizontal' ? 'vertical' : triggerMode\n      }, /*#__PURE__*/React.createElement(SubMenuList, {\n        id: popupId,\n        ref: popupRef\n      }, children)),\n      disabled: mergedDisabled,\n      onVisibleChange: onPopupVisibleChange\n    }, titleNode);\n  } // >>>>> List node\n\n\n  var listNode = /*#__PURE__*/React.createElement(Overflow.Item, _extends({\n    role: \"none\"\n  }, restProps, {\n    component: \"li\",\n    style: style,\n    className: classNames(subMenuPrefixCls, \"\".concat(subMenuPrefixCls, \"-\").concat(mode), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-open\"), open), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-active\"), mergedActive), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-selected\"), childrenSelected), _defineProperty(_classNames, \"\".concat(subMenuPrefixCls, \"-disabled\"), mergedDisabled), _classNames)),\n    onMouseEnter: onInternalMouseEnter,\n    onMouseLeave: onInternalMouseLeave\n  }), titleNode, !overflowDisabled && /*#__PURE__*/React.createElement(InlineSubMenuList, {\n    id: popupId,\n    open: open,\n    keyPath: connectedPath\n  }, children));\n\n  if (_internalRenderSubMenuItem) {\n    listNode = _internalRenderSubMenuItem(listNode, props, {\n      selected: childrenSelected,\n      active: mergedActive,\n      open: open,\n      disabled: mergedDisabled\n    });\n  } // >>>>> Render\n\n\n  return /*#__PURE__*/React.createElement(MenuContextProvider, {\n    onItemClick: onMergedItemClick,\n    mode: mode === 'horizontal' ? 'vertical' : mode,\n    itemIcon: mergedItemIcon,\n    expandIcon: mergedExpandIcon\n  }, listNode);\n};\n\nexport default function SubMenu(props) {\n  var eventKey = props.eventKey,\n      children = props.children;\n  var connectedKeyPath = useFullPath(eventKey);\n  var childList = parseChildren(children, connectedKeyPath); // ==================== Record KeyPath ====================\n\n  var measure = useMeasure(); // eslint-disable-next-line consistent-return\n\n  React.useEffect(function () {\n    if (measure) {\n      measure.registerPath(eventKey, connectedKeyPath);\n      return function () {\n        measure.unregisterPath(eventKey, connectedKeyPath);\n      };\n    }\n  }, [connectedKeyPath]);\n  var renderNode; // ======================== Render ========================\n\n  if (measure) {\n    renderNode = childList;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(InternalSubMenu, props, childList);\n  }\n\n  return /*#__PURE__*/React.createElement(PathTrackerContext.Provider, {\n    value: connectedKeyPath\n  }, renderNode);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;EAChRC,UAAU,GAAG,CAAC,QAAQ,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,mBAAmB,IAAIC,WAAW,QAAQ,wBAAwB;AACzE,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,WAAW,EAAEC,UAAU,QAAQ,wBAAwB;AACrG,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AAEtD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;EACpD,IAAIC,WAAW;EAEf,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,cAAc,GAAGZ,KAAK,CAACY,cAAc;IACrCC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,YAAY,GAAGf,KAAK,CAACe,YAAY;IACjCC,YAAY,GAAGhB,KAAK,CAACgB,YAAY;IACjCC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,iBAAiB,GAAGlB,KAAK,CAACkB,iBAAiB;IAC3CC,iBAAiB,GAAGnB,KAAK,CAACmB,iBAAiB;IAC3CC,SAAS,GAAG7C,wBAAwB,CAACyB,KAAK,EAAExB,SAAS,CAAC;EAE1D,IAAI6C,SAAS,GAAGxB,SAAS,CAACQ,QAAQ,CAAC;EAEnC,IAAIiB,iBAAiB,GAAG5C,KAAK,CAAC6C,UAAU,CAACtC,WAAW,CAAC;IACjDuC,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;IAC7BC,QAAQ,GAAGJ,iBAAiB,CAACI,QAAQ;IACrCC,eAAe,GAAGL,iBAAiB,CAACf,QAAQ;IAC5CqB,gBAAgB,GAAGN,iBAAiB,CAACM,gBAAgB;IACrDC,SAAS,GAAGP,iBAAiB,CAACO,SAAS;IACvCC,YAAY,GAAGR,iBAAiB,CAACQ,YAAY;IAC7CC,eAAe,GAAGT,iBAAiB,CAACZ,QAAQ;IAC5CsB,iBAAiB,GAAGV,iBAAiB,CAACX,UAAU;IAChDsB,WAAW,GAAGX,iBAAiB,CAACW,WAAW;IAC3CC,YAAY,GAAGZ,iBAAiB,CAACY,YAAY;IAC7CC,QAAQ,GAAGb,iBAAiB,CAACa,QAAQ;EAEzC,IAAIC,kBAAkB,GAAG1D,KAAK,CAAC6C,UAAU,CAACzB,cAAc,CAAC;IACrDuC,0BAA0B,GAAGD,kBAAkB,CAACC,0BAA0B;EAE9E,IAAIC,kBAAkB,GAAG5D,KAAK,CAAC6C,UAAU,CAAC7B,eAAe,CAAC;IACtD6C,YAAY,GAAGD,kBAAkB,CAACC,YAAY;EAElD,IAAIC,aAAa,GAAG7C,WAAW,CAAC,CAAC;EACjC,IAAI8C,gBAAgB,GAAG,EAAE,CAACC,MAAM,CAAClB,SAAS,EAAE,UAAU,CAAC;EACvD,IAAImB,cAAc,GAAGhB,eAAe,IAAIpB,QAAQ;EAChD,IAAIqC,UAAU,GAAGlE,KAAK,CAACmE,MAAM,CAAC,CAAC;EAC/B,IAAIC,QAAQ,GAAGpE,KAAK,CAACmE,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI3C,OAAO,EAAE;IACpDzB,OAAO,CAAC,KAAK,EAAE,2CAA2C,CAAC;EAC7D,CAAC,CAAC;;EAGF,IAAIqE,cAAc,GAAGxC,QAAQ,IAAIqB,eAAe;EAChD,IAAIoB,gBAAgB,GAAGxC,UAAU,IAAIqB,iBAAiB,CAAC,CAAC;;EAExD,IAAIoB,UAAU,GAAG1B,QAAQ,CAAC2B,QAAQ,CAAChD,QAAQ,CAAC;EAC5C,IAAIiD,IAAI,GAAG,CAAC1B,gBAAgB,IAAIwB,UAAU,CAAC,CAAC;;EAE5C,IAAIG,gBAAgB,GAAGhB,YAAY,CAACT,YAAY,EAAEzB,QAAQ,CAAC,CAAC,CAAC;;EAE7D,IAAImD,UAAU,GAAGnE,SAAS,CAACgB,QAAQ,EAAEsC,cAAc,EAAEzB,iBAAiB,EAAEC,iBAAiB,CAAC;IACtFsC,MAAM,GAAGD,UAAU,CAACC,MAAM;IAC1BC,WAAW,GAAGnF,wBAAwB,CAACiF,UAAU,EAAE/E,UAAU,CAAC,CAAC,CAAC;;EAGpE,IAAIkF,eAAe,GAAGjF,KAAK,CAACkF,QAAQ,CAAC,KAAK,CAAC;IACvCC,gBAAgB,GAAGvF,cAAc,CAACqF,eAAe,EAAE,CAAC,CAAC;IACrDG,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAE3C,IAAIG,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,SAAS,EAAE;IACpE,IAAI,CAACtB,cAAc,EAAE;MACnBoB,iBAAiB,CAACE,SAAS,CAAC;IAC9B;EACF,CAAC;EAED,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,QAAQ,EAAE;IACjEH,qBAAqB,CAAC,IAAI,CAAC;IAC3BjD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;MACvEqD,GAAG,EAAE/D,QAAQ;MACb8D,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,IAAIE,oBAAoB,GAAG,SAASA,oBAAoBA,CAACF,QAAQ,EAAE;IACjEH,qBAAqB,CAAC,KAAK,CAAC;IAC5BhD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;MACvEoD,GAAG,EAAE/D,QAAQ;MACb8D,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,IAAIG,YAAY,GAAG5F,KAAK,CAAC6F,OAAO,CAAC,YAAY;IAC3C,IAAId,MAAM,EAAE;MACV,OAAOA,MAAM;IACf;IAEA,IAAIhC,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAOqC,cAAc,IAAIvB,YAAY,CAAC,CAACV,SAAS,CAAC,EAAExB,QAAQ,CAAC;IAC9D;IAEA,OAAO,KAAK;EACd,CAAC,EAAE,CAACoB,IAAI,EAAEgC,MAAM,EAAE5B,SAAS,EAAEiC,cAAc,EAAEzD,QAAQ,EAAEkC,YAAY,CAAC,CAAC,CAAC,CAAC;;EAEvE,IAAIiC,cAAc,GAAGjF,iBAAiB,CAACiD,aAAa,CAACiC,MAAM,CAAC,CAAC,CAAC;EAC9D;;EAEA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,CAAC,EAAE;IAC1D;IACA,IAAIhC,cAAc,EAAE;MAClB;IACF;IAEA1B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;MACvEmD,GAAG,EAAE/D,QAAQ;MACb8D,QAAQ,EAAEQ;IACZ,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIlD,IAAI,KAAK,QAAQ,EAAE;MACrBS,YAAY,CAAC7B,QAAQ,EAAE,CAAC+C,UAAU,CAAC;IACrC;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIwB,iBAAiB,GAAG1F,eAAe,CAAC,UAAU2F,IAAI,EAAE;IACtD/D,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACxB,YAAY,CAACuF,IAAI,CAAC,CAAC;IAC7E5C,WAAW,CAAC4C,IAAI,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,UAAU,EAAE;IACnE,IAAItD,IAAI,KAAK,QAAQ,EAAE;MACrBS,YAAY,CAAC7B,QAAQ,EAAE0E,UAAU,CAAC;IACpC;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C7C,QAAQ,CAAC9B,QAAQ,CAAC;EACpB,CAAC,CAAC,CAAC;;EAGH,IAAI4E,OAAO,GAAG5D,SAAS,IAAI,EAAE,CAACqB,MAAM,CAACrB,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;;EAE3D,IAAI6D,SAAS,GAAG,aAAaxG,KAAK,CAACyG,aAAa,CAAC,KAAK,EAAE/G,QAAQ,CAAC;IAC/DgH,IAAI,EAAE,UAAU;IAChBlF,KAAK,EAAEsE,cAAc;IACrBrE,SAAS,EAAE,EAAE,CAACuC,MAAM,CAACD,gBAAgB,EAAE,QAAQ,CAAC;IAChD4C,QAAQ,EAAE1C,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC;IACpC2C,GAAG,EAAE1C,UAAU;IACfxC,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,IAAI;IAC/C,cAAc,EAAEwB,gBAAgB,IAAIP,SAAS,GAAG,IAAI,GAAGA,SAAS;IAChE,eAAe,EAAEiC,IAAI;IACrB,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE2B,OAAO;IACxB,eAAe,EAAEtC,cAAc;IAC/B7B,OAAO,EAAE4D,oBAAoB;IAC7Ba,OAAO,EAAEP;EACX,CAAC,EAAEtB,WAAW,CAAC,EAAEtD,KAAK,EAAE,aAAa1B,KAAK,CAACyG,aAAa,CAAC/F,IAAI,EAAE;IAC7DoG,IAAI,EAAE/D,IAAI,KAAK,YAAY,GAAG0B,gBAAgB,GAAG,IAAI;IACrDnD,KAAK,EAAE3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDyF,MAAM,EAAEnC,IAAI;MACZ;MACAoC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,EAAE,aAAahH,KAAK,CAACyG,aAAa,CAAC,GAAG,EAAE;IACvChF,SAAS,EAAE,EAAE,CAACuC,MAAM,CAACD,gBAAgB,EAAE,QAAQ;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEN,IAAIkD,cAAc,GAAGjH,KAAK,CAACmE,MAAM,CAACpB,IAAI,CAAC;EAEvC,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrBkE,cAAc,CAACC,OAAO,GAAGpD,aAAa,CAACiC,MAAM,GAAG,CAAC,GAAG,UAAU,GAAGhD,IAAI;EACvE;EAEA,IAAI,CAACG,gBAAgB,EAAE;IACrB,IAAIiE,WAAW,GAAGF,cAAc,CAACC,OAAO,CAAC,CAAC;IAC1C;;IAEAV,SAAS,GAAG,aAAaxG,KAAK,CAACyG,aAAa,CAAChG,YAAY,EAAE;MACzDsC,IAAI,EAAEoE,WAAW;MACjBrE,SAAS,EAAEiB,gBAAgB;MAC3BqD,OAAO,EAAE,CAACtF,kBAAkB,IAAI8C,IAAI,IAAI7B,IAAI,KAAK,QAAQ;MACzDb,cAAc,EAAEA,cAAc;MAC9BC,WAAW,EAAEA,WAAW;MACxBkF,KAAK,EAAE,aAAarH,KAAK,CAACyG,aAAa,CAACnG,mBAAmB,CAAC;MAAA,EAC1D;QACAyC,IAAI,EAAEoE,WAAW,KAAK,YAAY,GAAG,UAAU,GAAGA;MACpD,CAAC,EAAE,aAAanH,KAAK,CAACyG,aAAa,CAACrG,WAAW,EAAE;QAC/CkH,EAAE,EAAEf,OAAO;QACXK,GAAG,EAAExC;MACP,CAAC,EAAErC,QAAQ,CAAC,CAAC;MACbF,QAAQ,EAAEoC,cAAc;MACxBsD,eAAe,EAAEnB;IACnB,CAAC,EAAEI,SAAS,CAAC;EACf,CAAC,CAAC;;EAGF,IAAIgB,QAAQ,GAAG,aAAaxH,KAAK,CAACyG,aAAa,CAACvG,QAAQ,CAACuH,IAAI,EAAE/H,QAAQ,CAAC;IACtEgH,IAAI,EAAE;EACR,CAAC,EAAEhE,SAAS,EAAE;IACZgF,SAAS,EAAE,IAAI;IACflG,KAAK,EAAEA,KAAK;IACZC,SAAS,EAAExB,UAAU,CAAC8D,gBAAgB,EAAE,EAAE,CAACC,MAAM,CAACD,gBAAgB,EAAE,GAAG,CAAC,CAACC,MAAM,CAACjB,IAAI,CAAC,EAAEtB,SAAS,GAAGF,WAAW,GAAG,CAAC,CAAC,EAAE9B,eAAe,CAAC8B,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACD,gBAAgB,EAAE,OAAO,CAAC,EAAEa,IAAI,CAAC,EAAEnF,eAAe,CAAC8B,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACD,gBAAgB,EAAE,SAAS,CAAC,EAAE6B,YAAY,CAAC,EAAEnG,eAAe,CAAC8B,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACD,gBAAgB,EAAE,WAAW,CAAC,EAAEc,gBAAgB,CAAC,EAAEpF,eAAe,CAAC8B,WAAW,EAAE,EAAE,CAACyC,MAAM,CAACD,gBAAgB,EAAE,WAAW,CAAC,EAAEE,cAAc,CAAC,EAAE1C,WAAW,CAAC,CAAC;IACldc,YAAY,EAAEmD,oBAAoB;IAClClD,YAAY,EAAEqD;EAChB,CAAC,CAAC,EAAEa,SAAS,EAAE,CAACtD,gBAAgB,IAAI,aAAalD,KAAK,CAACyG,aAAa,CAAC3F,iBAAiB,EAAE;IACtFwG,EAAE,EAAEf,OAAO;IACX3B,IAAI,EAAEA,IAAI;IACV+C,OAAO,EAAE7D;EACX,CAAC,EAAE/B,QAAQ,CAAC,CAAC;EAEb,IAAI4B,0BAA0B,EAAE;IAC9B6D,QAAQ,GAAG7D,0BAA0B,CAAC6D,QAAQ,EAAElG,KAAK,EAAE;MACrDsG,QAAQ,EAAE/C,gBAAgB;MAC1BE,MAAM,EAAEa,YAAY;MACpBhB,IAAI,EAAEA,IAAI;MACV/C,QAAQ,EAAEoC;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;;EAGF,OAAO,aAAajE,KAAK,CAACyG,aAAa,CAACnG,mBAAmB,EAAE;IAC3DiD,WAAW,EAAE2C,iBAAiB;IAC9BnD,IAAI,EAAEA,IAAI,KAAK,YAAY,GAAG,UAAU,GAAGA,IAAI;IAC/Cf,QAAQ,EAAEwC,cAAc;IACxBvC,UAAU,EAAEwC;EACd,CAAC,EAAE+C,QAAQ,CAAC;AACd,CAAC;AAED,eAAe,SAASK,OAAOA,CAACvG,KAAK,EAAE;EACrC,IAAIK,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBI,QAAQ,GAAGT,KAAK,CAACS,QAAQ;EAC7B,IAAI+F,gBAAgB,GAAG7G,WAAW,CAACU,QAAQ,CAAC;EAC5C,IAAIoG,SAAS,GAAG1H,aAAa,CAAC0B,QAAQ,EAAE+F,gBAAgB,CAAC,CAAC,CAAC;;EAE3D,IAAIE,OAAO,GAAG9G,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE5BlB,KAAK,CAACiI,SAAS,CAAC,YAAY;IAC1B,IAAID,OAAO,EAAE;MACXA,OAAO,CAACE,YAAY,CAACvG,QAAQ,EAAEmG,gBAAgB,CAAC;MAChD,OAAO,YAAY;QACjBE,OAAO,CAACG,cAAc,CAACxG,QAAQ,EAAEmG,gBAAgB,CAAC;MACpD,CAAC;IACH;EACF,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EACtB,IAAIM,UAAU,CAAC,CAAC;;EAEhB,IAAIJ,OAAO,EAAE;IACXI,UAAU,GAAGL,SAAS;EACxB,CAAC,MAAM;IACLK,UAAU,GAAG,aAAapI,KAAK,CAACyG,aAAa,CAACpF,eAAe,EAAEC,KAAK,EAAEyG,SAAS,CAAC;EAClF;EAEA,OAAO,aAAa/H,KAAK,CAACyG,aAAa,CAAC1F,kBAAkB,CAACsH,QAAQ,EAAE;IACnEC,KAAK,EAAER;EACT,CAAC,EAAEM,UAAU,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}