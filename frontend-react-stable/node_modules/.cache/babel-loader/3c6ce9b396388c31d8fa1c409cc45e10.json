{"ast": null, "code": "/**\n * 任务管理API服务\n * 提供异步任务的创建、查询、取消等功能\n */\n\nimport api from './api';\n\n// 任务状态常量\nexport const TASK_STATUS = {\n  PENDING: 'pending',\n  RUNNING: 'running',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  CANCELLED: 'cancelled'\n};\n\n// 任务类型常量\nexport const TASK_TYPE = {\n  TRAINING: 'training',\n  PREDICTION: 'prediction',\n  DATA_CLEANING: 'data_cleaning'\n};\n\n// 任务接口定义\n\n/**\n * 获取所有任务列表\n */\nexport const getAllTasks = async () => {\n  try {\n    const response = await api.get('/tasks/tasks');\n    return response.data;\n  } catch (error) {\n    console.error('获取任务列表失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 获取特定任务的状态\n * @param taskId - 任务ID\n */\nexport const getTaskStatus = async taskId => {\n  try {\n    const response = await api.get(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`获取任务 ${taskId} 状态失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 取消任务\n * @param taskId - 任务ID\n */\nexport const cancelTask = async taskId => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`取消任务 ${taskId} 失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 获取正在运行的任务\n */\nexport const getRunningTasks = async () => {\n  try {\n    const response = await api.get('/tasks/tasks/running');\n    return response.data;\n  } catch (error) {\n    console.error('获取运行中任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型训练\n * @param formData - 训练参数\n */\nexport const startTrainingAsync = async formData => {\n  try {\n    const response = await api.post('/model_training/train_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步训练失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型预测\n * @param formData - 预测参数\n */\nexport const startPredictionAsync = async formData => {\n  try {\n    const response = await api.post('/model_prediction/predict_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动多模型预测\n * @param formData - 多模型预测参数\n */\nexport const startMultiPredictionAsync = async formData => {\n  try {\n    const response = await api.post('/model_prediction/predict_multi_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动多模型异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 轮询任务状态直到完成\n * @param taskId - 任务ID\n * @param onProgress - 进度回调函数\n * @param interval - 轮询间隔（毫秒）\n * @returns 任务完成时的结果\n */\nexport const pollTaskStatus = async (taskId, onProgress, interval = 2000) => {\n  return new Promise((resolve, reject) => {\n    const poll = async () => {\n      try {\n        const response = await getTaskStatus(taskId);\n        const task = response.task;\n        if (!task) {\n          reject(new Error('任务不存在'));\n          return;\n        }\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 检查任务状态\n        if (task.status === TASK_STATUS.COMPLETED) {\n          resolve(task);\n        } else if (task.status === TASK_STATUS.FAILED) {\n          reject(new Error(task.error || '任务执行失败'));\n        } else if (task.status === TASK_STATUS.CANCELLED) {\n          reject(new Error('任务已被取消'));\n        } else {\n          // 继续轮询\n          setTimeout(poll, interval);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    poll();\n  });\n};\n\n/**\n * 获取已完成的任务\n */\nexport const getCompletedTasks = async () => {\n  try {\n    const response = await api.get('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('获取已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 删除单个已完成的任务\n */\nexport const deleteSingleTask = async taskId => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}/delete`);\n    return response.data;\n  } catch (error) {\n    console.error('删除任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 清空所有已完成的任务\n */\nexport const clearCompletedTasks = async () => {\n  try {\n    const response = await api.delete('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('清空已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 格式化任务状态显示文本\n * @param status - 任务状态\n */\nexport const formatTaskStatus = status => {\n  const statusMap = {\n    [TASK_STATUS.PENDING]: '等待中',\n    [TASK_STATUS.RUNNING]: '运行中',\n    [TASK_STATUS.COMPLETED]: '已完成',\n    [TASK_STATUS.FAILED]: '失败',\n    [TASK_STATUS.CANCELLED]: '已取消'\n  };\n  return statusMap[status] || status;\n};\n\n/**\n * 格式化任务类型显示文本\n * @param type - 任务类型\n */\nexport const formatTaskType = type => {\n  const typeMap = {\n    [TASK_TYPE.TRAINING]: '模型训练',\n    [TASK_TYPE.PREDICTION]: '模型预测',\n    [TASK_TYPE.DATA_CLEANING]: '流量分析'\n  };\n  return typeMap[type] || type;\n};\n\n/**\n * 获取任务状态对应的颜色\n * @param status - 任务状态\n */\nexport const getTaskStatusColor = status => {\n  const colorMap = {\n    [TASK_STATUS.PENDING]: 'default',\n    [TASK_STATUS.RUNNING]: 'processing',\n    [TASK_STATUS.COMPLETED]: 'success',\n    [TASK_STATUS.FAILED]: 'error',\n    [TASK_STATUS.CANCELLED]: 'warning'\n  };\n  return colorMap[status] || 'default';\n};\n\n/**\n * 计算任务运行时间\n * @param startTime - 开始时间\n * @param endTime - 结束时间（可选）\n */\nexport const calculateTaskDuration = (startTime, endTime) => {\n  if (!startTime) return '未知';\n  const start = new Date(startTime);\n  const end = endTime ? new Date(endTime) : new Date();\n  const duration = Math.floor((end.getTime() - start.getTime()) / 1000); // 秒\n\n  if (duration < 60) {\n    return `${duration}秒`;\n  } else if (duration < 3600) {\n    return `${Math.floor(duration / 60)}分${duration % 60}秒`;\n  } else {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor(duration % 3600 / 60);\n    return `${hours}小时${minutes}分`;\n  }\n};\nconst taskApi = {\n  getAllTasks,\n  getTaskStatus,\n  cancelTask,\n  getRunningTasks,\n  getCompletedTasks,\n  deleteSingleTask,\n  clearCompletedTasks,\n  startTrainingAsync,\n  startPredictionAsync,\n  startMultiPredictionAsync,\n  pollTaskStatus,\n  formatTaskStatus,\n  formatTaskType,\n  getTaskStatusColor,\n  calculateTaskDuration,\n  TASK_STATUS,\n  TASK_TYPE\n};\nexport default taskApi;", "map": {"version": 3, "names": ["api", "TASK_STATUS", "PENDING", "RUNNING", "COMPLETED", "FAILED", "CANCELLED", "TASK_TYPE", "TRAINING", "PREDICTION", "DATA_CLEANING", "getAllTasks", "response", "get", "data", "error", "console", "getTaskStatus", "taskId", "cancelTask", "delete", "getRunningTasks", "startTrainingAsync", "formData", "post", "headers", "startPredictionAsync", "startMultiPredictionAsync", "pollTaskStatus", "onProgress", "interval", "Promise", "resolve", "reject", "poll", "task", "Error", "status", "setTimeout", "getCompletedTasks", "deleteSingleTask", "clearCompletedTasks", "formatTaskStatus", "statusMap", "formatTaskType", "type", "typeMap", "getTaskStatusColor", "colorMap", "calculateTaskDuration", "startTime", "endTime", "start", "Date", "end", "duration", "Math", "floor", "getTime", "hours", "minutes", "taskApi"], "sources": ["/home/<USER>/frontend-react-stable/src/services/taskApi.ts"], "sourcesContent": ["/**\n * 任务管理API服务\n * 提供异步任务的创建、查询、取消等功能\n */\n\nimport api from './api';\n\n// 任务状态常量\nexport const TASK_STATUS = {\n  PENDING: 'pending',\n  RUNNING: 'running',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  CANCELLED: 'cancelled'\n} as const;\n\n// 任务类型常量\nexport const TASK_TYPE = {\n  TRAINING: 'training',\n  PREDICTION: 'prediction',\n  DATA_CLEANING: 'data_cleaning'\n} as const;\n\nexport type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];\nexport type TaskType = typeof TASK_TYPE[keyof typeof TASK_TYPE];\n\n// 任务接口定义\nexport interface Task {\n  task_id: string;\n  task_type: TaskType;\n  status: TaskStatus;\n  progress?: number;\n  created_at: string;\n  updated_at?: string;\n  started_at?: string;\n  completed_at?: string;\n  current_step?: string;\n  total_steps?: number;\n  message?: string;\n  error?: string;\n  params?: any;\n  result?: any;\n}\n\nexport interface TaskResponse {\n  success: boolean;\n  task_id?: string;\n  task?: Task;\n  tasks?: Task[];\n  total?: number;\n  count?: number;\n  message?: string;\n}\n\n/**\n * 获取所有任务列表\n */\nexport const getAllTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks');\n    return response.data;\n  } catch (error) {\n    console.error('获取任务列表失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 获取特定任务的状态\n * @param taskId - 任务ID\n */\nexport const getTaskStatus = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.get(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`获取任务 ${taskId} 状态失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 取消任务\n * @param taskId - 任务ID\n */\nexport const cancelTask = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`取消任务 ${taskId} 失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 获取正在运行的任务\n */\nexport const getRunningTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks/running');\n    return response.data;\n  } catch (error) {\n    console.error('获取运行中任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型训练\n * @param formData - 训练参数\n */\nexport const startTrainingAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_training/train_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步训练失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型预测\n * @param formData - 预测参数\n */\nexport const startPredictionAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_prediction/predict_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动多模型预测\n * @param formData - 多模型预测参数\n */\nexport const startMultiPredictionAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_prediction/predict_multi_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动多模型异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 轮询任务状态直到完成\n * @param taskId - 任务ID\n * @param onProgress - 进度回调函数\n * @param interval - 轮询间隔（毫秒）\n * @returns 任务完成时的结果\n */\nexport const pollTaskStatus = async (\n  taskId: string, \n  onProgress?: (task: Task) => void, \n  interval: number = 2000\n): Promise<Task> => {\n  return new Promise((resolve, reject) => {\n    const poll = async () => {\n      try {\n        const response = await getTaskStatus(taskId);\n        const task = response.task;\n        \n        if (!task) {\n          reject(new Error('任务不存在'));\n          return;\n        }\n        \n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n        \n        // 检查任务状态\n        if (task.status === TASK_STATUS.COMPLETED) {\n          resolve(task);\n        } else if (task.status === TASK_STATUS.FAILED) {\n          reject(new Error(task.error || '任务执行失败'));\n        } else if (task.status === TASK_STATUS.CANCELLED) {\n          reject(new Error('任务已被取消'));\n        } else {\n          // 继续轮询\n          setTimeout(poll, interval);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    \n    poll();\n  });\n};\n\n/**\n * 获取已完成的任务\n */\nexport const getCompletedTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('获取已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 删除单个已完成的任务\n */\nexport const deleteSingleTask = async (taskId: string): Promise<{ success: boolean; message: string; deleted_task_id: string }> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}/delete`);\n    return response.data;\n  } catch (error) {\n    console.error('删除任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 清空所有已完成的任务\n */\nexport const clearCompletedTasks = async (): Promise<{ success: boolean; message: string; cleared_count: number; cleared_task_ids: string[] }> => {\n  try {\n    const response = await api.delete('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('清空已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 格式化任务状态显示文本\n * @param status - 任务状态\n */\nexport const formatTaskStatus = (status: TaskStatus): string => {\n  const statusMap = {\n    [TASK_STATUS.PENDING]: '等待中',\n    [TASK_STATUS.RUNNING]: '运行中',\n    [TASK_STATUS.COMPLETED]: '已完成',\n    [TASK_STATUS.FAILED]: '失败',\n    [TASK_STATUS.CANCELLED]: '已取消'\n  };\n  return statusMap[status] || status;\n};\n\n/**\n * 格式化任务类型显示文本\n * @param type - 任务类型\n */\nexport const formatTaskType = (type: TaskType): string => {\n  const typeMap = {\n    [TASK_TYPE.TRAINING]: '模型训练',\n    [TASK_TYPE.PREDICTION]: '模型预测',\n    [TASK_TYPE.DATA_CLEANING]: '流量分析'\n  };\n  return typeMap[type] || type;\n};\n\n/**\n * 获取任务状态对应的颜色\n * @param status - 任务状态\n */\nexport const getTaskStatusColor = (status: TaskStatus): string => {\n  const colorMap = {\n    [TASK_STATUS.PENDING]: 'default',\n    [TASK_STATUS.RUNNING]: 'processing',\n    [TASK_STATUS.COMPLETED]: 'success',\n    [TASK_STATUS.FAILED]: 'error',\n    [TASK_STATUS.CANCELLED]: 'warning'\n  };\n  return colorMap[status] || 'default';\n};\n\n/**\n * 计算任务运行时间\n * @param startTime - 开始时间\n * @param endTime - 结束时间（可选）\n */\nexport const calculateTaskDuration = (startTime?: string, endTime?: string): string => {\n  if (!startTime) return '未知';\n  \n  const start = new Date(startTime);\n  const end = endTime ? new Date(endTime) : new Date();\n  const duration = Math.floor((end.getTime() - start.getTime()) / 1000); // 秒\n  \n  if (duration < 60) {\n    return `${duration}秒`;\n  } else if (duration < 3600) {\n    return `${Math.floor(duration / 60)}分${duration % 60}秒`;\n  } else {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    return `${hours}小时${minutes}分`;\n  }\n};\n\nconst taskApi = {\n  getAllTasks,\n  getTaskStatus,\n  cancelTask,\n  getRunningTasks,\n  getCompletedTasks,\n  deleteSingleTask,\n  clearCompletedTasks,\n  startTrainingAsync,\n  startPredictionAsync,\n  startMultiPredictionAsync,\n  pollTaskStatus,\n  formatTaskStatus,\n  formatTaskType,\n  getTaskStatusColor,\n  calculateTaskDuration,\n  TASK_STATUS,\n  TASK_TYPE\n};\n\nexport default taskApi;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAU;;AAEV;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE;AACjB,CAAU;;AAKV;;AA4BA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAmC;EAC5D,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,cAAc,CAAC;IAC9C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAG,MAAOC,MAAc,IAA4B;EAC5E,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,eAAeK,MAAM,EAAE,CAAC;IACvD,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,QAAQG,MAAM,QAAQ,EAAEH,KAAK,CAAC;IAC5C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMI,UAAU,GAAG,MAAOD,MAAc,IAA4B;EACzE,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMZ,GAAG,CAACoB,MAAM,CAAC,eAAeF,MAAM,EAAE,CAAC;IAC1D,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,QAAQG,MAAM,MAAM,EAAEH,KAAK,CAAC;IAC1C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMM,eAAe,GAAG,MAAAA,CAAA,KAAmC;EAChE,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,sBAAsB,CAAC;IACtD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMO,kBAAkB,GAAG,MAAOC,QAAkB,IAA4B;EACrF,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,IAAI,CAAC,6BAA6B,EAAED,QAAQ,EAAE;MACvEE,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMW,oBAAoB,GAAG,MAAOH,QAAkB,IAA4B;EACvF,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,IAAI,CAAC,iCAAiC,EAAED,QAAQ,EAAE;MAC3EE,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMY,yBAAyB,GAAG,MAAOJ,QAAkB,IAA4B;EAC5F,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMZ,GAAG,CAACwB,IAAI,CAAC,uCAAuC,EAAED,QAAQ,EAAE;MACjFE,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,cAAc,GAAG,MAAAA,CAC5BV,MAAc,EACdW,UAAiC,EACjCC,QAAgB,GAAG,IAAI,KACL;EAClB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,IAAI,GAAG,MAAAA,CAAA,KAAY;MACvB,IAAI;QACF,MAAMtB,QAAQ,GAAG,MAAMK,aAAa,CAACC,MAAM,CAAC;QAC5C,MAAMiB,IAAI,GAAGvB,QAAQ,CAACuB,IAAI;QAE1B,IAAI,CAACA,IAAI,EAAE;UACTF,MAAM,CAAC,IAAIG,KAAK,CAAC,OAAO,CAAC,CAAC;UAC1B;QACF;;QAEA;QACA,IAAIP,UAAU,EAAE;UACdA,UAAU,CAACM,IAAI,CAAC;QAClB;;QAEA;QACA,IAAIA,IAAI,CAACE,MAAM,KAAKpC,WAAW,CAACG,SAAS,EAAE;UACzC4B,OAAO,CAACG,IAAI,CAAC;QACf,CAAC,MAAM,IAAIA,IAAI,CAACE,MAAM,KAAKpC,WAAW,CAACI,MAAM,EAAE;UAC7C4B,MAAM,CAAC,IAAIG,KAAK,CAACD,IAAI,CAACpB,KAAK,IAAI,QAAQ,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAIoB,IAAI,CAACE,MAAM,KAAKpC,WAAW,CAACK,SAAS,EAAE;UAChD2B,MAAM,CAAC,IAAIG,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC,MAAM;UACL;UACAE,UAAU,CAACJ,IAAI,EAAEJ,QAAQ,CAAC;QAC5B;MACF,CAAC,CAAC,OAAOf,KAAK,EAAE;QACdkB,MAAM,CAAClB,KAAK,CAAC;MACf;IACF,CAAC;IAEDmB,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAmC;EAClE,IAAI;IACF,MAAM3B,QAAQ,GAAG,MAAMZ,GAAG,CAACa,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyB,gBAAgB,GAAG,MAAOtB,MAAc,IAA8E;EACjI,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMZ,GAAG,CAACoB,MAAM,CAAC,eAAeF,MAAM,SAAS,CAAC;IACjE,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0B,mBAAmB,GAAG,MAAAA,CAAA,KAA+G;EAChJ,IAAI;IACF,MAAM7B,QAAQ,GAAG,MAAMZ,GAAG,CAACoB,MAAM,CAAC,kBAAkB,CAAC;IACrD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAM2B,gBAAgB,GAAIL,MAAkB,IAAa;EAC9D,MAAMM,SAAS,GAAG;IAChB,CAAC1C,WAAW,CAACC,OAAO,GAAG,KAAK;IAC5B,CAACD,WAAW,CAACE,OAAO,GAAG,KAAK;IAC5B,CAACF,WAAW,CAACG,SAAS,GAAG,KAAK;IAC9B,CAACH,WAAW,CAACI,MAAM,GAAG,IAAI;IAC1B,CAACJ,WAAW,CAACK,SAAS,GAAG;EAC3B,CAAC;EACD,OAAOqC,SAAS,CAACN,MAAM,CAAC,IAAIA,MAAM;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMO,cAAc,GAAIC,IAAc,IAAa;EACxD,MAAMC,OAAO,GAAG;IACd,CAACvC,SAAS,CAACC,QAAQ,GAAG,MAAM;IAC5B,CAACD,SAAS,CAACE,UAAU,GAAG,MAAM;IAC9B,CAACF,SAAS,CAACG,aAAa,GAAG;EAC7B,CAAC;EACD,OAAOoC,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,kBAAkB,GAAIV,MAAkB,IAAa;EAChE,MAAMW,QAAQ,GAAG;IACf,CAAC/C,WAAW,CAACC,OAAO,GAAG,SAAS;IAChC,CAACD,WAAW,CAACE,OAAO,GAAG,YAAY;IACnC,CAACF,WAAW,CAACG,SAAS,GAAG,SAAS;IAClC,CAACH,WAAW,CAACI,MAAM,GAAG,OAAO;IAC7B,CAACJ,WAAW,CAACK,SAAS,GAAG;EAC3B,CAAC;EACD,OAAO0C,QAAQ,CAACX,MAAM,CAAC,IAAI,SAAS;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,qBAAqB,GAAGA,CAACC,SAAkB,EAAEC,OAAgB,KAAa;EACrF,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAME,KAAK,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC;EACjC,MAAMI,GAAG,GAAGH,OAAO,GAAG,IAAIE,IAAI,CAACF,OAAO,CAAC,GAAG,IAAIE,IAAI,CAAC,CAAC;EACpD,MAAME,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGN,KAAK,CAACM,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;;EAEvE,IAAIH,QAAQ,GAAG,EAAE,EAAE;IACjB,OAAO,GAAGA,QAAQ,GAAG;EACvB,CAAC,MAAM,IAAIA,QAAQ,GAAG,IAAI,EAAE;IAC1B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,IAAIA,QAAQ,GAAG,EAAE,GAAG;EACzD,CAAC,MAAM;IACL,MAAMI,KAAK,GAAGH,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,IAAI,CAAC;IACzC,MAAMK,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAAEF,QAAQ,GAAG,IAAI,GAAI,EAAE,CAAC;IAClD,OAAO,GAAGI,KAAK,KAAKC,OAAO,GAAG;EAChC;AACF,CAAC;AAED,MAAMC,OAAO,GAAG;EACdlD,WAAW;EACXM,aAAa;EACbE,UAAU;EACVE,eAAe;EACfkB,iBAAiB;EACjBC,gBAAgB;EAChBC,mBAAmB;EACnBnB,kBAAkB;EAClBI,oBAAoB;EACpBC,yBAAyB;EACzBC,cAAc;EACdc,gBAAgB;EAChBE,cAAc;EACdG,kBAAkB;EAClBE,qBAAqB;EACrBhD,WAAW;EACXM;AACF,CAAC;AAED,eAAesD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}