{"ast": null, "code": "import React, { Children } from 'react';\nimport { TransitionGroup } from 'react-transition-group';\nimport PropTypes from 'prop-types';\nimport AnimateGroupChild from './AnimateGroupChild';\nfunction AnimateGroup(props) {\n  var component = props.component,\n    children = props.children,\n    appear = props.appear,\n    enter = props.enter,\n    leave = props.leave;\n  return /*#__PURE__*/React.createElement(TransitionGroup, {\n    component: component\n  }, Children.map(children, function (child, index) {\n    return /*#__PURE__*/React.createElement(AnimateGroupChild, {\n      appearOptions: appear,\n      enterOptions: enter,\n      leaveOptions: leave,\n      key: \"child-\".concat(index) // eslint-disable-line\n    }, child);\n  }));\n}\nAnimateGroup.propTypes = {\n  appear: PropTypes.object,\n  enter: PropTypes.object,\n  leave: PropTypes.object,\n  children: PropTypes.oneOfType([PropTypes.array, PropTypes.element]),\n  component: PropTypes.any\n};\nAnimateGroup.defaultProps = {\n  component: 'span'\n};\nexport default AnimateGroup;", "map": {"version": 3, "names": ["React", "Children", "TransitionGroup", "PropTypes", "AnimateGroupChild", "AnimateGroup", "props", "component", "children", "appear", "enter", "leave", "createElement", "map", "child", "index", "appearOptions", "enterOptions", "leaveOptions", "key", "concat", "propTypes", "object", "oneOfType", "array", "element", "any", "defaultProps"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-smooth/es6/AnimateGroup.js"], "sourcesContent": ["import React, { Children } from 'react';\nimport { TransitionGroup } from 'react-transition-group';\nimport PropTypes from 'prop-types';\nimport AnimateGroupChild from './AnimateGroupChild';\nfunction AnimateGroup(props) {\n  var component = props.component,\n    children = props.children,\n    appear = props.appear,\n    enter = props.enter,\n    leave = props.leave;\n  return /*#__PURE__*/React.createElement(TransitionGroup, {\n    component: component\n  }, Children.map(children, function (child, index) {\n    return /*#__PURE__*/React.createElement(AnimateGroupChild, {\n      appearOptions: appear,\n      enterOptions: enter,\n      leaveOptions: leave,\n      key: \"child-\".concat(index) // eslint-disable-line\n    }, child);\n  }));\n}\nAnimateGroup.propTypes = {\n  appear: PropTypes.object,\n  enter: PropTypes.object,\n  leave: PropTypes.object,\n  children: PropTypes.oneOfType([PropTypes.array, PropTypes.element]),\n  component: PropTypes.any\n};\nAnimateGroup.defaultProps = {\n  component: 'span'\n};\nexport default AnimateGroup;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,KAAK,GAAGL,KAAK,CAACK,KAAK;EACrB,OAAO,aAAaX,KAAK,CAACY,aAAa,CAACV,eAAe,EAAE;IACvDK,SAAS,EAAEA;EACb,CAAC,EAAEN,QAAQ,CAACY,GAAG,CAACL,QAAQ,EAAE,UAAUM,KAAK,EAAEC,KAAK,EAAE;IAChD,OAAO,aAAaf,KAAK,CAACY,aAAa,CAACR,iBAAiB,EAAE;MACzDY,aAAa,EAAEP,MAAM;MACrBQ,YAAY,EAAEP,KAAK;MACnBQ,YAAY,EAAEP,KAAK;MACnBQ,GAAG,EAAE,QAAQ,CAACC,MAAM,CAACL,KAAK,CAAC,CAAC;IAC9B,CAAC,EAAED,KAAK,CAAC;EACX,CAAC,CAAC,CAAC;AACL;AACAT,YAAY,CAACgB,SAAS,GAAG;EACvBZ,MAAM,EAAEN,SAAS,CAACmB,MAAM;EACxBZ,KAAK,EAAEP,SAAS,CAACmB,MAAM;EACvBX,KAAK,EAAER,SAAS,CAACmB,MAAM;EACvBd,QAAQ,EAAEL,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACqB,KAAK,EAAErB,SAAS,CAACsB,OAAO,CAAC,CAAC;EACnElB,SAAS,EAAEJ,SAAS,CAACuB;AACvB,CAAC;AACDrB,YAAY,CAACsB,YAAY,GAAG;EAC1BpB,SAAS,EAAE;AACb,CAAC;AACD,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}