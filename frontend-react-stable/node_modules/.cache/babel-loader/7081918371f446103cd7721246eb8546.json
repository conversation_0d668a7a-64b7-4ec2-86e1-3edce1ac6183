{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nfunction TabNode(_ref, ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    active = _ref.active,\n    _ref$tab = _ref.tab,\n    key = _ref$tab.key,\n    label = _ref$tab.label,\n    disabled = _ref$tab.disabled,\n    closeIcon = _ref$tab.closeIcon,\n    closable = _ref.closable,\n    renderWrapper = _ref.renderWrapper,\n    removeAriaLabel = _ref.removeAriaLabel,\n    editable = _ref.editable,\n    onClick = _ref.onClick,\n    onRemove = _ref.onRemove,\n    onFocus = _ref.onFocus,\n    style = _ref.style;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  React.useEffect(function () {\n    return onRemove;\n  }, []);\n  var removable = editable && closable !== false && !disabled;\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    ref: ref,\n    className: classNames(tabPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(tabPrefix, \"-with-remove\"), removable), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-active\"), active), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-disabled\"), disabled), _classNames)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : 0,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if ([KeyCode.SPACE, KeyCode.ENTER].includes(e.which)) {\n        e.preventDefault();\n        onInternalClick(e);\n      }\n    },\n    onFocus: onFocus\n  }, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: 0,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n}\nexport default /*#__PURE__*/React.forwardRef(TabNode);", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "KeyCode", "TabNode", "_ref", "ref", "_classNames", "prefixCls", "id", "active", "_ref$tab", "tab", "key", "label", "disabled", "closeIcon", "closable", "renderWrapper", "removeAriaLabel", "editable", "onClick", "onRemove", "onFocus", "style", "tabPrefix", "concat", "useEffect", "removable", "onInternalClick", "e", "onRemoveTab", "event", "preventDefault", "stopPropagation", "onEdit", "node", "createElement", "className", "role", "tabIndex", "onKeyDown", "SPACE", "ENTER", "includes", "which", "type", "removeIcon", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/TabNavList/TabNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\n\nfunction TabNode(_ref, ref) {\n  var _classNames;\n\n  var prefixCls = _ref.prefixCls,\n      id = _ref.id,\n      active = _ref.active,\n      _ref$tab = _ref.tab,\n      key = _ref$tab.key,\n      label = _ref$tab.label,\n      disabled = _ref$tab.disabled,\n      closeIcon = _ref$tab.closeIcon,\n      closable = _ref.closable,\n      renderWrapper = _ref.renderWrapper,\n      removeAriaLabel = _ref.removeAriaLabel,\n      editable = _ref.editable,\n      onClick = _ref.onClick,\n      onRemove = _ref.onRemove,\n      onFocus = _ref.onFocus,\n      style = _ref.style;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  React.useEffect(function () {\n    return onRemove;\n  }, []);\n  var removable = editable && closable !== false && !disabled;\n\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n\n    onClick(e);\n  }\n\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    ref: ref,\n    className: classNames(tabPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(tabPrefix, \"-with-remove\"), removable), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-active\"), active), _defineProperty(_classNames, \"\".concat(tabPrefix, \"-disabled\"), disabled), _classNames)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : 0,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if ([KeyCode.SPACE, KeyCode.ENTER].includes(e.which)) {\n        e.preventDefault();\n        onInternalClick(e);\n      }\n    },\n    onFocus: onFocus\n  }, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: 0,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n}\n\nexport default /*#__PURE__*/React.forwardRef(TabNode);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AAExC,SAASC,OAAOA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC1B,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,EAAE,GAAGJ,IAAI,CAACI,EAAE;IACZC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,QAAQ,GAAGN,IAAI,CAACO,GAAG;IACnBC,GAAG,GAAGF,QAAQ,CAACE,GAAG;IAClBC,KAAK,GAAGH,QAAQ,CAACG,KAAK;IACtBC,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ;IAC5BC,SAAS,GAAGL,QAAQ,CAACK,SAAS;IAC9BC,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;IACxBC,aAAa,GAAGb,IAAI,CAACa,aAAa;IAClCC,eAAe,GAAGd,IAAI,CAACc,eAAe;IACtCC,QAAQ,GAAGf,IAAI,CAACe,QAAQ;IACxBC,OAAO,GAAGhB,IAAI,CAACgB,OAAO;IACtBC,QAAQ,GAAGjB,IAAI,CAACiB,QAAQ;IACxBC,OAAO,GAAGlB,IAAI,CAACkB,OAAO;IACtBC,KAAK,GAAGnB,IAAI,CAACmB,KAAK;EACtB,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAAClB,SAAS,EAAE,MAAM,CAAC;EAC5CP,KAAK,CAAC0B,SAAS,CAAC,YAAY;IAC1B,OAAOL,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIM,SAAS,GAAGR,QAAQ,IAAIH,QAAQ,KAAK,KAAK,IAAI,CAACF,QAAQ;EAE3D,SAASc,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAIf,QAAQ,EAAE;MACZ;IACF;IAEAM,OAAO,CAACS,CAAC,CAAC;EACZ;EAEA,SAASC,WAAWA,CAACC,KAAK,EAAE;IAC1BA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvBd,QAAQ,CAACe,MAAM,CAAC,QAAQ,EAAE;MACxBtB,GAAG,EAAEA,GAAG;MACRmB,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EAEA,IAAII,IAAI,GAAG,aAAanC,KAAK,CAACoC,aAAa,CAAC,KAAK,EAAE;IACjDxB,GAAG,EAAEA,GAAG;IACRP,GAAG,EAAEA,GAAG;IACRgC,SAAS,EAAEpC,UAAU,CAACuB,SAAS,GAAGlB,WAAW,GAAG,CAAC,CAAC,EAAEP,eAAe,CAACO,WAAW,EAAE,EAAE,CAACmB,MAAM,CAACD,SAAS,EAAE,cAAc,CAAC,EAAEG,SAAS,CAAC,EAAE5B,eAAe,CAACO,WAAW,EAAE,EAAE,CAACmB,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAEf,MAAM,CAAC,EAAEV,eAAe,CAACO,WAAW,EAAE,EAAE,CAACmB,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAEV,QAAQ,CAAC,EAAER,WAAW,CAAC,CAAC;IAClSiB,KAAK,EAAEA,KAAK;IACZH,OAAO,EAAEQ;EACX,CAAC,EAAE,aAAa5B,KAAK,CAACoC,aAAa,CAAC,KAAK,EAAE;IACzCE,IAAI,EAAE,KAAK;IACX,eAAe,EAAE7B,MAAM;IACvBD,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACiB,MAAM,CAACjB,EAAE,EAAE,OAAO,CAAC,CAACiB,MAAM,CAACb,GAAG,CAAC;IAC5CyB,SAAS,EAAE,EAAE,CAACZ,MAAM,CAACD,SAAS,EAAE,MAAM,CAAC;IACvC,eAAe,EAAEhB,EAAE,IAAI,EAAE,CAACiB,MAAM,CAACjB,EAAE,EAAE,SAAS,CAAC,CAACiB,MAAM,CAACb,GAAG,CAAC;IAC3D,eAAe,EAAEE,QAAQ;IACzByB,QAAQ,EAAEzB,QAAQ,GAAG,IAAI,GAAG,CAAC;IAC7BM,OAAO,EAAE,SAASA,OAAOA,CAACS,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBL,eAAe,CAACC,CAAC,CAAC;IACpB,CAAC;IACDW,SAAS,EAAE,SAASA,SAASA,CAACX,CAAC,EAAE;MAC/B,IAAI,CAAC3B,OAAO,CAACuC,KAAK,EAAEvC,OAAO,CAACwC,KAAK,CAAC,CAACC,QAAQ,CAACd,CAAC,CAACe,KAAK,CAAC,EAAE;QACpDf,CAAC,CAACG,cAAc,CAAC,CAAC;QAClBJ,eAAe,CAACC,CAAC,CAAC;MACpB;IACF,CAAC;IACDP,OAAO,EAAEA;EACX,CAAC,EAAET,KAAK,CAAC,EAAEc,SAAS,IAAI,aAAa3B,KAAK,CAACoC,aAAa,CAAC,QAAQ,EAAE;IACjES,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE3B,eAAe,IAAI,QAAQ;IACzCqB,QAAQ,EAAE,CAAC;IACXF,SAAS,EAAE,EAAE,CAACZ,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC;IAC1CJ,OAAO,EAAE,SAASA,OAAOA,CAACS,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBH,WAAW,CAACD,CAAC,CAAC;IAChB;EACF,CAAC,EAAEd,SAAS,IAAII,QAAQ,CAAC2B,UAAU,IAAI,GAAG,CAAC,CAAC;EAC5C,OAAO7B,aAAa,GAAGA,aAAa,CAACkB,IAAI,CAAC,GAAGA,IAAI;AACnD;AAEA,eAAe,aAAanC,KAAK,CAAC+C,UAAU,CAAC5C,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}