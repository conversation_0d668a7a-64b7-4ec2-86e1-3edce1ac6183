{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/* eslint-disable no-underscore-dangle */\n\n/* eslint-disable react/prop-types */\nimport * as React from 'react';\nimport classnames from 'classnames';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classnames;\n  var prefixCls = props.prefixCls,\n    forceRender = props.forceRender,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    isActive = props.isActive,\n    role = props.role;\n  var _React$useState = React.useState(isActive || forceRender),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    rendered = _React$useState2[0],\n    setRendered = _React$useState2[1];\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n  if (!rendered) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-active\"), isActive), _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-inactive\"), !isActive), _classnames), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-box\")\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;", "map": {"version": 3, "names": ["_defineProperty", "_slicedToArray", "React", "classnames", "PanelContent", "forwardRef", "props", "ref", "_classnames", "prefixCls", "forceRender", "className", "style", "children", "isActive", "role", "_React$useState", "useState", "_React$useState2", "rendered", "setRendered", "useEffect", "createElement", "concat", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-collapse/es/PanelContent.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n\n/* eslint-disable no-underscore-dangle */\n\n/* eslint-disable react/prop-types */\nimport * as React from 'react';\nimport classnames from 'classnames';\nvar PanelContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classnames;\n\n  var prefixCls = props.prefixCls,\n      forceRender = props.forceRender,\n      className = props.className,\n      style = props.style,\n      children = props.children,\n      isActive = props.isActive,\n      role = props.role;\n\n  var _React$useState = React.useState(isActive || forceRender),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      rendered = _React$useState2[0],\n      setRendered = _React$useState2[1];\n\n  React.useEffect(function () {\n    if (forceRender || isActive) {\n      setRendered(true);\n    }\n  }, [forceRender, isActive]);\n\n  if (!rendered) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    className: classnames(\"\".concat(prefixCls, \"-content\"), (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-active\"), isActive), _defineProperty(_classnames, \"\".concat(prefixCls, \"-content-inactive\"), !isActive), _classnames), className),\n    style: style,\n    role: role\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-content-box\")\n  }, children));\n});\nPanelContent.displayName = 'PanelContent';\nexport default PanelContent;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;;AAErE;;AAEA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,IAAIC,YAAY,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACrE,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,IAAI,GAAGT,KAAK,CAACS,IAAI;EAErB,IAAIC,eAAe,GAAGd,KAAK,CAACe,QAAQ,CAACH,QAAQ,IAAIJ,WAAW,CAAC;IACzDQ,gBAAgB,GAAGjB,cAAc,CAACe,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAErChB,KAAK,CAACmB,SAAS,CAAC,YAAY;IAC1B,IAAIX,WAAW,IAAII,QAAQ,EAAE;MAC3BM,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,WAAW,EAAEI,QAAQ,CAAC,CAAC;EAE3B,IAAI,CAACK,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,OAAO,aAAajB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7Cf,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAER,UAAU,CAAC,EAAE,CAACoB,MAAM,CAACd,SAAS,EAAE,UAAU,CAAC,GAAGD,WAAW,GAAG,CAAC,CAAC,EAAER,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACe,MAAM,CAACd,SAAS,EAAE,iBAAiB,CAAC,EAAEK,QAAQ,CAAC,EAAEd,eAAe,CAACQ,WAAW,EAAE,EAAE,CAACe,MAAM,CAACd,SAAS,EAAE,mBAAmB,CAAC,EAAE,CAACK,QAAQ,CAAC,EAAEN,WAAW,GAAGG,SAAS,CAAC;IACxQC,KAAK,EAAEA,KAAK;IACZG,IAAI,EAAEA;EACR,CAAC,EAAE,aAAab,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACzCX,SAAS,EAAE,EAAE,CAACY,MAAM,CAACd,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEI,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACFT,YAAY,CAACoB,WAAW,GAAG,cAAc;AACzC,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}