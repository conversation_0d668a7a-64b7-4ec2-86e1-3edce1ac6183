{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport copy from 'copy-to-clipboard';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nimport { useLocaleReceiver } from '../../locale-provider/LocaleReceiver';\nimport TransButton from '../../_util/transButton';\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport useUpdatedEffect from '../hooks/useUpdatedEffect';\nimport Typography from '../Typography';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nfunction wrapperDecorations(_ref, content) {\n  var mark = _ref.mark,\n    code = _ref.code,\n    underline = _ref.underline,\n    del = _ref[\"delete\"],\n    strong = _ref.strong,\n    keyboard = _ref.keyboard,\n    italic = _ref.italic;\n  var currentContent = content;\n  function wrap(needed, tag) {\n    if (!needed) return;\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap(strong, 'strong');\n  wrap(underline, 'u');\n  wrap(del, 'del');\n  wrap(code, 'code');\n  wrap(mark, 'mark');\n  wrap(keyboard, 'kbd');\n  wrap(italic, 'i');\n  return currentContent;\n}\nfunction getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\nfunction toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nvar ELLIPSIS_STR = '...';\nvar Base = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _a, _b, _c;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    type = props.type,\n    disabled = props.disabled,\n    children = props.children,\n    ellipsis = props.ellipsis,\n    editable = props.editable,\n    copyable = props.copyable,\n    component = props.component,\n    title = props.title,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var textLocale = useLocaleReceiver('Text')[0]; // Force TS get this\n  var typographyRef = React.useRef(null);\n  var editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']);\n  // ========================== Editable ==========================\n  var _useMergedConfig = useMergedConfig(editable),\n    _useMergedConfig2 = _slicedToArray(_useMergedConfig, 2),\n    enableEdit = _useMergedConfig2[0],\n    editConfig = _useMergedConfig2[1];\n  var _useMergedState = useMergedState(false, {\n      value: editConfig.editing\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    editing = _useMergedState2[0],\n    setEditing = _useMergedState2[1];\n  var _editConfig$triggerTy = editConfig.triggerType,\n    triggerType = _editConfig$triggerTy === void 0 ? ['icon'] : _editConfig$triggerTy;\n  var triggerEdit = function triggerEdit(edit) {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  useUpdatedEffect(function () {\n    var _a;\n    if (!editing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  var onEditClick = function onEditClick(e) {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  var onEditChange = function onEditChange(value) {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  var onEditCancel = function onEditCancel() {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  var _useMergedConfig3 = useMergedConfig(copyable),\n    _useMergedConfig4 = _slicedToArray(_useMergedConfig3, 2),\n    enableCopy = _useMergedConfig4[0],\n    copyConfig = _useMergedConfig4[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    copied = _React$useState2[0],\n    setCopied = _React$useState2[1];\n  var copyIdRef = React.useRef();\n  var copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  var cleanCopyId = function cleanCopyId() {\n    window.clearTimeout(copyIdRef.current);\n  };\n  var onCopyClick = function onCopyClick(e) {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    copy(copyConfig.text || String(children) || '', copyOptions);\n    setCopied(true);\n    // Trigger tips update\n    cleanCopyId();\n    copyIdRef.current = window.setTimeout(function () {\n      setCopied(false);\n    }, 3000);\n    (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n  };\n  React.useEffect(function () {\n    return cleanCopyId;\n  }, []);\n  // ========================== Ellipsis ==========================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    isLineClampSupport = _React$useState4[0],\n    setIsLineClampSupport = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isTextOverflowSupport = _React$useState6[0],\n    setIsTextOverflowSupport = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    expanded = _React$useState8[0],\n    setExpanded = _React$useState8[1];\n  var _React$useState9 = React.useState(false),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    isJsEllipsis = _React$useState10[0],\n    setIsJsEllipsis = _React$useState10[1];\n  var _React$useState11 = React.useState(false),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    isNativeEllipsis = _React$useState12[0],\n    setIsNativeEllipsis = _React$useState12[1];\n  var _React$useState13 = React.useState(true),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    isNativeVisible = _React$useState14[0],\n    setIsNativeVisible = _React$useState14[1];\n  var _useMergedConfig5 = useMergedConfig(ellipsis, {\n      expandable: false\n    }),\n    _useMergedConfig6 = _slicedToArray(_useMergedConfig5, 2),\n    enableEllipsis = _useMergedConfig6[0],\n    ellipsisConfig = _useMergedConfig6[1];\n  var mergedEnableEllipsis = enableEllipsis && !expanded;\n  // Shared prop to reduce bundle size\n  var _ellipsisConfig$rows = ellipsisConfig.rows,\n    rows = _ellipsisConfig$rows === void 0 ? 1 : _ellipsisConfig$rows;\n  var needMeasureEllipsis = React.useMemo(function () {\n    return (\n      // Disable ellipsis\n      !mergedEnableEllipsis ||\n      // Provide suffix\n      ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n      // Can't use css ellipsis since we need to provide the place for button\n      ellipsisConfig.expandable || enableEdit || enableCopy\n    );\n  }, [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useIsomorphicLayoutEffect(function () {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  var cssEllipsis = React.useMemo(function () {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  var isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  var cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  var cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  var onExpandClick = function onExpandClick(e) {\n    var _a;\n    setExpanded(true);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e);\n  };\n  var _React$useState15 = React.useState(0),\n    _React$useState16 = _slicedToArray(_React$useState15, 2),\n    ellipsisWidth = _React$useState16[0],\n    setEllipsisWidth = _React$useState16[1];\n  var _React$useState17 = React.useState(0),\n    _React$useState18 = _slicedToArray(_React$useState17, 2),\n    ellipsisFontSize = _React$useState18[0],\n    setEllipsisFontSize = _React$useState18[1];\n  var onResize = function onResize(_ref2, element) {\n    var offsetWidth = _ref2.offsetWidth;\n    var _a;\n    setEllipsisWidth(offsetWidth);\n    setEllipsisFontSize(parseInt((_a = window.getComputedStyle) === null || _a === void 0 ? void 0 : _a.call(window, element).fontSize, 10) || 0);\n  };\n  // >>>>> JS Ellipsis\n  var onJsEllipsis = function onJsEllipsis(jsEllipsis) {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      var currentEllipsis = cssLineClamp ? textEle.offsetHeight < textEle.scrollHeight : textEle.offsetWidth < textEle.scrollWidth;\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    var observer = new IntersectionObserver(function () {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return function () {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  var tooltipProps = {};\n  if (ellipsisConfig.tooltip === true) {\n    tooltipProps = {\n      title: (_a = editConfig.text) !== null && _a !== void 0 ? _a : children\n    };\n  } else if (/*#__PURE__*/React.isValidElement(ellipsisConfig.tooltip)) {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  } else if (_typeof(ellipsisConfig.tooltip) === 'object') {\n    tooltipProps = _extends({\n      title: (_b = editConfig.text) !== null && _b !== void 0 ? _b : children\n    }, ellipsisConfig.tooltip);\n  } else {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  }\n  var topAriaLabel = React.useMemo(function () {\n    var isValid = function isValid(val) {\n      return ['string', 'number'].includes(_typeof(val));\n    };\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    if (isValid(editConfig.text)) {\n      return editConfig.text;\n    }\n    if (isValid(children)) {\n      return children;\n    }\n    if (isValid(title)) {\n      return title;\n    }\n    if (isValid(tooltipProps.title)) {\n      return tooltipProps.title;\n    }\n    return undefined;\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_c = editConfig.text) !== null && _c !== void 0 ? _c : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  var renderExpand = function renderExpand() {\n    var expandable = ellipsisConfig.expandable,\n      symbol = ellipsisConfig.symbol;\n    if (!expandable) return null;\n    var expandContent;\n    if (symbol) {\n      expandContent = symbol;\n    } else {\n      expandContent = textLocale.expand;\n    }\n    return /*#__PURE__*/React.createElement(\"a\", {\n      key: \"expand\",\n      className: \"\".concat(prefixCls, \"-expand\"),\n      onClick: onExpandClick,\n      \"aria-label\": textLocale.expand\n    }, expandContent);\n  };\n  // Edit\n  var renderEdit = function renderEdit() {\n    if (!enableEdit) return;\n    var icon = editConfig.icon,\n      tooltip = editConfig.tooltip;\n    var editTitle = toArray(tooltip)[0] || textLocale.edit;\n    var ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      ref: editIconRef,\n      className: \"\".concat(prefixCls, \"-edit\"),\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    }))) : null;\n  };\n  // Copy\n  var renderCopy = function renderCopy() {\n    if (!enableCopy) return;\n    var tooltips = copyConfig.tooltips,\n      icon = copyConfig.icon;\n    var tooltipNodes = toList(tooltips);\n    var iconNodes = toList(icon);\n    var copyTitle = copied ? getNode(tooltipNodes[1], textLocale.copied) : getNode(tooltipNodes[0], textLocale.copy);\n    var systemStr = copied ? textLocale.copied : textLocale.copy;\n    var ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n    return /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"copy\",\n      title: copyTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      className: classNames(\"\".concat(prefixCls, \"-copy\"), copied && \"\".concat(prefixCls, \"-copy-success\")),\n      onClick: onCopyClick,\n      \"aria-label\": ariaLabel\n    }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n  };\n  var renderOperations = function renderOperations(renderExpanded) {\n    return [renderExpanded && renderExpand(), renderEdit(), renderCopy()];\n  };\n  var renderEllipsis = function renderEllipsis(needEllipsis) {\n    return [needEllipsis && /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      key: \"ellipsis\"\n    }, ELLIPSIS_STR), ellipsisConfig.suffix, renderOperations(needEllipsis)];\n  };\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis || cssEllipsis\n  }, function (resizeRef) {\n    var _classNames;\n    return /*#__PURE__*/React.createElement(EllipsisTooltip, {\n      tooltipProps: tooltipProps,\n      enabledEllipsis: mergedEnableEllipsis,\n      isEllipsis: isMergedEllipsis\n    }, /*#__PURE__*/React.createElement(Typography, _extends({\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(type), type), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis\"), enableEllipsis), _defineProperty(_classNames, \"\".concat(prefixCls, \"-single-line\"), mergedEnableEllipsis && rows === 1), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-single-line\"), cssTextOverflow), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-multiple-line\"), cssLineClamp), _classNames), className),\n      style: _extends(_extends({}, style), {\n        WebkitLineClamp: cssLineClamp ? rows : undefined\n      }),\n      component: component,\n      ref: composeRef(resizeRef, typographyRef, ref),\n      direction: direction,\n      onClick: triggerType.includes('text') ? onEditClick : undefined,\n      \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n      title: title\n    }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n      enabledMeasure: mergedEnableEllipsis && !cssEllipsis,\n      text: children,\n      rows: rows,\n      width: ellipsisWidth,\n      fontSize: ellipsisFontSize,\n      onEllipsis: onJsEllipsis\n    }, function (node, needEllipsis) {\n      var renderNode = node;\n      if (node.length && needEllipsis && topAriaLabel) {\n        renderNode = /*#__PURE__*/React.createElement(\"span\", {\n          key: \"show-content\",\n          \"aria-hidden\": true\n        }, renderNode);\n      }\n      var wrappedContext = wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, renderNode, renderEllipsis(needEllipsis)));\n      return wrappedContext;\n    })));\n  });\n});\nexport default Base;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_typeof", "_slicedToArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "CheckOutlined", "CopyOutlined", "EditOutlined", "classNames", "copy", "ResizeObserver", "toArray", "useIsomorphicLayoutEffect", "useMergedState", "omit", "composeRef", "React", "ConfigContext", "useLocaleReceiver", "TransButton", "isStyleSupport", "<PERSON><PERSON><PERSON>", "Editable", "useMergedConfig", "useUpdatedEffect", "Typography", "El<PERSON><PERSON>", "EllipsisTooltip", "wrapperDecorations", "_ref", "content", "mark", "code", "underline", "del", "strong", "keyboard", "italic", "currentC<PERSON>nt", "wrap", "needed", "tag", "createElement", "getNode", "dom", "defaultNode", "needDom", "undefined", "toList", "val", "Array", "isArray", "ELLIPSIS_STR", "Base", "forwardRef", "props", "ref", "_a", "_b", "_c", "customizePrefixCls", "prefixCls", "className", "style", "type", "disabled", "children", "ellipsis", "editable", "copyable", "component", "title", "restProps", "_React$useContext", "useContext", "getPrefixCls", "direction", "textLocale", "typographyRef", "useRef", "editIconRef", "textProps", "_useMergedConfig", "_useMergedConfig2", "enableEdit", "editConfig", "_useMergedState", "value", "editing", "_useMergedState2", "setEditing", "_editConfig$triggerTy", "triggerType", "triggerEdit", "edit", "onStart", "current", "focus", "onEditClick", "preventDefault", "onEditChange", "onChange", "onEditCancel", "onCancel", "_useMergedConfig3", "_useMergedConfig4", "enableCopy", "copyConfig", "_React$useState", "useState", "_React$useState2", "copied", "setCopied", "copyIdRef", "copyOptions", "format", "cleanCopyId", "window", "clearTimeout", "onCopyClick", "stopPropagation", "text", "String", "setTimeout", "onCopy", "useEffect", "_React$useState3", "_React$useState4", "isLineClampSupport", "setIsLineClampSupport", "_React$useState5", "_React$useState6", "isTextOverflowSupport", "setIsTextOverflowSupport", "_React$useState7", "_React$useState8", "expanded", "setExpanded", "_React$useState9", "_React$useState10", "isJsEllipsis", "setIsJsEllipsis", "_React$useState11", "_React$useState12", "isNativeEllipsis", "setIsNativeEllipsis", "_React$useState13", "_React$useState14", "isNativeVisible", "setIsNativeVisible", "_useMergedConfig5", "expandable", "_useMergedConfig6", "enableEllipsis", "ellipsisConfig", "mergedEnableEllipsis", "_ellipsisConfig$rows", "rows", "needMeasureEllipsis", "useMemo", "suffix", "onEllipsis", "cssEllipsis", "isMergedEllipsis", "cssTextOverflow", "cssLineClamp", "onExpandClick", "onExpand", "_React$useState15", "_React$useState16", "ellip<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_React$useState17", "_React$useState18", "ellipsisFontSize", "setEllipsisFontSize", "onResize", "_ref2", "element", "offsetWidth", "parseInt", "getComputedStyle", "fontSize", "onJsEllipsis", "js<PERSON><PERSON><PERSON>", "textEle", "currentEllipsis", "offsetHeight", "scrollHeight", "scrollWidth", "IntersectionObserver", "observer", "offsetParent", "observe", "disconnect", "tooltipProps", "tooltip", "isValidElement", "topAriaLabel", "<PERSON><PERSON><PERSON><PERSON>", "includes", "onSave", "onEnd", "max<PERSON><PERSON><PERSON>", "autoSize", "enterIcon", "renderExpand", "symbol", "expandContent", "expand", "key", "concat", "onClick", "renderEdit", "icon", "editTitle", "aria<PERSON><PERSON><PERSON>", "role", "renderCopy", "tooltips", "tooltipNodes", "iconNodes", "copyTitle", "systemStr", "renderOperations", "renderExpanded", "renderEllipsis", "needEllipsis", "resizeRef", "_classNames", "enabledEllipsis", "isEllipsis", "WebkitLineClamp", "toString", "enabledMeasure", "width", "node", "renderNode", "wrappedContext", "Fragment"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Base/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport EditOutlined from \"@ant-design/icons/es/icons/EditOutlined\";\nimport classNames from 'classnames';\nimport copy from 'copy-to-clipboard';\nimport ResizeObserver from 'rc-resize-observer';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nimport { useLocaleReceiver } from '../../locale-provider/LocaleReceiver';\nimport TransButton from '../../_util/transButton';\nimport { isStyleSupport } from '../../_util/styleChecker';\nimport Tooltip from '../../tooltip';\nimport Editable from '../Editable';\nimport useMergedConfig from '../hooks/useMergedConfig';\nimport useUpdatedEffect from '../hooks/useUpdatedEffect';\nimport Typography from '../Typography';\nimport Ellipsis from './Ellipsis';\nimport EllipsisTooltip from './EllipsisTooltip';\nfunction wrapperDecorations(_ref, content) {\n  var mark = _ref.mark,\n    code = _ref.code,\n    underline = _ref.underline,\n    del = _ref[\"delete\"],\n    strong = _ref.strong,\n    keyboard = _ref.keyboard,\n    italic = _ref.italic;\n  var currentContent = content;\n  function wrap(needed, tag) {\n    if (!needed) return;\n    currentContent = /*#__PURE__*/React.createElement(tag, {}, currentContent);\n  }\n  wrap(strong, 'strong');\n  wrap(underline, 'u');\n  wrap(del, 'del');\n  wrap(code, 'code');\n  wrap(mark, 'mark');\n  wrap(keyboard, 'kbd');\n  wrap(italic, 'i');\n  return currentContent;\n}\nfunction getNode(dom, defaultNode, needDom) {\n  if (dom === true || dom === undefined) {\n    return defaultNode;\n  }\n  return dom || needDom && defaultNode;\n}\nfunction toList(val) {\n  if (val === false) {\n    return [false, false];\n  }\n  return Array.isArray(val) ? val : [val];\n}\nvar ELLIPSIS_STR = '...';\nvar Base = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _a, _b, _c;\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    type = props.type,\n    disabled = props.disabled,\n    children = props.children,\n    ellipsis = props.ellipsis,\n    editable = props.editable,\n    copyable = props.copyable,\n    component = props.component,\n    title = props.title,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"type\", \"disabled\", \"children\", \"ellipsis\", \"editable\", \"copyable\", \"component\", \"title\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var textLocale = useLocaleReceiver('Text')[0]; // Force TS get this\n  var typographyRef = React.useRef(null);\n  var editIconRef = React.useRef(null);\n  // ============================ MISC ============================\n  var prefixCls = getPrefixCls('typography', customizePrefixCls);\n  var textProps = omit(restProps, ['mark', 'code', 'delete', 'underline', 'strong', 'keyboard', 'italic']);\n  // ========================== Editable ==========================\n  var _useMergedConfig = useMergedConfig(editable),\n    _useMergedConfig2 = _slicedToArray(_useMergedConfig, 2),\n    enableEdit = _useMergedConfig2[0],\n    editConfig = _useMergedConfig2[1];\n  var _useMergedState = useMergedState(false, {\n      value: editConfig.editing\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    editing = _useMergedState2[0],\n    setEditing = _useMergedState2[1];\n  var _editConfig$triggerTy = editConfig.triggerType,\n    triggerType = _editConfig$triggerTy === void 0 ? ['icon'] : _editConfig$triggerTy;\n  var triggerEdit = function triggerEdit(edit) {\n    var _a;\n    if (edit) {\n      (_a = editConfig.onStart) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    }\n    setEditing(edit);\n  };\n  // Focus edit icon when back\n  useUpdatedEffect(function () {\n    var _a;\n    if (!editing) {\n      (_a = editIconRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, [editing]);\n  var onEditClick = function onEditClick(e) {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    triggerEdit(true);\n  };\n  var onEditChange = function onEditChange(value) {\n    var _a;\n    (_a = editConfig.onChange) === null || _a === void 0 ? void 0 : _a.call(editConfig, value);\n    triggerEdit(false);\n  };\n  var onEditCancel = function onEditCancel() {\n    var _a;\n    (_a = editConfig.onCancel) === null || _a === void 0 ? void 0 : _a.call(editConfig);\n    triggerEdit(false);\n  };\n  // ========================== Copyable ==========================\n  var _useMergedConfig3 = useMergedConfig(copyable),\n    _useMergedConfig4 = _slicedToArray(_useMergedConfig3, 2),\n    enableCopy = _useMergedConfig4[0],\n    copyConfig = _useMergedConfig4[1];\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    copied = _React$useState2[0],\n    setCopied = _React$useState2[1];\n  var copyIdRef = React.useRef();\n  var copyOptions = {};\n  if (copyConfig.format) {\n    copyOptions.format = copyConfig.format;\n  }\n  var cleanCopyId = function cleanCopyId() {\n    window.clearTimeout(copyIdRef.current);\n  };\n  var onCopyClick = function onCopyClick(e) {\n    var _a;\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    e === null || e === void 0 ? void 0 : e.stopPropagation();\n    copy(copyConfig.text || String(children) || '', copyOptions);\n    setCopied(true);\n    // Trigger tips update\n    cleanCopyId();\n    copyIdRef.current = window.setTimeout(function () {\n      setCopied(false);\n    }, 3000);\n    (_a = copyConfig.onCopy) === null || _a === void 0 ? void 0 : _a.call(copyConfig, e);\n  };\n  React.useEffect(function () {\n    return cleanCopyId;\n  }, []);\n  // ========================== Ellipsis ==========================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    isLineClampSupport = _React$useState4[0],\n    setIsLineClampSupport = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isTextOverflowSupport = _React$useState6[0],\n    setIsTextOverflowSupport = _React$useState6[1];\n  var _React$useState7 = React.useState(false),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    expanded = _React$useState8[0],\n    setExpanded = _React$useState8[1];\n  var _React$useState9 = React.useState(false),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    isJsEllipsis = _React$useState10[0],\n    setIsJsEllipsis = _React$useState10[1];\n  var _React$useState11 = React.useState(false),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    isNativeEllipsis = _React$useState12[0],\n    setIsNativeEllipsis = _React$useState12[1];\n  var _React$useState13 = React.useState(true),\n    _React$useState14 = _slicedToArray(_React$useState13, 2),\n    isNativeVisible = _React$useState14[0],\n    setIsNativeVisible = _React$useState14[1];\n  var _useMergedConfig5 = useMergedConfig(ellipsis, {\n      expandable: false\n    }),\n    _useMergedConfig6 = _slicedToArray(_useMergedConfig5, 2),\n    enableEllipsis = _useMergedConfig6[0],\n    ellipsisConfig = _useMergedConfig6[1];\n  var mergedEnableEllipsis = enableEllipsis && !expanded;\n  // Shared prop to reduce bundle size\n  var _ellipsisConfig$rows = ellipsisConfig.rows,\n    rows = _ellipsisConfig$rows === void 0 ? 1 : _ellipsisConfig$rows;\n  var needMeasureEllipsis = React.useMemo(function () {\n    return (\n      // Disable ellipsis\n      !mergedEnableEllipsis ||\n      // Provide suffix\n      ellipsisConfig.suffix !== undefined || ellipsisConfig.onEllipsis ||\n      // Can't use css ellipsis since we need to provide the place for button\n      ellipsisConfig.expandable || enableEdit || enableCopy\n    );\n  }, [mergedEnableEllipsis, ellipsisConfig, enableEdit, enableCopy]);\n  useIsomorphicLayoutEffect(function () {\n    if (enableEllipsis && !needMeasureEllipsis) {\n      setIsLineClampSupport(isStyleSupport('webkitLineClamp'));\n      setIsTextOverflowSupport(isStyleSupport('textOverflow'));\n    }\n  }, [needMeasureEllipsis, enableEllipsis]);\n  var cssEllipsis = React.useMemo(function () {\n    if (needMeasureEllipsis) {\n      return false;\n    }\n    if (rows === 1) {\n      return isTextOverflowSupport;\n    }\n    return isLineClampSupport;\n  }, [needMeasureEllipsis, isTextOverflowSupport, isLineClampSupport]);\n  var isMergedEllipsis = mergedEnableEllipsis && (cssEllipsis ? isNativeEllipsis : isJsEllipsis);\n  var cssTextOverflow = mergedEnableEllipsis && rows === 1 && cssEllipsis;\n  var cssLineClamp = mergedEnableEllipsis && rows > 1 && cssEllipsis;\n  // >>>>> Expand\n  var onExpandClick = function onExpandClick(e) {\n    var _a;\n    setExpanded(true);\n    (_a = ellipsisConfig.onExpand) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, e);\n  };\n  var _React$useState15 = React.useState(0),\n    _React$useState16 = _slicedToArray(_React$useState15, 2),\n    ellipsisWidth = _React$useState16[0],\n    setEllipsisWidth = _React$useState16[1];\n  var _React$useState17 = React.useState(0),\n    _React$useState18 = _slicedToArray(_React$useState17, 2),\n    ellipsisFontSize = _React$useState18[0],\n    setEllipsisFontSize = _React$useState18[1];\n  var onResize = function onResize(_ref2, element) {\n    var offsetWidth = _ref2.offsetWidth;\n    var _a;\n    setEllipsisWidth(offsetWidth);\n    setEllipsisFontSize(parseInt((_a = window.getComputedStyle) === null || _a === void 0 ? void 0 : _a.call(window, element).fontSize, 10) || 0);\n  };\n  // >>>>> JS Ellipsis\n  var onJsEllipsis = function onJsEllipsis(jsEllipsis) {\n    var _a;\n    setIsJsEllipsis(jsEllipsis);\n    // Trigger if changed\n    if (isJsEllipsis !== jsEllipsis) {\n      (_a = ellipsisConfig.onEllipsis) === null || _a === void 0 ? void 0 : _a.call(ellipsisConfig, jsEllipsis);\n    }\n  };\n  // >>>>> Native ellipsis\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n    if (enableEllipsis && cssEllipsis && textEle) {\n      var currentEllipsis = cssLineClamp ? textEle.offsetHeight < textEle.scrollHeight : textEle.offsetWidth < textEle.scrollWidth;\n      if (isNativeEllipsis !== currentEllipsis) {\n        setIsNativeEllipsis(currentEllipsis);\n      }\n    }\n  }, [enableEllipsis, cssEllipsis, children, cssLineClamp, isNativeVisible]);\n  // https://github.com/ant-design/ant-design/issues/36786\n  // Use IntersectionObserver to check if element is invisible\n  React.useEffect(function () {\n    var textEle = typographyRef.current;\n    if (typeof IntersectionObserver === 'undefined' || !textEle || !cssEllipsis || !mergedEnableEllipsis) {\n      return;\n    }\n    /* eslint-disable-next-line compat/compat */\n    var observer = new IntersectionObserver(function () {\n      setIsNativeVisible(!!textEle.offsetParent);\n    });\n    observer.observe(textEle);\n    return function () {\n      observer.disconnect();\n    };\n  }, [cssEllipsis, mergedEnableEllipsis]);\n  // ========================== Tooltip ===========================\n  var tooltipProps = {};\n  if (ellipsisConfig.tooltip === true) {\n    tooltipProps = {\n      title: (_a = editConfig.text) !== null && _a !== void 0 ? _a : children\n    };\n  } else if ( /*#__PURE__*/React.isValidElement(ellipsisConfig.tooltip)) {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  } else if (_typeof(ellipsisConfig.tooltip) === 'object') {\n    tooltipProps = _extends({\n      title: (_b = editConfig.text) !== null && _b !== void 0 ? _b : children\n    }, ellipsisConfig.tooltip);\n  } else {\n    tooltipProps = {\n      title: ellipsisConfig.tooltip\n    };\n  }\n  var topAriaLabel = React.useMemo(function () {\n    var isValid = function isValid(val) {\n      return ['string', 'number'].includes(_typeof(val));\n    };\n    if (!enableEllipsis || cssEllipsis) {\n      return undefined;\n    }\n    if (isValid(editConfig.text)) {\n      return editConfig.text;\n    }\n    if (isValid(children)) {\n      return children;\n    }\n    if (isValid(title)) {\n      return title;\n    }\n    if (isValid(tooltipProps.title)) {\n      return tooltipProps.title;\n    }\n    return undefined;\n  }, [enableEllipsis, cssEllipsis, title, tooltipProps.title, isMergedEllipsis]);\n  // =========================== Render ===========================\n  // >>>>>>>>>>> Editing input\n  if (editing) {\n    return /*#__PURE__*/React.createElement(Editable, {\n      value: (_c = editConfig.text) !== null && _c !== void 0 ? _c : typeof children === 'string' ? children : '',\n      onSave: onEditChange,\n      onCancel: onEditCancel,\n      onEnd: editConfig.onEnd,\n      prefixCls: prefixCls,\n      className: className,\n      style: style,\n      direction: direction,\n      component: component,\n      maxLength: editConfig.maxLength,\n      autoSize: editConfig.autoSize,\n      enterIcon: editConfig.enterIcon\n    });\n  }\n  // >>>>>>>>>>> Typography\n  // Expand\n  var renderExpand = function renderExpand() {\n    var expandable = ellipsisConfig.expandable,\n      symbol = ellipsisConfig.symbol;\n    if (!expandable) return null;\n    var expandContent;\n    if (symbol) {\n      expandContent = symbol;\n    } else {\n      expandContent = textLocale.expand;\n    }\n    return /*#__PURE__*/React.createElement(\"a\", {\n      key: \"expand\",\n      className: \"\".concat(prefixCls, \"-expand\"),\n      onClick: onExpandClick,\n      \"aria-label\": textLocale.expand\n    }, expandContent);\n  };\n  // Edit\n  var renderEdit = function renderEdit() {\n    if (!enableEdit) return;\n    var icon = editConfig.icon,\n      tooltip = editConfig.tooltip;\n    var editTitle = toArray(tooltip)[0] || textLocale.edit;\n    var ariaLabel = typeof editTitle === 'string' ? editTitle : '';\n    return triggerType.includes('icon') ? /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"edit\",\n      title: tooltip === false ? '' : editTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      ref: editIconRef,\n      className: \"\".concat(prefixCls, \"-edit\"),\n      onClick: onEditClick,\n      \"aria-label\": ariaLabel\n    }, icon || /*#__PURE__*/React.createElement(EditOutlined, {\n      role: \"button\"\n    }))) : null;\n  };\n  // Copy\n  var renderCopy = function renderCopy() {\n    if (!enableCopy) return;\n    var tooltips = copyConfig.tooltips,\n      icon = copyConfig.icon;\n    var tooltipNodes = toList(tooltips);\n    var iconNodes = toList(icon);\n    var copyTitle = copied ? getNode(tooltipNodes[1], textLocale.copied) : getNode(tooltipNodes[0], textLocale.copy);\n    var systemStr = copied ? textLocale.copied : textLocale.copy;\n    var ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n    return /*#__PURE__*/React.createElement(Tooltip, {\n      key: \"copy\",\n      title: copyTitle\n    }, /*#__PURE__*/React.createElement(TransButton, {\n      className: classNames(\"\".concat(prefixCls, \"-copy\"), copied && \"\".concat(prefixCls, \"-copy-success\")),\n      onClick: onCopyClick,\n      \"aria-label\": ariaLabel\n    }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n  };\n  var renderOperations = function renderOperations(renderExpanded) {\n    return [renderExpanded && renderExpand(), renderEdit(), renderCopy()];\n  };\n  var renderEllipsis = function renderEllipsis(needEllipsis) {\n    return [needEllipsis && /*#__PURE__*/React.createElement(\"span\", {\n      \"aria-hidden\": true,\n      key: \"ellipsis\"\n    }, ELLIPSIS_STR), ellipsisConfig.suffix, renderOperations(needEllipsis)];\n  };\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onResize,\n    disabled: !mergedEnableEllipsis || cssEllipsis\n  }, function (resizeRef) {\n    var _classNames;\n    return /*#__PURE__*/React.createElement(EllipsisTooltip, {\n      tooltipProps: tooltipProps,\n      enabledEllipsis: mergedEnableEllipsis,\n      isEllipsis: isMergedEllipsis\n    }, /*#__PURE__*/React.createElement(Typography, _extends({\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(type), type), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis\"), enableEllipsis), _defineProperty(_classNames, \"\".concat(prefixCls, \"-single-line\"), mergedEnableEllipsis && rows === 1), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-single-line\"), cssTextOverflow), _defineProperty(_classNames, \"\".concat(prefixCls, \"-ellipsis-multiple-line\"), cssLineClamp), _classNames), className),\n      style: _extends(_extends({}, style), {\n        WebkitLineClamp: cssLineClamp ? rows : undefined\n      }),\n      component: component,\n      ref: composeRef(resizeRef, typographyRef, ref),\n      direction: direction,\n      onClick: triggerType.includes('text') ? onEditClick : undefined,\n      \"aria-label\": topAriaLabel === null || topAriaLabel === void 0 ? void 0 : topAriaLabel.toString(),\n      title: title\n    }, textProps), /*#__PURE__*/React.createElement(Ellipsis, {\n      enabledMeasure: mergedEnableEllipsis && !cssEllipsis,\n      text: children,\n      rows: rows,\n      width: ellipsisWidth,\n      fontSize: ellipsisFontSize,\n      onEllipsis: onJsEllipsis\n    }, function (node, needEllipsis) {\n      var renderNode = node;\n      if (node.length && needEllipsis && topAriaLabel) {\n        renderNode = /*#__PURE__*/React.createElement(\"span\", {\n          key: \"show-content\",\n          \"aria-hidden\": true\n        }, renderNode);\n      }\n      var wrappedContext = wrapperDecorations(props, /*#__PURE__*/React.createElement(React.Fragment, null, renderNode, renderEllipsis(needEllipsis)));\n      return wrappedContext;\n    })));\n  });\n});\nexport default Base;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAClBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC1BC,GAAG,GAAGL,IAAI,CAAC,QAAQ,CAAC;IACpBM,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,MAAM,GAAGR,IAAI,CAACQ,MAAM;EACtB,IAAIC,cAAc,GAAGR,OAAO;EAC5B,SAASS,IAAIA,CAACC,MAAM,EAAEC,GAAG,EAAE;IACzB,IAAI,CAACD,MAAM,EAAE;IACbF,cAAc,GAAG,aAAatB,KAAK,CAAC0B,aAAa,CAACD,GAAG,EAAE,CAAC,CAAC,EAAEH,cAAc,CAAC;EAC5E;EACAC,IAAI,CAACJ,MAAM,EAAE,QAAQ,CAAC;EACtBI,IAAI,CAACN,SAAS,EAAE,GAAG,CAAC;EACpBM,IAAI,CAACL,GAAG,EAAE,KAAK,CAAC;EAChBK,IAAI,CAACP,IAAI,EAAE,MAAM,CAAC;EAClBO,IAAI,CAACR,IAAI,EAAE,MAAM,CAAC;EAClBQ,IAAI,CAACH,QAAQ,EAAE,KAAK,CAAC;EACrBG,IAAI,CAACF,MAAM,EAAE,GAAG,CAAC;EACjB,OAAOC,cAAc;AACvB;AACA,SAASK,OAAOA,CAACC,GAAG,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC1C,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,EAAE;IACrC,OAAOF,WAAW;EACpB;EACA,OAAOD,GAAG,IAAIE,OAAO,IAAID,WAAW;AACtC;AACA,SAASG,MAAMA,CAACC,GAAG,EAAE;EACnB,IAAIA,GAAG,KAAK,KAAK,EAAE;IACjB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;EACvB;EACA,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AACA,IAAIG,YAAY,GAAG,KAAK;AACxB,IAAIC,IAAI,GAAG,aAAarC,KAAK,CAACsC,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,IAAIC,kBAAkB,GAAGL,KAAK,CAACM,SAAS;IACtCC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,IAAI,GAAGT,KAAK,CAACS,IAAI;IACjBC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,SAAS,GAAGf,KAAK,CAACe,SAAS;IAC3BC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,SAAS,GAAGjF,MAAM,CAACgE,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;EAC1J,IAAIkB,iBAAiB,GAAGzD,KAAK,CAAC0D,UAAU,CAACzD,aAAa,CAAC;IACrD0D,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,UAAU,GAAG3D,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,IAAI4D,aAAa,GAAG9D,KAAK,CAAC+D,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIC,WAAW,GAAGhE,KAAK,CAAC+D,MAAM,CAAC,IAAI,CAAC;EACpC;EACA,IAAIlB,SAAS,GAAGc,YAAY,CAAC,YAAY,EAAEf,kBAAkB,CAAC;EAC9D,IAAIqB,SAAS,GAAGnE,IAAI,CAAC0D,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxG;EACA,IAAIU,gBAAgB,GAAG3D,eAAe,CAAC6C,QAAQ,CAAC;IAC9Ce,iBAAiB,GAAG7F,cAAc,CAAC4F,gBAAgB,EAAE,CAAC,CAAC;IACvDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACnC,IAAIG,eAAe,GAAGzE,cAAc,CAAC,KAAK,EAAE;MACxC0E,KAAK,EAAEF,UAAU,CAACG;IACpB,CAAC,CAAC;IACFC,gBAAgB,GAAGnG,cAAc,CAACgG,eAAe,EAAE,CAAC,CAAC;IACrDE,OAAO,GAAGC,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIE,qBAAqB,GAAGN,UAAU,CAACO,WAAW;IAChDA,WAAW,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAGA,qBAAqB;EACnF,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;IAC3C,IAAIrC,EAAE;IACN,IAAIqC,IAAI,EAAE;MACR,CAACrC,EAAE,GAAG4B,UAAU,CAACU,OAAO,MAAM,IAAI,IAAItC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAACsF,UAAU,CAAC;IACpF;IACAK,UAAU,CAACI,IAAI,CAAC;EAClB,CAAC;EACD;EACAtE,gBAAgB,CAAC,YAAY;IAC3B,IAAIiC,EAAE;IACN,IAAI,CAAC+B,OAAO,EAAE;MACZ,CAAC/B,EAAE,GAAGuB,WAAW,CAACgB,OAAO,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwC,KAAK,CAAC,CAAC;IAC5E;EACF,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;EACb,IAAIU,WAAW,GAAG,SAASA,WAAWA,CAACzG,CAAC,EAAE;IACxCA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0G,cAAc,CAAC,CAAC;IACxDN,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,IAAIO,YAAY,GAAG,SAASA,YAAYA,CAACb,KAAK,EAAE;IAC9C,IAAI9B,EAAE;IACN,CAACA,EAAE,GAAG4B,UAAU,CAACgB,QAAQ,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAACsF,UAAU,EAAEE,KAAK,CAAC;IAC1FM,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,IAAIS,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAI7C,EAAE;IACN,CAACA,EAAE,GAAG4B,UAAU,CAACkB,QAAQ,MAAM,IAAI,IAAI9C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAACsF,UAAU,CAAC;IACnFQ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EACD;EACA,IAAIW,iBAAiB,GAAGjF,eAAe,CAAC8C,QAAQ,CAAC;IAC/CoC,iBAAiB,GAAGnH,cAAc,CAACkH,iBAAiB,EAAE,CAAC,CAAC;IACxDE,UAAU,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACjCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACnC,IAAIG,eAAe,GAAG5F,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGxH,cAAc,CAACsH,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,SAAS,GAAGjG,KAAK,CAAC+D,MAAM,CAAC,CAAC;EAC9B,IAAImC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIP,UAAU,CAACQ,MAAM,EAAE;IACrBD,WAAW,CAACC,MAAM,GAAGR,UAAU,CAACQ,MAAM;EACxC;EACA,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCC,MAAM,CAACC,YAAY,CAACL,SAAS,CAACjB,OAAO,CAAC;EACxC,CAAC;EACD,IAAIuB,WAAW,GAAG,SAASA,WAAWA,CAAC9H,CAAC,EAAE;IACxC,IAAIgE,EAAE;IACNhE,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0G,cAAc,CAAC,CAAC;IACxD1G,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC+H,eAAe,CAAC,CAAC;IACzD/G,IAAI,CAACkG,UAAU,CAACc,IAAI,IAAIC,MAAM,CAACxD,QAAQ,CAAC,IAAI,EAAE,EAAEgD,WAAW,CAAC;IAC5DF,SAAS,CAAC,IAAI,CAAC;IACf;IACAI,WAAW,CAAC,CAAC;IACbH,SAAS,CAACjB,OAAO,GAAGqB,MAAM,CAACM,UAAU,CAAC,YAAY;MAChDX,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,EAAE,IAAI,CAAC;IACR,CAACvD,EAAE,GAAGkD,UAAU,CAACiB,MAAM,MAAM,IAAI,IAAInE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAAC4G,UAAU,EAAElH,CAAC,CAAC;EACtF,CAAC;EACDuB,KAAK,CAAC6G,SAAS,CAAC,YAAY;IAC1B,OAAOT,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;EACN;EACA,IAAIU,gBAAgB,GAAG9G,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IAC1CkB,gBAAgB,GAAGzI,cAAc,CAACwI,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,gBAAgB,GAAGlH,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IAC1CsB,gBAAgB,GAAG7I,cAAc,CAAC4I,gBAAgB,EAAE,CAAC,CAAC;IACtDE,qBAAqB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3CE,wBAAwB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChD,IAAIG,gBAAgB,GAAGtH,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IAC1C0B,gBAAgB,GAAGjJ,cAAc,CAACgJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,gBAAgB,GAAG1H,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IAC1C8B,iBAAiB,GAAGrJ,cAAc,CAACoJ,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,IAAIG,iBAAiB,GAAG9H,KAAK,CAAC6F,QAAQ,CAAC,KAAK,CAAC;IAC3CkC,iBAAiB,GAAGzJ,cAAc,CAACwJ,iBAAiB,EAAE,CAAC,CAAC;IACxDE,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC5C,IAAIG,iBAAiB,GAAGlI,KAAK,CAAC6F,QAAQ,CAAC,IAAI,CAAC;IAC1CsC,iBAAiB,GAAG7J,cAAc,CAAC4J,iBAAiB,EAAE,CAAC,CAAC;IACxDE,eAAe,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACtCE,kBAAkB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC3C,IAAIG,iBAAiB,GAAG/H,eAAe,CAAC4C,QAAQ,EAAE;MAC9CoF,UAAU,EAAE;IACd,CAAC,CAAC;IACFC,iBAAiB,GAAGlK,cAAc,CAACgK,iBAAiB,EAAE,CAAC,CAAC;IACxDG,cAAc,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACrCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACvC,IAAIG,oBAAoB,GAAGF,cAAc,IAAI,CAACjB,QAAQ;EACtD;EACA,IAAIoB,oBAAoB,GAAGF,cAAc,CAACG,IAAI;IAC5CA,IAAI,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,oBAAoB;EACnE,IAAIE,mBAAmB,GAAG9I,KAAK,CAAC+I,OAAO,CAAC,YAAY;IAClD;MACE;MACA,CAACJ,oBAAoB;MACrB;MACAD,cAAc,CAACM,MAAM,KAAKjH,SAAS,IAAI2G,cAAc,CAACO,UAAU;MAChE;MACAP,cAAc,CAACH,UAAU,IAAInE,UAAU,IAAIsB;IAAU;EAEzD,CAAC,EAAE,CAACiD,oBAAoB,EAAED,cAAc,EAAEtE,UAAU,EAAEsB,UAAU,CAAC,CAAC;EAClE9F,yBAAyB,CAAC,YAAY;IACpC,IAAI6I,cAAc,IAAI,CAACK,mBAAmB,EAAE;MAC1C7B,qBAAqB,CAAC7G,cAAc,CAAC,iBAAiB,CAAC,CAAC;MACxDiH,wBAAwB,CAACjH,cAAc,CAAC,cAAc,CAAC,CAAC;IAC1D;EACF,CAAC,EAAE,CAAC0I,mBAAmB,EAAEL,cAAc,CAAC,CAAC;EACzC,IAAIS,WAAW,GAAGlJ,KAAK,CAAC+I,OAAO,CAAC,YAAY;IAC1C,IAAID,mBAAmB,EAAE;MACvB,OAAO,KAAK;IACd;IACA,IAAID,IAAI,KAAK,CAAC,EAAE;MACd,OAAOzB,qBAAqB;IAC9B;IACA,OAAOJ,kBAAkB;EAC3B,CAAC,EAAE,CAAC8B,mBAAmB,EAAE1B,qBAAqB,EAAEJ,kBAAkB,CAAC,CAAC;EACpE,IAAImC,gBAAgB,GAAGR,oBAAoB,KAAKO,WAAW,GAAGlB,gBAAgB,GAAGJ,YAAY,CAAC;EAC9F,IAAIwB,eAAe,GAAGT,oBAAoB,IAAIE,IAAI,KAAK,CAAC,IAAIK,WAAW;EACvE,IAAIG,YAAY,GAAGV,oBAAoB,IAAIE,IAAI,GAAG,CAAC,IAAIK,WAAW;EAClE;EACA,IAAII,aAAa,GAAG,SAASA,aAAaA,CAAC7K,CAAC,EAAE;IAC5C,IAAIgE,EAAE;IACNgF,WAAW,CAAC,IAAI,CAAC;IACjB,CAAChF,EAAE,GAAGiG,cAAc,CAACa,QAAQ,MAAM,IAAI,IAAI9G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAAC2J,cAAc,EAAEjK,CAAC,CAAC;EAChG,CAAC;EACD,IAAI+K,iBAAiB,GAAGxJ,KAAK,CAAC6F,QAAQ,CAAC,CAAC,CAAC;IACvC4D,iBAAiB,GAAGnL,cAAc,CAACkL,iBAAiB,EAAE,CAAC,CAAC;IACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACzC,IAAIG,iBAAiB,GAAG5J,KAAK,CAAC6F,QAAQ,CAAC,CAAC,CAAC;IACvCgE,iBAAiB,GAAGvL,cAAc,CAACsL,iBAAiB,EAAE,CAAC,CAAC;IACxDE,gBAAgB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACvCE,mBAAmB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC5C,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC/C,IAAIC,WAAW,GAAGF,KAAK,CAACE,WAAW;IACnC,IAAI1H,EAAE;IACNkH,gBAAgB,CAACQ,WAAW,CAAC;IAC7BJ,mBAAmB,CAACK,QAAQ,CAAC,CAAC3H,EAAE,GAAG4D,MAAM,CAACgE,gBAAgB,MAAM,IAAI,IAAI5H,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAACsH,MAAM,EAAE6D,OAAO,CAAC,CAACI,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;EAC/I,CAAC;EACD;EACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,UAAU,EAAE;IACnD,IAAI/H,EAAE;IACNoF,eAAe,CAAC2C,UAAU,CAAC;IAC3B;IACA,IAAI5C,YAAY,KAAK4C,UAAU,EAAE;MAC/B,CAAC/H,EAAE,GAAGiG,cAAc,CAACO,UAAU,MAAM,IAAI,IAAIxG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1D,IAAI,CAAC2J,cAAc,EAAE8B,UAAU,CAAC;IAC3G;EACF,CAAC;EACD;EACAxK,KAAK,CAAC6G,SAAS,CAAC,YAAY;IAC1B,IAAI4D,OAAO,GAAG3G,aAAa,CAACkB,OAAO;IACnC,IAAIyD,cAAc,IAAIS,WAAW,IAAIuB,OAAO,EAAE;MAC5C,IAAIC,eAAe,GAAGrB,YAAY,GAAGoB,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACN,WAAW,GAAGM,OAAO,CAACI,WAAW;MAC5H,IAAI7C,gBAAgB,KAAK0C,eAAe,EAAE;QACxCzC,mBAAmB,CAACyC,eAAe,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAACjC,cAAc,EAAES,WAAW,EAAEhG,QAAQ,EAAEmG,YAAY,EAAEjB,eAAe,CAAC,CAAC;EAC1E;EACA;EACApI,KAAK,CAAC6G,SAAS,CAAC,YAAY;IAC1B,IAAI4D,OAAO,GAAG3G,aAAa,CAACkB,OAAO;IACnC,IAAI,OAAO8F,oBAAoB,KAAK,WAAW,IAAI,CAACL,OAAO,IAAI,CAACvB,WAAW,IAAI,CAACP,oBAAoB,EAAE;MACpG;IACF;IACA;IACA,IAAIoC,QAAQ,GAAG,IAAID,oBAAoB,CAAC,YAAY;MAClDzC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAACO,YAAY,CAAC;IAC5C,CAAC,CAAC;IACFD,QAAQ,CAACE,OAAO,CAACR,OAAO,CAAC;IACzB,OAAO,YAAY;MACjBM,QAAQ,CAACG,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAAChC,WAAW,EAAEP,oBAAoB,CAAC,CAAC;EACvC;EACA,IAAIwC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIzC,cAAc,CAAC0C,OAAO,KAAK,IAAI,EAAE;IACnCD,YAAY,GAAG;MACb5H,KAAK,EAAE,CAACd,EAAE,GAAG4B,UAAU,CAACoC,IAAI,MAAM,IAAI,IAAIhE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGS;IACjE,CAAC;EACH,CAAC,MAAM,IAAK,aAAalD,KAAK,CAACqL,cAAc,CAAC3C,cAAc,CAAC0C,OAAO,CAAC,EAAE;IACrED,YAAY,GAAG;MACb5H,KAAK,EAAEmF,cAAc,CAAC0C;IACxB,CAAC;EACH,CAAC,MAAM,IAAI/M,OAAO,CAACqK,cAAc,CAAC0C,OAAO,CAAC,KAAK,QAAQ,EAAE;IACvDD,YAAY,GAAG/M,QAAQ,CAAC;MACtBmF,KAAK,EAAE,CAACb,EAAE,GAAG2B,UAAU,CAACoC,IAAI,MAAM,IAAI,IAAI/D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGQ;IACjE,CAAC,EAAEwF,cAAc,CAAC0C,OAAO,CAAC;EAC5B,CAAC,MAAM;IACLD,YAAY,GAAG;MACb5H,KAAK,EAAEmF,cAAc,CAAC0C;IACxB,CAAC;EACH;EACA,IAAIE,YAAY,GAAGtL,KAAK,CAAC+I,OAAO,CAAC,YAAY;IAC3C,IAAIwC,OAAO,GAAG,SAASA,OAAOA,CAACtJ,GAAG,EAAE;MAClC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACuJ,QAAQ,CAACnN,OAAO,CAAC4D,GAAG,CAAC,CAAC;IACpD,CAAC;IACD,IAAI,CAACwG,cAAc,IAAIS,WAAW,EAAE;MAClC,OAAOnH,SAAS;IAClB;IACA,IAAIwJ,OAAO,CAAClH,UAAU,CAACoC,IAAI,CAAC,EAAE;MAC5B,OAAOpC,UAAU,CAACoC,IAAI;IACxB;IACA,IAAI8E,OAAO,CAACrI,QAAQ,CAAC,EAAE;MACrB,OAAOA,QAAQ;IACjB;IACA,IAAIqI,OAAO,CAAChI,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK;IACd;IACA,IAAIgI,OAAO,CAACJ,YAAY,CAAC5H,KAAK,CAAC,EAAE;MAC/B,OAAO4H,YAAY,CAAC5H,KAAK;IAC3B;IACA,OAAOxB,SAAS;EAClB,CAAC,EAAE,CAAC0G,cAAc,EAAES,WAAW,EAAE3F,KAAK,EAAE4H,YAAY,CAAC5H,KAAK,EAAE4F,gBAAgB,CAAC,CAAC;EAC9E;EACA;EACA,IAAI3E,OAAO,EAAE;IACX,OAAO,aAAaxE,KAAK,CAAC0B,aAAa,CAACpB,QAAQ,EAAE;MAChDiE,KAAK,EAAE,CAAC5B,EAAE,GAAG0B,UAAU,CAACoC,IAAI,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,OAAOO,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,EAAE;MAC3GuI,MAAM,EAAErG,YAAY;MACpBG,QAAQ,EAAED,YAAY;MACtBoG,KAAK,EAAErH,UAAU,CAACqH,KAAK;MACvB7I,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZa,SAAS,EAAEA,SAAS;MACpBN,SAAS,EAAEA,SAAS;MACpBqI,SAAS,EAAEtH,UAAU,CAACsH,SAAS;MAC/BC,QAAQ,EAAEvH,UAAU,CAACuH,QAAQ;MAC7BC,SAAS,EAAExH,UAAU,CAACwH;IACxB,CAAC,CAAC;EACJ;EACA;EACA;EACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIvD,UAAU,GAAGG,cAAc,CAACH,UAAU;MACxCwD,MAAM,GAAGrD,cAAc,CAACqD,MAAM;IAChC,IAAI,CAACxD,UAAU,EAAE,OAAO,IAAI;IAC5B,IAAIyD,aAAa;IACjB,IAAID,MAAM,EAAE;MACVC,aAAa,GAAGD,MAAM;IACxB,CAAC,MAAM;MACLC,aAAa,GAAGnI,UAAU,CAACoI,MAAM;IACnC;IACA,OAAO,aAAajM,KAAK,CAAC0B,aAAa,CAAC,GAAG,EAAE;MAC3CwK,GAAG,EAAE,QAAQ;MACbpJ,SAAS,EAAE,EAAE,CAACqJ,MAAM,CAACtJ,SAAS,EAAE,SAAS,CAAC;MAC1CuJ,OAAO,EAAE9C,aAAa;MACtB,YAAY,EAAEzF,UAAU,CAACoI;IAC3B,CAAC,EAAED,aAAa,CAAC;EACnB,CAAC;EACD;EACA,IAAIK,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAI,CAACjI,UAAU,EAAE;IACjB,IAAIkI,IAAI,GAAGjI,UAAU,CAACiI,IAAI;MACxBlB,OAAO,GAAG/G,UAAU,CAAC+G,OAAO;IAC9B,IAAImB,SAAS,GAAG5M,OAAO,CAACyL,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIvH,UAAU,CAACiB,IAAI;IACtD,IAAI0H,SAAS,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,EAAE;IAC9D,OAAO3H,WAAW,CAAC4G,QAAQ,CAAC,MAAM,CAAC,GAAG,aAAaxL,KAAK,CAAC0B,aAAa,CAACrB,OAAO,EAAE;MAC9E6L,GAAG,EAAE,MAAM;MACX3I,KAAK,EAAE6H,OAAO,KAAK,KAAK,GAAG,EAAE,GAAGmB;IAClC,CAAC,EAAE,aAAavM,KAAK,CAAC0B,aAAa,CAACvB,WAAW,EAAE;MAC/CqC,GAAG,EAAEwB,WAAW;MAChBlB,SAAS,EAAE,EAAE,CAACqJ,MAAM,CAACtJ,SAAS,EAAE,OAAO,CAAC;MACxCuJ,OAAO,EAAElH,WAAW;MACpB,YAAY,EAAEsH;IAChB,CAAC,EAAEF,IAAI,IAAI,aAAatM,KAAK,CAAC0B,aAAa,CAACnC,YAAY,EAAE;MACxDkN,IAAI,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACb,CAAC;EACD;EACA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAI,CAAChH,UAAU,EAAE;IACjB,IAAIiH,QAAQ,GAAGhH,UAAU,CAACgH,QAAQ;MAChCL,IAAI,GAAG3G,UAAU,CAAC2G,IAAI;IACxB,IAAIM,YAAY,GAAG5K,MAAM,CAAC2K,QAAQ,CAAC;IACnC,IAAIE,SAAS,GAAG7K,MAAM,CAACsK,IAAI,CAAC;IAC5B,IAAIQ,SAAS,GAAG/G,MAAM,GAAGpE,OAAO,CAACiL,YAAY,CAAC,CAAC,CAAC,EAAE/I,UAAU,CAACkC,MAAM,CAAC,GAAGpE,OAAO,CAACiL,YAAY,CAAC,CAAC,CAAC,EAAE/I,UAAU,CAACpE,IAAI,CAAC;IAChH,IAAIsN,SAAS,GAAGhH,MAAM,GAAGlC,UAAU,CAACkC,MAAM,GAAGlC,UAAU,CAACpE,IAAI;IAC5D,IAAI+M,SAAS,GAAG,OAAOM,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGC,SAAS;IACrE,OAAO,aAAa/M,KAAK,CAAC0B,aAAa,CAACrB,OAAO,EAAE;MAC/C6L,GAAG,EAAE,MAAM;MACX3I,KAAK,EAAEuJ;IACT,CAAC,EAAE,aAAa9M,KAAK,CAAC0B,aAAa,CAACvB,WAAW,EAAE;MAC/C2C,SAAS,EAAEtD,UAAU,CAAC,EAAE,CAAC2M,MAAM,CAACtJ,SAAS,EAAE,OAAO,CAAC,EAAEkD,MAAM,IAAI,EAAE,CAACoG,MAAM,CAACtJ,SAAS,EAAE,eAAe,CAAC,CAAC;MACrGuJ,OAAO,EAAE7F,WAAW;MACpB,YAAY,EAAEiG;IAChB,CAAC,EAAEzG,MAAM,GAAGpE,OAAO,CAACkL,SAAS,CAAC,CAAC,CAAC,EAAE,aAAa7M,KAAK,CAAC0B,aAAa,CAACrC,aAAa,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAGsC,OAAO,CAACkL,SAAS,CAAC,CAAC,CAAC,EAAE,aAAa7M,KAAK,CAAC0B,aAAa,CAACpC,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACtL,CAAC;EACD,IAAI0N,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,cAAc,EAAE;IAC/D,OAAO,CAACA,cAAc,IAAInB,YAAY,CAAC,CAAC,EAAEO,UAAU,CAAC,CAAC,EAAEK,UAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EACD,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAACC,YAAY,EAAE;IACzD,OAAO,CAACA,YAAY,IAAI,aAAanN,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;MAC/D,aAAa,EAAE,IAAI;MACnBwK,GAAG,EAAE;IACP,CAAC,EAAE9J,YAAY,CAAC,EAAEsG,cAAc,CAACM,MAAM,EAAEgE,gBAAgB,CAACG,YAAY,CAAC,CAAC;EAC1E,CAAC;EACD,OAAO,aAAanN,KAAK,CAAC0B,aAAa,CAAChC,cAAc,EAAE;IACtDsK,QAAQ,EAAEA,QAAQ;IAClB/G,QAAQ,EAAE,CAAC0F,oBAAoB,IAAIO;EACrC,CAAC,EAAE,UAAUkE,SAAS,EAAE;IACtB,IAAIC,WAAW;IACf,OAAO,aAAarN,KAAK,CAAC0B,aAAa,CAACf,eAAe,EAAE;MACvDwK,YAAY,EAAEA,YAAY;MAC1BmC,eAAe,EAAE3E,oBAAoB;MACrC4E,UAAU,EAAEpE;IACd,CAAC,EAAE,aAAanJ,KAAK,CAAC0B,aAAa,CAACjB,UAAU,EAAErC,QAAQ,CAAC;MACvD0E,SAAS,EAAEtD,UAAU,EAAE6N,WAAW,GAAG,CAAC,CAAC,EAAElP,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,GAAG,CAAC,CAACsJ,MAAM,CAACnJ,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAE7E,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,WAAW,CAAC,EAAEI,QAAQ,CAAC,EAAE9E,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,WAAW,CAAC,EAAE4F,cAAc,CAAC,EAAEtK,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,cAAc,CAAC,EAAE8F,oBAAoB,IAAIE,IAAI,KAAK,CAAC,CAAC,EAAE1K,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,uBAAuB,CAAC,EAAEuG,eAAe,CAAC,EAAEjL,eAAe,CAACkP,WAAW,EAAE,EAAE,CAAClB,MAAM,CAACtJ,SAAS,EAAE,yBAAyB,CAAC,EAAEwG,YAAY,CAAC,EAAEgE,WAAW,GAAGvK,SAAS,CAAC;MAC5kBC,KAAK,EAAE3E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2E,KAAK,CAAC,EAAE;QACnCyK,eAAe,EAAEnE,YAAY,GAAGR,IAAI,GAAG9G;MACzC,CAAC,CAAC;MACFuB,SAAS,EAAEA,SAAS;MACpBd,GAAG,EAAEzC,UAAU,CAACqN,SAAS,EAAEtJ,aAAa,EAAEtB,GAAG,CAAC;MAC9CoB,SAAS,EAAEA,SAAS;MACpBwI,OAAO,EAAExH,WAAW,CAAC4G,QAAQ,CAAC,MAAM,CAAC,GAAGtG,WAAW,GAAGnD,SAAS;MAC/D,YAAY,EAAEuJ,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACmC,QAAQ,CAAC,CAAC;MACjGlK,KAAK,EAAEA;IACT,CAAC,EAAEU,SAAS,CAAC,EAAE,aAAajE,KAAK,CAAC0B,aAAa,CAAChB,QAAQ,EAAE;MACxDgN,cAAc,EAAE/E,oBAAoB,IAAI,CAACO,WAAW;MACpDzC,IAAI,EAAEvD,QAAQ;MACd2F,IAAI,EAAEA,IAAI;MACV8E,KAAK,EAAEjE,aAAa;MACpBY,QAAQ,EAAER,gBAAgB;MAC1Bb,UAAU,EAAEsB;IACd,CAAC,EAAE,UAAUqD,IAAI,EAAET,YAAY,EAAE;MAC/B,IAAIU,UAAU,GAAGD,IAAI;MACrB,IAAIA,IAAI,CAACzO,MAAM,IAAIgO,YAAY,IAAI7B,YAAY,EAAE;QAC/CuC,UAAU,GAAG,aAAa7N,KAAK,CAAC0B,aAAa,CAAC,MAAM,EAAE;UACpDwK,GAAG,EAAE,cAAc;UACnB,aAAa,EAAE;QACjB,CAAC,EAAE2B,UAAU,CAAC;MAChB;MACA,IAAIC,cAAc,GAAGlN,kBAAkB,CAAC2B,KAAK,EAAE,aAAavC,KAAK,CAAC0B,aAAa,CAAC1B,KAAK,CAAC+N,QAAQ,EAAE,IAAI,EAAEF,UAAU,EAAEX,cAAc,CAACC,YAAY,CAAC,CAAC,CAAC;MAChJ,OAAOW,cAAc;IACvB,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAezL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}