{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nfunction _default(a, b) {\n  return a = +a, b = +b, function (t) {\n    return a * (1 - t) + b * t;\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "a", "b", "t"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/number.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nfunction _default(a, b) {\n  return a = +a, b = +b, function (t) {\n    return a * (1 - t) + b * t;\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,EAAE,UAAUC,CAAC,EAAE;IAClC,OAAOF,CAAC,IAAI,CAAC,GAAGE,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}