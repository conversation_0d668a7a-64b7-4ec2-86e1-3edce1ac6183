{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.classNamesShape = exports.timeoutsShape = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar timeoutsShape = process.env.NODE_ENV !== 'production' ? _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.shape({\n  enter: _propTypes.default.number,\n  exit: _propTypes.default.number,\n  appear: _propTypes.default.number\n}).isRequired]) : null;\nexports.timeoutsShape = timeoutsShape;\nvar classNamesShape = process.env.NODE_ENV !== 'production' ? _propTypes.default.oneOfType([_propTypes.default.string, _propTypes.default.shape({\n  enter: _propTypes.default.string,\n  exit: _propTypes.default.string,\n  active: _propTypes.default.string\n}), _propTypes.default.shape({\n  enter: _propTypes.default.string,\n  enterDone: _propTypes.default.string,\n  enterActive: _propTypes.default.string,\n  exit: _propTypes.default.string,\n  exitDone: _propTypes.default.string,\n  exitActive: _propTypes.default.string\n})]) : null;\nexports.classNamesShape = classNamesShape;", "map": {"version": 3, "names": ["exports", "__esModule", "classNamesShape", "timeoutsShape", "_propTypes", "_interopRequireDefault", "require", "obj", "default", "process", "env", "NODE_ENV", "oneOfType", "number", "shape", "enter", "exit", "appear", "isRequired", "string", "active", "enterDone", "enterActive", "exitDone", "exitActive"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-transition-group/utils/PropTypes.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.classNamesShape = exports.timeoutsShape = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar timeoutsShape = process.env.NODE_ENV !== 'production' ? _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.shape({\n  enter: _propTypes.default.number,\n  exit: _propTypes.default.number,\n  appear: _propTypes.default.number\n}).isRequired]) : null;\nexports.timeoutsShape = timeoutsShape;\nvar classNamesShape = process.env.NODE_ENV !== 'production' ? _propTypes.default.oneOfType([_propTypes.default.string, _propTypes.default.shape({\n  enter: _propTypes.default.string,\n  exit: _propTypes.default.string,\n  active: _propTypes.default.string\n}), _propTypes.default.shape({\n  enter: _propTypes.default.string,\n  enterDone: _propTypes.default.string,\n  enterActive: _propTypes.default.string,\n  exit: _propTypes.default.string,\n  exitDone: _propTypes.default.string,\n  exitActive: _propTypes.default.string\n})]) : null;\nexports.classNamesShape = classNamesShape;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,eAAe,GAAGF,OAAO,CAACG,aAAa,GAAG,KAAK,CAAC;AAExD,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACN,UAAU,GAAGM,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9F,IAAIJ,aAAa,GAAGM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,UAAU,CAACI,OAAO,CAACI,SAAS,CAAC,CAACR,UAAU,CAACI,OAAO,CAACK,MAAM,EAAET,UAAU,CAACI,OAAO,CAACM,KAAK,CAAC;EAC5IC,KAAK,EAAEX,UAAU,CAACI,OAAO,CAACK,MAAM;EAChCG,IAAI,EAAEZ,UAAU,CAACI,OAAO,CAACK,MAAM;EAC/BI,MAAM,EAAEb,UAAU,CAACI,OAAO,CAACK;AAC7B,CAAC,CAAC,CAACK,UAAU,CAAC,CAAC,GAAG,IAAI;AACtBlB,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrC,IAAID,eAAe,GAAGO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,UAAU,CAACI,OAAO,CAACI,SAAS,CAAC,CAACR,UAAU,CAACI,OAAO,CAACW,MAAM,EAAEf,UAAU,CAACI,OAAO,CAACM,KAAK,CAAC;EAC9IC,KAAK,EAAEX,UAAU,CAACI,OAAO,CAACW,MAAM;EAChCH,IAAI,EAAEZ,UAAU,CAACI,OAAO,CAACW,MAAM;EAC/BC,MAAM,EAAEhB,UAAU,CAACI,OAAO,CAACW;AAC7B,CAAC,CAAC,EAAEf,UAAU,CAACI,OAAO,CAACM,KAAK,CAAC;EAC3BC,KAAK,EAAEX,UAAU,CAACI,OAAO,CAACW,MAAM;EAChCE,SAAS,EAAEjB,UAAU,CAACI,OAAO,CAACW,MAAM;EACpCG,WAAW,EAAElB,UAAU,CAACI,OAAO,CAACW,MAAM;EACtCH,IAAI,EAAEZ,UAAU,CAACI,OAAO,CAACW,MAAM;EAC/BI,QAAQ,EAAEnB,UAAU,CAACI,OAAO,CAACW,MAAM;EACnCK,UAAU,EAAEpB,UAAU,CAACI,OAAO,CAACW;AACjC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACXnB,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script"}