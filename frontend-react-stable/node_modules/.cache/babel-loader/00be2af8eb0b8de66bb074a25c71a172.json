{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    var _classNames;\n    var prefixCls = _ref.prefixCls,\n      onExpand = _ref.onExpand,\n      record = _ref.record,\n      expanded = _ref.expanded,\n      expandable = _ref.expandable;\n    var iconPrefix = \"\".concat(prefixCls, \"-row-expand-icon\");\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: function onClick(e) {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(iconPrefix, \"-spaced\"), !expandable), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-expanded\"), expandable && expanded), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-collapsed\"), expandable && !expanded), _classNames)),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "renderExpandIcon", "locale", "expandIcon", "_ref", "_classNames", "prefixCls", "onExpand", "record", "expanded", "expandable", "iconPrefix", "concat", "createElement", "type", "onClick", "e", "stopPropagation", "className", "collapse", "expand"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/ExpandIcon.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    var _classNames;\n    var prefixCls = _ref.prefixCls,\n      onExpand = _ref.onExpand,\n      record = _ref.record,\n      expanded = _ref.expanded,\n      expandable = _ref.expandable;\n    var iconPrefix = \"\".concat(prefixCls, \"-row-expand-icon\");\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: function onClick(e) {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, (_classNames = {}, _defineProperty(_classNames, \"\".concat(iconPrefix, \"-spaced\"), !expandable), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-expanded\"), expandable && expanded), _defineProperty(_classNames, \"\".concat(iconPrefix, \"-collapsed\"), expandable && !expanded), _classNames)),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChC,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;IAC/B,IAAIC,WAAW;IACf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;MAC5BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;MACxBC,MAAM,GAAGJ,IAAI,CAACI,MAAM;MACpBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;MACxBC,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC9B,IAAIC,UAAU,GAAG,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,kBAAkB,CAAC;IACzD,OAAO,aAAaN,KAAK,CAACa,aAAa,CAAC,QAAQ,EAAE;MAChDC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;QAC3BT,QAAQ,CAACC,MAAM,EAAEQ,CAAC,CAAC;QACnBA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB,CAAC;MACDC,SAAS,EAAEnB,UAAU,CAACY,UAAU,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAEP,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,SAAS,CAAC,EAAE,CAACD,UAAU,CAAC,EAAEZ,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,WAAW,CAAC,EAAED,UAAU,IAAID,QAAQ,CAAC,EAAEX,eAAe,CAACO,WAAW,EAAE,EAAE,CAACO,MAAM,CAACD,UAAU,EAAE,YAAY,CAAC,EAAED,UAAU,IAAI,CAACD,QAAQ,CAAC,EAAEJ,WAAW,CAAC,CAAC;MACrU,YAAY,EAAEI,QAAQ,GAAGP,MAAM,CAACiB,QAAQ,GAAGjB,MAAM,CAACkB,MAAM;MACxD,eAAe,EAAEX;IACnB,CAAC,CAAC;EACJ,CAAC;AACH;AACA,eAAeR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}