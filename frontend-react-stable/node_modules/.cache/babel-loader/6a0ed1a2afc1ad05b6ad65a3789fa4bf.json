{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = cross;\nfunction length(array) {\n  return array.length | 0;\n}\nfunction empty(length) {\n  return !(length > 0);\n}\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\nfunction cross() {\n  for (var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++) {\n    values[_key] = arguments[_key];\n  }\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "cross", "length", "array", "empty", "arrayify", "values", "Array", "from", "reducer", "reduce", "_len", "arguments", "_key", "pop", "map", "lengths", "j", "index", "fill", "product", "some", "push", "i"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/cross.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = cross;\n\nfunction length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nfunction cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AAEvB,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,GAAG,CAAC;AACzB;AAEA,SAASE,KAAKA,CAACF,MAAM,EAAE;EACrB,OAAO,EAAEA,MAAM,GAAG,CAAC,CAAC;AACtB;AAEA,SAASG,QAAQA,CAACC,MAAM,EAAE;EACxB,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,GAAGA,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACF,MAAM,CAAC;AACvF;AAEA,SAASG,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOJ,MAAM,IAAII,MAAM,CAAC,GAAGJ,MAAM,CAAC;AACpC;AAEA,SAASL,KAAKA,CAAA,EAAY;EAAA,SAAAU,IAAA,GAAAC,SAAA,CAAAV,MAAA,EAARI,MAAM,OAAAC,KAAA,CAAAI,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAANP,MAAM,CAAAO,IAAA,IAAAD,SAAA,CAAAC,IAAA;EAAA;EACtB,MAAMH,MAAM,GAAG,OAAOJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,IAAIO,OAAO,CAACH,MAAM,CAACQ,GAAG,CAAC,CAAC,CAAC;EACvFR,MAAM,GAAGA,MAAM,CAACS,GAAG,CAACV,QAAQ,CAAC;EAC7B,MAAMW,OAAO,GAAGV,MAAM,CAACS,GAAG,CAACb,MAAM,CAAC;EAClC,MAAMe,CAAC,GAAGX,MAAM,CAACJ,MAAM,GAAG,CAAC;EAC3B,MAAMgB,KAAK,GAAG,IAAIX,KAAK,CAACU,CAAC,GAAG,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAIH,CAAC,GAAG,CAAC,IAAID,OAAO,CAACK,IAAI,CAACjB,KAAK,CAAC,EAAE,OAAOgB,OAAO;EAEhD,OAAO,IAAI,EAAE;IACXA,OAAO,CAACE,IAAI,CAACJ,KAAK,CAACH,GAAG,CAAC,CAACE,CAAC,EAAEM,CAAC,KAAKjB,MAAM,CAACiB,CAAC,CAAC,CAACN,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAIM,CAAC,GAAGN,CAAC;IAET,OAAO,EAAEC,KAAK,CAACK,CAAC,CAAC,KAAKP,OAAO,CAACO,CAAC,CAAC,EAAE;MAChC,IAAIA,CAAC,KAAK,CAAC,EAAE,OAAOb,MAAM,GAAGU,OAAO,CAACL,GAAG,CAACL,MAAM,CAAC,GAAGU,OAAO;MAC1DF,KAAK,CAACK,CAAC,EAAE,CAAC,GAAG,CAAC;IAChB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}