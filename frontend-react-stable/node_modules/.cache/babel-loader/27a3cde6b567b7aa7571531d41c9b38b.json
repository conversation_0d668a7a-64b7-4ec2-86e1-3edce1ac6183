{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\n/* eslint-disable no-lonely-if */\n/**\n * Legacy code. Should avoid to use if you are new to import these code.\n */\nimport warning from \"rc-util/es/warning\";\nimport React from 'react';\nimport TreeNode from './TreeNode';\nimport getEntity from './utils/keyUtil';\nexport { getPosition, isTreeNode } from './utils/treeUtil';\nexport function arrDel(list, value) {\n  if (!list) return [];\n  var clone = list.slice();\n  var index = clone.indexOf(value);\n  if (index >= 0) {\n    clone.splice(index, 1);\n  }\n  return clone;\n}\nexport function arrAdd(list, value) {\n  var clone = (list || []).slice();\n  if (clone.indexOf(value) === -1) {\n    clone.push(value);\n  }\n  return clone;\n}\nexport function posToArr(pos) {\n  return pos.split('-');\n}\nexport function getDragChildrenKeys(dragNodeKey, keyEntities) {\n  // not contains self\n  // self for left or right drag\n  var dragChildrenKeys = [];\n  var entity = getEntity(keyEntities, dragNodeKey);\n  function dig() {\n    var list = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    list.forEach(function (_ref) {\n      var key = _ref.key,\n        children = _ref.children;\n      dragChildrenKeys.push(key);\n      dig(children);\n    });\n  }\n  dig(entity.children);\n  return dragChildrenKeys;\n}\nexport function isLastChild(treeNodeEntity) {\n  if (treeNodeEntity.parent) {\n    var posArr = posToArr(treeNodeEntity.pos);\n    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;\n  }\n  return false;\n}\nexport function isFirstChild(treeNodeEntity) {\n  var posArr = posToArr(treeNodeEntity.pos);\n  return Number(posArr[posArr.length - 1]) === 0;\n}\n// Only used when drag, not affect SSR.\nexport function calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeys, direction) {\n  var _abstractDropNodeEnti;\n  var clientX = event.clientX,\n    clientY = event.clientY;\n  var _event$target$getBoun = event.target.getBoundingClientRect(),\n    top = _event$target$getBoun.top,\n    height = _event$target$getBoun.height;\n  // optional chain for testing\n  var horizontalMouseOffset = (direction === 'rtl' ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);\n  var rawDropLevelOffset = (horizontalMouseOffset - 12) / indent;\n  // find abstract drop node by horizontal offset\n  var abstractDropNodeEntity = getEntity(keyEntities, targetNode.props.eventKey);\n  if (clientY < top + height / 2) {\n    // first half, set abstract drop node to previous node\n    var nodeIndex = flattenedNodes.findIndex(function (flattenedNode) {\n      return flattenedNode.key === abstractDropNodeEntity.key;\n    });\n    var prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;\n    var prevNodeKey = flattenedNodes[prevNodeIndex].key;\n    abstractDropNodeEntity = getEntity(keyEntities, prevNodeKey);\n  }\n  var initialAbstractDropNodeKey = abstractDropNodeEntity.key;\n  var abstractDragOverEntity = abstractDropNodeEntity;\n  var dragOverNodeKey = abstractDropNodeEntity.key;\n  var dropPosition = 0;\n  var dropLevelOffset = 0;\n  // Only allow cross level drop when dragging on a non-expanded node\n  if (!expandKeys.includes(initialAbstractDropNodeKey)) {\n    for (var i = 0; i < rawDropLevelOffset; i += 1) {\n      if (isLastChild(abstractDropNodeEntity)) {\n        abstractDropNodeEntity = abstractDropNodeEntity.parent;\n        dropLevelOffset += 1;\n      } else {\n        break;\n      }\n    }\n  }\n  var abstractDragDataNode = dragNode.props.data;\n  var abstractDropDataNode = abstractDropNodeEntity.node;\n  var dropAllowed = true;\n  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({\n    dragNode: abstractDragDataNode,\n    dropNode: abstractDropDataNode,\n    dropPosition: -1\n  }) && abstractDropNodeEntity.key === targetNode.props.eventKey) {\n    // first half of first node in first level\n    dropPosition = -1;\n  } else if ((abstractDragOverEntity.children || []).length && expandKeys.includes(dragOverNodeKey)) {\n    // drop on expanded node\n    // only allow drop inside\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 0\n    })) {\n      dropPosition = 0;\n    } else {\n      dropAllowed = false;\n    }\n  } else if (dropLevelOffset === 0) {\n    if (rawDropLevelOffset > -1.5) {\n      // | Node     | <- abstractDropNode\n      // | -^-===== | <- mousePosition\n      // 1. try drop after\n      // 2. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    } else {\n      // | Node     | <- abstractDropNode\n      // | ---==^== | <- mousePosition\n      // whether it has children or doesn't has children\n      // always\n      // 1. try drop inside\n      // 2. try drop after\n      // 3. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 0\n      })) {\n        dropPosition = 0;\n      } else if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    }\n  } else {\n    // | Node1 | <- abstractDropNode\n    //      |  Node2  |\n    // --^--|----=====| <- mousePosition\n    // 1. try insert after Node1\n    // 2. do not allow drop\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 1\n    })) {\n      dropPosition = 1;\n    } else {\n      dropAllowed = false;\n    }\n  }\n  return {\n    dropPosition: dropPosition,\n    dropLevelOffset: dropLevelOffset,\n    dropTargetKey: abstractDropNodeEntity.key,\n    dropTargetPos: abstractDropNodeEntity.pos,\n    dragOverNodeKey: dragOverNodeKey,\n    dropContainerKey: dropPosition === 0 ? null : ((_abstractDropNodeEnti = abstractDropNodeEntity.parent) === null || _abstractDropNodeEnti === void 0 ? void 0 : _abstractDropNodeEnti.key) || null,\n    dropAllowed: dropAllowed\n  };\n}\n/**\n * Return selectedKeys according with multiple prop\n * @param selectedKeys\n * @param props\n * @returns [string]\n */\nexport function calcSelectedKeys(selectedKeys, props) {\n  if (!selectedKeys) return undefined;\n  var multiple = props.multiple;\n  if (multiple) {\n    return selectedKeys.slice();\n  }\n  if (selectedKeys.length) {\n    return [selectedKeys[0]];\n  }\n  return selectedKeys;\n}\nvar internalProcessProps = function internalProcessProps(props) {\n  return props;\n};\nexport function convertDataToTree(treeData, processor) {\n  if (!treeData) return [];\n  var _ref2 = processor || {},\n    _ref2$processProps = _ref2.processProps,\n    processProps = _ref2$processProps === void 0 ? internalProcessProps : _ref2$processProps;\n  var list = Array.isArray(treeData) ? treeData : [treeData];\n  return list.map(function (_ref3) {\n    var children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, _excluded);\n    var childrenNodes = convertDataToTree(children, processor);\n    return /*#__PURE__*/React.createElement(TreeNode, _extends({\n      key: props.key\n    }, processProps(props)), childrenNodes);\n  });\n}\n/**\n * Parse `checkedKeys` to { checkedKeys, halfCheckedKeys } style\n */\nexport function parseCheckedKeys(keys) {\n  if (!keys) {\n    return null;\n  }\n  // Convert keys to object format\n  var keyProps;\n  if (Array.isArray(keys)) {\n    // [Legacy] Follow the api doc\n    keyProps = {\n      checkedKeys: keys,\n      halfCheckedKeys: undefined\n    };\n  } else if (_typeof(keys) === 'object') {\n    keyProps = {\n      checkedKeys: keys.checked || undefined,\n      halfCheckedKeys: keys.halfChecked || undefined\n    };\n  } else {\n    warning(false, '`checkedKeys` is not an array or an object');\n    return null;\n  }\n  return keyProps;\n}\n/**\n * If user use `autoExpandParent` we should get the list of parent node\n * @param keyList\n * @param keyEntities\n */\nexport function conductExpandParent(keyList, keyEntities) {\n  var expandedKeys = new Set();\n  function conductUp(key) {\n    if (expandedKeys.has(key)) return;\n    var entity = getEntity(keyEntities, key);\n    if (!entity) return;\n    expandedKeys.add(key);\n    var parent = entity.parent,\n      node = entity.node;\n    if (node.disabled) return;\n    if (parent) {\n      conductUp(parent.key);\n    }\n  }\n  (keyList || []).forEach(function (key) {\n    conductUp(key);\n  });\n  return _toConsumableArray(expandedKeys);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_typeof", "_extends", "_objectWithoutProperties", "_excluded", "warning", "React", "TreeNode", "getEntity", "getPosition", "isTreeNode", "arr<PERSON><PERSON>", "list", "value", "clone", "slice", "index", "indexOf", "splice", "arrAdd", "push", "posToArr", "pos", "split", "getDrag<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragNode<PERSON>ey", "keyEntities", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity", "dig", "arguments", "length", "undefined", "for<PERSON>ach", "_ref", "key", "children", "isLastChild", "treeNodeEntity", "parent", "posArr", "Number", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calcDropPosition", "event", "dragNode", "targetNode", "indent", "startMousePosition", "allowDrop", "flattenedNodes", "expandKeys", "direction", "_abstractDropNodeEnti", "clientX", "clientY", "_event$target$getBoun", "target", "getBoundingClientRect", "top", "height", "horizontalMouseOffset", "x", "rawDropLevelOffset", "abstractDropNodeEntity", "props", "eventKey", "nodeIndex", "findIndex", "flattenedNode", "prevNodeIndex", "prevNodeKey", "initialAbstractDropNodeKey", "abstractDragOverEntity", "dragOverNodeKey", "dropPosition", "dropLevelOffset", "includes", "i", "abstractDragDataNode", "data", "abstractDropDataNode", "node", "dropAllowed", "level", "dropNode", "dropTargetKey", "dropTargetPos", "dropContainerKey", "calcSelectedKeys", "<PERSON><PERSON><PERSON><PERSON>", "multiple", "internalProcessProps", "convertDataToTree", "treeData", "processor", "_ref2", "_ref2$processProps", "processProps", "Array", "isArray", "map", "_ref3", "childrenNodes", "createElement", "parseCheckedKeys", "keys", "keyProps", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "checked", "halfChecked", "conductExpandParent", "keyList", "expandedKeys", "Set", "conductUp", "has", "add", "disabled"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/util.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\n/* eslint-disable no-lonely-if */\n/**\n * Legacy code. Should avoid to use if you are new to import these code.\n */\nimport warning from \"rc-util/es/warning\";\nimport React from 'react';\nimport TreeNode from './TreeNode';\nimport getEntity from './utils/keyUtil';\nexport { getPosition, isTreeNode } from './utils/treeUtil';\nexport function arrDel(list, value) {\n  if (!list) return [];\n  var clone = list.slice();\n  var index = clone.indexOf(value);\n  if (index >= 0) {\n    clone.splice(index, 1);\n  }\n  return clone;\n}\nexport function arrAdd(list, value) {\n  var clone = (list || []).slice();\n  if (clone.indexOf(value) === -1) {\n    clone.push(value);\n  }\n  return clone;\n}\nexport function posToArr(pos) {\n  return pos.split('-');\n}\nexport function getDragChildrenKeys(dragNodeKey, keyEntities) {\n  // not contains self\n  // self for left or right drag\n  var dragChildrenKeys = [];\n  var entity = getEntity(keyEntities, dragNodeKey);\n  function dig() {\n    var list = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    list.forEach(function (_ref) {\n      var key = _ref.key,\n        children = _ref.children;\n      dragChildrenKeys.push(key);\n      dig(children);\n    });\n  }\n  dig(entity.children);\n  return dragChildrenKeys;\n}\nexport function isLastChild(treeNodeEntity) {\n  if (treeNodeEntity.parent) {\n    var posArr = posToArr(treeNodeEntity.pos);\n    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;\n  }\n  return false;\n}\nexport function isFirstChild(treeNodeEntity) {\n  var posArr = posToArr(treeNodeEntity.pos);\n  return Number(posArr[posArr.length - 1]) === 0;\n}\n// Only used when drag, not affect SSR.\nexport function calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeys, direction) {\n  var _abstractDropNodeEnti;\n  var clientX = event.clientX,\n    clientY = event.clientY;\n  var _event$target$getBoun = event.target.getBoundingClientRect(),\n    top = _event$target$getBoun.top,\n    height = _event$target$getBoun.height;\n  // optional chain for testing\n  var horizontalMouseOffset = (direction === 'rtl' ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);\n  var rawDropLevelOffset = (horizontalMouseOffset - 12) / indent;\n  // find abstract drop node by horizontal offset\n  var abstractDropNodeEntity = getEntity(keyEntities, targetNode.props.eventKey);\n  if (clientY < top + height / 2) {\n    // first half, set abstract drop node to previous node\n    var nodeIndex = flattenedNodes.findIndex(function (flattenedNode) {\n      return flattenedNode.key === abstractDropNodeEntity.key;\n    });\n    var prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;\n    var prevNodeKey = flattenedNodes[prevNodeIndex].key;\n    abstractDropNodeEntity = getEntity(keyEntities, prevNodeKey);\n  }\n  var initialAbstractDropNodeKey = abstractDropNodeEntity.key;\n  var abstractDragOverEntity = abstractDropNodeEntity;\n  var dragOverNodeKey = abstractDropNodeEntity.key;\n  var dropPosition = 0;\n  var dropLevelOffset = 0;\n  // Only allow cross level drop when dragging on a non-expanded node\n  if (!expandKeys.includes(initialAbstractDropNodeKey)) {\n    for (var i = 0; i < rawDropLevelOffset; i += 1) {\n      if (isLastChild(abstractDropNodeEntity)) {\n        abstractDropNodeEntity = abstractDropNodeEntity.parent;\n        dropLevelOffset += 1;\n      } else {\n        break;\n      }\n    }\n  }\n  var abstractDragDataNode = dragNode.props.data;\n  var abstractDropDataNode = abstractDropNodeEntity.node;\n  var dropAllowed = true;\n  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({\n    dragNode: abstractDragDataNode,\n    dropNode: abstractDropDataNode,\n    dropPosition: -1\n  }) && abstractDropNodeEntity.key === targetNode.props.eventKey) {\n    // first half of first node in first level\n    dropPosition = -1;\n  } else if ((abstractDragOverEntity.children || []).length && expandKeys.includes(dragOverNodeKey)) {\n    // drop on expanded node\n    // only allow drop inside\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 0\n    })) {\n      dropPosition = 0;\n    } else {\n      dropAllowed = false;\n    }\n  } else if (dropLevelOffset === 0) {\n    if (rawDropLevelOffset > -1.5) {\n      // | Node     | <- abstractDropNode\n      // | -^-===== | <- mousePosition\n      // 1. try drop after\n      // 2. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    } else {\n      // | Node     | <- abstractDropNode\n      // | ---==^== | <- mousePosition\n      // whether it has children or doesn't has children\n      // always\n      // 1. try drop inside\n      // 2. try drop after\n      // 3. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 0\n      })) {\n        dropPosition = 0;\n      } else if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    }\n  } else {\n    // | Node1 | <- abstractDropNode\n    //      |  Node2  |\n    // --^--|----=====| <- mousePosition\n    // 1. try insert after Node1\n    // 2. do not allow drop\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 1\n    })) {\n      dropPosition = 1;\n    } else {\n      dropAllowed = false;\n    }\n  }\n  return {\n    dropPosition: dropPosition,\n    dropLevelOffset: dropLevelOffset,\n    dropTargetKey: abstractDropNodeEntity.key,\n    dropTargetPos: abstractDropNodeEntity.pos,\n    dragOverNodeKey: dragOverNodeKey,\n    dropContainerKey: dropPosition === 0 ? null : ((_abstractDropNodeEnti = abstractDropNodeEntity.parent) === null || _abstractDropNodeEnti === void 0 ? void 0 : _abstractDropNodeEnti.key) || null,\n    dropAllowed: dropAllowed\n  };\n}\n/**\n * Return selectedKeys according with multiple prop\n * @param selectedKeys\n * @param props\n * @returns [string]\n */\nexport function calcSelectedKeys(selectedKeys, props) {\n  if (!selectedKeys) return undefined;\n  var multiple = props.multiple;\n  if (multiple) {\n    return selectedKeys.slice();\n  }\n  if (selectedKeys.length) {\n    return [selectedKeys[0]];\n  }\n  return selectedKeys;\n}\nvar internalProcessProps = function internalProcessProps(props) {\n  return props;\n};\nexport function convertDataToTree(treeData, processor) {\n  if (!treeData) return [];\n  var _ref2 = processor || {},\n    _ref2$processProps = _ref2.processProps,\n    processProps = _ref2$processProps === void 0 ? internalProcessProps : _ref2$processProps;\n  var list = Array.isArray(treeData) ? treeData : [treeData];\n  return list.map(function (_ref3) {\n    var children = _ref3.children,\n      props = _objectWithoutProperties(_ref3, _excluded);\n    var childrenNodes = convertDataToTree(children, processor);\n    return /*#__PURE__*/React.createElement(TreeNode, _extends({\n      key: props.key\n    }, processProps(props)), childrenNodes);\n  });\n}\n/**\n * Parse `checkedKeys` to { checkedKeys, halfCheckedKeys } style\n */\nexport function parseCheckedKeys(keys) {\n  if (!keys) {\n    return null;\n  }\n  // Convert keys to object format\n  var keyProps;\n  if (Array.isArray(keys)) {\n    // [Legacy] Follow the api doc\n    keyProps = {\n      checkedKeys: keys,\n      halfCheckedKeys: undefined\n    };\n  } else if (_typeof(keys) === 'object') {\n    keyProps = {\n      checkedKeys: keys.checked || undefined,\n      halfCheckedKeys: keys.halfChecked || undefined\n    };\n  } else {\n    warning(false, '`checkedKeys` is not an array or an object');\n    return null;\n  }\n  return keyProps;\n}\n/**\n * If user use `autoExpandParent` we should get the list of parent node\n * @param keyList\n * @param keyEntities\n */\nexport function conductExpandParent(keyList, keyEntities) {\n  var expandedKeys = new Set();\n  function conductUp(key) {\n    if (expandedKeys.has(key)) return;\n    var entity = getEntity(keyEntities, key);\n    if (!entity) return;\n    expandedKeys.add(key);\n    var parent = entity.parent,\n      node = entity.node;\n    if (node.disabled) return;\n    if (parent) {\n      conductUp(parent.key);\n    }\n  }\n  (keyList || []).forEach(function (key) {\n    conductUp(key);\n  });\n  return _toConsumableArray(expandedKeys);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B;AACA;AACA;AACA;AACA,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,WAAW,EAAEC,UAAU,QAAQ,kBAAkB;AAC1D,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIE,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC;EACxB,IAAIC,KAAK,GAAGF,KAAK,CAACG,OAAO,CAACJ,KAAK,CAAC;EAChC,IAAIG,KAAK,IAAI,CAAC,EAAE;IACdF,KAAK,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACxB;EACA,OAAOF,KAAK;AACd;AACA,OAAO,SAASK,MAAMA,CAACP,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAIC,KAAK,GAAG,CAACF,IAAI,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAChC,IAAID,KAAK,CAACG,OAAO,CAACJ,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IAC/BC,KAAK,CAACM,IAAI,CAACP,KAAK,CAAC;EACnB;EACA,OAAOC,KAAK;AACd;AACA,OAAO,SAASO,QAAQA,CAACC,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;AACvB;AACA,OAAO,SAASC,mBAAmBA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC5D;EACA;EACA,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,MAAM,GAAGpB,SAAS,CAACkB,WAAW,EAAED,WAAW,CAAC;EAChD,SAASI,GAAGA,CAAA,EAAG;IACb,IAAIjB,IAAI,GAAGkB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjFlB,IAAI,CAACqB,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3B,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;QAChBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;MAC1BT,gBAAgB,CAACP,IAAI,CAACe,GAAG,CAAC;MAC1BN,GAAG,CAACO,QAAQ,CAAC;IACf,CAAC,CAAC;EACJ;EACAP,GAAG,CAACD,MAAM,CAACQ,QAAQ,CAAC;EACpB,OAAOT,gBAAgB;AACzB;AACA,OAAO,SAASU,WAAWA,CAACC,cAAc,EAAE;EAC1C,IAAIA,cAAc,CAACC,MAAM,EAAE;IACzB,IAAIC,MAAM,GAAGnB,QAAQ,CAACiB,cAAc,CAAChB,GAAG,CAAC;IACzC,OAAOmB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKO,cAAc,CAACC,MAAM,CAACH,QAAQ,CAACL,MAAM,GAAG,CAAC;EACxF;EACA,OAAO,KAAK;AACd;AACA,OAAO,SAASW,YAAYA,CAACJ,cAAc,EAAE;EAC3C,IAAIE,MAAM,GAAGnB,QAAQ,CAACiB,cAAc,CAAChB,GAAG,CAAC;EACzC,OAAOmB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,OAAO,SAASY,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,cAAc,EAAExB,WAAW,EAAEyB,UAAU,EAAEC,SAAS,EAAE;EACvJ,IAAIC,qBAAqB;EACzB,IAAIC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACzBC,OAAO,GAAGX,KAAK,CAACW,OAAO;EACzB,IAAIC,qBAAqB,GAAGZ,KAAK,CAACa,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAC9DC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;IAC/BC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM;EACvC;EACA,IAAIC,qBAAqB,GAAG,CAACT,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAACJ,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACc,CAAC,KAAK,CAAC,IAAIR,OAAO,CAAC;EAC9K,IAAIS,kBAAkB,GAAG,CAACF,qBAAqB,GAAG,EAAE,IAAId,MAAM;EAC9D;EACA,IAAIiB,sBAAsB,GAAGxD,SAAS,CAACkB,WAAW,EAAEoB,UAAU,CAACmB,KAAK,CAACC,QAAQ,CAAC;EAC9E,IAAIX,OAAO,GAAGI,GAAG,GAAGC,MAAM,GAAG,CAAC,EAAE;IAC9B;IACA,IAAIO,SAAS,GAAGjB,cAAc,CAACkB,SAAS,CAAC,UAAUC,aAAa,EAAE;MAChE,OAAOA,aAAa,CAAClC,GAAG,KAAK6B,sBAAsB,CAAC7B,GAAG;IACzD,CAAC,CAAC;IACF,IAAImC,aAAa,GAAGH,SAAS,IAAI,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC;IACtD,IAAII,WAAW,GAAGrB,cAAc,CAACoB,aAAa,CAAC,CAACnC,GAAG;IACnD6B,sBAAsB,GAAGxD,SAAS,CAACkB,WAAW,EAAE6C,WAAW,CAAC;EAC9D;EACA,IAAIC,0BAA0B,GAAGR,sBAAsB,CAAC7B,GAAG;EAC3D,IAAIsC,sBAAsB,GAAGT,sBAAsB;EACnD,IAAIU,eAAe,GAAGV,sBAAsB,CAAC7B,GAAG;EAChD,IAAIwC,YAAY,GAAG,CAAC;EACpB,IAAIC,eAAe,GAAG,CAAC;EACvB;EACA,IAAI,CAACzB,UAAU,CAAC0B,QAAQ,CAACL,0BAA0B,CAAC,EAAE;IACpD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,kBAAkB,EAAEe,CAAC,IAAI,CAAC,EAAE;MAC9C,IAAIzC,WAAW,CAAC2B,sBAAsB,CAAC,EAAE;QACvCA,sBAAsB,GAAGA,sBAAsB,CAACzB,MAAM;QACtDqC,eAAe,IAAI,CAAC;MACtB,CAAC,MAAM;QACL;MACF;IACF;EACF;EACA,IAAIG,oBAAoB,GAAGlC,QAAQ,CAACoB,KAAK,CAACe,IAAI;EAC9C,IAAIC,oBAAoB,GAAGjB,sBAAsB,CAACkB,IAAI;EACtD,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIzC,YAAY,CAACsB,sBAAsB,CAAC,IAAIA,sBAAsB,CAACoB,KAAK,KAAK,CAAC,IAAI7B,OAAO,GAAGI,GAAG,GAAGC,MAAM,GAAG,CAAC,IAAIX,SAAS,CAAC;IACxHJ,QAAQ,EAAEkC,oBAAoB;IAC9BM,QAAQ,EAAEJ,oBAAoB;IAC9BN,YAAY,EAAE,CAAC;EACjB,CAAC,CAAC,IAAIX,sBAAsB,CAAC7B,GAAG,KAAKW,UAAU,CAACmB,KAAK,CAACC,QAAQ,EAAE;IAC9D;IACAS,YAAY,GAAG,CAAC,CAAC;EACnB,CAAC,MAAM,IAAI,CAACF,sBAAsB,CAACrC,QAAQ,IAAI,EAAE,EAAEL,MAAM,IAAIoB,UAAU,CAAC0B,QAAQ,CAACH,eAAe,CAAC,EAAE;IACjG;IACA;IACA,IAAIzB,SAAS,CAAC;MACZJ,QAAQ,EAAEkC,oBAAoB;MAC9BM,QAAQ,EAAEJ,oBAAoB;MAC9BN,YAAY,EAAE;IAChB,CAAC,CAAC,EAAE;MACFA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM;MACLQ,WAAW,GAAG,KAAK;IACrB;EACF,CAAC,MAAM,IAAIP,eAAe,KAAK,CAAC,EAAE;IAChC,IAAIb,kBAAkB,GAAG,CAAC,GAAG,EAAE;MAC7B;MACA;MACA;MACA;MACA,IAAId,SAAS,CAAC;QACZJ,QAAQ,EAAEkC,oBAAoB;QAC9BM,QAAQ,EAAEJ,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACLQ,WAAW,GAAG,KAAK;MACrB;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIlC,SAAS,CAAC;QACZJ,QAAQ,EAAEkC,oBAAoB;QAC9BM,QAAQ,EAAEJ,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM,IAAI1B,SAAS,CAAC;QACnBJ,QAAQ,EAAEkC,oBAAoB;QAC9BM,QAAQ,EAAEJ,oBAAoB;QAC9BN,YAAY,EAAE;MAChB,CAAC,CAAC,EAAE;QACFA,YAAY,GAAG,CAAC;MAClB,CAAC,MAAM;QACLQ,WAAW,GAAG,KAAK;MACrB;IACF;EACF,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA;IACA,IAAIlC,SAAS,CAAC;MACZJ,QAAQ,EAAEkC,oBAAoB;MAC9BM,QAAQ,EAAEJ,oBAAoB;MAC9BN,YAAY,EAAE;IAChB,CAAC,CAAC,EAAE;MACFA,YAAY,GAAG,CAAC;IAClB,CAAC,MAAM;MACLQ,WAAW,GAAG,KAAK;IACrB;EACF;EACA,OAAO;IACLR,YAAY,EAAEA,YAAY;IAC1BC,eAAe,EAAEA,eAAe;IAChCU,aAAa,EAAEtB,sBAAsB,CAAC7B,GAAG;IACzCoD,aAAa,EAAEvB,sBAAsB,CAAC1C,GAAG;IACzCoD,eAAe,EAAEA,eAAe;IAChCc,gBAAgB,EAAEb,YAAY,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAACtB,qBAAqB,GAAGW,sBAAsB,CAACzB,MAAM,MAAM,IAAI,IAAIc,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAClB,GAAG,KAAK,IAAI;IACjMgD,WAAW,EAAEA;EACf,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,gBAAgBA,CAACC,YAAY,EAAEzB,KAAK,EAAE;EACpD,IAAI,CAACyB,YAAY,EAAE,OAAO1D,SAAS;EACnC,IAAI2D,QAAQ,GAAG1B,KAAK,CAAC0B,QAAQ;EAC7B,IAAIA,QAAQ,EAAE;IACZ,OAAOD,YAAY,CAAC3E,KAAK,CAAC,CAAC;EAC7B;EACA,IAAI2E,YAAY,CAAC3D,MAAM,EAAE;IACvB,OAAO,CAAC2D,YAAY,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,OAAOA,YAAY;AACrB;AACA,IAAIE,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC3B,KAAK,EAAE;EAC9D,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,SAAS4B,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACrD,IAAI,CAACD,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIE,KAAK,GAAGD,SAAS,IAAI,CAAC,CAAC;IACzBE,kBAAkB,GAAGD,KAAK,CAACE,YAAY;IACvCA,YAAY,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAGL,oBAAoB,GAAGK,kBAAkB;EAC1F,IAAIrF,IAAI,GAAGuF,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EAC1D,OAAOlF,IAAI,CAACyF,GAAG,CAAC,UAAUC,KAAK,EAAE;IAC/B,IAAIlE,QAAQ,GAAGkE,KAAK,CAAClE,QAAQ;MAC3B6B,KAAK,GAAG9D,wBAAwB,CAACmG,KAAK,EAAElG,SAAS,CAAC;IACpD,IAAImG,aAAa,GAAGV,iBAAiB,CAACzD,QAAQ,EAAE2D,SAAS,CAAC;IAC1D,OAAO,aAAazF,KAAK,CAACkG,aAAa,CAACjG,QAAQ,EAAEL,QAAQ,CAAC;MACzDiC,GAAG,EAAE8B,KAAK,CAAC9B;IACb,CAAC,EAAE+D,YAAY,CAACjC,KAAK,CAAC,CAAC,EAAEsC,aAAa,CAAC;EACzC,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EACrC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA;EACA,IAAIC,QAAQ;EACZ,IAAIR,KAAK,CAACC,OAAO,CAACM,IAAI,CAAC,EAAE;IACvB;IACAC,QAAQ,GAAG;MACTC,WAAW,EAAEF,IAAI;MACjBG,eAAe,EAAE7E;IACnB,CAAC;EACH,CAAC,MAAM,IAAI/B,OAAO,CAACyG,IAAI,CAAC,KAAK,QAAQ,EAAE;IACrCC,QAAQ,GAAG;MACTC,WAAW,EAAEF,IAAI,CAACI,OAAO,IAAI9E,SAAS;MACtC6E,eAAe,EAAEH,IAAI,CAACK,WAAW,IAAI/E;IACvC,CAAC;EACH,CAAC,MAAM;IACL3B,OAAO,CAAC,KAAK,EAAE,4CAA4C,CAAC;IAC5D,OAAO,IAAI;EACb;EACA,OAAOsG,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,mBAAmBA,CAACC,OAAO,EAAEvF,WAAW,EAAE;EACxD,IAAIwF,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B,SAASC,SAASA,CAACjF,GAAG,EAAE;IACtB,IAAI+E,YAAY,CAACG,GAAG,CAAClF,GAAG,CAAC,EAAE;IAC3B,IAAIP,MAAM,GAAGpB,SAAS,CAACkB,WAAW,EAAES,GAAG,CAAC;IACxC,IAAI,CAACP,MAAM,EAAE;IACbsF,YAAY,CAACI,GAAG,CAACnF,GAAG,CAAC;IACrB,IAAII,MAAM,GAAGX,MAAM,CAACW,MAAM;MACxB2C,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IACpB,IAAIA,IAAI,CAACqC,QAAQ,EAAE;IACnB,IAAIhF,MAAM,EAAE;MACV6E,SAAS,CAAC7E,MAAM,CAACJ,GAAG,CAAC;IACvB;EACF;EACA,CAAC8E,OAAO,IAAI,EAAE,EAAEhF,OAAO,CAAC,UAAUE,GAAG,EAAE;IACrCiF,SAAS,CAACjF,GAAG,CAAC;EAChB,CAAC,CAAC;EACF,OAAOnC,kBAAkB,CAACkH,YAAY,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}