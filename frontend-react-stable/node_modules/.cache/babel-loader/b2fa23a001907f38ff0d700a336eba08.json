{"ast": null, "code": "// This icon file is generated automatically.\nvar CaretDownOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"0 0 1024 1024\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z\"\n      }\n    }]\n  },\n  \"name\": \"caret-down\",\n  \"theme\": \"outlined\"\n};\nexport default CaretDownOutlined;", "map": {"version": 3, "names": ["CaretDownOutlined"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/CaretDownOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CaretDownOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"0 0 1024 1024\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z\" } }] }, \"name\": \"caret-down\", \"theme\": \"outlined\" };\nexport default CaretDownOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,iBAAiB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAqH;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,YAAY;EAAE,OAAO,EAAE;AAAW,CAAC;AACvU,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}