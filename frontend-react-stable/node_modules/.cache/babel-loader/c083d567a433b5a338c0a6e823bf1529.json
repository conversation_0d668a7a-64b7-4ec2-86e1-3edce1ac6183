{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcYears = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar utcYear = (0, _interval.default)(function (date) {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, function (start, end) {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, function (date) {\n  return date.getUTCFullYear();\n}); // An optimized implementation for this simple case.\n\nutcYear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, _interval.default)(function (date) {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\nvar _default = utcYear;\nexports.default = _default;\nvar utcYears = utcYear.range;\nexports.utcYears = utcYears;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcYears", "default", "_interval", "_interopRequireDefault", "require", "obj", "__esModule", "utcYear", "date", "setUTCMonth", "setUTCHours", "step", "setUTCFullYear", "getUTCFullYear", "start", "end", "every", "k", "isFinite", "Math", "floor", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/utcYear.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcYears = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar utcYear = (0, _interval.default)(function (date) {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, function (start, end) {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, function (date) {\n  return date.getUTCFullYear();\n}); // An optimized implementation for this simple case.\n\nutcYear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, _interval.default)(function (date) {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nvar _default = utcYear;\nexports.default = _default;\nvar utcYears = utcYear.range;\nexports.utcYears = utcYears;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAE3C,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIE,OAAO,GAAG,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;EACnDA,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACtBD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;EACvBH,IAAI,CAACI,cAAc,CAACJ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGF,IAAI,CAAC;AACnD,CAAC,EAAE,UAAUG,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACF,cAAc,CAAC,CAAC,GAAGC,KAAK,CAACD,cAAc,CAAC,CAAC;AACtD,CAAC,EAAE,UAAUL,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACK,cAAc,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC,CAAC;;AAEJN,OAAO,CAACS,KAAK,GAAG,UAAUC,CAAC,EAAE;EAC3B,OAAO,CAACC,QAAQ,CAACD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;IAC9FA,IAAI,CAACI,cAAc,CAACO,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGI,CAAC,CAAC,GAAGA,CAAC,CAAC;IAC9DT,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;IACtBD,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;IACvBH,IAAI,CAACI,cAAc,CAACJ,IAAI,CAACK,cAAc,CAAC,CAAC,GAAGF,IAAI,GAAGM,CAAC,CAAC;EACvD,CAAC,CAAC;AACJ,CAAC;AAED,IAAII,QAAQ,GAAGd,OAAO;AACtBT,OAAO,CAACG,OAAO,GAAGoB,QAAQ;AAC1B,IAAIrB,QAAQ,GAAGO,OAAO,CAACe,KAAK;AAC5BxB,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}