{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = newInterval;\nvar t0 = new Date(),\n  t1 = new Date();\nfunction newInterval(floori, offseti, count, field) {\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date() : new Date(+date)), date;\n  }\n  interval.floor = function (date) {\n    return floori(date = new Date(+date)), date;\n  };\n  interval.ceil = function (date) {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n  interval.round = function (date) {\n    var d0 = interval(date),\n      d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n  interval.offset = function (date, step) {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n  interval.range = function (start, stop, step) {\n    var range = [],\n      previous;\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start); while (previous < start && start < stop);\n    return range;\n  };\n  interval.filter = function (test) {\n    return newInterval(function (date) {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, function (date, step) {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n  if (count) {\n    interval.count = function (start, end) {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n    interval.every = function (step) {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? function (d) {\n        return field(d) % step === 0;\n      } : function (d) {\n        return interval.count(0, d) % step === 0;\n      });\n    };\n  }\n  return interval;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "newInterval", "t0", "Date", "t1", "floori", "offseti", "count", "field", "interval", "date", "arguments", "length", "floor", "ceil", "round", "d0", "d1", "offset", "step", "Math", "range", "start", "stop", "previous", "push", "filter", "test", "setTime", "end", "every", "isFinite", "d"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/interval.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = newInterval;\nvar t0 = new Date(),\n    t1 = new Date();\n\nfunction newInterval(floori, offseti, count, field) {\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date() : new Date(+date)), date;\n  }\n\n  interval.floor = function (date) {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = function (date) {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = function (date) {\n    var d0 = interval(date),\n        d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = function (date, step) {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = function (start, stop, step) {\n    var range = [],\n        previous;\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start); while (previous < start && start < stop);\n\n    return range;\n  };\n\n  interval.filter = function (test) {\n    return newInterval(function (date) {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, function (date, step) {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = function (start, end) {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = function (step) {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? function (d) {\n        return field(d) % step === 0;\n      } : function (d) {\n        return interval.count(0, d) % step === 0;\n      });\n    };\n  }\n\n  return interval;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,WAAW;AAC7B,IAAIC,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC;EACfC,EAAE,GAAG,IAAID,IAAI,CAAC,CAAC;AAEnB,SAASF,WAAWA,CAACI,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAClD,SAASC,QAAQA,CAACC,IAAI,EAAE;IACtB,OAAOL,MAAM,CAACK,IAAI,GAAGC,SAAS,CAACC,MAAM,KAAK,CAAC,GAAG,IAAIT,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAAC,CAACO,IAAI,CAAC,CAAC,EAAEA,IAAI;EACnF;EAEAD,QAAQ,CAACI,KAAK,GAAG,UAAUH,IAAI,EAAE;IAC/B,OAAOL,MAAM,CAACK,IAAI,GAAG,IAAIP,IAAI,CAAC,CAACO,IAAI,CAAC,CAAC,EAAEA,IAAI;EAC7C,CAAC;EAEDD,QAAQ,CAACK,IAAI,GAAG,UAAUJ,IAAI,EAAE;IAC9B,OAAOL,MAAM,CAACK,IAAI,GAAG,IAAIP,IAAI,CAACO,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,EAAEL,MAAM,CAACK,IAAI,CAAC,EAAEA,IAAI;EAChF,CAAC;EAEDD,QAAQ,CAACM,KAAK,GAAG,UAAUL,IAAI,EAAE;IAC/B,IAAIM,EAAE,GAAGP,QAAQ,CAACC,IAAI,CAAC;MACnBO,EAAE,GAAGR,QAAQ,CAACK,IAAI,CAACJ,IAAI,CAAC;IAC5B,OAAOA,IAAI,GAAGM,EAAE,GAAGC,EAAE,GAAGP,IAAI,GAAGM,EAAE,GAAGC,EAAE;EACxC,CAAC;EAEDR,QAAQ,CAACS,MAAM,GAAG,UAAUR,IAAI,EAAES,IAAI,EAAE;IACtC,OAAOb,OAAO,CAACI,IAAI,GAAG,IAAIP,IAAI,CAAC,CAACO,IAAI,CAAC,EAAES,IAAI,IAAI,IAAI,GAAG,CAAC,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC,CAAC,EAAET,IAAI;EACnF,CAAC;EAEDD,QAAQ,CAACY,KAAK,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAEJ,IAAI,EAAE;IAC5C,IAAIE,KAAK,GAAG,EAAE;MACVG,QAAQ;IACZF,KAAK,GAAGb,QAAQ,CAACK,IAAI,CAACQ,KAAK,CAAC;IAC5BH,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,CAAC,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC;IAC1C,IAAI,EAAEG,KAAK,GAAGC,IAAI,CAAC,IAAI,EAAEJ,IAAI,GAAG,CAAC,CAAC,EAAE,OAAOE,KAAK,CAAC,CAAC;;IAElD,GAAGA,KAAK,CAACI,IAAI,CAACD,QAAQ,GAAG,IAAIrB,IAAI,CAAC,CAACmB,KAAK,CAAC,CAAC,EAAEhB,OAAO,CAACgB,KAAK,EAAEH,IAAI,CAAC,EAAEd,MAAM,CAACiB,KAAK,CAAC,CAAC,QAAQE,QAAQ,GAAGF,KAAK,IAAIA,KAAK,GAAGC,IAAI;IAExH,OAAOF,KAAK;EACd,CAAC;EAEDZ,QAAQ,CAACiB,MAAM,GAAG,UAAUC,IAAI,EAAE;IAChC,OAAO1B,WAAW,CAAC,UAAUS,IAAI,EAAE;MACjC,IAAIA,IAAI,IAAIA,IAAI,EAAE,OAAOL,MAAM,CAACK,IAAI,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAEA,IAAI,CAACkB,OAAO,CAAClB,IAAI,GAAG,CAAC,CAAC;IAC5E,CAAC,EAAE,UAAUA,IAAI,EAAES,IAAI,EAAE;MACvB,IAAIT,IAAI,IAAIA,IAAI,EAAE;QAChB,IAAIS,IAAI,GAAG,CAAC,EAAE,OAAO,EAAEA,IAAI,IAAI,CAAC,EAAE;UAChC,OAAOb,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5C,CAAC,MAAM,OAAO,EAAES,IAAI,IAAI,CAAC,EAAE;UACzB,OAAOb,OAAO,CAACI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAACiB,IAAI,CAACjB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5C;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIH,KAAK,EAAE;IACTE,QAAQ,CAACF,KAAK,GAAG,UAAUe,KAAK,EAAEO,GAAG,EAAE;MACrC3B,EAAE,CAAC0B,OAAO,CAAC,CAACN,KAAK,CAAC,EAAElB,EAAE,CAACwB,OAAO,CAAC,CAACC,GAAG,CAAC;MACpCxB,MAAM,CAACH,EAAE,CAAC,EAAEG,MAAM,CAACD,EAAE,CAAC;MACtB,OAAOgB,IAAI,CAACP,KAAK,CAACN,KAAK,CAACL,EAAE,EAAEE,EAAE,CAAC,CAAC;IAClC,CAAC;IAEDK,QAAQ,CAACqB,KAAK,GAAG,UAAUX,IAAI,EAAE;MAC/BA,IAAI,GAAGC,IAAI,CAACP,KAAK,CAACM,IAAI,CAAC;MACvB,OAAO,CAACY,QAAQ,CAACZ,IAAI,CAAC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAEA,IAAI,GAAG,CAAC,CAAC,GAAGV,QAAQ,GAAGA,QAAQ,CAACiB,MAAM,CAAClB,KAAK,GAAG,UAAUwB,CAAC,EAAE;QAC3G,OAAOxB,KAAK,CAACwB,CAAC,CAAC,GAAGb,IAAI,KAAK,CAAC;MAC9B,CAAC,GAAG,UAAUa,CAAC,EAAE;QACf,OAAOvB,QAAQ,CAACF,KAAK,CAAC,CAAC,EAAEyB,CAAC,CAAC,GAAGb,IAAI,KAAK,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC;EACH;EAEA,OAAOV,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}