{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = difference;\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\nfunction difference(values) {\n  values = new _index.InternSet(values);\n  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    others[_key - 1] = arguments[_key];\n  }\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "difference", "_index", "require", "values", "InternSet", "_len", "arguments", "length", "others", "Array", "_key", "other", "delete"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/difference.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = difference;\n\nvar _index = require(\"../../../lib-vendor/internmap/src/index.js\");\n\nfunction difference(values, ...others) {\n  values = new _index.InternSet(values);\n\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n\n  return values;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,IAAIC,MAAM,GAAGC,OAAO,CAAC,4CAA4C,CAAC;AAElE,SAASF,UAAUA,CAACG,MAAM,EAAa;EACrCA,MAAM,GAAG,IAAIF,MAAM,CAACG,SAAS,CAACD,MAAM,CAAC;EAAC,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADTC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAGnC,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMV,KAAK,IAAIa,KAAK,EAAE;MACzBR,MAAM,CAACS,MAAM,CAACd,KAAK,CAAC;IACtB;EACF;EAEA,OAAOK,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script"}