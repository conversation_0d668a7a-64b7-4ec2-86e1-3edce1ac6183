{"ast": null, "code": "import * as React from 'react';\nimport { toPathOptions } from '../utils/treeUtil';\nexport default (function (options, fieldNames) {\n  return React.useCallback(function (rawValues) {\n    var missingValues = [];\n    var existsValues = [];\n    rawValues.forEach(function (valueCell) {\n      var pathOptions = toPathOptions(valueCell, options, fieldNames);\n      if (pathOptions.every(function (opt) {\n        return opt.option;\n      })) {\n        existsValues.push(valueCell);\n      } else {\n        missingValues.push(valueCell);\n      }\n    });\n    return [existsValues, missingValues];\n  }, [options, fieldNames]);\n});", "map": {"version": 3, "names": ["React", "toPathOptions", "options", "fieldNames", "useCallback", "rawValues", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "valueCell", "pathOptions", "every", "opt", "option", "push"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-cascader/es/hooks/useMissingValues.js"], "sourcesContent": ["import * as React from 'react';\nimport { toPathOptions } from '../utils/treeUtil';\nexport default (function (options, fieldNames) {\n  return React.useCallback(function (rawValues) {\n    var missingValues = [];\n    var existsValues = [];\n    rawValues.forEach(function (valueCell) {\n      var pathOptions = toPathOptions(valueCell, options, fieldNames);\n      if (pathOptions.every(function (opt) {\n        return opt.option;\n      })) {\n        existsValues.push(valueCell);\n      } else {\n        missingValues.push(valueCell);\n      }\n    });\n    return [existsValues, missingValues];\n  }, [options, fieldNames]);\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,mBAAmB;AACjD,gBAAgB,UAAUC,OAAO,EAAEC,UAAU,EAAE;EAC7C,OAAOH,KAAK,CAACI,WAAW,CAAC,UAAUC,SAAS,EAAE;IAC5C,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,YAAY,GAAG,EAAE;IACrBF,SAAS,CAACG,OAAO,CAAC,UAAUC,SAAS,EAAE;MACrC,IAAIC,WAAW,GAAGT,aAAa,CAACQ,SAAS,EAAEP,OAAO,EAAEC,UAAU,CAAC;MAC/D,IAAIO,WAAW,CAACC,KAAK,CAAC,UAAUC,GAAG,EAAE;QACnC,OAAOA,GAAG,CAACC,MAAM;MACnB,CAAC,CAAC,EAAE;QACFN,YAAY,CAACO,IAAI,CAACL,SAAS,CAAC;MAC9B,CAAC,MAAM;QACLH,aAAa,CAACQ,IAAI,CAACL,SAAS,CAAC;MAC/B;IACF,CAAC,CAAC;IACF,OAAO,CAACF,YAAY,EAAED,aAAa,CAAC;EACtC,CAAC,EAAE,CAACJ,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}