{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/AsyncGuide.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, Steps, Alert, Typography, Space, Button, Row, Col, Tag } from 'antd';\nimport { PlayCircleOutlined, ClockCircleOutlined, CheckCircleOutlined, EyeOutlined, RocketOutlined, BulbOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst AsyncGuide = () => {\n  _s();\n  const navigate = useNavigate();\n  const trainingSteps = [{\n    title: '配置训练参数',\n    description: '在模型训练页面设置数据源、协议、数据类型等参数',\n    icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '选择异步模式',\n    description: '在\"训练模式\"部分选择\"异步训练（推荐）\"',\n    icon: /*#__PURE__*/_jsxDEV(RocketOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '提交训练任务',\n    description: '点击\"开始训练\"按钮，系统会提示任务已启动',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '查看进度和结果',\n    description: '前往\"任务管理\"页面实时监控进度，查看训练结果',\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this)\n  }];\n  const predictionSteps = [{\n    title: '选择数据和模型',\n    description: '在模型预测页面选择数据源和训练好的模型文件',\n    icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '选择异步模式',\n    description: '在\"预测模式\"部分选择\"异步预测（推荐）\"',\n    icon: /*#__PURE__*/_jsxDEV(RocketOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '提交预测任务',\n    description: '点击\"开始预测与检测\"按钮，系统会提示任务已启动',\n    icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)\n  }, {\n    title: '查看预测结果',\n    description: '前往\"任务管理\"页面查看预测结果和异常检测报告',\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: [/*#__PURE__*/_jsxDEV(RocketOutlined, {\n          style: {\n            marginRight: 8,\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u4F7F\\u7528\\u6307\\u5357\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u5B66\\u4E60\\u5982\\u4F55\\u4F7F\\u7528\\u5F02\\u6B65\\u6A21\\u5F0F\\u8FDB\\u884C\\u6A21\\u578B\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\uFF0C\\u63D0\\u9AD8\\u5DE5\\u4F5C\\u6548\\u7387\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: [/*#__PURE__*/_jsxDEV(BulbOutlined, {\n          style: {\n            marginRight: 8,\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), \"\\u4E3A\\u4EC0\\u4E48\\u4F7F\\u7528\\u5F02\\u6B65\\u6A21\\u5F0F\\uFF1F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n              style: {\n                fontSize: '32px',\n                color: '#1890ff',\n                marginBottom: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: \"\\u8282\\u7701\\u65F6\\u95F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u4EFB\\u52A1\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u5176\\u4ED6\\u5DE5\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                fontSize: '32px',\n                color: '#52c41a',\n                marginBottom: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: \"\\u5B9E\\u65F6\\u76D1\\u63A7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u968F\\u65F6\\u67E5\\u770B\\u4EFB\\u52A1\\u8FDB\\u5EA6\\u548C\\u72B6\\u6001\\u66F4\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {\n              style: {\n                fontSize: '32px',\n                color: '#722ed1',\n                marginBottom: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: \"\\u7ED3\\u679C\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7EDF\\u4E00\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u6240\\u6709\\u4EFB\\u52A1\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\uD83C\\uDFAF \\u5F02\\u6B65\\u8BAD\\u7EC3\\u6D41\\u7A0B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Steps, {\n        direction: \"vertical\",\n        current: -1,\n        items: trainingSteps.map((step, index) => ({\n          title: step.title,\n          description: step.description,\n          icon: step.icon\n        }))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          onClick: () => navigate('/model-training'),\n          children: \"\\u524D\\u5F80\\u6A21\\u578B\\u8BAD\\u7EC3\\u9875\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\uD83D\\uDD0D \\u5F02\\u6B65\\u9884\\u6D4B\\u6D41\\u7A0B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Steps, {\n        direction: \"vertical\",\n        current: -1,\n        items: predictionSteps.map((step, index) => ({\n          title: step.title,\n          description: step.description,\n          icon: step.icon\n        }))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          onClick: () => navigate('/model-prediction'),\n          children: \"\\u524D\\u5F80\\u6A21\\u578B\\u9884\\u6D4B\\u9875\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\uD83D\\uDCCA \\u5982\\u4F55\\u67E5\\u770B\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u91CD\\u8981\\u63D0\\u793A\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n            children: [\"\\u6240\\u6709\\u5F02\\u6B65\\u4EFB\\u52A1\\u7684\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u90FD\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 32\n            }, this), \" \\u9875\\u9762\\u4E2D\\u67E5\\u770B\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5B9E\\u65F6\\u8FDB\\u5EA6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 23\n              }, this), \"\\uFF1A\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\\u7684\\u8FDB\\u5EA6\\u6761\\u548C\\u72B6\\u6001\\u66F4\\u65B0\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\uD83C\\uDFAF \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u8BAD\\u7EC3\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 24\n              }, this), \"\\uFF1A\\u6A21\\u578B\\u6587\\u4EF6\\u8DEF\\u5F84\\u3001R\\xB2\\u5206\\u6570\\u3001\\u8BAD\\u7EC3\\u6307\\u6807\\u7B49\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\uD83D\\uDD0D \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u9884\\u6D4B\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 24\n              }, this), \"\\uFF1A\\u5F02\\u5E38\\u68C0\\u6D4B\\u62A5\\u544A\\u3001\\u9884\\u6D4B\\u6570\\u636E\\u3001\\u9608\\u503C\\u8BBE\\u7F6E\\u7B49\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"\\uD83D\\uDCCB \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 24\n              }, this), \"\\uFF1A\\u4EFB\\u52A1\\u53C2\\u6570\\u3001\\u9519\\u8BEF\\u4FE1\\u606F\\u3001\\u6267\\u884C\\u65F6\\u95F4\\u7B49\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this),\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: '16px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/task-manager'),\n          children: \"\\u524D\\u5F80\\u4EFB\\u52A1\\u7BA1\\u7406\\u9875\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\uD83D\\uDD14 \\u4EFB\\u52A1\\u5B8C\\u6210\\u901A\\u77E5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"success\",\n                children: \"\\u6210\\u529F\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4EFB\\u52A1\\u6210\\u529F\\u5B8C\\u6210\\u65F6\\uFF0C\\u4F1A\\u663E\\u793A\\u7EFF\\u8272\\u901A\\u77E5\\uFF0C\\u70B9\\u51FB\\u901A\\u77E5\\u53EF\\u76F4\\u63A5\\u8DF3\\u8F6C\\u5230\\u4EFB\\u52A1\\u7BA1\\u7406\\u9875\\u9762\\u67E5\\u770B\\u7ED3\\u679C\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 5,\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"error\",\n                children: \"\\u6267\\u884C\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4EFB\\u52A1\\u5931\\u8D25\\u65F6\\uFF0C\\u4F1A\\u663E\\u793A\\u7EA2\\u8272\\u901A\\u77E5\\u5E76\\u5305\\u542B\\u9519\\u8BEF\\u4FE1\\u606F\\uFF0C\\u70B9\\u51FB\\u901A\\u77E5\\u53EF\\u67E5\\u770B\\u8BE6\\u7EC6\\u7684\\u9519\\u8BEF\\u539F\\u56E0\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u63D0\\u793A\",\n        description: \"\\u901A\\u77E5\\u4F1A\\u5728\\u9875\\u9762\\u53F3\\u4E0A\\u89D2\\u663E\\u793A\\uFF0C\\u6301\\u7EED\\u65F6\\u95F4\\u8F83\\u957F\\uFF0C\\u786E\\u4FDD\\u60A8\\u4E0D\\u4F1A\\u9519\\u8FC7\\u91CD\\u8981\\u4FE1\\u606F\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginTop: '16px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginTop: '24px',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"\\uD83D\\uDE80 \\u5FEB\\u901F\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          onClick: () => navigate('/model-training'),\n          children: \"\\u5F00\\u59CB\\u8BAD\\u7EC3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          onClick: () => navigate('/model-prediction'),\n          children: \"\\u5F00\\u59CB\\u9884\\u6D4B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          onClick: () => navigate('/task-manager'),\n          children: \"\\u67E5\\u770B\\u4EFB\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(AsyncGuide, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = AsyncGuide;\nexport default AsyncGuide;\nvar _c;\n$RefreshReg$(_c, \"AsyncGuide\");", "map": {"version": 3, "names": ["React", "Card", "Steps", "<PERSON><PERSON>", "Typography", "Space", "<PERSON><PERSON>", "Row", "Col", "Tag", "PlayCircleOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "EyeOutlined", "RocketOutlined", "BulbOutlined", "useNavigate", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "AsyncGuide", "_s", "navigate", "trainingSteps", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "predictionSteps", "style", "padding", "children", "marginBottom", "textAlign", "level", "marginRight", "color", "type", "gutter", "span", "size", "fontSize", "direction", "current", "items", "map", "step", "index", "marginTop", "onClick", "message", "showIcon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/AsyncGuide.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Steps, Alert, Typography, Space, Button, Row, Col, Tag } from 'antd';\nimport { \n  PlayCircleOutlined, \n  ClockCircleOutlined, \n  CheckCircleOutlined,\n  EyeOutlined,\n  RocketOutlined,\n  BulbOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst AsyncGuide: React.FC = () => {\n  const navigate = useNavigate();\n\n  const trainingSteps = [\n    {\n      title: '配置训练参数',\n      description: '在模型训练页面设置数据源、协议、数据类型等参数',\n      icon: <PlayCircleOutlined />\n    },\n    {\n      title: '选择异步模式',\n      description: '在\"训练模式\"部分选择\"异步训练（推荐）\"',\n      icon: <RocketOutlined />\n    },\n    {\n      title: '提交训练任务',\n      description: '点击\"开始训练\"按钮，系统会提示任务已启动',\n      icon: <CheckCircleOutlined />\n    },\n    {\n      title: '查看进度和结果',\n      description: '前往\"任务管理\"页面实时监控进度，查看训练结果',\n      icon: <EyeOutlined />\n    }\n  ];\n\n  const predictionSteps = [\n    {\n      title: '选择数据和模型',\n      description: '在模型预测页面选择数据源和训练好的模型文件',\n      icon: <PlayCircleOutlined />\n    },\n    {\n      title: '选择异步模式',\n      description: '在\"预测模式\"部分选择\"异步预测（推荐）\"',\n      icon: <RocketOutlined />\n    },\n    {\n      title: '提交预测任务',\n      description: '点击\"开始预测与检测\"按钮，系统会提示任务已启动',\n      icon: <CheckCircleOutlined />\n    },\n    {\n      title: '查看预测结果',\n      description: '前往\"任务管理\"页面查看预测结果和异常检测报告',\n      icon: <EyeOutlined />\n    }\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', textAlign: 'center' }}>\n        <Title level={2}>\n          <RocketOutlined style={{ marginRight: 8, color: '#1890ff' }} />\n          异步训练和预测使用指南\n        </Title>\n        <Text type=\"secondary\">\n          学习如何使用异步模式进行模型训练和预测，提高工作效率\n        </Text>\n      </div>\n\n      {/* 功能介绍 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Title level={4}>\n          <BulbOutlined style={{ marginRight: 8, color: '#faad14' }} />\n          为什么使用异步模式？\n        </Title>\n        <Row gutter={[16, 16]}>\n          <Col span={8}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <ClockCircleOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '8px' }} />\n              <Title level={5}>节省时间</Title>\n              <Text type=\"secondary\">任务在后台运行，您可以继续其他工作</Text>\n            </Card>\n          </Col>\n          <Col span={8}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <CheckCircleOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '8px' }} />\n              <Title level={5}>实时监控</Title>\n              <Text type=\"secondary\">随时查看任务进度和状态更新</Text>\n            </Card>\n          </Col>\n          <Col span={8}>\n            <Card size=\"small\" style={{ textAlign: 'center' }}>\n              <EyeOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '8px' }} />\n              <Title level={5}>结果管理</Title>\n              <Text type=\"secondary\">统一查看和管理所有任务结果</Text>\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 异步训练流程 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Title level={4}>🎯 异步训练流程</Title>\n        <Steps\n          direction=\"vertical\"\n          current={-1}\n          items={trainingSteps.map((step, index) => ({\n            title: step.title,\n            description: step.description,\n            icon: step.icon\n          }))}\n        />\n        <div style={{ marginTop: '16px', textAlign: 'center' }}>\n          <Button \n            type=\"primary\" \n            size=\"large\"\n            onClick={() => navigate('/model-training')}\n          >\n            前往模型训练页面\n          </Button>\n        </div>\n      </Card>\n\n      {/* 异步预测流程 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Title level={4}>🔍 异步预测流程</Title>\n        <Steps\n          direction=\"vertical\"\n          current={-1}\n          items={predictionSteps.map((step, index) => ({\n            title: step.title,\n            description: step.description,\n            icon: step.icon\n          }))}\n        />\n        <div style={{ marginTop: '16px', textAlign: 'center' }}>\n          <Button \n            type=\"primary\" \n            size=\"large\"\n            onClick={() => navigate('/model-prediction')}\n          >\n            前往模型预测页面\n          </Button>\n        </div>\n      </Card>\n\n      {/* 结果查看说明 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Title level={4}>📊 如何查看结果</Title>\n        <Alert\n          message=\"重要提示\"\n          description={\n            <div>\n              <Paragraph>\n                所有异步任务的进度和结果都在 <strong>任务管理</strong> 页面中查看：\n              </Paragraph>\n              <ul>\n                <li>✅ <strong>实时进度</strong>：运行中任务的进度条和状态更新</li>\n                <li>🎯 <strong>训练结果</strong>：模型文件路径、R²分数、训练指标等</li>\n                <li>🔍 <strong>预测结果</strong>：异常检测报告、预测数据、阈值设置等</li>\n                <li>📋 <strong>详细信息</strong>：任务参数、错误信息、执行时间等</li>\n              </ul>\n            </div>\n          }\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: '16px' }}\n        />\n        <div style={{ textAlign: 'center' }}>\n          <Button \n            type=\"primary\" \n            size=\"large\"\n            icon={<ClockCircleOutlined />}\n            onClick={() => navigate('/task-manager')}\n          >\n            前往任务管理页面\n          </Button>\n        </div>\n      </Card>\n\n      {/* 通知说明 */}\n      <Card>\n        <Title level={4}>🔔 任务完成通知</Title>\n        <Row gutter={[16, 16]}>\n          <Col span={12}>\n            <Card size=\"small\">\n              <Title level={5}>\n                <Tag color=\"success\">成功完成</Tag>\n              </Title>\n              <Text>\n                任务成功完成时，会显示绿色通知，点击通知可直接跳转到任务管理页面查看结果。\n              </Text>\n            </Card>\n          </Col>\n          <Col span={12}>\n            <Card size=\"small\">\n              <Title level={5}>\n                <Tag color=\"error\">执行失败</Tag>\n              </Title>\n              <Text>\n                任务失败时，会显示红色通知并包含错误信息，点击通知可查看详细的错误原因。\n              </Text>\n            </Card>\n          </Col>\n        </Row>\n        <Alert\n          message=\"提示\"\n          description=\"通知会在页面右上角显示，持续时间较长，确保您不会错过重要信息。\"\n          type=\"info\"\n          showIcon\n          style={{ marginTop: '16px' }}\n        />\n      </Card>\n\n      {/* 快速导航 */}\n      <Card style={{ marginTop: '24px', textAlign: 'center' }}>\n        <Title level={4}>🚀 快速开始</Title>\n        <Space size=\"large\">\n          <Button \n            type=\"primary\" \n            size=\"large\"\n            onClick={() => navigate('/model-training')}\n          >\n            开始训练\n          </Button>\n          <Button \n            type=\"primary\" \n            size=\"large\"\n            onClick={() => navigate('/model-prediction')}\n          >\n            开始预测\n          </Button>\n          <Button \n            size=\"large\"\n            onClick={() => navigate('/task-manager')}\n          >\n            查看任务\n          </Button>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default AsyncGuide;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACnF,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGjB,UAAU;AAE7C,MAAMkB,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,eAAEV,OAAA,CAACR,kBAAkB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,eAAEV,OAAA,CAACJ,cAAc;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,eAAEV,OAAA,CAACN,mBAAmB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACEN,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,eAAEV,OAAA,CAACL,WAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,CACF;EAED,MAAMC,eAAe,GAAG,CACtB;IACEP,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,eAAEV,OAAA,CAACR,kBAAkB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,eAAEV,OAAA,CAACJ,cAAc;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,eAAEV,OAAA,CAACN,mBAAmB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,eAAEV,OAAA,CAACL,WAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,CACF;EAED,oBACEd,OAAA;IAAKgB,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BlB,OAAA;MAAKgB,KAAK,EAAE;QAAEG,YAAY,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,gBACxDlB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,gBACdlB,OAAA,CAACJ,cAAc;UAACoB,KAAK,EAAE;YAAEM,WAAW,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sEAEjE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRd,OAAA,CAACE,IAAI;QAACsB,IAAI,EAAC,WAAW;QAAAN,QAAA,EAAC;MAEvB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNd,OAAA,CAACjB,IAAI;MAACiC,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACpClB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,gBACdlB,OAAA,CAACH,YAAY;UAACmB,KAAK,EAAE;YAAEM,WAAW,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gEAE/D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRd,OAAA,CAACX,GAAG;QAACoC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACpBlB,OAAA,CAACV,GAAG;UAACoC,IAAI,EAAE,CAAE;UAAAR,QAAA,eACXlB,OAAA,CAACjB,IAAI;YAAC4C,IAAI,EAAC,OAAO;YAACX,KAAK,EAAE;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,gBAChDlB,OAAA,CAACP,mBAAmB;cAACuB,KAAK,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAEL,KAAK,EAAE,SAAS;gBAAEJ,YAAY,EAAE;cAAM;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3Fd,OAAA,CAACC,KAAK;cAACoB,KAAK,EAAE,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7Bd,OAAA,CAACE,IAAI;cAACsB,IAAI,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAiB;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNd,OAAA,CAACV,GAAG;UAACoC,IAAI,EAAE,CAAE;UAAAR,QAAA,eACXlB,OAAA,CAACjB,IAAI;YAAC4C,IAAI,EAAC,OAAO;YAACX,KAAK,EAAE;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,gBAChDlB,OAAA,CAACN,mBAAmB;cAACsB,KAAK,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAEL,KAAK,EAAE,SAAS;gBAAEJ,YAAY,EAAE;cAAM;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3Fd,OAAA,CAACC,KAAK;cAACoB,KAAK,EAAE,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7Bd,OAAA,CAACE,IAAI;cAACsB,IAAI,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAa;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNd,OAAA,CAACV,GAAG;UAACoC,IAAI,EAAE,CAAE;UAAAR,QAAA,eACXlB,OAAA,CAACjB,IAAI;YAAC4C,IAAI,EAAC,OAAO;YAACX,KAAK,EAAE;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,gBAChDlB,OAAA,CAACL,WAAW;cAACqB,KAAK,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAEL,KAAK,EAAE,SAAS;gBAAEJ,YAAY,EAAE;cAAM;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnFd,OAAA,CAACC,KAAK;cAACoB,KAAK,EAAE,CAAE;cAAAH,QAAA,EAAC;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7Bd,OAAA,CAACE,IAAI;cAACsB,IAAI,EAAC,WAAW;cAAAN,QAAA,EAAC;YAAa;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPd,OAAA,CAACjB,IAAI;MAACiC,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACpClB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCd,OAAA,CAAChB,KAAK;QACJ6C,SAAS,EAAC,UAAU;QACpBC,OAAO,EAAE,CAAC,CAAE;QACZC,KAAK,EAAExB,aAAa,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACzC1B,KAAK,EAAEyB,IAAI,CAACzB,KAAK;UACjBC,WAAW,EAAEwB,IAAI,CAACxB,WAAW;UAC7BC,IAAI,EAAEuB,IAAI,CAACvB;QACb,CAAC,CAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACFd,OAAA;QAAKgB,KAAK,EAAE;UAAEmB,SAAS,EAAE,MAAM;UAAEf,SAAS,EAAE;QAAS,CAAE;QAAAF,QAAA,eACrDlB,OAAA,CAACZ,MAAM;UACLoC,IAAI,EAAC,SAAS;UACdG,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,iBAAiB,CAAE;UAAAY,QAAA,EAC5C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPd,OAAA,CAACjB,IAAI;MAACiC,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACpClB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCd,OAAA,CAAChB,KAAK;QACJ6C,SAAS,EAAC,UAAU;QACpBC,OAAO,EAAE,CAAC,CAAE;QACZC,KAAK,EAAEhB,eAAe,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UAC3C1B,KAAK,EAAEyB,IAAI,CAACzB,KAAK;UACjBC,WAAW,EAAEwB,IAAI,CAACxB,WAAW;UAC7BC,IAAI,EAAEuB,IAAI,CAACvB;QACb,CAAC,CAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACFd,OAAA;QAAKgB,KAAK,EAAE;UAAEmB,SAAS,EAAE,MAAM;UAAEf,SAAS,EAAE;QAAS,CAAE;QAAAF,QAAA,eACrDlB,OAAA,CAACZ,MAAM;UACLoC,IAAI,EAAC,SAAS;UACdG,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,mBAAmB,CAAE;UAAAY,QAAA,EAC9C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPd,OAAA,CAACjB,IAAI;MAACiC,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACpClB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCd,OAAA,CAACf,KAAK;QACJoD,OAAO,EAAC,0BAAM;QACd5B,WAAW,eACTT,OAAA;UAAAkB,QAAA,gBACElB,OAAA,CAACG,SAAS;YAAAe,QAAA,GAAC,uFACM,eAAAlB,OAAA;cAAAkB,QAAA,EAAQ;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yCACtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZd,OAAA;YAAAkB,QAAA,gBACElB,OAAA;cAAAkB,QAAA,GAAI,SAAE,eAAAlB,OAAA;gBAAAkB,QAAA,EAAQ;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,8FAAe;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/Cd,OAAA;cAAAkB,QAAA,GAAI,eAAG,eAAAlB,OAAA;gBAAAkB,QAAA,EAAQ;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yGAAkB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDd,OAAA;cAAAkB,QAAA,GAAI,eAAG,eAAAlB,OAAA;gBAAAkB,QAAA,EAAQ;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gHAAkB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDd,OAAA;cAAAkB,QAAA,GAAI,eAAG,eAAAlB,OAAA;gBAAAkB,QAAA,EAAQ;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,oGAAgB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;QACDU,IAAI,EAAC,MAAM;QACXc,QAAQ;QACRtB,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACFd,OAAA;QAAKgB,KAAK,EAAE;UAAEI,SAAS,EAAE;QAAS,CAAE;QAAAF,QAAA,eAClClB,OAAA,CAACZ,MAAM;UACLoC,IAAI,EAAC,SAAS;UACdG,IAAI,EAAC,OAAO;UACZjB,IAAI,eAAEV,OAAA,CAACP,mBAAmB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BsB,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAe,CAAE;UAAAY,QAAA,EAC1C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPd,OAAA,CAACjB,IAAI;MAAAmC,QAAA,gBACHlB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClCd,OAAA,CAACX,GAAG;QAACoC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACpBlB,OAAA,CAACV,GAAG;UAACoC,IAAI,EAAE,EAAG;UAAAR,QAAA,eACZlB,OAAA,CAACjB,IAAI;YAAC4C,IAAI,EAAC,OAAO;YAAAT,QAAA,gBAChBlB,OAAA,CAACC,KAAK;cAACoB,KAAK,EAAE,CAAE;cAAAH,QAAA,eACdlB,OAAA,CAACT,GAAG;gBAACgC,KAAK,EAAC,SAAS;gBAAAL,QAAA,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACRd,OAAA,CAACE,IAAI;cAAAgB,QAAA,EAAC;YAEN;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNd,OAAA,CAACV,GAAG;UAACoC,IAAI,EAAE,EAAG;UAAAR,QAAA,eACZlB,OAAA,CAACjB,IAAI;YAAC4C,IAAI,EAAC,OAAO;YAAAT,QAAA,gBAChBlB,OAAA,CAACC,KAAK;cAACoB,KAAK,EAAE,CAAE;cAAAH,QAAA,eACdlB,OAAA,CAACT,GAAG;gBAACgC,KAAK,EAAC,OAAO;gBAAAL,QAAA,EAAC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACRd,OAAA,CAACE,IAAI;cAAAgB,QAAA,EAAC;YAEN;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNd,OAAA,CAACf,KAAK;QACJoD,OAAO,EAAC,cAAI;QACZ5B,WAAW,EAAC,4LAAiC;QAC7Ce,IAAI,EAAC,MAAM;QACXc,QAAQ;QACRtB,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAO;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPd,OAAA,CAACjB,IAAI;MAACiC,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEf,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,gBACtDlB,OAAA,CAACC,KAAK;QAACoB,KAAK,EAAE,CAAE;QAAAH,QAAA,EAAC;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChCd,OAAA,CAACb,KAAK;QAACwC,IAAI,EAAC,OAAO;QAAAT,QAAA,gBACjBlB,OAAA,CAACZ,MAAM;UACLoC,IAAI,EAAC,SAAS;UACdG,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,iBAAiB,CAAE;UAAAY,QAAA,EAC5C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTd,OAAA,CAACZ,MAAM;UACLoC,IAAI,EAAC,SAAS;UACdG,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,mBAAmB,CAAE;UAAAY,QAAA,EAC9C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTd,OAAA,CAACZ,MAAM;UACLuC,IAAI,EAAC,OAAO;UACZS,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,eAAe,CAAE;UAAAY,QAAA,EAC1C;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACT,EAAA,CA1OID,UAAoB;EAAA,QACPN,WAAW;AAAA;AAAAyC,EAAA,GADxBnC,UAAoB;AA4O1B,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}