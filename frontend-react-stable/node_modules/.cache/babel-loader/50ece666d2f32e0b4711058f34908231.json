{"ast": null, "code": "// This icon file is generated automatically.\nvar FolderTwoTone = {\n  \"icon\": function render(primaryColor, secondaryColor) {\n    return {\n      \"tag\": \"svg\",\n      \"attrs\": {\n        \"viewBox\": \"64 64 896 896\",\n        \"focusable\": \"false\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z\",\n          \"fill\": primaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M372.5 256H184v512h656V370.4H492.1z\",\n          \"fill\": secondaryColor\n        }\n      }]\n    };\n  },\n  \"name\": \"folder\",\n  \"theme\": \"twotone\"\n};\nexport default FolderTwoTone;", "map": {"version": 3, "names": ["FolderTwoTone", "render", "primaryColor", "secondaryColor"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/FolderTwoTone.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar FolderTwoTone = { \"icon\": function render(primaryColor, secondaryColor) { return { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z\", \"fill\": primaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M372.5 256H184v512h656V370.4H492.1z\", \"fill\": secondaryColor } }] }; }, \"name\": \"folder\", \"theme\": \"twotone\" };\nexport default FolderTwoTone;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG;EAAE,MAAM,EAAE,SAASC,MAAMA,CAACC,YAAY,EAAEC,cAAc,EAAE;IAAE,OAAO;MAAE,KAAK,EAAE,KAAK;MAAE,OAAO,EAAE;QAAE,SAAS,EAAE,eAAe;QAAE,WAAW,EAAE;MAAQ,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,sMAAsM;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,qCAAqC;UAAE,MAAM,EAAEC;QAAe;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,QAAQ;EAAE,OAAO,EAAE;AAAU,CAAC;AACpkB,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}