{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { toArray } from \"../utils/commonUtil\";\nimport { injectPropsWithOption } from \"../utils/valueUtil\";\nfunction includes(test, search) {\n  return toArray(test).join('').toUpperCase().includes(search);\n}\nexport default (function (options, fieldNames, searchValue, filterOption, optionFilterProp) {\n  return React.useMemo(function () {\n    if (!searchValue || filterOption === false) {\n      return options;\n    }\n    var fieldOptions = fieldNames.options,\n      fieldLabel = fieldNames.label,\n      fieldValue = fieldNames.value;\n    var filteredOptions = [];\n    var customizeFilter = typeof filterOption === 'function';\n    var upperSearch = searchValue.toUpperCase();\n    var filterFunc = customizeFilter ? filterOption : function (_, option) {\n      // Use provided `optionFilterProp`\n      if (optionFilterProp) {\n        return includes(option[optionFilterProp], upperSearch);\n      }\n\n      // Auto select `label` or `value` by option type\n      if (option[fieldOptions]) {\n        // hack `fieldLabel` since `OptionGroup` children is not `label`\n        return includes(option[fieldLabel !== 'children' ? fieldLabel : 'label'], upperSearch);\n      }\n      return includes(option[fieldValue], upperSearch);\n    };\n    var wrapOption = customizeFilter ? function (opt) {\n      return injectPropsWithOption(opt);\n    } : function (opt) {\n      return opt;\n    };\n    options.forEach(function (item) {\n      // Group should check child options\n      if (item[fieldOptions]) {\n        // Check group first\n        var matchGroup = filterFunc(searchValue, wrapOption(item));\n        if (matchGroup) {\n          filteredOptions.push(item);\n        } else {\n          // Check option\n          var subOptions = item[fieldOptions].filter(function (subItem) {\n            return filterFunc(searchValue, wrapOption(subItem));\n          });\n          if (subOptions.length) {\n            filteredOptions.push(_objectSpread(_objectSpread({}, item), {}, _defineProperty({}, fieldOptions, subOptions)));\n          }\n        }\n        return;\n      }\n      if (filterFunc(searchValue, wrapOption(item))) {\n        filteredOptions.push(item);\n      }\n    });\n    return filteredOptions;\n  }, [options, filterOption, optionFilterProp, searchValue, fieldNames]);\n});", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "toArray", "injectPropsWithOption", "includes", "test", "search", "join", "toUpperCase", "options", "fieldNames", "searchValue", "filterOption", "optionFilterProp", "useMemo", "fieldOptions", "<PERSON><PERSON><PERSON><PERSON>", "label", "fieldValue", "value", "filteredOptions", "customizeFilter", "upperSearch", "filterFunc", "_", "option", "wrapOption", "opt", "for<PERSON>ach", "item", "matchGroup", "push", "subOptions", "filter", "subItem", "length"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/hooks/useFilterOptions.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { toArray } from \"../utils/commonUtil\";\nimport { injectPropsWithOption } from \"../utils/valueUtil\";\nfunction includes(test, search) {\n  return toArray(test).join('').toUpperCase().includes(search);\n}\nexport default (function (options, fieldNames, searchValue, filterOption, optionFilterProp) {\n  return React.useMemo(function () {\n    if (!searchValue || filterOption === false) {\n      return options;\n    }\n    var fieldOptions = fieldNames.options,\n      fieldLabel = fieldNames.label,\n      fieldValue = fieldNames.value;\n    var filteredOptions = [];\n    var customizeFilter = typeof filterOption === 'function';\n    var upperSearch = searchValue.toUpperCase();\n    var filterFunc = customizeFilter ? filterOption : function (_, option) {\n      // Use provided `optionFilterProp`\n      if (optionFilterProp) {\n        return includes(option[optionFilterProp], upperSearch);\n      }\n\n      // Auto select `label` or `value` by option type\n      if (option[fieldOptions]) {\n        // hack `fieldLabel` since `OptionGroup` children is not `label`\n        return includes(option[fieldLabel !== 'children' ? fieldLabel : 'label'], upperSearch);\n      }\n      return includes(option[fieldValue], upperSearch);\n    };\n    var wrapOption = customizeFilter ? function (opt) {\n      return injectPropsWithOption(opt);\n    } : function (opt) {\n      return opt;\n    };\n    options.forEach(function (item) {\n      // Group should check child options\n      if (item[fieldOptions]) {\n        // Check group first\n        var matchGroup = filterFunc(searchValue, wrapOption(item));\n        if (matchGroup) {\n          filteredOptions.push(item);\n        } else {\n          // Check option\n          var subOptions = item[fieldOptions].filter(function (subItem) {\n            return filterFunc(searchValue, wrapOption(subItem));\n          });\n          if (subOptions.length) {\n            filteredOptions.push(_objectSpread(_objectSpread({}, item), {}, _defineProperty({}, fieldOptions, subOptions)));\n          }\n        }\n        return;\n      }\n      if (filterFunc(searchValue, wrapOption(item))) {\n        filteredOptions.push(item);\n      }\n    });\n    return filteredOptions;\n  }, [options, filterOption, optionFilterProp, searchValue, fieldNames]);\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC9B,OAAOJ,OAAO,CAACG,IAAI,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACJ,QAAQ,CAACE,MAAM,CAAC;AAC9D;AACA,gBAAgB,UAAUG,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,gBAAgB,EAAE;EAC1F,OAAOZ,KAAK,CAACa,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACH,WAAW,IAAIC,YAAY,KAAK,KAAK,EAAE;MAC1C,OAAOH,OAAO;IAChB;IACA,IAAIM,YAAY,GAAGL,UAAU,CAACD,OAAO;MACnCO,UAAU,GAAGN,UAAU,CAACO,KAAK;MAC7BC,UAAU,GAAGR,UAAU,CAACS,KAAK;IAC/B,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAIC,eAAe,GAAG,OAAOT,YAAY,KAAK,UAAU;IACxD,IAAIU,WAAW,GAAGX,WAAW,CAACH,WAAW,CAAC,CAAC;IAC3C,IAAIe,UAAU,GAAGF,eAAe,GAAGT,YAAY,GAAG,UAAUY,CAAC,EAAEC,MAAM,EAAE;MACrE;MACA,IAAIZ,gBAAgB,EAAE;QACpB,OAAOT,QAAQ,CAACqB,MAAM,CAACZ,gBAAgB,CAAC,EAAES,WAAW,CAAC;MACxD;;MAEA;MACA,IAAIG,MAAM,CAACV,YAAY,CAAC,EAAE;QACxB;QACA,OAAOX,QAAQ,CAACqB,MAAM,CAACT,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAG,OAAO,CAAC,EAAEM,WAAW,CAAC;MACxF;MACA,OAAOlB,QAAQ,CAACqB,MAAM,CAACP,UAAU,CAAC,EAAEI,WAAW,CAAC;IAClD,CAAC;IACD,IAAII,UAAU,GAAGL,eAAe,GAAG,UAAUM,GAAG,EAAE;MAChD,OAAOxB,qBAAqB,CAACwB,GAAG,CAAC;IACnC,CAAC,GAAG,UAAUA,GAAG,EAAE;MACjB,OAAOA,GAAG;IACZ,CAAC;IACDlB,OAAO,CAACmB,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC9B;MACA,IAAIA,IAAI,CAACd,YAAY,CAAC,EAAE;QACtB;QACA,IAAIe,UAAU,GAAGP,UAAU,CAACZ,WAAW,EAAEe,UAAU,CAACG,IAAI,CAAC,CAAC;QAC1D,IAAIC,UAAU,EAAE;UACdV,eAAe,CAACW,IAAI,CAACF,IAAI,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,IAAIG,UAAU,GAAGH,IAAI,CAACd,YAAY,CAAC,CAACkB,MAAM,CAAC,UAAUC,OAAO,EAAE;YAC5D,OAAOX,UAAU,CAACZ,WAAW,EAAEe,UAAU,CAACQ,OAAO,CAAC,CAAC;UACrD,CAAC,CAAC;UACF,IAAIF,UAAU,CAACG,MAAM,EAAE;YACrBf,eAAe,CAACW,IAAI,CAAC/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE9B,eAAe,CAAC,CAAC,CAAC,EAAEgB,YAAY,EAAEiB,UAAU,CAAC,CAAC,CAAC;UACjH;QACF;QACA;MACF;MACA,IAAIT,UAAU,CAACZ,WAAW,EAAEe,UAAU,CAACG,IAAI,CAAC,CAAC,EAAE;QAC7CT,eAAe,CAACW,IAAI,CAACF,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,OAAOT,eAAe;EACxB,CAAC,EAAE,CAACX,OAAO,EAAEG,YAAY,EAAEC,gBAAgB,EAAEF,WAAW,EAAED,UAAU,CAAC,CAAC;AACxE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}