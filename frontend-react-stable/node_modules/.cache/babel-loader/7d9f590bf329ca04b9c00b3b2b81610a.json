{"ast": null, "code": "import { tuple } from './type';\nexport var PresetStatusColorTypes = tuple('success', 'processing', 'error', 'default', 'warning');\n// eslint-disable-next-line import/prefer-default-export\nexport var PresetColorTypes = tuple('pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime');", "map": {"version": 3, "names": ["tuple", "PresetStatusColorTypes", "PresetColorTypes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/_util/colors.js"], "sourcesContent": ["import { tuple } from './type';\nexport var PresetStatusColorTypes = tuple('success', 'processing', 'error', 'default', 'warning');\n// eslint-disable-next-line import/prefer-default-export\nexport var PresetColorTypes = tuple('pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime');"], "mappings": "AAAA,SAASA,KAAK,QAAQ,QAAQ;AAC9B,OAAO,IAAIC,sBAAsB,GAAGD,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AACjG;AACA,OAAO,IAAIE,gBAAgB,GAAGF,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}