{"ast": null, "code": "import { supportBigInt } from './supportUtil';\n/**\n * Format string number to readable number\n */\nexport function trimNumber(numStr) {\n  var str = numStr.trim();\n  var negative = str.startsWith('-');\n  if (negative) {\n    str = str.slice(1);\n  }\n  str = str\n  // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`\n  .replace(/(\\.\\d*[^0])0*$/, '$1')\n  // Remove useless decimal. `1.` => `1`\n  .replace(/\\.0*$/, '')\n  // Remove integer 0. `0001` => `1`, 000.1' => `.1`\n  .replace(/^0+/, '');\n  if (str.startsWith('.')) {\n    str = \"0\".concat(str);\n  }\n  var trimStr = str || '0';\n  var splitNumber = trimStr.split('.');\n  var integerStr = splitNumber[0] || '0';\n  var decimalStr = splitNumber[1] || '0';\n  if (integerStr === '0' && decimalStr === '0') {\n    negative = false;\n  }\n  var negativeStr = negative ? '-' : '';\n  return {\n    negative: negative,\n    negativeStr: negativeStr,\n    trimStr: trimStr,\n    integerStr: integerStr,\n    decimalStr: decimalStr,\n    fullStr: \"\".concat(negativeStr).concat(trimStr)\n  };\n}\nexport function isE(number) {\n  var str = String(number);\n  return !Number.isNaN(Number(str)) && str.includes('e');\n}\n/**\n * [Legacy] Convert 1e-9 to 0.000000001.\n * This may lose some precision if user really want 1e-9.\n */\nexport function getNumberPrecision(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));\n    var decimalMatch = numStr.match(/\\.(\\d+)/);\n    if (decimalMatch === null || decimalMatch === void 0 ? void 0 : decimalMatch[1]) {\n      precision += decimalMatch[1].length;\n    }\n    return precision;\n  }\n  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;\n}\n/**\n * Convert number (includes scientific notation) to -xxx.yyy format\n */\nexport function num2str(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    if (number > Number.MAX_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);\n    }\n    if (number < Number.MIN_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);\n    }\n    numStr = number.toFixed(getNumberPrecision(numStr));\n  }\n  return trimNumber(numStr).fullStr;\n}\nexport function validateNumber(num) {\n  if (typeof num === 'number') {\n    return !Number.isNaN(num);\n  }\n  // Empty\n  if (!num) {\n    return false;\n  }\n  return (\n    // Normal type: 11.28\n    /^\\s*-?\\d+(\\.\\d+)?\\s*$/.test(num) ||\n    // Pre-number: 1.\n    /^\\s*-?\\d+\\.\\s*$/.test(num) ||\n    // Post-number: .1\n    /^\\s*-?\\.\\d+\\s*$/.test(num)\n  );\n}\nexport function getDecupleSteps(step) {\n  var stepStr = typeof step === 'number' ? num2str(step) : trimNumber(step).fullStr;\n  var hasPoint = stepStr.includes('.');\n  if (!hasPoint) {\n    return step + '0';\n  }\n  return trimNumber(stepStr.replace(/(\\d)\\.(\\d)/g, '$1$2.')).fullStr;\n}", "map": {"version": 3, "names": ["supportBigInt", "trimNumber", "numStr", "str", "trim", "negative", "startsWith", "slice", "replace", "concat", "trimStr", "splitNumber", "split", "integerStr", "decimalStr", "negativeStr", "fullStr", "isE", "number", "String", "Number", "isNaN", "includes", "getNumberPrecision", "precision", "indexOf", "decimalMatch", "match", "length", "validateNumber", "num2str", "MAX_SAFE_INTEGER", "BigInt", "toString", "MIN_SAFE_INTEGER", "toFixed", "num", "test", "getDecupleSteps", "step", "stepStr", "hasPoint"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-input-number/es/utils/numberUtil.js"], "sourcesContent": ["import { supportBigInt } from './supportUtil';\n/**\n * Format string number to readable number\n */\nexport function trimNumber(numStr) {\n  var str = numStr.trim();\n  var negative = str.startsWith('-');\n  if (negative) {\n    str = str.slice(1);\n  }\n  str = str\n  // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`\n  .replace(/(\\.\\d*[^0])0*$/, '$1')\n  // Remove useless decimal. `1.` => `1`\n  .replace(/\\.0*$/, '')\n  // Remove integer 0. `0001` => `1`, 000.1' => `.1`\n  .replace(/^0+/, '');\n  if (str.startsWith('.')) {\n    str = \"0\".concat(str);\n  }\n  var trimStr = str || '0';\n  var splitNumber = trimStr.split('.');\n  var integerStr = splitNumber[0] || '0';\n  var decimalStr = splitNumber[1] || '0';\n  if (integerStr === '0' && decimalStr === '0') {\n    negative = false;\n  }\n  var negativeStr = negative ? '-' : '';\n  return {\n    negative: negative,\n    negativeStr: negativeStr,\n    trimStr: trimStr,\n    integerStr: integerStr,\n    decimalStr: decimalStr,\n    fullStr: \"\".concat(negativeStr).concat(trimStr)\n  };\n}\nexport function isE(number) {\n  var str = String(number);\n  return !Number.isNaN(Number(str)) && str.includes('e');\n}\n/**\n * [Legacy] Convert 1e-9 to 0.000000001.\n * This may lose some precision if user really want 1e-9.\n */\nexport function getNumberPrecision(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));\n    var decimalMatch = numStr.match(/\\.(\\d+)/);\n    if (decimalMatch === null || decimalMatch === void 0 ? void 0 : decimalMatch[1]) {\n      precision += decimalMatch[1].length;\n    }\n    return precision;\n  }\n  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;\n}\n/**\n * Convert number (includes scientific notation) to -xxx.yyy format\n */\nexport function num2str(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    if (number > Number.MAX_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);\n    }\n    if (number < Number.MIN_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);\n    }\n    numStr = number.toFixed(getNumberPrecision(numStr));\n  }\n  return trimNumber(numStr).fullStr;\n}\nexport function validateNumber(num) {\n  if (typeof num === 'number') {\n    return !Number.isNaN(num);\n  }\n  // Empty\n  if (!num) {\n    return false;\n  }\n  return (\n    // Normal type: 11.28\n    /^\\s*-?\\d+(\\.\\d+)?\\s*$/.test(num) ||\n    // Pre-number: 1.\n    /^\\s*-?\\d+\\.\\s*$/.test(num) ||\n    // Post-number: .1\n    /^\\s*-?\\.\\d+\\s*$/.test(num)\n  );\n}\nexport function getDecupleSteps(step) {\n  var stepStr = typeof step === 'number' ? num2str(step) : trimNumber(step).fullStr;\n  var hasPoint = stepStr.includes('.');\n  if (!hasPoint) {\n    return step + '0';\n  }\n  return trimNumber(stepStr.replace(/(\\d)\\.(\\d)/g, '$1$2.')).fullStr;\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,eAAe;AAC7C;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAE;EACjC,IAAIC,GAAG,GAAGD,MAAM,CAACE,IAAI,CAAC,CAAC;EACvB,IAAIC,QAAQ,GAAGF,GAAG,CAACG,UAAU,CAAC,GAAG,CAAC;EAClC,IAAID,QAAQ,EAAE;IACZF,GAAG,GAAGA,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC;EACpB;EACAJ,GAAG,GAAGA;EACN;EAAA,CACCK,OAAO,CAAC,gBAAgB,EAAE,IAAI;EAC/B;EAAA,CACCA,OAAO,CAAC,OAAO,EAAE,EAAE;EACpB;EAAA,CACCA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACnB,IAAIL,GAAG,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IACvBH,GAAG,GAAG,GAAG,CAACM,MAAM,CAACN,GAAG,CAAC;EACvB;EACA,IAAIO,OAAO,GAAGP,GAAG,IAAI,GAAG;EACxB,IAAIQ,WAAW,GAAGD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC;EACpC,IAAIC,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG;EACtC,IAAIG,UAAU,GAAGH,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG;EACtC,IAAIE,UAAU,KAAK,GAAG,IAAIC,UAAU,KAAK,GAAG,EAAE;IAC5CT,QAAQ,GAAG,KAAK;EAClB;EACA,IAAIU,WAAW,GAAGV,QAAQ,GAAG,GAAG,GAAG,EAAE;EACrC,OAAO;IACLA,QAAQ,EAAEA,QAAQ;IAClBU,WAAW,EAAEA,WAAW;IACxBL,OAAO,EAAEA,OAAO;IAChBG,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA,UAAU;IACtBE,OAAO,EAAE,EAAE,CAACP,MAAM,CAACM,WAAW,CAAC,CAACN,MAAM,CAACC,OAAO;EAChD,CAAC;AACH;AACA,OAAO,SAASO,GAAGA,CAACC,MAAM,EAAE;EAC1B,IAAIf,GAAG,GAAGgB,MAAM,CAACD,MAAM,CAAC;EACxB,OAAO,CAACE,MAAM,CAACC,KAAK,CAACD,MAAM,CAACjB,GAAG,CAAC,CAAC,IAAIA,GAAG,CAACmB,QAAQ,CAAC,GAAG,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACL,MAAM,EAAE;EACzC,IAAIhB,MAAM,GAAGiB,MAAM,CAACD,MAAM,CAAC;EAC3B,IAAID,GAAG,CAACC,MAAM,CAAC,EAAE;IACf,IAAIM,SAAS,GAAGJ,MAAM,CAAClB,MAAM,CAACK,KAAK,CAACL,MAAM,CAACuB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,IAAIC,YAAY,GAAGxB,MAAM,CAACyB,KAAK,CAAC,SAAS,CAAC;IAC1C,IAAID,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,EAAE;MAC/EF,SAAS,IAAIE,YAAY,CAAC,CAAC,CAAC,CAACE,MAAM;IACrC;IACA,OAAOJ,SAAS;EAClB;EACA,OAAOtB,MAAM,CAACoB,QAAQ,CAAC,GAAG,CAAC,IAAIO,cAAc,CAAC3B,MAAM,CAAC,GAAGA,MAAM,CAAC0B,MAAM,GAAG1B,MAAM,CAACuB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACrG;AACA;AACA;AACA;AACA,OAAO,SAASK,OAAOA,CAACZ,MAAM,EAAE;EAC9B,IAAIhB,MAAM,GAAGiB,MAAM,CAACD,MAAM,CAAC;EAC3B,IAAID,GAAG,CAACC,MAAM,CAAC,EAAE;IACf,IAAIA,MAAM,GAAGE,MAAM,CAACW,gBAAgB,EAAE;MACpC,OAAOZ,MAAM,CAACnB,aAAa,CAAC,CAAC,GAAGgC,MAAM,CAACd,MAAM,CAAC,CAACe,QAAQ,CAAC,CAAC,GAAGb,MAAM,CAACW,gBAAgB,CAAC;IACtF;IACA,IAAIb,MAAM,GAAGE,MAAM,CAACc,gBAAgB,EAAE;MACpC,OAAOf,MAAM,CAACnB,aAAa,CAAC,CAAC,GAAGgC,MAAM,CAACd,MAAM,CAAC,CAACe,QAAQ,CAAC,CAAC,GAAGb,MAAM,CAACc,gBAAgB,CAAC;IACtF;IACAhC,MAAM,GAAGgB,MAAM,CAACiB,OAAO,CAACZ,kBAAkB,CAACrB,MAAM,CAAC,CAAC;EACrD;EACA,OAAOD,UAAU,CAACC,MAAM,CAAC,CAACc,OAAO;AACnC;AACA,OAAO,SAASa,cAAcA,CAACO,GAAG,EAAE;EAClC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAChB,MAAM,CAACC,KAAK,CAACe,GAAG,CAAC;EAC3B;EACA;EACA,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA;IACE;IACA,uBAAuB,CAACC,IAAI,CAACD,GAAG,CAAC;IACjC;IACA,iBAAiB,CAACC,IAAI,CAACD,GAAG,CAAC;IAC3B;IACA,iBAAiB,CAACC,IAAI,CAACD,GAAG;EAAC;AAE/B;AACA,OAAO,SAASE,eAAeA,CAACC,IAAI,EAAE;EACpC,IAAIC,OAAO,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAGT,OAAO,CAACS,IAAI,CAAC,GAAGtC,UAAU,CAACsC,IAAI,CAAC,CAACvB,OAAO;EACjF,IAAIyB,QAAQ,GAAGD,OAAO,CAAClB,QAAQ,CAAC,GAAG,CAAC;EACpC,IAAI,CAACmB,QAAQ,EAAE;IACb,OAAOF,IAAI,GAAG,GAAG;EACnB;EACA,OAAOtC,UAAU,CAACuC,OAAO,CAAChC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAACQ,OAAO;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}