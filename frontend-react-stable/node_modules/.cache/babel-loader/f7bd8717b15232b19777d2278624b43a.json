{"ast": null, "code": "// This icon file is generated automatically.\nvar LeftSquareFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM624 380.9c0 10.2-4.9 19.9-13.2 25.9L465.4 512l145.4 105.2c8.3 6 13.2 15.6 13.2 25.9V690c0 6.5-7.4 10.3-12.7 6.5l-246-178a7.95 7.95 0 010-12.9l246-178c5.3-3.8 12.7 0 12.7 6.5v46.8z\"\n      }\n    }]\n  },\n  \"name\": \"left-square\",\n  \"theme\": \"filled\"\n};\nexport default LeftSquareFilled;", "map": {"version": 3, "names": ["LeftSquareFilled"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/LeftSquareFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar LeftSquareFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM624 380.9c0 10.2-4.9 19.9-13.2 25.9L465.4 512l145.4 105.2c8.3 6 13.2 15.6 13.2 25.9V690c0 6.5-7.4 10.3-12.7 6.5l-246-178a7.95 7.95 0 010-12.9l246-178c5.3-3.8 12.7 0 12.7 6.5v46.8z\" } }] }, \"name\": \"left-square\", \"theme\": \"filled\" };\nexport default LeftSquareFilled;\n"], "mappings": "AAAA;AACA,IAAIA,gBAAgB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAqS;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,aAAa;EAAE,OAAO,EAAE;AAAS,CAAC;AACrf,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}