{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.InternSet = exports.InternMap = void 0;\nclass InternMap extends Map {\n  constructor(entries) {\n    let key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : keyof;\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\nexports.InternMap = InternMap;\nclass InternSet extends Set {\n  constructor(values) {\n    let key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : keyof;\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\nexports.InternSet = InternSet;\nfunction intern_get(_ref, value) {\n  let {\n    _intern,\n    _key\n  } = _ref;\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\nfunction intern_set(_ref2, value) {\n  let {\n    _intern,\n    _key\n  } = _ref2;\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\nfunction intern_delete(_ref3, value) {\n  let {\n    _intern,\n    _key\n  } = _ref3;\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "InternSet", "InternMap", "Map", "constructor", "entries", "key", "arguments", "length", "undefined", "keyof", "defineProperties", "_intern", "_key", "set", "get", "intern_get", "has", "intern_set", "delete", "intern_delete", "Set", "values", "add", "_ref", "_ref2", "_ref3", "valueOf"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/internmap/src/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.InternSet = exports.InternMap = void 0;\n\nclass InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n\n}\n\nexports.InternMap = InternMap;\n\nclass InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n\n}\n\nexports.InternSet = InternSet;\n\nfunction intern_get({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  if (_intern.has(key)) return _intern.get(key);\n\n  _intern.set(key, value);\n\n  return value;\n}\n\nfunction intern_delete({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n\n    _intern.delete(key);\n  }\n\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,SAAS,GAAG,KAAK,CAAC;AAE9C,MAAMA,SAAS,SAASC,GAAG,CAAC;EAC1BC,WAAWA,CAACC,OAAO,EAAe;IAAA,IAAbC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,KAAK;IAC9B,KAAK,CAAC,CAAC;IACPb,MAAM,CAACc,gBAAgB,CAAC,IAAI,EAAE;MAC5BC,OAAO,EAAE;QACPZ,KAAK,EAAE,IAAIG,GAAG,CAAC;MACjB,CAAC;MACDU,IAAI,EAAE;QACJb,KAAK,EAAEM;MACT;IACF,CAAC,CAAC;IACF,IAAID,OAAO,IAAI,IAAI,EAAE,KAAK,MAAM,CAACC,GAAG,EAAEN,KAAK,CAAC,IAAIK,OAAO,EAAE,IAAI,CAACS,GAAG,CAACR,GAAG,EAAEN,KAAK,CAAC;EAC/E;EAEAe,GAAGA,CAACT,GAAG,EAAE;IACP,OAAO,KAAK,CAACS,GAAG,CAACC,UAAU,CAAC,IAAI,EAAEV,GAAG,CAAC,CAAC;EACzC;EAEAW,GAAGA,CAACX,GAAG,EAAE;IACP,OAAO,KAAK,CAACW,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEV,GAAG,CAAC,CAAC;EACzC;EAEAQ,GAAGA,CAACR,GAAG,EAAEN,KAAK,EAAE;IACd,OAAO,KAAK,CAACc,GAAG,CAACI,UAAU,CAAC,IAAI,EAAEZ,GAAG,CAAC,EAAEN,KAAK,CAAC;EAChD;EAEAmB,MAAMA,CAACb,GAAG,EAAE;IACV,OAAO,KAAK,CAACa,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEd,GAAG,CAAC,CAAC;EAC/C;AAEF;AAEAP,OAAO,CAACG,SAAS,GAAGA,SAAS;AAE7B,MAAMD,SAAS,SAASoB,GAAG,CAAC;EAC1BjB,WAAWA,CAACkB,MAAM,EAAe;IAAA,IAAbhB,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,KAAK;IAC7B,KAAK,CAAC,CAAC;IACPb,MAAM,CAACc,gBAAgB,CAAC,IAAI,EAAE;MAC5BC,OAAO,EAAE;QACPZ,KAAK,EAAE,IAAIG,GAAG,CAAC;MACjB,CAAC;MACDU,IAAI,EAAE;QACJb,KAAK,EAAEM;MACT;IACF,CAAC,CAAC;IACF,IAAIgB,MAAM,IAAI,IAAI,EAAE,KAAK,MAAMtB,KAAK,IAAIsB,MAAM,EAAE,IAAI,CAACC,GAAG,CAACvB,KAAK,CAAC;EACjE;EAEAiB,GAAGA,CAACjB,KAAK,EAAE;IACT,OAAO,KAAK,CAACiB,GAAG,CAACD,UAAU,CAAC,IAAI,EAAEhB,KAAK,CAAC,CAAC;EAC3C;EAEAuB,GAAGA,CAACvB,KAAK,EAAE;IACT,OAAO,KAAK,CAACuB,GAAG,CAACL,UAAU,CAAC,IAAI,EAAElB,KAAK,CAAC,CAAC;EAC3C;EAEAmB,MAAMA,CAACnB,KAAK,EAAE;IACZ,OAAO,KAAK,CAACmB,MAAM,CAACC,aAAa,CAAC,IAAI,EAAEpB,KAAK,CAAC,CAAC;EACjD;AAEF;AAEAD,OAAO,CAACE,SAAS,GAAGA,SAAS;AAE7B,SAASe,UAAUA,CAAAQ,IAAA,EAGhBxB,KAAK,EAAE;EAAA,IAHU;IAClBY,OAAO;IACPC;EACF,CAAC,GAAAW,IAAA;EACC,MAAMlB,GAAG,GAAGO,IAAI,CAACb,KAAK,CAAC;EAEvB,OAAOY,OAAO,CAACK,GAAG,CAACX,GAAG,CAAC,GAAGM,OAAO,CAACG,GAAG,CAACT,GAAG,CAAC,GAAGN,KAAK;AACpD;AAEA,SAASkB,UAAUA,CAAAO,KAAA,EAGhBzB,KAAK,EAAE;EAAA,IAHU;IAClBY,OAAO;IACPC;EACF,CAAC,GAAAY,KAAA;EACC,MAAMnB,GAAG,GAAGO,IAAI,CAACb,KAAK,CAAC;EAEvB,IAAIY,OAAO,CAACK,GAAG,CAACX,GAAG,CAAC,EAAE,OAAOM,OAAO,CAACG,GAAG,CAACT,GAAG,CAAC;EAE7CM,OAAO,CAACE,GAAG,CAACR,GAAG,EAAEN,KAAK,CAAC;EAEvB,OAAOA,KAAK;AACd;AAEA,SAASoB,aAAaA,CAAAM,KAAA,EAGnB1B,KAAK,EAAE;EAAA,IAHa;IACrBY,OAAO;IACPC;EACF,CAAC,GAAAa,KAAA;EACC,MAAMpB,GAAG,GAAGO,IAAI,CAACb,KAAK,CAAC;EAEvB,IAAIY,OAAO,CAACK,GAAG,CAACX,GAAG,CAAC,EAAE;IACpBN,KAAK,GAAGY,OAAO,CAACG,GAAG,CAACT,GAAG,CAAC;IAExBM,OAAO,CAACO,MAAM,CAACb,GAAG,CAAC;EACrB;EAEA,OAAON,KAAK;AACd;AAEA,SAASU,KAAKA,CAACV,KAAK,EAAE;EACpB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC2B,OAAO,CAAC,CAAC,GAAG3B,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "script"}