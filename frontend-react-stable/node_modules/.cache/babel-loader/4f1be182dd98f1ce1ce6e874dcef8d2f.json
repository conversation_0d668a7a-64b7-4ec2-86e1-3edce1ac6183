{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.years = exports.default = void 0;\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar year = (0, _interval.default)(function (date) {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setFullYear(date.getFullYear() + step);\n}, function (start, end) {\n  return end.getFullYear() - start.getFullYear();\n}, function (date) {\n  return date.getFullYear();\n}); // An optimized implementation for this simple case.\n\nyear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, _interval.default)(function (date) {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\nvar _default = year;\nexports.default = _default;\nvar years = year.range;\nexports.years = years;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "years", "default", "_interval", "_interopRequireDefault", "require", "obj", "__esModule", "year", "date", "setMonth", "setHours", "step", "setFullYear", "getFullYear", "start", "end", "every", "k", "isFinite", "Math", "floor", "_default", "range"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/year.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.years = exports.default = void 0;\n\nvar _interval = _interopRequireDefault(require(\"./interval.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar year = (0, _interval.default)(function (date) {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, function (date, step) {\n  date.setFullYear(date.getFullYear() + step);\n}, function (start, end) {\n  return end.getFullYear() - start.getFullYear();\n}, function (date) {\n  return date.getFullYear();\n}); // An optimized implementation for this simple case.\n\nyear.every = function (k) {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0, _interval.default)(function (date) {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, function (date, step) {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nvar _default = year;\nexports.default = _default;\nvar years = year.range;\nexports.years = years;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AAExC,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEJ,OAAO,EAAEI;EAAI,CAAC;AAAE;AAE9F,IAAIE,IAAI,GAAG,CAAC,CAAC,EAAEL,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;EAChDA,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACnBD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;EACvBH,IAAI,CAACI,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC7C,CAAC,EAAE,UAAUG,KAAK,EAAEC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACF,WAAW,CAAC,CAAC,GAAGC,KAAK,CAACD,WAAW,CAAC,CAAC;AAChD,CAAC,EAAE,UAAUL,IAAI,EAAE;EACjB,OAAOA,IAAI,CAACK,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC,CAAC;;AAEJN,IAAI,CAACS,KAAK,GAAG,UAAUC,CAAC,EAAE;EACxB,OAAO,CAACC,QAAQ,CAACD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,EAAEf,SAAS,CAACD,OAAO,EAAE,UAAUO,IAAI,EAAE;IAC9FA,IAAI,CAACI,WAAW,CAACO,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGI,CAAC,CAAC,GAAGA,CAAC,CAAC;IACxDT,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,CAAC,EAAE,UAAUF,IAAI,EAAEG,IAAI,EAAE;IACvBH,IAAI,CAACI,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGF,IAAI,GAAGM,CAAC,CAAC;EACjD,CAAC,CAAC;AACJ,CAAC;AAED,IAAII,QAAQ,GAAGd,IAAI;AACnBT,OAAO,CAACG,OAAO,GAAGoB,QAAQ;AAC1B,IAAIrB,KAAK,GAAGO,IAAI,CAACe,KAAK;AACtBxB,OAAO,CAACE,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script"}