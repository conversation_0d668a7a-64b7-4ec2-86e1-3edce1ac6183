{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from '../context/MenuContext';\nvar InternalSubMenuList = function InternalSubMenuList(_ref, ref) {\n  var className = _ref.className,\n    children = _ref.children,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(MenuContext),\n    prefixCls = _React$useContext.prefixCls,\n    mode = _React$useContext.mode,\n    rtl = _React$useContext.rtl;\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: classNames(prefixCls, rtl && \"\".concat(prefixCls, \"-rtl\"), \"\".concat(prefixCls, \"-sub\"), \"\".concat(prefixCls, \"-\").concat(mode === 'inline' ? 'inline' : 'vertical'), className)\n  }, restProps, {\n    \"data-menu-list\": true,\n    ref: ref\n  }), children);\n};\nvar SubMenuList = /*#__PURE__*/React.forwardRef(InternalSubMenuList);\nSubMenuList.displayName = 'SubMenuList';\nexport default SubMenuList;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "React", "classNames", "MenuContext", "InternalSubMenuList", "_ref", "ref", "className", "children", "restProps", "_React$useContext", "useContext", "prefixCls", "mode", "rtl", "createElement", "concat", "SubMenuList", "forwardRef", "displayName"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-menu/es/SubMenu/SubMenuList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"children\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { MenuContext } from '../context/MenuContext';\n\nvar InternalSubMenuList = function InternalSubMenuList(_ref, ref) {\n  var className = _ref.className,\n      children = _ref.children,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n\n  var _React$useContext = React.useContext(MenuContext),\n      prefixCls = _React$useContext.prefixCls,\n      mode = _React$useContext.mode,\n      rtl = _React$useContext.rtl;\n\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: classNames(prefixCls, rtl && \"\".concat(prefixCls, \"-rtl\"), \"\".concat(prefixCls, \"-sub\"), \"\".concat(prefixCls, \"-\").concat(mode === 'inline' ? 'inline' : 'vertical'), className)\n  }, restProps, {\n    \"data-menu-list\": true,\n    ref: ref\n  }), children);\n};\n\nvar SubMenuList = /*#__PURE__*/React.forwardRef(InternalSubMenuList);\nSubMenuList.displayName = 'SubMenuList';\nexport default SubMenuList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,WAAW,QAAQ,wBAAwB;AAEpD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAChE,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,SAAS,GAAGV,wBAAwB,CAACM,IAAI,EAAEL,SAAS,CAAC;EAEzD,IAAIU,iBAAiB,GAAGT,KAAK,CAACU,UAAU,CAACR,WAAW,CAAC;IACjDS,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;IAC7BC,GAAG,GAAGJ,iBAAiB,CAACI,GAAG;EAE/B,OAAO,aAAab,KAAK,CAACc,aAAa,CAAC,IAAI,EAAEjB,QAAQ,CAAC;IACrDS,SAAS,EAAEL,UAAU,CAACU,SAAS,EAAEE,GAAG,IAAI,EAAE,CAACE,MAAM,CAACJ,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC,EAAEN,SAAS;EAC5L,CAAC,EAAEE,SAAS,EAAE;IACZ,gBAAgB,EAAE,IAAI;IACtBH,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEE,QAAQ,CAAC;AACf,CAAC;AAED,IAAIS,WAAW,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAACd,mBAAmB,CAAC;AACpEa,WAAW,CAACE,WAAW,GAAG,aAAa;AACvC,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}