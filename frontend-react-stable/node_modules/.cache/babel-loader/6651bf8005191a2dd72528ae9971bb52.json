{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _reactLifecyclesCompat = require(\"react-lifecycles-compat\");\nvar _ChildMapping = require(\"./utils/ChildMapping\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n  /**\n   * The `<TransitionGroup>` component manages a set of transition components\n   * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n   * components, `<TransitionGroup>` is a state machine for managing the mounting\n   * and unmounting of components over time.\n   *\n   * Consider the example below. As items are removed or added to the TodoList the\n   * `in` prop is toggled automatically by the `<TransitionGroup>`.\n   *\n   * Note that `<TransitionGroup>`  does not define any animation behavior!\n   * Exactly _how_ a list item animates is up to the individual transition\n   * component. This means you can mix and match animations across different list\n   * items.\n   */\n};\nvar TransitionGroup = /*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n  function TransitionGroup(props, context) {\n    var _this;\n    _this = _React$Component.call(this, props, context) || this;\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_assertThisInitialized(_this))); // Initial children should all be entering, dependent on appear\n\n    _this.state = {\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n  var _proto = TransitionGroup.prototype;\n  _proto.getChildContext = function getChildContext() {\n    return {\n      transitionGroup: {\n        isMounting: !this.appeared\n      }\n    };\n  };\n  _proto.componentDidMount = function componentDidMount() {\n    this.appeared = true;\n    this.mounted = true;\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n      handleExited = _ref.handleExited,\n      firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? (0, _ChildMapping.getInitialChildMapping)(nextProps, handleExited) : (0, _ChildMapping.getNextChildMapping)(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  };\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = (0, _ChildMapping.getChildMapping)(this.props.children);\n    if (child.key in currentChildMapping) return;\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      Component = _this$props.component,\n      childFactory = _this$props.childFactory,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n    if (Component === null) {\n      return children;\n    }\n    return _react.default.createElement(Component, props, children);\n  };\n  return TransitionGroup;\n}(_react.default.Component);\nTransitionGroup.childContextTypes = {\n  transitionGroup: _propTypes.default.object.isRequired\n};\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: _propTypes.default.any,\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: _propTypes.default.node,\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: _propTypes.default.bool,\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: _propTypes.default.bool,\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: _propTypes.default.bool,\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: _propTypes.default.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nvar _default = (0, _reactLifecyclesCompat.polyfill)(TransitionGroup);\nexports.default = _default;\nmodule.exports = exports[\"default\"];", "map": {"version": 3, "names": ["exports", "__esModule", "default", "_propTypes", "_interopRequireDefault", "require", "_react", "_reactLifecyclesCompat", "_ChildMapping", "obj", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "_extends", "assign", "arguments", "prototype", "hasOwnProperty", "call", "apply", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "__proto__", "_assertThisInitialized", "self", "ReferenceError", "values", "map", "k", "defaultProps", "component", "childFactory", "child", "TransitionGroup", "_React$Component", "props", "context", "_this", "handleExited", "bind", "state", "firstRender", "_proto", "getChildContext", "transitionGroup", "isMounting", "appeared", "componentDidMount", "mounted", "componentWillUnmount", "getDerivedStateFromProps", "nextProps", "_ref", "prevChildMapping", "children", "getInitialChildMapping", "getNextChildMapping", "node", "currentChildMapping", "get<PERSON>hildMapping", "onExited", "setState", "render", "_this$props", "Component", "appear", "enter", "exit", "createElement", "childContextTypes", "object", "isRequired", "propTypes", "process", "env", "NODE_ENV", "any", "bool", "func", "_default", "polyfill", "module"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-transition-group/TransitionGroup.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\n\nvar _react = _interopRequireDefault(require(\"react\"));\n\nvar _reactLifecyclesCompat = require(\"react-lifecycles-compat\");\n\nvar _ChildMapping = require(\"./utils/ChildMapping\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; subClass.__proto__ = superClass; }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n  /**\n   * The `<TransitionGroup>` component manages a set of transition components\n   * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n   * components, `<TransitionGroup>` is a state machine for managing the mounting\n   * and unmounting of components over time.\n   *\n   * Consider the example below. As items are removed or added to the TodoList the\n   * `in` prop is toggled automatically by the `<TransitionGroup>`.\n   *\n   * Note that `<TransitionGroup>`  does not define any animation behavior!\n   * Exactly _how_ a list item animates is up to the individual transition\n   * component. This means you can mix and match animations across different list\n   * items.\n   */\n\n};\n\nvar TransitionGroup =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_assertThisInitialized(_this))); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.getChildContext = function getChildContext() {\n    return {\n      transitionGroup: {\n        isMounting: !this.appeared\n      }\n    };\n  };\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.appeared = true;\n    this.mounted = true;\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? (0, _ChildMapping.getInitialChildMapping)(nextProps, handleExited) : (0, _ChildMapping.getNextChildMapping)(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  };\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = (0, _ChildMapping.getChildMapping)(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return children;\n    }\n\n    return _react.default.createElement(Component, props, children);\n  };\n\n  return TransitionGroup;\n}(_react.default.Component);\n\nTransitionGroup.childContextTypes = {\n  transitionGroup: _propTypes.default.object.isRequired\n};\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: _propTypes.default.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: _propTypes.default.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: _propTypes.default.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: _propTypes.default.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: _propTypes.default.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: _propTypes.default.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\n\nvar _default = (0, _reactLifecyclesCompat.polyfill)(TransitionGroup);\n\nexports.default = _default;\nmodule.exports = exports[\"default\"];"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAE9D,IAAIC,MAAM,GAAGF,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAErD,IAAIE,sBAAsB,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AAE/D,IAAIG,aAAa,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AAEnD,SAASD,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACR,UAAU,GAAGQ,GAAG,GAAG;IAAEP,OAAO,EAAEO;EAAI,CAAC;AAAE;AAE9F,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAElT,SAASQ,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGN,MAAM,CAACO,MAAM,IAAI,UAAUT,MAAM,EAAE;IAAE,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,SAAS,CAACJ,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIP,MAAM,GAAGY,SAAS,CAACL,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIN,MAAM,EAAE;QAAE,IAAII,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACf,MAAM,EAAEM,GAAG,CAAC,EAAE;UAAEJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOJ,MAAM;EAAE,CAAC;EAAE,OAAOQ,QAAQ,CAACM,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;AAAE;AAE5T,SAASK,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAED,QAAQ,CAACL,SAAS,GAAGT,MAAM,CAACgB,MAAM,CAACD,UAAU,CAACN,SAAS,CAAC;EAAEK,QAAQ,CAACL,SAAS,CAACQ,WAAW,GAAGH,QAAQ;EAAEA,QAAQ,CAACI,SAAS,GAAGH,UAAU;AAAE;AAEtL,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI;AAAE;AAErK,IAAIE,MAAM,GAAGtB,MAAM,CAACsB,MAAM,IAAI,UAAU5B,GAAG,EAAE;EAC3C,OAAOM,MAAM,CAACC,IAAI,CAACP,GAAG,CAAC,CAAC6B,GAAG,CAAC,UAAUC,CAAC,EAAE;IACvC,OAAO9B,GAAG,CAAC8B,CAAC,CAAC;EACf,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,SAASA,YAAYA,CAACC,KAAK,EAAE;IACzC,OAAOA,KAAK;EACd;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,CAAC;AAED,IAAIC,eAAe,GACnB;AACA,UAAUC,gBAAgB,EAAE;EAC1BjB,cAAc,CAACgB,eAAe,EAAEC,gBAAgB,CAAC;EAEjD,SAASD,eAAeA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACvC,IAAIC,KAAK;IAETA,KAAK,GAAGH,gBAAgB,CAACnB,IAAI,CAAC,IAAI,EAAEoB,KAAK,EAAEC,OAAO,CAAC,IAAI,IAAI;IAE3D,IAAIE,YAAY,GAAGD,KAAK,CAACC,YAAY,CAACC,IAAI,CAAChB,sBAAsB,CAACA,sBAAsB,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;IAGnGA,KAAK,CAACG,KAAK,GAAG;MACZF,YAAY,EAAEA,YAAY;MAC1BG,WAAW,EAAE;IACf,CAAC;IACD,OAAOJ,KAAK;EACd;EAEA,IAAIK,MAAM,GAAGT,eAAe,CAACpB,SAAS;EAEtC6B,MAAM,CAACC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAClD,OAAO;MACLC,eAAe,EAAE;QACfC,UAAU,EAAE,CAAC,IAAI,CAACC;MACpB;IACF,CAAC;EACH,CAAC;EAEDJ,MAAM,CAACK,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,IAAI,CAACD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,OAAO,GAAG,IAAI;EACrB,CAAC;EAEDN,MAAM,CAACO,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5D,IAAI,CAACD,OAAO,GAAG,KAAK;EACtB,CAAC;EAEDf,eAAe,CAACiB,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;IAC5F,IAAIC,gBAAgB,GAAGD,IAAI,CAACE,QAAQ;MAChChB,YAAY,GAAGc,IAAI,CAACd,YAAY;MAChCG,WAAW,GAAGW,IAAI,CAACX,WAAW;IAClC,OAAO;MACLa,QAAQ,EAAEb,WAAW,GAAG,CAAC,CAAC,EAAE5C,aAAa,CAAC0D,sBAAsB,EAAEJ,SAAS,EAAEb,YAAY,CAAC,GAAG,CAAC,CAAC,EAAEzC,aAAa,CAAC2D,mBAAmB,EAAEL,SAAS,EAAEE,gBAAgB,EAAEf,YAAY,CAAC;MAC9KG,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAEDC,MAAM,CAACJ,YAAY,GAAG,SAASA,YAAYA,CAACN,KAAK,EAAEyB,IAAI,EAAE;IACvD,IAAIC,mBAAmB,GAAG,CAAC,CAAC,EAAE7D,aAAa,CAAC8D,eAAe,EAAE,IAAI,CAACxB,KAAK,CAACmB,QAAQ,CAAC;IACjF,IAAItB,KAAK,CAAC1B,GAAG,IAAIoD,mBAAmB,EAAE;IAEtC,IAAI1B,KAAK,CAACG,KAAK,CAACyB,QAAQ,EAAE;MACxB5B,KAAK,CAACG,KAAK,CAACyB,QAAQ,CAACH,IAAI,CAAC;IAC5B;IAEA,IAAI,IAAI,CAACT,OAAO,EAAE;MAChB,IAAI,CAACa,QAAQ,CAAC,UAAUrB,KAAK,EAAE;QAC7B,IAAIc,QAAQ,GAAG5C,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,CAACc,QAAQ,CAAC;QAE3C,OAAOA,QAAQ,CAACtB,KAAK,CAAC1B,GAAG,CAAC;QAC1B,OAAO;UACLgD,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;EAEDZ,MAAM,CAACoB,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIC,WAAW,GAAG,IAAI,CAAC5B,KAAK;MACxB6B,SAAS,GAAGD,WAAW,CAACjC,SAAS;MACjCC,YAAY,GAAGgC,WAAW,CAAChC,YAAY;MACvCI,KAAK,GAAGpC,6BAA6B,CAACgE,WAAW,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAErF,IAAIT,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAACc,KAAK,CAACc,QAAQ,CAAC,CAAC3B,GAAG,CAACI,YAAY,CAAC;IAC5D,OAAOI,KAAK,CAAC8B,MAAM;IACnB,OAAO9B,KAAK,CAAC+B,KAAK;IAClB,OAAO/B,KAAK,CAACgC,IAAI;IAEjB,IAAIH,SAAS,KAAK,IAAI,EAAE;MACtB,OAAOV,QAAQ;IACjB;IAEA,OAAO3D,MAAM,CAACJ,OAAO,CAAC6E,aAAa,CAACJ,SAAS,EAAE7B,KAAK,EAAEmB,QAAQ,CAAC;EACjE,CAAC;EAED,OAAOrB,eAAe;AACxB,CAAC,CAACtC,MAAM,CAACJ,OAAO,CAACyE,SAAS,CAAC;AAE3B/B,eAAe,CAACoC,iBAAiB,GAAG;EAClCzB,eAAe,EAAEpD,UAAU,CAACD,OAAO,CAAC+E,MAAM,CAACC;AAC7C,CAAC;AACDtC,eAAe,CAACuC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAClE;AACF;AACA;AACA;AACA;AACA;AACA;EACE7C,SAAS,EAAEtC,UAAU,CAACD,OAAO,CAACqF,GAAG;EAEjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtB,QAAQ,EAAE9D,UAAU,CAACD,OAAO,CAACkE,IAAI;EAEjC;AACF;AACA;AACA;AACA;EACEQ,MAAM,EAAEzE,UAAU,CAACD,OAAO,CAACsF,IAAI;EAE/B;AACF;AACA;AACA;AACA;EACEX,KAAK,EAAE1E,UAAU,CAACD,OAAO,CAACsF,IAAI;EAE9B;AACF;AACA;AACA;AACA;EACEV,IAAI,EAAE3E,UAAU,CAACD,OAAO,CAACsF,IAAI;EAE7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9C,YAAY,EAAEvC,UAAU,CAACD,OAAO,CAACuF;AACnC,CAAC,GAAG,CAAC,CAAC;AACN7C,eAAe,CAACJ,YAAY,GAAGA,YAAY;AAE3C,IAAIkD,QAAQ,GAAG,CAAC,CAAC,EAAEnF,sBAAsB,CAACoF,QAAQ,EAAE/C,eAAe,CAAC;AAEpE5C,OAAO,CAACE,OAAO,GAAGwF,QAAQ;AAC1BE,MAAM,CAAC5F,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}