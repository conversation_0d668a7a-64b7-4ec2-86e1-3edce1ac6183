{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nvar _excluded = [\"name\", \"errors\"];\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { HOOK_MARK } from './FieldContext';\nimport { allPromiseFinish } from './utils/asyncUtil';\nimport cloneDeep from './utils/cloneDeep';\nimport { defaultValidateMessages } from './utils/messages';\nimport NameMap from './utils/NameMap';\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue, setValues } from './utils/valueUtil';\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n  _classCallCheck(this, FormStore);\n  this.formHooked = false;\n  this.forceRootUpdate = void 0;\n  this.subscribable = true;\n  this.store = {};\n  this.fieldEntities = [];\n  this.initialValues = {};\n  this.callbacks = {};\n  this.validateMessages = null;\n  this.preserve = null;\n  this.lastValidatePromise = null;\n  this.getForm = function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldValue: _this.setFieldValue,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  };\n  this.getInternalHooks = function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  };\n  this.useSubscribe = function (subscribable) {\n    _this.subscribable = subscribable;\n  };\n  this.prevWithoutPreserves = null;\n  this.setInitialValues = function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n    if (init) {\n      var _this$prevWithoutPres;\n      var nextStore = setValues({}, initialValues, _this.store);\n      // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 ? void 0 : _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n      _this.updateStore(nextStore);\n    }\n  };\n  this.destroyForm = function () {\n    var prevWithoutPreserves = new NameMap();\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!_this.isMergedPreserve(entity.isPreserve())) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  };\n  this.getInitialValue = function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath);\n    // Not cloneDeep when without `namePath`\n    return namePath.length ? cloneDeep(initValue) : initValue;\n  };\n  this.setCallbacks = function (callbacks) {\n    _this.callbacks = callbacks;\n  };\n  this.setValidateMessages = function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  };\n  this.setPreserve = function (preserve) {\n    _this.preserve = preserve;\n  };\n  this.watchList = [];\n  this.registerWatch = function (callback) {\n    _this.watchList.push(callback);\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  };\n  this.notifyWatch = function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n      _this.watchList.forEach(function (callback) {\n        callback(values, namePath);\n      });\n    }\n  };\n  this.timeoutId = null;\n  this.warningUnhooked = function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  };\n  this.updateStore = function (nextStore) {\n    _this.store = nextStore;\n  };\n  this.getFieldEntities = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  };\n  this.getFieldsMap = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n    return cache;\n  };\n  this.getFieldEntitiesForNamePathList = function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n    var cache = _this.getFieldsMap(true);\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  };\n  this.getFieldsValue = function (nameList, filterFunc) {\n    _this.warningUnhooked();\n    if (nameList === true && !filterFunc) {\n      return _this.store;\n    }\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(nameList) ? nameList : null);\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _entity$isListField;\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath();\n      // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n      if (!nameList && ((_entity$isListField = entity.isListField) === null || _entity$isListField === void 0 ? void 0 : _entity$isListField.call(entity))) {\n        return;\n      }\n      if (!filterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n        if (filterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  };\n  this.getFieldValue = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  };\n  this.getFieldsError = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  };\n  this.getFieldError = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.errors;\n  };\n  this.getFieldWarning = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.warnings;\n  };\n  this.isFieldsTouched = function () {\n    _this.warningUnhooked();\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var arg0 = args[0],\n      arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n    var fieldEntities = _this.getFieldEntities(true);\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    };\n    // ===== Will get fully compare when not config namePathList =====\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(isFieldTouched) : fieldEntities.some(isFieldTouched);\n    }\n    // Generate a nest tree for validate\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath();\n      // Find matched entity and put into list\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    });\n    // Check if NameMap value is touched\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n    var namePathListEntities = map.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  };\n  this.isFieldTouched = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsTouched([name]);\n  };\n  this.isFieldsValidating = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntities();\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  };\n  this.isFieldValidating = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsValidating([name]);\n  };\n  this.resetWithFieldInitialValue = function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n    var fieldEntities = _this.getFieldEntities(true);\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath();\n      // Record only if has `initialValue`\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    });\n    // Reset\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n          var formInitialValue = _this.getInitialValue(namePath);\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath);\n              // Set `initialValue`\n              if (!info.skipExist || originValue === undefined) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n    var requiredFieldEntities;\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n        if (records) {\n          var _requiredFieldEntitie;\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n    resetWithFields(requiredFieldEntities);\n  };\n  this.resetFields = function (nameList) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (!nameList) {\n      _this.updateStore(setValues({}, _this.initialValues));\n      _this.resetWithFieldInitialValue();\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n      _this.notifyWatch();\n      return;\n    }\n    // Reset by `nameList`\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.setFields = function (fields) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n        errors = fieldData.errors,\n        data = _objectWithoutProperties(fieldData, _excluded);\n      var namePath = getNamePath(name);\n      namePathList.push(namePath);\n      // Value\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.getFields = function () {\n    var entities = _this.getFieldEntities(true);\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  };\n  this.initEntityValue = function (entity) {\n    var initialValue = entity.props.initialValue;\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  };\n  this.isMergedPreserve = function (fieldPreserve) {\n    var mergedPreserve = fieldPreserve !== undefined ? fieldPreserve : _this.preserve;\n    return mergedPreserve !== null && mergedPreserve !== void 0 ? mergedPreserve : true;\n  };\n  this.registerField = function (entity) {\n    _this.fieldEntities.push(entity);\n    var namePath = entity.getNamePath();\n    _this.notifyWatch([namePath]);\n    // Set initial values\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    }\n    // un-register field callback\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      });\n      // Clean up store value if not preserve\n      if (!_this.isMergedPreserve(preserve) && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (\n            // Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true));\n          // Notify that field is unmount\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          });\n          // Dependencies update\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n      _this.notifyWatch([namePath]);\n    };\n  };\n  this.dispatch = function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n            value = action.value;\n          _this.updateValue(namePath, value);\n          break;\n        }\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n            triggerName = action.triggerName;\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n          break;\n        }\n      default:\n      // Currently we don't have other action. Do nothing.\n    }\n  };\n  this.notifyObservers = function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n      _this.getFieldEntities().forEach(function (_ref3) {\n        var onStoreChange = _ref3.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  };\n  this.triggerDependenciesUpdate = function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n    return childrenFields;\n  };\n  this.updateValue = function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n    _this.updateStore(setValue(_this.store, namePath, value));\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n    _this.notifyWatch([namePath]);\n    // Dependencies update\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath);\n    // trigger callback function\n    var onValuesChange = _this.callbacks.onValuesChange;\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  };\n  this.setFieldsValue = function (store) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (store) {\n      var nextStore = setValues(_this.store, store);\n      _this.updateStore(nextStore);\n    }\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n    _this.notifyWatch();\n  };\n  this.setFieldValue = function (name, value) {\n    _this.setFields([{\n      name: name,\n      value: value\n    }]);\n  };\n  this.getDependencyChildrenFields = function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n    fillChildren(rootNamePath);\n    return childrenFields;\n  };\n  this.triggerOnFieldsChange = function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref4) {\n          var name = _ref4.name,\n            errors = _ref4.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n      var changedFields = fields.filter(function (_ref5) {\n        var fieldName = _ref5.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      onFieldsChange(changedFields, fields);\n    }\n  };\n  this.validateFields = function (nameList, options) {\n    _this.warningUnhooked();\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : [];\n    // Collect result in promise list\n    var promiseList = [];\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n      /**\n       * Recursive validate if configured.\n       * TODO: perf improvement @zombieJ\n       */\n      if ((options === null || options === void 0 ? void 0 : options.recursive) && provideNameList) {\n        var namePath = field.getNamePath();\n        if (\n        // nameList[i] === undefined 说明是以 nameList 开头的\n        // ['name'] -> ['name','list']\n        namePath.every(function (nameUnit, i) {\n          return nameList[i] === nameUnit || nameList[i] === undefined;\n        })) {\n          namePathList.push(namePath);\n        }\n      }\n      // Skip if without rule\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n      var fieldNamePath = field.getNamePath();\n      // Add field validate rule in to promise list\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options));\n        // Wrap promise with field\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var _ruleErrors$forEach;\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 ? void 0 : _ruleErrors$forEach.call(ruleErrors, function (_ref6) {\n            var warningOnly = _ref6.rule.warningOnly,\n              errors = _ref6.errors;\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise;\n    // Notify fields with rule that validate has finished and need update\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref7) {\n        var name = _ref7.name;\n        return name;\n      });\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    });\n    // Do not throw in console\n    returnPromise.catch(function (e) {\n      return e;\n    });\n    return returnPromise;\n  };\n  this.submit = function () {\n    _this.warningUnhooked();\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  };\n  this.forceRootUpdate = forceRootUpdate;\n});\nfunction useForm(form) {\n  var formRef = React.useRef();\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n  return [formRef.current];\n}\nexport default useForm;", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_toConsumableArray", "_createClass", "_classCallCheck", "_excluded", "warning", "React", "HOOK_MARK", "allPromiseFinish", "cloneDeep", "defaultValidateMessages", "NameMap", "cloneByNamePathList", "containsNamePath", "getNamePath", "getValue", "matchNamePath", "setValue", "set<PERSON><PERSON><PERSON>", "FormStore", "forceRootUpdate", "_this", "formHooked", "subscribable", "store", "fieldEntities", "initialValues", "callbacks", "validateMessages", "preserve", "lastValidatePromise", "getForm", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "setFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "_init", "getInternalHooks", "key", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "setValidateMessages", "getFields", "setPreserve", "getInitialValue", "registerWatch", "prevWithoutPreserves", "init", "_this$prevWithoutPres", "nextStore", "map", "_ref", "namePath", "updateStore", "getFieldEntities", "for<PERSON>ach", "entity", "isMergedPreserve", "isPreserve", "set", "initValue", "length", "watchList", "callback", "push", "filter", "fn", "notifyWatch", "arguments", "undefined", "values", "timeoutId", "warningUnhooked", "process", "env", "NODE_ENV", "window", "setTimeout", "pure", "field", "getFieldsMap", "cache", "getFieldEntitiesForNamePathList", "nameList", "name", "get", "INVALIDATE_NAME_PATH", "filterFunc", "Array", "isArray", "filteredNameList", "_entity$isListField", "isListField", "call", "meta", "getMeta", "index", "errors", "getErrors", "warnings", "getWarnings", "fieldError", "_len", "args", "_key", "arg0", "arg1", "namePathList", "isAllFieldsTouched", "every", "some", "shortNamePath", "fieldNamePath", "nameUnit", "i", "update", "list", "concat", "isNamePathListTouched", "entities", "namePathListEntities", "_ref2", "value", "testField", "resetWithFieldInitialValue", "info", "initialValue", "props", "records", "Set", "add", "reset<PERSON><PERSON><PERSON><PERSON>s", "formInitialValue", "join", "size", "originValue", "skipExist", "requiredFieldEntities", "_requiredF<PERSON><PERSON><PERSON><PERSON>", "apply", "r", "prevStore", "notifyObservers", "type", "fields", "fieldData", "data", "Object", "defineProperty", "prevValue", "fieldPreserve", "mergedPreserve", "source", "subNamePath", "item", "defaultValue", "_prevStore", "triggerDependenciesUpdate", "action", "updateValue", "_namePath", "triggerName", "mergedInfo", "_ref3", "onStoreChange", "childrenFields", "getDep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "relatedFields", "onValuesChange", "changedValues", "trigger<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNamePath", "children", "dependencies2fields", "dependencies", "dependency", "dependencyNamePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filedErrors", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "changed<PERSON>ields", "_ref5", "fieldName", "options", "provideNameList", "promiseList", "recursive", "rules", "promise", "validateRules", "then", "catch", "ruleErrors", "_ruleErrors$forEach", "mergedErrors", "mergedWarnings", "_ref6", "warningOnly", "rule", "Promise", "reject", "summaryPromise", "results", "resultNamePathList", "_ref7", "returnPromise", "resolve", "errorList", "result", "errorFields", "outOfDate", "e", "onFinish", "err", "console", "error", "onFinishFailed", "useForm", "form", "formRef", "useRef", "_React$useState", "useState", "_React$useState2", "forceUpdate", "current", "forceReRender", "formStore"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-field-form/es/useForm.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nvar _excluded = [\"name\", \"errors\"];\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { HOOK_MARK } from './FieldContext';\nimport { allPromiseFinish } from './utils/asyncUtil';\nimport cloneDeep from './utils/cloneDeep';\nimport { defaultValidateMessages } from './utils/messages';\nimport NameMap from './utils/NameMap';\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue, setValues } from './utils/valueUtil';\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n  _classCallCheck(this, FormStore);\n  this.formHooked = false;\n  this.forceRootUpdate = void 0;\n  this.subscribable = true;\n  this.store = {};\n  this.fieldEntities = [];\n  this.initialValues = {};\n  this.callbacks = {};\n  this.validateMessages = null;\n  this.preserve = null;\n  this.lastValidatePromise = null;\n  this.getForm = function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldValue: _this.setFieldValue,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  };\n  this.getInternalHooks = function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  };\n  this.useSubscribe = function (subscribable) {\n    _this.subscribable = subscribable;\n  };\n  this.prevWithoutPreserves = null;\n  this.setInitialValues = function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n    if (init) {\n      var _this$prevWithoutPres;\n      var nextStore = setValues({}, initialValues, _this.store);\n      // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 ? void 0 : _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n      _this.updateStore(nextStore);\n    }\n  };\n  this.destroyForm = function () {\n    var prevWithoutPreserves = new NameMap();\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!_this.isMergedPreserve(entity.isPreserve())) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  };\n  this.getInitialValue = function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath);\n    // Not cloneDeep when without `namePath`\n    return namePath.length ? cloneDeep(initValue) : initValue;\n  };\n  this.setCallbacks = function (callbacks) {\n    _this.callbacks = callbacks;\n  };\n  this.setValidateMessages = function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  };\n  this.setPreserve = function (preserve) {\n    _this.preserve = preserve;\n  };\n  this.watchList = [];\n  this.registerWatch = function (callback) {\n    _this.watchList.push(callback);\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  };\n  this.notifyWatch = function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n      _this.watchList.forEach(function (callback) {\n        callback(values, namePath);\n      });\n    }\n  };\n  this.timeoutId = null;\n  this.warningUnhooked = function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  };\n  this.updateStore = function (nextStore) {\n    _this.store = nextStore;\n  };\n  this.getFieldEntities = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  };\n  this.getFieldsMap = function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n    return cache;\n  };\n  this.getFieldEntitiesForNamePathList = function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n    var cache = _this.getFieldsMap(true);\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  };\n  this.getFieldsValue = function (nameList, filterFunc) {\n    _this.warningUnhooked();\n    if (nameList === true && !filterFunc) {\n      return _this.store;\n    }\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(nameList) ? nameList : null);\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _entity$isListField;\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath();\n      // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n      if (!nameList && ((_entity$isListField = entity.isListField) === null || _entity$isListField === void 0 ? void 0 : _entity$isListField.call(entity))) {\n        return;\n      }\n      if (!filterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n        if (filterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  };\n  this.getFieldValue = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  };\n  this.getFieldsError = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  };\n  this.getFieldError = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.errors;\n  };\n  this.getFieldWarning = function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.warnings;\n  };\n  this.isFieldsTouched = function () {\n    _this.warningUnhooked();\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var arg0 = args[0],\n      arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n    var fieldEntities = _this.getFieldEntities(true);\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    };\n    // ===== Will get fully compare when not config namePathList =====\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(isFieldTouched) : fieldEntities.some(isFieldTouched);\n    }\n    // Generate a nest tree for validate\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath();\n      // Find matched entity and put into list\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    });\n    // Check if NameMap value is touched\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n    var namePathListEntities = map.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  };\n  this.isFieldTouched = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsTouched([name]);\n  };\n  this.isFieldsValidating = function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntities();\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  };\n  this.isFieldValidating = function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsValidating([name]);\n  };\n  this.resetWithFieldInitialValue = function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n    var fieldEntities = _this.getFieldEntities(true);\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath();\n      // Record only if has `initialValue`\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    });\n    // Reset\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n          var formInitialValue = _this.getInitialValue(namePath);\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath);\n              // Set `initialValue`\n              if (!info.skipExist || originValue === undefined) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n    var requiredFieldEntities;\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n        if (records) {\n          var _requiredFieldEntitie;\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n    resetWithFields(requiredFieldEntities);\n  };\n  this.resetFields = function (nameList) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (!nameList) {\n      _this.updateStore(setValues({}, _this.initialValues));\n      _this.resetWithFieldInitialValue();\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n      _this.notifyWatch();\n      return;\n    }\n    // Reset by `nameList`\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.setFields = function (fields) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n        errors = fieldData.errors,\n        data = _objectWithoutProperties(fieldData, _excluded);\n      var namePath = getNamePath(name);\n      namePathList.push(namePath);\n      // Value\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n    _this.notifyWatch(namePathList);\n  };\n  this.getFields = function () {\n    var entities = _this.getFieldEntities(true);\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  };\n  this.initEntityValue = function (entity) {\n    var initialValue = entity.props.initialValue;\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  };\n  this.isMergedPreserve = function (fieldPreserve) {\n    var mergedPreserve = fieldPreserve !== undefined ? fieldPreserve : _this.preserve;\n    return mergedPreserve !== null && mergedPreserve !== void 0 ? mergedPreserve : true;\n  };\n  this.registerField = function (entity) {\n    _this.fieldEntities.push(entity);\n    var namePath = entity.getNamePath();\n    _this.notifyWatch([namePath]);\n    // Set initial values\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    }\n    // un-register field callback\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      });\n      // Clean up store value if not preserve\n      if (!_this.isMergedPreserve(preserve) && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (\n            // Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true));\n          // Notify that field is unmount\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          });\n          // Dependencies update\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n      _this.notifyWatch([namePath]);\n    };\n  };\n  this.dispatch = function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n            value = action.value;\n          _this.updateValue(namePath, value);\n          break;\n        }\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n            triggerName = action.triggerName;\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n          break;\n        }\n      default:\n      // Currently we don't have other action. Do nothing.\n    }\n  };\n  this.notifyObservers = function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n      _this.getFieldEntities().forEach(function (_ref3) {\n        var onStoreChange = _ref3.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  };\n  this.triggerDependenciesUpdate = function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n    return childrenFields;\n  };\n  this.updateValue = function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n    _this.updateStore(setValue(_this.store, namePath, value));\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n    _this.notifyWatch([namePath]);\n    // Dependencies update\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath);\n    // trigger callback function\n    var onValuesChange = _this.callbacks.onValuesChange;\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  };\n  this.setFieldsValue = function (store) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (store) {\n      var nextStore = setValues(_this.store, store);\n      _this.updateStore(nextStore);\n    }\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n    _this.notifyWatch();\n  };\n  this.setFieldValue = function (name, value) {\n    _this.setFields([{\n      name: name,\n      value: value\n    }]);\n  };\n  this.getDependencyChildrenFields = function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n    fillChildren(rootNamePath);\n    return childrenFields;\n  };\n  this.triggerOnFieldsChange = function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref4) {\n          var name = _ref4.name,\n            errors = _ref4.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n      var changedFields = fields.filter(function (_ref5) {\n        var fieldName = _ref5.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      onFieldsChange(changedFields, fields);\n    }\n  };\n  this.validateFields = function (nameList, options) {\n    _this.warningUnhooked();\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : [];\n    // Collect result in promise list\n    var promiseList = [];\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n      /**\n       * Recursive validate if configured.\n       * TODO: perf improvement @zombieJ\n       */\n      if ((options === null || options === void 0 ? void 0 : options.recursive) && provideNameList) {\n        var namePath = field.getNamePath();\n        if (\n        // nameList[i] === undefined 说明是以 nameList 开头的\n        // ['name'] -> ['name','list']\n        namePath.every(function (nameUnit, i) {\n          return nameList[i] === nameUnit || nameList[i] === undefined;\n        })) {\n          namePathList.push(namePath);\n        }\n      }\n      // Skip if without rule\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n      var fieldNamePath = field.getNamePath();\n      // Add field validate rule in to promise list\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options));\n        // Wrap promise with field\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var _ruleErrors$forEach;\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 ? void 0 : _ruleErrors$forEach.call(ruleErrors, function (_ref6) {\n            var warningOnly = _ref6.rule.warningOnly,\n              errors = _ref6.errors;\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise;\n    // Notify fields with rule that validate has finished and need update\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref7) {\n        var name = _ref7.name;\n        return name;\n      });\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    });\n    // Do not throw in console\n    returnPromise.catch(function (e) {\n      return e;\n    });\n    return returnPromise;\n  };\n  this.submit = function () {\n    _this.warningUnhooked();\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  };\n  this.forceRootUpdate = forceRootUpdate;\n});\nfunction useForm(form) {\n  var formRef = React.useRef();\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n  return [formRef.current];\n}\nexport default useForm;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;AAClC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACpI,OAAO,IAAIC,SAAS,GAAG,aAAajB,YAAY,CAAC,SAASiB,SAASA,CAACC,eAAe,EAAE;EACnF,IAAIC,KAAK,GAAG,IAAI;EAChBlB,eAAe,CAAC,IAAI,EAAEgB,SAAS,CAAC;EAChC,IAAI,CAACG,UAAU,GAAG,KAAK;EACvB,IAAI,CAACF,eAAe,GAAG,KAAK,CAAC;EAC7B,IAAI,CAACG,YAAY,GAAG,IAAI;EACxB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAACC,aAAa,GAAG,EAAE;EACvB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;EACvB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACnB,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,mBAAmB,GAAG,IAAI;EAC/B,IAAI,CAACC,OAAO,GAAG,YAAY;IACzB,OAAO;MACLC,aAAa,EAAEX,KAAK,CAACW,aAAa;MAClCC,cAAc,EAAEZ,KAAK,CAACY,cAAc;MACpCC,aAAa,EAAEb,KAAK,CAACa,aAAa;MAClCC,eAAe,EAAEd,KAAK,CAACc,eAAe;MACtCC,cAAc,EAAEf,KAAK,CAACe,cAAc;MACpCC,eAAe,EAAEhB,KAAK,CAACgB,eAAe;MACtCC,cAAc,EAAEjB,KAAK,CAACiB,cAAc;MACpCC,iBAAiB,EAAElB,KAAK,CAACkB,iBAAiB;MAC1CC,kBAAkB,EAAEnB,KAAK,CAACmB,kBAAkB;MAC5CC,WAAW,EAAEpB,KAAK,CAACoB,WAAW;MAC9BC,SAAS,EAAErB,KAAK,CAACqB,SAAS;MAC1BC,aAAa,EAAEtB,KAAK,CAACsB,aAAa;MAClCC,cAAc,EAAEvB,KAAK,CAACuB,cAAc;MACpCC,cAAc,EAAExB,KAAK,CAACwB,cAAc;MACpCC,MAAM,EAAEzB,KAAK,CAACyB,MAAM;MACpBC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE3B,KAAK,CAAC2B;IAC1B,CAAC;EACH,CAAC;EACD,IAAI,CAACA,gBAAgB,GAAG,UAAUC,GAAG,EAAE;IACrC,IAAIA,GAAG,KAAK1C,SAAS,EAAE;MACrBc,KAAK,CAACC,UAAU,GAAG,IAAI;MACvB,OAAO;QACL4B,QAAQ,EAAE7B,KAAK,CAAC6B,QAAQ;QACxBC,eAAe,EAAE9B,KAAK,CAAC8B,eAAe;QACtCC,aAAa,EAAE/B,KAAK,CAAC+B,aAAa;QAClCC,YAAY,EAAEhC,KAAK,CAACgC,YAAY;QAChCC,gBAAgB,EAAEjC,KAAK,CAACiC,gBAAgB;QACxCC,WAAW,EAAElC,KAAK,CAACkC,WAAW;QAC9BC,YAAY,EAAEnC,KAAK,CAACmC,YAAY;QAChCC,mBAAmB,EAAEpC,KAAK,CAACoC,mBAAmB;QAC9CC,SAAS,EAAErC,KAAK,CAACqC,SAAS;QAC1BC,WAAW,EAAEtC,KAAK,CAACsC,WAAW;QAC9BC,eAAe,EAAEvC,KAAK,CAACuC,eAAe;QACtCC,aAAa,EAAExC,KAAK,CAACwC;MACvB,CAAC;IACH;IACAxD,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;IACjF,OAAO,IAAI;EACb,CAAC;EACD,IAAI,CAACgD,YAAY,GAAG,UAAU9B,YAAY,EAAE;IAC1CF,KAAK,CAACE,YAAY,GAAGA,YAAY;EACnC,CAAC;EACD,IAAI,CAACuC,oBAAoB,GAAG,IAAI;EAChC,IAAI,CAACR,gBAAgB,GAAG,UAAU5B,aAAa,EAAEqC,IAAI,EAAE;IACrD1C,KAAK,CAACK,aAAa,GAAGA,aAAa,IAAI,CAAC,CAAC;IACzC,IAAIqC,IAAI,EAAE;MACR,IAAIC,qBAAqB;MACzB,IAAIC,SAAS,GAAG/C,SAAS,CAAC,CAAC,CAAC,EAAEQ,aAAa,EAAEL,KAAK,CAACG,KAAK,CAAC;MACzD;MACA;MACA;MACA,CAACwC,qBAAqB,GAAG3C,KAAK,CAACyC,oBAAoB,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrJ,IAAIC,QAAQ,GAAGD,IAAI,CAAClB,GAAG;QACvBgB,SAAS,GAAGhD,QAAQ,CAACgD,SAAS,EAAEG,QAAQ,EAAErD,QAAQ,CAACW,aAAa,EAAE0C,QAAQ,CAAC,CAAC;MAC9E,CAAC,CAAC;MACF/C,KAAK,CAACyC,oBAAoB,GAAG,IAAI;MACjCzC,KAAK,CAACgD,WAAW,CAACJ,SAAS,CAAC;IAC9B;EACF,CAAC;EACD,IAAI,CAACV,WAAW,GAAG,YAAY;IAC7B,IAAIO,oBAAoB,GAAG,IAAInD,OAAO,CAAC,CAAC;IACxCU,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MACrD,IAAI,CAACnD,KAAK,CAACoD,gBAAgB,CAACD,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;QAChDZ,oBAAoB,CAACa,GAAG,CAACH,MAAM,CAAC1D,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;MACtD;IACF,CAAC,CAAC;IACFO,KAAK,CAACyC,oBAAoB,GAAGA,oBAAoB;EACnD,CAAC;EACD,IAAI,CAACF,eAAe,GAAG,UAAUQ,QAAQ,EAAE;IACzC,IAAIQ,SAAS,GAAG7D,QAAQ,CAACM,KAAK,CAACK,aAAa,EAAE0C,QAAQ,CAAC;IACvD;IACA,OAAOA,QAAQ,CAACS,MAAM,GAAGpE,SAAS,CAACmE,SAAS,CAAC,GAAGA,SAAS;EAC3D,CAAC;EACD,IAAI,CAACpB,YAAY,GAAG,UAAU7B,SAAS,EAAE;IACvCN,KAAK,CAACM,SAAS,GAAGA,SAAS;EAC7B,CAAC;EACD,IAAI,CAAC8B,mBAAmB,GAAG,UAAU7B,gBAAgB,EAAE;IACrDP,KAAK,CAACO,gBAAgB,GAAGA,gBAAgB;EAC3C,CAAC;EACD,IAAI,CAAC+B,WAAW,GAAG,UAAU9B,QAAQ,EAAE;IACrCR,KAAK,CAACQ,QAAQ,GAAGA,QAAQ;EAC3B,CAAC;EACD,IAAI,CAACiD,SAAS,GAAG,EAAE;EACnB,IAAI,CAACjB,aAAa,GAAG,UAAUkB,QAAQ,EAAE;IACvC1D,KAAK,CAACyD,SAAS,CAACE,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO,YAAY;MACjB1D,KAAK,CAACyD,SAAS,GAAGzD,KAAK,CAACyD,SAAS,CAACG,MAAM,CAAC,UAAUC,EAAE,EAAE;QACrD,OAAOA,EAAE,KAAKH,QAAQ;MACxB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;EACD,IAAI,CAACI,WAAW,GAAG,YAAY;IAC7B,IAAIf,QAAQ,GAAGgB,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACrF;IACA,IAAI/D,KAAK,CAACyD,SAAS,CAACD,MAAM,EAAE;MAC1B,IAAIS,MAAM,GAAGjE,KAAK,CAACY,cAAc,CAAC,CAAC;MACnCZ,KAAK,CAACyD,SAAS,CAACP,OAAO,CAAC,UAAUQ,QAAQ,EAAE;QAC1CA,QAAQ,CAACO,MAAM,EAAElB,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAI,CAACmB,SAAS,GAAG,IAAI;EACrB,IAAI,CAACC,eAAe,GAAG,YAAY;IACjC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACtE,KAAK,CAACkE,SAAS,IAAI,OAAOK,MAAM,KAAK,WAAW,EAAE;MAC9FvE,KAAK,CAACkE,SAAS,GAAGM,UAAU,CAAC,YAAY;QACvCxE,KAAK,CAACkE,SAAS,GAAG,IAAI;QACtB,IAAI,CAAClE,KAAK,CAACC,UAAU,EAAE;UACrBjB,OAAO,CAAC,KAAK,EAAE,iGAAiG,CAAC;QACnH;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,IAAI,CAACgE,WAAW,GAAG,UAAUJ,SAAS,EAAE;IACtC5C,KAAK,CAACG,KAAK,GAAGyC,SAAS;EACzB,CAAC;EACD,IAAI,CAACK,gBAAgB,GAAG,YAAY;IAClC,IAAIwB,IAAI,GAAGV,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpF,IAAI,CAACU,IAAI,EAAE;MACT,OAAOzE,KAAK,CAACI,aAAa;IAC5B;IACA,OAAOJ,KAAK,CAACI,aAAa,CAACwD,MAAM,CAAC,UAAUc,KAAK,EAAE;MACjD,OAAOA,KAAK,CAACjF,WAAW,CAAC,CAAC,CAAC+D,MAAM;IACnC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACmB,YAAY,GAAG,YAAY;IAC9B,IAAIF,IAAI,GAAGV,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpF,IAAIa,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;IACzBU,KAAK,CAACiD,gBAAgB,CAACwB,IAAI,CAAC,CAACvB,OAAO,CAAC,UAAUwB,KAAK,EAAE;MACpD,IAAI3B,QAAQ,GAAG2B,KAAK,CAACjF,WAAW,CAAC,CAAC;MAClCmF,KAAK,CAACtB,GAAG,CAACP,QAAQ,EAAE2B,KAAK,CAAC;IAC5B,CAAC,CAAC;IACF,OAAOE,KAAK;EACd,CAAC;EACD,IAAI,CAACC,+BAA+B,GAAG,UAAUC,QAAQ,EAAE;IACzD,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO9E,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC;IACrC;IACA,IAAI2B,KAAK,GAAG5E,KAAK,CAAC2E,YAAY,CAAC,IAAI,CAAC;IACpC,OAAOG,QAAQ,CAACjC,GAAG,CAAC,UAAUkC,IAAI,EAAE;MAClC,IAAIhC,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;MAChC,OAAOH,KAAK,CAACI,GAAG,CAACjC,QAAQ,CAAC,IAAI;QAC5BkC,oBAAoB,EAAExF,WAAW,CAACsF,IAAI;MACxC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACnE,cAAc,GAAG,UAAUkE,QAAQ,EAAEI,UAAU,EAAE;IACpDlF,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIW,QAAQ,KAAK,IAAI,IAAI,CAACI,UAAU,EAAE;MACpC,OAAOlF,KAAK,CAACG,KAAK;IACpB;IACA,IAAIC,aAAa,GAAGJ,KAAK,CAAC6E,+BAA+B,CAACM,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAAC;IACpG,IAAIO,gBAAgB,GAAG,EAAE;IACzBjF,aAAa,CAAC8C,OAAO,CAAC,UAAUC,MAAM,EAAE;MACtC,IAAImC,mBAAmB;MACvB,IAAIvC,QAAQ,GAAG,sBAAsB,IAAII,MAAM,GAAGA,MAAM,CAAC8B,oBAAoB,GAAG9B,MAAM,CAAC1D,WAAW,CAAC,CAAC;MACpG;MACA;MACA,IAAI,CAACqF,QAAQ,KAAK,CAACQ,mBAAmB,GAAGnC,MAAM,CAACoC,WAAW,MAAM,IAAI,IAAID,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACE,IAAI,CAACrC,MAAM,CAAC,CAAC,EAAE;QACpJ;MACF;MACA,IAAI,CAAC+B,UAAU,EAAE;QACfG,gBAAgB,CAAC1B,IAAI,CAACZ,QAAQ,CAAC;MACjC,CAAC,MAAM;QACL,IAAI0C,IAAI,GAAG,SAAS,IAAItC,MAAM,GAAGA,MAAM,CAACuC,OAAO,CAAC,CAAC,GAAG,IAAI;QACxD,IAAIR,UAAU,CAACO,IAAI,CAAC,EAAE;UACpBJ,gBAAgB,CAAC1B,IAAI,CAACZ,QAAQ,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACF,OAAOxD,mBAAmB,CAACS,KAAK,CAACG,KAAK,EAAEkF,gBAAgB,CAACxC,GAAG,CAACpD,WAAW,CAAC,CAAC;EAC5E,CAAC;EACD,IAAI,CAACkB,aAAa,GAAG,UAAUoE,IAAI,EAAE;IACnC/E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIpB,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;IAChC,OAAOrF,QAAQ,CAACM,KAAK,CAACG,KAAK,EAAE4C,QAAQ,CAAC;EACxC,CAAC;EACD,IAAI,CAAChC,cAAc,GAAG,UAAU+D,QAAQ,EAAE;IACxC9E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAI/D,aAAa,GAAGJ,KAAK,CAAC6E,+BAA+B,CAACC,QAAQ,CAAC;IACnE,OAAO1E,aAAa,CAACyC,GAAG,CAAC,UAAUM,MAAM,EAAEwC,KAAK,EAAE;MAChD,IAAIxC,MAAM,IAAI,EAAE,sBAAsB,IAAIA,MAAM,CAAC,EAAE;QACjD,OAAO;UACL4B,IAAI,EAAE5B,MAAM,CAAC1D,WAAW,CAAC,CAAC;UAC1BmG,MAAM,EAAEzC,MAAM,CAAC0C,SAAS,CAAC,CAAC;UAC1BC,QAAQ,EAAE3C,MAAM,CAAC4C,WAAW,CAAC;QAC/B,CAAC;MACH;MACA,OAAO;QACLhB,IAAI,EAAEtF,WAAW,CAACqF,QAAQ,CAACa,KAAK,CAAC,CAAC;QAClCC,MAAM,EAAE,EAAE;QACVE,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACjF,aAAa,GAAG,UAAUkE,IAAI,EAAE;IACnC/E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIpB,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;IAChC,IAAIiB,UAAU,GAAGhG,KAAK,CAACe,cAAc,CAAC,CAACgC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,OAAOiD,UAAU,CAACJ,MAAM;EAC1B,CAAC;EACD,IAAI,CAAC9E,eAAe,GAAG,UAAUiE,IAAI,EAAE;IACrC/E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIpB,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;IAChC,IAAIiB,UAAU,GAAGhG,KAAK,CAACe,cAAc,CAAC,CAACgC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,OAAOiD,UAAU,CAACF,QAAQ;EAC5B,CAAC;EACD,IAAI,CAAC9E,eAAe,GAAG,YAAY;IACjChB,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,KAAK,IAAI8B,IAAI,GAAGlC,SAAS,CAACP,MAAM,EAAE0C,IAAI,GAAG,IAAIf,KAAK,CAACc,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGpC,SAAS,CAACoC,IAAI,CAAC;IAC9B;IACA,IAAIC,IAAI,GAAGF,IAAI,CAAC,CAAC,CAAC;MAChBG,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;IAChB,IAAII,YAAY;IAChB,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIL,IAAI,CAAC1C,MAAM,KAAK,CAAC,EAAE;MACrB8C,YAAY,GAAG,IAAI;IACrB,CAAC,MAAM,IAAIJ,IAAI,CAAC1C,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI2B,KAAK,CAACC,OAAO,CAACgB,IAAI,CAAC,EAAE;QACvBE,YAAY,GAAGF,IAAI,CAACvD,GAAG,CAACpD,WAAW,CAAC;QACpC8G,kBAAkB,GAAG,KAAK;MAC5B,CAAC,MAAM;QACLD,YAAY,GAAG,IAAI;QACnBC,kBAAkB,GAAGH,IAAI;MAC3B;IACF,CAAC,MAAM;MACLE,YAAY,GAAGF,IAAI,CAACvD,GAAG,CAACpD,WAAW,CAAC;MACpC8G,kBAAkB,GAAGF,IAAI;IAC3B;IACA,IAAIjG,aAAa,GAAGJ,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC;IAChD,IAAIhC,cAAc,GAAG,SAASA,cAAcA,CAACyD,KAAK,EAAE;MAClD,OAAOA,KAAK,CAACzD,cAAc,CAAC,CAAC;IAC/B,CAAC;IACD;IACA,IAAI,CAACqF,YAAY,EAAE;MACjB,OAAOC,kBAAkB,GAAGnG,aAAa,CAACoG,KAAK,CAACvF,cAAc,CAAC,GAAGb,aAAa,CAACqG,IAAI,CAACxF,cAAc,CAAC;IACtG;IACA;IACA,IAAI4B,GAAG,GAAG,IAAIvD,OAAO,CAAC,CAAC;IACvBgH,YAAY,CAACpD,OAAO,CAAC,UAAUwD,aAAa,EAAE;MAC5C7D,GAAG,CAACS,GAAG,CAACoD,aAAa,EAAE,EAAE,CAAC;IAC5B,CAAC,CAAC;IACFtG,aAAa,CAAC8C,OAAO,CAAC,UAAUwB,KAAK,EAAE;MACrC,IAAIiC,aAAa,GAAGjC,KAAK,CAACjF,WAAW,CAAC,CAAC;MACvC;MACA6G,YAAY,CAACpD,OAAO,CAAC,UAAUwD,aAAa,EAAE;QAC5C,IAAIA,aAAa,CAACF,KAAK,CAAC,UAAUI,QAAQ,EAAEC,CAAC,EAAE;UAC7C,OAAOF,aAAa,CAACE,CAAC,CAAC,KAAKD,QAAQ;QACtC,CAAC,CAAC,EAAE;UACF/D,GAAG,CAACiE,MAAM,CAACJ,aAAa,EAAE,UAAUK,IAAI,EAAE;YACxC,OAAO,EAAE,CAACC,MAAM,CAACpI,kBAAkB,CAACmI,IAAI,CAAC,EAAE,CAACrC,KAAK,CAAC,CAAC;UACrD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA,IAAIuC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,QAAQ,EAAE;MACnE,OAAOA,QAAQ,CAACT,IAAI,CAACxF,cAAc,CAAC;IACtC,CAAC;IACD,IAAIkG,oBAAoB,GAAGtE,GAAG,CAACA,GAAG,CAAC,UAAUuE,KAAK,EAAE;MAClD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,OAAOd,kBAAkB,GAAGY,oBAAoB,CAACX,KAAK,CAACS,qBAAqB,CAAC,GAAGE,oBAAoB,CAACV,IAAI,CAACQ,qBAAqB,CAAC;EAClI,CAAC;EACD,IAAI,CAAChG,cAAc,GAAG,UAAU8D,IAAI,EAAE;IACpC/E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,OAAOnE,KAAK,CAACgB,eAAe,CAAC,CAAC+D,IAAI,CAAC,CAAC;EACtC,CAAC;EACD,IAAI,CAAC5D,kBAAkB,GAAG,UAAU2D,QAAQ,EAAE;IAC5C9E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAI/D,aAAa,GAAGJ,KAAK,CAACiD,gBAAgB,CAAC,CAAC;IAC5C,IAAI,CAAC6B,QAAQ,EAAE;MACb,OAAO1E,aAAa,CAACqG,IAAI,CAAC,UAAUa,SAAS,EAAE;QAC7C,OAAOA,SAAS,CAACpG,iBAAiB,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;IACA,IAAIoF,YAAY,GAAGxB,QAAQ,CAACjC,GAAG,CAACpD,WAAW,CAAC;IAC5C,OAAOW,aAAa,CAACqG,IAAI,CAAC,UAAUa,SAAS,EAAE;MAC7C,IAAIX,aAAa,GAAGW,SAAS,CAAC7H,WAAW,CAAC,CAAC;MAC3C,OAAOD,gBAAgB,CAAC8G,YAAY,EAAEK,aAAa,CAAC,IAAIW,SAAS,CAACpG,iBAAiB,CAAC,CAAC;IACvF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACA,iBAAiB,GAAG,UAAU6D,IAAI,EAAE;IACvC/E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,OAAOnE,KAAK,CAACmB,kBAAkB,CAAC,CAAC4D,IAAI,CAAC,CAAC;EACzC,CAAC;EACD,IAAI,CAACwC,0BAA0B,GAAG,YAAY;IAC5C,IAAIC,IAAI,GAAGzD,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjF;IACA,IAAIa,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;IACzB,IAAIc,aAAa,GAAGJ,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC;IAChD7C,aAAa,CAAC8C,OAAO,CAAC,UAAUwB,KAAK,EAAE;MACrC,IAAI+C,YAAY,GAAG/C,KAAK,CAACgD,KAAK,CAACD,YAAY;MAC3C,IAAI1E,QAAQ,GAAG2B,KAAK,CAACjF,WAAW,CAAC,CAAC;MAClC;MACA,IAAIgI,YAAY,KAAKzD,SAAS,EAAE;QAC9B,IAAI2D,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAACjC,QAAQ,CAAC,IAAI,IAAI6E,GAAG,CAAC,CAAC;QAC9CD,OAAO,CAACE,GAAG,CAAC;UACV1E,MAAM,EAAEuB,KAAK;UACb2C,KAAK,EAAEI;QACT,CAAC,CAAC;QACF7C,KAAK,CAACtB,GAAG,CAACP,QAAQ,EAAE4E,OAAO,CAAC;MAC9B;IACF,CAAC,CAAC;IACF;IACA,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACZ,QAAQ,EAAE;MACvDA,QAAQ,CAAChE,OAAO,CAAC,UAAUwB,KAAK,EAAE;QAChC,IAAI+C,YAAY,GAAG/C,KAAK,CAACgD,KAAK,CAACD,YAAY;QAC3C,IAAIA,YAAY,KAAKzD,SAAS,EAAE;UAC9B,IAAIjB,QAAQ,GAAG2B,KAAK,CAACjF,WAAW,CAAC,CAAC;UAClC,IAAIsI,gBAAgB,GAAG/H,KAAK,CAACuC,eAAe,CAACQ,QAAQ,CAAC;UACtD,IAAIgF,gBAAgB,KAAK/D,SAAS,EAAE;YAClC;YACAhF,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAACgI,MAAM,CAACjE,QAAQ,CAACiF,IAAI,CAAC,GAAG,CAAC,EAAE,gCAAgC,CAAC,CAAC;UAC7H,CAAC,MAAM;YACL,IAAIL,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAACjC,QAAQ,CAAC;YACjC,IAAI4E,OAAO,IAAIA,OAAO,CAACM,IAAI,GAAG,CAAC,EAAE;cAC/B;cACAjJ,OAAO,CAAC,KAAK,EAAE,4BAA4B,CAACgI,MAAM,CAACjE,QAAQ,CAACiF,IAAI,CAAC,GAAG,CAAC,EAAE,yDAAyD,CAAC,CAAC;YACpI,CAAC,MAAM,IAAIL,OAAO,EAAE;cAClB,IAAIO,WAAW,GAAGlI,KAAK,CAACW,aAAa,CAACoC,QAAQ,CAAC;cAC/C;cACA,IAAI,CAACyE,IAAI,CAACW,SAAS,IAAID,WAAW,KAAKlE,SAAS,EAAE;gBAChDhE,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAACI,KAAK,CAACG,KAAK,EAAE4C,QAAQ,EAAEnE,kBAAkB,CAAC+I,OAAO,CAAC,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC;cAC1F;YACF;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIe,qBAAqB;IACzB,IAAIZ,IAAI,CAACN,QAAQ,EAAE;MACjBkB,qBAAqB,GAAGZ,IAAI,CAACN,QAAQ;IACvC,CAAC,MAAM,IAAIM,IAAI,CAAClB,YAAY,EAAE;MAC5B8B,qBAAqB,GAAG,EAAE;MAC1BZ,IAAI,CAAClB,YAAY,CAACpD,OAAO,CAAC,UAAUH,QAAQ,EAAE;QAC5C,IAAI4E,OAAO,GAAG/C,KAAK,CAACI,GAAG,CAACjC,QAAQ,CAAC;QACjC,IAAI4E,OAAO,EAAE;UACX,IAAIU,qBAAqB;UACzB,CAACA,qBAAqB,GAAGD,qBAAqB,EAAEzE,IAAI,CAAC2E,KAAK,CAACD,qBAAqB,EAAEzJ,kBAAkB,CAACA,kBAAkB,CAAC+I,OAAO,CAAC,CAAC9E,GAAG,CAAC,UAAU0F,CAAC,EAAE;YAChJ,OAAOA,CAAC,CAACpF,MAAM;UACjB,CAAC,CAAC,CAAC,CAAC;QACN;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLiF,qBAAqB,GAAGhI,aAAa;IACvC;IACA0H,eAAe,CAACM,qBAAqB,CAAC;EACxC,CAAC;EACD,IAAI,CAAChH,WAAW,GAAG,UAAU0D,QAAQ,EAAE;IACrC9E,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIqE,SAAS,GAAGxI,KAAK,CAACG,KAAK;IAC3B,IAAI,CAAC2E,QAAQ,EAAE;MACb9E,KAAK,CAACgD,WAAW,CAACnD,SAAS,CAAC,CAAC,CAAC,EAAEG,KAAK,CAACK,aAAa,CAAC,CAAC;MACrDL,KAAK,CAACuH,0BAA0B,CAAC,CAAC;MAClCvH,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAE,IAAI,EAAE;QACrCE,IAAI,EAAE;MACR,CAAC,CAAC;MACF1I,KAAK,CAAC8D,WAAW,CAAC,CAAC;MACnB;IACF;IACA;IACA,IAAIwC,YAAY,GAAGxB,QAAQ,CAACjC,GAAG,CAACpD,WAAW,CAAC;IAC5C6G,YAAY,CAACpD,OAAO,CAAC,UAAUH,QAAQ,EAAE;MACvC,IAAI0E,YAAY,GAAGzH,KAAK,CAACuC,eAAe,CAACQ,QAAQ,CAAC;MAClD/C,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAACI,KAAK,CAACG,KAAK,EAAE4C,QAAQ,EAAE0E,YAAY,CAAC,CAAC;IAClE,CAAC,CAAC;IACFzH,KAAK,CAACuH,0BAA0B,CAAC;MAC/BjB,YAAY,EAAEA;IAChB,CAAC,CAAC;IACFtG,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAElC,YAAY,EAAE;MAC7CoC,IAAI,EAAE;IACR,CAAC,CAAC;IACF1I,KAAK,CAAC8D,WAAW,CAACwC,YAAY,CAAC;EACjC,CAAC;EACD,IAAI,CAACjF,SAAS,GAAG,UAAUsH,MAAM,EAAE;IACjC3I,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIqE,SAAS,GAAGxI,KAAK,CAACG,KAAK;IAC3B,IAAImG,YAAY,GAAG,EAAE;IACrBqC,MAAM,CAACzF,OAAO,CAAC,UAAU0F,SAAS,EAAE;MAClC,IAAI7D,IAAI,GAAG6D,SAAS,CAAC7D,IAAI;QACvBa,MAAM,GAAGgD,SAAS,CAAChD,MAAM;QACzBiD,IAAI,GAAGlK,wBAAwB,CAACiK,SAAS,EAAE7J,SAAS,CAAC;MACvD,IAAIgE,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;MAChCuB,YAAY,CAAC3C,IAAI,CAACZ,QAAQ,CAAC;MAC3B;MACA,IAAI,OAAO,IAAI8F,IAAI,EAAE;QACnB7I,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAACI,KAAK,CAACG,KAAK,EAAE4C,QAAQ,EAAE8F,IAAI,CAACxB,KAAK,CAAC,CAAC;MAChE;MACArH,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAE,CAACzF,QAAQ,CAAC,EAAE;QAC3C2F,IAAI,EAAE,UAAU;QAChBG,IAAI,EAAED;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACF5I,KAAK,CAAC8D,WAAW,CAACwC,YAAY,CAAC;EACjC,CAAC;EACD,IAAI,CAACjE,SAAS,GAAG,YAAY;IAC3B,IAAI6E,QAAQ,GAAGlH,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC;IAC3C,IAAI0F,MAAM,GAAGzB,QAAQ,CAACrE,GAAG,CAAC,UAAU6B,KAAK,EAAE;MACzC,IAAI3B,QAAQ,GAAG2B,KAAK,CAACjF,WAAW,CAAC,CAAC;MAClC,IAAIgG,IAAI,GAAGf,KAAK,CAACgB,OAAO,CAAC,CAAC;MAC1B,IAAIkD,SAAS,GAAGlK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDV,IAAI,EAAEhC,QAAQ;QACdsE,KAAK,EAAErH,KAAK,CAACW,aAAa,CAACoC,QAAQ;MACrC,CAAC,CAAC;MACF+F,MAAM,CAACC,cAAc,CAACH,SAAS,EAAE,eAAe,EAAE;QAChDvB,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAOuB,SAAS;IAClB,CAAC,CAAC;IACF,OAAOD,MAAM;EACf,CAAC;EACD,IAAI,CAAC7G,eAAe,GAAG,UAAUqB,MAAM,EAAE;IACvC,IAAIsE,YAAY,GAAGtE,MAAM,CAACuE,KAAK,CAACD,YAAY;IAC5C,IAAIA,YAAY,KAAKzD,SAAS,EAAE;MAC9B,IAAIjB,QAAQ,GAAGI,MAAM,CAAC1D,WAAW,CAAC,CAAC;MACnC,IAAIuJ,SAAS,GAAGtJ,QAAQ,CAACM,KAAK,CAACG,KAAK,EAAE4C,QAAQ,CAAC;MAC/C,IAAIiG,SAAS,KAAKhF,SAAS,EAAE;QAC3BhE,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAACI,KAAK,CAACG,KAAK,EAAE4C,QAAQ,EAAE0E,YAAY,CAAC,CAAC;MAClE;IACF;EACF,CAAC;EACD,IAAI,CAACrE,gBAAgB,GAAG,UAAU6F,aAAa,EAAE;IAC/C,IAAIC,cAAc,GAAGD,aAAa,KAAKjF,SAAS,GAAGiF,aAAa,GAAGjJ,KAAK,CAACQ,QAAQ;IACjF,OAAO0I,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,IAAI;EACrF,CAAC;EACD,IAAI,CAACnH,aAAa,GAAG,UAAUoB,MAAM,EAAE;IACrCnD,KAAK,CAACI,aAAa,CAACuD,IAAI,CAACR,MAAM,CAAC;IAChC,IAAIJ,QAAQ,GAAGI,MAAM,CAAC1D,WAAW,CAAC,CAAC;IACnCO,KAAK,CAAC8D,WAAW,CAAC,CAACf,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAII,MAAM,CAACuE,KAAK,CAACD,YAAY,KAAKzD,SAAS,EAAE;MAC3C,IAAIwE,SAAS,GAAGxI,KAAK,CAACG,KAAK;MAC3BH,KAAK,CAACuH,0BAA0B,CAAC;QAC/BL,QAAQ,EAAE,CAAC/D,MAAM,CAAC;QAClBgF,SAAS,EAAE;MACb,CAAC,CAAC;MACFnI,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAE,CAACrF,MAAM,CAAC1D,WAAW,CAAC,CAAC,CAAC,EAAE;QACvDiJ,IAAI,EAAE,aAAa;QACnBS,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA;IACA,OAAO,UAAU5D,WAAW,EAAE/E,QAAQ,EAAE;MACtC,IAAI4I,WAAW,GAAGrF,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACxF/D,KAAK,CAACI,aAAa,GAAGJ,KAAK,CAACI,aAAa,CAACwD,MAAM,CAAC,UAAUyF,IAAI,EAAE;QAC/D,OAAOA,IAAI,KAAKlG,MAAM;MACxB,CAAC,CAAC;MACF;MACA,IAAI,CAACnD,KAAK,CAACoD,gBAAgB,CAAC5C,QAAQ,CAAC,KAAK,CAAC+E,WAAW,IAAI6D,WAAW,CAAC5F,MAAM,GAAG,CAAC,CAAC,EAAE;QACjF,IAAI8F,YAAY,GAAG/D,WAAW,GAAGvB,SAAS,GAAGhE,KAAK,CAACuC,eAAe,CAACQ,QAAQ,CAAC;QAC5E,IAAIA,QAAQ,CAACS,MAAM,IAAIxD,KAAK,CAACW,aAAa,CAACoC,QAAQ,CAAC,KAAKuG,YAAY,IAAItJ,KAAK,CAACI,aAAa,CAACoG,KAAK,CAAC,UAAU9B,KAAK,EAAE;UAClH;YACE;YACA,CAAC/E,aAAa,CAAC+E,KAAK,CAACjF,WAAW,CAAC,CAAC,EAAEsD,QAAQ;UAAC;QAEjD,CAAC,CAAC,EAAE;UACF,IAAIwG,UAAU,GAAGvJ,KAAK,CAACG,KAAK;UAC5BH,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAAC2J,UAAU,EAAExG,QAAQ,EAAEuG,YAAY,EAAE,IAAI,CAAC,CAAC;UACrE;UACAtJ,KAAK,CAACyI,eAAe,CAACc,UAAU,EAAE,CAACxG,QAAQ,CAAC,EAAE;YAC5C2F,IAAI,EAAE;UACR,CAAC,CAAC;UACF;UACA1I,KAAK,CAACwJ,yBAAyB,CAACD,UAAU,EAAExG,QAAQ,CAAC;QACvD;MACF;MACA/C,KAAK,CAAC8D,WAAW,CAAC,CAACf,QAAQ,CAAC,CAAC;IAC/B,CAAC;EACH,CAAC;EACD,IAAI,CAAClB,QAAQ,GAAG,UAAU4H,MAAM,EAAE;IAChC,QAAQA,MAAM,CAACf,IAAI;MACjB,KAAK,aAAa;QAChB;UACE,IAAI3F,QAAQ,GAAG0G,MAAM,CAAC1G,QAAQ;YAC5BsE,KAAK,GAAGoC,MAAM,CAACpC,KAAK;UACtBrH,KAAK,CAAC0J,WAAW,CAAC3G,QAAQ,EAAEsE,KAAK,CAAC;UAClC;QACF;MACF,KAAK,eAAe;QAClB;UACE,IAAIsC,SAAS,GAAGF,MAAM,CAAC1G,QAAQ;YAC7B6G,WAAW,GAAGH,MAAM,CAACG,WAAW;UAClC5J,KAAK,CAACwB,cAAc,CAAC,CAACmI,SAAS,CAAC,EAAE;YAChCC,WAAW,EAAEA;UACf,CAAC,CAAC;UACF;QACF;MACF;MACA;IACF;EACF,CAAC;EACD,IAAI,CAACnB,eAAe,GAAG,UAAUD,SAAS,EAAElC,YAAY,EAAEkB,IAAI,EAAE;IAC9D,IAAIxH,KAAK,CAACE,YAAY,EAAE;MACtB,IAAI2J,UAAU,GAAGnL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1DrH,KAAK,EAAEH,KAAK,CAACY,cAAc,CAAC,IAAI;MAClC,CAAC,CAAC;MACFZ,KAAK,CAACiD,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU4G,KAAK,EAAE;QAChD,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;QACvCA,aAAa,CAACvB,SAAS,EAAElC,YAAY,EAAEuD,UAAU,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL7J,KAAK,CAACD,eAAe,CAAC,CAAC;IACzB;EACF,CAAC;EACD,IAAI,CAACyJ,yBAAyB,GAAG,UAAUhB,SAAS,EAAEzF,QAAQ,EAAE;IAC9D,IAAIiH,cAAc,GAAGhK,KAAK,CAACiK,2BAA2B,CAAClH,QAAQ,CAAC;IAChE,IAAIiH,cAAc,CAACxG,MAAM,EAAE;MACzBxD,KAAK,CAACwB,cAAc,CAACwI,cAAc,CAAC;IACtC;IACAhK,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAEwB,cAAc,EAAE;MAC/CtB,IAAI,EAAE,oBAAoB;MAC1BwB,aAAa,EAAE,CAACnH,QAAQ,CAAC,CAACiE,MAAM,CAACpI,kBAAkB,CAACoL,cAAc,CAAC;IACrE,CAAC,CAAC;IACF,OAAOA,cAAc;EACvB,CAAC;EACD,IAAI,CAACN,WAAW,GAAG,UAAU3E,IAAI,EAAEsC,KAAK,EAAE;IACxC,IAAItE,QAAQ,GAAGtD,WAAW,CAACsF,IAAI,CAAC;IAChC,IAAIyD,SAAS,GAAGxI,KAAK,CAACG,KAAK;IAC3BH,KAAK,CAACgD,WAAW,CAACpD,QAAQ,CAACI,KAAK,CAACG,KAAK,EAAE4C,QAAQ,EAAEsE,KAAK,CAAC,CAAC;IACzDrH,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAE,CAACzF,QAAQ,CAAC,EAAE;MAC3C2F,IAAI,EAAE,aAAa;MACnBS,MAAM,EAAE;IACV,CAAC,CAAC;IACFnJ,KAAK,CAAC8D,WAAW,CAAC,CAACf,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAIiH,cAAc,GAAGhK,KAAK,CAACwJ,yBAAyB,CAAChB,SAAS,EAAEzF,QAAQ,CAAC;IACzE;IACA,IAAIoH,cAAc,GAAGnK,KAAK,CAACM,SAAS,CAAC6J,cAAc;IACnD,IAAIA,cAAc,EAAE;MAClB,IAAIC,aAAa,GAAG7K,mBAAmB,CAACS,KAAK,CAACG,KAAK,EAAE,CAAC4C,QAAQ,CAAC,CAAC;MAChEoH,cAAc,CAACC,aAAa,EAAEpK,KAAK,CAACY,cAAc,CAAC,CAAC,CAAC;IACvD;IACAZ,KAAK,CAACqK,qBAAqB,CAAC,CAACtH,QAAQ,CAAC,CAACiE,MAAM,CAACpI,kBAAkB,CAACoL,cAAc,CAAC,CAAC,CAAC;EACpF,CAAC;EACD,IAAI,CAACzI,cAAc,GAAG,UAAUpB,KAAK,EAAE;IACrCH,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAIqE,SAAS,GAAGxI,KAAK,CAACG,KAAK;IAC3B,IAAIA,KAAK,EAAE;MACT,IAAIyC,SAAS,GAAG/C,SAAS,CAACG,KAAK,CAACG,KAAK,EAAEA,KAAK,CAAC;MAC7CH,KAAK,CAACgD,WAAW,CAACJ,SAAS,CAAC;IAC9B;IACA5C,KAAK,CAACyI,eAAe,CAACD,SAAS,EAAE,IAAI,EAAE;MACrCE,IAAI,EAAE,aAAa;MACnBS,MAAM,EAAE;IACV,CAAC,CAAC;IACFnJ,KAAK,CAAC8D,WAAW,CAAC,CAAC;EACrB,CAAC;EACD,IAAI,CAACxC,aAAa,GAAG,UAAUyD,IAAI,EAAEsC,KAAK,EAAE;IAC1CrH,KAAK,CAACqB,SAAS,CAAC,CAAC;MACf0D,IAAI,EAAEA,IAAI;MACVsC,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAI,CAAC4C,2BAA2B,GAAG,UAAUK,YAAY,EAAE;IACzD,IAAIC,QAAQ,GAAG,IAAI3C,GAAG,CAAC,CAAC;IACxB,IAAIoC,cAAc,GAAG,EAAE;IACvB,IAAIQ,mBAAmB,GAAG,IAAIlL,OAAO,CAAC,CAAC;IACvC;AACJ;AACA;AACA;IACIU,KAAK,CAACiD,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUwB,KAAK,EAAE;MAChD,IAAI+F,YAAY,GAAG/F,KAAK,CAACgD,KAAK,CAAC+C,YAAY;MAC3C,CAACA,YAAY,IAAI,EAAE,EAAEvH,OAAO,CAAC,UAAUwH,UAAU,EAAE;QACjD,IAAIC,kBAAkB,GAAGlL,WAAW,CAACiL,UAAU,CAAC;QAChDF,mBAAmB,CAAC1D,MAAM,CAAC6D,kBAAkB,EAAE,YAAY;UACzD,IAAIhC,MAAM,GAAG5E,SAAS,CAACP,MAAM,GAAG,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI6D,GAAG,CAAC,CAAC;UAC1Fe,MAAM,CAACd,GAAG,CAACnD,KAAK,CAAC;UACjB,OAAOiE,MAAM;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIiC,YAAY,GAAG,SAASA,YAAYA,CAAC7H,QAAQ,EAAE;MACjD,IAAI4F,MAAM,GAAG6B,mBAAmB,CAACxF,GAAG,CAACjC,QAAQ,CAAC,IAAI,IAAI6E,GAAG,CAAC,CAAC;MAC3De,MAAM,CAACzF,OAAO,CAAC,UAAUwB,KAAK,EAAE;QAC9B,IAAI,CAAC6F,QAAQ,CAACM,GAAG,CAACnG,KAAK,CAAC,EAAE;UACxB6F,QAAQ,CAAC1C,GAAG,CAACnD,KAAK,CAAC;UACnB,IAAIiC,aAAa,GAAGjC,KAAK,CAACjF,WAAW,CAAC,CAAC;UACvC,IAAIiF,KAAK,CAACoG,YAAY,CAAC,CAAC,IAAInE,aAAa,CAACnD,MAAM,EAAE;YAChDwG,cAAc,CAACrG,IAAI,CAACgD,aAAa,CAAC;YAClCiE,YAAY,CAACjE,aAAa,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACDiE,YAAY,CAACN,YAAY,CAAC;IAC1B,OAAON,cAAc;EACvB,CAAC;EACD,IAAI,CAACK,qBAAqB,GAAG,UAAU/D,YAAY,EAAEyE,WAAW,EAAE;IAChE,IAAIC,cAAc,GAAGhL,KAAK,CAACM,SAAS,CAAC0K,cAAc;IACnD,IAAIA,cAAc,EAAE;MAClB,IAAIrC,MAAM,GAAG3I,KAAK,CAACqC,SAAS,CAAC,CAAC;MAC9B;AACN;AACA;MACM,IAAI0I,WAAW,EAAE;QACf,IAAInG,KAAK,GAAG,IAAItF,OAAO,CAAC,CAAC;QACzByL,WAAW,CAAC7H,OAAO,CAAC,UAAU+H,KAAK,EAAE;UACnC,IAAIlG,IAAI,GAAGkG,KAAK,CAAClG,IAAI;YACnBa,MAAM,GAAGqF,KAAK,CAACrF,MAAM;UACvBhB,KAAK,CAACtB,GAAG,CAACyB,IAAI,EAAEa,MAAM,CAAC;QACzB,CAAC,CAAC;QACF+C,MAAM,CAACzF,OAAO,CAAC,UAAUwB,KAAK,EAAE;UAC9B;UACAA,KAAK,CAACkB,MAAM,GAAGhB,KAAK,CAACI,GAAG,CAACN,KAAK,CAACK,IAAI,CAAC,IAAIL,KAAK,CAACkB,MAAM;QACtD,CAAC,CAAC;MACJ;MACA,IAAIsF,aAAa,GAAGvC,MAAM,CAAC/E,MAAM,CAAC,UAAUuH,KAAK,EAAE;QACjD,IAAIC,SAAS,GAAGD,KAAK,CAACpG,IAAI;QAC1B,OAAOvF,gBAAgB,CAAC8G,YAAY,EAAE8E,SAAS,CAAC;MAClD,CAAC,CAAC;MACFJ,cAAc,CAACE,aAAa,EAAEvC,MAAM,CAAC;IACvC;EACF,CAAC;EACD,IAAI,CAACnH,cAAc,GAAG,UAAUsD,QAAQ,EAAEuG,OAAO,EAAE;IACjDrL,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvB,IAAImH,eAAe,GAAG,CAAC,CAACxG,QAAQ;IAChC,IAAIwB,YAAY,GAAGgF,eAAe,GAAGxG,QAAQ,CAACjC,GAAG,CAACpD,WAAW,CAAC,GAAG,EAAE;IACnE;IACA,IAAI8L,WAAW,GAAG,EAAE;IACpBvL,KAAK,CAACiD,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUwB,KAAK,EAAE;MACpD;MACA,IAAI,CAAC4G,eAAe,EAAE;QACpBhF,YAAY,CAAC3C,IAAI,CAACe,KAAK,CAACjF,WAAW,CAAC,CAAC,CAAC;MACxC;MACA;AACN;AACA;AACA;MACM,IAAI,CAAC4L,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,SAAS,KAAKF,eAAe,EAAE;QAC5F,IAAIvI,QAAQ,GAAG2B,KAAK,CAACjF,WAAW,CAAC,CAAC;QAClC;QACA;QACA;QACAsD,QAAQ,CAACyD,KAAK,CAAC,UAAUI,QAAQ,EAAEC,CAAC,EAAE;UACpC,OAAO/B,QAAQ,CAAC+B,CAAC,CAAC,KAAKD,QAAQ,IAAI9B,QAAQ,CAAC+B,CAAC,CAAC,KAAK7C,SAAS;QAC9D,CAAC,CAAC,EAAE;UACFsC,YAAY,CAAC3C,IAAI,CAACZ,QAAQ,CAAC;QAC7B;MACF;MACA;MACA,IAAI,CAAC2B,KAAK,CAACgD,KAAK,CAAC+D,KAAK,IAAI,CAAC/G,KAAK,CAACgD,KAAK,CAAC+D,KAAK,CAACjI,MAAM,EAAE;QACnD;MACF;MACA,IAAImD,aAAa,GAAGjC,KAAK,CAACjF,WAAW,CAAC,CAAC;MACvC;MACA,IAAI,CAAC6L,eAAe,IAAI9L,gBAAgB,CAAC8G,YAAY,EAAEK,aAAa,CAAC,EAAE;QACrE,IAAI+E,OAAO,GAAGhH,KAAK,CAACiH,aAAa,CAACjN,aAAa,CAAC;UAC9C6B,gBAAgB,EAAE7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEW,uBAAuB,CAAC,EAAEW,KAAK,CAACO,gBAAgB;QACpG,CAAC,EAAE8K,OAAO,CAAC,CAAC;QACZ;QACAE,WAAW,CAAC5H,IAAI,CAAC+H,OAAO,CAACE,IAAI,CAAC,YAAY;UACxC,OAAO;YACL7G,IAAI,EAAE4B,aAAa;YACnBf,MAAM,EAAE,EAAE;YACVE,QAAQ,EAAE;UACZ,CAAC;QACH,CAAC,CAAC,CAAC+F,KAAK,CAAC,UAAUC,UAAU,EAAE;UAC7B,IAAIC,mBAAmB;UACvB,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAIC,cAAc,GAAG,EAAE;UACvB,CAACF,mBAAmB,GAAGD,UAAU,CAAC5I,OAAO,MAAM,IAAI,IAAI6I,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACvG,IAAI,CAACsG,UAAU,EAAE,UAAUI,KAAK,EAAE;YACrJ,IAAIC,WAAW,GAAGD,KAAK,CAACE,IAAI,CAACD,WAAW;cACtCvG,MAAM,GAAGsG,KAAK,CAACtG,MAAM;YACvB,IAAIuG,WAAW,EAAE;cACfF,cAAc,CAACtI,IAAI,CAAC2E,KAAK,CAAC2D,cAAc,EAAErN,kBAAkB,CAACgH,MAAM,CAAC,CAAC;YACvE,CAAC,MAAM;cACLoG,YAAY,CAACrI,IAAI,CAAC2E,KAAK,CAAC0D,YAAY,EAAEpN,kBAAkB,CAACgH,MAAM,CAAC,CAAC;YACnE;UACF,CAAC,CAAC;UACF,IAAIoG,YAAY,CAACxI,MAAM,EAAE;YACvB,OAAO6I,OAAO,CAACC,MAAM,CAAC;cACpBvH,IAAI,EAAE4B,aAAa;cACnBf,MAAM,EAAEoG,YAAY;cACpBlG,QAAQ,EAAEmG;YACZ,CAAC,CAAC;UACJ;UACA,OAAO;YACLlH,IAAI,EAAE4B,aAAa;YACnBf,MAAM,EAAEoG,YAAY;YACpBlG,QAAQ,EAAEmG;UACZ,CAAC;QACH,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;IACF,IAAIM,cAAc,GAAGpN,gBAAgB,CAACoM,WAAW,CAAC;IAClDvL,KAAK,CAACS,mBAAmB,GAAG8L,cAAc;IAC1C;IACAA,cAAc,CAACV,KAAK,CAAC,UAAUW,OAAO,EAAE;MACtC,OAAOA,OAAO;IAChB,CAAC,CAAC,CAACZ,IAAI,CAAC,UAAUY,OAAO,EAAE;MACzB,IAAIC,kBAAkB,GAAGD,OAAO,CAAC3J,GAAG,CAAC,UAAU6J,KAAK,EAAE;QACpD,IAAI3H,IAAI,GAAG2H,KAAK,CAAC3H,IAAI;QACrB,OAAOA,IAAI;MACb,CAAC,CAAC;MACF/E,KAAK,CAACyI,eAAe,CAACzI,KAAK,CAACG,KAAK,EAAEsM,kBAAkB,EAAE;QACrD/D,IAAI,EAAE;MACR,CAAC,CAAC;MACF1I,KAAK,CAACqK,qBAAqB,CAACoC,kBAAkB,EAAED,OAAO,CAAC;IAC1D,CAAC,CAAC;IACF,IAAIG,aAAa,GAAGJ,cAAc,CAACX,IAAI,CAAC,YAAY;MAClD,IAAI5L,KAAK,CAACS,mBAAmB,KAAK8L,cAAc,EAAE;QAChD,OAAOF,OAAO,CAACO,OAAO,CAAC5M,KAAK,CAACY,cAAc,CAAC0F,YAAY,CAAC,CAAC;MAC5D;MACA,OAAO+F,OAAO,CAACC,MAAM,CAAC,EAAE,CAAC;IAC3B,CAAC,CAAC,CAACT,KAAK,CAAC,UAAUW,OAAO,EAAE;MAC1B,IAAIK,SAAS,GAAGL,OAAO,CAAC5I,MAAM,CAAC,UAAUkJ,MAAM,EAAE;QAC/C,OAAOA,MAAM,IAAIA,MAAM,CAAClH,MAAM,CAACpC,MAAM;MACvC,CAAC,CAAC;MACF,OAAO6I,OAAO,CAACC,MAAM,CAAC;QACpBrI,MAAM,EAAEjE,KAAK,CAACY,cAAc,CAAC0F,YAAY,CAAC;QAC1CyG,WAAW,EAAEF,SAAS;QACtBG,SAAS,EAAEhN,KAAK,CAACS,mBAAmB,KAAK8L;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACAI,aAAa,CAACd,KAAK,CAAC,UAAUoB,CAAC,EAAE;MAC/B,OAAOA,CAAC;IACV,CAAC,CAAC;IACF,OAAON,aAAa;EACtB,CAAC;EACD,IAAI,CAAClL,MAAM,GAAG,YAAY;IACxBzB,KAAK,CAACmE,eAAe,CAAC,CAAC;IACvBnE,KAAK,CAACwB,cAAc,CAAC,CAAC,CAACoK,IAAI,CAAC,UAAU3H,MAAM,EAAE;MAC5C,IAAIiJ,QAAQ,GAAGlN,KAAK,CAACM,SAAS,CAAC4M,QAAQ;MACvC,IAAIA,QAAQ,EAAE;QACZ,IAAI;UACFA,QAAQ,CAACjJ,MAAM,CAAC;QAClB,CAAC,CAAC,OAAOkJ,GAAG,EAAE;UACZ;UACAC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;QACpB;MACF;IACF,CAAC,CAAC,CAACtB,KAAK,CAAC,UAAUoB,CAAC,EAAE;MACpB,IAAIK,cAAc,GAAGtN,KAAK,CAACM,SAAS,CAACgN,cAAc;MACnD,IAAIA,cAAc,EAAE;QAClBA,cAAc,CAACL,CAAC,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAAClN,eAAe,GAAGA,eAAe;AACxC,CAAC,CAAC;AACF,SAASwN,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,OAAO,GAAGxO,KAAK,CAACyO,MAAM,CAAC,CAAC;EAC5B,IAAIC,eAAe,GAAG1O,KAAK,CAAC2O,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGpP,cAAc,CAACkP,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAI,CAACJ,OAAO,CAACM,OAAO,EAAE;IACpB,IAAIP,IAAI,EAAE;MACRC,OAAO,CAACM,OAAO,GAAGP,IAAI;IACxB,CAAC,MAAM;MACL;MACA,IAAIQ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;QAC3CF,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;MACD,IAAIG,SAAS,GAAG,IAAInO,SAAS,CAACkO,aAAa,CAAC;MAC5CP,OAAO,CAACM,OAAO,GAAGE,SAAS,CAACvN,OAAO,CAAC,CAAC;IACvC;EACF;EACA,OAAO,CAAC+M,OAAO,CAACM,OAAO,CAAC;AAC1B;AACA,eAAeR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}