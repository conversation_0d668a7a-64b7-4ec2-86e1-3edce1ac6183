{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = bisector;\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\nvar _descending = _interopRequireDefault(require(\"./descending.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction bisector(f) {\n  let compare1, compare2, delta; // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n\n  if (f.length !== 2) {\n    compare1 = _ascending.default;\n    compare2 = (d, x) => (0, _ascending.default)(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === _ascending.default || f === _descending.default ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n  function left(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function right(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function center(a, x) {\n    let lo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    let hi = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction zero() {\n  return 0;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "bisector", "_ascending", "_interopRequireDefault", "require", "_descending", "obj", "__esModule", "f", "compare1", "compare2", "delta", "length", "d", "x", "zero", "left", "a", "lo", "arguments", "undefined", "hi", "mid", "right", "center", "i"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/bisector.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = bisector;\n\nvar _ascending = _interopRequireDefault(require(\"./ascending.js\"));\n\nvar _descending = _interopRequireDefault(require(\"./descending.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction bisector(f) {\n  let compare1, compare2, delta; // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n\n  if (f.length !== 2) {\n    compare1 = _ascending.default;\n\n    compare2 = (d, x) => (0, _ascending.default)(f(d), x);\n\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === _ascending.default || f === _descending.default ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {\n    left,\n    center,\n    right\n  };\n}\n\nfunction zero() {\n  return 0;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,UAAU,GAAGC,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIC,WAAW,GAAGF,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEpE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,QAAQA,CAACO,CAAC,EAAE;EACnB,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,CAAC,CAAC;EAC/B;EACA;EACA;EACA;;EAEA,IAAIH,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IAClBH,QAAQ,GAAGP,UAAU,CAACF,OAAO;IAE7BU,QAAQ,GAAGA,CAACG,CAAC,EAAEC,CAAC,KAAK,CAAC,CAAC,EAAEZ,UAAU,CAACF,OAAO,EAAEQ,CAAC,CAACK,CAAC,CAAC,EAAEC,CAAC,CAAC;IAErDH,KAAK,GAAGA,CAACE,CAAC,EAAEC,CAAC,KAAKN,CAAC,CAACK,CAAC,CAAC,GAAGC,CAAC;EAC5B,CAAC,MAAM;IACLL,QAAQ,GAAGD,CAAC,KAAKN,UAAU,CAACF,OAAO,IAAIQ,CAAC,KAAKH,WAAW,CAACL,OAAO,GAAGQ,CAAC,GAAGO,IAAI;IAC3EL,QAAQ,GAAGF,CAAC;IACZG,KAAK,GAAGH,CAAC;EACX;EAEA,SAASQ,IAAIA,CAACC,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACvC,IAAIM,EAAE,GAAGG,EAAE,EAAE;MACX,IAAIZ,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOO,EAAE;MAEnC,GAAG;QACD,MAAMC,GAAG,GAAGJ,EAAE,GAAGG,EAAE,KAAK,CAAC;QACzB,IAAIX,QAAQ,CAACO,CAAC,CAACK,GAAG,CAAC,EAAER,CAAC,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAGI,GAAG,GAAG,CAAC,CAAC,KAAKD,EAAE,GAAGC,GAAG;MACzD,CAAC,QAAQJ,EAAE,GAAGG,EAAE;IAClB;IAEA,OAAOH,EAAE;EACX;EAEA,SAASK,KAAKA,CAACN,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACxC,IAAIM,EAAE,GAAGG,EAAE,EAAE;MACX,IAAIZ,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOO,EAAE;MAEnC,GAAG;QACD,MAAMC,GAAG,GAAGJ,EAAE,GAAGG,EAAE,KAAK,CAAC;QACzB,IAAIX,QAAQ,CAACO,CAAC,CAACK,GAAG,CAAC,EAAER,CAAC,CAAC,IAAI,CAAC,EAAEI,EAAE,GAAGI,GAAG,GAAG,CAAC,CAAC,KAAKD,EAAE,GAAGC,GAAG;MAC1D,CAAC,QAAQJ,EAAE,GAAGG,EAAE;IAClB;IAEA,OAAOH,EAAE;EACX;EAEA,SAASM,MAAMA,CAACP,CAAC,EAAEH,CAAC,EAAyB;IAAA,IAAvBI,EAAE,GAAAC,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;IAAA,IAAEE,EAAE,GAAAF,SAAA,CAAAP,MAAA,QAAAO,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGF,CAAC,CAACL,MAAM;IACzC,MAAMa,CAAC,GAAGT,IAAI,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEG,EAAE,GAAG,CAAC,CAAC;IAChC,OAAOI,CAAC,GAAGP,EAAE,IAAIP,KAAK,CAACM,CAAC,CAACQ,CAAC,GAAG,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAG,CAACH,KAAK,CAACM,CAAC,CAACQ,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAGW,CAAC,GAAG,CAAC,GAAGA,CAAC;EACnE;EAEA,OAAO;IACLT,IAAI;IACJQ,MAAM;IACND;EACF,CAAC;AACH;AAEA,SAASR,IAAIA,CAAA,EAAG;EACd,OAAO,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script"}