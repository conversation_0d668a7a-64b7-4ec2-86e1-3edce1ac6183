{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './default';\nvar LocaleReceiver = function LocaleReceiver(props) {\n  var _props$componentName = props.componentName,\n    componentName = _props$componentName === void 0 ? 'global' : _props$componentName,\n    defaultLocale = props.defaultLocale,\n    children = props.children;\n  var antLocale = React.useContext(LocaleContext);\n  var getLocale = React.useMemo(function () {\n    var _a;\n    var locale = defaultLocale || defaultLocaleData[componentName];\n    var localeFromContext = (_a = antLocale === null || antLocale === void 0 ? void 0 : antLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return _extends(_extends({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  var getLocaleCode = React.useMemo(function () {\n    var localeCode = antLocale && antLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if (antLocale && antLocale.exist && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [antLocale]);\n  return children(getLocale, getLocaleCode, antLocale);\n};\nexport default LocaleReceiver;\nexport var useLocaleReceiver = function useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(LocaleContext);\n  var getLocale = React.useMemo(function () {\n    var _a;\n    var locale = defaultLocale || defaultLocaleData[componentName];\n    var localeFromContext = (_a = antLocale === null || antLocale === void 0 ? void 0 : antLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return _extends(_extends({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [getLocale];\n};", "map": {"version": 3, "names": ["_extends", "React", "LocaleContext", "defaultLocaleData", "LocaleReceiver", "props", "_props$componentName", "componentName", "defaultLocale", "children", "antLocale", "useContext", "getLocale", "useMemo", "_a", "locale", "localeFromContext", "Function", "getLocaleCode", "localeCode", "exist", "useLocaleReceiver"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/locale-provider/LocaleReceiver.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './default';\nvar LocaleReceiver = function LocaleReceiver(props) {\n  var _props$componentName = props.componentName,\n    componentName = _props$componentName === void 0 ? 'global' : _props$componentName,\n    defaultLocale = props.defaultLocale,\n    children = props.children;\n  var antLocale = React.useContext(LocaleContext);\n  var getLocale = React.useMemo(function () {\n    var _a;\n    var locale = defaultLocale || defaultLocaleData[componentName];\n    var localeFromContext = (_a = antLocale === null || antLocale === void 0 ? void 0 : antLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return _extends(_extends({}, locale instanceof Function ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  var getLocaleCode = React.useMemo(function () {\n    var localeCode = antLocale && antLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if (antLocale && antLocale.exist && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [antLocale]);\n  return children(getLocale, getLocaleCode, antLocale);\n};\nexport default LocaleReceiver;\nexport var useLocaleReceiver = function useLocaleReceiver(componentName, defaultLocale) {\n  var antLocale = React.useContext(LocaleContext);\n  var getLocale = React.useMemo(function () {\n    var _a;\n    var locale = defaultLocale || defaultLocaleData[componentName];\n    var localeFromContext = (_a = antLocale === null || antLocale === void 0 ? void 0 : antLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return _extends(_extends({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, antLocale]);\n  return [getLocale];\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,iBAAiB,MAAM,WAAW;AACzC,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EAClD,IAAIC,oBAAoB,GAAGD,KAAK,CAACE,aAAa;IAC5CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,oBAAoB;IACjFE,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC3B,IAAIC,SAAS,GAAGT,KAAK,CAACU,UAAU,CAACT,aAAa,CAAC;EAC/C,IAAIU,SAAS,GAAGX,KAAK,CAACY,OAAO,CAAC,YAAY;IACxC,IAAIC,EAAE;IACN,IAAIC,MAAM,GAAGP,aAAa,IAAIL,iBAAiB,CAACI,aAAa,CAAC;IAC9D,IAAIS,iBAAiB,GAAG,CAACF,EAAE,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACH,aAAa,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACjJ,OAAOd,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEe,MAAM,YAAYE,QAAQ,GAAGF,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEC,iBAAiB,IAAI,CAAC,CAAC,CAAC;EACxG,CAAC,EAAE,CAACT,aAAa,EAAEC,aAAa,EAAEE,SAAS,CAAC,CAAC;EAC7C,IAAIQ,aAAa,GAAGjB,KAAK,CAACY,OAAO,CAAC,YAAY;IAC5C,IAAIM,UAAU,GAAGT,SAAS,IAAIA,SAAS,CAACK,MAAM;IAC9C;IACA,IAAIL,SAAS,IAAIA,SAAS,CAACU,KAAK,IAAI,CAACD,UAAU,EAAE;MAC/C,OAAOhB,iBAAiB,CAACY,MAAM;IACjC;IACA,OAAOI,UAAU;EACnB,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EACf,OAAOD,QAAQ,CAACG,SAAS,EAAEM,aAAa,EAAER,SAAS,CAAC;AACtD,CAAC;AACD,eAAeN,cAAc;AAC7B,OAAO,IAAIiB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACd,aAAa,EAAEC,aAAa,EAAE;EACtF,IAAIE,SAAS,GAAGT,KAAK,CAACU,UAAU,CAACT,aAAa,CAAC;EAC/C,IAAIU,SAAS,GAAGX,KAAK,CAACY,OAAO,CAAC,YAAY;IACxC,IAAIC,EAAE;IACN,IAAIC,MAAM,GAAGP,aAAa,IAAIL,iBAAiB,CAACI,aAAa,CAAC;IAC9D,IAAIS,iBAAiB,GAAG,CAACF,EAAE,GAAGJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACH,aAAa,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACjJ,OAAOd,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAOe,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM,CAAC,EAAEC,iBAAiB,IAAI,CAAC,CAAC,CAAC;EAC1G,CAAC,EAAE,CAACT,aAAa,EAAEC,aAAa,EAAEE,SAAS,CAAC,CAAC;EAC7C,OAAO,CAACE,SAAS,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}