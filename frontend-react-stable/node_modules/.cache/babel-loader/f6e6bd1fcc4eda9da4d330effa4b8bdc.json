{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { VALUE_SPLIT } from '../utils/commonUtil';\n/** Lazy parse options data into conduct-able info to avoid perf issue in single mode */\nexport default (function (options, fieldNames) {\n  var cacheRef = React.useRef({\n    options: null,\n    info: null\n  });\n  var getEntities = React.useCallback(function () {\n    if (cacheRef.current.options !== options) {\n      cacheRef.current.options = options;\n      cacheRef.current.info = convertDataToEntities(options, {\n        fieldNames: fieldNames,\n        initWrapper: function initWrapper(wrapper) {\n          return _objectSpread(_objectSpread({}, wrapper), {}, {\n            pathKeyEntities: {}\n          });\n        },\n        processEntity: function processEntity(entity, wrapper) {\n          var pathKey = entity.nodes.map(function (node) {\n            return node[fieldNames.value];\n          }).join(VALUE_SPLIT);\n          wrapper.pathKeyEntities[pathKey] = entity;\n          // Overwrite origin key.\n          // this is very hack but we need let conduct logic work with connect path\n          entity.key = pathKey;\n        }\n      });\n    }\n    return cacheRef.current.info.pathKeyEntities;\n  }, [fieldNames, options]);\n  return getEntities;\n});", "map": {"version": 3, "names": ["_objectSpread", "React", "convertDataToEntities", "VALUE_SPLIT", "options", "fieldNames", "cacheRef", "useRef", "info", "getEntities", "useCallback", "current", "initWrapper", "wrapper", "pathKeyEntities", "processEntity", "entity", "path<PERSON><PERSON>", "nodes", "map", "node", "value", "join", "key"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-cascader/es/hooks/useEntities.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { VALUE_SPLIT } from '../utils/commonUtil';\n/** Lazy parse options data into conduct-able info to avoid perf issue in single mode */\nexport default (function (options, fieldNames) {\n  var cacheRef = React.useRef({\n    options: null,\n    info: null\n  });\n  var getEntities = React.useCallback(function () {\n    if (cacheRef.current.options !== options) {\n      cacheRef.current.options = options;\n      cacheRef.current.info = convertDataToEntities(options, {\n        fieldNames: fieldNames,\n        initWrapper: function initWrapper(wrapper) {\n          return _objectSpread(_objectSpread({}, wrapper), {}, {\n            pathKeyEntities: {}\n          });\n        },\n        processEntity: function processEntity(entity, wrapper) {\n          var pathKey = entity.nodes.map(function (node) {\n            return node[fieldNames.value];\n          }).join(VALUE_SPLIT);\n          wrapper.pathKeyEntities[pathKey] = entity;\n          // Overwrite origin key.\n          // this is very hack but we need let conduct logic work with connect path\n          entity.key = pathKey;\n        }\n      });\n    }\n    return cacheRef.current.info.pathKeyEntities;\n  }, [fieldNames, options]);\n  return getEntities;\n});"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,qBAAqB;AACjD;AACA,gBAAgB,UAAUC,OAAO,EAAEC,UAAU,EAAE;EAC7C,IAAIC,QAAQ,GAAGL,KAAK,CAACM,MAAM,CAAC;IAC1BH,OAAO,EAAE,IAAI;IACbI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGR,KAAK,CAACS,WAAW,CAAC,YAAY;IAC9C,IAAIJ,QAAQ,CAACK,OAAO,CAACP,OAAO,KAAKA,OAAO,EAAE;MACxCE,QAAQ,CAACK,OAAO,CAACP,OAAO,GAAGA,OAAO;MAClCE,QAAQ,CAACK,OAAO,CAACH,IAAI,GAAGN,qBAAqB,CAACE,OAAO,EAAE;QACrDC,UAAU,EAAEA,UAAU;QACtBO,WAAW,EAAE,SAASA,WAAWA,CAACC,OAAO,EAAE;UACzC,OAAOb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;YACnDC,eAAe,EAAE,CAAC;UACpB,CAAC,CAAC;QACJ,CAAC;QACDC,aAAa,EAAE,SAASA,aAAaA,CAACC,MAAM,EAAEH,OAAO,EAAE;UACrD,IAAII,OAAO,GAAGD,MAAM,CAACE,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;YAC7C,OAAOA,IAAI,CAACf,UAAU,CAACgB,KAAK,CAAC;UAC/B,CAAC,CAAC,CAACC,IAAI,CAACnB,WAAW,CAAC;UACpBU,OAAO,CAACC,eAAe,CAACG,OAAO,CAAC,GAAGD,MAAM;UACzC;UACA;UACAA,MAAM,CAACO,GAAG,GAAGN,OAAO;QACtB;MACF,CAAC,CAAC;IACJ;IACA,OAAOX,QAAQ,CAACK,OAAO,CAACH,IAAI,CAACM,eAAe;EAC9C,CAAC,EAAE,CAACT,UAAU,EAAED,OAAO,CAAC,CAAC;EACzB,OAAOK,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}