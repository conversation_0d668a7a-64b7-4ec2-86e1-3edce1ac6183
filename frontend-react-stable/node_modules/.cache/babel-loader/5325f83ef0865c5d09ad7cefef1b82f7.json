{"ast": null, "code": "import * as React from 'react';\nimport Header from '../Header';\nimport PanelContext from '../../PanelContext';\nimport { formatValue } from '../../utils/dateUtil';\nfunction TimeHeader(props) {\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    value = props.value,\n    format = props.format;\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  return /*#__PURE__*/React.createElement(Header, {\n    prefixCls: headerPrefixCls\n  }, value ? formatValue(value, {\n    locale: locale,\n    format: format,\n    generateConfig: generateConfig\n  }) : \"\\xA0\");\n}\nexport default TimeHeader;", "map": {"version": 3, "names": ["React", "Header", "PanelContext", "formatValue", "TimeHeader", "props", "_React$useContext", "useContext", "<PERSON><PERSON>ead<PERSON>", "prefixCls", "generateConfig", "locale", "value", "format", "headerPrefixCls", "concat", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/TimePanel/TimeHeader.js"], "sourcesContent": ["import * as React from 'react';\nimport Header from '../Header';\nimport PanelContext from '../../PanelContext';\nimport { formatValue } from '../../utils/dateUtil';\nfunction TimeHeader(props) {\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    value = props.value,\n    format = props.format;\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  return /*#__PURE__*/React.createElement(Header, {\n    prefixCls: headerPrefixCls\n  }, value ? formatValue(value, {\n    locale: locale,\n    format: format,\n    generateConfig: generateConfig\n  }) : \"\\xA0\");\n}\nexport default TimeHeader;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIC,iBAAiB,GAAGN,KAAK,CAACO,UAAU,CAACL,YAAY,CAAC;IACpDM,UAAU,GAAGF,iBAAiB,CAACE,UAAU;EAC3C,IAAIA,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;EACvB,IAAIC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACN,SAAS,EAAE,SAAS,CAAC;EACrD,OAAO,aAAaT,KAAK,CAACgB,aAAa,CAACf,MAAM,EAAE;IAC9CQ,SAAS,EAAEK;EACb,CAAC,EAAEF,KAAK,GAAGT,WAAW,CAACS,KAAK,EAAE;IAC5BD,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA,MAAM;IACdH,cAAc,EAAEA;EAClB,CAAC,CAAC,GAAG,MAAM,CAAC;AACd;AACA,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}