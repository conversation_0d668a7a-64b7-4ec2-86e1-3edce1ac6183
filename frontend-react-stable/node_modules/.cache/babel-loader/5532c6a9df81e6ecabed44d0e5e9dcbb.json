{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { TreeContext } from './contextTypes';\nimport DropIndicator from './DropIndicator';\nimport NodeList, { MotionEntity, MOTION_KEY } from './NodeList';\nimport TreeNode from './TreeNode';\nimport { arrAdd, arrDel, calcDropPosition, calcSelectedKeys, conductExpandParent, getDragChildrenKeys, parseCheckedKeys, posToArr } from './util';\nimport { conductCheck } from './utils/conductUtil';\nimport getEntity from './utils/keyUtil';\nimport { convertDataToEntities, convertNodePropsToEventData, convertTreeToData, fillFieldNames, flattenTreeData, getTreeNodeProps, warningWithoutKey } from './utils/treeUtil';\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n  var _super = _createSuper(Tree);\n  function Tree() {\n    var _this;\n    _classCallCheck(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _this.destroyed = false;\n    _this.delayedDragEnterLogic = void 0;\n    _this.loadingRetryTimes = {};\n    _this.state = {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      dropContainerKey: null,\n      dropLevelOffset: null,\n      dropTargetPos: null,\n      dropAllowed: true,\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    };\n    _this.dragStartMousePosition = null;\n    _this.dragNode = void 0;\n    _this.currentMouseOverDroppableNodeKey = null;\n    _this.listRef = /*#__PURE__*/React.createRef();\n    _this.onNodeDragStart = function (event, node) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = node.props.eventKey;\n      _this.dragNode = node;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    /**\n     * [Legacy] Select handler is smaller than node,\n     * so that this will trigger when drag enter node or select handler.\n     * This is a little tricky if customize css without padding.\n     * Better for use mouse move event to refresh drag state.\n     * But let's just keep it to avoid event trigger logic change.\n     */\n    _this.onNodeDragEnter = function (event, node) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var _node$props = node.props,\n        pos = _node$props.pos,\n        eventKey = _node$props.eventKey;\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize.dragNode;\n      // record the key of node which is latest entered, used in dragleave event.\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!dragNode) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.indexOf(dropTargetKey) !== -1 ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      }\n      // Side effect for delay drag\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (dragNode.props.eventKey !== node.props.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) return;\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n          var entity = getEntity(keyEntities, node.props.eventKey);\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, node.props.eventKey);\n          }\n          if (!('expandedKeys' in _this.props)) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 ? void 0 : onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(node.props),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      }\n      // Skip if drag node is self\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      }\n      // Update drag over node and drag state\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 ? void 0 : onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(node.props),\n        expandedKeys: expandedKeys\n      });\n    };\n    _this.onNodeDragOver = function (event, node) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      var _assertThisInitialize2 = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize2.dragNode;\n      if (!dragNode) {\n        return;\n      }\n      var _calcDropPosition2 = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed caculated by calcDropPosition\n        return;\n      }\n      // Update drag position\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 ? void 0 : onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    _this.onNodeDragLeave = function (event, node) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === node.props.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 ? void 0 : onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    // since stopPropagation() is called in treeNode\n    // if onWindowDrag is called, whice means state is keeped, drag state should be cleared\n    _this.onWindowDragEnd = function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n    // if onNodeDragEnd is called, onWindowDragEnd won't be called since stopPropagation() is called\n    _this.onNodeDragEnd = function (event, node) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n      _this.dragNode = null;\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n    _this.onNodeDrop = function (event, node) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) return;\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.key) === dropTargetKey,\n        data: getEntity(_this.state.keyEntities, dropTargetKey).node\n      });\n      var dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNode ? convertNodePropsToEventData(_this.dragNode.props) : null,\n        dragNodesKeys: [_this.dragNode.props.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 ? void 0 : onDrop(dropResult);\n      }\n      _this.dragNode = null;\n    };\n    _this.cleanDragState = function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    };\n    _this.triggerExpandActionExpand = function (e, treeNode) {\n      var _this$state5 = _this.state,\n        expandedKeys = _this$state5.expandedKeys,\n        flattenNodes = _this$state5.flattenNodes;\n      var expanded = treeNode.expanded,\n        key = treeNode.key,\n        isLeaf = treeNode.isLeaf;\n      if (isLeaf || e.shiftKey || e.metaKey || e.ctrlKey) {\n        return;\n      }\n      var node = flattenNodes.filter(function (nodeItem) {\n        return nodeItem.key === key;\n      })[0];\n      var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(key, _this.getTreeNodeRequiredProps())), {}, {\n        data: node.data\n      }));\n      _this.setExpandedKeys(expanded ? arrDel(expandedKeys, key) : arrAdd(expandedKeys, key));\n      _this.onNodeExpand(e, eventNode);\n    };\n    _this.onNodeClick = function (e, treeNode) {\n      var _this$props3 = _this.props,\n        onClick = _this$props3.onClick,\n        expandAction = _this$props3.expandAction;\n      if (expandAction === 'click') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, treeNode);\n    };\n    _this.onNodeDoubleClick = function (e, treeNode) {\n      var _this$props4 = _this.props,\n        onDoubleClick = _this$props4.onDoubleClick,\n        expandAction = _this$props4.expandAction;\n      if (expandAction === 'doubleClick') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onDoubleClick === null || onDoubleClick === void 0 ? void 0 : onDoubleClick(e, treeNode);\n    };\n    _this.onNodeSelect = function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        fieldNames = _this$state6.fieldNames;\n      var _this$props5 = _this.props,\n        onSelect = _this$props5.onSelect,\n        multiple = _this$props5.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected;\n      // Update selected keys\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      }\n      // [Legacy] Not found related usage in doc or upper libs\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = getEntity(keyEntities, selectedKey);\n        if (!entity) return null;\n        return entity.node;\n      }).filter(function (node) {\n        return node;\n      });\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    };\n    _this.onNodeCheck = function (e, treeNode, checked) {\n      var _this$state7 = _this.state,\n        keyEntities = _this$state7.keyEntities,\n        oriCheckedKeys = _this$state7.checkedKeys,\n        oriHalfCheckedKeys = _this$state7.halfCheckedKeys;\n      var _this$props6 = _this.props,\n        checkStrictly = _this$props6.checkStrictly,\n        onCheck = _this$props6.onCheck;\n      var key = treeNode.key;\n      // Prepare trigger arguments\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return getEntity(keyEntities, checkedKey);\n        }).filter(function (entity) {\n          return entity;\n        }).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys;\n        // If remove, we do it again to correction\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys;\n        // [Legacy] This is used for `rc-tree-select`\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = getEntity(keyEntities, checkedKey);\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 ? void 0 : onCheck(checkedObj, eventObj);\n    };\n    _this.onNodeLoad = function (treeNode) {\n      var key = treeNode.key;\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props7 = _this.props,\n            loadData = _this$props7.loadData,\n            onLoad = _this$props7.onLoad;\n          if (!loadData || loadedKeys.indexOf(key) !== -1 || loadingKeys.indexOf(key) !== -1) {\n            return null;\n          }\n          // Process load data\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key);\n            // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n            onLoad === null || onLoad === void 0 ? void 0 : onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            // If exceed max retry times, we give up retry\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      });\n      // Not care warning if we ignore this\n      loadPromise.catch(function () {});\n      return loadPromise;\n    };\n    _this.onNodeMouseEnter = function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeMouseLeave = function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeContextMenu = function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    };\n    _this.onFocus = function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus.apply(void 0, args);\n    };\n    _this.onBlur = function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur.apply(void 0, args);\n    };\n    _this.getTreeNodeRequiredProps = function () {\n      var _this$state8 = _this.state,\n        expandedKeys = _this$state8.expandedKeys,\n        selectedKeys = _this$state8.selectedKeys,\n        loadedKeys = _this$state8.loadedKeys,\n        loadingKeys = _this$state8.loadingKeys,\n        checkedKeys = _this$state8.checkedKeys,\n        halfCheckedKeys = _this$state8.halfCheckedKeys,\n        dragOverNodeKey = _this$state8.dragOverNodeKey,\n        dropPosition = _this$state8.dropPosition,\n        keyEntities = _this$state8.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    };\n    // =========================== Expanded ===========================\n    /** Set uncontrolled `expandedKeys`. This will also auto update `flattenNodes`. */\n    _this.setExpandedKeys = function (expandedKeys) {\n      var _this$state9 = _this.state,\n        treeData = _this$state9.treeData,\n        fieldNames = _this$state9.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    };\n    _this.onNodeExpand = function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state10 = _this.state,\n        listChanging = _this$state10.listChanging,\n        fieldNames = _this$state10.fieldNames;\n      var _this$props8 = _this.props,\n        onExpand = _this$props8.onExpand,\n        loadData = _this$props8.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key];\n      // Do nothing when motion is in progress\n      if (listChanging) {\n        return;\n      }\n      // Update selected keys\n      var index = expandedKeys.indexOf(key);\n      var targetExpanded = !expanded;\n      warning(expanded && index !== -1 || !expanded && index === -1, 'Expand state not sync with index check');\n      if (targetExpanded) {\n        expandedKeys = arrAdd(expandedKeys, key);\n      } else {\n        expandedKeys = arrDel(expandedKeys, key);\n      }\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 ? void 0 : onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      });\n      // Async Load data\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    };\n    _this.onListChangeStart = function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    };\n    _this.onListChangeEnd = function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    };\n    // =========================== Keyboard ===========================\n    _this.onActiveChange = function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var onActiveChange = _this.props.onActiveChange;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 ? void 0 : onActiveChange(newActiveKey);\n    };\n    _this.getActiveItem = function () {\n      var _this$state11 = _this.state,\n        activeKey = _this$state11.activeKey,\n        flattenNodes = _this$state11.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    };\n    _this.offsetActiveKey = function (offset) {\n      var _this$state12 = _this.state,\n        flattenNodes = _this$state12.flattenNodes,\n        activeKey = _this$state12.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      });\n      // Align with index\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var key = item.key;\n        _this.onActiveChange(key);\n      } else {\n        _this.onActiveChange(null);\n      }\n    };\n    _this.onKeyDown = function (event) {\n      var _this$state13 = _this.state,\n        activeKey = _this$state13.activeKey,\n        expandedKeys = _this$state13.expandedKeys,\n        checkedKeys = _this$state13.checkedKeys,\n        fieldNames = _this$state13.fieldNames;\n      var _this$props9 = _this.props,\n        onKeyDown = _this$props9.onKeyDown,\n        checkable = _this$props9.checkable,\n        selectable = _this$props9.selectable;\n      // >>>>>>>>>> Direction\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      }\n      // >>>>>>>>>> Expand & Selection\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data[fieldNames.children] || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].key);\n              }\n              event.preventDefault();\n              break;\n            }\n          // Selection\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event);\n    };\n    /**\n     * Only update the value which is not in props\n     */\n    _this.setUncontrolledState = function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (name in _this.props) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    };\n    _this.scrollTo = function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    };\n    return _this;\n  }\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var activeKey = this.props.activeKey;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n      var _this$state14 = this.state,\n        focused = _this$state14.focused,\n        flattenNodes = _this$state14.flattenNodes,\n        keyEntities = _this$state14.keyEntities,\n        draggingNodeKey = _this$state14.draggingNodeKey,\n        activeKey = _this$state14.activeKey,\n        dropLevelOffset = _this$state14.dropLevelOffset,\n        dropContainerKey = _this$state14.dropContainerKey,\n        dropTargetKey = _this$state14.dropTargetKey,\n        dropPosition = _this$state14.dropPosition,\n        dragOverNodeKey = _this$state14.dragOverNodeKey,\n        indent = _this$state14.indent;\n      var _this$props10 = this.props,\n        prefixCls = _this$props10.prefixCls,\n        className = _this$props10.className,\n        style = _this$props10.style,\n        showLine = _this$props10.showLine,\n        focusable = _this$props10.focusable,\n        _this$props10$tabInde = _this$props10.tabIndex,\n        tabIndex = _this$props10$tabInde === void 0 ? 0 : _this$props10$tabInde,\n        selectable = _this$props10.selectable,\n        showIcon = _this$props10.showIcon,\n        icon = _this$props10.icon,\n        switcherIcon = _this$props10.switcherIcon,\n        draggable = _this$props10.draggable,\n        checkable = _this$props10.checkable,\n        checkStrictly = _this$props10.checkStrictly,\n        disabled = _this$props10.disabled,\n        motion = _this$props10.motion,\n        loadData = _this$props10.loadData,\n        filterTreeNode = _this$props10.filterTreeNode,\n        height = _this$props10.height,\n        itemHeight = _this$props10.itemHeight,\n        virtual = _this$props10.virtual,\n        titleRender = _this$props10.titleRender,\n        dropIndicatorRender = _this$props10.dropIndicatorRender,\n        onContextMenu = _this$props10.onContextMenu,\n        onScroll = _this$props10.onScroll,\n        direction = _this$props10.direction,\n        rootClassName = _this$props10.rootClassName,\n        rootStyle = _this$props10.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n      // It's better move to hooks but we just simply keep here\n      var draggableConfig;\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: {\n          prefixCls: prefixCls,\n          selectable: selectable,\n          showIcon: showIcon,\n          icon: icon,\n          switcherIcon: switcherIcon,\n          draggable: draggableConfig,\n          draggingNodeKey: draggingNodeKey,\n          checkable: checkable,\n          checkStrictly: checkStrictly,\n          disabled: disabled,\n          keyEntities: keyEntities,\n          dropLevelOffset: dropLevelOffset,\n          dropContainerKey: dropContainerKey,\n          dropTargetKey: dropTargetKey,\n          dropPosition: dropPosition,\n          dragOverNodeKey: dragOverNodeKey,\n          indent: indent,\n          direction: direction,\n          dropIndicatorRender: dropIndicatorRender,\n          loadData: loadData,\n          filterTreeNode: filterTreeNode,\n          titleRender: titleRender,\n          onNodeClick: this.onNodeClick,\n          onNodeDoubleClick: this.onNodeDoubleClick,\n          onNodeExpand: this.onNodeExpand,\n          onNodeSelect: this.onNodeSelect,\n          onNodeCheck: this.onNodeCheck,\n          onNodeLoad: this.onNodeLoad,\n          onNodeMouseEnter: this.onNodeMouseEnter,\n          onNodeMouseLeave: this.onNodeMouseLeave,\n          onNodeContextMenu: this.onNodeContextMenu,\n          onNodeDragStart: this.onNodeDragStart,\n          onNodeDragEnter: this.onNodeDragEnter,\n          onNodeDragOver: this.onNodeDragOver,\n          onNodeDragLeave: this.onNodeDragLeave,\n          onNodeDragEnd: this.onNodeDragEnd,\n          onNodeDrop: this.onNodeDrop\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        role: \"tree\",\n        className: classNames(prefixCls, className, rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-show-line\"), showLine), _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focused), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null), _classNames)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && name in props || prevProps && prevProps[name] !== props[name];\n      }\n      // ================== Tree Node ==================\n      var treeData;\n      // fieldNames\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      }\n      // Check if `treeData` or `children` changed and save into the state.\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      }\n      // Save flatten nodes info and convert `treeData` into keyEntities\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities);\n        // Warning if treeNode not provide key\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities;\n      // ================ expandedKeys =================\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n        delete cloneKeyEntities[MOTION_KEY];\n        newState.expandedKeys = Object.keys(cloneKeyEntities).map(function (key) {\n          return cloneKeyEntities[key].key;\n        });\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      }\n      // ================ flattenNodes =================\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      }\n      // ================ selectedKeys =================\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      }\n      // ================= checkedKeys =================\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      }\n      // ================= loadedKeys ==================\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(React.Component);\nTree.defaultProps = {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  },\n  expandAction: false\n};\nTree.TreeNode = TreeNode;\nexport default Tree;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_typeof", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "classNames", "KeyCode", "pickAttrs", "warning", "React", "TreeContext", "DropIndicator", "NodeList", "MotionEntity", "MOTION_KEY", "TreeNode", "arrAdd", "arr<PERSON><PERSON>", "calcDropPosition", "calcSelectedKeys", "conductExpandParent", "getDrag<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseCheckedKeys", "posToArr", "conduct<PERSON>heck", "getEntity", "convertDataToEntities", "convertNodePropsToEventData", "convertTreeToData", "fillFieldNames", "flattenTreeData", "getTreeNodeProps", "warningWithoutKey", "MAX_RETRY_TIMES", "Tree", "_React$Component", "_super", "_this", "_len", "arguments", "length", "_args", "Array", "_key", "call", "apply", "concat", "destroyed", "delayedDragEnterLogic", "loadingRetryTimes", "state", "keyEntities", "indent", "<PERSON><PERSON><PERSON><PERSON>", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "loadedKeys", "loadingKeys", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropTargetKey", "dropPosition", "dropContainerKey", "dropLevelOffset", "dropTargetPos", "dropAllowed", "dragOverNodeKey", "treeData", "flattenNodes", "focused", "active<PERSON><PERSON>", "listChanging", "prevProps", "fieldNames", "dragStartMousePosition", "dragNode", "currentMouseOverDroppableNodeKey", "listRef", "createRef", "onNodeDragStart", "event", "node", "_this$state", "onDragStart", "props", "eventKey", "x", "clientX", "y", "clientY", "newExpandedKeys", "setState", "current", "getIndentWidth", "setExpandedKeys", "window", "addEventListener", "onWindowDragEnd", "onNodeDragEnter", "_this$state2", "_this$props", "onDragEnter", "onExpand", "allowDrop", "direction", "_node$props", "pos", "_assertThisInitialize", "resetDragState", "_calcDropPosition", "indexOf", "Object", "keys", "for<PERSON>ach", "key", "clearTimeout", "persist", "setTimeout", "entity", "children", "expanded", "nativeEvent", "onNodeDragOver", "_this$state3", "_this$props2", "onDragOver", "_assertThisInitialize2", "_calcDropPosition2", "onNodeDragLeave", "currentTarget", "contains", "relatedTarget", "onDragLeave", "onNodeDragEnd", "removeEventListener", "onDragEnd", "cleanDragState", "onNodeDrop", "_this$getActiveItem", "outsideTree", "undefined", "_this$state4", "onDrop", "abstractDropNodeProps", "getTreeNodeRequiredProps", "active", "getActiveItem", "data", "drop<PERSON>oChild", "posArr", "dropResult", "dragNodesKeys", "dropToGap", "Number", "triggerExpandActionExpand", "e", "treeNode", "_this$state5", "<PERSON><PERSON><PERSON><PERSON>", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "filter", "nodeItem", "eventNode", "onNodeExpand", "onNodeClick", "_this$props3", "onClick", "expandAction", "onNodeDoubleClick", "_this$props4", "onDoubleClick", "onNodeSelect", "_this$state6", "_this$props5", "onSelect", "multiple", "selected", "targetSelected", "selectedNodes", "map", "<PERSON><PERSON><PERSON>", "setUncontrolledState", "onNodeCheck", "checked", "_this$state7", "oriCheckedKeys", "oriHalfCheckedKeys", "_this$props6", "checkStrictly", "onCheck", "checked<PERSON>bj", "eventObj", "halfChecked", "checkedNodes", "<PERSON><PERSON><PERSON>", "_conductCheck", "_checked<PERSON><PERSON><PERSON>", "_half<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keySet", "Set", "delete", "_conductCheck2", "from", "checkedNodesPositions", "push", "onNodeLoad", "loadPromise", "Promise", "resolve", "reject", "_ref", "_ref$loadedKeys", "_ref$loadingKeys", "_this$props7", "loadData", "onLoad", "promise", "then", "currentLoaded<PERSON><PERSON><PERSON>", "new<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prevState", "catch", "onNodeMouseEnter", "onMouseEnter", "onNodeMouseLeave", "onMouseLeave", "onNodeContextMenu", "onRightClick", "preventDefault", "onFocus", "_len2", "args", "_key2", "onBlur", "onActiveChange", "_len3", "_key3", "_this$state8", "_this$state9", "_this$state10", "_this$props8", "index", "targetExpanded", "newFlattenTreeData", "currentExpandedKeys", "expandedKeysToRestore", "onListChangeStart", "onListChangeEnd", "newActiveKey", "scrollTo", "_this$state11", "find", "_ref2", "offsetActiveKey", "offset", "_this$state12", "findIndex", "_ref3", "item", "onKeyDown", "_this$state13", "_this$props9", "checkable", "selectable", "which", "UP", "DOWN", "activeItem", "treeNodeRequiredProps", "expandable", "LEFT", "includes", "parent", "RIGHT", "ENTER", "SPACE", "disabled", "disableCheckbox", "atomic", "forceState", "needSync", "allPassed", "newState", "name", "scroll", "value", "componentDidMount", "onUpdated", "componentDidUpdate", "componentWillUnmount", "render", "_classNames", "_this$state14", "_this$props10", "prefixCls", "className", "style", "showLine", "focusable", "_this$props10$tabInde", "tabIndex", "showIcon", "icon", "switcherIcon", "draggable", "motion", "filterTreeNode", "height", "itemHeight", "virtual", "titleRender", "dropIndicatorRender", "onContextMenu", "onScroll", "rootClassName", "rootStyle", "domProps", "aria", "draggableConfig", "nodeDraggable", "createElement", "Provider", "role", "ref", "dragging", "getDerivedStateFromProps", "entitiesMap", "process", "env", "NODE_ENV", "autoExpandParent", "defaultExpandParent", "defaultExpandAll", "cloneKeyEntities", "defaultExpandedKeys", "defaultSelectedKeys", "checkedKeyEntity", "defaultCheckedKeys", "_checkedKeyEntity", "_checkedKeyEntity$che", "_checkedKeyEntity$hal", "conductKeys", "Component", "defaultProps"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tree/es/Tree.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { TreeContext } from './contextTypes';\nimport DropIndicator from './DropIndicator';\nimport NodeList, { MotionEntity, MOTION_KEY } from './NodeList';\nimport TreeNode from './TreeNode';\nimport { arrAdd, arrDel, calcDropPosition, calcSelectedKeys, conductExpandParent, getDragChildrenKeys, parseCheckedKeys, posToArr } from './util';\nimport { conductCheck } from './utils/conductUtil';\nimport getEntity from './utils/keyUtil';\nimport { convertDataToEntities, convertNodePropsToEventData, convertTreeToData, fillFieldNames, flattenTreeData, getTreeNodeProps, warningWithoutKey } from './utils/treeUtil';\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n  var _super = _createSuper(Tree);\n  function Tree() {\n    var _this;\n    _classCallCheck(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _this.destroyed = false;\n    _this.delayedDragEnterLogic = void 0;\n    _this.loadingRetryTimes = {};\n    _this.state = {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      dropContainerKey: null,\n      dropLevelOffset: null,\n      dropTargetPos: null,\n      dropAllowed: true,\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    };\n    _this.dragStartMousePosition = null;\n    _this.dragNode = void 0;\n    _this.currentMouseOverDroppableNodeKey = null;\n    _this.listRef = /*#__PURE__*/React.createRef();\n    _this.onNodeDragStart = function (event, node) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = node.props.eventKey;\n      _this.dragNode = node;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 ? void 0 : onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    /**\n     * [Legacy] Select handler is smaller than node,\n     * so that this will trigger when drag enter node or select handler.\n     * This is a little tricky if customize css without padding.\n     * Better for use mouse move event to refresh drag state.\n     * But let's just keep it to avoid event trigger logic change.\n     */\n    _this.onNodeDragEnter = function (event, node) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var _node$props = node.props,\n        pos = _node$props.pos,\n        eventKey = _node$props.eventKey;\n      var _assertThisInitialize = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize.dragNode;\n      // record the key of node which is latest entered, used in dragleave event.\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!dragNode) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.indexOf(dropTargetKey) !== -1 ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      }\n      // Side effect for delay drag\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (dragNode.props.eventKey !== node.props.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) return;\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n          var entity = getEntity(keyEntities, node.props.eventKey);\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, node.props.eventKey);\n          }\n          if (!('expandedKeys' in _this.props)) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 ? void 0 : onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(node.props),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      }\n      // Skip if drag node is self\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      }\n      // Update drag over node and drag state\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 ? void 0 : onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(node.props),\n        expandedKeys: expandedKeys\n      });\n    };\n    _this.onNodeDragOver = function (event, node) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      var _assertThisInitialize2 = _assertThisInitialized(_this),\n        dragNode = _assertThisInitialize2.dragNode;\n      if (!dragNode) {\n        return;\n      }\n      var _calcDropPosition2 = calcDropPosition(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed caculated by calcDropPosition\n        return;\n      }\n      // Update drag position\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 ? void 0 : onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    _this.onNodeDragLeave = function (event, node) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === node.props.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 ? void 0 : onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n    };\n    // since stopPropagation() is called in treeNode\n    // if onWindowDrag is called, whice means state is keeped, drag state should be cleared\n    _this.onWindowDragEnd = function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n    // if onNodeDragEnd is called, onWindowDragEnd won't be called since stopPropagation() is called\n    _this.onNodeDragEnd = function (event, node) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 ? void 0 : onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(node.props)\n      });\n      _this.dragNode = null;\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    };\n    _this.onNodeDrop = function (event, node) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) return;\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.key) === dropTargetKey,\n        data: getEntity(_this.state.keyEntities, dropTargetKey).node\n      });\n      var dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNode ? convertNodePropsToEventData(_this.dragNode.props) : null,\n        dragNodesKeys: [_this.dragNode.props.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 ? void 0 : onDrop(dropResult);\n      }\n      _this.dragNode = null;\n    };\n    _this.cleanDragState = function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    };\n    _this.triggerExpandActionExpand = function (e, treeNode) {\n      var _this$state5 = _this.state,\n        expandedKeys = _this$state5.expandedKeys,\n        flattenNodes = _this$state5.flattenNodes;\n      var expanded = treeNode.expanded,\n        key = treeNode.key,\n        isLeaf = treeNode.isLeaf;\n      if (isLeaf || e.shiftKey || e.metaKey || e.ctrlKey) {\n        return;\n      }\n      var node = flattenNodes.filter(function (nodeItem) {\n        return nodeItem.key === key;\n      })[0];\n      var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(key, _this.getTreeNodeRequiredProps())), {}, {\n        data: node.data\n      }));\n      _this.setExpandedKeys(expanded ? arrDel(expandedKeys, key) : arrAdd(expandedKeys, key));\n      _this.onNodeExpand(e, eventNode);\n    };\n    _this.onNodeClick = function (e, treeNode) {\n      var _this$props3 = _this.props,\n        onClick = _this$props3.onClick,\n        expandAction = _this$props3.expandAction;\n      if (expandAction === 'click') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onClick === null || onClick === void 0 ? void 0 : onClick(e, treeNode);\n    };\n    _this.onNodeDoubleClick = function (e, treeNode) {\n      var _this$props4 = _this.props,\n        onDoubleClick = _this$props4.onDoubleClick,\n        expandAction = _this$props4.expandAction;\n      if (expandAction === 'doubleClick') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onDoubleClick === null || onDoubleClick === void 0 ? void 0 : onDoubleClick(e, treeNode);\n    };\n    _this.onNodeSelect = function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        fieldNames = _this$state6.fieldNames;\n      var _this$props5 = _this.props,\n        onSelect = _this$props5.onSelect,\n        multiple = _this$props5.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected;\n      // Update selected keys\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      }\n      // [Legacy] Not found related usage in doc or upper libs\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = getEntity(keyEntities, selectedKey);\n        if (!entity) return null;\n        return entity.node;\n      }).filter(function (node) {\n        return node;\n      });\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 ? void 0 : onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    };\n    _this.onNodeCheck = function (e, treeNode, checked) {\n      var _this$state7 = _this.state,\n        keyEntities = _this$state7.keyEntities,\n        oriCheckedKeys = _this$state7.checkedKeys,\n        oriHalfCheckedKeys = _this$state7.halfCheckedKeys;\n      var _this$props6 = _this.props,\n        checkStrictly = _this$props6.checkStrictly,\n        onCheck = _this$props6.onCheck;\n      var key = treeNode.key;\n      // Prepare trigger arguments\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return getEntity(keyEntities, checkedKey);\n        }).filter(function (entity) {\n          return entity;\n        }).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys;\n        // If remove, we do it again to correction\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys;\n        // [Legacy] This is used for `rc-tree-select`\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = getEntity(keyEntities, checkedKey);\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 ? void 0 : onCheck(checkedObj, eventObj);\n    };\n    _this.onNodeLoad = function (treeNode) {\n      var key = treeNode.key;\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props7 = _this.props,\n            loadData = _this$props7.loadData,\n            onLoad = _this$props7.onLoad;\n          if (!loadData || loadedKeys.indexOf(key) !== -1 || loadingKeys.indexOf(key) !== -1) {\n            return null;\n          }\n          // Process load data\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key);\n            // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n            onLoad === null || onLoad === void 0 ? void 0 : onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            // If exceed max retry times, we give up retry\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      });\n      // Not care warning if we ignore this\n      loadPromise.catch(function () {});\n      return loadPromise;\n    };\n    _this.onNodeMouseEnter = function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeMouseLeave = function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave({\n        event: event,\n        node: node\n      });\n    };\n    _this.onNodeContextMenu = function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    };\n    _this.onFocus = function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus.apply(void 0, args);\n    };\n    _this.onBlur = function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur.apply(void 0, args);\n    };\n    _this.getTreeNodeRequiredProps = function () {\n      var _this$state8 = _this.state,\n        expandedKeys = _this$state8.expandedKeys,\n        selectedKeys = _this$state8.selectedKeys,\n        loadedKeys = _this$state8.loadedKeys,\n        loadingKeys = _this$state8.loadingKeys,\n        checkedKeys = _this$state8.checkedKeys,\n        halfCheckedKeys = _this$state8.halfCheckedKeys,\n        dragOverNodeKey = _this$state8.dragOverNodeKey,\n        dropPosition = _this$state8.dropPosition,\n        keyEntities = _this$state8.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    };\n    // =========================== Expanded ===========================\n    /** Set uncontrolled `expandedKeys`. This will also auto update `flattenNodes`. */\n    _this.setExpandedKeys = function (expandedKeys) {\n      var _this$state9 = _this.state,\n        treeData = _this$state9.treeData,\n        fieldNames = _this$state9.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    };\n    _this.onNodeExpand = function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state10 = _this.state,\n        listChanging = _this$state10.listChanging,\n        fieldNames = _this$state10.fieldNames;\n      var _this$props8 = _this.props,\n        onExpand = _this$props8.onExpand,\n        loadData = _this$props8.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key];\n      // Do nothing when motion is in progress\n      if (listChanging) {\n        return;\n      }\n      // Update selected keys\n      var index = expandedKeys.indexOf(key);\n      var targetExpanded = !expanded;\n      warning(expanded && index !== -1 || !expanded && index === -1, 'Expand state not sync with index check');\n      if (targetExpanded) {\n        expandedKeys = arrAdd(expandedKeys, key);\n      } else {\n        expandedKeys = arrDel(expandedKeys, key);\n      }\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 ? void 0 : onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      });\n      // Async Load data\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    };\n    _this.onListChangeStart = function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    };\n    _this.onListChangeEnd = function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    };\n    // =========================== Keyboard ===========================\n    _this.onActiveChange = function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var onActiveChange = _this.props.onActiveChange;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 ? void 0 : onActiveChange(newActiveKey);\n    };\n    _this.getActiveItem = function () {\n      var _this$state11 = _this.state,\n        activeKey = _this$state11.activeKey,\n        flattenNodes = _this$state11.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    };\n    _this.offsetActiveKey = function (offset) {\n      var _this$state12 = _this.state,\n        flattenNodes = _this$state12.flattenNodes,\n        activeKey = _this$state12.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      });\n      // Align with index\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var key = item.key;\n        _this.onActiveChange(key);\n      } else {\n        _this.onActiveChange(null);\n      }\n    };\n    _this.onKeyDown = function (event) {\n      var _this$state13 = _this.state,\n        activeKey = _this$state13.activeKey,\n        expandedKeys = _this$state13.expandedKeys,\n        checkedKeys = _this$state13.checkedKeys,\n        fieldNames = _this$state13.fieldNames;\n      var _this$props9 = _this.props,\n        onKeyDown = _this$props9.onKeyDown,\n        checkable = _this$props9.checkable,\n        selectable = _this$props9.selectable;\n      // >>>>>>>>>> Direction\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      }\n      // >>>>>>>>>> Expand & Selection\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data[fieldNames.children] || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].key);\n              }\n              event.preventDefault();\n              break;\n            }\n          // Selection\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(event);\n    };\n    /**\n     * Only update the value which is not in props\n     */\n    _this.setUncontrolledState = function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (name in _this.props) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    };\n    _this.scrollTo = function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    };\n    return _this;\n  }\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var activeKey = this.props.activeKey;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n      var _this$state14 = this.state,\n        focused = _this$state14.focused,\n        flattenNodes = _this$state14.flattenNodes,\n        keyEntities = _this$state14.keyEntities,\n        draggingNodeKey = _this$state14.draggingNodeKey,\n        activeKey = _this$state14.activeKey,\n        dropLevelOffset = _this$state14.dropLevelOffset,\n        dropContainerKey = _this$state14.dropContainerKey,\n        dropTargetKey = _this$state14.dropTargetKey,\n        dropPosition = _this$state14.dropPosition,\n        dragOverNodeKey = _this$state14.dragOverNodeKey,\n        indent = _this$state14.indent;\n      var _this$props10 = this.props,\n        prefixCls = _this$props10.prefixCls,\n        className = _this$props10.className,\n        style = _this$props10.style,\n        showLine = _this$props10.showLine,\n        focusable = _this$props10.focusable,\n        _this$props10$tabInde = _this$props10.tabIndex,\n        tabIndex = _this$props10$tabInde === void 0 ? 0 : _this$props10$tabInde,\n        selectable = _this$props10.selectable,\n        showIcon = _this$props10.showIcon,\n        icon = _this$props10.icon,\n        switcherIcon = _this$props10.switcherIcon,\n        draggable = _this$props10.draggable,\n        checkable = _this$props10.checkable,\n        checkStrictly = _this$props10.checkStrictly,\n        disabled = _this$props10.disabled,\n        motion = _this$props10.motion,\n        loadData = _this$props10.loadData,\n        filterTreeNode = _this$props10.filterTreeNode,\n        height = _this$props10.height,\n        itemHeight = _this$props10.itemHeight,\n        virtual = _this$props10.virtual,\n        titleRender = _this$props10.titleRender,\n        dropIndicatorRender = _this$props10.dropIndicatorRender,\n        onContextMenu = _this$props10.onContextMenu,\n        onScroll = _this$props10.onScroll,\n        direction = _this$props10.direction,\n        rootClassName = _this$props10.rootClassName,\n        rootStyle = _this$props10.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n      // It's better move to hooks but we just simply keep here\n      var draggableConfig;\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: {\n          prefixCls: prefixCls,\n          selectable: selectable,\n          showIcon: showIcon,\n          icon: icon,\n          switcherIcon: switcherIcon,\n          draggable: draggableConfig,\n          draggingNodeKey: draggingNodeKey,\n          checkable: checkable,\n          checkStrictly: checkStrictly,\n          disabled: disabled,\n          keyEntities: keyEntities,\n          dropLevelOffset: dropLevelOffset,\n          dropContainerKey: dropContainerKey,\n          dropTargetKey: dropTargetKey,\n          dropPosition: dropPosition,\n          dragOverNodeKey: dragOverNodeKey,\n          indent: indent,\n          direction: direction,\n          dropIndicatorRender: dropIndicatorRender,\n          loadData: loadData,\n          filterTreeNode: filterTreeNode,\n          titleRender: titleRender,\n          onNodeClick: this.onNodeClick,\n          onNodeDoubleClick: this.onNodeDoubleClick,\n          onNodeExpand: this.onNodeExpand,\n          onNodeSelect: this.onNodeSelect,\n          onNodeCheck: this.onNodeCheck,\n          onNodeLoad: this.onNodeLoad,\n          onNodeMouseEnter: this.onNodeMouseEnter,\n          onNodeMouseLeave: this.onNodeMouseLeave,\n          onNodeContextMenu: this.onNodeContextMenu,\n          onNodeDragStart: this.onNodeDragStart,\n          onNodeDragEnter: this.onNodeDragEnter,\n          onNodeDragOver: this.onNodeDragOver,\n          onNodeDragLeave: this.onNodeDragLeave,\n          onNodeDragEnd: this.onNodeDragEnd,\n          onNodeDrop: this.onNodeDrop\n        }\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        role: \"tree\",\n        className: classNames(prefixCls, className, rootClassName, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-show-line\"), showLine), _defineProperty(_classNames, \"\".concat(prefixCls, \"-focused\"), focused), _defineProperty(_classNames, \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null), _classNames)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && name in props || prevProps && prevProps[name] !== props[name];\n      }\n      // ================== Tree Node ==================\n      var treeData;\n      // fieldNames\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      }\n      // Check if `treeData` or `children` changed and save into the state.\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      }\n      // Save flatten nodes info and convert `treeData` into keyEntities\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities);\n        // Warning if treeNode not provide key\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities;\n      // ================ expandedKeys =================\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n        delete cloneKeyEntities[MOTION_KEY];\n        newState.expandedKeys = Object.keys(cloneKeyEntities).map(function (key) {\n          return cloneKeyEntities[key].key;\n        });\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      }\n      // ================ flattenNodes =================\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      }\n      // ================ selectedKeys =================\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      }\n      // ================= checkedKeys =================\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      }\n      // ================= loadedKeys ==================\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(React.Component);\nTree.defaultProps = {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  },\n  expandAction: false\n};\nTree.TreeNode = TreeNode;\nexport default Tree;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE;AACA;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,IAAIC,YAAY,EAAEC,UAAU,QAAQ,YAAY;AAC/D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,QAAQ;AACjJ,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,qBAAqB,EAAEC,2BAA2B,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,kBAAkB;AAC9K,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,IAAI,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAClDhC,SAAS,CAAC+B,IAAI,EAAEC,gBAAgB,CAAC;EACjC,IAAIC,MAAM,GAAGhC,YAAY,CAAC8B,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTrC,eAAe,CAAC,IAAI,EAAEkC,IAAI,CAAC;IAC3B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACxFF,KAAK,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC/B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,KAAK,CAAC,CAAC;IACvDJ,KAAK,CAACU,SAAS,GAAG,KAAK;IACvBV,KAAK,CAACW,qBAAqB,GAAG,KAAK,CAAC;IACpCX,KAAK,CAACY,iBAAiB,GAAG,CAAC,CAAC;IAC5BZ,KAAK,CAACa,KAAK,GAAG;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,EAAE;MACpB;MACA;MACA;MACAC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE,IAAI;MACjB;MACA;MACA;MACAC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE7C,cAAc,CAAC;IAC7B,CAAC;IACDQ,KAAK,CAACsC,sBAAsB,GAAG,IAAI;IACnCtC,KAAK,CAACuC,QAAQ,GAAG,KAAK,CAAC;IACvBvC,KAAK,CAACwC,gCAAgC,GAAG,IAAI;IAC7CxC,KAAK,CAACyC,OAAO,GAAG,aAAarE,KAAK,CAACsE,SAAS,CAAC,CAAC;IAC9C1C,KAAK,CAAC2C,eAAe,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;MAC7C,IAAIC,WAAW,GAAG9C,KAAK,CAACa,KAAK;QAC3BQ,YAAY,GAAGyB,WAAW,CAACzB,YAAY;QACvCP,WAAW,GAAGgC,WAAW,CAAChC,WAAW;MACvC,IAAIiC,WAAW,GAAG/C,KAAK,CAACgD,KAAK,CAACD,WAAW;MACzC,IAAIE,QAAQ,GAAGJ,IAAI,CAACG,KAAK,CAACC,QAAQ;MAClCjD,KAAK,CAACuC,QAAQ,GAAGM,IAAI;MACrB7C,KAAK,CAACsC,sBAAsB,GAAG;QAC7BY,CAAC,EAAEN,KAAK,CAACO,OAAO;QAChBC,CAAC,EAAER,KAAK,CAACS;MACX,CAAC;MACD,IAAIC,eAAe,GAAG1E,MAAM,CAACyC,YAAY,EAAE4B,QAAQ,CAAC;MACpDjD,KAAK,CAACuD,QAAQ,CAAC;QACbjC,eAAe,EAAE2B,QAAQ;QACzB1B,gBAAgB,EAAEvC,mBAAmB,CAACiE,QAAQ,EAAEnC,WAAW,CAAC;QAC5DC,MAAM,EAAEf,KAAK,CAACyC,OAAO,CAACe,OAAO,CAACC,cAAc,CAAC;MAC/C,CAAC,CAAC;MACFzD,KAAK,CAAC0D,eAAe,CAACJ,eAAe,CAAC;MACtCK,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE5D,KAAK,CAAC6D,eAAe,CAAC;MACzDd,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpEH,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACIhD,KAAK,CAAC8D,eAAe,GAAG,UAAUlB,KAAK,EAAEC,IAAI,EAAE;MAC7C,IAAIkB,YAAY,GAAG/D,KAAK,CAACa,KAAK;QAC5BQ,YAAY,GAAG0C,YAAY,CAAC1C,YAAY;QACxCP,WAAW,GAAGiD,YAAY,CAACjD,WAAW;QACtCS,gBAAgB,GAAGwC,YAAY,CAACxC,gBAAgB;QAChDS,YAAY,GAAG+B,YAAY,CAAC/B,YAAY;QACxCjB,MAAM,GAAGgD,YAAY,CAAChD,MAAM;MAC9B,IAAIiD,WAAW,GAAGhE,KAAK,CAACgD,KAAK;QAC3BiB,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,SAAS,GAAGH,WAAW,CAACG,SAAS;QACjCC,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACnC,IAAIC,WAAW,GAAGxB,IAAI,CAACG,KAAK;QAC1BsB,GAAG,GAAGD,WAAW,CAACC,GAAG;QACrBrB,QAAQ,GAAGoB,WAAW,CAACpB,QAAQ;MACjC,IAAIsB,qBAAqB,GAAG1G,sBAAsB,CAACmC,KAAK,CAAC;QACvDuC,QAAQ,GAAGgC,qBAAqB,CAAChC,QAAQ;MAC3C;MACA,IAAIvC,KAAK,CAACwC,gCAAgC,KAAKS,QAAQ,EAAE;QACvDjD,KAAK,CAACwC,gCAAgC,GAAGS,QAAQ;MACnD;MACA,IAAI,CAACV,QAAQ,EAAE;QACbvC,KAAK,CAACwE,cAAc,CAAC,CAAC;QACtB;MACF;MACA,IAAIC,iBAAiB,GAAG5F,gBAAgB,CAAC+D,KAAK,EAAEL,QAAQ,EAAEM,IAAI,EAAE9B,MAAM,EAAEf,KAAK,CAACsC,sBAAsB,EAAE6B,SAAS,EAAEnC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE+C,SAAS,CAAC;QAClK3C,YAAY,GAAGgD,iBAAiB,CAAChD,YAAY;QAC7CE,eAAe,GAAG8C,iBAAiB,CAAC9C,eAAe;QACnDH,aAAa,GAAGiD,iBAAiB,CAACjD,aAAa;QAC/CE,gBAAgB,GAAG+C,iBAAiB,CAAC/C,gBAAgB;QACrDE,aAAa,GAAG6C,iBAAiB,CAAC7C,aAAa;QAC/CC,WAAW,GAAG4C,iBAAiB,CAAC5C,WAAW;QAC3CC,eAAe,GAAG2C,iBAAiB,CAAC3C,eAAe;MACrD;MACA;MACAP,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC;MAC9C;MACA,CAACK,WAAW,EAAE;QACZ7B,KAAK,CAACwE,cAAc,CAAC,CAAC;QACtB;MACF;MACA;MACA,IAAI,CAACxE,KAAK,CAACW,qBAAqB,EAAE;QAChCX,KAAK,CAACW,qBAAqB,GAAG,CAAC,CAAC;MAClC;MACAgE,MAAM,CAACC,IAAI,CAAC5E,KAAK,CAACW,qBAAqB,CAAC,CAACkE,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC9DC,YAAY,CAAC/E,KAAK,CAACW,qBAAqB,CAACmE,GAAG,CAAC,CAAC;MAChD,CAAC,CAAC;MACF,IAAIvC,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKJ,IAAI,CAACG,KAAK,CAACC,QAAQ,EAAE;QACnD;QACA;QACA;QACA;QACAL,KAAK,CAACoC,OAAO,CAAC,CAAC;QACfhF,KAAK,CAACW,qBAAqB,CAAC2D,GAAG,CAAC,GAAGX,MAAM,CAACsB,UAAU,CAAC,YAAY;UAC/D,IAAIjF,KAAK,CAACa,KAAK,CAACS,eAAe,KAAK,IAAI,EAAE;UAC1C,IAAIgC,eAAe,GAAG5F,kBAAkB,CAAC2D,YAAY,CAAC;UACtD,IAAI6D,MAAM,GAAG9F,SAAS,CAAC0B,WAAW,EAAE+B,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAC;UACxD,IAAIiC,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAEhF,MAAM,EAAE;YAC5CmD,eAAe,GAAG3E,MAAM,CAAC0C,YAAY,EAAEwB,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAC;UAC7D;UACA,IAAI,EAAE,cAAc,IAAIjD,KAAK,CAACgD,KAAK,CAAC,EAAE;YACpChD,KAAK,CAAC0D,eAAe,CAACJ,eAAe,CAAC;UACxC;UACAY,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACZ,eAAe,EAAE;YAC5ET,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK,CAAC;YAC7CoC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAEzC,KAAK,CAACyC;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;MACA;MACA,IAAI9C,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKzB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QACtE3B,KAAK,CAACwE,cAAc,CAAC,CAAC;QACtB;MACF;MACA;MACAxE,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BE,eAAe,EAAEA,eAAe;QAChCH,aAAa,EAAEA,aAAa;QAC5BE,gBAAgB,EAAEA,gBAAgB;QAClCE,aAAa,EAAEA,aAAa;QAC5BC,WAAW,EAAEA;MACf,CAAC,CAAC;MACFoC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpErB,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK,CAAC;QAC7C3B,YAAY,EAAEA;MAChB,CAAC,CAAC;IACJ,CAAC;IACDrB,KAAK,CAACsF,cAAc,GAAG,UAAU1C,KAAK,EAAEC,IAAI,EAAE;MAC5C,IAAI0C,YAAY,GAAGvF,KAAK,CAACa,KAAK;QAC5BU,gBAAgB,GAAGgE,YAAY,CAAChE,gBAAgB;QAChDS,YAAY,GAAGuD,YAAY,CAACvD,YAAY;QACxClB,WAAW,GAAGyE,YAAY,CAACzE,WAAW;QACtCO,YAAY,GAAGkE,YAAY,CAAClE,YAAY;QACxCN,MAAM,GAAGwE,YAAY,CAACxE,MAAM;MAC9B,IAAIyE,YAAY,GAAGxF,KAAK,CAACgD,KAAK;QAC5ByC,UAAU,GAAGD,YAAY,CAACC,UAAU;QACpCtB,SAAS,GAAGqB,YAAY,CAACrB,SAAS;QAClCC,SAAS,GAAGoB,YAAY,CAACpB,SAAS;MACpC,IAAIsB,sBAAsB,GAAG7H,sBAAsB,CAACmC,KAAK,CAAC;QACxDuC,QAAQ,GAAGmD,sBAAsB,CAACnD,QAAQ;MAC5C,IAAI,CAACA,QAAQ,EAAE;QACb;MACF;MACA,IAAIoD,kBAAkB,GAAG9G,gBAAgB,CAAC+D,KAAK,EAAEL,QAAQ,EAAEM,IAAI,EAAE9B,MAAM,EAAEf,KAAK,CAACsC,sBAAsB,EAAE6B,SAAS,EAAEnC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE+C,SAAS,CAAC;QACnK3C,YAAY,GAAGkE,kBAAkB,CAAClE,YAAY;QAC9CE,eAAe,GAAGgE,kBAAkB,CAAChE,eAAe;QACpDH,aAAa,GAAGmE,kBAAkB,CAACnE,aAAa;QAChDE,gBAAgB,GAAGiE,kBAAkB,CAACjE,gBAAgB;QACtDG,WAAW,GAAG8D,kBAAkB,CAAC9D,WAAW;QAC5CD,aAAa,GAAG+D,kBAAkB,CAAC/D,aAAa;QAChDE,eAAe,GAAG6D,kBAAkB,CAAC7D,eAAe;MACtD,IAAIP,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAACK,WAAW,EAAE;QAClE;QACA;QACA;MACF;MACA;MACA,IAAIU,QAAQ,CAACS,KAAK,CAACC,QAAQ,KAAKzB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QACtE,IAAI,EAAE3B,KAAK,CAACa,KAAK,CAACY,YAAY,KAAK,IAAI,IAAIzB,KAAK,CAACa,KAAK,CAACc,eAAe,KAAK,IAAI,IAAI3B,KAAK,CAACa,KAAK,CAACW,aAAa,KAAK,IAAI,IAAIxB,KAAK,CAACa,KAAK,CAACa,gBAAgB,KAAK,IAAI,IAAI1B,KAAK,CAACa,KAAK,CAACe,aAAa,KAAK,IAAI,IAAI5B,KAAK,CAACa,KAAK,CAACgB,WAAW,KAAK,KAAK,IAAI7B,KAAK,CAACa,KAAK,CAACiB,eAAe,KAAK,IAAI,CAAC,EAAE;UAClR9B,KAAK,CAACwE,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM,IAAI,EAAE/C,YAAY,KAAKzB,KAAK,CAACa,KAAK,CAACY,YAAY,IAAIE,eAAe,KAAK3B,KAAK,CAACa,KAAK,CAACc,eAAe,IAAIH,aAAa,KAAKxB,KAAK,CAACa,KAAK,CAACW,aAAa,IAAIE,gBAAgB,KAAK1B,KAAK,CAACa,KAAK,CAACa,gBAAgB,IAAIE,aAAa,KAAK5B,KAAK,CAACa,KAAK,CAACe,aAAa,IAAIC,WAAW,KAAK7B,KAAK,CAACa,KAAK,CAACgB,WAAW,IAAIC,eAAe,KAAK9B,KAAK,CAACa,KAAK,CAACiB,eAAe,CAAC,EAAE;QAC3V9B,KAAK,CAACuD,QAAQ,CAAC;UACb9B,YAAY,EAAEA,YAAY;UAC1BE,eAAe,EAAEA,eAAe;UAChCH,aAAa,EAAEA,aAAa;UAC5BE,gBAAgB,EAAEA,gBAAgB;UAClCE,aAAa,EAAEA,aAAa;UAC5BC,WAAW,EAAEA,WAAW;UACxBC,eAAe,EAAEA;QACnB,CAAC,CAAC;MACJ;MACA2D,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC;QACjE7C,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IACDhD,KAAK,CAAC4F,eAAe,GAAG,UAAUhD,KAAK,EAAEC,IAAI,EAAE;MAC7C;MACA;MACA,IAAI7C,KAAK,CAACwC,gCAAgC,KAAKK,IAAI,CAACG,KAAK,CAACC,QAAQ,IAAI,CAACL,KAAK,CAACiD,aAAa,CAACC,QAAQ,CAAClD,KAAK,CAACmD,aAAa,CAAC,EAAE;QACxH/F,KAAK,CAACwE,cAAc,CAAC,CAAC;QACtBxE,KAAK,CAACwC,gCAAgC,GAAG,IAAI;MAC/C;MACA,IAAIwD,WAAW,GAAGhG,KAAK,CAACgD,KAAK,CAACgD,WAAW;MACzCA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;QACpEpD,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;IACJ,CAAC;IACD;IACA;IACAhD,KAAK,CAAC6D,eAAe,GAAG,UAAUjB,KAAK,EAAE;MACvC5C,KAAK,CAACiG,aAAa,CAACrD,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;MACtCe,MAAM,CAACuC,mBAAmB,CAAC,SAAS,EAAElG,KAAK,CAAC6D,eAAe,CAAC;IAC9D,CAAC;IACD;IACA7D,KAAK,CAACiG,aAAa,GAAG,UAAUrD,KAAK,EAAEC,IAAI,EAAE;MAC3C,IAAIsD,SAAS,GAAGnG,KAAK,CAACgD,KAAK,CAACmD,SAAS;MACrCnG,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAE;MACnB,CAAC,CAAC;MACF9B,KAAK,CAACoG,cAAc,CAAC,CAAC;MACtBD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC;QAC9DvD,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACuD,IAAI,CAACG,KAAK;MAC9C,CAAC,CAAC;MACFhD,KAAK,CAACuC,QAAQ,GAAG,IAAI;MACrBoB,MAAM,CAACuC,mBAAmB,CAAC,SAAS,EAAElG,KAAK,CAAC6D,eAAe,CAAC;IAC9D,CAAC;IACD7D,KAAK,CAACqG,UAAU,GAAG,UAAUzD,KAAK,EAAEC,IAAI,EAAE;MACxC,IAAIyD,mBAAmB;MACvB,IAAIC,WAAW,GAAGrG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3F,IAAIuG,YAAY,GAAGzG,KAAK,CAACa,KAAK;QAC5BU,gBAAgB,GAAGkF,YAAY,CAAClF,gBAAgB;QAChDE,YAAY,GAAGgF,YAAY,CAAChF,YAAY;QACxCD,aAAa,GAAGiF,YAAY,CAACjF,aAAa;QAC1CI,aAAa,GAAG6E,YAAY,CAAC7E,aAAa;QAC1CC,WAAW,GAAG4E,YAAY,CAAC5E,WAAW;MACxC,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI6E,MAAM,GAAG1G,KAAK,CAACgD,KAAK,CAAC0D,MAAM;MAC/B1G,KAAK,CAACuD,QAAQ,CAAC;QACbzB,eAAe,EAAE;MACnB,CAAC,CAAC;MACF9B,KAAK,CAACoG,cAAc,CAAC,CAAC;MACtB,IAAI5E,aAAa,KAAK,IAAI,EAAE;MAC5B,IAAImF,qBAAqB,GAAGlJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,gBAAgB,CAAC8B,aAAa,EAAExB,KAAK,CAAC4G,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClIC,MAAM,EAAE,CAAC,CAACP,mBAAmB,GAAGtG,KAAK,CAAC8G,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIR,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACxB,GAAG,MAAMtD,aAAa;QACvJuF,IAAI,EAAE3H,SAAS,CAACY,KAAK,CAACa,KAAK,CAACC,WAAW,EAAEU,aAAa,CAAC,CAACqB;MAC1D,CAAC,CAAC;MACF,IAAImE,WAAW,GAAGzF,gBAAgB,CAACmD,OAAO,CAAClD,aAAa,CAAC,KAAK,CAAC,CAAC;MAChErD,OAAO,CAAC,CAAC6I,WAAW,EAAE,6FAA6F,CAAC;MACpH,IAAIC,MAAM,GAAG/H,QAAQ,CAAC0C,aAAa,CAAC;MACpC,IAAIsF,UAAU,GAAG;QACftE,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEvD,2BAA2B,CAACqH,qBAAqB,CAAC;QACxDpE,QAAQ,EAAEvC,KAAK,CAACuC,QAAQ,GAAGjD,2BAA2B,CAACU,KAAK,CAACuC,QAAQ,CAACS,KAAK,CAAC,GAAG,IAAI;QACnFmE,aAAa,EAAE,CAACnH,KAAK,CAACuC,QAAQ,CAACS,KAAK,CAACC,QAAQ,CAAC,CAACxC,MAAM,CAACc,gBAAgB,CAAC;QACvE6F,SAAS,EAAE3F,YAAY,KAAK,CAAC;QAC7BA,YAAY,EAAEA,YAAY,GAAG4F,MAAM,CAACJ,MAAM,CAACA,MAAM,CAAC9G,MAAM,GAAG,CAAC,CAAC;MAC/D,CAAC;MACD,IAAI,CAACoG,WAAW,EAAE;QAChBG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,UAAU,CAAC;MACpE;MACAlH,KAAK,CAACuC,QAAQ,GAAG,IAAI;IACvB,CAAC;IACDvC,KAAK,CAACoG,cAAc,GAAG,YAAY;MACjC,IAAI9E,eAAe,GAAGtB,KAAK,CAACa,KAAK,CAACS,eAAe;MACjD,IAAIA,eAAe,KAAK,IAAI,EAAE;QAC5BtB,KAAK,CAACuD,QAAQ,CAAC;UACbjC,eAAe,EAAE,IAAI;UACrBG,YAAY,EAAE,IAAI;UAClBC,gBAAgB,EAAE,IAAI;UACtBF,aAAa,EAAE,IAAI;UACnBG,eAAe,EAAE,IAAI;UACrBE,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;MACA9B,KAAK,CAACsC,sBAAsB,GAAG,IAAI;MACnCtC,KAAK,CAACwC,gCAAgC,GAAG,IAAI;IAC/C,CAAC;IACDxC,KAAK,CAACsH,yBAAyB,GAAG,UAAUC,CAAC,EAAEC,QAAQ,EAAE;MACvD,IAAIC,YAAY,GAAGzH,KAAK,CAACa,KAAK;QAC5BQ,YAAY,GAAGoG,YAAY,CAACpG,YAAY;QACxCW,YAAY,GAAGyF,YAAY,CAACzF,YAAY;MAC1C,IAAIoD,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ;QAC9BN,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG;QAClB4C,MAAM,GAAGF,QAAQ,CAACE,MAAM;MAC1B,IAAIA,MAAM,IAAIH,CAAC,CAACI,QAAQ,IAAIJ,CAAC,CAACK,OAAO,IAAIL,CAAC,CAACM,OAAO,EAAE;QAClD;MACF;MACA,IAAIhF,IAAI,GAAGb,YAAY,CAAC8F,MAAM,CAAC,UAAUC,QAAQ,EAAE;QACjD,OAAOA,QAAQ,CAACjD,GAAG,KAAKA,GAAG;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,IAAIkD,SAAS,GAAG1I,2BAA2B,CAAC7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,gBAAgB,CAACoF,GAAG,EAAE9E,KAAK,CAAC4G,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACxIG,IAAI,EAAElE,IAAI,CAACkE;MACb,CAAC,CAAC,CAAC;MACH/G,KAAK,CAAC0D,eAAe,CAAC0B,QAAQ,GAAGxG,MAAM,CAACyC,YAAY,EAAEyD,GAAG,CAAC,GAAGnG,MAAM,CAAC0C,YAAY,EAAEyD,GAAG,CAAC,CAAC;MACvF9E,KAAK,CAACiI,YAAY,CAACV,CAAC,EAAES,SAAS,CAAC;IAClC,CAAC;IACDhI,KAAK,CAACkI,WAAW,GAAG,UAAUX,CAAC,EAAEC,QAAQ,EAAE;MACzC,IAAIW,YAAY,GAAGnI,KAAK,CAACgD,KAAK;QAC5BoF,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,YAAY,GAAGF,YAAY,CAACE,YAAY;MAC1C,IAAIA,YAAY,KAAK,OAAO,EAAE;QAC5BrI,KAAK,CAACsH,yBAAyB,CAACC,CAAC,EAAEC,QAAQ,CAAC;MAC9C;MACAY,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACb,CAAC,EAAEC,QAAQ,CAAC;IACxE,CAAC;IACDxH,KAAK,CAACsI,iBAAiB,GAAG,UAAUf,CAAC,EAAEC,QAAQ,EAAE;MAC/C,IAAIe,YAAY,GAAGvI,KAAK,CAACgD,KAAK;QAC5BwF,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CH,YAAY,GAAGE,YAAY,CAACF,YAAY;MAC1C,IAAIA,YAAY,KAAK,aAAa,EAAE;QAClCrI,KAAK,CAACsH,yBAAyB,CAACC,CAAC,EAAEC,QAAQ,CAAC;MAC9C;MACAgB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACjB,CAAC,EAAEC,QAAQ,CAAC;IAC1F,CAAC;IACDxH,KAAK,CAACyI,YAAY,GAAG,UAAUlB,CAAC,EAAEC,QAAQ,EAAE;MAC1C,IAAIxG,YAAY,GAAGhB,KAAK,CAACa,KAAK,CAACG,YAAY;MAC3C,IAAI0H,YAAY,GAAG1I,KAAK,CAACa,KAAK;QAC5BC,WAAW,GAAG4H,YAAY,CAAC5H,WAAW;QACtCuB,UAAU,GAAGqG,YAAY,CAACrG,UAAU;MACtC,IAAIsG,YAAY,GAAG3I,KAAK,CAACgD,KAAK;QAC5B4F,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;MAClC,IAAIC,QAAQ,GAAGtB,QAAQ,CAACsB,QAAQ;MAChC,IAAIhE,GAAG,GAAG0C,QAAQ,CAACnF,UAAU,CAACyC,GAAG,CAAC;MAClC,IAAIiE,cAAc,GAAG,CAACD,QAAQ;MAC9B;MACA,IAAI,CAACC,cAAc,EAAE;QACnB/H,YAAY,GAAGpC,MAAM,CAACoC,YAAY,EAAE8D,GAAG,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC+D,QAAQ,EAAE;QACpB7H,YAAY,GAAG,CAAC8D,GAAG,CAAC;MACtB,CAAC,MAAM;QACL9D,YAAY,GAAGrC,MAAM,CAACqC,YAAY,EAAE8D,GAAG,CAAC;MAC1C;MACA;MACA,IAAIkE,aAAa,GAAGhI,YAAY,CAACiI,GAAG,CAAC,UAAUC,WAAW,EAAE;QAC1D,IAAIhE,MAAM,GAAG9F,SAAS,CAAC0B,WAAW,EAAEoI,WAAW,CAAC;QAChD,IAAI,CAAChE,MAAM,EAAE,OAAO,IAAI;QACxB,OAAOA,MAAM,CAACrC,IAAI;MACpB,CAAC,CAAC,CAACiF,MAAM,CAAC,UAAUjF,IAAI,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,CAAC;MACF7C,KAAK,CAACmJ,oBAAoB,CAAC;QACzBnI,YAAY,EAAEA;MAChB,CAAC,CAAC;MACF4H,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC5H,YAAY,EAAE;QACzE4B,KAAK,EAAE,QAAQ;QACfkG,QAAQ,EAAEC,cAAc;QACxBlG,IAAI,EAAE2E,QAAQ;QACdwB,aAAa,EAAEA,aAAa;QAC5B3D,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC,CAAC;IACJ,CAAC;IACDrF,KAAK,CAACoJ,WAAW,GAAG,UAAU7B,CAAC,EAAEC,QAAQ,EAAE6B,OAAO,EAAE;MAClD,IAAIC,YAAY,GAAGtJ,KAAK,CAACa,KAAK;QAC5BC,WAAW,GAAGwI,YAAY,CAACxI,WAAW;QACtCyI,cAAc,GAAGD,YAAY,CAACrI,WAAW;QACzCuI,kBAAkB,GAAGF,YAAY,CAACpI,eAAe;MACnD,IAAIuI,YAAY,GAAGzJ,KAAK,CAACgD,KAAK;QAC5B0G,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAI7E,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG;MACtB;MACA,IAAI8E,UAAU;MACd,IAAIC,QAAQ,GAAG;QACbjH,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE2E,QAAQ;QACd6B,OAAO,EAAEA,OAAO;QAChBhE,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC;MACD,IAAIqE,aAAa,EAAE;QACjB,IAAIzI,WAAW,GAAGoI,OAAO,GAAG1K,MAAM,CAAC4K,cAAc,EAAEzE,GAAG,CAAC,GAAGlG,MAAM,CAAC2K,cAAc,EAAEzE,GAAG,CAAC;QACrF,IAAI5D,eAAe,GAAGtC,MAAM,CAAC4K,kBAAkB,EAAE1E,GAAG,CAAC;QACrD8E,UAAU,GAAG;UACXP,OAAO,EAAEpI,WAAW;UACpB6I,WAAW,EAAE5I;QACf,CAAC;QACD2I,QAAQ,CAACE,YAAY,GAAG9I,WAAW,CAACgI,GAAG,CAAC,UAAUe,UAAU,EAAE;UAC5D,OAAO5K,SAAS,CAAC0B,WAAW,EAAEkJ,UAAU,CAAC;QAC3C,CAAC,CAAC,CAAClC,MAAM,CAAC,UAAU5C,MAAM,EAAE;UAC1B,OAAOA,MAAM;QACf,CAAC,CAAC,CAAC+D,GAAG,CAAC,UAAU/D,MAAM,EAAE;UACvB,OAAOA,MAAM,CAACrC,IAAI;QACpB,CAAC,CAAC;QACF7C,KAAK,CAACmJ,oBAAoB,CAAC;UACzBlI,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAIgJ,aAAa,GAAG9K,YAAY,CAAC,EAAE,CAACsB,MAAM,CAAC/C,kBAAkB,CAAC6L,cAAc,CAAC,EAAE,CAACzE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAEhE,WAAW,CAAC;UACvGoJ,YAAY,GAAGD,aAAa,CAAChJ,WAAW;UACxCkJ,gBAAgB,GAAGF,aAAa,CAAC/I,eAAe;QAClD;QACA,IAAI,CAACmI,OAAO,EAAE;UACZ,IAAIe,MAAM,GAAG,IAAIC,GAAG,CAACH,YAAY,CAAC;UAClCE,MAAM,CAACE,MAAM,CAACxF,GAAG,CAAC;UAClB,IAAIyF,cAAc,GAAGpL,YAAY,CAACkB,KAAK,CAACmK,IAAI,CAACJ,MAAM,CAAC,EAAE;YACpDf,OAAO,EAAE,KAAK;YACdnI,eAAe,EAAEiJ;UACnB,CAAC,EAAErJ,WAAW,CAAC;UACfoJ,YAAY,GAAGK,cAAc,CAACtJ,WAAW;UACzCkJ,gBAAgB,GAAGI,cAAc,CAACrJ,eAAe;QACnD;QACA0I,UAAU,GAAGM,YAAY;QACzB;QACAL,QAAQ,CAACE,YAAY,GAAG,EAAE;QAC1BF,QAAQ,CAACY,qBAAqB,GAAG,EAAE;QACnCZ,QAAQ,CAAC3I,eAAe,GAAGiJ,gBAAgB;QAC3CD,YAAY,CAACrF,OAAO,CAAC,UAAUmF,UAAU,EAAE;UACzC,IAAI9E,MAAM,GAAG9F,SAAS,CAAC0B,WAAW,EAAEkJ,UAAU,CAAC;UAC/C,IAAI,CAAC9E,MAAM,EAAE;UACb,IAAIrC,IAAI,GAAGqC,MAAM,CAACrC,IAAI;YACpByB,GAAG,GAAGY,MAAM,CAACZ,GAAG;UAClBuF,QAAQ,CAACE,YAAY,CAACW,IAAI,CAAC7H,IAAI,CAAC;UAChCgH,QAAQ,CAACY,qBAAqB,CAACC,IAAI,CAAC;YAClC7H,IAAI,EAAEA,IAAI;YACVyB,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ,CAAC,CAAC;QACFtE,KAAK,CAACmJ,oBAAoB,CAAC;UACzBlI,WAAW,EAAEiJ;QACf,CAAC,EAAE,KAAK,EAAE;UACRhJ,eAAe,EAAEiJ;QACnB,CAAC,CAAC;MACJ;MACAR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,UAAU,EAAEC,QAAQ,CAAC;IACjF,CAAC;IACD7J,KAAK,CAAC2K,UAAU,GAAG,UAAUnD,QAAQ,EAAE;MACrC,IAAI1C,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG;MACtB,IAAI8F,WAAW,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QACvD;QACA/K,KAAK,CAACuD,QAAQ,CAAC,UAAUyH,IAAI,EAAE;UAC7B,IAAIC,eAAe,GAAGD,IAAI,CAAC7J,UAAU;YACnCA,UAAU,GAAG8J,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;YAC9DC,gBAAgB,GAAGF,IAAI,CAAC5J,WAAW;YACnCA,WAAW,GAAG8J,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;UACnE,IAAIC,YAAY,GAAGnL,KAAK,CAACgD,KAAK;YAC5BoI,QAAQ,GAAGD,YAAY,CAACC,QAAQ;YAChCC,MAAM,GAAGF,YAAY,CAACE,MAAM;UAC9B,IAAI,CAACD,QAAQ,IAAIjK,UAAU,CAACuD,OAAO,CAACI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI1D,WAAW,CAACsD,OAAO,CAACI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAClF,OAAO,IAAI;UACb;UACA;UACA,IAAIwG,OAAO,GAAGF,QAAQ,CAAC5D,QAAQ,CAAC;UAChC8D,OAAO,CAACC,IAAI,CAAC,YAAY;YACvB,IAAIC,iBAAiB,GAAGxL,KAAK,CAACa,KAAK,CAACM,UAAU;YAC9C,IAAIsK,aAAa,GAAG9M,MAAM,CAAC6M,iBAAiB,EAAE1G,GAAG,CAAC;YAClD;YACA;YACAuG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,aAAa,EAAE;cACpE7I,KAAK,EAAE,MAAM;cACbC,IAAI,EAAE2E;YACR,CAAC,CAAC;YACFxH,KAAK,CAACmJ,oBAAoB,CAAC;cACzBhI,UAAU,EAAEsK;YACd,CAAC,CAAC;YACFzL,KAAK,CAACuD,QAAQ,CAAC,UAAUmI,SAAS,EAAE;cAClC,OAAO;gBACLtK,WAAW,EAAExC,MAAM,CAAC8M,SAAS,CAACtK,WAAW,EAAE0D,GAAG;cAChD,CAAC;YACH,CAAC,CAAC;YACFgG,OAAO,CAAC,CAAC;UACX,CAAC,CAAC,CAACa,KAAK,CAAC,UAAUpE,CAAC,EAAE;YACpBvH,KAAK,CAACuD,QAAQ,CAAC,UAAUmI,SAAS,EAAE;cAClC,OAAO;gBACLtK,WAAW,EAAExC,MAAM,CAAC8M,SAAS,CAACtK,WAAW,EAAE0D,GAAG;cAChD,CAAC;YACH,CAAC,CAAC;YACF;YACA9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,GAAG,CAAC9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YACtE,IAAI9E,KAAK,CAACY,iBAAiB,CAACkE,GAAG,CAAC,IAAIlF,eAAe,EAAE;cACnD,IAAI4L,iBAAiB,GAAGxL,KAAK,CAACa,KAAK,CAACM,UAAU;cAC9ChD,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;cAClF6B,KAAK,CAACmJ,oBAAoB,CAAC;gBACzBhI,UAAU,EAAExC,MAAM,CAAC6M,iBAAiB,EAAE1G,GAAG;cAC3C,CAAC,CAAC;cACFgG,OAAO,CAAC,CAAC;YACX;YACAC,MAAM,CAACxD,CAAC,CAAC;UACX,CAAC,CAAC;UACF,OAAO;YACLnG,WAAW,EAAEzC,MAAM,CAACyC,WAAW,EAAE0D,GAAG;UACtC,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;MACA8F,WAAW,CAACe,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;MACjC,OAAOf,WAAW;IACpB,CAAC;IACD5K,KAAK,CAAC4L,gBAAgB,GAAG,UAAUhJ,KAAK,EAAEC,IAAI,EAAE;MAC9C,IAAIgJ,YAAY,GAAG7L,KAAK,CAACgD,KAAK,CAAC6I,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvEjJ,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC;IACD7C,KAAK,CAAC8L,gBAAgB,GAAG,UAAUlJ,KAAK,EAAEC,IAAI,EAAE;MAC9C,IAAIkJ,YAAY,GAAG/L,KAAK,CAACgD,KAAK,CAAC+I,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC;QACvEnJ,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC;IACD7C,KAAK,CAACgM,iBAAiB,GAAG,UAAUpJ,KAAK,EAAEC,IAAI,EAAE;MAC/C,IAAIoJ,YAAY,GAAGjM,KAAK,CAACgD,KAAK,CAACiJ,YAAY;MAC3C,IAAIA,YAAY,EAAE;QAChBrJ,KAAK,CAACsJ,cAAc,CAAC,CAAC;QACtBD,YAAY,CAAC;UACXrJ,KAAK,EAAEA,KAAK;UACZC,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC;IACD7C,KAAK,CAACmM,OAAO,GAAG,YAAY;MAC1B,IAAIA,OAAO,GAAGnM,KAAK,CAACgD,KAAK,CAACmJ,OAAO;MACjCnM,KAAK,CAACuD,QAAQ,CAAC;QACbtB,OAAO,EAAE;MACX,CAAC,CAAC;MACF,KAAK,IAAImK,KAAK,GAAGlM,SAAS,CAACC,MAAM,EAAEkM,IAAI,GAAG,IAAIhM,KAAK,CAAC+L,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QAC7FD,IAAI,CAACC,KAAK,CAAC,GAAGpM,SAAS,CAACoM,KAAK,CAAC;MAChC;MACAH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC3L,KAAK,CAAC,KAAK,CAAC,EAAE6L,IAAI,CAAC;IAC/E,CAAC;IACDrM,KAAK,CAACuM,MAAM,GAAG,YAAY;MACzB,IAAIA,MAAM,GAAGvM,KAAK,CAACgD,KAAK,CAACuJ,MAAM;MAC/BvM,KAAK,CAACuD,QAAQ,CAAC;QACbtB,OAAO,EAAE;MACX,CAAC,CAAC;MACFjC,KAAK,CAACwM,cAAc,CAAC,IAAI,CAAC;MAC1B,KAAK,IAAIC,KAAK,GAAGvM,SAAS,CAACC,MAAM,EAAEkM,IAAI,GAAG,IAAIhM,KAAK,CAACoM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FL,IAAI,CAACK,KAAK,CAAC,GAAGxM,SAAS,CAACwM,KAAK,CAAC;MAChC;MACAH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC/L,KAAK,CAAC,KAAK,CAAC,EAAE6L,IAAI,CAAC;IAC5E,CAAC;IACDrM,KAAK,CAAC4G,wBAAwB,GAAG,YAAY;MAC3C,IAAI+F,YAAY,GAAG3M,KAAK,CAACa,KAAK;QAC5BQ,YAAY,GAAGsL,YAAY,CAACtL,YAAY;QACxCL,YAAY,GAAG2L,YAAY,CAAC3L,YAAY;QACxCG,UAAU,GAAGwL,YAAY,CAACxL,UAAU;QACpCC,WAAW,GAAGuL,YAAY,CAACvL,WAAW;QACtCH,WAAW,GAAG0L,YAAY,CAAC1L,WAAW;QACtCC,eAAe,GAAGyL,YAAY,CAACzL,eAAe;QAC9CY,eAAe,GAAG6K,YAAY,CAAC7K,eAAe;QAC9CL,YAAY,GAAGkL,YAAY,CAAClL,YAAY;QACxCX,WAAW,GAAG6L,YAAY,CAAC7L,WAAW;MACxC,OAAO;QACLO,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCL,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCG,UAAU,EAAEA,UAAU,IAAI,EAAE;QAC5BC,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BH,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BC,eAAe,EAAEA,eAAe,IAAI,EAAE;QACtCY,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BX,WAAW,EAAEA;MACf,CAAC;IACH,CAAC;IACD;IACA;IACAd,KAAK,CAAC0D,eAAe,GAAG,UAAUrC,YAAY,EAAE;MAC9C,IAAIuL,YAAY,GAAG5M,KAAK,CAACa,KAAK;QAC5BkB,QAAQ,GAAG6K,YAAY,CAAC7K,QAAQ;QAChCM,UAAU,GAAGuK,YAAY,CAACvK,UAAU;MACtC,IAAIL,YAAY,GAAGvC,eAAe,CAACsC,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;MACtErC,KAAK,CAACmJ,oBAAoB,CAAC;QACzB9H,YAAY,EAAEA,YAAY;QAC1BW,YAAY,EAAEA;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACDhC,KAAK,CAACiI,YAAY,GAAG,UAAUV,CAAC,EAAEC,QAAQ,EAAE;MAC1C,IAAInG,YAAY,GAAGrB,KAAK,CAACa,KAAK,CAACQ,YAAY;MAC3C,IAAIwL,aAAa,GAAG7M,KAAK,CAACa,KAAK;QAC7BsB,YAAY,GAAG0K,aAAa,CAAC1K,YAAY;QACzCE,UAAU,GAAGwK,aAAa,CAACxK,UAAU;MACvC,IAAIyK,YAAY,GAAG9M,KAAK,CAACgD,KAAK;QAC5BkB,QAAQ,GAAG4I,YAAY,CAAC5I,QAAQ;QAChCkH,QAAQ,GAAG0B,YAAY,CAAC1B,QAAQ;MAClC,IAAIhG,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ;MAChC,IAAIN,GAAG,GAAG0C,QAAQ,CAACnF,UAAU,CAACyC,GAAG,CAAC;MAClC;MACA,IAAI3C,YAAY,EAAE;QAChB;MACF;MACA;MACA,IAAI4K,KAAK,GAAG1L,YAAY,CAACqD,OAAO,CAACI,GAAG,CAAC;MACrC,IAAIkI,cAAc,GAAG,CAAC5H,QAAQ;MAC9BjH,OAAO,CAACiH,QAAQ,IAAI2H,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC3H,QAAQ,IAAI2H,KAAK,KAAK,CAAC,CAAC,EAAE,wCAAwC,CAAC;MACxG,IAAIC,cAAc,EAAE;QAClB3L,YAAY,GAAG1C,MAAM,CAAC0C,YAAY,EAAEyD,GAAG,CAAC;MAC1C,CAAC,MAAM;QACLzD,YAAY,GAAGzC,MAAM,CAACyC,YAAY,EAAEyD,GAAG,CAAC;MAC1C;MACA9E,KAAK,CAAC0D,eAAe,CAACrC,YAAY,CAAC;MACnC6C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7C,YAAY,EAAE;QACzEwB,IAAI,EAAE2E,QAAQ;QACdpC,QAAQ,EAAE4H,cAAc;QACxB3H,WAAW,EAAEkC,CAAC,CAAClC;MACjB,CAAC,CAAC;MACF;MACA,IAAI2H,cAAc,IAAI5B,QAAQ,EAAE;QAC9B,IAAIR,WAAW,GAAG5K,KAAK,CAAC2K,UAAU,CAACnD,QAAQ,CAAC;QAC5C,IAAIoD,WAAW,EAAE;UACfA,WAAW,CAACW,IAAI,CAAC,YAAY;YAC3B;YACA,IAAI0B,kBAAkB,GAAGxN,eAAe,CAACO,KAAK,CAACa,KAAK,CAACkB,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;YACxFrC,KAAK,CAACmJ,oBAAoB,CAAC;cACzBnH,YAAY,EAAEiL;YAChB,CAAC,CAAC;UACJ,CAAC,CAAC,CAACtB,KAAK,CAAC,YAAY;YACnB,IAAIuB,mBAAmB,GAAGlN,KAAK,CAACa,KAAK,CAACQ,YAAY;YAClD,IAAI8L,qBAAqB,GAAGvO,MAAM,CAACsO,mBAAmB,EAAEpI,GAAG,CAAC;YAC5D9E,KAAK,CAAC0D,eAAe,CAACyJ,qBAAqB,CAAC;UAC9C,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACDnN,KAAK,CAACoN,iBAAiB,GAAG,YAAY;MACpCpN,KAAK,CAACmJ,oBAAoB,CAAC;QACzBhH,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC;IACDnC,KAAK,CAACqN,eAAe,GAAG,YAAY;MAClCpI,UAAU,CAAC,YAAY;QACrBjF,KAAK,CAACmJ,oBAAoB,CAAC;UACzBhH,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD;IACAnC,KAAK,CAACwM,cAAc,GAAG,UAAUc,YAAY,EAAE;MAC7C,IAAIpL,SAAS,GAAGlC,KAAK,CAACa,KAAK,CAACqB,SAAS;MACrC,IAAIsK,cAAc,GAAGxM,KAAK,CAACgD,KAAK,CAACwJ,cAAc;MAC/C,IAAItK,SAAS,KAAKoL,YAAY,EAAE;QAC9B;MACF;MACAtN,KAAK,CAACuD,QAAQ,CAAC;QACbrB,SAAS,EAAEoL;MACb,CAAC,CAAC;MACF,IAAIA,YAAY,KAAK,IAAI,EAAE;QACzBtN,KAAK,CAACuN,QAAQ,CAAC;UACbzI,GAAG,EAAEwI;QACP,CAAC,CAAC;MACJ;MACAd,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACc,YAAY,CAAC;IAC9F,CAAC;IACDtN,KAAK,CAAC8G,aAAa,GAAG,YAAY;MAChC,IAAI0G,aAAa,GAAGxN,KAAK,CAACa,KAAK;QAC7BqB,SAAS,GAAGsL,aAAa,CAACtL,SAAS;QACnCF,YAAY,GAAGwL,aAAa,CAACxL,YAAY;MAC3C,IAAIE,SAAS,KAAK,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAOF,YAAY,CAACyL,IAAI,CAAC,UAAUC,KAAK,EAAE;QACxC,IAAI5I,GAAG,GAAG4I,KAAK,CAAC5I,GAAG;QACnB,OAAOA,GAAG,KAAK5C,SAAS;MAC1B,CAAC,CAAC,IAAI,IAAI;IACZ,CAAC;IACDlC,KAAK,CAAC2N,eAAe,GAAG,UAAUC,MAAM,EAAE;MACxC,IAAIC,aAAa,GAAG7N,KAAK,CAACa,KAAK;QAC7BmB,YAAY,GAAG6L,aAAa,CAAC7L,YAAY;QACzCE,SAAS,GAAG2L,aAAa,CAAC3L,SAAS;MACrC,IAAI6K,KAAK,GAAG/K,YAAY,CAAC8L,SAAS,CAAC,UAAUC,KAAK,EAAE;QAClD,IAAIjJ,GAAG,GAAGiJ,KAAK,CAACjJ,GAAG;QACnB,OAAOA,GAAG,KAAK5C,SAAS;MAC1B,CAAC,CAAC;MACF;MACA,IAAI6K,KAAK,KAAK,CAAC,CAAC,IAAIa,MAAM,GAAG,CAAC,EAAE;QAC9Bb,KAAK,GAAG/K,YAAY,CAAC7B,MAAM;MAC7B;MACA4M,KAAK,GAAG,CAACA,KAAK,GAAGa,MAAM,GAAG5L,YAAY,CAAC7B,MAAM,IAAI6B,YAAY,CAAC7B,MAAM;MACpE,IAAI6N,IAAI,GAAGhM,YAAY,CAAC+K,KAAK,CAAC;MAC9B,IAAIiB,IAAI,EAAE;QACR,IAAIlJ,GAAG,GAAGkJ,IAAI,CAAClJ,GAAG;QAClB9E,KAAK,CAACwM,cAAc,CAAC1H,GAAG,CAAC;MAC3B,CAAC,MAAM;QACL9E,KAAK,CAACwM,cAAc,CAAC,IAAI,CAAC;MAC5B;IACF,CAAC;IACDxM,KAAK,CAACiO,SAAS,GAAG,UAAUrL,KAAK,EAAE;MACjC,IAAIsL,aAAa,GAAGlO,KAAK,CAACa,KAAK;QAC7BqB,SAAS,GAAGgM,aAAa,CAAChM,SAAS;QACnCb,YAAY,GAAG6M,aAAa,CAAC7M,YAAY;QACzCJ,WAAW,GAAGiN,aAAa,CAACjN,WAAW;QACvCoB,UAAU,GAAG6L,aAAa,CAAC7L,UAAU;MACvC,IAAI8L,YAAY,GAAGnO,KAAK,CAACgD,KAAK;QAC5BiL,SAAS,GAAGE,YAAY,CAACF,SAAS;QAClCG,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,UAAU,GAAGF,YAAY,CAACE,UAAU;MACtC;MACA,QAAQzL,KAAK,CAAC0L,KAAK;QACjB,KAAKrQ,OAAO,CAACsQ,EAAE;UACb;YACEvO,KAAK,CAAC2N,eAAe,CAAC,CAAC,CAAC,CAAC;YACzB/K,KAAK,CAACsJ,cAAc,CAAC,CAAC;YACtB;UACF;QACF,KAAKjO,OAAO,CAACuQ,IAAI;UACf;YACExO,KAAK,CAAC2N,eAAe,CAAC,CAAC,CAAC;YACxB/K,KAAK,CAACsJ,cAAc,CAAC,CAAC;YACtB;UACF;MACJ;MACA;MACA,IAAIuC,UAAU,GAAGzO,KAAK,CAAC8G,aAAa,CAAC,CAAC;MACtC,IAAI2H,UAAU,IAAIA,UAAU,CAAC1H,IAAI,EAAE;QACjC,IAAI2H,qBAAqB,GAAG1O,KAAK,CAAC4G,wBAAwB,CAAC,CAAC;QAC5D,IAAI+H,UAAU,GAAGF,UAAU,CAAC1H,IAAI,CAACW,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC+G,UAAU,CAAC1H,IAAI,CAAC1E,UAAU,CAAC8C,QAAQ,CAAC,IAAI,EAAE,EAAEhF,MAAM;QAC1G,IAAI6H,SAAS,GAAG1I,2BAA2B,CAAC7B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,gBAAgB,CAACwC,SAAS,EAAEwM,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACnI3H,IAAI,EAAE0H,UAAU,CAAC1H,IAAI;UACrBF,MAAM,EAAE;QACV,CAAC,CAAC,CAAC;QACH,QAAQjE,KAAK,CAAC0L,KAAK;UACjB;UACA,KAAKrQ,OAAO,CAAC2Q,IAAI;YACf;cACE;cACA,IAAID,UAAU,IAAItN,YAAY,CAACwN,QAAQ,CAAC3M,SAAS,CAAC,EAAE;gBAClDlC,KAAK,CAACiI,YAAY,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIyG,UAAU,CAACK,MAAM,EAAE;gBAC5B9O,KAAK,CAACwM,cAAc,CAACiC,UAAU,CAACK,MAAM,CAAChK,GAAG,CAAC;cAC7C;cACAlC,KAAK,CAACsJ,cAAc,CAAC,CAAC;cACtB;YACF;UACF,KAAKjO,OAAO,CAAC8Q,KAAK;YAChB;cACE;cACA,IAAIJ,UAAU,IAAI,CAACtN,YAAY,CAACwN,QAAQ,CAAC3M,SAAS,CAAC,EAAE;gBACnDlC,KAAK,CAACiI,YAAY,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIyG,UAAU,CAACtJ,QAAQ,IAAIsJ,UAAU,CAACtJ,QAAQ,CAAChF,MAAM,EAAE;gBAC5DH,KAAK,CAACwM,cAAc,CAACiC,UAAU,CAACtJ,QAAQ,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC;cAClD;cACAlC,KAAK,CAACsJ,cAAc,CAAC,CAAC;cACtB;YACF;UACF;UACA,KAAKjO,OAAO,CAAC+Q,KAAK;UAClB,KAAK/Q,OAAO,CAACgR,KAAK;YAChB;cACE,IAAIb,SAAS,IAAI,CAACpG,SAAS,CAACkH,QAAQ,IAAIlH,SAAS,CAACoG,SAAS,KAAK,KAAK,IAAI,CAACpG,SAAS,CAACmH,eAAe,EAAE;gBACnGnP,KAAK,CAACoJ,WAAW,CAAC,CAAC,CAAC,EAAEpB,SAAS,EAAE,CAAC/G,WAAW,CAAC4N,QAAQ,CAAC3M,SAAS,CAAC,CAAC;cACpE,CAAC,MAAM,IAAI,CAACkM,SAAS,IAAIC,UAAU,IAAI,CAACrG,SAAS,CAACkH,QAAQ,IAAIlH,SAAS,CAACqG,UAAU,KAAK,KAAK,EAAE;gBAC5FrO,KAAK,CAACyI,YAAY,CAAC,CAAC,CAAC,EAAET,SAAS,CAAC;cACnC;cACA;YACF;QACJ;MACF;MACAiG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACrL,KAAK,CAAC;IACxE,CAAC;IACD;AACJ;AACA;IACI5C,KAAK,CAACmJ,oBAAoB,GAAG,UAAUtI,KAAK,EAAE;MAC5C,IAAIuO,MAAM,GAAGlP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACtF,IAAImP,UAAU,GAAGnP,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKsG,SAAS,GAAGtG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MACzF,IAAI,CAACF,KAAK,CAACU,SAAS,EAAE;QACpB,IAAI4O,QAAQ,GAAG,KAAK;QACpB,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAIC,QAAQ,GAAG,CAAC,CAAC;QACjB7K,MAAM,CAACC,IAAI,CAAC/D,KAAK,CAAC,CAACgE,OAAO,CAAC,UAAU4K,IAAI,EAAE;UACzC,IAAIA,IAAI,IAAIzP,KAAK,CAACgD,KAAK,EAAE;YACvBuM,SAAS,GAAG,KAAK;YACjB;UACF;UACAD,QAAQ,GAAG,IAAI;UACfE,QAAQ,CAACC,IAAI,CAAC,GAAG5O,KAAK,CAAC4O,IAAI,CAAC;QAC9B,CAAC,CAAC;QACF,IAAIH,QAAQ,KAAK,CAACF,MAAM,IAAIG,SAAS,CAAC,EAAE;UACtCvP,KAAK,CAACuD,QAAQ,CAAC9F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+R,QAAQ,CAAC,EAAEH,UAAU,CAAC,CAAC;QACxE;MACF;IACF,CAAC;IACDrP,KAAK,CAACuN,QAAQ,GAAG,UAAUmC,MAAM,EAAE;MACjC1P,KAAK,CAACyC,OAAO,CAACe,OAAO,CAAC+J,QAAQ,CAACmC,MAAM,CAAC;IACxC,CAAC;IACD,OAAO1P,KAAK;EACd;EACApC,YAAY,CAACiC,IAAI,EAAE,CAAC;IAClBiF,GAAG,EAAE,mBAAmB;IACxB6K,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAClP,SAAS,GAAG,KAAK;MACtB,IAAI,CAACmP,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,oBAAoB;IACzB6K,KAAK,EAAE,SAASG,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,WAAW;IAChB6K,KAAK,EAAE,SAASE,SAASA,CAAA,EAAG;MAC1B,IAAI3N,SAAS,GAAG,IAAI,CAACc,KAAK,CAACd,SAAS;MACpC,IAAIA,SAAS,KAAKsE,SAAS,IAAItE,SAAS,KAAK,IAAI,CAACrB,KAAK,CAACqB,SAAS,EAAE;QACjE,IAAI,CAACqB,QAAQ,CAAC;UACZrB,SAAS,EAAEA;QACb,CAAC,CAAC;QACF,IAAIA,SAAS,KAAK,IAAI,EAAE;UACtB,IAAI,CAACqL,QAAQ,CAAC;YACZzI,GAAG,EAAE5C;UACP,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACD4C,GAAG,EAAE,sBAAsB;IAC3B6K,KAAK,EAAE,SAASI,oBAAoBA,CAAA,EAAG;MACrCpM,MAAM,CAACuC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrC,eAAe,CAAC;MAC3D,IAAI,CAACnD,SAAS,GAAG,IAAI;IACvB;EACF,CAAC,EAAE;IACDoE,GAAG,EAAE,gBAAgB;IACrB6K,KAAK,EAAE,SAASnL,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAACjB,QAAQ,CAAC;QACZzB,eAAe,EAAE,IAAI;QACrBL,YAAY,EAAE,IAAI;QAClBE,eAAe,EAAE,IAAI;QACrBH,aAAa,EAAE,IAAI;QACnBE,gBAAgB,EAAE,IAAI;QACtBE,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDiD,GAAG,EAAE,QAAQ;IACb6K,KAAK,EAAE,SAASK,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;MACf,IAAIC,aAAa,GAAG,IAAI,CAACrP,KAAK;QAC5BoB,OAAO,GAAGiO,aAAa,CAACjO,OAAO;QAC/BD,YAAY,GAAGkO,aAAa,CAAClO,YAAY;QACzClB,WAAW,GAAGoP,aAAa,CAACpP,WAAW;QACvCQ,eAAe,GAAG4O,aAAa,CAAC5O,eAAe;QAC/CY,SAAS,GAAGgO,aAAa,CAAChO,SAAS;QACnCP,eAAe,GAAGuO,aAAa,CAACvO,eAAe;QAC/CD,gBAAgB,GAAGwO,aAAa,CAACxO,gBAAgB;QACjDF,aAAa,GAAG0O,aAAa,CAAC1O,aAAa;QAC3CC,YAAY,GAAGyO,aAAa,CAACzO,YAAY;QACzCK,eAAe,GAAGoO,aAAa,CAACpO,eAAe;QAC/Cf,MAAM,GAAGmP,aAAa,CAACnP,MAAM;MAC/B,IAAIoP,aAAa,GAAG,IAAI,CAACnN,KAAK;QAC5BoN,SAAS,GAAGD,aAAa,CAACC,SAAS;QACnCC,SAAS,GAAGF,aAAa,CAACE,SAAS;QACnCC,KAAK,GAAGH,aAAa,CAACG,KAAK;QAC3BC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;QACjCC,SAAS,GAAGL,aAAa,CAACK,SAAS;QACnCC,qBAAqB,GAAGN,aAAa,CAACO,QAAQ;QAC9CA,QAAQ,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;QACvEpC,UAAU,GAAG8B,aAAa,CAAC9B,UAAU;QACrCsC,QAAQ,GAAGR,aAAa,CAACQ,QAAQ;QACjCC,IAAI,GAAGT,aAAa,CAACS,IAAI;QACzBC,YAAY,GAAGV,aAAa,CAACU,YAAY;QACzCC,SAAS,GAAGX,aAAa,CAACW,SAAS;QACnC1C,SAAS,GAAG+B,aAAa,CAAC/B,SAAS;QACnC1E,aAAa,GAAGyG,aAAa,CAACzG,aAAa;QAC3CwF,QAAQ,GAAGiB,aAAa,CAACjB,QAAQ;QACjC6B,MAAM,GAAGZ,aAAa,CAACY,MAAM;QAC7B3F,QAAQ,GAAG+E,aAAa,CAAC/E,QAAQ;QACjC4F,cAAc,GAAGb,aAAa,CAACa,cAAc;QAC7CC,MAAM,GAAGd,aAAa,CAACc,MAAM;QAC7BC,UAAU,GAAGf,aAAa,CAACe,UAAU;QACrCC,OAAO,GAAGhB,aAAa,CAACgB,OAAO;QAC/BC,WAAW,GAAGjB,aAAa,CAACiB,WAAW;QACvCC,mBAAmB,GAAGlB,aAAa,CAACkB,mBAAmB;QACvDC,aAAa,GAAGnB,aAAa,CAACmB,aAAa;QAC3CC,QAAQ,GAAGpB,aAAa,CAACoB,QAAQ;QACjCnN,SAAS,GAAG+L,aAAa,CAAC/L,SAAS;QACnCoN,aAAa,GAAGrB,aAAa,CAACqB,aAAa;QAC3CC,SAAS,GAAGtB,aAAa,CAACsB,SAAS;MACrC,IAAIC,QAAQ,GAAGxT,SAAS,CAAC,IAAI,CAAC8E,KAAK,EAAE;QACnC2O,IAAI,EAAE,IAAI;QACV5K,IAAI,EAAE;MACR,CAAC,CAAC;MACF;MACA,IAAI6K,eAAe;MACnB,IAAId,SAAS,EAAE;QACb,IAAItT,OAAO,CAACsT,SAAS,CAAC,KAAK,QAAQ,EAAE;UACnCc,eAAe,GAAGd,SAAS;QAC7B,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;UAC1Cc,eAAe,GAAG;YAChBC,aAAa,EAAEf;UACjB,CAAC;QACH,CAAC,MAAM;UACLc,eAAe,GAAG,CAAC,CAAC;QACtB;MACF;MACA,OAAO,aAAaxT,KAAK,CAAC0T,aAAa,CAACzT,WAAW,CAAC0T,QAAQ,EAAE;QAC5DpC,KAAK,EAAE;UACLS,SAAS,EAAEA,SAAS;UACpB/B,UAAU,EAAEA,UAAU;UACtBsC,QAAQ,EAAEA,QAAQ;UAClBC,IAAI,EAAEA,IAAI;UACVC,YAAY,EAAEA,YAAY;UAC1BC,SAAS,EAAEc,eAAe;UAC1BtQ,eAAe,EAAEA,eAAe;UAChC8M,SAAS,EAAEA,SAAS;UACpB1E,aAAa,EAAEA,aAAa;UAC5BwF,QAAQ,EAAEA,QAAQ;UAClBpO,WAAW,EAAEA,WAAW;UACxBa,eAAe,EAAEA,eAAe;UAChCD,gBAAgB,EAAEA,gBAAgB;UAClCF,aAAa,EAAEA,aAAa;UAC5BC,YAAY,EAAEA,YAAY;UAC1BK,eAAe,EAAEA,eAAe;UAChCf,MAAM,EAAEA,MAAM;UACdqD,SAAS,EAAEA,SAAS;UACpBiN,mBAAmB,EAAEA,mBAAmB;UACxCjG,QAAQ,EAAEA,QAAQ;UAClB4F,cAAc,EAAEA,cAAc;UAC9BI,WAAW,EAAEA,WAAW;UACxBlJ,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BI,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzCL,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BQ,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BW,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BuB,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BiB,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCE,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;UACvCE,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzCrJ,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCmB,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCwB,cAAc,EAAE,IAAI,CAACA,cAAc;UACnCM,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCK,aAAa,EAAE,IAAI,CAACA,aAAa;UACjCI,UAAU,EAAE,IAAI,CAACA;QACnB;MACF,CAAC,EAAE,aAAajI,KAAK,CAAC0T,aAAa,CAAC,KAAK,EAAE;QACzCE,IAAI,EAAE,MAAM;QACZ3B,SAAS,EAAErS,UAAU,CAACoS,SAAS,EAAEC,SAAS,EAAEmB,aAAa,GAAGvB,WAAW,GAAG,CAAC,CAAC,EAAE1S,eAAe,CAAC0S,WAAW,EAAE,EAAE,CAACxP,MAAM,CAAC2P,SAAS,EAAE,YAAY,CAAC,EAAEG,QAAQ,CAAC,EAAEhT,eAAe,CAAC0S,WAAW,EAAE,EAAE,CAACxP,MAAM,CAAC2P,SAAS,EAAE,UAAU,CAAC,EAAEnO,OAAO,CAAC,EAAE1E,eAAe,CAAC0S,WAAW,EAAE,EAAE,CAACxP,MAAM,CAAC2P,SAAS,EAAE,iBAAiB,CAAC,EAAElO,SAAS,KAAK,IAAI,CAAC,EAAE+N,WAAW,CAAC,CAAC;QAC3UK,KAAK,EAAEmB;MACT,CAAC,EAAE,aAAarT,KAAK,CAAC0T,aAAa,CAACvT,QAAQ,EAAEjB,QAAQ,CAAC;QACrD2U,GAAG,EAAE,IAAI,CAACxP,OAAO;QACjB2N,SAAS,EAAEA,SAAS;QACpBE,KAAK,EAAEA,KAAK;QACZvJ,IAAI,EAAE/E,YAAY;QAClBkN,QAAQ,EAAEA,QAAQ;QAClBb,UAAU,EAAEA,UAAU;QACtBD,SAAS,EAAE,CAAC,CAACA,SAAS;QACtB2C,MAAM,EAAEA,MAAM;QACdmB,QAAQ,EAAE5Q,eAAe,KAAK,IAAI;QAClC2P,MAAM,EAAEA,MAAM;QACdC,UAAU,EAAEA,UAAU;QACtBC,OAAO,EAAEA,OAAO;QAChBX,SAAS,EAAEA,SAAS;QACpBvO,OAAO,EAAEA,OAAO;QAChByO,QAAQ,EAAEA,QAAQ;QAClBjC,UAAU,EAAE,IAAI,CAAC3H,aAAa,CAAC,CAAC;QAChCqF,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBI,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB0B,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBzB,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCY,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCiE,aAAa,EAAEA,aAAa;QAC5BC,QAAQ,EAAEA;MACZ,CAAC,EAAE,IAAI,CAAC3K,wBAAwB,CAAC,CAAC,EAAE8K,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD;EACF,CAAC,CAAC,EAAE,CAAC;IACH5M,GAAG,EAAE,0BAA0B;IAC/B6K,KAAK,EAAE,SAASwC,wBAAwBA,CAACnP,KAAK,EAAE0I,SAAS,EAAE;MACzD,IAAItJ,SAAS,GAAGsJ,SAAS,CAACtJ,SAAS;MACnC,IAAIoN,QAAQ,GAAG;QACbpN,SAAS,EAAEY;MACb,CAAC;MACD,SAASsM,QAAQA,CAACG,IAAI,EAAE;QACtB,OAAO,CAACrN,SAAS,IAAIqN,IAAI,IAAIzM,KAAK,IAAIZ,SAAS,IAAIA,SAAS,CAACqN,IAAI,CAAC,KAAKzM,KAAK,CAACyM,IAAI,CAAC;MACpF;MACA;MACA,IAAI1N,QAAQ;MACZ;MACA,IAAIM,UAAU,GAAGqJ,SAAS,CAACrJ,UAAU;MACrC,IAAIiN,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BjN,UAAU,GAAG7C,cAAc,CAACwD,KAAK,CAACX,UAAU,CAAC;QAC7CmN,QAAQ,CAACnN,UAAU,GAAGA,UAAU;MAClC;MACA;MACA,IAAIiN,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxBvN,QAAQ,GAAGiB,KAAK,CAACjB,QAAQ;MAC3B,CAAC,MAAM,IAAIuN,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC/BnR,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;QAClF4D,QAAQ,GAAGxC,iBAAiB,CAACyD,KAAK,CAACmC,QAAQ,CAAC;MAC9C;MACA;MACA,IAAIpD,QAAQ,EAAE;QACZyN,QAAQ,CAACzN,QAAQ,GAAGA,QAAQ;QAC5B,IAAIqQ,WAAW,GAAG/S,qBAAqB,CAAC0C,QAAQ,EAAE;UAChDM,UAAU,EAAEA;QACd,CAAC,CAAC;QACFmN,QAAQ,CAAC1O,WAAW,GAAGrD,aAAa,CAACF,eAAe,CAAC,CAAC,CAAC,EAAEkB,UAAU,EAAED,YAAY,CAAC,EAAE4T,WAAW,CAACtR,WAAW,CAAC;QAC5G;QACA,IAAIuR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC5S,iBAAiB,CAACoC,QAAQ,EAAEM,UAAU,CAAC;QACzC;MACF;MACA,IAAIvB,WAAW,GAAG0O,QAAQ,CAAC1O,WAAW,IAAI4K,SAAS,CAAC5K,WAAW;MAC/D;MACA,IAAIwO,QAAQ,CAAC,cAAc,CAAC,IAAIlN,SAAS,IAAIkN,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACzEE,QAAQ,CAACnO,YAAY,GAAG2B,KAAK,CAACwP,gBAAgB,IAAI,CAACpQ,SAAS,IAAIY,KAAK,CAACyP,mBAAmB,GAAG1T,mBAAmB,CAACiE,KAAK,CAAC3B,YAAY,EAAEP,WAAW,CAAC,GAAGkC,KAAK,CAAC3B,YAAY;MACvK,CAAC,MAAM,IAAI,CAACe,SAAS,IAAIY,KAAK,CAAC0P,gBAAgB,EAAE;QAC/C,IAAIC,gBAAgB,GAAGlV,aAAa,CAAC,CAAC,CAAC,EAAEqD,WAAW,CAAC;QACrD,OAAO6R,gBAAgB,CAAClU,UAAU,CAAC;QACnC+Q,QAAQ,CAACnO,YAAY,GAAGsD,MAAM,CAACC,IAAI,CAAC+N,gBAAgB,CAAC,CAAC1J,GAAG,CAAC,UAAUnE,GAAG,EAAE;UACvE,OAAO6N,gBAAgB,CAAC7N,GAAG,CAAC,CAACA,GAAG;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAAC1C,SAAS,IAAIY,KAAK,CAAC4P,mBAAmB,EAAE;QAClDpD,QAAQ,CAACnO,YAAY,GAAG2B,KAAK,CAACwP,gBAAgB,IAAIxP,KAAK,CAACyP,mBAAmB,GAAG1T,mBAAmB,CAACiE,KAAK,CAAC4P,mBAAmB,EAAE9R,WAAW,CAAC,GAAGkC,KAAK,CAAC4P,mBAAmB;MACvK;MACA,IAAI,CAACpD,QAAQ,CAACnO,YAAY,EAAE;QAC1B,OAAOmO,QAAQ,CAACnO,YAAY;MAC9B;MACA;MACA,IAAIU,QAAQ,IAAIyN,QAAQ,CAACnO,YAAY,EAAE;QACrC,IAAIW,YAAY,GAAGvC,eAAe,CAACsC,QAAQ,IAAI2J,SAAS,CAAC3J,QAAQ,EAAEyN,QAAQ,CAACnO,YAAY,IAAIqK,SAAS,CAACrK,YAAY,EAAEgB,UAAU,CAAC;QAC/HmN,QAAQ,CAACxN,YAAY,GAAGA,YAAY;MACtC;MACA;MACA,IAAIgB,KAAK,CAACqL,UAAU,EAAE;QACpB,IAAIiB,QAAQ,CAAC,cAAc,CAAC,EAAE;UAC5BE,QAAQ,CAACxO,YAAY,GAAGlC,gBAAgB,CAACkE,KAAK,CAAChC,YAAY,EAAEgC,KAAK,CAAC;QACrE,CAAC,MAAM,IAAI,CAACZ,SAAS,IAAIY,KAAK,CAAC6P,mBAAmB,EAAE;UAClDrD,QAAQ,CAACxO,YAAY,GAAGlC,gBAAgB,CAACkE,KAAK,CAAC6P,mBAAmB,EAAE7P,KAAK,CAAC;QAC5E;MACF;MACA;MACA,IAAIA,KAAK,CAACoL,SAAS,EAAE;QACnB,IAAI0E,gBAAgB;QACpB,IAAIxD,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC3BwD,gBAAgB,GAAG7T,gBAAgB,CAAC+D,KAAK,CAAC/B,WAAW,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,MAAM,IAAI,CAACmB,SAAS,IAAIY,KAAK,CAAC+P,kBAAkB,EAAE;UACjDD,gBAAgB,GAAG7T,gBAAgB,CAAC+D,KAAK,CAAC+P,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,MAAM,IAAIhR,QAAQ,EAAE;UACnB;UACA+Q,gBAAgB,GAAG7T,gBAAgB,CAAC+D,KAAK,CAAC/B,WAAW,CAAC,IAAI;YACxDA,WAAW,EAAEyK,SAAS,CAACzK,WAAW;YAClCC,eAAe,EAAEwK,SAAS,CAACxK;UAC7B,CAAC;QACH;QACA,IAAI4R,gBAAgB,EAAE;UACpB,IAAIE,iBAAiB,GAAGF,gBAAgB;YACtCG,qBAAqB,GAAGD,iBAAiB,CAAC/R,WAAW;YACrDA,WAAW,GAAGgS,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;YAC3EC,qBAAqB,GAAGF,iBAAiB,CAAC9R,eAAe;YACzDA,eAAe,GAAGgS,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;UACjF,IAAI,CAAClQ,KAAK,CAAC0G,aAAa,EAAE;YACxB,IAAIyJ,WAAW,GAAGhU,YAAY,CAAC8B,WAAW,EAAE,IAAI,EAAEH,WAAW,CAAC;YAC9DG,WAAW,GAAGkS,WAAW,CAAClS,WAAW;YACrCC,eAAe,GAAGiS,WAAW,CAACjS,eAAe;UAC/C;UACAsO,QAAQ,CAACvO,WAAW,GAAGA,WAAW;UAClCuO,QAAQ,CAACtO,eAAe,GAAGA,eAAe;QAC5C;MACF;MACA;MACA,IAAIoO,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BE,QAAQ,CAACrO,UAAU,GAAG6B,KAAK,CAAC7B,UAAU;MACxC;MACA,OAAOqO,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAO3P,IAAI;AACb,CAAC,CAACzB,KAAK,CAACgV,SAAS,CAAC;AAClBvT,IAAI,CAACwT,YAAY,GAAG;EAClBjD,SAAS,EAAE,SAAS;EACpBG,QAAQ,EAAE,KAAK;EACfI,QAAQ,EAAE,IAAI;EACdtC,UAAU,EAAE,IAAI;EAChBxF,QAAQ,EAAE,KAAK;EACfuF,SAAS,EAAE,KAAK;EAChBc,QAAQ,EAAE,KAAK;EACfxF,aAAa,EAAE,KAAK;EACpBoH,SAAS,EAAE,KAAK;EAChB2B,mBAAmB,EAAE,IAAI;EACzBD,gBAAgB,EAAE,KAAK;EACvBE,gBAAgB,EAAE,KAAK;EACvBE,mBAAmB,EAAE,EAAE;EACvBG,kBAAkB,EAAE,EAAE;EACtBF,mBAAmB,EAAE,EAAE;EACvBxB,mBAAmB,EAAE/S,aAAa;EAClC6F,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI;EACb,CAAC;EACDkE,YAAY,EAAE;AAChB,CAAC;AACDxI,IAAI,CAACnB,QAAQ,GAAGA,QAAQ;AACxB,eAAemB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}