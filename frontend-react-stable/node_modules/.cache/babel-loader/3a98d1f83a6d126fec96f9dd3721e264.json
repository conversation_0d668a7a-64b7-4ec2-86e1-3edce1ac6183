{"ast": null, "code": "import React from 'react';\nexport default (function (callback, buffer) {\n  var calledRef = React.useRef(false);\n  var timeoutRef = React.useRef(null);\n  function cancelTrigger() {\n    window.clearTimeout(timeoutRef.current);\n  }\n  function trigger(force) {\n    cancelTrigger();\n    if (!calledRef.current || force === true) {\n      if (callback(force) === false) {\n        // Not delay since callback cancelled self\n        return;\n      }\n      calledRef.current = true;\n      timeoutRef.current = window.setTimeout(function () {\n        calledRef.current = false;\n      }, buffer);\n    } else {\n      timeoutRef.current = window.setTimeout(function () {\n        calledRef.current = false;\n        trigger();\n      }, buffer);\n    }\n  }\n  return [trigger, function () {\n    calledRef.current = false;\n    cancelTrigger();\n  }];\n});", "map": {"version": 3, "names": ["React", "callback", "buffer", "calledRef", "useRef", "timeoutRef", "cancelTrigger", "window", "clearTimeout", "current", "trigger", "force", "setTimeout"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-align/es/hooks/useBuffer.js"], "sourcesContent": ["import React from 'react';\nexport default (function (callback, buffer) {\n  var calledRef = React.useRef(false);\n  var timeoutRef = React.useRef(null);\n\n  function cancelTrigger() {\n    window.clearTimeout(timeoutRef.current);\n  }\n\n  function trigger(force) {\n    cancelTrigger();\n\n    if (!calledRef.current || force === true) {\n      if (callback(force) === false) {\n        // Not delay since callback cancelled self\n        return;\n      }\n\n      calledRef.current = true;\n      timeoutRef.current = window.setTimeout(function () {\n        calledRef.current = false;\n      }, buffer);\n    } else {\n      timeoutRef.current = window.setTimeout(function () {\n        calledRef.current = false;\n        trigger();\n      }, buffer);\n    }\n  }\n\n  return [trigger, function () {\n    calledRef.current = false;\n    cancelTrigger();\n  }];\n});"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,gBAAgB,UAAUC,QAAQ,EAAEC,MAAM,EAAE;EAC1C,IAAIC,SAAS,GAAGH,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC;EACnC,IAAIC,UAAU,GAAGL,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EAEnC,SAASE,aAAaA,CAAA,EAAG;IACvBC,MAAM,CAACC,YAAY,CAACH,UAAU,CAACI,OAAO,CAAC;EACzC;EAEA,SAASC,OAAOA,CAACC,KAAK,EAAE;IACtBL,aAAa,CAAC,CAAC;IAEf,IAAI,CAACH,SAAS,CAACM,OAAO,IAAIE,KAAK,KAAK,IAAI,EAAE;MACxC,IAAIV,QAAQ,CAACU,KAAK,CAAC,KAAK,KAAK,EAAE;QAC7B;QACA;MACF;MAEAR,SAAS,CAACM,OAAO,GAAG,IAAI;MACxBJ,UAAU,CAACI,OAAO,GAAGF,MAAM,CAACK,UAAU,CAAC,YAAY;QACjDT,SAAS,CAACM,OAAO,GAAG,KAAK;MAC3B,CAAC,EAAEP,MAAM,CAAC;IACZ,CAAC,MAAM;MACLG,UAAU,CAACI,OAAO,GAAGF,MAAM,CAACK,UAAU,CAAC,YAAY;QACjDT,SAAS,CAACM,OAAO,GAAG,KAAK;QACzBC,OAAO,CAAC,CAAC;MACX,CAAC,EAAER,MAAM,CAAC;IACZ;EACF;EAEA,OAAO,CAACQ,OAAO,EAAE,YAAY;IAC3BP,SAAS,CAACM,OAAO,GAAG,KAAK;IACzBH,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}