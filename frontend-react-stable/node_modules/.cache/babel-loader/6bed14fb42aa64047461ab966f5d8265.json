{"ast": null, "code": "/* parser generated by jison 0.6.1-215 */\n\n/*\n * Returns a Parser object of the following structure:\n *\n *  Parser: {\n *    yy: {}     The so-called \"shared state\" or rather the *source* of it;\n *               the real \"shared state\" `yy` passed around to\n *               the rule actions, etc. is a derivative/copy of this one,\n *               not a direct reference!\n *  }\n *\n *  Parser.prototype: {\n *    yy: {},\n *    EOF: 1,\n *    TERROR: 2,\n *\n *    trace: function(errorMessage, ...),\n *\n *    JisonParserError: function(msg, hash),\n *\n *    quoteName: function(name),\n *               Helper function which can be overridden by user code later on: put suitable\n *               quotes around literal IDs in a description string.\n *\n *    originalQuoteName: function(name),\n *               The basic quoteName handler provided by JISON.\n *               `cleanupAfterParse()` will clean up and reset `quoteName()` to reference this function\n *               at the end of the `parse()`.\n *\n *    describeSymbol: function(symbol),\n *               Return a more-or-less human-readable description of the given symbol, when\n *               available, or the symbol itself, serving as its own 'description' for lack\n *               of something better to serve up.\n *\n *               Return NULL when the symbol is unknown to the parser.\n *\n *    symbols_: {associative list: name ==> number},\n *    terminals_: {associative list: number ==> name},\n *    nonterminals: {associative list: rule-name ==> {associative list: number ==> rule-alt}},\n *    terminal_descriptions_: (if there are any) {associative list: number ==> description},\n *    productions_: [...],\n *\n *    performAction: function parser__performAction(yytext, yyleng, yylineno, yyloc, yystate, yysp, yyvstack, yylstack, yystack, yysstack),\n *\n *               The function parameters and `this` have the following value/meaning:\n *               - `this`    : reference to the `yyval` internal object, which has members (`$` and `_$`)\n *                             to store/reference the rule value `$$` and location info `@$`.\n *\n *                 One important thing to note about `this` a.k.a. `yyval`: every *reduce* action gets\n *                 to see the same object via the `this` reference, i.e. if you wish to carry custom\n *                 data from one reduce action through to the next within a single parse run, then you\n *                 may get nasty and use `yyval` a.k.a. `this` for storing you own semi-permanent data.\n *\n *                 `this.yy` is a direct reference to the `yy` shared state object.\n *\n *                 `%parse-param`-specified additional `parse()` arguments have been added to this `yy`\n *                 object at `parse()` start and are therefore available to the action code via the\n *                 same named `yy.xxxx` attributes (where `xxxx` represents a identifier name from\n *                 the %parse-param` list.\n *\n *               - `yytext`  : reference to the lexer value which belongs to the last lexer token used\n *                             to match this rule. This is *not* the look-ahead token, but the last token\n *                             that's actually part of this rule.\n *\n *                 Formulated another way, `yytext` is the value of the token immediately preceeding\n *                 the current look-ahead token.\n *                 Caveats apply for rules which don't require look-ahead, such as epsilon rules.\n *\n *               - `yyleng`  : ditto as `yytext`, only now for the lexer.yyleng value.\n *\n *               - `yylineno`: ditto as `yytext`, only now for the lexer.yylineno value.\n *\n *               - `yyloc`   : ditto as `yytext`, only now for the lexer.yylloc lexer token location info.\n *\n *                               WARNING: since jison 0.4.18-186 this entry may be NULL/UNDEFINED instead\n *                               of an empty object when no suitable location info can be provided.\n *\n *               - `yystate` : the current parser state number, used internally for dispatching and\n *                               executing the action code chunk matching the rule currently being reduced.\n *\n *               - `yysp`    : the current state stack position (a.k.a. 'stack pointer')\n *\n *                 This one comes in handy when you are going to do advanced things to the parser\n *                 stacks, all of which are accessible from your action code (see the next entries below).\n *\n *                 Also note that you can access this and other stack index values using the new double-hash\n *                 syntax, i.e. `##$ === ##0 === yysp`, while `##1` is the stack index for all things\n *                 related to the first rule term, just like you have `$1`, `@1` and `#1`.\n *                 This is made available to write very advanced grammar action rules, e.g. when you want\n *                 to investigate the parse state stack in your action code, which would, for example,\n *                 be relevant when you wish to implement error diagnostics and reporting schemes similar\n *                 to the work described here:\n *\n *                 + Pottier, F., 2016. Reachability and error diagnosis in LR(1) automata.\n *                   In Journées Francophones des Languages Applicatifs.\n *\n *                 + Jeffery, C.L., 2003. Generating LR syntax error messages from examples.\n *                   ACM Transactions on Programming Languages and Systems (TOPLAS), 25(5), pp.631–640.\n *\n *               - `yyrulelength`: the current rule's term count, i.e. the number of entries occupied on the stack.\n *\n *                 This one comes in handy when you are going to do advanced things to the parser\n *                 stacks, all of which are accessible from your action code (see the next entries below).\n *\n *               - `yyvstack`: reference to the parser value stack. Also accessed via the `$1` etc.\n *                             constructs.\n *\n *               - `yylstack`: reference to the parser token location stack. Also accessed via\n *                             the `@1` etc. constructs.\n *\n *                             WARNING: since jison 0.4.18-186 this array MAY contain slots which are\n *                             UNDEFINED rather than an empty (location) object, when the lexer/parser\n *                             action code did not provide a suitable location info object when such a\n *                             slot was filled!\n *\n *               - `yystack` : reference to the parser token id stack. Also accessed via the\n *                             `#1` etc. constructs.\n *\n *                 Note: this is a bit of a **white lie** as we can statically decode any `#n` reference to\n *                 its numeric token id value, hence that code wouldn't need the `yystack` but *you* might\n *                 want access this array for your own purposes, such as error analysis as mentioned above!\n *\n *                 Note that this stack stores the current stack of *tokens*, that is the sequence of\n *                 already parsed=reduced *nonterminals* (tokens representing rules) and *terminals*\n *                 (lexer tokens *shifted* onto the stack until the rule they belong to is found and\n *                 *reduced*.\n *\n *               - `yysstack`: reference to the parser state stack. This one carries the internal parser\n *                             *states* such as the one in `yystate`, which are used to represent\n *                             the parser state machine in the *parse table*. *Very* *internal* stuff,\n *                             what can I say? If you access this one, you're clearly doing wicked things\n *\n *               - `...`     : the extra arguments you specified in the `%parse-param` statement in your\n *                             grammar definition file.\n *\n *    table: [...],\n *               State transition table\n *               ----------------------\n *\n *               index levels are:\n *               - `state`  --> hash table\n *               - `symbol` --> action (number or array)\n *\n *                 If the `action` is an array, these are the elements' meaning:\n *                 - index [0]: 1 = shift, 2 = reduce, 3 = accept\n *                 - index [1]: GOTO `state`\n *\n *                 If the `action` is a number, it is the GOTO `state`\n *\n *    defaultActions: {...},\n *\n *    parseError: function(str, hash, ExceptionClass),\n *    yyError: function(str, ...),\n *    yyRecovering: function(),\n *    yyErrOk: function(),\n *    yyClearIn: function(),\n *\n *    constructParseErrorInfo: function(error_message, exception_object, expected_token_set, is_recoverable),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               Produces a new errorInfo 'hash object' which can be passed into `parseError()`.\n *               See it's use in this parser kernel in many places; example usage:\n *\n *                   var infoObj = parser.constructParseErrorInfo('fail!', null,\n *                                     parser.collect_expected_token_set(state), true);\n *                   var retVal = parser.parseError(infoObj.errStr, infoObj, parser.JisonParserError);\n *\n *    originalParseError: function(str, hash, ExceptionClass),\n *               The basic `parseError` handler provided by JISON.\n *               `cleanupAfterParse()` will clean up and reset `parseError()` to reference this function\n *               at the end of the `parse()`.\n *\n *    options: { ... parser %options ... },\n *\n *    parse: function(input[, args...]),\n *               Parse the given `input` and return the parsed value (or `true` when none was provided by\n *               the root action, in which case the parser is acting as a *matcher*).\n *               You MAY use the additional `args...` parameters as per `%parse-param` spec of this grammar:\n *               these extra `args...` are added verbatim to the `yy` object reference as member variables.\n *\n *               WARNING:\n *               Parser's additional `args...` parameters (via `%parse-param`) MAY conflict with\n *               any attributes already added to `yy` by the jison run-time;\n *               when such a collision is detected an exception is thrown to prevent the generated run-time\n *               from silently accepting this confusing and potentially hazardous situation!\n *\n *               The lexer MAY add its own set of additional parameters (via the `%parse-param` line in\n *               the lexer section of the grammar spec): these will be inserted in the `yy` shared state\n *               object and any collision with those will be reported by the lexer via a thrown exception.\n *\n *    cleanupAfterParse: function(resultValue, invoke_post_methods, do_not_nuke_errorinfos),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               This helper API is invoked at the end of the `parse()` call, unless an exception was thrown\n *               and `%options no-try-catch` has been defined for this grammar: in that case this helper MAY\n *               be invoked by calling user code to ensure the `post_parse` callbacks are invoked and\n *               the internal parser gets properly garbage collected under these particular circumstances.\n *\n *    yyMergeLocationInfo: function(first_index, last_index, first_yylloc, last_yylloc, dont_look_back),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               This helper API can be invoked to calculate a spanning `yylloc` location info object.\n *\n *               Note: %epsilon rules MAY specify no `first_index` and `first_yylloc`, in which case\n *               this function will attempt to obtain a suitable location marker by inspecting the location stack\n *               backwards.\n *\n *               For more info see the documentation comment further below, immediately above this function's\n *               implementation.\n *\n *    lexer: {\n *        yy: {...},           A reference to the so-called \"shared state\" `yy` once\n *                             received via a call to the `.setInput(input, yy)` lexer API.\n *        EOF: 1,\n *        ERROR: 2,\n *        JisonLexerError: function(msg, hash),\n *        parseError: function(str, hash, ExceptionClass),\n *        setInput: function(input, [yy]),\n *        input: function(),\n *        unput: function(str),\n *        more: function(),\n *        reject: function(),\n *        less: function(n),\n *        pastInput: function(n),\n *        upcomingInput: function(n),\n *        showPosition: function(),\n *        test_match: function(regex_match_array, rule_index, ...),\n *        next: function(...),\n *        lex: function(...),\n *        begin: function(condition),\n *        pushState: function(condition),\n *        popState: function(),\n *        topState: function(),\n *        _currentRules: function(),\n *        stateStackSize: function(),\n *        cleanupAfterLex: function()\n *\n *        options: { ... lexer %options ... },\n *\n *        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START, ...),\n *        rules: [...],\n *        conditions: {associative list: name ==> set},\n *    }\n *  }\n *\n *\n *  token location info (@$, _$, etc.): {\n *    first_line: n,\n *    last_line: n,\n *    first_column: n,\n *    last_column: n,\n *    range: [start_number, end_number]\n *               (where the numbers are indexes into the input string, zero-based)\n *  }\n *\n * ---\n *\n * The `parseError` function receives a 'hash' object with these members for lexer and\n * parser errors:\n *\n *  {\n *    text:        (matched text)\n *    token:       (the produced terminal token, if any)\n *    token_id:    (the produced terminal token numeric ID, if any)\n *    line:        (yylineno)\n *    loc:         (yylloc)\n *  }\n *\n * parser (grammar) errors will also provide these additional members:\n *\n *  {\n *    expected:    (array describing the set of expected tokens;\n *                  may be UNDEFINED when we cannot easily produce such a set)\n *    state:       (integer (or array when the table includes grammar collisions);\n *                  represents the current internal state of the parser kernel.\n *                  can, for example, be used to pass to the `collect_expected_token_set()`\n *                  API to obtain the expected token set)\n *    action:      (integer; represents the current internal action which will be executed)\n *    new_state:   (integer; represents the next/planned internal state, once the current\n *                  action has executed)\n *    recoverable: (boolean: TRUE when the parser MAY have an error recovery rule\n *                  available for this particular error)\n *    state_stack: (array: the current parser LALR/LR internal state stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    value_stack: (array: the current parser LALR/LR internal `$$` value stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    location_stack: (array: the current parser LALR/LR internal location stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    yy:          (object: the current parser internal \"shared state\" `yy`\n *                  as is also available in the rule actions; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    lexer:       (reference to the current lexer instance used by the parser)\n *    parser:      (reference to the current parser instance)\n *  }\n *\n * while `this` will reference the current parser instance.\n *\n * When `parseError` is invoked by the lexer, `this` will still reference the related *parser*\n * instance, while these additional `hash` fields will also be provided:\n *\n *  {\n *    lexer:       (reference to the current lexer instance which reported the error)\n *  }\n *\n * When `parseError` is invoked by the parser due to a **JavaScript exception** being fired\n * from either the parser or lexer, `this` will still reference the related *parser*\n * instance, while these additional `hash` fields will also be provided:\n *\n *  {\n *    exception:   (reference to the exception thrown)\n *  }\n *\n * Please do note that in the latter situation, the `expected` field will be omitted as\n * this type of failure is assumed not to be due to *parse errors* but rather due to user\n * action code in either parser or lexer failing unexpectedly.\n *\n * ---\n *\n * You can specify parser options by setting / modifying the `.yy` object of your Parser instance.\n * These options are available:\n *\n * ### options which are global for all parser instances\n *\n *  Parser.pre_parse: function(yy)\n *                 optional: you can specify a pre_parse() function in the chunk following\n *                 the grammar, i.e. after the last `%%`.\n *  Parser.post_parse: function(yy, retval, parseInfo) { return retval; }\n *                 optional: you can specify a post_parse() function in the chunk following\n *                 the grammar, i.e. after the last `%%`. When it does not return any value,\n *                 the parser will return the original `retval`.\n *\n * ### options which can be set up per parser instance\n *\n *  yy: {\n *      pre_parse:  function(yy)\n *                 optional: is invoked before the parse cycle starts (and before the first\n *                 invocation of `lex()`) but immediately after the invocation of\n *                 `parser.pre_parse()`).\n *      post_parse: function(yy, retval, parseInfo) { return retval; }\n *                 optional: is invoked when the parse terminates due to success ('accept')\n *                 or failure (even when exceptions are thrown).\n *                 `retval` contains the return value to be produced by `Parser.parse()`;\n *                 this function can override the return value by returning another.\n *                 When it does not return any value, the parser will return the original\n *                 `retval`.\n *                 This function is invoked immediately before `parser.post_parse()`.\n *\n *      parseError: function(str, hash, ExceptionClass)\n *                 optional: overrides the default `parseError` function.\n *      quoteName: function(name),\n *                 optional: overrides the default `quoteName` function.\n *  }\n *\n *  parser.lexer.options: {\n *      pre_lex:  function()\n *                 optional: is invoked before the lexer is invoked to produce another token.\n *                 `this` refers to the Lexer object.\n *      post_lex: function(token) { return token; }\n *                 optional: is invoked when the lexer has produced a token `token`;\n *                 this function can override the returned token value by returning another.\n *                 When it does not return any (truthy) value, the lexer will return\n *                 the original `token`.\n *                 `this` refers to the Lexer object.\n *\n *      ranges: boolean\n *                 optional: `true` ==> token location info will include a .range[] member.\n *      flex: boolean\n *                 optional: `true` ==> flex-like lexing behaviour where the rules are tested\n *                 exhaustively to find the longest match.\n *      backtrack_lexer: boolean\n *                 optional: `true` ==> lexer regexes are tested in order and for invoked;\n *                 the lexer terminates the scan when a token is returned by the action code.\n *      xregexp: boolean\n *                 optional: `true` ==> lexer rule regexes are \"extended regex format\" requiring the\n *                 `XRegExp` library. When this `%option` has not been specified at compile time, all lexer\n *                 rule regexes have been written as standard JavaScript RegExp expressions.\n *  }\n */\n\nvar parser = function () {\n  // See also:\n  // http://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript/#35881508\n  // but we keep the prototype.constructor and prototype.name assignment lines too for compatibility\n  // with userland code which might access the derived class in a 'classic' way.\n  function JisonParserError(msg, hash) {\n    Object.defineProperty(this, 'name', {\n      enumerable: false,\n      writable: false,\n      value: 'JisonParserError'\n    });\n    if (msg == null) msg = '???';\n    Object.defineProperty(this, 'message', {\n      enumerable: false,\n      writable: true,\n      value: msg\n    });\n    this.hash = hash;\n    var stacktrace;\n    if (hash && hash.exception instanceof Error) {\n      var ex2 = hash.exception;\n      this.message = ex2.message || msg;\n      stacktrace = ex2.stack;\n    }\n    if (!stacktrace) {\n      if (Error.hasOwnProperty('captureStackTrace')) {\n        // V8/Chrome engine\n        Error.captureStackTrace(this, this.constructor);\n      } else {\n        stacktrace = new Error(msg).stack;\n      }\n    }\n    if (stacktrace) {\n      Object.defineProperty(this, 'stack', {\n        enumerable: false,\n        writable: false,\n        value: stacktrace\n      });\n    }\n  }\n  if (typeof Object.setPrototypeOf === 'function') {\n    Object.setPrototypeOf(JisonParserError.prototype, Error.prototype);\n  } else {\n    JisonParserError.prototype = Object.create(Error.prototype);\n  }\n  JisonParserError.prototype.constructor = JisonParserError;\n  JisonParserError.prototype.name = 'JisonParserError';\n\n  // helper: reconstruct the productions[] table\n  function bp(s) {\n    var rv = [];\n    var p = s.pop;\n    var r = s.rule;\n    for (var i = 0, l = p.length; i < l; i++) {\n      rv.push([p[i], r[i]]);\n    }\n    return rv;\n  }\n\n  // helper: reconstruct the defaultActions[] table\n  function bda(s) {\n    var rv = {};\n    var d = s.idx;\n    var g = s.goto;\n    for (var i = 0, l = d.length; i < l; i++) {\n      var j = d[i];\n      rv[j] = g[i];\n    }\n    return rv;\n  }\n\n  // helper: reconstruct the 'goto' table\n  function bt(s) {\n    var rv = [];\n    var d = s.len;\n    var y = s.symbol;\n    var t = s.type;\n    var a = s.state;\n    var m = s.mode;\n    var g = s.goto;\n    for (var i = 0, l = d.length; i < l; i++) {\n      var n = d[i];\n      var q = {};\n      for (var j = 0; j < n; j++) {\n        var z = y.shift();\n        switch (t.shift()) {\n          case 2:\n            q[z] = [m.shift(), g.shift()];\n            break;\n          case 0:\n            q[z] = a.shift();\n            break;\n          default:\n            // type === 1: accept\n            q[z] = [3];\n        }\n      }\n      rv.push(q);\n    }\n    return rv;\n  }\n\n  // helper: runlength encoding with increment step: code, length: step (default step = 0)\n  // `this` references an array\n  function s(c, l, a) {\n    a = a || 0;\n    for (var i = 0; i < l; i++) {\n      this.push(c);\n      c += a;\n    }\n  }\n\n  // helper: duplicate sequence from *relative* offset and length.\n  // `this` references an array\n  function c(i, l) {\n    i = this.length - i;\n    for (l += i; i < l; i++) {\n      this.push(this[i]);\n    }\n  }\n\n  // helper: unpack an array using helpers and data, all passed in an array argument 'a'.\n  function u(a) {\n    var rv = [];\n    for (var i = 0, l = a.length; i < l; i++) {\n      var e = a[i];\n      // Is this entry a helper function?\n      if (typeof e === 'function') {\n        i++;\n        e.apply(rv, a[i]);\n      } else {\n        rv.push(e);\n      }\n    }\n    return rv;\n  }\n  var parser = {\n    // Code Generator Information Report\n    // ---------------------------------\n    //\n    // Options:\n    //\n    //   default action mode: ............. [\"classic\",\"merge\"]\n    //   test-compile action mode: ........ \"parser:*,lexer:*\"\n    //   try..catch: ...................... true\n    //   default resolve on conflict: ..... true\n    //   on-demand look-ahead: ............ false\n    //   error recovery token skip maximum: 3\n    //   yyerror in parse actions is: ..... NOT recoverable,\n    //   yyerror in lexer actions and other non-fatal lexer are:\n    //   .................................. NOT recoverable,\n    //   debug grammar/output: ............ false\n    //   has partial LR conflict upgrade:   true\n    //   rudimentary token-stack support:   false\n    //   parser table compression mode: ... 2\n    //   export debug tables: ............. false\n    //   export *all* tables: ............. false\n    //   module type: ..................... commonjs\n    //   parser engine type: .............. lalr\n    //   output main() in the module: ..... true\n    //   has user-specified main(): ....... false\n    //   has user-specified require()/import modules for main():\n    //   .................................. false\n    //   number of expected conflicts: .... 0\n    //\n    //\n    // Parser Analysis flags:\n    //\n    //   no significant actions (parser is a language matcher only):\n    //   .................................. false\n    //   uses yyleng: ..................... false\n    //   uses yylineno: ................... false\n    //   uses yytext: ..................... false\n    //   uses yylloc: ..................... false\n    //   uses ParseError API: ............. false\n    //   uses YYERROR: .................... false\n    //   uses YYRECOVERING: ............... false\n    //   uses YYERROK: .................... false\n    //   uses YYCLEARIN: .................. false\n    //   tracks rule values: .............. true\n    //   assigns rule values: ............. true\n    //   uses location tracking: .......... false\n    //   assigns location: ................ false\n    //   uses yystack: .................... false\n    //   uses yysstack: ................... false\n    //   uses yysp: ....................... true\n    //   uses yyrulelength: ............... false\n    //   uses yyMergeLocationInfo API: .... false\n    //   has error recovery: .............. false\n    //   has error reporting: ............. false\n    //\n    // --------- END OF REPORT -----------\n\n    trace: function no_op_trace() {},\n    JisonParserError: JisonParserError,\n    yy: {},\n    options: {\n      type: \"lalr\",\n      hasPartialLrUpgradeOnConflict: true,\n      errorRecoveryTokenDiscardCount: 3\n    },\n    symbols_: {\n      \"$accept\": 0,\n      \"$end\": 1,\n      \"ADD\": 3,\n      \"ANGLE\": 16,\n      \"CHS\": 22,\n      \"COMMA\": 14,\n      \"CSS_CPROP\": 13,\n      \"CSS_VAR\": 12,\n      \"DIV\": 6,\n      \"EMS\": 20,\n      \"EOF\": 1,\n      \"EXS\": 21,\n      \"FREQ\": 18,\n      \"LENGTH\": 15,\n      \"LPAREN\": 7,\n      \"MUL\": 5,\n      \"NESTED_CALC\": 9,\n      \"NUMBER\": 11,\n      \"PERCENTAGE\": 28,\n      \"PREFIX\": 10,\n      \"REMS\": 23,\n      \"RES\": 19,\n      \"RPAREN\": 8,\n      \"SUB\": 4,\n      \"TIME\": 17,\n      \"VHS\": 24,\n      \"VMAXS\": 27,\n      \"VMINS\": 26,\n      \"VWS\": 25,\n      \"css_value\": 33,\n      \"css_variable\": 32,\n      \"error\": 2,\n      \"expression\": 29,\n      \"math_expression\": 30,\n      \"value\": 31\n    },\n    terminals_: {\n      1: \"EOF\",\n      2: \"error\",\n      3: \"ADD\",\n      4: \"SUB\",\n      5: \"MUL\",\n      6: \"DIV\",\n      7: \"LPAREN\",\n      8: \"RPAREN\",\n      9: \"NESTED_CALC\",\n      10: \"PREFIX\",\n      11: \"NUMBER\",\n      12: \"CSS_VAR\",\n      13: \"CSS_CPROP\",\n      14: \"COMMA\",\n      15: \"LENGTH\",\n      16: \"ANGLE\",\n      17: \"TIME\",\n      18: \"FREQ\",\n      19: \"RES\",\n      20: \"EMS\",\n      21: \"EXS\",\n      22: \"CHS\",\n      23: \"REMS\",\n      24: \"VHS\",\n      25: \"VWS\",\n      26: \"VMINS\",\n      27: \"VMAXS\",\n      28: \"PERCENTAGE\"\n    },\n    TERROR: 2,\n    EOF: 1,\n    // internals: defined here so the object *structure* doesn't get modified by parse() et al,\n    // thus helping JIT compilers like Chrome V8.\n    originalQuoteName: null,\n    originalParseError: null,\n    cleanupAfterParse: null,\n    constructParseErrorInfo: null,\n    yyMergeLocationInfo: null,\n    __reentrant_call_depth: 0,\n    // INTERNAL USE ONLY\n    __error_infos: [],\n    // INTERNAL USE ONLY: the set of parseErrorInfo objects created since the last cleanup\n    __error_recovery_infos: [],\n    // INTERNAL USE ONLY: the set of parseErrorInfo objects created since the last cleanup\n\n    // APIs which will be set up depending on user action code analysis:\n    //yyRecovering: 0,\n    //yyErrOk: 0,\n    //yyClearIn: 0,\n\n    // Helper APIs\n    // -----------\n\n    // Helper function which can be overridden by user code later on: put suitable quotes around\n    // literal IDs in a description string.\n    quoteName: function parser_quoteName(id_str) {\n      return '\"' + id_str + '\"';\n    },\n    // Return the name of the given symbol (terminal or non-terminal) as a string, when available.\n    //\n    // Return NULL when the symbol is unknown to the parser.\n    getSymbolName: function parser_getSymbolName(symbol) {\n      if (this.terminals_[symbol]) {\n        return this.terminals_[symbol];\n      }\n\n      // Otherwise... this might refer to a RULE token i.e. a non-terminal: see if we can dig that one up.\n      //\n      // An example of this may be where a rule's action code contains a call like this:\n      //\n      //      parser.getSymbolName(#$)\n      //\n      // to obtain a human-readable name of the current grammar rule.\n      var s = this.symbols_;\n      for (var key in s) {\n        if (s[key] === symbol) {\n          return key;\n        }\n      }\n      return null;\n    },\n    // Return a more-or-less human-readable description of the given symbol, when available,\n    // or the symbol itself, serving as its own 'description' for lack of something better to serve up.\n    //\n    // Return NULL when the symbol is unknown to the parser.\n    describeSymbol: function parser_describeSymbol(symbol) {\n      if (symbol !== this.EOF && this.terminal_descriptions_ && this.terminal_descriptions_[symbol]) {\n        return this.terminal_descriptions_[symbol];\n      } else if (symbol === this.EOF) {\n        return 'end of input';\n      }\n      var id = this.getSymbolName(symbol);\n      if (id) {\n        return this.quoteName(id);\n      }\n      return null;\n    },\n    // Produce a (more or less) human-readable list of expected tokens at the point of failure.\n    //\n    // The produced list may contain token or token set descriptions instead of the tokens\n    // themselves to help turning this output into something that easier to read by humans\n    // unless `do_not_describe` parameter is set, in which case a list of the raw, *numeric*,\n    // expected terminals and nonterminals is produced.\n    //\n    // The returned list (array) will not contain any duplicate entries.\n    collect_expected_token_set: function parser_collect_expected_token_set(state, do_not_describe) {\n      var TERROR = this.TERROR;\n      var tokenset = [];\n      var check = {};\n      // Has this (error?) state been outfitted with a custom expectations description text for human consumption?\n      // If so, use that one instead of the less palatable token set.\n      if (!do_not_describe && this.state_descriptions_ && this.state_descriptions_[state]) {\n        return [this.state_descriptions_[state]];\n      }\n      for (var p in this.table[state]) {\n        p = +p;\n        if (p !== TERROR) {\n          var d = do_not_describe ? p : this.describeSymbol(p);\n          if (d && !check[d]) {\n            tokenset.push(d);\n            check[d] = true; // Mark this token description as already mentioned to prevent outputting duplicate entries.\n          }\n        }\n      }\n      return tokenset;\n    },\n    productions_: bp({\n      pop: u([29, s, [30, 10], 31, 31, 32, 32, s, [33, 15]]),\n      rule: u([2, s, [3, 5], 4, 7, s, [1, 4], 2, 4, 6, s, [1, 14], 2])\n    }),\n    performAction: function parser__PerformAction(yystate /* action[1] */, yysp, yyvstack) {\n      /* this == yyval */\n\n      // the JS engine itself can go and remove these statements when `yy` turns out to be unused in any action code!\n      var yy = this.yy;\n      var yyparser = yy.parser;\n      var yylexer = yy.lexer;\n      switch (yystate) {\n        case 0:\n          /*! Production::    $accept : expression $end */\n\n          // default action (generated by JISON mode classic/merge :: 1,VT,VA,-,-,-,-,-,-):\n          this.$ = yyvstack[yysp - 1];\n          // END of default action (generated by JISON mode classic/merge :: 1,VT,VA,-,-,-,-,-,-)\n          break;\n        case 1:\n          /*! Production::    expression : math_expression EOF */\n\n          // default action (generated by JISON mode classic/merge :: 2,VT,VA,-,-,-,-,-,-):\n          this.$ = yyvstack[yysp - 1];\n          // END of default action (generated by JISON mode classic/merge :: 2,VT,VA,-,-,-,-,-,-)\n\n          return yyvstack[yysp - 1];\n          break;\n        case 2:\n        /*! Production::    math_expression : math_expression ADD math_expression */\n        case 3:\n        /*! Production::    math_expression : math_expression SUB math_expression */\n        case 4:\n        /*! Production::    math_expression : math_expression MUL math_expression */\n        case 5:\n          /*! Production::    math_expression : math_expression DIV math_expression */\n\n          this.$ = {\n            type: 'MathExpression',\n            operator: yyvstack[yysp - 1],\n            left: yyvstack[yysp - 2],\n            right: yyvstack[yysp]\n          };\n          break;\n        case 6:\n          /*! Production::    math_expression : LPAREN math_expression RPAREN */\n\n          this.$ = yyvstack[yysp - 1];\n          break;\n        case 7:\n          /*! Production::    math_expression : NESTED_CALC LPAREN math_expression RPAREN */\n\n          this.$ = {\n            type: 'Calc',\n            value: yyvstack[yysp - 1]\n          };\n          break;\n        case 8:\n          /*! Production::    math_expression : SUB PREFIX SUB NESTED_CALC LPAREN math_expression RPAREN */\n\n          this.$ = {\n            type: 'Calc',\n            value: yyvstack[yysp - 1],\n            prefix: yyvstack[yysp - 5]\n          };\n          break;\n        case 9:\n        /*! Production::    math_expression : css_variable */\n        case 10:\n        /*! Production::    math_expression : css_value */\n        case 11:\n          /*! Production::    math_expression : value */\n\n          this.$ = yyvstack[yysp];\n          break;\n        case 12:\n          /*! Production::    value : NUMBER */\n\n          this.$ = {\n            type: 'Value',\n            value: parseFloat(yyvstack[yysp])\n          };\n          break;\n        case 13:\n          /*! Production::    value : SUB NUMBER */\n\n          this.$ = {\n            type: 'Value',\n            value: parseFloat(yyvstack[yysp]) * -1\n          };\n          break;\n        case 14:\n          /*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP RPAREN */\n\n          this.$ = {\n            type: 'CssVariable',\n            value: yyvstack[yysp - 1]\n          };\n          break;\n        case 15:\n          /*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP COMMA math_expression RPAREN */\n\n          this.$ = {\n            type: 'CssVariable',\n            value: yyvstack[yysp - 3],\n            fallback: yyvstack[yysp - 1]\n          };\n          break;\n        case 16:\n          /*! Production::    css_value : LENGTH */\n\n          this.$ = {\n            type: 'LengthValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: /[a-z]+/.exec(yyvstack[yysp])[0]\n          };\n          break;\n        case 17:\n          /*! Production::    css_value : ANGLE */\n\n          this.$ = {\n            type: 'AngleValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: /[a-z]+/.exec(yyvstack[yysp])[0]\n          };\n          break;\n        case 18:\n          /*! Production::    css_value : TIME */\n\n          this.$ = {\n            type: 'TimeValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: /[a-z]+/.exec(yyvstack[yysp])[0]\n          };\n          break;\n        case 19:\n          /*! Production::    css_value : FREQ */\n\n          this.$ = {\n            type: 'FrequencyValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: /[a-z]+/.exec(yyvstack[yysp])[0]\n          };\n          break;\n        case 20:\n          /*! Production::    css_value : RES */\n\n          this.$ = {\n            type: 'ResolutionValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: /[a-z]+/.exec(yyvstack[yysp])[0]\n          };\n          break;\n        case 21:\n          /*! Production::    css_value : EMS */\n\n          this.$ = {\n            type: 'EmValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'em'\n          };\n          break;\n        case 22:\n          /*! Production::    css_value : EXS */\n\n          this.$ = {\n            type: 'ExValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'ex'\n          };\n          break;\n        case 23:\n          /*! Production::    css_value : CHS */\n\n          this.$ = {\n            type: 'ChValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'ch'\n          };\n          break;\n        case 24:\n          /*! Production::    css_value : REMS */\n\n          this.$ = {\n            type: 'RemValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'rem'\n          };\n          break;\n        case 25:\n          /*! Production::    css_value : VHS */\n\n          this.$ = {\n            type: 'VhValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'vh'\n          };\n          break;\n        case 26:\n          /*! Production::    css_value : VWS */\n\n          this.$ = {\n            type: 'VwValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'vw'\n          };\n          break;\n        case 27:\n          /*! Production::    css_value : VMINS */\n\n          this.$ = {\n            type: 'VminValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'vmin'\n          };\n          break;\n        case 28:\n          /*! Production::    css_value : VMAXS */\n\n          this.$ = {\n            type: 'VmaxValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: 'vmax'\n          };\n          break;\n        case 29:\n          /*! Production::    css_value : PERCENTAGE */\n\n          this.$ = {\n            type: 'PercentageValue',\n            value: parseFloat(yyvstack[yysp]),\n            unit: '%'\n          };\n          break;\n        case 30:\n          /*! Production::    css_value : SUB css_value */\n\n          var prev = yyvstack[yysp];\n          prev.value *= -1;\n          this.$ = prev;\n          break;\n      }\n    },\n    table: bt({\n      len: u([24, 1, 5, 23, 1, 18, s, [0, 3], 1, s, [0, 16], s, [23, 4], c, [28, 3], 0, 0, 16, 1, 6, 6, s, [0, 3], 5, 1, 2, c, [37, 3], c, [20, 3], 5, 0, 0]),\n      symbol: u([4, 7, 9, 11, 12, s, [15, 19, 1], 1, 1, s, [3, 4, 1], c, [30, 19], c, [29, 4], 7, 4, 10, 11, c, [22, 14], c, [19, 3], c, [43, 22], c, [23, 69], c, [139, 4], 8, c, [51, 24], 4, c, [138, 15], 13, c, [186, 5], 8, c, [6, 6], c, [5, 5], 9, 8, 14, c, [159, 47], c, [60, 10]]),\n      type: u([s, [2, 19], s, [0, 5], 1, s, [2, 24], s, [0, 4], c, [22, 19], c, [43, 42], c, [23, 70], c, [28, 25], c, [45, 25], c, [113, 54]]),\n      state: u([1, 2, 8, 6, 7, 30, c, [4, 3], 33, 37, c, [5, 3], 38, c, [4, 3], 39, c, [4, 3], 40, c, [4, 3], 42, c, [21, 4], 50, c, [5, 3], 51, c, [4, 3]]),\n      mode: u([s, [1, 179], s, [2, 3], c, [5, 5], c, [6, 4], s, [1, 57]]),\n      goto: u([5, 3, 4, 24, s, [9, 15, 1], s, [25, 5, 1], c, [24, 19], 31, 35, 32, 34, c, [18, 14], 36, c, [38, 19], c, [19, 57], c, [118, 4], 41, c, [24, 19], 43, 35, c, [16, 14], 44, s, [2, 3], 28, 29, 2, s, [3, 3], 28, 29, 3, c, [53, 4], s, [45, 5, 1], c, [100, 42], 52, c, [5, 4], 53])\n    }),\n    defaultActions: bda({\n      idx: u([6, 7, 8, s, [10, 16, 1], 33, 34, 39, 40, 41, 45, 47, 52, 53]),\n      goto: u([9, 10, 11, s, [16, 14, 1], 12, 1, 30, 13, s, [4, 4, 1], 14, 15, 8])\n    }),\n    parseError: function parseError(str, hash, ExceptionClass) {\n      if (hash.recoverable) {\n        if (typeof this.trace === 'function') {\n          this.trace(str);\n        }\n        hash.destroy(); // destroy... well, *almost*!\n      } else {\n        if (typeof this.trace === 'function') {\n          this.trace(str);\n        }\n        if (!ExceptionClass) {\n          ExceptionClass = this.JisonParserError;\n        }\n        throw new ExceptionClass(str, hash);\n      }\n    },\n    parse: function parse(input) {\n      var self = this;\n      var stack = new Array(128); // token stack: stores token which leads to state at the same index (column storage)\n      var sstack = new Array(128); // state stack: stores states (column storage)\n\n      var vstack = new Array(128); // semantic value stack\n\n      var table = this.table;\n      var sp = 0; // 'stack pointer': index into the stacks\n\n      var symbol = 0;\n      var TERROR = this.TERROR;\n      var EOF = this.EOF;\n      var ERROR_RECOVERY_TOKEN_DISCARD_COUNT = this.options.errorRecoveryTokenDiscardCount | 0 || 3;\n      var NO_ACTION = [0, 54 /* === table.length :: ensures that anyone using this new state will fail dramatically! */];\n      var lexer;\n      if (this.__lexer__) {\n        lexer = this.__lexer__;\n      } else {\n        lexer = this.__lexer__ = Object.create(this.lexer);\n      }\n      var sharedState_yy = {\n        parseError: undefined,\n        quoteName: undefined,\n        lexer: undefined,\n        parser: undefined,\n        pre_parse: undefined,\n        post_parse: undefined,\n        pre_lex: undefined,\n        post_lex: undefined // WARNING: must be written this way for the code expanders to work correctly in both ES5 and ES6 modes!\n      };\n      var ASSERT;\n      if (typeof assert !== 'function') {\n        ASSERT = function JisonAssert(cond, msg) {\n          if (!cond) {\n            throw new Error('assertion failed: ' + (msg || '***'));\n          }\n        };\n      } else {\n        ASSERT = assert;\n      }\n      this.yyGetSharedState = function yyGetSharedState() {\n        return sharedState_yy;\n      };\n      function shallow_copy_noclobber(dst, src) {\n        for (var k in src) {\n          if (typeof dst[k] === 'undefined' && Object.prototype.hasOwnProperty.call(src, k)) {\n            dst[k] = src[k];\n          }\n        }\n      }\n\n      // copy state\n      shallow_copy_noclobber(sharedState_yy, this.yy);\n      sharedState_yy.lexer = lexer;\n      sharedState_yy.parser = this;\n\n      // Does the shared state override the default `parseError` that already comes with this instance?\n      if (typeof sharedState_yy.parseError === 'function') {\n        this.parseError = function parseErrorAlt(str, hash, ExceptionClass) {\n          if (!ExceptionClass) {\n            ExceptionClass = this.JisonParserError;\n          }\n          return sharedState_yy.parseError.call(this, str, hash, ExceptionClass);\n        };\n      } else {\n        this.parseError = this.originalParseError;\n      }\n\n      // Does the shared state override the default `quoteName` that already comes with this instance?\n      if (typeof sharedState_yy.quoteName === 'function') {\n        this.quoteName = function quoteNameAlt(id_str) {\n          return sharedState_yy.quoteName.call(this, id_str);\n        };\n      } else {\n        this.quoteName = this.originalQuoteName;\n      }\n\n      // set up the cleanup function; make it an API so that external code can re-use this one in case of\n      // calamities or when the `%options no-try-catch` option has been specified for the grammar, in which\n      // case this parse() API method doesn't come with a `finally { ... }` block any more!\n      //\n      // NOTE: as this API uses parse() as a closure, it MUST be set again on every parse() invocation,\n      //       or else your `sharedState`, etc. references will be *wrong*!\n      this.cleanupAfterParse = function parser_cleanupAfterParse(resultValue, invoke_post_methods, do_not_nuke_errorinfos) {\n        var rv;\n        if (invoke_post_methods) {\n          var hash;\n          if (sharedState_yy.post_parse || this.post_parse) {\n            // create an error hash info instance: we re-use this API in a **non-error situation**\n            // as this one delivers all parser internals ready for access by userland code.\n            hash = this.constructParseErrorInfo(null /* no error! */, null /* no exception! */, null, false);\n          }\n          if (sharedState_yy.post_parse) {\n            rv = sharedState_yy.post_parse.call(this, sharedState_yy, resultValue, hash);\n            if (typeof rv !== 'undefined') resultValue = rv;\n          }\n          if (this.post_parse) {\n            rv = this.post_parse.call(this, sharedState_yy, resultValue, hash);\n            if (typeof rv !== 'undefined') resultValue = rv;\n          }\n\n          // cleanup:\n          if (hash && hash.destroy) {\n            hash.destroy();\n          }\n        }\n        if (this.__reentrant_call_depth > 1) return resultValue; // do not (yet) kill the sharedState when this is a reentrant run.\n\n        // clean up the lingering lexer structures as well:\n        if (lexer.cleanupAfterLex) {\n          lexer.cleanupAfterLex(do_not_nuke_errorinfos);\n        }\n\n        // prevent lingering circular references from causing memory leaks:\n        if (sharedState_yy) {\n          sharedState_yy.lexer = undefined;\n          sharedState_yy.parser = undefined;\n          if (lexer.yy === sharedState_yy) {\n            lexer.yy = undefined;\n          }\n        }\n        sharedState_yy = undefined;\n        this.parseError = this.originalParseError;\n        this.quoteName = this.originalQuoteName;\n\n        // nuke the vstack[] array at least as that one will still reference obsoleted user values.\n        // To be safe, we nuke the other internal stack columns as well...\n        stack.length = 0; // fastest way to nuke an array without overly bothering the GC\n        sstack.length = 0;\n        vstack.length = 0;\n        sp = 0;\n\n        // nuke the error hash info instances created during this run.\n        // Userland code must COPY any data/references\n        // in the error hash instance(s) it is more permanently interested in.\n        if (!do_not_nuke_errorinfos) {\n          for (var i = this.__error_infos.length - 1; i >= 0; i--) {\n            var el = this.__error_infos[i];\n            if (el && typeof el.destroy === 'function') {\n              el.destroy();\n            }\n          }\n          this.__error_infos.length = 0;\n        }\n        return resultValue;\n      };\n\n      // NOTE: as this API uses parse() as a closure, it MUST be set again on every parse() invocation,\n      //       or else your `lexer`, `sharedState`, etc. references will be *wrong*!\n      this.constructParseErrorInfo = function parser_constructParseErrorInfo(msg, ex, expected, recoverable) {\n        var pei = {\n          errStr: msg,\n          exception: ex,\n          text: lexer.match,\n          value: lexer.yytext,\n          token: this.describeSymbol(symbol) || symbol,\n          token_id: symbol,\n          line: lexer.yylineno,\n          expected: expected,\n          recoverable: recoverable,\n          state: state,\n          action: action,\n          new_state: newState,\n          symbol_stack: stack,\n          state_stack: sstack,\n          value_stack: vstack,\n          stack_pointer: sp,\n          yy: sharedState_yy,\n          lexer: lexer,\n          parser: this,\n          // and make sure the error info doesn't stay due to potential\n          // ref cycle via userland code manipulations.\n          // These would otherwise all be memory leak opportunities!\n          //\n          // Note that only array and object references are nuked as those\n          // constitute the set of elements which can produce a cyclic ref.\n          // The rest of the members is kept intact as they are harmless.\n          destroy: function destructParseErrorInfo() {\n            // remove cyclic references added to error info:\n            // info.yy = null;\n            // info.lexer = null;\n            // info.value = null;\n            // info.value_stack = null;\n            // ...\n            var rec = !!this.recoverable;\n            for (var key in this) {\n              if (this.hasOwnProperty(key) && typeof key === 'object') {\n                this[key] = undefined;\n              }\n            }\n            this.recoverable = rec;\n          }\n        };\n        // track this instance so we can `destroy()` it once we deem it superfluous and ready for garbage collection!\n        this.__error_infos.push(pei);\n        return pei;\n      };\n      function getNonTerminalFromCode(symbol) {\n        var tokenName = self.getSymbolName(symbol);\n        if (!tokenName) {\n          tokenName = symbol;\n        }\n        return tokenName;\n      }\n      function stdLex() {\n        var token = lexer.lex();\n        // if token isn't its numeric value, convert\n        if (typeof token !== 'number') {\n          token = self.symbols_[token] || token;\n        }\n        return token || EOF;\n      }\n      function fastLex() {\n        var token = lexer.fastLex();\n        // if token isn't its numeric value, convert\n        if (typeof token !== 'number') {\n          token = self.symbols_[token] || token;\n        }\n        return token || EOF;\n      }\n      var lex = stdLex;\n      var state, action, r, t;\n      var yyval = {\n        $: true,\n        _$: undefined,\n        yy: sharedState_yy\n      };\n      var p;\n      var yyrulelen;\n      var this_production;\n      var newState;\n      var retval = false;\n      try {\n        this.__reentrant_call_depth++;\n        lexer.setInput(input, sharedState_yy);\n\n        // NOTE: we *assume* no lexer pre/post handlers are set up *after* \n        // this initial `setInput()` call: hence we can now check and decide\n        // whether we'll go with the standard, slower, lex() API or the\n        // `fast_lex()` one:\n        if (typeof lexer.canIUse === 'function') {\n          var lexerInfo = lexer.canIUse();\n          if (lexerInfo.fastLex && typeof fastLex === 'function') {\n            lex = fastLex;\n          }\n        }\n        vstack[sp] = null;\n        sstack[sp] = 0;\n        stack[sp] = 0;\n        ++sp;\n        if (this.pre_parse) {\n          this.pre_parse.call(this, sharedState_yy);\n        }\n        if (sharedState_yy.pre_parse) {\n          sharedState_yy.pre_parse.call(this, sharedState_yy);\n        }\n        newState = sstack[sp - 1];\n        for (;;) {\n          // retrieve state number from top of stack\n          state = newState; // sstack[sp - 1];\n\n          // use default actions if available\n          if (this.defaultActions[state]) {\n            action = 2;\n            newState = this.defaultActions[state];\n          } else {\n            // The single `==` condition below covers both these `===` comparisons in a single\n            // operation:\n            //\n            //     if (symbol === null || typeof symbol === 'undefined') ...\n            if (!symbol) {\n              symbol = lex();\n            }\n            // read action for current state and first input\n            t = table[state] && table[state][symbol] || NO_ACTION;\n            newState = t[1];\n            action = t[0];\n\n            // handle parse error\n            if (!action) {\n              var errStr;\n              var errSymbolDescr = this.describeSymbol(symbol) || symbol;\n              var expected = this.collect_expected_token_set(state);\n\n              // Report error\n              if (typeof lexer.yylineno === 'number') {\n                errStr = 'Parse error on line ' + (lexer.yylineno + 1) + ': ';\n              } else {\n                errStr = 'Parse error: ';\n              }\n              if (typeof lexer.showPosition === 'function') {\n                errStr += '\\n' + lexer.showPosition(79 - 10, 10) + '\\n';\n              }\n              if (expected.length) {\n                errStr += 'Expecting ' + expected.join(', ') + ', got unexpected ' + errSymbolDescr;\n              } else {\n                errStr += 'Unexpected ' + errSymbolDescr;\n              }\n              // we cannot recover from the error!\n              p = this.constructParseErrorInfo(errStr, null, expected, false);\n              r = this.parseError(p.errStr, p, this.JisonParserError);\n              if (typeof r !== 'undefined') {\n                retval = r;\n              }\n              break;\n            }\n          }\n          switch (action) {\n            // catch misc. parse failures:\n            default:\n              // this shouldn't happen, unless resolve defaults are off\n              if (action instanceof Array) {\n                p = this.constructParseErrorInfo('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol, null, null, false);\n                r = this.parseError(p.errStr, p, this.JisonParserError);\n                if (typeof r !== 'undefined') {\n                  retval = r;\n                }\n                break;\n              }\n              // Another case of better safe than sorry: in case state transitions come out of another error recovery process\n              // or a buggy LUT (LookUp Table):\n              p = this.constructParseErrorInfo('Parsing halted. No viable error recovery approach available due to internal system failure.', null, null, false);\n              r = this.parseError(p.errStr, p, this.JisonParserError);\n              if (typeof r !== 'undefined') {\n                retval = r;\n              }\n              break;\n\n            // shift:\n            case 1:\n              stack[sp] = symbol;\n              vstack[sp] = lexer.yytext;\n              sstack[sp] = newState; // push state\n\n              ++sp;\n              symbol = 0;\n\n              // Pick up the lexer details for the current symbol as that one is not 'look-ahead' any more:\n\n              continue;\n\n            // reduce:\n            case 2:\n              this_production = this.productions_[newState - 1]; // `this.productions_[]` is zero-based indexed while states start from 1 upwards...\n              yyrulelen = this_production[1];\n              r = this.performAction.call(yyval, newState, sp - 1, vstack);\n              if (typeof r !== 'undefined') {\n                retval = r;\n                break;\n              }\n\n              // pop off stack\n              sp -= yyrulelen;\n\n              // don't overwrite the `symbol` variable: use a local var to speed things up:\n              var ntsymbol = this_production[0]; // push nonterminal (reduce)\n              stack[sp] = ntsymbol;\n              vstack[sp] = yyval.$;\n\n              // goto new state = table[STATE][NONTERMINAL]\n              newState = table[sstack[sp - 1]][ntsymbol];\n              sstack[sp] = newState;\n              ++sp;\n              continue;\n\n            // accept:\n            case 3:\n              if (sp !== -2) {\n                retval = true;\n                // Return the `$accept` rule's `$$` result, if available.\n                //\n                // Also note that JISON always adds this top-most `$accept` rule (with implicit,\n                // default, action):\n                //\n                //     $accept: <startSymbol> $end\n                //                  %{ $$ = $1; @$ = @1; %}\n                //\n                // which, combined with the parse kernel's `$accept` state behaviour coded below,\n                // will produce the `$$` value output of the <startSymbol> rule as the parse result,\n                // IFF that result is *not* `undefined`. (See also the parser kernel code.)\n                //\n                // In code:\n                //\n                //                  %{\n                //                      @$ = @1;            // if location tracking support is included\n                //                      if (typeof $1 !== 'undefined')\n                //                          return $1;\n                //                      else\n                //                          return true;           // the default parse result if the rule actions don't produce anything\n                //                  %}\n                sp--;\n                if (typeof vstack[sp] !== 'undefined') {\n                  retval = vstack[sp];\n                }\n              }\n              break;\n          }\n\n          // break out of loop: we accept or fail with error\n          break;\n        }\n      } catch (ex) {\n        // report exceptions through the parseError callback too, but keep the exception intact\n        // if it is a known parser or lexer error which has been thrown by parseError() already:\n        if (ex instanceof this.JisonParserError) {\n          throw ex;\n        } else if (lexer && typeof lexer.JisonLexerError === 'function' && ex instanceof lexer.JisonLexerError) {\n          throw ex;\n        }\n        p = this.constructParseErrorInfo('Parsing aborted due to exception.', ex, null, false);\n        retval = false;\n        r = this.parseError(p.errStr, p, this.JisonParserError);\n        if (typeof r !== 'undefined') {\n          retval = r;\n        }\n      } finally {\n        retval = this.cleanupAfterParse(retval, true, true);\n        this.__reentrant_call_depth--;\n      } // /finally\n\n      return retval;\n    }\n  };\n  parser.originalParseError = parser.parseError;\n  parser.originalQuoteName = parser.quoteName;\n  /* lexer generated by jison-lex 0.6.1-215 */\n\n  /*\n   * Returns a Lexer object of the following structure:\n   *\n   *  Lexer: {\n   *    yy: {}     The so-called \"shared state\" or rather the *source* of it;\n   *               the real \"shared state\" `yy` passed around to\n   *               the rule actions, etc. is a direct reference!\n   *\n   *               This \"shared context\" object was passed to the lexer by way of \n   *               the `lexer.setInput(str, yy)` API before you may use it.\n   *\n   *               This \"shared context\" object is passed to the lexer action code in `performAction()`\n   *               so userland code in the lexer actions may communicate with the outside world \n   *               and/or other lexer rules' actions in more or less complex ways.\n   *\n   *  }\n   *\n   *  Lexer.prototype: {\n   *    EOF: 1,\n   *    ERROR: 2,\n   *\n   *    yy:        The overall \"shared context\" object reference.\n   *\n   *    JisonLexerError: function(msg, hash),\n   *\n   *    performAction: function lexer__performAction(yy, yyrulenumber, YY_START),\n   *\n   *               The function parameters and `this` have the following value/meaning:\n   *               - `this`    : reference to the `lexer` instance. \n   *                               `yy_` is an alias for `this` lexer instance reference used internally.\n   *\n   *               - `yy`      : a reference to the `yy` \"shared state\" object which was passed to the lexer\n   *                             by way of the `lexer.setInput(str, yy)` API before.\n   *\n   *                             Note:\n   *                             The extra arguments you specified in the `%parse-param` statement in your\n   *                             **parser** grammar definition file are passed to the lexer via this object\n   *                             reference as member variables.\n   *\n   *               - `yyrulenumber`   : index of the matched lexer rule (regex), used internally.\n   *\n   *               - `YY_START`: the current lexer \"start condition\" state.\n   *\n   *    parseError: function(str, hash, ExceptionClass),\n   *\n   *    constructLexErrorInfo: function(error_message, is_recoverable),\n   *               Helper function.\n   *               Produces a new errorInfo 'hash object' which can be passed into `parseError()`.\n   *               See it's use in this lexer kernel in many places; example usage:\n   *\n   *                   var infoObj = lexer.constructParseErrorInfo('fail!', true);\n   *                   var retVal = lexer.parseError(infoObj.errStr, infoObj, lexer.JisonLexerError);\n   *\n   *    options: { ... lexer %options ... },\n   *\n   *    lex: function(),\n   *               Produce one token of lexed input, which was passed in earlier via the `lexer.setInput()` API.\n   *               You MAY use the additional `args...` parameters as per `%parse-param` spec of the **lexer** grammar:\n   *               these extra `args...` are added verbatim to the `yy` object reference as member variables.\n   *\n   *               WARNING:\n   *               Lexer's additional `args...` parameters (via lexer's `%parse-param`) MAY conflict with\n   *               any attributes already added to `yy` by the **parser** or the jison run-time; \n   *               when such a collision is detected an exception is thrown to prevent the generated run-time \n   *               from silently accepting this confusing and potentially hazardous situation! \n   *\n   *    cleanupAfterLex: function(do_not_nuke_errorinfos),\n   *               Helper function.\n   *\n   *               This helper API is invoked when the **parse process** has completed: it is the responsibility\n   *               of the **parser** (or the calling userland code) to invoke this method once cleanup is desired. \n   *\n   *               This helper may be invoked by user code to ensure the internal lexer gets properly garbage collected.\n   *\n   *    setInput: function(input, [yy]),\n   *\n   *\n   *    input: function(),\n   *\n   *\n   *    unput: function(str),\n   *\n   *\n   *    more: function(),\n   *\n   *\n   *    reject: function(),\n   *\n   *\n   *    less: function(n),\n   *\n   *\n   *    pastInput: function(n),\n   *\n   *\n   *    upcomingInput: function(n),\n   *\n   *\n   *    showPosition: function(),\n   *\n   *\n   *    test_match: function(regex_match_array, rule_index),\n   *\n   *\n   *    next: function(),\n   *\n   *\n   *    begin: function(condition),\n   *\n   *\n   *    pushState: function(condition),\n   *\n   *\n   *    popState: function(),\n   *\n   *\n   *    topState: function(),\n   *\n   *\n   *    _currentRules: function(),\n   *\n   *\n   *    stateStackSize: function(),\n   *\n   *\n   *    performAction: function(yy, yy_, yyrulenumber, YY_START),\n   *\n   *\n   *    rules: [...],\n   *\n   *\n   *    conditions: {associative list: name ==> set},\n   *  }\n   *\n   *\n   *  token location info (`yylloc`): {\n   *    first_line: n,\n   *    last_line: n,\n   *    first_column: n,\n   *    last_column: n,\n   *    range: [start_number, end_number]\n   *               (where the numbers are indexes into the input string, zero-based)\n   *  }\n   *\n   * ---\n   *\n   * The `parseError` function receives a 'hash' object with these members for lexer errors:\n   *\n   *  {\n   *    text:        (matched text)\n   *    token:       (the produced terminal token, if any)\n   *    token_id:    (the produced terminal token numeric ID, if any)\n   *    line:        (yylineno)\n   *    loc:         (yylloc)\n   *    recoverable: (boolean: TRUE when the parser MAY have an error recovery rule\n   *                  available for this particular error)\n   *    yy:          (object: the current parser internal \"shared state\" `yy`\n   *                  as is also available in the rule actions; this can be used,\n   *                  for instance, for advanced error analysis and reporting)\n   *    lexer:       (reference to the current lexer instance used by the parser)\n   *  }\n   *\n   * while `this` will reference the current lexer instance.\n   *\n   * When `parseError` is invoked by the lexer, the default implementation will\n   * attempt to invoke `yy.parser.parseError()`; when this callback is not provided\n   * it will try to invoke `yy.parseError()` instead. When that callback is also not\n   * provided, a `JisonLexerError` exception will be thrown containing the error\n   * message and `hash`, as constructed by the `constructLexErrorInfo()` API.\n   *\n   * Note that the lexer's `JisonLexerError` error class is passed via the\n   * `ExceptionClass` argument, which is invoked to construct the exception\n   * instance to be thrown, so technically `parseError` will throw the object\n   * produced by the `new ExceptionClass(str, hash)` JavaScript expression.\n   *\n   * ---\n   *\n   * You can specify lexer options by setting / modifying the `.options` object of your Lexer instance.\n   * These options are available:\n   *\n   * (Options are permanent.)\n   *  \n   *  yy: {\n   *      parseError: function(str, hash, ExceptionClass)\n   *                 optional: overrides the default `parseError` function.\n   *  }\n   *\n   *  lexer.options: {\n   *      pre_lex:  function()\n   *                 optional: is invoked before the lexer is invoked to produce another token.\n   *                 `this` refers to the Lexer object.\n   *      post_lex: function(token) { return token; }\n   *                 optional: is invoked when the lexer has produced a token `token`;\n   *                 this function can override the returned token value by returning another.\n   *                 When it does not return any (truthy) value, the lexer will return\n   *                 the original `token`.\n   *                 `this` refers to the Lexer object.\n   *\n   * WARNING: the next set of options are not meant to be changed. They echo the abilities of\n   * the lexer as per when it was compiled!\n   *\n   *      ranges: boolean\n   *                 optional: `true` ==> token location info will include a .range[] member.\n   *      flex: boolean\n   *                 optional: `true` ==> flex-like lexing behaviour where the rules are tested\n   *                 exhaustively to find the longest match.\n   *      backtrack_lexer: boolean\n   *                 optional: `true` ==> lexer regexes are tested in order and for invoked;\n   *                 the lexer terminates the scan when a token is returned by the action code.\n   *      xregexp: boolean\n   *                 optional: `true` ==> lexer rule regexes are \"extended regex format\" requiring the\n   *                 `XRegExp` library. When this %option has not been specified at compile time, all lexer\n   *                 rule regexes have been written as standard JavaScript RegExp expressions.\n   *  }\n   */\n\n  var lexer = function () {\n    /**\n     * See also:\n     * http://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript/#35881508\n     * but we keep the prototype.constructor and prototype.name assignment lines too for compatibility\n     * with userland code which might access the derived class in a 'classic' way.\n     *\n     * @public\n     * @constructor\n     * @nocollapse\n     */\n    function JisonLexerError(msg, hash) {\n      Object.defineProperty(this, 'name', {\n        enumerable: false,\n        writable: false,\n        value: 'JisonLexerError'\n      });\n      if (msg == null) msg = '???';\n      Object.defineProperty(this, 'message', {\n        enumerable: false,\n        writable: true,\n        value: msg\n      });\n      this.hash = hash;\n      var stacktrace;\n      if (hash && hash.exception instanceof Error) {\n        var ex2 = hash.exception;\n        this.message = ex2.message || msg;\n        stacktrace = ex2.stack;\n      }\n      if (!stacktrace) {\n        if (Error.hasOwnProperty('captureStackTrace')) {\n          // V8\n          Error.captureStackTrace(this, this.constructor);\n        } else {\n          stacktrace = new Error(msg).stack;\n        }\n      }\n      if (stacktrace) {\n        Object.defineProperty(this, 'stack', {\n          enumerable: false,\n          writable: false,\n          value: stacktrace\n        });\n      }\n    }\n    if (typeof Object.setPrototypeOf === 'function') {\n      Object.setPrototypeOf(JisonLexerError.prototype, Error.prototype);\n    } else {\n      JisonLexerError.prototype = Object.create(Error.prototype);\n    }\n    JisonLexerError.prototype.constructor = JisonLexerError;\n    JisonLexerError.prototype.name = 'JisonLexerError';\n    var lexer = {\n      // Code Generator Information Report\n      // ---------------------------------\n      //\n      // Options:\n      //\n      //   backtracking: .................... false\n      //   location.ranges: ................. false\n      //   location line+column tracking: ... true\n      //\n      //\n      // Forwarded Parser Analysis flags:\n      //\n      //   uses yyleng: ..................... false\n      //   uses yylineno: ................... false\n      //   uses yytext: ..................... false\n      //   uses yylloc: ..................... false\n      //   uses lexer values: ............... true / true\n      //   location tracking: ............... false\n      //   location assignment: ............. false\n      //\n      //\n      // Lexer Analysis flags:\n      //\n      //   uses yyleng: ..................... ???\n      //   uses yylineno: ................... ???\n      //   uses yytext: ..................... ???\n      //   uses yylloc: ..................... ???\n      //   uses ParseError API: ............. ???\n      //   uses yyerror: .................... ???\n      //   uses location tracking & editing:  ???\n      //   uses more() API: ................. ???\n      //   uses unput() API: ................ ???\n      //   uses reject() API: ............... ???\n      //   uses less() API: ................. ???\n      //   uses display APIs pastInput(), upcomingInput(), showPosition():\n      //        ............................. ???\n      //   uses describeYYLLOC() API: ....... ???\n      //\n      // --------- END OF REPORT -----------\n\n      EOF: 1,\n      ERROR: 2,\n      // JisonLexerError: JisonLexerError,        /// <-- injected by the code generator\n\n      // options: {},                             /// <-- injected by the code generator\n\n      // yy: ...,                                 /// <-- injected by setInput()\n\n      __currentRuleSet__: null,\n      /// INTERNAL USE ONLY: internal rule set cache for the current lexer state  \n\n      __error_infos: [],\n      /// INTERNAL USE ONLY: the set of lexErrorInfo objects created since the last cleanup  \n      __decompressed: false,\n      /// INTERNAL USE ONLY: mark whether the lexer instance has been 'unfolded' completely and is now ready for use  \n      done: false,\n      /// INTERNAL USE ONLY  \n      _backtrack: false,\n      /// INTERNAL USE ONLY  \n      _input: '',\n      /// INTERNAL USE ONLY  \n      _more: false,\n      /// INTERNAL USE ONLY  \n      _signaled_error_token: false,\n      /// INTERNAL USE ONLY  \n      conditionStack: [],\n      /// INTERNAL USE ONLY; managed via `pushState()`, `popState()`, `topState()` and `stateStackSize()`  \n      match: '',\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks input which has been matched so far for the lexer token under construction. `match` is identical to `yytext` except that this one still contains the matched input string after `lexer.performAction()` has been invoked, where userland code MAY have changed/replaced the `yytext` value entirely!  \n      matched: '',\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks entire input which has been matched so far  \n      matches: false,\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks RE match result for last (successful) match attempt  \n      yytext: '',\n      /// ADVANCED USE ONLY: tracks input which has been matched so far for the lexer token under construction; this value is transferred to the parser as the 'token value' when the parser consumes the lexer token produced through a call to the `lex()` API.  \n      offset: 0,\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks the 'cursor position' in the input string, i.e. the number of characters matched so far  \n      yyleng: 0,\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: length of matched input for the token under construction (`yytext`)  \n      yylineno: 0,\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: 'line number' at which the token under construction is located  \n      yylloc: null,\n      /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks location info (lines + columns) for the token under construction  \n\n      /**\n       * INTERNAL USE: construct a suitable error info hash object instance for `parseError`.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      constructLexErrorInfo: function lexer_constructLexErrorInfo(msg, recoverable, show_input_position) {\n        msg = '' + msg;\n\n        // heuristic to determine if the error message already contains a (partial) source code dump\n        // as produced by either `showPosition()` or `prettyPrintRange()`:\n        if (show_input_position == undefined) {\n          show_input_position = !(msg.indexOf('\\n') > 0 && msg.indexOf('^') > 0);\n        }\n        if (this.yylloc && show_input_position) {\n          if (typeof this.prettyPrintRange === 'function') {\n            var pretty_src = this.prettyPrintRange(this.yylloc);\n            if (!/\\n\\s*$/.test(msg)) {\n              msg += '\\n';\n            }\n            msg += '\\n  Erroneous area:\\n' + this.prettyPrintRange(this.yylloc);\n          } else if (typeof this.showPosition === 'function') {\n            var pos_str = this.showPosition();\n            if (pos_str) {\n              if (msg.length && msg[msg.length - 1] !== '\\n' && pos_str[0] !== '\\n') {\n                msg += '\\n' + pos_str;\n              } else {\n                msg += pos_str;\n              }\n            }\n          }\n        }\n\n        /** @constructor */\n        var pei = {\n          errStr: msg,\n          recoverable: !!recoverable,\n          text: this.match,\n          // This one MAY be empty; userland code should use the `upcomingInput` API to obtain more text which follows the 'lexer cursor position'...  \n          token: null,\n          line: this.yylineno,\n          loc: this.yylloc,\n          yy: this.yy,\n          lexer: this,\n          /**\n           * and make sure the error info doesn't stay due to potential\n           * ref cycle via userland code manipulations.\n           * These would otherwise all be memory leak opportunities!\n           * \n           * Note that only array and object references are nuked as those\n           * constitute the set of elements which can produce a cyclic ref.\n           * The rest of the members is kept intact as they are harmless.\n           * \n           * @public\n           * @this {LexErrorInfo}\n           */\n          destroy: function destructLexErrorInfo() {\n            // remove cyclic references added to error info:\n            // info.yy = null;\n            // info.lexer = null;\n            // ...\n            var rec = !!this.recoverable;\n            for (var key in this) {\n              if (this.hasOwnProperty(key) && typeof key === 'object') {\n                this[key] = undefined;\n              }\n            }\n            this.recoverable = rec;\n          }\n        };\n\n        // track this instance so we can `destroy()` it once we deem it superfluous and ready for garbage collection!\n        this.__error_infos.push(pei);\n        return pei;\n      },\n      /**\n       * handler which is invoked when a lexer error occurs.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      parseError: function lexer_parseError(str, hash, ExceptionClass) {\n        if (!ExceptionClass) {\n          ExceptionClass = this.JisonLexerError;\n        }\n        if (this.yy) {\n          if (this.yy.parser && typeof this.yy.parser.parseError === 'function') {\n            return this.yy.parser.parseError.call(this, str, hash, ExceptionClass) || this.ERROR;\n          } else if (typeof this.yy.parseError === 'function') {\n            return this.yy.parseError.call(this, str, hash, ExceptionClass) || this.ERROR;\n          }\n        }\n        throw new ExceptionClass(str, hash);\n      },\n      /**\n       * method which implements `yyerror(str, ...args)` functionality for use inside lexer actions.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      yyerror: function yyError(str /*, ...args */) {\n        var lineno_msg = '';\n        if (this.yylloc) {\n          lineno_msg = ' on line ' + (this.yylineno + 1);\n        }\n        var p = this.constructLexErrorInfo('Lexical error' + lineno_msg + ': ' + str, this.options.lexerErrorsAreRecoverable);\n\n        // Add any extra args to the hash under the name `extra_error_attributes`:\n        var args = Array.prototype.slice.call(arguments, 1);\n        if (args.length) {\n          p.extra_error_attributes = args;\n        }\n        return this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n      },\n      /**\n       * final cleanup function for when we have completed lexing the input;\n       * make it an API so that external code can use this one once userland\n       * code has decided it's time to destroy any lingering lexer error\n       * hash object instances and the like: this function helps to clean\n       * up these constructs, which *may* carry cyclic references which would\n       * otherwise prevent the instances from being properly and timely\n       * garbage-collected, i.e. this function helps prevent memory leaks!\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      cleanupAfterLex: function lexer_cleanupAfterLex(do_not_nuke_errorinfos) {\n        // prevent lingering circular references from causing memory leaks:\n        this.setInput('', {});\n\n        // nuke the error hash info instances created during this run.\n        // Userland code must COPY any data/references\n        // in the error hash instance(s) it is more permanently interested in.\n        if (!do_not_nuke_errorinfos) {\n          for (var i = this.__error_infos.length - 1; i >= 0; i--) {\n            var el = this.__error_infos[i];\n            if (el && typeof el.destroy === 'function') {\n              el.destroy();\n            }\n          }\n          this.__error_infos.length = 0;\n        }\n        return this;\n      },\n      /**\n       * clear the lexer token context; intended for internal use only\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      clear: function lexer_clear() {\n        this.yytext = '';\n        this.yyleng = 0;\n        this.match = '';\n\n        // - DO NOT reset `this.matched`\n        this.matches = false;\n        this._more = false;\n        this._backtrack = false;\n        var col = this.yylloc ? this.yylloc.last_column : 0;\n        this.yylloc = {\n          first_line: this.yylineno + 1,\n          first_column: col,\n          last_line: this.yylineno + 1,\n          last_column: col,\n          range: [this.offset, this.offset]\n        };\n      },\n      /**\n       * resets the lexer, sets new input\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      setInput: function lexer_setInput(input, yy) {\n        this.yy = yy || this.yy || {};\n\n        // also check if we've fully initialized the lexer instance,\n        // including expansion work to be done to go from a loaded\n        // lexer to a usable lexer:\n        if (!this.__decompressed) {\n          // step 1: decompress the regex list:\n          var rules = this.rules;\n          for (var i = 0, len = rules.length; i < len; i++) {\n            var rule_re = rules[i];\n\n            // compression: is the RE an xref to another RE slot in the rules[] table?\n            if (typeof rule_re === 'number') {\n              rules[i] = rules[rule_re];\n            }\n          }\n\n          // step 2: unfold the conditions[] set to make these ready for use:\n          var conditions = this.conditions;\n          for (var k in conditions) {\n            var spec = conditions[k];\n            var rule_ids = spec.rules;\n            var len = rule_ids.length;\n            var rule_regexes = new Array(len + 1); // slot 0 is unused; we use a 1-based index approach here to keep the hottest code in `lexer_next()` fast and simple! \n            var rule_new_ids = new Array(len + 1);\n            for (var i = 0; i < len; i++) {\n              var idx = rule_ids[i];\n              var rule_re = rules[idx];\n              rule_regexes[i + 1] = rule_re;\n              rule_new_ids[i + 1] = idx;\n            }\n            spec.rules = rule_new_ids;\n            spec.__rule_regexes = rule_regexes;\n            spec.__rule_count = len;\n          }\n          this.__decompressed = true;\n        }\n        this._input = input || '';\n        this.clear();\n        this._signaled_error_token = false;\n        this.done = false;\n        this.yylineno = 0;\n        this.matched = '';\n        this.conditionStack = ['INITIAL'];\n        this.__currentRuleSet__ = null;\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0,\n          range: [0, 0]\n        };\n        this.offset = 0;\n        return this;\n      },\n      /**\n       * edit the remaining input via user-specified callback.\n       * This can be used to forward-adjust the input-to-parse, \n       * e.g. inserting macro expansions and alike in the\n       * input which has yet to be lexed.\n       * The behaviour of this API contrasts the `unput()` et al\n       * APIs as those act on the *consumed* input, while this\n       * one allows one to manipulate the future, without impacting\n       * the current `yyloc` cursor location or any history. \n       * \n       * Use this API to help implement C-preprocessor-like\n       * `#include` statements, etc.\n       * \n       * The provided callback must be synchronous and is\n       * expected to return the edited input (string).\n       *\n       * The `cpsArg` argument value is passed to the callback\n       * as-is.\n       *\n       * `callback` interface: \n       * `function callback(input, cpsArg)`\n       * \n       * - `input` will carry the remaining-input-to-lex string\n       *   from the lexer.\n       * - `cpsArg` is `cpsArg` passed into this API.\n       * \n       * The `this` reference for the callback will be set to\n       * reference this lexer instance so that userland code\n       * in the callback can easily and quickly access any lexer\n       * API. \n       *\n       * When the callback returns a non-string-type falsey value,\n       * we assume the callback did not edit the input and we\n       * will using the input as-is.\n       *\n       * When the callback returns a non-string-type value, it\n       * is converted to a string for lexing via the `\"\" + retval`\n       * operation. (See also why: http://2ality.com/2012/03/converting-to-string.html \n       * -- that way any returned object's `toValue()` and `toString()`\n       * methods will be invoked in a proper/desirable order.)\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      editRemainingInput: function lexer_editRemainingInput(callback, cpsArg) {\n        var rv = callback.call(this, this._input, cpsArg);\n        if (typeof rv !== 'string') {\n          if (rv) {\n            this._input = '' + rv;\n          }\n          // else: keep `this._input` as is.  \n        } else {\n          this._input = rv;\n        }\n        return this;\n      },\n      /**\n       * consumes and returns one char from the input\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      input: function lexer_input() {\n        if (!this._input) {\n          //this.done = true;    -- don't set `done` as we want the lex()/next() API to be able to produce one custom EOF token match after this anyhow. (lexer can match special <<EOF>> tokens and perform user action code for a <<EOF>> match, but only does so *once*)\n          return null;\n        }\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n\n        // Count the linenumber up when we hit the LF (or a stand-alone CR).\n        // On CRLF, the linenumber is incremented when you fetch the CR or the CRLF combo\n        // and we advance immediately past the LF as well, returning both together as if\n        // it was all a single 'character' only.\n        var slice_len = 1;\n        var lines = false;\n        if (ch === '\\n') {\n          lines = true;\n        } else if (ch === '\\r') {\n          lines = true;\n          var ch2 = this._input[1];\n          if (ch2 === '\\n') {\n            slice_len++;\n            ch += ch2;\n            this.yytext += ch2;\n            this.yyleng++;\n            this.offset++;\n            this.match += ch2;\n            this.matched += ch2;\n            this.yylloc.range[1]++;\n          }\n        }\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n          this.yylloc.last_column = 0;\n        } else {\n          this.yylloc.last_column++;\n        }\n        this.yylloc.range[1]++;\n        this._input = this._input.slice(slice_len);\n        return ch;\n      },\n      /**\n       * unshifts one char (or an entire string) into the input\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      unput: function lexer_unput(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.yyleng = this.yytext.length;\n        this.offset -= len;\n        this.match = this.match.substr(0, this.match.length - len);\n        this.matched = this.matched.substr(0, this.matched.length - len);\n        if (lines.length > 1) {\n          this.yylineno -= lines.length - 1;\n          this.yylloc.last_line = this.yylineno + 1;\n\n          // Get last entirely matched line into the `pre_lines[]` array's\n          // last index slot; we don't mind when other previously \n          // matched lines end up in the array too. \n          var pre = this.match;\n          var pre_lines = pre.split(/(?:\\r\\n?|\\n)/g);\n          if (pre_lines.length === 1) {\n            pre = this.matched;\n            pre_lines = pre.split(/(?:\\r\\n?|\\n)/g);\n          }\n          this.yylloc.last_column = pre_lines[pre_lines.length - 1].length;\n        } else {\n          this.yylloc.last_column -= len;\n        }\n        this.yylloc.range[1] = this.yylloc.range[0] + this.yyleng;\n        this.done = false;\n        return this;\n      },\n      /**\n       * cache matched text and append it on next action\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      more: function lexer_more() {\n        this._more = true;\n        return this;\n      },\n      /**\n       * signal the lexer that this rule fails to match the input, so the\n       * next matching rule (regex) should be tested instead.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      reject: function lexer_reject() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          // when the `parseError()` call returns, we MUST ensure that the error is registered.\n          // We accomplish this by signaling an 'error' token to be produced for the current\n          // `.lex()` run.\n          var lineno_msg = '';\n          if (this.yylloc) {\n            lineno_msg = ' on line ' + (this.yylineno + 1);\n          }\n          var p = this.constructLexErrorInfo('Lexical error' + lineno_msg + ': You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).', false);\n          this._signaled_error_token = this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n        }\n        return this;\n      },\n      /**\n       * retain first n characters of the match\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      less: function lexer_less(n) {\n        return this.unput(this.match.slice(n));\n      },\n      /**\n       * return (part of the) already matched input, i.e. for error\n       * messages.\n       * \n       * Limit the returned string length to `maxSize` (default: 20).\n       * \n       * Limit the returned string to the `maxLines` number of lines of\n       * input (default: 1).\n       * \n       * Negative limit values equal *unlimited*.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      pastInput: function lexer_pastInput(maxSize, maxLines) {\n        var past = this.matched.substring(0, this.matched.length - this.match.length);\n        if (maxSize < 0) maxSize = past.length;else if (!maxSize) maxSize = 20;\n        if (maxLines < 0) maxLines = past.length; // can't ever have more input lines than this! \n        else if (!maxLines) maxLines = 1;\n\n        // `substr` anticipation: treat \\r\\n as a single character and take a little\n        // more than necessary so that we can still properly check against maxSize\n        // after we've transformed and limited the newLines in here:\n        past = past.substr(-maxSize * 2 - 2);\n\n        // now that we have a significantly reduced string to process, transform the newlines\n        // and chop them, then limit them:\n        var a = past.replace(/\\r\\n|\\r/g, '\\n').split('\\n');\n        a = a.slice(-maxLines);\n        past = a.join('\\n');\n\n        // When, after limiting to maxLines, we still have too much to return,\n        // do add an ellipsis prefix...\n        if (past.length > maxSize) {\n          past = '...' + past.substr(-maxSize);\n        }\n        return past;\n      },\n      /**\n       * return (part of the) upcoming input, i.e. for error messages.\n       * \n       * Limit the returned string length to `maxSize` (default: 20).\n       * \n       * Limit the returned string to the `maxLines` number of lines of input (default: 1).\n       * \n       * Negative limit values equal *unlimited*.\n       *\n       * > ### NOTE ###\n       * >\n       * > *\"upcoming input\"* is defined as the whole of the both\n       * > the *currently lexed* input, together with any remaining input\n       * > following that. *\"currently lexed\"* input is the input \n       * > already recognized by the lexer but not yet returned with\n       * > the lexer token. This happens when you are invoking this API\n       * > from inside any lexer rule action code block. \n       * >\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      upcomingInput: function lexer_upcomingInput(maxSize, maxLines) {\n        var next = this.match;\n        if (maxSize < 0) maxSize = next.length + this._input.length;else if (!maxSize) maxSize = 20;\n        if (maxLines < 0) maxLines = maxSize; // can't ever have more input lines than this! \n        else if (!maxLines) maxLines = 1;\n\n        // `substring` anticipation: treat \\r\\n as a single character and take a little\n        // more than necessary so that we can still properly check against maxSize\n        // after we've transformed and limited the newLines in here:\n        if (next.length < maxSize * 2 + 2) {\n          next += this._input.substring(0, maxSize * 2 + 2); // substring is faster on Chrome/V8 \n        }\n\n        // now that we have a significantly reduced string to process, transform the newlines\n        // and chop them, then limit them:\n        var a = next.replace(/\\r\\n|\\r/g, '\\n').split('\\n');\n        a = a.slice(0, maxLines);\n        next = a.join('\\n');\n\n        // When, after limiting to maxLines, we still have too much to return,\n        // do add an ellipsis postfix...\n        if (next.length > maxSize) {\n          next = next.substring(0, maxSize) + '...';\n        }\n        return next;\n      },\n      /**\n       * return a string which displays the character position where the\n       * lexing error occurred, i.e. for error messages\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      showPosition: function lexer_showPosition(maxPrefix, maxPostfix) {\n        var pre = this.pastInput(maxPrefix).replace(/\\s/g, ' ');\n        var c = new Array(pre.length + 1).join('-');\n        return pre + this.upcomingInput(maxPostfix).replace(/\\s/g, ' ') + '\\n' + c + '^';\n      },\n      /**\n       * return an YYLLOC info object derived off the given context (actual, preceding, following, current).\n       * Use this method when the given `actual` location is not guaranteed to exist (i.e. when\n       * it MAY be NULL) and you MUST have a valid location info object anyway:\n       * then we take the given context of the `preceding` and `following` locations, IFF those are available,\n       * and reconstruct the `actual` location info from those.\n       * If this fails, the heuristic is to take the `current` location, IFF available.\n       * If this fails as well, we assume the sought location is at/around the current lexer position\n       * and then produce that one as a response. DO NOTE that these heuristic/derived location info\n       * values MAY be inaccurate!\n       *\n       * NOTE: `deriveLocationInfo()` ALWAYS produces a location info object *copy* of `actual`, not just\n       * a *reference* hence all input location objects can be assumed to be 'constant' (function has no side-effects).\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      deriveLocationInfo: function lexer_deriveYYLLOC(actual, preceding, following, current) {\n        var loc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0,\n          range: [0, 0]\n        };\n        if (actual) {\n          loc.first_line = actual.first_line | 0;\n          loc.last_line = actual.last_line | 0;\n          loc.first_column = actual.first_column | 0;\n          loc.last_column = actual.last_column | 0;\n          if (actual.range) {\n            loc.range[0] = actual.range[0] | 0;\n            loc.range[1] = actual.range[1] | 0;\n          }\n        }\n        if (loc.first_line <= 0 || loc.last_line < loc.first_line) {\n          // plan B: heuristic using preceding and following:\n          if (loc.first_line <= 0 && preceding) {\n            loc.first_line = preceding.last_line | 0;\n            loc.first_column = preceding.last_column | 0;\n            if (preceding.range) {\n              loc.range[0] = actual.range[1] | 0;\n            }\n          }\n          if ((loc.last_line <= 0 || loc.last_line < loc.first_line) && following) {\n            loc.last_line = following.first_line | 0;\n            loc.last_column = following.first_column | 0;\n            if (following.range) {\n              loc.range[1] = actual.range[0] | 0;\n            }\n          }\n\n          // plan C?: see if the 'current' location is useful/sane too:\n          if (loc.first_line <= 0 && current && (loc.last_line <= 0 || current.last_line <= loc.last_line)) {\n            loc.first_line = current.first_line | 0;\n            loc.first_column = current.first_column | 0;\n            if (current.range) {\n              loc.range[0] = current.range[0] | 0;\n            }\n          }\n          if (loc.last_line <= 0 && current && (loc.first_line <= 0 || current.first_line >= loc.first_line)) {\n            loc.last_line = current.last_line | 0;\n            loc.last_column = current.last_column | 0;\n            if (current.range) {\n              loc.range[1] = current.range[1] | 0;\n            }\n          }\n        }\n\n        // sanitize: fix last_line BEFORE we fix first_line as we use the 'raw' value of the latter\n        // or plan D heuristics to produce a 'sensible' last_line value:\n        if (loc.last_line <= 0) {\n          if (loc.first_line <= 0) {\n            loc.first_line = this.yylloc.first_line;\n            loc.last_line = this.yylloc.last_line;\n            loc.first_column = this.yylloc.first_column;\n            loc.last_column = this.yylloc.last_column;\n            loc.range[0] = this.yylloc.range[0];\n            loc.range[1] = this.yylloc.range[1];\n          } else {\n            loc.last_line = this.yylloc.last_line;\n            loc.last_column = this.yylloc.last_column;\n            loc.range[1] = this.yylloc.range[1];\n          }\n        }\n        if (loc.first_line <= 0) {\n          loc.first_line = loc.last_line;\n          loc.first_column = 0; // loc.last_column; \n          loc.range[1] = loc.range[0];\n        }\n        if (loc.first_column < 0) {\n          loc.first_column = 0;\n        }\n        if (loc.last_column < 0) {\n          loc.last_column = loc.first_column > 0 ? loc.first_column : 80;\n        }\n        return loc;\n      },\n      /**\n       * return a string which displays the lines & columns of input which are referenced \n       * by the given location info range, plus a few lines of context.\n       * \n       * This function pretty-prints the indicated section of the input, with line numbers \n       * and everything!\n       * \n       * This function is very useful to provide highly readable error reports, while\n       * the location range may be specified in various flexible ways:\n       * \n       * - `loc` is the location info object which references the area which should be\n       *   displayed and 'marked up': these lines & columns of text are marked up by `^`\n       *   characters below each character in the entire input range.\n       * \n       * - `context_loc` is the *optional* location info object which instructs this\n       *   pretty-printer how much *leading* context should be displayed alongside\n       *   the area referenced by `loc`. This can help provide context for the displayed\n       *   error, etc.\n       * \n       *   When this location info is not provided, a default context of 3 lines is\n       *   used.\n       * \n       * - `context_loc2` is another *optional* location info object, which serves\n       *   a similar purpose to `context_loc`: it specifies the amount of *trailing*\n       *   context lines to display in the pretty-print output.\n       * \n       *   When this location info is not provided, a default context of 1 line only is\n       *   used.\n       * \n       * Special Notes:\n       * \n       * - when the `loc`-indicated range is very large (about 5 lines or more), then\n       *   only the first and last few lines of this block are printed while a\n       *   `...continued...` message will be printed between them.\n       * \n       *   This serves the purpose of not printing a huge amount of text when the `loc`\n       *   range happens to be huge: this way a manageable & readable output results\n       *   for arbitrary large ranges.\n       * \n       * - this function can display lines of input which whave not yet been lexed.\n       *   `prettyPrintRange()` can access the entire input!\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      prettyPrintRange: function lexer_prettyPrintRange(loc, context_loc, context_loc2) {\n        loc = this.deriveLocationInfo(loc, context_loc, context_loc2);\n        const CONTEXT = 3;\n        const CONTEXT_TAIL = 1;\n        const MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT = 2;\n        var input = this.matched + this._input;\n        var lines = input.split('\\n');\n        var l0 = Math.max(1, context_loc ? context_loc.first_line : loc.first_line - CONTEXT);\n        var l1 = Math.max(1, context_loc2 ? context_loc2.last_line : loc.last_line + CONTEXT_TAIL);\n        var lineno_display_width = 1 + Math.log10(l1 | 1) | 0;\n        var ws_prefix = new Array(lineno_display_width).join(' ');\n        var nonempty_line_indexes = [];\n        var rv = lines.slice(l0 - 1, l1 + 1).map(function injectLineNumber(line, index) {\n          var lno = index + l0;\n          var lno_pfx = (ws_prefix + lno).substr(-lineno_display_width);\n          var rv = lno_pfx + ': ' + line;\n          var errpfx = new Array(lineno_display_width + 1).join('^');\n          var offset = 2 + 1;\n          var len = 0;\n          if (lno === loc.first_line) {\n            offset += loc.first_column;\n            len = Math.max(2, (lno === loc.last_line ? loc.last_column : line.length) - loc.first_column + 1);\n          } else if (lno === loc.last_line) {\n            len = Math.max(2, loc.last_column + 1);\n          } else if (lno > loc.first_line && lno < loc.last_line) {\n            len = Math.max(2, line.length + 1);\n          }\n          if (len) {\n            var lead = new Array(offset).join('.');\n            var mark = new Array(len).join('^');\n            rv += '\\n' + errpfx + lead + mark;\n            if (line.trim().length > 0) {\n              nonempty_line_indexes.push(index);\n            }\n          }\n          rv = rv.replace(/\\t/g, ' ');\n          return rv;\n        });\n\n        // now make sure we don't print an overly large amount of error area: limit it \n        // to the top and bottom line count:\n        if (nonempty_line_indexes.length > 2 * MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT) {\n          var clip_start = nonempty_line_indexes[MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT - 1] + 1;\n          var clip_end = nonempty_line_indexes[nonempty_line_indexes.length - MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT] - 1;\n          var intermediate_line = new Array(lineno_display_width + 1).join(' ') + '  (...continued...)';\n          intermediate_line += '\\n' + new Array(lineno_display_width + 1).join('-') + '  (---------------)';\n          rv.splice(clip_start, clip_end - clip_start + 1, intermediate_line);\n        }\n        return rv.join('\\n');\n      },\n      /**\n       * helper function, used to produce a human readable description as a string, given\n       * the input `yylloc` location object.\n       * \n       * Set `display_range_too` to TRUE to include the string character index position(s)\n       * in the description if the `yylloc.range` is available.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      describeYYLLOC: function lexer_describe_yylloc(yylloc, display_range_too) {\n        var l1 = yylloc.first_line;\n        var l2 = yylloc.last_line;\n        var c1 = yylloc.first_column;\n        var c2 = yylloc.last_column;\n        var dl = l2 - l1;\n        var dc = c2 - c1;\n        var rv;\n        if (dl === 0) {\n          rv = 'line ' + l1 + ', ';\n          if (dc <= 1) {\n            rv += 'column ' + c1;\n          } else {\n            rv += 'columns ' + c1 + ' .. ' + c2;\n          }\n        } else {\n          rv = 'lines ' + l1 + '(column ' + c1 + ') .. ' + l2 + '(column ' + c2 + ')';\n        }\n        if (yylloc.range && display_range_too) {\n          var r1 = yylloc.range[0];\n          var r2 = yylloc.range[1] - 1;\n          if (r2 <= r1) {\n            rv += ' {String Offset: ' + r1 + '}';\n          } else {\n            rv += ' {String Offset range: ' + r1 + ' .. ' + r2 + '}';\n          }\n        }\n        return rv;\n      },\n      /**\n       * test the lexed token: return FALSE when not a match, otherwise return token.\n       * \n       * `match` is supposed to be an array coming out of a regex match, i.e. `match[0]`\n       * contains the actually matched text string.\n       * \n       * Also move the input cursor forward and update the match collectors:\n       * \n       * - `yytext`\n       * - `yyleng`\n       * - `match`\n       * - `matches`\n       * - `yylloc`\n       * - `offset`\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      test_match: function lexer_test_match(match, indexed_rule) {\n        var token, lines, backup, match_str, match_str_len;\n        if (this.options.backtrack_lexer) {\n          // save context\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.yylloc.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column,\n              range: this.yylloc.range.slice(0)\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            //_signaled_error_token: this._signaled_error_token,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n        }\n        match_str = match[0];\n        match_str_len = match_str.length;\n\n        // if (match_str.indexOf('\\n') !== -1 || match_str.indexOf('\\r') !== -1) {\n        lines = match_str.split(/(?:\\r\\n?|\\n)/g);\n        if (lines.length > 1) {\n          this.yylineno += lines.length - 1;\n          this.yylloc.last_line = this.yylineno + 1;\n          this.yylloc.last_column = lines[lines.length - 1].length;\n        } else {\n          this.yylloc.last_column += match_str_len;\n        }\n\n        // }\n        this.yytext += match_str;\n        this.match += match_str;\n        this.matched += match_str;\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        this.yylloc.range[1] += match_str_len;\n\n        // previous lex rules MAY have invoked the `more()` API rather than producing a token:\n        // those rules will already have moved this `offset` forward matching their match lengths,\n        // hence we must only add our own match length now:\n        this.offset += match_str_len;\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match_str_len);\n\n        // calling this method:\n        //\n        //   function lexer__performAction(yy, yyrulenumber, YY_START) {...}\n        token = this.performAction.call(this, this.yy, indexed_rule, this.conditionStack[this.conditionStack.length - 1] /* = YY_START */);\n\n        // otherwise, when the action codes are all simple return token statements:\n        //token = this.simpleCaseActionClusters[indexed_rule];\n\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          // recover context\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          this.__currentRuleSet__ = null;\n          return false; // rule action called reject() implying the next rule should be tested instead. \n        } else if (this._signaled_error_token) {\n          // produce one 'error' token as `.parseError()` in `reject()`\n          // did not guarantee a failure signal by throwing an exception!\n          token = this._signaled_error_token;\n          this._signaled_error_token = false;\n          return token;\n        }\n        return false;\n      },\n      /**\n       * return next match in input\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      next: function lexer_next() {\n        if (this.done) {\n          this.clear();\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.clear();\n        }\n        var spec = this.__currentRuleSet__;\n        if (!spec) {\n          // Update the ruleset cache as we apparently encountered a state change or just started lexing.\n          // The cache is set up for fast lookup -- we assume a lexer will switch states much less often than it will\n          // invoke the `lex()` token-producing API and related APIs, hence caching the set for direct access helps\n          // speed up those activities a tiny bit.\n          spec = this.__currentRuleSet__ = this._currentRules();\n\n          // Check whether a *sane* condition has been pushed before: this makes the lexer robust against\n          // user-programmer bugs such as https://github.com/zaach/jison-lex/issues/19\n          if (!spec || !spec.rules) {\n            var lineno_msg = '';\n            if (this.options.trackPosition) {\n              lineno_msg = ' on line ' + (this.yylineno + 1);\n            }\n            var p = this.constructLexErrorInfo('Internal lexer engine error' + lineno_msg + ': The lex grammar programmer pushed a non-existing condition name \"' + this.topState() + '\"; this is a fatal error and should be reported to the application programmer team!', false);\n\n            // produce one 'error' token until this situation has been resolved, most probably by parse termination!\n            return this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n          }\n        }\n        var rule_ids = spec.rules;\n        var regexes = spec.__rule_regexes;\n        var len = spec.__rule_count;\n\n        // Note: the arrays are 1-based, while `len` itself is a valid index,\n        // hence the non-standard less-or-equal check in the next loop condition!\n        for (var i = 1; i <= len; i++) {\n          tempMatch = this._input.match(regexes[i]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rule_ids[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = undefined;\n                continue; // rule action called reject() implying a rule MISmatch. \n              } else {\n                // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rule_ids[index]);\n          if (token !== false) {\n            return token;\n          }\n\n          // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n          return false;\n        }\n        if (!this._input) {\n          this.done = true;\n          this.clear();\n          return this.EOF;\n        } else {\n          var lineno_msg = '';\n          if (this.options.trackPosition) {\n            lineno_msg = ' on line ' + (this.yylineno + 1);\n          }\n          var p = this.constructLexErrorInfo('Lexical error' + lineno_msg + ': Unrecognized text.', this.options.lexerErrorsAreRecoverable);\n          var pendingInput = this._input;\n          var activeCondition = this.topState();\n          var conditionStackDepth = this.conditionStack.length;\n          token = this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n          if (token === this.ERROR) {\n            // we can try to recover from a lexer error that `parseError()` did not 'recover' for us\n            // by moving forward at least one character at a time IFF the (user-specified?) `parseError()`\n            // has not consumed/modified any pending input or changed state in the error handler:\n            if (!this.matches &&\n            // and make sure the input has been modified/consumed ...\n            pendingInput === this._input &&\n            // ...or the lexer state has been modified significantly enough\n            // to merit a non-consuming error handling action right now.\n            activeCondition === this.topState() && conditionStackDepth === this.conditionStack.length) {\n              this.input();\n            }\n          }\n          return token;\n        }\n      },\n      /**\n       * return next match that has a token\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      lex: function lexer_lex() {\n        var r;\n\n        // allow the PRE/POST handlers set/modify the return token for maximum flexibility of the generated lexer:\n        if (typeof this.pre_lex === 'function') {\n          r = this.pre_lex.call(this, 0);\n        }\n        if (typeof this.options.pre_lex === 'function') {\n          // (also account for a userdef function which does not return any value: keep the token as is)\n          r = this.options.pre_lex.call(this, r) || r;\n        }\n        if (this.yy && typeof this.yy.pre_lex === 'function') {\n          // (also account for a userdef function which does not return any value: keep the token as is)\n          r = this.yy.pre_lex.call(this, r) || r;\n        }\n        while (!r) {\n          r = this.next();\n        }\n        if (this.yy && typeof this.yy.post_lex === 'function') {\n          // (also account for a userdef function which does not return any value: keep the token as is)\n          r = this.yy.post_lex.call(this, r) || r;\n        }\n        if (typeof this.options.post_lex === 'function') {\n          // (also account for a userdef function which does not return any value: keep the token as is)\n          r = this.options.post_lex.call(this, r) || r;\n        }\n        if (typeof this.post_lex === 'function') {\n          // (also account for a userdef function which does not return any value: keep the token as is)\n          r = this.post_lex.call(this, r) || r;\n        }\n        return r;\n      },\n      /**\n       * return next match that has a token. Identical to the `lex()` API but does not invoke any of the \n       * `pre_lex()` nor any of the `post_lex()` callbacks.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      fastLex: function lexer_fastLex() {\n        var r;\n        while (!r) {\n          r = this.next();\n        }\n        return r;\n      },\n      /**\n       * return info about the lexer state that can help a parser or other lexer API user to use the\n       * most efficient means available. This API is provided to aid run-time performance for larger\n       * systems which employ this lexer.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      canIUse: function lexer_canIUse() {\n        var rv = {\n          fastLex: !(typeof this.pre_lex === 'function' || typeof this.options.pre_lex === 'function' || this.yy && typeof this.yy.pre_lex === 'function' || this.yy && typeof this.yy.post_lex === 'function' || typeof this.options.post_lex === 'function' || typeof this.post_lex === 'function') && typeof this.fastLex === 'function'\n        };\n        return rv;\n      },\n      /**\n       * backwards compatible alias for `pushState()`;\n       * the latter is symmetrical with `popState()` and we advise to use\n       * those APIs in any modern lexer code, rather than `begin()`.\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      begin: function lexer_begin(condition) {\n        return this.pushState(condition);\n      },\n      /**\n       * activates a new lexer condition state (pushes the new lexer\n       * condition state onto the condition stack)\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      pushState: function lexer_pushState(condition) {\n        this.conditionStack.push(condition);\n        this.__currentRuleSet__ = null;\n        return this;\n      },\n      /**\n       * pop the previously active lexer condition state off the condition\n       * stack\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      popState: function lexer_popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          this.__currentRuleSet__ = null;\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      /**\n       * return the currently active lexer condition state; when an index\n       * argument is provided it produces the N-th previous condition state,\n       * if available\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      topState: function lexer_topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return 'INITIAL';\n        }\n      },\n      /**\n       * (internal) determine the lexer rule set which is active for the\n       * currently active lexer condition state\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      _currentRules: function lexer__currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]];\n        } else {\n          return this.conditions['INITIAL'];\n        }\n      },\n      /**\n       * return the number of states currently on the stack\n       * \n       * @public\n       * @this {RegExpLexer}\n       */\n      stateStackSize: function lexer_stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: {\n        trackPosition: true\n      },\n      JisonLexerError: JisonLexerError,\n      performAction: function lexer__performAction(yy, yyrulenumber, YY_START) {\n        var yy_ = this;\n        var YYSTATE = YY_START;\n        switch (yyrulenumber) {\n          case 1:\n            /*! Conditions:: INITIAL */\n            /*! Rule::       \\s+ */\n            /* skip whitespace */\n            break;\n          default:\n            return this.simpleCaseActionClusters[yyrulenumber];\n        }\n      },\n      simpleCaseActionClusters: {\n        /*! Conditions:: INITIAL */\n        /*! Rule::       (--[0-9a-z-A-Z-]*) */\n        0: 13,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\* */\n        2: 5,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\/ */\n        3: 6,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\+ */\n        4: 3,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       - */\n        5: 4,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)px\\b */\n        6: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)cm\\b */\n        7: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)mm\\b */\n        8: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)in\\b */\n        9: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)pt\\b */\n        10: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)pc\\b */\n        11: 15,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)deg\\b */\n        12: 16,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)grad\\b */\n        13: 16,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)rad\\b */\n        14: 16,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)turn\\b */\n        15: 16,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)s\\b */\n        16: 17,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ms\\b */\n        17: 17,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)Hz\\b */\n        18: 18,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)kHz\\b */\n        19: 18,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dpi\\b */\n        20: 19,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dpcm\\b */\n        21: 19,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dppx\\b */\n        22: 19,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)em\\b */\n        23: 20,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ex\\b */\n        24: 21,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ch\\b */\n        25: 22,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)rem\\b */\n        26: 23,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vw\\b */\n        27: 25,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vh\\b */\n        28: 24,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vmin\\b */\n        29: 26,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vmax\\b */\n        30: 27,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)% */\n        31: 28,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)\\b */\n        32: 11,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       (calc) */\n        33: 9,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       (var) */\n        34: 12,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       ([a-z]+) */\n        35: 10,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\( */\n        36: 7,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\) */\n        37: 8,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       , */\n        38: 14,\n        /*! Conditions:: INITIAL */\n        /*! Rule::       $ */\n        39: 1\n      },\n      rules: [/*  0: *//^(?:(--[\\d\\-A-Za-z]*))/, /*  1: *//^(?:\\s+)/, /*  2: *//^(?:\\*)/, /*  3: *//^(?:\\/)/, /*  4: *//^(?:\\+)/, /*  5: *//^(?:-)/, /*  6: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)px\\b)/, /*  7: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)cm\\b)/, /*  8: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)mm\\b)/, /*  9: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)in\\b)/, /* 10: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)pt\\b)/, /* 11: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)pc\\b)/, /* 12: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)deg\\b)/, /* 13: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)grad\\b)/, /* 14: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)rad\\b)/, /* 15: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)turn\\b)/, /* 16: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)s\\b)/, /* 17: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)ms\\b)/, /* 18: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)Hz\\b)/, /* 19: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)kHz\\b)/, /* 20: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)dpi\\b)/, /* 21: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)dpcm\\b)/, /* 22: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)dppx\\b)/, /* 23: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)em\\b)/, /* 24: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)ex\\b)/, /* 25: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)ch\\b)/, /* 26: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)rem\\b)/, /* 27: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)vw\\b)/, /* 28: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)vh\\b)/, /* 29: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)vmin\\b)/, /* 30: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)vmax\\b)/, /* 31: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)%)/, /* 32: *//^(?:(\\d+(\\.\\d*)?|\\.\\d+)\\b)/, /* 33: *//^(?:(calc))/, /* 34: *//^(?:(var))/, /* 35: *//^(?:([a-z]+))/, /* 36: *//^(?:\\()/, /* 37: *//^(?:\\))/, /* 38: *//^(?:,)/, /* 39: *//^(?:$)/],\n      conditions: {\n        'INITIAL': {\n          rules: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39],\n          inclusive: true\n        }\n      }\n    };\n    return lexer;\n  }();\n  parser.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser;\n  parser.Parser = Parser;\n  return new Parser();\n}();\nif (typeof require !== 'undefined' && typeof exports !== 'undefined') {\n  exports.parser = parser;\n  exports.Parser = parser.Parser;\n  exports.parse = function () {\n    return parser.parse.apply(parser, arguments);\n  };\n}", "map": {"version": 3, "names": ["parser", "JisonParserError", "msg", "hash", "Object", "defineProperty", "enumerable", "writable", "value", "stacktrace", "exception", "Error", "ex2", "message", "stack", "hasOwnProperty", "captureStackTrace", "constructor", "setPrototypeOf", "prototype", "create", "name", "bp", "s", "rv", "p", "pop", "r", "rule", "i", "l", "length", "push", "bda", "d", "idx", "g", "goto", "j", "bt", "len", "y", "symbol", "t", "type", "a", "state", "m", "mode", "n", "q", "z", "shift", "c", "u", "e", "apply", "trace", "no_op_trace", "yy", "options", "hasPartialLrUpgradeOnConflict", "errorRecoveryTokenDiscardCount", "symbols_", "terminals_", "TERROR", "EOF", "originalQuoteName", "originalParseError", "cleanupAfterParse", "constructParseErrorInfo", "yyMergeLocationInfo", "__reentrant_call_depth", "__error_infos", "__error_recovery_infos", "quoteName", "parser_quoteName", "id_str", "getSymbolName", "parser_getSymbolName", "key", "describeSymbol", "parser_describeSymbol", "terminal_descriptions_", "id", "collect_expected_token_set", "parser_collect_expected_token_set", "do_not_describe", "tokenset", "check", "state_descriptions_", "table", "productions_", "performAction", "parser__PerformAction", "yystate", "yysp", "yyvstack", "yy<PERSON><PERSON>", "yylexer", "lexer", "$", "operator", "left", "right", "prefix", "parseFloat", "fallback", "unit", "exec", "prev", "defaultActions", "parseError", "str", "ExceptionClass", "recoverable", "destroy", "parse", "input", "self", "Array", "sstack", "vstack", "sp", "ERROR_RECOVERY_TOKEN_DISCARD_COUNT", "NO_ACTION", "__lexer__", "sharedState_yy", "undefined", "pre_parse", "post_parse", "pre_lex", "post_lex", "ASSERT", "assert", "JisonAssert", "cond", "yyGetSharedState", "shallow_copy_noclobber", "dst", "src", "k", "call", "parseErrorAlt", "quoteNameAlt", "parser_cleanupAfterParse", "resultValue", "invoke_post_methods", "do_not_nuke_errorinfos", "cleanupAfterLex", "el", "parser_constructParseErrorInfo", "ex", "expected", "pei", "errStr", "text", "match", "yytext", "token", "token_id", "line", "y<PERSON><PERSON>o", "action", "new_state", "newState", "symbol_stack", "state_stack", "value_stack", "stack_pointer", "destructParseErrorInfo", "rec", "getNonTerminalFromCode", "tokenName", "stdLex", "lex", "fastLex", "yyval", "_$", "yyr<PERSON><PERSON>", "this_production", "retval", "setInput", "canIUse", "lexerInfo", "errSymbolDescr", "showPosition", "join", "ntsymbol", "JisonLexerError", "ERROR", "__currentRuleSet__", "__decompressed", "done", "_backtrack", "_input", "_more", "_signaled_error_token", "conditionStack", "matched", "matches", "offset", "yyleng", "yylloc", "constructLexErrorInfo", "lexer_constructLexErrorInfo", "show_input_position", "indexOf", "prettyPrintRange", "pretty_src", "test", "pos_str", "loc", "destructLexErrorInfo", "lexer_parseError", "y<PERSON><PERSON><PERSON>", "yyError", "lineno_msg", "lexerErrorsAreRecoverable", "args", "slice", "arguments", "extra_error_attributes", "lexer_cleanupAfterLex", "clear", "lexer_clear", "col", "last_column", "first_line", "first_column", "last_line", "range", "lexer_setInput", "rules", "rule_re", "conditions", "spec", "rule_ids", "rule_regexes", "rule_new_ids", "__rule_regexes", "__rule_count", "editRemainingInput", "lexer_editRemainingInput", "callback", "cpsArg", "lexer_input", "ch", "slice_len", "lines", "ch2", "unput", "lexer_unput", "split", "substr", "pre", "pre_lines", "more", "lexer_more", "reject", "lexer_reject", "backtrack_lexer", "less", "lexer_less", "pastInput", "lexer_pastInput", "maxSize", "maxLines", "past", "substring", "replace", "upcomingInput", "lexer_upcomingInput", "next", "lexer_showPosition", "maxPrefix", "maxPostfix", "deriveLocationInfo", "lexer_deriveYYLLOC", "actual", "preceding", "following", "current", "lexer_prettyPrintRange", "context_loc", "context_loc2", "CONTEXT", "CONTEXT_TAIL", "MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT", "l0", "Math", "max", "l1", "lineno_display_width", "log10", "ws_prefix", "nonempty_line_indexes", "map", "injectLineNumber", "index", "lno", "lno_pfx", "errpfx", "lead", "mark", "trim", "clip_start", "clip_end", "intermediate_line", "splice", "describeYYLLOC", "lexer_describe_yylloc", "display_range_too", "l2", "c1", "c2", "dl", "dc", "r1", "r2", "test_match", "lexer_test_match", "indexed_rule", "backup", "match_str", "match_str_len", "lexer_next", "tempMatch", "_currentRules", "trackPosition", "topState", "regexes", "flex", "pendingInput", "activeCondition", "condition<PERSON><PERSON><PERSON><PERSON>epth", "lexer_lex", "lexer_fastLex", "lexer_canIUse", "begin", "lexer_begin", "condition", "pushState", "lexer_pushState", "popState", "lexer_popState", "lexer_topState", "abs", "lexer__currentRules", "stateStackSize", "lexer_stateStackSize", "lexer__performAction", "yyrulenumber", "YY_START", "yy_", "YYSTATE", "simpleCaseActionClusters", "inclusive", "<PERSON><PERSON><PERSON>", "require", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/dist/parser.js"], "sourcesContent": ["\n/* parser generated by jison 0.6.1-215 */\n\n/*\n * Returns a Parser object of the following structure:\n *\n *  Parser: {\n *    yy: {}     The so-called \"shared state\" or rather the *source* of it;\n *               the real \"shared state\" `yy` passed around to\n *               the rule actions, etc. is a derivative/copy of this one,\n *               not a direct reference!\n *  }\n *\n *  Parser.prototype: {\n *    yy: {},\n *    EOF: 1,\n *    TERROR: 2,\n *\n *    trace: function(errorMessage, ...),\n *\n *    JisonParserError: function(msg, hash),\n *\n *    quoteName: function(name),\n *               Helper function which can be overridden by user code later on: put suitable\n *               quotes around literal IDs in a description string.\n *\n *    originalQuoteName: function(name),\n *               The basic quoteName handler provided by JISON.\n *               `cleanupAfterParse()` will clean up and reset `quoteName()` to reference this function\n *               at the end of the `parse()`.\n *\n *    describeSymbol: function(symbol),\n *               Return a more-or-less human-readable description of the given symbol, when\n *               available, or the symbol itself, serving as its own 'description' for lack\n *               of something better to serve up.\n *\n *               Return NULL when the symbol is unknown to the parser.\n *\n *    symbols_: {associative list: name ==> number},\n *    terminals_: {associative list: number ==> name},\n *    nonterminals: {associative list: rule-name ==> {associative list: number ==> rule-alt}},\n *    terminal_descriptions_: (if there are any) {associative list: number ==> description},\n *    productions_: [...],\n *\n *    performAction: function parser__performAction(yytext, yyleng, yylineno, yyloc, yystate, yysp, yyvstack, yylstack, yystack, yysstack),\n *\n *               The function parameters and `this` have the following value/meaning:\n *               - `this`    : reference to the `yyval` internal object, which has members (`$` and `_$`)\n *                             to store/reference the rule value `$$` and location info `@$`.\n *\n *                 One important thing to note about `this` a.k.a. `yyval`: every *reduce* action gets\n *                 to see the same object via the `this` reference, i.e. if you wish to carry custom\n *                 data from one reduce action through to the next within a single parse run, then you\n *                 may get nasty and use `yyval` a.k.a. `this` for storing you own semi-permanent data.\n *\n *                 `this.yy` is a direct reference to the `yy` shared state object.\n *\n *                 `%parse-param`-specified additional `parse()` arguments have been added to this `yy`\n *                 object at `parse()` start and are therefore available to the action code via the\n *                 same named `yy.xxxx` attributes (where `xxxx` represents a identifier name from\n *                 the %parse-param` list.\n *\n *               - `yytext`  : reference to the lexer value which belongs to the last lexer token used\n *                             to match this rule. This is *not* the look-ahead token, but the last token\n *                             that's actually part of this rule.\n *\n *                 Formulated another way, `yytext` is the value of the token immediately preceeding\n *                 the current look-ahead token.\n *                 Caveats apply for rules which don't require look-ahead, such as epsilon rules.\n *\n *               - `yyleng`  : ditto as `yytext`, only now for the lexer.yyleng value.\n *\n *               - `yylineno`: ditto as `yytext`, only now for the lexer.yylineno value.\n *\n *               - `yyloc`   : ditto as `yytext`, only now for the lexer.yylloc lexer token location info.\n *\n *                               WARNING: since jison 0.4.18-186 this entry may be NULL/UNDEFINED instead\n *                               of an empty object when no suitable location info can be provided.\n *\n *               - `yystate` : the current parser state number, used internally for dispatching and\n *                               executing the action code chunk matching the rule currently being reduced.\n *\n *               - `yysp`    : the current state stack position (a.k.a. 'stack pointer')\n *\n *                 This one comes in handy when you are going to do advanced things to the parser\n *                 stacks, all of which are accessible from your action code (see the next entries below).\n *\n *                 Also note that you can access this and other stack index values using the new double-hash\n *                 syntax, i.e. `##$ === ##0 === yysp`, while `##1` is the stack index for all things\n *                 related to the first rule term, just like you have `$1`, `@1` and `#1`.\n *                 This is made available to write very advanced grammar action rules, e.g. when you want\n *                 to investigate the parse state stack in your action code, which would, for example,\n *                 be relevant when you wish to implement error diagnostics and reporting schemes similar\n *                 to the work described here:\n *\n *                 + Pottier, F., 2016. Reachability and error diagnosis in LR(1) automata.\n *                   In Journées Francophones des Languages Applicatifs.\n *\n *                 + Jeffery, C.L., 2003. Generating LR syntax error messages from examples.\n *                   ACM Transactions on Programming Languages and Systems (TOPLAS), 25(5), pp.631–640.\n *\n *               - `yyrulelength`: the current rule's term count, i.e. the number of entries occupied on the stack.\n *\n *                 This one comes in handy when you are going to do advanced things to the parser\n *                 stacks, all of which are accessible from your action code (see the next entries below).\n *\n *               - `yyvstack`: reference to the parser value stack. Also accessed via the `$1` etc.\n *                             constructs.\n *\n *               - `yylstack`: reference to the parser token location stack. Also accessed via\n *                             the `@1` etc. constructs.\n *\n *                             WARNING: since jison 0.4.18-186 this array MAY contain slots which are\n *                             UNDEFINED rather than an empty (location) object, when the lexer/parser\n *                             action code did not provide a suitable location info object when such a\n *                             slot was filled!\n *\n *               - `yystack` : reference to the parser token id stack. Also accessed via the\n *                             `#1` etc. constructs.\n *\n *                 Note: this is a bit of a **white lie** as we can statically decode any `#n` reference to\n *                 its numeric token id value, hence that code wouldn't need the `yystack` but *you* might\n *                 want access this array for your own purposes, such as error analysis as mentioned above!\n *\n *                 Note that this stack stores the current stack of *tokens*, that is the sequence of\n *                 already parsed=reduced *nonterminals* (tokens representing rules) and *terminals*\n *                 (lexer tokens *shifted* onto the stack until the rule they belong to is found and\n *                 *reduced*.\n *\n *               - `yysstack`: reference to the parser state stack. This one carries the internal parser\n *                             *states* such as the one in `yystate`, which are used to represent\n *                             the parser state machine in the *parse table*. *Very* *internal* stuff,\n *                             what can I say? If you access this one, you're clearly doing wicked things\n *\n *               - `...`     : the extra arguments you specified in the `%parse-param` statement in your\n *                             grammar definition file.\n *\n *    table: [...],\n *               State transition table\n *               ----------------------\n *\n *               index levels are:\n *               - `state`  --> hash table\n *               - `symbol` --> action (number or array)\n *\n *                 If the `action` is an array, these are the elements' meaning:\n *                 - index [0]: 1 = shift, 2 = reduce, 3 = accept\n *                 - index [1]: GOTO `state`\n *\n *                 If the `action` is a number, it is the GOTO `state`\n *\n *    defaultActions: {...},\n *\n *    parseError: function(str, hash, ExceptionClass),\n *    yyError: function(str, ...),\n *    yyRecovering: function(),\n *    yyErrOk: function(),\n *    yyClearIn: function(),\n *\n *    constructParseErrorInfo: function(error_message, exception_object, expected_token_set, is_recoverable),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               Produces a new errorInfo 'hash object' which can be passed into `parseError()`.\n *               See it's use in this parser kernel in many places; example usage:\n *\n *                   var infoObj = parser.constructParseErrorInfo('fail!', null,\n *                                     parser.collect_expected_token_set(state), true);\n *                   var retVal = parser.parseError(infoObj.errStr, infoObj, parser.JisonParserError);\n *\n *    originalParseError: function(str, hash, ExceptionClass),\n *               The basic `parseError` handler provided by JISON.\n *               `cleanupAfterParse()` will clean up and reset `parseError()` to reference this function\n *               at the end of the `parse()`.\n *\n *    options: { ... parser %options ... },\n *\n *    parse: function(input[, args...]),\n *               Parse the given `input` and return the parsed value (or `true` when none was provided by\n *               the root action, in which case the parser is acting as a *matcher*).\n *               You MAY use the additional `args...` parameters as per `%parse-param` spec of this grammar:\n *               these extra `args...` are added verbatim to the `yy` object reference as member variables.\n *\n *               WARNING:\n *               Parser's additional `args...` parameters (via `%parse-param`) MAY conflict with\n *               any attributes already added to `yy` by the jison run-time;\n *               when such a collision is detected an exception is thrown to prevent the generated run-time\n *               from silently accepting this confusing and potentially hazardous situation!\n *\n *               The lexer MAY add its own set of additional parameters (via the `%parse-param` line in\n *               the lexer section of the grammar spec): these will be inserted in the `yy` shared state\n *               object and any collision with those will be reported by the lexer via a thrown exception.\n *\n *    cleanupAfterParse: function(resultValue, invoke_post_methods, do_not_nuke_errorinfos),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               This helper API is invoked at the end of the `parse()` call, unless an exception was thrown\n *               and `%options no-try-catch` has been defined for this grammar: in that case this helper MAY\n *               be invoked by calling user code to ensure the `post_parse` callbacks are invoked and\n *               the internal parser gets properly garbage collected under these particular circumstances.\n *\n *    yyMergeLocationInfo: function(first_index, last_index, first_yylloc, last_yylloc, dont_look_back),\n *               Helper function **which will be set up during the first invocation of the `parse()` method**.\n *               This helper API can be invoked to calculate a spanning `yylloc` location info object.\n *\n *               Note: %epsilon rules MAY specify no `first_index` and `first_yylloc`, in which case\n *               this function will attempt to obtain a suitable location marker by inspecting the location stack\n *               backwards.\n *\n *               For more info see the documentation comment further below, immediately above this function's\n *               implementation.\n *\n *    lexer: {\n *        yy: {...},           A reference to the so-called \"shared state\" `yy` once\n *                             received via a call to the `.setInput(input, yy)` lexer API.\n *        EOF: 1,\n *        ERROR: 2,\n *        JisonLexerError: function(msg, hash),\n *        parseError: function(str, hash, ExceptionClass),\n *        setInput: function(input, [yy]),\n *        input: function(),\n *        unput: function(str),\n *        more: function(),\n *        reject: function(),\n *        less: function(n),\n *        pastInput: function(n),\n *        upcomingInput: function(n),\n *        showPosition: function(),\n *        test_match: function(regex_match_array, rule_index, ...),\n *        next: function(...),\n *        lex: function(...),\n *        begin: function(condition),\n *        pushState: function(condition),\n *        popState: function(),\n *        topState: function(),\n *        _currentRules: function(),\n *        stateStackSize: function(),\n *        cleanupAfterLex: function()\n *\n *        options: { ... lexer %options ... },\n *\n *        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START, ...),\n *        rules: [...],\n *        conditions: {associative list: name ==> set},\n *    }\n *  }\n *\n *\n *  token location info (@$, _$, etc.): {\n *    first_line: n,\n *    last_line: n,\n *    first_column: n,\n *    last_column: n,\n *    range: [start_number, end_number]\n *               (where the numbers are indexes into the input string, zero-based)\n *  }\n *\n * ---\n *\n * The `parseError` function receives a 'hash' object with these members for lexer and\n * parser errors:\n *\n *  {\n *    text:        (matched text)\n *    token:       (the produced terminal token, if any)\n *    token_id:    (the produced terminal token numeric ID, if any)\n *    line:        (yylineno)\n *    loc:         (yylloc)\n *  }\n *\n * parser (grammar) errors will also provide these additional members:\n *\n *  {\n *    expected:    (array describing the set of expected tokens;\n *                  may be UNDEFINED when we cannot easily produce such a set)\n *    state:       (integer (or array when the table includes grammar collisions);\n *                  represents the current internal state of the parser kernel.\n *                  can, for example, be used to pass to the `collect_expected_token_set()`\n *                  API to obtain the expected token set)\n *    action:      (integer; represents the current internal action which will be executed)\n *    new_state:   (integer; represents the next/planned internal state, once the current\n *                  action has executed)\n *    recoverable: (boolean: TRUE when the parser MAY have an error recovery rule\n *                  available for this particular error)\n *    state_stack: (array: the current parser LALR/LR internal state stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    value_stack: (array: the current parser LALR/LR internal `$$` value stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    location_stack: (array: the current parser LALR/LR internal location stack; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    yy:          (object: the current parser internal \"shared state\" `yy`\n *                  as is also available in the rule actions; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    lexer:       (reference to the current lexer instance used by the parser)\n *    parser:      (reference to the current parser instance)\n *  }\n *\n * while `this` will reference the current parser instance.\n *\n * When `parseError` is invoked by the lexer, `this` will still reference the related *parser*\n * instance, while these additional `hash` fields will also be provided:\n *\n *  {\n *    lexer:       (reference to the current lexer instance which reported the error)\n *  }\n *\n * When `parseError` is invoked by the parser due to a **JavaScript exception** being fired\n * from either the parser or lexer, `this` will still reference the related *parser*\n * instance, while these additional `hash` fields will also be provided:\n *\n *  {\n *    exception:   (reference to the exception thrown)\n *  }\n *\n * Please do note that in the latter situation, the `expected` field will be omitted as\n * this type of failure is assumed not to be due to *parse errors* but rather due to user\n * action code in either parser or lexer failing unexpectedly.\n *\n * ---\n *\n * You can specify parser options by setting / modifying the `.yy` object of your Parser instance.\n * These options are available:\n *\n * ### options which are global for all parser instances\n *\n *  Parser.pre_parse: function(yy)\n *                 optional: you can specify a pre_parse() function in the chunk following\n *                 the grammar, i.e. after the last `%%`.\n *  Parser.post_parse: function(yy, retval, parseInfo) { return retval; }\n *                 optional: you can specify a post_parse() function in the chunk following\n *                 the grammar, i.e. after the last `%%`. When it does not return any value,\n *                 the parser will return the original `retval`.\n *\n * ### options which can be set up per parser instance\n *\n *  yy: {\n *      pre_parse:  function(yy)\n *                 optional: is invoked before the parse cycle starts (and before the first\n *                 invocation of `lex()`) but immediately after the invocation of\n *                 `parser.pre_parse()`).\n *      post_parse: function(yy, retval, parseInfo) { return retval; }\n *                 optional: is invoked when the parse terminates due to success ('accept')\n *                 or failure (even when exceptions are thrown).\n *                 `retval` contains the return value to be produced by `Parser.parse()`;\n *                 this function can override the return value by returning another.\n *                 When it does not return any value, the parser will return the original\n *                 `retval`.\n *                 This function is invoked immediately before `parser.post_parse()`.\n *\n *      parseError: function(str, hash, ExceptionClass)\n *                 optional: overrides the default `parseError` function.\n *      quoteName: function(name),\n *                 optional: overrides the default `quoteName` function.\n *  }\n *\n *  parser.lexer.options: {\n *      pre_lex:  function()\n *                 optional: is invoked before the lexer is invoked to produce another token.\n *                 `this` refers to the Lexer object.\n *      post_lex: function(token) { return token; }\n *                 optional: is invoked when the lexer has produced a token `token`;\n *                 this function can override the returned token value by returning another.\n *                 When it does not return any (truthy) value, the lexer will return\n *                 the original `token`.\n *                 `this` refers to the Lexer object.\n *\n *      ranges: boolean\n *                 optional: `true` ==> token location info will include a .range[] member.\n *      flex: boolean\n *                 optional: `true` ==> flex-like lexing behaviour where the rules are tested\n *                 exhaustively to find the longest match.\n *      backtrack_lexer: boolean\n *                 optional: `true` ==> lexer regexes are tested in order and for invoked;\n *                 the lexer terminates the scan when a token is returned by the action code.\n *      xregexp: boolean\n *                 optional: `true` ==> lexer rule regexes are \"extended regex format\" requiring the\n *                 `XRegExp` library. When this `%option` has not been specified at compile time, all lexer\n *                 rule regexes have been written as standard JavaScript RegExp expressions.\n *  }\n */\n\n        \n    \n            var parser = (function () {\n\n\n// See also:\n// http://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript/#35881508\n// but we keep the prototype.constructor and prototype.name assignment lines too for compatibility\n// with userland code which might access the derived class in a 'classic' way.\nfunction JisonParserError(msg, hash) {\n    Object.defineProperty(this, 'name', {\n        enumerable: false,\n        writable: false,\n        value: 'JisonParserError'\n    });\n\n    if (msg == null) msg = '???';\n\n    Object.defineProperty(this, 'message', {\n        enumerable: false,\n        writable: true,\n        value: msg\n    });\n\n    this.hash = hash;\n\n    var stacktrace;\n    if (hash && hash.exception instanceof Error) {\n        var ex2 = hash.exception;\n        this.message = ex2.message || msg;\n        stacktrace = ex2.stack;\n    }\n    if (!stacktrace) {\n        if (Error.hasOwnProperty('captureStackTrace')) {        // V8/Chrome engine\n            Error.captureStackTrace(this, this.constructor);\n        } else {\n            stacktrace = (new Error(msg)).stack;\n        }\n    }\n    if (stacktrace) {\n        Object.defineProperty(this, 'stack', {\n            enumerable: false,\n            writable: false,\n            value: stacktrace\n        });\n    }\n}\n\nif (typeof Object.setPrototypeOf === 'function') {\n    Object.setPrototypeOf(JisonParserError.prototype, Error.prototype);\n} else {\n    JisonParserError.prototype = Object.create(Error.prototype);\n}\nJisonParserError.prototype.constructor = JisonParserError;\nJisonParserError.prototype.name = 'JisonParserError';\n\n\n\n\n        // helper: reconstruct the productions[] table\n        function bp(s) {\n            var rv = [];\n            var p = s.pop;\n            var r = s.rule;\n            for (var i = 0, l = p.length; i < l; i++) {\n                rv.push([\n                    p[i],\n                    r[i]\n                ]);\n            }\n            return rv;\n        }\n    \n\n\n        // helper: reconstruct the defaultActions[] table\n        function bda(s) {\n            var rv = {};\n            var d = s.idx;\n            var g = s.goto;\n            for (var i = 0, l = d.length; i < l; i++) {\n                var j = d[i];\n                rv[j] = g[i];\n            }\n            return rv;\n        }\n    \n\n\n        // helper: reconstruct the 'goto' table\n        function bt(s) {\n            var rv = [];\n            var d = s.len;\n            var y = s.symbol;\n            var t = s.type;\n            var a = s.state;\n            var m = s.mode;\n            var g = s.goto;\n            for (var i = 0, l = d.length; i < l; i++) {\n                var n = d[i];\n                var q = {};\n                for (var j = 0; j < n; j++) {\n                    var z = y.shift();\n                    switch (t.shift()) {\n                    case 2:\n                        q[z] = [\n                            m.shift(),\n                            g.shift()\n                        ];\n                        break;\n\n                    case 0:\n                        q[z] = a.shift();\n                        break;\n\n                    default:\n                        // type === 1: accept\n                        q[z] = [\n                            3\n                        ];\n                    }\n                }\n                rv.push(q);\n            }\n            return rv;\n        }\n    \n\n\n        // helper: runlength encoding with increment step: code, length: step (default step = 0)\n        // `this` references an array\n        function s(c, l, a) {\n            a = a || 0;\n            for (var i = 0; i < l; i++) {\n                this.push(c);\n                c += a;\n            }\n        }\n\n        // helper: duplicate sequence from *relative* offset and length.\n        // `this` references an array\n        function c(i, l) {\n            i = this.length - i;\n            for (l += i; i < l; i++) {\n                this.push(this[i]);\n            }\n        }\n\n        // helper: unpack an array using helpers and data, all passed in an array argument 'a'.\n        function u(a) {\n            var rv = [];\n            for (var i = 0, l = a.length; i < l; i++) {\n                var e = a[i];\n                // Is this entry a helper function?\n                if (typeof e === 'function') {\n                    i++;\n                    e.apply(rv, a[i]);\n                } else {\n                    rv.push(e);\n                }\n            }\n            return rv;\n        }\n    \n\nvar parser = {\n    // Code Generator Information Report\n    // ---------------------------------\n    //\n    // Options:\n    //\n    //   default action mode: ............. [\"classic\",\"merge\"]\n    //   test-compile action mode: ........ \"parser:*,lexer:*\"\n    //   try..catch: ...................... true\n    //   default resolve on conflict: ..... true\n    //   on-demand look-ahead: ............ false\n    //   error recovery token skip maximum: 3\n    //   yyerror in parse actions is: ..... NOT recoverable,\n    //   yyerror in lexer actions and other non-fatal lexer are:\n    //   .................................. NOT recoverable,\n    //   debug grammar/output: ............ false\n    //   has partial LR conflict upgrade:   true\n    //   rudimentary token-stack support:   false\n    //   parser table compression mode: ... 2\n    //   export debug tables: ............. false\n    //   export *all* tables: ............. false\n    //   module type: ..................... commonjs\n    //   parser engine type: .............. lalr\n    //   output main() in the module: ..... true\n    //   has user-specified main(): ....... false\n    //   has user-specified require()/import modules for main():\n    //   .................................. false\n    //   number of expected conflicts: .... 0\n    //\n    //\n    // Parser Analysis flags:\n    //\n    //   no significant actions (parser is a language matcher only):\n    //   .................................. false\n    //   uses yyleng: ..................... false\n    //   uses yylineno: ................... false\n    //   uses yytext: ..................... false\n    //   uses yylloc: ..................... false\n    //   uses ParseError API: ............. false\n    //   uses YYERROR: .................... false\n    //   uses YYRECOVERING: ............... false\n    //   uses YYERROK: .................... false\n    //   uses YYCLEARIN: .................. false\n    //   tracks rule values: .............. true\n    //   assigns rule values: ............. true\n    //   uses location tracking: .......... false\n    //   assigns location: ................ false\n    //   uses yystack: .................... false\n    //   uses yysstack: ................... false\n    //   uses yysp: ....................... true\n    //   uses yyrulelength: ............... false\n    //   uses yyMergeLocationInfo API: .... false\n    //   has error recovery: .............. false\n    //   has error reporting: ............. false\n    //\n    // --------- END OF REPORT -----------\n\ntrace: function no_op_trace() { },\nJisonParserError: JisonParserError,\nyy: {},\noptions: {\n  type: \"lalr\",\n  hasPartialLrUpgradeOnConflict: true,\n  errorRecoveryTokenDiscardCount: 3\n},\nsymbols_: {\n  \"$accept\": 0,\n  \"$end\": 1,\n  \"ADD\": 3,\n  \"ANGLE\": 16,\n  \"CHS\": 22,\n  \"COMMA\": 14,\n  \"CSS_CPROP\": 13,\n  \"CSS_VAR\": 12,\n  \"DIV\": 6,\n  \"EMS\": 20,\n  \"EOF\": 1,\n  \"EXS\": 21,\n  \"FREQ\": 18,\n  \"LENGTH\": 15,\n  \"LPAREN\": 7,\n  \"MUL\": 5,\n  \"NESTED_CALC\": 9,\n  \"NUMBER\": 11,\n  \"PERCENTAGE\": 28,\n  \"PREFIX\": 10,\n  \"REMS\": 23,\n  \"RES\": 19,\n  \"RPAREN\": 8,\n  \"SUB\": 4,\n  \"TIME\": 17,\n  \"VHS\": 24,\n  \"VMAXS\": 27,\n  \"VMINS\": 26,\n  \"VWS\": 25,\n  \"css_value\": 33,\n  \"css_variable\": 32,\n  \"error\": 2,\n  \"expression\": 29,\n  \"math_expression\": 30,\n  \"value\": 31\n},\nterminals_: {\n  1: \"EOF\",\n  2: \"error\",\n  3: \"ADD\",\n  4: \"SUB\",\n  5: \"MUL\",\n  6: \"DIV\",\n  7: \"LPAREN\",\n  8: \"RPAREN\",\n  9: \"NESTED_CALC\",\n  10: \"PREFIX\",\n  11: \"NUMBER\",\n  12: \"CSS_VAR\",\n  13: \"CSS_CPROP\",\n  14: \"COMMA\",\n  15: \"LENGTH\",\n  16: \"ANGLE\",\n  17: \"TIME\",\n  18: \"FREQ\",\n  19: \"RES\",\n  20: \"EMS\",\n  21: \"EXS\",\n  22: \"CHS\",\n  23: \"REMS\",\n  24: \"VHS\",\n  25: \"VWS\",\n  26: \"VMINS\",\n  27: \"VMAXS\",\n  28: \"PERCENTAGE\"\n},\nTERROR: 2,\n    EOF: 1,\n\n    // internals: defined here so the object *structure* doesn't get modified by parse() et al,\n    // thus helping JIT compilers like Chrome V8.\n    originalQuoteName: null,\n    originalParseError: null,\n    cleanupAfterParse: null,\n    constructParseErrorInfo: null,\n    yyMergeLocationInfo: null,\n\n    __reentrant_call_depth: 0,      // INTERNAL USE ONLY\n    __error_infos: [],              // INTERNAL USE ONLY: the set of parseErrorInfo objects created since the last cleanup\n    __error_recovery_infos: [],     // INTERNAL USE ONLY: the set of parseErrorInfo objects created since the last cleanup\n\n    // APIs which will be set up depending on user action code analysis:\n    //yyRecovering: 0,\n    //yyErrOk: 0,\n    //yyClearIn: 0,\n\n    // Helper APIs\n    // -----------\n\n    // Helper function which can be overridden by user code later on: put suitable quotes around\n    // literal IDs in a description string.\n    quoteName: function parser_quoteName(id_str) {\n        return '\"' + id_str + '\"';\n    },\n\n    // Return the name of the given symbol (terminal or non-terminal) as a string, when available.\n    //\n    // Return NULL when the symbol is unknown to the parser.\n    getSymbolName: function parser_getSymbolName(symbol) {\n        if (this.terminals_[symbol]) {\n            return this.terminals_[symbol];\n        }\n\n        // Otherwise... this might refer to a RULE token i.e. a non-terminal: see if we can dig that one up.\n        //\n        // An example of this may be where a rule's action code contains a call like this:\n        //\n        //      parser.getSymbolName(#$)\n        //\n        // to obtain a human-readable name of the current grammar rule.\n        var s = this.symbols_;\n        for (var key in s) {\n            if (s[key] === symbol) {\n                return key;\n            }\n        }\n        return null;\n    },\n\n    // Return a more-or-less human-readable description of the given symbol, when available,\n    // or the symbol itself, serving as its own 'description' for lack of something better to serve up.\n    //\n    // Return NULL when the symbol is unknown to the parser.\n    describeSymbol: function parser_describeSymbol(symbol) {\n        if (symbol !== this.EOF && this.terminal_descriptions_ && this.terminal_descriptions_[symbol]) {\n            return this.terminal_descriptions_[symbol];\n        }\n        else if (symbol === this.EOF) {\n            return 'end of input';\n        }\n        var id = this.getSymbolName(symbol);\n        if (id) {\n            return this.quoteName(id);\n        }\n        return null;\n    },\n\n    // Produce a (more or less) human-readable list of expected tokens at the point of failure.\n    //\n    // The produced list may contain token or token set descriptions instead of the tokens\n    // themselves to help turning this output into something that easier to read by humans\n    // unless `do_not_describe` parameter is set, in which case a list of the raw, *numeric*,\n    // expected terminals and nonterminals is produced.\n    //\n    // The returned list (array) will not contain any duplicate entries.\n    collect_expected_token_set: function parser_collect_expected_token_set(state, do_not_describe) {\n        var TERROR = this.TERROR;\n        var tokenset = [];\n        var check = {};\n        // Has this (error?) state been outfitted with a custom expectations description text for human consumption?\n        // If so, use that one instead of the less palatable token set.\n        if (!do_not_describe && this.state_descriptions_ && this.state_descriptions_[state]) {\n            return [\n                this.state_descriptions_[state]\n            ];\n        }\n        for (var p in this.table[state]) {\n            p = +p;\n            if (p !== TERROR) {\n                var d = do_not_describe ? p : this.describeSymbol(p);\n                if (d && !check[d]) {\n                    tokenset.push(d);\n                    check[d] = true;        // Mark this token description as already mentioned to prevent outputting duplicate entries.\n                }\n            }\n        }\n        return tokenset;\n    },\nproductions_: bp({\n  pop: u([\n  29,\n  s,\n  [30, 10],\n  31,\n  31,\n  32,\n  32,\n  s,\n  [33, 15]\n]),\n  rule: u([\n  2,\n  s,\n  [3, 5],\n  4,\n  7,\n  s,\n  [1, 4],\n  2,\n  4,\n  6,\n  s,\n  [1, 14],\n  2\n])\n}),\nperformAction: function parser__PerformAction(yystate /* action[1] */, yysp, yyvstack) {\n\n          /* this == yyval */\n\n          // the JS engine itself can go and remove these statements when `yy` turns out to be unused in any action code!\n          var yy = this.yy;\n          var yyparser = yy.parser;\n          var yylexer = yy.lexer;\n\n          \n\n          switch (yystate) {\ncase 0:\n    /*! Production::    $accept : expression $end */\n\n    // default action (generated by JISON mode classic/merge :: 1,VT,VA,-,-,-,-,-,-):\n    this.$ = yyvstack[yysp - 1];\n    // END of default action (generated by JISON mode classic/merge :: 1,VT,VA,-,-,-,-,-,-)\n    break;\n\ncase 1:\n    /*! Production::    expression : math_expression EOF */\n\n    // default action (generated by JISON mode classic/merge :: 2,VT,VA,-,-,-,-,-,-):\n    this.$ = yyvstack[yysp - 1];\n    // END of default action (generated by JISON mode classic/merge :: 2,VT,VA,-,-,-,-,-,-)\n    \n    \n    return yyvstack[yysp - 1];\n    break;\n\ncase 2:\n    /*! Production::    math_expression : math_expression ADD math_expression */\ncase 3:\n    /*! Production::    math_expression : math_expression SUB math_expression */\ncase 4:\n    /*! Production::    math_expression : math_expression MUL math_expression */\ncase 5:\n    /*! Production::    math_expression : math_expression DIV math_expression */\n\n    this.$ = { type: 'MathExpression', operator: yyvstack[yysp - 1], left: yyvstack[yysp - 2], right: yyvstack[yysp] };\n    break;\n\ncase 6:\n    /*! Production::    math_expression : LPAREN math_expression RPAREN */\n\n    this.$ = yyvstack[yysp - 1];\n    break;\n\ncase 7:\n    /*! Production::    math_expression : NESTED_CALC LPAREN math_expression RPAREN */\n\n    this.$ = { type: 'Calc', value: yyvstack[yysp - 1] };\n    break;\n\ncase 8:\n    /*! Production::    math_expression : SUB PREFIX SUB NESTED_CALC LPAREN math_expression RPAREN */\n\n    this.$ = { type: 'Calc', value: yyvstack[yysp - 1], prefix: yyvstack[yysp - 5] };\n    break;\n\ncase 9:\n    /*! Production::    math_expression : css_variable */\ncase 10:\n    /*! Production::    math_expression : css_value */\ncase 11:\n    /*! Production::    math_expression : value */\n\n    this.$ = yyvstack[yysp];\n    break;\n\ncase 12:\n    /*! Production::    value : NUMBER */\n\n    this.$ = { type: 'Value', value: parseFloat(yyvstack[yysp]) };\n    break;\n\ncase 13:\n    /*! Production::    value : SUB NUMBER */\n\n    this.$ = { type: 'Value', value: parseFloat(yyvstack[yysp]) * -1 };\n    break;\n\ncase 14:\n    /*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP RPAREN */\n\n    this.$ = { type: 'CssVariable', value: yyvstack[yysp - 1] };\n    break;\n\ncase 15:\n    /*! Production::    css_variable : CSS_VAR LPAREN CSS_CPROP COMMA math_expression RPAREN */\n\n    this.$ = { type: 'CssVariable', value: yyvstack[yysp - 3], fallback: yyvstack[yysp - 1] };\n    break;\n\ncase 16:\n    /*! Production::    css_value : LENGTH */\n\n    this.$ = { type: 'LengthValue', value: parseFloat(yyvstack[yysp]), unit: /[a-z]+/.exec(yyvstack[yysp])[0] };\n    break;\n\ncase 17:\n    /*! Production::    css_value : ANGLE */\n\n    this.$ = { type: 'AngleValue', value: parseFloat(yyvstack[yysp]), unit: /[a-z]+/.exec(yyvstack[yysp])[0] };\n    break;\n\ncase 18:\n    /*! Production::    css_value : TIME */\n\n    this.$ = { type: 'TimeValue', value: parseFloat(yyvstack[yysp]), unit: /[a-z]+/.exec(yyvstack[yysp])[0] };\n    break;\n\ncase 19:\n    /*! Production::    css_value : FREQ */\n\n    this.$ = { type: 'FrequencyValue', value: parseFloat(yyvstack[yysp]), unit: /[a-z]+/.exec(yyvstack[yysp])[0] };\n    break;\n\ncase 20:\n    /*! Production::    css_value : RES */\n\n    this.$ = { type: 'ResolutionValue', value: parseFloat(yyvstack[yysp]), unit: /[a-z]+/.exec(yyvstack[yysp])[0] };\n    break;\n\ncase 21:\n    /*! Production::    css_value : EMS */\n\n    this.$ = { type: 'EmValue', value: parseFloat(yyvstack[yysp]), unit: 'em' };\n    break;\n\ncase 22:\n    /*! Production::    css_value : EXS */\n\n    this.$ = { type: 'ExValue', value: parseFloat(yyvstack[yysp]), unit: 'ex' };\n    break;\n\ncase 23:\n    /*! Production::    css_value : CHS */\n\n    this.$ = { type: 'ChValue', value: parseFloat(yyvstack[yysp]), unit: 'ch' };\n    break;\n\ncase 24:\n    /*! Production::    css_value : REMS */\n\n    this.$ = { type: 'RemValue', value: parseFloat(yyvstack[yysp]), unit: 'rem' };\n    break;\n\ncase 25:\n    /*! Production::    css_value : VHS */\n\n    this.$ = { type: 'VhValue', value: parseFloat(yyvstack[yysp]), unit: 'vh' };\n    break;\n\ncase 26:\n    /*! Production::    css_value : VWS */\n\n    this.$ = { type: 'VwValue', value: parseFloat(yyvstack[yysp]), unit: 'vw' };\n    break;\n\ncase 27:\n    /*! Production::    css_value : VMINS */\n\n    this.$ = { type: 'VminValue', value: parseFloat(yyvstack[yysp]), unit: 'vmin' };\n    break;\n\ncase 28:\n    /*! Production::    css_value : VMAXS */\n\n    this.$ = { type: 'VmaxValue', value: parseFloat(yyvstack[yysp]), unit: 'vmax' };\n    break;\n\ncase 29:\n    /*! Production::    css_value : PERCENTAGE */\n\n    this.$ = { type: 'PercentageValue', value: parseFloat(yyvstack[yysp]), unit: '%' };\n    break;\n\ncase 30:\n    /*! Production::    css_value : SUB css_value */\n\n    var prev = yyvstack[yysp]; prev.value *= -1; this.$ = prev;\n    break;\n\n}\n},\ntable: bt({\n  len: u([\n  24,\n  1,\n  5,\n  23,\n  1,\n  18,\n  s,\n  [0, 3],\n  1,\n  s,\n  [0, 16],\n  s,\n  [23, 4],\n  c,\n  [28, 3],\n  0,\n  0,\n  16,\n  1,\n  6,\n  6,\n  s,\n  [0, 3],\n  5,\n  1,\n  2,\n  c,\n  [37, 3],\n  c,\n  [20, 3],\n  5,\n  0,\n  0\n]),\n  symbol: u([\n  4,\n  7,\n  9,\n  11,\n  12,\n  s,\n  [15, 19, 1],\n  1,\n  1,\n  s,\n  [3, 4, 1],\n  c,\n  [30, 19],\n  c,\n  [29, 4],\n  7,\n  4,\n  10,\n  11,\n  c,\n  [22, 14],\n  c,\n  [19, 3],\n  c,\n  [43, 22],\n  c,\n  [23, 69],\n  c,\n  [139, 4],\n  8,\n  c,\n  [51, 24],\n  4,\n  c,\n  [138, 15],\n  13,\n  c,\n  [186, 5],\n  8,\n  c,\n  [6, 6],\n  c,\n  [5, 5],\n  9,\n  8,\n  14,\n  c,\n  [159, 47],\n  c,\n  [60, 10]\n]),\n  type: u([\n  s,\n  [2, 19],\n  s,\n  [0, 5],\n  1,\n  s,\n  [2, 24],\n  s,\n  [0, 4],\n  c,\n  [22, 19],\n  c,\n  [43, 42],\n  c,\n  [23, 70],\n  c,\n  [28, 25],\n  c,\n  [45, 25],\n  c,\n  [113, 54]\n]),\n  state: u([\n  1,\n  2,\n  8,\n  6,\n  7,\n  30,\n  c,\n  [4, 3],\n  33,\n  37,\n  c,\n  [5, 3],\n  38,\n  c,\n  [4, 3],\n  39,\n  c,\n  [4, 3],\n  40,\n  c,\n  [4, 3],\n  42,\n  c,\n  [21, 4],\n  50,\n  c,\n  [5, 3],\n  51,\n  c,\n  [4, 3]\n]),\n  mode: u([\n  s,\n  [1, 179],\n  s,\n  [2, 3],\n  c,\n  [5, 5],\n  c,\n  [6, 4],\n  s,\n  [1, 57]\n]),\n  goto: u([\n  5,\n  3,\n  4,\n  24,\n  s,\n  [9, 15, 1],\n  s,\n  [25, 5, 1],\n  c,\n  [24, 19],\n  31,\n  35,\n  32,\n  34,\n  c,\n  [18, 14],\n  36,\n  c,\n  [38, 19],\n  c,\n  [19, 57],\n  c,\n  [118, 4],\n  41,\n  c,\n  [24, 19],\n  43,\n  35,\n  c,\n  [16, 14],\n  44,\n  s,\n  [2, 3],\n  28,\n  29,\n  2,\n  s,\n  [3, 3],\n  28,\n  29,\n  3,\n  c,\n  [53, 4],\n  s,\n  [45, 5, 1],\n  c,\n  [100, 42],\n  52,\n  c,\n  [5, 4],\n  53\n])\n}),\ndefaultActions: bda({\n  idx: u([\n  6,\n  7,\n  8,\n  s,\n  [10, 16, 1],\n  33,\n  34,\n  39,\n  40,\n  41,\n  45,\n  47,\n  52,\n  53\n]),\n  goto: u([\n  9,\n  10,\n  11,\n  s,\n  [16, 14, 1],\n  12,\n  1,\n  30,\n  13,\n  s,\n  [4, 4, 1],\n  14,\n  15,\n  8\n])\n}),\nparseError: function parseError(str, hash, ExceptionClass) {\n    if (hash.recoverable) {\n        if (typeof this.trace === 'function') {\n            this.trace(str);\n        }\n        hash.destroy();             // destroy... well, *almost*!\n    } else {\n        if (typeof this.trace === 'function') {\n            this.trace(str);\n        }\n        if (!ExceptionClass) {\n            ExceptionClass = this.JisonParserError;\n        }\n        throw new ExceptionClass(str, hash);\n    }\n},\nparse: function parse(input) {\n    var self = this;\n    var stack = new Array(128);         // token stack: stores token which leads to state at the same index (column storage)\n    var sstack = new Array(128);        // state stack: stores states (column storage)\n\n    var vstack = new Array(128);        // semantic value stack\n\n    var table = this.table;\n    var sp = 0;                         // 'stack pointer': index into the stacks\n\n\n    \n\n\n    var symbol = 0;\n\n\n\n    var TERROR = this.TERROR;\n    var EOF = this.EOF;\n    var ERROR_RECOVERY_TOKEN_DISCARD_COUNT = (this.options.errorRecoveryTokenDiscardCount | 0) || 3;\n    var NO_ACTION = [0, 54 /* === table.length :: ensures that anyone using this new state will fail dramatically! */];\n\n    var lexer;\n    if (this.__lexer__) {\n        lexer = this.__lexer__;\n    } else {\n        lexer = this.__lexer__ = Object.create(this.lexer);\n    }\n\n    var sharedState_yy = {\n        parseError: undefined,\n        quoteName: undefined,\n        lexer: undefined,\n        parser: undefined,\n        pre_parse: undefined,\n        post_parse: undefined,\n        pre_lex: undefined,\n        post_lex: undefined      // WARNING: must be written this way for the code expanders to work correctly in both ES5 and ES6 modes!\n    };\n\n    var ASSERT;\n    if (typeof assert !== 'function') {\n        ASSERT = function JisonAssert(cond, msg) {\n            if (!cond) {\n                throw new Error('assertion failed: ' + (msg || '***'));\n            }\n        };\n    } else {\n        ASSERT = assert;\n    }\n\n    this.yyGetSharedState = function yyGetSharedState() {\n        return sharedState_yy;\n    };\n\n\n\n\n\n\n\n\n    function shallow_copy_noclobber(dst, src) {\n        for (var k in src) {\n            if (typeof dst[k] === 'undefined' && Object.prototype.hasOwnProperty.call(src, k)) {\n                dst[k] = src[k];\n            }\n        }\n    }\n\n    // copy state\n    shallow_copy_noclobber(sharedState_yy, this.yy);\n\n    sharedState_yy.lexer = lexer;\n    sharedState_yy.parser = this;\n\n\n\n\n\n\n    // Does the shared state override the default `parseError` that already comes with this instance?\n    if (typeof sharedState_yy.parseError === 'function') {\n        this.parseError = function parseErrorAlt(str, hash, ExceptionClass) {\n            if (!ExceptionClass) {\n                ExceptionClass = this.JisonParserError;\n            }\n            return sharedState_yy.parseError.call(this, str, hash, ExceptionClass);\n        };\n    } else {\n        this.parseError = this.originalParseError;\n    }\n\n    // Does the shared state override the default `quoteName` that already comes with this instance?\n    if (typeof sharedState_yy.quoteName === 'function') {\n        this.quoteName = function quoteNameAlt(id_str) {\n            return sharedState_yy.quoteName.call(this, id_str);\n        };\n    } else {\n        this.quoteName = this.originalQuoteName;\n    }\n\n    // set up the cleanup function; make it an API so that external code can re-use this one in case of\n    // calamities or when the `%options no-try-catch` option has been specified for the grammar, in which\n    // case this parse() API method doesn't come with a `finally { ... }` block any more!\n    //\n    // NOTE: as this API uses parse() as a closure, it MUST be set again on every parse() invocation,\n    //       or else your `sharedState`, etc. references will be *wrong*!\n    this.cleanupAfterParse = function parser_cleanupAfterParse(resultValue, invoke_post_methods, do_not_nuke_errorinfos) {\n        var rv;\n\n        if (invoke_post_methods) {\n            var hash;\n\n            if (sharedState_yy.post_parse || this.post_parse) {\n                // create an error hash info instance: we re-use this API in a **non-error situation**\n                // as this one delivers all parser internals ready for access by userland code.\n                hash = this.constructParseErrorInfo(null /* no error! */, null /* no exception! */, null, false);\n            }\n\n            if (sharedState_yy.post_parse) {\n                rv = sharedState_yy.post_parse.call(this, sharedState_yy, resultValue, hash);\n                if (typeof rv !== 'undefined') resultValue = rv;\n            }\n            if (this.post_parse) {\n                rv = this.post_parse.call(this, sharedState_yy, resultValue, hash);\n                if (typeof rv !== 'undefined') resultValue = rv;\n            }\n\n            // cleanup:\n            if (hash && hash.destroy) {\n                hash.destroy();\n            }\n        }\n\n        if (this.__reentrant_call_depth > 1) return resultValue;        // do not (yet) kill the sharedState when this is a reentrant run.\n\n        // clean up the lingering lexer structures as well:\n        if (lexer.cleanupAfterLex) {\n            lexer.cleanupAfterLex(do_not_nuke_errorinfos);\n        }\n\n        // prevent lingering circular references from causing memory leaks:\n        if (sharedState_yy) {\n            sharedState_yy.lexer = undefined;\n            sharedState_yy.parser = undefined;\n            if (lexer.yy === sharedState_yy) {\n                lexer.yy = undefined;\n            }\n        }\n        sharedState_yy = undefined;\n        this.parseError = this.originalParseError;\n        this.quoteName = this.originalQuoteName;\n\n        // nuke the vstack[] array at least as that one will still reference obsoleted user values.\n        // To be safe, we nuke the other internal stack columns as well...\n        stack.length = 0;               // fastest way to nuke an array without overly bothering the GC\n        sstack.length = 0;\n\n        vstack.length = 0;\n        sp = 0;\n\n        // nuke the error hash info instances created during this run.\n        // Userland code must COPY any data/references\n        // in the error hash instance(s) it is more permanently interested in.\n        if (!do_not_nuke_errorinfos) {\n            for (var i = this.__error_infos.length - 1; i >= 0; i--) {\n                var el = this.__error_infos[i];\n                if (el && typeof el.destroy === 'function') {\n                    el.destroy();\n                }\n            }\n            this.__error_infos.length = 0;\n\n\n        }\n\n        return resultValue;\n    };\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    // NOTE: as this API uses parse() as a closure, it MUST be set again on every parse() invocation,\n    //       or else your `lexer`, `sharedState`, etc. references will be *wrong*!\n    this.constructParseErrorInfo = function parser_constructParseErrorInfo(msg, ex, expected, recoverable) {\n        var pei = {\n            errStr: msg,\n            exception: ex,\n            text: lexer.match,\n            value: lexer.yytext,\n            token: this.describeSymbol(symbol) || symbol,\n            token_id: symbol,\n            line: lexer.yylineno,\n\n            expected: expected,\n            recoverable: recoverable,\n            state: state,\n            action: action,\n            new_state: newState,\n            symbol_stack: stack,\n            state_stack: sstack,\n            value_stack: vstack,\n\n            stack_pointer: sp,\n            yy: sharedState_yy,\n            lexer: lexer,\n            parser: this,\n\n            // and make sure the error info doesn't stay due to potential\n            // ref cycle via userland code manipulations.\n            // These would otherwise all be memory leak opportunities!\n            //\n            // Note that only array and object references are nuked as those\n            // constitute the set of elements which can produce a cyclic ref.\n            // The rest of the members is kept intact as they are harmless.\n            destroy: function destructParseErrorInfo() {\n                // remove cyclic references added to error info:\n                // info.yy = null;\n                // info.lexer = null;\n                // info.value = null;\n                // info.value_stack = null;\n                // ...\n                var rec = !!this.recoverable;\n                for (var key in this) {\n                    if (this.hasOwnProperty(key) && typeof key === 'object') {\n                        this[key] = undefined;\n                    }\n                }\n                this.recoverable = rec;\n            }\n        };\n        // track this instance so we can `destroy()` it once we deem it superfluous and ready for garbage collection!\n        this.__error_infos.push(pei);\n        return pei;\n    };\n\n\n\n\n\n\n\n\n\n\n\n\n\n    function getNonTerminalFromCode(symbol) {\n        var tokenName = self.getSymbolName(symbol);\n        if (!tokenName) {\n            tokenName = symbol;\n        }\n        return tokenName;\n    }\n\n\n    function stdLex() {\n        var token = lexer.lex();\n        // if token isn't its numeric value, convert\n        if (typeof token !== 'number') {\n            token = self.symbols_[token] || token;\n        }\n\n        return token || EOF;\n    }\n\n    function fastLex() {\n        var token = lexer.fastLex();\n        // if token isn't its numeric value, convert\n        if (typeof token !== 'number') {\n            token = self.symbols_[token] || token;\n        }\n\n        return token || EOF;\n    }\n\n    var lex = stdLex;\n\n\n    var state, action, r, t;\n    var yyval = {\n        $: true,\n        _$: undefined,\n        yy: sharedState_yy\n    };\n    var p;\n    var yyrulelen;\n    var this_production;\n    var newState;\n    var retval = false;\n\n\n    try {\n        this.__reentrant_call_depth++;\n\n        lexer.setInput(input, sharedState_yy);\n\n        // NOTE: we *assume* no lexer pre/post handlers are set up *after* \n        // this initial `setInput()` call: hence we can now check and decide\n        // whether we'll go with the standard, slower, lex() API or the\n        // `fast_lex()` one:\n        if (typeof lexer.canIUse === 'function') {\n            var lexerInfo = lexer.canIUse();\n            if (lexerInfo.fastLex && typeof fastLex === 'function') {\n                lex = fastLex;\n            }\n        } \n\n\n\n        vstack[sp] = null;\n        sstack[sp] = 0;\n        stack[sp] = 0;\n        ++sp;\n\n\n\n\n\n        if (this.pre_parse) {\n            this.pre_parse.call(this, sharedState_yy);\n        }\n        if (sharedState_yy.pre_parse) {\n            sharedState_yy.pre_parse.call(this, sharedState_yy);\n        }\n\n        newState = sstack[sp - 1];\n        for (;;) {\n            // retrieve state number from top of stack\n            state = newState;               // sstack[sp - 1];\n\n            // use default actions if available\n            if (this.defaultActions[state]) {\n                action = 2;\n                newState = this.defaultActions[state];\n            } else {\n                // The single `==` condition below covers both these `===` comparisons in a single\n                // operation:\n                //\n                //     if (symbol === null || typeof symbol === 'undefined') ...\n                if (!symbol) {\n                    symbol = lex();\n                }\n                // read action for current state and first input\n                t = (table[state] && table[state][symbol]) || NO_ACTION;\n                newState = t[1];\n                action = t[0];\n\n\n\n\n\n\n\n\n\n\n\n                // handle parse error\n                if (!action) {\n                    var errStr;\n                    var errSymbolDescr = (this.describeSymbol(symbol) || symbol);\n                    var expected = this.collect_expected_token_set(state);\n\n                    // Report error\n                    if (typeof lexer.yylineno === 'number') {\n                        errStr = 'Parse error on line ' + (lexer.yylineno + 1) + ': ';\n                    } else {\n                        errStr = 'Parse error: ';\n                    }\n                    if (typeof lexer.showPosition === 'function') {\n                        errStr += '\\n' + lexer.showPosition(79 - 10, 10) + '\\n';\n                    }\n                    if (expected.length) {\n                        errStr += 'Expecting ' + expected.join(', ') + ', got unexpected ' + errSymbolDescr;\n                    } else {\n                        errStr += 'Unexpected ' + errSymbolDescr;\n                    }\n                    // we cannot recover from the error!\n                    p = this.constructParseErrorInfo(errStr, null, expected, false);\n                    r = this.parseError(p.errStr, p, this.JisonParserError);\n                    if (typeof r !== 'undefined') {\n                        retval = r;\n                    }\n                    break;\n                }\n\n\n            }\n\n\n\n\n\n\n\n\n\n\n            switch (action) {\n            // catch misc. parse failures:\n            default:\n                // this shouldn't happen, unless resolve defaults are off\n                if (action instanceof Array) {\n                    p = this.constructParseErrorInfo('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol, null, null, false);\n                    r = this.parseError(p.errStr, p, this.JisonParserError);\n                    if (typeof r !== 'undefined') {\n                        retval = r;\n                    }\n                    break;\n                }\n                // Another case of better safe than sorry: in case state transitions come out of another error recovery process\n                // or a buggy LUT (LookUp Table):\n                p = this.constructParseErrorInfo('Parsing halted. No viable error recovery approach available due to internal system failure.', null, null, false);\n                r = this.parseError(p.errStr, p, this.JisonParserError);\n                if (typeof r !== 'undefined') {\n                    retval = r;\n                }\n                break;\n\n            // shift:\n            case 1:\n                stack[sp] = symbol;\n                vstack[sp] = lexer.yytext;\n\n                sstack[sp] = newState; // push state\n\n                ++sp;\n                symbol = 0;\n\n\n\n\n                // Pick up the lexer details for the current symbol as that one is not 'look-ahead' any more:\n\n\n\n\n                continue;\n\n            // reduce:\n            case 2:\n\n\n\n                this_production = this.productions_[newState - 1];  // `this.productions_[]` is zero-based indexed while states start from 1 upwards...\n                yyrulelen = this_production[1];\n\n\n\n\n\n\n\n\n\n\n                r = this.performAction.call(yyval, newState, sp - 1, vstack);\n\n                if (typeof r !== 'undefined') {\n                    retval = r;\n                    break;\n                }\n\n                // pop off stack\n                sp -= yyrulelen;\n\n                // don't overwrite the `symbol` variable: use a local var to speed things up:\n                var ntsymbol = this_production[0];    // push nonterminal (reduce)\n                stack[sp] = ntsymbol;\n                vstack[sp] = yyval.$;\n\n                // goto new state = table[STATE][NONTERMINAL]\n                newState = table[sstack[sp - 1]][ntsymbol];\n                sstack[sp] = newState;\n                ++sp;\n\n\n\n\n\n\n\n\n\n                continue;\n\n            // accept:\n            case 3:\n                if (sp !== -2) {\n                    retval = true;\n                    // Return the `$accept` rule's `$$` result, if available.\n                    //\n                    // Also note that JISON always adds this top-most `$accept` rule (with implicit,\n                    // default, action):\n                    //\n                    //     $accept: <startSymbol> $end\n                    //                  %{ $$ = $1; @$ = @1; %}\n                    //\n                    // which, combined with the parse kernel's `$accept` state behaviour coded below,\n                    // will produce the `$$` value output of the <startSymbol> rule as the parse result,\n                    // IFF that result is *not* `undefined`. (See also the parser kernel code.)\n                    //\n                    // In code:\n                    //\n                    //                  %{\n                    //                      @$ = @1;            // if location tracking support is included\n                    //                      if (typeof $1 !== 'undefined')\n                    //                          return $1;\n                    //                      else\n                    //                          return true;           // the default parse result if the rule actions don't produce anything\n                    //                  %}\n                    sp--;\n                    if (typeof vstack[sp] !== 'undefined') {\n                        retval = vstack[sp];\n                    }\n                }\n                break;\n            }\n\n            // break out of loop: we accept or fail with error\n            break;\n        }\n    } catch (ex) {\n        // report exceptions through the parseError callback too, but keep the exception intact\n        // if it is a known parser or lexer error which has been thrown by parseError() already:\n        if (ex instanceof this.JisonParserError) {\n            throw ex;\n        }\n        else if (lexer && typeof lexer.JisonLexerError === 'function' && ex instanceof lexer.JisonLexerError) {\n            throw ex;\n        }\n\n        p = this.constructParseErrorInfo('Parsing aborted due to exception.', ex, null, false);\n        retval = false;\n        r = this.parseError(p.errStr, p, this.JisonParserError);\n        if (typeof r !== 'undefined') {\n            retval = r;\n        }\n    } finally {\n        retval = this.cleanupAfterParse(retval, true, true);\n        this.__reentrant_call_depth--;\n    }   // /finally\n\n    return retval;\n}\n};\nparser.originalParseError = parser.parseError;\nparser.originalQuoteName = parser.quoteName;\n/* lexer generated by jison-lex 0.6.1-215 */\n\n/*\n * Returns a Lexer object of the following structure:\n *\n *  Lexer: {\n *    yy: {}     The so-called \"shared state\" or rather the *source* of it;\n *               the real \"shared state\" `yy` passed around to\n *               the rule actions, etc. is a direct reference!\n *\n *               This \"shared context\" object was passed to the lexer by way of \n *               the `lexer.setInput(str, yy)` API before you may use it.\n *\n *               This \"shared context\" object is passed to the lexer action code in `performAction()`\n *               so userland code in the lexer actions may communicate with the outside world \n *               and/or other lexer rules' actions in more or less complex ways.\n *\n *  }\n *\n *  Lexer.prototype: {\n *    EOF: 1,\n *    ERROR: 2,\n *\n *    yy:        The overall \"shared context\" object reference.\n *\n *    JisonLexerError: function(msg, hash),\n *\n *    performAction: function lexer__performAction(yy, yyrulenumber, YY_START),\n *\n *               The function parameters and `this` have the following value/meaning:\n *               - `this`    : reference to the `lexer` instance. \n *                               `yy_` is an alias for `this` lexer instance reference used internally.\n *\n *               - `yy`      : a reference to the `yy` \"shared state\" object which was passed to the lexer\n *                             by way of the `lexer.setInput(str, yy)` API before.\n *\n *                             Note:\n *                             The extra arguments you specified in the `%parse-param` statement in your\n *                             **parser** grammar definition file are passed to the lexer via this object\n *                             reference as member variables.\n *\n *               - `yyrulenumber`   : index of the matched lexer rule (regex), used internally.\n *\n *               - `YY_START`: the current lexer \"start condition\" state.\n *\n *    parseError: function(str, hash, ExceptionClass),\n *\n *    constructLexErrorInfo: function(error_message, is_recoverable),\n *               Helper function.\n *               Produces a new errorInfo 'hash object' which can be passed into `parseError()`.\n *               See it's use in this lexer kernel in many places; example usage:\n *\n *                   var infoObj = lexer.constructParseErrorInfo('fail!', true);\n *                   var retVal = lexer.parseError(infoObj.errStr, infoObj, lexer.JisonLexerError);\n *\n *    options: { ... lexer %options ... },\n *\n *    lex: function(),\n *               Produce one token of lexed input, which was passed in earlier via the `lexer.setInput()` API.\n *               You MAY use the additional `args...` parameters as per `%parse-param` spec of the **lexer** grammar:\n *               these extra `args...` are added verbatim to the `yy` object reference as member variables.\n *\n *               WARNING:\n *               Lexer's additional `args...` parameters (via lexer's `%parse-param`) MAY conflict with\n *               any attributes already added to `yy` by the **parser** or the jison run-time; \n *               when such a collision is detected an exception is thrown to prevent the generated run-time \n *               from silently accepting this confusing and potentially hazardous situation! \n *\n *    cleanupAfterLex: function(do_not_nuke_errorinfos),\n *               Helper function.\n *\n *               This helper API is invoked when the **parse process** has completed: it is the responsibility\n *               of the **parser** (or the calling userland code) to invoke this method once cleanup is desired. \n *\n *               This helper may be invoked by user code to ensure the internal lexer gets properly garbage collected.\n *\n *    setInput: function(input, [yy]),\n *\n *\n *    input: function(),\n *\n *\n *    unput: function(str),\n *\n *\n *    more: function(),\n *\n *\n *    reject: function(),\n *\n *\n *    less: function(n),\n *\n *\n *    pastInput: function(n),\n *\n *\n *    upcomingInput: function(n),\n *\n *\n *    showPosition: function(),\n *\n *\n *    test_match: function(regex_match_array, rule_index),\n *\n *\n *    next: function(),\n *\n *\n *    begin: function(condition),\n *\n *\n *    pushState: function(condition),\n *\n *\n *    popState: function(),\n *\n *\n *    topState: function(),\n *\n *\n *    _currentRules: function(),\n *\n *\n *    stateStackSize: function(),\n *\n *\n *    performAction: function(yy, yy_, yyrulenumber, YY_START),\n *\n *\n *    rules: [...],\n *\n *\n *    conditions: {associative list: name ==> set},\n *  }\n *\n *\n *  token location info (`yylloc`): {\n *    first_line: n,\n *    last_line: n,\n *    first_column: n,\n *    last_column: n,\n *    range: [start_number, end_number]\n *               (where the numbers are indexes into the input string, zero-based)\n *  }\n *\n * ---\n *\n * The `parseError` function receives a 'hash' object with these members for lexer errors:\n *\n *  {\n *    text:        (matched text)\n *    token:       (the produced terminal token, if any)\n *    token_id:    (the produced terminal token numeric ID, if any)\n *    line:        (yylineno)\n *    loc:         (yylloc)\n *    recoverable: (boolean: TRUE when the parser MAY have an error recovery rule\n *                  available for this particular error)\n *    yy:          (object: the current parser internal \"shared state\" `yy`\n *                  as is also available in the rule actions; this can be used,\n *                  for instance, for advanced error analysis and reporting)\n *    lexer:       (reference to the current lexer instance used by the parser)\n *  }\n *\n * while `this` will reference the current lexer instance.\n *\n * When `parseError` is invoked by the lexer, the default implementation will\n * attempt to invoke `yy.parser.parseError()`; when this callback is not provided\n * it will try to invoke `yy.parseError()` instead. When that callback is also not\n * provided, a `JisonLexerError` exception will be thrown containing the error\n * message and `hash`, as constructed by the `constructLexErrorInfo()` API.\n *\n * Note that the lexer's `JisonLexerError` error class is passed via the\n * `ExceptionClass` argument, which is invoked to construct the exception\n * instance to be thrown, so technically `parseError` will throw the object\n * produced by the `new ExceptionClass(str, hash)` JavaScript expression.\n *\n * ---\n *\n * You can specify lexer options by setting / modifying the `.options` object of your Lexer instance.\n * These options are available:\n *\n * (Options are permanent.)\n *  \n *  yy: {\n *      parseError: function(str, hash, ExceptionClass)\n *                 optional: overrides the default `parseError` function.\n *  }\n *\n *  lexer.options: {\n *      pre_lex:  function()\n *                 optional: is invoked before the lexer is invoked to produce another token.\n *                 `this` refers to the Lexer object.\n *      post_lex: function(token) { return token; }\n *                 optional: is invoked when the lexer has produced a token `token`;\n *                 this function can override the returned token value by returning another.\n *                 When it does not return any (truthy) value, the lexer will return\n *                 the original `token`.\n *                 `this` refers to the Lexer object.\n *\n * WARNING: the next set of options are not meant to be changed. They echo the abilities of\n * the lexer as per when it was compiled!\n *\n *      ranges: boolean\n *                 optional: `true` ==> token location info will include a .range[] member.\n *      flex: boolean\n *                 optional: `true` ==> flex-like lexing behaviour where the rules are tested\n *                 exhaustively to find the longest match.\n *      backtrack_lexer: boolean\n *                 optional: `true` ==> lexer regexes are tested in order and for invoked;\n *                 the lexer terminates the scan when a token is returned by the action code.\n *      xregexp: boolean\n *                 optional: `true` ==> lexer rule regexes are \"extended regex format\" requiring the\n *                 `XRegExp` library. When this %option has not been specified at compile time, all lexer\n *                 rule regexes have been written as standard JavaScript RegExp expressions.\n *  }\n */\n\n\nvar lexer = function() {\n  /**\n   * See also:\n   * http://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript/#35881508\n   * but we keep the prototype.constructor and prototype.name assignment lines too for compatibility\n   * with userland code which might access the derived class in a 'classic' way.\n   *\n   * @public\n   * @constructor\n   * @nocollapse\n   */\n  function JisonLexerError(msg, hash) {\n    Object.defineProperty(this, 'name', {\n      enumerable: false,\n      writable: false,\n      value: 'JisonLexerError'\n    });\n\n    if (msg == null)\n      msg = '???';\n\n    Object.defineProperty(this, 'message', {\n      enumerable: false,\n      writable: true,\n      value: msg\n    });\n\n    this.hash = hash;\n    var stacktrace;\n\n    if (hash && hash.exception instanceof Error) {\n      var ex2 = hash.exception;\n      this.message = ex2.message || msg;\n      stacktrace = ex2.stack;\n    }\n\n    if (!stacktrace) {\n      if (Error.hasOwnProperty('captureStackTrace')) {\n        // V8\n        Error.captureStackTrace(this, this.constructor);\n      } else {\n        stacktrace = new Error(msg).stack;\n      }\n    }\n\n    if (stacktrace) {\n      Object.defineProperty(this, 'stack', {\n        enumerable: false,\n        writable: false,\n        value: stacktrace\n      });\n    }\n  }\n\n  if (typeof Object.setPrototypeOf === 'function') {\n    Object.setPrototypeOf(JisonLexerError.prototype, Error.prototype);\n  } else {\n    JisonLexerError.prototype = Object.create(Error.prototype);\n  }\n\n  JisonLexerError.prototype.constructor = JisonLexerError;\n  JisonLexerError.prototype.name = 'JisonLexerError';\n\n  var lexer = {\n    \n// Code Generator Information Report\n// ---------------------------------\n//\n// Options:\n//\n//   backtracking: .................... false\n//   location.ranges: ................. false\n//   location line+column tracking: ... true\n//\n//\n// Forwarded Parser Analysis flags:\n//\n//   uses yyleng: ..................... false\n//   uses yylineno: ................... false\n//   uses yytext: ..................... false\n//   uses yylloc: ..................... false\n//   uses lexer values: ............... true / true\n//   location tracking: ............... false\n//   location assignment: ............. false\n//\n//\n// Lexer Analysis flags:\n//\n//   uses yyleng: ..................... ???\n//   uses yylineno: ................... ???\n//   uses yytext: ..................... ???\n//   uses yylloc: ..................... ???\n//   uses ParseError API: ............. ???\n//   uses yyerror: .................... ???\n//   uses location tracking & editing:  ???\n//   uses more() API: ................. ???\n//   uses unput() API: ................ ???\n//   uses reject() API: ............... ???\n//   uses less() API: ................. ???\n//   uses display APIs pastInput(), upcomingInput(), showPosition():\n//        ............................. ???\n//   uses describeYYLLOC() API: ....... ???\n//\n// --------- END OF REPORT -----------\n\nEOF: 1,\n    ERROR: 2,\n\n    // JisonLexerError: JisonLexerError,        /// <-- injected by the code generator\n\n    // options: {},                             /// <-- injected by the code generator\n\n    // yy: ...,                                 /// <-- injected by setInput()\n\n    __currentRuleSet__: null,                   /// INTERNAL USE ONLY: internal rule set cache for the current lexer state  \n\n    __error_infos: [],                          /// INTERNAL USE ONLY: the set of lexErrorInfo objects created since the last cleanup  \n    __decompressed: false,                      /// INTERNAL USE ONLY: mark whether the lexer instance has been 'unfolded' completely and is now ready for use  \n    done: false,                                /// INTERNAL USE ONLY  \n    _backtrack: false,                          /// INTERNAL USE ONLY  \n    _input: '',                                 /// INTERNAL USE ONLY  \n    _more: false,                               /// INTERNAL USE ONLY  \n    _signaled_error_token: false,               /// INTERNAL USE ONLY  \n    conditionStack: [],                         /// INTERNAL USE ONLY; managed via `pushState()`, `popState()`, `topState()` and `stateStackSize()`  \n    match: '',                                  /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks input which has been matched so far for the lexer token under construction. `match` is identical to `yytext` except that this one still contains the matched input string after `lexer.performAction()` has been invoked, where userland code MAY have changed/replaced the `yytext` value entirely!  \n    matched: '',                                /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks entire input which has been matched so far  \n    matches: false,                             /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks RE match result for last (successful) match attempt  \n    yytext: '',                                 /// ADVANCED USE ONLY: tracks input which has been matched so far for the lexer token under construction; this value is transferred to the parser as the 'token value' when the parser consumes the lexer token produced through a call to the `lex()` API.  \n    offset: 0,                                  /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks the 'cursor position' in the input string, i.e. the number of characters matched so far  \n    yyleng: 0,                                  /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: length of matched input for the token under construction (`yytext`)  \n    yylineno: 0,                                /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: 'line number' at which the token under construction is located  \n    yylloc: null,                               /// READ-ONLY EXTERNAL ACCESS - ADVANCED USE ONLY: tracks location info (lines + columns) for the token under construction  \n\n    /**\n     * INTERNAL USE: construct a suitable error info hash object instance for `parseError`.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    constructLexErrorInfo: function lexer_constructLexErrorInfo(msg, recoverable, show_input_position) {\n      msg = '' + msg;\n\n      // heuristic to determine if the error message already contains a (partial) source code dump\n      // as produced by either `showPosition()` or `prettyPrintRange()`:\n      if (show_input_position == undefined) {\n        show_input_position = !(msg.indexOf('\\n') > 0 && msg.indexOf('^') > 0);\n      }\n\n      if (this.yylloc && show_input_position) {\n        if (typeof this.prettyPrintRange === 'function') {\n          var pretty_src = this.prettyPrintRange(this.yylloc);\n\n          if (!/\\n\\s*$/.test(msg)) {\n            msg += '\\n';\n          }\n\n          msg += '\\n  Erroneous area:\\n' + this.prettyPrintRange(this.yylloc);\n        } else if (typeof this.showPosition === 'function') {\n          var pos_str = this.showPosition();\n\n          if (pos_str) {\n            if (msg.length && msg[msg.length - 1] !== '\\n' && pos_str[0] !== '\\n') {\n              msg += '\\n' + pos_str;\n            } else {\n              msg += pos_str;\n            }\n          }\n        }\n      }\n\n      /** @constructor */\n      var pei = {\n        errStr: msg,\n        recoverable: !!recoverable,\n        text: this.match,           // This one MAY be empty; userland code should use the `upcomingInput` API to obtain more text which follows the 'lexer cursor position'...  \n        token: null,\n        line: this.yylineno,\n        loc: this.yylloc,\n        yy: this.yy,\n        lexer: this,\n\n        /**\n         * and make sure the error info doesn't stay due to potential\n         * ref cycle via userland code manipulations.\n         * These would otherwise all be memory leak opportunities!\n         * \n         * Note that only array and object references are nuked as those\n         * constitute the set of elements which can produce a cyclic ref.\n         * The rest of the members is kept intact as they are harmless.\n         * \n         * @public\n         * @this {LexErrorInfo}\n         */\n        destroy: function destructLexErrorInfo() {\n          // remove cyclic references added to error info:\n          // info.yy = null;\n          // info.lexer = null;\n          // ...\n          var rec = !!this.recoverable;\n\n          for (var key in this) {\n            if (this.hasOwnProperty(key) && typeof key === 'object') {\n              this[key] = undefined;\n            }\n          }\n\n          this.recoverable = rec;\n        }\n      };\n\n      // track this instance so we can `destroy()` it once we deem it superfluous and ready for garbage collection!\n      this.__error_infos.push(pei);\n\n      return pei;\n    },\n\n    /**\n     * handler which is invoked when a lexer error occurs.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    parseError: function lexer_parseError(str, hash, ExceptionClass) {\n      if (!ExceptionClass) {\n        ExceptionClass = this.JisonLexerError;\n      }\n\n      if (this.yy) {\n        if (this.yy.parser && typeof this.yy.parser.parseError === 'function') {\n          return this.yy.parser.parseError.call(this, str, hash, ExceptionClass) || this.ERROR;\n        } else if (typeof this.yy.parseError === 'function') {\n          return this.yy.parseError.call(this, str, hash, ExceptionClass) || this.ERROR;\n        }\n      }\n\n      throw new ExceptionClass(str, hash);\n    },\n\n    /**\n     * method which implements `yyerror(str, ...args)` functionality for use inside lexer actions.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    yyerror: function yyError(str /*, ...args */) {\n      var lineno_msg = '';\n\n      if (this.yylloc) {\n        lineno_msg = ' on line ' + (this.yylineno + 1);\n      }\n\n      var p = this.constructLexErrorInfo(\n        'Lexical error' + lineno_msg + ': ' + str,\n        this.options.lexerErrorsAreRecoverable\n      );\n\n      // Add any extra args to the hash under the name `extra_error_attributes`:\n      var args = Array.prototype.slice.call(arguments, 1);\n\n      if (args.length) {\n        p.extra_error_attributes = args;\n      }\n\n      return this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n    },\n\n    /**\n     * final cleanup function for when we have completed lexing the input;\n     * make it an API so that external code can use this one once userland\n     * code has decided it's time to destroy any lingering lexer error\n     * hash object instances and the like: this function helps to clean\n     * up these constructs, which *may* carry cyclic references which would\n     * otherwise prevent the instances from being properly and timely\n     * garbage-collected, i.e. this function helps prevent memory leaks!\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    cleanupAfterLex: function lexer_cleanupAfterLex(do_not_nuke_errorinfos) {\n      // prevent lingering circular references from causing memory leaks:\n      this.setInput('', {});\n\n      // nuke the error hash info instances created during this run.\n      // Userland code must COPY any data/references\n      // in the error hash instance(s) it is more permanently interested in.\n      if (!do_not_nuke_errorinfos) {\n        for (var i = this.__error_infos.length - 1; i >= 0; i--) {\n          var el = this.__error_infos[i];\n\n          if (el && typeof el.destroy === 'function') {\n            el.destroy();\n          }\n        }\n\n        this.__error_infos.length = 0;\n      }\n\n      return this;\n    },\n\n    /**\n     * clear the lexer token context; intended for internal use only\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    clear: function lexer_clear() {\n      this.yytext = '';\n      this.yyleng = 0;\n      this.match = '';\n\n      // - DO NOT reset `this.matched`\n      this.matches = false;\n\n      this._more = false;\n      this._backtrack = false;\n      var col = (this.yylloc ? this.yylloc.last_column : 0);\n\n      this.yylloc = {\n        first_line: this.yylineno + 1,\n        first_column: col,\n        last_line: this.yylineno + 1,\n        last_column: col,\n        range: [this.offset, this.offset]\n      };\n    },\n\n    /**\n     * resets the lexer, sets new input\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    setInput: function lexer_setInput(input, yy) {\n      this.yy = yy || this.yy || {};\n\n      // also check if we've fully initialized the lexer instance,\n      // including expansion work to be done to go from a loaded\n      // lexer to a usable lexer:\n      if (!this.__decompressed) {\n        // step 1: decompress the regex list:\n        var rules = this.rules;\n\n        for (var i = 0, len = rules.length; i < len; i++) {\n          var rule_re = rules[i];\n\n          // compression: is the RE an xref to another RE slot in the rules[] table?\n          if (typeof rule_re === 'number') {\n            rules[i] = rules[rule_re];\n          }\n        }\n\n        // step 2: unfold the conditions[] set to make these ready for use:\n        var conditions = this.conditions;\n\n        for (var k in conditions) {\n          var spec = conditions[k];\n          var rule_ids = spec.rules;\n          var len = rule_ids.length;\n          var rule_regexes = new Array(len + 1);             // slot 0 is unused; we use a 1-based index approach here to keep the hottest code in `lexer_next()` fast and simple! \n          var rule_new_ids = new Array(len + 1);\n\n          for (var i = 0; i < len; i++) {\n            var idx = rule_ids[i];\n            var rule_re = rules[idx];\n            rule_regexes[i + 1] = rule_re;\n            rule_new_ids[i + 1] = idx;\n          }\n\n          spec.rules = rule_new_ids;\n          spec.__rule_regexes = rule_regexes;\n          spec.__rule_count = len;\n        }\n\n        this.__decompressed = true;\n      }\n\n      this._input = input || '';\n      this.clear();\n      this._signaled_error_token = false;\n      this.done = false;\n      this.yylineno = 0;\n      this.matched = '';\n      this.conditionStack = ['INITIAL'];\n      this.__currentRuleSet__ = null;\n\n      this.yylloc = {\n        first_line: 1,\n        first_column: 0,\n        last_line: 1,\n        last_column: 0,\n        range: [0, 0]\n      };\n\n      this.offset = 0;\n      return this;\n    },\n\n    /**\n     * edit the remaining input via user-specified callback.\n     * This can be used to forward-adjust the input-to-parse, \n     * e.g. inserting macro expansions and alike in the\n     * input which has yet to be lexed.\n     * The behaviour of this API contrasts the `unput()` et al\n     * APIs as those act on the *consumed* input, while this\n     * one allows one to manipulate the future, without impacting\n     * the current `yyloc` cursor location or any history. \n     * \n     * Use this API to help implement C-preprocessor-like\n     * `#include` statements, etc.\n     * \n     * The provided callback must be synchronous and is\n     * expected to return the edited input (string).\n     *\n     * The `cpsArg` argument value is passed to the callback\n     * as-is.\n     *\n     * `callback` interface: \n     * `function callback(input, cpsArg)`\n     * \n     * - `input` will carry the remaining-input-to-lex string\n     *   from the lexer.\n     * - `cpsArg` is `cpsArg` passed into this API.\n     * \n     * The `this` reference for the callback will be set to\n     * reference this lexer instance so that userland code\n     * in the callback can easily and quickly access any lexer\n     * API. \n     *\n     * When the callback returns a non-string-type falsey value,\n     * we assume the callback did not edit the input and we\n     * will using the input as-is.\n     *\n     * When the callback returns a non-string-type value, it\n     * is converted to a string for lexing via the `\"\" + retval`\n     * operation. (See also why: http://2ality.com/2012/03/converting-to-string.html \n     * -- that way any returned object's `toValue()` and `toString()`\n     * methods will be invoked in a proper/desirable order.)\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    editRemainingInput: function lexer_editRemainingInput(callback, cpsArg) {\n      var rv = callback.call(this, this._input, cpsArg);\n\n      if (typeof rv !== 'string') {\n        if (rv) {\n          this._input = '' + rv;\n        } \n        // else: keep `this._input` as is.  \n      } else {\n        this._input = rv;\n      }\n\n      return this;\n    },\n\n    /**\n     * consumes and returns one char from the input\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    input: function lexer_input() {\n      if (!this._input) {\n        //this.done = true;    -- don't set `done` as we want the lex()/next() API to be able to produce one custom EOF token match after this anyhow. (lexer can match special <<EOF>> tokens and perform user action code for a <<EOF>> match, but only does so *once*)\n        return null;\n      }\n\n      var ch = this._input[0];\n      this.yytext += ch;\n      this.yyleng++;\n      this.offset++;\n      this.match += ch;\n      this.matched += ch;\n\n      // Count the linenumber up when we hit the LF (or a stand-alone CR).\n      // On CRLF, the linenumber is incremented when you fetch the CR or the CRLF combo\n      // and we advance immediately past the LF as well, returning both together as if\n      // it was all a single 'character' only.\n      var slice_len = 1;\n\n      var lines = false;\n\n      if (ch === '\\n') {\n        lines = true;\n      } else if (ch === '\\r') {\n        lines = true;\n        var ch2 = this._input[1];\n\n        if (ch2 === '\\n') {\n          slice_len++;\n          ch += ch2;\n          this.yytext += ch2;\n          this.yyleng++;\n          this.offset++;\n          this.match += ch2;\n          this.matched += ch2;\n          this.yylloc.range[1]++;\n        }\n      }\n\n      if (lines) {\n        this.yylineno++;\n        this.yylloc.last_line++;\n        this.yylloc.last_column = 0;\n      } else {\n        this.yylloc.last_column++;\n      }\n\n      this.yylloc.range[1]++;\n      this._input = this._input.slice(slice_len);\n      return ch;\n    },\n\n    /**\n     * unshifts one char (or an entire string) into the input\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    unput: function lexer_unput(ch) {\n      var len = ch.length;\n      var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n      this._input = ch + this._input;\n      this.yytext = this.yytext.substr(0, this.yytext.length - len);\n      this.yyleng = this.yytext.length;\n      this.offset -= len;\n      this.match = this.match.substr(0, this.match.length - len);\n      this.matched = this.matched.substr(0, this.matched.length - len);\n\n      if (lines.length > 1) {\n        this.yylineno -= lines.length - 1;\n        this.yylloc.last_line = this.yylineno + 1;\n\n        // Get last entirely matched line into the `pre_lines[]` array's\n        // last index slot; we don't mind when other previously \n        // matched lines end up in the array too. \n        var pre = this.match;\n\n        var pre_lines = pre.split(/(?:\\r\\n?|\\n)/g);\n\n        if (pre_lines.length === 1) {\n          pre = this.matched;\n          pre_lines = pre.split(/(?:\\r\\n?|\\n)/g);\n        }\n\n        this.yylloc.last_column = pre_lines[pre_lines.length - 1].length;\n      } else {\n        this.yylloc.last_column -= len;\n      }\n\n      this.yylloc.range[1] = this.yylloc.range[0] + this.yyleng;\n      this.done = false;\n      return this;\n    },\n\n    /**\n     * cache matched text and append it on next action\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    more: function lexer_more() {\n      this._more = true;\n      return this;\n    },\n\n    /**\n     * signal the lexer that this rule fails to match the input, so the\n     * next matching rule (regex) should be tested instead.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    reject: function lexer_reject() {\n      if (this.options.backtrack_lexer) {\n        this._backtrack = true;\n      } else {\n        // when the `parseError()` call returns, we MUST ensure that the error is registered.\n        // We accomplish this by signaling an 'error' token to be produced for the current\n        // `.lex()` run.\n        var lineno_msg = '';\n\n        if (this.yylloc) {\n          lineno_msg = ' on line ' + (this.yylineno + 1);\n        }\n\n        var p = this.constructLexErrorInfo(\n          'Lexical error' + lineno_msg + ': You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).',\n          false\n        );\n\n        this._signaled_error_token = this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n      }\n\n      return this;\n    },\n\n    /**\n     * retain first n characters of the match\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    less: function lexer_less(n) {\n      return this.unput(this.match.slice(n));\n    },\n\n    /**\n     * return (part of the) already matched input, i.e. for error\n     * messages.\n     * \n     * Limit the returned string length to `maxSize` (default: 20).\n     * \n     * Limit the returned string to the `maxLines` number of lines of\n     * input (default: 1).\n     * \n     * Negative limit values equal *unlimited*.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    pastInput: function lexer_pastInput(maxSize, maxLines) {\n      var past = this.matched.substring(0, this.matched.length - this.match.length);\n\n      if (maxSize < 0)\n        maxSize = past.length;\n      else if (!maxSize)\n        maxSize = 20;\n\n      if (maxLines < 0)\n        maxLines = past.length;          // can't ever have more input lines than this! \n      else if (!maxLines)\n        maxLines = 1;\n\n      // `substr` anticipation: treat \\r\\n as a single character and take a little\n      // more than necessary so that we can still properly check against maxSize\n      // after we've transformed and limited the newLines in here:\n      past = past.substr(-maxSize * 2 - 2);\n\n      // now that we have a significantly reduced string to process, transform the newlines\n      // and chop them, then limit them:\n      var a = past.replace(/\\r\\n|\\r/g, '\\n').split('\\n');\n\n      a = a.slice(-maxLines);\n      past = a.join('\\n');\n\n      // When, after limiting to maxLines, we still have too much to return,\n      // do add an ellipsis prefix...\n      if (past.length > maxSize) {\n        past = '...' + past.substr(-maxSize);\n      }\n\n      return past;\n    },\n\n    /**\n     * return (part of the) upcoming input, i.e. for error messages.\n     * \n     * Limit the returned string length to `maxSize` (default: 20).\n     * \n     * Limit the returned string to the `maxLines` number of lines of input (default: 1).\n     * \n     * Negative limit values equal *unlimited*.\n     *\n     * > ### NOTE ###\n     * >\n     * > *\"upcoming input\"* is defined as the whole of the both\n     * > the *currently lexed* input, together with any remaining input\n     * > following that. *\"currently lexed\"* input is the input \n     * > already recognized by the lexer but not yet returned with\n     * > the lexer token. This happens when you are invoking this API\n     * > from inside any lexer rule action code block. \n     * >\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    upcomingInput: function lexer_upcomingInput(maxSize, maxLines) {\n      var next = this.match;\n\n      if (maxSize < 0)\n        maxSize = next.length + this._input.length;\n      else if (!maxSize)\n        maxSize = 20;\n\n      if (maxLines < 0)\n        maxLines = maxSize;          // can't ever have more input lines than this! \n      else if (!maxLines)\n        maxLines = 1;\n\n      // `substring` anticipation: treat \\r\\n as a single character and take a little\n      // more than necessary so that we can still properly check against maxSize\n      // after we've transformed and limited the newLines in here:\n      if (next.length < maxSize * 2 + 2) {\n        next += this._input.substring(0, maxSize * 2 + 2);   // substring is faster on Chrome/V8 \n      }\n\n      // now that we have a significantly reduced string to process, transform the newlines\n      // and chop them, then limit them:\n      var a = next.replace(/\\r\\n|\\r/g, '\\n').split('\\n');\n\n      a = a.slice(0, maxLines);\n      next = a.join('\\n');\n\n      // When, after limiting to maxLines, we still have too much to return,\n      // do add an ellipsis postfix...\n      if (next.length > maxSize) {\n        next = next.substring(0, maxSize) + '...';\n      }\n\n      return next;\n    },\n\n    /**\n     * return a string which displays the character position where the\n     * lexing error occurred, i.e. for error messages\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    showPosition: function lexer_showPosition(maxPrefix, maxPostfix) {\n      var pre = this.pastInput(maxPrefix).replace(/\\s/g, ' ');\n      var c = new Array(pre.length + 1).join('-');\n      return pre + this.upcomingInput(maxPostfix).replace(/\\s/g, ' ') + '\\n' + c + '^';\n    },\n\n    /**\n     * return an YYLLOC info object derived off the given context (actual, preceding, following, current).\n     * Use this method when the given `actual` location is not guaranteed to exist (i.e. when\n     * it MAY be NULL) and you MUST have a valid location info object anyway:\n     * then we take the given context of the `preceding` and `following` locations, IFF those are available,\n     * and reconstruct the `actual` location info from those.\n     * If this fails, the heuristic is to take the `current` location, IFF available.\n     * If this fails as well, we assume the sought location is at/around the current lexer position\n     * and then produce that one as a response. DO NOTE that these heuristic/derived location info\n     * values MAY be inaccurate!\n     *\n     * NOTE: `deriveLocationInfo()` ALWAYS produces a location info object *copy* of `actual`, not just\n     * a *reference* hence all input location objects can be assumed to be 'constant' (function has no side-effects).\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    deriveLocationInfo: function lexer_deriveYYLLOC(actual, preceding, following, current) {\n      var loc = {\n        first_line: 1,\n        first_column: 0,\n        last_line: 1,\n        last_column: 0,\n        range: [0, 0]\n      };\n\n      if (actual) {\n        loc.first_line = actual.first_line | 0;\n        loc.last_line = actual.last_line | 0;\n        loc.first_column = actual.first_column | 0;\n        loc.last_column = actual.last_column | 0;\n\n        if (actual.range) {\n          loc.range[0] = actual.range[0] | 0;\n          loc.range[1] = actual.range[1] | 0;\n        }\n      }\n\n      if (loc.first_line <= 0 || loc.last_line < loc.first_line) {\n        // plan B: heuristic using preceding and following:\n        if (loc.first_line <= 0 && preceding) {\n          loc.first_line = preceding.last_line | 0;\n          loc.first_column = preceding.last_column | 0;\n\n          if (preceding.range) {\n            loc.range[0] = actual.range[1] | 0;\n          }\n        }\n\n        if ((loc.last_line <= 0 || loc.last_line < loc.first_line) && following) {\n          loc.last_line = following.first_line | 0;\n          loc.last_column = following.first_column | 0;\n\n          if (following.range) {\n            loc.range[1] = actual.range[0] | 0;\n          }\n        }\n\n        // plan C?: see if the 'current' location is useful/sane too:\n        if (loc.first_line <= 0 && current && (loc.last_line <= 0 || current.last_line <= loc.last_line)) {\n          loc.first_line = current.first_line | 0;\n          loc.first_column = current.first_column | 0;\n\n          if (current.range) {\n            loc.range[0] = current.range[0] | 0;\n          }\n        }\n\n        if (loc.last_line <= 0 && current && (loc.first_line <= 0 || current.first_line >= loc.first_line)) {\n          loc.last_line = current.last_line | 0;\n          loc.last_column = current.last_column | 0;\n\n          if (current.range) {\n            loc.range[1] = current.range[1] | 0;\n          }\n        }\n      }\n\n      // sanitize: fix last_line BEFORE we fix first_line as we use the 'raw' value of the latter\n      // or plan D heuristics to produce a 'sensible' last_line value:\n      if (loc.last_line <= 0) {\n        if (loc.first_line <= 0) {\n          loc.first_line = this.yylloc.first_line;\n          loc.last_line = this.yylloc.last_line;\n          loc.first_column = this.yylloc.first_column;\n          loc.last_column = this.yylloc.last_column;\n          loc.range[0] = this.yylloc.range[0];\n          loc.range[1] = this.yylloc.range[1];\n        } else {\n          loc.last_line = this.yylloc.last_line;\n          loc.last_column = this.yylloc.last_column;\n          loc.range[1] = this.yylloc.range[1];\n        }\n      }\n\n      if (loc.first_line <= 0) {\n        loc.first_line = loc.last_line;\n        loc.first_column = 0;  // loc.last_column; \n        loc.range[1] = loc.range[0];\n      }\n\n      if (loc.first_column < 0) {\n        loc.first_column = 0;\n      }\n\n      if (loc.last_column < 0) {\n        loc.last_column = (loc.first_column > 0 ? loc.first_column : 80);\n      }\n\n      return loc;\n    },\n\n    /**\n     * return a string which displays the lines & columns of input which are referenced \n     * by the given location info range, plus a few lines of context.\n     * \n     * This function pretty-prints the indicated section of the input, with line numbers \n     * and everything!\n     * \n     * This function is very useful to provide highly readable error reports, while\n     * the location range may be specified in various flexible ways:\n     * \n     * - `loc` is the location info object which references the area which should be\n     *   displayed and 'marked up': these lines & columns of text are marked up by `^`\n     *   characters below each character in the entire input range.\n     * \n     * - `context_loc` is the *optional* location info object which instructs this\n     *   pretty-printer how much *leading* context should be displayed alongside\n     *   the area referenced by `loc`. This can help provide context for the displayed\n     *   error, etc.\n     * \n     *   When this location info is not provided, a default context of 3 lines is\n     *   used.\n     * \n     * - `context_loc2` is another *optional* location info object, which serves\n     *   a similar purpose to `context_loc`: it specifies the amount of *trailing*\n     *   context lines to display in the pretty-print output.\n     * \n     *   When this location info is not provided, a default context of 1 line only is\n     *   used.\n     * \n     * Special Notes:\n     * \n     * - when the `loc`-indicated range is very large (about 5 lines or more), then\n     *   only the first and last few lines of this block are printed while a\n     *   `...continued...` message will be printed between them.\n     * \n     *   This serves the purpose of not printing a huge amount of text when the `loc`\n     *   range happens to be huge: this way a manageable & readable output results\n     *   for arbitrary large ranges.\n     * \n     * - this function can display lines of input which whave not yet been lexed.\n     *   `prettyPrintRange()` can access the entire input!\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    prettyPrintRange: function lexer_prettyPrintRange(loc, context_loc, context_loc2) {\n      loc = this.deriveLocationInfo(loc, context_loc, context_loc2);\n      const CONTEXT = 3;\n      const CONTEXT_TAIL = 1;\n      const MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT = 2;\n      var input = this.matched + this._input;\n      var lines = input.split('\\n');\n      var l0 = Math.max(1, (context_loc ? context_loc.first_line : loc.first_line - CONTEXT));\n      var l1 = Math.max(1, (context_loc2 ? context_loc2.last_line : loc.last_line + CONTEXT_TAIL));\n      var lineno_display_width = 1 + Math.log10(l1 | 1) | 0;\n      var ws_prefix = new Array(lineno_display_width).join(' ');\n      var nonempty_line_indexes = [];\n\n      var rv = lines.slice(l0 - 1, l1 + 1).map(function injectLineNumber(line, index) {\n        var lno = index + l0;\n        var lno_pfx = (ws_prefix + lno).substr(-lineno_display_width);\n        var rv = lno_pfx + ': ' + line;\n        var errpfx = new Array(lineno_display_width + 1).join('^');\n        var offset = 2 + 1;\n        var len = 0;\n\n        if (lno === loc.first_line) {\n          offset += loc.first_column;\n\n          len = Math.max(\n            2,\n            ((lno === loc.last_line ? loc.last_column : line.length)) - loc.first_column + 1\n          );\n        } else if (lno === loc.last_line) {\n          len = Math.max(2, loc.last_column + 1);\n        } else if (lno > loc.first_line && lno < loc.last_line) {\n          len = Math.max(2, line.length + 1);\n        }\n\n        if (len) {\n          var lead = new Array(offset).join('.');\n          var mark = new Array(len).join('^');\n          rv += '\\n' + errpfx + lead + mark;\n\n          if (line.trim().length > 0) {\n            nonempty_line_indexes.push(index);\n          }\n        }\n\n        rv = rv.replace(/\\t/g, ' ');\n        return rv;\n      });\n\n      // now make sure we don't print an overly large amount of error area: limit it \n      // to the top and bottom line count:\n      if (nonempty_line_indexes.length > 2 * MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT) {\n        var clip_start = nonempty_line_indexes[MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT - 1] + 1;\n        var clip_end = nonempty_line_indexes[nonempty_line_indexes.length - MINIMUM_VISIBLE_NONEMPTY_LINE_COUNT] - 1;\n        var intermediate_line = new Array(lineno_display_width + 1).join(' ') + '  (...continued...)';\n        intermediate_line += '\\n' + new Array(lineno_display_width + 1).join('-') + '  (---------------)';\n        rv.splice(clip_start, clip_end - clip_start + 1, intermediate_line);\n      }\n\n      return rv.join('\\n');\n    },\n\n    /**\n     * helper function, used to produce a human readable description as a string, given\n     * the input `yylloc` location object.\n     * \n     * Set `display_range_too` to TRUE to include the string character index position(s)\n     * in the description if the `yylloc.range` is available.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    describeYYLLOC: function lexer_describe_yylloc(yylloc, display_range_too) {\n      var l1 = yylloc.first_line;\n      var l2 = yylloc.last_line;\n      var c1 = yylloc.first_column;\n      var c2 = yylloc.last_column;\n      var dl = l2 - l1;\n      var dc = c2 - c1;\n      var rv;\n\n      if (dl === 0) {\n        rv = 'line ' + l1 + ', ';\n\n        if (dc <= 1) {\n          rv += 'column ' + c1;\n        } else {\n          rv += 'columns ' + c1 + ' .. ' + c2;\n        }\n      } else {\n        rv = 'lines ' + l1 + '(column ' + c1 + ') .. ' + l2 + '(column ' + c2 + ')';\n      }\n\n      if (yylloc.range && display_range_too) {\n        var r1 = yylloc.range[0];\n        var r2 = yylloc.range[1] - 1;\n\n        if (r2 <= r1) {\n          rv += ' {String Offset: ' + r1 + '}';\n        } else {\n          rv += ' {String Offset range: ' + r1 + ' .. ' + r2 + '}';\n        }\n      }\n\n      return rv;\n    },\n\n    /**\n     * test the lexed token: return FALSE when not a match, otherwise return token.\n     * \n     * `match` is supposed to be an array coming out of a regex match, i.e. `match[0]`\n     * contains the actually matched text string.\n     * \n     * Also move the input cursor forward and update the match collectors:\n     * \n     * - `yytext`\n     * - `yyleng`\n     * - `match`\n     * - `matches`\n     * - `yylloc`\n     * - `offset`\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    test_match: function lexer_test_match(match, indexed_rule) {\n      var token, lines, backup, match_str, match_str_len;\n\n      if (this.options.backtrack_lexer) {\n        // save context\n        backup = {\n          yylineno: this.yylineno,\n\n          yylloc: {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylloc.last_line,\n            first_column: this.yylloc.first_column,\n            last_column: this.yylloc.last_column,\n            range: this.yylloc.range.slice(0)\n          },\n\n          yytext: this.yytext,\n          match: this.match,\n          matches: this.matches,\n          matched: this.matched,\n          yyleng: this.yyleng,\n          offset: this.offset,\n          _more: this._more,\n          _input: this._input,\n\n          //_signaled_error_token: this._signaled_error_token,\n          yy: this.yy,\n\n          conditionStack: this.conditionStack.slice(0),\n          done: this.done\n        };\n      }\n\n      match_str = match[0];\n      match_str_len = match_str.length;\n\n      // if (match_str.indexOf('\\n') !== -1 || match_str.indexOf('\\r') !== -1) {\n      lines = match_str.split(/(?:\\r\\n?|\\n)/g);\n\n      if (lines.length > 1) {\n        this.yylineno += lines.length - 1;\n        this.yylloc.last_line = this.yylineno + 1;\n        this.yylloc.last_column = lines[lines.length - 1].length;\n      } else {\n        this.yylloc.last_column += match_str_len;\n      }\n\n      // }\n      this.yytext += match_str;\n\n      this.match += match_str;\n      this.matched += match_str;\n      this.matches = match;\n      this.yyleng = this.yytext.length;\n      this.yylloc.range[1] += match_str_len;\n\n      // previous lex rules MAY have invoked the `more()` API rather than producing a token:\n      // those rules will already have moved this `offset` forward matching their match lengths,\n      // hence we must only add our own match length now:\n      this.offset += match_str_len;\n\n      this._more = false;\n      this._backtrack = false;\n      this._input = this._input.slice(match_str_len);\n\n      // calling this method:\n      //\n      //   function lexer__performAction(yy, yyrulenumber, YY_START) {...}\n      token = this.performAction.call(\n        this,\n        this.yy,\n        indexed_rule,\n        this.conditionStack[this.conditionStack.length - 1] /* = YY_START */\n      );\n\n      // otherwise, when the action codes are all simple return token statements:\n      //token = this.simpleCaseActionClusters[indexed_rule];\n\n      if (this.done && this._input) {\n        this.done = false;\n      }\n\n      if (token) {\n        return token;\n      } else if (this._backtrack) {\n        // recover context\n        for (var k in backup) {\n          this[k] = backup[k];\n        }\n\n        this.__currentRuleSet__ = null;\n        return false;  // rule action called reject() implying the next rule should be tested instead. \n      } else if (this._signaled_error_token) {\n        // produce one 'error' token as `.parseError()` in `reject()`\n        // did not guarantee a failure signal by throwing an exception!\n        token = this._signaled_error_token;\n\n        this._signaled_error_token = false;\n        return token;\n      }\n\n      return false;\n    },\n\n    /**\n     * return next match in input\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    next: function lexer_next() {\n      if (this.done) {\n        this.clear();\n        return this.EOF;\n      }\n\n      if (!this._input) {\n        this.done = true;\n      }\n\n      var token, match, tempMatch, index;\n\n      if (!this._more) {\n        this.clear();\n      }\n\n      var spec = this.__currentRuleSet__;\n\n      if (!spec) {\n        // Update the ruleset cache as we apparently encountered a state change or just started lexing.\n        // The cache is set up for fast lookup -- we assume a lexer will switch states much less often than it will\n        // invoke the `lex()` token-producing API and related APIs, hence caching the set for direct access helps\n        // speed up those activities a tiny bit.\n        spec = this.__currentRuleSet__ = this._currentRules();\n\n        // Check whether a *sane* condition has been pushed before: this makes the lexer robust against\n        // user-programmer bugs such as https://github.com/zaach/jison-lex/issues/19\n        if (!spec || !spec.rules) {\n          var lineno_msg = '';\n\n          if (this.options.trackPosition) {\n            lineno_msg = ' on line ' + (this.yylineno + 1);\n          }\n\n          var p = this.constructLexErrorInfo(\n            'Internal lexer engine error' + lineno_msg + ': The lex grammar programmer pushed a non-existing condition name \"' + this.topState() + '\"; this is a fatal error and should be reported to the application programmer team!',\n            false\n          );\n\n          // produce one 'error' token until this situation has been resolved, most probably by parse termination!\n          return this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n        }\n      }\n\n      var rule_ids = spec.rules;\n      var regexes = spec.__rule_regexes;\n      var len = spec.__rule_count;\n\n      // Note: the arrays are 1-based, while `len` itself is a valid index,\n      // hence the non-standard less-or-equal check in the next loop condition!\n      for (var i = 1; i <= len; i++) {\n        tempMatch = this._input.match(regexes[i]);\n\n        if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n          match = tempMatch;\n          index = i;\n\n          if (this.options.backtrack_lexer) {\n            token = this.test_match(tempMatch, rule_ids[i]);\n\n            if (token !== false) {\n              return token;\n            } else if (this._backtrack) {\n              match = undefined;\n              continue;  // rule action called reject() implying a rule MISmatch. \n            } else {\n              // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n              return false;\n            }\n          } else if (!this.options.flex) {\n            break;\n          }\n        }\n      }\n\n      if (match) {\n        token = this.test_match(match, rule_ids[index]);\n\n        if (token !== false) {\n          return token;\n        }\n\n        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n        return false;\n      }\n\n      if (!this._input) {\n        this.done = true;\n        this.clear();\n        return this.EOF;\n      } else {\n        var lineno_msg = '';\n\n        if (this.options.trackPosition) {\n          lineno_msg = ' on line ' + (this.yylineno + 1);\n        }\n\n        var p = this.constructLexErrorInfo(\n          'Lexical error' + lineno_msg + ': Unrecognized text.',\n          this.options.lexerErrorsAreRecoverable\n        );\n\n        var pendingInput = this._input;\n        var activeCondition = this.topState();\n        var conditionStackDepth = this.conditionStack.length;\n        token = this.parseError(p.errStr, p, this.JisonLexerError) || this.ERROR;\n\n        if (token === this.ERROR) {\n          // we can try to recover from a lexer error that `parseError()` did not 'recover' for us\n          // by moving forward at least one character at a time IFF the (user-specified?) `parseError()`\n          // has not consumed/modified any pending input or changed state in the error handler:\n          if (!this.matches && // and make sure the input has been modified/consumed ...\n          pendingInput === this._input && // ...or the lexer state has been modified significantly enough\n          // to merit a non-consuming error handling action right now.\n          activeCondition === this.topState() && conditionStackDepth === this.conditionStack.length) {\n            this.input();\n          }\n        }\n\n        return token;\n      }\n    },\n\n    /**\n     * return next match that has a token\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    lex: function lexer_lex() {\n      var r;\n\n      // allow the PRE/POST handlers set/modify the return token for maximum flexibility of the generated lexer:\n      if (typeof this.pre_lex === 'function') {\n        r = this.pre_lex.call(this, 0);\n      }\n\n      if (typeof this.options.pre_lex === 'function') {\n        // (also account for a userdef function which does not return any value: keep the token as is)\n        r = this.options.pre_lex.call(this, r) || r;\n      }\n\n      if (this.yy && typeof this.yy.pre_lex === 'function') {\n        // (also account for a userdef function which does not return any value: keep the token as is)\n        r = this.yy.pre_lex.call(this, r) || r;\n      }\n\n      while (!r) {\n        r = this.next();\n      }\n\n      if (this.yy && typeof this.yy.post_lex === 'function') {\n        // (also account for a userdef function which does not return any value: keep the token as is)\n        r = this.yy.post_lex.call(this, r) || r;\n      }\n\n      if (typeof this.options.post_lex === 'function') {\n        // (also account for a userdef function which does not return any value: keep the token as is)\n        r = this.options.post_lex.call(this, r) || r;\n      }\n\n      if (typeof this.post_lex === 'function') {\n        // (also account for a userdef function which does not return any value: keep the token as is)\n        r = this.post_lex.call(this, r) || r;\n      }\n\n      return r;\n    },\n\n    /**\n     * return next match that has a token. Identical to the `lex()` API but does not invoke any of the \n     * `pre_lex()` nor any of the `post_lex()` callbacks.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    fastLex: function lexer_fastLex() {\n      var r;\n\n      while (!r) {\n        r = this.next();\n      }\n\n      return r;\n    },\n\n    /**\n     * return info about the lexer state that can help a parser or other lexer API user to use the\n     * most efficient means available. This API is provided to aid run-time performance for larger\n     * systems which employ this lexer.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    canIUse: function lexer_canIUse() {\n      var rv = {\n        fastLex: !(typeof this.pre_lex === 'function' || typeof this.options.pre_lex === 'function' || this.yy && typeof this.yy.pre_lex === 'function' || this.yy && typeof this.yy.post_lex === 'function' || typeof this.options.post_lex === 'function' || typeof this.post_lex === 'function') && typeof this.fastLex === 'function'\n      };\n\n      return rv;\n    },\n\n    /**\n     * backwards compatible alias for `pushState()`;\n     * the latter is symmetrical with `popState()` and we advise to use\n     * those APIs in any modern lexer code, rather than `begin()`.\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    begin: function lexer_begin(condition) {\n      return this.pushState(condition);\n    },\n\n    /**\n     * activates a new lexer condition state (pushes the new lexer\n     * condition state onto the condition stack)\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    pushState: function lexer_pushState(condition) {\n      this.conditionStack.push(condition);\n      this.__currentRuleSet__ = null;\n      return this;\n    },\n\n    /**\n     * pop the previously active lexer condition state off the condition\n     * stack\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    popState: function lexer_popState() {\n      var n = this.conditionStack.length - 1;\n\n      if (n > 0) {\n        this.__currentRuleSet__ = null;\n        return this.conditionStack.pop();\n      } else {\n        return this.conditionStack[0];\n      }\n    },\n\n    /**\n     * return the currently active lexer condition state; when an index\n     * argument is provided it produces the N-th previous condition state,\n     * if available\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    topState: function lexer_topState(n) {\n      n = this.conditionStack.length - 1 - Math.abs(n || 0);\n\n      if (n >= 0) {\n        return this.conditionStack[n];\n      } else {\n        return 'INITIAL';\n      }\n    },\n\n    /**\n     * (internal) determine the lexer rule set which is active for the\n     * currently active lexer condition state\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    _currentRules: function lexer__currentRules() {\n      if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n        return this.conditions[this.conditionStack[this.conditionStack.length - 1]];\n      } else {\n        return this.conditions['INITIAL'];\n      }\n    },\n\n    /**\n     * return the number of states currently on the stack\n     * \n     * @public\n     * @this {RegExpLexer}\n     */\n    stateStackSize: function lexer_stateStackSize() {\n      return this.conditionStack.length;\n    },\n\n    options: {\n      trackPosition: true\n    },\n\n    JisonLexerError: JisonLexerError,\n\n    performAction: function lexer__performAction(yy, yyrulenumber, YY_START) {\n      var yy_ = this;\n      var YYSTATE = YY_START;\n\n      switch (yyrulenumber) {\n      case 1:\n        /*! Conditions:: INITIAL */\n        /*! Rule::       \\s+ */\n        /* skip whitespace */\n        break;\n\n      default:\n        return this.simpleCaseActionClusters[yyrulenumber];\n      }\n    },\n\n    simpleCaseActionClusters: {\n      /*! Conditions:: INITIAL */\n      /*! Rule::       (--[0-9a-z-A-Z-]*) */\n      0: 13,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       \\* */\n      2: 5,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       \\/ */\n      3: 6,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       \\+ */\n      4: 3,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       - */\n      5: 4,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)px\\b */\n      6: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)cm\\b */\n      7: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)mm\\b */\n      8: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)in\\b */\n      9: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)pt\\b */\n      10: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)pc\\b */\n      11: 15,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)deg\\b */\n      12: 16,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)grad\\b */\n      13: 16,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)rad\\b */\n      14: 16,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)turn\\b */\n      15: 16,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)s\\b */\n      16: 17,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ms\\b */\n      17: 17,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)Hz\\b */\n      18: 18,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)kHz\\b */\n      19: 18,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dpi\\b */\n      20: 19,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dpcm\\b */\n      21: 19,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)dppx\\b */\n      22: 19,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)em\\b */\n      23: 20,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ex\\b */\n      24: 21,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)ch\\b */\n      25: 22,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)rem\\b */\n      26: 23,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vw\\b */\n      27: 25,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vh\\b */\n      28: 24,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vmin\\b */\n      29: 26,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)vmax\\b */\n      30: 27,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)% */\n      31: 28,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([0-9]+(\\.[0-9]*)?|\\.[0-9]+)\\b */\n      32: 11,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       (calc) */\n      33: 9,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       (var) */\n      34: 12,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       ([a-z]+) */\n      35: 10,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       \\( */\n      36: 7,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       \\) */\n      37: 8,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       , */\n      38: 14,\n\n      /*! Conditions:: INITIAL */\n      /*! Rule::       $ */\n      39: 1\n    },\n\n    rules: [\n      /*  0: */  /^(?:(--[\\d\\-A-Za-z]*))/,\n      /*  1: */  /^(?:\\s+)/,\n      /*  2: */  /^(?:\\*)/,\n      /*  3: */  /^(?:\\/)/,\n      /*  4: */  /^(?:\\+)/,\n      /*  5: */  /^(?:-)/,\n      /*  6: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)px\\b)/,\n      /*  7: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)cm\\b)/,\n      /*  8: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)mm\\b)/,\n      /*  9: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)in\\b)/,\n      /* 10: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)pt\\b)/,\n      /* 11: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)pc\\b)/,\n      /* 12: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)deg\\b)/,\n      /* 13: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)grad\\b)/,\n      /* 14: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)rad\\b)/,\n      /* 15: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)turn\\b)/,\n      /* 16: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)s\\b)/,\n      /* 17: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)ms\\b)/,\n      /* 18: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)Hz\\b)/,\n      /* 19: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)kHz\\b)/,\n      /* 20: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)dpi\\b)/,\n      /* 21: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)dpcm\\b)/,\n      /* 22: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)dppx\\b)/,\n      /* 23: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)em\\b)/,\n      /* 24: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)ex\\b)/,\n      /* 25: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)ch\\b)/,\n      /* 26: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)rem\\b)/,\n      /* 27: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)vw\\b)/,\n      /* 28: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)vh\\b)/,\n      /* 29: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)vmin\\b)/,\n      /* 30: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)vmax\\b)/,\n      /* 31: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)%)/,\n      /* 32: */  /^(?:(\\d+(\\.\\d*)?|\\.\\d+)\\b)/,\n      /* 33: */  /^(?:(calc))/,\n      /* 34: */  /^(?:(var))/,\n      /* 35: */  /^(?:([a-z]+))/,\n      /* 36: */  /^(?:\\()/,\n      /* 37: */  /^(?:\\))/,\n      /* 38: */  /^(?:,)/,\n      /* 39: */  /^(?:$)/\n    ],\n\n    conditions: {\n      'INITIAL': {\n        rules: [\n          0,\n          1,\n          2,\n          3,\n          4,\n          5,\n          6,\n          7,\n          8,\n          9,\n          10,\n          11,\n          12,\n          13,\n          14,\n          15,\n          16,\n          17,\n          18,\n          19,\n          20,\n          21,\n          22,\n          23,\n          24,\n          25,\n          26,\n          27,\n          28,\n          29,\n          30,\n          31,\n          32,\n          33,\n          34,\n          35,\n          36,\n          37,\n          38,\n          39\n        ],\n\n        inclusive: true\n      }\n    }\n  };\n\n  return lexer;\n}();\nparser.lexer = lexer;\n\n\n\nfunction Parser() {\n  this.yy = {};\n}\nParser.prototype = parser;\nparser.Parser = Parser;\n\nreturn new Parser();\n})();\n\n        \n\n\nif (typeof require !== 'undefined' && typeof exports !== 'undefined') {\n  exports.parser = parser;\n  exports.Parser = parser.Parser;\n  exports.parse = function () {\n    return parser.parse.apply(parser, arguments);\n  };\n  \n}\n"], "mappings": "AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIY,IAAIA,MAAM,GAAI,YAAY;EAGtC;EACA;EACA;EACA;EACA,SAASC,gBAAgBA,CAACC,GAAG,EAAEC,IAAI,EAAE;IACjCC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;MAChCC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE;IACX,CAAC,CAAC;IAEF,IAAIN,GAAG,IAAI,IAAI,EAAEA,GAAG,GAAG,KAAK;IAE5BE,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;MACnCC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAEN;IACX,CAAC,CAAC;IAEF,IAAI,CAACC,IAAI,GAAGA,IAAI;IAEhB,IAAIM,UAAU;IACd,IAAIN,IAAI,IAAIA,IAAI,CAACO,SAAS,YAAYC,KAAK,EAAE;MACzC,IAAIC,GAAG,GAAGT,IAAI,CAACO,SAAS;MACxB,IAAI,CAACG,OAAO,GAAGD,GAAG,CAACC,OAAO,IAAIX,GAAG;MACjCO,UAAU,GAAGG,GAAG,CAACE,KAAK;IAC1B;IACA,IAAI,CAACL,UAAU,EAAE;MACb,IAAIE,KAAK,CAACI,cAAc,CAAC,mBAAmB,CAAC,EAAE;QAAS;QACpDJ,KAAK,CAACK,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACC,WAAW,CAAC;MACnD,CAAC,MAAM;QACHR,UAAU,GAAI,IAAIE,KAAK,CAACT,GAAG,CAAC,CAAEY,KAAK;MACvC;IACJ;IACA,IAAIL,UAAU,EAAE;MACZL,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;QACjCC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAEC;MACX,CAAC,CAAC;IACN;EACJ;EAEA,IAAI,OAAOL,MAAM,CAACc,cAAc,KAAK,UAAU,EAAE;IAC7Cd,MAAM,CAACc,cAAc,CAACjB,gBAAgB,CAACkB,SAAS,EAAER,KAAK,CAACQ,SAAS,CAAC;EACtE,CAAC,MAAM;IACHlB,gBAAgB,CAACkB,SAAS,GAAGf,MAAM,CAACgB,MAAM,CAACT,KAAK,CAACQ,SAAS,CAAC;EAC/D;EACAlB,gBAAgB,CAACkB,SAAS,CAACF,WAAW,GAAGhB,gBAAgB;EACzDA,gBAAgB,CAACkB,SAAS,CAACE,IAAI,GAAG,kBAAkB;;EAK5C;EACA,SAASC,EAAEA,CAACC,CAAC,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,CAAC,GAAGF,CAAC,CAACG,GAAG;IACb,IAAIC,CAAC,GAAGJ,CAAC,CAACK,IAAI;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGL,CAAC,CAACM,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtCL,EAAE,CAACQ,IAAI,CAAC,CACJP,CAAC,CAACI,CAAC,CAAC,EACJF,CAAC,CAACE,CAAC,CAAC,CACP,CAAC;IACN;IACA,OAAOL,EAAE;EACb;;EAIA;EACA,SAASS,GAAGA,CAACV,CAAC,EAAE;IACZ,IAAIC,EAAE,GAAG,CAAC,CAAC;IACX,IAAIU,CAAC,GAAGX,CAAC,CAACY,GAAG;IACb,IAAIC,CAAC,GAAGb,CAAC,CAACc,IAAI;IACd,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACH,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIS,CAAC,GAAGJ,CAAC,CAACL,CAAC,CAAC;MACZL,EAAE,CAACc,CAAC,CAAC,GAAGF,CAAC,CAACP,CAAC,CAAC;IAChB;IACA,OAAOL,EAAE;EACb;;EAIA;EACA,SAASe,EAAEA,CAAChB,CAAC,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIU,CAAC,GAAGX,CAAC,CAACiB,GAAG;IACb,IAAIC,CAAC,GAAGlB,CAAC,CAACmB,MAAM;IAChB,IAAIC,CAAC,GAAGpB,CAAC,CAACqB,IAAI;IACd,IAAIC,CAAC,GAAGtB,CAAC,CAACuB,KAAK;IACf,IAAIC,CAAC,GAAGxB,CAAC,CAACyB,IAAI;IACd,IAAIZ,CAAC,GAAGb,CAAC,CAACc,IAAI;IACd,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACH,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIoB,CAAC,GAAGf,CAAC,CAACL,CAAC,CAAC;MACZ,IAAIqB,CAAC,GAAG,CAAC,CAAC;MACV,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,CAAC,EAAEX,CAAC,EAAE,EAAE;QACxB,IAAIa,CAAC,GAAGV,CAAC,CAACW,KAAK,CAAC,CAAC;QACjB,QAAQT,CAAC,CAACS,KAAK,CAAC,CAAC;UACjB,KAAK,CAAC;YACFF,CAAC,CAACC,CAAC,CAAC,GAAG,CACHJ,CAAC,CAACK,KAAK,CAAC,CAAC,EACThB,CAAC,CAACgB,KAAK,CAAC,CAAC,CACZ;YACD;UAEJ,KAAK,CAAC;YACFF,CAAC,CAACC,CAAC,CAAC,GAAGN,CAAC,CAACO,KAAK,CAAC,CAAC;YAChB;UAEJ;YACI;YACAF,CAAC,CAACC,CAAC,CAAC,GAAG,CACH,CAAC,CACJ;QACL;MACJ;MACA3B,EAAE,CAACQ,IAAI,CAACkB,CAAC,CAAC;IACd;IACA,OAAO1B,EAAE;EACb;;EAIA;EACA;EACA,SAASD,CAACA,CAAC8B,CAAC,EAAEvB,CAAC,EAAEe,CAAC,EAAE;IAChBA,CAAC,GAAGA,CAAC,IAAI,CAAC;IACV,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACxB,IAAI,CAACG,IAAI,CAACqB,CAAC,CAAC;MACZA,CAAC,IAAIR,CAAC;IACV;EACJ;;EAEA;EACA;EACA,SAASQ,CAACA,CAACxB,CAAC,EAAEC,CAAC,EAAE;IACbD,CAAC,GAAG,IAAI,CAACE,MAAM,GAAGF,CAAC;IACnB,KAAKC,CAAC,IAAID,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACrB,IAAI,CAACG,IAAI,CAAC,IAAI,CAACH,CAAC,CAAC,CAAC;IACtB;EACJ;;EAEA;EACA,SAASyB,CAACA,CAACT,CAAC,EAAE;IACV,IAAIrB,EAAE,GAAG,EAAE;IACX,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGe,CAAC,CAACd,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACtC,IAAI0B,CAAC,GAAGV,CAAC,CAAChB,CAAC,CAAC;MACZ;MACA,IAAI,OAAO0B,CAAC,KAAK,UAAU,EAAE;QACzB1B,CAAC,EAAE;QACH0B,CAAC,CAACC,KAAK,CAAChC,EAAE,EAAEqB,CAAC,CAAChB,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACHL,EAAE,CAACQ,IAAI,CAACuB,CAAC,CAAC;MACd;IACJ;IACA,OAAO/B,EAAE;EACb;EAGR,IAAIxB,MAAM,GAAG;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEJyD,KAAK,EAAE,SAASC,WAAWA,CAAA,EAAG,CAAE,CAAC;IACjCzD,gBAAgB,EAAEA,gBAAgB;IAClC0D,EAAE,EAAE,CAAC,CAAC;IACNC,OAAO,EAAE;MACPhB,IAAI,EAAE,MAAM;MACZiB,6BAA6B,EAAE,IAAI;MACnCC,8BAA8B,EAAE;IAClC,CAAC;IACDC,QAAQ,EAAE;MACR,SAAS,EAAE,CAAC;MACZ,MAAM,EAAE,CAAC;MACT,KAAK,EAAE,CAAC;MACR,OAAO,EAAE,EAAE;MACX,KAAK,EAAE,EAAE;MACT,OAAO,EAAE,EAAE;MACX,WAAW,EAAE,EAAE;MACf,SAAS,EAAE,EAAE;MACb,KAAK,EAAE,CAAC;MACR,KAAK,EAAE,EAAE;MACT,KAAK,EAAE,CAAC;MACR,KAAK,EAAE,EAAE;MACT,MAAM,EAAE,EAAE;MACV,QAAQ,EAAE,EAAE;MACZ,QAAQ,EAAE,CAAC;MACX,KAAK,EAAE,CAAC;MACR,aAAa,EAAE,CAAC;MAChB,QAAQ,EAAE,EAAE;MACZ,YAAY,EAAE,EAAE;MAChB,QAAQ,EAAE,EAAE;MACZ,MAAM,EAAE,EAAE;MACV,KAAK,EAAE,EAAE;MACT,QAAQ,EAAE,CAAC;MACX,KAAK,EAAE,CAAC;MACR,MAAM,EAAE,EAAE;MACV,KAAK,EAAE,EAAE;MACT,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,EAAE;MACX,KAAK,EAAE,EAAE;MACT,WAAW,EAAE,EAAE;MACf,cAAc,EAAE,EAAE;MAClB,OAAO,EAAE,CAAC;MACV,YAAY,EAAE,EAAE;MAChB,iBAAiB,EAAE,EAAE;MACrB,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACV,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,OAAO;MACV,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,aAAa;MAChB,EAAE,EAAE,QAAQ;MACZ,EAAE,EAAE,QAAQ;MACZ,EAAE,EAAE,SAAS;MACb,EAAE,EAAE,WAAW;MACf,EAAE,EAAE,OAAO;MACX,EAAE,EAAE,QAAQ;MACZ,EAAE,EAAE,OAAO;MACX,EAAE,EAAE,MAAM;MACV,EAAE,EAAE,MAAM;MACV,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,MAAM;MACV,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,KAAK;MACT,EAAE,EAAE,OAAO;MACX,EAAE,EAAE,OAAO;MACX,EAAE,EAAE;IACN,CAAC;IACDC,MAAM,EAAE,CAAC;IACLC,GAAG,EAAE,CAAC;IAEN;IACA;IACAC,iBAAiB,EAAE,IAAI;IACvBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE,IAAI;IACvBC,uBAAuB,EAAE,IAAI;IAC7BC,mBAAmB,EAAE,IAAI;IAEzBC,sBAAsB,EAAE,CAAC;IAAO;IAChCC,aAAa,EAAE,EAAE;IAAe;IAChCC,sBAAsB,EAAE,EAAE;IAAM;;IAEhC;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACAC,SAAS,EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAE;MACzC,OAAO,GAAG,GAAGA,MAAM,GAAG,GAAG;IAC7B,CAAC;IAED;IACA;IACA;IACAC,aAAa,EAAE,SAASC,oBAAoBA,CAACrC,MAAM,EAAE;MACjD,IAAI,IAAI,CAACsB,UAAU,CAACtB,MAAM,CAAC,EAAE;QACzB,OAAO,IAAI,CAACsB,UAAU,CAACtB,MAAM,CAAC;MAClC;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAInB,CAAC,GAAG,IAAI,CAACwC,QAAQ;MACrB,KAAK,IAAIiB,GAAG,IAAIzD,CAAC,EAAE;QACf,IAAIA,CAAC,CAACyD,GAAG,CAAC,KAAKtC,MAAM,EAAE;UACnB,OAAOsC,GAAG;QACd;MACJ;MACA,OAAO,IAAI;IACf,CAAC;IAED;IACA;IACA;IACA;IACAC,cAAc,EAAE,SAASC,qBAAqBA,CAACxC,MAAM,EAAE;MACnD,IAAIA,MAAM,KAAK,IAAI,CAACwB,GAAG,IAAI,IAAI,CAACiB,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,CAACzC,MAAM,CAAC,EAAE;QAC3F,OAAO,IAAI,CAACyC,sBAAsB,CAACzC,MAAM,CAAC;MAC9C,CAAC,MACI,IAAIA,MAAM,KAAK,IAAI,CAACwB,GAAG,EAAE;QAC1B,OAAO,cAAc;MACzB;MACA,IAAIkB,EAAE,GAAG,IAAI,CAACN,aAAa,CAACpC,MAAM,CAAC;MACnC,IAAI0C,EAAE,EAAE;QACJ,OAAO,IAAI,CAACT,SAAS,CAACS,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACf,CAAC;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,0BAA0B,EAAE,SAASC,iCAAiCA,CAACxC,KAAK,EAAEyC,eAAe,EAAE;MAC3F,IAAItB,MAAM,GAAG,IAAI,CAACA,MAAM;MACxB,IAAIuB,QAAQ,GAAG,EAAE;MACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;MACd;MACA;MACA,IAAI,CAACF,eAAe,IAAI,IAAI,CAACG,mBAAmB,IAAI,IAAI,CAACA,mBAAmB,CAAC5C,KAAK,CAAC,EAAE;QACjF,OAAO,CACH,IAAI,CAAC4C,mBAAmB,CAAC5C,KAAK,CAAC,CAClC;MACL;MACA,KAAK,IAAIrB,CAAC,IAAI,IAAI,CAACkE,KAAK,CAAC7C,KAAK,CAAC,EAAE;QAC7BrB,CAAC,GAAG,CAACA,CAAC;QACN,IAAIA,CAAC,KAAKwC,MAAM,EAAE;UACd,IAAI/B,CAAC,GAAGqD,eAAe,GAAG9D,CAAC,GAAG,IAAI,CAACwD,cAAc,CAACxD,CAAC,CAAC;UACpD,IAAIS,CAAC,IAAI,CAACuD,KAAK,CAACvD,CAAC,CAAC,EAAE;YAChBsD,QAAQ,CAACxD,IAAI,CAACE,CAAC,CAAC;YAChBuD,KAAK,CAACvD,CAAC,CAAC,GAAG,IAAI,CAAC,CAAQ;UAC5B;QACJ;MACJ;MACA,OAAOsD,QAAQ;IACnB,CAAC;IACLI,YAAY,EAAEtE,EAAE,CAAC;MACfI,GAAG,EAAE4B,CAAC,CAAC,CACP,EAAE,EACF/B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,CACT,CAAC;MACAK,IAAI,EAAE0B,CAAC,CAAC,CACR,CAAC,EACD/B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACD,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,EACP,CAAC,CACF;IACD,CAAC,CAAC;IACFsE,aAAa,EAAE,SAASC,qBAAqBA,CAACC,OAAO,CAAC,iBAAiBC,IAAI,EAAEC,QAAQ,EAAE;MAE7E;;MAEA;MACA,IAAItC,EAAE,GAAG,IAAI,CAACA,EAAE;MAChB,IAAIuC,QAAQ,GAAGvC,EAAE,CAAC3D,MAAM;MACxB,IAAImG,OAAO,GAAGxC,EAAE,CAACyC,KAAK;MAItB,QAAQL,OAAO;QACzB,KAAK,CAAC;UACF;;UAEA;UACA,IAAI,CAACM,CAAC,GAAGJ,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;UAC3B;UACA;QAEJ,KAAK,CAAC;UACF;;UAEA;UACA,IAAI,CAACK,CAAC,GAAGJ,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;UAC3B;;UAGA,OAAOC,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;UACzB;QAEJ,KAAK,CAAC;QACF;QACJ,KAAK,CAAC;QACF;QACJ,KAAK,CAAC;QACF;QACJ,KAAK,CAAC;UACF;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,gBAAgB;YAAE0D,QAAQ,EAAEL,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;YAAEO,IAAI,EAAEN,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;YAAEQ,KAAK,EAAEP,QAAQ,CAACD,IAAI;UAAE,CAAC;UAClH;QAEJ,KAAK,CAAC;UACF;;UAEA,IAAI,CAACK,CAAC,GAAGJ,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;UAC3B;QAEJ,KAAK,CAAC;UACF;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,MAAM;YAAEpC,KAAK,EAAEyF,QAAQ,CAACD,IAAI,GAAG,CAAC;UAAE,CAAC;UACpD;QAEJ,KAAK,CAAC;UACF;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,MAAM;YAAEpC,KAAK,EAAEyF,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;YAAES,MAAM,EAAER,QAAQ,CAACD,IAAI,GAAG,CAAC;UAAE,CAAC;UAChF;QAEJ,KAAK,CAAC;QACF;QACJ,KAAK,EAAE;QACH;QACJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAGJ,QAAQ,CAACD,IAAI,CAAC;UACvB;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,OAAO;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC;UAAE,CAAC;UAC7D;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,OAAO;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC,GAAG,CAAC;UAAE,CAAC;UAClE;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,aAAa;YAAEpC,KAAK,EAAEyF,QAAQ,CAACD,IAAI,GAAG,CAAC;UAAE,CAAC;UAC3D;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,aAAa;YAAEpC,KAAK,EAAEyF,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC;YAAEW,QAAQ,EAAEV,QAAQ,CAACD,IAAI,GAAG,CAAC;UAAE,CAAC;UACzF;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,aAAa;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE,QAAQ,CAACC,IAAI,CAACZ,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;UAAE,CAAC;UAC3G;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,YAAY;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE,QAAQ,CAACC,IAAI,CAACZ,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;UAAE,CAAC;UAC1G;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,WAAW;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE,QAAQ,CAACC,IAAI,CAACZ,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;UAAE,CAAC;UACzG;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,gBAAgB;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE,QAAQ,CAACC,IAAI,CAACZ,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;UAAE,CAAC;UAC9G;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,iBAAiB;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE,QAAQ,CAACC,IAAI,CAACZ,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;UAAE,CAAC;UAC/G;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACK,CAAC,GAAG;YAAEzD,IAAI,EAAE,SAAS;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAK,CAAC;UAC3E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,SAAS;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAK,CAAC;UAC3E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,SAAS;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAK,CAAC;UAC3E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,UAAU;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAM,CAAC;UAC7E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,SAAS;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAK,CAAC;UAC3E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,SAAS;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAK,CAAC;UAC3E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,WAAW;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAO,CAAC;UAC/E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,WAAW;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAO,CAAC;UAC/E;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAI,CAACP,CAAC,GAAG;YAAEzD,IAAI,EAAE,iBAAiB;YAAEpC,KAAK,EAAEkG,UAAU,CAACT,QAAQ,CAACD,IAAI,CAAC,CAAC;YAAEY,IAAI,EAAE;UAAI,CAAC;UAClF;QAEJ,KAAK,EAAE;UACH;;UAEA,IAAIE,IAAI,GAAGb,QAAQ,CAACD,IAAI,CAAC;UAAEc,IAAI,CAACtG,KAAK,IAAI,CAAC,CAAC;UAAE,IAAI,CAAC6F,CAAC,GAAGS,IAAI;UAC1D;MAEJ;IACA,CAAC;IACDnB,KAAK,EAAEpD,EAAE,CAAC;MACRC,GAAG,EAAEc,CAAC,CAAC,CACP,EAAE,EACF,CAAC,EACD,CAAC,EACD,EAAE,EACF,CAAC,EACD,EAAE,EACF/B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,EACPA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP8B,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,EACD,CAAC,EACD,EAAE,EACF,CAAC,EACD,CAAC,EACD,CAAC,EACD9B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD8B,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACPA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,EACD,CAAC,EACD,CAAC,CACF,CAAC;MACAX,MAAM,EAAEY,CAAC,CAAC,CACV,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,EACF/B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACX,CAAC,EACD,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACT8B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACPA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,EACDA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EACDA,CAAC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,EAAE,EACFA,CAAC,EACD,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACNA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACD,CAAC,EACD,EAAE,EACFA,CAAC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,EACTA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,CACT,CAAC;MACAT,IAAI,EAAEU,CAAC,CAAC,CACR/B,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,EACPA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,EACPA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN8B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,CACV,CAAC;MACAP,KAAK,EAAEQ,CAAC,CAAC,CACT,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACFD,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,CACP,CAAC;MACAL,IAAI,EAAEM,CAAC,CAAC,CACR/B,CAAC,EACD,CAAC,CAAC,EAAE,GAAG,CAAC,EACRA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN8B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACNA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN9B,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,CACR,CAAC;MACAc,IAAI,EAAEiB,CAAC,CAAC,CACR,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF/B,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EACVA,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACV8B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACRA,CAAC,EACD,CAAC,GAAG,EAAE,CAAC,CAAC,EACR,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,EAAE,EACF9B,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACF,EAAE,EACF,CAAC,EACDA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,EACF,EAAE,EACF,CAAC,EACD8B,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,CAAC,EACP9B,CAAC,EACD,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EACV8B,CAAC,EACD,CAAC,GAAG,EAAE,EAAE,CAAC,EACT,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,EAAE,CACH;IACD,CAAC,CAAC;IACF0D,cAAc,EAAE9E,GAAG,CAAC;MAClBE,GAAG,EAAEmB,CAAC,CAAC,CACP,CAAC,EACD,CAAC,EACD,CAAC,EACD/B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACX,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,CACH,CAAC;MACAc,IAAI,EAAEiB,CAAC,CAAC,CACR,CAAC,EACD,EAAE,EACF,EAAE,EACF/B,CAAC,EACD,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACX,EAAE,EACF,CAAC,EACD,EAAE,EACF,EAAE,EACFA,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACT,EAAE,EACF,EAAE,EACF,CAAC,CACF;IACD,CAAC,CAAC;IACFyF,UAAU,EAAE,SAASA,UAAUA,CAACC,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,EAAE;MACvD,IAAI/G,IAAI,CAACgH,WAAW,EAAE;QAClB,IAAI,OAAO,IAAI,CAAC1D,KAAK,KAAK,UAAU,EAAE;UAClC,IAAI,CAACA,KAAK,CAACwD,GAAG,CAAC;QACnB;QACA9G,IAAI,CAACiH,OAAO,CAAC,CAAC,CAAC,CAAa;MAChC,CAAC,MAAM;QACH,IAAI,OAAO,IAAI,CAAC3D,KAAK,KAAK,UAAU,EAAE;UAClC,IAAI,CAACA,KAAK,CAACwD,GAAG,CAAC;QACnB;QACA,IAAI,CAACC,cAAc,EAAE;UACjBA,cAAc,GAAG,IAAI,CAACjH,gBAAgB;QAC1C;QACA,MAAM,IAAIiH,cAAc,CAACD,GAAG,EAAE9G,IAAI,CAAC;MACvC;IACJ,CAAC;IACDkH,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;MACzB,IAAIC,IAAI,GAAG,IAAI;MACf,IAAIzG,KAAK,GAAG,IAAI0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAS;MACpC,IAAIC,MAAM,GAAG,IAAID,KAAK,CAAC,GAAG,CAAC,CAAC,CAAQ;;MAEpC,IAAIE,MAAM,GAAG,IAAIF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAQ;;MAEpC,IAAI7B,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAIgC,EAAE,GAAG,CAAC,CAAC,CAAyB;;MAMpC,IAAIjF,MAAM,GAAG,CAAC;MAId,IAAIuB,MAAM,GAAG,IAAI,CAACA,MAAM;MACxB,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG;MAClB,IAAI0D,kCAAkC,GAAI,IAAI,CAAChE,OAAO,CAACE,8BAA8B,GAAG,CAAC,IAAK,CAAC;MAC/F,IAAI+D,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,2FAA2F;MAElH,IAAIzB,KAAK;MACT,IAAI,IAAI,CAAC0B,SAAS,EAAE;QAChB1B,KAAK,GAAG,IAAI,CAAC0B,SAAS;MAC1B,CAAC,MAAM;QACH1B,KAAK,GAAG,IAAI,CAAC0B,SAAS,GAAG1H,MAAM,CAACgB,MAAM,CAAC,IAAI,CAACgF,KAAK,CAAC;MACtD;MAEA,IAAI2B,cAAc,GAAG;QACjBf,UAAU,EAAEgB,SAAS;QACrBrD,SAAS,EAAEqD,SAAS;QACpB5B,KAAK,EAAE4B,SAAS;QAChBhI,MAAM,EAAEgI,SAAS;QACjBC,SAAS,EAAED,SAAS;QACpBE,UAAU,EAAEF,SAAS;QACrBG,OAAO,EAAEH,SAAS;QAClBI,QAAQ,EAAEJ,SAAS,CAAM;MAC7B,CAAC;MAED,IAAIK,MAAM;MACV,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;QAC9BD,MAAM,GAAG,SAASE,WAAWA,CAACC,IAAI,EAAEtI,GAAG,EAAE;UACrC,IAAI,CAACsI,IAAI,EAAE;YACP,MAAM,IAAI7H,KAAK,CAAC,oBAAoB,IAAIT,GAAG,IAAI,KAAK,CAAC,CAAC;UAC1D;QACJ,CAAC;MACL,CAAC,MAAM;QACHmI,MAAM,GAAGC,MAAM;MACnB;MAEA,IAAI,CAACG,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;QAChD,OAAOV,cAAc;MACzB,CAAC;MASD,SAASW,sBAAsBA,CAACC,GAAG,EAAEC,GAAG,EAAE;QACtC,KAAK,IAAIC,CAAC,IAAID,GAAG,EAAE;UACf,IAAI,OAAOD,GAAG,CAACE,CAAC,CAAC,KAAK,WAAW,IAAIzI,MAAM,CAACe,SAAS,CAACJ,cAAc,CAAC+H,IAAI,CAACF,GAAG,EAAEC,CAAC,CAAC,EAAE;YAC/EF,GAAG,CAACE,CAAC,CAAC,GAAGD,GAAG,CAACC,CAAC,CAAC;UACnB;QACJ;MACJ;;MAEA;MACAH,sBAAsB,CAACX,cAAc,EAAE,IAAI,CAACpE,EAAE,CAAC;MAE/CoE,cAAc,CAAC3B,KAAK,GAAGA,KAAK;MAC5B2B,cAAc,CAAC/H,MAAM,GAAG,IAAI;;MAO5B;MACA,IAAI,OAAO+H,cAAc,CAACf,UAAU,KAAK,UAAU,EAAE;QACjD,IAAI,CAACA,UAAU,GAAG,SAAS+B,aAAaA,CAAC9B,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,EAAE;UAChE,IAAI,CAACA,cAAc,EAAE;YACjBA,cAAc,GAAG,IAAI,CAACjH,gBAAgB;UAC1C;UACA,OAAO8H,cAAc,CAACf,UAAU,CAAC8B,IAAI,CAAC,IAAI,EAAE7B,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,CAAC;QAC1E,CAAC;MACL,CAAC,MAAM;QACH,IAAI,CAACF,UAAU,GAAG,IAAI,CAAC5C,kBAAkB;MAC7C;;MAEA;MACA,IAAI,OAAO2D,cAAc,CAACpD,SAAS,KAAK,UAAU,EAAE;QAChD,IAAI,CAACA,SAAS,GAAG,SAASqE,YAAYA,CAACnE,MAAM,EAAE;UAC3C,OAAOkD,cAAc,CAACpD,SAAS,CAACmE,IAAI,CAAC,IAAI,EAAEjE,MAAM,CAAC;QACtD,CAAC;MACL,CAAC,MAAM;QACH,IAAI,CAACF,SAAS,GAAG,IAAI,CAACR,iBAAiB;MAC3C;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACE,iBAAiB,GAAG,SAAS4E,wBAAwBA,CAACC,WAAW,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAE;QACjH,IAAI5H,EAAE;QAEN,IAAI2H,mBAAmB,EAAE;UACrB,IAAIhJ,IAAI;UAER,IAAI4H,cAAc,CAACG,UAAU,IAAI,IAAI,CAACA,UAAU,EAAE;YAC9C;YACA;YACA/H,IAAI,GAAG,IAAI,CAACmE,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,qBAAqB,IAAI,EAAE,KAAK,CAAC;UACpG;UAEA,IAAIyD,cAAc,CAACG,UAAU,EAAE;YAC3B1G,EAAE,GAAGuG,cAAc,CAACG,UAAU,CAACY,IAAI,CAAC,IAAI,EAAEf,cAAc,EAAEmB,WAAW,EAAE/I,IAAI,CAAC;YAC5E,IAAI,OAAOqB,EAAE,KAAK,WAAW,EAAE0H,WAAW,GAAG1H,EAAE;UACnD;UACA,IAAI,IAAI,CAAC0G,UAAU,EAAE;YACjB1G,EAAE,GAAG,IAAI,CAAC0G,UAAU,CAACY,IAAI,CAAC,IAAI,EAAEf,cAAc,EAAEmB,WAAW,EAAE/I,IAAI,CAAC;YAClE,IAAI,OAAOqB,EAAE,KAAK,WAAW,EAAE0H,WAAW,GAAG1H,EAAE;UACnD;;UAEA;UACA,IAAIrB,IAAI,IAAIA,IAAI,CAACiH,OAAO,EAAE;YACtBjH,IAAI,CAACiH,OAAO,CAAC,CAAC;UAClB;QACJ;QAEA,IAAI,IAAI,CAAC5C,sBAAsB,GAAG,CAAC,EAAE,OAAO0E,WAAW,CAAC,CAAQ;;QAEhE;QACA,IAAI9C,KAAK,CAACiD,eAAe,EAAE;UACvBjD,KAAK,CAACiD,eAAe,CAACD,sBAAsB,CAAC;QACjD;;QAEA;QACA,IAAIrB,cAAc,EAAE;UAChBA,cAAc,CAAC3B,KAAK,GAAG4B,SAAS;UAChCD,cAAc,CAAC/H,MAAM,GAAGgI,SAAS;UACjC,IAAI5B,KAAK,CAACzC,EAAE,KAAKoE,cAAc,EAAE;YAC7B3B,KAAK,CAACzC,EAAE,GAAGqE,SAAS;UACxB;QACJ;QACAD,cAAc,GAAGC,SAAS;QAC1B,IAAI,CAAChB,UAAU,GAAG,IAAI,CAAC5C,kBAAkB;QACzC,IAAI,CAACO,SAAS,GAAG,IAAI,CAACR,iBAAiB;;QAEvC;QACA;QACArD,KAAK,CAACiB,MAAM,GAAG,CAAC,CAAC,CAAe;QAChC0F,MAAM,CAAC1F,MAAM,GAAG,CAAC;QAEjB2F,MAAM,CAAC3F,MAAM,GAAG,CAAC;QACjB4F,EAAE,GAAG,CAAC;;QAEN;QACA;QACA;QACA,IAAI,CAACyB,sBAAsB,EAAE;UACzB,KAAK,IAAIvH,CAAC,GAAG,IAAI,CAAC4C,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YACrD,IAAIyH,EAAE,GAAG,IAAI,CAAC7E,aAAa,CAAC5C,CAAC,CAAC;YAC9B,IAAIyH,EAAE,IAAI,OAAOA,EAAE,CAAClC,OAAO,KAAK,UAAU,EAAE;cACxCkC,EAAE,CAAClC,OAAO,CAAC,CAAC;YAChB;UACJ;UACA,IAAI,CAAC3C,aAAa,CAAC1C,MAAM,GAAG,CAAC;QAGjC;QAEA,OAAOmH,WAAW;MACtB,CAAC;;MAuID;MACA;MACA,IAAI,CAAC5E,uBAAuB,GAAG,SAASiF,8BAA8BA,CAACrJ,GAAG,EAAEsJ,EAAE,EAAEC,QAAQ,EAAEtC,WAAW,EAAE;QACnG,IAAIuC,GAAG,GAAG;UACNC,MAAM,EAAEzJ,GAAG;UACXQ,SAAS,EAAE8I,EAAE;UACbI,IAAI,EAAExD,KAAK,CAACyD,KAAK;UACjBrJ,KAAK,EAAE4F,KAAK,CAAC0D,MAAM;UACnBC,KAAK,EAAE,IAAI,CAAC9E,cAAc,CAACvC,MAAM,CAAC,IAAIA,MAAM;UAC5CsH,QAAQ,EAAEtH,MAAM;UAChBuH,IAAI,EAAE7D,KAAK,CAAC8D,QAAQ;UAEpBT,QAAQ,EAAEA,QAAQ;UAClBtC,WAAW,EAAEA,WAAW;UACxBrE,KAAK,EAAEA,KAAK;UACZqH,MAAM,EAAEA,MAAM;UACdC,SAAS,EAAEC,QAAQ;UACnBC,YAAY,EAAExJ,KAAK;UACnByJ,WAAW,EAAE9C,MAAM;UACnB+C,WAAW,EAAE9C,MAAM;UAEnB+C,aAAa,EAAE9C,EAAE;UACjBhE,EAAE,EAAEoE,cAAc;UAClB3B,KAAK,EAAEA,KAAK;UACZpG,MAAM,EAAE,IAAI;UAEZ;UACA;UACA;UACA;UACA;UACA;UACA;UACAoH,OAAO,EAAE,SAASsD,sBAAsBA,CAAA,EAAG;YACvC;YACA;YACA;YACA;YACA;YACA;YACA,IAAIC,GAAG,GAAG,CAAC,CAAC,IAAI,CAACxD,WAAW;YAC5B,KAAK,IAAInC,GAAG,IAAI,IAAI,EAAE;cAClB,IAAI,IAAI,CAACjE,cAAc,CAACiE,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;gBACrD,IAAI,CAACA,GAAG,CAAC,GAAGgD,SAAS;cACzB;YACJ;YACA,IAAI,CAACb,WAAW,GAAGwD,GAAG;UAC1B;QACJ,CAAC;QACD;QACA,IAAI,CAAClG,aAAa,CAACzC,IAAI,CAAC0H,GAAG,CAAC;QAC5B,OAAOA,GAAG;MACd,CAAC;MAcD,SAASkB,sBAAsBA,CAAClI,MAAM,EAAE;QACpC,IAAImI,SAAS,GAAGtD,IAAI,CAACzC,aAAa,CAACpC,MAAM,CAAC;QAC1C,IAAI,CAACmI,SAAS,EAAE;UACZA,SAAS,GAAGnI,MAAM;QACtB;QACA,OAAOmI,SAAS;MACpB;MAGA,SAASC,MAAMA,CAAA,EAAG;QACd,IAAIf,KAAK,GAAG3D,KAAK,CAAC2E,GAAG,CAAC,CAAC;QACvB;QACA,IAAI,OAAOhB,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAGxC,IAAI,CAACxD,QAAQ,CAACgG,KAAK,CAAC,IAAIA,KAAK;QACzC;QAEA,OAAOA,KAAK,IAAI7F,GAAG;MACvB;MAEA,SAAS8G,OAAOA,CAAA,EAAG;QACf,IAAIjB,KAAK,GAAG3D,KAAK,CAAC4E,OAAO,CAAC,CAAC;QAC3B;QACA,IAAI,OAAOjB,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAGxC,IAAI,CAACxD,QAAQ,CAACgG,KAAK,CAAC,IAAIA,KAAK;QACzC;QAEA,OAAOA,KAAK,IAAI7F,GAAG;MACvB;MAEA,IAAI6G,GAAG,GAAGD,MAAM;MAGhB,IAAIhI,KAAK,EAAEqH,MAAM,EAAExI,CAAC,EAAEgB,CAAC;MACvB,IAAIsI,KAAK,GAAG;QACR5E,CAAC,EAAE,IAAI;QACP6E,EAAE,EAAElD,SAAS;QACbrE,EAAE,EAAEoE;MACR,CAAC;MACD,IAAItG,CAAC;MACL,IAAI0J,SAAS;MACb,IAAIC,eAAe;MACnB,IAAIf,QAAQ;MACZ,IAAIgB,MAAM,GAAG,KAAK;MAGlB,IAAI;QACA,IAAI,CAAC7G,sBAAsB,EAAE;QAE7B4B,KAAK,CAACkF,QAAQ,CAAChE,KAAK,EAAES,cAAc,CAAC;;QAErC;QACA;QACA;QACA;QACA,IAAI,OAAO3B,KAAK,CAACmF,OAAO,KAAK,UAAU,EAAE;UACrC,IAAIC,SAAS,GAAGpF,KAAK,CAACmF,OAAO,CAAC,CAAC;UAC/B,IAAIC,SAAS,CAACR,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;YACpDD,GAAG,GAAGC,OAAO;UACjB;QACJ;QAIAtD,MAAM,CAACC,EAAE,CAAC,GAAG,IAAI;QACjBF,MAAM,CAACE,EAAE,CAAC,GAAG,CAAC;QACd7G,KAAK,CAAC6G,EAAE,CAAC,GAAG,CAAC;QACb,EAAEA,EAAE;QAMJ,IAAI,IAAI,CAACM,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAACa,IAAI,CAAC,IAAI,EAAEf,cAAc,CAAC;QAC7C;QACA,IAAIA,cAAc,CAACE,SAAS,EAAE;UAC1BF,cAAc,CAACE,SAAS,CAACa,IAAI,CAAC,IAAI,EAAEf,cAAc,CAAC;QACvD;QAEAsC,QAAQ,GAAG5C,MAAM,CAACE,EAAE,GAAG,CAAC,CAAC;QACzB,SAAS;UACL;UACA7E,KAAK,GAAGuH,QAAQ,CAAC,CAAe;;UAEhC;UACA,IAAI,IAAI,CAACtD,cAAc,CAACjE,KAAK,CAAC,EAAE;YAC5BqH,MAAM,GAAG,CAAC;YACVE,QAAQ,GAAG,IAAI,CAACtD,cAAc,CAACjE,KAAK,CAAC;UACzC,CAAC,MAAM;YACH;YACA;YACA;YACA;YACA,IAAI,CAACJ,MAAM,EAAE;cACTA,MAAM,GAAGqI,GAAG,CAAC,CAAC;YAClB;YACA;YACApI,CAAC,GAAIgD,KAAK,CAAC7C,KAAK,CAAC,IAAI6C,KAAK,CAAC7C,KAAK,CAAC,CAACJ,MAAM,CAAC,IAAKmF,SAAS;YACvDwC,QAAQ,GAAG1H,CAAC,CAAC,CAAC,CAAC;YACfwH,MAAM,GAAGxH,CAAC,CAAC,CAAC,CAAC;;YAYb;YACA,IAAI,CAACwH,MAAM,EAAE;cACT,IAAIR,MAAM;cACV,IAAI8B,cAAc,GAAI,IAAI,CAACxG,cAAc,CAACvC,MAAM,CAAC,IAAIA,MAAO;cAC5D,IAAI+G,QAAQ,GAAG,IAAI,CAACpE,0BAA0B,CAACvC,KAAK,CAAC;;cAErD;cACA,IAAI,OAAOsD,KAAK,CAAC8D,QAAQ,KAAK,QAAQ,EAAE;gBACpCP,MAAM,GAAG,sBAAsB,IAAIvD,KAAK,CAAC8D,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI;cACjE,CAAC,MAAM;gBACHP,MAAM,GAAG,eAAe;cAC5B;cACA,IAAI,OAAOvD,KAAK,CAACsF,YAAY,KAAK,UAAU,EAAE;gBAC1C/B,MAAM,IAAI,IAAI,GAAGvD,KAAK,CAACsF,YAAY,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI;cAC3D;cACA,IAAIjC,QAAQ,CAAC1H,MAAM,EAAE;gBACjB4H,MAAM,IAAI,YAAY,GAAGF,QAAQ,CAACkC,IAAI,CAAC,IAAI,CAAC,GAAG,mBAAmB,GAAGF,cAAc;cACvF,CAAC,MAAM;gBACH9B,MAAM,IAAI,aAAa,GAAG8B,cAAc;cAC5C;cACA;cACAhK,CAAC,GAAG,IAAI,CAAC6C,uBAAuB,CAACqF,MAAM,EAAE,IAAI,EAAEF,QAAQ,EAAE,KAAK,CAAC;cAC/D9H,CAAC,GAAG,IAAI,CAACqF,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACxB,gBAAgB,CAAC;cACvD,IAAI,OAAO0B,CAAC,KAAK,WAAW,EAAE;gBAC1B0J,MAAM,GAAG1J,CAAC;cACd;cACA;YACJ;UAGJ;UAWA,QAAQwI,MAAM;YACd;YACA;cACI;cACA,IAAIA,MAAM,YAAY3C,KAAK,EAAE;gBACzB/F,CAAC,GAAG,IAAI,CAAC6C,uBAAuB,CAAC,mDAAmD,GAAGxB,KAAK,GAAG,WAAW,GAAGJ,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;gBACvIf,CAAC,GAAG,IAAI,CAACqF,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACxB,gBAAgB,CAAC;gBACvD,IAAI,OAAO0B,CAAC,KAAK,WAAW,EAAE;kBAC1B0J,MAAM,GAAG1J,CAAC;gBACd;gBACA;cACJ;cACA;cACA;cACAF,CAAC,GAAG,IAAI,CAAC6C,uBAAuB,CAAC,6FAA6F,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;cAClJ3C,CAAC,GAAG,IAAI,CAACqF,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACxB,gBAAgB,CAAC;cACvD,IAAI,OAAO0B,CAAC,KAAK,WAAW,EAAE;gBAC1B0J,MAAM,GAAG1J,CAAC;cACd;cACA;;YAEJ;YACA,KAAK,CAAC;cACFb,KAAK,CAAC6G,EAAE,CAAC,GAAGjF,MAAM;cAClBgF,MAAM,CAACC,EAAE,CAAC,GAAGvB,KAAK,CAAC0D,MAAM;cAEzBrC,MAAM,CAACE,EAAE,CAAC,GAAG0C,QAAQ,CAAC,CAAC;;cAEvB,EAAE1C,EAAE;cACJjF,MAAM,GAAG,CAAC;;cAKV;;cAKA;;YAEJ;YACA,KAAK,CAAC;cAIF0I,eAAe,GAAG,IAAI,CAACxF,YAAY,CAACyE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAE;cACpDc,SAAS,GAAGC,eAAe,CAAC,CAAC,CAAC;cAW9BzJ,CAAC,GAAG,IAAI,CAACkE,aAAa,CAACiD,IAAI,CAACmC,KAAK,EAAEZ,QAAQ,EAAE1C,EAAE,GAAG,CAAC,EAAED,MAAM,CAAC;cAE5D,IAAI,OAAO/F,CAAC,KAAK,WAAW,EAAE;gBAC1B0J,MAAM,GAAG1J,CAAC;gBACV;cACJ;;cAEA;cACAgG,EAAE,IAAIwD,SAAS;;cAEf;cACA,IAAIS,QAAQ,GAAGR,eAAe,CAAC,CAAC,CAAC,CAAC,CAAI;cACtCtK,KAAK,CAAC6G,EAAE,CAAC,GAAGiE,QAAQ;cACpBlE,MAAM,CAACC,EAAE,CAAC,GAAGsD,KAAK,CAAC5E,CAAC;;cAEpB;cACAgE,QAAQ,GAAG1E,KAAK,CAAC8B,MAAM,CAACE,EAAE,GAAG,CAAC,CAAC,CAAC,CAACiE,QAAQ,CAAC;cAC1CnE,MAAM,CAACE,EAAE,CAAC,GAAG0C,QAAQ;cACrB,EAAE1C,EAAE;cAUJ;;YAEJ;YACA,KAAK,CAAC;cACF,IAAIA,EAAE,KAAK,CAAC,CAAC,EAAE;gBACX0D,MAAM,GAAG,IAAI;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA1D,EAAE,EAAE;gBACJ,IAAI,OAAOD,MAAM,CAACC,EAAE,CAAC,KAAK,WAAW,EAAE;kBACnC0D,MAAM,GAAG3D,MAAM,CAACC,EAAE,CAAC;gBACvB;cACJ;cACA;UACJ;;UAEA;UACA;QACJ;MACJ,CAAC,CAAC,OAAO6B,EAAE,EAAE;QACT;QACA;QACA,IAAIA,EAAE,YAAY,IAAI,CAACvJ,gBAAgB,EAAE;UACrC,MAAMuJ,EAAE;QACZ,CAAC,MACI,IAAIpD,KAAK,IAAI,OAAOA,KAAK,CAACyF,eAAe,KAAK,UAAU,IAAIrC,EAAE,YAAYpD,KAAK,CAACyF,eAAe,EAAE;UAClG,MAAMrC,EAAE;QACZ;QAEA/H,CAAC,GAAG,IAAI,CAAC6C,uBAAuB,CAAC,mCAAmC,EAAEkF,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC;QACtF6B,MAAM,GAAG,KAAK;QACd1J,CAAC,GAAG,IAAI,CAACqF,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACxB,gBAAgB,CAAC;QACvD,IAAI,OAAO0B,CAAC,KAAK,WAAW,EAAE;UAC1B0J,MAAM,GAAG1J,CAAC;QACd;MACJ,CAAC,SAAS;QACN0J,MAAM,GAAG,IAAI,CAAChH,iBAAiB,CAACgH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;QACnD,IAAI,CAAC7G,sBAAsB,EAAE;MACjC,CAAC,CAAG;;MAEJ,OAAO6G,MAAM;IACjB;EACA,CAAC;EACDrL,MAAM,CAACoE,kBAAkB,GAAGpE,MAAM,CAACgH,UAAU;EAC7ChH,MAAM,CAACmE,iBAAiB,GAAGnE,MAAM,CAAC2E,SAAS;EAC3C;;EAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGA,IAAIyB,KAAK,GAAG,YAAW;IACrB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASyF,eAAeA,CAAC3L,GAAG,EAAEC,IAAI,EAAE;MAClCC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;QAClCC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIN,GAAG,IAAI,IAAI,EACbA,GAAG,GAAG,KAAK;MAEbE,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;QACrCC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAEN;MACT,CAAC,CAAC;MAEF,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAIM,UAAU;MAEd,IAAIN,IAAI,IAAIA,IAAI,CAACO,SAAS,YAAYC,KAAK,EAAE;QAC3C,IAAIC,GAAG,GAAGT,IAAI,CAACO,SAAS;QACxB,IAAI,CAACG,OAAO,GAAGD,GAAG,CAACC,OAAO,IAAIX,GAAG;QACjCO,UAAU,GAAGG,GAAG,CAACE,KAAK;MACxB;MAEA,IAAI,CAACL,UAAU,EAAE;QACf,IAAIE,KAAK,CAACI,cAAc,CAAC,mBAAmB,CAAC,EAAE;UAC7C;UACAJ,KAAK,CAACK,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACC,WAAW,CAAC;QACjD,CAAC,MAAM;UACLR,UAAU,GAAG,IAAIE,KAAK,CAACT,GAAG,CAAC,CAACY,KAAK;QACnC;MACF;MAEA,IAAIL,UAAU,EAAE;QACdL,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;UACnCC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfC,KAAK,EAAEC;QACT,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,OAAOL,MAAM,CAACc,cAAc,KAAK,UAAU,EAAE;MAC/Cd,MAAM,CAACc,cAAc,CAAC2K,eAAe,CAAC1K,SAAS,EAAER,KAAK,CAACQ,SAAS,CAAC;IACnE,CAAC,MAAM;MACL0K,eAAe,CAAC1K,SAAS,GAAGf,MAAM,CAACgB,MAAM,CAACT,KAAK,CAACQ,SAAS,CAAC;IAC5D;IAEA0K,eAAe,CAAC1K,SAAS,CAACF,WAAW,GAAG4K,eAAe;IACvDA,eAAe,CAAC1K,SAAS,CAACE,IAAI,GAAG,iBAAiB;IAElD,IAAI+E,KAAK,GAAG;MAEd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEAlC,GAAG,EAAE,CAAC;MACF4H,KAAK,EAAE,CAAC;MAER;;MAEA;;MAEA;;MAEAC,kBAAkB,EAAE,IAAI;MAAoB;;MAE5CtH,aAAa,EAAE,EAAE;MAA2B;MAC5CuH,cAAc,EAAE,KAAK;MAAuB;MAC5CC,IAAI,EAAE,KAAK;MAAiC;MAC5CC,UAAU,EAAE,KAAK;MAA2B;MAC5CC,MAAM,EAAE,EAAE;MAAkC;MAC5CC,KAAK,EAAE,KAAK;MAAgC;MAC5CC,qBAAqB,EAAE,KAAK;MAAgB;MAC5CC,cAAc,EAAE,EAAE;MAA0B;MAC5CzC,KAAK,EAAE,EAAE;MAAmC;MAC5C0C,OAAO,EAAE,EAAE;MAAiC;MAC5CC,OAAO,EAAE,KAAK;MAA8B;MAC5C1C,MAAM,EAAE,EAAE;MAAkC;MAC5C2C,MAAM,EAAE,CAAC;MAAmC;MAC5CC,MAAM,EAAE,CAAC;MAAmC;MAC5CxC,QAAQ,EAAE,CAAC;MAAiC;MAC5CyC,MAAM,EAAE,IAAI;MAAgC;;MAE5C;AACJ;AACA;AACA;AACA;AACA;MACIC,qBAAqB,EAAE,SAASC,2BAA2BA,CAAC3M,GAAG,EAAEiH,WAAW,EAAE2F,mBAAmB,EAAE;QACjG5M,GAAG,GAAG,EAAE,GAAGA,GAAG;;QAEd;QACA;QACA,IAAI4M,mBAAmB,IAAI9E,SAAS,EAAE;UACpC8E,mBAAmB,GAAG,EAAE5M,GAAG,CAAC6M,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI7M,GAAG,CAAC6M,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxE;QAEA,IAAI,IAAI,CAACJ,MAAM,IAAIG,mBAAmB,EAAE;UACtC,IAAI,OAAO,IAAI,CAACE,gBAAgB,KAAK,UAAU,EAAE;YAC/C,IAAIC,UAAU,GAAG,IAAI,CAACD,gBAAgB,CAAC,IAAI,CAACL,MAAM,CAAC;YAEnD,IAAI,CAAC,QAAQ,CAACO,IAAI,CAAChN,GAAG,CAAC,EAAE;cACvBA,GAAG,IAAI,IAAI;YACb;YAEAA,GAAG,IAAI,uBAAuB,GAAG,IAAI,CAAC8M,gBAAgB,CAAC,IAAI,CAACL,MAAM,CAAC;UACrE,CAAC,MAAM,IAAI,OAAO,IAAI,CAACjB,YAAY,KAAK,UAAU,EAAE;YAClD,IAAIyB,OAAO,GAAG,IAAI,CAACzB,YAAY,CAAC,CAAC;YAEjC,IAAIyB,OAAO,EAAE;cACX,IAAIjN,GAAG,CAAC6B,MAAM,IAAI7B,GAAG,CAACA,GAAG,CAAC6B,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,IAAIoL,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBACrEjN,GAAG,IAAI,IAAI,GAAGiN,OAAO;cACvB,CAAC,MAAM;gBACLjN,GAAG,IAAIiN,OAAO;cAChB;YACF;UACF;QACF;;QAEA;QACA,IAAIzD,GAAG,GAAG;UACRC,MAAM,EAAEzJ,GAAG;UACXiH,WAAW,EAAE,CAAC,CAACA,WAAW;UAC1ByC,IAAI,EAAE,IAAI,CAACC,KAAK;UAAY;UAC5BE,KAAK,EAAE,IAAI;UACXE,IAAI,EAAE,IAAI,CAACC,QAAQ;UACnBkD,GAAG,EAAE,IAAI,CAACT,MAAM;UAChBhJ,EAAE,EAAE,IAAI,CAACA,EAAE;UACXyC,KAAK,EAAE,IAAI;UAEX;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACQgB,OAAO,EAAE,SAASiG,oBAAoBA,CAAA,EAAG;YACvC;YACA;YACA;YACA;YACA,IAAI1C,GAAG,GAAG,CAAC,CAAC,IAAI,CAACxD,WAAW;YAE5B,KAAK,IAAInC,GAAG,IAAI,IAAI,EAAE;cACpB,IAAI,IAAI,CAACjE,cAAc,CAACiE,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;gBACvD,IAAI,CAACA,GAAG,CAAC,GAAGgD,SAAS;cACvB;YACF;YAEA,IAAI,CAACb,WAAW,GAAGwD,GAAG;UACxB;QACF,CAAC;;QAED;QACA,IAAI,CAAClG,aAAa,CAACzC,IAAI,CAAC0H,GAAG,CAAC;QAE5B,OAAOA,GAAG;MACZ,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACI1C,UAAU,EAAE,SAASsG,gBAAgBA,CAACrG,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,EAAE;QAC/D,IAAI,CAACA,cAAc,EAAE;UACnBA,cAAc,GAAG,IAAI,CAAC2E,eAAe;QACvC;QAEA,IAAI,IAAI,CAAClI,EAAE,EAAE;UACX,IAAI,IAAI,CAACA,EAAE,CAAC3D,MAAM,IAAI,OAAO,IAAI,CAAC2D,EAAE,CAAC3D,MAAM,CAACgH,UAAU,KAAK,UAAU,EAAE;YACrE,OAAO,IAAI,CAACrD,EAAE,CAAC3D,MAAM,CAACgH,UAAU,CAAC8B,IAAI,CAAC,IAAI,EAAE7B,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,CAAC,IAAI,IAAI,CAAC4E,KAAK;UACtF,CAAC,MAAM,IAAI,OAAO,IAAI,CAACnI,EAAE,CAACqD,UAAU,KAAK,UAAU,EAAE;YACnD,OAAO,IAAI,CAACrD,EAAE,CAACqD,UAAU,CAAC8B,IAAI,CAAC,IAAI,EAAE7B,GAAG,EAAE9G,IAAI,EAAE+G,cAAc,CAAC,IAAI,IAAI,CAAC4E,KAAK;UAC/E;QACF;QAEA,MAAM,IAAI5E,cAAc,CAACD,GAAG,EAAE9G,IAAI,CAAC;MACrC,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIoN,OAAO,EAAE,SAASC,OAAOA,CAACvG,GAAG,CAAC,gBAAgB;QAC5C,IAAIwG,UAAU,GAAG,EAAE;QAEnB,IAAI,IAAI,CAACd,MAAM,EAAE;UACfc,UAAU,GAAG,WAAW,IAAI,IAAI,CAACvD,QAAQ,GAAG,CAAC,CAAC;QAChD;QAEA,IAAIzI,CAAC,GAAG,IAAI,CAACmL,qBAAqB,CAChC,eAAe,GAAGa,UAAU,GAAG,IAAI,GAAGxG,GAAG,EACzC,IAAI,CAACrD,OAAO,CAAC8J,yBACf,CAAC;;QAED;QACA,IAAIC,IAAI,GAAGnG,KAAK,CAACrG,SAAS,CAACyM,KAAK,CAAC9E,IAAI,CAAC+E,SAAS,EAAE,CAAC,CAAC;QAEnD,IAAIF,IAAI,CAAC5L,MAAM,EAAE;UACfN,CAAC,CAACqM,sBAAsB,GAAGH,IAAI;QACjC;QAEA,OAAO,IAAI,CAAC3G,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACoK,eAAe,CAAC,IAAI,IAAI,CAACC,KAAK;MACzE,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIzC,eAAe,EAAE,SAAS0E,qBAAqBA,CAAC3E,sBAAsB,EAAE;QACtE;QACA,IAAI,CAACkC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;QAErB;QACA;QACA;QACA,IAAI,CAAClC,sBAAsB,EAAE;UAC3B,KAAK,IAAIvH,CAAC,GAAG,IAAI,CAAC4C,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YACvD,IAAIyH,EAAE,GAAG,IAAI,CAAC7E,aAAa,CAAC5C,CAAC,CAAC;YAE9B,IAAIyH,EAAE,IAAI,OAAOA,EAAE,CAAClC,OAAO,KAAK,UAAU,EAAE;cAC1CkC,EAAE,CAAClC,OAAO,CAAC,CAAC;YACd;UACF;UAEA,IAAI,CAAC3C,aAAa,CAAC1C,MAAM,GAAG,CAAC;QAC/B;QAEA,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIiM,KAAK,EAAE,SAASC,WAAWA,CAAA,EAAG;QAC5B,IAAI,CAACnE,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC4C,MAAM,GAAG,CAAC;QACf,IAAI,CAAC7C,KAAK,GAAG,EAAE;;QAEf;QACA,IAAI,CAAC2C,OAAO,GAAG,KAAK;QAEpB,IAAI,CAACJ,KAAK,GAAG,KAAK;QAClB,IAAI,CAACF,UAAU,GAAG,KAAK;QACvB,IAAIgC,GAAG,GAAI,IAAI,CAACvB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACwB,WAAW,GAAG,CAAE;QAErD,IAAI,CAACxB,MAAM,GAAG;UACZyB,UAAU,EAAE,IAAI,CAAClE,QAAQ,GAAG,CAAC;UAC7BmE,YAAY,EAAEH,GAAG;UACjBI,SAAS,EAAE,IAAI,CAACpE,QAAQ,GAAG,CAAC;UAC5BiE,WAAW,EAAED,GAAG;UAChBK,KAAK,EAAE,CAAC,IAAI,CAAC9B,MAAM,EAAE,IAAI,CAACA,MAAM;QAClC,CAAC;MACH,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACInB,QAAQ,EAAE,SAASkD,cAAcA,CAAClH,KAAK,EAAE3D,EAAE,EAAE;QAC3C,IAAI,CAACA,EAAE,GAAGA,EAAE,IAAI,IAAI,CAACA,EAAE,IAAI,CAAC,CAAC;;QAE7B;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAACqI,cAAc,EAAE;UACxB;UACA,IAAIyC,KAAK,GAAG,IAAI,CAACA,KAAK;UAEtB,KAAK,IAAI5M,CAAC,GAAG,CAAC,EAAEW,GAAG,GAAGiM,KAAK,CAAC1M,MAAM,EAAEF,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;YAChD,IAAI6M,OAAO,GAAGD,KAAK,CAAC5M,CAAC,CAAC;;YAEtB;YACA,IAAI,OAAO6M,OAAO,KAAK,QAAQ,EAAE;cAC/BD,KAAK,CAAC5M,CAAC,CAAC,GAAG4M,KAAK,CAACC,OAAO,CAAC;YAC3B;UACF;;UAEA;UACA,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;UAEhC,KAAK,IAAI9F,CAAC,IAAI8F,UAAU,EAAE;YACxB,IAAIC,IAAI,GAAGD,UAAU,CAAC9F,CAAC,CAAC;YACxB,IAAIgG,QAAQ,GAAGD,IAAI,CAACH,KAAK;YACzB,IAAIjM,GAAG,GAAGqM,QAAQ,CAAC9M,MAAM;YACzB,IAAI+M,YAAY,GAAG,IAAItH,KAAK,CAAChF,GAAG,GAAG,CAAC,CAAC,CAAC,CAAa;YACnD,IAAIuM,YAAY,GAAG,IAAIvH,KAAK,CAAChF,GAAG,GAAG,CAAC,CAAC;YAErC,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,GAAG,EAAEX,CAAC,EAAE,EAAE;cAC5B,IAAIM,GAAG,GAAG0M,QAAQ,CAAChN,CAAC,CAAC;cACrB,IAAI6M,OAAO,GAAGD,KAAK,CAACtM,GAAG,CAAC;cACxB2M,YAAY,CAACjN,CAAC,GAAG,CAAC,CAAC,GAAG6M,OAAO;cAC7BK,YAAY,CAAClN,CAAC,GAAG,CAAC,CAAC,GAAGM,GAAG;YAC3B;YAEAyM,IAAI,CAACH,KAAK,GAAGM,YAAY;YACzBH,IAAI,CAACI,cAAc,GAAGF,YAAY;YAClCF,IAAI,CAACK,YAAY,GAAGzM,GAAG;UACzB;UAEA,IAAI,CAACwJ,cAAc,GAAG,IAAI;QAC5B;QAEA,IAAI,CAACG,MAAM,GAAG7E,KAAK,IAAI,EAAE;QACzB,IAAI,CAAC0G,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC3B,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACJ,IAAI,GAAG,KAAK;QACjB,IAAI,CAAC/B,QAAQ,GAAG,CAAC;QACjB,IAAI,CAACqC,OAAO,GAAG,EAAE;QACjB,IAAI,CAACD,cAAc,GAAG,CAAC,SAAS,CAAC;QACjC,IAAI,CAACP,kBAAkB,GAAG,IAAI;QAE9B,IAAI,CAACY,MAAM,GAAG;UACZyB,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZH,WAAW,EAAE,CAAC;UACdI,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;QACd,CAAC;QAED,IAAI,CAAC9B,MAAM,GAAG,CAAC;QACf,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIyC,kBAAkB,EAAE,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;QACtE,IAAI7N,EAAE,GAAG4N,QAAQ,CAACtG,IAAI,CAAC,IAAI,EAAE,IAAI,CAACqD,MAAM,EAAEkD,MAAM,CAAC;QAEjD,IAAI,OAAO7N,EAAE,KAAK,QAAQ,EAAE;UAC1B,IAAIA,EAAE,EAAE;YACN,IAAI,CAAC2K,MAAM,GAAG,EAAE,GAAG3K,EAAE;UACvB;UACA;QACF,CAAC,MAAM;UACL,IAAI,CAAC2K,MAAM,GAAG3K,EAAE;QAClB;QAEA,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACI8F,KAAK,EAAE,SAASgI,WAAWA,CAAA,EAAG;QAC5B,IAAI,CAAC,IAAI,CAACnD,MAAM,EAAE;UAChB;UACA,OAAO,IAAI;QACb;QAEA,IAAIoD,EAAE,GAAG,IAAI,CAACpD,MAAM,CAAC,CAAC,CAAC;QACvB,IAAI,CAACrC,MAAM,IAAIyF,EAAE;QACjB,IAAI,CAAC7C,MAAM,EAAE;QACb,IAAI,CAACD,MAAM,EAAE;QACb,IAAI,CAAC5C,KAAK,IAAI0F,EAAE;QAChB,IAAI,CAAChD,OAAO,IAAIgD,EAAE;;QAElB;QACA;QACA;QACA;QACA,IAAIC,SAAS,GAAG,CAAC;QAEjB,IAAIC,KAAK,GAAG,KAAK;QAEjB,IAAIF,EAAE,KAAK,IAAI,EAAE;UACfE,KAAK,GAAG,IAAI;QACd,CAAC,MAAM,IAAIF,EAAE,KAAK,IAAI,EAAE;UACtBE,KAAK,GAAG,IAAI;UACZ,IAAIC,GAAG,GAAG,IAAI,CAACvD,MAAM,CAAC,CAAC,CAAC;UAExB,IAAIuD,GAAG,KAAK,IAAI,EAAE;YAChBF,SAAS,EAAE;YACXD,EAAE,IAAIG,GAAG;YACT,IAAI,CAAC5F,MAAM,IAAI4F,GAAG;YAClB,IAAI,CAAChD,MAAM,EAAE;YACb,IAAI,CAACD,MAAM,EAAE;YACb,IAAI,CAAC5C,KAAK,IAAI6F,GAAG;YACjB,IAAI,CAACnD,OAAO,IAAImD,GAAG;YACnB,IAAI,CAAC/C,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,EAAE;UACxB;QACF;QAEA,IAAIkB,KAAK,EAAE;UACT,IAAI,CAACvF,QAAQ,EAAE;UACf,IAAI,CAACyC,MAAM,CAAC2B,SAAS,EAAE;UACvB,IAAI,CAAC3B,MAAM,CAACwB,WAAW,GAAG,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACxB,MAAM,CAACwB,WAAW,EAAE;QAC3B;QAEA,IAAI,CAACxB,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,EAAE;QACtB,IAAI,CAACpC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACyB,KAAK,CAAC4B,SAAS,CAAC;QAC1C,OAAOD,EAAE;MACX,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACII,KAAK,EAAE,SAASC,WAAWA,CAACL,EAAE,EAAE;QAC9B,IAAI/M,GAAG,GAAG+M,EAAE,CAACxN,MAAM;QACnB,IAAI0N,KAAK,GAAGF,EAAE,CAACM,KAAK,CAAC,eAAe,CAAC;QACrC,IAAI,CAAC1D,MAAM,GAAGoD,EAAE,GAAG,IAAI,CAACpD,MAAM;QAC9B,IAAI,CAACrC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACgG,MAAM,CAAC,CAAC,EAAE,IAAI,CAAChG,MAAM,CAAC/H,MAAM,GAAGS,GAAG,CAAC;QAC7D,IAAI,CAACkK,MAAM,GAAG,IAAI,CAAC5C,MAAM,CAAC/H,MAAM;QAChC,IAAI,CAAC0K,MAAM,IAAIjK,GAAG;QAClB,IAAI,CAACqH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACiG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACjG,KAAK,CAAC9H,MAAM,GAAGS,GAAG,CAAC;QAC1D,IAAI,CAAC+J,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuD,MAAM,CAAC,CAAC,EAAE,IAAI,CAACvD,OAAO,CAACxK,MAAM,GAAGS,GAAG,CAAC;QAEhE,IAAIiN,KAAK,CAAC1N,MAAM,GAAG,CAAC,EAAE;UACpB,IAAI,CAACmI,QAAQ,IAAIuF,KAAK,CAAC1N,MAAM,GAAG,CAAC;UACjC,IAAI,CAAC4K,MAAM,CAAC2B,SAAS,GAAG,IAAI,CAACpE,QAAQ,GAAG,CAAC;;UAEzC;UACA;UACA;UACA,IAAI6F,GAAG,GAAG,IAAI,CAAClG,KAAK;UAEpB,IAAImG,SAAS,GAAGD,GAAG,CAACF,KAAK,CAAC,eAAe,CAAC;UAE1C,IAAIG,SAAS,CAACjO,MAAM,KAAK,CAAC,EAAE;YAC1BgO,GAAG,GAAG,IAAI,CAACxD,OAAO;YAClByD,SAAS,GAAGD,GAAG,CAACF,KAAK,CAAC,eAAe,CAAC;UACxC;UAEA,IAAI,CAAClD,MAAM,CAACwB,WAAW,GAAG6B,SAAS,CAACA,SAAS,CAACjO,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;QAClE,CAAC,MAAM;UACL,IAAI,CAAC4K,MAAM,CAACwB,WAAW,IAAI3L,GAAG;QAChC;QAEA,IAAI,CAACmK,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC7B,MAAM;QACzD,IAAI,CAACT,IAAI,GAAG,KAAK;QACjB,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIgE,IAAI,EAAE,SAASC,UAAUA,CAAA,EAAG;QAC1B,IAAI,CAAC9D,KAAK,GAAG,IAAI;QACjB,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACI+D,MAAM,EAAE,SAASC,YAAYA,CAAA,EAAG;QAC9B,IAAI,IAAI,CAACxM,OAAO,CAACyM,eAAe,EAAE;UAChC,IAAI,CAACnE,UAAU,GAAG,IAAI;QACxB,CAAC,MAAM;UACL;UACA;UACA;UACA,IAAIuB,UAAU,GAAG,EAAE;UAEnB,IAAI,IAAI,CAACd,MAAM,EAAE;YACfc,UAAU,GAAG,WAAW,IAAI,IAAI,CAACvD,QAAQ,GAAG,CAAC,CAAC;UAChD;UAEA,IAAIzI,CAAC,GAAG,IAAI,CAACmL,qBAAqB,CAChC,eAAe,GAAGa,UAAU,GAAG,gIAAgI,EAC/J,KACF,CAAC;UAED,IAAI,CAACpB,qBAAqB,GAAG,IAAI,CAACrF,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACoK,eAAe,CAAC,IAAI,IAAI,CAACC,KAAK;QAC/F;QAEA,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIwE,IAAI,EAAE,SAASC,UAAUA,CAACtN,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC0M,KAAK,CAAC,IAAI,CAAC9F,KAAK,CAAC+D,KAAK,CAAC3K,CAAC,CAAC,CAAC;MACxC,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIuN,SAAS,EAAE,SAASC,eAAeA,CAACC,OAAO,EAAEC,QAAQ,EAAE;QACrD,IAAIC,IAAI,GAAG,IAAI,CAACrE,OAAO,CAACsE,SAAS,CAAC,CAAC,EAAE,IAAI,CAACtE,OAAO,CAACxK,MAAM,GAAG,IAAI,CAAC8H,KAAK,CAAC9H,MAAM,CAAC;QAE7E,IAAI2O,OAAO,GAAG,CAAC,EACbA,OAAO,GAAGE,IAAI,CAAC7O,MAAM,CAAC,KACnB,IAAI,CAAC2O,OAAO,EACfA,OAAO,GAAG,EAAE;QAEd,IAAIC,QAAQ,GAAG,CAAC,EACdA,QAAQ,GAAGC,IAAI,CAAC7O,MAAM,CAAC,CAAU;QAAA,KAC9B,IAAI,CAAC4O,QAAQ,EAChBA,QAAQ,GAAG,CAAC;;QAEd;QACA;QACA;QACAC,IAAI,GAAGA,IAAI,CAACd,MAAM,CAAC,CAACY,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;;QAEpC;QACA;QACA,IAAI7N,CAAC,GAAG+N,IAAI,CAACE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAACjB,KAAK,CAAC,IAAI,CAAC;QAElDhN,CAAC,GAAGA,CAAC,CAAC+K,KAAK,CAAC,CAAC+C,QAAQ,CAAC;QACtBC,IAAI,GAAG/N,CAAC,CAAC8I,IAAI,CAAC,IAAI,CAAC;;QAEnB;QACA;QACA,IAAIiF,IAAI,CAAC7O,MAAM,GAAG2O,OAAO,EAAE;UACzBE,IAAI,GAAG,KAAK,GAAGA,IAAI,CAACd,MAAM,CAAC,CAACY,OAAO,CAAC;QACtC;QAEA,OAAOE,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIG,aAAa,EAAE,SAASC,mBAAmBA,CAACN,OAAO,EAAEC,QAAQ,EAAE;QAC7D,IAAIM,IAAI,GAAG,IAAI,CAACpH,KAAK;QAErB,IAAI6G,OAAO,GAAG,CAAC,EACbA,OAAO,GAAGO,IAAI,CAAClP,MAAM,GAAG,IAAI,CAACoK,MAAM,CAACpK,MAAM,CAAC,KACxC,IAAI,CAAC2O,OAAO,EACfA,OAAO,GAAG,EAAE;QAEd,IAAIC,QAAQ,GAAG,CAAC,EACdA,QAAQ,GAAGD,OAAO,CAAC,CAAU;QAAA,KAC1B,IAAI,CAACC,QAAQ,EAChBA,QAAQ,GAAG,CAAC;;QAEd;QACA;QACA;QACA,IAAIM,IAAI,CAAClP,MAAM,GAAG2O,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;UACjCO,IAAI,IAAI,IAAI,CAAC9E,MAAM,CAAC0E,SAAS,CAAC,CAAC,EAAEH,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG;QACvD;;QAEA;QACA;QACA,IAAI7N,CAAC,GAAGoO,IAAI,CAACH,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAACjB,KAAK,CAAC,IAAI,CAAC;QAElDhN,CAAC,GAAGA,CAAC,CAAC+K,KAAK,CAAC,CAAC,EAAE+C,QAAQ,CAAC;QACxBM,IAAI,GAAGpO,CAAC,CAAC8I,IAAI,CAAC,IAAI,CAAC;;QAEnB;QACA;QACA,IAAIsF,IAAI,CAAClP,MAAM,GAAG2O,OAAO,EAAE;UACzBO,IAAI,GAAGA,IAAI,CAACJ,SAAS,CAAC,CAAC,EAAEH,OAAO,CAAC,GAAG,KAAK;QAC3C;QAEA,OAAOO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACIvF,YAAY,EAAE,SAASwF,kBAAkBA,CAACC,SAAS,EAAEC,UAAU,EAAE;QAC/D,IAAIrB,GAAG,GAAG,IAAI,CAACS,SAAS,CAACW,SAAS,CAAC,CAACL,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QACvD,IAAIzN,CAAC,GAAG,IAAImE,KAAK,CAACuI,GAAG,CAAChO,MAAM,GAAG,CAAC,CAAC,CAAC4J,IAAI,CAAC,GAAG,CAAC;QAC3C,OAAOoE,GAAG,GAAG,IAAI,CAACgB,aAAa,CAACK,UAAU,CAAC,CAACN,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI,GAAGzN,CAAC,GAAG,GAAG;MAClF,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIgO,kBAAkB,EAAE,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAE;QACrF,IAAItE,GAAG,GAAG;UACRgB,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZH,WAAW,EAAE,CAAC;UACdI,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;QACd,CAAC;QAED,IAAIgD,MAAM,EAAE;UACVnE,GAAG,CAACgB,UAAU,GAAGmD,MAAM,CAACnD,UAAU,GAAG,CAAC;UACtChB,GAAG,CAACkB,SAAS,GAAGiD,MAAM,CAACjD,SAAS,GAAG,CAAC;UACpClB,GAAG,CAACiB,YAAY,GAAGkD,MAAM,CAAClD,YAAY,GAAG,CAAC;UAC1CjB,GAAG,CAACe,WAAW,GAAGoD,MAAM,CAACpD,WAAW,GAAG,CAAC;UAExC,IAAIoD,MAAM,CAAChD,KAAK,EAAE;YAChBnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGgD,MAAM,CAAChD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YAClCnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGgD,MAAM,CAAChD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;UACpC;QACF;QAEA,IAAInB,GAAG,CAACgB,UAAU,IAAI,CAAC,IAAIhB,GAAG,CAACkB,SAAS,GAAGlB,GAAG,CAACgB,UAAU,EAAE;UACzD;UACA,IAAIhB,GAAG,CAACgB,UAAU,IAAI,CAAC,IAAIoD,SAAS,EAAE;YACpCpE,GAAG,CAACgB,UAAU,GAAGoD,SAAS,CAAClD,SAAS,GAAG,CAAC;YACxClB,GAAG,CAACiB,YAAY,GAAGmD,SAAS,CAACrD,WAAW,GAAG,CAAC;YAE5C,IAAIqD,SAAS,CAACjD,KAAK,EAAE;cACnBnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGgD,MAAM,CAAChD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YACpC;UACF;UAEA,IAAI,CAACnB,GAAG,CAACkB,SAAS,IAAI,CAAC,IAAIlB,GAAG,CAACkB,SAAS,GAAGlB,GAAG,CAACgB,UAAU,KAAKqD,SAAS,EAAE;YACvErE,GAAG,CAACkB,SAAS,GAAGmD,SAAS,CAACrD,UAAU,GAAG,CAAC;YACxChB,GAAG,CAACe,WAAW,GAAGsD,SAAS,CAACpD,YAAY,GAAG,CAAC;YAE5C,IAAIoD,SAAS,CAAClD,KAAK,EAAE;cACnBnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGgD,MAAM,CAAChD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YACpC;UACF;;UAEA;UACA,IAAInB,GAAG,CAACgB,UAAU,IAAI,CAAC,IAAIsD,OAAO,KAAKtE,GAAG,CAACkB,SAAS,IAAI,CAAC,IAAIoD,OAAO,CAACpD,SAAS,IAAIlB,GAAG,CAACkB,SAAS,CAAC,EAAE;YAChGlB,GAAG,CAACgB,UAAU,GAAGsD,OAAO,CAACtD,UAAU,GAAG,CAAC;YACvChB,GAAG,CAACiB,YAAY,GAAGqD,OAAO,CAACrD,YAAY,GAAG,CAAC;YAE3C,IAAIqD,OAAO,CAACnD,KAAK,EAAE;cACjBnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGmD,OAAO,CAACnD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YACrC;UACF;UAEA,IAAInB,GAAG,CAACkB,SAAS,IAAI,CAAC,IAAIoD,OAAO,KAAKtE,GAAG,CAACgB,UAAU,IAAI,CAAC,IAAIsD,OAAO,CAACtD,UAAU,IAAIhB,GAAG,CAACgB,UAAU,CAAC,EAAE;YAClGhB,GAAG,CAACkB,SAAS,GAAGoD,OAAO,CAACpD,SAAS,GAAG,CAAC;YACrClB,GAAG,CAACe,WAAW,GAAGuD,OAAO,CAACvD,WAAW,GAAG,CAAC;YAEzC,IAAIuD,OAAO,CAACnD,KAAK,EAAE;cACjBnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGmD,OAAO,CAACnD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YACrC;UACF;QACF;;QAEA;QACA;QACA,IAAInB,GAAG,CAACkB,SAAS,IAAI,CAAC,EAAE;UACtB,IAAIlB,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;YACvBhB,GAAG,CAACgB,UAAU,GAAG,IAAI,CAACzB,MAAM,CAACyB,UAAU;YACvChB,GAAG,CAACkB,SAAS,GAAG,IAAI,CAAC3B,MAAM,CAAC2B,SAAS;YACrClB,GAAG,CAACiB,YAAY,GAAG,IAAI,CAAC1B,MAAM,CAAC0B,YAAY;YAC3CjB,GAAG,CAACe,WAAW,GAAG,IAAI,CAACxB,MAAM,CAACwB,WAAW;YACzCf,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC;YACnCnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC;UACrC,CAAC,MAAM;YACLnB,GAAG,CAACkB,SAAS,GAAG,IAAI,CAAC3B,MAAM,CAAC2B,SAAS;YACrClB,GAAG,CAACe,WAAW,GAAG,IAAI,CAACxB,MAAM,CAACwB,WAAW;YACzCf,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC;UACrC;QACF;QAEA,IAAInB,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;UACvBhB,GAAG,CAACgB,UAAU,GAAGhB,GAAG,CAACkB,SAAS;UAC9BlB,GAAG,CAACiB,YAAY,GAAG,CAAC,CAAC,CAAE;UACvBjB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC,GAAGnB,GAAG,CAACmB,KAAK,CAAC,CAAC,CAAC;QAC7B;QAEA,IAAInB,GAAG,CAACiB,YAAY,GAAG,CAAC,EAAE;UACxBjB,GAAG,CAACiB,YAAY,GAAG,CAAC;QACtB;QAEA,IAAIjB,GAAG,CAACe,WAAW,GAAG,CAAC,EAAE;UACvBf,GAAG,CAACe,WAAW,GAAIf,GAAG,CAACiB,YAAY,GAAG,CAAC,GAAGjB,GAAG,CAACiB,YAAY,GAAG,EAAG;QAClE;QAEA,OAAOjB,GAAG;MACZ,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIJ,gBAAgB,EAAE,SAAS2E,sBAAsBA,CAACvE,GAAG,EAAEwE,WAAW,EAAEC,YAAY,EAAE;QAChFzE,GAAG,GAAG,IAAI,CAACiE,kBAAkB,CAACjE,GAAG,EAAEwE,WAAW,EAAEC,YAAY,CAAC;QAC7D,MAAMC,OAAO,GAAG,CAAC;QACjB,MAAMC,YAAY,GAAG,CAAC;QACtB,MAAMC,mCAAmC,GAAG,CAAC;QAC7C,IAAI1K,KAAK,GAAG,IAAI,CAACiF,OAAO,GAAG,IAAI,CAACJ,MAAM;QACtC,IAAIsD,KAAK,GAAGnI,KAAK,CAACuI,KAAK,CAAC,IAAI,CAAC;QAC7B,IAAIoC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGP,WAAW,GAAGA,WAAW,CAACxD,UAAU,GAAGhB,GAAG,CAACgB,UAAU,GAAG0D,OAAQ,CAAC;QACvF,IAAIM,EAAE,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGN,YAAY,GAAGA,YAAY,CAACvD,SAAS,GAAGlB,GAAG,CAACkB,SAAS,GAAGyD,YAAa,CAAC;QAC5F,IAAIM,oBAAoB,GAAG,CAAC,GAAGH,IAAI,CAACI,KAAK,CAACF,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACrD,IAAIG,SAAS,GAAG,IAAI/K,KAAK,CAAC6K,oBAAoB,CAAC,CAAC1G,IAAI,CAAC,GAAG,CAAC;QACzD,IAAI6G,qBAAqB,GAAG,EAAE;QAE9B,IAAIhR,EAAE,GAAGiO,KAAK,CAAC7B,KAAK,CAACqE,EAAE,GAAG,CAAC,EAAEG,EAAE,GAAG,CAAC,CAAC,CAACK,GAAG,CAAC,SAASC,gBAAgBA,CAACzI,IAAI,EAAE0I,KAAK,EAAE;UAC9E,IAAIC,GAAG,GAAGD,KAAK,GAAGV,EAAE;UACpB,IAAIY,OAAO,GAAG,CAACN,SAAS,GAAGK,GAAG,EAAE9C,MAAM,CAAC,CAACuC,oBAAoB,CAAC;UAC7D,IAAI7Q,EAAE,GAAGqR,OAAO,GAAG,IAAI,GAAG5I,IAAI;UAC9B,IAAI6I,MAAM,GAAG,IAAItL,KAAK,CAAC6K,oBAAoB,GAAG,CAAC,CAAC,CAAC1G,IAAI,CAAC,GAAG,CAAC;UAC1D,IAAIc,MAAM,GAAG,CAAC,GAAG,CAAC;UAClB,IAAIjK,GAAG,GAAG,CAAC;UAEX,IAAIoQ,GAAG,KAAKxF,GAAG,CAACgB,UAAU,EAAE;YAC1B3B,MAAM,IAAIW,GAAG,CAACiB,YAAY;YAE1B7L,GAAG,GAAG0P,IAAI,CAACC,GAAG,CACZ,CAAC,EACD,CAAES,GAAG,KAAKxF,GAAG,CAACkB,SAAS,GAAGlB,GAAG,CAACe,WAAW,GAAGlE,IAAI,CAAClI,MAAM,IAAKqL,GAAG,CAACiB,YAAY,GAAG,CACjF,CAAC;UACH,CAAC,MAAM,IAAIuE,GAAG,KAAKxF,GAAG,CAACkB,SAAS,EAAE;YAChC9L,GAAG,GAAG0P,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/E,GAAG,CAACe,WAAW,GAAG,CAAC,CAAC;UACxC,CAAC,MAAM,IAAIyE,GAAG,GAAGxF,GAAG,CAACgB,UAAU,IAAIwE,GAAG,GAAGxF,GAAG,CAACkB,SAAS,EAAE;YACtD9L,GAAG,GAAG0P,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElI,IAAI,CAAClI,MAAM,GAAG,CAAC,CAAC;UACpC;UAEA,IAAIS,GAAG,EAAE;YACP,IAAIuQ,IAAI,GAAG,IAAIvL,KAAK,CAACiF,MAAM,CAAC,CAACd,IAAI,CAAC,GAAG,CAAC;YACtC,IAAIqH,IAAI,GAAG,IAAIxL,KAAK,CAAChF,GAAG,CAAC,CAACmJ,IAAI,CAAC,GAAG,CAAC;YACnCnK,EAAE,IAAI,IAAI,GAAGsR,MAAM,GAAGC,IAAI,GAAGC,IAAI;YAEjC,IAAI/I,IAAI,CAACgJ,IAAI,CAAC,CAAC,CAAClR,MAAM,GAAG,CAAC,EAAE;cAC1ByQ,qBAAqB,CAACxQ,IAAI,CAAC2Q,KAAK,CAAC;YACnC;UACF;UAEAnR,EAAE,GAAGA,EAAE,CAACsP,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;UAC3B,OAAOtP,EAAE;QACX,CAAC,CAAC;;QAEF;QACA;QACA,IAAIgR,qBAAqB,CAACzQ,MAAM,GAAG,CAAC,GAAGiQ,mCAAmC,EAAE;UAC1E,IAAIkB,UAAU,GAAGV,qBAAqB,CAACR,mCAAmC,GAAG,CAAC,CAAC,GAAG,CAAC;UACnF,IAAImB,QAAQ,GAAGX,qBAAqB,CAACA,qBAAqB,CAACzQ,MAAM,GAAGiQ,mCAAmC,CAAC,GAAG,CAAC;UAC5G,IAAIoB,iBAAiB,GAAG,IAAI5L,KAAK,CAAC6K,oBAAoB,GAAG,CAAC,CAAC,CAAC1G,IAAI,CAAC,GAAG,CAAC,GAAG,qBAAqB;UAC7FyH,iBAAiB,IAAI,IAAI,GAAG,IAAI5L,KAAK,CAAC6K,oBAAoB,GAAG,CAAC,CAAC,CAAC1G,IAAI,CAAC,GAAG,CAAC,GAAG,qBAAqB;UACjGnK,EAAE,CAAC6R,MAAM,CAACH,UAAU,EAAEC,QAAQ,GAAGD,UAAU,GAAG,CAAC,EAAEE,iBAAiB,CAAC;QACrE;QAEA,OAAO5R,EAAE,CAACmK,IAAI,CAAC,IAAI,CAAC;MACtB,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACI2H,cAAc,EAAE,SAASC,qBAAqBA,CAAC5G,MAAM,EAAE6G,iBAAiB,EAAE;QACxE,IAAIpB,EAAE,GAAGzF,MAAM,CAACyB,UAAU;QAC1B,IAAIqF,EAAE,GAAG9G,MAAM,CAAC2B,SAAS;QACzB,IAAIoF,EAAE,GAAG/G,MAAM,CAAC0B,YAAY;QAC5B,IAAIsF,EAAE,GAAGhH,MAAM,CAACwB,WAAW;QAC3B,IAAIyF,EAAE,GAAGH,EAAE,GAAGrB,EAAE;QAChB,IAAIyB,EAAE,GAAGF,EAAE,GAAGD,EAAE;QAChB,IAAIlS,EAAE;QAEN,IAAIoS,EAAE,KAAK,CAAC,EAAE;UACZpS,EAAE,GAAG,OAAO,GAAG4Q,EAAE,GAAG,IAAI;UAExB,IAAIyB,EAAE,IAAI,CAAC,EAAE;YACXrS,EAAE,IAAI,SAAS,GAAGkS,EAAE;UACtB,CAAC,MAAM;YACLlS,EAAE,IAAI,UAAU,GAAGkS,EAAE,GAAG,MAAM,GAAGC,EAAE;UACrC;QACF,CAAC,MAAM;UACLnS,EAAE,GAAG,QAAQ,GAAG4Q,EAAE,GAAG,UAAU,GAAGsB,EAAE,GAAG,OAAO,GAAGD,EAAE,GAAG,UAAU,GAAGE,EAAE,GAAG,GAAG;QAC7E;QAEA,IAAIhH,MAAM,CAAC4B,KAAK,IAAIiF,iBAAiB,EAAE;UACrC,IAAIM,EAAE,GAAGnH,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC;UACxB,IAAIwF,EAAE,GAAGpH,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;UAE5B,IAAIwF,EAAE,IAAID,EAAE,EAAE;YACZtS,EAAE,IAAI,mBAAmB,GAAGsS,EAAE,GAAG,GAAG;UACtC,CAAC,MAAM;YACLtS,EAAE,IAAI,yBAAyB,GAAGsS,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,GAAG;UAC1D;QACF;QAEA,OAAOvS,EAAE;MACX,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACIwS,UAAU,EAAE,SAASC,gBAAgBA,CAACpK,KAAK,EAAEqK,YAAY,EAAE;QACzD,IAAInK,KAAK,EAAE0F,KAAK,EAAE0E,MAAM,EAAEC,SAAS,EAAEC,aAAa;QAElD,IAAI,IAAI,CAACzQ,OAAO,CAACyM,eAAe,EAAE;UAChC;UACA8D,MAAM,GAAG;YACPjK,QAAQ,EAAE,IAAI,CAACA,QAAQ;YAEvByC,MAAM,EAAE;cACNyB,UAAU,EAAE,IAAI,CAACzB,MAAM,CAACyB,UAAU;cAClCE,SAAS,EAAE,IAAI,CAAC3B,MAAM,CAAC2B,SAAS;cAChCD,YAAY,EAAE,IAAI,CAAC1B,MAAM,CAAC0B,YAAY;cACtCF,WAAW,EAAE,IAAI,CAACxB,MAAM,CAACwB,WAAW;cACpCI,KAAK,EAAE,IAAI,CAAC5B,MAAM,CAAC4B,KAAK,CAACX,KAAK,CAAC,CAAC;YAClC,CAAC;YAED9D,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBD,KAAK,EAAE,IAAI,CAACA,KAAK;YACjB2C,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBD,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBG,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBD,MAAM,EAAE,IAAI,CAACA,MAAM;YACnBL,KAAK,EAAE,IAAI,CAACA,KAAK;YACjBD,MAAM,EAAE,IAAI,CAACA,MAAM;YAEnB;YACAxI,EAAE,EAAE,IAAI,CAACA,EAAE;YAEX2I,cAAc,EAAE,IAAI,CAACA,cAAc,CAACsB,KAAK,CAAC,CAAC,CAAC;YAC5C3B,IAAI,EAAE,IAAI,CAACA;UACb,CAAC;QACH;QAEAmI,SAAS,GAAGvK,KAAK,CAAC,CAAC,CAAC;QACpBwK,aAAa,GAAGD,SAAS,CAACrS,MAAM;;QAEhC;QACA0N,KAAK,GAAG2E,SAAS,CAACvE,KAAK,CAAC,eAAe,CAAC;QAExC,IAAIJ,KAAK,CAAC1N,MAAM,GAAG,CAAC,EAAE;UACpB,IAAI,CAACmI,QAAQ,IAAIuF,KAAK,CAAC1N,MAAM,GAAG,CAAC;UACjC,IAAI,CAAC4K,MAAM,CAAC2B,SAAS,GAAG,IAAI,CAACpE,QAAQ,GAAG,CAAC;UACzC,IAAI,CAACyC,MAAM,CAACwB,WAAW,GAAGsB,KAAK,CAACA,KAAK,CAAC1N,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;QAC1D,CAAC,MAAM;UACL,IAAI,CAAC4K,MAAM,CAACwB,WAAW,IAAIkG,aAAa;QAC1C;;QAEA;QACA,IAAI,CAACvK,MAAM,IAAIsK,SAAS;QAExB,IAAI,CAACvK,KAAK,IAAIuK,SAAS;QACvB,IAAI,CAAC7H,OAAO,IAAI6H,SAAS;QACzB,IAAI,CAAC5H,OAAO,GAAG3C,KAAK;QACpB,IAAI,CAAC6C,MAAM,GAAG,IAAI,CAAC5C,MAAM,CAAC/H,MAAM;QAChC,IAAI,CAAC4K,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,IAAI8F,aAAa;;QAErC;QACA;QACA;QACA,IAAI,CAAC5H,MAAM,IAAI4H,aAAa;QAE5B,IAAI,CAACjI,KAAK,GAAG,KAAK;QAClB,IAAI,CAACF,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACyB,KAAK,CAACyG,aAAa,CAAC;;QAE9C;QACA;QACA;QACAtK,KAAK,GAAG,IAAI,CAAClE,aAAa,CAACiD,IAAI,CAC7B,IAAI,EACJ,IAAI,CAACnF,EAAE,EACPuQ,YAAY,EACZ,IAAI,CAAC5H,cAAc,CAAC,IAAI,CAACA,cAAc,CAACvK,MAAM,GAAG,CAAC,CAAC,CAAC,gBACtD,CAAC;;QAED;QACA;;QAEA,IAAI,IAAI,CAACkK,IAAI,IAAI,IAAI,CAACE,MAAM,EAAE;UAC5B,IAAI,CAACF,IAAI,GAAG,KAAK;QACnB;QAEA,IAAIlC,KAAK,EAAE;UACT,OAAOA,KAAK;QACd,CAAC,MAAM,IAAI,IAAI,CAACmC,UAAU,EAAE;UAC1B;UACA,KAAK,IAAIrD,CAAC,IAAIsL,MAAM,EAAE;YACpB,IAAI,CAACtL,CAAC,CAAC,GAAGsL,MAAM,CAACtL,CAAC,CAAC;UACrB;UAEA,IAAI,CAACkD,kBAAkB,GAAG,IAAI;UAC9B,OAAO,KAAK,CAAC,CAAE;QACjB,CAAC,MAAM,IAAI,IAAI,CAACM,qBAAqB,EAAE;UACrC;UACA;UACAtC,KAAK,GAAG,IAAI,CAACsC,qBAAqB;UAElC,IAAI,CAACA,qBAAqB,GAAG,KAAK;UAClC,OAAOtC,KAAK;QACd;QAEA,OAAO,KAAK;MACd,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIkH,IAAI,EAAE,SAASqD,UAAUA,CAAA,EAAG;QAC1B,IAAI,IAAI,CAACrI,IAAI,EAAE;UACb,IAAI,CAAC+B,KAAK,CAAC,CAAC;UACZ,OAAO,IAAI,CAAC9J,GAAG;QACjB;QAEA,IAAI,CAAC,IAAI,CAACiI,MAAM,EAAE;UAChB,IAAI,CAACF,IAAI,GAAG,IAAI;QAClB;QAEA,IAAIlC,KAAK,EAAEF,KAAK,EAAE0K,SAAS,EAAE5B,KAAK;QAElC,IAAI,CAAC,IAAI,CAACvG,KAAK,EAAE;UACf,IAAI,CAAC4B,KAAK,CAAC,CAAC;QACd;QAEA,IAAIY,IAAI,GAAG,IAAI,CAAC7C,kBAAkB;QAElC,IAAI,CAAC6C,IAAI,EAAE;UACT;UACA;UACA;UACA;UACAA,IAAI,GAAG,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAACyI,aAAa,CAAC,CAAC;;UAErD;UACA;UACA,IAAI,CAAC5F,IAAI,IAAI,CAACA,IAAI,CAACH,KAAK,EAAE;YACxB,IAAIhB,UAAU,GAAG,EAAE;YAEnB,IAAI,IAAI,CAAC7J,OAAO,CAAC6Q,aAAa,EAAE;cAC9BhH,UAAU,GAAG,WAAW,IAAI,IAAI,CAACvD,QAAQ,GAAG,CAAC,CAAC;YAChD;YAEA,IAAIzI,CAAC,GAAG,IAAI,CAACmL,qBAAqB,CAChC,6BAA6B,GAAGa,UAAU,GAAG,qEAAqE,GAAG,IAAI,CAACiH,QAAQ,CAAC,CAAC,GAAG,qFAAqF,EAC5N,KACF,CAAC;;YAED;YACA,OAAO,IAAI,CAAC1N,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACoK,eAAe,CAAC,IAAI,IAAI,CAACC,KAAK;UACzE;QACF;QAEA,IAAI+C,QAAQ,GAAGD,IAAI,CAACH,KAAK;QACzB,IAAIkG,OAAO,GAAG/F,IAAI,CAACI,cAAc;QACjC,IAAIxM,GAAG,GAAGoM,IAAI,CAACK,YAAY;;QAE3B;QACA;QACA,KAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIW,GAAG,EAAEX,CAAC,EAAE,EAAE;UAC7B0S,SAAS,GAAG,IAAI,CAACpI,MAAM,CAACtC,KAAK,CAAC8K,OAAO,CAAC9S,CAAC,CAAC,CAAC;UAEzC,IAAI0S,SAAS,KAAK,CAAC1K,KAAK,IAAI0K,SAAS,CAAC,CAAC,CAAC,CAACxS,MAAM,GAAG8H,KAAK,CAAC,CAAC,CAAC,CAAC9H,MAAM,CAAC,EAAE;YAClE8H,KAAK,GAAG0K,SAAS;YACjB5B,KAAK,GAAG9Q,CAAC;YAET,IAAI,IAAI,CAAC+B,OAAO,CAACyM,eAAe,EAAE;cAChCtG,KAAK,GAAG,IAAI,CAACiK,UAAU,CAACO,SAAS,EAAE1F,QAAQ,CAAChN,CAAC,CAAC,CAAC;cAE/C,IAAIkI,KAAK,KAAK,KAAK,EAAE;gBACnB,OAAOA,KAAK;cACd,CAAC,MAAM,IAAI,IAAI,CAACmC,UAAU,EAAE;gBAC1BrC,KAAK,GAAG7B,SAAS;gBACjB,SAAS,CAAE;cACb,CAAC,MAAM;gBACL;gBACA,OAAO,KAAK;cACd;YACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACpE,OAAO,CAACgR,IAAI,EAAE;cAC7B;YACF;UACF;QACF;QAEA,IAAI/K,KAAK,EAAE;UACTE,KAAK,GAAG,IAAI,CAACiK,UAAU,CAACnK,KAAK,EAAEgF,QAAQ,CAAC8D,KAAK,CAAC,CAAC;UAE/C,IAAI5I,KAAK,KAAK,KAAK,EAAE;YACnB,OAAOA,KAAK;UACd;;UAEA;UACA,OAAO,KAAK;QACd;QAEA,IAAI,CAAC,IAAI,CAACoC,MAAM,EAAE;UAChB,IAAI,CAACF,IAAI,GAAG,IAAI;UAChB,IAAI,CAAC+B,KAAK,CAAC,CAAC;UACZ,OAAO,IAAI,CAAC9J,GAAG;QACjB,CAAC,MAAM;UACL,IAAIuJ,UAAU,GAAG,EAAE;UAEnB,IAAI,IAAI,CAAC7J,OAAO,CAAC6Q,aAAa,EAAE;YAC9BhH,UAAU,GAAG,WAAW,IAAI,IAAI,CAACvD,QAAQ,GAAG,CAAC,CAAC;UAChD;UAEA,IAAIzI,CAAC,GAAG,IAAI,CAACmL,qBAAqB,CAChC,eAAe,GAAGa,UAAU,GAAG,sBAAsB,EACrD,IAAI,CAAC7J,OAAO,CAAC8J,yBACf,CAAC;UAED,IAAImH,YAAY,GAAG,IAAI,CAAC1I,MAAM;UAC9B,IAAI2I,eAAe,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;UACrC,IAAIK,mBAAmB,GAAG,IAAI,CAACzI,cAAc,CAACvK,MAAM;UACpDgI,KAAK,GAAG,IAAI,CAAC/C,UAAU,CAACvF,CAAC,CAACkI,MAAM,EAAElI,CAAC,EAAE,IAAI,CAACoK,eAAe,CAAC,IAAI,IAAI,CAACC,KAAK;UAExE,IAAI/B,KAAK,KAAK,IAAI,CAAC+B,KAAK,EAAE;YACxB;YACA;YACA;YACA,IAAI,CAAC,IAAI,CAACU,OAAO;YAAI;YACrBqI,YAAY,KAAK,IAAI,CAAC1I,MAAM;YAAI;YAChC;YACA2I,eAAe,KAAK,IAAI,CAACJ,QAAQ,CAAC,CAAC,IAAIK,mBAAmB,KAAK,IAAI,CAACzI,cAAc,CAACvK,MAAM,EAAE;cACzF,IAAI,CAACuF,KAAK,CAAC,CAAC;YACd;UACF;UAEA,OAAOyC,KAAK;QACd;MACF,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIgB,GAAG,EAAE,SAASiK,SAASA,CAAA,EAAG;QACxB,IAAIrT,CAAC;;QAEL;QACA,IAAI,OAAO,IAAI,CAACwG,OAAO,KAAK,UAAU,EAAE;UACtCxG,CAAC,GAAG,IAAI,CAACwG,OAAO,CAACW,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAChC;QAEA,IAAI,OAAO,IAAI,CAAClF,OAAO,CAACuE,OAAO,KAAK,UAAU,EAAE;UAC9C;UACAxG,CAAC,GAAG,IAAI,CAACiC,OAAO,CAACuE,OAAO,CAACW,IAAI,CAAC,IAAI,EAAEnH,CAAC,CAAC,IAAIA,CAAC;QAC7C;QAEA,IAAI,IAAI,CAACgC,EAAE,IAAI,OAAO,IAAI,CAACA,EAAE,CAACwE,OAAO,KAAK,UAAU,EAAE;UACpD;UACAxG,CAAC,GAAG,IAAI,CAACgC,EAAE,CAACwE,OAAO,CAACW,IAAI,CAAC,IAAI,EAAEnH,CAAC,CAAC,IAAIA,CAAC;QACxC;QAEA,OAAO,CAACA,CAAC,EAAE;UACTA,CAAC,GAAG,IAAI,CAACsP,IAAI,CAAC,CAAC;QACjB;QAEA,IAAI,IAAI,CAACtN,EAAE,IAAI,OAAO,IAAI,CAACA,EAAE,CAACyE,QAAQ,KAAK,UAAU,EAAE;UACrD;UACAzG,CAAC,GAAG,IAAI,CAACgC,EAAE,CAACyE,QAAQ,CAACU,IAAI,CAAC,IAAI,EAAEnH,CAAC,CAAC,IAAIA,CAAC;QACzC;QAEA,IAAI,OAAO,IAAI,CAACiC,OAAO,CAACwE,QAAQ,KAAK,UAAU,EAAE;UAC/C;UACAzG,CAAC,GAAG,IAAI,CAACiC,OAAO,CAACwE,QAAQ,CAACU,IAAI,CAAC,IAAI,EAAEnH,CAAC,CAAC,IAAIA,CAAC;QAC9C;QAEA,IAAI,OAAO,IAAI,CAACyG,QAAQ,KAAK,UAAU,EAAE;UACvC;UACAzG,CAAC,GAAG,IAAI,CAACyG,QAAQ,CAACU,IAAI,CAAC,IAAI,EAAEnH,CAAC,CAAC,IAAIA,CAAC;QACtC;QAEA,OAAOA,CAAC;MACV,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACIqJ,OAAO,EAAE,SAASiK,aAAaA,CAAA,EAAG;QAChC,IAAItT,CAAC;QAEL,OAAO,CAACA,CAAC,EAAE;UACTA,CAAC,GAAG,IAAI,CAACsP,IAAI,CAAC,CAAC;QACjB;QAEA,OAAOtP,CAAC;MACV,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;MACI4J,OAAO,EAAE,SAAS2J,aAAaA,CAAA,EAAG;QAChC,IAAI1T,EAAE,GAAG;UACPwJ,OAAO,EAAE,EAAE,OAAO,IAAI,CAAC7C,OAAO,KAAK,UAAU,IAAI,OAAO,IAAI,CAACvE,OAAO,CAACuE,OAAO,KAAK,UAAU,IAAI,IAAI,CAACxE,EAAE,IAAI,OAAO,IAAI,CAACA,EAAE,CAACwE,OAAO,KAAK,UAAU,IAAI,IAAI,CAACxE,EAAE,IAAI,OAAO,IAAI,CAACA,EAAE,CAACyE,QAAQ,KAAK,UAAU,IAAI,OAAO,IAAI,CAACxE,OAAO,CAACwE,QAAQ,KAAK,UAAU,IAAI,OAAO,IAAI,CAACA,QAAQ,KAAK,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC4C,OAAO,KAAK;QACzT,CAAC;QAED,OAAOxJ,EAAE;MACX,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;MACI2T,KAAK,EAAE,SAASC,WAAWA,CAACC,SAAS,EAAE;QACrC,OAAO,IAAI,CAACC,SAAS,CAACD,SAAS,CAAC;MAClC,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACIC,SAAS,EAAE,SAASC,eAAeA,CAACF,SAAS,EAAE;QAC7C,IAAI,CAAC/I,cAAc,CAACtK,IAAI,CAACqT,SAAS,CAAC;QACnC,IAAI,CAACtJ,kBAAkB,GAAG,IAAI;QAC9B,OAAO,IAAI;MACb,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACIyJ,QAAQ,EAAE,SAASC,cAAcA,CAAA,EAAG;QAClC,IAAIxS,CAAC,GAAG,IAAI,CAACqJ,cAAc,CAACvK,MAAM,GAAG,CAAC;QAEtC,IAAIkB,CAAC,GAAG,CAAC,EAAE;UACT,IAAI,CAAC8I,kBAAkB,GAAG,IAAI;UAC9B,OAAO,IAAI,CAACO,cAAc,CAAC5K,GAAG,CAAC,CAAC;QAClC,CAAC,MAAM;UACL,OAAO,IAAI,CAAC4K,cAAc,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;MACIoI,QAAQ,EAAE,SAASgB,cAAcA,CAACzS,CAAC,EAAE;QACnCA,CAAC,GAAG,IAAI,CAACqJ,cAAc,CAACvK,MAAM,GAAG,CAAC,GAAGmQ,IAAI,CAACyD,GAAG,CAAC1S,CAAC,IAAI,CAAC,CAAC;QAErD,IAAIA,CAAC,IAAI,CAAC,EAAE;UACV,OAAO,IAAI,CAACqJ,cAAc,CAACrJ,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL,OAAO,SAAS;QAClB;MACF,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;MACIuR,aAAa,EAAE,SAASoB,mBAAmBA,CAAA,EAAG;QAC5C,IAAI,IAAI,CAACtJ,cAAc,CAACvK,MAAM,IAAI,IAAI,CAACuK,cAAc,CAAC,IAAI,CAACA,cAAc,CAACvK,MAAM,GAAG,CAAC,CAAC,EAAE;UACrF,OAAO,IAAI,CAAC4M,UAAU,CAAC,IAAI,CAACrC,cAAc,CAAC,IAAI,CAACA,cAAc,CAACvK,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7E,CAAC,MAAM;UACL,OAAO,IAAI,CAAC4M,UAAU,CAAC,SAAS,CAAC;QACnC;MACF,CAAC;MAED;AACJ;AACA;AACA;AACA;AACA;MACIkH,cAAc,EAAE,SAASC,oBAAoBA,CAAA,EAAG;QAC9C,OAAO,IAAI,CAACxJ,cAAc,CAACvK,MAAM;MACnC,CAAC;MAED6B,OAAO,EAAE;QACP6Q,aAAa,EAAE;MACjB,CAAC;MAED5I,eAAe,EAAEA,eAAe;MAEhChG,aAAa,EAAE,SAASkQ,oBAAoBA,CAACpS,EAAE,EAAEqS,YAAY,EAAEC,QAAQ,EAAE;QACvE,IAAIC,GAAG,GAAG,IAAI;QACd,IAAIC,OAAO,GAAGF,QAAQ;QAEtB,QAAQD,YAAY;UACpB,KAAK,CAAC;YACJ;YACA;YACA;YACA;UAEF;YACE,OAAO,IAAI,CAACI,wBAAwB,CAACJ,YAAY,CAAC;QACpD;MACF,CAAC;MAEDI,wBAAwB,EAAE;QACxB;QACA;QACA,CAAC,EAAE,EAAE;QAEL;QACA;QACA,CAAC,EAAE,CAAC;QAEJ;QACA;QACA,CAAC,EAAE,CAAC;QAEJ;QACA;QACA,CAAC,EAAE,CAAC;QAEJ;QACA;QACA,CAAC,EAAE,CAAC;QAEJ;QACA;QACA,CAAC,EAAE,EAAE;QAEL;QACA;QACA,CAAC,EAAE,EAAE;QAEL;QACA;QACA,CAAC,EAAE,EAAE;QAEL;QACA;QACA,CAAC,EAAE,EAAE;QAEL;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,CAAC;QAEL;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE,CAAC;QAEL;QACA;QACA,EAAE,EAAE,CAAC;QAEL;QACA;QACA,EAAE,EAAE,EAAE;QAEN;QACA;QACA,EAAE,EAAE;MACN,CAAC;MAED3H,KAAK,EAAE,CACL,SAAW,wBAAwB,EACnC,SAAW,UAAU,EACrB,SAAW,SAAS,EACpB,SAAW,SAAS,EACpB,SAAW,SAAS,EACpB,SAAW,QAAQ,EACnB,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,+BAA+B,EAC1C,SAAW,gCAAgC,EAC3C,SAAW,+BAA+B,EAC1C,SAAW,gCAAgC,EAC3C,SAAW,6BAA6B,EACxC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,+BAA+B,EAC1C,SAAW,+BAA+B,EAC1C,SAAW,gCAAgC,EAC3C,SAAW,gCAAgC,EAC3C,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,+BAA+B,EAC1C,SAAW,8BAA8B,EACzC,SAAW,8BAA8B,EACzC,SAAW,gCAAgC,EAC3C,SAAW,gCAAgC,EAC3C,SAAW,2BAA2B,EACtC,SAAW,4BAA4B,EACvC,SAAW,aAAa,EACxB,SAAW,YAAY,EACvB,SAAW,eAAe,EAC1B,SAAW,SAAS,EACpB,SAAW,SAAS,EACpB,SAAW,QAAQ,EACnB,SAAW,QAAQ,CACpB;MAEDE,UAAU,EAAE;QACV,SAAS,EAAE;UACTF,KAAK,EAAE,CACL,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,CACH;UAED4H,SAAS,EAAE;QACb;MACF;IACF,CAAC;IAED,OAAOjQ,KAAK;EACd,CAAC,CAAC,CAAC;EACHpG,MAAM,CAACoG,KAAK,GAAGA,KAAK;EAIpB,SAASkQ,MAAMA,CAAA,EAAG;IAChB,IAAI,CAAC3S,EAAE,GAAG,CAAC,CAAC;EACd;EACA2S,MAAM,CAACnV,SAAS,GAAGnB,MAAM;EACzBA,MAAM,CAACsW,MAAM,GAAGA,MAAM;EAEtB,OAAO,IAAIA,MAAM,CAAC,CAAC;AACnB,CAAC,CAAE,CAAC;AAKJ,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;EACpEA,OAAO,CAACxW,MAAM,GAAGA,MAAM;EACvBwW,OAAO,CAACF,MAAM,GAAGtW,MAAM,CAACsW,MAAM;EAC9BE,OAAO,CAACnP,KAAK,GAAG,YAAY;IAC1B,OAAOrH,MAAM,CAACqH,KAAK,CAAC7D,KAAK,CAACxD,MAAM,EAAE6N,SAAS,CAAC;EAC9C,CAAC;AAEH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}