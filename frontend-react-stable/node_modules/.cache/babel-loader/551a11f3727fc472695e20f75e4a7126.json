{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _math = require(\"../math.js\");\nvar _default = {\n  draw(context, size) {\n    const r = (0, _math.sqrt)(size - (0, _math.min)(size / 7, 2)) * 0.87559;\n    context.moveTo(-r, 0);\n    context.lineTo(r, 0);\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n  }\n};\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_math", "require", "_default", "draw", "context", "size", "r", "sqrt", "min", "moveTo", "lineTo"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/symbol/plus.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _math = require(\"../math.js\");\n\nvar _default = {\n  draw(context, size) {\n    const r = (0, _math.sqrt)(size - (0, _math.min)(size / 7, 2)) * 0.87559;\n    context.moveTo(-r, 0);\n    context.lineTo(r, 0);\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n  }\n\n};\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,IAAIC,QAAQ,GAAG;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACO,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACQ,GAAG,EAAEH,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO;IACvED,OAAO,CAACK,MAAM,CAAC,CAACH,CAAC,EAAE,CAAC,CAAC;IACrBF,OAAO,CAACM,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;IACpBF,OAAO,CAACK,MAAM,CAAC,CAAC,EAAEH,CAAC,CAAC;IACpBF,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAACJ,CAAC,CAAC;EACvB;AAEF,CAAC;AACDT,OAAO,CAACE,OAAO,GAAGG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}