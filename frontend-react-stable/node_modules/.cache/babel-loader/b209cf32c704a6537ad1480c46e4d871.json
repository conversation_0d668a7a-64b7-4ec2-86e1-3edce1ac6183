{"ast": null, "code": "/**\n * @fileOverview Y Axis\n */\n\nexport var YAxis = function YAxis() {\n  return null;\n};\nYAxis.displayName = 'YAxis';\nYAxis.defaultProps = {\n  allowDuplicatedCategory: true,\n  allowDecimals: true,\n  hide: false,\n  orientation: 'left',\n  width: 60,\n  height: 0,\n  mirror: false,\n  yAxisId: 0,\n  tickCount: 5,\n  type: 'number',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  allowDataOverflow: false,\n  scale: 'auto',\n  reversed: false\n};", "map": {"version": 3, "names": ["YA<PERSON>s", "displayName", "defaultProps", "allowDuplicatedCategory", "allowDecimals", "hide", "orientation", "width", "height", "mirror", "yAxisId", "tickCount", "type", "padding", "top", "bottom", "allowDataOverflow", "scale", "reversed"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/cartesian/YAxis.js"], "sourcesContent": ["/**\n * @fileOverview Y Axis\n */\n\nexport var YAxis = function YAxis() {\n  return null;\n};\nYAxis.displayName = 'YAxis';\nYAxis.defaultProps = {\n  allowDuplicatedCategory: true,\n  allowDecimals: true,\n  hide: false,\n  orientation: 'left',\n  width: 60,\n  height: 0,\n  mirror: false,\n  yAxisId: 0,\n  tickCount: 5,\n  type: 'number',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  allowDataOverflow: false,\n  scale: 'auto',\n  reversed: false\n};"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,IAAIA,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;EAClC,OAAO,IAAI;AACb,CAAC;AACDA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,YAAY,GAAG;EACnBC,uBAAuB,EAAE,IAAI;EAC7BC,aAAa,EAAE,IAAI;EACnBC,IAAI,EAAE,KAAK;EACXC,WAAW,EAAE,MAAM;EACnBC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE;IACPC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDC,iBAAiB,EAAE,KAAK;EACxBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}