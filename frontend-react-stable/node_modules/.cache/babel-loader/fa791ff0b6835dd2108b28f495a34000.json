{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\nimport * as React from 'react';\nimport OriginCSSMotion from \"./CSSMotion\";\nimport { diffKeys, parseKeys, STATUS_ADD, STATUS_KEEP, STATUS_REMOVE, STATUS_REMOVED } from \"./util/diff\";\nimport { supportTransition } from \"./util/motion\";\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nexport function genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : OriginCSSMotion;\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    _inherits(CSSMotionList, _React$Component);\n    var _super = _createSuper(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      _classCallCheck(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      _defineProperty(_assertThisInitialized(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      _defineProperty(_assertThisInitialized(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return _objectSpread(_objectSpread({}, entity), {}, {\n              status: STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    _createClass(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = _objectWithoutProperties(_this$props, _excluded);\n        var Component = component || React.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/React.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = _objectWithoutProperties(_ref2, _excluded2);\n          var visible = status === STATUS_ADD || status === STATUS_KEEP;\n          return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children(_objectSpread(_objectSpread({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = parseKeys(keys);\n        var mixedKeyEntities = diffKeys(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === STATUS_REMOVED && entity.status === STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(React.Component);\n  _defineProperty(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\nexport default genCSSMotionList(supportTransition);", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_objectSpread", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "_excluded", "_excluded2", "React", "OriginCSSMotion", "diff<PERSON>eys", "parse<PERSON>eys", "STATUS_ADD", "STATUS_KEEP", "STATUS_REMOVE", "STATUS_REMOVED", "supportTransition", "MOTION_PROP_NAMES", "genCSSMotionList", "transitionSupport", "CSSMotion", "arguments", "length", "undefined", "CSSMotionList", "_React$Component", "_super", "_this", "_len", "args", "Array", "_key", "call", "apply", "concat", "keyEntities", "<PERSON><PERSON><PERSON>", "setState", "prevState", "nextKeyEntities", "map", "entity", "key", "status", "state", "restKeysCount", "filter", "_ref", "props", "onAllRemoved", "value", "render", "_this2", "_this$props", "component", "children", "_onVisibleChanged", "onVisibleChanged", "restProps", "Component", "Fragment", "motionProps", "for<PERSON>ach", "prop", "keys", "createElement", "_ref2", "index", "eventProps", "visible", "changedVisible", "ref", "getDerivedStateFromProps", "_ref3", "_ref4", "parsedKeyObjects", "mixedKeyEntities", "prevEntity", "find", "_ref5"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-motion/es/CSSMotionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\nimport * as React from 'react';\nimport OriginCSSMotion from \"./CSSMotion\";\nimport { diffKeys, parseKeys, STATUS_ADD, STATUS_KEEP, STATUS_REMOVE, STATUS_REMOVED } from \"./util/diff\";\nimport { supportTransition } from \"./util/motion\";\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nexport function genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : OriginCSSMotion;\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    _inherits(CSSMotionList, _React$Component);\n    var _super = _createSuper(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      _classCallCheck(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      _defineProperty(_assertThisInitialized(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      _defineProperty(_assertThisInitialized(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return _objectSpread(_objectSpread({}, entity), {}, {\n              status: STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    _createClass(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = _objectWithoutProperties(_this$props, _excluded);\n        var Component = component || React.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/React.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = _objectWithoutProperties(_ref2, _excluded2);\n          var visible = status === STATUS_ADD || status === STATUS_KEEP;\n          return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children(_objectSpread(_objectSpread({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = parseKeys(keys);\n        var mixedKeyEntities = diffKeys(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === STATUS_REMOVED && entity.status === STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(React.Component);\n  _defineProperty(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\nexport default genCSSMotionList(supportTransition);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,cAAc,CAAC;EAC3EC,UAAU,GAAG,CAAC,QAAQ,CAAC;AACzB;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,aAAa;AACzC,SAASC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,QAAQ,aAAa;AACzG,SAASC,iBAAiB,QAAQ,eAAe;AACjD,IAAIC,iBAAiB,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC;AAC5W;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,iBAAiB,EAAE;EAClD,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGZ,eAAe;EACnG,IAAIe,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;IAC3DtB,SAAS,CAACqB,aAAa,EAAEC,gBAAgB,CAAC;IAC1C,IAAIC,MAAM,GAAGtB,YAAY,CAACoB,aAAa,CAAC;IACxC,SAASA,aAAaA,CAAA,EAAG;MACvB,IAAIG,KAAK;MACT3B,eAAe,CAAC,IAAI,EAAEwB,aAAa,CAAC;MACpC,KAAK,IAAII,IAAI,GAAGP,SAAS,CAACC,MAAM,EAAEO,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGV,SAAS,CAACU,IAAI,CAAC;MAC9B;MACAJ,KAAK,GAAGD,MAAM,CAACM,IAAI,CAACC,KAAK,CAACP,MAAM,EAAE,CAAC,IAAI,CAAC,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAC;MACtDxB,eAAe,CAACH,sBAAsB,CAACyB,KAAK,CAAC,EAAE,OAAO,EAAE;QACtDQ,WAAW,EAAE;MACf,CAAC,CAAC;MACF;MACA9B,eAAe,CAACH,sBAAsB,CAACyB,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUS,SAAS,EAAE;QAC/ET,KAAK,CAACU,QAAQ,CAAC,UAAUC,SAAS,EAAE;UAClC,IAAIC,eAAe,GAAGD,SAAS,CAACH,WAAW,CAACK,GAAG,CAAC,UAAUC,MAAM,EAAE;YAChE,IAAIA,MAAM,CAACC,GAAG,KAAKN,SAAS,EAAE,OAAOK,MAAM;YAC3C,OAAO1C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;cAClDE,MAAM,EAAE5B;YACV,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,OAAO;YACLoB,WAAW,EAAEI;UACf,CAAC;QACH,CAAC,EAAE,YAAY;UACb,IAAIJ,WAAW,GAAGR,KAAK,CAACiB,KAAK,CAACT,WAAW;UACzC,IAAIU,aAAa,GAAGV,WAAW,CAACW,MAAM,CAAC,UAAUC,IAAI,EAAE;YACrD,IAAIJ,MAAM,GAAGI,IAAI,CAACJ,MAAM;YACxB,OAAOA,MAAM,KAAK5B,cAAc;UAClC,CAAC,CAAC,CAACO,MAAM;UACT,IAAIuB,aAAa,KAAK,CAAC,IAAIlB,KAAK,CAACqB,KAAK,CAACC,YAAY,EAAE;YACnDtB,KAAK,CAACqB,KAAK,CAACC,YAAY,CAAC,CAAC;UAC5B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOtB,KAAK;IACd;IACA1B,YAAY,CAACuB,aAAa,EAAE,CAAC;MAC3BkB,GAAG,EAAE,QAAQ;MACbQ,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;QACvB,IAAIC,MAAM,GAAG,IAAI;QACjB,IAAIjB,WAAW,GAAG,IAAI,CAACS,KAAK,CAACT,WAAW;QACxC,IAAIkB,WAAW,GAAG,IAAI,CAACL,KAAK;UAC1BM,SAAS,GAAGD,WAAW,CAACC,SAAS;UACjCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;UAC/BC,iBAAiB,GAAGH,WAAW,CAACI,gBAAgB;UAChDR,YAAY,GAAGI,WAAW,CAACJ,YAAY;UACvCS,SAAS,GAAG5D,wBAAwB,CAACuD,WAAW,EAAE/C,SAAS,CAAC;QAC9D,IAAIqD,SAAS,GAAGL,SAAS,IAAI9C,KAAK,CAACoD,QAAQ;QAC3C,IAAIC,WAAW,GAAG,CAAC,CAAC;QACpB5C,iBAAiB,CAAC6C,OAAO,CAAC,UAAUC,IAAI,EAAE;UACxCF,WAAW,CAACE,IAAI,CAAC,GAAGL,SAAS,CAACK,IAAI,CAAC;UACnC,OAAOL,SAAS,CAACK,IAAI,CAAC;QACxB,CAAC,CAAC;QACF,OAAOL,SAAS,CAACM,IAAI;QACrB,OAAO,aAAaxD,KAAK,CAACyD,aAAa,CAACN,SAAS,EAAED,SAAS,EAAEvB,WAAW,CAACK,GAAG,CAAC,UAAU0B,KAAK,EAAEC,KAAK,EAAE;UACpG,IAAIxB,MAAM,GAAGuB,KAAK,CAACvB,MAAM;YACvByB,UAAU,GAAGtE,wBAAwB,CAACoE,KAAK,EAAE3D,UAAU,CAAC;UAC1D,IAAI8D,OAAO,GAAG1B,MAAM,KAAK/B,UAAU,IAAI+B,MAAM,KAAK9B,WAAW;UAC7D,OAAO,aAAaL,KAAK,CAACyD,aAAa,CAAC7C,SAAS,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEgE,WAAW,EAAE;YAC3EnB,GAAG,EAAE0B,UAAU,CAAC1B,GAAG;YACnB2B,OAAO,EAAEA,OAAO;YAChBD,UAAU,EAAEA,UAAU;YACtBX,gBAAgB,EAAE,SAASA,gBAAgBA,CAACa,cAAc,EAAE;cAC1Dd,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACc,cAAc,EAAE;gBAC9F5B,GAAG,EAAE0B,UAAU,CAAC1B;cAClB,CAAC,CAAC;cACF,IAAI,CAAC4B,cAAc,EAAE;gBACnBlB,MAAM,CAAChB,SAAS,CAACgC,UAAU,CAAC1B,GAAG,CAAC;cAClC;YACF;UACF,CAAC,CAAC,EAAE,UAAUM,KAAK,EAAEuB,GAAG,EAAE;YACxB,OAAOhB,QAAQ,CAACxD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cAC1DmB,KAAK,EAAEA;YACT,CAAC,CAAC,EAAEI,GAAG,CAAC;UACV,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,EAAE,CAAC;MACH7B,GAAG,EAAE,0BAA0B;MAC/BQ,KAAK,EAAE,SAASsB,wBAAwBA,CAACC,KAAK,EAAEC,KAAK,EAAE;QACrD,IAAIV,IAAI,GAAGS,KAAK,CAACT,IAAI;QACrB,IAAI7B,WAAW,GAAGuC,KAAK,CAACvC,WAAW;QACnC,IAAIwC,gBAAgB,GAAGhE,SAAS,CAACqD,IAAI,CAAC;QACtC,IAAIY,gBAAgB,GAAGlE,QAAQ,CAACyB,WAAW,EAAEwC,gBAAgB,CAAC;QAC9D,OAAO;UACLxC,WAAW,EAAEyC,gBAAgB,CAAC9B,MAAM,CAAC,UAAUL,MAAM,EAAE;YACrD,IAAIoC,UAAU,GAAG1C,WAAW,CAAC2C,IAAI,CAAC,UAAUC,KAAK,EAAE;cACjD,IAAIrC,GAAG,GAAGqC,KAAK,CAACrC,GAAG;cACnB,OAAOD,MAAM,CAACC,GAAG,KAAKA,GAAG;YAC3B,CAAC,CAAC;;YAEF;YACA,IAAImC,UAAU,IAAIA,UAAU,CAAClC,MAAM,KAAK5B,cAAc,IAAI0B,MAAM,CAACE,MAAM,KAAK7B,aAAa,EAAE;cACzF,OAAO,KAAK;YACd;YACA,OAAO,IAAI;UACb,CAAC;QACH,CAAC;MACH;IACF,CAAC,CAAC,CAAC;IACH,OAAOU,aAAa;EACtB,CAAC,CAAChB,KAAK,CAACmD,SAAS,CAAC;EAClBtD,eAAe,CAACmB,aAAa,EAAE,cAAc,EAAE;IAC7C8B,SAAS,EAAE;EACb,CAAC,CAAC;EACF,OAAO9B,aAAa;AACtB;AACA,eAAeN,gBAAgB,CAACF,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}