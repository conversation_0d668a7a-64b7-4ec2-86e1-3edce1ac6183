{"ast": null, "code": "// This icon file is generated automatically.\nvar MinusCircleFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 ***********.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h368c4.4 0 8 3.6 8 8v48z\"\n      }\n    }]\n  },\n  \"name\": \"minus-circle\",\n  \"theme\": \"filled\"\n};\nexport default MinusCircleFilled;", "map": {"version": 3, "names": ["MinusCircleFilled"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons-svg/es/asn/MinusCircleFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar MinusCircleFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 ***********.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h368c4.4 0 8 3.6 8 8v48z\" } }] }, \"name\": \"minus-circle\", \"theme\": \"filled\" };\nexport default MinusCircleFilled;\n"], "mappings": "AAAA;AACA,IAAIA,iBAAiB,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAiL;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,cAAc;EAAE,OAAO,EAAE;AAAS,CAAC;AACnY,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}