{"ast": null, "code": "/**\n * 任务管理API服务\n * 提供异步任务的创建、查询、取消等功能\n */import api from'./api';// 任务状态常量\nexport const TASK_STATUS={PENDING:'pending',RUNNING:'running',COMPLETED:'completed',FAILED:'failed',CANCELLED:'cancelled'};// 任务类型常量\nexport const TASK_TYPE={TRAINING:'training',PREDICTION:'prediction'};// 任务接口定义\n/**\n * 获取所有任务列表\n */export const getAllTasks=async()=>{try{const response=await api.get('/tasks/tasks');return response.data;}catch(error){console.error('获取任务列表失败:',error);throw error;}};/**\n * 获取特定任务的状态\n * @param taskId - 任务ID\n */export const getTaskStatus=async taskId=>{try{const response=await api.get(`/tasks/task/${taskId}`);return response.data;}catch(error){console.error(`获取任务 ${taskId} 状态失败:`,error);throw error;}};/**\n * 取消任务\n * @param taskId - 任务ID\n */export const cancelTask=async taskId=>{try{const response=await api.delete(`/tasks/task/${taskId}`);return response.data;}catch(error){console.error(`取消任务 ${taskId} 失败:`,error);throw error;}};/**\n * 获取正在运行的任务\n */export const getRunningTasks=async()=>{try{const response=await api.get('/tasks/tasks/running');return response.data;}catch(error){console.error('获取运行中任务失败:',error);throw error;}};/**\n * 异步启动模型训练\n * @param formData - 训练参数\n */export const startTrainingAsync=async formData=>{try{const response=await api.post('/model_training/train_async',formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){console.error('启动异步训练失败:',error);throw error;}};/**\n * 异步启动批量模型训练\n * @param formData - 批量训练参数\n */export const startBatchTrainingAsync=async formData=>{try{const response=await api.post('/model_training/train_batch',formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){console.error('启动批量异步训练失败:',error);throw error;}};/**\n * 异步启动模型预测\n * @param formData - 预测参数\n */export const startPredictionAsync=async formData=>{try{const response=await api.post('/model_prediction/predict_async',formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data;}catch(error){console.error('启动异步预测失败:',error);throw error;}};/**\n * 轮询任务状态直到完成\n * @param taskId - 任务ID\n * @param onProgress - 进度回调函数\n * @param interval - 轮询间隔（毫秒）\n * @returns 任务完成时的结果\n */export const pollTaskStatus=async function(taskId,onProgress){let interval=arguments.length>2&&arguments[2]!==undefined?arguments[2]:2000;return new Promise((resolve,reject)=>{const poll=async()=>{try{const response=await getTaskStatus(taskId);const task=response.task;if(!task){reject(new Error('任务不存在'));return;}// 调用进度回调\nif(onProgress){onProgress(task);}// 检查任务状态\nif(task.status===TASK_STATUS.COMPLETED){resolve(task);}else if(task.status===TASK_STATUS.FAILED){reject(new Error(task.error||'任务执行失败'));}else if(task.status===TASK_STATUS.CANCELLED){reject(new Error('任务已被取消'));}else{// 继续轮询\nsetTimeout(poll,interval);}}catch(error){reject(error);}};poll();});};/**\n * 获取已完成的任务\n */export const getCompletedTasks=async()=>{try{const response=await api.get('/tasks/completed');return response.data;}catch(error){console.error('获取已完成任务失败:',error);throw error;}};/**\n * 删除单个已完成的任务\n */export const deleteSingleTask=async taskId=>{try{const response=await api.delete(`/tasks/task/${taskId}`);return response.data;}catch(error){console.error('删除任务失败:',error);throw error;}};/**\n * 清空所有已完成的任务\n */export const clearCompletedTasks=async()=>{try{const response=await api.delete('/tasks/completed');return response.data;}catch(error){console.error('清空已完成任务失败:',error);throw error;}};/**\n * 格式化任务状态显示文本\n * @param status - 任务状态\n */export const formatTaskStatus=status=>{const statusMap={[TASK_STATUS.PENDING]:'等待中',[TASK_STATUS.RUNNING]:'运行中',[TASK_STATUS.COMPLETED]:'已完成',[TASK_STATUS.FAILED]:'失败',[TASK_STATUS.CANCELLED]:'已取消'};return statusMap[status]||status;};/**\n * 格式化任务类型显示文本\n * @param type - 任务类型\n */export const formatTaskType=type=>{const typeMap={[TASK_TYPE.TRAINING]:'模型训练',[TASK_TYPE.PREDICTION]:'模型预测'};return typeMap[type]||type;};/**\n * 获取任务状态对应的颜色\n * @param status - 任务状态\n */export const getTaskStatusColor=status=>{const colorMap={[TASK_STATUS.PENDING]:'default',[TASK_STATUS.RUNNING]:'processing',[TASK_STATUS.COMPLETED]:'success',[TASK_STATUS.FAILED]:'error',[TASK_STATUS.CANCELLED]:'warning'};return colorMap[status]||'default';};/**\n * 计算任务运行时间\n * @param startTime - 开始时间\n * @param endTime - 结束时间（可选）\n */export const calculateTaskDuration=(startTime,endTime)=>{if(!startTime)return'未知';const start=new Date(startTime);const end=endTime?new Date(endTime):new Date();const duration=Math.floor((end.getTime()-start.getTime())/1000);// 秒\nif(duration<60){return`${duration}秒`;}else if(duration<3600){return`${Math.floor(duration/60)}分${duration%60}秒`;}else{const hours=Math.floor(duration/3600);const minutes=Math.floor(duration%3600/60);return`${hours}小时${minutes}分`;}};const taskApi={getAllTasks,getTaskStatus,cancelTask,getRunningTasks,getCompletedTasks,deleteSingleTask,clearCompletedTasks,startTrainingAsync,startPredictionAsync,pollTaskStatus,formatTaskStatus,formatTaskType,getTaskStatusColor,calculateTaskDuration,TASK_STATUS,TASK_TYPE};export default taskApi;", "map": {"version": 3, "names": ["api", "TASK_STATUS", "PENDING", "RUNNING", "COMPLETED", "FAILED", "CANCELLED", "TASK_TYPE", "TRAINING", "PREDICTION", "getAllTasks", "response", "get", "data", "error", "console", "getTaskStatus", "taskId", "cancelTask", "delete", "getRunningTasks", "startTrainingAsync", "formData", "post", "headers", "startBatchTrainingAsync", "startPredictionAsync", "pollTaskStatus", "onProgress", "interval", "arguments", "length", "undefined", "Promise", "resolve", "reject", "poll", "task", "Error", "status", "setTimeout", "getCompletedTasks", "deleteSingleTask", "clearCompletedTasks", "formatTaskStatus", "statusMap", "formatTaskType", "type", "typeMap", "getTaskStatusColor", "colorMap", "calculateTaskDuration", "startTime", "endTime", "start", "Date", "end", "duration", "Math", "floor", "getTime", "hours", "minutes", "taskApi"], "sources": ["/home/<USER>/frontend-react-stable/src/services/taskApi.ts"], "sourcesContent": ["/**\n * 任务管理API服务\n * 提供异步任务的创建、查询、取消等功能\n */\n\nimport api from './api';\n\n// 任务状态常量\nexport const TASK_STATUS = {\n  PENDING: 'pending',\n  RUNNING: 'running',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  CANCELLED: 'cancelled'\n} as const;\n\n// 任务类型常量\nexport const TASK_TYPE = {\n  TRAINING: 'training',\n  PREDICTION: 'prediction'\n} as const;\n\nexport type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];\nexport type TaskType = typeof TASK_TYPE[keyof typeof TASK_TYPE];\n\n// 任务接口定义\nexport interface Task {\n  task_id: string;\n  task_type: TaskType;\n  status: TaskStatus;\n  progress?: number;\n  created_at: string;\n  updated_at?: string;\n  started_at?: string;\n  completed_at?: string;\n  current_step?: string;\n  total_steps?: number;\n  message?: string;\n  error?: string;\n  params?: any;\n  result?: any;\n}\n\nexport interface TaskResponse {\n  success: boolean;\n  task_id?: string;\n  task?: Task;\n  tasks?: Task[];\n  total?: number;\n  count?: number;\n  message?: string;\n}\n\n/**\n * 获取所有任务列表\n */\nexport const getAllTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks');\n    return response.data;\n  } catch (error) {\n    console.error('获取任务列表失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 获取特定任务的状态\n * @param taskId - 任务ID\n */\nexport const getTaskStatus = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.get(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`获取任务 ${taskId} 状态失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 取消任务\n * @param taskId - 任务ID\n */\nexport const cancelTask = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`取消任务 ${taskId} 失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 获取正在运行的任务\n */\nexport const getRunningTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks/running');\n    return response.data;\n  } catch (error) {\n    console.error('获取运行中任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型训练\n * @param formData - 训练参数\n */\nexport const startTrainingAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_training/train_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步训练失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动批量模型训练\n * @param formData - 批量训练参数\n */\nexport const startBatchTrainingAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_training/train_batch', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动批量异步训练失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型预测\n * @param formData - 预测参数\n */\nexport const startPredictionAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_prediction/predict_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 轮询任务状态直到完成\n * @param taskId - 任务ID\n * @param onProgress - 进度回调函数\n * @param interval - 轮询间隔（毫秒）\n * @returns 任务完成时的结果\n */\nexport const pollTaskStatus = async (\n  taskId: string, \n  onProgress?: (task: Task) => void, \n  interval: number = 2000\n): Promise<Task> => {\n  return new Promise((resolve, reject) => {\n    const poll = async () => {\n      try {\n        const response = await getTaskStatus(taskId);\n        const task = response.task;\n        \n        if (!task) {\n          reject(new Error('任务不存在'));\n          return;\n        }\n        \n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n        \n        // 检查任务状态\n        if (task.status === TASK_STATUS.COMPLETED) {\n          resolve(task);\n        } else if (task.status === TASK_STATUS.FAILED) {\n          reject(new Error(task.error || '任务执行失败'));\n        } else if (task.status === TASK_STATUS.CANCELLED) {\n          reject(new Error('任务已被取消'));\n        } else {\n          // 继续轮询\n          setTimeout(poll, interval);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    \n    poll();\n  });\n};\n\n/**\n * 获取已完成的任务\n */\nexport const getCompletedTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('获取已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 删除单个已完成的任务\n */\nexport const deleteSingleTask = async (taskId: string): Promise<{ success: boolean; message: string; deleted_task_id: string }> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error('删除任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 清空所有已完成的任务\n */\nexport const clearCompletedTasks = async (): Promise<{ success: boolean; message: string; cleared_count: number; cleared_task_ids: string[] }> => {\n  try {\n    const response = await api.delete('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('清空已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 格式化任务状态显示文本\n * @param status - 任务状态\n */\nexport const formatTaskStatus = (status: TaskStatus): string => {\n  const statusMap = {\n    [TASK_STATUS.PENDING]: '等待中',\n    [TASK_STATUS.RUNNING]: '运行中',\n    [TASK_STATUS.COMPLETED]: '已完成',\n    [TASK_STATUS.FAILED]: '失败',\n    [TASK_STATUS.CANCELLED]: '已取消'\n  };\n  return statusMap[status] || status;\n};\n\n/**\n * 格式化任务类型显示文本\n * @param type - 任务类型\n */\nexport const formatTaskType = (type: TaskType): string => {\n  const typeMap = {\n    [TASK_TYPE.TRAINING]: '模型训练',\n    [TASK_TYPE.PREDICTION]: '模型预测'\n  };\n  return typeMap[type] || type;\n};\n\n/**\n * 获取任务状态对应的颜色\n * @param status - 任务状态\n */\nexport const getTaskStatusColor = (status: TaskStatus): string => {\n  const colorMap = {\n    [TASK_STATUS.PENDING]: 'default',\n    [TASK_STATUS.RUNNING]: 'processing',\n    [TASK_STATUS.COMPLETED]: 'success',\n    [TASK_STATUS.FAILED]: 'error',\n    [TASK_STATUS.CANCELLED]: 'warning'\n  };\n  return colorMap[status] || 'default';\n};\n\n/**\n * 计算任务运行时间\n * @param startTime - 开始时间\n * @param endTime - 结束时间（可选）\n */\nexport const calculateTaskDuration = (startTime?: string, endTime?: string): string => {\n  if (!startTime) return '未知';\n  \n  const start = new Date(startTime);\n  const end = endTime ? new Date(endTime) : new Date();\n  const duration = Math.floor((end.getTime() - start.getTime()) / 1000); // 秒\n  \n  if (duration < 60) {\n    return `${duration}秒`;\n  } else if (duration < 3600) {\n    return `${Math.floor(duration / 60)}分${duration % 60}秒`;\n  } else {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    return `${hours}小时${minutes}分`;\n  }\n};\n\nconst taskApi = {\n  getAllTasks,\n  getTaskStatus,\n  cancelTask,\n  getRunningTasks,\n  getCompletedTasks,\n  deleteSingleTask,\n  clearCompletedTasks,\n  startTrainingAsync,\n  startPredictionAsync,\n  pollTaskStatus,\n  formatTaskStatus,\n  formatTaskType,\n  getTaskStatusColor,\n  calculateTaskDuration,\n  TASK_STATUS,\n  TASK_TYPE\n};\n\nexport default taskApi;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,GAAG,KAAM,OAAO,CAEvB;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,WAAW,CACtBC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,WACb,CAAU,CAEV;AACA,MAAO,MAAM,CAAAC,SAAS,CAAG,CACvBC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,YACd,CAAU,CAKV;AA4BA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,WAAW,CAAG,KAAAA,CAAA,GAAmC,CAC5D,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACY,GAAG,CAAC,cAAc,CAAC,CAC9C,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,aAAa,CAAG,KAAO,CAAAC,MAAc,EAA4B,CAC5E,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACY,GAAG,CAAC,eAAeK,MAAM,EAAE,CAAC,CACvD,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQG,MAAM,QAAQ,CAAEH,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAI,UAAU,CAAG,KAAO,CAAAD,MAAc,EAA4B,CACzE,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACmB,MAAM,CAAC,eAAeF,MAAM,EAAE,CAAC,CAC1D,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQG,MAAM,MAAM,CAAEH,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAM,eAAe,CAAG,KAAAA,CAAA,GAAmC,CAChE,GAAI,CACF,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACY,GAAG,CAAC,sBAAsB,CAAC,CACtD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAO,kBAAkB,CAAG,KAAO,CAAAC,QAAkB,EAA4B,CACrF,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACuB,IAAI,CAAC,6BAA6B,CAAED,QAAQ,CAAE,CACvEE,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAb,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAW,uBAAuB,CAAG,KAAO,CAAAH,QAAkB,EAA4B,CAC1F,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACuB,IAAI,CAAC,6BAA6B,CAAED,QAAQ,CAAE,CACvEE,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAb,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAY,oBAAoB,CAAG,KAAO,CAAAJ,QAAkB,EAA4B,CACvF,GAAI,CACF,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACuB,IAAI,CAAC,iCAAiC,CAAED,QAAQ,CAAE,CAC3EE,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAb,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAa,cAAc,CAAG,cAAAA,CAC5BV,MAAc,CACdW,UAAiC,CAEf,IADlB,CAAAC,QAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAEvB,MAAO,IAAI,CAAAG,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,IAAI,CAAG,KAAAA,CAAA,GAAY,CACvB,GAAI,CACF,KAAM,CAAAzB,QAAQ,CAAG,KAAM,CAAAK,aAAa,CAACC,MAAM,CAAC,CAC5C,KAAM,CAAAoB,IAAI,CAAG1B,QAAQ,CAAC0B,IAAI,CAE1B,GAAI,CAACA,IAAI,CAAE,CACTF,MAAM,CAAC,GAAI,CAAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAC1B,OACF,CAEA;AACA,GAAIV,UAAU,CAAE,CACdA,UAAU,CAACS,IAAI,CAAC,CAClB,CAEA;AACA,GAAIA,IAAI,CAACE,MAAM,GAAKtC,WAAW,CAACG,SAAS,CAAE,CACzC8B,OAAO,CAACG,IAAI,CAAC,CACf,CAAC,IAAM,IAAIA,IAAI,CAACE,MAAM,GAAKtC,WAAW,CAACI,MAAM,CAAE,CAC7C8B,MAAM,CAAC,GAAI,CAAAG,KAAK,CAACD,IAAI,CAACvB,KAAK,EAAI,QAAQ,CAAC,CAAC,CAC3C,CAAC,IAAM,IAAIuB,IAAI,CAACE,MAAM,GAAKtC,WAAW,CAACK,SAAS,CAAE,CAChD6B,MAAM,CAAC,GAAI,CAAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAC7B,CAAC,IAAM,CACL;AACAE,UAAU,CAACJ,IAAI,CAAEP,QAAQ,CAAC,CAC5B,CACF,CAAE,MAAOf,KAAK,CAAE,CACdqB,MAAM,CAACrB,KAAK,CAAC,CACf,CACF,CAAC,CAEDsB,IAAI,CAAC,CAAC,CACR,CAAC,CAAC,CACJ,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAK,iBAAiB,CAAG,KAAAA,CAAA,GAAmC,CAClE,GAAI,CACF,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACY,GAAG,CAAC,kBAAkB,CAAC,CAClD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA4B,gBAAgB,CAAG,KAAO,CAAAzB,MAAc,EAA8E,CACjI,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACmB,MAAM,CAAC,eAAeF,MAAM,EAAE,CAAC,CAC1D,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA6B,mBAAmB,CAAG,KAAAA,CAAA,GAA+G,CAChJ,GAAI,CACF,KAAM,CAAAhC,QAAQ,CAAG,KAAM,CAAAX,GAAG,CAACmB,MAAM,CAAC,kBAAkB,CAAC,CACrD,MAAO,CAAAR,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA8B,gBAAgB,CAAIL,MAAkB,EAAa,CAC9D,KAAM,CAAAM,SAAS,CAAG,CAChB,CAAC5C,WAAW,CAACC,OAAO,EAAG,KAAK,CAC5B,CAACD,WAAW,CAACE,OAAO,EAAG,KAAK,CAC5B,CAACF,WAAW,CAACG,SAAS,EAAG,KAAK,CAC9B,CAACH,WAAW,CAACI,MAAM,EAAG,IAAI,CAC1B,CAACJ,WAAW,CAACK,SAAS,EAAG,KAC3B,CAAC,CACD,MAAO,CAAAuC,SAAS,CAACN,MAAM,CAAC,EAAIA,MAAM,CACpC,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAO,cAAc,CAAIC,IAAc,EAAa,CACxD,KAAM,CAAAC,OAAO,CAAG,CACd,CAACzC,SAAS,CAACC,QAAQ,EAAG,MAAM,CAC5B,CAACD,SAAS,CAACE,UAAU,EAAG,MAC1B,CAAC,CACD,MAAO,CAAAuC,OAAO,CAACD,IAAI,CAAC,EAAIA,IAAI,CAC9B,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,kBAAkB,CAAIV,MAAkB,EAAa,CAChE,KAAM,CAAAW,QAAQ,CAAG,CACf,CAACjD,WAAW,CAACC,OAAO,EAAG,SAAS,CAChC,CAACD,WAAW,CAACE,OAAO,EAAG,YAAY,CACnC,CAACF,WAAW,CAACG,SAAS,EAAG,SAAS,CAClC,CAACH,WAAW,CAACI,MAAM,EAAG,OAAO,CAC7B,CAACJ,WAAW,CAACK,SAAS,EAAG,SAC3B,CAAC,CACD,MAAO,CAAA4C,QAAQ,CAACX,MAAM,CAAC,EAAI,SAAS,CACtC,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAY,qBAAqB,CAAGA,CAACC,SAAkB,CAAEC,OAAgB,GAAa,CACrF,GAAI,CAACD,SAAS,CAAE,MAAO,IAAI,CAE3B,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAAC,IAAI,CAACH,SAAS,CAAC,CACjC,KAAM,CAAAI,GAAG,CAAGH,OAAO,CAAG,GAAI,CAAAE,IAAI,CAACF,OAAO,CAAC,CAAG,GAAI,CAAAE,IAAI,CAAC,CAAC,CACpD,KAAM,CAAAE,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAGN,KAAK,CAACM,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CAAE;AAEvE,GAAIH,QAAQ,CAAG,EAAE,CAAE,CACjB,MAAO,GAAGA,QAAQ,GAAG,CACvB,CAAC,IAAM,IAAIA,QAAQ,CAAG,IAAI,CAAE,CAC1B,MAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAG,EAAE,CAAC,IAAIA,QAAQ,CAAG,EAAE,GAAG,CACzD,CAAC,IAAM,CACL,KAAM,CAAAI,KAAK,CAAGH,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAG,IAAI,CAAC,CACzC,KAAM,CAAAK,OAAO,CAAGJ,IAAI,CAACC,KAAK,CAAEF,QAAQ,CAAG,IAAI,CAAI,EAAE,CAAC,CAClD,MAAO,GAAGI,KAAK,KAAKC,OAAO,GAAG,CAChC,CACF,CAAC,CAED,KAAM,CAAAC,OAAO,CAAG,CACdrD,WAAW,CACXM,aAAa,CACbE,UAAU,CACVE,eAAe,CACfqB,iBAAiB,CACjBC,gBAAgB,CAChBC,mBAAmB,CACnBtB,kBAAkB,CAClBK,oBAAoB,CACpBC,cAAc,CACdiB,gBAAgB,CAChBE,cAAc,CACdG,kBAAkB,CAClBE,qBAAqB,CACrBlD,WAAW,CACXM,SACF,CAAC,CAED,cAAe,CAAAwD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}