{"ast": null, "code": "var getTag = require('./_getTag'),\n  isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]';\n\n/**\n * The base implementation of `_.isMap` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n */\nfunction baseIsMap(value) {\n  return isObjectLike(value) && getTag(value) == mapTag;\n}\nmodule.exports = baseIsMap;", "map": {"version": 3, "names": ["getTag", "require", "isObjectLike", "mapTag", "baseIsMap", "value", "module", "exports"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/lodash/_baseIsMap.js"], "sourcesContent": ["var getTag = require('./_getTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]';\n\n/**\n * The base implementation of `_.isMap` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n */\nfunction baseIsMap(value) {\n  return isObjectLike(value) && getTag(value) == mapTag;\n}\n\nmodule.exports = baseIsMap;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC7BC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIE,MAAM,GAAG,cAAc;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIL,MAAM,CAACK,KAAK,CAAC,IAAIF,MAAM;AACvD;AAEAG,MAAM,CAACC,OAAO,GAAGH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}