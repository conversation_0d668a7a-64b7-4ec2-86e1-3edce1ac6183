{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { FormItemInputContext } from '../form/context';\nimport { cloneElement } from '../_util/reactNode';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { tuple } from '../_util/type';\nvar ClearableInputType = tuple('text', 'input');\nfunction hasAddon(props) {\n  return !!(props.addonBefore || props.addonAfter);\n}\nvar ClearableLabeledInput = /*#__PURE__*/function (_React$Component) {\n  _inherits(ClearableLabeledInput, _React$Component);\n  var _super = _createSuper(ClearableLabeledInput);\n  function ClearableLabeledInput() {\n    _classCallCheck(this, ClearableLabeledInput);\n    return _super.apply(this, arguments);\n  }\n  _createClass(ClearableLabeledInput, [{\n    key: \"renderClearIcon\",\n    value: function renderClearIcon(prefixCls) {\n      var _classNames;\n      var _this$props = this.props,\n        value = _this$props.value,\n        disabled = _this$props.disabled,\n        readOnly = _this$props.readOnly,\n        handleReset = _this$props.handleReset,\n        suffix = _this$props.suffix;\n      var needClear = !disabled && !readOnly && value;\n      var className = \"\".concat(prefixCls, \"-clear-icon\");\n      return /*#__PURE__*/React.createElement(CloseCircleFilled, {\n        onClick: handleReset,\n        // Do not trigger onBlur when clear input\n        // https://github.com/ant-design/ant-design/issues/31200\n        onMouseDown: function onMouseDown(e) {\n          return e.preventDefault();\n        },\n        className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(className, \"-hidden\"), !needClear), _defineProperty(_classNames, \"\".concat(className, \"-has-suffix\"), !!suffix), _classNames), className),\n        role: \"button\"\n      });\n    }\n  }, {\n    key: \"renderTextAreaWithClearIcon\",\n    value: function renderTextAreaWithClearIcon(prefixCls, element, statusContext) {\n      var _classNames2;\n      var _this$props2 = this.props,\n        value = _this$props2.value,\n        allowClear = _this$props2.allowClear,\n        className = _this$props2.className,\n        style = _this$props2.style,\n        direction = _this$props2.direction,\n        bordered = _this$props2.bordered,\n        hidden = _this$props2.hidden,\n        customStatus = _this$props2.status;\n      var contextStatus = statusContext.status,\n        hasFeedback = statusContext.hasFeedback;\n      if (!allowClear) {\n        return cloneElement(element, {\n          value: value\n        });\n      }\n      var affixWrapperCls = classNames(\"\".concat(prefixCls, \"-affix-wrapper\"), \"\".concat(prefixCls, \"-affix-wrapper-textarea-with-clear-btn\"), getStatusClassNames(\"\".concat(prefixCls, \"-affix-wrapper\"), getMergedStatus(contextStatus, customStatus), hasFeedback), (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-affix-wrapper-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-affix-wrapper-borderless\"), !bordered), _defineProperty(_classNames2, \"\".concat(className), !hasAddon(this.props) && className), _classNames2));\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: affixWrapperCls,\n        style: style,\n        hidden: hidden\n      }, cloneElement(element, {\n        style: null,\n        value: value\n      }), this.renderClearIcon(prefixCls));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this = this;\n      return /*#__PURE__*/React.createElement(FormItemInputContext.Consumer, null, function (statusContext) {\n        var _this$props3 = _this.props,\n          prefixCls = _this$props3.prefixCls,\n          inputType = _this$props3.inputType,\n          element = _this$props3.element;\n        if (inputType === ClearableInputType[0]) {\n          return _this.renderTextAreaWithClearIcon(prefixCls, element, statusContext);\n        }\n      });\n    }\n  }]);\n  return ClearableLabeledInput;\n}(React.Component);\nexport default ClearableLabeledInput;", "map": {"version": 3, "names": ["_defineProperty", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "CloseCircleFilled", "classNames", "React", "FormItemInputContext", "cloneElement", "getMergedStatus", "getStatusClassNames", "tuple", "ClearableInputType", "hasAddon", "props", "addonBefore", "addonAfter", "ClearableLabeledInput", "_React$Component", "_super", "apply", "arguments", "key", "value", "renderClearIcon", "prefixCls", "_classNames", "_this$props", "disabled", "readOnly", "handleReset", "suffix", "needClear", "className", "concat", "createElement", "onClick", "onMouseDown", "e", "preventDefault", "role", "renderTextAreaWithClearIcon", "element", "statusContext", "_classNames2", "_this$props2", "allowClear", "style", "direction", "bordered", "hidden", "customStatus", "status", "contextStatus", "hasFeedback", "affixWrapperCls", "render", "_this", "Consumer", "_this$props3", "inputType", "Component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/ClearableLabeledInput.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { FormItemInputContext } from '../form/context';\nimport { cloneElement } from '../_util/reactNode';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { tuple } from '../_util/type';\nvar ClearableInputType = tuple('text', 'input');\nfunction hasAddon(props) {\n  return !!(props.addonBefore || props.addonAfter);\n}\nvar ClearableLabeledInput = /*#__PURE__*/function (_React$Component) {\n  _inherits(ClearableLabeledInput, _React$Component);\n  var _super = _createSuper(ClearableLabeledInput);\n  function ClearableLabeledInput() {\n    _classCallCheck(this, ClearableLabeledInput);\n    return _super.apply(this, arguments);\n  }\n  _createClass(ClearableLabeledInput, [{\n    key: \"renderClearIcon\",\n    value: function renderClearIcon(prefixCls) {\n      var _classNames;\n      var _this$props = this.props,\n        value = _this$props.value,\n        disabled = _this$props.disabled,\n        readOnly = _this$props.readOnly,\n        handleReset = _this$props.handleReset,\n        suffix = _this$props.suffix;\n      var needClear = !disabled && !readOnly && value;\n      var className = \"\".concat(prefixCls, \"-clear-icon\");\n      return /*#__PURE__*/React.createElement(CloseCircleFilled, {\n        onClick: handleReset,\n        // Do not trigger onBlur when clear input\n        // https://github.com/ant-design/ant-design/issues/31200\n        onMouseDown: function onMouseDown(e) {\n          return e.preventDefault();\n        },\n        className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(className, \"-hidden\"), !needClear), _defineProperty(_classNames, \"\".concat(className, \"-has-suffix\"), !!suffix), _classNames), className),\n        role: \"button\"\n      });\n    }\n  }, {\n    key: \"renderTextAreaWithClearIcon\",\n    value: function renderTextAreaWithClearIcon(prefixCls, element, statusContext) {\n      var _classNames2;\n      var _this$props2 = this.props,\n        value = _this$props2.value,\n        allowClear = _this$props2.allowClear,\n        className = _this$props2.className,\n        style = _this$props2.style,\n        direction = _this$props2.direction,\n        bordered = _this$props2.bordered,\n        hidden = _this$props2.hidden,\n        customStatus = _this$props2.status;\n      var contextStatus = statusContext.status,\n        hasFeedback = statusContext.hasFeedback;\n      if (!allowClear) {\n        return cloneElement(element, {\n          value: value\n        });\n      }\n      var affixWrapperCls = classNames(\"\".concat(prefixCls, \"-affix-wrapper\"), \"\".concat(prefixCls, \"-affix-wrapper-textarea-with-clear-btn\"), getStatusClassNames(\"\".concat(prefixCls, \"-affix-wrapper\"), getMergedStatus(contextStatus, customStatus), hasFeedback), (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-affix-wrapper-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-affix-wrapper-borderless\"), !bordered), _defineProperty(_classNames2, \"\".concat(className), !hasAddon(this.props) && className), _classNames2));\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: affixWrapperCls,\n        style: style,\n        hidden: hidden\n      }, cloneElement(element, {\n        style: null,\n        value: value\n      }), this.renderClearIcon(prefixCls));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this = this;\n      return /*#__PURE__*/React.createElement(FormItemInputContext.Consumer, null, function (statusContext) {\n        var _this$props3 = _this.props,\n          prefixCls = _this$props3.prefixCls,\n          inputType = _this$props3.inputType,\n          element = _this$props3.element;\n        if (inputType === ClearableInputType[0]) {\n          return _this.renderTextAreaWithClearIcon(prefixCls, element, statusContext);\n        }\n      });\n    }\n  }]);\n  return ClearableLabeledInput;\n}(React.Component);\nexport default ClearableLabeledInput;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC3E,SAASC,KAAK,QAAQ,eAAe;AACrC,IAAIC,kBAAkB,GAAGD,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC;AAC/C,SAASE,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,CAAC,EAAEA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACE,UAAU,CAAC;AAClD;AACA,IAAIC,qBAAqB,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnEhB,SAAS,CAACe,qBAAqB,EAAEC,gBAAgB,CAAC;EAClD,IAAIC,MAAM,GAAGhB,YAAY,CAACc,qBAAqB,CAAC;EAChD,SAASA,qBAAqBA,CAAA,EAAG;IAC/BjB,eAAe,CAAC,IAAI,EAAEiB,qBAAqB,CAAC;IAC5C,OAAOE,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtC;EACApB,YAAY,CAACgB,qBAAqB,EAAE,CAAC;IACnCK,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAASC,eAAeA,CAACC,SAAS,EAAE;MACzC,IAAIC,WAAW;MACf,IAAIC,WAAW,GAAG,IAAI,CAACb,KAAK;QAC1BS,KAAK,GAAGI,WAAW,CAACJ,KAAK;QACzBK,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,WAAW,GAAGH,WAAW,CAACG,WAAW;QACrCC,MAAM,GAAGJ,WAAW,CAACI,MAAM;MAC7B,IAAIC,SAAS,GAAG,CAACJ,QAAQ,IAAI,CAACC,QAAQ,IAAIN,KAAK;MAC/C,IAAIU,SAAS,GAAG,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,aAAa,CAAC;MACnD,OAAO,aAAanB,KAAK,CAAC6B,aAAa,CAAC/B,iBAAiB,EAAE;QACzDgC,OAAO,EAAEN,WAAW;QACpB;QACA;QACAO,WAAW,EAAE,SAASA,WAAWA,CAACC,CAAC,EAAE;UACnC,OAAOA,CAAC,CAACC,cAAc,CAAC,CAAC;QAC3B,CAAC;QACDN,SAAS,EAAE5B,UAAU,EAAEqB,WAAW,GAAG,CAAC,CAAC,EAAE3B,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAE,CAACD,SAAS,CAAC,EAAEjC,eAAe,CAAC2B,WAAW,EAAE,EAAE,CAACQ,MAAM,CAACD,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC,CAACF,MAAM,CAAC,EAAEL,WAAW,GAAGO,SAAS,CAAC;QACzNO,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDlB,GAAG,EAAE,6BAA6B;IAClCC,KAAK,EAAE,SAASkB,2BAA2BA,CAAChB,SAAS,EAAEiB,OAAO,EAAEC,aAAa,EAAE;MAC7E,IAAIC,YAAY;MAChB,IAAIC,YAAY,GAAG,IAAI,CAAC/B,KAAK;QAC3BS,KAAK,GAAGsB,YAAY,CAACtB,KAAK;QAC1BuB,UAAU,GAAGD,YAAY,CAACC,UAAU;QACpCb,SAAS,GAAGY,YAAY,CAACZ,SAAS;QAClCc,KAAK,GAAGF,YAAY,CAACE,KAAK;QAC1BC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,QAAQ,GAAGJ,YAAY,CAACI,QAAQ;QAChCC,MAAM,GAAGL,YAAY,CAACK,MAAM;QAC5BC,YAAY,GAAGN,YAAY,CAACO,MAAM;MACpC,IAAIC,aAAa,GAAGV,aAAa,CAACS,MAAM;QACtCE,WAAW,GAAGX,aAAa,CAACW,WAAW;MACzC,IAAI,CAACR,UAAU,EAAE;QACf,OAAOtC,YAAY,CAACkC,OAAO,EAAE;UAC3BnB,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,IAAIgC,eAAe,GAAGlD,UAAU,CAAC,EAAE,CAAC6B,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC,EAAE,EAAE,CAACS,MAAM,CAACT,SAAS,EAAE,wCAAwC,CAAC,EAAEf,mBAAmB,CAAC,EAAE,CAACwB,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC,EAAEhB,eAAe,CAAC4C,aAAa,EAAEF,YAAY,CAAC,EAAEG,WAAW,CAAC,GAAGV,YAAY,GAAG,CAAC,CAAC,EAAE7C,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACV,MAAM,CAACT,SAAS,EAAE,oBAAoB,CAAC,EAAEuB,SAAS,KAAK,KAAK,CAAC,EAAEjD,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACV,MAAM,CAACT,SAAS,EAAE,2BAA2B,CAAC,EAAE,CAACwB,QAAQ,CAAC,EAAElD,eAAe,CAAC6C,YAAY,EAAE,EAAE,CAACV,MAAM,CAACD,SAAS,CAAC,EAAE,CAACpB,QAAQ,CAAC,IAAI,CAACC,KAAK,CAAC,IAAImB,SAAS,CAAC,EAAEW,YAAY,CAAC,CAAC;MACzjB,OAAO,aAAatC,KAAK,CAAC6B,aAAa,CAAC,MAAM,EAAE;QAC9CF,SAAS,EAAEsB,eAAe;QAC1BR,KAAK,EAAEA,KAAK;QACZG,MAAM,EAAEA;MACV,CAAC,EAAE1C,YAAY,CAACkC,OAAO,EAAE;QACvBK,KAAK,EAAE,IAAI;QACXxB,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,IAAI,CAACC,eAAe,CAACC,SAAS,CAAC,CAAC;IACtC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASiC,MAAMA,CAAA,EAAG;MACvB,IAAIC,KAAK,GAAG,IAAI;MAChB,OAAO,aAAanD,KAAK,CAAC6B,aAAa,CAAC5B,oBAAoB,CAACmD,QAAQ,EAAE,IAAI,EAAE,UAAUf,aAAa,EAAE;QACpG,IAAIgB,YAAY,GAAGF,KAAK,CAAC3C,KAAK;UAC5BW,SAAS,GAAGkC,YAAY,CAAClC,SAAS;UAClCmC,SAAS,GAAGD,YAAY,CAACC,SAAS;UAClClB,OAAO,GAAGiB,YAAY,CAACjB,OAAO;QAChC,IAAIkB,SAAS,KAAKhD,kBAAkB,CAAC,CAAC,CAAC,EAAE;UACvC,OAAO6C,KAAK,CAAChB,2BAA2B,CAAChB,SAAS,EAAEiB,OAAO,EAAEC,aAAa,CAAC;QAC7E;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EACH,OAAO1B,qBAAqB;AAC9B,CAAC,CAACX,KAAK,CAACuD,SAAS,CAAC;AAClB,eAAe5C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}