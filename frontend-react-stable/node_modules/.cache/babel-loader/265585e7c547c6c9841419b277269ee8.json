{"ast": null, "code": "import _get from \"lodash/get\";\nimport _omit from \"lodash/omit\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNaN from \"lodash/isNaN\";\nvar _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport classNames from 'classnames';\n/**\n * @fileOverview TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport Smooth from 'react-smooth';\nimport { Tooltip } from '../component/Tooltip';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterSvgElements, findChildByType, validateWidthHeight, filterProps } from '../util/ReactUtils';\nvar NODE_VALUE_KEY = 'value';\nvar computeNode = function computeNode(_ref) {\n  var _objectSpread2;\n  var depth = _ref.depth,\n    node = _ref.node,\n    index = _ref.index,\n    valueKey = _ref.valueKey;\n  var children = node.children;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map(function (child, i) {\n    return computeNode({\n      depth: childDepth,\n      node: child,\n      index: i,\n      valueKey: valueKey\n    });\n  }) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce(function (result, child) {\n      return result + child[NODE_VALUE_KEY];\n    }, 0);\n  } else {\n    // TODO need to verify valueKey\n    nodeValue = _isNaN(node[valueKey]) || node[valueKey] <= 0 ? 0 : node[valueKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, (_objectSpread2 = {\n    children: computedChildren\n  }, _defineProperty(_objectSpread2, NODE_VALUE_KEY, nodeValue), _defineProperty(_objectSpread2, \"depth\", depth), _defineProperty(_objectSpread2, \"index\", index), _objectSpread2));\n};\nvar filterRect = function filterRect(node) {\n  return {\n    x: node.x,\n    y: node.y,\n    width: node.width,\n    height: node.height\n  };\n};\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = function getAreaOfChildren(children, areaValueRatio) {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(function (child) {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: _isNaN(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = function getWorstScore(row, parentSize, aspectRatio) {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var _row$reduce = row.reduce(function (result, child) {\n      return {\n        min: Math.min(result.min, child.area),\n        max: Math.max(result.max, child.area)\n      };\n    }, {\n      min: Infinity,\n      max: 0\n    }),\n    min = _row$reduce.min,\n    max = _row$reduce.max;\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = function horizontalPosition(row, parentSize, parentRect, isFlush) {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var _i = 0, len = row.length; _i < len; _i++) {\n    child = row[_i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = function verticalPosition(row, parentSize, parentRect, isFlush) {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var _i2 = 0, len = row.length; _i2 < len; _i2++) {\n    child = row[_i2];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = function position(row, parentSize, parentRect, isFlush) {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = function squarify(node, aspectRatio) {\n  var children = node.children;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(function (c) {\n        return squarify(c, aspectRatio);\n      })\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isTooltipActive: false,\n  isAnimationFinished: false,\n  activeNode: null,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nexport var Treemap = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Treemap, _PureComponent);\n  var _super = _createSuper(Treemap);\n  function Treemap() {\n    var _this;\n    _classCallCheck(this, Treemap);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", _objectSpread({}, defaultState));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Treemap, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(node, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: true,\n          activeNode: node\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(node, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(node, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(node, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: false,\n          activeNode: null\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(node, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(node, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(node) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        type = _this$props3.type;\n      if (type === 'nest' && node.children) {\n        var _this$props4 = this.props,\n          width = _this$props4.width,\n          height = _this$props4.height,\n          dataKey = _this$props4.dataKey,\n          aspectRatio = _this$props4.aspectRatio;\n        var root = computeNode({\n          depth: 0,\n          node: _objectSpread(_objectSpread({}, node), {}, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }),\n          index: 0,\n          valueKey: dataKey\n        });\n        var formatRoot = squarify(root, aspectRatio);\n        var nestIndex = this.state.nestIndex;\n        nestIndex.push(node);\n        this.setState({\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: nestIndex\n        });\n      }\n      if (onClick) {\n        onClick(node);\n      }\n    }\n  }, {\n    key: \"handleNestIndex\",\n    value: function handleNestIndex(node, i) {\n      var nestIndex = this.state.nestIndex;\n      var _this$props5 = this.props,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        dataKey = _this$props5.dataKey,\n        aspectRatio = _this$props5.aspectRatio;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }),\n        index: 0,\n        valueKey: dataKey\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      nestIndex = nestIndex.slice(0, i + 1);\n      this.setState({\n        formatRoot: formatRoot,\n        currentRoot: node,\n        nestIndex: nestIndex\n      });\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(content, nodeProps, isLeaf) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        isAnimationActive = _this$props6.isAnimationActive,\n        animationBegin = _this$props6.animationBegin,\n        animationDuration = _this$props6.animationDuration,\n        animationEasing = _this$props6.animationEasing,\n        isUpdateAnimationActive = _this$props6.isUpdateAnimationActive,\n        type = _this$props6.type,\n        animationId = _this$props6.animationId,\n        colorPanel = _this$props6.colorPanel;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var width = nodeProps.width,\n        height = nodeProps.height,\n        x = nodeProps.x,\n        y = nodeProps.y,\n        depth = nodeProps.depth;\n      var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n      var event = {};\n      if (isLeaf || type === 'nest') {\n        event = {\n          onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n          onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n          onClick: this.handleClick.bind(this, nodeProps)\n        };\n      }\n      if (!isAnimationActive) {\n        return /*#__PURE__*/React.createElement(Layer, event, this.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width: width,\n          height: height,\n          x: x,\n          y: y\n        }), type, colorPanel));\n      }\n      return /*#__PURE__*/React.createElement(Smooth, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        key: \"treemap-\".concat(animationId),\n        from: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        to: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var currX = _ref2.x,\n          currY = _ref2.y,\n          currWidth = _ref2.width,\n          currHeight = _ref2.height;\n        return /*#__PURE__*/React.createElement(Smooth, {\n          from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\"),\n          to: \"translate(0, 0)\",\n          attributeName: \"transform\",\n          begin: animationBegin,\n          easing: animationEasing,\n          isActive: isAnimationActive,\n          duration: animationDuration\n        }, /*#__PURE__*/React.createElement(Layer, event, function () {\n          // when animation Duration , only render depth=1 nodes\n          if (depth > 2 && !isAnimationFinished) {\n            return null;\n          }\n          return _this2.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n            isAnimationActive: isAnimationActive,\n            isUpdateAnimationActive: !isUpdateAnimationActive,\n            width: currWidth,\n            height: currHeight,\n            x: currX,\n            y: currY\n          }), type, colorPanel);\n        }()));\n      });\n    }\n  }, {\n    key: \"renderNode\",\n    value: function renderNode(root, node, i) {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        content = _this$props7.content,\n        type = _this$props7.type;\n      var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props)), node), {}, {\n        root: root\n      });\n      var isLeaf = !node.children || !node.children.length;\n      var currentRoot = this.state.currentRoot;\n      var isCurrentRootChild = (currentRoot.children || []).filter(function (item) {\n        return item.depth === node.depth && item.name === node.name;\n      });\n      if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n        return null;\n      }\n      return (/*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        React.createElement(Layer, {\n          key: \"recharts-treemap-node-\".concat(i),\n          className: \"recharts-treemap-depth-\".concat(node.depth)\n        }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(function (child, index) {\n          return _this3.renderNode(node, child, index);\n        }) : null)\n      );\n    }\n  }, {\n    key: \"renderAllNodes\",\n    value: function renderAllNodes() {\n      var formatRoot = this.state.formatRoot;\n      if (!formatRoot) {\n        return null;\n      }\n      return this.renderNode(formatRoot, formatRoot, 0);\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props8 = this.props,\n        children = _this$props8.children,\n        nameKey = _this$props8.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$props9 = this.props,\n        width = _this$props9.width,\n        height = _this$props9.height;\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeNode = _this$state.activeNode;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeNode ? {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      } : null;\n      var payload = isTooltipActive && activeNode ? [{\n        payload: activeNode,\n        name: getValueByDataKey(activeNode, nameKey, ''),\n        value: getValueByDataKey(activeNode, NODE_VALUE_KEY)\n      }] : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n\n    // render nest treemap\n  }, {\n    key: \"renderNestIndex\",\n    value: function renderNestIndex() {\n      var _this4 = this;\n      var _this$props10 = this.props,\n        nameKey = _this$props10.nameKey,\n        nestIndexContent = _this$props10.nestIndexContent;\n      var nestIndex = this.state.nestIndex;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-treemap-nest-index-wrapper\",\n        style: {\n          marginTop: '8px',\n          textAlign: 'center'\n        }\n      }, nestIndex.map(function (item, i) {\n        // TODO need to verify nameKey type\n        var name = _get(item, nameKey, 'root');\n        var content = null;\n        if (/*#__PURE__*/React.isValidElement(nestIndexContent)) {\n          content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n        }\n        if (_isFunction(nestIndexContent)) {\n          content = nestIndexContent(item, i);\n        } else {\n          content = name;\n        }\n        return (/*#__PURE__*/\n          // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n          React.createElement(\"div\", {\n            onClick: _this4.handleNestIndex.bind(_this4, item, i),\n            key: \"nest-index-\".concat(uniqueId()),\n            className: \"recharts-treemap-nest-index-box\",\n            style: {\n              cursor: 'pointer',\n              display: 'inline-block',\n              padding: '0 7px',\n              background: '#000',\n              color: '#fff',\n              marginRight: '3px'\n            }\n          }, content)\n        );\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props11 = this.props,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        className = _this$props11.className,\n        style = _this$props11.style,\n        children = _this$props11.children,\n        type = _this$props11.type,\n        others = _objectWithoutProperties(_this$props11, _excluded);\n      var attrs = filterProps(others);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: type === 'nest' ? height - 30 : height\n      }), this.renderAllNodes(), filterSvgElements(children)), this.renderTooltip(), type === 'nest' && this.renderNestIndex());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n        var root = computeNode({\n          depth: 0,\n          node: {\n            children: nextProps.data,\n            x: 0,\n            y: 0,\n            width: nextProps.width,\n            height: nextProps.height\n          },\n          index: 0,\n          valueKey: nextProps.dataKey\n        });\n        var formatRoot = squarify(root, nextProps.aspectRatio);\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: [root],\n          prevAspectRatio: nextProps.aspectRatio,\n          prevData: nextProps.data,\n          prevWidth: nextProps.width,\n          prevHeight: nextProps.height,\n          prevDataKey: nextProps.dataKey,\n          prevType: nextProps.type\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContentItem\",\n    value: function renderContentItem(content, nodeProps, type, colorPanel) {\n      if (/*#__PURE__*/React.isValidElement(content)) {\n        return /*#__PURE__*/React.cloneElement(content, nodeProps);\n      }\n      if (_isFunction(content)) {\n        return content(nodeProps);\n      }\n      // optimize default shape\n      var x = nodeProps.x,\n        y = nodeProps.y,\n        width = nodeProps.width,\n        height = nodeProps.height,\n        index = nodeProps.index;\n      var arrow = null;\n      if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n        arrow = /*#__PURE__*/React.createElement(Polygon, {\n          points: [{\n            x: x + 2,\n            y: y + height / 2\n          }, {\n            x: x + 6,\n            y: y + height / 2 + 3\n          }, {\n            x: x + 2,\n            y: y + height / 2 + 6\n          }]\n        });\n      }\n      var text = null;\n      var nameSize = getStringSize(nodeProps.name);\n      if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n        text = /*#__PURE__*/React.createElement(\"text\", {\n          x: x + 8,\n          y: y + height / 2 + 7,\n          fontSize: 14\n        }, nodeProps.name);\n      }\n      var colors = colorPanel || COLOR_PANEL;\n      return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n        fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n        stroke: \"#fff\"\n      }, _omit(nodeProps, 'children'), {\n        role: \"img\"\n      })), arrow, text);\n    }\n  }]);\n  return Treemap;\n}(PureComponent);\n_defineProperty(Treemap, \"displayName\", 'Treemap');\n_defineProperty(Treemap, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});", "map": {"version": 3, "names": ["_get", "_omit", "_isFunction", "_isNaN", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "keys", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "classNames", "React", "PureComponent", "Smooth", "<PERSON><PERSON><PERSON>", "Layer", "Surface", "Polygon", "Rectangle", "getValueByDataKey", "COLOR_PANEL", "uniqueId", "getStringSize", "Global", "filterSvgElements", "findChildByType", "validateWidthHeight", "filterProps", "NODE_VALUE_KEY", "computeNode", "_ref", "_objectSpread2", "depth", "node", "index", "valueKey", "children", "<PERSON><PERSON><PERSON><PERSON>", "computed<PERSON><PERSON><PERSON>n", "map", "child", "nodeValue", "reduce", "filterRect", "x", "y", "width", "height", "getAreaOfChildren", "areaValueRatio", "ratio", "area", "getWorstScore", "row", "parentSize", "aspectRatio", "parentArea", "rowArea", "_row$reduce", "min", "Math", "max", "Infinity", "horizontalPosition", "parentRect", "isFlush", "rowHeight", "round", "curX", "_i", "len", "verticalPosition", "row<PERSON>id<PERSON>", "curY", "_i2", "position", "squarify", "rect", "best", "score", "size", "scaleChildren", "tempC<PERSON><PERSON>n", "slice", "shift", "pop", "c", "defaultState", "isTooltipActive", "isAnimationFinished", "activeNode", "formatRoot", "currentRoot", "nestIndex", "Treemap", "_PureComponent", "_super", "_this", "_len", "args", "Array", "_key", "concat", "onAnimationEnd", "setState", "onAnimationStart", "handleMouseEnter", "_this$props", "onMouseEnter", "tooltipItem", "handleMouseLeave", "_this$props2", "onMouseLeave", "handleClick", "_this$props3", "onClick", "type", "_this$props4", "dataKey", "root", "state", "handleNestIndex", "_this$props5", "renderItem", "content", "nodeProps", "<PERSON><PERSON><PERSON><PERSON>", "_this2", "_this$props6", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "isUpdateAnimationActive", "animationId", "colorPanel", "translateX", "parseInt", "random", "event", "createElement", "renderContentItem", "begin", "duration", "isActive", "easing", "from", "to", "handleAnimationStart", "handleAnimationEnd", "_ref2", "currX", "currY", "currWidth", "currHeight", "attributeName", "renderNode", "_this3", "_this$props7", "isCurrentRootChild", "item", "name", "className", "renderAllNodes", "renderTooltip", "_this$props8", "<PERSON><PERSON><PERSON>", "_this$props9", "_this$state", "viewBox", "coordinate", "payload", "cloneElement", "active", "label", "renderNestIndex", "_this4", "_this$props10", "nestIndexContent", "style", "marginTop", "textAlign", "isValidElement", "cursor", "display", "padding", "background", "color", "marginRight", "render", "_this$props11", "others", "attrs", "role", "getDerivedStateFromProps", "nextProps", "prevState", "data", "prevData", "prevType", "prevWidth", "prevHeight", "prevDataKey", "prevAspectRatio", "arrow", "points", "text", "nameSize", "fontSize", "colors", "fill", "stroke", "sqrt", "isSsr"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/chart/Treemap.js"], "sourcesContent": ["import _get from \"lodash/get\";\nimport _omit from \"lodash/omit\";\nimport _isFunction from \"lodash/isFunction\";\nimport _isNaN from \"lodash/isNaN\";\nvar _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\", \"type\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport classNames from 'classnames';\n/**\n * @fileOverview TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport Smooth from 'react-smooth';\nimport { Tooltip } from '../component/Tooltip';\nimport { Layer } from '../container/Layer';\nimport { Surface } from '../container/Surface';\nimport { Polygon } from '../shape/Polygon';\nimport { Rectangle } from '../shape/Rectangle';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { COLOR_PANEL } from '../util/Constants';\nimport { uniqueId } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { filterSvgElements, findChildByType, validateWidthHeight, filterProps } from '../util/ReactUtils';\nvar NODE_VALUE_KEY = 'value';\nvar computeNode = function computeNode(_ref) {\n  var _objectSpread2;\n  var depth = _ref.depth,\n    node = _ref.node,\n    index = _ref.index,\n    valueKey = _ref.valueKey;\n  var children = node.children;\n  var childDepth = depth + 1;\n  var computedChildren = children && children.length ? children.map(function (child, i) {\n    return computeNode({\n      depth: childDepth,\n      node: child,\n      index: i,\n      valueKey: valueKey\n    });\n  }) : null;\n  var nodeValue;\n  if (children && children.length) {\n    nodeValue = computedChildren.reduce(function (result, child) {\n      return result + child[NODE_VALUE_KEY];\n    }, 0);\n  } else {\n    // TODO need to verify valueKey\n    nodeValue = _isNaN(node[valueKey]) || node[valueKey] <= 0 ? 0 : node[valueKey];\n  }\n  return _objectSpread(_objectSpread({}, node), {}, (_objectSpread2 = {\n    children: computedChildren\n  }, _defineProperty(_objectSpread2, NODE_VALUE_KEY, nodeValue), _defineProperty(_objectSpread2, \"depth\", depth), _defineProperty(_objectSpread2, \"index\", index), _objectSpread2));\n};\nvar filterRect = function filterRect(node) {\n  return {\n    x: node.x,\n    y: node.y,\n    width: node.width,\n    height: node.height\n  };\n};\n\n// Compute the area for each child based on value & scale.\nvar getAreaOfChildren = function getAreaOfChildren(children, areaValueRatio) {\n  var ratio = areaValueRatio < 0 ? 0 : areaValueRatio;\n  return children.map(function (child) {\n    var area = child[NODE_VALUE_KEY] * ratio;\n    return _objectSpread(_objectSpread({}, child), {}, {\n      area: _isNaN(area) || area <= 0 ? 0 : area\n    });\n  });\n};\n\n// Computes the score for the specified row, as the worst aspect ratio.\nvar getWorstScore = function getWorstScore(row, parentSize, aspectRatio) {\n  var parentArea = parentSize * parentSize;\n  var rowArea = row.area * row.area;\n  var _row$reduce = row.reduce(function (result, child) {\n      return {\n        min: Math.min(result.min, child.area),\n        max: Math.max(result.max, child.area)\n      };\n    }, {\n      min: Infinity,\n      max: 0\n    }),\n    min = _row$reduce.min,\n    max = _row$reduce.max;\n  return rowArea ? Math.max(parentArea * max * aspectRatio / rowArea, rowArea / (parentArea * min * aspectRatio)) : Infinity;\n};\nvar horizontalPosition = function horizontalPosition(row, parentSize, parentRect, isFlush) {\n  var rowHeight = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowHeight > parentRect.height) {\n    rowHeight = parentRect.height;\n  }\n  var curX = parentRect.x;\n  var child;\n  for (var _i = 0, len = row.length; _i < len; _i++) {\n    child = row[_i];\n    child.x = curX;\n    child.y = parentRect.y;\n    child.height = rowHeight;\n    child.width = Math.min(rowHeight ? Math.round(child.area / rowHeight) : 0, parentRect.x + parentRect.width - curX);\n    curX += child.width;\n  }\n  // add the remain x to the last one of row\n  child.width += parentRect.x + parentRect.width - curX;\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    y: parentRect.y + rowHeight,\n    height: parentRect.height - rowHeight\n  });\n};\nvar verticalPosition = function verticalPosition(row, parentSize, parentRect, isFlush) {\n  var rowWidth = parentSize ? Math.round(row.area / parentSize) : 0;\n  if (isFlush || rowWidth > parentRect.width) {\n    rowWidth = parentRect.width;\n  }\n  var curY = parentRect.y;\n  var child;\n  for (var _i2 = 0, len = row.length; _i2 < len; _i2++) {\n    child = row[_i2];\n    child.x = parentRect.x;\n    child.y = curY;\n    child.width = rowWidth;\n    child.height = Math.min(rowWidth ? Math.round(child.area / rowWidth) : 0, parentRect.y + parentRect.height - curY);\n    curY += child.height;\n  }\n  if (child) {\n    child.height += parentRect.y + parentRect.height - curY;\n  }\n  return _objectSpread(_objectSpread({}, parentRect), {}, {\n    x: parentRect.x + rowWidth,\n    width: parentRect.width - rowWidth\n  });\n};\nvar position = function position(row, parentSize, parentRect, isFlush) {\n  if (parentSize === parentRect.width) {\n    return horizontalPosition(row, parentSize, parentRect, isFlush);\n  }\n  return verticalPosition(row, parentSize, parentRect, isFlush);\n};\n\n// Recursively arranges the specified node's children into squarified rows.\nvar squarify = function squarify(node, aspectRatio) {\n  var children = node.children;\n  if (children && children.length) {\n    var rect = filterRect(node);\n    // maybe a bug\n    var row = [];\n    var best = Infinity; // the best row score so far\n    var child, score; // the current row score\n    var size = Math.min(rect.width, rect.height); // initial orientation\n    var scaleChildren = getAreaOfChildren(children, rect.width * rect.height / node[NODE_VALUE_KEY]);\n    var tempChildren = scaleChildren.slice();\n    row.area = 0;\n    while (tempChildren.length > 0) {\n      // row first\n      // eslint-disable-next-line prefer-destructuring\n      row.push(child = tempChildren[0]);\n      row.area += child.area;\n      score = getWorstScore(row, size, aspectRatio);\n      if (score <= best) {\n        // continue with this orientation\n        tempChildren.shift();\n        best = score;\n      } else {\n        // abort, and try a different orientation\n        row.area -= row.pop().area;\n        rect = position(row, size, rect, false);\n        size = Math.min(rect.width, rect.height);\n        row.length = row.area = 0;\n        best = Infinity;\n      }\n    }\n    if (row.length) {\n      rect = position(row, size, rect, true);\n      row.length = row.area = 0;\n    }\n    return _objectSpread(_objectSpread({}, node), {}, {\n      children: scaleChildren.map(function (c) {\n        return squarify(c, aspectRatio);\n      })\n    });\n  }\n  return node;\n};\nvar defaultState = {\n  isTooltipActive: false,\n  isAnimationFinished: false,\n  activeNode: null,\n  formatRoot: null,\n  currentRoot: null,\n  nestIndex: []\n};\nexport var Treemap = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Treemap, _PureComponent);\n  var _super = _createSuper(Treemap);\n  function Treemap() {\n    var _this;\n    _classCallCheck(this, Treemap);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", _objectSpread({}, defaultState));\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationEnd\", function () {\n      var onAnimationEnd = _this.props.onAnimationEnd;\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_isFunction(onAnimationEnd)) {\n        onAnimationEnd();\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleAnimationStart\", function () {\n      var onAnimationStart = _this.props.onAnimationStart;\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_isFunction(onAnimationStart)) {\n        onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _createClass(Treemap, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(node, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: true,\n          activeNode: node\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(node, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(node, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(node, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState({\n          isTooltipActive: false,\n          activeNode: null\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(node, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(node, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(node) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        type = _this$props3.type;\n      if (type === 'nest' && node.children) {\n        var _this$props4 = this.props,\n          width = _this$props4.width,\n          height = _this$props4.height,\n          dataKey = _this$props4.dataKey,\n          aspectRatio = _this$props4.aspectRatio;\n        var root = computeNode({\n          depth: 0,\n          node: _objectSpread(_objectSpread({}, node), {}, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height\n          }),\n          index: 0,\n          valueKey: dataKey\n        });\n        var formatRoot = squarify(root, aspectRatio);\n        var nestIndex = this.state.nestIndex;\n        nestIndex.push(node);\n        this.setState({\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: nestIndex\n        });\n      }\n      if (onClick) {\n        onClick(node);\n      }\n    }\n  }, {\n    key: \"handleNestIndex\",\n    value: function handleNestIndex(node, i) {\n      var nestIndex = this.state.nestIndex;\n      var _this$props5 = this.props,\n        width = _this$props5.width,\n        height = _this$props5.height,\n        dataKey = _this$props5.dataKey,\n        aspectRatio = _this$props5.aspectRatio;\n      var root = computeNode({\n        depth: 0,\n        node: _objectSpread(_objectSpread({}, node), {}, {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }),\n        index: 0,\n        valueKey: dataKey\n      });\n      var formatRoot = squarify(root, aspectRatio);\n      nestIndex = nestIndex.slice(0, i + 1);\n      this.setState({\n        formatRoot: formatRoot,\n        currentRoot: node,\n        nestIndex: nestIndex\n      });\n    }\n  }, {\n    key: \"renderItem\",\n    value: function renderItem(content, nodeProps, isLeaf) {\n      var _this2 = this;\n      var _this$props6 = this.props,\n        isAnimationActive = _this$props6.isAnimationActive,\n        animationBegin = _this$props6.animationBegin,\n        animationDuration = _this$props6.animationDuration,\n        animationEasing = _this$props6.animationEasing,\n        isUpdateAnimationActive = _this$props6.isUpdateAnimationActive,\n        type = _this$props6.type,\n        animationId = _this$props6.animationId,\n        colorPanel = _this$props6.colorPanel;\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var width = nodeProps.width,\n        height = nodeProps.height,\n        x = nodeProps.x,\n        y = nodeProps.y,\n        depth = nodeProps.depth;\n      var translateX = parseInt(\"\".concat((Math.random() * 2 - 1) * width), 10);\n      var event = {};\n      if (isLeaf || type === 'nest') {\n        event = {\n          onMouseEnter: this.handleMouseEnter.bind(this, nodeProps),\n          onMouseLeave: this.handleMouseLeave.bind(this, nodeProps),\n          onClick: this.handleClick.bind(this, nodeProps)\n        };\n      }\n      if (!isAnimationActive) {\n        return /*#__PURE__*/React.createElement(Layer, event, this.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n          isAnimationActive: false,\n          isUpdateAnimationActive: false,\n          width: width,\n          height: height,\n          x: x,\n          y: y\n        }), type, colorPanel));\n      }\n      return /*#__PURE__*/React.createElement(Smooth, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        key: \"treemap-\".concat(animationId),\n        from: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        to: {\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        },\n        onAnimationStart: this.handleAnimationStart,\n        onAnimationEnd: this.handleAnimationEnd\n      }, function (_ref2) {\n        var currX = _ref2.x,\n          currY = _ref2.y,\n          currWidth = _ref2.width,\n          currHeight = _ref2.height;\n        return /*#__PURE__*/React.createElement(Smooth, {\n          from: \"translate(\".concat(translateX, \"px, \").concat(translateX, \"px)\"),\n          to: \"translate(0, 0)\",\n          attributeName: \"transform\",\n          begin: animationBegin,\n          easing: animationEasing,\n          isActive: isAnimationActive,\n          duration: animationDuration\n        }, /*#__PURE__*/React.createElement(Layer, event, function () {\n          // when animation Duration , only render depth=1 nodes\n          if (depth > 2 && !isAnimationFinished) {\n            return null;\n          }\n          return _this2.constructor.renderContentItem(content, _objectSpread(_objectSpread({}, nodeProps), {}, {\n            isAnimationActive: isAnimationActive,\n            isUpdateAnimationActive: !isUpdateAnimationActive,\n            width: currWidth,\n            height: currHeight,\n            x: currX,\n            y: currY\n          }), type, colorPanel);\n        }()));\n      });\n    }\n  }, {\n    key: \"renderNode\",\n    value: function renderNode(root, node, i) {\n      var _this3 = this;\n      var _this$props7 = this.props,\n        content = _this$props7.content,\n        type = _this$props7.type;\n      var nodeProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props)), node), {}, {\n        root: root\n      });\n      var isLeaf = !node.children || !node.children.length;\n      var currentRoot = this.state.currentRoot;\n      var isCurrentRootChild = (currentRoot.children || []).filter(function (item) {\n        return item.depth === node.depth && item.name === node.name;\n      });\n      if (!isCurrentRootChild.length && root.depth && type === 'nest') {\n        return null;\n      }\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        React.createElement(Layer, {\n          key: \"recharts-treemap-node-\".concat(i),\n          className: \"recharts-treemap-depth-\".concat(node.depth)\n        }, this.renderItem(content, nodeProps, isLeaf), node.children && node.children.length ? node.children.map(function (child, index) {\n          return _this3.renderNode(node, child, index);\n        }) : null)\n      );\n    }\n  }, {\n    key: \"renderAllNodes\",\n    value: function renderAllNodes() {\n      var formatRoot = this.state.formatRoot;\n      if (!formatRoot) {\n        return null;\n      }\n      return this.renderNode(formatRoot, formatRoot, 0);\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props8 = this.props,\n        children = _this$props8.children,\n        nameKey = _this$props8.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$props9 = this.props,\n        width = _this$props9.width,\n        height = _this$props9.height;\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeNode = _this$state.activeNode;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeNode ? {\n        x: activeNode.x + activeNode.width / 2,\n        y: activeNode.y + activeNode.height / 2\n      } : null;\n      var payload = isTooltipActive && activeNode ? [{\n        payload: activeNode,\n        name: getValueByDataKey(activeNode, nameKey, ''),\n        value: getValueByDataKey(activeNode, NODE_VALUE_KEY)\n      }] : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n\n    // render nest treemap\n  }, {\n    key: \"renderNestIndex\",\n    value: function renderNestIndex() {\n      var _this4 = this;\n      var _this$props10 = this.props,\n        nameKey = _this$props10.nameKey,\n        nestIndexContent = _this$props10.nestIndexContent;\n      var nestIndex = this.state.nestIndex;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-treemap-nest-index-wrapper\",\n        style: {\n          marginTop: '8px',\n          textAlign: 'center'\n        }\n      }, nestIndex.map(function (item, i) {\n        // TODO need to verify nameKey type\n        var name = _get(item, nameKey, 'root');\n        var content = null;\n        if ( /*#__PURE__*/React.isValidElement(nestIndexContent)) {\n          content = /*#__PURE__*/React.cloneElement(nestIndexContent, item, i);\n        }\n        if (_isFunction(nestIndexContent)) {\n          content = nestIndexContent(item, i);\n        } else {\n          content = name;\n        }\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions\n          React.createElement(\"div\", {\n            onClick: _this4.handleNestIndex.bind(_this4, item, i),\n            key: \"nest-index-\".concat(uniqueId()),\n            className: \"recharts-treemap-nest-index-box\",\n            style: {\n              cursor: 'pointer',\n              display: 'inline-block',\n              padding: '0 7px',\n              background: '#000',\n              color: '#fff',\n              marginRight: '3px'\n            }\n          }, content)\n        );\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props11 = this.props,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        className = _this$props11.className,\n        style = _this$props11.style,\n        children = _this$props11.children,\n        type = _this$props11.type,\n        others = _objectWithoutProperties(_this$props11, _excluded);\n      var attrs = filterProps(others);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: type === 'nest' ? height - 30 : height\n      }), this.renderAllNodes(), filterSvgElements(children)), this.renderTooltip(), type === 'nest' && this.renderNestIndex());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.data !== prevState.prevData || nextProps.type !== prevState.prevType || nextProps.width !== prevState.prevWidth || nextProps.height !== prevState.prevHeight || nextProps.dataKey !== prevState.prevDataKey || nextProps.aspectRatio !== prevState.prevAspectRatio) {\n        var root = computeNode({\n          depth: 0,\n          node: {\n            children: nextProps.data,\n            x: 0,\n            y: 0,\n            width: nextProps.width,\n            height: nextProps.height\n          },\n          index: 0,\n          valueKey: nextProps.dataKey\n        });\n        var formatRoot = squarify(root, nextProps.aspectRatio);\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          formatRoot: formatRoot,\n          currentRoot: root,\n          nestIndex: [root],\n          prevAspectRatio: nextProps.aspectRatio,\n          prevData: nextProps.data,\n          prevWidth: nextProps.width,\n          prevHeight: nextProps.height,\n          prevDataKey: nextProps.dataKey,\n          prevType: nextProps.type\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderContentItem\",\n    value: function renderContentItem(content, nodeProps, type, colorPanel) {\n      if ( /*#__PURE__*/React.isValidElement(content)) {\n        return /*#__PURE__*/React.cloneElement(content, nodeProps);\n      }\n      if (_isFunction(content)) {\n        return content(nodeProps);\n      }\n      // optimize default shape\n      var x = nodeProps.x,\n        y = nodeProps.y,\n        width = nodeProps.width,\n        height = nodeProps.height,\n        index = nodeProps.index;\n      var arrow = null;\n      if (width > 10 && height > 10 && nodeProps.children && type === 'nest') {\n        arrow = /*#__PURE__*/React.createElement(Polygon, {\n          points: [{\n            x: x + 2,\n            y: y + height / 2\n          }, {\n            x: x + 6,\n            y: y + height / 2 + 3\n          }, {\n            x: x + 2,\n            y: y + height / 2 + 6\n          }]\n        });\n      }\n      var text = null;\n      var nameSize = getStringSize(nodeProps.name);\n      if (width > 20 && height > 20 && nameSize.width < width && nameSize.height < height) {\n        text = /*#__PURE__*/React.createElement(\"text\", {\n          x: x + 8,\n          y: y + height / 2 + 7,\n          fontSize: 14\n        }, nodeProps.name);\n      }\n      var colors = colorPanel || COLOR_PANEL;\n      return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(Rectangle, _extends({\n        fill: nodeProps.depth < 2 ? colors[index % colors.length] : 'rgba(255,255,255,0)',\n        stroke: \"#fff\"\n      }, _omit(nodeProps, 'children'), {\n        role: \"img\"\n      })), arrow, text);\n    }\n  }]);\n  return Treemap;\n}(PureComponent);\n_defineProperty(Treemap, \"displayName\", 'Treemap');\n_defineProperty(Treemap, \"defaultProps\", {\n  aspectRatio: 0.5 * (1 + Math.sqrt(5)),\n  dataKey: 'value',\n  type: 'flat',\n  isAnimationActive: !Global.isSsr,\n  isUpdateAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'linear'\n});"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,IAAIC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;AAC7E,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,wBAAwBA,CAACL,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGW,6BAA6B,CAACP,MAAM,EAAEM,QAAQ,CAAC;EAAE,IAAIL,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGhB,MAAM,CAACe,qBAAqB,CAACR,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,gBAAgB,CAACV,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGQ,gBAAgB,CAACZ,CAAC,CAAC;MAAE,IAAIS,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACoB,oBAAoB,CAACR,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASW,6BAA6BA,CAACP,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIgB,UAAU,GAAGnB,MAAM,CAACoB,IAAI,CAACb,MAAM,CAAC;EAAE,IAAIC,GAAG,EAAEJ,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,UAAU,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAEI,GAAG,GAAGW,UAAU,CAACf,CAAC,CAAC;IAAE,IAAIS,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AAClT,SAASkB,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACtB,MAAM,EAAEuB,KAAK,EAAE;EAAE,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,KAAK,CAACpB,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIuB,UAAU,GAAGD,KAAK,CAACtB,CAAC,CAAC;IAAEuB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAE9B,MAAM,CAAC+B,cAAc,CAAC5B,MAAM,EAAE6B,cAAc,CAACL,UAAU,CAACnB,GAAG,CAAC,EAAEmB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACF,WAAW,CAACzB,SAAS,EAAEoC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACF,WAAW,EAAEY,WAAW,CAAC;EAAEnC,MAAM,CAAC+B,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASa,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAId,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEa,QAAQ,CAACvC,SAAS,GAAGE,MAAM,CAACuC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxC,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE2C,KAAK,EAAEH,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE7B,MAAM,CAAC+B,cAAc,CAACM,QAAQ,EAAE,WAAW,EAAE;IAAEP,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIQ,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGzC,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAAC4C,cAAc,CAAC1C,IAAI,CAAC,CAAC,GAAG,SAASuC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACvD,WAAW;MAAEwD,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE9C,SAAS,EAAEiD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACxC,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAOoD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEhD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACjE,SAAS,CAACkE,OAAO,CAACtD,IAAI,CAAC6C,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAGpD,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAACkE,cAAc,CAAChE,IAAI,CAAC,CAAC,GAAG,SAASkD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAI7C,MAAM,CAACkE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASyB,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIjD,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACgD,MAAM,CAAC;EAAE,IAAIpE,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIuD,OAAO,GAAGtE,MAAM,CAACe,qBAAqB,CAACqD,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOxE,MAAM,CAACyE,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAAC5C,UAAU;IAAE,CAAC,CAAC,CAAC,EAAER,IAAI,CAACsD,IAAI,CAAC/D,KAAK,CAACS,IAAI,EAAEkD,OAAO,CAAC;EAAE;EAAE,OAAOlD,IAAI;AAAE;AACpV,SAASuD,aAAaA,CAACxE,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG+D,OAAO,CAACnE,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACqE,OAAO,CAAC,UAAUpE,GAAG,EAAE;MAAEqE,eAAe,CAAC1E,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC8E,yBAAyB,GAAG9E,MAAM,CAAC+E,gBAAgB,CAAC5E,MAAM,EAAEH,MAAM,CAAC8E,yBAAyB,CAACvE,MAAM,CAAC,CAAC,GAAG4D,OAAO,CAACnE,MAAM,CAACO,MAAM,CAAC,CAAC,CAACqE,OAAO,CAAC,UAAUpE,GAAG,EAAE;MAAER,MAAM,CAAC+B,cAAc,CAAC5B,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACyE,wBAAwB,CAAClE,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAAS0E,eAAeA,CAACnF,GAAG,EAAEc,GAAG,EAAEgC,KAAK,EAAE;EAAEhC,GAAG,GAAGwB,cAAc,CAACxB,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC+B,cAAc,CAACrC,GAAG,EAAEc,GAAG,EAAE;MAAEgC,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEpC,GAAG,CAACc,GAAG,CAAC,GAAGgC,KAAK;EAAE;EAAE,OAAO9C,GAAG;AAAE;AAC3O,SAASsC,cAAcA,CAACgD,GAAG,EAAE;EAAE,IAAIxE,GAAG,GAAGyE,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOvF,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAG0E,MAAM,CAAC1E,GAAG,CAAC;AAAE;AAC5H,SAASyE,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI3F,OAAO,CAAC0F,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACxF,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC3E,IAAI,CAACyE,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI3F,OAAO,CAAC+F,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIhE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC4D,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,OAAOO,UAAU,MAAM,YAAY;AACnC;AACA;AACA;AACA,OAAOC,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,oBAAoB;AACzG,IAAIC,cAAc,GAAG,OAAO;AAC5B,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,cAAc;EAClB,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpBC,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;EAC1B,IAAIC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,UAAU,GAAGL,KAAK,GAAG,CAAC;EAC1B,IAAIM,gBAAgB,GAAGF,QAAQ,IAAIA,QAAQ,CAAC9G,MAAM,GAAG8G,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAEpH,CAAC,EAAE;IACpF,OAAOyG,WAAW,CAAC;MACjBG,KAAK,EAAEK,UAAU;MACjBJ,IAAI,EAAEO,KAAK;MACXN,KAAK,EAAE9G,CAAC;MACR+G,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,GAAG,IAAI;EACT,IAAIM,SAAS;EACb,IAAIL,QAAQ,IAAIA,QAAQ,CAAC9G,MAAM,EAAE;IAC/BmH,SAAS,GAAGH,gBAAgB,CAACI,MAAM,CAAC,UAAUrE,MAAM,EAAEmE,KAAK,EAAE;MAC3D,OAAOnE,MAAM,GAAGmE,KAAK,CAACZ,cAAc,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,MAAM;IACL;IACAa,SAAS,GAAGlI,MAAM,CAAC0H,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACE,QAAQ,CAAC;EAChF;EACA,OAAOxC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAGF,cAAc,GAAG;IAClEK,QAAQ,EAAEE;EACZ,CAAC,EAAEzC,eAAe,CAACkC,cAAc,EAAEH,cAAc,EAAEa,SAAS,CAAC,EAAE5C,eAAe,CAACkC,cAAc,EAAE,OAAO,EAAEC,KAAK,CAAC,EAAEnC,eAAe,CAACkC,cAAc,EAAE,OAAO,EAAEG,KAAK,CAAC,EAAEH,cAAc,CAAC,CAAC;AACnL,CAAC;AACD,IAAIY,UAAU,GAAG,SAASA,UAAUA,CAACV,IAAI,EAAE;EACzC,OAAO;IACLW,CAAC,EAAEX,IAAI,CAACW,CAAC;IACTC,CAAC,EAAEZ,IAAI,CAACY,CAAC;IACTC,KAAK,EAAEb,IAAI,CAACa,KAAK;IACjBC,MAAM,EAAEd,IAAI,CAACc;EACf,CAAC;AACH,CAAC;;AAED;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACZ,QAAQ,EAAEa,cAAc,EAAE;EAC3E,IAAIC,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc;EACnD,OAAOb,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;IACnC,IAAIW,IAAI,GAAGX,KAAK,CAACZ,cAAc,CAAC,GAAGsB,KAAK;IACxC,OAAOvD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDW,IAAI,EAAE5I,MAAM,CAAC4I,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,GAAG,CAAC,GAAGA;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACvE,IAAIC,UAAU,GAAGF,UAAU,GAAGA,UAAU;EACxC,IAAIG,OAAO,GAAGJ,GAAG,CAACF,IAAI,GAAGE,GAAG,CAACF,IAAI;EACjC,IAAIO,WAAW,GAAGL,GAAG,CAACX,MAAM,CAAC,UAAUrE,MAAM,EAAEmE,KAAK,EAAE;MAClD,OAAO;QACLmB,GAAG,EAAEC,IAAI,CAACD,GAAG,CAACtF,MAAM,CAACsF,GAAG,EAAEnB,KAAK,CAACW,IAAI,CAAC;QACrCU,GAAG,EAAED,IAAI,CAACC,GAAG,CAACxF,MAAM,CAACwF,GAAG,EAAErB,KAAK,CAACW,IAAI;MACtC,CAAC;IACH,CAAC,EAAE;MACDQ,GAAG,EAAEG,QAAQ;MACbD,GAAG,EAAE;IACP,CAAC,CAAC;IACFF,GAAG,GAAGD,WAAW,CAACC,GAAG;IACrBE,GAAG,GAAGH,WAAW,CAACG,GAAG;EACvB,OAAOJ,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAGN,WAAW,GAAGE,OAAO,EAAEA,OAAO,IAAID,UAAU,GAAGG,GAAG,GAAGJ,WAAW,CAAC,CAAC,GAAGO,QAAQ;AAC5H,CAAC;AACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACV,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACzF,IAAIC,SAAS,GAAGZ,UAAU,GAAGM,IAAI,CAACO,KAAK,CAACd,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EAClE,IAAIW,OAAO,IAAIC,SAAS,GAAGF,UAAU,CAACjB,MAAM,EAAE;IAC5CmB,SAAS,GAAGF,UAAU,CAACjB,MAAM;EAC/B;EACA,IAAIqB,IAAI,GAAGJ,UAAU,CAACpB,CAAC;EACvB,IAAIJ,KAAK;EACT,KAAK,IAAI6B,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAGjB,GAAG,CAAC/H,MAAM,EAAE+I,EAAE,GAAGC,GAAG,EAAED,EAAE,EAAE,EAAE;IACjD7B,KAAK,GAAGa,GAAG,CAACgB,EAAE,CAAC;IACf7B,KAAK,CAACI,CAAC,GAAGwB,IAAI;IACd5B,KAAK,CAACK,CAAC,GAAGmB,UAAU,CAACnB,CAAC;IACtBL,KAAK,CAACO,MAAM,GAAGmB,SAAS;IACxB1B,KAAK,CAACM,KAAK,GAAGc,IAAI,CAACD,GAAG,CAACO,SAAS,GAAGN,IAAI,CAACO,KAAK,CAAC3B,KAAK,CAACW,IAAI,GAAGe,SAAS,CAAC,GAAG,CAAC,EAAEF,UAAU,CAACpB,CAAC,GAAGoB,UAAU,CAAClB,KAAK,GAAGsB,IAAI,CAAC;IAClHA,IAAI,IAAI5B,KAAK,CAACM,KAAK;EACrB;EACA;EACAN,KAAK,CAACM,KAAK,IAAIkB,UAAU,CAACpB,CAAC,GAAGoB,UAAU,CAAClB,KAAK,GAAGsB,IAAI;EACrD,OAAOzE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDnB,CAAC,EAAEmB,UAAU,CAACnB,CAAC,GAAGqB,SAAS;IAC3BnB,MAAM,EAAEiB,UAAU,CAACjB,MAAM,GAAGmB;EAC9B,CAAC,CAAC;AACJ,CAAC;AACD,IAAIK,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACrF,IAAIO,QAAQ,GAAGlB,UAAU,GAAGM,IAAI,CAACO,KAAK,CAACd,GAAG,CAACF,IAAI,GAAGG,UAAU,CAAC,GAAG,CAAC;EACjE,IAAIW,OAAO,IAAIO,QAAQ,GAAGR,UAAU,CAAClB,KAAK,EAAE;IAC1C0B,QAAQ,GAAGR,UAAU,CAAClB,KAAK;EAC7B;EACA,IAAI2B,IAAI,GAAGT,UAAU,CAACnB,CAAC;EACvB,IAAIL,KAAK;EACT,KAAK,IAAIkC,GAAG,GAAG,CAAC,EAAEJ,GAAG,GAAGjB,GAAG,CAAC/H,MAAM,EAAEoJ,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;IACpDlC,KAAK,GAAGa,GAAG,CAACqB,GAAG,CAAC;IAChBlC,KAAK,CAACI,CAAC,GAAGoB,UAAU,CAACpB,CAAC;IACtBJ,KAAK,CAACK,CAAC,GAAG4B,IAAI;IACdjC,KAAK,CAACM,KAAK,GAAG0B,QAAQ;IACtBhC,KAAK,CAACO,MAAM,GAAGa,IAAI,CAACD,GAAG,CAACa,QAAQ,GAAGZ,IAAI,CAACO,KAAK,CAAC3B,KAAK,CAACW,IAAI,GAAGqB,QAAQ,CAAC,GAAG,CAAC,EAAER,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,MAAM,GAAG0B,IAAI,CAAC;IAClHA,IAAI,IAAIjC,KAAK,CAACO,MAAM;EACtB;EACA,IAAIP,KAAK,EAAE;IACTA,KAAK,CAACO,MAAM,IAAIiB,UAAU,CAACnB,CAAC,GAAGmB,UAAU,CAACjB,MAAM,GAAG0B,IAAI;EACzD;EACA,OAAO9E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IACtDpB,CAAC,EAAEoB,UAAU,CAACpB,CAAC,GAAG4B,QAAQ;IAC1B1B,KAAK,EAAEkB,UAAU,CAAClB,KAAK,GAAG0B;EAC5B,CAAC,CAAC;AACJ,CAAC;AACD,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACtB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,EAAE;EACrE,IAAIX,UAAU,KAAKU,UAAU,CAAClB,KAAK,EAAE;IACnC,OAAOiB,kBAAkB,CAACV,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,CAAC;EACjE;EACA,OAAOM,gBAAgB,CAAClB,GAAG,EAAEC,UAAU,EAAEU,UAAU,EAAEC,OAAO,CAAC;AAC/D,CAAC;;AAED;AACA,IAAIW,QAAQ,GAAG,SAASA,QAAQA,CAAC3C,IAAI,EAAEsB,WAAW,EAAE;EAClD,IAAInB,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIA,QAAQ,IAAIA,QAAQ,CAAC9G,MAAM,EAAE;IAC/B,IAAIuJ,IAAI,GAAGlC,UAAU,CAACV,IAAI,CAAC;IAC3B;IACA,IAAIoB,GAAG,GAAG,EAAE;IACZ,IAAIyB,IAAI,GAAGhB,QAAQ,CAAC,CAAC;IACrB,IAAItB,KAAK,EAAEuC,KAAK,CAAC,CAAC;IAClB,IAAIC,IAAI,GAAGpB,IAAI,CAACD,GAAG,CAACkB,IAAI,CAAC/B,KAAK,EAAE+B,IAAI,CAAC9B,MAAM,CAAC,CAAC,CAAC;IAC9C,IAAIkC,aAAa,GAAGjC,iBAAiB,CAACZ,QAAQ,EAAEyC,IAAI,CAAC/B,KAAK,GAAG+B,IAAI,CAAC9B,MAAM,GAAGd,IAAI,CAACL,cAAc,CAAC,CAAC;IAChG,IAAIsD,YAAY,GAAGD,aAAa,CAACE,KAAK,CAAC,CAAC;IACxC9B,GAAG,CAACF,IAAI,GAAG,CAAC;IACZ,OAAO+B,YAAY,CAAC5J,MAAM,GAAG,CAAC,EAAE;MAC9B;MACA;MACA+H,GAAG,CAAC3D,IAAI,CAAC8C,KAAK,GAAG0C,YAAY,CAAC,CAAC,CAAC,CAAC;MACjC7B,GAAG,CAACF,IAAI,IAAIX,KAAK,CAACW,IAAI;MACtB4B,KAAK,GAAG3B,aAAa,CAACC,GAAG,EAAE2B,IAAI,EAAEzB,WAAW,CAAC;MAC7C,IAAIwB,KAAK,IAAID,IAAI,EAAE;QACjB;QACAI,YAAY,CAACE,KAAK,CAAC,CAAC;QACpBN,IAAI,GAAGC,KAAK;MACd,CAAC,MAAM;QACL;QACA1B,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACgC,GAAG,CAAC,CAAC,CAAClC,IAAI;QAC1B0B,IAAI,GAAGF,QAAQ,CAACtB,GAAG,EAAE2B,IAAI,EAAEH,IAAI,EAAE,KAAK,CAAC;QACvCG,IAAI,GAAGpB,IAAI,CAACD,GAAG,CAACkB,IAAI,CAAC/B,KAAK,EAAE+B,IAAI,CAAC9B,MAAM,CAAC;QACxCM,GAAG,CAAC/H,MAAM,GAAG+H,GAAG,CAACF,IAAI,GAAG,CAAC;QACzB2B,IAAI,GAAGhB,QAAQ;MACjB;IACF;IACA,IAAIT,GAAG,CAAC/H,MAAM,EAAE;MACduJ,IAAI,GAAGF,QAAQ,CAACtB,GAAG,EAAE2B,IAAI,EAAEH,IAAI,EAAE,IAAI,CAAC;MACtCxB,GAAG,CAAC/H,MAAM,GAAG+H,GAAG,CAACF,IAAI,GAAG,CAAC;IAC3B;IACA,OAAOxD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDG,QAAQ,EAAE6C,aAAa,CAAC1C,GAAG,CAAC,UAAU+C,CAAC,EAAE;QACvC,OAAOV,QAAQ,CAACU,CAAC,EAAE/B,WAAW,CAAC;MACjC,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOtB,IAAI;AACb,CAAC;AACD,IAAIsD,YAAY,GAAG;EACjBC,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE,KAAK;EAC1BC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,IAAIC,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC1D3I,SAAS,CAAC0I,OAAO,EAAEC,cAAc,CAAC;EAClC,IAAIC,MAAM,GAAGlI,YAAY,CAACgI,OAAO,CAAC;EAClC,SAASA,OAAOA,CAAA,EAAG;IACjB,IAAIG,KAAK;IACT5J,eAAe,CAAC,IAAI,EAAEyJ,OAAO,CAAC;IAC9B,KAAK,IAAII,IAAI,GAAG7K,SAAS,CAACC,MAAM,EAAE6K,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGhL,SAAS,CAACgL,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGD,MAAM,CAACtK,IAAI,CAACC,KAAK,CAACqK,MAAM,EAAE,CAAC,IAAI,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;IACtDtG,eAAe,CAAClB,sBAAsB,CAACsH,KAAK,CAAC,EAAE,OAAO,EAAEtG,aAAa,CAAC,CAAC,CAAC,EAAE4F,YAAY,CAAC,CAAC;IACxF1F,eAAe,CAAClB,sBAAsB,CAACsH,KAAK,CAAC,EAAE,oBAAoB,EAAE,YAAY;MAC/E,IAAIM,cAAc,GAAGN,KAAK,CAACvJ,KAAK,CAAC6J,cAAc;MAC/CN,KAAK,CAACO,QAAQ,CAAC;QACbf,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAInL,WAAW,CAACiM,cAAc,CAAC,EAAE;QAC/BA,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF1G,eAAe,CAAClB,sBAAsB,CAACsH,KAAK,CAAC,EAAE,sBAAsB,EAAE,YAAY;MACjF,IAAIQ,gBAAgB,GAAGR,KAAK,CAACvJ,KAAK,CAAC+J,gBAAgB;MACnDR,KAAK,CAACO,QAAQ,CAAC;QACbf,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAInL,WAAW,CAACmM,gBAAgB,CAAC,EAAE;QACjCA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAOR,KAAK;EACd;EACAhJ,YAAY,CAAC6I,OAAO,EAAE,CAAC;IACrBtK,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASkJ,gBAAgBA,CAACzE,IAAI,EAAEhD,CAAC,EAAE;MACxC,IAAI0H,WAAW,GAAG,IAAI,CAACjK,KAAK;QAC1BkK,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCxE,QAAQ,GAAGuE,WAAW,CAACvE,QAAQ;MACjC,IAAIyE,WAAW,GAAGpF,eAAe,CAACW,QAAQ,EAAEtB,OAAO,CAAC;MACpD,IAAI+F,WAAW,EAAE;QACf,IAAI,CAACL,QAAQ,CAAC;UACZhB,eAAe,EAAE,IAAI;UACrBE,UAAU,EAAEzD;QACd,CAAC,EAAE,YAAY;UACb,IAAI2E,YAAY,EAAE;YAChBA,YAAY,CAAC3E,IAAI,EAAEhD,CAAC,CAAC;UACvB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI2H,YAAY,EAAE;QACvBA,YAAY,CAAC3E,IAAI,EAAEhD,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDzD,GAAG,EAAE,kBAAkB;IACvBgC,KAAK,EAAE,SAASsJ,gBAAgBA,CAAC7E,IAAI,EAAEhD,CAAC,EAAE;MACxC,IAAI8H,YAAY,GAAG,IAAI,CAACrK,KAAK;QAC3BsK,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxC5E,QAAQ,GAAG2E,YAAY,CAAC3E,QAAQ;MAClC,IAAIyE,WAAW,GAAGpF,eAAe,CAACW,QAAQ,EAAEtB,OAAO,CAAC;MACpD,IAAI+F,WAAW,EAAE;QACf,IAAI,CAACL,QAAQ,CAAC;UACZhB,eAAe,EAAE,KAAK;UACtBE,UAAU,EAAE;QACd,CAAC,EAAE,YAAY;UACb,IAAIsB,YAAY,EAAE;YAChBA,YAAY,CAAC/E,IAAI,EAAEhD,CAAC,CAAC;UACvB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI+H,YAAY,EAAE;QACvBA,YAAY,CAAC/E,IAAI,EAAEhD,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE;IACDzD,GAAG,EAAE,aAAa;IAClBgC,KAAK,EAAE,SAASyJ,WAAWA,CAAChF,IAAI,EAAE;MAChC,IAAIiF,YAAY,GAAG,IAAI,CAACxK,KAAK;QAC3ByK,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,IAAI,GAAGF,YAAY,CAACE,IAAI;MAC1B,IAAIA,IAAI,KAAK,MAAM,IAAInF,IAAI,CAACG,QAAQ,EAAE;QACpC,IAAIiF,YAAY,GAAG,IAAI,CAAC3K,KAAK;UAC3BoG,KAAK,GAAGuE,YAAY,CAACvE,KAAK;UAC1BC,MAAM,GAAGsE,YAAY,CAACtE,MAAM;UAC5BuE,OAAO,GAAGD,YAAY,CAACC,OAAO;UAC9B/D,WAAW,GAAG8D,YAAY,CAAC9D,WAAW;QACxC,IAAIgE,IAAI,GAAG1F,WAAW,CAAC;UACrBG,KAAK,EAAE,CAAC;UACRC,IAAI,EAAEtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/CW,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC,CAAC;UACFb,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAEmF;QACZ,CAAC,CAAC;QACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAEhE,WAAW,CAAC;QAC5C,IAAIsC,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;QACpCA,SAAS,CAACnG,IAAI,CAACuC,IAAI,CAAC;QACpB,IAAI,CAACuE,QAAQ,CAAC;UACZb,UAAU,EAAEA,UAAU;UACtBC,WAAW,EAAE2B,IAAI;UACjB1B,SAAS,EAAEA;QACb,CAAC,CAAC;MACJ;MACA,IAAIsB,OAAO,EAAE;QACXA,OAAO,CAAClF,IAAI,CAAC;MACf;IACF;EACF,CAAC,EAAE;IACDzG,GAAG,EAAE,iBAAiB;IACtBgC,KAAK,EAAE,SAASiK,eAAeA,CAACxF,IAAI,EAAE7G,CAAC,EAAE;MACvC,IAAIyK,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;MACpC,IAAI6B,YAAY,GAAG,IAAI,CAAChL,KAAK;QAC3BoG,KAAK,GAAG4E,YAAY,CAAC5E,KAAK;QAC1BC,MAAM,GAAG2E,YAAY,CAAC3E,MAAM;QAC5BuE,OAAO,GAAGI,YAAY,CAACJ,OAAO;QAC9B/D,WAAW,GAAGmE,YAAY,CAACnE,WAAW;MACxC,IAAIgE,IAAI,GAAG1F,WAAW,CAAC;QACrBG,KAAK,EAAE,CAAC;QACRC,IAAI,EAAEtC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC/CW,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC,CAAC;QACFb,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAEmF;MACZ,CAAC,CAAC;MACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAEhE,WAAW,CAAC;MAC5CsC,SAAS,GAAGA,SAAS,CAACV,KAAK,CAAC,CAAC,EAAE/J,CAAC,GAAG,CAAC,CAAC;MACrC,IAAI,CAACoL,QAAQ,CAAC;QACZb,UAAU,EAAEA,UAAU;QACtBC,WAAW,EAAE3D,IAAI;QACjB4D,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrK,GAAG,EAAE,YAAY;IACjBgC,KAAK,EAAE,SAASmK,UAAUA,CAACC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAE;MACrD,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACtL,KAAK;QAC3BuL,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;QAClDC,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,uBAAuB,GAAGL,YAAY,CAACK,uBAAuB;QAC9DjB,IAAI,GAAGY,YAAY,CAACZ,IAAI;QACxBkB,WAAW,GAAGN,YAAY,CAACM,WAAW;QACtCC,UAAU,GAAGP,YAAY,CAACO,UAAU;MACtC,IAAI9C,mBAAmB,GAAG,IAAI,CAAC+B,KAAK,CAAC/B,mBAAmB;MACxD,IAAI3C,KAAK,GAAG+E,SAAS,CAAC/E,KAAK;QACzBC,MAAM,GAAG8E,SAAS,CAAC9E,MAAM;QACzBH,CAAC,GAAGiF,SAAS,CAACjF,CAAC;QACfC,CAAC,GAAGgF,SAAS,CAAChF,CAAC;QACfb,KAAK,GAAG6F,SAAS,CAAC7F,KAAK;MACzB,IAAIwG,UAAU,GAAGC,QAAQ,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC1C,IAAI,CAAC8E,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI5F,KAAK,CAAC,EAAE,EAAE,CAAC;MACzE,IAAI6F,KAAK,GAAG,CAAC,CAAC;MACd,IAAIb,MAAM,IAAIV,IAAI,KAAK,MAAM,EAAE;QAC7BuB,KAAK,GAAG;UACN/B,YAAY,EAAE,IAAI,CAACF,gBAAgB,CAACxL,IAAI,CAAC,IAAI,EAAE2M,SAAS,CAAC;UACzDb,YAAY,EAAE,IAAI,CAACF,gBAAgB,CAAC5L,IAAI,CAAC,IAAI,EAAE2M,SAAS,CAAC;UACzDV,OAAO,EAAE,IAAI,CAACF,WAAW,CAAC/L,IAAI,CAAC,IAAI,EAAE2M,SAAS;QAChD,CAAC;MACH;MACA,IAAI,CAACI,iBAAiB,EAAE;QACtB,OAAO,aAAatH,KAAK,CAACiI,aAAa,CAAC7H,KAAK,EAAE4H,KAAK,EAAE,IAAI,CAAC9N,WAAW,CAACgO,iBAAiB,CAACjB,OAAO,EAAEjI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkI,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAChJI,iBAAiB,EAAE,KAAK;UACxBI,uBAAuB,EAAE,KAAK;UAC9BvF,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdH,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA;QACL,CAAC,CAAC,EAAEuE,IAAI,EAAEmB,UAAU,CAAC,CAAC;MACxB;MACA,OAAO,aAAa5H,KAAK,CAACiI,aAAa,CAAC/H,MAAM,EAAE;QAC9CiI,KAAK,EAAEZ,cAAc;QACrBa,QAAQ,EAAEZ,iBAAiB;QAC3Ba,QAAQ,EAAEf,iBAAiB;QAC3BgB,MAAM,EAAEb,eAAe;QACvB5M,GAAG,EAAE,UAAU,CAAC8K,MAAM,CAACgC,WAAW,CAAC;QACnCY,IAAI,EAAE;UACJtG,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC;QACDoG,EAAE,EAAE;UACFvG,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA,CAAC;UACJC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC;QACD0D,gBAAgB,EAAE,IAAI,CAAC2C,oBAAoB;QAC3C7C,cAAc,EAAE,IAAI,CAAC8C;MACvB,CAAC,EAAE,UAAUC,KAAK,EAAE;QAClB,IAAIC,KAAK,GAAGD,KAAK,CAAC1G,CAAC;UACjB4G,KAAK,GAAGF,KAAK,CAACzG,CAAC;UACf4G,SAAS,GAAGH,KAAK,CAACxG,KAAK;UACvB4G,UAAU,GAAGJ,KAAK,CAACvG,MAAM;QAC3B,OAAO,aAAapC,KAAK,CAACiI,aAAa,CAAC/H,MAAM,EAAE;UAC9CqI,IAAI,EAAE,YAAY,CAAC5C,MAAM,CAACkC,UAAU,EAAE,MAAM,CAAC,CAAClC,MAAM,CAACkC,UAAU,EAAE,KAAK,CAAC;UACvEW,EAAE,EAAE,iBAAiB;UACrBQ,aAAa,EAAE,WAAW;UAC1Bb,KAAK,EAAEZ,cAAc;UACrBe,MAAM,EAAEb,eAAe;UACvBY,QAAQ,EAAEf,iBAAiB;UAC3Bc,QAAQ,EAAEZ;QACZ,CAAC,EAAE,aAAaxH,KAAK,CAACiI,aAAa,CAAC7H,KAAK,EAAE4H,KAAK,EAAE,YAAY;UAC5D;UACA,IAAI3G,KAAK,GAAG,CAAC,IAAI,CAACyD,mBAAmB,EAAE;YACrC,OAAO,IAAI;UACb;UACA,OAAOsC,MAAM,CAAClN,WAAW,CAACgO,iBAAiB,CAACjB,OAAO,EAAEjI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkI,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;YACnGI,iBAAiB,EAAEA,iBAAiB;YACpCI,uBAAuB,EAAE,CAACA,uBAAuB;YACjDvF,KAAK,EAAE2G,SAAS;YAChB1G,MAAM,EAAE2G,UAAU;YAClB9G,CAAC,EAAE2G,KAAK;YACR1G,CAAC,EAAE2G;UACL,CAAC,CAAC,EAAEpC,IAAI,EAAEmB,UAAU,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/M,GAAG,EAAE,YAAY;IACjBgC,KAAK,EAAE,SAASoM,UAAUA,CAACrC,IAAI,EAAEtF,IAAI,EAAE7G,CAAC,EAAE;MACxC,IAAIyO,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACpN,KAAK;QAC3BkL,OAAO,GAAGkC,YAAY,CAAClC,OAAO;QAC9BR,IAAI,GAAG0C,YAAY,CAAC1C,IAAI;MAC1B,IAAIS,SAAS,GAAGlI,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,WAAW,CAAC,IAAI,CAACjF,KAAK,CAAC,CAAC,EAAEuF,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACjGsF,IAAI,EAAEA;MACR,CAAC,CAAC;MACF,IAAIO,MAAM,GAAG,CAAC7F,IAAI,CAACG,QAAQ,IAAI,CAACH,IAAI,CAACG,QAAQ,CAAC9G,MAAM;MACpD,IAAIsK,WAAW,GAAG,IAAI,CAAC4B,KAAK,CAAC5B,WAAW;MACxC,IAAImE,kBAAkB,GAAG,CAACnE,WAAW,CAACxD,QAAQ,IAAI,EAAE,EAAE7C,MAAM,CAAC,UAAUyK,IAAI,EAAE;QAC3E,OAAOA,IAAI,CAAChI,KAAK,KAAKC,IAAI,CAACD,KAAK,IAAIgI,IAAI,CAACC,IAAI,KAAKhI,IAAI,CAACgI,IAAI;MAC7D,CAAC,CAAC;MACF,IAAI,CAACF,kBAAkB,CAACzO,MAAM,IAAIiM,IAAI,CAACvF,KAAK,IAAIoF,IAAI,KAAK,MAAM,EAAE;QAC/D,OAAO,IAAI;MACb;MACA,QACE;QACA;QACAzG,KAAK,CAACiI,aAAa,CAAC7H,KAAK,EAAE;UACzBvF,GAAG,EAAE,wBAAwB,CAAC8K,MAAM,CAAClL,CAAC,CAAC;UACvC8O,SAAS,EAAE,yBAAyB,CAAC5D,MAAM,CAACrE,IAAI,CAACD,KAAK;QACxD,CAAC,EAAE,IAAI,CAAC2F,UAAU,CAACC,OAAO,EAAEC,SAAS,EAAEC,MAAM,CAAC,EAAE7F,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACG,QAAQ,CAAC9G,MAAM,GAAG2G,IAAI,CAACG,QAAQ,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAEN,KAAK,EAAE;UAChI,OAAO2H,MAAM,CAACD,UAAU,CAAC3H,IAAI,EAAEO,KAAK,EAAEN,KAAK,CAAC;QAC9C,CAAC,CAAC,GAAG,IAAI;MAAC;IAEd;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,gBAAgB;IACrBgC,KAAK,EAAE,SAAS2M,cAAcA,CAAA,EAAG;MAC/B,IAAIxE,UAAU,GAAG,IAAI,CAAC6B,KAAK,CAAC7B,UAAU;MACtC,IAAI,CAACA,UAAU,EAAE;QACf,OAAO,IAAI;MACb;MACA,OAAO,IAAI,CAACiE,UAAU,CAACjE,UAAU,EAAEA,UAAU,EAAE,CAAC,CAAC;IACnD;EACF,CAAC,EAAE;IACDnK,GAAG,EAAE,eAAe;IACpBgC,KAAK,EAAE,SAAS4M,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAAC3N,KAAK;QAC3B0F,QAAQ,GAAGiI,YAAY,CAACjI,QAAQ;QAChCkI,OAAO,GAAGD,YAAY,CAACC,OAAO;MAChC,IAAIzD,WAAW,GAAGpF,eAAe,CAACW,QAAQ,EAAEtB,OAAO,CAAC;MACpD,IAAI,CAAC+F,WAAW,EAAE;QAChB,OAAO,IAAI;MACb;MACA,IAAI0D,YAAY,GAAG,IAAI,CAAC7N,KAAK;QAC3BoG,KAAK,GAAGyH,YAAY,CAACzH,KAAK;QAC1BC,MAAM,GAAGwH,YAAY,CAACxH,MAAM;MAC9B,IAAIyH,WAAW,GAAG,IAAI,CAAChD,KAAK;QAC1BhC,eAAe,GAAGgF,WAAW,CAAChF,eAAe;QAC7CE,UAAU,GAAG8E,WAAW,CAAC9E,UAAU;MACrC,IAAI+E,OAAO,GAAG;QACZ7H,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;MACD,IAAI2H,UAAU,GAAGhF,UAAU,GAAG;QAC5B9C,CAAC,EAAE8C,UAAU,CAAC9C,CAAC,GAAG8C,UAAU,CAAC5C,KAAK,GAAG,CAAC;QACtCD,CAAC,EAAE6C,UAAU,CAAC7C,CAAC,GAAG6C,UAAU,CAAC3C,MAAM,GAAG;MACxC,CAAC,GAAG,IAAI;MACR,IAAI4H,OAAO,GAAGnF,eAAe,IAAIE,UAAU,GAAG,CAAC;QAC7CiF,OAAO,EAAEjF,UAAU;QACnBuE,IAAI,EAAE9I,iBAAiB,CAACuE,UAAU,EAAE4E,OAAO,EAAE,EAAE,CAAC;QAChD9M,KAAK,EAAE2D,iBAAiB,CAACuE,UAAU,EAAE9D,cAAc;MACrD,CAAC,CAAC,GAAG,EAAE;MACP,OAAO,aAAajB,KAAK,CAACiK,YAAY,CAAC/D,WAAW,EAAE;QAClD4D,OAAO,EAAEA,OAAO;QAChBI,MAAM,EAAErF,eAAe;QACvBkF,UAAU,EAAEA,UAAU;QACtBI,KAAK,EAAE,EAAE;QACTH,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;;IAEA;EACF,CAAC,EAAE;IACDnP,GAAG,EAAE,iBAAiB;IACtBgC,KAAK,EAAE,SAASuN,eAAeA,CAAA,EAAG;MAChC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,aAAa,GAAG,IAAI,CAACvO,KAAK;QAC5B4N,OAAO,GAAGW,aAAa,CAACX,OAAO;QAC/BY,gBAAgB,GAAGD,aAAa,CAACC,gBAAgB;MACnD,IAAIrF,SAAS,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,SAAS;MACpC,OAAO,aAAalF,KAAK,CAACiI,aAAa,CAAC,KAAK,EAAE;QAC7CsB,SAAS,EAAE,qCAAqC;QAChDiB,KAAK,EAAE;UACLC,SAAS,EAAE,KAAK;UAChBC,SAAS,EAAE;QACb;MACF,CAAC,EAAExF,SAAS,CAACtD,GAAG,CAAC,UAAUyH,IAAI,EAAE5O,CAAC,EAAE;QAClC;QACA,IAAI6O,IAAI,GAAG7P,IAAI,CAAC4P,IAAI,EAAEM,OAAO,EAAE,MAAM,CAAC;QACtC,IAAI1C,OAAO,GAAG,IAAI;QAClB,IAAK,aAAajH,KAAK,CAAC2K,cAAc,CAACJ,gBAAgB,CAAC,EAAE;UACxDtD,OAAO,GAAG,aAAajH,KAAK,CAACiK,YAAY,CAACM,gBAAgB,EAAElB,IAAI,EAAE5O,CAAC,CAAC;QACtE;QACA,IAAId,WAAW,CAAC4Q,gBAAgB,CAAC,EAAE;UACjCtD,OAAO,GAAGsD,gBAAgB,CAAClB,IAAI,EAAE5O,CAAC,CAAC;QACrC,CAAC,MAAM;UACLwM,OAAO,GAAGqC,IAAI;QAChB;QACA,QACE;UACA;UACAtJ,KAAK,CAACiI,aAAa,CAAC,KAAK,EAAE;YACzBzB,OAAO,EAAE6D,MAAM,CAACvD,eAAe,CAACvM,IAAI,CAAC8P,MAAM,EAAEhB,IAAI,EAAE5O,CAAC,CAAC;YACrDI,GAAG,EAAE,aAAa,CAAC8K,MAAM,CAACjF,QAAQ,CAAC,CAAC,CAAC;YACrC6I,SAAS,EAAE,iCAAiC;YAC5CiB,KAAK,EAAE;cACLI,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAE,cAAc;cACvBC,OAAO,EAAE,OAAO;cAChBC,UAAU,EAAE,MAAM;cAClBC,KAAK,EAAE,MAAM;cACbC,WAAW,EAAE;YACf;UACF,CAAC,EAAEhE,OAAO;QAAC;MAEf,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDpM,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASqO,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACnK,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAIoK,aAAa,GAAG,IAAI,CAACpP,KAAK;QAC5BoG,KAAK,GAAGgJ,aAAa,CAAChJ,KAAK;QAC3BC,MAAM,GAAG+I,aAAa,CAAC/I,MAAM;QAC7BmH,SAAS,GAAG4B,aAAa,CAAC5B,SAAS;QACnCiB,KAAK,GAAGW,aAAa,CAACX,KAAK;QAC3B/I,QAAQ,GAAG0J,aAAa,CAAC1J,QAAQ;QACjCgF,IAAI,GAAG0E,aAAa,CAAC1E,IAAI;QACzB2E,MAAM,GAAGnQ,wBAAwB,CAACkQ,aAAa,EAAEtR,SAAS,CAAC;MAC7D,IAAIwR,KAAK,GAAGrK,WAAW,CAACoK,MAAM,CAAC;MAC/B,OAAO,aAAapL,KAAK,CAACiI,aAAa,CAAC,KAAK,EAAE;QAC7CsB,SAAS,EAAExJ,UAAU,CAAC,kBAAkB,EAAEwJ,SAAS,CAAC;QACpDiB,KAAK,EAAExL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwL,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDxG,QAAQ,EAAE,UAAU;UACpB4G,MAAM,EAAE,SAAS;UACjBzI,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA;QACV,CAAC,CAAC;QACFkJ,IAAI,EAAE;MACR,CAAC,EAAE,aAAatL,KAAK,CAACiI,aAAa,CAAC5H,OAAO,EAAEjG,QAAQ,CAAC,CAAC,CAAC,EAAEiR,KAAK,EAAE;QAC/DlJ,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEqE,IAAI,KAAK,MAAM,GAAGrE,MAAM,GAAG,EAAE,GAAGA;MAC1C,CAAC,CAAC,EAAE,IAAI,CAACoH,cAAc,CAAC,CAAC,EAAE3I,iBAAiB,CAACY,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACgI,aAAa,CAAC,CAAC,EAAEhD,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC2D,eAAe,CAAC,CAAC,CAAC;IAC3H;EACF,CAAC,CAAC,EAAE,CAAC;IACHvP,GAAG,EAAE,0BAA0B;IAC/BgC,KAAK,EAAE,SAAS0O,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAACE,IAAI,KAAKD,SAAS,CAACE,QAAQ,IAAIH,SAAS,CAAC/E,IAAI,KAAKgF,SAAS,CAACG,QAAQ,IAAIJ,SAAS,CAACrJ,KAAK,KAAKsJ,SAAS,CAACI,SAAS,IAAIL,SAAS,CAACpJ,MAAM,KAAKqJ,SAAS,CAACK,UAAU,IAAIN,SAAS,CAAC7E,OAAO,KAAK8E,SAAS,CAACM,WAAW,IAAIP,SAAS,CAAC5I,WAAW,KAAK6I,SAAS,CAACO,eAAe,EAAE;QAChR,IAAIpF,IAAI,GAAG1F,WAAW,CAAC;UACrBG,KAAK,EAAE,CAAC;UACRC,IAAI,EAAE;YACJG,QAAQ,EAAE+J,SAAS,CAACE,IAAI;YACxBzJ,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAEqJ,SAAS,CAACrJ,KAAK;YACtBC,MAAM,EAAEoJ,SAAS,CAACpJ;UACpB,CAAC;UACDb,KAAK,EAAE,CAAC;UACRC,QAAQ,EAAEgK,SAAS,CAAC7E;QACtB,CAAC,CAAC;QACF,IAAI3B,UAAU,GAAGf,QAAQ,CAAC2C,IAAI,EAAE4E,SAAS,CAAC5I,WAAW,CAAC;QACtD,OAAO5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyM,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACrDzG,UAAU,EAAEA,UAAU;UACtBC,WAAW,EAAE2B,IAAI;UACjB1B,SAAS,EAAE,CAAC0B,IAAI,CAAC;UACjBoF,eAAe,EAAER,SAAS,CAAC5I,WAAW;UACtC+I,QAAQ,EAAEH,SAAS,CAACE,IAAI;UACxBG,SAAS,EAAEL,SAAS,CAACrJ,KAAK;UAC1B2J,UAAU,EAAEN,SAAS,CAACpJ,MAAM;UAC5B2J,WAAW,EAAEP,SAAS,CAAC7E,OAAO;UAC9BiF,QAAQ,EAAEJ,SAAS,CAAC/E;QACtB,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACD5L,GAAG,EAAE,mBAAmB;IACxBgC,KAAK,EAAE,SAASqL,iBAAiBA,CAACjB,OAAO,EAAEC,SAAS,EAAET,IAAI,EAAEmB,UAAU,EAAE;MACtE,IAAK,aAAa5H,KAAK,CAAC2K,cAAc,CAAC1D,OAAO,CAAC,EAAE;QAC/C,OAAO,aAAajH,KAAK,CAACiK,YAAY,CAAChD,OAAO,EAAEC,SAAS,CAAC;MAC5D;MACA,IAAIvN,WAAW,CAACsN,OAAO,CAAC,EAAE;QACxB,OAAOA,OAAO,CAACC,SAAS,CAAC;MAC3B;MACA;MACA,IAAIjF,CAAC,GAAGiF,SAAS,CAACjF,CAAC;QACjBC,CAAC,GAAGgF,SAAS,CAAChF,CAAC;QACfC,KAAK,GAAG+E,SAAS,CAAC/E,KAAK;QACvBC,MAAM,GAAG8E,SAAS,CAAC9E,MAAM;QACzBb,KAAK,GAAG2F,SAAS,CAAC3F,KAAK;MACzB,IAAI0K,KAAK,GAAG,IAAI;MAChB,IAAI9J,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAI8E,SAAS,CAACzF,QAAQ,IAAIgF,IAAI,KAAK,MAAM,EAAE;QACtEwF,KAAK,GAAG,aAAajM,KAAK,CAACiI,aAAa,CAAC3H,OAAO,EAAE;UAChD4L,MAAM,EAAE,CAAC;YACPjK,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG;UAClB,CAAC,EAAE;YACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;UACtB,CAAC,EAAE;YACDH,CAAC,EAAEA,CAAC,GAAG,CAAC;YACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG;UACtB,CAAC;QACH,CAAC,CAAC;MACJ;MACA,IAAI+J,IAAI,GAAG,IAAI;MACf,IAAIC,QAAQ,GAAGzL,aAAa,CAACuG,SAAS,CAACoC,IAAI,CAAC;MAC5C,IAAInH,KAAK,GAAG,EAAE,IAAIC,MAAM,GAAG,EAAE,IAAIgK,QAAQ,CAACjK,KAAK,GAAGA,KAAK,IAAIiK,QAAQ,CAAChK,MAAM,GAAGA,MAAM,EAAE;QACnF+J,IAAI,GAAG,aAAanM,KAAK,CAACiI,aAAa,CAAC,MAAM,EAAE;UAC9ChG,CAAC,EAAEA,CAAC,GAAG,CAAC;UACRC,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;UACrBiK,QAAQ,EAAE;QACZ,CAAC,EAAEnF,SAAS,CAACoC,IAAI,CAAC;MACpB;MACA,IAAIgD,MAAM,GAAG1E,UAAU,IAAInH,WAAW;MACtC,OAAO,aAAaT,KAAK,CAACiI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAajI,KAAK,CAACiI,aAAa,CAAC1H,SAAS,EAAEnG,QAAQ,CAAC;QACtGmS,IAAI,EAAErF,SAAS,CAAC7F,KAAK,GAAG,CAAC,GAAGiL,MAAM,CAAC/K,KAAK,GAAG+K,MAAM,CAAC3R,MAAM,CAAC,GAAG,qBAAqB;QACjF6R,MAAM,EAAE;MACV,CAAC,EAAE9S,KAAK,CAACwN,SAAS,EAAE,UAAU,CAAC,EAAE;QAC/BoE,IAAI,EAAE;MACR,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAEE,IAAI,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;EACH,OAAOhH,OAAO;AAChB,CAAC,CAAClF,aAAa,CAAC;AAChBf,eAAe,CAACiG,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClDjG,eAAe,CAACiG,OAAO,EAAE,cAAc,EAAE;EACvCvC,WAAW,EAAE,GAAG,IAAI,CAAC,GAAGK,IAAI,CAACwJ,IAAI,CAAC,CAAC,CAAC,CAAC;EACrC9F,OAAO,EAAE,OAAO;EAChBF,IAAI,EAAE,MAAM;EACZa,iBAAiB,EAAE,CAAC1G,MAAM,CAAC8L,KAAK;EAChChF,uBAAuB,EAAE,CAAC9G,MAAM,CAAC8L,KAAK;EACtCnF,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}