{"ast": null, "code": "import Group from './Group';\nimport InternalInput from './Input';\nimport Password from './Password';\nimport Search from './Search';\nimport TextArea from './TextArea';\nvar Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nexport default Input;", "map": {"version": 3, "names": ["Group", "InternalInput", "Password", "Search", "TextArea", "Input"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/input/index.js"], "sourcesContent": ["import Group from './Group';\nimport InternalInput from './Input';\nimport Password from './Password';\nimport Search from './Search';\nimport TextArea from './TextArea';\nvar Input = InternalInput;\nInput.Group = Group;\nInput.Search = Search;\nInput.TextArea = TextArea;\nInput.Password = Password;\nexport default Input;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,IAAIC,KAAK,GAAGJ,aAAa;AACzBI,KAAK,CAACL,KAAK,GAAGA,KAAK;AACnBK,KAAK,CAACF,MAAM,GAAGA,MAAM;AACrBE,KAAK,CAACD,QAAQ,GAAGA,QAAQ;AACzBC,KAAK,CAACH,QAAQ,GAAGA,QAAQ;AACzB,eAAeG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}