{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SummaryContext from './SummaryContext';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport { getCellFixedInfo } from '../utils/fixUtil';\nexport default function SummaryCell(_ref) {\n  var className = _ref.className,\n    index = _ref.index,\n    children = _ref.children,\n    _ref$colSpan = _ref.colSpan,\n    colSpan = _ref$colSpan === void 0 ? 1 : _ref$colSpan,\n    rowSpan = _ref.rowSpan,\n    align = _ref.align;\n  var _React$useContext = React.useContext(TableContext),\n    prefixCls = _React$useContext.prefixCls,\n    direction = _React$useContext.direction;\n  var _React$useContext2 = React.useContext(SummaryContext),\n    scrollColumnIndex = _React$useContext2.scrollColumnIndex,\n    stickyOffsets = _React$useContext2.stickyOffsets,\n    flattenColumns = _React$useContext2.flattenColumns;\n  var lastIndex = index + colSpan - 1;\n  var mergedColSpan = lastIndex + 1 === scrollColumnIndex ? colSpan + 1 : colSpan;\n  var fixedInfo = getCellFixedInfo(index, index + mergedColSpan - 1, flattenColumns, stickyOffsets, direction);\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: className,\n    index: index,\n    component: \"td\",\n    prefixCls: prefixCls,\n    record: null,\n    dataIndex: null,\n    align: align,\n    colSpan: mergedColSpan,\n    rowSpan: rowSpan,\n    render: function render() {\n      return children;\n    }\n  }, fixedInfo));\n}", "map": {"version": 3, "names": ["_extends", "React", "SummaryContext", "Cell", "TableContext", "getCellFixedInfo", "<PERSON><PERSON>ryCell", "_ref", "className", "index", "children", "_ref$colSpan", "colSpan", "rowSpan", "align", "_React$useContext", "useContext", "prefixCls", "direction", "_React$useContext2", "scrollColumnIndex", "stickyOffsets", "flattenColumns", "lastIndex", "mergedColSpan", "fixedInfo", "createElement", "component", "record", "dataIndex", "render"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/Footer/Cell.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SummaryContext from './SummaryContext';\nimport Cell from '../Cell';\nimport TableContext from '../context/TableContext';\nimport { getCellFixedInfo } from '../utils/fixUtil';\nexport default function SummaryCell(_ref) {\n  var className = _ref.className,\n      index = _ref.index,\n      children = _ref.children,\n      _ref$colSpan = _ref.colSpan,\n      colSpan = _ref$colSpan === void 0 ? 1 : _ref$colSpan,\n      rowSpan = _ref.rowSpan,\n      align = _ref.align;\n\n  var _React$useContext = React.useContext(TableContext),\n      prefixCls = _React$useContext.prefixCls,\n      direction = _React$useContext.direction;\n\n  var _React$useContext2 = React.useContext(SummaryContext),\n      scrollColumnIndex = _React$useContext2.scrollColumnIndex,\n      stickyOffsets = _React$useContext2.stickyOffsets,\n      flattenColumns = _React$useContext2.flattenColumns;\n\n  var lastIndex = index + colSpan - 1;\n  var mergedColSpan = lastIndex + 1 === scrollColumnIndex ? colSpan + 1 : colSpan;\n  var fixedInfo = getCellFixedInfo(index, index + mergedColSpan - 1, flattenColumns, stickyOffsets, direction);\n  return /*#__PURE__*/React.createElement(Cell, _extends({\n    className: className,\n    index: index,\n    component: \"td\",\n    prefixCls: prefixCls,\n    record: null,\n    dataIndex: null,\n    align: align,\n    colSpan: mergedColSpan,\n    rowSpan: rowSpan,\n    render: function render() {\n      return children;\n    }\n  }, fixedInfo));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,YAAY,GAAGJ,IAAI,CAACK,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IACpDE,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,KAAK,GAAGP,IAAI,CAACO,KAAK;EAEtB,IAAIC,iBAAiB,GAAGd,KAAK,CAACe,UAAU,CAACZ,YAAY,CAAC;IAClDa,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,IAAIC,kBAAkB,GAAGlB,KAAK,CAACe,UAAU,CAACd,cAAc,CAAC;IACrDkB,iBAAiB,GAAGD,kBAAkB,CAACC,iBAAiB;IACxDC,aAAa,GAAGF,kBAAkB,CAACE,aAAa;IAChDC,cAAc,GAAGH,kBAAkB,CAACG,cAAc;EAEtD,IAAIC,SAAS,GAAGd,KAAK,GAAGG,OAAO,GAAG,CAAC;EACnC,IAAIY,aAAa,GAAGD,SAAS,GAAG,CAAC,KAAKH,iBAAiB,GAAGR,OAAO,GAAG,CAAC,GAAGA,OAAO;EAC/E,IAAIa,SAAS,GAAGpB,gBAAgB,CAACI,KAAK,EAAEA,KAAK,GAAGe,aAAa,GAAG,CAAC,EAAEF,cAAc,EAAED,aAAa,EAAEH,SAAS,CAAC;EAC5G,OAAO,aAAajB,KAAK,CAACyB,aAAa,CAACvB,IAAI,EAAEH,QAAQ,CAAC;IACrDQ,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAEA,KAAK;IACZkB,SAAS,EAAE,IAAI;IACfV,SAAS,EAAEA,SAAS;IACpBW,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACff,KAAK,EAAEA,KAAK;IACZF,OAAO,EAAEY,aAAa;IACtBX,OAAO,EAAEA,OAAO;IAChBiB,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;MACxB,OAAOpB,QAAQ;IACjB;EACF,CAAC,EAAEe,SAAS,CAAC,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}