{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"prefixCls\", \"style\", \"className\", \"children\", \"direction\", \"type\", \"labelPlacement\", \"iconPrefix\", \"status\", \"size\", \"current\", \"progressDot\", \"stepIcon\", \"initial\", \"icons\", \"onChange\", \"items\"];\n\n/* eslint react/no-did-mount-set-state: 0, react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nimport Step from \"./Step\";\nvar Steps = /*#__PURE__*/function (_React$Component) {\n  _inherits(Steps, _React$Component);\n  var _super = _createSuper(Steps);\n  function Steps() {\n    var _this;\n    _classCallCheck(this, Steps);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"onStepClick\", function (next) {\n      var _this$props = _this.props,\n        onChange = _this$props.onChange,\n        current = _this$props.current;\n      if (onChange && current !== next) {\n        onChange(next);\n      }\n    });\n    return _this;\n  }\n  _createClass(Steps, [{\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n        _this2 = this;\n      var _this$props2 = this.props,\n        prefixCls = _this$props2.prefixCls,\n        _this$props2$style = _this$props2.style,\n        style = _this$props2$style === void 0 ? {} : _this$props2$style,\n        className = _this$props2.className,\n        children = _this$props2.children,\n        direction = _this$props2.direction,\n        type = _this$props2.type,\n        labelPlacement = _this$props2.labelPlacement,\n        iconPrefix = _this$props2.iconPrefix,\n        status = _this$props2.status,\n        size = _this$props2.size,\n        current = _this$props2.current,\n        progressDot = _this$props2.progressDot,\n        stepIcon = _this$props2.stepIcon,\n        initial = _this$props2.initial,\n        icons = _this$props2.icons,\n        onChange = _this$props2.onChange,\n        _this$props2$items = _this$props2.items,\n        items = _this$props2$items === void 0 ? [] : _this$props2$items,\n        restProps = _objectWithoutProperties(_this$props2, _excluded);\n      var isNav = type === 'navigation';\n      var adjustedLabelPlacement = progressDot ? 'vertical' : labelPlacement;\n      var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label-\").concat(adjustedLabelPlacement), direction === 'horizontal'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dot\"), !!progressDot), _defineProperty(_classNames, \"\".concat(prefixCls, \"-navigation\"), isNav), _classNames));\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        className: classString,\n        style: style\n      }, restProps), items.filter(function (item) {\n        return item;\n      }).map(function (item, index) {\n        var mergedItem = _objectSpread({}, item);\n        var stepNumber = initial + index; // fix tail color\n\n        if (status === 'error' && index === current - 1) {\n          mergedItem.className = \"\".concat(prefixCls, \"-next-error\");\n        }\n        if (!mergedItem.status) {\n          if (stepNumber === current) {\n            mergedItem.status = status;\n          } else if (stepNumber < current) {\n            mergedItem.status = 'finish';\n          } else {\n            mergedItem.status = 'wait';\n          }\n        }\n        return /*#__PURE__*/React.createElement(Step, _extends({}, mergedItem, {\n          active: stepNumber === current,\n          stepNumber: stepNumber + 1,\n          stepIndex: stepNumber,\n          key: stepNumber,\n          prefixCls: prefixCls,\n          iconPrefix: iconPrefix,\n          wrapperStyle: style,\n          progressDot: progressDot,\n          stepIcon: stepIcon,\n          icons: icons,\n          onStepClick: onChange && _this2.onStepClick\n        }));\n      }));\n    }\n  }]);\n  return Steps;\n}(React.Component);\n_defineProperty(Steps, \"Step\", Step);\n_defineProperty(Steps, \"defaultProps\", {\n  type: 'default',\n  prefixCls: 'rc-steps',\n  iconPrefix: 'rc',\n  direction: 'horizontal',\n  labelPlacement: 'horizontal',\n  initial: 0,\n  current: 0,\n  status: 'process',\n  size: '',\n  progressDot: false\n});\nexport { Steps as default };", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "_excluded", "classNames", "React", "Step", "Steps", "_React$Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "next", "_this$props", "props", "onChange", "current", "key", "value", "render", "_classNames", "_this2", "_this$props2", "prefixCls", "_this$props2$style", "style", "className", "children", "direction", "type", "labelPlacement", "iconPrefix", "status", "size", "progressDot", "stepIcon", "initial", "icons", "_this$props2$items", "items", "restProps", "isNav", "adjustedLabelPlacement", "classString", "createElement", "filter", "item", "map", "index", "mergedItem", "<PERSON><PERSON><PERSON><PERSON>", "active", "stepIndex", "wrapperStyle", "onStepClick", "Component", "default"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-steps/es/Steps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"prefixCls\", \"style\", \"className\", \"children\", \"direction\", \"type\", \"labelPlacement\", \"iconPrefix\", \"status\", \"size\", \"current\", \"progressDot\", \"stepIcon\", \"initial\", \"icons\", \"onChange\", \"items\"];\n\n/* eslint react/no-did-mount-set-state: 0, react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nimport Step from \"./Step\";\n\nvar Steps = /*#__PURE__*/function (_React$Component) {\n  _inherits(Steps, _React$Component);\n\n  var _super = _createSuper(Steps);\n\n  function Steps() {\n    var _this;\n\n    _classCallCheck(this, Steps);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _super.call.apply(_super, [this].concat(args));\n\n    _defineProperty(_assertThisInitialized(_this), \"onStepClick\", function (next) {\n      var _this$props = _this.props,\n          onChange = _this$props.onChange,\n          current = _this$props.current;\n\n      if (onChange && current !== next) {\n        onChange(next);\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(Steps, [{\n    key: \"render\",\n    value: function render() {\n      var _classNames,\n          _this2 = this;\n\n      var _this$props2 = this.props,\n          prefixCls = _this$props2.prefixCls,\n          _this$props2$style = _this$props2.style,\n          style = _this$props2$style === void 0 ? {} : _this$props2$style,\n          className = _this$props2.className,\n          children = _this$props2.children,\n          direction = _this$props2.direction,\n          type = _this$props2.type,\n          labelPlacement = _this$props2.labelPlacement,\n          iconPrefix = _this$props2.iconPrefix,\n          status = _this$props2.status,\n          size = _this$props2.size,\n          current = _this$props2.current,\n          progressDot = _this$props2.progressDot,\n          stepIcon = _this$props2.stepIcon,\n          initial = _this$props2.initial,\n          icons = _this$props2.icons,\n          onChange = _this$props2.onChange,\n          _this$props2$items = _this$props2.items,\n          items = _this$props2$items === void 0 ? [] : _this$props2$items,\n          restProps = _objectWithoutProperties(_this$props2, _excluded);\n\n      var isNav = type === 'navigation';\n      var adjustedLabelPlacement = progressDot ? 'vertical' : labelPlacement;\n      var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(direction), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label-\").concat(adjustedLabelPlacement), direction === 'horizontal'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dot\"), !!progressDot), _defineProperty(_classNames, \"\".concat(prefixCls, \"-navigation\"), isNav), _classNames));\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        className: classString,\n        style: style\n      }, restProps), items.filter(function (item) {\n        return item;\n      }).map(function (item, index) {\n        var mergedItem = _objectSpread({}, item);\n\n        var stepNumber = initial + index; // fix tail color\n\n        if (status === 'error' && index === current - 1) {\n          mergedItem.className = \"\".concat(prefixCls, \"-next-error\");\n        }\n\n        if (!mergedItem.status) {\n          if (stepNumber === current) {\n            mergedItem.status = status;\n          } else if (stepNumber < current) {\n            mergedItem.status = 'finish';\n          } else {\n            mergedItem.status = 'wait';\n          }\n        }\n\n        return /*#__PURE__*/React.createElement(Step, _extends({}, mergedItem, {\n          active: stepNumber === current,\n          stepNumber: stepNumber + 1,\n          stepIndex: stepNumber,\n          key: stepNumber,\n          prefixCls: prefixCls,\n          iconPrefix: iconPrefix,\n          wrapperStyle: style,\n          progressDot: progressDot,\n          stepIcon: stepIcon,\n          icons: icons,\n          onStepClick: onChange && _this2.onStepClick\n        }));\n      }));\n    }\n  }]);\n\n  return Steps;\n}(React.Component);\n\n_defineProperty(Steps, \"Step\", Step);\n\n_defineProperty(Steps, \"defaultProps\", {\n  type: 'default',\n  prefixCls: 'rc-steps',\n  iconPrefix: 'rc',\n  direction: 'horizontal',\n  labelPlacement: 'horizontal',\n  initial: 0,\n  current: 0,\n  status: 'process',\n  size: '',\n  progressDot: false\n});\n\nexport { Steps as default };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;;AAErN;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,QAAQ;AAEzB,IAAIC,KAAK,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACnDR,SAAS,CAACO,KAAK,EAAEC,gBAAgB,CAAC;EAElC,IAAIC,MAAM,GAAGR,YAAY,CAACM,KAAK,CAAC;EAEhC,SAASA,KAAKA,CAAA,EAAG;IACf,IAAIG,KAAK;IAETb,eAAe,CAAC,IAAI,EAAEU,KAAK,CAAC;IAE5B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IAEtDZ,eAAe,CAACH,sBAAsB,CAACW,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUU,IAAI,EAAE;MAC5E,IAAIC,WAAW,GAAGX,KAAK,CAACY,KAAK;QACzBC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,OAAO,GAAGH,WAAW,CAACG,OAAO;MAEjC,IAAID,QAAQ,IAAIC,OAAO,KAAKJ,IAAI,EAAE;QAChCG,QAAQ,CAACH,IAAI,CAAC;MAChB;IACF,CAAC,CAAC;IAEF,OAAOV,KAAK;EACd;EAEAZ,YAAY,CAACS,KAAK,EAAE,CAAC;IACnBkB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW;QACXC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAACR,KAAK;QACzBS,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,kBAAkB,GAAGF,YAAY,CAACG,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;QAC/DE,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,QAAQ,GAAGL,YAAY,CAACK,QAAQ;QAChCC,SAAS,GAAGN,YAAY,CAACM,SAAS;QAClCC,IAAI,GAAGP,YAAY,CAACO,IAAI;QACxBC,cAAc,GAAGR,YAAY,CAACQ,cAAc;QAC5CC,UAAU,GAAGT,YAAY,CAACS,UAAU;QACpCC,MAAM,GAAGV,YAAY,CAACU,MAAM;QAC5BC,IAAI,GAAGX,YAAY,CAACW,IAAI;QACxBjB,OAAO,GAAGM,YAAY,CAACN,OAAO;QAC9BkB,WAAW,GAAGZ,YAAY,CAACY,WAAW;QACtCC,QAAQ,GAAGb,YAAY,CAACa,QAAQ;QAChCC,OAAO,GAAGd,YAAY,CAACc,OAAO;QAC9BC,KAAK,GAAGf,YAAY,CAACe,KAAK;QAC1BtB,QAAQ,GAAGO,YAAY,CAACP,QAAQ;QAChCuB,kBAAkB,GAAGhB,YAAY,CAACiB,KAAK;QACvCA,KAAK,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,kBAAkB;QAC/DE,SAAS,GAAGpD,wBAAwB,CAACkC,YAAY,EAAE3B,SAAS,CAAC;MAEjE,IAAI8C,KAAK,GAAGZ,IAAI,KAAK,YAAY;MACjC,IAAIa,sBAAsB,GAAGR,WAAW,GAAG,UAAU,GAAGJ,cAAc;MACtE,IAAIa,WAAW,GAAG/C,UAAU,CAAC2B,SAAS,EAAE,EAAE,CAACZ,MAAM,CAACY,SAAS,EAAE,GAAG,CAAC,CAACZ,MAAM,CAACiB,SAAS,CAAC,EAAEF,SAAS,GAAGN,WAAW,GAAG,CAAC,CAAC,EAAE1B,eAAe,CAAC0B,WAAW,EAAE,EAAE,CAACT,MAAM,CAACY,SAAS,EAAE,GAAG,CAAC,CAACZ,MAAM,CAACsB,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAEvC,eAAe,CAAC0B,WAAW,EAAE,EAAE,CAACT,MAAM,CAACY,SAAS,EAAE,SAAS,CAAC,CAACZ,MAAM,CAAC+B,sBAAsB,CAAC,EAAEd,SAAS,KAAK,YAAY,CAAC,EAAElC,eAAe,CAAC0B,WAAW,EAAE,EAAE,CAACT,MAAM,CAACY,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC,CAACW,WAAW,CAAC,EAAExC,eAAe,CAAC0B,WAAW,EAAE,EAAE,CAACT,MAAM,CAACY,SAAS,EAAE,aAAa,CAAC,EAAEkB,KAAK,CAAC,EAAErB,WAAW,CAAC,CAAC;MAC3d,OAAO,aAAavB,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE1D,QAAQ,CAAC;QACtDwC,SAAS,EAAEiB,WAAW;QACtBlB,KAAK,EAAEA;MACT,CAAC,EAAEe,SAAS,CAAC,EAAED,KAAK,CAACM,MAAM,CAAC,UAAUC,IAAI,EAAE;QAC1C,OAAOA,IAAI;MACb,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUD,IAAI,EAAEE,KAAK,EAAE;QAC5B,IAAIC,UAAU,GAAG9D,aAAa,CAAC,CAAC,CAAC,EAAE2D,IAAI,CAAC;QAExC,IAAII,UAAU,GAAGd,OAAO,GAAGY,KAAK,CAAC,CAAC;;QAElC,IAAIhB,MAAM,KAAK,OAAO,IAAIgB,KAAK,KAAKhC,OAAO,GAAG,CAAC,EAAE;UAC/CiC,UAAU,CAACvB,SAAS,GAAG,EAAE,CAACf,MAAM,CAACY,SAAS,EAAE,aAAa,CAAC;QAC5D;QAEA,IAAI,CAAC0B,UAAU,CAACjB,MAAM,EAAE;UACtB,IAAIkB,UAAU,KAAKlC,OAAO,EAAE;YAC1BiC,UAAU,CAACjB,MAAM,GAAGA,MAAM;UAC5B,CAAC,MAAM,IAAIkB,UAAU,GAAGlC,OAAO,EAAE;YAC/BiC,UAAU,CAACjB,MAAM,GAAG,QAAQ;UAC9B,CAAC,MAAM;YACLiB,UAAU,CAACjB,MAAM,GAAG,MAAM;UAC5B;QACF;QAEA,OAAO,aAAanC,KAAK,CAAC+C,aAAa,CAAC9C,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAE+D,UAAU,EAAE;UACrEE,MAAM,EAAED,UAAU,KAAKlC,OAAO;UAC9BkC,UAAU,EAAEA,UAAU,GAAG,CAAC;UAC1BE,SAAS,EAAEF,UAAU;UACrBjC,GAAG,EAAEiC,UAAU;UACf3B,SAAS,EAAEA,SAAS;UACpBQ,UAAU,EAAEA,UAAU;UACtBsB,YAAY,EAAE5B,KAAK;UACnBS,WAAW,EAAEA,WAAW;UACxBC,QAAQ,EAAEA,QAAQ;UAClBE,KAAK,EAAEA,KAAK;UACZiB,WAAW,EAAEvC,QAAQ,IAAIM,MAAM,CAACiC;QAClC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EAEH,OAAOvD,KAAK;AACd,CAAC,CAACF,KAAK,CAAC0D,SAAS,CAAC;AAElB7D,eAAe,CAACK,KAAK,EAAE,MAAM,EAAED,IAAI,CAAC;AAEpCJ,eAAe,CAACK,KAAK,EAAE,cAAc,EAAE;EACrC8B,IAAI,EAAE,SAAS;EACfN,SAAS,EAAE,UAAU;EACrBQ,UAAU,EAAE,IAAI;EAChBH,SAAS,EAAE,YAAY;EACvBE,cAAc,EAAE,YAAY;EAC5BM,OAAO,EAAE,CAAC;EACVpB,OAAO,EAAE,CAAC;EACVgB,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,EAAE;EACRC,WAAW,EAAE;AACf,CAAC,CAAC;AAEF,SAASnC,KAAK,IAAIyD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}