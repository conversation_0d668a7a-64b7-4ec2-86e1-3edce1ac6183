{"ast": null, "code": "import _isNil from \"lodash/isNil\";\nvar _excluded = [\"dx\", \"dy\", \"textAnchor\", \"verticalAnchor\", \"scaleToFit\", \"angle\", \"lineHeight\", \"capHeight\", \"className\", \"breakAll\"];\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s,\n      _e,\n      _x,\n      _r,\n      _arr = [],\n      _n = !0,\n      _d = !1;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i) return;\n        _n = !1;\n      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);\n    } catch (err) {\n      _d = !0, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return;\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport React, { Component } from 'react';\nimport reduceCSSCalc from 'reduce-css-calc';\nimport classNames from 'classnames';\nimport { isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = function calculateWordWidths(props) {\n  try {\n    var words = [];\n    if (!_isNil(props.children)) {\n      if (props.breakAll) {\n        words = props.children.toString().split('');\n      } else {\n        words = props.children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(function (word) {\n      return {\n        word: word,\n        width: getStringSize(word, props.style).width\n      };\n    });\n    var spaceWidth = props.breakAll ? 0 : getStringSize(\"\\xA0\", props.style).width;\n    return {\n      wordsWithComputedWidth: wordsWithComputedWidth,\n      spaceWidth: spaceWidth\n    };\n  } catch (e) {\n    return null;\n  }\n};\nvar calculateWordsByLines = function calculateWordsByLines(props, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) {\n  var shouldLimitLines = isNumber(props.maxLines);\n  var text = props.children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce(function (result, _ref) {\n      var word = _ref.word,\n        width = _ref.width;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < lineWidth)) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width: width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = function findLongestLine(words) {\n    return words.reduce(function (a, b) {\n      return a.width > b.width ? a : b;\n    });\n  };\n  if (!shouldLimitLines) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = function checkOverflow(index) {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths(_objectSpread(_objectSpread({}, props), {}, {\n      children: tempText + suffix\n    })).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > props.maxLines || findLongestLine(result).width > lineWidth;\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var _checkOverflow = checkOverflow(prev),\n      _checkOverflow2 = _slicedToArray(_checkOverflow, 2),\n      doesPrevOverflow = _checkOverflow2[0],\n      result = _checkOverflow2[1];\n    var _checkOverflow3 = checkOverflow(middle),\n      _checkOverflow4 = _slicedToArray(_checkOverflow3, 1),\n      doesMiddleOverflow = _checkOverflow4[0];\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = function getWordsWithoutCalculate(children) {\n  var words = !_isNil(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words: words\n  }];\n};\nvar getWordsByLines = function getWordsByLines(props, needCalculate) {\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((props.width || props.scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    if (needCalculate) {\n      var wordWidths = calculateWordWidths(props);\n      if (wordWidths) {\n        var wcw = wordWidths.wordsWithComputedWidth,\n          sw = wordWidths.spaceWidth;\n        wordsWithComputedWidth = wcw;\n        spaceWidth = sw;\n      } else {\n        return getWordsWithoutCalculate(props.children);\n      }\n      return calculateWordsByLines(props, wordsWithComputedWidth, spaceWidth, props.width, props.scaleToFit);\n    }\n  }\n  return getWordsWithoutCalculate(props.children);\n};\nexport var Text = /*#__PURE__*/function (_Component) {\n  _inherits(Text, _Component);\n  var _super = _createSuper(Text);\n  function Text() {\n    var _this;\n    _classCallCheck(this, Text);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {});\n    return _this;\n  }\n  _createClass(Text, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        dx = _this$props.dx,\n        dy = _this$props.dy,\n        textAnchor = _this$props.textAnchor,\n        verticalAnchor = _this$props.verticalAnchor,\n        scaleToFit = _this$props.scaleToFit,\n        angle = _this$props.angle,\n        lineHeight = _this$props.lineHeight,\n        capHeight = _this$props.capHeight,\n        className = _this$props.className,\n        breakAll = _this$props.breakAll,\n        textProps = _objectWithoutProperties(_this$props, _excluded);\n      var wordsByLines = this.state.wordsByLines;\n      if (!isNumOrStr(textProps.x) || !isNumOrStr(textProps.y)) {\n        return null;\n      }\n      var x = textProps.x + (isNumber(dx) ? dx : 0);\n      var y = textProps.y + (isNumber(dy) ? dy : 0);\n      var startDy;\n      switch (verticalAnchor) {\n        case 'start':\n          startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n          break;\n        case 'middle':\n          startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n          break;\n        default:\n          startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n          break;\n      }\n      var transforms = [];\n      if (scaleToFit) {\n        var lineWidth = wordsByLines[0].width;\n        var width = this.props.width;\n        transforms.push(\"scale(\".concat((isNumber(width) ? width / lineWidth : 1) / lineWidth, \")\"));\n      }\n      if (angle) {\n        transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n      }\n      if (transforms.length) {\n        textProps.transform = transforms.join(' ');\n      }\n      return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n        x: x,\n        y: y,\n        className: classNames('recharts-text', className),\n        textAnchor: textAnchor,\n        fill: textProps.fill.includes('url') ? Text.defaultProps.fill : textProps.fill\n      }), wordsByLines.map(function (line, index) {\n        return (/*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(\"tspan\", {\n            x: x,\n            dy: index === 0 ? startDy : lineHeight,\n            key: index\n          }, line.words.join(breakAll ? '' : ' '))\n        );\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.width !== prevState.prevWidth || nextProps.scaleToFit !== prevState.prevScaleToFit || nextProps.children !== prevState.prevChildren || nextProps.style !== prevState.prevStyle || nextProps.breakAll !== prevState.prevBreakAll) {\n        var needCalculate = nextProps.children !== prevState.prevChildren || nextProps.style !== prevState.prevStyle || nextProps.breakAll !== prevState.prevBreakAll;\n        return {\n          prevWidth: nextProps.width,\n          prevScaleToFit: nextProps.scaleToFit,\n          prevChildren: nextProps.children,\n          prevStyle: nextProps.style,\n          wordsByLines: getWordsByLines(nextProps, needCalculate)\n        };\n      }\n      return null;\n    }\n  }]);\n  return Text;\n}(Component);\n_defineProperty(Text, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  lineHeight: '1em',\n  capHeight: '0.71em',\n  // Magic number from d3\n  scaleToFit: false,\n  textAnchor: 'start',\n  verticalAnchor: 'end',\n  // Maintain compat with existing charts / default SVG behavior\n  fill: '#808080'\n});", "map": {"version": 3, "names": ["_isNil", "_excluded", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "sourceKeys", "keys", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "len", "arr2", "_i", "_s", "_e", "_x", "_r", "_arr", "_n", "_d", "next", "done", "push", "err", "isArray", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "React", "Component", "reduceCSSCalc", "classNames", "isNumber", "isNumOrStr", "Global", "filterProps", "getStringSize", "BREAKING_SPACES", "calculateWordWidths", "words", "children", "breakAll", "split", "wordsWithComputedWidth", "map", "word", "width", "style", "spaceWidth", "calculateWordsByLines", "initialWordsWithComputedWith", "lineWidth", "scaleToFit", "shouldLimitLines", "maxLines", "text", "calculate", "reduce", "_ref", "currentLine", "newLine", "originalResult", "findLongestLine", "a", "b", "suffix", "checkOverflow", "index", "tempText", "doesOverflow", "start", "end", "iterations", "trimmedResult", "middle", "Math", "floor", "prev", "_checkOverflow", "_checkOverflow2", "doesPrevOverflow", "_checkOverflow3", "_checkOverflow4", "doesMiddleOverflow", "getWordsWithoutCalculate", "getWordsByLines", "needCalculate", "isSsr", "wordWidths", "wcw", "sw", "Text", "_Component", "_super", "_this", "_len", "args", "_key", "concat", "render", "_this$props", "dx", "dy", "textAnchor", "verticalAnchor", "angle", "lineHeight", "capHeight", "className", "textProps", "wordsByLines", "state", "x", "y", "startDy", "transforms", "transform", "join", "createElement", "fill", "includes", "defaultProps", "line", "getDerivedStateFromProps", "nextProps", "prevState", "prevWidth", "prevScaleToFit", "prev<PERSON><PERSON><PERSON><PERSON>", "prevStyle", "prevBreakAll"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/recharts/es6/component/Text.js"], "sourcesContent": ["import _isNil from \"lodash/isNil\";\nvar _excluded = [\"dx\", \"dy\", \"textAnchor\", \"verticalAnchor\", \"scaleToFit\", \"angle\", \"lineHeight\", \"capHeight\", \"className\", \"breakAll\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i[\"return\"] && (_r = _i[\"return\"](), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport React, { Component } from 'react';\nimport reduceCSSCalc from 'reduce-css-calc';\nimport classNames from 'classnames';\nimport { isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = function calculateWordWidths(props) {\n  try {\n    var words = [];\n    if (!_isNil(props.children)) {\n      if (props.breakAll) {\n        words = props.children.toString().split('');\n      } else {\n        words = props.children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(function (word) {\n      return {\n        word: word,\n        width: getStringSize(word, props.style).width\n      };\n    });\n    var spaceWidth = props.breakAll ? 0 : getStringSize(\"\\xA0\", props.style).width;\n    return {\n      wordsWithComputedWidth: wordsWithComputedWidth,\n      spaceWidth: spaceWidth\n    };\n  } catch (e) {\n    return null;\n  }\n};\nvar calculateWordsByLines = function calculateWordsByLines(props, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) {\n  var shouldLimitLines = isNumber(props.maxLines);\n  var text = props.children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce(function (result, _ref) {\n      var word = _ref.word,\n        width = _ref.width;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < lineWidth)) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width: width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = function findLongestLine(words) {\n    return words.reduce(function (a, b) {\n      return a.width > b.width ? a : b;\n    });\n  };\n  if (!shouldLimitLines) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = function checkOverflow(index) {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths(_objectSpread(_objectSpread({}, props), {}, {\n      children: tempText + suffix\n    })).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > props.maxLines || findLongestLine(result).width > lineWidth;\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var _checkOverflow = checkOverflow(prev),\n      _checkOverflow2 = _slicedToArray(_checkOverflow, 2),\n      doesPrevOverflow = _checkOverflow2[0],\n      result = _checkOverflow2[1];\n    var _checkOverflow3 = checkOverflow(middle),\n      _checkOverflow4 = _slicedToArray(_checkOverflow3, 1),\n      doesMiddleOverflow = _checkOverflow4[0];\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = function getWordsWithoutCalculate(children) {\n  var words = !_isNil(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words: words\n  }];\n};\nvar getWordsByLines = function getWordsByLines(props, needCalculate) {\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((props.width || props.scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    if (needCalculate) {\n      var wordWidths = calculateWordWidths(props);\n      if (wordWidths) {\n        var wcw = wordWidths.wordsWithComputedWidth,\n          sw = wordWidths.spaceWidth;\n        wordsWithComputedWidth = wcw;\n        spaceWidth = sw;\n      } else {\n        return getWordsWithoutCalculate(props.children);\n      }\n      return calculateWordsByLines(props, wordsWithComputedWidth, spaceWidth, props.width, props.scaleToFit);\n    }\n  }\n  return getWordsWithoutCalculate(props.children);\n};\nexport var Text = /*#__PURE__*/function (_Component) {\n  _inherits(Text, _Component);\n  var _super = _createSuper(Text);\n  function Text() {\n    var _this;\n    _classCallCheck(this, Text);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {});\n    return _this;\n  }\n  _createClass(Text, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        dx = _this$props.dx,\n        dy = _this$props.dy,\n        textAnchor = _this$props.textAnchor,\n        verticalAnchor = _this$props.verticalAnchor,\n        scaleToFit = _this$props.scaleToFit,\n        angle = _this$props.angle,\n        lineHeight = _this$props.lineHeight,\n        capHeight = _this$props.capHeight,\n        className = _this$props.className,\n        breakAll = _this$props.breakAll,\n        textProps = _objectWithoutProperties(_this$props, _excluded);\n      var wordsByLines = this.state.wordsByLines;\n      if (!isNumOrStr(textProps.x) || !isNumOrStr(textProps.y)) {\n        return null;\n      }\n      var x = textProps.x + (isNumber(dx) ? dx : 0);\n      var y = textProps.y + (isNumber(dy) ? dy : 0);\n      var startDy;\n      switch (verticalAnchor) {\n        case 'start':\n          startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n          break;\n        case 'middle':\n          startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n          break;\n        default:\n          startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n          break;\n      }\n      var transforms = [];\n      if (scaleToFit) {\n        var lineWidth = wordsByLines[0].width;\n        var width = this.props.width;\n        transforms.push(\"scale(\".concat((isNumber(width) ? width / lineWidth : 1) / lineWidth, \")\"));\n      }\n      if (angle) {\n        transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n      }\n      if (transforms.length) {\n        textProps.transform = transforms.join(' ');\n      }\n      return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n        x: x,\n        y: y,\n        className: classNames('recharts-text', className),\n        textAnchor: textAnchor,\n        fill: textProps.fill.includes('url') ? Text.defaultProps.fill : textProps.fill\n      }), wordsByLines.map(function (line, index) {\n        return (\n          /*#__PURE__*/\n          // eslint-disable-next-line react/no-array-index-key\n          React.createElement(\"tspan\", {\n            x: x,\n            dy: index === 0 ? startDy : lineHeight,\n            key: index\n          }, line.words.join(breakAll ? '' : ' '))\n        );\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.width !== prevState.prevWidth || nextProps.scaleToFit !== prevState.prevScaleToFit || nextProps.children !== prevState.prevChildren || nextProps.style !== prevState.prevStyle || nextProps.breakAll !== prevState.prevBreakAll) {\n        var needCalculate = nextProps.children !== prevState.prevChildren || nextProps.style !== prevState.prevStyle || nextProps.breakAll !== prevState.prevBreakAll;\n        return {\n          prevWidth: nextProps.width,\n          prevScaleToFit: nextProps.scaleToFit,\n          prevChildren: nextProps.children,\n          prevStyle: nextProps.style,\n          wordsByLines: getWordsByLines(nextProps, needCalculate)\n        };\n      }\n      return null;\n    }\n  }]);\n  return Text;\n}(Component);\n_defineProperty(Text, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  lineHeight: '1em',\n  capHeight: '0.71em',\n  // Magic number from d3\n  scaleToFit: false,\n  textAnchor: 'start',\n  verticalAnchor: 'end',\n  // Maintain compat with existing charts / default SVG behavior\n  fill: '#808080'\n});"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;AACvI,SAASC,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,wBAAwBA,CAACL,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGW,6BAA6B,CAACP,MAAM,EAAEM,QAAQ,CAAC;EAAE,IAAIL,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGhB,MAAM,CAACe,qBAAqB,CAACR,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,gBAAgB,CAACV,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGQ,gBAAgB,CAACZ,CAAC,CAAC;MAAE,IAAIS,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACoB,oBAAoB,CAACR,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASW,6BAA6BA,CAACP,MAAM,EAAEM,QAAQ,EAAE;EAAE,IAAIN,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIgB,UAAU,GAAGnB,MAAM,CAACoB,IAAI,CAACb,MAAM,CAAC;EAAE,IAAIC,GAAG,EAAEJ,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,UAAU,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAEI,GAAG,GAAGW,UAAU,CAACf,CAAC,CAAC;IAAE,IAAIS,QAAQ,CAACI,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AAClT,SAASkB,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACtB,MAAM,EAAEuB,KAAK,EAAE;EAAE,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,KAAK,CAACpB,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIuB,UAAU,GAAGD,KAAK,CAACtB,CAAC,CAAC;IAAEuB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAE9B,MAAM,CAAC+B,cAAc,CAAC5B,MAAM,EAAE6B,cAAc,CAACL,UAAU,CAACnB,GAAG,CAAC,EAAEmB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACF,WAAW,CAACzB,SAAS,EAAEoC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACF,WAAW,EAAEY,WAAW,CAAC;EAAEnC,MAAM,CAAC+B,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASa,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAId,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEa,QAAQ,CAACvC,SAAS,GAAGE,MAAM,CAACuC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxC,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE2C,KAAK,EAAEH,QAAQ;MAAEP,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE7B,MAAM,CAAC+B,cAAc,CAACM,QAAQ,EAAE,WAAW,EAAE;IAAEP,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIQ,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAEF,eAAe,GAAGzC,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAAC4C,cAAc,CAAC1C,IAAI,CAAC,CAAC,GAAG,SAASuC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAE,OAAOD,CAAC;EAAE,CAAC;EAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAAE;AACvM,SAASG,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAACvD,WAAW;MAAEwD,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE9C,SAAS,EAAEiD,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAACxC,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IAAE;IAAE,OAAOoD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAEhD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOmC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACjE,SAAS,CAACkE,OAAO,CAACtD,IAAI,CAAC6C,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACV,CAAC,EAAE;EAAEU,eAAe,GAAGpD,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAACkE,cAAc,CAAChE,IAAI,CAAC,CAAC,GAAG,SAASkD,eAAeA,CAACV,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACG,SAAS,IAAI7C,MAAM,CAACkE,cAAc,CAACxB,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;AAAE;AACnN,SAASyB,cAAcA,CAACC,GAAG,EAAEhE,CAAC,EAAE;EAAE,OAAOiE,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAEhE,CAAC,CAAC,IAAImE,2BAA2B,CAACH,GAAG,EAAEhE,CAAC,CAAC,IAAIoE,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIhD,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAAS+C,2BAA2BA,CAAC7B,CAAC,EAAE+B,MAAM,EAAE;EAAE,IAAI,CAAC/B,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOgC,iBAAiB,CAAChC,CAAC,EAAE+B,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAG3E,MAAM,CAACF,SAAS,CAAC8E,QAAQ,CAAClE,IAAI,CAACgC,CAAC,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIjC,CAAC,CAAC7C,WAAW,EAAE8E,CAAC,GAAGjC,CAAC,CAAC7C,WAAW,CAACiF,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAACtC,CAAC,CAAC;EAAE,IAAIiC,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAAChC,CAAC,EAAE+B,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACN,GAAG,EAAEc,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGd,GAAG,CAAC9D,MAAM,EAAE4E,GAAG,GAAGd,GAAG,CAAC9D,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE+E,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAE9E,CAAC,GAAG8E,GAAG,EAAE9E,CAAC,EAAE,EAAE+E,IAAI,CAAC/E,CAAC,CAAC,GAAGgE,GAAG,CAAChE,CAAC,CAAC;EAAE,OAAO+E,IAAI;AAAE;AAClL,SAASb,qBAAqBA,CAACF,GAAG,EAAEhE,CAAC,EAAE;EAAE,IAAIgF,EAAE,GAAG,IAAI,IAAIhB,GAAG,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOzE,MAAM,IAAIyE,GAAG,CAACzE,MAAM,CAACC,QAAQ,CAAC,IAAIwE,GAAG,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIgB,EAAE,EAAE;IAAE,IAAIC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,IAAI,GAAG,EAAE;MAAEC,EAAE,GAAG,CAAC,CAAC;MAAEC,EAAE,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIJ,EAAE,GAAG,CAACH,EAAE,GAAGA,EAAE,CAAC1E,IAAI,CAAC0D,GAAG,CAAC,EAAEwB,IAAI,EAAE,CAAC,KAAKxF,CAAC,EAAE;QAAE,IAAIJ,MAAM,CAACoF,EAAE,CAAC,KAAKA,EAAE,EAAE;QAAQM,EAAE,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,EAAE,GAAG,CAACL,EAAE,GAAGE,EAAE,CAAC7E,IAAI,CAAC0E,EAAE,CAAC,EAAES,IAAI,CAAC,KAAKJ,IAAI,CAACK,IAAI,CAACT,EAAE,CAAC7C,KAAK,CAAC,EAAEiD,IAAI,CAACnF,MAAM,KAAKF,CAAC,CAAC,EAAEsF,EAAE,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOK,GAAG,EAAE;MAAEJ,EAAE,GAAG,CAAC,CAAC,EAAEL,EAAE,GAAGS,GAAG;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACL,EAAE,IAAI,IAAI,IAAIN,EAAE,CAAC,QAAQ,CAAC,KAAKI,EAAE,GAAGJ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEpF,MAAM,CAACwF,EAAE,CAAC,KAAKA,EAAE,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAIG,EAAE,EAAE,MAAML,EAAE;MAAE;IAAE;IAAE,OAAOG,IAAI;EAAE;AAAE;AACjlB,SAASpB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIW,KAAK,CAACiB,OAAO,CAAC5B,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAAS6B,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAI/E,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAAC8E,MAAM,CAAC;EAAE,IAAIlG,MAAM,CAACe,qBAAqB,EAAE;IAAE,IAAIqF,OAAO,GAAGpG,MAAM,CAACe,qBAAqB,CAACmF,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOtG,MAAM,CAACuG,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAAC1E,UAAU;IAAE,CAAC,CAAC,CAAC,EAAER,IAAI,CAAC0E,IAAI,CAACnF,KAAK,CAACS,IAAI,EAAEgF,OAAO,CAAC;EAAE;EAAE,OAAOhF,IAAI;AAAE;AACpV,SAASoF,aAAaA,CAACrG,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG6F,OAAO,CAACjG,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACkG,OAAO,CAAC,UAAUjG,GAAG,EAAE;MAAEkG,eAAe,CAACvG,MAAM,EAAEK,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC2G,yBAAyB,GAAG3G,MAAM,CAAC4G,gBAAgB,CAACzG,MAAM,EAAEH,MAAM,CAAC2G,yBAAyB,CAACpG,MAAM,CAAC,CAAC,GAAG0F,OAAO,CAACjG,MAAM,CAACO,MAAM,CAAC,CAAC,CAACkG,OAAO,CAAC,UAAUjG,GAAG,EAAE;MAAER,MAAM,CAAC+B,cAAc,CAAC5B,MAAM,EAAEK,GAAG,EAAER,MAAM,CAACuG,wBAAwB,CAAChG,MAAM,EAAEC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOL,MAAM;AAAE;AACzf,SAASuG,eAAeA,CAAChH,GAAG,EAAEc,GAAG,EAAEgC,KAAK,EAAE;EAAEhC,GAAG,GAAGwB,cAAc,CAACxB,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAId,GAAG,EAAE;IAAEM,MAAM,CAAC+B,cAAc,CAACrC,GAAG,EAAEc,GAAG,EAAE;MAAEgC,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEpC,GAAG,CAACc,GAAG,CAAC,GAAGgC,KAAK;EAAE;EAAE,OAAO9C,GAAG;AAAE;AAC3O,SAASsC,cAAcA,CAAC6E,GAAG,EAAE;EAAE,IAAIrG,GAAG,GAAGsG,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOpH,OAAO,CAACe,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGuG,MAAM,CAACvG,GAAG,CAAC;AAAE;AAC5H,SAASsG,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIxH,OAAO,CAACuH,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACrH,MAAM,CAACwH,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACxG,IAAI,CAACsG,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIxH,OAAO,CAAC4H,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAI7F,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACyF,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,OAAOO,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,mBAAmB;AACxD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,IAAIC,eAAe,GAAG,4BAA4B;AAClD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACvG,KAAK,EAAE;EAC5D,IAAI;IACF,IAAIwG,KAAK,GAAG,EAAE;IACd,IAAI,CAAC3I,MAAM,CAACmC,KAAK,CAACyG,QAAQ,CAAC,EAAE;MAC3B,IAAIzG,KAAK,CAAC0G,QAAQ,EAAE;QAClBF,KAAK,GAAGxG,KAAK,CAACyG,QAAQ,CAACvD,QAAQ,CAAC,CAAC,CAACyD,KAAK,CAAC,EAAE,CAAC;MAC7C,CAAC,MAAM;QACLH,KAAK,GAAGxG,KAAK,CAACyG,QAAQ,CAACvD,QAAQ,CAAC,CAAC,CAACyD,KAAK,CAACL,eAAe,CAAC;MAC1D;IACF;IACA,IAAIM,sBAAsB,GAAGJ,KAAK,CAACK,GAAG,CAAC,UAAUC,IAAI,EAAE;MACrD,OAAO;QACLA,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAEV,aAAa,CAACS,IAAI,EAAE9G,KAAK,CAACgH,KAAK,CAAC,CAACD;MAC1C,CAAC;IACH,CAAC,CAAC;IACF,IAAIE,UAAU,GAAGjH,KAAK,CAAC0G,QAAQ,GAAG,CAAC,GAAGL,aAAa,CAAC,MAAM,EAAErG,KAAK,CAACgH,KAAK,CAAC,CAACD,KAAK;IAC9E,OAAO;MACLH,sBAAsB,EAAEA,sBAAsB;MAC9CK,UAAU,EAAEA;IACd,CAAC;EACH,CAAC,CAAC,OAAO1E,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF,CAAC;AACD,IAAI2E,qBAAqB,GAAG,SAASA,qBAAqBA,CAAClH,KAAK,EAAEmH,4BAA4B,EAAEF,UAAU,EAAEG,SAAS,EAAEC,UAAU,EAAE;EACjI,IAAIC,gBAAgB,GAAGrB,QAAQ,CAACjG,KAAK,CAACuH,QAAQ,CAAC;EAC/C,IAAIC,IAAI,GAAGxH,KAAK,CAACyG,QAAQ;EACzB,IAAIgB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIjB,KAAK,GAAG7H,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK+G,SAAS,GAAG/G,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAClF,OAAO6H,KAAK,CAACkB,MAAM,CAAC,UAAU/F,MAAM,EAAEgG,IAAI,EAAE;MAC1C,IAAIb,IAAI,GAAGa,IAAI,CAACb,IAAI;QAClBC,KAAK,GAAGY,IAAI,CAACZ,KAAK;MACpB,IAAIa,WAAW,GAAGjG,MAAM,CAACA,MAAM,CAAC/C,MAAM,GAAG,CAAC,CAAC;MAC3C,IAAIgJ,WAAW,KAAKR,SAAS,IAAI,IAAI,IAAIC,UAAU,IAAIO,WAAW,CAACb,KAAK,GAAGA,KAAK,GAAGE,UAAU,GAAGG,SAAS,CAAC,EAAE;QAC1G;QACAQ,WAAW,CAACpB,KAAK,CAACpC,IAAI,CAAC0C,IAAI,CAAC;QAC5Bc,WAAW,CAACb,KAAK,IAAIA,KAAK,GAAGE,UAAU;MACzC,CAAC,MAAM;QACL;QACA,IAAIY,OAAO,GAAG;UACZrB,KAAK,EAAE,CAACM,IAAI,CAAC;UACbC,KAAK,EAAEA;QACT,CAAC;QACDpF,MAAM,CAACyC,IAAI,CAACyD,OAAO,CAAC;MACtB;MACA,OAAOlG,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,IAAImG,cAAc,GAAGL,SAAS,CAACN,4BAA4B,CAAC;EAC5D,IAAIY,eAAe,GAAG,SAASA,eAAeA,CAACvB,KAAK,EAAE;IACpD,OAAOA,KAAK,CAACkB,MAAM,CAAC,UAAUM,CAAC,EAAEC,CAAC,EAAE;MAClC,OAAOD,CAAC,CAACjB,KAAK,GAAGkB,CAAC,CAAClB,KAAK,GAAGiB,CAAC,GAAGC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACD,IAAI,CAACX,gBAAgB,EAAE;IACrB,OAAOQ,cAAc;EACvB;EACA,IAAII,MAAM,GAAG,GAAG;EAChB,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,IAAIC,QAAQ,GAAGb,IAAI,CAACrE,KAAK,CAAC,CAAC,EAAEiF,KAAK,CAAC;IACnC,IAAI5B,KAAK,GAAGD,mBAAmB,CAACzB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE9E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1EyG,QAAQ,EAAE4B,QAAQ,GAAGH;IACvB,CAAC,CAAC,CAAC,CAACtB,sBAAsB;IAC1B,IAAIjF,MAAM,GAAG8F,SAAS,CAACjB,KAAK,CAAC;IAC7B,IAAI8B,YAAY,GAAG3G,MAAM,CAAC/C,MAAM,GAAGoB,KAAK,CAACuH,QAAQ,IAAIQ,eAAe,CAACpG,MAAM,CAAC,CAACoF,KAAK,GAAGK,SAAS;IAC9F,OAAO,CAACkB,YAAY,EAAE3G,MAAM,CAAC;EAC/B,CAAC;EACD,IAAI4G,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGhB,IAAI,CAAC5I,MAAM,GAAG,CAAC;EACzB,IAAI6J,UAAU,GAAG,CAAC;EAClB,IAAIC,aAAa;EACjB,OAAOH,KAAK,IAAIC,GAAG,IAAIC,UAAU,IAAIjB,IAAI,CAAC5I,MAAM,GAAG,CAAC,EAAE;IACpD,IAAI+J,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACN,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;IAC1C,IAAIM,IAAI,GAAGH,MAAM,GAAG,CAAC;IACrB,IAAII,cAAc,GAAGZ,aAAa,CAACW,IAAI,CAAC;MACtCE,eAAe,GAAGvG,cAAc,CAACsG,cAAc,EAAE,CAAC,CAAC;MACnDE,gBAAgB,GAAGD,eAAe,CAAC,CAAC,CAAC;MACrCrH,MAAM,GAAGqH,eAAe,CAAC,CAAC,CAAC;IAC7B,IAAIE,eAAe,GAAGf,aAAa,CAACQ,MAAM,CAAC;MACzCQ,eAAe,GAAG1G,cAAc,CAACyG,eAAe,EAAE,CAAC,CAAC;MACpDE,kBAAkB,GAAGD,eAAe,CAAC,CAAC,CAAC;IACzC,IAAI,CAACF,gBAAgB,IAAI,CAACG,kBAAkB,EAAE;MAC5Cb,KAAK,GAAGI,MAAM,GAAG,CAAC;IACpB;IACA,IAAIM,gBAAgB,IAAIG,kBAAkB,EAAE;MAC1CZ,GAAG,GAAGG,MAAM,GAAG,CAAC;IAClB;IACA,IAAI,CAACM,gBAAgB,IAAIG,kBAAkB,EAAE;MAC3CV,aAAa,GAAG/G,MAAM;MACtB;IACF;IACA8G,UAAU,EAAE;EACd;;EAEA;EACA;EACA,OAAOC,aAAa,IAAIZ,cAAc;AACxC,CAAC;AACD,IAAIuB,wBAAwB,GAAG,SAASA,wBAAwBA,CAAC5C,QAAQ,EAAE;EACzE,IAAID,KAAK,GAAG,CAAC3I,MAAM,CAAC4I,QAAQ,CAAC,GAAGA,QAAQ,CAACvD,QAAQ,CAAC,CAAC,CAACyD,KAAK,CAACL,eAAe,CAAC,GAAG,EAAE;EAC/E,OAAO,CAAC;IACNE,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AACD,IAAI8C,eAAe,GAAG,SAASA,eAAeA,CAACtJ,KAAK,EAAEuJ,aAAa,EAAE;EACnE;EACA,IAAI,CAACvJ,KAAK,CAAC+G,KAAK,IAAI/G,KAAK,CAACqH,UAAU,KAAK,CAAClB,MAAM,CAACqD,KAAK,EAAE;IACtD,IAAI5C,sBAAsB,EAAEK,UAAU;IACtC,IAAIsC,aAAa,EAAE;MACjB,IAAIE,UAAU,GAAGlD,mBAAmB,CAACvG,KAAK,CAAC;MAC3C,IAAIyJ,UAAU,EAAE;QACd,IAAIC,GAAG,GAAGD,UAAU,CAAC7C,sBAAsB;UACzC+C,EAAE,GAAGF,UAAU,CAACxC,UAAU;QAC5BL,sBAAsB,GAAG8C,GAAG;QAC5BzC,UAAU,GAAG0C,EAAE;MACjB,CAAC,MAAM;QACL,OAAON,wBAAwB,CAACrJ,KAAK,CAACyG,QAAQ,CAAC;MACjD;MACA,OAAOS,qBAAqB,CAAClH,KAAK,EAAE4G,sBAAsB,EAAEK,UAAU,EAAEjH,KAAK,CAAC+G,KAAK,EAAE/G,KAAK,CAACqH,UAAU,CAAC;IACxG;EACF;EACA,OAAOgC,wBAAwB,CAACrJ,KAAK,CAACyG,QAAQ,CAAC;AACjD,CAAC;AACD,OAAO,IAAImD,IAAI,GAAG,aAAa,UAAUC,UAAU,EAAE;EACnDnJ,SAAS,CAACkJ,IAAI,EAAEC,UAAU,CAAC;EAC3B,IAAIC,MAAM,GAAG1I,YAAY,CAACwI,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTpK,eAAe,CAAC,IAAI,EAAEiK,IAAI,CAAC;IAC3B,KAAK,IAAII,IAAI,GAAGrL,SAAS,CAACC,MAAM,EAAEqL,IAAI,GAAG,IAAI5G,KAAK,CAAC2G,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGvL,SAAS,CAACuL,IAAI,CAAC;IAC9B;IACAH,KAAK,GAAGD,MAAM,CAAC9K,IAAI,CAACC,KAAK,CAAC6K,MAAM,EAAE,CAAC,IAAI,CAAC,CAACK,MAAM,CAACF,IAAI,CAAC,CAAC;IACtDjF,eAAe,CAAC/C,sBAAsB,CAAC8H,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IAC3D,OAAOA,KAAK;EACd;EACAxJ,YAAY,CAACqJ,IAAI,EAAE,CAAC;IAClB9K,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASsJ,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAACrK,KAAK;QAC1BsK,EAAE,GAAGD,WAAW,CAACC,EAAE;QACnBC,EAAE,GAAGF,WAAW,CAACE,EAAE;QACnBC,UAAU,GAAGH,WAAW,CAACG,UAAU;QACnCC,cAAc,GAAGJ,WAAW,CAACI,cAAc;QAC3CpD,UAAU,GAAGgD,WAAW,CAAChD,UAAU;QACnCqD,KAAK,GAAGL,WAAW,CAACK,KAAK;QACzBC,UAAU,GAAGN,WAAW,CAACM,UAAU;QACnCC,SAAS,GAAGP,WAAW,CAACO,SAAS;QACjCC,SAAS,GAAGR,WAAW,CAACQ,SAAS;QACjCnE,QAAQ,GAAG2D,WAAW,CAAC3D,QAAQ;QAC/BoE,SAAS,GAAG5L,wBAAwB,CAACmL,WAAW,EAAEvM,SAAS,CAAC;MAC9D,IAAIiN,YAAY,GAAG,IAAI,CAACC,KAAK,CAACD,YAAY;MAC1C,IAAI,CAAC7E,UAAU,CAAC4E,SAAS,CAACG,CAAC,CAAC,IAAI,CAAC/E,UAAU,CAAC4E,SAAS,CAACI,CAAC,CAAC,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAID,CAAC,GAAGH,SAAS,CAACG,CAAC,IAAIhF,QAAQ,CAACqE,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;MAC7C,IAAIY,CAAC,GAAGJ,SAAS,CAACI,CAAC,IAAIjF,QAAQ,CAACsE,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;MAC7C,IAAIY,OAAO;MACX,QAAQV,cAAc;QACpB,KAAK,OAAO;UACVU,OAAO,GAAGpF,aAAa,CAAC,OAAO,CAACoE,MAAM,CAACS,SAAS,EAAE,GAAG,CAAC,CAAC;UACvD;QACF,KAAK,QAAQ;UACXO,OAAO,GAAGpF,aAAa,CAAC,OAAO,CAACoE,MAAM,CAAC,CAACY,YAAY,CAACnM,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAACuL,MAAM,CAACQ,UAAU,EAAE,MAAM,CAAC,CAACR,MAAM,CAACS,SAAS,EAAE,QAAQ,CAAC,CAAC;UACrI;QACF;UACEO,OAAO,GAAGpF,aAAa,CAAC,OAAO,CAACoE,MAAM,CAACY,YAAY,CAACnM,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAACuL,MAAM,CAACQ,UAAU,EAAE,GAAG,CAAC,CAAC;UAChG;MACJ;MACA,IAAIS,UAAU,GAAG,EAAE;MACnB,IAAI/D,UAAU,EAAE;QACd,IAAID,SAAS,GAAG2D,YAAY,CAAC,CAAC,CAAC,CAAChE,KAAK;QACrC,IAAIA,KAAK,GAAG,IAAI,CAAC/G,KAAK,CAAC+G,KAAK;QAC5BqE,UAAU,CAAChH,IAAI,CAAC,QAAQ,CAAC+F,MAAM,CAAC,CAAClE,QAAQ,CAACc,KAAK,CAAC,GAAGA,KAAK,GAAGK,SAAS,GAAG,CAAC,IAAIA,SAAS,EAAE,GAAG,CAAC,CAAC;MAC9F;MACA,IAAIsD,KAAK,EAAE;QACTU,UAAU,CAAChH,IAAI,CAAC,SAAS,CAAC+F,MAAM,CAACO,KAAK,EAAE,IAAI,CAAC,CAACP,MAAM,CAACc,CAAC,EAAE,IAAI,CAAC,CAACd,MAAM,CAACe,CAAC,EAAE,GAAG,CAAC,CAAC;MAC/E;MACA,IAAIE,UAAU,CAACxM,MAAM,EAAE;QACrBkM,SAAS,CAACO,SAAS,GAAGD,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC;MAC5C;MACA,OAAO,aAAazF,KAAK,CAAC0F,aAAa,CAAC,MAAM,EAAElN,QAAQ,CAAC,CAAC,CAAC,EAAE+H,WAAW,CAAC0E,SAAS,EAAE,IAAI,CAAC,EAAE;QACzFG,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJL,SAAS,EAAE7E,UAAU,CAAC,eAAe,EAAE6E,SAAS,CAAC;QACjDL,UAAU,EAAEA,UAAU;QACtBgB,IAAI,EAAEV,SAAS,CAACU,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,GAAG7B,IAAI,CAAC8B,YAAY,CAACF,IAAI,GAAGV,SAAS,CAACU;MAC5E,CAAC,CAAC,EAAET,YAAY,CAAClE,GAAG,CAAC,UAAU8E,IAAI,EAAEvD,KAAK,EAAE;QAC1C,QACE;UACA;UACAvC,KAAK,CAAC0F,aAAa,CAAC,OAAO,EAAE;YAC3BN,CAAC,EAAEA,CAAC;YACJV,EAAE,EAAEnC,KAAK,KAAK,CAAC,GAAG+C,OAAO,GAAGR,UAAU;YACtC7L,GAAG,EAAEsJ;UACP,CAAC,EAAEuD,IAAI,CAACnF,KAAK,CAAC8E,IAAI,CAAC5E,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC;QAAC;MAE5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,EAAE,CAAC;IACH5H,GAAG,EAAE,0BAA0B;IAC/BgC,KAAK,EAAE,SAAS8K,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC9E,KAAK,KAAK+E,SAAS,CAACC,SAAS,IAAIF,SAAS,CAACxE,UAAU,KAAKyE,SAAS,CAACE,cAAc,IAAIH,SAAS,CAACpF,QAAQ,KAAKqF,SAAS,CAACG,YAAY,IAAIJ,SAAS,CAAC7E,KAAK,KAAK8E,SAAS,CAACI,SAAS,IAAIL,SAAS,CAACnF,QAAQ,KAAKoF,SAAS,CAACK,YAAY,EAAE;QAC7O,IAAI5C,aAAa,GAAGsC,SAAS,CAACpF,QAAQ,KAAKqF,SAAS,CAACG,YAAY,IAAIJ,SAAS,CAAC7E,KAAK,KAAK8E,SAAS,CAACI,SAAS,IAAIL,SAAS,CAACnF,QAAQ,KAAKoF,SAAS,CAACK,YAAY;QAC7J,OAAO;UACLJ,SAAS,EAAEF,SAAS,CAAC9E,KAAK;UAC1BiF,cAAc,EAAEH,SAAS,CAACxE,UAAU;UACpC4E,YAAY,EAAEJ,SAAS,CAACpF,QAAQ;UAChCyF,SAAS,EAAEL,SAAS,CAAC7E,KAAK;UAC1B+D,YAAY,EAAEzB,eAAe,CAACuC,SAAS,EAAEtC,aAAa;QACxD,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOK,IAAI;AACb,CAAC,CAAC9D,SAAS,CAAC;AACZd,eAAe,CAAC4E,IAAI,EAAE,cAAc,EAAE;EACpCqB,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJP,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE,QAAQ;EACnB;EACAvD,UAAU,EAAE,KAAK;EACjBmD,UAAU,EAAE,OAAO;EACnBC,cAAc,EAAE,KAAK;EACrB;EACAe,IAAI,EAAE;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}