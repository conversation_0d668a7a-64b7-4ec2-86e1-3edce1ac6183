{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options;\n  return {\n    label: label || (childrenAsData ? 'children' : 'label'),\n    value: value || 'value',\n    options: options || 'options'\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options;\n  function dig(list, isGroupOption) {\n    list.forEach(function (data) {\n      var label = data[fieldLabel];\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: label,\n          value: value\n        });\n      } else {\n        var grpLabel = label;\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport function getSeparatedContent(text, tokens) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(function (unit) {\n      return unit;\n    });\n  }\n  var list = separate(text, tokens);\n  return match ? list : null;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_toArray", "_objectSpread", "warning", "<PERSON><PERSON><PERSON>", "data", "index", "key", "value", "undefined", "concat", "fillFieldNames", "fieldNames", "childrenAsData", "_ref", "label", "options", "flattenOptions", "_ref2", "arguments", "length", "flattenList", "_fillField<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "fieldValue", "fieldOptions", "dig", "list", "isGroupOption", "for<PERSON>ach", "push", "groupOption", "grpLabel", "group", "injectPropsWithOption", "option", "newOption", "Object", "defineProperty", "get", "getSeparatedContent", "text", "tokens", "match", "separate", "str", "_ref3", "_ref4", "token", "restTokens", "slice", "split", "reduce", "prevList", "unitStr", "filter", "unit"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-select/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options;\n  return {\n    label: label || (childrenAsData ? 'children' : 'label'),\n    value: value || 'value',\n    options: options || 'options'\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options;\n  function dig(list, isGroupOption) {\n    list.forEach(function (data) {\n      var label = data[fieldLabel];\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: label,\n          value: value\n        });\n      } else {\n        var grpLabel = label;\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport function getSeparatedContent(text, tokens) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(function (unit) {\n      return unit;\n    });\n  }\n  var list = separate(text, tokens);\n  return match ? list : null;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC3B,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;EAClB,IAAIC,KAAK;EACT,IAAI,OAAO,IAAIH,IAAI,EAAE;IACnBG,KAAK,GAAGH,IAAI,CAACG,KAAK;EACpB;EACA,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACrC,OAAOF,GAAG;EACZ;EACA,IAAIC,KAAK,KAAKC,SAAS,EAAE;IACvB,OAAOD,KAAK;EACd;EACA,OAAO,eAAe,CAACE,MAAM,CAACJ,KAAK,CAAC;AACtC;AACA,OAAO,SAASK,cAAcA,CAACC,UAAU,EAAEC,cAAc,EAAE;EACzD,IAAIC,IAAI,GAAGF,UAAU,IAAI,CAAC,CAAC;IACzBG,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAClBQ,OAAO,GAAGF,IAAI,CAACE,OAAO;EACxB,OAAO;IACLD,KAAK,EAAEA,KAAK,KAAKF,cAAc,GAAG,UAAU,GAAG,OAAO,CAAC;IACvDL,KAAK,EAAEA,KAAK,IAAI,OAAO;IACvBQ,OAAO,EAAEA,OAAO,IAAI;EACtB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACD,OAAO,EAAE;EACtC,IAAIE,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKV,SAAS,GAAGU,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChFP,UAAU,GAAGM,KAAK,CAACN,UAAU;IAC7BC,cAAc,GAAGK,KAAK,CAACL,cAAc;EACvC,IAAIQ,WAAW,GAAG,EAAE;EACpB,IAAIC,eAAe,GAAGX,cAAc,CAACC,UAAU,EAAE,KAAK,CAAC;IACrDW,UAAU,GAAGD,eAAe,CAACP,KAAK;IAClCS,UAAU,GAAGF,eAAe,CAACd,KAAK;IAClCiB,YAAY,GAAGH,eAAe,CAACN,OAAO;EACxC,SAASU,GAAGA,CAACC,IAAI,EAAEC,aAAa,EAAE;IAChCD,IAAI,CAACE,OAAO,CAAC,UAAUxB,IAAI,EAAE;MAC3B,IAAIU,KAAK,GAAGV,IAAI,CAACkB,UAAU,CAAC;MAC5B,IAAIK,aAAa,IAAI,EAAEH,YAAY,IAAIpB,IAAI,CAAC,EAAE;QAC5C,IAAIG,KAAK,GAAGH,IAAI,CAACmB,UAAU,CAAC;;QAE5B;QACAH,WAAW,CAACS,IAAI,CAAC;UACfvB,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEgB,WAAW,CAACD,MAAM,CAAC;UACrCW,WAAW,EAAEH,aAAa;UAC1BvB,IAAI,EAAEA,IAAI;UACVU,KAAK,EAAEA,KAAK;UACZP,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIwB,QAAQ,GAAGjB,KAAK;QACpB,IAAIiB,QAAQ,KAAKvB,SAAS,IAAII,cAAc,EAAE;UAC5CmB,QAAQ,GAAG3B,IAAI,CAACU,KAAK;QACvB;;QAEA;QACAM,WAAW,CAACS,IAAI,CAAC;UACfvB,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEgB,WAAW,CAACD,MAAM,CAAC;UACrCa,KAAK,EAAE,IAAI;UACX5B,IAAI,EAAEA,IAAI;UACVU,KAAK,EAAEiB;QACT,CAAC,CAAC;QACFN,GAAG,CAACrB,IAAI,CAACoB,YAAY,CAAC,EAAE,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EACAC,GAAG,CAACV,OAAO,EAAE,KAAK,CAAC;EACnB,OAAOK,WAAW;AACpB;;AAEA;AACA;AACA;AACA,OAAO,SAASa,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,IAAIC,SAAS,GAAGlC,aAAa,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAAC;EACzC,IAAI,EAAE,OAAO,IAAIC,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBpC,OAAO,CAAC,KAAK,EAAE,+GAA+G,CAAC;QAC/H,OAAOiC,SAAS;MAClB;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,SAAS;AAClB;AACA,OAAO,SAASI,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChD,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACtB,MAAM,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAIuB,KAAK,GAAG,KAAK;EACjB,SAASC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC5B,IAAIC,KAAK,GAAG9C,QAAQ,CAAC6C,KAAK,CAAC;MACzBE,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;MAChBE,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACF,KAAK,EAAE;MACV,OAAO,CAACH,GAAG,CAAC;IACd;IACA,IAAIlB,IAAI,GAAGkB,GAAG,CAACM,KAAK,CAACH,KAAK,CAAC;IAC3BL,KAAK,GAAGA,KAAK,IAAIhB,IAAI,CAACP,MAAM,GAAG,CAAC;IAChC,OAAOO,IAAI,CAACyB,MAAM,CAAC,UAAUC,QAAQ,EAAEC,OAAO,EAAE;MAC9C,OAAO,EAAE,CAAC5C,MAAM,CAACV,kBAAkB,CAACqD,QAAQ,CAAC,EAAErD,kBAAkB,CAAC4C,QAAQ,CAACU,OAAO,EAAEL,UAAU,CAAC,CAAC,CAAC;IACnG,CAAC,EAAE,EAAE,CAAC,CAACM,MAAM,CAAC,UAAUC,IAAI,EAAE;MAC5B,OAAOA,IAAI;IACb,CAAC,CAAC;EACJ;EACA,IAAI7B,IAAI,GAAGiB,QAAQ,CAACH,IAAI,EAAEC,MAAM,CAAC;EACjC,OAAOC,KAAK,GAAGhB,IAAI,GAAG,IAAI;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}