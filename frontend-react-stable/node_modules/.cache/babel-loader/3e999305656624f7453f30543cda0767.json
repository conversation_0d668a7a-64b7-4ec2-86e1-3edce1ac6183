{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nvar SkeletonImage = function SkeletonImage(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    active = props.active;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: \"\".concat(prefixCls, \"-image-svg\")\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: \"\".concat(prefixCls, \"-image-path\")\n  }))));\n};\nexport default SkeletonImage;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "ConfigContext", "path", "SkeletonImage", "props", "customizePrefixCls", "prefixCls", "className", "style", "active", "_React$useContext", "useContext", "getPrefixCls", "cls", "concat", "createElement", "viewBox", "xmlns", "d"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/skeleton/Image.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nvar SkeletonImage = function SkeletonImage(props) {\n  var customizePrefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    active = props.active;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls;\n  var prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-element\"), _defineProperty({}, \"\".concat(prefixCls, \"-active\"), active), className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-image\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: \"\".concat(prefixCls, \"-image-svg\")\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: \"\".concat(prefixCls, \"-image-path\")\n  }))));\n};\nexport default SkeletonImage;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,IAAI,GAAG,i3BAAi3B;AAC53B,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EAChD,IAAIC,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,aAAa,CAAC;IACrDW,YAAY,GAAGF,iBAAiB,CAACE,YAAY;EAC/C,IAAIN,SAAS,GAAGM,YAAY,CAAC,UAAU,EAAEP,kBAAkB,CAAC;EAC5D,IAAIQ,GAAG,GAAGd,UAAU,CAACO,SAAS,EAAE,EAAE,CAACQ,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC,EAAER,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACgB,MAAM,CAACR,SAAS,EAAE,SAAS,CAAC,EAAEG,MAAM,CAAC,EAAEF,SAAS,CAAC;EAC1I,OAAO,aAAaP,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IAC7CR,SAAS,EAAEM;EACb,CAAC,EAAE,aAAab,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACzCR,SAAS,EAAER,UAAU,CAAC,EAAE,CAACe,MAAM,CAACR,SAAS,EAAE,QAAQ,CAAC,EAAEC,SAAS,CAAC;IAChEC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaR,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACzCC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE,4BAA4B;IACnCV,SAAS,EAAE,EAAE,CAACO,MAAM,CAACR,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAE,aAAaN,KAAK,CAACe,aAAa,CAAC,MAAM,EAAE;IAC1CG,CAAC,EAAEhB,IAAI;IACPK,SAAS,EAAE,EAAE,CAACO,MAAM,CAACR,SAAS,EAAE,aAAa;EAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}