{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar Link = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var ellipsis = _a.ellipsis,\n    rel = _a.rel,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  process.env.NODE_ENV !== \"production\" ? warning(_typeof(ellipsis) !== 'object', 'Typography.Link', '`ellipsis` only supports boolean value.') : void 0;\n  var mergedProps = _extends(_extends({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, _extends({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;", "map": {"version": 3, "names": ["_extends", "_typeof", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "warning", "Base", "Link", "forwardRef", "_a", "ref", "ellipsis", "rel", "restProps", "process", "env", "NODE_ENV", "mergedProps", "undefined", "target", "navigate", "createElement", "component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Link.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport warning from '../_util/warning';\nimport Base from './Base';\nvar Link = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var ellipsis = _a.ellipsis,\n    rel = _a.rel,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  process.env.NODE_ENV !== \"production\" ? warning(_typeof(ellipsis) !== 'object', 'Typography.Link', '`ellipsis` only supports boolean value.') : void 0;\n  var mergedProps = _extends(_extends({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(Base, _extends({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nexport default Link;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,IAAI,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAC1D,IAAIC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;IACxBC,GAAG,GAAGH,EAAE,CAACG,GAAG;IACZC,SAAS,GAAGvB,MAAM,CAACmB,EAAE,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;EAC7CK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,OAAO,CAAChB,OAAO,CAACsB,QAAQ,CAAC,KAAK,QAAQ,EAAE,iBAAiB,EAAE,yCAAyC,CAAC,GAAG,KAAK,CAAC;EACtJ,IAAIM,WAAW,GAAG7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,SAAS,CAAC,EAAE;IAClDD,GAAG,EAAEA,GAAG,KAAKM,SAAS,IAAIL,SAAS,CAACM,MAAM,KAAK,QAAQ,GAAG,qBAAqB,GAAGP;EACpF,CAAC,CAAC;EACF;EACA,OAAOK,WAAW,CAACG,QAAQ;EAC3B,OAAO,aAAahB,KAAK,CAACiB,aAAa,CAACf,IAAI,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,WAAW,EAAE;IACtEP,GAAG,EAAEA,GAAG;IACRC,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBW,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAef,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}