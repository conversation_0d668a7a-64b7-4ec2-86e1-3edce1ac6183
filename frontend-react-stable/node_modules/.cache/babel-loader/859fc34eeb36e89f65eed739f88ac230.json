{"ast": null, "code": "/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */import{useState,useEffect,useCallback,useRef}from'react';import{message,notification}from'antd';import taskApi,{TASK_STATUS,TASK_TYPE}from'../services/taskApi';export const useTaskManager=()=>{const[tasks,setTasks]=useState([]);const[loading,setLoading]=useState(false);const[runningTasks,setRunningTasks]=useState([]);const[completedTasks,setCompletedTasks]=useState([]);const[initialized,setInitialized]=useState(false);const pollingIntervals=useRef(new Map());// 存储轮询定时器\n/**\n   * 获取所有任务\n   */const fetchAllTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;try{setLoading(true);const response=await taskApi.getAllTasks();if(response.success){setTasks(response.tasks||[]);}}catch(error){console.error('获取任务列表失败:',error);if(showError){message.error('获取任务列表失败');}}finally{setLoading(false);}},[]);/**\n   * 获取正在运行的任务\n   */const fetchRunningTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{const response=await taskApi.getRunningTasks();if(response.success){setRunningTasks(response.tasks||[]);}}catch(error){console.error('获取运行中任务失败:',error);if(showError){message.error('获取运行中任务失败');}}},[]);/**\n   * 获取已完成的任务\n   */const fetchCompletedTasks=useCallback(async function(){let showError=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{const response=await taskApi.getCompletedTasks();if(response.success){setCompletedTasks(response.tasks||[]);}}catch(error){console.error('获取已完成任务失败:',error);if(showError){message.error('获取已完成任务失败');}}},[]);/**\n   * 删除单个已完成的任务\n   */const deleteSingleTask=useCallback(async taskId=>{try{const response=await taskApi.deleteSingleTask(taskId);if(response.success){// 从已完成任务列表中移除\nsetCompletedTasks(prev=>prev.filter(task=>task.task_id!==taskId));message.success(`已删除任务: ${taskId.substring(0,8)}...`);return true;}return false;}catch(error){var _error$response,_error$response$data;console.error('删除任务失败:',error);const errorMessage=((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'删除任务失败';message.error(errorMessage);return false;}},[]);/**\n   * 清空所有已完成的任务\n   */const clearCompletedTasks=useCallback(async()=>{try{const response=await taskApi.clearCompletedTasks();if(response.success){setCompletedTasks([]);message.success(`已清空 ${response.cleared_count} 个完成的任务`);}}catch(error){console.error('清空已完成任务失败:',error);message.error('清空已完成任务失败');}},[]);/**\n   * 启动任务轮询\n   */const startPolling=useCallback((taskId,onProgress)=>{// 如果已经在轮询，先清除\nif(pollingIntervals.current.has(taskId)){clearInterval(pollingIntervals.current.get(taskId));}const interval=setInterval(async()=>{try{const response=await taskApi.getTaskStatus(taskId);const task=response.task;if(!task)return;// 更新任务列表中的对应任务\nsetTasks(prevTasks=>{const index=prevTasks.findIndex(t=>t.task_id===taskId);if(index>=0){const newTasks=[...prevTasks];newTasks[index]=task;return newTasks;}else{return[...prevTasks,task];}});// 调用进度回调\nif(onProgress){onProgress(task);}// 如果任务完成，停止轮询并发送通知\nif([TASK_STATUS.COMPLETED,TASK_STATUS.FAILED,TASK_STATUS.CANCELLED].includes(task.status)){clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);// 发送通知\nif(task.status===TASK_STATUS.COMPLETED){notification.success({message:'🎉 任务完成',description:`${taskApi.formatTaskType(task.task_type)}任务已成功完成！点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细结果`,duration:10,onClick:()=>{// 跳转到任务管理页面\nwindow.location.hash='#/task-manager';},style:{cursor:'pointer'}});}else if(task.status===TASK_STATUS.FAILED){notification.error({message:'❌ 任务失败',description:`${taskApi.formatTaskType(task.task_type)}任务执行失败: ${task.error||'未知错误'}。点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细错误信息`,duration:15,onClick:()=>{// 跳转到任务管理页面\nwindow.location.hash='#/task-manager';},style:{cursor:'pointer'}});}else if(task.status===TASK_STATUS.CANCELLED){notification.warning({message:'⚠️ 任务已取消',description:`${taskApi.formatTaskType(task.task_type)}任务已被取消`,duration:5});}// 更新运行中任务列表和已完成任务列表\nfetchRunningTasks();fetchCompletedTasks();}}catch(error){console.error(`轮询任务 ${taskId} 状态失败:`,error);clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);}},2000);pollingIntervals.current.set(taskId,interval);},[fetchRunningTasks,fetchCompletedTasks]);/**\n   * 停止任务轮询\n   */const stopPolling=useCallback(taskId=>{if(pollingIntervals.current.has(taskId)){clearInterval(pollingIntervals.current.get(taskId));pollingIntervals.current.delete(taskId);}},[]);/**\n   * 初始化任务管理器（延迟初始化）\n   */const initializeTaskManager=useCallback(async()=>{if(initialized)return;try{// 尝试获取运行中任务\nawait fetchRunningTasks(false);// 为已存在的运行中任务启动轮询\nconst response=await taskApi.getRunningTasks();if(response.success&&response.tasks){response.tasks.forEach(task=>{startPolling(task.task_id);});}setInitialized(true);}catch(error){console.warn('初始化任务管理失败:',error);// 静默失败，不影响用户体验\n}},[initialized,fetchRunningTasks,startPolling]);/**\n   * 提交训练任务\n   */const submitTrainingTask=useCallback(async formData=>{try{// 确保任务管理器已初始化\nawait initializeTaskManager();const response=await taskApi.startTrainingAsync(formData);if(response.success){const taskId=response.task_id;if(taskId){message.success('🚀 训练任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果',6);// 开始轮询任务状态\nstartPolling(taskId);// 更新运行中任务列表\nfetchRunningTasks(false);return taskId;}}}catch(error){var _error$response2,_error$response2$data;console.error('启动训练任务失败:',error);message.error('启动训练任务失败: '+(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message));throw error;}},[initializeTaskManager,startPolling,fetchRunningTasks]);/**\n   * 提交预测任务\n   */const submitPredictionTask=useCallback(async formData=>{try{// 确保任务管理器已初始化\nawait initializeTaskManager();const response=await taskApi.startPredictionAsync(formData);if(response.success){const taskId=response.task_id;if(taskId){message.success('🚀 预测任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果',6);// 开始轮询任务状态\nstartPolling(taskId);// 更新运行中任务列表\nfetchRunningTasks(false);return taskId;}}}catch(error){var _error$response3,_error$response3$data;console.error('启动预测任务失败:',error);message.error('启动预测任务失败: '+(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message));throw error;}},[initializeTaskManager,startPolling,fetchRunningTasks]);/**\n   * 取消任务\n   */const cancelTask=useCallback(async taskId=>{try{const response=await taskApi.cancelTask(taskId);if(response.success){message.success('任务已取消');// 停止轮询\nstopPolling(taskId);// 刷新任务列表\nfetchAllTasks();fetchRunningTasks();}}catch(error){var _error$response4,_error$response4$data;console.error('取消任务失败:',error);message.error('取消任务失败: '+(((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message));}},[stopPolling,fetchAllTasks,fetchRunningTasks]);/**\n   * 获取任务详情\n   */const getTaskDetail=useCallback(async taskId=>{try{const response=await taskApi.getTaskStatus(taskId);return response.task||null;}catch(error){console.error('获取任务详情失败:',error);message.error('获取任务详情失败');return null;}},[]);// 组件挂载时初始化，获取已完成任务\nuseEffect(()=>{// 初始化时获取已完成任务\nfetchCompletedTasks();// 清理函数：清除所有轮询\nreturn()=>{// eslint-disable-next-line react-hooks/exhaustive-deps\nconst intervals=pollingIntervals.current;intervals.forEach(interval=>clearInterval(interval));intervals.clear();};},[fetchCompletedTasks]);return{// 状态\ntasks,runningTasks,completedTasks,loading,// 方法\nfetchAllTasks,fetchRunningTasks,fetchCompletedTasks,deleteSingleTask,clearCompletedTasks,submitTrainingTask,submitPredictionTask,cancelTask,getTaskDetail,startPolling,stopPolling,// 结果获取方法\ngetTaskResult:useCallback(taskId=>{const task=completedTasks.find(t=>t.task_id===taskId);return(task===null||task===void 0?void 0:task.result)||null;},[completedTasks]),getCompletedTasksByType:useCallback(taskType=>{return completedTasks.filter(task=>task.task_type===taskType&&task.status===TASK_STATUS.COMPLETED&&task.result);},[completedTasks]),// 工具方法\nformatTaskStatus:taskApi.formatTaskStatus,formatTaskType:taskApi.formatTaskType,getTaskStatusColor:taskApi.getTaskStatusColor,calculateTaskDuration:taskApi.calculateTaskDuration,// 常量\nTASK_STATUS,TASK_TYPE};};export default useTaskManager;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "message", "notification", "taskApi", "TASK_STATUS", "TASK_TYPE", "useTaskManager", "tasks", "setTasks", "loading", "setLoading", "runningTasks", "setRunningTasks", "completedTasks", "setCompletedTasks", "initialized", "setInitialized", "pollingIntervals", "Map", "fetchAllTasks", "showError", "arguments", "length", "undefined", "response", "getAllTasks", "success", "error", "console", "fetchRunningTasks", "getRunningTasks", "fetchCompletedTasks", "getCompletedTasks", "deleteSingleTask", "taskId", "prev", "filter", "task", "task_id", "substring", "_error$response", "_error$response$data", "errorMessage", "data", "detail", "clearCompletedTasks", "cleared_count", "startPolling", "onProgress", "current", "has", "clearInterval", "get", "interval", "setInterval", "getTaskStatus", "prevTasks", "index", "findIndex", "t", "newTasks", "COMPLETED", "FAILED", "CANCELLED", "includes", "status", "delete", "description", "formatTaskType", "task_type", "duration", "onClick", "window", "location", "hash", "style", "cursor", "warning", "set", "stopPolling", "initializeTaskManager", "for<PERSON>ach", "warn", "submitTrainingTask", "formData", "startTrainingAsync", "_error$response2", "_error$response2$data", "submitPredictionTask", "startPredictionAsync", "_error$response3", "_error$response3$data", "cancelTask", "_error$response4", "_error$response4$data", "getTaskDetail", "intervals", "clear", "getTaskResult", "find", "result", "getCompletedTasksByType", "taskType", "formatTaskStatus", "getTaskStatusColor", "calculateTaskDuration"], "sources": ["/home/<USER>/frontend-react-stable/src/hooks/useTaskManager.ts"], "sourcesContent": ["/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { message, notification } from 'antd';\nimport taskApi, {\n  TASK_STATUS,\n  TASK_TYPE,\n  Task\n} from '../services/taskApi';\n\nexport const useTaskManager = () => {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [runningTasks, setRunningTasks] = useState<Task[]>([]);\n  const [completedTasks, setCompletedTasks] = useState<Task[]>([]);\n  const [initialized, setInitialized] = useState(false);\n  const pollingIntervals = useRef(new Map<string, NodeJS.Timeout>()); // 存储轮询定时器\n\n  /**\n   * 获取所有任务\n   */\n  const fetchAllTasks = useCallback(async (showError = true) => {\n    try {\n      setLoading(true);\n      const response = await taskApi.getAllTasks();\n      if (response.success) {\n        setTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取任务列表失败:', error);\n      if (showError) {\n        message.error('获取任务列表失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * 获取正在运行的任务\n   */\n  const fetchRunningTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getRunningTasks();\n      if (response.success) {\n        setRunningTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取运行中任务失败:', error);\n      if (showError) {\n        message.error('获取运行中任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 获取已完成的任务\n   */\n  const fetchCompletedTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getCompletedTasks();\n      if (response.success) {\n        setCompletedTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取已完成任务失败:', error);\n      if (showError) {\n        message.error('获取已完成任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 删除单个已完成的任务\n   */\n  const deleteSingleTask = useCallback(async (taskId: string): Promise<boolean> => {\n    try {\n      const response = await taskApi.deleteSingleTask(taskId);\n      if (response.success) {\n        // 从已完成任务列表中移除\n        setCompletedTasks(prev => prev.filter(task => task.task_id !== taskId));\n        message.success(`已删除任务: ${taskId.substring(0, 8)}...`);\n        return true;\n      }\n      return false;\n    } catch (error: any) {\n      console.error('删除任务失败:', error);\n      const errorMessage = error.response?.data?.detail || '删除任务失败';\n      message.error(errorMessage);\n      return false;\n    }\n  }, []);\n\n  /**\n   * 清空所有已完成的任务\n   */\n  const clearCompletedTasks = useCallback(async () => {\n    try {\n      const response = await taskApi.clearCompletedTasks();\n      if (response.success) {\n        setCompletedTasks([]);\n        message.success(`已清空 ${response.cleared_count} 个完成的任务`);\n      }\n    } catch (error) {\n      console.error('清空已完成任务失败:', error);\n      message.error('清空已完成任务失败');\n    }\n  }, []);\n\n  /**\n   * 启动任务轮询\n   */\n  const startPolling = useCallback((taskId: string, onProgress?: (task: Task) => void) => {\n    // 如果已经在轮询，先清除\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const response = await taskApi.getTaskStatus(taskId);\n        const task = response.task;\n\n        if (!task) return;\n\n        // 更新任务列表中的对应任务\n        setTasks(prevTasks => {\n          const index = prevTasks.findIndex(t => t.task_id === taskId);\n          if (index >= 0) {\n            const newTasks = [...prevTasks];\n            newTasks[index] = task;\n            return newTasks;\n          } else {\n            return [...prevTasks, task];\n          }\n        });\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 如果任务完成，停止轮询并发送通知\n        if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED].includes(task.status as any)) {\n          clearInterval(pollingIntervals.current.get(taskId)!);\n          pollingIntervals.current.delete(taskId);\n\n          // 发送通知\n          if (task.status === TASK_STATUS.COMPLETED) {\n            notification.success({\n              message: '🎉 任务完成',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已成功完成！点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细结果`,\n              duration: 10,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.FAILED) {\n            notification.error({\n              message: '❌ 任务失败',\n              description: `${taskApi.formatTaskType(task.task_type)}任务执行失败: ${task.error || '未知错误'}。点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细错误信息`,\n              duration: 15,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.CANCELLED) {\n            notification.warning({\n              message: '⚠️ 任务已取消',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已被取消`,\n              duration: 5,\n            });\n          }\n\n          // 更新运行中任务列表和已完成任务列表\n          fetchRunningTasks();\n          fetchCompletedTasks();\n        }\n      } catch (error) {\n        console.error(`轮询任务 ${taskId} 状态失败:`, error);\n        clearInterval(pollingIntervals.current.get(taskId)!);\n        pollingIntervals.current.delete(taskId);\n      }\n    }, 2000);\n\n    pollingIntervals.current.set(taskId, interval);\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  /**\n   * 停止任务轮询\n   */\n  const stopPolling = useCallback((taskId: string) => {\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n      pollingIntervals.current.delete(taskId);\n    }\n  }, []);\n\n  /**\n   * 初始化任务管理器（延迟初始化）\n   */\n  const initializeTaskManager = useCallback(async () => {\n    if (initialized) return;\n\n    try {\n      // 尝试获取运行中任务\n      await fetchRunningTasks(false);\n\n      // 为已存在的运行中任务启动轮询\n      const response = await taskApi.getRunningTasks();\n      if (response.success && response.tasks) {\n        response.tasks.forEach(task => {\n          startPolling(task.task_id);\n        });\n      }\n\n      setInitialized(true);\n    } catch (error) {\n      console.warn('初始化任务管理失败:', error);\n      // 静默失败，不影响用户体验\n    }\n  }, [initialized, fetchRunningTasks, startPolling]);\n\n  /**\n   * 提交训练任务\n   */\n  const submitTrainingTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startTrainingAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 训练任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动训练任务失败:', error);\n      message.error('启动训练任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 提交预测任务\n   */\n  const submitPredictionTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startPredictionAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 预测任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动预测任务失败:', error);\n      message.error('启动预测任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 取消任务\n   */\n  const cancelTask = useCallback(async (taskId: string) => {\n    try {\n      const response = await taskApi.cancelTask(taskId);\n      if (response.success) {\n        message.success('任务已取消');\n        \n        // 停止轮询\n        stopPolling(taskId);\n        \n        // 刷新任务列表\n        fetchAllTasks();\n        fetchRunningTasks();\n      }\n    } catch (error: any) {\n      console.error('取消任务失败:', error);\n      message.error('取消任务失败: ' + (error.response?.data?.detail || error.message));\n    }\n  }, [stopPolling, fetchAllTasks, fetchRunningTasks]);\n\n  /**\n   * 获取任务详情\n   */\n  const getTaskDetail = useCallback(async (taskId: string): Promise<Task | null> => {\n    try {\n      const response = await taskApi.getTaskStatus(taskId);\n      return response.task || null;\n    } catch (error) {\n      console.error('获取任务详情失败:', error);\n      message.error('获取任务详情失败');\n      return null;\n    }\n  }, []);\n\n  // 组件挂载时初始化，获取已完成任务\n  useEffect(() => {\n    // 初始化时获取已完成任务\n    fetchCompletedTasks();\n\n    // 清理函数：清除所有轮询\n    return () => {\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const intervals = pollingIntervals.current;\n      intervals.forEach(interval => clearInterval(interval));\n      intervals.clear();\n    };\n  }, [fetchCompletedTasks]);\n\n  return {\n    // 状态\n    tasks,\n    runningTasks,\n    completedTasks,\n    loading,\n\n    // 方法\n    fetchAllTasks,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    deleteSingleTask,\n    clearCompletedTasks,\n    submitTrainingTask,\n    submitPredictionTask,\n    cancelTask,\n    getTaskDetail,\n    startPolling,\n    stopPolling,\n\n    // 结果获取方法\n    getTaskResult: useCallback((taskId: string) => {\n      const task = completedTasks.find(t => t.task_id === taskId);\n      return task?.result || null;\n    }, [completedTasks]),\n\n    getCompletedTasksByType: useCallback((taskType: 'training' | 'prediction') => {\n      return completedTasks.filter(task =>\n        task.task_type === taskType &&\n        task.status === TASK_STATUS.COMPLETED &&\n        task.result\n      );\n    }, [completedTasks]),\n\n    // 工具方法\n    formatTaskStatus: taskApi.formatTaskStatus,\n    formatTaskType: taskApi.formatTaskType,\n    getTaskStatusColor: taskApi.getTaskStatusColor,\n    calculateTaskDuration: taskApi.calculateTaskDuration,\n\n    // 常量\n    TASK_STATUS,\n    TASK_TYPE\n  };\n};\n\nexport default useTaskManager;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CAChE,OAASC,OAAO,CAAEC,YAAY,KAAQ,MAAM,CAC5C,MAAO,CAAAC,OAAO,EACZC,WAAW,CACXC,SAAS,KAEJ,qBAAqB,CAE5B,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGX,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACY,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACc,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAoB,gBAAgB,CAAGjB,MAAM,CAAC,GAAI,CAAAkB,GAAG,CAAyB,CAAC,CAAC,CAAE;AAEpE;AACF;AACA,KACE,KAAM,CAAAC,aAAa,CAAGpB,WAAW,CAAC,gBAA4B,IAArB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACvD,GAAI,CACFX,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACsB,WAAW,CAAC,CAAC,CAC5C,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpBlB,QAAQ,CAACgB,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CAChC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAmB,iBAAiB,CAAG9B,WAAW,CAAC,gBAA6B,IAAtB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC5D,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC2B,eAAe,CAAC,CAAC,CAChD,GAAIN,QAAQ,CAACE,OAAO,CAAE,CACpBd,eAAe,CAACY,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CACvC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAI,mBAAmB,CAAGhC,WAAW,CAAC,gBAA6B,IAAtB,CAAAqB,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC9D,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC6B,iBAAiB,CAAC,CAAC,CAClD,GAAIR,QAAQ,CAACE,OAAO,CAAE,CACpBZ,iBAAiB,CAACU,QAAQ,CAACjB,KAAK,EAAI,EAAE,CAAC,CACzC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,GAAIP,SAAS,CAAE,CACbnB,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAM,gBAAgB,CAAGlC,WAAW,CAAC,KAAO,CAAAmC,MAAc,EAAuB,CAC/E,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC8B,gBAAgB,CAACC,MAAM,CAAC,CACvD,GAAIV,QAAQ,CAACE,OAAO,CAAE,CACpB;AACAZ,iBAAiB,CAACqB,IAAI,EAAIA,IAAI,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,OAAO,GAAKJ,MAAM,CAAC,CAAC,CACvEjC,OAAO,CAACyB,OAAO,CAAC,UAAUQ,MAAM,CAACK,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,KAAK,CAAC,CACtD,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOZ,KAAU,CAAE,KAAAa,eAAA,CAAAC,oBAAA,CACnBb,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,CAAAe,YAAY,CAAG,EAAAF,eAAA,CAAAb,KAAK,CAACH,QAAQ,UAAAgB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBG,IAAI,UAAAF,oBAAA,iBAApBA,oBAAA,CAAsBG,MAAM,GAAI,QAAQ,CAC7D3C,OAAO,CAAC0B,KAAK,CAACe,YAAY,CAAC,CAC3B,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAG,mBAAmB,CAAG9C,WAAW,CAAC,SAAY,CAClD,GAAI,CACF,KAAM,CAAAyB,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC0C,mBAAmB,CAAC,CAAC,CACpD,GAAIrB,QAAQ,CAACE,OAAO,CAAE,CACpBZ,iBAAiB,CAAC,EAAE,CAAC,CACrBb,OAAO,CAACyB,OAAO,CAAC,OAAOF,QAAQ,CAACsB,aAAa,SAAS,CAAC,CACzD,CACF,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,CAAC,CAC5B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAoB,YAAY,CAAGhD,WAAW,CAAC,CAACmC,MAAc,CAAEc,UAAiC,GAAK,CACtF;AACA,GAAI/B,gBAAgB,CAACgC,OAAO,CAACC,GAAG,CAAChB,MAAM,CAAC,CAAE,CACxCiB,aAAa,CAAClC,gBAAgB,CAACgC,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC,CACtD,CAEA,KAAM,CAAAmB,QAAQ,CAAGC,WAAW,CAAC,SAAY,CACvC,GAAI,CACF,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACoD,aAAa,CAACrB,MAAM,CAAC,CACpD,KAAM,CAAAG,IAAI,CAAGb,QAAQ,CAACa,IAAI,CAE1B,GAAI,CAACA,IAAI,CAAE,OAEX;AACA7B,QAAQ,CAACgD,SAAS,EAAI,CACpB,KAAM,CAAAC,KAAK,CAAGD,SAAS,CAACE,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACrB,OAAO,GAAKJ,MAAM,CAAC,CAC5D,GAAIuB,KAAK,EAAI,CAAC,CAAE,CACd,KAAM,CAAAG,QAAQ,CAAG,CAAC,GAAGJ,SAAS,CAAC,CAC/BI,QAAQ,CAACH,KAAK,CAAC,CAAGpB,IAAI,CACtB,MAAO,CAAAuB,QAAQ,CACjB,CAAC,IAAM,CACL,MAAO,CAAC,GAAGJ,SAAS,CAAEnB,IAAI,CAAC,CAC7B,CACF,CAAC,CAAC,CAEF;AACA,GAAIW,UAAU,CAAE,CACdA,UAAU,CAACX,IAAI,CAAC,CAClB,CAEA;AACA,GAAI,CAACjC,WAAW,CAACyD,SAAS,CAAEzD,WAAW,CAAC0D,MAAM,CAAE1D,WAAW,CAAC2D,SAAS,CAAC,CAACC,QAAQ,CAAC3B,IAAI,CAAC4B,MAAa,CAAC,CAAE,CACnGd,aAAa,CAAClC,gBAAgB,CAACgC,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC,CACpDjB,gBAAgB,CAACgC,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC,CAEvC;AACA,GAAIG,IAAI,CAAC4B,MAAM,GAAK7D,WAAW,CAACyD,SAAS,CAAE,CACzC3D,YAAY,CAACwB,OAAO,CAAC,CACnBzB,OAAO,CAAE,SAAS,CAClBkE,WAAW,CAAE,GAAGhE,OAAO,CAACiE,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,sCAAsC,CAC5FC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAEA,CAAA,GAAM,CACb;AACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,gBAAgB,CACzC,CAAC,CACDC,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAC7B,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIvC,IAAI,CAAC4B,MAAM,GAAK7D,WAAW,CAAC0D,MAAM,CAAE,CAC7C5D,YAAY,CAACyB,KAAK,CAAC,CACjB1B,OAAO,CAAE,QAAQ,CACjBkE,WAAW,CAAE,GAAGhE,OAAO,CAACiE,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,WAAWhC,IAAI,CAACV,KAAK,EAAI,MAAM,iCAAiC,CACtH2C,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAEA,CAAA,GAAM,CACb;AACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,gBAAgB,CACzC,CAAC,CACDC,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAC7B,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIvC,IAAI,CAAC4B,MAAM,GAAK7D,WAAW,CAAC2D,SAAS,CAAE,CAChD7D,YAAY,CAAC2E,OAAO,CAAC,CACnB5E,OAAO,CAAE,UAAU,CACnBkE,WAAW,CAAE,GAAGhE,OAAO,CAACiE,cAAc,CAAC/B,IAAI,CAACgC,SAAS,CAAC,QAAQ,CAC9DC,QAAQ,CAAE,CACZ,CAAC,CAAC,CACJ,CAEA;AACAzC,iBAAiB,CAAC,CAAC,CACnBE,mBAAmB,CAAC,CAAC,CACvB,CACF,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQO,MAAM,QAAQ,CAAEP,KAAK,CAAC,CAC5CwB,aAAa,CAAClC,gBAAgB,CAACgC,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC,CACpDjB,gBAAgB,CAACgC,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC,CACzC,CACF,CAAC,CAAE,IAAI,CAAC,CAERjB,gBAAgB,CAACgC,OAAO,CAAC6B,GAAG,CAAC5C,MAAM,CAAEmB,QAAQ,CAAC,CAChD,CAAC,CAAE,CAACxB,iBAAiB,CAAEE,mBAAmB,CAAC,CAAC,CAE5C;AACF;AACA,KACE,KAAM,CAAAgD,WAAW,CAAGhF,WAAW,CAAEmC,MAAc,EAAK,CAClD,GAAIjB,gBAAgB,CAACgC,OAAO,CAACC,GAAG,CAAChB,MAAM,CAAC,CAAE,CACxCiB,aAAa,CAAClC,gBAAgB,CAACgC,OAAO,CAACG,GAAG,CAAClB,MAAM,CAAE,CAAC,CACpDjB,gBAAgB,CAACgC,OAAO,CAACiB,MAAM,CAAChC,MAAM,CAAC,CACzC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAA8C,qBAAqB,CAAGjF,WAAW,CAAC,SAAY,CACpD,GAAIgB,WAAW,CAAE,OAEjB,GAAI,CACF;AACA,KAAM,CAAAc,iBAAiB,CAAC,KAAK,CAAC,CAE9B;AACA,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAAC2B,eAAe,CAAC,CAAC,CAChD,GAAIN,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACjB,KAAK,CAAE,CACtCiB,QAAQ,CAACjB,KAAK,CAAC0E,OAAO,CAAC5C,IAAI,EAAI,CAC7BU,YAAY,CAACV,IAAI,CAACC,OAAO,CAAC,CAC5B,CAAC,CAAC,CACJ,CAEAtB,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACsD,IAAI,CAAC,YAAY,CAAEvD,KAAK,CAAC,CACjC;AACF,CACF,CAAC,CAAE,CAACZ,WAAW,CAAEc,iBAAiB,CAAEkB,YAAY,CAAC,CAAC,CAElD;AACF;AACA,KACE,KAAM,CAAAoC,kBAAkB,CAAGpF,WAAW,CAAC,KAAO,CAAAqF,QAAkB,EAAkC,CAChG,GAAI,CACF;AACA,KAAM,CAAAJ,qBAAqB,CAAC,CAAC,CAE7B,KAAM,CAAAxD,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACkF,kBAAkB,CAACD,QAAQ,CAAC,CAC3D,GAAI5D,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAQ,MAAM,CAAGV,QAAQ,CAACc,OAAO,CAC/B,GAAIJ,MAAM,CAAE,CACVjC,OAAO,CAACyB,OAAO,CAAC,qCAAqC,CAAE,CAAC,CAAC,CAEzD;AACAqB,YAAY,CAACb,MAAM,CAAC,CAEpB;AACAL,iBAAiB,CAAC,KAAK,CAAC,CAExB,MAAO,CAAAK,MAAM,CACf,CACF,CACF,CAAE,MAAOP,KAAU,CAAE,KAAA2D,gBAAA,CAAAC,qBAAA,CACnB3D,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAI,EAAA2D,gBAAA,CAAA3D,KAAK,CAACH,QAAQ,UAAA8D,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3C,IAAI,UAAA4C,qBAAA,iBAApBA,qBAAA,CAAsB3C,MAAM,GAAIjB,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,KAAM,CAAA0B,KAAK,CACb,CACF,CAAC,CAAE,CAACqD,qBAAqB,CAAEjC,YAAY,CAAElB,iBAAiB,CAAC,CAAC,CAE5D;AACF;AACA,KACE,KAAM,CAAA2D,oBAAoB,CAAGzF,WAAW,CAAC,KAAO,CAAAqF,QAAkB,EAAkC,CAClG,GAAI,CACF;AACA,KAAM,CAAAJ,qBAAqB,CAAC,CAAC,CAE7B,KAAM,CAAAxD,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACsF,oBAAoB,CAACL,QAAQ,CAAC,CAC7D,GAAI5D,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAQ,MAAM,CAAGV,QAAQ,CAACc,OAAO,CAC/B,GAAIJ,MAAM,CAAE,CACVjC,OAAO,CAACyB,OAAO,CAAC,qCAAqC,CAAE,CAAC,CAAC,CAEzD;AACAqB,YAAY,CAACb,MAAM,CAAC,CAEpB;AACAL,iBAAiB,CAAC,KAAK,CAAC,CAExB,MAAO,CAAAK,MAAM,CACf,CACF,CACF,CAAE,MAAOP,KAAU,CAAE,KAAA+D,gBAAA,CAAAC,qBAAA,CACnB/D,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAI,EAAA+D,gBAAA,CAAA/D,KAAK,CAACH,QAAQ,UAAAkE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB/C,IAAI,UAAAgD,qBAAA,iBAApBA,qBAAA,CAAsB/C,MAAM,GAAIjB,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,KAAM,CAAA0B,KAAK,CACb,CACF,CAAC,CAAE,CAACqD,qBAAqB,CAAEjC,YAAY,CAAElB,iBAAiB,CAAC,CAAC,CAE5D;AACF;AACA,KACE,KAAM,CAAA+D,UAAU,CAAG7F,WAAW,CAAC,KAAO,CAAAmC,MAAc,EAAK,CACvD,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACyF,UAAU,CAAC1D,MAAM,CAAC,CACjD,GAAIV,QAAQ,CAACE,OAAO,CAAE,CACpBzB,OAAO,CAACyB,OAAO,CAAC,OAAO,CAAC,CAExB;AACAqD,WAAW,CAAC7C,MAAM,CAAC,CAEnB;AACAf,aAAa,CAAC,CAAC,CACfU,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAE,MAAOF,KAAU,CAAE,KAAAkE,gBAAA,CAAAC,qBAAA,CACnBlE,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAI,EAAAkE,gBAAA,CAAAlE,KAAK,CAACH,QAAQ,UAAAqE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBlD,IAAI,UAAAmD,qBAAA,iBAApBA,qBAAA,CAAsBlD,MAAM,GAAIjB,KAAK,CAAC1B,OAAO,CAAC,CAAC,CAC7E,CACF,CAAC,CAAE,CAAC8E,WAAW,CAAE5D,aAAa,CAAEU,iBAAiB,CAAC,CAAC,CAEnD;AACF;AACA,KACE,KAAM,CAAAkE,aAAa,CAAGhG,WAAW,CAAC,KAAO,CAAAmC,MAAc,EAA2B,CAChF,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAArB,OAAO,CAACoD,aAAa,CAACrB,MAAM,CAAC,CACpD,MAAO,CAAAV,QAAQ,CAACa,IAAI,EAAI,IAAI,CAC9B,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,CAAC,CACzB,MAAO,KAAI,CACb,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7B,SAAS,CAAC,IAAM,CACd;AACAiC,mBAAmB,CAAC,CAAC,CAErB;AACA,MAAO,IAAM,CACX;AACA,KAAM,CAAAiE,SAAS,CAAG/E,gBAAgB,CAACgC,OAAO,CAC1C+C,SAAS,CAACf,OAAO,CAAC5B,QAAQ,EAAIF,aAAa,CAACE,QAAQ,CAAC,CAAC,CACtD2C,SAAS,CAACC,KAAK,CAAC,CAAC,CACnB,CAAC,CACH,CAAC,CAAE,CAAClE,mBAAmB,CAAC,CAAC,CAEzB,MAAO,CACL;AACAxB,KAAK,CACLI,YAAY,CACZE,cAAc,CACdJ,OAAO,CAEP;AACAU,aAAa,CACbU,iBAAiB,CACjBE,mBAAmB,CACnBE,gBAAgB,CAChBY,mBAAmB,CACnBsC,kBAAkB,CAClBK,oBAAoB,CACpBI,UAAU,CACVG,aAAa,CACbhD,YAAY,CACZgC,WAAW,CAEX;AACAmB,aAAa,CAAEnG,WAAW,CAAEmC,MAAc,EAAK,CAC7C,KAAM,CAAAG,IAAI,CAAGxB,cAAc,CAACsF,IAAI,CAACxC,CAAC,EAAIA,CAAC,CAACrB,OAAO,GAAKJ,MAAM,CAAC,CAC3D,MAAO,CAAAG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE+D,MAAM,GAAI,IAAI,CAC7B,CAAC,CAAE,CAACvF,cAAc,CAAC,CAAC,CAEpBwF,uBAAuB,CAAEtG,WAAW,CAAEuG,QAAmC,EAAK,CAC5E,MAAO,CAAAzF,cAAc,CAACuB,MAAM,CAACC,IAAI,EAC/BA,IAAI,CAACgC,SAAS,GAAKiC,QAAQ,EAC3BjE,IAAI,CAAC4B,MAAM,GAAK7D,WAAW,CAACyD,SAAS,EACrCxB,IAAI,CAAC+D,MACP,CAAC,CACH,CAAC,CAAE,CAACvF,cAAc,CAAC,CAAC,CAEpB;AACA0F,gBAAgB,CAAEpG,OAAO,CAACoG,gBAAgB,CAC1CnC,cAAc,CAAEjE,OAAO,CAACiE,cAAc,CACtCoC,kBAAkB,CAAErG,OAAO,CAACqG,kBAAkB,CAC9CC,qBAAqB,CAAEtG,OAAO,CAACsG,qBAAqB,CAEpD;AACArG,WAAW,CACXC,SACF,CAAC,CACH,CAAC,CAED,cAAe,CAAAC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}