{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = identity;\nvar _linear = require(\"./linear.js\");\nvar _number = _interopRequireDefault(require(\"./number.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction identity(domain) {\n  var unknown;\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n  scale.invert = scale;\n  scale.domain = scale.range = function (_) {\n    return arguments.length ? (domain = Array.from(_, _number.default), scale) : domain.slice();\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return identity(domain).unknown(unknown);\n  };\n  domain = arguments.length ? Array.from(domain, _number.default) : [0, 1];\n  return (0, _linear.linearish)(scale);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "identity", "_linear", "require", "_number", "_interopRequireDefault", "obj", "__esModule", "domain", "unknown", "scale", "x", "isNaN", "invert", "range", "_", "arguments", "length", "Array", "from", "slice", "copy", "linearish"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/identity.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = identity;\n\nvar _linear = require(\"./linear.js\");\n\nvar _number = _interopRequireDefault(require(\"./number.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function (_) {\n    return arguments.length ? (domain = Array.from(_, _number.default), scale) : domain.slice();\n  };\n\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function () {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, _number.default) : [0, 1];\n  return (0, _linear.linearish)(scale);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIC,OAAO,GAAGC,sBAAsB,CAACF,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,QAAQA,CAACO,MAAM,EAAE;EACxB,IAAIC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,GAAGE,CAAC;EACjD;EAEAD,KAAK,CAACG,MAAM,GAAGH,KAAK;EAEpBA,KAAK,CAACF,MAAM,GAAGE,KAAK,CAACI,KAAK,GAAG,UAAUC,CAAC,EAAE;IACxC,OAAOC,SAAS,CAACC,MAAM,IAAIT,MAAM,GAAGU,KAAK,CAACC,IAAI,CAACJ,CAAC,EAAEX,OAAO,CAACJ,OAAO,CAAC,EAAEU,KAAK,IAAIF,MAAM,CAACY,KAAK,CAAC,CAAC;EAC7F,CAAC;EAEDV,KAAK,CAACD,OAAO,GAAG,UAAUM,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAIR,OAAO,GAAGM,CAAC,EAAEL,KAAK,IAAID,OAAO;EAC1D,CAAC;EAEDC,KAAK,CAACW,IAAI,GAAG,YAAY;IACvB,OAAOpB,QAAQ,CAACO,MAAM,CAAC,CAACC,OAAO,CAACA,OAAO,CAAC;EAC1C,CAAC;EAEDD,MAAM,GAAGQ,SAAS,CAACC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACX,MAAM,EAAEJ,OAAO,CAACJ,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE,OAAO,CAAC,CAAC,EAAEE,OAAO,CAACoB,SAAS,EAAEZ,KAAK,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}