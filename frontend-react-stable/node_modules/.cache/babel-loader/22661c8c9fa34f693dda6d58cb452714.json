{"ast": null, "code": "'use strict';\n\nmodule.exports = function bind(fn, thisArg) {\n  return function wrap() {\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    return fn.apply(thisArg, args);\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "bind", "fn", "thisArg", "wrap", "args", "Array", "arguments", "length", "i", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/helpers/bind.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function bind(fn, thisArg) {\n  return function wrap() {\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    return fn.apply(thisArg, args);\n  };\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAC1C,OAAO,SAASC,IAAIA,CAAA,EAAG;IACrB,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC;IACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;MACpCJ,IAAI,CAACI,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,CAAC;IACxB;IACA,OAAOP,EAAE,CAACQ,KAAK,CAACP,OAAO,EAAEE,IAAI,CAAC;EAChC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}