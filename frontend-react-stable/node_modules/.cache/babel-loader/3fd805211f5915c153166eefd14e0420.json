{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nvar _formatDecimal = require(\"./formatDecimal.js\");\nfunction _default(x, p) {\n  var d = (0, _formatDecimal.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "_formatDecimal", "require", "x", "p", "d", "formatDecimalParts", "coefficient", "exponent", "Array", "join", "length", "slice"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-format/src/formatRounded.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nvar _formatDecimal = require(\"./formatDecimal.js\");\n\nfunction _default(x, p) {\n  var d = (0, _formatDecimal.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,IAAIC,cAAc,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAElD,SAASF,QAAQA,CAACG,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEJ,cAAc,CAACK,kBAAkB,EAAEH,CAAC,EAAEC,CAAC,CAAC;EACpD,IAAI,CAACC,CAAC,EAAE,OAAOF,CAAC,GAAG,EAAE;EACrB,IAAII,WAAW,GAAGF,CAAC,CAAC,CAAC,CAAC;IAClBG,QAAQ,GAAGH,CAAC,CAAC,CAAC,CAAC;EACnB,OAAOG,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,IAAIC,KAAK,CAAC,CAACD,QAAQ,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGH,WAAW,GAAGA,WAAW,CAACI,MAAM,GAAGH,QAAQ,GAAG,CAAC,GAAGD,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGD,WAAW,CAACK,KAAK,CAACJ,QAAQ,GAAG,CAAC,CAAC,GAAGD,WAAW,GAAG,IAAIE,KAAK,CAACD,QAAQ,GAAGD,WAAW,CAACI,MAAM,GAAG,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;AACnQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}