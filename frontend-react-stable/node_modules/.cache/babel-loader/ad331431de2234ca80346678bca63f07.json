{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nexport default function useRefs() {\n  var cacheRefs = useRef(new Map());\n  function getRef(key) {\n    if (!cacheRefs.current.has(key)) {\n      cacheRefs.current.set(key, /*#__PURE__*/React.createRef());\n    }\n    return cacheRefs.current.get(key);\n  }\n  function removeRef(key) {\n    cacheRefs.current.delete(key);\n  }\n  return [getRef, removeRef];\n}", "map": {"version": 3, "names": ["React", "useRef", "useRefs", "cacheRefs", "Map", "getRef", "key", "current", "has", "set", "createRef", "get", "removeRef", "delete"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-tabs/es/hooks/useRefs.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nexport default function useRefs() {\n  var cacheRefs = useRef(new Map());\n\n  function getRef(key) {\n    if (!cacheRefs.current.has(key)) {\n      cacheRefs.current.set(key, /*#__PURE__*/React.createRef());\n    }\n\n    return cacheRefs.current.get(key);\n  }\n\n  function removeRef(key) {\n    cacheRefs.current.delete(key);\n  }\n\n  return [getRef, removeRef];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,eAAe,SAASC,OAAOA,CAAA,EAAG;EAChC,IAAIC,SAAS,GAAGF,MAAM,CAAC,IAAIG,GAAG,CAAC,CAAC,CAAC;EAEjC,SAASC,MAAMA,CAACC,GAAG,EAAE;IACnB,IAAI,CAACH,SAAS,CAACI,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;MAC/BH,SAAS,CAACI,OAAO,CAACE,GAAG,CAACH,GAAG,EAAE,aAAaN,KAAK,CAACU,SAAS,CAAC,CAAC,CAAC;IAC5D;IAEA,OAAOP,SAAS,CAACI,OAAO,CAACI,GAAG,CAACL,GAAG,CAAC;EACnC;EAEA,SAASM,SAASA,CAACN,GAAG,EAAE;IACtBH,SAAS,CAACI,OAAO,CAACM,MAAM,CAACP,GAAG,CAAC;EAC/B;EAEA,OAAO,CAACD,MAAM,EAAEO,SAAS,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}