{"ast": null, "code": "import Countdown from './Countdown';\nimport Statistic from './Statistic';\nStatistic.Countdown = Countdown;\nexport default Statistic;", "map": {"version": 3, "names": ["Countdown", "Statistic"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/statistic/index.js"], "sourcesContent": ["import Countdown from './Countdown';\nimport Statistic from './Statistic';\nStatistic.Countdown = Countdown;\nexport default Statistic;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,SAAS,MAAM,aAAa;AACnCA,SAAS,CAACD,SAAS,GAAGA,SAAS;AAC/B,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}