{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nvar _excluded = [\"children\", \"begin\", \"duration\", \"attributeName\", \"easing\", \"isActive\", \"steps\", \"from\", \"to\", \"canBegin\", \"onAnimationEnd\", \"shouldReAnimate\", \"onAnimationReStart\"];\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nimport React, { PureComponent, cloneElement, Children } from 'react';\nimport PropTypes from 'prop-types';\nimport { deepEqual } from 'fast-equals';\nimport createAnimateManager from './AnimateManager';\nimport { configEasing } from './easing';\nimport configUpdate from './configUpdate';\nimport { getTransitionVal, identity, translateStyle } from './util';\nvar Animate = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Animate, _PureComponent);\n  var _super = _createSuper(Animate);\n  function Animate(props, context) {\n    var _this;\n    _classCallCheck(this, Animate);\n    _this = _super.call(this, props, context);\n    var _this$props = _this.props,\n      isActive = _this$props.isActive,\n      attributeName = _this$props.attributeName,\n      from = _this$props.from,\n      to = _this$props.to,\n      steps = _this$props.steps,\n      children = _this$props.children,\n      duration = _this$props.duration;\n    _this.handleStyleChange = _this.handleStyleChange.bind(_assertThisInitialized(_this));\n    _this.changeStyle = _this.changeStyle.bind(_assertThisInitialized(_this));\n    if (!isActive || duration <= 0) {\n      _this.state = {\n        style: {}\n      };\n\n      // if children is a function and animation is not active, set style to 'to'\n      if (typeof children === 'function') {\n        _this.state = {\n          style: to\n        };\n      }\n      return _possibleConstructorReturn(_this);\n    }\n    if (steps && steps.length) {\n      _this.state = {\n        style: steps[0].style\n      };\n    } else if (from) {\n      if (typeof children === 'function') {\n        _this.state = {\n          style: from\n        };\n        return _possibleConstructorReturn(_this);\n      }\n      _this.state = {\n        style: attributeName ? _defineProperty({}, attributeName, from) : from\n      };\n    } else {\n      _this.state = {\n        style: {}\n      };\n    }\n    return _this;\n  }\n  _createClass(Animate, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props2 = this.props,\n        isActive = _this$props2.isActive,\n        canBegin = _this$props2.canBegin;\n      this.mounted = true;\n      if (!isActive || !canBegin) {\n        return;\n      }\n      this.runAnimation(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props3 = this.props,\n        isActive = _this$props3.isActive,\n        canBegin = _this$props3.canBegin,\n        attributeName = _this$props3.attributeName,\n        shouldReAnimate = _this$props3.shouldReAnimate,\n        to = _this$props3.to,\n        currentFrom = _this$props3.from;\n      var style = this.state.style;\n      if (!canBegin) {\n        return;\n      }\n      if (!isActive) {\n        var newState = {\n          style: attributeName ? _defineProperty({}, attributeName, to) : to\n        };\n        if (this.state && style) {\n          if (attributeName && style[attributeName] !== to || !attributeName && style !== to) {\n            // eslint-disable-next-line react/no-did-update-set-state\n            this.setState(newState);\n          }\n        }\n        return;\n      }\n      if (deepEqual(prevProps.to, to) && prevProps.canBegin && prevProps.isActive) {\n        return;\n      }\n      var isTriggered = !prevProps.canBegin || !prevProps.isActive;\n      if (this.manager) {\n        this.manager.stop();\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      var from = isTriggered || shouldReAnimate ? currentFrom : prevProps.to;\n      if (this.state && style) {\n        var _newState = {\n          style: attributeName ? _defineProperty({}, attributeName, from) : from\n        };\n        if (attributeName && [attributeName] !== from || !attributeName && style !== from) {\n          // eslint-disable-next-line react/no-did-update-set-state\n          this.setState(_newState);\n        }\n      }\n      this.runAnimation(_objectSpread(_objectSpread({}, this.props), {}, {\n        from: from,\n        begin: 0\n      }));\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.mounted = false;\n      var onAnimationEnd = this.props.onAnimationEnd;\n      if (this.unSubscribe) {\n        this.unSubscribe();\n      }\n      if (this.manager) {\n        this.manager.stop();\n        this.manager = null;\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    }\n  }, {\n    key: \"handleStyleChange\",\n    value: function handleStyleChange(style) {\n      this.changeStyle(style);\n    }\n  }, {\n    key: \"changeStyle\",\n    value: function changeStyle(style) {\n      if (this.mounted) {\n        this.setState({\n          style: style\n        });\n      }\n    }\n  }, {\n    key: \"runJSAnimation\",\n    value: function runJSAnimation(props) {\n      var _this2 = this;\n      var from = props.from,\n        to = props.to,\n        duration = props.duration,\n        easing = props.easing,\n        begin = props.begin,\n        onAnimationEnd = props.onAnimationEnd,\n        onAnimationStart = props.onAnimationStart;\n      var startAnimation = configUpdate(from, to, configEasing(easing), duration, this.changeStyle);\n      var finalStartAnimation = function finalStartAnimation() {\n        _this2.stopJSAnimation = startAnimation();\n      };\n      this.manager.start([onAnimationStart, begin, finalStartAnimation, duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"runStepAnimation\",\n    value: function runStepAnimation(props) {\n      var _this3 = this;\n      var steps = props.steps,\n        begin = props.begin,\n        onAnimationStart = props.onAnimationStart;\n      var _steps$ = steps[0],\n        initialStyle = _steps$.style,\n        _steps$$duration = _steps$.duration,\n        initialTime = _steps$$duration === void 0 ? 0 : _steps$$duration;\n      var addStyle = function addStyle(sequence, nextItem, index) {\n        if (index === 0) {\n          return sequence;\n        }\n        var duration = nextItem.duration,\n          _nextItem$easing = nextItem.easing,\n          easing = _nextItem$easing === void 0 ? 'ease' : _nextItem$easing,\n          style = nextItem.style,\n          nextProperties = nextItem.properties,\n          onAnimationEnd = nextItem.onAnimationEnd;\n        var preItem = index > 0 ? steps[index - 1] : nextItem;\n        var properties = nextProperties || Object.keys(style);\n        if (typeof easing === 'function' || easing === 'spring') {\n          return [].concat(_toConsumableArray(sequence), [_this3.runJSAnimation.bind(_this3, {\n            from: preItem.style,\n            to: style,\n            duration: duration,\n            easing: easing\n          }), duration]);\n        }\n        var transition = getTransitionVal(properties, duration, easing);\n        var newStyle = _objectSpread(_objectSpread(_objectSpread({}, preItem.style), style), {}, {\n          transition: transition\n        });\n        return [].concat(_toConsumableArray(sequence), [newStyle, duration, onAnimationEnd]).filter(identity);\n      };\n      return this.manager.start([onAnimationStart].concat(_toConsumableArray(steps.reduce(addStyle, [initialStyle, Math.max(initialTime, begin)])), [props.onAnimationEnd]));\n    }\n  }, {\n    key: \"runAnimation\",\n    value: function runAnimation(props) {\n      if (!this.manager) {\n        this.manager = createAnimateManager();\n      }\n      var begin = props.begin,\n        duration = props.duration,\n        attributeName = props.attributeName,\n        propsTo = props.to,\n        easing = props.easing,\n        onAnimationStart = props.onAnimationStart,\n        onAnimationEnd = props.onAnimationEnd,\n        steps = props.steps,\n        children = props.children;\n      var manager = this.manager;\n      this.unSubscribe = manager.subscribe(this.handleStyleChange);\n      if (typeof easing === 'function' || typeof children === 'function' || easing === 'spring') {\n        this.runJSAnimation(props);\n        return;\n      }\n      if (steps.length > 1) {\n        this.runStepAnimation(props);\n        return;\n      }\n      var to = attributeName ? _defineProperty({}, attributeName, propsTo) : propsTo;\n      var transition = getTransitionVal(Object.keys(to), duration, easing);\n      manager.start([onAnimationStart, begin, _objectSpread(_objectSpread({}, to), {}, {\n        transition: transition\n      }), duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        children = _this$props4.children,\n        begin = _this$props4.begin,\n        duration = _this$props4.duration,\n        attributeName = _this$props4.attributeName,\n        easing = _this$props4.easing,\n        isActive = _this$props4.isActive,\n        steps = _this$props4.steps,\n        from = _this$props4.from,\n        to = _this$props4.to,\n        canBegin = _this$props4.canBegin,\n        onAnimationEnd = _this$props4.onAnimationEnd,\n        shouldReAnimate = _this$props4.shouldReAnimate,\n        onAnimationReStart = _this$props4.onAnimationReStart,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      var count = Children.count(children);\n      // eslint-disable-next-line react/destructuring-assignment\n      var stateStyle = translateStyle(this.state.style);\n      if (typeof children === 'function') {\n        return children(stateStyle);\n      }\n      if (!isActive || count === 0 || duration <= 0) {\n        return children;\n      }\n      var cloneContainer = function cloneContainer(container) {\n        var _container$props = container.props,\n          _container$props$styl = _container$props.style,\n          style = _container$props$styl === void 0 ? {} : _container$props$styl,\n          className = _container$props.className;\n        var res = /*#__PURE__*/cloneElement(container, _objectSpread(_objectSpread({}, others), {}, {\n          style: _objectSpread(_objectSpread({}, style), stateStyle),\n          className: className\n        }));\n        return res;\n      };\n      if (count === 1) {\n        return cloneContainer(Children.only(children));\n      }\n      return /*#__PURE__*/React.createElement(\"div\", null, Children.map(children, function (child) {\n        return cloneContainer(child);\n      }));\n    }\n  }]);\n  return Animate;\n}(PureComponent);\nAnimate.displayName = 'Animate';\nAnimate.defaultProps = {\n  begin: 0,\n  duration: 1000,\n  from: '',\n  to: '',\n  attributeName: '',\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  steps: [],\n  onAnimationEnd: function onAnimationEnd() {},\n  onAnimationStart: function onAnimationStart() {}\n};\nAnimate.propTypes = {\n  from: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  to: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  attributeName: PropTypes.string,\n  // animation duration\n  duration: PropTypes.number,\n  begin: PropTypes.number,\n  easing: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),\n  steps: PropTypes.arrayOf(PropTypes.shape({\n    duration: PropTypes.number.isRequired,\n    style: PropTypes.object.isRequired,\n    easing: PropTypes.oneOfType([PropTypes.oneOf(['ease', 'ease-in', 'ease-out', 'ease-in-out', 'linear']), PropTypes.func]),\n    // transition css properties(dash case), optional\n    properties: PropTypes.arrayOf('string'),\n    onAnimationEnd: PropTypes.func\n  })),\n  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),\n  isActive: PropTypes.bool,\n  canBegin: PropTypes.bool,\n  onAnimationEnd: PropTypes.func,\n  // decide if it should reanimate with initial from style when props change\n  shouldReAnimate: PropTypes.bool,\n  onAnimationStart: PropTypes.func,\n  onAnimationReStart: PropTypes.func\n};\nexport default Animate;", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_excluded", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "sourceKeys", "keys", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "arr2", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "_createClass", "protoProps", "staticProps", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "Number", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "p", "setPrototypeOf", "bind", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Boolean", "valueOf", "e", "getPrototypeOf", "React", "PureComponent", "cloneElement", "Children", "PropTypes", "deepEqual", "createAnimateManager", "configEasing", "configUpdate", "getTransitionVal", "identity", "translateStyle", "Animate", "_PureComponent", "_super", "context", "_this", "_this$props", "isActive", "attributeName", "to", "steps", "children", "duration", "handleStyleChange", "changeStyle", "state", "style", "componentDidMount", "_this$props2", "canBegin", "mounted", "runAnimation", "componentDidUpdate", "prevProps", "_this$props3", "shouldReAnimate", "currentFrom", "newState", "setState", "isTriggered", "manager", "stop", "stopJSAnimation", "_newState", "begin", "componentWillUnmount", "onAnimationEnd", "unSubscribe", "runJSAnimation", "_this2", "easing", "onAnimationStart", "startAnimation", "finalStartAnimation", "start", "runStepAnimation", "_this3", "_steps$", "initialStyle", "_steps$$duration", "initialTime", "addStyle", "sequence", "nextItem", "index", "_nextItem$easing", "nextProperties", "properties", "preItem", "concat", "transition", "newStyle", "reduce", "Math", "max", "propsTo", "subscribe", "render", "_this$props4", "onAnimationReStart", "others", "count", "stateStyle", "cloneContainer", "container", "_container$props", "_container$props$styl", "className", "only", "createElement", "map", "child", "displayName", "defaultProps", "propTypes", "oneOfType", "string", "number", "func", "arrayOf", "shape", "isRequired", "oneOf", "node", "bool"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/react-smooth/es6/Animate.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar _excluded = [\"children\", \"begin\", \"duration\", \"attributeName\", \"easing\", \"isActive\", \"steps\", \"from\", \"to\", \"canBegin\", \"onAnimationEnd\", \"shouldReAnimate\", \"onAnimationReStart\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nimport React, { PureComponent, cloneElement, Children } from 'react';\nimport PropTypes from 'prop-types';\nimport { deepEqual } from 'fast-equals';\nimport createAnimateManager from './AnimateManager';\nimport { configEasing } from './easing';\nimport configUpdate from './configUpdate';\nimport { getTransitionVal, identity, translateStyle } from './util';\nvar Animate = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Animate, _PureComponent);\n  var _super = _createSuper(Animate);\n  function Animate(props, context) {\n    var _this;\n    _classCallCheck(this, Animate);\n    _this = _super.call(this, props, context);\n    var _this$props = _this.props,\n      isActive = _this$props.isActive,\n      attributeName = _this$props.attributeName,\n      from = _this$props.from,\n      to = _this$props.to,\n      steps = _this$props.steps,\n      children = _this$props.children,\n      duration = _this$props.duration;\n    _this.handleStyleChange = _this.handleStyleChange.bind(_assertThisInitialized(_this));\n    _this.changeStyle = _this.changeStyle.bind(_assertThisInitialized(_this));\n    if (!isActive || duration <= 0) {\n      _this.state = {\n        style: {}\n      };\n\n      // if children is a function and animation is not active, set style to 'to'\n      if (typeof children === 'function') {\n        _this.state = {\n          style: to\n        };\n      }\n      return _possibleConstructorReturn(_this);\n    }\n    if (steps && steps.length) {\n      _this.state = {\n        style: steps[0].style\n      };\n    } else if (from) {\n      if (typeof children === 'function') {\n        _this.state = {\n          style: from\n        };\n        return _possibleConstructorReturn(_this);\n      }\n      _this.state = {\n        style: attributeName ? _defineProperty({}, attributeName, from) : from\n      };\n    } else {\n      _this.state = {\n        style: {}\n      };\n    }\n    return _this;\n  }\n  _createClass(Animate, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props2 = this.props,\n        isActive = _this$props2.isActive,\n        canBegin = _this$props2.canBegin;\n      this.mounted = true;\n      if (!isActive || !canBegin) {\n        return;\n      }\n      this.runAnimation(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props3 = this.props,\n        isActive = _this$props3.isActive,\n        canBegin = _this$props3.canBegin,\n        attributeName = _this$props3.attributeName,\n        shouldReAnimate = _this$props3.shouldReAnimate,\n        to = _this$props3.to,\n        currentFrom = _this$props3.from;\n      var style = this.state.style;\n      if (!canBegin) {\n        return;\n      }\n      if (!isActive) {\n        var newState = {\n          style: attributeName ? _defineProperty({}, attributeName, to) : to\n        };\n        if (this.state && style) {\n          if (attributeName && style[attributeName] !== to || !attributeName && style !== to) {\n            // eslint-disable-next-line react/no-did-update-set-state\n            this.setState(newState);\n          }\n        }\n        return;\n      }\n      if (deepEqual(prevProps.to, to) && prevProps.canBegin && prevProps.isActive) {\n        return;\n      }\n      var isTriggered = !prevProps.canBegin || !prevProps.isActive;\n      if (this.manager) {\n        this.manager.stop();\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      var from = isTriggered || shouldReAnimate ? currentFrom : prevProps.to;\n      if (this.state && style) {\n        var _newState = {\n          style: attributeName ? _defineProperty({}, attributeName, from) : from\n        };\n        if (attributeName && [attributeName] !== from || !attributeName && style !== from) {\n          // eslint-disable-next-line react/no-did-update-set-state\n          this.setState(_newState);\n        }\n      }\n      this.runAnimation(_objectSpread(_objectSpread({}, this.props), {}, {\n        from: from,\n        begin: 0\n      }));\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.mounted = false;\n      var onAnimationEnd = this.props.onAnimationEnd;\n      if (this.unSubscribe) {\n        this.unSubscribe();\n      }\n      if (this.manager) {\n        this.manager.stop();\n        this.manager = null;\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    }\n  }, {\n    key: \"handleStyleChange\",\n    value: function handleStyleChange(style) {\n      this.changeStyle(style);\n    }\n  }, {\n    key: \"changeStyle\",\n    value: function changeStyle(style) {\n      if (this.mounted) {\n        this.setState({\n          style: style\n        });\n      }\n    }\n  }, {\n    key: \"runJSAnimation\",\n    value: function runJSAnimation(props) {\n      var _this2 = this;\n      var from = props.from,\n        to = props.to,\n        duration = props.duration,\n        easing = props.easing,\n        begin = props.begin,\n        onAnimationEnd = props.onAnimationEnd,\n        onAnimationStart = props.onAnimationStart;\n      var startAnimation = configUpdate(from, to, configEasing(easing), duration, this.changeStyle);\n      var finalStartAnimation = function finalStartAnimation() {\n        _this2.stopJSAnimation = startAnimation();\n      };\n      this.manager.start([onAnimationStart, begin, finalStartAnimation, duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"runStepAnimation\",\n    value: function runStepAnimation(props) {\n      var _this3 = this;\n      var steps = props.steps,\n        begin = props.begin,\n        onAnimationStart = props.onAnimationStart;\n      var _steps$ = steps[0],\n        initialStyle = _steps$.style,\n        _steps$$duration = _steps$.duration,\n        initialTime = _steps$$duration === void 0 ? 0 : _steps$$duration;\n      var addStyle = function addStyle(sequence, nextItem, index) {\n        if (index === 0) {\n          return sequence;\n        }\n        var duration = nextItem.duration,\n          _nextItem$easing = nextItem.easing,\n          easing = _nextItem$easing === void 0 ? 'ease' : _nextItem$easing,\n          style = nextItem.style,\n          nextProperties = nextItem.properties,\n          onAnimationEnd = nextItem.onAnimationEnd;\n        var preItem = index > 0 ? steps[index - 1] : nextItem;\n        var properties = nextProperties || Object.keys(style);\n        if (typeof easing === 'function' || easing === 'spring') {\n          return [].concat(_toConsumableArray(sequence), [_this3.runJSAnimation.bind(_this3, {\n            from: preItem.style,\n            to: style,\n            duration: duration,\n            easing: easing\n          }), duration]);\n        }\n        var transition = getTransitionVal(properties, duration, easing);\n        var newStyle = _objectSpread(_objectSpread(_objectSpread({}, preItem.style), style), {}, {\n          transition: transition\n        });\n        return [].concat(_toConsumableArray(sequence), [newStyle, duration, onAnimationEnd]).filter(identity);\n      };\n      return this.manager.start([onAnimationStart].concat(_toConsumableArray(steps.reduce(addStyle, [initialStyle, Math.max(initialTime, begin)])), [props.onAnimationEnd]));\n    }\n  }, {\n    key: \"runAnimation\",\n    value: function runAnimation(props) {\n      if (!this.manager) {\n        this.manager = createAnimateManager();\n      }\n      var begin = props.begin,\n        duration = props.duration,\n        attributeName = props.attributeName,\n        propsTo = props.to,\n        easing = props.easing,\n        onAnimationStart = props.onAnimationStart,\n        onAnimationEnd = props.onAnimationEnd,\n        steps = props.steps,\n        children = props.children;\n      var manager = this.manager;\n      this.unSubscribe = manager.subscribe(this.handleStyleChange);\n      if (typeof easing === 'function' || typeof children === 'function' || easing === 'spring') {\n        this.runJSAnimation(props);\n        return;\n      }\n      if (steps.length > 1) {\n        this.runStepAnimation(props);\n        return;\n      }\n      var to = attributeName ? _defineProperty({}, attributeName, propsTo) : propsTo;\n      var transition = getTransitionVal(Object.keys(to), duration, easing);\n      manager.start([onAnimationStart, begin, _objectSpread(_objectSpread({}, to), {}, {\n        transition: transition\n      }), duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        children = _this$props4.children,\n        begin = _this$props4.begin,\n        duration = _this$props4.duration,\n        attributeName = _this$props4.attributeName,\n        easing = _this$props4.easing,\n        isActive = _this$props4.isActive,\n        steps = _this$props4.steps,\n        from = _this$props4.from,\n        to = _this$props4.to,\n        canBegin = _this$props4.canBegin,\n        onAnimationEnd = _this$props4.onAnimationEnd,\n        shouldReAnimate = _this$props4.shouldReAnimate,\n        onAnimationReStart = _this$props4.onAnimationReStart,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      var count = Children.count(children);\n      // eslint-disable-next-line react/destructuring-assignment\n      var stateStyle = translateStyle(this.state.style);\n      if (typeof children === 'function') {\n        return children(stateStyle);\n      }\n      if (!isActive || count === 0 || duration <= 0) {\n        return children;\n      }\n      var cloneContainer = function cloneContainer(container) {\n        var _container$props = container.props,\n          _container$props$styl = _container$props.style,\n          style = _container$props$styl === void 0 ? {} : _container$props$styl,\n          className = _container$props.className;\n        var res = /*#__PURE__*/cloneElement(container, _objectSpread(_objectSpread({}, others), {}, {\n          style: _objectSpread(_objectSpread({}, style), stateStyle),\n          className: className\n        }));\n        return res;\n      };\n      if (count === 1) {\n        return cloneContainer(Children.only(children));\n      }\n      return /*#__PURE__*/React.createElement(\"div\", null, Children.map(children, function (child) {\n        return cloneContainer(child);\n      }));\n    }\n  }]);\n  return Animate;\n}(PureComponent);\nAnimate.displayName = 'Animate';\nAnimate.defaultProps = {\n  begin: 0,\n  duration: 1000,\n  from: '',\n  to: '',\n  attributeName: '',\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  steps: [],\n  onAnimationEnd: function onAnimationEnd() {},\n  onAnimationStart: function onAnimationStart() {}\n};\nAnimate.propTypes = {\n  from: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  to: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  attributeName: PropTypes.string,\n  // animation duration\n  duration: PropTypes.number,\n  begin: PropTypes.number,\n  easing: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),\n  steps: PropTypes.arrayOf(PropTypes.shape({\n    duration: PropTypes.number.isRequired,\n    style: PropTypes.object.isRequired,\n    easing: PropTypes.oneOfType([PropTypes.oneOf(['ease', 'ease-in', 'ease-out', 'ease-in-out', 'linear']), PropTypes.func]),\n    // transition css properties(dash case), optional\n    properties: PropTypes.arrayOf('string'),\n    onAnimationEnd: PropTypes.func\n  })),\n  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),\n  isActive: PropTypes.bool,\n  canBegin: PropTypes.bool,\n  onAnimationEnd: PropTypes.func,\n  // decide if it should reanimate with initial from style when props change\n  shouldReAnimate: PropTypes.bool,\n  onAnimationStart: PropTypes.func,\n  onAnimationReStart: PropTypes.func\n};\nexport default Animate;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAAE,OAAO,OAAOA,GAAG;EAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;IAAE,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAAE,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AAAE;AAC/U,IAAIK,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;AACtL,SAASC,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACT,SAAS,CAACc,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIW,UAAU,GAAGP,MAAM,CAACQ,IAAI,CAACd,MAAM,CAAC;EAAE,IAAII,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACJ,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGS,UAAU,CAACR,CAAC,CAAC;IAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AAClT,SAASa,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACG,CAAC,EAAEC,MAAM,EAAE;EAAE,IAAI,CAACD,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGnB,MAAM,CAACT,SAAS,CAAC6B,QAAQ,CAACd,IAAI,CAACU,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAAC1B,WAAW,EAAE6B,CAAC,GAAGH,CAAC,CAAC1B,WAAW,CAACgC,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAACR,CAAC,CAAC;EAAE,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACF,CAAC,EAAEC,MAAM,CAAC;AAAE;AAC/Z,SAASL,gBAAgBA,CAACc,IAAI,EAAE;EAAE,IAAI,OAAOtC,MAAM,KAAK,WAAW,IAAIsC,IAAI,CAACtC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIqC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASf,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIa,KAAK,CAACI,OAAO,CAACjB,GAAG,CAAC,EAAE,OAAOQ,iBAAiB,CAACR,GAAG,CAAC;AAAE;AAC1F,SAASQ,iBAAiBA,CAACR,GAAG,EAAEkB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGlB,GAAG,CAACP,MAAM,EAAEyB,GAAG,GAAGlB,GAAG,CAACP,MAAM;EAAE,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAE8B,IAAI,GAAG,IAAIN,KAAK,CAACK,GAAG,CAAC,EAAE7B,CAAC,GAAG6B,GAAG,EAAE7B,CAAC,EAAE,EAAE8B,IAAI,CAAC9B,CAAC,CAAC,GAAGW,GAAG,CAACX,CAAC,CAAC;EAAE,OAAO8B,IAAI;AAAE;AAClL,SAASC,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIxB,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACuB,MAAM,CAAC;EAAE,IAAI/B,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIgC,OAAO,GAAGjC,MAAM,CAACC,qBAAqB,CAAC8B,MAAM,CAAC;IAAEC,cAAc,KAAKC,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOnC,MAAM,CAACoC,wBAAwB,CAACL,MAAM,EAAEI,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAE7B,IAAI,CAAC8B,IAAI,CAACC,KAAK,CAAC/B,IAAI,EAAEyB,OAAO,CAAC;EAAE;EAAE,OAAOzB,IAAI;AAAE;AACpV,SAASgC,aAAaA,CAAC5C,MAAM,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,SAAS,CAACtC,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIL,MAAM,GAAG,IAAI,IAAI+C,SAAS,CAAC1C,CAAC,CAAC,GAAG0C,SAAS,CAAC1C,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAG+B,OAAO,CAAC9B,MAAM,CAACN,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAAE6C,eAAe,CAAC/C,MAAM,EAAEE,GAAG,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAAC4C,yBAAyB,GAAG5C,MAAM,CAAC6C,gBAAgB,CAACjD,MAAM,EAAEI,MAAM,CAAC4C,yBAAyB,CAAClD,MAAM,CAAC,CAAC,GAAGoC,OAAO,CAAC9B,MAAM,CAACN,MAAM,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU5C,GAAG,EAAE;MAAEE,MAAM,CAAC8C,cAAc,CAAClD,MAAM,EAAEE,GAAG,EAAEE,MAAM,CAACoC,wBAAwB,CAAC1C,MAAM,EAAEI,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOF,MAAM;AAAE;AACzf,SAAS+C,eAAeA,CAACxD,GAAG,EAAEW,GAAG,EAAEiD,KAAK,EAAE;EAAEjD,GAAG,GAAGkD,cAAc,CAAClD,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIX,GAAG,EAAE;IAAEa,MAAM,CAAC8C,cAAc,CAAC3D,GAAG,EAAEW,GAAG,EAAE;MAAEiD,KAAK,EAAEA,KAAK;MAAEV,UAAU,EAAE,IAAI;MAAEY,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE/D,GAAG,CAACW,GAAG,CAAC,GAAGiD,KAAK;EAAE;EAAE,OAAO5D,GAAG;AAAE;AAC3O,SAASgE,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAItC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASuC,iBAAiBA,CAAC1D,MAAM,EAAE2D,KAAK,EAAE;EAAE,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,KAAK,CAACpD,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIyD,UAAU,GAAGD,KAAK,CAACxD,CAAC,CAAC;IAAEyD,UAAU,CAACnB,UAAU,GAAGmB,UAAU,CAACnB,UAAU,IAAI,KAAK;IAAEmB,UAAU,CAACP,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIO,UAAU,EAAEA,UAAU,CAACN,QAAQ,GAAG,IAAI;IAAElD,MAAM,CAAC8C,cAAc,CAAClD,MAAM,EAAEoD,cAAc,CAACQ,UAAU,CAAC1D,GAAG,CAAC,EAAE0D,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASC,YAAYA,CAACJ,WAAW,EAAEK,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEJ,iBAAiB,CAACD,WAAW,CAAC9D,SAAS,EAAEmE,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEL,iBAAiB,CAACD,WAAW,EAAEM,WAAW,CAAC;EAAE3D,MAAM,CAAC8C,cAAc,CAACO,WAAW,EAAE,WAAW,EAAE;IAAEH,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOG,WAAW;AAAE;AAC5R,SAASL,cAAcA,CAACY,GAAG,EAAE;EAAE,IAAI9D,GAAG,GAAG+D,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO1E,OAAO,CAACY,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGgE,MAAM,CAAChE,GAAG,CAAC;AAAE;AAC5H,SAAS+D,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI9E,OAAO,CAAC6E,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAAC3E,MAAM,CAAC8E,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAAC3D,IAAI,CAACyD,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI9E,OAAO,CAACkF,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIrD,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACiD,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGO,MAAM,EAAEN,KAAK,CAAC;AAAE;AAC5X,SAASO,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIzD,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEwD,QAAQ,CAAChF,SAAS,GAAGS,MAAM,CAACyE,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjF,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEyD,KAAK,EAAEwB,QAAQ;MAAErB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEjD,MAAM,CAAC8C,cAAc,CAACyB,QAAQ,EAAE,WAAW,EAAE;IAAErB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIsB,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASE,eAAeA,CAAC1D,CAAC,EAAE2D,CAAC,EAAE;EAAED,eAAe,GAAG1E,MAAM,CAAC4E,cAAc,GAAG5E,MAAM,CAAC4E,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASH,eAAeA,CAAC1D,CAAC,EAAE2D,CAAC,EAAE;IAAE3D,CAAC,CAAC8D,SAAS,GAAGH,CAAC;IAAE,OAAO3D,CAAC;EAAE,CAAC;EAAE,OAAO0D,eAAe,CAAC1D,CAAC,EAAE2D,CAAC,CAAC;AAAE;AACvM,SAASI,YAAYA,CAACC,OAAO,EAAE;EAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;EAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;IAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;MAAEM,MAAM;IAAE,IAAIL,yBAAyB,EAAE;MAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC/F,WAAW;MAAEgG,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAE3C,SAAS,EAAE8C,SAAS,CAAC;IAAE,CAAC,MAAM;MAAED,MAAM,GAAGF,KAAK,CAAC7C,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;IAAE;IAAE,OAAOiD,0BAA0B,CAAC,IAAI,EAAEJ,MAAM,CAAC;EAAE,CAAC;AAAE;AACxa,SAASI,0BAA0BA,CAACC,IAAI,EAAErF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKpB,OAAO,CAACoB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIS,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAO6E,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAAST,yBAAyBA,CAAA,EAAG;EAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;EAAE,IAAID,OAAO,CAACC,SAAS,CAACK,IAAI,EAAE,OAAO,KAAK;EAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;EAAE,IAAI;IAAEC,OAAO,CAACzG,SAAS,CAAC0G,OAAO,CAAC3F,IAAI,CAACkF,OAAO,CAACC,SAAS,CAACO,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;AAAE;AACxU,SAASb,eAAeA,CAACrE,CAAC,EAAE;EAAEqE,eAAe,GAAGrF,MAAM,CAAC4E,cAAc,GAAG5E,MAAM,CAACmG,cAAc,CAACtB,IAAI,CAAC,CAAC,GAAG,SAASQ,eAAeA,CAACrE,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC8D,SAAS,IAAI9E,MAAM,CAACmG,cAAc,CAACnF,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOqE,eAAe,CAACrE,CAAC,CAAC;AAAE;AACnN,OAAOoF,KAAK,IAAIC,aAAa,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,OAAO;AACpE,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAOC,oBAAoB,MAAM,kBAAkB;AACnD,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,QAAQ;AACnE,IAAIC,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EACnD3C,SAAS,CAAC0C,OAAO,EAAEC,cAAc,CAAC;EAClC,IAAIC,MAAM,GAAGnC,YAAY,CAACiC,OAAO,CAAC;EAClC,SAASA,OAAOA,CAACzD,KAAK,EAAE4D,OAAO,EAAE;IAC/B,IAAIC,KAAK;IACTjE,eAAe,CAAC,IAAI,EAAE6D,OAAO,CAAC;IAC9BI,KAAK,GAAGF,MAAM,CAAC5G,IAAI,CAAC,IAAI,EAAEiD,KAAK,EAAE4D,OAAO,CAAC;IACzC,IAAIE,WAAW,GAAGD,KAAK,CAAC7D,KAAK;MAC3B+D,QAAQ,GAAGD,WAAW,CAACC,QAAQ;MAC/BC,aAAa,GAAGF,WAAW,CAACE,aAAa;MACzC/F,IAAI,GAAG6F,WAAW,CAAC7F,IAAI;MACvBgG,EAAE,GAAGH,WAAW,CAACG,EAAE;MACnBC,KAAK,GAAGJ,WAAW,CAACI,KAAK;MACzBC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;MAC/BC,QAAQ,GAAGN,WAAW,CAACM,QAAQ;IACjCP,KAAK,CAACQ,iBAAiB,GAAGR,KAAK,CAACQ,iBAAiB,CAAC/C,IAAI,CAACe,sBAAsB,CAACwB,KAAK,CAAC,CAAC;IACrFA,KAAK,CAACS,WAAW,GAAGT,KAAK,CAACS,WAAW,CAAChD,IAAI,CAACe,sBAAsB,CAACwB,KAAK,CAAC,CAAC;IACzE,IAAI,CAACE,QAAQ,IAAIK,QAAQ,IAAI,CAAC,EAAE;MAC9BP,KAAK,CAACU,KAAK,GAAG;QACZC,KAAK,EAAE,CAAC;MACV,CAAC;;MAED;MACA,IAAI,OAAOL,QAAQ,KAAK,UAAU,EAAE;QAClCN,KAAK,CAACU,KAAK,GAAG;UACZC,KAAK,EAAEP;QACT,CAAC;MACH;MACA,OAAO9B,0BAA0B,CAAC0B,KAAK,CAAC;IAC1C;IACA,IAAIK,KAAK,IAAIA,KAAK,CAACtH,MAAM,EAAE;MACzBiH,KAAK,CAACU,KAAK,GAAG;QACZC,KAAK,EAAEN,KAAK,CAAC,CAAC,CAAC,CAACM;MAClB,CAAC;IACH,CAAC,MAAM,IAAIvG,IAAI,EAAE;MACf,IAAI,OAAOkG,QAAQ,KAAK,UAAU,EAAE;QAClCN,KAAK,CAACU,KAAK,GAAG;UACZC,KAAK,EAAEvG;QACT,CAAC;QACD,OAAOkE,0BAA0B,CAAC0B,KAAK,CAAC;MAC1C;MACAA,KAAK,CAACU,KAAK,GAAG;QACZC,KAAK,EAAER,aAAa,GAAG5E,eAAe,CAAC,CAAC,CAAC,EAAE4E,aAAa,EAAE/F,IAAI,CAAC,GAAGA;MACpE,CAAC;IACH,CAAC,MAAM;MACL4F,KAAK,CAACU,KAAK,GAAG;QACZC,KAAK,EAAE,CAAC;MACV,CAAC;IACH;IACA,OAAOX,KAAK;EACd;EACA3D,YAAY,CAACuD,OAAO,EAAE,CAAC;IACrBlH,GAAG,EAAE,mBAAmB;IACxBiD,KAAK,EAAE,SAASiF,iBAAiBA,CAAA,EAAG;MAClC,IAAIC,YAAY,GAAG,IAAI,CAAC1E,KAAK;QAC3B+D,QAAQ,GAAGW,YAAY,CAACX,QAAQ;QAChCY,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAClC,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACb,QAAQ,IAAI,CAACY,QAAQ,EAAE;QAC1B;MACF;MACA,IAAI,CAACE,YAAY,CAAC,IAAI,CAAC7E,KAAK,CAAC;IAC/B;EACF,CAAC,EAAE;IACDzD,GAAG,EAAE,oBAAoB;IACzBiD,KAAK,EAAE,SAASsF,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIC,YAAY,GAAG,IAAI,CAAChF,KAAK;QAC3B+D,QAAQ,GAAGiB,YAAY,CAACjB,QAAQ;QAChCY,QAAQ,GAAGK,YAAY,CAACL,QAAQ;QAChCX,aAAa,GAAGgB,YAAY,CAAChB,aAAa;QAC1CiB,eAAe,GAAGD,YAAY,CAACC,eAAe;QAC9ChB,EAAE,GAAGe,YAAY,CAACf,EAAE;QACpBiB,WAAW,GAAGF,YAAY,CAAC/G,IAAI;MACjC,IAAIuG,KAAK,GAAG,IAAI,CAACD,KAAK,CAACC,KAAK;MAC5B,IAAI,CAACG,QAAQ,EAAE;QACb;MACF;MACA,IAAI,CAACZ,QAAQ,EAAE;QACb,IAAIoB,QAAQ,GAAG;UACbX,KAAK,EAAER,aAAa,GAAG5E,eAAe,CAAC,CAAC,CAAC,EAAE4E,aAAa,EAAEC,EAAE,CAAC,GAAGA;QAClE,CAAC;QACD,IAAI,IAAI,CAACM,KAAK,IAAIC,KAAK,EAAE;UACvB,IAAIR,aAAa,IAAIQ,KAAK,CAACR,aAAa,CAAC,KAAKC,EAAE,IAAI,CAACD,aAAa,IAAIQ,KAAK,KAAKP,EAAE,EAAE;YAClF;YACA,IAAI,CAACmB,QAAQ,CAACD,QAAQ,CAAC;UACzB;QACF;QACA;MACF;MACA,IAAIjC,SAAS,CAAC6B,SAAS,CAACd,EAAE,EAAEA,EAAE,CAAC,IAAIc,SAAS,CAACJ,QAAQ,IAAII,SAAS,CAAChB,QAAQ,EAAE;QAC3E;MACF;MACA,IAAIsB,WAAW,GAAG,CAACN,SAAS,CAACJ,QAAQ,IAAI,CAACI,SAAS,CAAChB,QAAQ;MAC5D,IAAI,IAAI,CAACuB,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,CAAC;MACrB;MACA,IAAI,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC,CAAC;MACxB;MACA,IAAIvH,IAAI,GAAGoH,WAAW,IAAIJ,eAAe,GAAGC,WAAW,GAAGH,SAAS,CAACd,EAAE;MACtE,IAAI,IAAI,CAACM,KAAK,IAAIC,KAAK,EAAE;QACvB,IAAIiB,SAAS,GAAG;UACdjB,KAAK,EAAER,aAAa,GAAG5E,eAAe,CAAC,CAAC,CAAC,EAAE4E,aAAa,EAAE/F,IAAI,CAAC,GAAGA;QACpE,CAAC;QACD,IAAI+F,aAAa,IAAI,CAACA,aAAa,CAAC,KAAK/F,IAAI,IAAI,CAAC+F,aAAa,IAAIQ,KAAK,KAAKvG,IAAI,EAAE;UACjF;UACA,IAAI,CAACmH,QAAQ,CAACK,SAAS,CAAC;QAC1B;MACF;MACA,IAAI,CAACZ,YAAY,CAAC5F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACe,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjE/B,IAAI,EAAEA,IAAI;QACVyH,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDnJ,GAAG,EAAE,sBAAsB;IAC3BiD,KAAK,EAAE,SAASmG,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACf,OAAO,GAAG,KAAK;MACpB,IAAIgB,cAAc,GAAG,IAAI,CAAC5F,KAAK,CAAC4F,cAAc;MAC9C,IAAI,IAAI,CAACC,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAAC,CAAC;MACpB;MACA,IAAI,IAAI,CAACP,OAAO,EAAE;QAChB,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,CAAC;QACnB,IAAI,CAACD,OAAO,GAAG,IAAI;MACrB;MACA,IAAI,IAAI,CAACE,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC,CAAC;MACxB;MACA,IAAII,cAAc,EAAE;QAClBA,cAAc,CAAC,CAAC;MAClB;IACF;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,mBAAmB;IACxBiD,KAAK,EAAE,SAAS6E,iBAAiBA,CAACG,KAAK,EAAE;MACvC,IAAI,CAACF,WAAW,CAACE,KAAK,CAAC;IACzB;EACF,CAAC,EAAE;IACDjI,GAAG,EAAE,aAAa;IAClBiD,KAAK,EAAE,SAAS8E,WAAWA,CAACE,KAAK,EAAE;MACjC,IAAI,IAAI,CAACI,OAAO,EAAE;QAChB,IAAI,CAACQ,QAAQ,CAAC;UACZZ,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDjI,GAAG,EAAE,gBAAgB;IACrBiD,KAAK,EAAE,SAASsG,cAAcA,CAAC9F,KAAK,EAAE;MACpC,IAAI+F,MAAM,GAAG,IAAI;MACjB,IAAI9H,IAAI,GAAG+B,KAAK,CAAC/B,IAAI;QACnBgG,EAAE,GAAGjE,KAAK,CAACiE,EAAE;QACbG,QAAQ,GAAGpE,KAAK,CAACoE,QAAQ;QACzB4B,MAAM,GAAGhG,KAAK,CAACgG,MAAM;QACrBN,KAAK,GAAG1F,KAAK,CAAC0F,KAAK;QACnBE,cAAc,GAAG5F,KAAK,CAAC4F,cAAc;QACrCK,gBAAgB,GAAGjG,KAAK,CAACiG,gBAAgB;MAC3C,IAAIC,cAAc,GAAG7C,YAAY,CAACpF,IAAI,EAAEgG,EAAE,EAAEb,YAAY,CAAC4C,MAAM,CAAC,EAAE5B,QAAQ,EAAE,IAAI,CAACE,WAAW,CAAC;MAC7F,IAAI6B,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;QACvDJ,MAAM,CAACP,eAAe,GAAGU,cAAc,CAAC,CAAC;MAC3C,CAAC;MACD,IAAI,CAACZ,OAAO,CAACc,KAAK,CAAC,CAACH,gBAAgB,EAAEP,KAAK,EAAES,mBAAmB,EAAE/B,QAAQ,EAAEwB,cAAc,CAAC,CAAC;IAC9F;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,kBAAkB;IACvBiD,KAAK,EAAE,SAAS6G,gBAAgBA,CAACrG,KAAK,EAAE;MACtC,IAAIsG,MAAM,GAAG,IAAI;MACjB,IAAIpC,KAAK,GAAGlE,KAAK,CAACkE,KAAK;QACrBwB,KAAK,GAAG1F,KAAK,CAAC0F,KAAK;QACnBO,gBAAgB,GAAGjG,KAAK,CAACiG,gBAAgB;MAC3C,IAAIM,OAAO,GAAGrC,KAAK,CAAC,CAAC,CAAC;QACpBsC,YAAY,GAAGD,OAAO,CAAC/B,KAAK;QAC5BiC,gBAAgB,GAAGF,OAAO,CAACnC,QAAQ;QACnCsC,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;MAClE,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAE;QAC1D,IAAIA,KAAK,KAAK,CAAC,EAAE;UACf,OAAOF,QAAQ;QACjB;QACA,IAAIxC,QAAQ,GAAGyC,QAAQ,CAACzC,QAAQ;UAC9B2C,gBAAgB,GAAGF,QAAQ,CAACb,MAAM;UAClCA,MAAM,GAAGe,gBAAgB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,gBAAgB;UAChEvC,KAAK,GAAGqC,QAAQ,CAACrC,KAAK;UACtBwC,cAAc,GAAGH,QAAQ,CAACI,UAAU;UACpCrB,cAAc,GAAGiB,QAAQ,CAACjB,cAAc;QAC1C,IAAIsB,OAAO,GAAGJ,KAAK,GAAG,CAAC,GAAG5C,KAAK,CAAC4C,KAAK,GAAG,CAAC,CAAC,GAAGD,QAAQ;QACrD,IAAII,UAAU,GAAGD,cAAc,IAAIvK,MAAM,CAACQ,IAAI,CAACuH,KAAK,CAAC;QACrD,IAAI,OAAOwB,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,QAAQ,EAAE;UACvD,OAAO,EAAE,CAACmB,MAAM,CAACjK,kBAAkB,CAAC0J,QAAQ,CAAC,EAAE,CAACN,MAAM,CAACR,cAAc,CAACxE,IAAI,CAACgF,MAAM,EAAE;YACjFrI,IAAI,EAAEiJ,OAAO,CAAC1C,KAAK;YACnBP,EAAE,EAAEO,KAAK;YACTJ,QAAQ,EAAEA,QAAQ;YAClB4B,MAAM,EAAEA;UACV,CAAC,CAAC,EAAE5B,QAAQ,CAAC,CAAC;QAChB;QACA,IAAIgD,UAAU,GAAG9D,gBAAgB,CAAC2D,UAAU,EAAE7C,QAAQ,EAAE4B,MAAM,CAAC;QAC/D,IAAIqB,QAAQ,GAAGpI,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiI,OAAO,CAAC1C,KAAK,CAAC,EAAEA,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACvF4C,UAAU,EAAEA;QACd,CAAC,CAAC;QACF,OAAO,EAAE,CAACD,MAAM,CAACjK,kBAAkB,CAAC0J,QAAQ,CAAC,EAAE,CAACS,QAAQ,EAAEjD,QAAQ,EAAEwB,cAAc,CAAC,CAAC,CAACjH,MAAM,CAAC4E,QAAQ,CAAC;MACvG,CAAC;MACD,OAAO,IAAI,CAAC+B,OAAO,CAACc,KAAK,CAAC,CAACH,gBAAgB,CAAC,CAACkB,MAAM,CAACjK,kBAAkB,CAACgH,KAAK,CAACoD,MAAM,CAACX,QAAQ,EAAE,CAACH,YAAY,EAAEe,IAAI,CAACC,GAAG,CAACd,WAAW,EAAEhB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC1F,KAAK,CAAC4F,cAAc,CAAC,CAAC,CAAC;IACxK;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,cAAc;IACnBiD,KAAK,EAAE,SAASqF,YAAYA,CAAC7E,KAAK,EAAE;MAClC,IAAI,CAAC,IAAI,CAACsF,OAAO,EAAE;QACjB,IAAI,CAACA,OAAO,GAAGnC,oBAAoB,CAAC,CAAC;MACvC;MACA,IAAIuC,KAAK,GAAG1F,KAAK,CAAC0F,KAAK;QACrBtB,QAAQ,GAAGpE,KAAK,CAACoE,QAAQ;QACzBJ,aAAa,GAAGhE,KAAK,CAACgE,aAAa;QACnCyD,OAAO,GAAGzH,KAAK,CAACiE,EAAE;QAClB+B,MAAM,GAAGhG,KAAK,CAACgG,MAAM;QACrBC,gBAAgB,GAAGjG,KAAK,CAACiG,gBAAgB;QACzCL,cAAc,GAAG5F,KAAK,CAAC4F,cAAc;QACrC1B,KAAK,GAAGlE,KAAK,CAACkE,KAAK;QACnBC,QAAQ,GAAGnE,KAAK,CAACmE,QAAQ;MAC3B,IAAImB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAI,CAACO,WAAW,GAAGP,OAAO,CAACoC,SAAS,CAAC,IAAI,CAACrD,iBAAiB,CAAC;MAC5D,IAAI,OAAO2B,MAAM,KAAK,UAAU,IAAI,OAAO7B,QAAQ,KAAK,UAAU,IAAI6B,MAAM,KAAK,QAAQ,EAAE;QACzF,IAAI,CAACF,cAAc,CAAC9F,KAAK,CAAC;QAC1B;MACF;MACA,IAAIkE,KAAK,CAACtH,MAAM,GAAG,CAAC,EAAE;QACpB,IAAI,CAACyJ,gBAAgB,CAACrG,KAAK,CAAC;QAC5B;MACF;MACA,IAAIiE,EAAE,GAAGD,aAAa,GAAG5E,eAAe,CAAC,CAAC,CAAC,EAAE4E,aAAa,EAAEyD,OAAO,CAAC,GAAGA,OAAO;MAC9E,IAAIL,UAAU,GAAG9D,gBAAgB,CAAC7G,MAAM,CAACQ,IAAI,CAACgH,EAAE,CAAC,EAAEG,QAAQ,EAAE4B,MAAM,CAAC;MACpEV,OAAO,CAACc,KAAK,CAAC,CAACH,gBAAgB,EAAEP,KAAK,EAAEzG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgF,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/EmD,UAAU,EAAEA;MACd,CAAC,CAAC,EAAEhD,QAAQ,EAAEwB,cAAc,CAAC,CAAC;IAChC;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,QAAQ;IACbiD,KAAK,EAAE,SAASmI,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC5H,KAAK;QAC3BmE,QAAQ,GAAGyD,YAAY,CAACzD,QAAQ;QAChCuB,KAAK,GAAGkC,YAAY,CAAClC,KAAK;QAC1BtB,QAAQ,GAAGwD,YAAY,CAACxD,QAAQ;QAChCJ,aAAa,GAAG4D,YAAY,CAAC5D,aAAa;QAC1CgC,MAAM,GAAG4B,YAAY,CAAC5B,MAAM;QAC5BjC,QAAQ,GAAG6D,YAAY,CAAC7D,QAAQ;QAChCG,KAAK,GAAG0D,YAAY,CAAC1D,KAAK;QAC1BjG,IAAI,GAAG2J,YAAY,CAAC3J,IAAI;QACxBgG,EAAE,GAAG2D,YAAY,CAAC3D,EAAE;QACpBU,QAAQ,GAAGiD,YAAY,CAACjD,QAAQ;QAChCiB,cAAc,GAAGgC,YAAY,CAAChC,cAAc;QAC5CX,eAAe,GAAG2C,YAAY,CAAC3C,eAAe;QAC9C4C,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;QACpDC,MAAM,GAAG5L,wBAAwB,CAAC0L,YAAY,EAAE3L,SAAS,CAAC;MAC5D,IAAI8L,KAAK,GAAG/E,QAAQ,CAAC+E,KAAK,CAAC5D,QAAQ,CAAC;MACpC;MACA,IAAI6D,UAAU,GAAGxE,cAAc,CAAC,IAAI,CAACe,KAAK,CAACC,KAAK,CAAC;MACjD,IAAI,OAAOL,QAAQ,KAAK,UAAU,EAAE;QAClC,OAAOA,QAAQ,CAAC6D,UAAU,CAAC;MAC7B;MACA,IAAI,CAACjE,QAAQ,IAAIgE,KAAK,KAAK,CAAC,IAAI3D,QAAQ,IAAI,CAAC,EAAE;QAC7C,OAAOD,QAAQ;MACjB;MACA,IAAI8D,cAAc,GAAG,SAASA,cAAcA,CAACC,SAAS,EAAE;QACtD,IAAIC,gBAAgB,GAAGD,SAAS,CAAClI,KAAK;UACpCoI,qBAAqB,GAAGD,gBAAgB,CAAC3D,KAAK;UAC9CA,KAAK,GAAG4D,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;UACrEC,SAAS,GAAGF,gBAAgB,CAACE,SAAS;QACxC,IAAIxH,GAAG,GAAG,aAAakC,YAAY,CAACmF,SAAS,EAAEjJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6I,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1FtD,KAAK,EAAEvF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,KAAK,CAAC,EAAEwD,UAAU,CAAC;UAC1DK,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;QACH,OAAOxH,GAAG;MACZ,CAAC;MACD,IAAIkH,KAAK,KAAK,CAAC,EAAE;QACf,OAAOE,cAAc,CAACjF,QAAQ,CAACsF,IAAI,CAACnE,QAAQ,CAAC,CAAC;MAChD;MACA,OAAO,aAAatB,KAAK,CAAC0F,aAAa,CAAC,KAAK,EAAE,IAAI,EAAEvF,QAAQ,CAACwF,GAAG,CAACrE,QAAQ,EAAE,UAAUsE,KAAK,EAAE;QAC3F,OAAOR,cAAc,CAACQ,KAAK,CAAC;MAC9B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOhF,OAAO;AAChB,CAAC,CAACX,aAAa,CAAC;AAChBW,OAAO,CAACiF,WAAW,GAAG,SAAS;AAC/BjF,OAAO,CAACkF,YAAY,GAAG;EACrBjD,KAAK,EAAE,CAAC;EACRtB,QAAQ,EAAE,IAAI;EACdnG,IAAI,EAAE,EAAE;EACRgG,EAAE,EAAE,EAAE;EACND,aAAa,EAAE,EAAE;EACjBgC,MAAM,EAAE,MAAM;EACdjC,QAAQ,EAAE,IAAI;EACdY,QAAQ,EAAE,IAAI;EACdT,KAAK,EAAE,EAAE;EACT0B,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;EAC5CK,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG,CAAC;AACjD,CAAC;AACDxC,OAAO,CAACmF,SAAS,GAAG;EAClB3K,IAAI,EAAEgF,SAAS,CAAC4F,SAAS,CAAC,CAAC5F,SAAS,CAACzE,MAAM,EAAEyE,SAAS,CAAC6F,MAAM,CAAC,CAAC;EAC/D7E,EAAE,EAAEhB,SAAS,CAAC4F,SAAS,CAAC,CAAC5F,SAAS,CAACzE,MAAM,EAAEyE,SAAS,CAAC6F,MAAM,CAAC,CAAC;EAC7D9E,aAAa,EAAEf,SAAS,CAAC6F,MAAM;EAC/B;EACA1E,QAAQ,EAAEnB,SAAS,CAAC8F,MAAM;EAC1BrD,KAAK,EAAEzC,SAAS,CAAC8F,MAAM;EACvB/C,MAAM,EAAE/C,SAAS,CAAC4F,SAAS,CAAC,CAAC5F,SAAS,CAAC6F,MAAM,EAAE7F,SAAS,CAAC+F,IAAI,CAAC,CAAC;EAC/D9E,KAAK,EAAEjB,SAAS,CAACgG,OAAO,CAAChG,SAAS,CAACiG,KAAK,CAAC;IACvC9E,QAAQ,EAAEnB,SAAS,CAAC8F,MAAM,CAACI,UAAU;IACrC3E,KAAK,EAAEvB,SAAS,CAACzE,MAAM,CAAC2K,UAAU;IAClCnD,MAAM,EAAE/C,SAAS,CAAC4F,SAAS,CAAC,CAAC5F,SAAS,CAACmG,KAAK,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAEnG,SAAS,CAAC+F,IAAI,CAAC,CAAC;IACxH;IACA/B,UAAU,EAAEhE,SAAS,CAACgG,OAAO,CAAC,QAAQ,CAAC;IACvCrD,cAAc,EAAE3C,SAAS,CAAC+F;EAC5B,CAAC,CAAC,CAAC;EACH7E,QAAQ,EAAElB,SAAS,CAAC4F,SAAS,CAAC,CAAC5F,SAAS,CAACoG,IAAI,EAAEpG,SAAS,CAAC+F,IAAI,CAAC,CAAC;EAC/DjF,QAAQ,EAAEd,SAAS,CAACqG,IAAI;EACxB3E,QAAQ,EAAE1B,SAAS,CAACqG,IAAI;EACxB1D,cAAc,EAAE3C,SAAS,CAAC+F,IAAI;EAC9B;EACA/D,eAAe,EAAEhC,SAAS,CAACqG,IAAI;EAC/BrD,gBAAgB,EAAEhD,SAAS,CAAC+F,IAAI;EAChCnB,kBAAkB,EAAE5E,SAAS,CAAC+F;AAChC,CAAC;AACD,eAAevF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}