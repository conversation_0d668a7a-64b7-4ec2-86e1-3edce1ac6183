{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CodeSandboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/CodeSandboxCircleFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CodeSandboxCircleFilledSvg\n  }));\n};\nCodeSandboxCircleFilled.displayName = 'CodeSandboxCircleFilled';\nexport default /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);", "map": {"version": 3, "names": ["_objectSpread", "React", "CodeSandboxCircleFilledSvg", "AntdIcon", "CodeSandboxCircleFilled", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/CodeSandboxCircleFilled.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CodeSandboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/CodeSandboxCircleFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CodeSandboxCircleFilledSvg\n  }));\n};\nCodeSandboxCircleFilled.displayName = 'CodeSandboxCircleFilled';\nexport default /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,uBAAuB,CAACK,WAAW,GAAG,yBAAyB;AAC/D,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}