{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, Alert, Statistic, Row, Col, Progress } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay = ({\n  result\n}) => {\n  // 表格列定义 - 与Streamlit版本保持一致\n  const predictionColumns = [{\n    title: '时间戳',\n    dataIndex: 'timestamp',\n    key: 'timestamp',\n    width: 180\n  }, {\n    title: '真实流量 (pps)',\n    dataIndex: 'packets_per_sec',\n    key: 'packets_per_sec',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '平滑后真实流量',\n    dataIndex: 'packets_per_sec_smooth',\n    key: 'packets_per_sec_smooth',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '模型预测趋势',\n    dataIndex: 'pred_smooth',\n    key: 'pred_smooth',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '动态阈值',\n    dataIndex: 'threshold',\n    key: 'threshold',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '异常状态',\n    dataIndex: 'is_anomaly',\n    key: 'is_anomaly',\n    render: isAnomaly => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: isAnomaly ? '#ff4d4f' : '#52c41a'\n      },\n      children: isAnomaly ? '🔴 异常' : '🟢 正常'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",\n          value: result.suggested_threshold,\n          precision: 2,\n          valueStyle: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u68C0\\u6D4B\\u5230\\u7684\\u5F02\\u5E38\\u70B9\\u6570\\u91CF\",\n          value: result.anomaly_count,\n          valueStyle: {\n            color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u8FD9\\u662F\\u57FA\\u4E8E\\u56FE\\u4E2D\\u7684\\u52A8\\u6001\\u9608\\u503C\\uFF08\\u7EA2\\u8272\\u865A\\u7EBF\\uFF09\\u68C0\\u6D4B\\u51FA\\u7684\\u3001\\u6D41\\u91CF\\u8D85\\u8FC7\\u9608\\u503C\\u7684\\u5177\\u4F53\\u65F6\\u95F4\\u70B9\\u6570\\u91CF\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), result.predictions && result.predictions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u4E0B\\u56FE\\u5C55\\u793A\\u4E86\\u771F\\u5B9E\\u6D41\\u91CF\\u3001\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\\u548C\\u7528\\u4E8E\\u5B9E\\u65F6\\u68C0\\u6D4B\\u7684\\u52A8\\u6001\\u9608\\u503C\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u9884\\u6D4B\\u7ED3\\u679C\\u56FE\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 400,\n          marginTop: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: result.predictions // 显示所有数据点\n            ,\n            margin: {\n              top: 5,\n              right: 30,\n              left: 20,\n              bottom: 5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"timestamp\",\n              tick: {\n                fontSize: 10\n              },\n              interval: \"preserveStartEnd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              labelFormatter: value => `时间: ${value}`,\n              formatter: (value, name) => [Number(value === null || value === void 0 ? void 0 : value.toFixed(2)), name]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"packets_per_sec_smooth\",\n              stroke: \"#007bff\",\n              strokeWidth: 2,\n              dot: false,\n              name: \"\\u771F\\u5B9E\\u6D41\\u91CF (\\u5E73\\u6ED1\\u540E)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"pred_smooth\",\n              stroke: \"#ffa500\",\n              strokeWidth: 2,\n              strokeDasharray: \"5 5\",\n              dot: false,\n              name: \"\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"threshold\",\n              stroke: \"#ff0000\",\n              strokeWidth: 2,\n              strokeDasharray: \"3 3\",\n              dot: false,\n              name: \"\\u52A8\\u6001\\u6E05\\u6D17\\u9608\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), (result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        children: \"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [result.duration_seconds !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u6D4B\\u8017\\u65F6\",\n            value: result.duration_seconds,\n            precision: 2,\n            suffix: \"\\u79D2\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 15\n        }, this), result.cpu_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 15\n        }, this), result.memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this), result.gpu_memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 15\n        }, this), result.gpu_utilization_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5229\\u7528\\u7387\",\n            value: result.gpu_utilization_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#eb2f96'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\n_c = PredictionResultDisplay;\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\nconst ModelPredictionPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState([]);\n  const [selectedModels, setSelectedModels] = useState([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState('');\n  const [selectedProt, setSelectedProt] = useState('');\n  const [selectedDatatype, setSelectedDatatype] = useState('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const {\n    submitPredictionTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为与同步预测相同的格式\n      const asyncResult = {\n        predictions: selectedTask.result.predictions || [],\n        anomaly_count: selectedTask.result.anomaly_count || 0,\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async modelFile => {\n    if (!modelFile || !modelDir) return;\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = modelFile => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict = [];\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels = [];\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error) {\n          var _error$response4, _error$response4$data;\n          message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n        }\n      }\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n      modelsToPredict = validModels;\n    }\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults = [];\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round(i / modelsToPredict.length * 90));\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        const response = await modelPredictionAPI.predict(formData);\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent\n          });\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      message.error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : csvDir && selectedCsvFile;\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: csvFilesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: csvFilesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCsvFile,\n                onChange: setSelectedCsvFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: csvFilesLoading,\n                children: availableCsvFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6A21\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n            compact: true,\n            style: {\n              marginTop: 8,\n              display: 'flex'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              value: modelDir,\n              onChange: e => setModelDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              style: {\n                flex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: fetchModelFiles,\n              loading: modelsLoading,\n              disabled: !modelDir,\n              style: {\n                marginLeft: 8\n              },\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: predictionMode,\n            onChange: e => setPredictionMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multiple\",\n              children: \"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), predictionMode === 'single' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedModelFile,\n              onChange: handleModelFileChange,\n              placeholder: \"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 15\n          }, this), selectedModelFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                  spinning: matchingFilesLoading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 8,\n                      padding: 12,\n                      backgroundColor: '#f5f5f5',\n                      borderRadius: 4\n                    },\n                    children: matchingFilesLoading ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u53C2\\u6570\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 34\n                        }, this), \" \", selectedParamsFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 34\n                        }, this), \" \", selectedScalerFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 822,\n                        columnNumber: 31\n                      }, this), !showManualSelection && selectedProt && selectedDatatype && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u534F\\u8BAE:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 38\n                          }, this), \" \", selectedProt]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 825,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 826,\n                            columnNumber: 38\n                          }, this), \" \", selectedDatatype]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 826,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 21\n              }, this), showManualSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedProt,\n                      onChange: setSelectedProt,\n                      placeholder: \"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",\n                      style: {\n                        width: '100%'\n                      },\n                      children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                        value: prot,\n                        children: prot\n                      }, prot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 847,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 840,\n                      columnNumber: 29\n                    }, this), selectedProt && /*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedDatatype,\n                      onChange: setSelectedDatatype,\n                      placeholder: `选择与模型对应的 ${selectedProt} 数据类型`,\n                      style: {\n                        width: '100%'\n                      },\n                      children: (datatypeOptions[selectedProt] || []).map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                        value: datatype,\n                        children: datatype\n                      }, datatype, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 861,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              mode: \"multiple\",\n              value: selectedModels,\n              onChange: setSelectedModels,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 13\n        }, this), availablePthFiles.length === 0 && !modelsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",\n          description: \"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u9884\\u6D4B\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncPrediction,\n            onChange: e => setUseAsyncPrediction(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            disabled: predictionMode === 'multiple' // 多模型预测暂不支持异步\n            ,\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u9884\\u6D4B\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 11\n        }, this), useAsyncPrediction && predictionMode === 'single' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 13\n        }, this), predictionMode === 'multiple' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\",\n          description: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\\u76EE\\u524D\\u4EC5\\u652F\\u6301\\u540C\\u6B65\\u6A21\\u5F0F\\uFF0C\\u9884\\u6D4B\\u8FC7\\u7A0B\\u4E2D\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 910,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 909,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartPrediction,\n        loading: predicting,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: predicting ? '正在预测...' : '开始预测与检测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 966,\n        columnNumber: 9\n      }, this), predicting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 965,\n      columnNumber: 7\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      children: results.length > 1 ?\n      /*#__PURE__*/\n      // 多模型结果展示 - 与Streamlit版本一致\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n            value: selectedResultIndex,\n            onChange: value => setSelectedResultIndex(value),\n            children: results.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: index,\n              children: result.model_name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 15\n        }, this), results[selectedResultIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: [\"\\u6A21\\u578B: \", results[selectedResultIndex].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: results[selectedResultIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // 单模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", results[0].model_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n          result: results[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 989,\n      columnNumber: 9\n    }, this), completedPredictionTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\",\n        description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u9884\\u6D4B\\u6570\\u636E\\u548C\\u5F02\\u5E38\\u68C0\\u6D4B\\u62A5\\u544A\\u3002\",\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u4EFB\\u52A1\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedAsyncTaskId,\n            onChange: handleAsyncTaskSelect,\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u9884\\u6D4B\\u4EFB\\u52A1\",\n            children: completedPredictionTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n              value: task.task_id,\n              children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n            }, task.task_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1043,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1041,\n          columnNumber: 13\n        }, this), asyncPredictionResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", asyncPredictionResults[0].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: asyncPredictionResults[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1031,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 669,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelPredictionPage, \"wJQzR8+FGUU4HP1Z5Od9Oc7kbO4=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelPredictionPage;\nexport default ModelPredictionPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PredictionResultDisplay\");\n$RefreshReg$(_c2, \"ModelPredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelPredictionAPI", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "result", "predictionColumns", "title", "dataIndex", "key", "width", "render", "value", "toFixed", "isAnomaly", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "span", "suggested_threshold", "precision", "valueStyle", "type", "showIcon", "marginTop", "anomaly_count", "fontSize", "predictions", "length", "strong", "height", "data", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "interval", "labelFormatter", "formatter", "name", "Number", "stroke", "strokeWidth", "dot", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "_c", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "_s", "dataSource", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "submitPredictionTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedPredictionTasks", "handleAsyncTaskSelect", "taskId", "selectedTask", "find", "task", "task_id", "asyncResult", "model_name", "latestTask", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "allResults", "i", "model", "Math", "round", "predict", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "fontWeight", "className", "direction", "size", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "map", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status", "index", "includes", "split", "Date", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Table,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n  // 表格列定义 - 与Streamlit版本保持一致\n  const predictionColumns = [\n    {\n      title: '时间戳',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 180,\n    },\n    {\n      title: '真实流量 (pps)',\n      dataIndex: 'packets_per_sec',\n      key: 'packets_per_sec',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '平滑后真实流量',\n      dataIndex: 'packets_per_sec_smooth',\n      key: 'packets_per_sec_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '模型预测趋势',\n      dataIndex: 'pred_smooth',\n      key: 'pred_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '动态阈值',\n      dataIndex: 'threshold',\n      key: 'threshold',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '异常状态',\n      dataIndex: 'is_anomaly',\n      key: 'is_anomaly',\n      render: (isAnomaly: boolean) => (\n        <span style={{ color: isAnomaly ? '#ff4d4f' : '#52c41a' }}>\n          {isAnomaly ? '🔴 异常' : '🟢 正常'}\n        </span>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={12}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n        <Col span={12}>\n          <Statistic\n            title=\"检测到的异常点数量\"\n            value={result.anomaly_count}\n            valueStyle={{ color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a' }}\n          />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            这是基于图中的动态阈值（红色虚线）检测出的、流量超过阈值的具体时间点数量。\n          </Text>\n        </Col>\n      </Row>\n\n      {/* 预测图表 - 与Streamlit版本保持一致 */}\n      {result.predictions && result.predictions.length > 0 && (\n        <div style={{ marginBottom: 24 }}>\n          <Alert\n            message=\"下图展示了真实流量、模型预测趋势和用于实时检测的动态阈值。\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Text strong>预测结果图表</Text>\n          <div style={{ height: 400, marginTop: 8 }}>\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart\n                data={result.predictions} // 显示所有数据点\n                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n              >\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis\n                  dataKey=\"timestamp\"\n                  tick={{ fontSize: 10 }}\n                  interval=\"preserveStartEnd\"\n                />\n                <YAxis />\n                <Tooltip\n                  labelFormatter={(value) => `时间: ${value}`}\n                  formatter={(value: number, name: string) => [Number(value?.toFixed(2)), name]}\n                />\n                <Legend />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"packets_per_sec_smooth\"\n                  stroke=\"#007bff\"\n                  strokeWidth={2}\n                  dot={false}\n                  name=\"真实流量 (平滑后)\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"pred_smooth\"\n                  stroke=\"#ffa500\"\n                  strokeWidth={2}\n                  strokeDasharray=\"5 5\"\n                  dot={false}\n                  name=\"模型预测趋势\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"threshold\"\n                  stroke=\"#ff0000\"\n                  strokeWidth={2}\n                  strokeDasharray=\"3 3\"\n                  dot={false}\n                  name=\"动态清洗阈值\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  anomaly_count: number;\n  suggested_threshold: number;\n  predictions: Array<{\n    timestamp: string;\n    packets_per_sec: number;\n    packets_per_sec_smooth: number;\n    pred_smooth: number;\n    threshold: number;\n    is_anomaly: boolean;\n  }>;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const { submitPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为与同步预测相同的格式\n      const asyncResult: PredictionResult = {\n        predictions: selectedTask.result.predictions || [],\n        anomaly_count: selectedTask.result.anomaly_count || 0,\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测数据和异常检测报告。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                <PredictionResultDisplay result={asyncPredictionResults[0]} />\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EAEJC,KAAK,EACLC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,QACH,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAQ,CAAC,GAAGhC,MAAM;AAC1B,MAAM;EAAEiC;AAAO,CAAC,GAAG/B,MAAM;;AAEzB;AACA,MAAMgC,uBAA+D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EACtF;EACA,MAAMC,iBAAiB,GAAG,CACxB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,wBAAwB;IACnCC,GAAG,EAAE,wBAAwB;IAC7BE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGG,SAAkB,iBACzBjB,OAAA;MAAMkB,KAAK,EAAE;QAAEC,KAAK,EAAEF,SAAS,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAG,QAAA,EACvDH,SAAS,GAAG,OAAO,GAAG;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAEV,CAAC,CACF;EAED,oBACExB,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAAChB,GAAG;MAACyC,MAAM,EAAE,EAAG;MAACP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC3CpB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,8DAAiB;UACvBK,KAAK,EAAEP,MAAM,CAACoB,mBAAoB;UAClCC,SAAS,EAAE,CAAE;UACbC,UAAU,EAAE;YAAEX,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDhB,MAAM,CAACoB,mBAAmB,iBACzB5B,OAAA,CAAClB,KAAK;UACJF,OAAO,EAAC,kNAAwC;UAChDmD,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRd,KAAK,EAAE;YAAEe,SAAS,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,wDAAW;UACjBK,KAAK,EAAEP,MAAM,CAAC0B,aAAc;UAC5BJ,UAAU,EAAE;YAAEX,KAAK,EAAEX,MAAM,CAAC0B,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG;UAAU;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACFxB,OAAA,CAACI,IAAI;UAAC2B,IAAI,EAAC,WAAW;UAACb,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAG,CAAE;UAAAf,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhB,MAAM,CAAC4B,WAAW,IAAI5B,MAAM,CAAC4B,WAAW,CAACC,MAAM,GAAG,CAAC,iBAClDrC,OAAA;MAAKkB,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC/BpB,OAAA,CAAClB,KAAK;QACJF,OAAO,EAAC,gLAA+B;QACvCmD,IAAI,EAAC,MAAM;QACXC,QAAQ;QACRd,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAG;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFxB,OAAA,CAACI,IAAI;QAACkC,MAAM;QAAAlB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1BxB,OAAA;QAAKkB,KAAK,EAAE;UAAEqB,MAAM,EAAE,GAAG;UAAEN,SAAS,EAAE;QAAE,CAAE;QAAAb,QAAA,eACxCpB,OAAA,CAACJ,mBAAmB;UAACiB,KAAK,EAAC,MAAM;UAAC0B,MAAM,EAAC,MAAM;UAAAnB,QAAA,eAC7CpB,OAAA,CAACX,SAAS;YACRmD,IAAI,EAAEhC,MAAM,CAAC4B,WAAY,CAAC;YAAA;YAC1BK,MAAM,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,IAAI,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAEnDpB,OAAA,CAACP,aAAa;cAACqD,eAAe,EAAC;YAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCxB,OAAA,CAACT,KAAK;cACJwD,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAE;gBAAEb,QAAQ,EAAE;cAAG,CAAE;cACvBc,QAAQ,EAAC;YAAkB;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxB,OAAA,CAACR,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTxB,OAAA,CAACN,OAAO;cACNwD,cAAc,EAAGnC,KAAK,IAAK,OAAOA,KAAK,EAAG;cAC1CoC,SAAS,EAAEA,CAACpC,KAAa,EAAEqC,IAAY,KAAK,CAACC,MAAM,CAACtC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEoC,IAAI;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACFxB,OAAA,CAACL,MAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,wBAAwB;cAChCO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAY;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACFxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,aAAa;cACrBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfT,eAAe,EAAC,KAAK;cACrBU,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,WAAW;cACnBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfT,eAAe,EAAC,KAAK;cACrBU,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAAChB,MAAM,CAACiD,gBAAgB,KAAKC,SAAS,IAAIlD,MAAM,CAACmD,WAAW,KAAKD,SAAS,IAAIlD,MAAM,CAACoD,SAAS,KAAKF,SAAS,IAAIlD,MAAM,CAACqD,aAAa,KAAKH,SAAS,IAAIlD,MAAM,CAACsD,uBAAuB,KAAKJ,SAAS,kBACjM1D,OAAA;MAAKkB,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC/BpB,OAAA,CAACG,KAAK;QAAC4D,KAAK,EAAE,CAAE;QAAA3C,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BxB,OAAA,CAAChB,GAAG;QAACyC,MAAM,EAAE,EAAG;QAAAL,QAAA,GACbZ,MAAM,CAACiD,gBAAgB,KAAKC,SAAS,iBACpC1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,0BAAM;YACZK,KAAK,EAAEP,MAAM,CAACiD,gBAAiB;YAC/B5B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,QAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACmD,WAAW,KAAKD,SAAS,iBAC/B1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,uBAAQ;YACdK,KAAK,EAAEP,MAAM,CAACmD,WAAY;YAC1B9B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,GAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACoD,SAAS,KAAKF,SAAS,iBAC7B1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,0BAAM;YACZK,KAAK,EAAEP,MAAM,CAACoD,SAAU;YACxB/B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,IAAI;YACXnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACqD,aAAa,KAAKH,SAAS,iBACjC1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,iBAAO;YACbK,KAAK,EAAEP,MAAM,CAACqD,aAAc;YAC5BhC,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,IAAI;YACXnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACsD,uBAAuB,KAAKJ,SAAS,iBAC3C1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,uBAAQ;YACdK,KAAK,EAAEP,MAAM,CAACsD,uBAAwB;YACtCjC,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,GAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CAAC;AAEV,CAAC;;AAED;AAAA0C,EAAA,GA7MM3D,uBAA+D;AA8MrE,MAAM4D,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC9C,MAAMC,eAAe,GAAG;EACtBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACnCC,IAAI,EAAE,CAAC,KAAK;AACd,CAAC;AAsBD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1G,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAAC2G,YAAY,EAAEC,eAAe,CAAC,GAAG5G,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC6G,MAAM,EAAEC,SAAS,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhH,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACmH,eAAe,EAAEC,kBAAkB,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACqH,QAAQ,EAAEC,WAAW,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxH,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACyH,cAAc,EAAEC,iBAAiB,CAAC,GAAG1H,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC2H,aAAa,EAAEC,gBAAgB,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6H,cAAc,EAAEC,iBAAiB,CAAC,GAAG9H,QAAQ,CAAwB,QAAQ,CAAC;;EAErF;EACA,MAAM,CAAC+H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhI,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAACiI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlI,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAACmI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpI,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAACqI,YAAY,EAAEC,eAAe,CAAC,GAAGtI,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACuI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxI,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACyI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC2I,UAAU,EAAEC,aAAa,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6I,QAAQ,EAAEC,WAAW,CAAC,GAAG9I,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC+I,OAAO,EAAEC,UAAU,CAAC,GAAGhJ,QAAQ,CAAqB,EAAE,CAAC;EAC9D,MAAM,CAACiJ,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlJ,QAAQ,CAAS,CAAC,CAAC;EACzE,MAAM,CAACmJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM;IAAEqJ,oBAAoB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAG1H,cAAc,CAAC,CAAC;EAC/F,MAAM,CAAC2H,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzJ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpE;EACA,MAAM,CAAC0J,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3J,QAAQ,CAAqB,EAAE,CAAC;EAC5F,MAAM,CAAC4J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7J,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAM8J,wBAAwB,GAAGR,uBAAuB,CAAC,YAAY,CAAC;;EAEtE;EACA,MAAMS,qBAAqB,GAAIC,MAAc,IAAK;IAChDH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMC,YAAY,GAAGH,wBAAwB,CAACI,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKJ,MAAM,CAAC;IAEnF,IAAIC,YAAY,IAAIA,YAAY,CAAC1H,MAAM,EAAE;MACvC;MACA,MAAM8H,WAA6B,GAAG;QACpClG,WAAW,EAAE8F,YAAY,CAAC1H,MAAM,CAAC4B,WAAW,IAAI,EAAE;QAClDF,aAAa,EAAEgG,YAAY,CAAC1H,MAAM,CAAC0B,aAAa,IAAI,CAAC;QACrDN,mBAAmB,EAAEsG,YAAY,CAAC1H,MAAM,CAACoB,mBAAmB,IAAI,CAAC;QACjE2G,UAAU,EAAEL,YAAY,CAAC1H,MAAM,CAAC+H,UAAU,IAAI,MAAM;QACpD9E,gBAAgB,EAAEyE,YAAY,CAAC1H,MAAM,CAACiD,gBAAgB;QACtDE,WAAW,EAAEuE,YAAY,CAAC1H,MAAM,CAACmD,WAAW;QAC5CC,SAAS,EAAEsE,YAAY,CAAC1H,MAAM,CAACoD,SAAS;QACxCC,aAAa,EAAEqE,YAAY,CAAC1H,MAAM,CAACqD,aAAa;QAChDC,uBAAuB,EAAEoE,YAAY,CAAC1H,MAAM,CAACsD;MAC/C,CAAC;MAED8D,yBAAyB,CAAC,CAACU,WAAW,CAAC,CAAC;IAC1C;EACF,CAAC;;EAED;EACApK,SAAS,CAAC,MAAM;IACdsJ,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAtJ,SAAS,CAAC,MAAM;IACd,IAAI6J,wBAAwB,CAAC1F,MAAM,GAAG,CAAC,IAAI,CAACwF,mBAAmB,EAAE;MAC/D,MAAMW,UAAU,GAAGT,wBAAwB,CAACA,wBAAwB,CAAC1F,MAAM,GAAG,CAAC,CAAC;MAChF2F,qBAAqB,CAACQ,UAAU,CAACH,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACN,wBAAwB,EAAEF,mBAAmB,CAAC,CAAC;;EAEnD;EACA,MAAMY,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC3D,MAAM,EAAE;IAEbO,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAM7I,kBAAkB,CAAC8I,YAAY,CAAC7D,MAAM,CAAC;MAC9DG,oBAAoB,CAACyD,QAAQ,CAAClG,IAAI,CAACoG,KAAK,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBnK,OAAO,CAACiK,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtG,IAAI,cAAAuG,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAa,CAAC;MAC5D/D,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC3D,QAAQ,EAAE;IAEfO,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAM6C,QAAQ,GAAG,MAAM7I,kBAAkB,CAACqJ,cAAc,CAAC5D,QAAQ,CAAC;MAClEG,oBAAoB,CAACiD,QAAQ,CAAClG,IAAI,CAAC2G,SAAS,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAON,KAAU,EAAE;MAAA,IAAAO,gBAAA,EAAAC,qBAAA;MACnBzK,OAAO,CAACiK,KAAK,CAAC,EAAAO,gBAAA,GAAAP,KAAK,CAACH,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5G,IAAI,cAAA6G,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,YAAY,CAAC;MAC3DvD,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMyD,cAAc,GAAG,MAAOC,SAAiB,IAAK;IAClD,IAAI,CAACA,SAAS,IAAI,CAACjE,QAAQ,EAAE;IAE7B+B,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF;MACA,MAAMqB,QAAQ,GAAG,MAAM7I,kBAAkB,CAAC2J,gBAAgB,CAACD,SAAS,EAAEjE,QAAQ,CAAC;MAE/E,IAAIoD,QAAQ,CAAClG,IAAI,EAAE;QACjB,MAAMiH,aAAa,GAAGf,QAAQ,CAAClG,IAAI;;QAEnC;QACA2D,qBAAqB,CAACsD,aAAa,CAACC,eAAe,IAAI,EAAE,CAAC;QAC1DrD,qBAAqB,CAACoD,aAAa,CAACE,eAAe,IAAI,EAAE,CAAC;QAC1DpD,eAAe,CAACkD,aAAa,CAACG,QAAQ,IAAI,EAAE,CAAC;QAC7CnD,mBAAmB,CAACgD,aAAa,CAACI,QAAQ,IAAI,EAAE,CAAC;;QAEjD;QACA,IAAIJ,aAAa,CAACC,eAAe,IAAID,aAAa,CAACE,eAAe,EAAE;UAClE/K,OAAO,CAACkL,OAAO,CAAC,aAAa,CAAC;QAChC;QAEA,IAAIL,aAAa,CAACG,QAAQ,IAAIH,aAAa,CAACI,QAAQ,EAAE;UACpDjL,OAAO,CAACkL,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC;UAC5FlD,sBAAsB,CAAC,KAAK,CAAC;QAC/B,CAAC,MAAM;UACL/H,OAAO,CAACmL,OAAO,CAAC,4BAA4B,CAAC;UAC7CpD,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC,CAAC,OAAOkC,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnBrL,OAAO,CAACiK,KAAK,CAAC,EAAAmB,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxH,IAAI,cAAAyH,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAI,UAAU,CAAC;MACzD;MACA,MAAMkB,kBAAkB,GAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACnEhE,qBAAqB,CAAC,GAAG+D,kBAAkB,cAAc,CAAC;MAC1D7D,qBAAqB,CAAC,GAAG6D,kBAAkB,eAAe,CAAC;MAC3DvD,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,SAAS;MACRU,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM+C,qBAAqB,GAAIb,SAAiB,IAAK;IACnDtD,oBAAoB,CAACsD,SAAS,CAAC;IAC/B;IACApD,qBAAqB,CAAC,EAAE,CAAC;IACzBE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,eAAe,CAAC,EAAE,CAAC;IACnBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,sBAAsB,CAAC,KAAK,CAAC;;IAE7B;IACA2C,cAAc,CAACC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACArL,SAAS,CAAC,MAAM;IACd,IAAIwG,UAAU,KAAK,OAAO,IAAII,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAMgI,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B7B,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC3F,UAAU,EAAEI,MAAM,CAAC,CAAC;EAExB5G,SAAS,CAAC,MAAM;IACd,IAAIoH,QAAQ,IAAIA,QAAQ,CAACjD,MAAM,GAAG,CAAC,EAAE;MAAE;MACrC,MAAMgI,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BrB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMsB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAC/E,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkF,WAAW,GAAG;IAClBpH,IAAI,EAAE,MAAM;IACZqH,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACzI,MAAM,GAAG,CAAC,EAAE;QAC5BwC,eAAe,CAACgG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLjG,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMkG,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAIrG,UAAU,KAAK,QAAQ,IAAI,CAACE,YAAY,EAAE;MAC5ChG,OAAO,CAACiK,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAInE,UAAU,KAAK,OAAO,KAAK,CAACI,MAAM,IAAI,CAACI,eAAe,CAAC,EAAE;MAC3DtG,OAAO,CAACiK,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;;IAEA;IACA,IAAImC,eAMF,GAAG,EAAE;IAEP,IAAIlF,cAAc,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACE,iBAAiB,IAAI,CAACM,YAAY,IAAI,CAACE,gBAAgB,EAAE;QAC5D5H,OAAO,CAACiK,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MACAmC,eAAe,GAAG,CAAC;QACjBC,UAAU,EAAEjF,iBAAiB;QAC7BkF,WAAW,EAAEhF,kBAAkB;QAC/BiF,WAAW,EAAE/E,kBAAkB;QAC/BwD,QAAQ,EAAEtD,YAAY;QACtBuD,QAAQ,EAAErD;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAId,cAAc,CAACrD,MAAM,KAAK,CAAC,EAAE;QAC/BzD,OAAO,CAACiK,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;;MAEA;MACA,MAAMuC,WAMJ,GAAG,EAAE;MAEP,KAAK,MAAM7B,SAAS,IAAI7D,cAAc,EAAE;QACtC,IAAI;UACF,MAAMgD,QAAQ,GAAG,MAAM7I,kBAAkB,CAAC2J,gBAAgB,CAACD,SAAS,EAAEjE,QAAQ,CAAC;UAC/E,IAAIoD,QAAQ,CAAClG,IAAI,EAAE;YACjB,MAAMiH,aAAa,GAAGf,QAAQ,CAAClG,IAAI;YACnC,MAAM0I,WAAW,GAAGzB,aAAa,CAACC,eAAe;YACjD,MAAMyB,WAAW,GAAG1B,aAAa,CAACE,eAAe;YACjD,MAAMC,QAAQ,GAAGH,aAAa,CAACG,QAAQ;YACvC,MAAMC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;;YAEvC;YACA,IAAIqB,WAAW,IAAIC,WAAW,IAAIvB,QAAQ,IAAIC,QAAQ,EAAE;cACtDuB,WAAW,CAACC,IAAI,CAAC;gBACfJ,UAAU,EAAE1B,SAAS;gBACrB2B,WAAW;gBACXC,WAAW;gBACXvB,QAAQ;gBACRC;cACF,CAAC,CAAC;cACFjL,OAAO,CAACkL,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC;YACrE,CAAC,MAAM;cACLjL,OAAO,CAACmL,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC;YACxD;UACF,CAAC,MAAM;YACL3K,OAAO,CAACiK,KAAK,CAAC,QAAQU,SAAS,UAAU,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOV,KAAU,EAAE;UAAA,IAAAyC,gBAAA,EAAAC,qBAAA;UACnB3M,OAAO,CAACiK,KAAK,CAAC,QAAQU,SAAS,SAAS,EAAA+B,gBAAA,GAAAzC,KAAK,CAACH,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9I,IAAI,cAAA+I,qBAAA,uBAApBA,qBAAA,CAAsBvC,MAAM,KAAIH,KAAK,CAACjK,OAAO,EAAE,CAAC;QAC1F;MACF;MAEA,IAAIwM,WAAW,CAAC/I,MAAM,KAAK,CAAC,EAAE;QAC5BzD,OAAO,CAACiK,KAAK,CAAC,wCAAwC,CAAC;QACvD;MACF;MAEAmC,eAAe,GAAGI,WAAW;IAC/B;IAEAvE,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIQ,kBAAkB,IAAI3B,cAAc,KAAK,QAAQ,EAAE;QACrD;QACA,MAAM0F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAI/G,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3C4G,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE9G,YAAY,CAAC+G,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE5G,MAAM,CAAC;UAClC0G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAExG,eAAe,CAAC;QACnD;QAEAsG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE1F,iBAAiB,CAAC;QACpDwF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAExF,kBAAkB,CAAC;QACtDsF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEtF,kBAAkB,CAAC;QACtDoF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEpF,YAAY,CAAC;QAC9CkF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAElF,gBAAgB,CAAC;QACtDgF,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEpG,QAAQ,CAAC;QACtCkG,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEpG,QAAQ,CAAC;;QAE1C;QACA,MAAM2C,MAAM,GAAG,MAAMX,oBAAoB,CAACkE,QAAQ,CAAC;QAEnD,IAAIvD,MAAM,EAAE;UACVrJ,OAAO,CAACkL,OAAO,CAAC,gCAAgC,CAAC;UACjD;UACAjD,aAAa,CAAC,KAAK,CAAC;UACpBE,WAAW,CAAC,CAAC,CAAC;QAChB;QAEA,OAAO,CAAC;MACV;;MAEA;MACA,MAAM6E,UAA8B,GAAG,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,eAAe,CAAC3I,MAAM,EAAEwJ,CAAC,EAAE,EAAE;QAC/C,MAAMC,KAAK,GAAGd,eAAe,CAACa,CAAC,CAAC;;QAEhC;QACA9E,WAAW,CAACgF,IAAI,CAACC,KAAK,CAAEH,CAAC,GAAGb,eAAe,CAAC3I,MAAM,GAAI,EAAE,CAAC,CAAC;QAE1D,IAAI2I,eAAe,CAAC3I,MAAM,GAAG,CAAC,EAAE;UAC9BzD,OAAO,CAACiM,IAAI,CAAC,UAAUgB,CAAC,GAAG,CAAC,IAAIb,eAAe,CAAC3I,MAAM,KAAKyJ,KAAK,CAACb,UAAU,UAAU,CAAC;QACxF;QAEA,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAI/G,UAAU,KAAK,QAAQ,IAAIE,YAAY,EAAE;UAC3C;UACA4G,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE9G,YAAY,CAAC+G,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE5G,MAAM,CAAC;UAClC0G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAExG,eAAe,CAAC;QACnD;QAEAsG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEI,KAAK,CAACb,UAAU,CAAC;QACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEI,KAAK,CAACZ,WAAW,CAAC;QACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEI,KAAK,CAACX,WAAW,CAAC;QACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEI,KAAK,CAAClC,QAAQ,CAAC;QAChD4B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEI,KAAK,CAACjC,QAAQ,CAAC;QACpD2B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEpG,QAAQ,CAAC;QAE1C,MAAMoD,QAAQ,GAAG,MAAM7I,kBAAkB,CAACoM,OAAO,CAACT,QAAQ,CAAC;QAE3D,IAAI9C,QAAQ,CAAClG,IAAI,EAAE;UACjBoJ,UAAU,CAACP,IAAI,CAAC;YACd9C,UAAU,EAAEG,QAAQ,CAAClG,IAAI,CAAC+F,UAAU,IAAI,GAAGuD,KAAK,CAAClC,QAAQ,IAAIkC,KAAK,CAACjC,QAAQ,EAAE;YAC7E3H,aAAa,EAAEwG,QAAQ,CAAClG,IAAI,CAACN,aAAa,IAAI,CAAC;YAC/CN,mBAAmB,EAAE8G,QAAQ,CAAClG,IAAI,CAACZ,mBAAmB,IAAI,CAAC;YAC3DQ,WAAW,EAAEsG,QAAQ,CAAClG,IAAI,CAACJ,WAAW,IAAI,EAAE;YAC5C;YACAqB,gBAAgB,EAAEiF,QAAQ,CAAClG,IAAI,CAACiB,gBAAgB;YAChDE,WAAW,EAAE+E,QAAQ,CAAClG,IAAI,CAACmB,WAAW;YACtCC,SAAS,EAAE8E,QAAQ,CAAClG,IAAI,CAACoB,SAAS;YAClCC,aAAa,EAAE6E,QAAQ,CAAClG,IAAI,CAACqB,aAAa;YAC1CC,uBAAuB,EAAE4E,QAAQ,CAAClG,IAAI,CAACsB;UACzC,CAAC,CAAC;UAEF,IAAIkH,eAAe,CAAC3I,MAAM,GAAG,CAAC,EAAE;YAC9BzD,OAAO,CAACkL,OAAO,CAAC,QAAQgC,KAAK,CAACb,UAAU,OAAO,CAAC;UAClD;QACF;MACF;MAEAlE,WAAW,CAAC,GAAG,CAAC;MAChBE,UAAU,CAAC2E,UAAU,CAAC;MACtBhN,OAAO,CAACkL,OAAO,CAAC,SAAS8B,UAAU,CAACvJ,MAAM,SAAS,CAAC;IAEtD,CAAC,CAAC,OAAOwG,KAAU,EAAE;MAAA,IAAAqD,gBAAA,EAAAC,qBAAA;MACnBvN,OAAO,CAACiK,KAAK,CAAC,EAAAqD,gBAAA,GAAArD,KAAK,CAACH,QAAQ,cAAAwD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1J,IAAI,cAAA2J,qBAAA,uBAApBA,qBAAA,CAAsBnD,MAAM,KAAI,MAAM,CAAC;IACvD,CAAC,SAAS;MACRnC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMuF,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAG3H,UAAU,KAAK,QAAQ,GAAGE,YAAY,GAAIE,MAAM,IAAII,eAAgB;IAEpF,IAAIY,cAAc,KAAK,QAAQ,EAAE;MAC/B,OAAOuG,OAAO,IAAIrG,iBAAiB,IAAIM,YAAY,IAAIE,gBAAgB;IACzE,CAAC,MAAM;MACL,OAAO6F,OAAO,IAAI3G,cAAc,CAACrD,MAAM,GAAG,CAAC;IAC7C;EACF,CAAC;EAID,oBACErC,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;MAAC4D,KAAK,EAAE,CAAE;MAAC7C,KAAK,EAAE;QAAEiB,QAAQ,EAAE,MAAM;QAAEmK,UAAU,EAAE,GAAG;QAAE5K,YAAY,EAAE;MAAM,CAAE;MAAAN,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACvGxB,OAAA,CAACI,IAAI;MAAC2B,IAAI,EAAC,WAAW;MAAAX,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxB,OAAA,CAACrB,OAAO;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXxB,OAAA,CAAC7B,IAAI;MAACuC,KAAK,EAAC,gCAAO;MAAC6L,SAAS,EAAC,eAAe;MAAAnL,QAAA,eAC3CpB,OAAA,CAACtB,KAAK;QAAC8N,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvL,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BxB,OAAA,CAAC5B,KAAK,CAACsO,KAAK;YACV3L,KAAK,EAAE2D,UAAW;YAClBkG,QAAQ,EAAG+B,CAAC,IAAKhI,aAAa,CAACgI,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;YAC/CG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCxB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAC,OAAO;cAAAK,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGLkD,UAAU,KAAK,QAAQ,iBACtB1E,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCxB,OAAA,CAACK,OAAO;YAAA,GAAKmK,WAAW;YAAEtJ,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAChDpB,OAAA;cAAGuM,SAAS,EAAC,sBAAsB;cAAAnL,QAAA,eACjCpB,OAAA,CAACb,aAAa;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJxB,OAAA;cAAGuM,SAAS,EAAC,iBAAiB;cAAAnL,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDxB,OAAA;cAAGuM,SAAS,EAAC,iBAAiB;cAAAnL,QAAA,EAAC;YAE/B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGAkD,UAAU,KAAK,OAAO,iBACrB1E,OAAA,CAACtB,KAAK;UAAC8N,SAAS,EAAC,UAAU;UAACtL,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAO,CAAE;UAAAO,QAAA,gBACnDpB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAAC1B,KAAK,CAACoO,KAAK;cAACG,OAAO;cAAC3L,KAAK,EAAE;gBAAEe,SAAS,EAAE,CAAC;gBAAE6K,OAAO,EAAE;cAAO,CAAE;cAAA1L,QAAA,gBAC5DpB,OAAA,CAAC1B,KAAK;gBACJyC,KAAK,EAAE+D,MAAO;gBACd8F,QAAQ,EAAG+B,CAAC,IAAK5H,SAAS,CAAC4H,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;gBAC3CgM,WAAW,EAAC,4BAAkB;gBAC9B7L,KAAK,EAAE;kBAAE8C,IAAI,EAAE;gBAAE;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFxB,OAAA,CAACxB,MAAM;gBACLuD,IAAI,EAAC,SAAS;gBACdiL,OAAO,EAAEvE,aAAc;gBACvBwE,OAAO,EAAE7H,eAAgB;gBACzB8H,QAAQ,EAAE,CAACpI,MAAO;gBAClB5D,KAAK,EAAE;kBAAEiM,UAAU,EAAE;gBAAE,CAAE;gBAAA/L,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAACnB,IAAI;cAACuO,QAAQ,EAAEhI,eAAgB;cAAAhE,QAAA,eAC9BpB,OAAA,CAACzB,MAAM;gBACLwC,KAAK,EAAEmE,eAAgB;gBACvB0F,QAAQ,EAAEzF,kBAAmB;gBAC7B4H,WAAW,EAAC,mCAAU;gBACtB7L,KAAK,EAAE;kBAAEL,KAAK,EAAE,MAAM;kBAAEoB,SAAS,EAAE;gBAAE,CAAE;gBACvCgL,OAAO,EAAE7H,eAAgB;gBAAAhE,QAAA,EAExB4D,iBAAiB,CAACqI,GAAG,CAAEC,IAAI,iBAC1BtN,OAAA,CAACM,MAAM;kBAAYS,KAAK,EAAEuM,IAAK;kBAAAlM,QAAA,EAC5BkM;gBAAI,GADMA,IAAI;kBAAAjM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC7B,IAAI;MAACuC,KAAK,EAAC,0BAAM;MAAC6L,SAAS,EAAC,eAAe;MAAAnL,QAAA,eAC1CpB,OAAA,CAACtB,KAAK;QAAC8N,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvL,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC1B,KAAK,CAACoO,KAAK;YAACG,OAAO;YAAC3L,KAAK,EAAE;cAAEe,SAAS,EAAE,CAAC;cAAE6K,OAAO,EAAE;YAAO,CAAE;YAAA1L,QAAA,gBAC5DpB,OAAA,CAAC1B,KAAK;cACJyC,KAAK,EAAEuE,QAAS;cAChBsF,QAAQ,EAAG+B,CAAC,IAAKpH,WAAW,CAACoH,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;cAC7CgM,WAAW,EAAC,4BAAkB;cAC9B7L,KAAK,EAAE;gBAAE8C,IAAI,EAAE;cAAE;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFxB,OAAA,CAACxB,MAAM;cACLuD,IAAI,EAAC,SAAS;cACdiL,OAAO,EAAE/D,eAAgB;cACzBgE,OAAO,EAAErH,aAAc;cACvBsH,QAAQ,EAAE,CAAC5H,QAAS;cACpBpE,KAAK,EAAE;gBAAEiM,UAAU,EAAE;cAAE,CAAE;cAAA/L,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC5B,KAAK,CAACsO,KAAK;YACV3L,KAAK,EAAE+E,cAAe;YACtB8E,QAAQ,EAAG+B,CAAC,IAAK5G,iBAAiB,CAAC4G,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;YACnDG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAC,UAAU;cAAAK,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELsE,cAAc,KAAK,QAAQ,gBAC1B9F,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAACnB,IAAI;YAACuO,QAAQ,EAAExH,aAAc;YAAAxE,QAAA,eAC5BpB,OAAA,CAACzB,MAAM;cACLwC,KAAK,EAAEiF,iBAAkB;cACzB4E,QAAQ,EAAER,qBAAsB;cAChC2C,WAAW,EAAC,kMAAkC;cAC9C7L,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvCgL,OAAO,EAAErH,aAAc;cAAAxE,QAAA,EAEtBoE,iBAAiB,CAAC6H,GAAG,CAAEC,IAAI,iBAC1BtN,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEuM,IAAK;gBAAAlM,QAAA,EAC5BkM;cAAI,GADMA,IAAI;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAENwE,iBAAiB,iBAChBhG,OAAA;YAAKkB,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAG,CAAE;YAAAb,QAAA,eAC5BpB,OAAA,CAACtB,KAAK;cAAC8N,SAAS,EAAC,UAAU;cAACtL,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cAAAO,QAAA,gBACnDpB,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAAAX,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxB,OAAA,CAACnB,IAAI;kBAACuO,QAAQ,EAAEhG,oBAAqB;kBAAAhG,QAAA,eACnCpB,OAAA;oBAAKkB,KAAK,EAAE;sBAAEe,SAAS,EAAE,CAAC;sBAAEsL,OAAO,EAAE,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAArM,QAAA,EACpFgG,oBAAoB,gBACnBpH,OAAA;sBAAAoB,QAAA,EAAG;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,gBAEpBxB,OAAA,CAAAE,SAAA;sBAAAkB,QAAA,gBACEpB,OAAA;wBAAAoB,QAAA,gBAAGpB,OAAA;0BAAAoB,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC0E,kBAAkB,IAAI,KAAK;sBAAA;wBAAA7E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3DxB,OAAA;wBAAAoB,QAAA,gBAAGpB,OAAA;0BAAAoB,QAAA,EAAQ;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAAC4E,kBAAkB,IAAI,KAAK;sBAAA;wBAAA/E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC5D,CAACkF,mBAAmB,IAAIJ,YAAY,IAAIE,gBAAgB,iBACvDxG,OAAA,CAAAE,SAAA;wBAAAkB,QAAA,gBACEpB,OAAA;0BAAAoB,QAAA,gBAAGpB,OAAA;4BAAAoB,QAAA,EAAQ;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC8E,YAAY;wBAAA;0BAAAjF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1CxB,OAAA;0BAAAoB,QAAA,gBAAGpB,OAAA;4BAAAoB,QAAA,EAAQ;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACgF,gBAAgB;wBAAA;0BAAAnF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eAChD,CACH;oBAAA,eACD;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAELkF,mBAAmB,iBAClB1G,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAACkC,MAAM;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCxB,OAAA;kBAAKkB,KAAK,EAAE;oBAAEe,SAAS,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAC3BpB,OAAA,CAACtB,KAAK;oBAAC8N,SAAS,EAAC,UAAU;oBAACtL,KAAK,EAAE;sBAAEL,KAAK,EAAE;oBAAO,CAAE;oBAAAO,QAAA,gBACnDpB,OAAA,CAACzB,MAAM;sBACLwC,KAAK,EAAEuF,YAAa;sBACpBsE,QAAQ,EAAErE,eAAgB;sBAC1BwG,WAAW,EAAC,8DAAY;sBACxB7L,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExB+C,eAAe,CAACkJ,GAAG,CAAEK,IAAI,iBACxB1N,OAAA,CAACM,MAAM;wBAAYS,KAAK,EAAE2M,IAAK;wBAAAtM,QAAA,EAC5BsM;sBAAI,GADMA,IAAI;wBAAArM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EAER8E,YAAY,iBACXtG,OAAA,CAACzB,MAAM;sBACLwC,KAAK,EAAEyF,gBAAiB;sBACxBoE,QAAQ,EAAEnE,mBAAoB;sBAC9BsG,WAAW,EAAE,YAAYzG,YAAY,OAAQ;sBAC7CpF,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExB,CAACgD,eAAe,CAACkC,YAAY,CAAiC,IAAI,EAAE,EAAE+G,GAAG,CAAExD,QAAQ,iBAClF7J,OAAA,CAACM,MAAM;wBAAgBS,KAAK,EAAE8I,QAAS;wBAAAzI,QAAA,EACpCyI;sBAAQ,GADEA,QAAQ;wBAAAxI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEb,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAACnB,IAAI;YAACuO,QAAQ,EAAExH,aAAc;YAAAxE,QAAA,eAC5BpB,OAAA,CAACzB,MAAM;cACLoP,IAAI,EAAC,UAAU;cACf5M,KAAK,EAAE2E,cAAe;cACtBkF,QAAQ,EAAEjF,iBAAkB;cAC5BoH,WAAW,EAAC,4FAAiB;cAC7B7L,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvCgL,OAAO,EAAErH,aAAc;cAAAxE,QAAA,EAEtBoE,iBAAiB,CAAC6H,GAAG,CAAEC,IAAI,iBAC1BtN,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEuM,IAAK;gBAAAlM,QAAA,EAC5BkM;cAAI,GADMA,IAAI;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAgE,iBAAiB,CAACnD,MAAM,KAAK,CAAC,IAAI,CAACuD,aAAa,iBAC/C5F,OAAA,CAAClB,KAAK;UACJF,OAAO,EAAC,4CAAS;UACjBgP,WAAW,EAAC,oOAA2C;UACvD7L,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC7B,IAAI;MAACoO,SAAS,EAAC,eAAe;MAAC7L,KAAK,EAAC,0BAAM;MAAAU,QAAA,eAC1CpB,OAAA,CAACtB,KAAK;QAAC8N,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,QAAQ;QAACvL,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBACjEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAAC5B,KAAK,CAACsO,KAAK;YACV3L,KAAK,EAAE0G,kBAAmB;YAC1BmD,QAAQ,EAAG+B,CAAC,IAAKjF,qBAAqB,CAACiF,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;YACvDG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YACxBiL,QAAQ,EAAEpH,cAAc,KAAK,UAAW,CAAC;YAAA;YAAA1E,QAAA,gBAEzCpB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAE,IAAK;cAAAK,QAAA,eACjBpB,OAAA,CAACtB,KAAK;gBAAA0C,QAAA,GAAC,kDAEL,eAAApB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG,CAAE;kBAAAf,QAAA,EAAC;gBAEhD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRxB,OAAA,CAAC5B,KAAK;cAAC2C,KAAK,EAAE,KAAM;cAAAK,QAAA,eAClBpB,OAAA,CAACtB,KAAK;gBAAA0C,QAAA,GAAC,0BAEL,eAAApB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG,CAAE;kBAAAf,QAAA,EAAC;gBAEhD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELiG,kBAAkB,IAAI3B,cAAc,KAAK,QAAQ,iBAChD9F,OAAA,CAAClB,KAAK;UACJF,OAAO,EAAC,sCAAQ;UAChBgP,WAAW,eACT5N,OAAA;YAAAoB,QAAA,GAAK,8JAEH,eAAApB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAxB,OAAA;cAAAoB,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDO,IAAI,EAAC,MAAM;UACXC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EAEAsE,cAAc,KAAK,UAAU,iBAC5B9F,OAAA,CAAClB,KAAK;UACJF,OAAO,EAAC,4CAAS;UACjBgP,WAAW,EAAC,0KAA8B;UAC1C7L,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC7B,IAAI;MAACoO,SAAS,EAAC,eAAe;MAAAnL,QAAA,gBAC7BpB,OAAA,CAACxB,MAAM;QACLuD,IAAI,EAAC,SAAS;QACd0K,IAAI,EAAC,OAAO;QACZoB,IAAI,eAAE7N,OAAA,CAACZ,kBAAkB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BwL,OAAO,EAAEjC,qBAAsB;QAC/BkC,OAAO,EAAErG,UAAW;QACpBsG,QAAQ,EAAE,CAACd,WAAW,CAAC,CAAE;QACzBG,SAAS,EAAC,eAAe;QAAAnL,QAAA,EAExBwF,UAAU,GAAG,SAAS,GAAG;MAAS;QAAAvF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EAGRoF,UAAU,iBACT5G,OAAA;QAAKuM,SAAS,EAAC,kBAAkB;QAAAnL,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;UAAAgB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBxB,OAAA,CAACd,QAAQ;UAAC4O,OAAO,EAAEhH,QAAS;UAACiH,MAAM,EAAC;QAAQ;UAAA1M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNwF,OAAO,CAAC3E,MAAM,GAAG,CAAC,iBACjBrC,OAAA,CAAC7B,IAAI;MAACuC,KAAK,EAAC,0BAAM;MAAC6L,SAAS,EAAC,eAAe;MAAAnL,QAAA,EACzC4F,OAAO,CAAC3E,MAAM,GAAG,CAAC;MAAA;MACjB;MACArC,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACrB,OAAO;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXxB,OAAA,CAACG,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAA3C,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxB,OAAA;UAAKkB,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAACzB,MAAM;YACL2C,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAEoB,SAAS,EAAE;YAAE,CAAE;YACvC8K,WAAW,EAAC,sCAAQ;YACpBhM,KAAK,EAAEmG,mBAAoB;YAC3B0D,QAAQ,EAAG7J,KAAK,IAAKoG,sBAAsB,CAACpG,KAAK,CAAE;YAAAK,QAAA,EAElD4F,OAAO,CAACqG,GAAG,CAAC,CAAC7M,MAAM,EAAEwN,KAAK,kBACzBhO,OAAA,CAACM,MAAM;cAAaS,KAAK,EAAEiN,KAAM;cAAA5M,QAAA,EAC9BZ,MAAM,CAAC+H;YAAU,GADPyF,KAAK;cAAA3M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLwF,OAAO,CAACE,mBAAmB,CAAC,iBAC3BlH,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;YAAC4D,KAAK,EAAE,CAAE;YAAA3C,QAAA,GAAC,gBAAI,EAAC4F,OAAO,CAACE,mBAAmB,CAAC,CAACqB,UAAU;UAAA;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtExB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEwG,OAAO,CAACE,mBAAmB;UAAE;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAxB,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAA3C,QAAA,GAAC,6BAAO,EAAC4F,OAAO,CAAC,CAAC,CAAC,CAACuB,UAAU;QAAA;UAAAlH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvDxB,OAAA,CAACO,uBAAuB;UAACC,MAAM,EAAEwG,OAAO,CAAC,CAAC;QAAE;UAAA3F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACP,EAGAuG,wBAAwB,CAAC1F,MAAM,GAAG,CAAC,iBAClCrC,OAAA,CAAC7B,IAAI;MAACuC,KAAK,EAAC,sCAAQ;MAAC6L,SAAS,EAAC,eAAe;MAACrL,KAAK,EAAE;QAAEe,SAAS,EAAE;MAAG,CAAE;MAAAb,QAAA,gBACtEpB,OAAA,CAAClB,KAAK;QACJF,OAAO,EAAC,4CAAS;QACjBgP,WAAW,EAAC,sLAAgC;QAC5C7L,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRd,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAG;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFxB,OAAA,CAACtB,KAAK;QAAC8N,SAAS,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAACvL,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAEhEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAACzB,MAAM;YACLwC,KAAK,EAAE8G,mBAAoB;YAC3B+C,QAAQ,EAAE5C,qBAAsB;YAChC9G,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAEoB,SAAS,EAAE;YAAE,CAAE;YACvC8K,WAAW,EAAC,oEAAa;YAAA3L,QAAA,EAExB2G,wBAAwB,CAACsF,GAAG,CAAEjF,IAAI,iBACjCpI,OAAA,CAACM,MAAM;cAAoBS,KAAK,EAAEqH,IAAI,CAACC,OAAQ;cAAAjH,QAAA,EAC5CgH,IAAI,CAACC,OAAO,CAAC4F,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAG7F,IAAI,CAACC,OAAO,CAAC6F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIC,IAAI,CAAC/F,IAAI,CAACgG,UAAU,IAAIhG,IAAI,CAACiG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAMlG,IAAI,CAACC,OAAO,CAACkG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAIJ,IAAI,CAAC/F,IAAI,CAACgG,UAAU,IAAIhG,IAAI,CAACiG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAAG,GAHjGlG,IAAI,CAACC,OAAO;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLmG,sBAAsB,CAACtF,MAAM,GAAG,CAAC,iBAChCrC,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;YAAC4D,KAAK,EAAE,CAAE;YAAA3C,QAAA,GAAC,6BAAO,EAACuG,sBAAsB,CAAC,CAAC,CAAC,CAACY,UAAU;UAAA;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtExB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEmH,sBAAsB,CAAC,CAAC;UAAE;YAAAtG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiD,EAAA,CAxyBID,mBAA6B;EAAA,QA+B8C1E,cAAc;AAAA;AAAA0O,GAAA,GA/BzFhK,mBAA6B;AA0yBnC,eAAeA,mBAAmB;AAAC,IAAAN,EAAA,EAAAsK,GAAA;AAAAC,YAAA,CAAAvK,EAAA;AAAAuK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}