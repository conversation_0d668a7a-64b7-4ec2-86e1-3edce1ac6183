{"ast": null, "code": "import Link from './Link';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nvar Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;", "map": {"version": 3, "names": ["Link", "Paragraph", "Text", "Title", "OriginTypography", "Typography"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/index.js"], "sourcesContent": ["import Link from './Link';\nimport Paragraph from './Paragraph';\nimport Text from './Text';\nimport Title from './Title';\nimport OriginTypography from './Typography';\nvar Typography = OriginTypography;\nTypography.Text = Text;\nTypography.Link = Link;\nTypography.Title = Title;\nTypography.Paragraph = Paragraph;\nexport default Typography;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,QAAQ;AACzB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,gBAAgB,MAAM,cAAc;AAC3C,IAAIC,UAAU,GAAGD,gBAAgB;AACjCC,UAAU,CAACH,IAAI,GAAGA,IAAI;AACtBG,UAAU,CAACL,IAAI,GAAGA,IAAI;AACtBK,UAAU,CAACF,KAAK,GAAGA,KAAK;AACxBE,UAAU,CAACJ,SAAS,GAAGA,SAAS;AAChC,eAAeI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}