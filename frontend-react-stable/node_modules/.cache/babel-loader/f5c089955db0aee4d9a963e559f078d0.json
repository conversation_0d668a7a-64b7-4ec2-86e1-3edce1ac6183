{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.durationYear = exports.durationWeek = exports.durationSecond = exports.durationMonth = exports.durationMinute = exports.durationHour = exports.durationDay = void 0;\nconst durationSecond = 1000;\nexports.durationSecond = durationSecond;\nconst durationMinute = durationSecond * 60;\nexports.durationMinute = durationMinute;\nconst durationHour = durationMinute * 60;\nexports.durationHour = durationHour;\nconst durationDay = durationHour * 24;\nexports.durationDay = durationDay;\nconst durationWeek = durationDay * 7;\nexports.durationWeek = durationWeek;\nconst durationMonth = durationDay * 30;\nexports.durationMonth = durationMonth;\nconst durationYear = durationDay * 365;\nexports.durationYear = durationYear;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "durationYear", "durationWeek", "durationSecond", "durationMonth", "durationMinute", "durationHour", "durationDay"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/duration.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.durationYear = exports.durationWeek = exports.durationSecond = exports.durationMonth = exports.durationMinute = exports.durationHour = exports.durationDay = void 0;\nconst durationSecond = 1000;\nexports.durationSecond = durationSecond;\nconst durationMinute = durationSecond * 60;\nexports.durationMinute = durationMinute;\nconst durationHour = durationMinute * 60;\nexports.durationHour = durationHour;\nconst durationDay = durationHour * 24;\nexports.durationDay = durationDay;\nconst durationWeek = durationDay * 7;\nexports.durationWeek = durationWeek;\nconst durationMonth = durationDay * 30;\nexports.durationMonth = durationMonth;\nconst durationYear = durationDay * 365;\nexports.durationYear = durationYear;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAACK,aAAa,GAAGL,OAAO,CAACM,cAAc,GAAGN,OAAO,CAACO,YAAY,GAAGP,OAAO,CAACQ,WAAW,GAAG,KAAK,CAAC;AAC3K,MAAMJ,cAAc,GAAG,IAAI;AAC3BJ,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAME,cAAc,GAAGF,cAAc,GAAG,EAAE;AAC1CJ,OAAO,CAACM,cAAc,GAAGA,cAAc;AACvC,MAAMC,YAAY,GAAGD,cAAc,GAAG,EAAE;AACxCN,OAAO,CAACO,YAAY,GAAGA,YAAY;AACnC,MAAMC,WAAW,GAAGD,YAAY,GAAG,EAAE;AACrCP,OAAO,CAACQ,WAAW,GAAGA,WAAW;AACjC,MAAML,YAAY,GAAGK,WAAW,GAAG,CAAC;AACpCR,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,MAAME,aAAa,GAAGG,WAAW,GAAG,EAAE;AACtCR,OAAO,CAACK,aAAa,GAAGA,aAAa;AACrC,MAAMH,YAAY,GAAGM,WAAW,GAAG,GAAG;AACtCR,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}