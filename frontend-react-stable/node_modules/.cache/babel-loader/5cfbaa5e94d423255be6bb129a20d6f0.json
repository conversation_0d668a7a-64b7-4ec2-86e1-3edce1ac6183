{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from '../../_util/reactNode';\nexport default function renderSwitcherIcon(prefixCls, switcherIcon, showLine, treeNodeProps) {\n  var isLeaf = treeNodeProps.isLeaf,\n    expanded = treeNodeProps.expanded,\n    loading = treeNodeProps.loading;\n  if (loading) {\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-loading-icon\")\n    });\n  }\n  var showLeafIcon;\n  if (showLine && _typeof(showLine) === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (!showLine) {\n      return null;\n    }\n    if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {\n      var leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;\n      var leafCls = \"\".concat(prefixCls, \"-switcher-line-custom-icon\");\n      if (isValidElement(leafIcon)) {\n        return cloneElement(leafIcon, {\n          className: classNames(leafIcon.props.className || '', leafCls)\n        });\n      }\n      return leafIcon;\n    }\n    return showLeafIcon ? /*#__PURE__*/React.createElement(FileOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-switcher-leaf-line\")\n    });\n  }\n  var switcherCls = \"\".concat(prefixCls, \"-switcher-icon\");\n  var switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;\n  if (isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? /*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    });\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n}", "map": {"version": 3, "names": ["_typeof", "CaretDownFilled", "FileOutlined", "LoadingOutlined", "MinusSquareOutlined", "PlusSquareOutlined", "classNames", "React", "cloneElement", "isValidElement", "renderSwitcherIcon", "prefixCls", "switcherIcon", "showLine", "treeNodeProps", "<PERSON><PERSON><PERSON><PERSON>", "expanded", "loading", "createElement", "className", "concat", "showLeafIcon", "leafIcon", "leafCls", "props", "switcherCls", "switcher"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tree/utils/iconUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { cloneElement, isValidElement } from '../../_util/reactNode';\nexport default function renderSwitcherIcon(prefixCls, switcherIcon, showLine, treeNodeProps) {\n  var isLeaf = treeNodeProps.isLeaf,\n    expanded = treeNodeProps.expanded,\n    loading = treeNodeProps.loading;\n  if (loading) {\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-loading-icon\")\n    });\n  }\n  var showLeafIcon;\n  if (showLine && _typeof(showLine) === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (!showLine) {\n      return null;\n    }\n    if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {\n      var leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;\n      var leafCls = \"\".concat(prefixCls, \"-switcher-line-custom-icon\");\n      if (isValidElement(leafIcon)) {\n        return cloneElement(leafIcon, {\n          className: classNames(leafIcon.props.className || '', leafCls)\n        });\n      }\n      return leafIcon;\n    }\n    return showLeafIcon ? /*#__PURE__*/React.createElement(FileOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-switcher-leaf-line\")\n    });\n  }\n  var switcherCls = \"\".concat(prefixCls, \"-switcher-icon\");\n  var switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;\n  if (isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? /*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    }) : /*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: \"\".concat(prefixCls, \"-switcher-line-icon\")\n    });\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,mBAAmB,MAAM,gDAAgD;AAChF,OAAOC,kBAAkB,MAAM,+CAA+C;AAC9E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,cAAc,QAAQ,uBAAuB;AACpE,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EAC3F,IAAIC,MAAM,GAAGD,aAAa,CAACC,MAAM;IAC/BC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;IACjCC,OAAO,GAAGH,aAAa,CAACG,OAAO;EACjC,IAAIA,OAAO,EAAE;IACX,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACf,eAAe,EAAE;MACvDgB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,wBAAwB;IAC1D,CAAC,CAAC;EACJ;EACA,IAAIU,YAAY;EAChB,IAAIR,QAAQ,IAAIb,OAAO,CAACa,QAAQ,CAAC,KAAK,QAAQ,EAAE;IAC9CQ,YAAY,GAAGR,QAAQ,CAACQ,YAAY;EACtC;EACA,IAAIN,MAAM,EAAE;IACV,IAAI,CAACF,QAAQ,EAAE;MACb,OAAO,IAAI;IACb;IACA,IAAI,OAAOQ,YAAY,KAAK,SAAS,IAAI,CAAC,CAACA,YAAY,EAAE;MACvD,IAAIC,QAAQ,GAAG,OAAOD,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACP,aAAa,CAAC,GAAGO,YAAY;MAC9F,IAAIE,OAAO,GAAG,EAAE,CAACH,MAAM,CAACT,SAAS,EAAE,4BAA4B,CAAC;MAChE,IAAIF,cAAc,CAACa,QAAQ,CAAC,EAAE;QAC5B,OAAOd,YAAY,CAACc,QAAQ,EAAE;UAC5BH,SAAS,EAAEb,UAAU,CAACgB,QAAQ,CAACE,KAAK,CAACL,SAAS,IAAI,EAAE,EAAEI,OAAO;QAC/D,CAAC,CAAC;MACJ;MACA,OAAOD,QAAQ;IACjB;IACA,OAAOD,YAAY,GAAG,aAAad,KAAK,CAACW,aAAa,CAAChB,YAAY,EAAE;MACnEiB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC,GAAG,aAAaJ,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;MAC5CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC;EACJ;EACA,IAAIc,WAAW,GAAG,EAAE,CAACL,MAAM,CAACT,SAAS,EAAE,gBAAgB,CAAC;EACxD,IAAIe,QAAQ,GAAG,OAAOd,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACE,aAAa,CAAC,GAAGF,YAAY;EAC9F,IAAIH,cAAc,CAACiB,QAAQ,CAAC,EAAE;IAC5B,OAAOlB,YAAY,CAACkB,QAAQ,EAAE;MAC5BP,SAAS,EAAEb,UAAU,CAACoB,QAAQ,CAACF,KAAK,CAACL,SAAS,IAAI,EAAE,EAAEM,WAAW;IACnE,CAAC,CAAC;EACJ;EACA,IAAIC,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EACA,IAAIb,QAAQ,EAAE;IACZ,OAAOG,QAAQ,GAAG,aAAaT,KAAK,CAACW,aAAa,CAACd,mBAAmB,EAAE;MACtEe,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC,GAAG,aAAaJ,KAAK,CAACW,aAAa,CAACb,kBAAkB,EAAE;MACxDc,SAAS,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,qBAAqB;IACvD,CAAC,CAAC;EACJ;EACA,OAAO,aAAaJ,KAAK,CAACW,aAAa,CAACjB,eAAe,EAAE;IACvDkB,SAAS,EAAEM;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}