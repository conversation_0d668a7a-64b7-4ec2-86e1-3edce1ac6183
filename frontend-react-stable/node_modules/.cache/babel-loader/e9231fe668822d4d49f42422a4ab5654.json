{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\nexports.loggish = loggish;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _index2 = require(\"../../../lib-vendor/d3-format/src/index.js\");\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction transformLog(x) {\n  return Math.log(x);\n}\nfunction transformExp(x) {\n  return Math.exp(x);\n}\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\nfunction powp(base) {\n  return base === 10 ? pow10 : base === Math.E ? Math.exp : x => Math.pow(base, x);\n}\nfunction logp(base) {\n  return base === Math.E ? Math.log : base === 10 && Math.log10 || base === 2 && Math.log2 || (base = Math.log(base), x => Math.log(x) / base);\n}\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\nfunction loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n  scale.base = function (_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n    if (r) [u, v] = [v, u];\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = (0, _index.ticks)(u, v, n);\n    } else {\n      z = (0, _index.ticks)(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = (0, _index2.formatSpecifier)(specifier)).precision == null) specifier.trim = true;\n      specifier = (0, _index2.format)(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n  scale.nice = () => {\n    return domain((0, _nice.default)(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n  return scale;\n}\nfunction log() {\n  const scale = loggish((0, _continuous.transformer)()).domain([1, 10]);\n  scale.copy = () => (0, _continuous.copy)(scale, log()).base(scale.base());\n  _init.initRange.apply(scale, arguments);\n  return scale;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "log", "loggish", "_index", "require", "_index2", "_nice", "_interopRequireDefault", "_continuous", "_init", "obj", "__esModule", "transformLog", "x", "Math", "transformExp", "exp", "transformLogn", "transformExpn", "pow10", "isFinite", "powp", "base", "E", "pow", "logp", "log10", "log2", "reflect", "f", "k", "transform", "scale", "domain", "logs", "pows", "rescale", "_", "arguments", "length", "ticks", "count", "d", "u", "v", "r", "i", "j", "t", "n", "z", "floor", "ceil", "push", "min", "map", "reverse", "tickFormat", "specifier", "formatSpecifier", "precision", "trim", "format", "Infinity", "max", "round", "nice", "transformer", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/log.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\nexports.loggish = loggish;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _index2 = require(\"../../../lib-vendor/d3-format/src/index.js\");\n\nvar _nice = _interopRequireDefault(require(\"./nice.js\"));\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10 : base === Math.E ? Math.exp : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log : base === 10 && Math.log10 || base === 2 && Math.log2 || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nfunction loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n\n    return scale;\n  }\n\n  scale.base = function (_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function (_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n    if (r) [u, v] = [v, u];\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = (0, _index.ticks)(u, v, n);\n    } else {\n      z = (0, _index.ticks)(i, j, Math.min(j - i, n)).map(pows);\n    }\n\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = (0, _index2.formatSpecifier)(specifier)).precision == null) specifier.trim = true;\n      specifier = (0, _index2.format)(specifier);\n    }\n\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain((0, _nice.default)(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nfunction log() {\n  const scale = loggish((0, _continuous.transformer)()).domain([1, 10]);\n\n  scale.copy = () => (0, _continuous.copy)(scale, log()).base(scale.base());\n\n  _init.initRange.apply(scale, arguments);\n\n  return scale;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AACrBH,OAAO,CAACI,OAAO,GAAGA,OAAO;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,OAAO,GAAGD,OAAO,CAAC,4CAA4C,CAAC;AAEnE,IAAIE,KAAK,GAAGC,sBAAsB,CAACH,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,IAAII,WAAW,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIK,KAAK,GAAGL,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASG,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;AAE9F,SAASE,YAAYA,CAACC,CAAC,EAAE;EACvB,OAAOC,IAAI,CAACb,GAAG,CAACY,CAAC,CAAC;AACpB;AAEA,SAASE,YAAYA,CAACF,CAAC,EAAE;EACvB,OAAOC,IAAI,CAACE,GAAG,CAACH,CAAC,CAAC;AACpB;AAEA,SAASI,aAAaA,CAACJ,CAAC,EAAE;EACxB,OAAO,CAACC,IAAI,CAACb,GAAG,CAAC,CAACY,CAAC,CAAC;AACtB;AAEA,SAASK,aAAaA,CAACL,CAAC,EAAE;EACxB,OAAO,CAACC,IAAI,CAACE,GAAG,CAAC,CAACH,CAAC,CAAC;AACtB;AAEA,SAASM,KAAKA,CAACN,CAAC,EAAE;EAChB,OAAOO,QAAQ,CAACP,CAAC,CAAC,GAAG,EAAE,IAAI,GAAGA,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC;AAClD;AAEA,SAASQ,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAOA,IAAI,KAAK,EAAE,GAAGH,KAAK,GAAGG,IAAI,KAAKR,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACE,GAAG,GAAGH,CAAC,IAAIC,IAAI,CAACU,GAAG,CAACF,IAAI,EAAET,CAAC,CAAC;AAClF;AAEA,SAASY,IAAIA,CAACH,IAAI,EAAE;EAClB,OAAOA,IAAI,KAAKR,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACb,GAAG,GAAGqB,IAAI,KAAK,EAAE,IAAIR,IAAI,CAACY,KAAK,IAAIJ,IAAI,KAAK,CAAC,IAAIR,IAAI,CAACa,IAAI,KAAKL,IAAI,GAAGR,IAAI,CAACb,GAAG,CAACqB,IAAI,CAAC,EAAET,CAAC,IAAIC,IAAI,CAACb,GAAG,CAACY,CAAC,CAAC,GAAGS,IAAI,CAAC;AAC9I;AAEA,SAASM,OAAOA,CAACC,CAAC,EAAE;EAClB,OAAO,CAAChB,CAAC,EAAEiB,CAAC,KAAK,CAACD,CAAC,CAAC,CAAChB,CAAC,EAAEiB,CAAC,CAAC;AAC5B;AAEA,SAAS5B,OAAOA,CAAC6B,SAAS,EAAE;EAC1B,MAAMC,KAAK,GAAGD,SAAS,CAACnB,YAAY,EAAEG,YAAY,CAAC;EACnD,MAAMkB,MAAM,GAAGD,KAAK,CAACC,MAAM;EAC3B,IAAIX,IAAI,GAAG,EAAE;EACb,IAAIY,IAAI;EACR,IAAIC,IAAI;EAER,SAASC,OAAOA,CAAA,EAAG;IACjBF,IAAI,GAAGT,IAAI,CAACH,IAAI,CAAC,EAAEa,IAAI,GAAGd,IAAI,CAACC,IAAI,CAAC;IAEpC,IAAIW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACnBC,IAAI,GAAGN,OAAO,CAACM,IAAI,CAAC,EAAEC,IAAI,GAAGP,OAAO,CAACO,IAAI,CAAC;MAC1CJ,SAAS,CAACd,aAAa,EAAEC,aAAa,CAAC;IACzC,CAAC,MAAM;MACLa,SAAS,CAACnB,YAAY,EAAEG,YAAY,CAAC;IACvC;IAEA,OAAOiB,KAAK;EACd;EAEAA,KAAK,CAACV,IAAI,GAAG,UAAUe,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIjB,IAAI,GAAG,CAACe,CAAC,EAAED,OAAO,CAAC,CAAC,IAAId,IAAI;EACzD,CAAC;EAEDU,KAAK,CAACC,MAAM,GAAG,UAAUI,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIN,MAAM,CAACI,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,IAAIH,MAAM,CAAC,CAAC;EAC7D,CAAC;EAEDD,KAAK,CAACQ,KAAK,GAAGC,KAAK,IAAI;IACrB,MAAMC,CAAC,GAAGT,MAAM,CAAC,CAAC;IAClB,IAAIU,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIE,CAAC,GAAGF,CAAC,CAACA,CAAC,CAACH,MAAM,GAAG,CAAC,CAAC;IACvB,MAAMM,CAAC,GAAGD,CAAC,GAAGD,CAAC;IACf,IAAIE,CAAC,EAAE,CAACF,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACA,CAAC,EAAED,CAAC,CAAC;IACtB,IAAIG,CAAC,GAAGZ,IAAI,CAACS,CAAC,CAAC;IACf,IAAII,CAAC,GAAGb,IAAI,CAACU,CAAC,CAAC;IACf,IAAId,CAAC;IACL,IAAIkB,CAAC;IACL,MAAMC,CAAC,GAAGR,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,KAAK;IACrC,IAAIS,CAAC,GAAG,EAAE;IAEV,IAAI,EAAE5B,IAAI,GAAG,CAAC,CAAC,IAAIyB,CAAC,GAAGD,CAAC,GAAGG,CAAC,EAAE;MAC5BH,CAAC,GAAGhC,IAAI,CAACqC,KAAK,CAACL,CAAC,CAAC,EAAEC,CAAC,GAAGjC,IAAI,CAACsC,IAAI,CAACL,CAAC,CAAC;MACnC,IAAIJ,CAAC,GAAG,CAAC,EAAE,OAAOG,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC7B,KAAKhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,EAAE,EAAEQ,CAAC,EAAE;UACzBkB,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAGhB,CAAC,GAAGK,IAAI,CAAC,CAACW,CAAC,CAAC,GAAGhB,CAAC,GAAGK,IAAI,CAACW,CAAC,CAAC;UACtC,IAAIE,CAAC,GAAGL,CAAC,EAAE;UACX,IAAIK,CAAC,GAAGJ,CAAC,EAAE;UACXM,CAAC,CAACG,IAAI,CAACL,CAAC,CAAC;QACX;MACF,CAAC,MAAM,OAAOF,CAAC,IAAIC,CAAC,EAAE,EAAED,CAAC,EAAE;QACzB,KAAKhB,CAAC,GAAGR,IAAI,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAC9BkB,CAAC,GAAGF,CAAC,GAAG,CAAC,GAAGhB,CAAC,GAAGK,IAAI,CAAC,CAACW,CAAC,CAAC,GAAGhB,CAAC,GAAGK,IAAI,CAACW,CAAC,CAAC;UACtC,IAAIE,CAAC,GAAGL,CAAC,EAAE;UACX,IAAIK,CAAC,GAAGJ,CAAC,EAAE;UACXM,CAAC,CAACG,IAAI,CAACL,CAAC,CAAC;QACX;MACF;MACA,IAAIE,CAAC,CAACX,MAAM,GAAG,CAAC,GAAGU,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE/C,MAAM,CAACqC,KAAK,EAAEG,CAAC,EAAEC,CAAC,EAAEK,CAAC,CAAC;IACtD,CAAC,MAAM;MACLC,CAAC,GAAG,CAAC,CAAC,EAAE/C,MAAM,CAACqC,KAAK,EAAEM,CAAC,EAAEC,CAAC,EAAEjC,IAAI,CAACwC,GAAG,CAACP,CAAC,GAAGD,CAAC,EAAEG,CAAC,CAAC,CAAC,CAACM,GAAG,CAACpB,IAAI,CAAC;IAC3D;IAEA,OAAOU,CAAC,GAAGK,CAAC,CAACM,OAAO,CAAC,CAAC,GAAGN,CAAC;EAC5B,CAAC;EAEDlB,KAAK,CAACyB,UAAU,GAAG,CAAChB,KAAK,EAAEiB,SAAS,KAAK;IACvC,IAAIjB,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;IAC7B,IAAIiB,SAAS,IAAI,IAAI,EAAEA,SAAS,GAAGpC,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG;IAE1D,IAAI,OAAOoC,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,EAAEpC,IAAI,GAAG,CAAC,CAAC,IAAI,CAACoC,SAAS,GAAG,CAAC,CAAC,EAAErD,OAAO,CAACsD,eAAe,EAAED,SAAS,CAAC,EAAEE,SAAS,IAAI,IAAI,EAAEF,SAAS,CAACG,IAAI,GAAG,IAAI;MACjHH,SAAS,GAAG,CAAC,CAAC,EAAErD,OAAO,CAACyD,MAAM,EAAEJ,SAAS,CAAC;IAC5C;IAEA,IAAIjB,KAAK,KAAKsB,QAAQ,EAAE,OAAOL,SAAS;IACxC,MAAM5B,CAAC,GAAGhB,IAAI,CAACkD,GAAG,CAAC,CAAC,EAAE1C,IAAI,GAAGmB,KAAK,GAAGT,KAAK,CAACQ,KAAK,CAAC,CAAC,CAACD,MAAM,CAAC,CAAC,CAAC;;IAE5D,OAAOG,CAAC,IAAI;MACV,IAAII,CAAC,GAAGJ,CAAC,GAAGP,IAAI,CAACrB,IAAI,CAACmD,KAAK,CAAC/B,IAAI,CAACQ,CAAC,CAAC,CAAC,CAAC;MACrC,IAAII,CAAC,GAAGxB,IAAI,GAAGA,IAAI,GAAG,GAAG,EAAEwB,CAAC,IAAIxB,IAAI;MACpC,OAAOwB,CAAC,IAAIhB,CAAC,GAAG4B,SAAS,CAAChB,CAAC,CAAC,GAAG,EAAE;IACnC,CAAC;EACH,CAAC;EAEDV,KAAK,CAACkC,IAAI,GAAG,MAAM;IACjB,OAAOjC,MAAM,CAAC,CAAC,CAAC,EAAE3B,KAAK,CAACN,OAAO,EAAEiC,MAAM,CAAC,CAAC,EAAE;MACzCkB,KAAK,EAAEtC,CAAC,IAAIsB,IAAI,CAACrB,IAAI,CAACqC,KAAK,CAACjB,IAAI,CAACrB,CAAC,CAAC,CAAC,CAAC;MACrCuC,IAAI,EAAEvC,CAAC,IAAIsB,IAAI,CAACrB,IAAI,CAACsC,IAAI,CAAClB,IAAI,CAACrB,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOmB,KAAK;AACd;AAEA,SAAS/B,GAAGA,CAAA,EAAG;EACb,MAAM+B,KAAK,GAAG9B,OAAO,CAAC,CAAC,CAAC,EAAEM,WAAW,CAAC2D,WAAW,EAAE,CAAC,CAAC,CAAClC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAErED,KAAK,CAACoC,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE5D,WAAW,CAAC4D,IAAI,EAAEpC,KAAK,EAAE/B,GAAG,CAAC,CAAC,CAAC,CAACqB,IAAI,CAACU,KAAK,CAACV,IAAI,CAAC,CAAC,CAAC;EAEzEb,KAAK,CAAC4D,SAAS,CAACC,KAAK,CAACtC,KAAK,EAAEM,SAAS,CAAC;EAEvC,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}