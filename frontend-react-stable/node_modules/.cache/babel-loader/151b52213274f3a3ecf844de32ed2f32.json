{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = nogamma;\nexports.gamma = gamma;\nexports.hue = hue;\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction linear(a, d) {\n  return function (t) {\n    return a + t * d;\n  };\n}\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function (t) {\n    return Math.pow(a + t * b, y);\n  };\n}\nfunction hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : (0, _constant.default)(isNaN(a) ? b : a);\n}\nfunction gamma(y) {\n  return (y = +y) === 1 ? nogamma : function (a, b) {\n    return b - a ? exponential(a, b, y) : (0, _constant.default)(isNaN(a) ? b : a);\n  };\n}\nfunction nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : (0, _constant.default)(isNaN(a) ? b : a);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "nogamma", "gamma", "hue", "_constant", "_interopRequireDefault", "require", "obj", "__esModule", "linear", "a", "d", "t", "exponential", "b", "y", "Math", "pow", "round", "isNaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/color.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = nogamma;\nexports.gamma = gamma;\nexports.hue = hue;\n\nvar _constant = _interopRequireDefault(require(\"./constant.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction linear(a, d) {\n  return function (t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function (t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nfunction hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : (0, _constant.default)(isNaN(a) ? b : a);\n}\n\nfunction gamma(y) {\n  return (y = +y) === 1 ? nogamma : function (a, b) {\n    return b - a ? exponential(a, b, y) : (0, _constant.default)(isNaN(a) ? b : a);\n  };\n}\n\nfunction nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : (0, _constant.default)(isNaN(a) ? b : a);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,OAAO;AACzBH,OAAO,CAACI,KAAK,GAAGA,KAAK;AACrBJ,OAAO,CAACK,GAAG,GAAGA,GAAG;AAEjB,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEP,OAAO,EAAEO;EAAI,CAAC;AAAE;AAE9F,SAASE,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpB,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOF,CAAC,GAAGE,CAAC,GAAGD,CAAC;EAClB,CAAC;AACH;AAEA,SAASE,WAAWA,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOL,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEK,CAAC,CAAC,EAAED,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,GAAGL,CAAC,EAAEK,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,UAAUH,CAAC,EAAE;IACzE,OAAOI,IAAI,CAACC,GAAG,CAACP,CAAC,GAAGE,CAAC,GAAGE,CAAC,EAAEC,CAAC,CAAC;EAC/B,CAAC;AACH;AAEA,SAASZ,GAAGA,CAACO,CAAC,EAAEI,CAAC,EAAE;EACjB,IAAIH,CAAC,GAAGG,CAAC,GAAGJ,CAAC;EACb,OAAOC,CAAC,GAAGF,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,CAAC,GAAG,GAAGA,CAAC,GAAG,GAAG,GAAGK,IAAI,CAACE,KAAK,CAACP,CAAC,GAAG,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEP,SAAS,CAACJ,OAAO,EAAEmB,KAAK,CAACT,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;AAC1H;AAEA,SAASR,KAAKA,CAACa,CAAC,EAAE;EAChB,OAAO,CAACA,CAAC,GAAG,CAACA,CAAC,MAAM,CAAC,GAAGd,OAAO,GAAG,UAAUS,CAAC,EAAEI,CAAC,EAAE;IAChD,OAAOA,CAAC,GAAGJ,CAAC,GAAGG,WAAW,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEX,SAAS,CAACJ,OAAO,EAAEmB,KAAK,CAACT,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;EAChF,CAAC;AACH;AAEA,SAAST,OAAOA,CAACS,CAAC,EAAEI,CAAC,EAAE;EACrB,IAAIH,CAAC,GAAGG,CAAC,GAAGJ,CAAC;EACb,OAAOC,CAAC,GAAGF,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEP,SAAS,CAACJ,OAAO,EAAEmB,KAAK,CAACT,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "script"}