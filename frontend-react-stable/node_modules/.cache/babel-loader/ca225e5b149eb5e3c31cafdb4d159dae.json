{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport SliderContext from '../context';\nimport { getOffset } from '../util';\nexport default function Track(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    start = props.start,\n    end = props.end,\n    index = props.index,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    direction = _React$useContext.direction,\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range;\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max); // ============================ Events ============================\n\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  }; // ============================ Render ============================\n\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(trackPrefixCls, range && \"\".concat(trackPrefixCls, \"-\").concat(index + 1)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "classNames", "SliderContext", "getOffset", "Track", "props", "prefixCls", "style", "start", "end", "index", "onStartMove", "_React$useContext", "useContext", "direction", "min", "max", "disabled", "range", "trackPrefixCls", "concat", "offsetStart", "offsetEnd", "onInternalStartMove", "e", "positionStyle", "right", "width", "bottom", "height", "top", "left", "createElement", "className", "onMouseDown", "onTouchStart"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-slider/es/Tracks/Track.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport SliderContext from '../context';\nimport { getOffset } from '../util';\nexport default function Track(props) {\n  var prefixCls = props.prefixCls,\n      style = props.style,\n      start = props.start,\n      end = props.end,\n      index = props.index,\n      onStartMove = props.onStartMove;\n\n  var _React$useContext = React.useContext(SliderContext),\n      direction = _React$useContext.direction,\n      min = _React$useContext.min,\n      max = _React$useContext.max,\n      disabled = _React$useContext.disabled,\n      range = _React$useContext.range;\n\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max); // ============================ Events ============================\n\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  }; // ============================ Render ============================\n\n\n  var positionStyle = {};\n\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(trackPrefixCls, range && \"\".concat(trackPrefixCls, \"-\").concat(index + 1)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,SAAS,QAAQ,SAAS;AACnC,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,GAAG,GAAGJ,KAAK,CAACI,GAAG;IACfC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,WAAW,GAAGN,KAAK,CAACM,WAAW;EAEnC,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACX,aAAa,CAAC;IACnDY,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,GAAG,GAAGH,iBAAiB,CAACG,GAAG;IAC3BC,GAAG,GAAGJ,iBAAiB,CAACI,GAAG;IAC3BC,QAAQ,GAAGL,iBAAiB,CAACK,QAAQ;IACrCC,KAAK,GAAGN,iBAAiB,CAACM,KAAK;EAEnC,IAAIC,cAAc,GAAG,EAAE,CAACC,MAAM,CAACd,SAAS,EAAE,QAAQ,CAAC;EACnD,IAAIe,WAAW,GAAGlB,SAAS,CAACK,KAAK,EAAEO,GAAG,EAAEC,GAAG,CAAC;EAC5C,IAAIM,SAAS,GAAGnB,SAAS,CAACM,GAAG,EAAEM,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;;EAE1C,IAAIO,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,CAAC,EAAE;IACxD,IAAI,CAACP,QAAQ,IAAIN,WAAW,EAAE;MAC5BA,WAAW,CAACa,CAAC,EAAE,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,CAAC,CAAC;;EAGH,IAAIC,aAAa,GAAG,CAAC,CAAC;EAEtB,QAAQX,SAAS;IACf,KAAK,KAAK;MACRW,aAAa,CAACC,KAAK,GAAG,EAAE,CAACN,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACvDI,aAAa,CAACE,KAAK,GAAG,EAAE,CAACP,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACzE;IAEF,KAAK,KAAK;MACRI,aAAa,CAACG,MAAM,GAAG,EAAE,CAACR,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACxDI,aAAa,CAACI,MAAM,GAAG,EAAE,CAACT,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1E;IAEF,KAAK,KAAK;MACRI,aAAa,CAACK,GAAG,GAAG,EAAE,CAACV,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACrDI,aAAa,CAACI,MAAM,GAAG,EAAE,CAACT,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1E;IAEF;MACEI,aAAa,CAACM,IAAI,GAAG,EAAE,CAACX,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;MACtDI,aAAa,CAACE,KAAK,GAAG,EAAE,CAACP,MAAM,CAACE,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,GAAG,EAAE,GAAG,CAAC;EAC7E;EAEA,OAAO,aAAarB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEhC,UAAU,CAACkB,cAAc,EAAED,KAAK,IAAI,EAAE,CAACE,MAAM,CAACD,cAAc,EAAE,GAAG,CAAC,CAACC,MAAM,CAACV,KAAK,GAAG,CAAC,CAAC,CAAC;IAChGH,KAAK,EAAER,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,aAAa,CAAC,EAAElB,KAAK,CAAC;IAC7D2B,WAAW,EAAEX,mBAAmB;IAChCY,YAAY,EAAEZ;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}