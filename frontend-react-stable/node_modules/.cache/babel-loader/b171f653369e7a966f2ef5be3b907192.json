{"ast": null, "code": "import InternalAnchor from './Anchor';\nimport AnchorLink from './AnchorLink';\nvar Anchor = InternalAnchor;\nAnchor.Link = AnchorLink;\nexport default Anchor;", "map": {"version": 3, "names": ["InternalAnchor", "AnchorLink", "<PERSON><PERSON>", "Link"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/anchor/index.js"], "sourcesContent": ["import InternalAnchor from './Anchor';\nimport AnchorLink from './AnchorLink';\nvar Anchor = InternalAnchor;\nAnchor.Link = AnchorLink;\nexport default Anchor;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,UAAU;AACrC,OAAOC,UAAU,MAAM,cAAc;AACrC,IAAIC,MAAM,GAAGF,cAAc;AAC3BE,MAAM,CAACC,IAAI,GAAGF,UAAU;AACxB,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module"}