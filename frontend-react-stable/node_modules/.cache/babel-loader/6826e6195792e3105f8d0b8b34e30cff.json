{"ast": null, "code": "import * as React from 'react';\nimport canUseDom from \"../Dom/canUseDom\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nvar useInternalLayoutEffect = process.env.NODE_ENV !== 'test' && canUseDom() ? React.useLayoutEffect : React.useEffect;\nvar useLayoutEffect = function useLayoutEffect(callback, deps) {\n  var firstMountRef = React.useRef(true);\n  useInternalLayoutEffect(function () {\n    return callback(firstMountRef.current);\n  }, deps);\n\n  // We tell react that first mount has passed\n  useInternalLayoutEffect(function () {\n    firstMountRef.current = false;\n    return function () {\n      firstMountRef.current = true;\n    };\n  }, []);\n};\nexport var useLayoutUpdateEffect = function useLayoutUpdateEffect(callback, deps) {\n  useLayoutEffect(function (firstMount) {\n    if (!firstMount) {\n      return callback();\n    }\n  }, deps);\n};\nexport default useLayoutEffect;", "map": {"version": 3, "names": ["React", "canUseDom", "useInternalLayoutEffect", "process", "env", "NODE_ENV", "useLayoutEffect", "useEffect", "callback", "deps", "firstMountRef", "useRef", "current", "useLayoutUpdateEffect", "firstMount"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/hooks/useLayoutEffect.js"], "sourcesContent": ["import * as React from 'react';\nimport canUseDom from \"../Dom/canUseDom\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nvar useInternalLayoutEffect = process.env.NODE_ENV !== 'test' && canUseDom() ? React.useLayoutEffect : React.useEffect;\nvar useLayoutEffect = function useLayoutEffect(callback, deps) {\n  var firstMountRef = React.useRef(true);\n  useInternalLayoutEffect(function () {\n    return callback(firstMountRef.current);\n  }, deps);\n\n  // We tell react that first mount has passed\n  useInternalLayoutEffect(function () {\n    firstMountRef.current = false;\n    return function () {\n      firstMountRef.current = true;\n    };\n  }, []);\n};\nexport var useLayoutUpdateEffect = function useLayoutUpdateEffect(callback, deps) {\n  useLayoutEffect(function (firstMount) {\n    if (!firstMount) {\n      return callback();\n    }\n  }, deps);\n};\nexport default useLayoutEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;;AAExC;AACA;AACA;AACA,IAAIC,uBAAuB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIJ,SAAS,CAAC,CAAC,GAAGD,KAAK,CAACM,eAAe,GAAGN,KAAK,CAACO,SAAS;AACtH,IAAID,eAAe,GAAG,SAASA,eAAeA,CAACE,QAAQ,EAAEC,IAAI,EAAE;EAC7D,IAAIC,aAAa,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACtCT,uBAAuB,CAAC,YAAY;IAClC,OAAOM,QAAQ,CAACE,aAAa,CAACE,OAAO,CAAC;EACxC,CAAC,EAAEH,IAAI,CAAC;;EAER;EACAP,uBAAuB,CAAC,YAAY;IAClCQ,aAAa,CAACE,OAAO,GAAG,KAAK;IAC7B,OAAO,YAAY;MACjBF,aAAa,CAACE,OAAO,GAAG,IAAI;IAC9B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACL,QAAQ,EAAEC,IAAI,EAAE;EAChFH,eAAe,CAAC,UAAUQ,UAAU,EAAE;IACpC,IAAI,CAACA,UAAU,EAAE;MACf,OAAON,QAAQ,CAAC,CAAC;IACnB;EACF,CAAC,EAAEC,IAAI,CAAC;AACV,CAAC;AACD,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}