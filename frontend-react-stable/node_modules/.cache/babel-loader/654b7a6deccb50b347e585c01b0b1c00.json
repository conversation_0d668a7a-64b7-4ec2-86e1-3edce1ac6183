{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = min;\nfunction min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "min", "values", "valueof", "undefined", "index"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/min.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = min;\n\nfunction min(values, valueof) {\n  let min;\n\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  }\n\n  return min;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,SAASA,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC5B,IAAIF,GAAG;EAEP,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,MAAML,KAAK,IAAIG,MAAM,EAAE;MAC1B,IAAIH,KAAK,IAAI,IAAI,KAAKE,GAAG,GAAGF,KAAK,IAAIE,GAAG,KAAKG,SAAS,IAAIL,KAAK,IAAIA,KAAK,CAAC,EAAE;QACzEE,GAAG,GAAGF,KAAK;MACb;IACF;EACF,CAAC,MAAM;IACL,IAAIM,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAIN,KAAK,IAAIG,MAAM,EAAE;MACxB,IAAI,CAACH,KAAK,GAAGI,OAAO,CAACJ,KAAK,EAAE,EAAEM,KAAK,EAAEH,MAAM,CAAC,KAAK,IAAI,KAAKD,GAAG,GAAGF,KAAK,IAAIE,GAAG,KAAKG,SAAS,IAAIL,KAAK,IAAIA,KAAK,CAAC,EAAE;QAC7GE,GAAG,GAAGF,KAAK;MACb;IACF;EACF;EAEA,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}