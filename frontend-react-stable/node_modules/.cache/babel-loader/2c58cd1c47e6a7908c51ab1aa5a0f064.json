{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Typography,Card,Input,Select,Button,message,Row,Col,Spin,Empty,Space,Divider,List,Tag}from'antd';import{FolderOpenOutlined,ReloadOutlined,DownloadOutlined,FileTextOutlined,EyeOutlined,SearchOutlined}from'@ant-design/icons';import{dataQueryAPI}from'../services/api';import TextArea from'antd/es/input/TextArea';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Option}=Select;const DataQueryPage=()=>{const[loading,setLoading]=useState(false);const[csvLoading,setCsvLoading]=useState(false);const[resultLoading,setResultLoading]=useState(false);// CSV文件查询相关状态\nconst[csvDir,setCsvDir]=useState('');const[csvFiles,setCsvFiles]=useState([]);const[selectedCsv,setSelectedCsv]=useState('');// 结果文件查询相关状态\nconst[resultDir,setResultDir]=useState('');const[resultFiles,setResultFiles]=useState([]);const[selectedResult,setSelectedResult]=useState('');const[resultContent,setResultContent]=useState('');const[contentVisible,setContentVisible]=useState(false);// 获取CSV文件列表\nconst fetchCsvFiles=useCallback(async()=>{setCsvLoading(true);try{const response=await dataQueryAPI.listCsvFiles(csvDir);if(response.data.csv_files){setCsvFiles(response.data.csv_files);setSelectedCsv('');// 重置选择\nif(response.data.csv_files.length===0){message.info('📁 该目录下暂无CSV文件');}else{message.success(`📊 找到 ${response.data.csv_files.length} 个CSV文件`);}}}catch(error){var _error$response,_error$response$data;console.error('获取CSV文件列表失败:',error);message.error(`❌ 获取CSV文件列表失败: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||error.message}`);setCsvFiles([]);}finally{setCsvLoading(false);}},[csvDir]);// 获取结果文件列表\nconst fetchResultFiles=useCallback(async()=>{setResultLoading(true);try{const response=await dataQueryAPI.listResultFiles(resultDir);if(response.data.result_files){setResultFiles(response.data.result_files);setSelectedResult('');// 重置选择\nsetResultContent('');// 清空内容\nsetContentVisible(false);if(response.data.result_files.length===0){message.info('📁 该目录下暂无结果文件');}else{message.success(`📊 找到 ${response.data.result_files.length} 个结果文件`);}}}catch(error){var _error$response2,_error$response2$data;console.error('获取结果文件列表失败:',error);message.error(`❌ 获取结果文件列表失败: ${((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||error.message}`);setResultFiles([]);}finally{setResultLoading(false);}},[resultDir]);// 下载CSV文件\nconst downloadCsv=async fileName=>{try{setLoading(true);const response=await dataQueryAPI.downloadCsv(csvDir,fileName);// 创建下载链接\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download',fileName);document.body.appendChild(link);link.click();link.remove();window.URL.revokeObjectURL(url);message.success(`✅ ${fileName} 下载成功`);}catch(error){var _error$response3,_error$response3$data;console.error('下载CSV文件失败:',error);message.error(`❌ 下载失败: ${((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||error.message}`);}finally{setLoading(false);}};// 获取结果文件内容\nconst getResultContent=async fileName=>{try{setLoading(true);const response=await dataQueryAPI.getResultContent(resultDir,fileName);if(response.data.content){setResultContent(response.data.content);setContentVisible(true);message.success('✅ 文件内容加载成功');}}catch(error){var _error$response4,_error$response4$data;console.error('获取文件内容失败:',error);message.error(`❌ 获取文件内容失败: ${((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message}`);}finally{setLoading(false);}};// 下载结果文件\nconst downloadResult=async fileName=>{try{setLoading(true);const response=await dataQueryAPI.getResultContent(resultDir,fileName);// 创建下载链接\nconst blob=new Blob([response.data.content],{type:'text/plain'});const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.setAttribute('download',fileName);document.body.appendChild(link);link.click();link.remove();window.URL.revokeObjectURL(url);message.success(`✅ ${fileName} 下载成功`);}catch(error){var _error$response5,_error$response5$data;console.error('下载结果文件失败:',error);message.error(`❌ 下载失败: ${((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||error.message}`);}finally{setLoading(false);}};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(csvDir&&csvDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchCsvFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[csvDir]);useEffect(()=>{if(resultDir&&resultDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchResultFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[resultDir]);return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6570\\u636E\\u67E5\\u8BE2\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u67E5\\u8BE2\\u6D41\\u91CF\\u5206\\u6790\\u6A21\\u5757\\u751F\\u6210\\u7684CSV\\u6587\\u4EF6\\u548C\\u6D41\\u91CF\\u68C0\\u6D4B\\u6A21\\u578B\\u9884\\u6D4B\\u7684\\u7279\\u5F81\\u503C\\u3002\"}),/*#__PURE__*/_jsxs(Row,{gutter:[24,24],style:{marginTop:24},children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6E05\\u6D17\\u51FA\\u7684 CSV \\u6587\\u4EF6\\u67E5\\u8BE2\"})]}),size:\"small\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:csvDir,onChange:e=>setCsvDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",prefix:/*#__PURE__*/_jsx(FolderOpenOutlined,{}),style:{marginTop:8},addonAfter:/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchCsvFiles,loading:csvLoading,children:\"\\u5237\\u65B0\"})})]}),/*#__PURE__*/_jsx(Divider,{style:{margin:'16px 0'}}),csvLoading?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'20px'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6B63\\u5728\\u52A0\\u8F7DCSV\\u6587\\u4EF6\\u5217\\u8868...\"})})]}):csvFiles.length===0?/*#__PURE__*/_jsx(Empty,{description:\"\\u8BF7\\u5148\\u8F93\\u5165CSV\\u6587\\u4EF6\\u76EE\\u5F55\\u8DEF\\u5F84\\u5E76\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\\u83B7\\u53D6\\u6587\\u4EF6\\u5217\\u8868\",image:Empty.PRESENTED_IMAGE_SIMPLE}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:{width:'100%',marginTop:8},placeholder:\"\\u9009\\u62E9\\u8981\\u4E0B\\u8F7D\\u7684CSV\\u6587\\u4EF6\",value:selectedCsv,onChange:setSelectedCsv,showSearch:true,filterOption:(input,option)=>{var _option$children;return option===null||option===void 0?void 0:(_option$children=option.children)===null||_option$children===void 0?void 0:_option$children.toLowerCase().includes(input.toLowerCase());},children:csvFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),file]})},file))}),selectedCsv&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16,textAlign:'center'},children:/*#__PURE__*/_jsxs(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>downloadCsv(selectedCsv),loading:loading,size:\"large\",children:[\"\\u4E0B\\u8F7D \",selectedCsv]})}),/*#__PURE__*/_jsx(Divider,{style:{margin:'16px 0'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6587\\u4EF6\\u5217\\u8868\\uFF1A\"}),/*#__PURE__*/_jsx(List,{size:\"small\",style:{marginTop:8,maxHeight:200,overflow:'auto'},dataSource:csvFiles,renderItem:file=>/*#__PURE__*/_jsx(List.Item,{actions:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>downloadCsv(file),loading:loading,children:\"\\u4E0B\\u8F7D\"},\"download\")],children:/*#__PURE__*/_jsx(List.Item.Meta,{avatar:/*#__PURE__*/_jsx(FileTextOutlined,{style:{color:'#1890ff'}}),title:file,description:/*#__PURE__*/_jsx(Tag,{color:\"blue\",size:\"small\",children:\"CSV\\u6587\\u4EF6\"})})})})]})]})]})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(SearchOutlined,{}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7279\\u5F81\\u9884\\u6D4B\\u503C\\u67E5\\u8BE2\"})]}),size:\"small\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u7ED3\\u679C\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:resultDir,onChange:e=>setResultDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",prefix:/*#__PURE__*/_jsx(FolderOpenOutlined,{}),style:{marginTop:8},addonAfter:/*#__PURE__*/_jsx(Button,{size:\"small\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchResultFiles,loading:resultLoading,children:\"\\u5237\\u65B0\"})})]}),/*#__PURE__*/_jsx(Divider,{style:{margin:'16px 0'}}),resultLoading?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'20px'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u7ED3\\u679C\\u6587\\u4EF6\\u5217\\u8868...\"})})]}):resultFiles.length===0?/*#__PURE__*/_jsx(Empty,{description:\"\\u8BF7\\u5148\\u8F93\\u5165\\u7ED3\\u679C\\u6587\\u4EF6\\u76EE\\u5F55\\u8DEF\\u5F84\\u5E76\\u70B9\\u51FB\\u5237\\u65B0\\u6309\\u94AE\\u83B7\\u53D6\\u6587\\u4EF6\\u5217\\u8868\",image:Empty.PRESENTED_IMAGE_SIMPLE}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u7ED3\\u679C\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:{width:'100%',marginTop:8},placeholder:\"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\",value:selectedResult,onChange:setSelectedResult,showSearch:true,filterOption:(input,option)=>{var _option$children2;return option===null||option===void 0?void 0:(_option$children2=option.children)===null||_option$children2===void 0?void 0:_option$children2.toLowerCase().includes(input.toLowerCase());},children:resultFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(FileTextOutlined,{}),file]})},file))}),selectedResult&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>getResultContent(selectedResult),loading:loading,children:\"\\u67E5\\u770B\\u5185\\u5BB9\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>downloadResult(selectedResult),loading:loading,children:\"\\u4E0B\\u8F7D\\u6587\\u4EF6\"})]})}),/*#__PURE__*/_jsx(Divider,{style:{margin:'16px 0'}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6587\\u4EF6\\u5217\\u8868\\uFF1A\"}),/*#__PURE__*/_jsx(List,{size:\"small\",style:{marginTop:8,maxHeight:200,overflow:'auto'},dataSource:resultFiles,renderItem:file=>/*#__PURE__*/_jsx(List.Item,{actions:[/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>getResultContent(file),loading:loading,children:\"\\u67E5\\u770B\"},\"view\"),/*#__PURE__*/_jsx(Button,{type:\"link\",size:\"small\",icon:/*#__PURE__*/_jsx(DownloadOutlined,{}),onClick:()=>downloadResult(file),loading:loading,children:\"\\u4E0B\\u8F7D\"},\"download\")],children:/*#__PURE__*/_jsx(List.Item.Meta,{avatar:/*#__PURE__*/_jsx(FileTextOutlined,{style:{color:'#52c41a'}}),title:file,description:/*#__PURE__*/_jsx(Tag,{color:\"green\",size:\"small\",children:\"\\u7ED3\\u679C\\u6587\\u4EF6\"})})})})]})]})]})})})]}),contentVisible&&resultContent&&/*#__PURE__*/_jsx(Card,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(EyeOutlined,{}),/*#__PURE__*/_jsxs(\"span\",{children:[selectedResult,\" \\u5185\\u5BB9\"]})]}),style:{marginTop:24},size:\"small\",extra:/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:()=>setContentVisible(false),children:\"\\u5173\\u95ED\"}),children:/*#__PURE__*/_jsx(TextArea,{value:resultContent,rows:15,readOnly:true,style:{fontFamily:'monospace',fontSize:'12px',backgroundColor:'#f5f5f5'}})})]});};export default DataQueryPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Typography", "Card", "Input", "Select", "<PERSON><PERSON>", "message", "Row", "Col", "Spin", "Empty", "Space", "Divider", "List", "Tag", "FolderOpenOutlined", "ReloadOutlined", "DownloadOutlined", "FileTextOutlined", "EyeOutlined", "SearchOutlined", "dataQueryAPI", "TextArea", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Option", "DataQueryPage", "loading", "setLoading", "csvLoading", "setCsvLoading", "resultLoading", "setResultLoading", "csvDir", "setCsvDir", "csvFiles", "setCsvFiles", "selectedCsv", "setSelectedCsv", "resultDir", "setResultDir", "resultFiles", "setResultFiles", "selected<PERSON><PERSON><PERSON>", "setSelectedResult", "resultContent", "set<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "contentVisible", "setContentVisible", "fetchCsvFiles", "response", "listCsvFiles", "data", "csv_files", "length", "info", "success", "error", "_error$response", "_error$response$data", "console", "detail", "fetchResultFiles", "listResultFiles", "result_files", "_error$response2", "_error$response2$data", "downloadCsv", "fileName", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "_error$response3", "_error$response3$data", "getResultContent", "content", "_error$response4", "_error$response4$data", "downloadResult", "blob", "type", "_error$response5", "_error$response5$data", "timer", "setTimeout", "clearTimeout", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "gutter", "marginTop", "span", "title", "size", "direction", "width", "strong", "value", "onChange", "e", "target", "placeholder", "prefix", "addonAfter", "icon", "onClick", "margin", "textAlign", "padding", "description", "image", "PRESENTED_IMAGE_SIMPLE", "showSearch", "filterOption", "input", "option", "_option$children", "toLowerCase", "includes", "map", "file", "maxHeight", "overflow", "dataSource", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "avatar", "color", "_option$children2", "extra", "rows", "readOnly", "fontFamily", "backgroundColor"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataQueryPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n\n  Input,\n  Select,\n  Button,\n  message,\n  Row,\n  Col,\n  Spin,\n  Empty,\n  Space,\n  Divider,\n  List,\n  Tag\n} from 'antd';\nimport {\n  FolderOpenOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  FileTextOutlined,\n  EyeOutlined,\n  SearchOutlined\n} from '@ant-design/icons';\nimport { dataQueryAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\n\n\nconst DataQueryPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [csvLoading, setCsvLoading] = useState(false);\n  const [resultLoading, setResultLoading] = useState(false);\n\n  // CSV文件查询相关状态\n  const [csvDir, setCsvDir] = useState('');\n  const [csvFiles, setCsvFiles] = useState<string[]>([]);\n  const [selectedCsv, setSelectedCsv] = useState<string>('');\n\n  // 结果文件查询相关状态\n  const [resultDir, setResultDir] = useState('');\n  const [resultFiles, setResultFiles] = useState<string[]>([]);\n  const [selectedResult, setSelectedResult] = useState<string>('');\n  const [resultContent, setResultContent] = useState<string>('');\n  const [contentVisible, setContentVisible] = useState(false);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = useCallback(async () => {\n    setCsvLoading(true);\n    try {\n      const response = await dataQueryAPI.listCsvFiles(csvDir);\n      if (response.data.csv_files) {\n        setCsvFiles(response.data.csv_files);\n        setSelectedCsv(''); // 重置选择\n        if (response.data.csv_files.length === 0) {\n          message.info('📁 该目录下暂无CSV文件');\n        } else {\n          message.success(`📊 找到 ${response.data.csv_files.length} 个CSV文件`);\n        }\n      }\n    } catch (error: any) {\n      console.error('获取CSV文件列表失败:', error);\n      message.error(`❌ 获取CSV文件列表失败: ${error.response?.data?.detail || error.message}`);\n      setCsvFiles([]);\n    } finally {\n      setCsvLoading(false);\n    }\n  }, [csvDir]);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async () => {\n    setResultLoading(true);\n    try {\n      const response = await dataQueryAPI.listResultFiles(resultDir);\n      if (response.data.result_files) {\n        setResultFiles(response.data.result_files);\n        setSelectedResult(''); // 重置选择\n        setResultContent(''); // 清空内容\n        setContentVisible(false);\n        if (response.data.result_files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        } else {\n          message.success(`📊 找到 ${response.data.result_files.length} 个结果文件`);\n        }\n      }\n    } catch (error: any) {\n      console.error('获取结果文件列表失败:', error);\n      message.error(`❌ 获取结果文件列表失败: ${error.response?.data?.detail || error.message}`);\n      setResultFiles([]);\n    } finally {\n      setResultLoading(false);\n    }\n  }, [resultDir]);\n\n  // 下载CSV文件\n  const downloadCsv = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.downloadCsv(csvDir, fileName);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success(`✅ ${fileName} 下载成功`);\n    } catch (error: any) {\n      console.error('下载CSV文件失败:', error);\n      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取结果文件内容\n  const getResultContent = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.getResultContent(resultDir, fileName);\n      if (response.data.content) {\n        setResultContent(response.data.content);\n        setContentVisible(true);\n        message.success('✅ 文件内容加载成功');\n      }\n    } catch (error: any) {\n      console.error('获取文件内容失败:', error);\n      message.error(`❌ 获取文件内容失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载结果文件\n  const downloadResult = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.getResultContent(resultDir, fileName);\n\n      // 创建下载链接\n      const blob = new Blob([response.data.content], { type: 'text/plain' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success(`✅ ${fileName} 下载成功`);\n    } catch (error: any) {\n      console.error('下载结果文件失败:', error);\n      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [csvDir]);\n\n  useEffect(() => {\n    if (resultDir && resultDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchResultFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [resultDir]);\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>数据查询</Title>\n      <Text type=\"secondary\">\n        查询流量分析模块生成的CSV文件和流量检测模型预测的特征值。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* CSV文件查询 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <FileTextOutlined />\n                <span>清洗出的 CSV 文件查询</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input\n                  value={csvDir}\n                  onChange={(e) => setCsvDir(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  prefix={<FolderOpenOutlined />}\n                  style={{ marginTop: 8 }}\n                  addonAfter={\n                    <Button\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      onClick={fetchCsvFiles}\n                      loading={csvLoading}\n                    >\n                      刷新\n                    </Button>\n                  }\n                />\n              </div>\n\n              <Divider style={{ margin: '16px 0' }} />\n\n              {csvLoading ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <Spin size=\"large\" />\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">正在加载CSV文件列表...</Text>\n                  </div>\n                </div>\n              ) : csvFiles.length === 0 ? (\n                <Empty\n                  description=\"请先输入CSV文件目录路径并点击刷新按钮获取文件列表\"\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                />\n              ) : (\n                <div>\n                  <Text strong>选择CSV文件：</Text>\n                  <Select\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder=\"选择要下载的CSV文件\"\n                    value={selectedCsv}\n                    onChange={setSelectedCsv}\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {csvFiles.map(file => (\n                      <Option key={file} value={file}>\n                        <Space>\n                          <FileTextOutlined />\n                          {file}\n                        </Space>\n                      </Option>\n                    ))}\n                  </Select>\n\n                  {selectedCsv && (\n                    <div style={{ marginTop: 16, textAlign: 'center' }}>\n                      <Button\n                        type=\"primary\"\n                        icon={<DownloadOutlined />}\n                        onClick={() => downloadCsv(selectedCsv)}\n                        loading={loading}\n                        size=\"large\"\n                      >\n                        下载 {selectedCsv}\n                      </Button>\n                    </div>\n                  )}\n\n                  <Divider style={{ margin: '16px 0' }} />\n\n                  <div>\n                    <Text strong>文件列表：</Text>\n                    <List\n                      size=\"small\"\n                      style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}\n                      dataSource={csvFiles}\n                      renderItem={(file) => (\n                        <List.Item\n                          actions={[\n                            <Button\n                              key=\"download\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<DownloadOutlined />}\n                              onClick={() => downloadCsv(file)}\n                              loading={loading}\n                            >\n                              下载\n                            </Button>\n                          ]}\n                        >\n                          <List.Item.Meta\n                            avatar={<FileTextOutlined style={{ color: '#1890ff' }} />}\n                            title={file}\n                            description={\n                              <Tag color=\"blue\" size=\"small\">CSV文件</Tag>\n                            }\n                          />\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                </div>\n              )}\n            </Space>\n          </Card>\n        </Col>\n\n        {/* 结果文件查询 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SearchOutlined />\n                <span>特征预测值查询</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>结果文件目录：</Text>\n                <Input\n                  value={resultDir}\n                  onChange={(e) => setResultDir(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  prefix={<FolderOpenOutlined />}\n                  style={{ marginTop: 8 }}\n                  addonAfter={\n                    <Button\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      onClick={fetchResultFiles}\n                      loading={resultLoading}\n                    >\n                      刷新\n                    </Button>\n                  }\n                />\n              </div>\n\n              <Divider style={{ margin: '16px 0' }} />\n\n              {resultLoading ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <Spin size=\"large\" />\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">正在加载结果文件列表...</Text>\n                  </div>\n                </div>\n              ) : resultFiles.length === 0 ? (\n                <Empty\n                  description=\"请先输入结果文件目录路径并点击刷新按钮获取文件列表\"\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                />\n              ) : (\n                <div>\n                  <Text strong>选择结果文件：</Text>\n                  <Select\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder=\"选择要查看的结果文件\"\n                    value={selectedResult}\n                    onChange={setSelectedResult}\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {resultFiles.map(file => (\n                      <Option key={file} value={file}>\n                        <Space>\n                          <FileTextOutlined />\n                          {file}\n                        </Space>\n                      </Option>\n                    ))}\n                  </Select>\n\n                  {selectedResult && (\n                    <div style={{ marginTop: 16 }}>\n                      <Space>\n                        <Button\n                          type=\"primary\"\n                          icon={<EyeOutlined />}\n                          onClick={() => getResultContent(selectedResult)}\n                          loading={loading}\n                        >\n                          查看内容\n                        </Button>\n                        <Button\n                          icon={<DownloadOutlined />}\n                          onClick={() => downloadResult(selectedResult)}\n                          loading={loading}\n                        >\n                          下载文件\n                        </Button>\n                      </Space>\n                    </div>\n                  )}\n\n                  <Divider style={{ margin: '16px 0' }} />\n\n                  <div>\n                    <Text strong>文件列表：</Text>\n                    <List\n                      size=\"small\"\n                      style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}\n                      dataSource={resultFiles}\n                      renderItem={(file) => (\n                        <List.Item\n                          actions={[\n                            <Button\n                              key=\"view\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<EyeOutlined />}\n                              onClick={() => getResultContent(file)}\n                              loading={loading}\n                            >\n                              查看\n                            </Button>,\n                            <Button\n                              key=\"download\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<DownloadOutlined />}\n                              onClick={() => downloadResult(file)}\n                              loading={loading}\n                            >\n                              下载\n                            </Button>\n                          ]}\n                        >\n                          <List.Item.Meta\n                            avatar={<FileTextOutlined style={{ color: '#52c41a' }} />}\n                            title={file}\n                            description={\n                              <Tag color=\"green\" size=\"small\">结果文件</Tag>\n                            }\n                          />\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                </div>\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 文件内容查看 */}\n      {contentVisible && resultContent && (\n        <Card\n          title={\n            <Space>\n              <EyeOutlined />\n              <span>{selectedResult} 内容</span>\n            </Space>\n          }\n          style={{ marginTop: 24 }}\n          size=\"small\"\n          extra={\n            <Button\n              size=\"small\"\n              onClick={() => setContentVisible(false)}\n            >\n              关闭\n            </Button>\n          }\n        >\n          <TextArea\n            value={resultContent}\n            rows={15}\n            readOnly\n            style={{\n              fontFamily: 'monospace',\n              fontSize: '12px',\n              backgroundColor: '#f5f5f5'\n            }}\n          />\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default DataQueryPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,UAAU,CACVC,IAAI,CAEJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,OAAO,CACPC,GAAG,CACHC,GAAG,CACHC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,IAAI,CACJC,GAAG,KACE,MAAM,CACb,OACEC,kBAAkB,CAClBC,cAAc,CACdC,gBAAgB,CAChBC,gBAAgB,CAChBC,WAAW,CACXC,cAAc,KACT,mBAAmB,CAC1B,OAASC,YAAY,KAAQ,iBAAiB,CAC9C,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG3B,UAAU,CAClC,KAAM,CAAE4B,MAAO,CAAC,CAAGzB,MAAM,CAIzB,KAAM,CAAA0B,aAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmC,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAEzD;AACA,KAAM,CAACuC,MAAM,CAAEC,SAAS,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACyC,QAAQ,CAAEC,WAAW,CAAC,CAAG1C,QAAQ,CAAW,EAAE,CAAC,CACtD,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAS,EAAE,CAAC,CAE1D;AACA,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+C,WAAW,CAAEC,cAAc,CAAC,CAAGhD,QAAQ,CAAW,EAAE,CAAC,CAC5D,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACmD,aAAa,CAAEC,gBAAgB,CAAC,CAAGpD,QAAQ,CAAS,EAAE,CAAC,CAC9D,KAAM,CAACqD,cAAc,CAAEC,iBAAiB,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAAAuD,aAAa,CAAGrD,WAAW,CAAC,SAAY,CAC5CkC,aAAa,CAAC,IAAI,CAAC,CACnB,GAAI,CACF,KAAM,CAAAoB,QAAQ,CAAG,KAAM,CAAAjC,YAAY,CAACkC,YAAY,CAAClB,MAAM,CAAC,CACxD,GAAIiB,QAAQ,CAACE,IAAI,CAACC,SAAS,CAAE,CAC3BjB,WAAW,CAACc,QAAQ,CAACE,IAAI,CAACC,SAAS,CAAC,CACpCf,cAAc,CAAC,EAAE,CAAC,CAAE;AACpB,GAAIY,QAAQ,CAACE,IAAI,CAACC,SAAS,CAACC,MAAM,GAAK,CAAC,CAAE,CACxCpD,OAAO,CAACqD,IAAI,CAAC,gBAAgB,CAAC,CAChC,CAAC,IAAM,CACLrD,OAAO,CAACsD,OAAO,CAAC,SAASN,QAAQ,CAACE,IAAI,CAACC,SAAS,CAACC,MAAM,SAAS,CAAC,CACnE,CACF,CACF,CAAE,MAAOG,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnBC,OAAO,CAACH,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpCvD,OAAO,CAACuD,KAAK,CAAC,kBAAkB,EAAAC,eAAA,CAAAD,KAAK,CAACP,QAAQ,UAAAQ,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBN,IAAI,UAAAO,oBAAA,iBAApBA,oBAAA,CAAsBE,MAAM,GAAIJ,KAAK,CAACvD,OAAO,EAAE,CAAC,CAChFkC,WAAW,CAAC,EAAE,CAAC,CACjB,CAAC,OAAS,CACRN,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACG,MAAM,CAAC,CAAC,CAEZ;AACA,KAAM,CAAA6B,gBAAgB,CAAGlE,WAAW,CAAC,SAAY,CAC/CoC,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAI,CACF,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAjC,YAAY,CAAC8C,eAAe,CAACxB,SAAS,CAAC,CAC9D,GAAIW,QAAQ,CAACE,IAAI,CAACY,YAAY,CAAE,CAC9BtB,cAAc,CAACQ,QAAQ,CAACE,IAAI,CAACY,YAAY,CAAC,CAC1CpB,iBAAiB,CAAC,EAAE,CAAC,CAAE;AACvBE,gBAAgB,CAAC,EAAE,CAAC,CAAE;AACtBE,iBAAiB,CAAC,KAAK,CAAC,CACxB,GAAIE,QAAQ,CAACE,IAAI,CAACY,YAAY,CAACV,MAAM,GAAK,CAAC,CAAE,CAC3CpD,OAAO,CAACqD,IAAI,CAAC,eAAe,CAAC,CAC/B,CAAC,IAAM,CACLrD,OAAO,CAACsD,OAAO,CAAC,SAASN,QAAQ,CAACE,IAAI,CAACY,YAAY,CAACV,MAAM,QAAQ,CAAC,CACrE,CACF,CACF,CAAE,MAAOG,KAAU,CAAE,KAAAQ,gBAAA,CAAAC,qBAAA,CACnBN,OAAO,CAACH,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnCvD,OAAO,CAACuD,KAAK,CAAC,iBAAiB,EAAAQ,gBAAA,CAAAR,KAAK,CAACP,QAAQ,UAAAe,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBb,IAAI,UAAAc,qBAAA,iBAApBA,qBAAA,CAAsBL,MAAM,GAAIJ,KAAK,CAACvD,OAAO,EAAE,CAAC,CAC/EwC,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,OAAS,CACRV,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAAE,CAACO,SAAS,CAAC,CAAC,CAEf;AACA,KAAM,CAAA4B,WAAW,CAAG,KAAO,CAAAC,QAAgB,EAAK,CAC9C,GAAI,CACFxC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAjC,YAAY,CAACkD,WAAW,CAAClC,MAAM,CAAEmC,QAAQ,CAAC,CAEjE;AACA,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACvB,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAsB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAEV,QAAQ,CAAC,CACvCO,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CACbZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC,CAE/BnE,OAAO,CAACsD,OAAO,CAAC,KAAKY,QAAQ,OAAO,CAAC,CACvC,CAAE,MAAOX,KAAU,CAAE,KAAA2B,gBAAA,CAAAC,qBAAA,CACnBzB,OAAO,CAACH,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClCvD,OAAO,CAACuD,KAAK,CAAC,WAAW,EAAA2B,gBAAA,CAAA3B,KAAK,CAACP,QAAQ,UAAAkC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBhC,IAAI,UAAAiC,qBAAA,iBAApBA,qBAAA,CAAsBxB,MAAM,GAAIJ,KAAK,CAACvD,OAAO,EAAE,CAAC,CAC3E,CAAC,OAAS,CACR0B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0D,gBAAgB,CAAG,KAAO,CAAAlB,QAAgB,EAAK,CACnD,GAAI,CACFxC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAjC,YAAY,CAACqE,gBAAgB,CAAC/C,SAAS,CAAE6B,QAAQ,CAAC,CACzE,GAAIlB,QAAQ,CAACE,IAAI,CAACmC,OAAO,CAAE,CACzBzC,gBAAgB,CAACI,QAAQ,CAACE,IAAI,CAACmC,OAAO,CAAC,CACvCvC,iBAAiB,CAAC,IAAI,CAAC,CACvB9C,OAAO,CAACsD,OAAO,CAAC,YAAY,CAAC,CAC/B,CACF,CAAE,MAAOC,KAAU,CAAE,KAAA+B,gBAAA,CAAAC,qBAAA,CACnB7B,OAAO,CAACH,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCvD,OAAO,CAACuD,KAAK,CAAC,eAAe,EAAA+B,gBAAA,CAAA/B,KAAK,CAACP,QAAQ,UAAAsC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBpC,IAAI,UAAAqC,qBAAA,iBAApBA,qBAAA,CAAsB5B,MAAM,GAAIJ,KAAK,CAACvD,OAAO,EAAE,CAAC,CAC/E,CAAC,OAAS,CACR0B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA8D,cAAc,CAAG,KAAO,CAAAtB,QAAgB,EAAK,CACjD,GAAI,CACFxC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAjC,YAAY,CAACqE,gBAAgB,CAAC/C,SAAS,CAAE6B,QAAQ,CAAC,CAEzE;AACA,KAAM,CAAAuB,IAAI,CAAG,GAAI,CAAAlB,IAAI,CAAC,CAACvB,QAAQ,CAACE,IAAI,CAACmC,OAAO,CAAC,CAAE,CAAEK,IAAI,CAAE,YAAa,CAAC,CAAC,CACtE,KAAM,CAAAvB,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACmB,IAAI,CAAC,CAC5C,KAAM,CAAAjB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAEV,QAAQ,CAAC,CACvCO,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CACbZ,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC,CAE/BnE,OAAO,CAACsD,OAAO,CAAC,KAAKY,QAAQ,OAAO,CAAC,CACvC,CAAE,MAAOX,KAAU,CAAE,KAAAoC,gBAAA,CAAAC,qBAAA,CACnBlC,OAAO,CAACH,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCvD,OAAO,CAACuD,KAAK,CAAC,WAAW,EAAAoC,gBAAA,CAAApC,KAAK,CAACP,QAAQ,UAAA2C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBzC,IAAI,UAAA0C,qBAAA,iBAApBA,qBAAA,CAAsBjC,MAAM,GAAIJ,KAAK,CAACvD,OAAO,EAAE,CAAC,CAC3E,CAAC,OAAS,CACR0B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAID;AACAjC,SAAS,CAAC,IAAM,CACd,GAAIsC,MAAM,EAAIA,MAAM,CAACqB,MAAM,CAAG,CAAC,CAAE,CAAE;AACjC,KAAM,CAAAyC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B/C,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMgD,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAAC9D,MAAM,CAAC,CAAC,CAEZtC,SAAS,CAAC,IAAM,CACd,GAAI4C,SAAS,EAAIA,SAAS,CAACe,MAAM,CAAG,CAAC,CAAE,CAAE;AACvC,KAAM,CAAAyC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BlC,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMmC,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAACxD,SAAS,CAAC,CAAC,CAEf,mBACEjB,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACG,KAAK,EAAC4E,KAAK,CAAE,CAAE,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,CAAC,0BAAI,CAAO,CAAC,cAChG9E,IAAA,CAACI,IAAI,EAACoE,IAAI,CAAC,WAAW,CAAAM,QAAA,CAAC,uKAEvB,CAAM,CAAC,cAEP5E,KAAA,CAACnB,GAAG,EAACqG,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACJ,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAP,QAAA,eAE9C9E,IAAA,CAAChB,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAR,QAAA,cACZ9E,IAAA,CAACtB,IAAI,EACH6G,KAAK,cACHrF,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACN,gBAAgB,GAAE,CAAC,cACpBM,IAAA,SAAA8E,QAAA,CAAM,uDAAa,CAAM,CAAC,EACrB,CACR,CACDU,IAAI,CAAC,OAAO,CAAAV,QAAA,cAEZ5E,KAAA,CAACf,KAAK,EAACsG,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAZ,QAAA,eACnD5E,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5B9E,IAAA,CAACrB,KAAK,EACJiH,KAAK,CAAE/E,MAAO,CACdgF,QAAQ,CAAGC,CAAC,EAAKhF,SAAS,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC3CI,WAAW,CAAC,4BAAkB,CAC9BC,MAAM,cAAEjG,IAAA,CAACT,kBAAkB,GAAE,CAAE,CAC/ByF,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CACxBa,UAAU,cACRlG,IAAA,CAACnB,MAAM,EACL2G,IAAI,CAAC,OAAO,CACZW,IAAI,cAAEnG,IAAA,CAACR,cAAc,GAAE,CAAE,CACzB4G,OAAO,CAAEvE,aAAc,CACvBtB,OAAO,CAAEE,UAAW,CAAAqE,QAAA,CACrB,cAED,CAAQ,CACT,CACF,CAAC,EACC,CAAC,cAEN9E,IAAA,CAACZ,OAAO,EAAC4F,KAAK,CAAE,CAAEqB,MAAM,CAAE,QAAS,CAAE,CAAE,CAAC,CAEvC5F,UAAU,cACTP,KAAA,QAAK8E,KAAK,CAAE,CAAEsB,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAzB,QAAA,eACnD9E,IAAA,CAACf,IAAI,EAACuG,IAAI,CAAC,OAAO,CAAE,CAAC,cACrBxF,IAAA,QAAKgF,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CAAAP,QAAA,cAC3B9E,IAAA,CAACI,IAAI,EAACoE,IAAI,CAAC,WAAW,CAAAM,QAAA,CAAC,wDAAc,CAAM,CAAC,CACzC,CAAC,EACH,CAAC,CACJ/D,QAAQ,CAACmB,MAAM,GAAK,CAAC,cACvBlC,IAAA,CAACd,KAAK,EACJsH,WAAW,CAAC,+IAA4B,CACxCC,KAAK,CAAEvH,KAAK,CAACwH,sBAAuB,CACrC,CAAC,cAEFxG,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5B9E,IAAA,CAACpB,MAAM,EACLoG,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEL,SAAS,CAAE,CAAE,CAAE,CACvCW,WAAW,CAAC,qDAAa,CACzBJ,KAAK,CAAE3E,WAAY,CACnB4E,QAAQ,CAAE3E,cAAe,CACzByF,UAAU,MACVC,YAAY,CAAEA,CAACC,KAAK,CAAEC,MAAM,QAAAC,gBAAA,OACzB,CAAAD,MAAM,SAANA,MAAM,kBAAAC,gBAAA,CAAND,MAAM,CAAEhC,QAAQ,UAAAiC,gBAAA,iBAAjBA,gBAAA,CAAyCC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EACrF,CAAAlC,QAAA,CAEA/D,QAAQ,CAACmG,GAAG,CAACC,IAAI,eAChBnH,IAAA,CAACK,MAAM,EAAYuF,KAAK,CAAEuB,IAAK,CAAArC,QAAA,cAC7B5E,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACN,gBAAgB,GAAE,CAAC,CACnByH,IAAI,EACA,CAAC,EAJGA,IAKL,CACT,CAAC,CACI,CAAC,CAERlG,WAAW,eACVjB,IAAA,QAAKgF,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAE,CAAEiB,SAAS,CAAE,QAAS,CAAE,CAAAxB,QAAA,cACjD5E,KAAA,CAACrB,MAAM,EACL2F,IAAI,CAAC,SAAS,CACd2B,IAAI,cAAEnG,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAC3B2G,OAAO,CAAEA,CAAA,GAAMrD,WAAW,CAAC9B,WAAW,CAAE,CACxCV,OAAO,CAAEA,OAAQ,CACjBiF,IAAI,CAAC,OAAO,CAAAV,QAAA,EACb,eACI,CAAC7D,WAAW,EACT,CAAC,CACN,CACN,cAEDjB,IAAA,CAACZ,OAAO,EAAC4F,KAAK,CAAE,CAAEqB,MAAM,CAAE,QAAS,CAAE,CAAE,CAAC,cAExCnG,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB9E,IAAA,CAACX,IAAI,EACHmG,IAAI,CAAC,OAAO,CACZR,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAC,CAAE+B,SAAS,CAAE,GAAG,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAC1DC,UAAU,CAAEvG,QAAS,CACrBwG,UAAU,CAAGJ,IAAI,eACfnH,IAAA,CAACX,IAAI,CAACmI,IAAI,EACRC,OAAO,CAAE,cACPzH,IAAA,CAACnB,MAAM,EAEL2F,IAAI,CAAC,MAAM,CACXgB,IAAI,CAAC,OAAO,CACZW,IAAI,cAAEnG,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAC3B2G,OAAO,CAAEA,CAAA,GAAMrD,WAAW,CAACoE,IAAI,CAAE,CACjC5G,OAAO,CAAEA,OAAQ,CAAAuE,QAAA,CAClB,cAED,EARM,UAQE,CAAC,CACT,CAAAA,QAAA,cAEF9E,IAAA,CAACX,IAAI,CAACmI,IAAI,CAACE,IAAI,EACbC,MAAM,cAAE3H,IAAA,CAACN,gBAAgB,EAACsF,KAAK,CAAE,CAAE4C,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CAC1DrC,KAAK,CAAE4B,IAAK,CACZX,WAAW,cACTxG,IAAA,CAACV,GAAG,EAACsI,KAAK,CAAC,MAAM,CAACpC,IAAI,CAAC,OAAO,CAAAV,QAAA,CAAC,iBAAK,CAAK,CAC1C,CACF,CAAC,CACO,CACX,CACH,CAAC,EACC,CAAC,EACH,CACN,EACI,CAAC,CACJ,CAAC,CACJ,CAAC,cAGN9E,IAAA,CAAChB,GAAG,EAACsG,IAAI,CAAE,EAAG,CAAAR,QAAA,cACZ9E,IAAA,CAACtB,IAAI,EACH6G,KAAK,cACHrF,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACJ,cAAc,GAAE,CAAC,cAClBI,IAAA,SAAA8E,QAAA,CAAM,4CAAO,CAAM,CAAC,EACf,CACR,CACDU,IAAI,CAAC,OAAO,CAAAV,QAAA,cAEZ5E,KAAA,CAACf,KAAK,EAACsG,SAAS,CAAC,UAAU,CAACT,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAZ,QAAA,eACnD5E,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3B9E,IAAA,CAACrB,KAAK,EACJiH,KAAK,CAAEzE,SAAU,CACjB0E,QAAQ,CAAGC,CAAC,EAAK1E,YAAY,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9CI,WAAW,CAAC,4BAAkB,CAC9BC,MAAM,cAAEjG,IAAA,CAACT,kBAAkB,GAAE,CAAE,CAC/ByF,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CACxBa,UAAU,cACRlG,IAAA,CAACnB,MAAM,EACL2G,IAAI,CAAC,OAAO,CACZW,IAAI,cAAEnG,IAAA,CAACR,cAAc,GAAE,CAAE,CACzB4G,OAAO,CAAE1D,gBAAiB,CAC1BnC,OAAO,CAAEI,aAAc,CAAAmE,QAAA,CACxB,cAED,CAAQ,CACT,CACF,CAAC,EACC,CAAC,cAEN9E,IAAA,CAACZ,OAAO,EAAC4F,KAAK,CAAE,CAAEqB,MAAM,CAAE,QAAS,CAAE,CAAE,CAAC,CAEvC1F,aAAa,cACZT,KAAA,QAAK8E,KAAK,CAAE,CAAEsB,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAzB,QAAA,eACnD9E,IAAA,CAACf,IAAI,EAACuG,IAAI,CAAC,OAAO,CAAE,CAAC,cACrBxF,IAAA,QAAKgF,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CAAAP,QAAA,cAC3B9E,IAAA,CAACI,IAAI,EAACoE,IAAI,CAAC,WAAW,CAAAM,QAAA,CAAC,iEAAa,CAAM,CAAC,CACxC,CAAC,EACH,CAAC,CACJzD,WAAW,CAACa,MAAM,GAAK,CAAC,cAC1BlC,IAAA,CAACd,KAAK,EACJsH,WAAW,CAAC,wJAA2B,CACvCC,KAAK,CAAEvH,KAAK,CAACwH,sBAAuB,CACrC,CAAC,cAEFxG,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3B9E,IAAA,CAACpB,MAAM,EACLoG,KAAK,CAAE,CAAEU,KAAK,CAAE,MAAM,CAAEL,SAAS,CAAE,CAAE,CAAE,CACvCW,WAAW,CAAC,8DAAY,CACxBJ,KAAK,CAAErE,cAAe,CACtBsE,QAAQ,CAAErE,iBAAkB,CAC5BmF,UAAU,MACVC,YAAY,CAAEA,CAACC,KAAK,CAAEC,MAAM,QAAAe,iBAAA,OACzB,CAAAf,MAAM,SAANA,MAAM,kBAAAe,iBAAA,CAANf,MAAM,CAAEhC,QAAQ,UAAA+C,iBAAA,iBAAjBA,iBAAA,CAAyCb,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,EACrF,CAAAlC,QAAA,CAEAzD,WAAW,CAAC6F,GAAG,CAACC,IAAI,eACnBnH,IAAA,CAACK,MAAM,EAAYuF,KAAK,CAAEuB,IAAK,CAAArC,QAAA,cAC7B5E,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACN,gBAAgB,GAAE,CAAC,CACnByH,IAAI,EACA,CAAC,EAJGA,IAKL,CACT,CAAC,CACI,CAAC,CAER5F,cAAc,eACbvB,IAAA,QAAKgF,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAP,QAAA,cAC5B5E,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACnB,MAAM,EACL2F,IAAI,CAAC,SAAS,CACd2B,IAAI,cAAEnG,IAAA,CAACL,WAAW,GAAE,CAAE,CACtByG,OAAO,CAAEA,CAAA,GAAMlC,gBAAgB,CAAC3C,cAAc,CAAE,CAChDhB,OAAO,CAAEA,OAAQ,CAAAuE,QAAA,CAClB,0BAED,CAAQ,CAAC,cACT9E,IAAA,CAACnB,MAAM,EACLsH,IAAI,cAAEnG,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAC3B2G,OAAO,CAAEA,CAAA,GAAM9B,cAAc,CAAC/C,cAAc,CAAE,CAC9ChB,OAAO,CAAEA,OAAQ,CAAAuE,QAAA,CAClB,0BAED,CAAQ,CAAC,EACJ,CAAC,CACL,CACN,cAED9E,IAAA,CAACZ,OAAO,EAAC4F,KAAK,CAAE,CAAEqB,MAAM,CAAE,QAAS,CAAE,CAAE,CAAC,cAExCnG,KAAA,QAAA4E,QAAA,eACE9E,IAAA,CAACI,IAAI,EAACuF,MAAM,MAAAb,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB9E,IAAA,CAACX,IAAI,EACHmG,IAAI,CAAC,OAAO,CACZR,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAC,CAAE+B,SAAS,CAAE,GAAG,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAC1DC,UAAU,CAAEjG,WAAY,CACxBkG,UAAU,CAAGJ,IAAI,eACfnH,IAAA,CAACX,IAAI,CAACmI,IAAI,EACRC,OAAO,CAAE,cACPzH,IAAA,CAACnB,MAAM,EAEL2F,IAAI,CAAC,MAAM,CACXgB,IAAI,CAAC,OAAO,CACZW,IAAI,cAAEnG,IAAA,CAACL,WAAW,GAAE,CAAE,CACtByG,OAAO,CAAEA,CAAA,GAAMlC,gBAAgB,CAACiD,IAAI,CAAE,CACtC5G,OAAO,CAAEA,OAAQ,CAAAuE,QAAA,CAClB,cAED,EARM,MAQE,CAAC,cACT9E,IAAA,CAACnB,MAAM,EAEL2F,IAAI,CAAC,MAAM,CACXgB,IAAI,CAAC,OAAO,CACZW,IAAI,cAAEnG,IAAA,CAACP,gBAAgB,GAAE,CAAE,CAC3B2G,OAAO,CAAEA,CAAA,GAAM9B,cAAc,CAAC6C,IAAI,CAAE,CACpC5G,OAAO,CAAEA,OAAQ,CAAAuE,QAAA,CAClB,cAED,EARM,UAQE,CAAC,CACT,CAAAA,QAAA,cAEF9E,IAAA,CAACX,IAAI,CAACmI,IAAI,CAACE,IAAI,EACbC,MAAM,cAAE3H,IAAA,CAACN,gBAAgB,EAACsF,KAAK,CAAE,CAAE4C,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CAC1DrC,KAAK,CAAE4B,IAAK,CACZX,WAAW,cACTxG,IAAA,CAACV,GAAG,EAACsI,KAAK,CAAC,OAAO,CAACpC,IAAI,CAAC,OAAO,CAAAV,QAAA,CAAC,0BAAI,CAAK,CAC1C,CACF,CAAC,CACO,CACX,CACH,CAAC,EACC,CAAC,EACH,CACN,EACI,CAAC,CACJ,CAAC,CACJ,CAAC,EACH,CAAC,CAGLnD,cAAc,EAAIF,aAAa,eAC9BzB,IAAA,CAACtB,IAAI,EACH6G,KAAK,cACHrF,KAAA,CAACf,KAAK,EAAA2F,QAAA,eACJ9E,IAAA,CAACL,WAAW,GAAE,CAAC,cACfO,KAAA,SAAA4E,QAAA,EAAOvD,cAAc,CAAC,eAAG,EAAM,CAAC,EAC3B,CACR,CACDyD,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CACzBG,IAAI,CAAC,OAAO,CACZsC,KAAK,cACH9H,IAAA,CAACnB,MAAM,EACL2G,IAAI,CAAC,OAAO,CACZY,OAAO,CAAEA,CAAA,GAAMxE,iBAAiB,CAAC,KAAK,CAAE,CAAAkD,QAAA,CACzC,cAED,CAAQ,CACT,CAAAA,QAAA,cAED9E,IAAA,CAACF,QAAQ,EACP8F,KAAK,CAAEnE,aAAc,CACrBsG,IAAI,CAAE,EAAG,CACTC,QAAQ,MACRhD,KAAK,CAAE,CACLiD,UAAU,CAAE,WAAW,CACvBhD,QAAQ,CAAE,MAAM,CAChBiD,eAAe,CAAE,SACnB,CAAE,CACH,CAAC,CACE,CACP,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5H,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}