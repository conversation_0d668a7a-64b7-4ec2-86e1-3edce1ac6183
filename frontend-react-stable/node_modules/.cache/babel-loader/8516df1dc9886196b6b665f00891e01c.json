{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nfunction _default(interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "interpolator", "n", "samples", "Array", "i"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/quantize.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nfunction _default(interpolator, n) {\n  var samples = new Array(n);\n\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n\n  return samples;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,YAAY,EAAEC,CAAC,EAAE;EACjC,IAAIC,OAAO,GAAG,IAAIC,KAAK,CAACF,CAAC,CAAC;EAE1B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,GAAGJ,YAAY,CAACI,CAAC,IAAIH,CAAC,GAAG,CAAC,CAAC,CAAC;EAElE,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}