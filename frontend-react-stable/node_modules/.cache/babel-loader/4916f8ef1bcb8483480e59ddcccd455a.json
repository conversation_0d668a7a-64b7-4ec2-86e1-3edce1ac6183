{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = descending;\nfunction descending(a, b) {\n  return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "descending", "a", "b", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/descending.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = descending;\n\nfunction descending(a, b) {\n  return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,UAAU;AAE5B,SAASA,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,OAAOD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,GAAGC,GAAG,GAAGD,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAG,CAAC,GAAGE,GAAG;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}