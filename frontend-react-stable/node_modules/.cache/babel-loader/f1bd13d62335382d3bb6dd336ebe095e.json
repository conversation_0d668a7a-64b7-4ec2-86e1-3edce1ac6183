{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Radio, Upload, Input, Select, Button, Typography, Space, Divider, message, Spin, Table, Alert, Statistic, Row, Col, Progress } from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Dragger\n} = Upload;\nconst {\n  Option\n} = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay = ({\n  result\n}) => {\n  // 表格列定义 - 与Streamlit版本保持一致\n  const predictionColumns = [{\n    title: '时间戳',\n    dataIndex: 'timestamp',\n    key: 'timestamp',\n    width: 180\n  }, {\n    title: '真实流量 (pps)',\n    dataIndex: 'packets_per_sec',\n    key: 'packets_per_sec',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '平滑后真实流量',\n    dataIndex: 'packets_per_sec_smooth',\n    key: 'packets_per_sec_smooth',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '模型预测趋势',\n    dataIndex: 'pred_smooth',\n    key: 'pred_smooth',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '动态阈值',\n    dataIndex: 'threshold',\n    key: 'threshold',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '异常状态',\n    dataIndex: 'is_anomaly',\n    key: 'is_anomaly',\n    render: isAnomaly => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: isAnomaly ? '#ff4d4f' : '#52c41a'\n      },\n      children: isAnomaly ? '🔴 异常' : '🟢 正常'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",\n          value: result.suggested_threshold,\n          precision: 2,\n          valueStyle: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), result.suggested_threshold && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginTop: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u68C0\\u6D4B\\u5230\\u7684\\u5F02\\u5E38\\u70B9\\u6570\\u91CF\",\n          value: result.anomaly_count,\n          valueStyle: {\n            color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: \"\\u8FD9\\u662F\\u57FA\\u4E8E\\u56FE\\u4E2D\\u7684\\u52A8\\u6001\\u9608\\u503C\\uFF08\\u7EA2\\u8272\\u865A\\u7EBF\\uFF09\\u68C0\\u6D4B\\u51FA\\u7684\\u3001\\u6D41\\u91CF\\u8D85\\u8FC7\\u9608\\u503C\\u7684\\u5177\\u4F53\\u65F6\\u95F4\\u70B9\\u6570\\u91CF\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), result.predictions && result.predictions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u4E0B\\u56FE\\u5C55\\u793A\\u4E86\\u771F\\u5B9E\\u6D41\\u91CF\\u3001\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\\u548C\\u7528\\u4E8E\\u5B9E\\u65F6\\u68C0\\u6D4B\\u7684\\u52A8\\u6001\\u9608\\u503C\\u3002\",\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u9884\\u6D4B\\u7ED3\\u679C\\u56FE\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: 400,\n          marginTop: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: result.predictions // 显示所有数据点\n            ,\n            margin: {\n              top: 5,\n              right: 30,\n              left: 20,\n              bottom: 5\n            },\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"timestamp\",\n              tick: {\n                fontSize: 10\n              },\n              interval: \"preserveStartEnd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              labelFormatter: value => `时间: ${value}`,\n              formatter: (value, name) => [Number(value === null || value === void 0 ? void 0 : value.toFixed(2)), name]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"packets_per_sec_smooth\",\n              stroke: \"#007bff\",\n              strokeWidth: 2,\n              dot: false,\n              name: \"\\u771F\\u5B9E\\u6D41\\u91CF (\\u5E73\\u6ED1\\u540E)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"pred_smooth\",\n              stroke: \"#ffa500\",\n              strokeWidth: 2,\n              strokeDasharray: \"5 5\",\n              dot: false,\n              name: \"\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"threshold\",\n              stroke: \"#ff0000\",\n              strokeWidth: 2,\n              strokeDasharray: \"3 3\",\n              dot: false,\n              name: \"\\u52A8\\u6001\\u6E05\\u6D17\\u9608\\u503C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), (result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 5,\n        children: \"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [result.duration_seconds !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9884\\u6D4B\\u8017\\u65F6\",\n            value: result.duration_seconds,\n            precision: 2,\n            suffix: \"\\u79D2\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 15\n        }, this), result.cpu_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"CPU\\u4F7F\\u7528\\u7387\",\n            value: result.cpu_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 15\n        }, this), result.memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5185\\u5B58\\u4F7F\\u7528\",\n            value: result.memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this), result.gpu_memory_mb !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5185\\u5B58\",\n            value: result.gpu_memory_mb,\n            precision: 1,\n            suffix: \"MB\",\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 15\n        }, this), result.gpu_utilization_percent !== undefined && /*#__PURE__*/_jsxDEV(Col, {\n          flex: \"auto\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"GPU\\u5229\\u7528\\u7387\",\n            value: result.gpu_utilization_percent,\n            precision: 1,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#eb2f96'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: \"\\u9884\\u6D4B\\u8BE6\\u60C5\\uFF08\\u524D100\\u6761\\uFF09\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: predictionColumns,\n        dataSource: result.predictions.slice(0, 100) // 只显示前100条\n        ,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        },\n        size: \"small\",\n        style: {\n          marginTop: 8\n        },\n        rowKey: (_, index) => `${index}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\n_c = PredictionResultDisplay;\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\nconst ModelPredictionPage = () => {\n  _s();\n  const [dataSource, setDataSource] = useState('upload');\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState([]);\n  const [selectedModels, setSelectedModels] = useState([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState('');\n  const [selectedProt, setSelectedProt] = useState('');\n  const [selectedDatatype, setSelectedDatatype] = useState('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const {\n    submitPredictionTask,\n    getCompletedTasksByType,\n    fetchCompletedTasks\n  } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = taskId => {\n    console.log('🎯 选择异步预测任务:', taskId);\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n    console.log('📄 选中的任务:', selectedTask);\n    if (selectedTask && selectedTask.result) {\n      console.log('📊 任务结果:', selectedTask.result);\n      console.log('📊 预测数据:', selectedTask.result.predictions);\n      console.log('📊 异常数量:', selectedTask.result.anomaly_count);\n\n      // 转换异步预测结果为与同步预测相同的格式\n      const asyncResult = {\n        predictions: selectedTask.result.predictions || [],\n        anomaly_count: selectedTask.result.anomaly_count || 0,\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n      console.log('✅ 转换后的异步预测结果:', asyncResult);\n      setAsyncPredictionResults([asyncResult]);\n    } else {\n      console.log('❌ 任务结果为空或不存在');\n      console.log('   selectedTask:', selectedTask);\n      console.log('   selectedTask.result:', selectedTask === null || selectedTask === void 0 ? void 0 : selectedTask.result);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    console.log('🔄 检查是否需要自动选择任务...');\n    console.log('   completedPredictionTasks.length:', completedPredictionTasks.length);\n    console.log('   selectedAsyncTaskId:', selectedAsyncTaskId);\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      console.log('🎯 自动选择最新任务:', latestTask.task_id);\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 监控异步预测结果状态\n  useEffect(() => {\n    console.log('🎨 异步预测结果状态更新:', asyncPredictionResults);\n    console.log('🎨 结果数量:', asyncPredictionResults.length);\n    if (asyncPredictionResults.length > 0) {\n      console.log('🎨 第一个结果:', asyncPredictionResults[0]);\n    }\n  }, [asyncPredictionResults]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async modelFile => {\n    if (!modelFile || !modelDir) return;\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = modelFile => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) {\n      // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: info => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    }\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict = [];\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels = [];\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error) {\n          var _error$response4, _error$response4$data;\n          message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message}`);\n        }\n      }\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n      modelsToPredict = validModels;\n    }\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults = [];\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round(i / modelsToPredict.length * 90));\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n        const formData = new FormData();\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n        const response = await modelPredictionAPI.predict(formData);\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent\n          });\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      message.error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : csvDir && selectedCsvFile;\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      style: {\n        fontSize: '20px',\n        fontWeight: 600,\n        marginBottom: '8px'\n      },\n      children: \"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: dataSource,\n            onChange: e => setDataSource(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"upload\",\n              children: \"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"local\",\n              children: \"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this), dataSource === 'upload' && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Dragger, {\n            ...uploadProps,\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this), dataSource === 'local' && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n              compact: true,\n              style: {\n                marginTop: 8,\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                value: csvDir,\n                onChange: e => setCsvDir(e.target.value),\n                placeholder: \"\\u4F8B\\u5982: /data/output\",\n                style: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                onClick: fetchCsvFiles,\n                loading: csvFilesLoading,\n                disabled: !csvDir,\n                style: {\n                  marginLeft: 8\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: csvFilesLoading,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedCsvFile,\n                onChange: setSelectedCsvFile,\n                placeholder: \"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",\n                style: {\n                  width: '100%',\n                  marginTop: 8\n                },\n                loading: csvFilesLoading,\n                children: availableCsvFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                  value: file,\n                  children: file\n                }, file, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6A21\\u578B\\u9009\\u62E9\",\n      className: \"function-card\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input.Group, {\n            compact: true,\n            style: {\n              marginTop: 8,\n              display: 'flex'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              value: modelDir,\n              onChange: e => setModelDir(e.target.value),\n              placeholder: \"\\u4F8B\\u5982: /data/output\",\n              style: {\n                flex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: fetchModelFiles,\n              loading: modelsLoading,\n              disabled: !modelDir,\n              style: {\n                marginLeft: 8\n              },\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: predictionMode,\n            onChange: e => setPredictionMode(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: \"single\",\n              children: \"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: \"multiple\",\n              children: \"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this), predictionMode === 'single' ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedModelFile,\n              onChange: handleModelFileChange,\n              placeholder: \"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 15\n          }, this), selectedModelFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Spin, {\n                  spinning: matchingFilesLoading,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: 8,\n                      padding: 12,\n                      backgroundColor: '#f5f5f5',\n                      borderRadius: 4\n                    },\n                    children: matchingFilesLoading ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u53C2\\u6570\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 861,\n                          columnNumber: 34\n                        }, this), \" \", selectedParamsFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 861,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 862,\n                          columnNumber: 34\n                        }, this), \" \", selectedScalerFile || '未匹配']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 862,\n                        columnNumber: 31\n                      }, this), !showManualSelection && selectedProt && selectedDatatype && /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u534F\\u8BAE:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 865,\n                            columnNumber: 38\n                          }, this), \" \", selectedProt]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 865,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"\\u6570\\u636E\\u7C7B\\u578B:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 866,\n                            columnNumber: 38\n                          }, this), \" \", selectedDatatype]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 866,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 21\n              }, this), showManualSelection && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Space, {\n                    direction: \"vertical\",\n                    style: {\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedProt,\n                      onChange: setSelectedProt,\n                      placeholder: \"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",\n                      style: {\n                        width: '100%'\n                      },\n                      children: protocolOptions.map(prot => /*#__PURE__*/_jsxDEV(Option, {\n                        value: prot,\n                        children: prot\n                      }, prot, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 29\n                    }, this), selectedProt && /*#__PURE__*/_jsxDEV(Select, {\n                      value: selectedDatatype,\n                      onChange: setSelectedDatatype,\n                      placeholder: `选择与模型对应的 ${selectedProt} 数据类型`,\n                      style: {\n                        width: '100%'\n                      },\n                      children: (datatypeOptions[selectedProt] || []).map(datatype => /*#__PURE__*/_jsxDEV(Option, {\n                        value: datatype,\n                        children: datatype\n                      }, datatype, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 901,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: modelsLoading,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              mode: \"multiple\",\n              value: selectedModels,\n              onChange: setSelectedModels,\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",\n              style: {\n                width: '100%',\n                marginTop: 8\n              },\n              loading: modelsLoading,\n              children: availablePthFiles.map(file => /*#__PURE__*/_jsxDEV(Option, {\n                value: file,\n                children: file\n              }, file, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 13\n        }, this), availablePthFiles.length === 0 && !modelsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",\n          description: \"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 796,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      title: \"\\u9884\\u6D4B\\u6A21\\u5F0F\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"middle\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: useAsyncPrediction,\n            onChange: e => setUseAsyncPrediction(e.target.value),\n            style: {\n              marginTop: 8\n            },\n            disabled: predictionMode === 'multiple' // 多模型预测暂不支持异步\n            ,\n            children: [/*#__PURE__*/_jsxDEV(Radio, {\n              value: true,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Radio, {\n              value: false,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u540C\\u6B65\\u9884\\u6D4B\", /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: 12\n                  },\n                  children: \"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this), useAsyncPrediction && predictionMode === 'single' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 19\n            }, this), \"\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4FA7\\u8FB9\\u680F\\u83DC\\u5355 \\u2192 \\u4EFB\\u52A1\\u7BA1\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 33\n            }, this), \" \\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 13\n        }, this), predictionMode === 'multiple' && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\",\n          description: \"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\\u76EE\\u524D\\u4EC5\\u652F\\u6301\\u540C\\u6B65\\u6A21\\u5F0F\\uFF0C\\u9884\\u6D4B\\u8FC7\\u7A0B\\u4E2D\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",\n          type: \"warning\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 949,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"function-card\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 17\n        }, this),\n        onClick: handleStartPrediction,\n        loading: predicting,\n        disabled: !isFormValid(),\n        className: \"action-button\",\n        children: predicting ? '正在预测...' : '开始预测与检测'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1006,\n        columnNumber: 9\n      }, this), predicting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-section\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: progress,\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1005,\n      columnNumber: 7\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      children: results.length > 1 ?\n      /*#__PURE__*/\n      // 多模型结果展示 - 与Streamlit版本一致\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",\n            value: selectedResultIndex,\n            onChange: value => setSelectedResultIndex(value),\n            children: results.map((result, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: index,\n              children: result.model_name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 15\n        }, this), results[selectedResultIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: [\"\\u6A21\\u578B: \", results[selectedResultIndex].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: results[selectedResultIndex]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // 单模型结果展示\n      _jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", results[0].model_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n          result: results[0]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1061,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1029,\n      columnNumber: 9\n    }, this), completedPredictionTasks.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u7ED3\\u679C\",\n      className: \"function-card\",\n      style: {\n        marginTop: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u5F02\\u6B65\\u9884\\u6D4B\\u5DF2\\u5B8C\\u6210\",\n        description: \"\\u4EE5\\u4E0B\\u662F\\u540E\\u53F0\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u7ED3\\u679C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u67E5\\u770B\\u9884\\u6D4B\\u6570\\u636E\\u548C\\u5F02\\u5E38\\u68C0\\u6D4B\\u62A5\\u544A\\u3002\",\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1072,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        size: \"large\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9009\\u62E9\\u9884\\u6D4B\\u4EFB\\u52A1\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1082,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedAsyncTaskId,\n            onChange: handleAsyncTaskSelect,\n            style: {\n              width: '100%',\n              marginTop: 8\n            },\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u9884\\u6D4B\\u4EFB\\u52A1\",\n            children: completedPredictionTasks.map(task => /*#__PURE__*/_jsxDEV(Option, {\n              value: task.task_id,\n              children: task.task_id.includes('_') ? `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` : `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n            }, task.task_id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 13\n        }, this), asyncPredictionResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: [\"\\u9884\\u6D4B\\u7ED3\\u679C - \", asyncPredictionResults[0].model_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(PredictionResultDisplay, {\n            result: asyncPredictionResults[0]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1071,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 709,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelPredictionPage, \"HDB6RZAhm3tsxFlTMzcbU6nEXVI=\", false, function () {\n  return [useTaskManager];\n});\n_c2 = ModelPredictionPage;\nexport default ModelPredictionPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PredictionResultDisplay\");\n$RefreshReg$(_c2, \"ModelPredictionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "Table", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelPredictionAPI", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "result", "predictionColumns", "title", "dataIndex", "key", "width", "render", "value", "toFixed", "isAnomaly", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "span", "suggested_threshold", "precision", "valueStyle", "type", "showIcon", "marginTop", "anomaly_count", "fontSize", "predictions", "length", "strong", "height", "data", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "interval", "labelFormatter", "formatter", "name", "Number", "stroke", "strokeWidth", "dot", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "columns", "dataSource", "slice", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "size", "<PERSON><PERSON><PERSON>", "_", "index", "_c", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "_s", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "submitPredictionTask", "getCompletedTasksByType", "fetchCompletedTasks", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedPredictionTasks", "handleAsyncTaskSelect", "taskId", "console", "log", "selectedTask", "find", "task", "task_id", "asyncResult", "model_name", "latestTask", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "allResults", "i", "model", "Math", "round", "predict", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "fontWeight", "className", "direction", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "map", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status", "includes", "split", "Date", "updated_at", "created_at", "toLocaleString", "substring", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Table,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n  // 表格列定义 - 与Streamlit版本保持一致\n  const predictionColumns = [\n    {\n      title: '时间戳',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 180,\n    },\n    {\n      title: '真实流量 (pps)',\n      dataIndex: 'packets_per_sec',\n      key: 'packets_per_sec',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '平滑后真实流量',\n      dataIndex: 'packets_per_sec_smooth',\n      key: 'packets_per_sec_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '模型预测趋势',\n      dataIndex: 'pred_smooth',\n      key: 'pred_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '动态阈值',\n      dataIndex: 'threshold',\n      key: 'threshold',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '异常状态',\n      dataIndex: 'is_anomaly',\n      key: 'is_anomaly',\n      render: (isAnomaly: boolean) => (\n        <span style={{ color: isAnomaly ? '#ff4d4f' : '#52c41a' }}>\n          {isAnomaly ? '🔴 异常' : '🟢 正常'}\n        </span>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={12}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n        <Col span={12}>\n          <Statistic\n            title=\"检测到的异常点数量\"\n            value={result.anomaly_count}\n            valueStyle={{ color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a' }}\n          />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            这是基于图中的动态阈值（红色虚线）检测出的、流量超过阈值的具体时间点数量。\n          </Text>\n        </Col>\n      </Row>\n\n      {/* 预测图表 - 与Streamlit版本保持一致 */}\n      {result.predictions && result.predictions.length > 0 && (\n        <div style={{ marginBottom: 24 }}>\n          <Alert\n            message=\"下图展示了真实流量、模型预测趋势和用于实时检测的动态阈值。\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Text strong>预测结果图表</Text>\n          <div style={{ height: 400, marginTop: 8 }}>\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart\n                data={result.predictions} // 显示所有数据点\n                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n              >\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis\n                  dataKey=\"timestamp\"\n                  tick={{ fontSize: 10 }}\n                  interval=\"preserveStartEnd\"\n                />\n                <YAxis />\n                <Tooltip\n                  labelFormatter={(value) => `时间: ${value}`}\n                  formatter={(value: number, name: string) => [Number(value?.toFixed(2)), name]}\n                />\n                <Legend />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"packets_per_sec_smooth\"\n                  stroke=\"#007bff\"\n                  strokeWidth={2}\n                  dot={false}\n                  name=\"真实流量 (平滑后)\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"pred_smooth\"\n                  stroke=\"#ffa500\"\n                  strokeWidth={2}\n                  strokeDasharray=\"5 5\"\n                  dot={false}\n                  name=\"模型预测趋势\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"threshold\"\n                  stroke=\"#ff0000\"\n                  strokeWidth={2}\n                  strokeDasharray=\"3 3\"\n                  dot={false}\n                  name=\"动态清洗阈值\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n      <div>\n        <Text strong>预测详情（前100条）：</Text>\n        <Table\n          columns={predictionColumns}\n          dataSource={result.predictions.slice(0, 100)} // 只显示前100条\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n          size=\"small\"\n          style={{ marginTop: 8 }}\n          rowKey={(_, index) => `${index}`}\n        />\n      </div>\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  anomaly_count: number;\n  suggested_threshold: number;\n  predictions: Array<{\n    timestamp: string;\n    packets_per_sec: number;\n    packets_per_sec_smooth: number;\n    pred_smooth: number;\n    threshold: number;\n    is_anomaly: boolean;\n  }>;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const { submitPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    console.log('🎯 选择异步预测任务:', taskId);\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n    console.log('📄 选中的任务:', selectedTask);\n\n    if (selectedTask && selectedTask.result) {\n      console.log('📊 任务结果:', selectedTask.result);\n      console.log('📊 预测数据:', selectedTask.result.predictions);\n      console.log('📊 异常数量:', selectedTask.result.anomaly_count);\n\n      // 转换异步预测结果为与同步预测相同的格式\n      const asyncResult: PredictionResult = {\n        predictions: selectedTask.result.predictions || [],\n        anomaly_count: selectedTask.result.anomaly_count || 0,\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n\n      console.log('✅ 转换后的异步预测结果:', asyncResult);\n      setAsyncPredictionResults([asyncResult]);\n    } else {\n      console.log('❌ 任务结果为空或不存在');\n      console.log('   selectedTask:', selectedTask);\n      console.log('   selectedTask.result:', selectedTask?.result);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    console.log('🔄 检查是否需要自动选择任务...');\n    console.log('   completedPredictionTasks.length:', completedPredictionTasks.length);\n    console.log('   selectedAsyncTaskId:', selectedAsyncTaskId);\n\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      console.log('🎯 自动选择最新任务:', latestTask.task_id);\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 监控异步预测结果状态\n  useEffect(() => {\n    console.log('🎨 异步预测结果状态更新:', asyncPredictionResults);\n    console.log('🎨 结果数量:', asyncPredictionResults.length);\n    if (asyncPredictionResults.length > 0) {\n      console.log('🎨 第一个结果:', asyncPredictionResults[0]);\n    }\n  }, [asyncPredictionResults]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测数据和异常检测报告。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                <PredictionResultDisplay result={asyncPredictionResults[0]} />\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,QACH,MAAM;AACb,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAC7G,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG5B,UAAU;AAClC,MAAM;EAAE6B;AAAQ,CAAC,GAAGjC,MAAM;AAC1B,MAAM;EAAEkC;AAAO,CAAC,GAAGhC,MAAM;;AAEzB;AACA,MAAMiC,uBAA+D,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EACtF;EACA,MAAMC,iBAAiB,GAAG,CACxB;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,wBAAwB;IACnCC,GAAG,EAAE,wBAAwB;IAC7BE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBE,MAAM,EAAGC,KAAa,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EAC7C,CAAC,EACD;IACEN,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBE,MAAM,EAAGG,SAAkB,iBACzBjB,OAAA;MAAMkB,KAAK,EAAE;QAAEC,KAAK,EAAEF,SAAS,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAG,QAAA,EACvDH,SAAS,GAAG,OAAO,GAAG;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B;EAEV,CAAC,CACF;EAED,oBACExB,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAAChB,GAAG;MAACyC,MAAM,EAAE,EAAG;MAACP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC3CpB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,8DAAiB;UACvBK,KAAK,EAAEP,MAAM,CAACoB,mBAAoB;UAClCC,SAAS,EAAE,CAAE;UACbC,UAAU,EAAE;YAAEX,KAAK,EAAE;UAAU;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACDhB,MAAM,CAACoB,mBAAmB,iBACzB5B,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,kNAAwC;UAChDoD,IAAI,EAAC,SAAS;UACdC,QAAQ;UACRd,KAAK,EAAE;YAAEe,SAAS,EAAE;UAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxB,OAAA,CAACf,GAAG;QAAC0C,IAAI,EAAE,EAAG;QAAAP,QAAA,gBACZpB,OAAA,CAACjB,SAAS;UACR2B,KAAK,EAAC,wDAAW;UACjBK,KAAK,EAAEP,MAAM,CAAC0B,aAAc;UAC5BJ,UAAU,EAAE;YAAEX,KAAK,EAAEX,MAAM,CAAC0B,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG;UAAU;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACFxB,OAAA,CAACI,IAAI;UAAC2B,IAAI,EAAC,WAAW;UAACb,KAAK,EAAE;YAAEiB,QAAQ,EAAE;UAAG,CAAE;UAAAf,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhB,MAAM,CAAC4B,WAAW,IAAI5B,MAAM,CAAC4B,WAAW,CAACC,MAAM,GAAG,CAAC,iBAClDrC,OAAA;MAAKkB,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC/BpB,OAAA,CAAClB,KAAK;QACJH,OAAO,EAAC,gLAA+B;QACvCoD,IAAI,EAAC,MAAM;QACXC,QAAQ;QACRd,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAG;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFxB,OAAA,CAACI,IAAI;QAACkC,MAAM;QAAAlB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1BxB,OAAA;QAAKkB,KAAK,EAAE;UAAEqB,MAAM,EAAE,GAAG;UAAEN,SAAS,EAAE;QAAE,CAAE;QAAAb,QAAA,eACxCpB,OAAA,CAACJ,mBAAmB;UAACiB,KAAK,EAAC,MAAM;UAAC0B,MAAM,EAAC,MAAM;UAAAnB,QAAA,eAC7CpB,OAAA,CAACX,SAAS;YACRmD,IAAI,EAAEhC,MAAM,CAAC4B,WAAY,CAAC;YAAA;YAC1BK,MAAM,EAAE;cAAEC,GAAG,EAAE,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,IAAI,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAEnDpB,OAAA,CAACP,aAAa;cAACqD,eAAe,EAAC;YAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCxB,OAAA,CAACT,KAAK;cACJwD,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAE;gBAAEb,QAAQ,EAAE;cAAG,CAAE;cACvBc,QAAQ,EAAC;YAAkB;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxB,OAAA,CAACR,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTxB,OAAA,CAACN,OAAO;cACNwD,cAAc,EAAGnC,KAAK,IAAK,OAAOA,KAAK,EAAG;cAC1CoC,SAAS,EAAEA,CAACpC,KAAa,EAAEqC,IAAY,KAAK,CAACC,MAAM,CAACtC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAEoC,IAAI;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACFxB,OAAA,CAACL,MAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,wBAAwB;cAChCO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfC,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAY;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACFxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,aAAa;cACrBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfT,eAAe,EAAC,KAAK;cACrBU,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFxB,OAAA,CAACV,IAAI;cACHyC,IAAI,EAAC,UAAU;cACfgB,OAAO,EAAC,WAAW;cACnBO,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfT,eAAe,EAAC,KAAK;cACrBU,GAAG,EAAE,KAAM;cACXJ,IAAI,EAAC;YAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAAChB,MAAM,CAACiD,gBAAgB,KAAKC,SAAS,IAAIlD,MAAM,CAACmD,WAAW,KAAKD,SAAS,IAAIlD,MAAM,CAACoD,SAAS,KAAKF,SAAS,IAAIlD,MAAM,CAACqD,aAAa,KAAKH,SAAS,IAAIlD,MAAM,CAACsD,uBAAuB,KAAKJ,SAAS,kBACjM1D,OAAA;MAAKkB,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAC/BpB,OAAA,CAACG,KAAK;QAAC4D,KAAK,EAAE,CAAE;QAAA3C,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BxB,OAAA,CAAChB,GAAG;QAACyC,MAAM,EAAE,EAAG;QAAAL,QAAA,GACbZ,MAAM,CAACiD,gBAAgB,KAAKC,SAAS,iBACpC1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,0BAAM;YACZK,KAAK,EAAEP,MAAM,CAACiD,gBAAiB;YAC/B5B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,QAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACmD,WAAW,KAAKD,SAAS,iBAC/B1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,uBAAQ;YACdK,KAAK,EAAEP,MAAM,CAACmD,WAAY;YAC1B9B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,GAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACoD,SAAS,KAAKF,SAAS,iBAC7B1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,0BAAM;YACZK,KAAK,EAAEP,MAAM,CAACoD,SAAU;YACxB/B,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,IAAI;YACXnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACqD,aAAa,KAAKH,SAAS,iBACjC1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,iBAAO;YACbK,KAAK,EAAEP,MAAM,CAACqD,aAAc;YAC5BhC,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,IAAI;YACXnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhB,MAAM,CAACsD,uBAAuB,KAAKJ,SAAS,iBAC3C1D,OAAA,CAACf,GAAG;UAAC+E,IAAI,EAAC,MAAM;UAAA5C,QAAA,eACdpB,OAAA,CAACjB,SAAS;YACR2B,KAAK,EAAC,uBAAQ;YACdK,KAAK,EAAEP,MAAM,CAACsD,uBAAwB;YACtCjC,SAAS,EAAE,CAAE;YACboC,MAAM,EAAC,GAAG;YACVnC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDxB,OAAA;MAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;QAACkC,MAAM;QAAAlB,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChCxB,OAAA,CAACnB,KAAK;QACJqF,OAAO,EAAEzD,iBAAkB;QAC3B0D,UAAU,EAAE3D,MAAM,CAAC4B,WAAW,CAACgC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAE,CAAC;QAAA;QAC9CC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,IAAI,EAAC,OAAO;QACZzD,KAAK,EAAE;UAAEe,SAAS,EAAE;QAAE,CAAE;QACxB2C,MAAM,EAAEA,CAACC,CAAC,EAAEC,KAAK,KAAK,GAAGA,KAAK;MAAG;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAuD,EAAA,GA5NMxE,uBAA+D;AA6NrE,MAAMyE,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAC9C,MAAMC,eAAe,GAAG;EACtBC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;EACjEC,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACnCC,IAAI,EAAE,CAAC,KAAK;AACd,CAAC;AAsBD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACnB,UAAU,EAAEoB,aAAa,CAAC,GAAGvH,QAAQ,CAAqB,QAAQ,CAAC;EAC1E,MAAM,CAACwH,YAAY,EAAEC,eAAe,CAAC,GAAGzH,QAAQ,CAAM,IAAI,CAAC;EAC3D,MAAM,CAAC0H,MAAM,EAAEC,SAAS,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7H,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAAC8H,eAAe,EAAEC,kBAAkB,CAAC,GAAG/H,QAAQ,CAAS,EAAE,CAAC;EAClE,MAAM,CAACgI,eAAe,EAAEC,kBAAkB,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkI,QAAQ,EAAEC,WAAW,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrI,QAAQ,CAAW,EAAE,CAAC;EACxE,MAAM,CAACsI,cAAc,EAAEC,iBAAiB,CAAC,GAAGvI,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACwI,aAAa,EAAEC,gBAAgB,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0I,cAAc,EAAEC,iBAAiB,CAAC,GAAG3I,QAAQ,CAAwB,QAAQ,CAAC;;EAErF;EACA,MAAM,CAAC4I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7I,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAAC8I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/I,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAACgJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjJ,QAAQ,CAAS,EAAE,CAAC;EACxE,MAAM,CAACkJ,YAAY,EAAEC,eAAe,CAAC,GAAGnJ,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACoJ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrJ,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACsJ,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvJ,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACwJ,UAAU,EAAEC,aAAa,CAAC,GAAGzJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0J,QAAQ,EAAEC,WAAW,CAAC,GAAG3J,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC4J,OAAO,EAAEC,UAAU,CAAC,GAAG7J,QAAQ,CAAqB,EAAE,CAAC;EAC9D,MAAM,CAAC8J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/J,QAAQ,CAAS,CAAC,CAAC;EACzE,MAAM,CAACgK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjK,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM;IAAEkK,oBAAoB;IAAEC,uBAAuB;IAAEC;EAAoB,CAAC,GAAGtI,cAAc,CAAC,CAAC;EAC/F,MAAM,CAACuI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpE;EACA,MAAM,CAACuK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxK,QAAQ,CAAqB,EAAE,CAAC;EAC5F,MAAM,CAACyK,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1K,QAAQ,CAAS,EAAE,CAAC;;EAE1E;EACA,MAAM2K,wBAAwB,GAAGR,uBAAuB,CAAC,YAAY,CAAC;;EAEtE;EACA,MAAMS,qBAAqB,GAAIC,MAAc,IAAK;IAChDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,MAAM,CAAC;IACnCH,sBAAsB,CAACG,MAAM,CAAC;IAC9B,MAAMG,YAAY,GAAGL,wBAAwB,CAACM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKN,MAAM,CAAC;IACnFC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,YAAY,CAAC;IAEtC,IAAIA,YAAY,IAAIA,YAAY,CAACxI,MAAM,EAAE;MACvCsI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,YAAY,CAACxI,MAAM,CAAC;MAC5CsI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,YAAY,CAACxI,MAAM,CAAC4B,WAAW,CAAC;MACxD0G,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,YAAY,CAACxI,MAAM,CAAC0B,aAAa,CAAC;;MAE1D;MACA,MAAMkH,WAA6B,GAAG;QACpChH,WAAW,EAAE4G,YAAY,CAACxI,MAAM,CAAC4B,WAAW,IAAI,EAAE;QAClDF,aAAa,EAAE8G,YAAY,CAACxI,MAAM,CAAC0B,aAAa,IAAI,CAAC;QACrDN,mBAAmB,EAAEoH,YAAY,CAACxI,MAAM,CAACoB,mBAAmB,IAAI,CAAC;QACjEyH,UAAU,EAAEL,YAAY,CAACxI,MAAM,CAAC6I,UAAU,IAAI,MAAM;QACpD5F,gBAAgB,EAAEuF,YAAY,CAACxI,MAAM,CAACiD,gBAAgB;QACtDE,WAAW,EAAEqF,YAAY,CAACxI,MAAM,CAACmD,WAAW;QAC5CC,SAAS,EAAEoF,YAAY,CAACxI,MAAM,CAACoD,SAAS;QACxCC,aAAa,EAAEmF,YAAY,CAACxI,MAAM,CAACqD,aAAa;QAChDC,uBAAuB,EAAEkF,YAAY,CAACxI,MAAM,CAACsD;MAC/C,CAAC;MAEDgF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,WAAW,CAAC;MACzCZ,yBAAyB,CAAC,CAACY,WAAW,CAAC,CAAC;IAC1C,CAAC,MAAM;MACLN,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3BD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,YAAY,CAAC;MAC7CF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAExI,MAAM,CAAC;IAC9D;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACdmK,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAnK,SAAS,CAAC,MAAM;IACd6K,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjCD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEJ,wBAAwB,CAACtG,MAAM,CAAC;IACnFyG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEN,mBAAmB,CAAC;IAE3D,IAAIE,wBAAwB,CAACtG,MAAM,GAAG,CAAC,IAAI,CAACoG,mBAAmB,EAAE;MAC/D,MAAMa,UAAU,GAAGX,wBAAwB,CAACA,wBAAwB,CAACtG,MAAM,GAAG,CAAC,CAAC;MAChFyG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEO,UAAU,CAACH,OAAO,CAAC;MAC/CP,qBAAqB,CAACU,UAAU,CAACH,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACR,wBAAwB,EAAEF,mBAAmB,CAAC,CAAC;;EAEnD;EACAxK,SAAS,CAAC,MAAM;IACd6K,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAER,sBAAsB,CAAC;IACrDO,OAAO,CAACC,GAAG,CAAC,UAAU,EAAER,sBAAsB,CAAClG,MAAM,CAAC;IACtD,IAAIkG,sBAAsB,CAAClG,MAAM,GAAG,CAAC,EAAE;MACrCyG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAER,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACrD;EACF,CAAC,EAAE,CAACA,sBAAsB,CAAC,CAAC;;EAE5B;EACA,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC7D,MAAM,EAAE;IAEbO,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMuD,QAAQ,GAAG,MAAM3J,kBAAkB,CAAC4J,YAAY,CAAC/D,MAAM,CAAC;MAC9DG,oBAAoB,CAAC2D,QAAQ,CAAChH,IAAI,CAACkH,KAAK,IAAI,EAAE,CAAC;IACjD,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBlL,OAAO,CAACgL,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpH,IAAI,cAAAqH,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,aAAa,CAAC;MAC5DjE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM8D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC7D,QAAQ,EAAE;IAEfO,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAM+C,QAAQ,GAAG,MAAM3J,kBAAkB,CAACmK,cAAc,CAAC9D,QAAQ,CAAC;MAClEG,oBAAoB,CAACmD,QAAQ,CAAChH,IAAI,CAACyH,SAAS,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAON,KAAU,EAAE;MAAA,IAAAO,gBAAA,EAAAC,qBAAA;MACnBxL,OAAO,CAACgL,KAAK,CAAC,EAAAO,gBAAA,GAAAP,KAAK,CAACH,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1H,IAAI,cAAA2H,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAI,YAAY,CAAC;MAC3DzD,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM2D,cAAc,GAAG,MAAOC,SAAiB,IAAK;IAClD,IAAI,CAACA,SAAS,IAAI,CAACnE,QAAQ,EAAE;IAE7B+B,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI;MACF;MACA,MAAMuB,QAAQ,GAAG,MAAM3J,kBAAkB,CAACyK,gBAAgB,CAACD,SAAS,EAAEnE,QAAQ,CAAC;MAE/E,IAAIsD,QAAQ,CAAChH,IAAI,EAAE;QACjB,MAAM+H,aAAa,GAAGf,QAAQ,CAAChH,IAAI;;QAEnC;QACAuE,qBAAqB,CAACwD,aAAa,CAACC,eAAe,IAAI,EAAE,CAAC;QAC1DvD,qBAAqB,CAACsD,aAAa,CAACE,eAAe,IAAI,EAAE,CAAC;QAC1DtD,eAAe,CAACoD,aAAa,CAACG,QAAQ,IAAI,EAAE,CAAC;QAC7CrD,mBAAmB,CAACkD,aAAa,CAACI,QAAQ,IAAI,EAAE,CAAC;;QAEjD;QACA,IAAIJ,aAAa,CAACC,eAAe,IAAID,aAAa,CAACE,eAAe,EAAE;UAClE9L,OAAO,CAACiM,OAAO,CAAC,aAAa,CAAC;QAChC;QAEA,IAAIL,aAAa,CAACG,QAAQ,IAAIH,aAAa,CAACI,QAAQ,EAAE;UACpDhM,OAAO,CAACiM,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC;UAC5FpD,sBAAsB,CAAC,KAAK,CAAC;QAC/B,CAAC,MAAM;UACL5I,OAAO,CAACkM,OAAO,CAAC,4BAA4B,CAAC;UAC7CtD,sBAAsB,CAAC,IAAI,CAAC;QAC9B;MACF;IACF,CAAC,CAAC,OAAOoC,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnBpM,OAAO,CAACgL,KAAK,CAAC,EAAAmB,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtI,IAAI,cAAAuI,qBAAA,uBAApBA,qBAAA,CAAsBjB,MAAM,KAAI,UAAU,CAAC;MACzD;MACA,MAAMkB,kBAAkB,GAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACnElE,qBAAqB,CAAC,GAAGiE,kBAAkB,cAAc,CAAC;MAC1D/D,qBAAqB,CAAC,GAAG+D,kBAAkB,eAAe,CAAC;MAC3DzD,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,SAAS;MACRU,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMiD,qBAAqB,GAAIb,SAAiB,IAAK;IACnDxD,oBAAoB,CAACwD,SAAS,CAAC;IAC/B;IACAtD,qBAAqB,CAAC,EAAE,CAAC;IACzBE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,eAAe,CAAC,EAAE,CAAC;IACnBE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,sBAAsB,CAAC,KAAK,CAAC;;IAE7B;IACA6C,cAAc,CAACC,SAAS,CAAC;EAC3B,CAAC;;EAED;EACApM,SAAS,CAAC,MAAM;IACd,IAAIkG,UAAU,KAAK,OAAO,IAAIuB,MAAM,IAAIA,MAAM,CAACrD,MAAM,GAAG,CAAC,EAAE;MAAE;MAC3D,MAAM8I,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B7B,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAAChH,UAAU,EAAEuB,MAAM,CAAC,CAAC;EAExBzH,SAAS,CAAC,MAAM;IACd,IAAIiI,QAAQ,IAAIA,QAAQ,CAAC7D,MAAM,GAAG,CAAC,EAAE;MAAE;MACrC,MAAM8I,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BrB,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMsB,YAAY,CAACF,KAAK,CAAC;IAClC;IACA;EACF,CAAC,EAAE,CAACjF,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoF,WAAW,GAAG;IAClBlI,IAAI,EAAE,MAAM;IACZmI,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAGC,IAAS,IAAK;MACvB,IAAIA,IAAI,CAACC,QAAQ,CAACvJ,MAAM,GAAG,CAAC,EAAE;QAC5BoD,eAAe,CAACkG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLnG,eAAe,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;;EAED;EACA,MAAMoG,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAI1H,UAAU,KAAK,QAAQ,IAAI,CAACqB,YAAY,EAAE;MAC5C7G,OAAO,CAACgL,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;IAEA,IAAIxF,UAAU,KAAK,OAAO,KAAK,CAACuB,MAAM,IAAI,CAACI,eAAe,CAAC,EAAE;MAC3DnH,OAAO,CAACgL,KAAK,CAAC,UAAU,CAAC;MACzB;IACF;;IAEA;IACA,IAAImC,eAMF,GAAG,EAAE;IAEP,IAAIpF,cAAc,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAACE,iBAAiB,IAAI,CAACM,YAAY,IAAI,CAACE,gBAAgB,EAAE;QAC5DzI,OAAO,CAACgL,KAAK,CAAC,sBAAsB,CAAC;QACrC;MACF;MACAmC,eAAe,GAAG,CAAC;QACjBC,UAAU,EAAEnF,iBAAiB;QAC7BoF,WAAW,EAAElF,kBAAkB;QAC/BmF,WAAW,EAAEjF,kBAAkB;QAC/B0D,QAAQ,EAAExD,YAAY;QACtByD,QAAQ,EAAEvD;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAId,cAAc,CAACjE,MAAM,KAAK,CAAC,EAAE;QAC/B1D,OAAO,CAACgL,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;;MAEA;MACA,MAAMuC,WAMJ,GAAG,EAAE;MAEP,KAAK,MAAM7B,SAAS,IAAI/D,cAAc,EAAE;QACtC,IAAI;UACF,MAAMkD,QAAQ,GAAG,MAAM3J,kBAAkB,CAACyK,gBAAgB,CAACD,SAAS,EAAEnE,QAAQ,CAAC;UAC/E,IAAIsD,QAAQ,CAAChH,IAAI,EAAE;YACjB,MAAM+H,aAAa,GAAGf,QAAQ,CAAChH,IAAI;YACnC,MAAMwJ,WAAW,GAAGzB,aAAa,CAACC,eAAe;YACjD,MAAMyB,WAAW,GAAG1B,aAAa,CAACE,eAAe;YACjD,MAAMC,QAAQ,GAAGH,aAAa,CAACG,QAAQ;YACvC,MAAMC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;;YAEvC;YACA,IAAIqB,WAAW,IAAIC,WAAW,IAAIvB,QAAQ,IAAIC,QAAQ,EAAE;cACtDuB,WAAW,CAACC,IAAI,CAAC;gBACfJ,UAAU,EAAE1B,SAAS;gBACrB2B,WAAW;gBACXC,WAAW;gBACXvB,QAAQ;gBACRC;cACF,CAAC,CAAC;cACFhM,OAAO,CAACiM,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC;YACrE,CAAC,MAAM;cACLhM,OAAO,CAACkM,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC;YACxD;UACF,CAAC,MAAM;YACL1L,OAAO,CAACgL,KAAK,CAAC,QAAQU,SAAS,UAAU,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOV,KAAU,EAAE;UAAA,IAAAyC,gBAAA,EAAAC,qBAAA;UACnB1N,OAAO,CAACgL,KAAK,CAAC,QAAQU,SAAS,SAAS,EAAA+B,gBAAA,GAAAzC,KAAK,CAACH,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5J,IAAI,cAAA6J,qBAAA,uBAApBA,qBAAA,CAAsBvC,MAAM,KAAIH,KAAK,CAAChL,OAAO,EAAE,CAAC;QAC1F;MACF;MAEA,IAAIuN,WAAW,CAAC7J,MAAM,KAAK,CAAC,EAAE;QAC5B1D,OAAO,CAACgL,KAAK,CAAC,wCAAwC,CAAC;QACvD;MACF;MAEAmC,eAAe,GAAGI,WAAW;IAC/B;IAEAzE,aAAa,CAAC,IAAI,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,IAAIQ,kBAAkB,IAAI3B,cAAc,KAAK,QAAQ,EAAE;QACrD;QACA,MAAM4F,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAIpI,UAAU,KAAK,QAAQ,IAAIqB,YAAY,EAAE;UAC3C8G,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhH,YAAY,CAACiH,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE9G,MAAM,CAAC;UAClC4G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE1G,eAAe,CAAC;QACnD;QAEAwG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE5F,iBAAiB,CAAC;QACpD0F,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAE1F,kBAAkB,CAAC;QACtDwF,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAExF,kBAAkB,CAAC;QACtDsF,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEtF,YAAY,CAAC;QAC9CoF,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEpF,gBAAgB,CAAC;QACtDkF,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEtG,QAAQ,CAAC;QACtCoG,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEtG,QAAQ,CAAC;;QAE1C;QACA,MAAM2C,MAAM,GAAG,MAAMX,oBAAoB,CAACoE,QAAQ,CAAC;QAEnD,IAAIzD,MAAM,EAAE;UACVlK,OAAO,CAACiM,OAAO,CAAC,gCAAgC,CAAC;UACjD;UACAnD,aAAa,CAAC,KAAK,CAAC;UACpBE,WAAW,CAAC,CAAC,CAAC;QAChB;QAEA,OAAO,CAAC;MACV;;MAEA;MACA,MAAM+E,UAA8B,GAAG,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,eAAe,CAACzJ,MAAM,EAAEsK,CAAC,EAAE,EAAE;QAC/C,MAAMC,KAAK,GAAGd,eAAe,CAACa,CAAC,CAAC;;QAEhC;QACAhF,WAAW,CAACkF,IAAI,CAACC,KAAK,CAAEH,CAAC,GAAGb,eAAe,CAACzJ,MAAM,GAAI,EAAE,CAAC,CAAC;QAE1D,IAAIyJ,eAAe,CAACzJ,MAAM,GAAG,CAAC,EAAE;UAC9B1D,OAAO,CAACgN,IAAI,CAAC,UAAUgB,CAAC,GAAG,CAAC,IAAIb,eAAe,CAACzJ,MAAM,KAAKuK,KAAK,CAACb,UAAU,UAAU,CAAC;QACxF;QAEA,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAE/B,IAAIpI,UAAU,KAAK,QAAQ,IAAIqB,YAAY,EAAE;UAC3C;UACA8G,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEhH,YAAY,CAACiH,aAAa,CAAC;QACrD,CAAC,MAAM;UACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE9G,MAAM,CAAC;UAClC4G,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAE1G,eAAe,CAAC;QACnD;QAEAwG,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEI,KAAK,CAACb,UAAU,CAAC;QACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEI,KAAK,CAACZ,WAAW,CAAC;QACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,EAAEI,KAAK,CAACX,WAAW,CAAC;QACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEI,KAAK,CAAClC,QAAQ,CAAC;QAChD4B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEI,KAAK,CAACjC,QAAQ,CAAC;QACpD2B,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEtG,QAAQ,CAAC;QAE1C,MAAMsD,QAAQ,GAAG,MAAM3J,kBAAkB,CAACkN,OAAO,CAACT,QAAQ,CAAC;QAE3D,IAAI9C,QAAQ,CAAChH,IAAI,EAAE;UACjBkK,UAAU,CAACP,IAAI,CAAC;YACd9C,UAAU,EAAEG,QAAQ,CAAChH,IAAI,CAAC6G,UAAU,IAAI,GAAGuD,KAAK,CAAClC,QAAQ,IAAIkC,KAAK,CAACjC,QAAQ,EAAE;YAC7EzI,aAAa,EAAEsH,QAAQ,CAAChH,IAAI,CAACN,aAAa,IAAI,CAAC;YAC/CN,mBAAmB,EAAE4H,QAAQ,CAAChH,IAAI,CAACZ,mBAAmB,IAAI,CAAC;YAC3DQ,WAAW,EAAEoH,QAAQ,CAAChH,IAAI,CAACJ,WAAW,IAAI,EAAE;YAC5C;YACAqB,gBAAgB,EAAE+F,QAAQ,CAAChH,IAAI,CAACiB,gBAAgB;YAChDE,WAAW,EAAE6F,QAAQ,CAAChH,IAAI,CAACmB,WAAW;YACtCC,SAAS,EAAE4F,QAAQ,CAAChH,IAAI,CAACoB,SAAS;YAClCC,aAAa,EAAE2F,QAAQ,CAAChH,IAAI,CAACqB,aAAa;YAC1CC,uBAAuB,EAAE0F,QAAQ,CAAChH,IAAI,CAACsB;UACzC,CAAC,CAAC;UAEF,IAAIgI,eAAe,CAACzJ,MAAM,GAAG,CAAC,EAAE;YAC9B1D,OAAO,CAACiM,OAAO,CAAC,QAAQgC,KAAK,CAACb,UAAU,OAAO,CAAC;UAClD;QACF;MACF;MAEApE,WAAW,CAAC,GAAG,CAAC;MAChBE,UAAU,CAAC6E,UAAU,CAAC;MACtB/N,OAAO,CAACiM,OAAO,CAAC,SAAS8B,UAAU,CAACrK,MAAM,SAAS,CAAC;IAEtD,CAAC,CAAC,OAAOsH,KAAU,EAAE;MAAA,IAAAqD,gBAAA,EAAAC,qBAAA;MACnBtO,OAAO,CAACgL,KAAK,CAAC,EAAAqD,gBAAA,GAAArD,KAAK,CAACH,QAAQ,cAAAwD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxK,IAAI,cAAAyK,qBAAA,uBAApBA,qBAAA,CAAsBnD,MAAM,KAAI,MAAM,CAAC;IACvD,CAAC,SAAS;MACRrC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyF,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAGhJ,UAAU,KAAK,QAAQ,GAAGqB,YAAY,GAAIE,MAAM,IAAII,eAAgB;IAEpF,IAAIY,cAAc,KAAK,QAAQ,EAAE;MAC/B,OAAOyG,OAAO,IAAIvG,iBAAiB,IAAIM,YAAY,IAAIE,gBAAgB;IACzE,CAAC,MAAM;MACL,OAAO+F,OAAO,IAAI7G,cAAc,CAACjE,MAAM,GAAG,CAAC;IAC7C;EACF,CAAC;EAID,oBACErC,OAAA;IAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;MAAC4D,KAAK,EAAE,CAAE;MAAC7C,KAAK,EAAE;QAAEiB,QAAQ,EAAE,MAAM;QAAEiL,UAAU,EAAE,GAAG;QAAE1L,YAAY,EAAE;MAAM,CAAE;MAAAN,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACvGxB,OAAA,CAACI,IAAI;MAAC2B,IAAI,EAAC,WAAW;MAAAX,QAAA,EAAC;IAEvB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxB,OAAA,CAACtB,OAAO;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXxB,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,gCAAO;MAAC2M,SAAS,EAAC,eAAe;MAAAjM,QAAA,eAC3CpB,OAAA,CAACvB,KAAK;QAAC6O,SAAS,EAAC,UAAU;QAAC3I,IAAI,EAAC,OAAO;QAACzD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BxB,OAAA,CAAC7B,KAAK,CAACoP,KAAK;YACVxM,KAAK,EAAEoD,UAAW;YAClBuH,QAAQ,EAAG8B,CAAC,IAAKjI,aAAa,CAACiI,CAAC,CAACC,MAAM,CAAC1M,KAAK,CAAE;YAC/CG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCxB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,OAAO;cAAAK,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAGL2C,UAAU,KAAK,QAAQ,iBACtBnE,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChCxB,OAAA,CAACK,OAAO;YAAA,GAAKiL,WAAW;YAAEpK,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAChDpB,OAAA;cAAGqN,SAAS,EAAC,sBAAsB;cAAAjM,QAAA,eACjCpB,OAAA,CAACb,aAAa;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACJxB,OAAA;cAAGqN,SAAS,EAAC,iBAAiB;cAAAjM,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDxB,OAAA;cAAGqN,SAAS,EAAC,iBAAiB;cAAAjM,QAAA,EAAC;YAE/B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,EAGA2C,UAAU,KAAK,OAAO,iBACrBnE,OAAA,CAACvB,KAAK;UAAC6O,SAAS,EAAC,UAAU;UAACpM,KAAK,EAAE;YAAEL,KAAK,EAAE;UAAO,CAAE;UAAAO,QAAA,gBACnDpB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAAC3B,KAAK,CAACkP,KAAK;cAACG,OAAO;cAACxM,KAAK,EAAE;gBAAEe,SAAS,EAAE,CAAC;gBAAE0L,OAAO,EAAE;cAAO,CAAE;cAAAvM,QAAA,gBAC5DpB,OAAA,CAAC3B,KAAK;gBACJ0C,KAAK,EAAE2E,MAAO;gBACdgG,QAAQ,EAAG8B,CAAC,IAAK7H,SAAS,CAAC6H,CAAC,CAACC,MAAM,CAAC1M,KAAK,CAAE;gBAC3C6M,WAAW,EAAC,4BAAkB;gBAC9B1M,KAAK,EAAE;kBAAE8C,IAAI,EAAE;gBAAE;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFxB,OAAA,CAACzB,MAAM;gBACLwD,IAAI,EAAC,SAAS;gBACd8L,OAAO,EAAEtE,aAAc;gBACvBuE,OAAO,EAAE9H,eAAgB;gBACzB+H,QAAQ,EAAE,CAACrI,MAAO;gBAClBxE,KAAK,EAAE;kBAAE8M,UAAU,EAAE;gBAAE,CAAE;gBAAA5M,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;cAACkC,MAAM;cAAAlB,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BxB,OAAA,CAACpB,IAAI;cAACqP,QAAQ,EAAEjI,eAAgB;cAAA5E,QAAA,eAC9BpB,OAAA,CAAC1B,MAAM;gBACLyC,KAAK,EAAE+E,eAAgB;gBACvB4F,QAAQ,EAAE3F,kBAAmB;gBAC7B6H,WAAW,EAAC,mCAAU;gBACtB1M,KAAK,EAAE;kBAAEL,KAAK,EAAE,MAAM;kBAAEoB,SAAS,EAAE;gBAAE,CAAE;gBACvC6L,OAAO,EAAE9H,eAAgB;gBAAA5E,QAAA,EAExBwE,iBAAiB,CAACsI,GAAG,CAAEC,IAAI,iBAC1BnO,OAAA,CAACM,MAAM;kBAAYS,KAAK,EAAEoN,IAAK;kBAAA/M,QAAA,EAC5B+M;gBAAI,GADMA,IAAI;kBAAA9M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,0BAAM;MAAC2M,SAAS,EAAC,eAAe;MAAAjM,QAAA,eAC1CpB,OAAA,CAACvB,KAAK;QAAC6O,SAAS,EAAC,UAAU;QAAC3I,IAAI,EAAC,OAAO;QAACzD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAChEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC3B,KAAK,CAACkP,KAAK;YAACG,OAAO;YAACxM,KAAK,EAAE;cAAEe,SAAS,EAAE,CAAC;cAAE0L,OAAO,EAAE;YAAO,CAAE;YAAAvM,QAAA,gBAC5DpB,OAAA,CAAC3B,KAAK;cACJ0C,KAAK,EAAEmF,QAAS;cAChBwF,QAAQ,EAAG8B,CAAC,IAAKrH,WAAW,CAACqH,CAAC,CAACC,MAAM,CAAC1M,KAAK,CAAE;cAC7C6M,WAAW,EAAC,4BAAkB;cAC9B1M,KAAK,EAAE;gBAAE8C,IAAI,EAAE;cAAE;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFxB,OAAA,CAACzB,MAAM;cACLwD,IAAI,EAAC,SAAS;cACd8L,OAAO,EAAE9D,eAAgB;cACzB+D,OAAO,EAAEtH,aAAc;cACvBuH,QAAQ,EAAE,CAAC7H,QAAS;cACpBhF,KAAK,EAAE;gBAAE8M,UAAU,EAAE;cAAE,CAAE;cAAA5M,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBxB,OAAA,CAAC7B,KAAK,CAACoP,KAAK;YACVxM,KAAK,EAAE2F,cAAe;YACtBgF,QAAQ,EAAG8B,CAAC,IAAK7G,iBAAiB,CAAC6G,CAAC,CAACC,MAAM,CAAC1M,KAAK,CAAE;YACnDG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAExBpB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAAAK,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCxB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAC,UAAU;cAAAK,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAELkF,cAAc,KAAK,QAAQ,gBAC1B1G,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAACpB,IAAI;YAACqP,QAAQ,EAAEzH,aAAc;YAAApF,QAAA,eAC5BpB,OAAA,CAAC1B,MAAM;cACLyC,KAAK,EAAE6F,iBAAkB;cACzB8E,QAAQ,EAAER,qBAAsB;cAChC0C,WAAW,EAAC,kMAAkC;cAC9C1M,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvC6L,OAAO,EAAEtH,aAAc;cAAApF,QAAA,EAEtBgF,iBAAiB,CAAC8H,GAAG,CAAEC,IAAI,iBAC1BnO,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEoN,IAAK;gBAAA/M,QAAA,EAC5B+M;cAAI,GADMA,IAAI;gBAAA9M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAENoF,iBAAiB,iBAChB5G,OAAA;YAAKkB,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAG,CAAE;YAAAb,QAAA,eAC5BpB,OAAA,CAACvB,KAAK;cAAC6O,SAAS,EAAC,UAAU;cAACpM,KAAK,EAAE;gBAAEL,KAAK,EAAE;cAAO,CAAE;cAAAO,QAAA,gBACnDpB,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAAAX,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxB,OAAA,CAACpB,IAAI;kBAACqP,QAAQ,EAAEjG,oBAAqB;kBAAA5G,QAAA,eACnCpB,OAAA;oBAAKkB,KAAK,EAAE;sBAAEe,SAAS,EAAE,CAAC;sBAAEmM,OAAO,EAAE,EAAE;sBAAEC,eAAe,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAlN,QAAA,EACpF4G,oBAAoB,gBACnBhI,OAAA;sBAAAoB,QAAA,EAAG;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,gBAEpBxB,OAAA,CAAAE,SAAA;sBAAAkB,QAAA,gBACEpB,OAAA;wBAAAoB,QAAA,gBAAGpB,OAAA;0BAAAoB,QAAA,EAAQ;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACsF,kBAAkB,IAAI,KAAK;sBAAA;wBAAAzF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3DxB,OAAA;wBAAAoB,QAAA,gBAAGpB,OAAA;0BAAAoB,QAAA,EAAQ;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,KAAC,EAACwF,kBAAkB,IAAI,KAAK;sBAAA;wBAAA3F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC5D,CAAC8F,mBAAmB,IAAIJ,YAAY,IAAIE,gBAAgB,iBACvDpH,OAAA,CAAAE,SAAA;wBAAAkB,QAAA,gBACEpB,OAAA;0BAAAoB,QAAA,gBAAGpB,OAAA;4BAAAoB,QAAA,EAAQ;0BAAG;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC0F,YAAY;wBAAA;0BAAA7F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1CxB,OAAA;0BAAAoB,QAAA,gBAAGpB,OAAA;4BAAAoB,QAAA,EAAQ;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAAC4F,gBAAgB;wBAAA;0BAAA/F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA,eAChD,CACH;oBAAA,eACD;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAEL8F,mBAAmB,iBAClBtH,OAAA;gBAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;kBAACkC,MAAM;kBAAAlB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCxB,OAAA;kBAAKkB,KAAK,EAAE;oBAAEe,SAAS,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAC3BpB,OAAA,CAACvB,KAAK;oBAAC6O,SAAS,EAAC,UAAU;oBAACpM,KAAK,EAAE;sBAAEL,KAAK,EAAE;oBAAO,CAAE;oBAAAO,QAAA,gBACnDpB,OAAA,CAAC1B,MAAM;sBACLyC,KAAK,EAAEmG,YAAa;sBACpBwE,QAAQ,EAAEvE,eAAgB;sBAC1ByG,WAAW,EAAC,8DAAY;sBACxB1M,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExB4D,eAAe,CAACkJ,GAAG,CAAEK,IAAI,iBACxBvO,OAAA,CAACM,MAAM;wBAAYS,KAAK,EAAEwN,IAAK;wBAAAnN,QAAA,EAC5BmN;sBAAI,GADMA,IAAI;wBAAAlN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,EAER0F,YAAY,iBACXlH,OAAA,CAAC1B,MAAM;sBACLyC,KAAK,EAAEqG,gBAAiB;sBACxBsE,QAAQ,EAAErE,mBAAoB;sBAC9BuG,WAAW,EAAE,YAAY1G,YAAY,OAAQ;sBAC7ChG,KAAK,EAAE;wBAAEL,KAAK,EAAE;sBAAO,CAAE;sBAAAO,QAAA,EAExB,CAAC6D,eAAe,CAACiC,YAAY,CAAiC,IAAI,EAAE,EAAEgH,GAAG,CAAEvD,QAAQ,iBAClF3K,OAAA,CAACM,MAAM;wBAAgBS,KAAK,EAAE4J,QAAS;wBAAAvJ,QAAA,EACpCuJ;sBAAQ,GADEA,QAAQ;wBAAAtJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEb,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENxB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAACpB,IAAI;YAACqP,QAAQ,EAAEzH,aAAc;YAAApF,QAAA,eAC5BpB,OAAA,CAAC1B,MAAM;cACLkQ,IAAI,EAAC,UAAU;cACfzN,KAAK,EAAEuF,cAAe;cACtBoF,QAAQ,EAAEnF,iBAAkB;cAC5BqH,WAAW,EAAC,4FAAiB;cAC7B1M,KAAK,EAAE;gBAAEL,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAE,CAAE;cACvC6L,OAAO,EAAEtH,aAAc;cAAApF,QAAA,EAEtBgF,iBAAiB,CAAC8H,GAAG,CAAEC,IAAI,iBAC1BnO,OAAA,CAACM,MAAM;gBAAYS,KAAK,EAAEoN,IAAK;gBAAA/M,QAAA,EAC5B+M;cAAI,GADMA,IAAI;gBAAA9M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA4E,iBAAiB,CAAC/D,MAAM,KAAK,CAAC,IAAI,CAACmE,aAAa,iBAC/CxG,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,4CAAS;UACjB8P,WAAW,EAAC,oOAA2C;UACvD1M,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC9B,IAAI;MAACmP,SAAS,EAAC,eAAe;MAAC3M,KAAK,EAAC,0BAAM;MAAAU,QAAA,eAC1CpB,OAAA,CAACvB,KAAK;QAAC6O,SAAS,EAAC,UAAU;QAAC3I,IAAI,EAAC,QAAQ;QAACzD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBACjEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAAC7B,KAAK,CAACoP,KAAK;YACVxM,KAAK,EAAEsH,kBAAmB;YAC1BqD,QAAQ,EAAG8B,CAAC,IAAKlF,qBAAqB,CAACkF,CAAC,CAACC,MAAM,CAAC1M,KAAK,CAAE;YACvDG,KAAK,EAAE;cAAEe,SAAS,EAAE;YAAE,CAAE;YACxB8L,QAAQ,EAAErH,cAAc,KAAK,UAAW,CAAC;YAAA;YAAAtF,QAAA,gBAEzCpB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAE,IAAK;cAAAK,QAAA,eACjBpB,OAAA,CAACvB,KAAK;gBAAA2C,QAAA,GAAC,kDAEL,eAAApB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG,CAAE;kBAAAf,QAAA,EAAC;gBAEhD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACRxB,OAAA,CAAC7B,KAAK;cAAC4C,KAAK,EAAE,KAAM;cAAAK,QAAA,eAClBpB,OAAA,CAACvB,KAAK;gBAAA2C,QAAA,GAAC,0BAEL,eAAApB,OAAA,CAACI,IAAI;kBAAC2B,IAAI,EAAC,WAAW;kBAACb,KAAK,EAAE;oBAAEiB,QAAQ,EAAE;kBAAG,CAAE;kBAAAf,QAAA,EAAC;gBAEhD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,EAEL6G,kBAAkB,IAAI3B,cAAc,KAAK,QAAQ,iBAChD1G,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,sCAAQ;UAChB8P,WAAW,eACTzO,OAAA;YAAAoB,QAAA,GAAK,8JAEH,eAAApB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,mFACQ,eAAAxB,OAAA;cAAAoB,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAC7C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;UACDO,IAAI,EAAC,MAAM;UACXC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,EAEAkF,cAAc,KAAK,UAAU,iBAC5B1G,OAAA,CAAClB,KAAK;UACJH,OAAO,EAAC,4CAAS;UACjB8P,WAAW,EAAC,0KAA8B;UAC1C1M,IAAI,EAAC,SAAS;UACdC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPxB,OAAA,CAAC9B,IAAI;MAACmP,SAAS,EAAC,eAAe;MAAAjM,QAAA,gBAC7BpB,OAAA,CAACzB,MAAM;QACLwD,IAAI,EAAC,SAAS;QACd4C,IAAI,EAAC,OAAO;QACZ+J,IAAI,eAAE1O,OAAA,CAACZ,kBAAkB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BqM,OAAO,EAAEhC,qBAAsB;QAC/BiC,OAAO,EAAEtG,UAAW;QACpBuG,QAAQ,EAAE,CAACb,WAAW,CAAC,CAAE;QACzBG,SAAS,EAAC,eAAe;QAAAjM,QAAA,EAExBoG,UAAU,GAAG,SAAS,GAAG;MAAS;QAAAnG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EAGRgG,UAAU,iBACTxH,OAAA;QAAKqN,SAAS,EAAC,kBAAkB;QAAAjM,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;UAAAgB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClBxB,OAAA,CAACd,QAAQ;UAACyP,OAAO,EAAEjH,QAAS;UAACkH,MAAM,EAAC;QAAQ;UAAAvN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGNoG,OAAO,CAACvF,MAAM,GAAG,CAAC,iBACjBrC,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,0BAAM;MAAC2M,SAAS,EAAC,eAAe;MAAAjM,QAAA,EACzCwG,OAAO,CAACvF,MAAM,GAAG,CAAC;MAAA;MACjB;MACArC,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACtB,OAAO;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXxB,OAAA,CAACG,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAA3C,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxB,OAAA;UAAKkB,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG,CAAE;UAAAN,QAAA,gBAC/BpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxB,OAAA,CAAC1B,MAAM;YACL4C,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAEoB,SAAS,EAAE;YAAE,CAAE;YACvC2L,WAAW,EAAC,sCAAQ;YACpB7M,KAAK,EAAE+G,mBAAoB;YAC3B4D,QAAQ,EAAG3K,KAAK,IAAKgH,sBAAsB,CAAChH,KAAK,CAAE;YAAAK,QAAA,EAElDwG,OAAO,CAACsG,GAAG,CAAC,CAAC1N,MAAM,EAAEsE,KAAK,kBACzB9E,OAAA,CAACM,MAAM;cAAaS,KAAK,EAAE+D,KAAM;cAAA1D,QAAA,EAC9BZ,MAAM,CAAC6I;YAAU,GADPvE,KAAK;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLoG,OAAO,CAACE,mBAAmB,CAAC,iBAC3B9H,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;YAAC4D,KAAK,EAAE,CAAE;YAAA3C,QAAA,GAAC,gBAAI,EAACwG,OAAO,CAACE,mBAAmB,CAAC,CAACuB,UAAU;UAAA;YAAAhI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtExB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAEoH,OAAO,CAACE,mBAAmB;UAAE;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAxB,OAAA;QAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAA3C,QAAA,GAAC,6BAAO,EAACwG,OAAO,CAAC,CAAC,CAAC,CAACyB,UAAU;QAAA;UAAAhI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvDxB,OAAA,CAACO,uBAAuB;UAACC,MAAM,EAAEoH,OAAO,CAAC,CAAC;QAAE;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACP,EAGAmH,wBAAwB,CAACtG,MAAM,GAAG,CAAC,iBAClCrC,OAAA,CAAC9B,IAAI;MAACwC,KAAK,EAAC,sCAAQ;MAAC2M,SAAS,EAAC,eAAe;MAACnM,KAAK,EAAE;QAAEe,SAAS,EAAE;MAAG,CAAE;MAAAb,QAAA,gBACtEpB,OAAA,CAAClB,KAAK;QACJH,OAAO,EAAC,4CAAS;QACjB8P,WAAW,EAAC,sLAAgC;QAC5C1M,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRd,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAG;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFxB,OAAA,CAACvB,KAAK;QAAC6O,SAAS,EAAC,UAAU;QAAC3I,IAAI,EAAC,OAAO;QAACzD,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,gBAEhEpB,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACI,IAAI;YAACkC,MAAM;YAAAlB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3BxB,OAAA,CAAC1B,MAAM;YACLyC,KAAK,EAAE0H,mBAAoB;YAC3BiD,QAAQ,EAAE9C,qBAAsB;YAChC1H,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAEoB,SAAS,EAAE;YAAE,CAAE;YACvC2L,WAAW,EAAC,oEAAa;YAAAxM,QAAA,EAExBuH,wBAAwB,CAACuF,GAAG,CAAEhF,IAAI,iBACjClJ,OAAA,CAACM,MAAM;cAAoBS,KAAK,EAAEmI,IAAI,CAACC,OAAQ;cAAA/H,QAAA,EAC5C8H,IAAI,CAACC,OAAO,CAAC0F,QAAQ,CAAC,GAAG,CAAC,GACzB,GAAG3F,IAAI,CAACC,OAAO,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIC,IAAI,CAAC7F,IAAI,CAAC8F,UAAU,IAAI9F,IAAI,CAAC+F,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,GAClG,MAAMhG,IAAI,CAACC,OAAO,CAACgG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,IAAIJ,IAAI,CAAC7F,IAAI,CAAC8F,UAAU,IAAI9F,IAAI,CAAC+F,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;YAAG,GAHjGhG,IAAI,CAACC,OAAO;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGL+G,sBAAsB,CAAClG,MAAM,GAAG,CAAC,iBAChCrC,OAAA;UAAAoB,QAAA,gBACEpB,OAAA,CAACG,KAAK;YAAC4D,KAAK,EAAE,CAAE;YAAA3C,QAAA,GAAC,6BAAO,EAACmH,sBAAsB,CAAC,CAAC,CAAC,CAACc,UAAU;UAAA;YAAAhI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtExB,OAAA,CAACO,uBAAuB;YAACC,MAAM,EAAE+H,sBAAsB,CAAC,CAAC;UAAE;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC8D,EAAA,CAj0BID,mBAA6B;EAAA,QA+B8CvF,cAAc;AAAA;AAAAsP,GAAA,GA/BzF/J,mBAA6B;AAm0BnC,eAAeA,mBAAmB;AAAC,IAAAN,EAAA,EAAAqK,GAAA;AAAAC,YAAA,CAAAtK,EAAA;AAAAsK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}