{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pairs;\nexports.pair = pair;\nfunction pairs(values) {\n  let pairof = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : pair;\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\nfunction pair(a, b) {\n  return [a, b];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "pairs", "pair", "values", "pairof", "arguments", "length", "undefined", "previous", "first", "push", "a", "b"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/pairs.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = pairs;\nexports.pair = pair;\n\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n\n  return pairs;\n}\n\nfunction pair(a, b) {\n  return [a, b];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,KAAK;AACvBH,OAAO,CAACI,IAAI,GAAGA,IAAI;AAEnB,SAASD,KAAKA,CAACE,MAAM,EAAiB;EAAA,IAAfC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGH,IAAI;EAClC,MAAMD,KAAK,GAAG,EAAE;EAChB,IAAIO,QAAQ;EACZ,IAAIC,KAAK,GAAG,KAAK;EAEjB,KAAK,MAAMV,KAAK,IAAII,MAAM,EAAE;IAC1B,IAAIM,KAAK,EAAER,KAAK,CAACS,IAAI,CAACN,MAAM,CAACI,QAAQ,EAAET,KAAK,CAAC,CAAC;IAC9CS,QAAQ,GAAGT,KAAK;IAChBU,KAAK,GAAG,IAAI;EACd;EAEA,OAAOR,KAAK;AACd;AAEA,SAASC,IAAIA,CAACS,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script"}