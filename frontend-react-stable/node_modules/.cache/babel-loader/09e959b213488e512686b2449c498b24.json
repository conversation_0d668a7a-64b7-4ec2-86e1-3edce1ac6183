{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport SafetyCertificateOutlinedSvg from \"@ant-design/icons-svg/es/asn/SafetyCertificateOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar SafetyCertificateOutlined = function SafetyCertificateOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: SafetyCertificateOutlinedSvg\n  }));\n};\nSafetyCertificateOutlined.displayName = 'SafetyCertificateOutlined';\nexport default /*#__PURE__*/React.forwardRef(SafetyCertificateOutlined);", "map": {"version": 3, "names": ["_objectSpread", "React", "SafetyCertificateOutlinedSvg", "AntdIcon", "SafetyCertificateOutlined", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/SafetyCertificateOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport SafetyCertificateOutlinedSvg from \"@ant-design/icons-svg/es/asn/SafetyCertificateOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar SafetyCertificateOutlined = function SafetyCertificateOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: SafetyCertificateOutlinedSvg\n  }));\n};\nSafetyCertificateOutlined.displayName = 'SafetyCertificateOutlined';\nexport default /*#__PURE__*/React.forwardRef(SafetyCertificateOutlined);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,4BAA4B,MAAM,wDAAwD;AACjG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7E,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,yBAAyB,CAACK,WAAW,GAAG,2BAA2B;AACnE,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}