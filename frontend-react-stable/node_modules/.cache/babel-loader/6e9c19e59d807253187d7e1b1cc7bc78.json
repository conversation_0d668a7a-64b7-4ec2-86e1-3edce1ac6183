{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, List, Button, Space, Typography, Tag, Progress, Modal, Empty, message, Spin, Tooltip, Popconfirm } from 'antd';\nimport { SyncOutlined, CheckCircleOutlined, CloseCircleOutlined, StopOutlined, ClockCircleOutlined, EyeOutlined, DeleteOutlined, ReloadOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TaskManagerPage = () => {\n  _s();\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([fetchRunningTasks(), fetchCompletedTasks()]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async taskId => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空已完成任务\n  const handleClearCompleted = async () => {\n    try {\n      await clearCompletedTasks();\n      message.success('已清空完成的任务');\n    } catch (error) {\n      console.error('清空任务失败:', error);\n    }\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 46\n          }, this),\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.COMPLETED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.FAILED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 16\n        }, this);\n      case TASK_STATUS.CANCELLED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 43\n          }, this),\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 格式化时间\n  const formatTime = timeString => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"\\u4EFB\\u52A1\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u67E5\\u770B\\u548C\\u7BA1\\u7406\\u5F02\\u6B65\\u8BAD\\u7EC3\\u3001\\u9884\\u6D4B\\u4EFB\\u52A1\\u7684\\u72B6\\u6001\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 17\n        }, this),\n        onClick: handleRefresh,\n        loading: refreshing,\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), runningTasks.length === 0 && completedTasks.length === 0 && /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(PlayCircleOutlined, {\n          style: {\n            fontSize: '48px',\n            color: '#1890ff',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u6B22\\u8FCE\\u4F7F\\u7528\\u4EFB\\u52A1\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u8FD9\\u91CC\\u662F\\u67E5\\u770B\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u4EFB\\u52A1\\u7ED3\\u679C\\u7684\\u5730\\u65B9\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), \"\\u5F53\\u60A8\\u5728\\u8BAD\\u7EC3\\u6216\\u9884\\u6D4B\\u9875\\u9762\\u9009\\u62E9\\u5F02\\u6B65\\u6A21\\u5F0F\\u5E76\\u63D0\\u4EA4\\u4EFB\\u52A1\\u540E\\uFF0C\\u53EF\\u4EE5\\u5728\\u8FD9\\u91CC\\u5B9E\\u65F6\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            value: runningTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5931\\u8D25\\u4EFB\\u52A1\",\n            value: completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length,\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n            value: runningTasks.length + completedTasks.length,\n            prefix: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"processing\",\n              children: runningTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u81EA\\u52A8\\u5237\\u65B0\\u4E2D\",\n            children: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n              spin: true,\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                  description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 38\n                }, this)\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u8981\\u53D6\\u6D88\\u8FD9\\u4E2A\\u4EFB\\u52A1\\u5417\\uFF1F\",\n                  onConfirm: () => handleCancelTask(task.task_id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    danger: true,\n                    icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 33\n                    }, this),\n                    children: \"\\u53D6\\u6D88\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      copyable: {\n                        text: task.task_id\n                      },\n                      children: [\"ID: \", task.task_id.substring(0, 8), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", formatTime(task.created_at)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 27\n                    }, this), task.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [\"\\u72B6\\u6001: \", task.message]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              children: completedTasks.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this),\n          extra: /*#__PURE__*/_jsxDEV(Popconfirm, {\n            title: \"\\u786E\\u5B9A\\u8981\\u6E05\\u7A7A\\u6240\\u6709\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\\u5417\\uFF1F\",\n            onConfirm: handleClearCompleted,\n            okText: \"\\u786E\\u5B9A\",\n            cancelText: \"\\u53D6\\u6D88\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              danger: true,\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 25\n              }, this),\n              disabled: completedTasks.length === 0,\n              children: \"\\u6E05\\u7A7A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: completedTasks,\n            locale: {\n              emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"\\u6682\\u65E0\\u5DF2\\u5B8C\\u6210\\u7684\\u4EFB\\u52A1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: \"\\u5F02\\u6B65\\u8BAD\\u7EC3\\u548C\\u9884\\u6D4B\\u5B8C\\u6210\\u540E\\uFF0C\\u7ED3\\u679C\\u4F1A\\u663E\\u793A\\u5728\\u8FD9\\u91CC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)\n            },\n            renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleViewTaskDetail(task),\n                children: \"\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: getTaskStatusIcon(task.status),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: formatTaskType(task.task_type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this), getTaskStatusTag(task.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    copyable: {\n                      text: task.task_id\n                    },\n                    children: [\"ID: \", task.task_id.substring(0, 8), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", formatTime(task.updated_at)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this), task.status === TASK_STATUS.FAILED && task.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"danger\",\n                      children: [\"\\u9519\\u8BEF: \", task.error]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      open: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.created_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTime(selectedTask.updated_at)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 17\n          }, this), selectedTask.error && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u9519\\u8BEF\\u4FE1\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: selectedTask.error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 17\n          }, this), selectedTask.result && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ED3\\u679C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 16,\n                borderRadius: 4,\n                fontSize: 12,\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(selectedTask.result, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(TaskManagerPage, \"PqritZihkqxsa0b8+o2T2yh9hdU=\", false, function () {\n  return [useTaskManager];\n});\n_c = TaskManagerPage;\nexport default TaskManagerPage;\nvar _c;\n$RefreshReg$(_c, \"TaskManagerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "List", "<PERSON><PERSON>", "Space", "Typography", "Tag", "Progress", "Modal", "Empty", "message", "Spin", "<PERSON><PERSON><PERSON>", "Popconfirm", "SyncOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "ClockCircleOutlined", "EyeOutlined", "DeleteOutlined", "ReloadOutlined", "PlayCircleOutlined", "useTaskManager", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TaskManagerPage", "_s", "runningTasks", "completedTasks", "loading", "fetchRunningTasks", "fetchCompletedTasks", "cancelTask", "clearCompletedTasks", "formatTaskType", "TASK_STATUS", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "interval", "setInterval", "clearInterval", "handleRefresh", "Promise", "all", "success", "error", "handleViewTaskDetail", "task", "handleCancelTask", "taskId", "console", "handleClearCompleted", "getTaskStatusIcon", "status", "RUNNING", "spin", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "COMPLETED", "FAILED", "CANCELLED", "getTaskStatusTag", "icon", "children", "formatTime", "timeString", "Date", "toLocaleString", "padding", "marginBottom", "display", "justifyContent", "alignItems", "level", "type", "onClick", "length", "textAlign", "fontSize", "gutter", "span", "title", "value", "prefix", "valueStyle", "filter", "t", "extra", "spinning", "dataSource", "locale", "emptyText", "description", "renderItem", "<PERSON><PERSON>", "actions", "onConfirm", "task_id", "okText", "cancelText", "danger", "Meta", "avatar", "strong", "task_type", "copyable", "text", "substring", "created_at", "progress", "undefined", "marginTop", "percent", "size", "disabled", "updated_at", "open", "onCancel", "footer", "width", "result", "background", "borderRadius", "maxHeight", "overflow", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/TaskManagerPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空已完成任务\n  const handleClearCompleted = async () => {\n    try {\n      await clearCompletedTasks();\n      message.success('已清空完成的任务');\n    } catch (error) {\n      console.error('清空任务失败:', error);\n    }\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>任务管理</Title>\n          <Text type=\"secondary\">\n            查看和管理异步训练、预测任务的状态和结果\n          </Text>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 使用提示 */}\n      {runningTasks.length === 0 && completedTasks.length === 0 && (\n        <Card style={{ marginBottom: '24px', textAlign: 'center' }}>\n          <div style={{ padding: '20px 0' }}>\n            <PlayCircleOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />\n            <Title level={4}>欢迎使用任务管理</Title>\n            <Text type=\"secondary\">\n              这里是查看异步训练和预测任务结果的地方。\n              <br />\n              当您在训练或预测页面选择异步模式并提交任务后，可以在这里实时查看进度和结果。\n            </Text>\n          </div>\n        </Card>\n      )}\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.substring(0, 8)}...\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Popconfirm\n                title=\"确定要清空所有已完成的任务吗？\"\n                onConfirm={handleClearCompleted}\n                okText=\"确定\"\n                cancelText=\"取消\"\n              >\n                <Button\n                  type=\"primary\"\n                  danger\n                  size=\"small\"\n                  icon={<DeleteOutlined />}\n                  disabled={completedTasks.length === 0}\n                >\n                  清空\n                </Button>\n              </Popconfirm>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.substring(0, 8)}...\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.result && (\n                <Col span={24}>\n                  <Text strong>结果:</Text>\n                  <br />\n                  <div style={{ \n                    background: '#f5f5f5', \n                    padding: 16, \n                    borderRadius: 4, \n                    fontSize: 12,\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.result, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAElC,MAAMyB,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC,OAAO;IACPC,iBAAiB;IACjBC,mBAAmB;IACnBC,UAAU;IACVC,mBAAmB;IACnBC,cAAc;IACdC;EACF,CAAC,GAAGjB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC+C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACD,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACAvC,SAAS,CAAC,MAAM;IACd,MAAMkD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMc,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACZ,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMe,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCJ,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMK,OAAO,CAACC,GAAG,CAAC,CAChBjB,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACtB,CAAC;MACF1B,OAAO,CAAC2C,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRR,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAIC,IAAU,IAAK;IAC3Cd,eAAe,CAACc,IAAI,CAAC;IACrBZ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMa,gBAAgB,GAAG,MAAOC,MAAc,IAAK;IACjD,IAAI;MACF,MAAMrB,UAAU,CAACqB,MAAM,CAAC;MACxB,MAAMvB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMtB,mBAAmB,CAAC,CAAC;MAC3B5B,OAAO,CAAC2C,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMO,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAKtB,WAAW,CAACuB,OAAO;QACtB,oBAAOtC,OAAA,CAACX,YAAY;UAACkD,IAAI;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK9B,WAAW,CAAC+B,SAAS;QACxB,oBAAO9C,OAAA,CAACV,mBAAmB;UAACkD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK9B,WAAW,CAACgC,MAAM;QACrB,oBAAO/C,OAAA,CAACT,mBAAmB;UAACiD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK9B,WAAW,CAACiC,SAAS;QACxB,oBAAOhD,OAAA,CAACR,YAAY;UAACgD,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAO7C,OAAA,CAACP,mBAAmB;UAAC+C,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAIZ,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAKtB,WAAW,CAACuB,OAAO;QACtB,oBAAOtC,OAAA,CAACnB,GAAG;UAAC4D,KAAK,EAAC,YAAY;UAACS,IAAI,eAAElD,OAAA,CAACX,YAAY;YAACkD,IAAI;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE,KAAK9B,WAAW,CAAC+B,SAAS;QACxB,oBAAO9C,OAAA,CAACnB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAElD,OAAA,CAACV,mBAAmB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK9B,WAAW,CAACgC,MAAM;QACrB,oBAAO/C,OAAA,CAACnB,GAAG;UAAC4D,KAAK,EAAC,OAAO;UAACS,IAAI,eAAElD,OAAA,CAACT,mBAAmB;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK9B,WAAW,CAACiC,SAAS;QACxB,oBAAOhD,OAAA,CAACnB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAElD,OAAA,CAACR,YAAY;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/D;QACE,oBAAO7C,OAAA,CAACnB,GAAG;UAAC4D,KAAK,EAAC,SAAS;UAACS,IAAI,eAAElD,OAAA,CAACP,mBAAmB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMO,UAAU,GAAIC,UAAmB,IAAK;IAC1C,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;EAED,oBACEvD,OAAA;IAAKwC,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAO,CAAE;IAAAL,QAAA,gBAC9BnD,OAAA;MAAKwC,KAAK,EAAE;QAAEiB,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAT,QAAA,gBAC3GnD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA,CAACG,KAAK;UAAC0D,KAAK,EAAE,CAAE;UAAAV,QAAA,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B7C,OAAA,CAACI,IAAI;UAAC0D,IAAI,EAAC,WAAW;UAAAX,QAAA,EAAC;QAEvB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7C,OAAA,CAACtB,MAAM;QACLoF,IAAI,EAAC,SAAS;QACdZ,IAAI,eAAElD,OAAA,CAACJ,cAAc;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBkB,OAAO,EAAEtC,aAAc;QACvBhB,OAAO,EAAEW,UAAW;QAAA+B,QAAA,EACrB;MAED;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLtC,YAAY,CAACyD,MAAM,KAAK,CAAC,IAAIxD,cAAc,CAACwD,MAAM,KAAK,CAAC,iBACvDhE,OAAA,CAAC3B,IAAI;MAACmE,KAAK,EAAE;QAAEiB,YAAY,EAAE,MAAM;QAAEQ,SAAS,EAAE;MAAS,CAAE;MAAAd,QAAA,eACzDnD,OAAA;QAAKwC,KAAK,EAAE;UAAEgB,OAAO,EAAE;QAAS,CAAE;QAAAL,QAAA,gBAChCnD,OAAA,CAACH,kBAAkB;UAAC2C,KAAK,EAAE;YAAE0B,QAAQ,EAAE,MAAM;YAAEzB,KAAK,EAAE,SAAS;YAAEgB,YAAY,EAAE;UAAO;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3F7C,OAAA,CAACG,KAAK;UAAC0D,KAAK,EAAE,CAAE;UAAAV,QAAA,EAAC;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjC7C,OAAA,CAACI,IAAI;UAAC0D,IAAI,EAAC,WAAW;UAAAX,QAAA,GAAC,0HAErB,eAAAnD,OAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,wOAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAGD7C,OAAA,CAAC3B,IAAI;MAACmE,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAO,CAAE;MAAAN,QAAA,eACpCnD,OAAA,CAAC1B,GAAG;QAAC6F,MAAM,EAAE,EAAG;QAAAhB,QAAA,gBACdnD,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAjB,QAAA,eACXnD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE/D,YAAY,CAACyD,MAAO;YAC3BO,MAAM,eAAEvE,OAAA,CAACX,YAAY;cAACkD,IAAI;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9B2B,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7C,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAjB,QAAA,eACXnD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE9D,cAAc,CAACiE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAKtB,WAAW,CAAC+B,SAAS,CAAC,CAACkB,MAAO;YAC7EO,MAAM,eAAEvE,OAAA,CAACV,mBAAmB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC2B,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7C,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAjB,QAAA,eACXnD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE9D,cAAc,CAACiE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,MAAM,KAAKtB,WAAW,CAACgC,MAAM,CAAC,CAACiB,MAAO;YAC1EO,MAAM,eAAEvE,OAAA,CAACT,mBAAmB;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChC2B,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7C,OAAA,CAACzB,GAAG;UAAC6F,IAAI,EAAE,CAAE;UAAAjB,QAAA,eACXnD,OAAA,CAACxB,SAAS;YACR6F,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE/D,YAAY,CAACyD,MAAM,GAAGxD,cAAc,CAACwD,MAAO;YACnDO,MAAM,eAAEvE,OAAA,CAACH,kBAAkB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP7C,OAAA,CAAC1B,GAAG;MAAC6F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAhB,QAAA,gBAEpBnD,OAAA,CAACzB,GAAG;QAAC6F,IAAI,EAAE,EAAG;QAAAjB,QAAA,eACZnD,OAAA,CAAC3B,IAAI;UACHgG,KAAK,eACHrE,OAAA,CAACrB,KAAK;YAAAwE,QAAA,gBACJnD,OAAA,CAACX,YAAY;cAACkD,IAAI;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAErB,eAAA7C,OAAA,CAACnB,GAAG;cAAC4D,KAAK,EAAC,YAAY;cAAAU,QAAA,EAAE5C,YAAY,CAACyD;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACR;UACD8B,KAAK,eACH3E,OAAA,CAACb,OAAO;YAACkF,KAAK,EAAC,gCAAO;YAAAlB,QAAA,eACpBnD,OAAA,CAACX,YAAY;cAACkD,IAAI;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACV;UAAAM,QAAA,eAEDnD,OAAA,CAACd,IAAI;YAAC0F,QAAQ,EAAEnE,OAAQ;YAAA0C,QAAA,eACtBnD,OAAA,CAACvB,IAAI;cACHoG,UAAU,EAAEtE,YAAa;cACzBuE,MAAM,EAAE;gBAAEC,SAAS,eAAE/E,OAAA,CAAChB,KAAK;kBAACgG,WAAW,EAAC;gBAAU;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE,CAAE;cACxDoC,UAAU,EAAGlD,IAAI,iBACf/B,OAAA,CAACvB,IAAI,CAACyG,IAAI;gBACRC,OAAO,EAAE,cACPnF,OAAA,CAACtB,MAAM;kBACLoF,IAAI,EAAC,MAAM;kBACXZ,IAAI,eAAElD,OAAA,CAACN,WAAW;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBkB,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAACC,IAAI,CAAE;kBAAAoB,QAAA,EAC3C;gBAED;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7C,OAAA,CAACZ,UAAU;kBACTiF,KAAK,EAAC,oEAAa;kBACnBe,SAAS,EAAEA,CAAA,KAAMpD,gBAAgB,CAACD,IAAI,CAACsD,OAAO,CAAE;kBAChDC,MAAM,EAAC,cAAI;kBACXC,UAAU,EAAC,cAAI;kBAAApC,QAAA,eAEfnD,OAAA,CAACtB,MAAM;oBACLoF,IAAI,EAAC,MAAM;oBACX0B,MAAM;oBACNtC,IAAI,eAAElD,OAAA,CAACR,YAAY;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAM,QAAA,EACxB;kBAED;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,CACb;gBAAAM,QAAA,eAEFnD,OAAA,CAACvB,IAAI,CAACyG,IAAI,CAACO,IAAI;kBACbC,MAAM,EAAEtD,iBAAiB,CAACL,IAAI,CAACM,MAAM,CAAE;kBACvCgC,KAAK,eACHrE,OAAA,CAACrB,KAAK;oBAAAwE,QAAA,gBACJnD,OAAA,CAACI,IAAI;sBAACuF,MAAM;sBAAAxC,QAAA,EAAErC,cAAc,CAACiB,IAAI,CAAC6D,SAAS;oBAAC;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDI,gBAAgB,CAAClB,IAAI,CAACM,MAAM,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDmC,WAAW,eACThF,OAAA;oBAAAmD,QAAA,gBACEnD,OAAA,CAACI,IAAI;sBAAC0D,IAAI,EAAC,WAAW;sBAAC+B,QAAQ,EAAE;wBAAEC,IAAI,EAAE/D,IAAI,CAACsD;sBAAQ,CAAE;sBAAAlC,QAAA,GAAC,MACnD,EAACpB,IAAI,CAACsD,OAAO,CAACU,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KACpC;oBAAA;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP7C,OAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;sBAAC0D,IAAI,EAAC,WAAW;sBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAACrB,IAAI,CAACiE,UAAU,CAAC;oBAAA;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAChEd,IAAI,CAAC9C,OAAO,iBACXe,OAAA,CAAAE,SAAA;sBAAAiD,QAAA,gBACEnD,OAAA;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;wBAAC0D,IAAI,EAAC,WAAW;wBAAAX,QAAA,GAAC,gBAAI,EAACpB,IAAI,CAAC9C,OAAO;sBAAA;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,eAChD,CACH,EACAd,IAAI,CAACkE,QAAQ,KAAKC,SAAS,iBAC1BlG,OAAA;sBAAKwC,KAAK,EAAE;wBAAE2D,SAAS,EAAE;sBAAE,CAAE;sBAAAhD,QAAA,eAC3BnD,OAAA,CAAClB,QAAQ;wBAACsH,OAAO,EAAErE,IAAI,CAACkE,QAAS;wBAACI,IAAI,EAAC;sBAAO;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7C,OAAA,CAACzB,GAAG;QAAC6F,IAAI,EAAE,EAAG;QAAAjB,QAAA,eACZnD,OAAA,CAAC3B,IAAI;UACHgG,KAAK,eACHrE,OAAA,CAACrB,KAAK;YAAAwE,QAAA,gBACJnD,OAAA,CAACV,mBAAmB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEvB,eAAA7C,OAAA,CAACnB,GAAG;cAAC4D,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAE3C,cAAc,CAACwD;YAAM;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACR;UACD8B,KAAK,eACH3E,OAAA,CAACZ,UAAU;YACTiF,KAAK,EAAC,4FAAiB;YACvBe,SAAS,EAAEjD,oBAAqB;YAChCmD,MAAM,EAAC,cAAI;YACXC,UAAU,EAAC,cAAI;YAAApC,QAAA,eAEfnD,OAAA,CAACtB,MAAM;cACLoF,IAAI,EAAC,SAAS;cACd0B,MAAM;cACNa,IAAI,EAAC,OAAO;cACZnD,IAAI,eAAElD,OAAA,CAACL,cAAc;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzByD,QAAQ,EAAE9F,cAAc,CAACwD,MAAM,KAAK,CAAE;cAAAb,QAAA,EACvC;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACb;UAAAM,QAAA,eAEDnD,OAAA,CAACvB,IAAI;YACHoG,UAAU,EAAErE,cAAe;YAC3BsE,MAAM,EAAE;cACNC,SAAS,eACP/E,OAAA,CAAChB,KAAK;gBACJgG,WAAW,eACThF,OAAA;kBAAAmD,QAAA,gBACEnD,OAAA;oBAAAmD,QAAA,EAAK;kBAAQ;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnB7C,OAAA,CAACI,IAAI;oBAAC0D,IAAI,EAAC,WAAW;oBAACtB,KAAK,EAAE;sBAAE0B,QAAQ,EAAE;oBAAO,CAAE;oBAAAf,QAAA,EAAC;kBAEpD;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAEL,CAAE;YACFoC,UAAU,EAAGlD,IAAI,iBACf/B,OAAA,CAACvB,IAAI,CAACyG,IAAI;cACRC,OAAO,EAAE,cACPnF,OAAA,CAACtB,MAAM;gBACLoF,IAAI,EAAC,MAAM;gBACXZ,IAAI,eAAElD,OAAA,CAACN,WAAW;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBkB,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAACC,IAAI,CAAE;gBAAAoB,QAAA,EAC3C;cAED;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,CACT;cAAAM,QAAA,eAEFnD,OAAA,CAACvB,IAAI,CAACyG,IAAI,CAACO,IAAI;gBACbC,MAAM,EAAEtD,iBAAiB,CAACL,IAAI,CAACM,MAAM,CAAE;gBACvCgC,KAAK,eACHrE,OAAA,CAACrB,KAAK;kBAAAwE,QAAA,gBACJnD,OAAA,CAACI,IAAI;oBAACuF,MAAM;oBAAAxC,QAAA,EAAErC,cAAc,CAACiB,IAAI,CAAC6D,SAAS;kBAAC;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACnDI,gBAAgB,CAAClB,IAAI,CAACM,MAAM,CAAC;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACR;gBACDmC,WAAW,eACThF,OAAA;kBAAAmD,QAAA,gBACEnD,OAAA,CAACI,IAAI;oBAAC0D,IAAI,EAAC,WAAW;oBAAC+B,QAAQ,EAAE;sBAAEC,IAAI,EAAE/D,IAAI,CAACsD;oBAAQ,CAAE;oBAAAlC,QAAA,GAAC,MACnD,EAACpB,IAAI,CAACsD,OAAO,CAACU,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KACpC;kBAAA;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP7C,OAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;oBAAC0D,IAAI,EAAC,WAAW;oBAAAX,QAAA,GAAC,4BAAM,EAACC,UAAU,CAACrB,IAAI,CAACwE,UAAU,CAAC;kBAAA;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChEd,IAAI,CAACM,MAAM,KAAKtB,WAAW,CAACgC,MAAM,IAAIhB,IAAI,CAACF,KAAK,iBAC/C7B,OAAA,CAAAE,SAAA;oBAAAiD,QAAA,gBACEnD,OAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;sBAAC0D,IAAI,EAAC,QAAQ;sBAAAX,QAAA,GAAC,gBAAI,EAACpB,IAAI,CAACF,KAAK;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eAC3C,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA,CAACjB,KAAK;MACJsF,KAAK,EAAC,0BAAM;MACZmC,IAAI,EAAEtF,iBAAkB;MACxBuF,QAAQ,EAAEA,CAAA,KAAMtF,oBAAoB,CAAC,KAAK,CAAE;MAC5CuF,MAAM,EAAE,cACN1G,OAAA,CAACtB,MAAM;QAAaqF,OAAO,EAAEA,CAAA,KAAM5C,oBAAoB,CAAC,KAAK,CAAE;QAAAgC,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACF8D,KAAK,EAAE,GAAI;MAAAxD,QAAA,EAEVnC,YAAY,iBACXhB,OAAA;QAAAmD,QAAA,eACEnD,OAAA,CAAC1B,GAAG;UAAC6F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAhB,QAAA,gBACpBnD,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAACyF,QAAQ;cAAA1C,QAAA,EAAEnC,YAAY,CAACqE;YAAO;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN7C,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAAA+C,QAAA,EAAErC,cAAc,CAACE,YAAY,CAAC4E,SAAS;YAAC;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN7C,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLI,gBAAgB,CAACjC,YAAY,CAACqB,MAAM,CAAC;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN7C,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACL7B,YAAY,CAACiF,QAAQ,KAAKC,SAAS,gBAClClG,OAAA,CAAClB,QAAQ;cAACsH,OAAO,EAAEpF,YAAY,CAACiF,QAAS;cAACI,IAAI,EAAC;YAAO;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD7C,OAAA,CAACI,IAAI;cAAC0D,IAAI,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN7C,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAAA+C,QAAA,EAAEC,UAAU,CAACpC,YAAY,CAACgF,UAAU;YAAC;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACN7C,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAAA+C,QAAA,EAAEC,UAAU,CAACpC,YAAY,CAACuF,UAAU;YAAC;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,EACL7B,YAAY,CAAC/B,OAAO,iBACnBe,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAAA+C,QAAA,EAAEnC,YAAY,CAAC/B;YAAO;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACA7B,YAAY,CAACa,KAAK,iBACjB7B,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA,CAACI,IAAI;cAAC0D,IAAI,EAAC,QAAQ;cAAAX,QAAA,EAAEnC,YAAY,CAACa;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CACN,EACA7B,YAAY,CAAC4F,MAAM,iBAClB5G,OAAA,CAACzB,GAAG;YAAC6F,IAAI,EAAE,EAAG;YAAAjB,QAAA,gBACZnD,OAAA,CAACI,IAAI;cAACuF,MAAM;cAAAxC,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB7C,OAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7C,OAAA;cAAKwC,KAAK,EAAE;gBACVqE,UAAU,EAAE,SAAS;gBACrBrD,OAAO,EAAE,EAAE;gBACXsD,YAAY,EAAE,CAAC;gBACf5C,QAAQ,EAAE,EAAE;gBACZ6C,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE;cACZ,CAAE;cAAA7D,QAAA,eACAnD,OAAA;gBAAAmD,QAAA,EAAM8D,IAAI,CAACC,SAAS,CAAClG,YAAY,CAAC4F,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvC,EAAA,CAvbID,eAAyB;EAAA,QAWzBP,cAAc;AAAA;AAAAqH,EAAA,GAXd9G,eAAyB;AAyb/B,eAAeA,eAAe;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}