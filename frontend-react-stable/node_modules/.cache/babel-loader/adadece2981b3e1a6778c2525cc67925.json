{"ast": null, "code": "var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topCenter: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomCenter: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  }\n};\nexport default placements;", "map": {"version": 3, "names": ["autoAdjustOverflow", "adjustX", "adjustY", "targetOffset", "placements", "topLeft", "points", "overflow", "offset", "topCenter", "topRight", "bottomLeft", "bottomCenter", "bottomRight"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-dropdown/es/placements.js"], "sourcesContent": ["var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topCenter: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomCenter: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  }\n};\nexport default placements;"], "mappings": "AAAA,IAAIA,kBAAkB,GAAG;EACvBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,IAAIC,UAAU,GAAG;EACfC,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDM,SAAS,EAAE;IACTH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDO,QAAQ,EAAE;IACRJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDQ,UAAU,EAAE;IACVL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDS,YAAY,EAAE;IACZN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDU,WAAW,EAAE;IACXP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,kBAAkB;IAC5BQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB;AACF,CAAC;AACD,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module"}