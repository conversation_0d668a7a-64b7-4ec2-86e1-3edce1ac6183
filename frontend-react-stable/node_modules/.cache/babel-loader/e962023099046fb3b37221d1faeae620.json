{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport PlusOutlined from \"@ant-design/icons/es/icons/PlusOutlined\";\nimport classNames from 'classnames';\nimport RcTabs from 'rc-tabs';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\nimport warning from '../_util/warning';\nimport useAnimateConfig from './hooks/useAnimateConfig';\nimport useLegacyItems from './hooks/useLegacyItems';\nimport TabPane from './TabPane';\nfunction Tabs(_a) {\n  var type = _a.type,\n    className = _a.className,\n    propSize = _a.size,\n    _onEdit = _a.onEdit,\n    hideAdd = _a.hideAdd,\n    centered = _a.centered,\n    addIcon = _a.addIcon,\n    children = _a.children,\n    items = _a.items,\n    animated = _a.animated,\n    props = __rest(_a, [\"type\", \"className\", \"size\", \"onEdit\", \"hideAdd\", \"centered\", \"addIcon\", \"children\", \"items\", \"animated\"]);\n  var customizePrefixCls = props.prefixCls,\n    _props$moreIcon = props.moreIcon,\n    moreIcon = _props$moreIcon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$moreIcon;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    getPopupContainer = _React$useContext.getPopupContainer;\n  var prefixCls = getPrefixCls('tabs', customizePrefixCls);\n  var editable;\n  if (type === 'editable-card') {\n    editable = {\n      onEdit: function onEdit(editType, _ref) {\n        var key = _ref.key,\n          event = _ref.event;\n        _onEdit === null || _onEdit === void 0 ? void 0 : _onEdit(editType === 'add' ? event : key, editType);\n      },\n      removeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null),\n      addIcon: addIcon || /*#__PURE__*/React.createElement(PlusOutlined, null),\n      showAdd: hideAdd !== true\n    };\n  }\n  var rootPrefixCls = getPrefixCls();\n  process.env.NODE_ENV !== \"production\" ? warning(!('onPrevClick' in props) && !('onNextClick' in props), 'Tabs', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.') : void 0;\n  var mergedItems = useLegacyItems(items, children);\n  var mergedAnimated = useAnimateConfig(prefixCls, animated);\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (contextSize) {\n    var _classNames;\n    var size = propSize !== undefined ? propSize : contextSize;\n    return /*#__PURE__*/React.createElement(RcTabs, _extends({\n      direction: direction,\n      getPopupContainer: getPopupContainer,\n      moreTransitionName: \"\".concat(rootPrefixCls, \"-slide-up\")\n    }, props, {\n      items: mergedItems,\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-card\"), ['card', 'editable-card'].includes(type)), _defineProperty(_classNames, \"\".concat(prefixCls, \"-editable-card\"), type === 'editable-card'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-centered\"), centered), _classNames), className),\n      editable: editable,\n      moreIcon: moreIcon,\n      prefixCls: prefixCls,\n      animated: mergedAnimated\n    }));\n  });\n}\nTabs.TabPane = TabPane;\nexport default Tabs;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "CloseOutlined", "EllipsisOutlined", "PlusOutlined", "classNames", "RcTabs", "React", "ConfigContext", "SizeContext", "warning", "useAnimateConfig", "useLegacyItems", "TabPane", "Tabs", "_a", "type", "className", "propSize", "size", "_onEdit", "onEdit", "<PERSON><PERSON><PERSON>", "centered", "addIcon", "children", "items", "animated", "props", "customizePrefixCls", "prefixCls", "_props$moreIcon", "moreIcon", "createElement", "_React$useContext", "useContext", "getPrefixCls", "direction", "getPopupContainer", "editable", "editType", "_ref", "key", "event", "removeIcon", "showAdd", "rootPrefixCls", "process", "env", "NODE_ENV", "mergedItems", "mergedAnimated", "Consumer", "contextSize", "_classNames", "undefined", "moreTransitionName", "concat", "includes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/tabs/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport PlusOutlined from \"@ant-design/icons/es/icons/PlusOutlined\";\nimport classNames from 'classnames';\nimport RcTabs from 'rc-tabs';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport SizeContext from '../config-provider/SizeContext';\nimport warning from '../_util/warning';\nimport useAnimateConfig from './hooks/useAnimateConfig';\nimport useLegacyItems from './hooks/useLegacyItems';\nimport TabPane from './TabPane';\nfunction Tabs(_a) {\n  var type = _a.type,\n    className = _a.className,\n    propSize = _a.size,\n    _onEdit = _a.onEdit,\n    hideAdd = _a.hideAdd,\n    centered = _a.centered,\n    addIcon = _a.addIcon,\n    children = _a.children,\n    items = _a.items,\n    animated = _a.animated,\n    props = __rest(_a, [\"type\", \"className\", \"size\", \"onEdit\", \"hideAdd\", \"centered\", \"addIcon\", \"children\", \"items\", \"animated\"]);\n  var customizePrefixCls = props.prefixCls,\n    _props$moreIcon = props.moreIcon,\n    moreIcon = _props$moreIcon === void 0 ? /*#__PURE__*/React.createElement(EllipsisOutlined, null) : _props$moreIcon;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction,\n    getPopupContainer = _React$useContext.getPopupContainer;\n  var prefixCls = getPrefixCls('tabs', customizePrefixCls);\n  var editable;\n  if (type === 'editable-card') {\n    editable = {\n      onEdit: function onEdit(editType, _ref) {\n        var key = _ref.key,\n          event = _ref.event;\n        _onEdit === null || _onEdit === void 0 ? void 0 : _onEdit(editType === 'add' ? event : key, editType);\n      },\n      removeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null),\n      addIcon: addIcon || /*#__PURE__*/React.createElement(PlusOutlined, null),\n      showAdd: hideAdd !== true\n    };\n  }\n  var rootPrefixCls = getPrefixCls();\n  process.env.NODE_ENV !== \"production\" ? warning(!('onPrevClick' in props) && !('onNextClick' in props), 'Tabs', '`onPrevClick` and `onNextClick` has been removed. Please use `onTabScroll` instead.') : void 0;\n  var mergedItems = useLegacyItems(items, children);\n  var mergedAnimated = useAnimateConfig(prefixCls, animated);\n  return /*#__PURE__*/React.createElement(SizeContext.Consumer, null, function (contextSize) {\n    var _classNames;\n    var size = propSize !== undefined ? propSize : contextSize;\n    return /*#__PURE__*/React.createElement(RcTabs, _extends({\n      direction: direction,\n      getPopupContainer: getPopupContainer,\n      moreTransitionName: \"\".concat(rootPrefixCls, \"-slide-up\")\n    }, props, {\n      items: mergedItems,\n      className: classNames((_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(size), size), _defineProperty(_classNames, \"\".concat(prefixCls, \"-card\"), ['card', 'editable-card'].includes(type)), _defineProperty(_classNames, \"\".concat(prefixCls, \"-editable-card\"), type === 'editable-card'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-centered\"), centered), _classNames), className),\n      editable: editable,\n      moreIcon: moreIcon,\n      prefixCls: prefixCls,\n      animated: mergedAnimated\n    }));\n  });\n}\nTabs.TabPane = TabPane;\nexport default Tabs;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,aAAa,MAAM,0CAA0C;AACpE,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,SAAS;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,IAAIA,CAACC,EAAE,EAAE;EAChB,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;IAChBC,SAAS,GAAGF,EAAE,CAACE,SAAS;IACxBC,QAAQ,GAAGH,EAAE,CAACI,IAAI;IAClBC,OAAO,GAAGL,EAAE,CAACM,MAAM;IACnBC,OAAO,GAAGP,EAAE,CAACO,OAAO;IACpBC,QAAQ,GAAGR,EAAE,CAACQ,QAAQ;IACtBC,OAAO,GAAGT,EAAE,CAACS,OAAO;IACpBC,QAAQ,GAAGV,EAAE,CAACU,QAAQ;IACtBC,KAAK,GAAGX,EAAE,CAACW,KAAK;IAChBC,QAAQ,GAAGZ,EAAE,CAACY,QAAQ;IACtBC,KAAK,GAAGxC,MAAM,CAAC2B,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;EAChI,IAAIc,kBAAkB,GAAGD,KAAK,CAACE,SAAS;IACtCC,eAAe,GAAGH,KAAK,CAACI,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,aAAaxB,KAAK,CAAC0B,aAAa,CAAC9B,gBAAgB,EAAE,IAAI,CAAC,GAAG4B,eAAe;EACpH,IAAIG,iBAAiB,GAAG3B,KAAK,CAAC4B,UAAU,CAAC3B,aAAa,CAAC;IACrD4B,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;IACvCC,iBAAiB,GAAGJ,iBAAiB,CAACI,iBAAiB;EACzD,IAAIR,SAAS,GAAGM,YAAY,CAAC,MAAM,EAAEP,kBAAkB,CAAC;EACxD,IAAIU,QAAQ;EACZ,IAAIvB,IAAI,KAAK,eAAe,EAAE;IAC5BuB,QAAQ,GAAG;MACTlB,MAAM,EAAE,SAASA,MAAMA,CAACmB,QAAQ,EAAEC,IAAI,EAAE;QACtC,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;UAChBC,KAAK,GAAGF,IAAI,CAACE,KAAK;QACpBvB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,QAAQ,KAAK,KAAK,GAAGG,KAAK,GAAGD,GAAG,EAAEF,QAAQ,CAAC;MACvG,CAAC;MACDI,UAAU,EAAE,aAAarC,KAAK,CAAC0B,aAAa,CAAC/B,aAAa,EAAE,IAAI,CAAC;MACjEsB,OAAO,EAAEA,OAAO,IAAI,aAAajB,KAAK,CAAC0B,aAAa,CAAC7B,YAAY,EAAE,IAAI,CAAC;MACxEyC,OAAO,EAAEvB,OAAO,KAAK;IACvB,CAAC;EACH;EACA,IAAIwB,aAAa,GAAGV,YAAY,CAAC,CAAC;EAClCW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,OAAO,CAAC,EAAE,aAAa,IAAIkB,KAAK,CAAC,IAAI,EAAE,aAAa,IAAIA,KAAK,CAAC,EAAE,MAAM,EAAE,qFAAqF,CAAC,GAAG,KAAK,CAAC;EAC/M,IAAIsB,WAAW,GAAGtC,cAAc,CAACc,KAAK,EAAED,QAAQ,CAAC;EACjD,IAAI0B,cAAc,GAAGxC,gBAAgB,CAACmB,SAAS,EAAEH,QAAQ,CAAC;EAC1D,OAAO,aAAapB,KAAK,CAAC0B,aAAa,CAACxB,WAAW,CAAC2C,QAAQ,EAAE,IAAI,EAAE,UAAUC,WAAW,EAAE;IACzF,IAAIC,WAAW;IACf,IAAInC,IAAI,GAAGD,QAAQ,KAAKqC,SAAS,GAAGrC,QAAQ,GAAGmC,WAAW;IAC1D,OAAO,aAAa9C,KAAK,CAAC0B,aAAa,CAAC3B,MAAM,EAAEnB,QAAQ,CAAC;MACvDkD,SAAS,EAAEA,SAAS;MACpBC,iBAAiB,EAAEA,iBAAiB;MACpCkB,kBAAkB,EAAE,EAAE,CAACC,MAAM,CAACX,aAAa,EAAE,WAAW;IAC1D,CAAC,EAAElB,KAAK,EAAE;MACRF,KAAK,EAAEwB,WAAW;MAClBjC,SAAS,EAAEZ,UAAU,EAAEiD,WAAW,GAAG,CAAC,CAAC,EAAEpE,eAAe,CAACoE,WAAW,EAAE,EAAE,CAACG,MAAM,CAAC3B,SAAS,EAAE,GAAG,CAAC,CAAC2B,MAAM,CAACtC,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAEjC,eAAe,CAACoE,WAAW,EAAE,EAAE,CAACG,MAAM,CAAC3B,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC4B,QAAQ,CAAC1C,IAAI,CAAC,CAAC,EAAE9B,eAAe,CAACoE,WAAW,EAAE,EAAE,CAACG,MAAM,CAAC3B,SAAS,EAAE,gBAAgB,CAAC,EAAEd,IAAI,KAAK,eAAe,CAAC,EAAE9B,eAAe,CAACoE,WAAW,EAAE,EAAE,CAACG,MAAM,CAAC3B,SAAS,EAAE,WAAW,CAAC,EAAEP,QAAQ,CAAC,EAAE+B,WAAW,GAAGrC,SAAS,CAAC;MAC/ZsB,QAAQ,EAAEA,QAAQ;MAClBP,QAAQ,EAAEA,QAAQ;MAClBF,SAAS,EAAEA,SAAS;MACpBH,QAAQ,EAAEwB;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACArC,IAAI,CAACD,OAAO,GAAGA,OAAO;AACtB,eAAeC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}