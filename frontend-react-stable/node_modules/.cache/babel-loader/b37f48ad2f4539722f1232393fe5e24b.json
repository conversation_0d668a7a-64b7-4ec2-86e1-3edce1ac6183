{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LikeTwoToneSvg from \"@ant-design/icons-svg/es/asn/LikeTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar LikeTwoTone = function LikeTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LikeTwoToneSvg\n  }));\n};\nLikeTwoTone.displayName = 'LikeTwoTone';\nexport default /*#__PURE__*/React.forwardRef(LikeTwoTone);", "map": {"version": 3, "names": ["_objectSpread", "React", "LikeTwoToneSvg", "AntdIcon", "LikeTwoTone", "props", "ref", "createElement", "icon", "displayName", "forwardRef"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/@ant-design/icons/es/icons/LikeTwoTone.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LikeTwoToneSvg from \"@ant-design/icons-svg/es/asn/LikeTwoTone\";\nimport AntdIcon from '../components/AntdIcon';\nvar LikeTwoTone = function LikeTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LikeTwoToneSvg\n  }));\n};\nLikeTwoTone.displayName = 'LikeTwoTone';\nexport default /*#__PURE__*/React.forwardRef(LikeTwoTone);"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACDE,WAAW,CAACK,WAAW,GAAG,aAAa;AACvC,eAAe,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}