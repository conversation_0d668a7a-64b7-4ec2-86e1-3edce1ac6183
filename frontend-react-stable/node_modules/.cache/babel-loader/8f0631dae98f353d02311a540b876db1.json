{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = lab;\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\nvar _color = _interopRequireDefault(require(\"./color.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction lab(start, end) {\n  var l = (0, _color.default)((start = (0, _index.lab)(start)).l, (end = (0, _index.lab)(end)).l),\n    a = (0, _color.default)(start.a, end.a),\n    b = (0, _color.default)(start.b, end.b),\n    opacity = (0, _color.default)(start.opacity, end.opacity);\n  return function (t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "lab", "_index", "require", "_color", "_interopRequireDefault", "obj", "__esModule", "start", "end", "l", "a", "b", "opacity", "t"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-interpolate/src/lab.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = lab;\n\nvar _index = require(\"../../../lib-vendor/d3-color/src/index.js\");\n\nvar _color = _interopRequireDefault(require(\"./color.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction lab(start, end) {\n  var l = (0, _color.default)((start = (0, _index.lab)(start)).l, (end = (0, _index.lab)(end)).l),\n      a = (0, _color.default)(start.a, end.a),\n      b = (0, _color.default)(start.b, end.b),\n      opacity = (0, _color.default)(start.opacity, end.opacity);\n  return function (t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,GAAG;AAErB,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,MAAM,GAAGC,sBAAsB,CAACF,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASL,GAAGA,CAACO,KAAK,EAAEC,GAAG,EAAE;EACvB,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACJ,OAAO,EAAE,CAACQ,KAAK,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACD,GAAG,EAAEO,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAG,CAAC,CAAC,EAAEP,MAAM,CAACD,GAAG,EAAEQ,GAAG,CAAC,EAAEC,CAAC,CAAC;IAC3FC,CAAC,GAAG,CAAC,CAAC,EAAEP,MAAM,CAACJ,OAAO,EAAEQ,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;IACvCC,CAAC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACJ,OAAO,EAAEQ,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;IACvCC,OAAO,GAAG,CAAC,CAAC,EAAET,MAAM,CAACJ,OAAO,EAAEQ,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;EAC7D,OAAO,UAAUC,CAAC,EAAE;IAClBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;IACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;IACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;IACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;IAC1B,OAAON,KAAK,GAAG,EAAE;EACnB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}