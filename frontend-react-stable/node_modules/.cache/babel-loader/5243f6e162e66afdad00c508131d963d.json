{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\nfunction _default(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_default", "a", "b", "NaN"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-shape/src/descending.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = _default;\n\nfunction _default(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,QAAQ;AAE1B,SAASA,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAG,CAAC,GAAGE,GAAG;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}