{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = symlog;\nexports.symlogish = symlogish;\nvar _linear = require(\"./linear.js\");\nvar _continuous = require(\"./continuous.js\");\nvar _init = require(\"./init.js\");\nfunction transformSymlog(c) {\n  return function (x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\nfunction transformSymexp(c) {\n  return function (x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\nfunction symlogish(transform) {\n  var c = 1,\n    scale = transform(transformSymlog(c), transformSymexp(c));\n  scale.constant = function (_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n  return (0, _linear.linearish)(scale);\n}\nfunction symlog() {\n  var scale = symlogish((0, _continuous.transformer)());\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, symlog()).constant(scale.constant());\n  };\n  return _init.initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "symlog", "symlogish", "_linear", "require", "_continuous", "_init", "transformSymlog", "c", "x", "Math", "sign", "log1p", "abs", "transformSymexp", "expm1", "transform", "scale", "constant", "_", "arguments", "length", "linearish", "transformer", "copy", "initRange", "apply"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-scale/src/symlog.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = symlog;\nexports.symlogish = symlogish;\n\nvar _linear = require(\"./linear.js\");\n\nvar _continuous = require(\"./continuous.js\");\n\nvar _init = require(\"./init.js\");\n\nfunction transformSymlog(c) {\n  return function (x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function (x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nfunction symlogish(transform) {\n  var c = 1,\n      scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function (_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return (0, _linear.linearish)(scale);\n}\n\nfunction symlog() {\n  var scale = symlogish((0, _continuous.transformer)());\n\n  scale.copy = function () {\n    return (0, _continuous.copy)(scale, symlog()).constant(scale.constant());\n  };\n\n  return _init.initRange.apply(scale, arguments);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AACxBH,OAAO,CAACI,SAAS,GAAGA,SAAS;AAE7B,IAAIC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEpC,IAAIC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE5C,IAAIE,KAAK,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEhC,SAASG,eAAeA,CAACC,CAAC,EAAE;EAC1B,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGC,IAAI,CAACE,KAAK,CAACF,IAAI,CAACG,GAAG,CAACJ,CAAC,GAAGD,CAAC,CAAC,CAAC;EACnD,CAAC;AACH;AAEA,SAASM,eAAeA,CAACN,CAAC,EAAE;EAC1B,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGC,IAAI,CAACK,KAAK,CAACL,IAAI,CAACG,GAAG,CAACJ,CAAC,CAAC,CAAC,GAAGD,CAAC;EACnD,CAAC;AACH;AAEA,SAASN,SAASA,CAACc,SAAS,EAAE;EAC5B,IAAIR,CAAC,GAAG,CAAC;IACLS,KAAK,GAAGD,SAAS,CAACT,eAAe,CAACC,CAAC,CAAC,EAAEM,eAAe,CAACN,CAAC,CAAC,CAAC;EAE7DS,KAAK,CAACC,QAAQ,GAAG,UAAUC,CAAC,EAAE;IAC5B,OAAOC,SAAS,CAACC,MAAM,GAAGL,SAAS,CAACT,eAAe,CAACC,CAAC,GAAG,CAACW,CAAC,CAAC,EAAEL,eAAe,CAACN,CAAC,CAAC,CAAC,GAAGA,CAAC;EACtF,CAAC;EAED,OAAO,CAAC,CAAC,EAAEL,OAAO,CAACmB,SAAS,EAAEL,KAAK,CAAC;AACtC;AAEA,SAAShB,MAAMA,CAAA,EAAG;EAChB,IAAIgB,KAAK,GAAGf,SAAS,CAAC,CAAC,CAAC,EAAEG,WAAW,CAACkB,WAAW,EAAE,CAAC,CAAC;EAErDN,KAAK,CAACO,IAAI,GAAG,YAAY;IACvB,OAAO,CAAC,CAAC,EAAEnB,WAAW,CAACmB,IAAI,EAAEP,KAAK,EAAEhB,MAAM,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAACD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC1E,CAAC;EAED,OAAOZ,KAAK,CAACmB,SAAS,CAACC,KAAK,CAACT,KAAK,EAAEG,SAAS,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}