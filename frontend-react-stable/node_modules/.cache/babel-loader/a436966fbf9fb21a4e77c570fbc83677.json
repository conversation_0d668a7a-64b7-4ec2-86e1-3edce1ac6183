{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n// 从localStorage获取初始状态\nconst getInitialAuthState = () => {\n  const token = localStorage.getItem('token');\n  const username = localStorage.getItem('username');\n  return {\n    isAuthenticated: !!(token && username),\n    user: token && username ? {\n      username: username,\n      isDefault: false // 刷新时假设已经修改过密码\n    } : null,\n    token: token,\n    loading: false,\n    error: null,\n    showChangePassword: false,\n    forceChangePassword: false\n  };\n};\nconst initialState = getInitialAuthState();\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk('auth/login', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.login(credentials);\n    const {\n      access_token\n    } = response.data;\n\n    // 获取用户信息\n    const userResponse = await authAPI.getUsers(access_token);\n    const userData = userResponse.data[credentials.username];\n    return {\n      token: access_token,\n      user: {\n        username: credentials.username,\n        isDefault: (userData === null || userData === void 0 ? void 0 : userData.is_default) || false\n      }\n    };\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '登录失败');\n  }\n});\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk('auth/changePassword', async (data, {\n  getState,\n  rejectWithValue\n}) => {\n  try {\n    const state = getState();\n    const response = await authAPI.changePassword(data, state.auth.token);\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || '修改密码失败');\n  }\n});\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: state => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: state => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // 登录\n    .addCase(loginAsync.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(loginAsync.fulfilled, (state, action) => {\n      state.loading = false;\n      state.isAuthenticated = true;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.showChangePassword = action.payload.user.isDefault;\n      state.forceChangePassword = action.payload.user.isDefault;\n      localStorage.setItem('token', action.payload.token);\n      localStorage.setItem('username', action.payload.user.username);\n    }).addCase(loginAsync.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    })\n    // 修改密码\n    .addCase(changePasswordAsync.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(changePasswordAsync.fulfilled, state => {\n      state.loading = false;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      if (state.user) {\n        state.user.isDefault = false;\n      }\n    }).addCase(changePasswordAsync.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.payload;\n    });\n  }\n});\nexport const {\n  logout,\n  clearError,\n  setShowChangePassword,\n  restoreAuth\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authAPI", "getInitialAuthState", "token", "localStorage", "getItem", "username", "isAuthenticated", "user", "isDefault", "loading", "error", "showChangePassword", "forceChangePassword", "initialState", "loginAsync", "credentials", "rejectWithValue", "response", "login", "access_token", "data", "userResponse", "getUsers", "userData", "is_default", "_error$response", "_error$response$data", "detail", "changePasswordAsync", "getState", "state", "changePassword", "auth", "_error$response2", "_error$response2$data", "authSlice", "name", "reducers", "logout", "removeItem", "clearError", "setShowChangePassword", "action", "payload", "restoreAuth", "extraReducers", "builder", "addCase", "pending", "fulfilled", "setItem", "rejected", "actions", "reducer"], "sources": ["/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\nexport interface User {\n  username: string;\n  isDefault: boolean;\n}\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  user: User | null;\n  token: string | null;\n  loading: boolean;\n  error: string | null;\n  showChangePassword: boolean;\n  forceChangePassword: boolean;\n}\n\n// 从localStorage获取初始状态\nconst getInitialAuthState = (): AuthState => {\n  const token = localStorage.getItem('token');\n  const username = localStorage.getItem('username');\n\n  return {\n    isAuthenticated: !!(token && username),\n    user: token && username ? {\n      username: username,\n      isDefault: false, // 刷新时假设已经修改过密码\n    } : null,\n    token: token,\n    loading: false,\n    error: null,\n    showChangePassword: false,\n    forceChangePassword: false,\n  };\n};\n\nconst initialState: AuthState = getInitialAuthState();\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { access_token } = response.data;\n      \n      // 获取用户信息\n      const userResponse = await authAPI.getUsers(access_token);\n      const userData = userResponse.data[credentials.username];\n      \n      return {\n        token: access_token,\n        user: {\n          username: credentials.username,\n          isDefault: userData?.is_default || false,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '登录失败');\n    }\n  }\n);\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk(\n  'auth/changePassword',\n  async (\n    data: { username: string; old_password: string; new_password: string; confirm_password: string },\n    { getState, rejectWithValue }\n  ) => {\n    try {\n      const state = getState() as { auth: AuthState };\n      const response = await authAPI.changePassword(data, state.auth.token!);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '修改密码失败');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: (state) => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action: PayloadAction<boolean>) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false, // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // 登录\n      .addCase(loginAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(loginAsync.fulfilled, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = true;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.showChangePassword = action.payload.user.isDefault;\n        state.forceChangePassword = action.payload.user.isDefault;\n        localStorage.setItem('token', action.payload.token);\n        localStorage.setItem('username', action.payload.user.username);\n      })\n      .addCase(loginAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // 修改密码\n      .addCase(changePasswordAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(changePasswordAsync.fulfilled, (state) => {\n        state.loading = false;\n        state.showChangePassword = false;\n        state.forceChangePassword = false;\n        if (state.user) {\n          state.user.isDefault = false;\n        }\n      })\n      .addCase(changePasswordAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const { logout, clearError, setShowChangePassword, restoreAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,OAAO,QAAQ,oBAAoB;AAiB5C;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAiB;EAC3C,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAEjD,OAAO;IACLE,eAAe,EAAE,CAAC,EAAEJ,KAAK,IAAIG,QAAQ,CAAC;IACtCE,IAAI,EAAEL,KAAK,IAAIG,QAAQ,GAAG;MACxBA,QAAQ,EAAEA,QAAQ;MAClBG,SAAS,EAAE,KAAK,CAAE;IACpB,CAAC,GAAG,IAAI;IACRN,KAAK,EAAEA,KAAK;IACZO,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE,IAAI;IACXC,kBAAkB,EAAE,KAAK;IACzBC,mBAAmB,EAAE;EACvB,CAAC;AACH,CAAC;AAED,MAAMC,YAAuB,GAAGZ,mBAAmB,CAAC,CAAC;;AAErD;AACA,OAAO,MAAMa,UAAU,GAAGf,gBAAgB,CACxC,YAAY,EACZ,OAAOgB,WAAmD,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAClF,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMjB,OAAO,CAACkB,KAAK,CAACH,WAAW,CAAC;IACjD,MAAM;MAAEI;IAAa,CAAC,GAAGF,QAAQ,CAACG,IAAI;;IAEtC;IACA,MAAMC,YAAY,GAAG,MAAMrB,OAAO,CAACsB,QAAQ,CAACH,YAAY,CAAC;IACzD,MAAMI,QAAQ,GAAGF,YAAY,CAACD,IAAI,CAACL,WAAW,CAACV,QAAQ,CAAC;IAExD,OAAO;MACLH,KAAK,EAAEiB,YAAY;MACnBZ,IAAI,EAAE;QACJF,QAAQ,EAAEU,WAAW,CAACV,QAAQ;QAC9BG,SAAS,EAAE,CAAAe,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,UAAU,KAAI;MACrC;IACF,CAAC;EACH,CAAC,CAAC,OAAOd,KAAU,EAAE;IAAA,IAAAe,eAAA,EAAAC,oBAAA;IACnB,OAAOV,eAAe,CAAC,EAAAS,eAAA,GAAAf,KAAK,CAACO,QAAQ,cAAAQ,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBL,IAAI,cAAAM,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAI,MAAM,CAAC;EAChE;AACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAG7B,gBAAgB,CACjD,qBAAqB,EACrB,OACEqB,IAAgG,EAChG;EAAES,QAAQ;EAAEb;AAAgB,CAAC,KAC1B;EACH,IAAI;IACF,MAAMc,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAC/C,MAAMZ,QAAQ,GAAG,MAAMjB,OAAO,CAAC+B,cAAc,CAACX,IAAI,EAAEU,KAAK,CAACE,IAAI,CAAC9B,KAAM,CAAC;IACtE,OAAOe,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOV,KAAU,EAAE;IAAA,IAAAuB,gBAAA,EAAAC,qBAAA;IACnB,OAAOlB,eAAe,CAAC,EAAAiB,gBAAA,GAAAvB,KAAK,CAACO,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI,QAAQ,CAAC;EAClE;AACF,CACF,CAAC;AAED,MAAMQ,SAAS,GAAGrC,WAAW,CAAC;EAC5BsC,IAAI,EAAE,MAAM;EACZvB,YAAY;EACZwB,QAAQ,EAAE;IACRC,MAAM,EAAGR,KAAK,IAAK;MACjBA,KAAK,CAACxB,eAAe,GAAG,KAAK;MAC7BwB,KAAK,CAACvB,IAAI,GAAG,IAAI;MACjBuB,KAAK,CAAC5B,KAAK,GAAG,IAAI;MAClB4B,KAAK,CAACnB,kBAAkB,GAAG,KAAK;MAChCmB,KAAK,CAAClB,mBAAmB,GAAG,KAAK;MACjCT,YAAY,CAACoC,UAAU,CAAC,OAAO,CAAC;MAChCpC,YAAY,CAACoC,UAAU,CAAC,UAAU,CAAC;IACrC,CAAC;IACDC,UAAU,EAAGV,KAAK,IAAK;MACrBA,KAAK,CAACpB,KAAK,GAAG,IAAI;IACpB,CAAC;IACD+B,qBAAqB,EAAEA,CAACX,KAAK,EAAEY,MAA8B,KAAK;MAChEZ,KAAK,CAACnB,kBAAkB,GAAG+B,MAAM,CAACC,OAAO;IAC3C,CAAC;IACD;IACAC,WAAW,EAAGd,KAAK,IAAK;MACtB,MAAM5B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;QACrByB,KAAK,CAAC5B,KAAK,GAAGA,KAAK;QACnB4B,KAAK,CAACxB,eAAe,GAAG,IAAI;QAC5BwB,KAAK,CAACvB,IAAI,GAAG;UACXF,QAAQ,EAAEA,QAAQ;UAClBG,SAAS,EAAE,KAAK,CAAE;QACpB,CAAC;QACD;MACF;IACF;EACF,CAAC;EACDqC,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACjC,UAAU,CAACkC,OAAO,EAAGlB,KAAK,IAAK;MACtCA,KAAK,CAACrB,OAAO,GAAG,IAAI;MACpBqB,KAAK,CAACpB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDqC,OAAO,CAACjC,UAAU,CAACmC,SAAS,EAAE,CAACnB,KAAK,EAAEY,MAAM,KAAK;MAChDZ,KAAK,CAACrB,OAAO,GAAG,KAAK;MACrBqB,KAAK,CAACxB,eAAe,GAAG,IAAI;MAC5BwB,KAAK,CAACvB,IAAI,GAAGmC,MAAM,CAACC,OAAO,CAACpC,IAAI;MAChCuB,KAAK,CAAC5B,KAAK,GAAGwC,MAAM,CAACC,OAAO,CAACzC,KAAK;MAClC4B,KAAK,CAACnB,kBAAkB,GAAG+B,MAAM,CAACC,OAAO,CAACpC,IAAI,CAACC,SAAS;MACxDsB,KAAK,CAAClB,mBAAmB,GAAG8B,MAAM,CAACC,OAAO,CAACpC,IAAI,CAACC,SAAS;MACzDL,YAAY,CAAC+C,OAAO,CAAC,OAAO,EAAER,MAAM,CAACC,OAAO,CAACzC,KAAK,CAAC;MACnDC,YAAY,CAAC+C,OAAO,CAAC,UAAU,EAAER,MAAM,CAACC,OAAO,CAACpC,IAAI,CAACF,QAAQ,CAAC;IAChE,CAAC,CAAC,CACD0C,OAAO,CAACjC,UAAU,CAACqC,QAAQ,EAAE,CAACrB,KAAK,EAAEY,MAAM,KAAK;MAC/CZ,KAAK,CAACrB,OAAO,GAAG,KAAK;MACrBqB,KAAK,CAACpB,KAAK,GAAGgC,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCI,OAAO,CAACnB,mBAAmB,CAACoB,OAAO,EAAGlB,KAAK,IAAK;MAC/CA,KAAK,CAACrB,OAAO,GAAG,IAAI;MACpBqB,KAAK,CAACpB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDqC,OAAO,CAACnB,mBAAmB,CAACqB,SAAS,EAAGnB,KAAK,IAAK;MACjDA,KAAK,CAACrB,OAAO,GAAG,KAAK;MACrBqB,KAAK,CAACnB,kBAAkB,GAAG,KAAK;MAChCmB,KAAK,CAAClB,mBAAmB,GAAG,KAAK;MACjC,IAAIkB,KAAK,CAACvB,IAAI,EAAE;QACduB,KAAK,CAACvB,IAAI,CAACC,SAAS,GAAG,KAAK;MAC9B;IACF,CAAC,CAAC,CACDuC,OAAO,CAACnB,mBAAmB,CAACuB,QAAQ,EAAE,CAACrB,KAAK,EAAEY,MAAM,KAAK;MACxDZ,KAAK,CAACrB,OAAO,GAAG,KAAK;MACrBqB,KAAK,CAACpB,KAAK,GAAGgC,MAAM,CAACC,OAAiB;IACxC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEL,MAAM;EAAEE,UAAU;EAAEC,qBAAqB;EAAEG;AAAY,CAAC,GAAGT,SAAS,CAACiB,OAAO;AAC3F,eAAejB,SAAS,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}