{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/frontend-react-stable/src/components/TaskStatusIndicator.tsx\",\n  _s = $RefreshSig$();\n/**\n * 任务状态指示器组件\n * 在Header中显示运行中任务的简要信息\n */\n\nimport React, { useState } from 'react';\nimport { Badge, Button, Dropdown, Menu, Empty, Space, Typography, Progress, Modal, Card, Row, Col, Statistic, List, Tag } from 'antd';\nimport { ClockCircleOutlined, PlayCircleOutlined, EyeOutlined, StopOutlined, CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, DeleteOutlined } from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst TaskStatusIndicator = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    runningTasks,\n    completedTasks,\n    cancelTask,\n    formatTaskType,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    clearCompletedTasks\n  } = useTaskManager();\n  const [taskManagerVisible, setTaskManagerVisible] = useState(false);\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n\n  // 查看任务详情\n  const handleViewTaskDetail = task => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = status => {\n    switch (status) {\n      case 'running':\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true,\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#ff4d4f'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(StopOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {\n          style: {\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = status => {\n    switch (status) {\n      case 'running':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          children: \"\\u8FD0\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          children: \"\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 16\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          children: \"\\u5DF2\\u53D6\\u6D88\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"\\u7B49\\u5F85\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 创建下拉菜单\n  const menu = /*#__PURE__*/_jsxDEV(Menu, {\n    style: {\n      maxWidth: 400,\n      maxHeight: 400,\n      overflow: 'auto'\n    },\n    children: [runningTasks.length === 0 ? /*#__PURE__*/_jsxDEV(Menu.Item, {\n      disabled: true,\n      children: /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6682\\u65E0\\u8FD0\\u884C\\u4E2D\\u7684\\u4EFB\\u52A1\",\n        image: Empty.PRESENTED_IMAGE_SIMPLE,\n        style: {\n          margin: '16px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)\n    }, \"empty\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this) : runningTasks.map(task => /*#__PURE__*/_jsxDEV(Menu.Item, {\n      style: {\n        height: 'auto',\n        padding: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(PlayCircleOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: formatTaskType(task.type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 25\n            }, this),\n            onClick: e => {\n              e.stopPropagation();\n              cancelTask(task.task_id);\n            },\n            children: \"\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Progress, {\n            percent: task.progress || 0,\n            size: \"small\",\n            showInfo: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 15\n        }, this), task.current_step && /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: task.current_step\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8,\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: 11\n            },\n            children: [\"ID: \", task.task_id.substring(0, 8), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 13\n      }, this)\n    }, task.task_id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 11\n    }, this)), runningTasks.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Menu.Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 21\n          }, this),\n          onClick: () => setTaskManagerVisible(true),\n          style: {\n            padding: 0\n          },\n          children: \"\\u67E5\\u770B\\u6240\\u6709\\u4EFB\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this)\n      }, \"view-all\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n\n  // 处理任务按钮点击\n  const handleTaskButtonClick = async () => {\n    try {\n      await fetchRunningTasks(false); // 静默获取任务\n      setTaskManagerVisible(true);\n    } catch (error) {\n      console.warn('获取任务失败:', error);\n      setTaskManagerVisible(true); // 即使失败也显示任务管理界面\n    }\n  };\n\n  // 如果没有运行中的任务，只显示一个简单的按钮\n  if (runningTasks.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Button, {\n      type: \"text\",\n      icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 15\n      }, this),\n      style: {\n        color: 'white'\n      },\n      onClick: handleTaskButtonClick,\n      children: \"\\u4EFB\\u52A1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n      overlay: menu,\n      trigger: ['click'],\n      placement: \"bottomRight\",\n      overlayStyle: {\n        zIndex: 1050\n      },\n      children: /*#__PURE__*/_jsxDEV(Badge, {\n        count: runningTasks.length,\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 19\n          }, this),\n          style: {\n            color: 'white'\n          },\n          children: \"\\u4EFB\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u7BA1\\u7406\",\n      visible: taskManagerVisible,\n      onCancel: () => setTaskManagerVisible(false),\n      footer: null,\n      width: \"90%\",\n      style: {\n        top: 20\n      },\n      bodyStyle: {\n        maxHeight: 'calc(100vh - 200px)',\n        overflow: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n                  value: runningTasks.length,\n                  prefix: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n                    spin: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n                  value: completedTasks.length,\n                  prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"\\u603B\\u4EFB\\u52A1\\u6570\",\n                  value: runningTasks.length + completedTasks.length,\n                  prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 6,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 27\n                  }, this),\n                  onClick: clearCompletedTasks,\n                  disabled: completedTasks.length === 0,\n                  children: \"\\u6E05\\u7A7A\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u4EFB\\u52A1\",\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: runningTasks,\n              locale: {\n                emptyText: '暂无运行中的任务'\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => cancelTask(task.task_id),\n                  children: \"\\u53D6\\u6D88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"ID: \", task.task_id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5F00\\u59CB\\u65F6\\u95F4: \", task.created_at]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 27\n                    }, this), task.progress !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 8\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Progress, {\n                        percent: task.progress,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u4EFB\\u52A1\",\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dataSource: completedTasks,\n              locale: {\n                emptyText: '暂无已完成的任务'\n              },\n              renderItem: task => /*#__PURE__*/_jsxDEV(List.Item, {\n                actions: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 31\n                  }, this),\n                  onClick: () => handleViewTaskDetail(task),\n                  children: \"\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this)],\n                children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  avatar: getTaskStatusIcon(task.status),\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      children: formatTaskType(task.task_type)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 27\n                    }, this), getTaskStatusTag(task.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 25\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"ID: \", task.task_id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u5B8C\\u6210\\u65F6\\u95F4: \", task.updated_at]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EFB\\u52A1\\u8BE6\\u60C5\",\n      visible: taskDetailVisible,\n      onCancel: () => setTaskDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setTaskDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              copyable: true,\n              children: selectedTask.task_id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u4EFB\\u52A1\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: formatTaskType(selectedTask.task_type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), getTaskStatusTag(selectedTask.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u8FDB\\u5EA6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), selectedTask.progress !== undefined ? /*#__PURE__*/_jsxDEV(Progress, {\n              percent: selectedTask.progress,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u65E0\\u8FDB\\u5EA6\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.created_at\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u66F4\\u65B0\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.updated_at\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), selectedTask.message && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u6D88\\u606F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              children: selectedTask.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), selectedTask.result && /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u7ED3\\u679C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 8,\n                borderRadius: 4,\n                fontSize: 12\n              },\n              children: JSON.stringify(selectedTask.result, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(TaskStatusIndicator, \"n1Nt3PMhh1p6KHFrDa7ODsbmx0w=\", false, function () {\n  return [useNavigate, useTaskManager];\n});\n_c = TaskStatusIndicator;\nexport default TaskStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"TaskStatusIndicator\");", "map": {"version": 3, "names": ["React", "useState", "Badge", "<PERSON><PERSON>", "Dropdown", "<PERSON><PERSON>", "Empty", "Space", "Typography", "Progress", "Modal", "Card", "Row", "Col", "Statistic", "List", "Tag", "ClockCircleOutlined", "PlayCircleOutlined", "EyeOutlined", "StopOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "SyncOutlined", "DeleteOutlined", "useTaskManager", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "TaskStatusIndicator", "_s", "navigate", "runningTasks", "completedTasks", "cancelTask", "formatTaskType", "fetchRunningTasks", "fetchCompletedTasks", "clearCompletedTasks", "taskManagerVisible", "setTaskManagerVisible", "selectedTask", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "handleViewTaskDetail", "task", "getTaskStatusIcon", "status", "spin", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTaskStatusTag", "children", "menu", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "length", "<PERSON><PERSON>", "disabled", "description", "image", "PRESENTED_IMAGE_SIMPLE", "margin", "map", "height", "padding", "display", "justifyContent", "alignItems", "marginBottom", "strong", "type", "size", "danger", "icon", "onClick", "e", "stopPropagation", "task_id", "percent", "progress", "showInfo", "current_step", "fontSize", "marginTop", "textAlign", "substring", "Divider", "handleTaskButtonClick", "error", "console", "warn", "overlay", "trigger", "placement", "overlayStyle", "zIndex", "count", "title", "visible", "onCancel", "footer", "width", "top", "bodyStyle", "gutter", "span", "value", "prefix", "valueStyle", "dataSource", "locale", "emptyText", "renderItem", "actions", "Meta", "avatar", "task_type", "created_at", "undefined", "updated_at", "copyable", "message", "result", "background", "borderRadius", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/frontend-react-stable/src/components/TaskStatusIndicator.tsx"], "sourcesContent": ["/**\n * 任务状态指示器组件\n * 在Header中显示运行中任务的简要信息\n */\n\nimport React, { useState } from 'react';\nimport {\n  Badge,\n  Button,\n  Dropdown,\n  Menu,\n  Empty,\n  Space,\n  Typography,\n  Progress,\n  Modal,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Tag,\n  Divider\n} from 'antd';\nimport {\n  ClockCircleOutlined,\n  PlayCircleOutlined,\n  EyeOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  SyncOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Text } = Typography;\n\nconst TaskStatusIndicator: React.FC = () => {\n  const navigate = useNavigate();\n  const {\n    runningTasks,\n    completedTasks,\n    cancelTask,\n    formatTaskType,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    clearCompletedTasks\n  } = useTaskManager();\n\n  const [taskManagerVisible, setTaskManagerVisible] = useState(false);\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case 'running':\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case 'completed':\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'failed':\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case 'cancelled':\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case 'running':\n        return <Tag color=\"processing\">运行中</Tag>;\n      case 'completed':\n        return <Tag color=\"success\">已完成</Tag>;\n      case 'failed':\n        return <Tag color=\"error\">失败</Tag>;\n      case 'cancelled':\n        return <Tag color=\"warning\">已取消</Tag>;\n      default:\n        return <Tag color=\"default\">等待中</Tag>;\n    }\n  };\n\n  // 创建下拉菜单\n  const menu = (\n    <Menu style={{ maxWidth: 400, maxHeight: 400, overflow: 'auto' }}>\n      {runningTasks.length === 0 ? (\n        <Menu.Item key=\"empty\" disabled>\n          <Empty \n            description=\"暂无运行中的任务\" \n            image={Empty.PRESENTED_IMAGE_SIMPLE}\n            style={{ margin: '16px 0' }}\n          />\n        </Menu.Item>\n      ) : (\n        runningTasks.map(task => (\n          <Menu.Item key={task.task_id} style={{ height: 'auto', padding: 8 }}>\n            <div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>\n                <Space>\n                  <PlayCircleOutlined style={{ color: '#1890ff' }} />\n                  <Text strong>{formatTaskType(task.type)}</Text>\n                </Space>\n                <Button \n                  size=\"small\" \n                  danger \n                  icon={<StopOutlined />}\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    cancelTask(task.task_id);\n                  }}\n                >\n                  取消\n                </Button>\n              </div>\n              \n              <div style={{ marginBottom: 8 }}>\n                <Progress \n                  percent={task.progress || 0} \n                  size=\"small\"\n                  showInfo={true}\n                />\n              </div>\n              \n              {task.current_step && (\n                <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                  {task.current_step}\n                </Text>\n              )}\n              \n              <div style={{ marginTop: 8, textAlign: 'right' }}>\n                <Text type=\"secondary\" style={{ fontSize: 11 }}>\n                  ID: {task.task_id.substring(0, 8)}...\n                </Text>\n              </div>\n            </div>\n          </Menu.Item>\n        ))\n      )}\n      \n      {runningTasks.length > 0 && (\n        <>\n          <Menu.Divider />\n          <Menu.Item key=\"view-all\">\n            <Button \n              type=\"link\" \n              icon={<EyeOutlined />}\n              onClick={() => setTaskManagerVisible(true)}\n              style={{ padding: 0 }}\n            >\n              查看所有任务\n            </Button>\n          </Menu.Item>\n        </>\n      )}\n    </Menu>\n  );\n\n  // 处理任务按钮点击\n  const handleTaskButtonClick = async () => {\n    try {\n      await fetchRunningTasks(false); // 静默获取任务\n      setTaskManagerVisible(true);\n    } catch (error) {\n      console.warn('获取任务失败:', error);\n      setTaskManagerVisible(true); // 即使失败也显示任务管理界面\n    }\n  };\n\n  // 如果没有运行中的任务，只显示一个简单的按钮\n  if (runningTasks.length === 0) {\n    return (\n      <Button\n        type=\"text\"\n        icon={<ClockCircleOutlined />}\n        style={{ color: 'white' }}\n        onClick={handleTaskButtonClick}\n      >\n        任务\n      </Button>\n    );\n  }\n\n  return (\n    <>\n      <Dropdown\n        overlay={menu}\n        trigger={['click']}\n        placement=\"bottomRight\"\n        overlayStyle={{ zIndex: 1050 }}\n      >\n        <Badge count={runningTasks.length} size=\"small\">\n          <Button\n            type=\"text\"\n            icon={<ClockCircleOutlined />}\n            style={{ color: 'white' }}\n          >\n            任务\n          </Button>\n        </Badge>\n      </Dropdown>\n\n      {/* 任务管理模态框 */}\n      <Modal\n        title=\"任务管理\"\n        visible={taskManagerVisible}\n        onCancel={() => setTaskManagerVisible(false)}\n        footer={null}\n        width=\"90%\"\n        style={{ top: 20 }}\n        bodyStyle={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}\n      >\n        <Row gutter={[16, 16]}>\n          {/* 任务统计 */}\n          <Col span={24}>\n            <Card>\n              <Row gutter={16}>\n                <Col span={6}>\n                  <Statistic\n                    title=\"运行中任务\"\n                    value={runningTasks.length}\n                    prefix={<SyncOutlined spin />}\n                    valueStyle={{ color: '#1890ff' }}\n                  />\n                </Col>\n                <Col span={6}>\n                  <Statistic\n                    title=\"已完成任务\"\n                    value={completedTasks.length}\n                    prefix={<CheckCircleOutlined />}\n                    valueStyle={{ color: '#52c41a' }}\n                  />\n                </Col>\n                <Col span={6}>\n                  <Statistic\n                    title=\"总任务数\"\n                    value={runningTasks.length + completedTasks.length}\n                    prefix={<ClockCircleOutlined />}\n                  />\n                </Col>\n                <Col span={6}>\n                  <Button\n                    type=\"primary\"\n                    danger\n                    icon={<DeleteOutlined />}\n                    onClick={clearCompletedTasks}\n                    disabled={completedTasks.length === 0}\n                  >\n                    清空已完成\n                  </Button>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n\n          {/* 运行中任务 */}\n          <Col span={12}>\n            <Card title=\"运行中任务\" size=\"small\">\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: '暂无运行中的任务' }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Button\n                        type=\"link\"\n                        danger\n                        icon={<StopOutlined />}\n                        onClick={() => cancelTask(task.task_id)}\n                      >\n                        取消\n                      </Button>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\">ID: {task.task_id}</Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {task.created_at}</Text>\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Card>\n          </Col>\n\n          {/* 已完成任务 */}\n          <Col span={12}>\n            <Card title=\"已完成任务\" size=\"small\">\n              <List\n                dataSource={completedTasks}\n                locale={{ emptyText: '暂无已完成的任务' }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\">ID: {task.task_id}</Text>\n                          <br />\n                          <Text type=\"secondary\">完成时间: {task.updated_at}</Text>\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Modal>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        visible={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={24}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{selectedTask.created_at}</Text>\n              </Col>\n              <Col span={24}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{selectedTask.updated_at}</Text>\n              </Col>\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.result && (\n                <Col span={24}>\n                  <Text strong>结果:</Text>\n                  <br />\n                  <pre style={{ background: '#f5f5f5', padding: 8, borderRadius: 4, fontSize: 12 }}>\n                    {JSON.stringify(selectedTask.result, null, 2)}\n                  </pre>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </>\n  );\n};\n\nexport default TaskStatusIndicator;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,GAAG,QAEE,MAAM;AACb,SACEC,mBAAmB,EACnBC,kBAAkB,EAClBC,WAAW,EACXC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,yBAAyB;AAEpD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAM;EAAEC;AAAK,CAAC,GAAGvB,UAAU;AAE3B,MAAMwB,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJS,YAAY;IACZC,cAAc;IACdC,UAAU;IACVC,cAAc;IACdC,iBAAiB;IACjBC,mBAAmB;IACnBC;EACF,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACiB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM+C,oBAAoB,GAAIC,IAAU,IAAK;IAC3CJ,eAAe,CAACI,IAAI,CAAC;IACrBF,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,MAAc,IAAK;IAC5C,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOvB,OAAA,CAACL,YAAY;UAAC6B,IAAI;UAACC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,WAAW;QACd,oBAAO9B,OAAA,CAACP,mBAAmB;UAACgC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,QAAQ;QACX,oBAAO9B,OAAA,CAACN,mBAAmB;UAAC+B,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,WAAW;QACd,oBAAO9B,OAAA,CAACR,YAAY;UAACiC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAO9B,OAAA,CAACX,mBAAmB;UAACoC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIR,MAAc,IAAK;IAC3C,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOvB,OAAA,CAACZ,GAAG;UAACsC,KAAK,EAAC,YAAY;UAAAM,QAAA,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC1C,KAAK,WAAW;QACd,oBAAO9B,OAAA,CAACZ,GAAG;UAACsC,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvC,KAAK,QAAQ;QACX,oBAAO9B,OAAA,CAACZ,GAAG;UAACsC,KAAK,EAAC,OAAO;UAAAM,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpC,KAAK,WAAW;QACd,oBAAO9B,OAAA,CAACZ,GAAG;UAACsC,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvC;QACE,oBAAO9B,OAAA,CAACZ,GAAG;UAACsC,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMG,IAAI,gBACRjC,OAAA,CAACvB,IAAI;IAACgD,KAAK,EAAE;MAAES,QAAQ,EAAE,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAJ,QAAA,GAC9DzB,YAAY,CAAC8B,MAAM,KAAK,CAAC,gBACxBrC,OAAA,CAACvB,IAAI,CAAC6D,IAAI;MAAaC,QAAQ;MAAAP,QAAA,eAC7BhC,OAAA,CAACtB,KAAK;QACJ8D,WAAW,EAAC,kDAAU;QACtBC,KAAK,EAAE/D,KAAK,CAACgE,sBAAuB;QACpCjB,KAAK,EAAE;UAAEkB,MAAM,EAAE;QAAS;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC,GALW,OAAO;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMX,CAAC,GAEZvB,YAAY,CAACqC,GAAG,CAACvB,IAAI,iBACnBrB,OAAA,CAACvB,IAAI,CAAC6D,IAAI;MAAoBb,KAAK,EAAE;QAAEoB,MAAM,EAAE,MAAM;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAAd,QAAA,eAClEhC,OAAA;QAAAgC,QAAA,gBACEhC,OAAA;UAAKyB,KAAK,EAAE;YAAEsB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACtGhC,OAAA,CAACrB,KAAK;YAAAqD,QAAA,gBACJhC,OAAA,CAACV,kBAAkB;cAACmC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnD9B,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAEtB,cAAc,CAACW,IAAI,CAAC+B,IAAI;YAAC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACR9B,OAAA,CAACzB,MAAM;YACL8E,IAAI,EAAC,OAAO;YACZC,MAAM;YACNC,IAAI,eAAEvD,OAAA,CAACR,YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBjD,UAAU,CAACY,IAAI,CAACsC,OAAO,CAAC;YAC1B,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9B,OAAA;UAAKyB,KAAK,EAAE;YAAEyB,YAAY,EAAE;UAAE,CAAE;UAAAlB,QAAA,eAC9BhC,OAAA,CAACnB,QAAQ;YACP+E,OAAO,EAAEvC,IAAI,CAACwC,QAAQ,IAAI,CAAE;YAC5BR,IAAI,EAAC,OAAO;YACZS,QAAQ,EAAE;UAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELT,IAAI,CAAC0C,YAAY,iBAChB/D,OAAA,CAACG,IAAI;UAACiD,IAAI,EAAC,WAAW;UAAC3B,KAAK,EAAE;YAAEuC,QAAQ,EAAE;UAAG,CAAE;UAAAhC,QAAA,EAC5CX,IAAI,CAAC0C;QAAY;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACP,eAED9B,OAAA;UAAKyB,KAAK,EAAE;YAAEwC,SAAS,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAAlC,QAAA,eAC/ChC,OAAA,CAACG,IAAI;YAACiD,IAAI,EAAC,WAAW;YAAC3B,KAAK,EAAE;cAAEuC,QAAQ,EAAE;YAAG,CAAE;YAAAhC,QAAA,GAAC,MAC1C,EAACX,IAAI,CAACsC,OAAO,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KACpC;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAvCQT,IAAI,CAACsC,OAAO;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwCjB,CACZ,CACF,EAEAvB,YAAY,CAAC8B,MAAM,GAAG,CAAC,iBACtBrC,OAAA,CAAAE,SAAA;MAAA8B,QAAA,gBACEhC,OAAA,CAACvB,IAAI,CAAC2F,OAAO;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChB9B,OAAA,CAACvB,IAAI,CAAC6D,IAAI;QAAAN,QAAA,eACRhC,OAAA,CAACzB,MAAM;UACL6E,IAAI,EAAC,MAAM;UACXG,IAAI,eAAEvD,OAAA,CAACT,WAAW;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB0B,OAAO,EAAEA,CAAA,KAAMzC,qBAAqB,CAAC,IAAI,CAAE;UAC3CU,KAAK,EAAE;YAAEqB,OAAO,EAAE;UAAE,CAAE;UAAAd,QAAA,EACvB;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC,GARI,UAAU;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASd,CAAC;IAAA,eACZ,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;;EAED;EACA,MAAMuC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAM1D,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;MAChCI,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAEF,KAAK,CAAC;MAC9BvD,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,IAAIR,YAAY,CAAC8B,MAAM,KAAK,CAAC,EAAE;IAC7B,oBACErC,OAAA,CAACzB,MAAM;MACL6E,IAAI,EAAC,MAAM;MACXG,IAAI,eAAEvD,OAAA,CAACX,mBAAmB;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC9BL,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAQ,CAAE;MAC1B8B,OAAO,EAAEa,qBAAsB;MAAArC,QAAA,EAChC;IAED;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAEb;EAEA,oBACE9B,OAAA,CAAAE,SAAA;IAAA8B,QAAA,gBACEhC,OAAA,CAACxB,QAAQ;MACPiG,OAAO,EAAExC,IAAK;MACdyC,OAAO,EAAE,CAAC,OAAO,CAAE;MACnBC,SAAS,EAAC,aAAa;MACvBC,YAAY,EAAE;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAA7C,QAAA,eAE/BhC,OAAA,CAAC1B,KAAK;QAACwG,KAAK,EAAEvE,YAAY,CAAC8B,MAAO;QAACgB,IAAI,EAAC,OAAO;QAAArB,QAAA,eAC7ChC,OAAA,CAACzB,MAAM;UACL6E,IAAI,EAAC,MAAM;UACXG,IAAI,eAAEvD,OAAA,CAACX,mBAAmB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BL,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAM,QAAA,EAC3B;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX9B,OAAA,CAAClB,KAAK;MACJiG,KAAK,EAAC,0BAAM;MACZC,OAAO,EAAElE,kBAAmB;MAC5BmE,QAAQ,EAAEA,CAAA,KAAMlE,qBAAqB,CAAC,KAAK,CAAE;MAC7CmE,MAAM,EAAE,IAAK;MACbC,KAAK,EAAC,KAAK;MACX1D,KAAK,EAAE;QAAE2D,GAAG,EAAE;MAAG,CAAE;MACnBC,SAAS,EAAE;QAAElD,SAAS,EAAE,qBAAqB;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAJ,QAAA,eAElEhC,OAAA,CAAChB,GAAG;QAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAtD,QAAA,gBAEpBhC,OAAA,CAACf,GAAG;UAACsG,IAAI,EAAE,EAAG;UAAAvD,QAAA,eACZhC,OAAA,CAACjB,IAAI;YAAAiD,QAAA,eACHhC,OAAA,CAAChB,GAAG;cAACsG,MAAM,EAAE,EAAG;cAAAtD,QAAA,gBACdhC,OAAA,CAACf,GAAG;gBAACsG,IAAI,EAAE,CAAE;gBAAAvD,QAAA,eACXhC,OAAA,CAACd,SAAS;kBACR6F,KAAK,EAAC,gCAAO;kBACbS,KAAK,EAAEjF,YAAY,CAAC8B,MAAO;kBAC3BoD,MAAM,eAAEzF,OAAA,CAACL,YAAY;oBAAC6B,IAAI;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC9B4D,UAAU,EAAE;oBAAEhE,KAAK,EAAE;kBAAU;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9B,OAAA,CAACf,GAAG;gBAACsG,IAAI,EAAE,CAAE;gBAAAvD,QAAA,eACXhC,OAAA,CAACd,SAAS;kBACR6F,KAAK,EAAC,gCAAO;kBACbS,KAAK,EAAEhF,cAAc,CAAC6B,MAAO;kBAC7BoD,MAAM,eAAEzF,OAAA,CAACP,mBAAmB;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAChC4D,UAAU,EAAE;oBAAEhE,KAAK,EAAE;kBAAU;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9B,OAAA,CAACf,GAAG;gBAACsG,IAAI,EAAE,CAAE;gBAAAvD,QAAA,eACXhC,OAAA,CAACd,SAAS;kBACR6F,KAAK,EAAC,0BAAM;kBACZS,KAAK,EAAEjF,YAAY,CAAC8B,MAAM,GAAG7B,cAAc,CAAC6B,MAAO;kBACnDoD,MAAM,eAAEzF,OAAA,CAACX,mBAAmB;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9B,OAAA,CAACf,GAAG;gBAACsG,IAAI,EAAE,CAAE;gBAAAvD,QAAA,eACXhC,OAAA,CAACzB,MAAM;kBACL6E,IAAI,EAAC,SAAS;kBACdE,MAAM;kBACNC,IAAI,eAAEvD,OAAA,CAACJ,cAAc;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB0B,OAAO,EAAE3C,mBAAoB;kBAC7B0B,QAAQ,EAAE/B,cAAc,CAAC6B,MAAM,KAAK,CAAE;kBAAAL,QAAA,EACvC;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN9B,OAAA,CAACf,GAAG;UAACsG,IAAI,EAAE,EAAG;UAAAvD,QAAA,eACZhC,OAAA,CAACjB,IAAI;YAACgG,KAAK,EAAC,gCAAO;YAAC1B,IAAI,EAAC,OAAO;YAAArB,QAAA,eAC9BhC,OAAA,CAACb,IAAI;cACHwG,UAAU,EAAEpF,YAAa;cACzBqF,MAAM,EAAE;gBAAEC,SAAS,EAAE;cAAW,CAAE;cAClCC,UAAU,EAAGzE,IAAI,iBACfrB,OAAA,CAACb,IAAI,CAACmD,IAAI;gBACRyD,OAAO,EAAE,cACP/F,OAAA,CAACzB,MAAM;kBACL6E,IAAI,EAAC,MAAM;kBACXG,IAAI,eAAEvD,OAAA,CAACT,WAAW;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtB0B,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAACC,IAAI,CAAE;kBAAAW,QAAA,EAC3C;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9B,OAAA,CAACzB,MAAM;kBACL6E,IAAI,EAAC,MAAM;kBACXE,MAAM;kBACNC,IAAI,eAAEvD,OAAA,CAACR,YAAY;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvB0B,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAACY,IAAI,CAACsC,OAAO,CAAE;kBAAA3B,QAAA,EACzC;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,CACT;gBAAAE,QAAA,eAEFhC,OAAA,CAACb,IAAI,CAACmD,IAAI,CAAC0D,IAAI;kBACbC,MAAM,EAAE3E,iBAAiB,CAACD,IAAI,CAACE,MAAM,CAAE;kBACvCwD,KAAK,eACH/E,OAAA,CAACrB,KAAK;oBAAAqD,QAAA,gBACJhC,OAAA,CAACG,IAAI;sBAACgD,MAAM;sBAAAnB,QAAA,EAAEtB,cAAc,CAACW,IAAI,CAAC6E,SAAS;oBAAC;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDC,gBAAgB,CAACV,IAAI,CAACE,MAAM,CAAC;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDU,WAAW,eACTxC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA,CAACG,IAAI;sBAACiD,IAAI,EAAC,WAAW;sBAAApB,QAAA,GAAC,MAAI,EAACX,IAAI,CAACsC,OAAO;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChD9B,OAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;sBAACiD,IAAI,EAAC,WAAW;sBAAApB,QAAA,GAAC,4BAAM,EAACX,IAAI,CAAC8E,UAAU;oBAAA;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACpDT,IAAI,CAACwC,QAAQ,KAAKuC,SAAS,iBAC1BpG,OAAA;sBAAKyB,KAAK,EAAE;wBAAEwC,SAAS,EAAE;sBAAE,CAAE;sBAAAjC,QAAA,eAC3BhC,OAAA,CAACnB,QAAQ;wBAAC+E,OAAO,EAAEvC,IAAI,CAACwC,QAAS;wBAACR,IAAI,EAAC;sBAAO;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN9B,OAAA,CAACf,GAAG;UAACsG,IAAI,EAAE,EAAG;UAAAvD,QAAA,eACZhC,OAAA,CAACjB,IAAI;YAACgG,KAAK,EAAC,gCAAO;YAAC1B,IAAI,EAAC,OAAO;YAAArB,QAAA,eAC9BhC,OAAA,CAACb,IAAI;cACHwG,UAAU,EAAEnF,cAAe;cAC3BoF,MAAM,EAAE;gBAAEC,SAAS,EAAE;cAAW,CAAE;cAClCC,UAAU,EAAGzE,IAAI,iBACfrB,OAAA,CAACb,IAAI,CAACmD,IAAI;gBACRyD,OAAO,EAAE,cACP/F,OAAA,CAACzB,MAAM;kBACL6E,IAAI,EAAC,MAAM;kBACXG,IAAI,eAAEvD,OAAA,CAACT,WAAW;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtB0B,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAACC,IAAI,CAAE;kBAAAW,QAAA,EAC3C;gBAED;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,CACT;gBAAAE,QAAA,eAEFhC,OAAA,CAACb,IAAI,CAACmD,IAAI,CAAC0D,IAAI;kBACbC,MAAM,EAAE3E,iBAAiB,CAACD,IAAI,CAACE,MAAM,CAAE;kBACvCwD,KAAK,eACH/E,OAAA,CAACrB,KAAK;oBAAAqD,QAAA,gBACJhC,OAAA,CAACG,IAAI;sBAACgD,MAAM;sBAAAnB,QAAA,EAAEtB,cAAc,CAACW,IAAI,CAAC6E,SAAS;oBAAC;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EACnDC,gBAAgB,CAACV,IAAI,CAACE,MAAM,CAAC;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CACR;kBACDU,WAAW,eACTxC,OAAA;oBAAAgC,QAAA,gBACEhC,OAAA,CAACG,IAAI;sBAACiD,IAAI,EAAC,WAAW;sBAAApB,QAAA,GAAC,MAAI,EAACX,IAAI,CAACsC,OAAO;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChD9B,OAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;sBAACiD,IAAI,EAAC,WAAW;sBAAApB,QAAA,GAAC,4BAAM,EAACX,IAAI,CAACgF,UAAU;oBAAA;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR9B,OAAA,CAAClB,KAAK;MACJiG,KAAK,EAAC,0BAAM;MACZC,OAAO,EAAE9D,iBAAkB;MAC3B+D,QAAQ,EAAEA,CAAA,KAAM9D,oBAAoB,CAAC,KAAK,CAAE;MAC5C+D,MAAM,EAAE,cACNlF,OAAA,CAACzB,MAAM;QAAaiF,OAAO,EAAEA,CAAA,KAAMrC,oBAAoB,CAAC,KAAK,CAAE;QAAAa,QAAA,EAAC;MAEhE,GAFY,OAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFqD,KAAK,EAAE,GAAI;MAAAnD,QAAA,EAEVhB,YAAY,iBACXhB,OAAA;QAAAgC,QAAA,eACEhC,OAAA,CAAChB,GAAG;UAACsG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAtD,QAAA,gBACpBhC,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;cAACmG,QAAQ;cAAAtE,QAAA,EAAEhB,YAAY,CAAC2C;YAAO;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN9B,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;cAAA6B,QAAA,EAAEtB,cAAc,CAACM,YAAY,CAACkF,SAAS;YAAC;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN9B,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLC,gBAAgB,CAACf,YAAY,CAACO,MAAM,CAAC;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN9B,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLd,YAAY,CAAC6C,QAAQ,KAAKuC,SAAS,gBAClCpG,OAAA,CAACnB,QAAQ;cAAC+E,OAAO,EAAE5C,YAAY,CAAC6C,QAAS;cAACR,IAAI,EAAC;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD9B,OAAA,CAACG,IAAI;cAACiD,IAAI,EAAC,WAAW;cAAApB,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN9B,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;cAAA6B,QAAA,EAAEhB,YAAY,CAACmF;YAAU;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACN9B,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;cAAA6B,QAAA,EAAEhB,YAAY,CAACqF;YAAU;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACLd,YAAY,CAACuF,OAAO,iBACnBvG,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA,CAACG,IAAI;cAAA6B,QAAA,EAAEhB,YAAY,CAACuF;YAAO;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACN,EACAd,YAAY,CAACwF,MAAM,iBAClBxG,OAAA,CAACf,GAAG;YAACsG,IAAI,EAAE,EAAG;YAAAvD,QAAA,gBACZhC,OAAA,CAACG,IAAI;cAACgD,MAAM;cAAAnB,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB9B,OAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN9B,OAAA;cAAKyB,KAAK,EAAE;gBAAEgF,UAAU,EAAE,SAAS;gBAAE3D,OAAO,EAAE,CAAC;gBAAE4D,YAAY,EAAE,CAAC;gBAAE1C,QAAQ,EAAE;cAAG,CAAE;cAAAhC,QAAA,EAC9E2E,IAAI,CAACC,SAAS,CAAC5F,YAAY,CAACwF,MAAM,EAAE,IAAI,EAAE,CAAC;YAAC;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACzB,EAAA,CAxYID,mBAA6B;EAAA,QAChBN,WAAW,EASxBD,cAAc;AAAA;AAAAgH,EAAA,GAVdzG,mBAA6B;AA0YnC,eAAeA,mBAAmB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}