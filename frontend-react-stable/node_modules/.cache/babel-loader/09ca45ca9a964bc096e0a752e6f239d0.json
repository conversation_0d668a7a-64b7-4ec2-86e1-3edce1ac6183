{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar attributes = \"accept acceptCharset accessKey action allowFullScreen allowTransparency\\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\\n    charSet checked classID className colSpan cols content contentEditable contextMenu\\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\\n    mediaGroup method min minLength multiple muted name noValidate nonce open\\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\\n    summary tabIndex target title type useMap value width wmode wrap\";\nvar eventsName = \"onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError\";\nvar propList = \"\".concat(attributes, \" \").concat(eventsName).split(/[\\s\\n]+/);\n\n/* eslint-enable max-len */\nvar ariaPrefix = 'aria-';\nvar dataPrefix = 'data-';\nfunction match(key, prefix) {\n  return key.indexOf(prefix) === 0;\n}\n/**\n * Picker props from exist props with filter\n * @param props Passed props\n * @param ariaOnly boolean | { aria?: boolean; data?: boolean; attr?: boolean; } filter config\n */\nexport default function pickAttrs(props) {\n  var ariaOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var mergedConfig;\n  if (ariaOnly === false) {\n    mergedConfig = {\n      aria: true,\n      data: true,\n      attr: true\n    };\n  } else if (ariaOnly === true) {\n    mergedConfig = {\n      aria: true\n    };\n  } else {\n    mergedConfig = _objectSpread({}, ariaOnly);\n  }\n  var attrs = {};\n  Object.keys(props).forEach(function (key) {\n    if (\n    // Aria\n    mergedConfig.aria && (key === 'role' || match(key, ariaPrefix)) ||\n    // Data\n    mergedConfig.data && match(key, dataPrefix) ||\n    // Attr\n    mergedConfig.attr && propList.includes(key)) {\n      attrs[key] = props[key];\n    }\n  });\n  return attrs;\n}", "map": {"version": 3, "names": ["_objectSpread", "attributes", "eventsName", "propList", "concat", "split", "ariaPrefix", "dataPrefix", "match", "key", "prefix", "indexOf", "pickAttrs", "props", "aria<PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "mergedConfig", "aria", "data", "attr", "attrs", "Object", "keys", "for<PERSON>ach", "includes"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-util/es/pickAttrs.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar attributes = \"accept acceptCharset accessKey action allowFullScreen allowTransparency\\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\\n    charSet checked classID className colSpan cols content contentEditable contextMenu\\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\\n    mediaGroup method min minLength multiple muted name noValidate nonce open\\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\\n    summary tabIndex target title type useMap value width wmode wrap\";\nvar eventsName = \"onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError\";\nvar propList = \"\".concat(attributes, \" \").concat(eventsName).split(/[\\s\\n]+/);\n\n/* eslint-enable max-len */\nvar ariaPrefix = 'aria-';\nvar dataPrefix = 'data-';\nfunction match(key, prefix) {\n  return key.indexOf(prefix) === 0;\n}\n/**\n * Picker props from exist props with filter\n * @param props Passed props\n * @param ariaOnly boolean | { aria?: boolean; data?: boolean; attr?: boolean; } filter config\n */\nexport default function pickAttrs(props) {\n  var ariaOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var mergedConfig;\n  if (ariaOnly === false) {\n    mergedConfig = {\n      aria: true,\n      data: true,\n      attr: true\n    };\n  } else if (ariaOnly === true) {\n    mergedConfig = {\n      aria: true\n    };\n  } else {\n    mergedConfig = _objectSpread({}, ariaOnly);\n  }\n  var attrs = {};\n  Object.keys(props).forEach(function (key) {\n    if (\n    // Aria\n    mergedConfig.aria && (key === 'role' || match(key, ariaPrefix)) ||\n    // Data\n    mergedConfig.data && match(key, dataPrefix) ||\n    // Attr\n    mergedConfig.attr && propList.includes(key)) {\n      attrs[key] = props[key];\n    }\n  });\n  return attrs;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,IAAIC,UAAU,GAAG,mgCAAmgC;AACphC,IAAIC,UAAU,GAAG,wtBAAwtB;AACzuB,IAAIC,QAAQ,GAAG,EAAE,CAACC,MAAM,CAACH,UAAU,EAAE,GAAG,CAAC,CAACG,MAAM,CAACF,UAAU,CAAC,CAACG,KAAK,CAAC,SAAS,CAAC;;AAE7E;AACA,IAAIC,UAAU,GAAG,OAAO;AACxB,IAAIC,UAAU,GAAG,OAAO;AACxB,SAASC,KAAKA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC1B,OAAOD,GAAG,CAACE,OAAO,CAACD,MAAM,CAAC,KAAK,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,SAASA,CAACC,KAAK,EAAE;EACvC,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxF,IAAIG,YAAY;EAChB,IAAIJ,QAAQ,KAAK,KAAK,EAAE;IACtBI,YAAY,GAAG;MACbC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;IACR,CAAC;EACH,CAAC,MAAM,IAAIP,QAAQ,KAAK,IAAI,EAAE;IAC5BI,YAAY,GAAG;MACbC,IAAI,EAAE;IACR,CAAC;EACH,CAAC,MAAM;IACLD,YAAY,GAAGlB,aAAa,CAAC,CAAC,CAAC,EAAEc,QAAQ,CAAC;EAC5C;EACA,IAAIQ,KAAK,GAAG,CAAC,CAAC;EACdC,MAAM,CAACC,IAAI,CAACX,KAAK,CAAC,CAACY,OAAO,CAAC,UAAUhB,GAAG,EAAE;IACxC;IACA;IACAS,YAAY,CAACC,IAAI,KAAKV,GAAG,KAAK,MAAM,IAAID,KAAK,CAACC,GAAG,EAAEH,UAAU,CAAC,CAAC;IAC/D;IACAY,YAAY,CAACE,IAAI,IAAIZ,KAAK,CAACC,GAAG,EAAEF,UAAU,CAAC;IAC3C;IACAW,YAAY,CAACG,IAAI,IAAIlB,QAAQ,CAACuB,QAAQ,CAACjB,GAAG,CAAC,EAAE;MAC3Ca,KAAK,CAACb,GAAG,CAAC,GAAGI,KAAK,CAACJ,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOa,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}