{"ast": null, "code": "import * as React from 'react';\nvar RangeContext = /*#__PURE__*/React.createContext({});\nexport default RangeContext;", "map": {"version": 3, "names": ["React", "RangeContext", "createContext"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/RangeContext.js"], "sourcesContent": ["import * as React from 'react';\nvar RangeContext = /*#__PURE__*/React.createContext({});\nexport default RangeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACvD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}