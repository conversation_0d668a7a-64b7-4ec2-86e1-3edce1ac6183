{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport SlickCarousel from '@ant-design/react-slick';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Carousel = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var _a$dots = _a.dots,\n    dots = _a$dots === void 0 ? true : _a$dots,\n    _a$arrows = _a.arrows,\n    arrows = _a$arrows === void 0 ? false : _a$arrows,\n    _a$draggable = _a.draggable,\n    draggable = _a$draggable === void 0 ? false : _a$draggable,\n    _a$dotPosition = _a.dotPosition,\n    dotPosition = _a$dotPosition === void 0 ? 'bottom' : _a$dotPosition,\n    _a$vertical = _a.vertical,\n    vertical = _a$vertical === void 0 ? dotPosition === 'left' || dotPosition === 'right' : _a$vertical,\n    props = __rest(_a, [\"dots\", \"arrows\", \"draggable\", \"dotPosition\", \"vertical\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var slickRef = React.useRef();\n  var goTo = function goTo(slide) {\n    var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    slickRef.current.slickGoTo(slide, dontAnimate);\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      goTo: goTo,\n      autoPlay: slickRef.current.innerSlider.autoPlay,\n      innerSlider: slickRef.current.innerSlider,\n      prev: slickRef.current.slickPrev,\n      next: slickRef.current.slickNext\n    };\n  }, [slickRef.current]);\n  var prevCount = React.useRef(React.Children.count(props.children));\n  React.useEffect(function () {\n    if (prevCount.current !== React.Children.count(props.children)) {\n      goTo(props.initialSlide || 0, false);\n      prevCount.current = React.Children.count(props.children);\n    }\n  }, [props.children]);\n  var newProps = _extends({\n    vertical: vertical\n  }, props);\n  if (newProps.effect === 'fade') {\n    newProps.fade = true;\n  }\n  var prefixCls = getPrefixCls('carousel', newProps.prefixCls);\n  var dotsClass = 'slick-dots';\n  var enableDots = !!dots;\n  var dsClass = classNames(dotsClass, \"\".concat(dotsClass, \"-\").concat(dotPosition), typeof dots === 'boolean' ? false : dots === null || dots === void 0 ? void 0 : dots.className);\n  var className = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-vertical\"), dotPosition === 'left' || dotPosition === 'right'), _classNames));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, /*#__PURE__*/React.createElement(SlickCarousel, _extends({\n    ref: slickRef\n  }, newProps, {\n    dots: enableDots,\n    dotsClass: dsClass,\n    arrows: arrows,\n    draggable: draggable\n  })));\n});\nexport default Carousel;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "SlickCarousel", "classNames", "React", "ConfigContext", "Carousel", "forwardRef", "_a", "ref", "_classNames", "_a$dots", "dots", "_a$arrows", "arrows", "_a$draggable", "draggable", "_a$dotPosition", "dotPosition", "_a$vertical", "vertical", "props", "_React$useContext", "useContext", "getPrefixCls", "direction", "slickRef", "useRef", "goTo", "slide", "dontAnimate", "arguments", "undefined", "current", "slickGoTo", "useImperativeHandle", "autoPlay", "innerSlider", "prev", "slick<PERSON>rev", "next", "slickNext", "prevCount", "Children", "count", "children", "useEffect", "initialSlide", "newProps", "effect", "fade", "prefixCls", "dotsClass", "enableDots", "dsClass", "concat", "className", "createElement"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/carousel/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport SlickCarousel from '@ant-design/react-slick';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nvar Carousel = /*#__PURE__*/React.forwardRef(function (_a, ref) {\n  var _classNames;\n  var _a$dots = _a.dots,\n    dots = _a$dots === void 0 ? true : _a$dots,\n    _a$arrows = _a.arrows,\n    arrows = _a$arrows === void 0 ? false : _a$arrows,\n    _a$draggable = _a.draggable,\n    draggable = _a$draggable === void 0 ? false : _a$draggable,\n    _a$dotPosition = _a.dotPosition,\n    dotPosition = _a$dotPosition === void 0 ? 'bottom' : _a$dotPosition,\n    _a$vertical = _a.vertical,\n    vertical = _a$vertical === void 0 ? dotPosition === 'left' || dotPosition === 'right' : _a$vertical,\n    props = __rest(_a, [\"dots\", \"arrows\", \"draggable\", \"dotPosition\", \"vertical\"]);\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var slickRef = React.useRef();\n  var goTo = function goTo(slide) {\n    var dontAnimate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    slickRef.current.slickGoTo(slide, dontAnimate);\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      goTo: goTo,\n      autoPlay: slickRef.current.innerSlider.autoPlay,\n      innerSlider: slickRef.current.innerSlider,\n      prev: slickRef.current.slickPrev,\n      next: slickRef.current.slickNext\n    };\n  }, [slickRef.current]);\n  var prevCount = React.useRef(React.Children.count(props.children));\n  React.useEffect(function () {\n    if (prevCount.current !== React.Children.count(props.children)) {\n      goTo(props.initialSlide || 0, false);\n      prevCount.current = React.Children.count(props.children);\n    }\n  }, [props.children]);\n  var newProps = _extends({\n    vertical: vertical\n  }, props);\n  if (newProps.effect === 'fade') {\n    newProps.fade = true;\n  }\n  var prefixCls = getPrefixCls('carousel', newProps.prefixCls);\n  var dotsClass = 'slick-dots';\n  var enableDots = !!dots;\n  var dsClass = classNames(dotsClass, \"\".concat(dotsClass, \"-\").concat(dotPosition), typeof dots === 'boolean' ? false : dots === null || dots === void 0 ? void 0 : dots.className);\n  var className = classNames(prefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-vertical\"), dotPosition === 'left' || dotPosition === 'right'), _classNames));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, /*#__PURE__*/React.createElement(SlickCarousel, _extends({\n    ref: slickRef\n  }, newProps, {\n    dots: enableDots,\n    dotsClass: dsClass,\n    arrows: arrows,\n    draggable: draggable\n  })));\n});\nexport default Carousel;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,aAAa,MAAM,yBAAyB;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,QAAQ,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,EAAE,EAAEC,GAAG,EAAE;EAC9D,IAAIC,WAAW;EACf,IAAIC,OAAO,GAAGH,EAAE,CAACI,IAAI;IACnBA,IAAI,GAAGD,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,OAAO;IAC1CE,SAAS,GAAGL,EAAE,CAACM,MAAM;IACrBA,MAAM,GAAGD,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,SAAS;IACjDE,YAAY,GAAGP,EAAE,CAACQ,SAAS;IAC3BA,SAAS,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,YAAY;IAC1DE,cAAc,GAAGT,EAAE,CAACU,WAAW;IAC/BA,WAAW,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,cAAc;IACnEE,WAAW,GAAGX,EAAE,CAACY,QAAQ;IACzBA,QAAQ,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGD,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,OAAO,GAAGC,WAAW;IACnGE,KAAK,GAAGjC,MAAM,CAACoB,EAAE,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;EAChF,IAAIc,iBAAiB,GAAGlB,KAAK,CAACmB,UAAU,CAAClB,aAAa,CAAC;IACrDmB,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,QAAQ,GAAGtB,KAAK,CAACuB,MAAM,CAAC,CAAC;EAC7B,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;IAC9B,IAAIC,WAAW,GAAGC,SAAS,CAAC/B,MAAM,GAAG,CAAC,IAAI+B,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IAC3FL,QAAQ,CAACO,OAAO,CAACC,SAAS,CAACL,KAAK,EAAEC,WAAW,CAAC;EAChD,CAAC;EACD1B,KAAK,CAAC+B,mBAAmB,CAAC1B,GAAG,EAAE,YAAY;IACzC,OAAO;MACLmB,IAAI,EAAEA,IAAI;MACVQ,QAAQ,EAAEV,QAAQ,CAACO,OAAO,CAACI,WAAW,CAACD,QAAQ;MAC/CC,WAAW,EAAEX,QAAQ,CAACO,OAAO,CAACI,WAAW;MACzCC,IAAI,EAAEZ,QAAQ,CAACO,OAAO,CAACM,SAAS;MAChCC,IAAI,EAAEd,QAAQ,CAACO,OAAO,CAACQ;IACzB,CAAC;EACH,CAAC,EAAE,CAACf,QAAQ,CAACO,OAAO,CAAC,CAAC;EACtB,IAAIS,SAAS,GAAGtC,KAAK,CAACuB,MAAM,CAACvB,KAAK,CAACuC,QAAQ,CAACC,KAAK,CAACvB,KAAK,CAACwB,QAAQ,CAAC,CAAC;EAClEzC,KAAK,CAAC0C,SAAS,CAAC,YAAY;IAC1B,IAAIJ,SAAS,CAACT,OAAO,KAAK7B,KAAK,CAACuC,QAAQ,CAACC,KAAK,CAACvB,KAAK,CAACwB,QAAQ,CAAC,EAAE;MAC9DjB,IAAI,CAACP,KAAK,CAAC0B,YAAY,IAAI,CAAC,EAAE,KAAK,CAAC;MACpCL,SAAS,CAACT,OAAO,GAAG7B,KAAK,CAACuC,QAAQ,CAACC,KAAK,CAACvB,KAAK,CAACwB,QAAQ,CAAC;IAC1D;EACF,CAAC,EAAE,CAACxB,KAAK,CAACwB,QAAQ,CAAC,CAAC;EACpB,IAAIG,QAAQ,GAAG7D,QAAQ,CAAC;IACtBiC,QAAQ,EAAEA;EACZ,CAAC,EAAEC,KAAK,CAAC;EACT,IAAI2B,QAAQ,CAACC,MAAM,KAAK,MAAM,EAAE;IAC9BD,QAAQ,CAACE,IAAI,GAAG,IAAI;EACtB;EACA,IAAIC,SAAS,GAAG3B,YAAY,CAAC,UAAU,EAAEwB,QAAQ,CAACG,SAAS,CAAC;EAC5D,IAAIC,SAAS,GAAG,YAAY;EAC5B,IAAIC,UAAU,GAAG,CAAC,CAACzC,IAAI;EACvB,IAAI0C,OAAO,GAAGnD,UAAU,CAACiD,SAAS,EAAE,EAAE,CAACG,MAAM,CAACH,SAAS,EAAE,GAAG,CAAC,CAACG,MAAM,CAACrC,WAAW,CAAC,EAAE,OAAON,IAAI,KAAK,SAAS,GAAG,KAAK,GAAGA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC4C,SAAS,CAAC;EAClL,IAAIA,SAAS,GAAGrD,UAAU,CAACgD,SAAS,GAAGzC,WAAW,GAAG,CAAC,CAAC,EAAExB,eAAe,CAACwB,WAAW,EAAE,EAAE,CAAC6C,MAAM,CAACJ,SAAS,EAAE,MAAM,CAAC,EAAE1B,SAAS,KAAK,KAAK,CAAC,EAAEvC,eAAe,CAACwB,WAAW,EAAE,EAAE,CAAC6C,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAEjC,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,OAAO,CAAC,EAAER,WAAW,CAAC,CAAC;EAC3Q,OAAO,aAAaN,KAAK,CAACqD,aAAa,CAAC,KAAK,EAAE;IAC7CD,SAAS,EAAEA;EACb,CAAC,EAAE,aAAapD,KAAK,CAACqD,aAAa,CAACvD,aAAa,EAAEf,QAAQ,CAAC;IAC1DsB,GAAG,EAAEiB;EACP,CAAC,EAAEsB,QAAQ,EAAE;IACXpC,IAAI,EAAEyC,UAAU;IAChBD,SAAS,EAAEE,OAAO;IAClBxC,MAAM,EAAEA,MAAM;IACdE,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}