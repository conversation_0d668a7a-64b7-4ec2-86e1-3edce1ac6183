{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar Divider = function Divider(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'horizontal' : _props$type,\n    _props$orientation = props.orientation,\n    orientation = _props$orientation === void 0 ? 'center' : _props$orientation,\n    orientationMargin = props.orientationMargin,\n    className = props.className,\n    children = props.children,\n    dashed = props.dashed,\n    plain = props.plain,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"orientation\", \"orientationMargin\", \"className\", \"children\", \"dashed\", \"plain\"]);\n  var prefixCls = getPrefixCls('divider', customizePrefixCls);\n  var orientationPrefix = orientation.length > 0 ? \"-\".concat(orientation) : orientation;\n  var hasChildren = !!children;\n  var hasCustomMarginLeft = orientation === 'left' && orientationMargin != null;\n  var hasCustomMarginRight = orientation === 'right' && orientationMargin != null;\n  var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(type), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-text\"), hasChildren), _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-text\").concat(orientationPrefix), hasChildren), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dashed\"), !!dashed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-plain\"), !!plain), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-no-default-orientation-margin-left\"), hasCustomMarginLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-no-default-orientation-margin-right\"), hasCustomMarginRight), _classNames), className);\n  var innerStyle = _extends(_extends({}, hasCustomMarginLeft && {\n    marginLeft: orientationMargin\n  }), hasCustomMarginRight && {\n    marginRight: orientationMargin\n  });\n  // Warning children not work in vertical mode\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!children || type !== 'vertical', 'Divider', '`children` not working in `vertical` mode.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classString\n  }, restProps, {\n    role: \"separator\"\n  }), children && type !== 'vertical' && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner-text\"),\n    style: innerStyle\n  }, children));\n};\nexport default Divider;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "classNames", "React", "ConfigContext", "warning", "Divider", "props", "_classNames", "_React$useContext", "useContext", "getPrefixCls", "direction", "customizePrefixCls", "prefixCls", "_props$type", "type", "_props$orientation", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "dashed", "plain", "restProps", "orientationPrefix", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasCustomMarginLeft", "hasCustomMarginRight", "classString", "innerStyle", "marginLeft", "marginRight", "process", "env", "NODE_ENV", "createElement", "role", "style"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/divider/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport warning from '../_util/warning';\nvar Divider = function Divider(props) {\n  var _classNames;\n  var _React$useContext = React.useContext(ConfigContext),\n    getPrefixCls = _React$useContext.getPrefixCls,\n    direction = _React$useContext.direction;\n  var customizePrefixCls = props.prefixCls,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'horizontal' : _props$type,\n    _props$orientation = props.orientation,\n    orientation = _props$orientation === void 0 ? 'center' : _props$orientation,\n    orientationMargin = props.orientationMargin,\n    className = props.className,\n    children = props.children,\n    dashed = props.dashed,\n    plain = props.plain,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"orientation\", \"orientationMargin\", \"className\", \"children\", \"dashed\", \"plain\"]);\n  var prefixCls = getPrefixCls('divider', customizePrefixCls);\n  var orientationPrefix = orientation.length > 0 ? \"-\".concat(orientation) : orientation;\n  var hasChildren = !!children;\n  var hasCustomMarginLeft = orientation === 'left' && orientationMargin != null;\n  var hasCustomMarginRight = orientation === 'right' && orientationMargin != null;\n  var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(type), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-text\"), hasChildren), _defineProperty(_classNames, \"\".concat(prefixCls, \"-with-text\").concat(orientationPrefix), hasChildren), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dashed\"), !!dashed), _defineProperty(_classNames, \"\".concat(prefixCls, \"-plain\"), !!plain), _defineProperty(_classNames, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-no-default-orientation-margin-left\"), hasCustomMarginLeft), _defineProperty(_classNames, \"\".concat(prefixCls, \"-no-default-orientation-margin-right\"), hasCustomMarginRight), _classNames), className);\n  var innerStyle = _extends(_extends({}, hasCustomMarginLeft && {\n    marginLeft: orientationMargin\n  }), hasCustomMarginRight && {\n    marginRight: orientationMargin\n  });\n  // Warning children not work in vertical mode\n  if (process.env.NODE_ENV !== 'production') {\n    process.env.NODE_ENV !== \"production\" ? warning(!children || type !== 'vertical', 'Divider', '`children` not working in `vertical` mode.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classString\n  }, restProps, {\n    role: \"separator\"\n  }), children && type !== 'vertical' && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-inner-text\"),\n    style: innerStyle\n  }, children));\n};\nexport default Divider;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE;IACf,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjF;EACA,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,WAAW;EACf,IAAIC,iBAAiB,GAAGN,KAAK,CAACO,UAAU,CAACN,aAAa,CAAC;IACrDO,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EACzC,IAAIC,kBAAkB,GAAGN,KAAK,CAACO,SAAS;IACtCC,WAAW,GAAGR,KAAK,CAACS,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,WAAW;IAC1DE,kBAAkB,GAAGV,KAAK,CAACW,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,kBAAkB;IAC3EE,iBAAiB,GAAGZ,KAAK,CAACY,iBAAiB;IAC3CC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,MAAM,GAAGf,KAAK,CAACe,MAAM;IACrBC,KAAK,GAAGhB,KAAK,CAACgB,KAAK;IACnBC,SAAS,GAAGpC,MAAM,CAACmB,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EAClI,IAAIO,SAAS,GAAGH,YAAY,CAAC,SAAS,EAAEE,kBAAkB,CAAC;EAC3D,IAAIY,iBAAiB,GAAGP,WAAW,CAAClB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC0B,MAAM,CAACR,WAAW,CAAC,GAAGA,WAAW;EACtF,IAAIS,WAAW,GAAG,CAAC,CAACN,QAAQ;EAC5B,IAAIO,mBAAmB,GAAGV,WAAW,KAAK,MAAM,IAAIC,iBAAiB,IAAI,IAAI;EAC7E,IAAIU,oBAAoB,GAAGX,WAAW,KAAK,OAAO,IAAIC,iBAAiB,IAAI,IAAI;EAC/E,IAAIW,WAAW,GAAG5B,UAAU,CAACY,SAAS,EAAE,EAAE,CAACY,MAAM,CAACZ,SAAS,EAAE,GAAG,CAAC,CAACY,MAAM,CAACV,IAAI,CAAC,GAAGR,WAAW,GAAG,CAAC,CAAC,EAAErB,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,YAAY,CAAC,EAAEa,WAAW,CAAC,EAAExC,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,YAAY,CAAC,CAACY,MAAM,CAACD,iBAAiB,CAAC,EAAEE,WAAW,CAAC,EAAExC,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC,CAACQ,MAAM,CAAC,EAAEnC,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAACS,KAAK,CAAC,EAAEpC,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,MAAM,CAAC,EAAEF,SAAS,KAAK,KAAK,CAAC,EAAEzB,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,qCAAqC,CAAC,EAAEc,mBAAmB,CAAC,EAAEzC,eAAe,CAACqB,WAAW,EAAE,EAAE,CAACkB,MAAM,CAACZ,SAAS,EAAE,sCAAsC,CAAC,EAAEe,oBAAoB,CAAC,EAAErB,WAAW,GAAGY,SAAS,CAAC;EACtvB,IAAIW,UAAU,GAAG7C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,mBAAmB,IAAI;IAC5DI,UAAU,EAAEb;EACd,CAAC,CAAC,EAAEU,oBAAoB,IAAI;IAC1BI,WAAW,EAAEd;EACf,CAAC,CAAC;EACF;EACA,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,OAAO,CAAC,CAACgB,QAAQ,IAAIL,IAAI,KAAK,UAAU,EAAE,SAAS,EAAE,4CAA4C,CAAC,GAAG,KAAK,CAAC;EACrJ;EACA,OAAO,aAAab,KAAK,CAACkC,aAAa,CAAC,KAAK,EAAEnD,QAAQ,CAAC;IACtDkC,SAAS,EAAEU;EACb,CAAC,EAAEN,SAAS,EAAE;IACZc,IAAI,EAAE;EACR,CAAC,CAAC,EAAEjB,QAAQ,IAAIL,IAAI,KAAK,UAAU,IAAI,aAAab,KAAK,CAACkC,aAAa,CAAC,MAAM,EAAE;IAC9EjB,SAAS,EAAE,EAAE,CAACM,MAAM,CAACZ,SAAS,EAAE,aAAa,CAAC;IAC9CyB,KAAK,EAAER;EACT,CAAC,EAAEV,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,eAAef,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}