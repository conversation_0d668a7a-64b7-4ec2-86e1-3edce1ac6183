{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quickselect;\nvar _sort = require(\"./sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k) {\n  let left = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let right = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : array.length - 1;\n  let compare = arguments.length > 4 ? arguments[4] : undefined;\n  compare = compare === undefined ? _sort.ascendingDefined : (0, _sort.compareDefined)(compare);\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "quickselect", "_sort", "require", "array", "k", "left", "arguments", "length", "undefined", "right", "compare", "ascendingDefined", "compareDefined", "n", "m", "z", "Math", "log", "s", "exp", "sd", "sqrt", "newLeft", "max", "floor", "newRight", "min", "t", "i", "j", "swap"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/quickselect.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = quickselect;\n\nvar _sort = require(\"./sort.js\");\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = array.length - 1, compare) {\n  compare = compare === undefined ? _sort.ascendingDefined : (0, _sort.compareDefined)(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n\n      while (compare(array[i], t) < 0) ++i;\n\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,WAAW;AAE7B,IAAIC,KAAK,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAEhC;AACA;AACA,SAASF,WAAWA,CAACG,KAAK,EAAEC,CAAC,EAA+C;EAAA,IAA7CC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGH,KAAK,CAACI,MAAM,GAAG,CAAC;EAAA,IAAEG,OAAO,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACxEE,OAAO,GAAGA,OAAO,KAAKF,SAAS,GAAGP,KAAK,CAACU,gBAAgB,GAAG,CAAC,CAAC,EAAEV,KAAK,CAACW,cAAc,EAAEF,OAAO,CAAC;EAE7F,OAAOD,KAAK,GAAGJ,IAAI,EAAE;IACnB,IAAII,KAAK,GAAGJ,IAAI,GAAG,GAAG,EAAE;MACtB,MAAMQ,CAAC,GAAGJ,KAAK,GAAGJ,IAAI,GAAG,CAAC;MAC1B,MAAMS,CAAC,GAAGV,CAAC,GAAGC,IAAI,GAAG,CAAC;MACtB,MAAMU,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,CAAC;MACrB,MAAMK,CAAC,GAAG,GAAG,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAGJ,CAAC,GAAG,CAAC,CAAC;MACnC,MAAMK,EAAE,GAAG,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACN,CAAC,GAAGG,CAAC,IAAIL,CAAC,GAAGK,CAAC,CAAC,GAAGL,CAAC,CAAC,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1E,MAAMS,OAAO,GAAGN,IAAI,CAACO,GAAG,CAAClB,IAAI,EAAEW,IAAI,CAACQ,KAAK,CAACpB,CAAC,GAAGU,CAAC,GAAGI,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MAC9D,MAAMK,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACjB,KAAK,EAAEO,IAAI,CAACQ,KAAK,CAACpB,CAAC,GAAG,CAACS,CAAC,GAAGC,CAAC,IAAII,CAAC,GAAGL,CAAC,GAAGO,EAAE,CAAC,CAAC;MACtEpB,WAAW,CAACG,KAAK,EAAEC,CAAC,EAAEkB,OAAO,EAAEG,QAAQ,EAAEf,OAAO,CAAC;IACnD;IAEA,MAAMiB,CAAC,GAAGxB,KAAK,CAACC,CAAC,CAAC;IAClB,IAAIwB,CAAC,GAAGvB,IAAI;IACZ,IAAIwB,CAAC,GAAGpB,KAAK;IACbqB,IAAI,CAAC3B,KAAK,EAAEE,IAAI,EAAED,CAAC,CAAC;IACpB,IAAIM,OAAO,CAACP,KAAK,CAACM,KAAK,CAAC,EAAEkB,CAAC,CAAC,GAAG,CAAC,EAAEG,IAAI,CAAC3B,KAAK,EAAEE,IAAI,EAAEI,KAAK,CAAC;IAE1D,OAAOmB,CAAC,GAAGC,CAAC,EAAE;MACZC,IAAI,CAAC3B,KAAK,EAAEyB,CAAC,EAAEC,CAAC,CAAC,EAAE,EAAED,CAAC,EAAE,EAAEC,CAAC;MAE3B,OAAOnB,OAAO,CAACP,KAAK,CAACyB,CAAC,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEC,CAAC;MAEpC,OAAOlB,OAAO,CAACP,KAAK,CAAC0B,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEE,CAAC;IACtC;IAEA,IAAInB,OAAO,CAACP,KAAK,CAACE,IAAI,CAAC,EAAEsB,CAAC,CAAC,KAAK,CAAC,EAAEG,IAAI,CAAC3B,KAAK,EAAEE,IAAI,EAAEwB,CAAC,CAAC,CAAC,KAAK,EAAEA,CAAC,EAAEC,IAAI,CAAC3B,KAAK,EAAE0B,CAAC,EAAEpB,KAAK,CAAC;IACvF,IAAIoB,CAAC,IAAIzB,CAAC,EAAEC,IAAI,GAAGwB,CAAC,GAAG,CAAC;IACxB,IAAIzB,CAAC,IAAIyB,CAAC,EAAEpB,KAAK,GAAGoB,CAAC,GAAG,CAAC;EAC3B;EAEA,OAAO1B,KAAK;AACd;AAEA,SAAS2B,IAAIA,CAAC3B,KAAK,EAAEyB,CAAC,EAAEC,CAAC,EAAE;EACzB,MAAMF,CAAC,GAAGxB,KAAK,CAACyB,CAAC,CAAC;EAClBzB,KAAK,CAACyB,CAAC,CAAC,GAAGzB,KAAK,CAAC0B,CAAC,CAAC;EACnB1B,KAAK,CAAC0B,CAAC,CAAC,GAAGF,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script"}