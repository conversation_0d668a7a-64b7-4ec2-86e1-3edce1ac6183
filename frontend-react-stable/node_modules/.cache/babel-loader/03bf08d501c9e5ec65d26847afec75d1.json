{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { renderColumnTitle } from '../util';\nfunction fillTitle(columns, columnTitleProps) {\n  return columns.map(function (column) {\n    var cloneColumn = _extends({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n}\nexport default function useTitleColumns(columnTitleProps) {\n  var filledColumns = React.useCallback(function (columns) {\n    return fillTitle(columns, columnTitleProps);\n  }, [columnTitleProps]);\n  return [filledColumns];\n}", "map": {"version": 3, "names": ["_extends", "React", "renderColumnTitle", "fill<PERSON>itle", "columns", "columnTitleProps", "map", "column", "cloneColumn", "title", "children", "useTitleColumns", "filledColumns", "useCallback"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/table/hooks/useTitleColumns.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { renderColumnTitle } from '../util';\nfunction fillTitle(columns, columnTitleProps) {\n  return columns.map(function (column) {\n    var cloneColumn = _extends({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n}\nexport default function useTitleColumns(columnTitleProps) {\n  var filledColumns = React.useCallback(function (columns) {\n    return fillTitle(columns, columnTitleProps);\n  }, [columnTitleProps]);\n  return [filledColumns];\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,SAASC,SAASA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC5C,OAAOD,OAAO,CAACE,GAAG,CAAC,UAAUC,MAAM,EAAE;IACnC,IAAIC,WAAW,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEO,MAAM,CAAC;IACtCC,WAAW,CAACC,KAAK,GAAGP,iBAAiB,CAACK,MAAM,CAACE,KAAK,EAAEJ,gBAAgB,CAAC;IACrE,IAAI,UAAU,IAAIG,WAAW,EAAE;MAC7BA,WAAW,CAACE,QAAQ,GAAGP,SAAS,CAACK,WAAW,CAACE,QAAQ,EAAEL,gBAAgB,CAAC;IAC1E;IACA,OAAOG,WAAW;EACpB,CAAC,CAAC;AACJ;AACA,eAAe,SAASG,eAAeA,CAACN,gBAAgB,EAAE;EACxD,IAAIO,aAAa,GAAGX,KAAK,CAACY,WAAW,CAAC,UAAUT,OAAO,EAAE;IACvD,OAAOD,SAAS,CAACC,OAAO,EAAEC,gBAAgB,CAAC;EAC7C,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EACtB,OAAO,CAACO,aAAa,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}