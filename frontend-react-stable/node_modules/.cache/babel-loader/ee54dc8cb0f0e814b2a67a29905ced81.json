{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Base from './Base';\nvar Paragraph = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, props, {\n    component: \"div\"\n  }));\n});\nexport default Paragraph;", "map": {"version": 3, "names": ["_extends", "React", "Base", "Paragraph", "forwardRef", "props", "ref", "createElement", "component"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/antd/es/typography/Paragraph.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Base from './Base';\nvar Paragraph = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  return /*#__PURE__*/React.createElement(Base, _extends({\n    ref: ref\n  }, props, {\n    component: \"div\"\n  }));\n});\nexport default Paragraph;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,IAAIC,SAAS,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAClE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACL,IAAI,EAAEF,QAAQ,CAAC;IACrDM,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRG,SAAS,EAAE;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}