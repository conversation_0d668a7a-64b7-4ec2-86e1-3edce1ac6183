{"ast": null, "code": "'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n * @returns {string} The combined URL\n */\nmodule.exports = function combineURLs(baseURL, relativeURL) {\n  return relativeURL ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '') : baseURL;\n};", "map": {"version": 3, "names": ["module", "exports", "combineURLs", "baseURL", "relativeURL", "replace"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/axios/lib/helpers/combineURLs.js"], "sourcesContent": ["'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n * @returns {string} The combined URL\n */\nmodule.exports = function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAE;EAC1D,OAAOA,WAAW,GACdD,OAAO,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGD,WAAW,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GACnEF,OAAO;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}