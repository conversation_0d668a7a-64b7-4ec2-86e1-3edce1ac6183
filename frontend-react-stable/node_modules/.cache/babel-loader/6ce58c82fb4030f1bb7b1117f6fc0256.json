{"ast": null, "code": "import{createSlice,createAsyncThunk}from'@reduxjs/toolkit';import{authAPI}from'../../services/api';const initialState={isAuthenticated:false,user:null,token:localStorage.getItem('token'),loading:false,error:null,showChangePassword:false,forceChangePassword:false};// 异步登录操作\nexport const loginAsync=createAsyncThunk('auth/login',async(credentials,_ref)=>{let{rejectWithValue}=_ref;try{const response=await authAPI.login(credentials);const{access_token}=response.data;// 获取用户信息\nconst userResponse=await authAPI.getUsers(access_token);const userData=userResponse.data[credentials.username];return{token:access_token,user:{username:credentials.username,isDefault:(userData===null||userData===void 0?void 0:userData.is_default)||false}};}catch(error){var _error$response,_error$response$data;return rejectWithValue(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'登录失败');}});// 异步修改密码操作\nexport const changePasswordAsync=createAsyncThunk('auth/changePassword',async(data,_ref2)=>{let{getState,rejectWithValue}=_ref2;try{const state=getState();const response=await authAPI.changePassword(data,state.auth.token);return response.data;}catch(error){var _error$response2,_error$response2$data;return rejectWithValue(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||'修改密码失败');}});const authSlice=createSlice({name:'auth',initialState,reducers:{logout:state=>{state.isAuthenticated=false;state.user=null;state.token=null;state.showChangePassword=false;state.forceChangePassword=false;localStorage.removeItem('token');localStorage.removeItem('username');},clearError:state=>{state.error=null;},setShowChangePassword:(state,action)=>{state.showChangePassword=action.payload;},// 从localStorage恢复登录状态\nrestoreAuth:state=>{const token=localStorage.getItem('token');const username=localStorage.getItem('username');if(token&&username){state.token=token;state.isAuthenticated=true;state.user={username:username,isDefault:false// 恢复时假设已经修改过密码\n};// 这里可以添加验证token有效性的逻辑\n}}},extraReducers:builder=>{builder// 登录\n.addCase(loginAsync.pending,state=>{state.loading=true;state.error=null;}).addCase(loginAsync.fulfilled,(state,action)=>{state.loading=false;state.isAuthenticated=true;state.user=action.payload.user;state.token=action.payload.token;state.showChangePassword=action.payload.user.isDefault;state.forceChangePassword=action.payload.user.isDefault;localStorage.setItem('token',action.payload.token);localStorage.setItem('username',action.payload.user.username);}).addCase(loginAsync.rejected,(state,action)=>{state.loading=false;state.error=action.payload;})// 修改密码\n.addCase(changePasswordAsync.pending,state=>{state.loading=true;state.error=null;}).addCase(changePasswordAsync.fulfilled,state=>{state.loading=false;state.showChangePassword=false;state.forceChangePassword=false;if(state.user){state.user.isDefault=false;}}).addCase(changePasswordAsync.rejected,(state,action)=>{state.loading=false;state.error=action.payload;});}});export const{logout,clearError,setShowChangePassword,restoreAuth}=authSlice.actions;export default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authAPI", "initialState", "isAuthenticated", "user", "token", "localStorage", "getItem", "loading", "error", "showChangePassword", "forceChangePassword", "loginAsync", "credentials", "_ref", "rejectWithValue", "response", "login", "access_token", "data", "userResponse", "getUsers", "userData", "username", "isDefault", "is_default", "_error$response", "_error$response$data", "detail", "changePasswordAsync", "_ref2", "getState", "state", "changePassword", "auth", "_error$response2", "_error$response2$data", "authSlice", "name", "reducers", "logout", "removeItem", "clearError", "setShowChangePassword", "action", "payload", "restoreAuth", "extraReducers", "builder", "addCase", "pending", "fulfilled", "setItem", "rejected", "actions", "reducer"], "sources": ["/home/<USER>/frontend-react-stable/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\nexport interface User {\n  username: string;\n  isDefault: boolean;\n}\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  user: User | null;\n  token: string | null;\n  loading: boolean;\n  error: string | null;\n  showChangePassword: boolean;\n  forceChangePassword: boolean;\n}\n\nconst initialState: AuthState = {\n  isAuthenticated: false,\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: false,\n  error: null,\n  showChangePassword: false,\n  forceChangePassword: false,\n};\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { access_token } = response.data;\n      \n      // 获取用户信息\n      const userResponse = await authAPI.getUsers(access_token);\n      const userData = userResponse.data[credentials.username];\n      \n      return {\n        token: access_token,\n        user: {\n          username: credentials.username,\n          isDefault: userData?.is_default || false,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '登录失败');\n    }\n  }\n);\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk(\n  'auth/changePassword',\n  async (\n    data: { username: string; old_password: string; new_password: string; confirm_password: string },\n    { getState, rejectWithValue }\n  ) => {\n    try {\n      const state = getState() as { auth: AuthState };\n      const response = await authAPI.changePassword(data, state.auth.token!);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '修改密码失败');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: (state) => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action: PayloadAction<boolean>) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false, // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // 登录\n      .addCase(loginAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(loginAsync.fulfilled, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = true;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.showChangePassword = action.payload.user.isDefault;\n        state.forceChangePassword = action.payload.user.isDefault;\n        localStorage.setItem('token', action.payload.token);\n        localStorage.setItem('username', action.payload.user.username);\n      })\n      .addCase(loginAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // 修改密码\n      .addCase(changePasswordAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(changePasswordAsync.fulfilled, (state) => {\n        state.loading = false;\n        state.showChangePassword = false;\n        state.forceChangePassword = false;\n        if (state.user) {\n          state.user.isDefault = false;\n        }\n      })\n      .addCase(changePasswordAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const { logout, clearError, setShowChangePassword, restoreAuth } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,gBAAgB,KAAuB,kBAAkB,CAC/E,OAASC,OAAO,KAAQ,oBAAoB,CAiB5C,KAAM,CAAAC,YAAuB,CAAG,CAC9BC,eAAe,CAAE,KAAK,CACtBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CACpCC,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,IAAI,CACXC,kBAAkB,CAAE,KAAK,CACzBC,mBAAmB,CAAE,KACvB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAGZ,gBAAgB,CACxC,YAAY,CACZ,MAAOa,WAAmD,CAAAC,IAAA,GAA0B,IAAxB,CAAEC,eAAgB,CAAC,CAAAD,IAAA,CAC7E,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAf,OAAO,CAACgB,KAAK,CAACJ,WAAW,CAAC,CACjD,KAAM,CAAEK,YAAa,CAAC,CAAGF,QAAQ,CAACG,IAAI,CAEtC;AACA,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAnB,OAAO,CAACoB,QAAQ,CAACH,YAAY,CAAC,CACzD,KAAM,CAAAI,QAAQ,CAAGF,YAAY,CAACD,IAAI,CAACN,WAAW,CAACU,QAAQ,CAAC,CAExD,MAAO,CACLlB,KAAK,CAAEa,YAAY,CACnBd,IAAI,CAAE,CACJmB,QAAQ,CAAEV,WAAW,CAACU,QAAQ,CAC9BC,SAAS,CAAE,CAAAF,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEG,UAAU,GAAI,KACrC,CACF,CAAC,CACH,CAAE,MAAOhB,KAAU,CAAE,KAAAiB,eAAA,CAAAC,oBAAA,CACnB,MAAO,CAAAZ,eAAe,CAAC,EAAAW,eAAA,CAAAjB,KAAK,CAACO,QAAQ,UAAAU,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBP,IAAI,UAAAQ,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,MAAM,CAAC,CAChE,CACF,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,mBAAmB,CAAG7B,gBAAgB,CACjD,qBAAqB,CACrB,MACEmB,IAAgG,CAAAW,KAAA,GAE7F,IADH,CAAEC,QAAQ,CAAEhB,eAAgB,CAAC,CAAAe,KAAA,CAE7B,GAAI,CACF,KAAM,CAAAE,KAAK,CAAGD,QAAQ,CAAC,CAAwB,CAC/C,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAf,OAAO,CAACgC,cAAc,CAACd,IAAI,CAAEa,KAAK,CAACE,IAAI,CAAC7B,KAAM,CAAC,CACtE,MAAO,CAAAW,QAAQ,CAACG,IAAI,CACtB,CAAE,MAAOV,KAAU,CAAE,KAAA0B,gBAAA,CAAAC,qBAAA,CACnB,MAAO,CAAArB,eAAe,CAAC,EAAAoB,gBAAA,CAAA1B,KAAK,CAACO,QAAQ,UAAAmB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBhB,IAAI,UAAAiB,qBAAA,iBAApBA,qBAAA,CAAsBR,MAAM,GAAI,QAAQ,CAAC,CAClE,CACF,CACF,CAAC,CAED,KAAM,CAAAS,SAAS,CAAGtC,WAAW,CAAC,CAC5BuC,IAAI,CAAE,MAAM,CACZpC,YAAY,CACZqC,QAAQ,CAAE,CACRC,MAAM,CAAGR,KAAK,EAAK,CACjBA,KAAK,CAAC7B,eAAe,CAAG,KAAK,CAC7B6B,KAAK,CAAC5B,IAAI,CAAG,IAAI,CACjB4B,KAAK,CAAC3B,KAAK,CAAG,IAAI,CAClB2B,KAAK,CAACtB,kBAAkB,CAAG,KAAK,CAChCsB,KAAK,CAACrB,mBAAmB,CAAG,KAAK,CACjCL,YAAY,CAACmC,UAAU,CAAC,OAAO,CAAC,CAChCnC,YAAY,CAACmC,UAAU,CAAC,UAAU,CAAC,CACrC,CAAC,CACDC,UAAU,CAAGV,KAAK,EAAK,CACrBA,KAAK,CAACvB,KAAK,CAAG,IAAI,CACpB,CAAC,CACDkC,qBAAqB,CAAEA,CAACX,KAAK,CAAEY,MAA8B,GAAK,CAChEZ,KAAK,CAACtB,kBAAkB,CAAGkC,MAAM,CAACC,OAAO,CAC3C,CAAC,CACD;AACAC,WAAW,CAAGd,KAAK,EAAK,CACtB,KAAM,CAAA3B,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAgB,QAAQ,CAAGjB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACjD,GAAIF,KAAK,EAAIkB,QAAQ,CAAE,CACrBS,KAAK,CAAC3B,KAAK,CAAGA,KAAK,CACnB2B,KAAK,CAAC7B,eAAe,CAAG,IAAI,CAC5B6B,KAAK,CAAC5B,IAAI,CAAG,CACXmB,QAAQ,CAAEA,QAAQ,CAClBC,SAAS,CAAE,KAAO;AACpB,CAAC,CACD;AACF,CACF,CACF,CAAC,CACDuB,aAAa,CAAGC,OAAO,EAAK,CAC1BA,OACE;AAAA,CACCC,OAAO,CAACrC,UAAU,CAACsC,OAAO,CAAGlB,KAAK,EAAK,CACtCA,KAAK,CAACxB,OAAO,CAAG,IAAI,CACpBwB,KAAK,CAACvB,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACDwC,OAAO,CAACrC,UAAU,CAACuC,SAAS,CAAE,CAACnB,KAAK,CAAEY,MAAM,GAAK,CAChDZ,KAAK,CAACxB,OAAO,CAAG,KAAK,CACrBwB,KAAK,CAAC7B,eAAe,CAAG,IAAI,CAC5B6B,KAAK,CAAC5B,IAAI,CAAGwC,MAAM,CAACC,OAAO,CAACzC,IAAI,CAChC4B,KAAK,CAAC3B,KAAK,CAAGuC,MAAM,CAACC,OAAO,CAACxC,KAAK,CAClC2B,KAAK,CAACtB,kBAAkB,CAAGkC,MAAM,CAACC,OAAO,CAACzC,IAAI,CAACoB,SAAS,CACxDQ,KAAK,CAACrB,mBAAmB,CAAGiC,MAAM,CAACC,OAAO,CAACzC,IAAI,CAACoB,SAAS,CACzDlB,YAAY,CAAC8C,OAAO,CAAC,OAAO,CAAER,MAAM,CAACC,OAAO,CAACxC,KAAK,CAAC,CACnDC,YAAY,CAAC8C,OAAO,CAAC,UAAU,CAAER,MAAM,CAACC,OAAO,CAACzC,IAAI,CAACmB,QAAQ,CAAC,CAChE,CAAC,CAAC,CACD0B,OAAO,CAACrC,UAAU,CAACyC,QAAQ,CAAE,CAACrB,KAAK,CAAEY,MAAM,GAAK,CAC/CZ,KAAK,CAACxB,OAAO,CAAG,KAAK,CACrBwB,KAAK,CAACvB,KAAK,CAAGmC,MAAM,CAACC,OAAiB,CACxC,CAAC,CACD;AAAA,CACCI,OAAO,CAACpB,mBAAmB,CAACqB,OAAO,CAAGlB,KAAK,EAAK,CAC/CA,KAAK,CAACxB,OAAO,CAAG,IAAI,CACpBwB,KAAK,CAACvB,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACDwC,OAAO,CAACpB,mBAAmB,CAACsB,SAAS,CAAGnB,KAAK,EAAK,CACjDA,KAAK,CAACxB,OAAO,CAAG,KAAK,CACrBwB,KAAK,CAACtB,kBAAkB,CAAG,KAAK,CAChCsB,KAAK,CAACrB,mBAAmB,CAAG,KAAK,CACjC,GAAIqB,KAAK,CAAC5B,IAAI,CAAE,CACd4B,KAAK,CAAC5B,IAAI,CAACoB,SAAS,CAAG,KAAK,CAC9B,CACF,CAAC,CAAC,CACDyB,OAAO,CAACpB,mBAAmB,CAACwB,QAAQ,CAAE,CAACrB,KAAK,CAAEY,MAAM,GAAK,CACxDZ,KAAK,CAACxB,OAAO,CAAG,KAAK,CACrBwB,KAAK,CAACvB,KAAK,CAAGmC,MAAM,CAACC,OAAiB,CACxC,CAAC,CAAC,CACN,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEL,MAAM,CAAEE,UAAU,CAAEC,qBAAqB,CAAEG,WAAY,CAAC,CAAGT,SAAS,CAACiB,OAAO,CAC3F,cAAe,CAAAjB,SAAS,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}