{"ast": null, "code": "var openParentheses = \"(\".charCodeAt(0);\nvar closeParentheses = \")\".charCodeAt(0);\nvar singleQuote = \"'\".charCodeAt(0);\nvar doubleQuote = '\"'.charCodeAt(0);\nvar backslash = \"\\\\\".charCodeAt(0);\nvar slash = \"/\".charCodeAt(0);\nvar comma = \",\".charCodeAt(0);\nvar colon = \":\".charCodeAt(0);\nvar star = \"*\".charCodeAt(0);\nmodule.exports = function (input) {\n  var tokens = [];\n  var value = input;\n  var next, quote, prev, token, escape, escapePos, whitespacePos;\n  var pos = 0;\n  var code = value.charCodeAt(pos);\n  var max = value.length;\n  var stack = [{\n    nodes: tokens\n  }];\n  var balanced = 0;\n  var parent;\n  var name = \"\";\n  var before = \"\";\n  var after = \"\";\n  while (pos < max) {\n    // Whitespaces\n    if (code <= 32) {\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = value.slice(pos, next);\n      prev = tokens[tokens.length - 1];\n      if (code === closeParentheses && balanced) {\n        after = token;\n      } else if (prev && prev.type === \"div\") {\n        prev.after = token;\n      } else if (code === comma || code === colon || code === slash && value.charCodeAt(next + 1) !== star) {\n        before = token;\n      } else {\n        tokens.push({\n          type: \"space\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n      pos = next;\n\n      // Quotes\n    } else if (code === singleQuote || code === doubleQuote) {\n      next = pos;\n      quote = code === singleQuote ? \"'\" : '\"';\n      token = {\n        type: \"string\",\n        sourceIndex: pos,\n        quote: quote\n      };\n      do {\n        escape = false;\n        next = value.indexOf(quote, next + 1);\n        if (~next) {\n          escapePos = next;\n          while (value.charCodeAt(escapePos - 1) === backslash) {\n            escapePos -= 1;\n            escape = !escape;\n          }\n        } else {\n          value += quote;\n          next = value.length - 1;\n          token.unclosed = true;\n        }\n      } while (escape);\n      token.value = value.slice(pos + 1, next);\n      tokens.push(token);\n      pos = next + 1;\n      code = value.charCodeAt(pos);\n\n      // Comments\n    } else if (code === slash && value.charCodeAt(pos + 1) === star) {\n      token = {\n        type: \"comment\",\n        sourceIndex: pos\n      };\n      next = value.indexOf(\"*/\", pos);\n      if (next === -1) {\n        token.unclosed = true;\n        next = value.length;\n      }\n      token.value = value.slice(pos + 2, next);\n      tokens.push(token);\n      pos = next + 2;\n      code = value.charCodeAt(pos);\n\n      // Dividers\n    } else if (code === slash || code === comma || code === colon) {\n      token = value[pos];\n      tokens.push({\n        type: \"div\",\n        sourceIndex: pos - before.length,\n        value: token,\n        before: before,\n        after: \"\"\n      });\n      before = \"\";\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Open parentheses\n    } else if (openParentheses === code) {\n      // Whitespaces after open parentheses\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = {\n        type: \"function\",\n        sourceIndex: pos - name.length,\n        value: name,\n        before: value.slice(pos + 1, next)\n      };\n      pos = next;\n      if (name === \"url\" && code !== singleQuote && code !== doubleQuote) {\n        next -= 1;\n        do {\n          escape = false;\n          next = value.indexOf(\")\", next + 1);\n          if (~next) {\n            escapePos = next;\n            while (value.charCodeAt(escapePos - 1) === backslash) {\n              escapePos -= 1;\n              escape = !escape;\n            }\n          } else {\n            value += \")\";\n            next = value.length - 1;\n            token.unclosed = true;\n          }\n        } while (escape);\n        // Whitespaces before closed\n        whitespacePos = next;\n        do {\n          whitespacePos -= 1;\n          code = value.charCodeAt(whitespacePos);\n        } while (code <= 32);\n        if (pos !== whitespacePos + 1) {\n          token.nodes = [{\n            type: \"word\",\n            sourceIndex: pos,\n            value: value.slice(pos, whitespacePos + 1)\n          }];\n        } else {\n          token.nodes = [];\n        }\n        if (token.unclosed && whitespacePos + 1 !== next) {\n          token.after = \"\";\n          token.nodes.push({\n            type: \"space\",\n            sourceIndex: whitespacePos + 1,\n            value: value.slice(whitespacePos + 1, next)\n          });\n        } else {\n          token.after = value.slice(whitespacePos + 1, next);\n        }\n        pos = next + 1;\n        code = value.charCodeAt(pos);\n        tokens.push(token);\n      } else {\n        balanced += 1;\n        token.after = \"\";\n        tokens.push(token);\n        stack.push(token);\n        tokens = token.nodes = [];\n        parent = token;\n      }\n      name = \"\";\n\n      // Close parentheses\n    } else if (closeParentheses === code && balanced) {\n      pos += 1;\n      code = value.charCodeAt(pos);\n      parent.after = after;\n      after = \"\";\n      balanced -= 1;\n      stack.pop();\n      parent = stack[balanced];\n      tokens = parent.nodes;\n\n      // Words\n    } else {\n      next = pos;\n      do {\n        if (code === backslash) {\n          next += 1;\n        }\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (next < max && !(code <= 32 || code === singleQuote || code === doubleQuote || code === comma || code === colon || code === slash || code === openParentheses || code === closeParentheses && balanced));\n      token = value.slice(pos, next);\n      if (openParentheses === code) {\n        name = token;\n      } else {\n        tokens.push({\n          type: \"word\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n      pos = next;\n    }\n  }\n  for (pos = stack.length - 1; pos; pos -= 1) {\n    stack[pos].unclosed = true;\n  }\n  return stack[0].nodes;\n};", "map": {"version": 3, "names": ["openParentheses", "charCodeAt", "closeParentheses", "singleQuote", "doubleQuote", "backslash", "slash", "comma", "colon", "star", "module", "exports", "input", "tokens", "value", "next", "quote", "prev", "token", "escape", "escapePos", "whitespacePos", "pos", "code", "max", "length", "stack", "nodes", "balanced", "parent", "name", "before", "after", "slice", "type", "push", "sourceIndex", "indexOf", "unclosed", "pop"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/reduce-css-calc/node_modules/postcss-value-parser/lib/parse.js"], "sourcesContent": ["var openParentheses = \"(\".charCodeAt(0);\nvar closeParentheses = \")\".charCodeAt(0);\nvar singleQuote = \"'\".charCodeAt(0);\nvar doubleQuote = '\"'.charCodeAt(0);\nvar backslash = \"\\\\\".charCodeAt(0);\nvar slash = \"/\".charCodeAt(0);\nvar comma = \",\".charCodeAt(0);\nvar colon = \":\".charCodeAt(0);\nvar star = \"*\".charCodeAt(0);\n\nmodule.exports = function(input) {\n  var tokens = [];\n  var value = input;\n\n  var next, quote, prev, token, escape, escapePos, whitespacePos;\n  var pos = 0;\n  var code = value.charCodeAt(pos);\n  var max = value.length;\n  var stack = [{ nodes: tokens }];\n  var balanced = 0;\n  var parent;\n\n  var name = \"\";\n  var before = \"\";\n  var after = \"\";\n\n  while (pos < max) {\n    // Whitespaces\n    if (code <= 32) {\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = value.slice(pos, next);\n\n      prev = tokens[tokens.length - 1];\n      if (code === closeParentheses && balanced) {\n        after = token;\n      } else if (prev && prev.type === \"div\") {\n        prev.after = token;\n      } else if (\n        code === comma ||\n        code === colon ||\n        (code === slash && value.charCodeAt(next + 1) !== star)\n      ) {\n        before = token;\n      } else {\n        tokens.push({\n          type: \"space\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n\n      pos = next;\n\n      // Quotes\n    } else if (code === singleQuote || code === doubleQuote) {\n      next = pos;\n      quote = code === singleQuote ? \"'\" : '\"';\n      token = {\n        type: \"string\",\n        sourceIndex: pos,\n        quote: quote\n      };\n      do {\n        escape = false;\n        next = value.indexOf(quote, next + 1);\n        if (~next) {\n          escapePos = next;\n          while (value.charCodeAt(escapePos - 1) === backslash) {\n            escapePos -= 1;\n            escape = !escape;\n          }\n        } else {\n          value += quote;\n          next = value.length - 1;\n          token.unclosed = true;\n        }\n      } while (escape);\n      token.value = value.slice(pos + 1, next);\n\n      tokens.push(token);\n      pos = next + 1;\n      code = value.charCodeAt(pos);\n\n      // Comments\n    } else if (code === slash && value.charCodeAt(pos + 1) === star) {\n      token = {\n        type: \"comment\",\n        sourceIndex: pos\n      };\n\n      next = value.indexOf(\"*/\", pos);\n      if (next === -1) {\n        token.unclosed = true;\n        next = value.length;\n      }\n\n      token.value = value.slice(pos + 2, next);\n      tokens.push(token);\n\n      pos = next + 2;\n      code = value.charCodeAt(pos);\n\n      // Dividers\n    } else if (code === slash || code === comma || code === colon) {\n      token = value[pos];\n\n      tokens.push({\n        type: \"div\",\n        sourceIndex: pos - before.length,\n        value: token,\n        before: before,\n        after: \"\"\n      });\n      before = \"\";\n\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      // Open parentheses\n    } else if (openParentheses === code) {\n      // Whitespaces after open parentheses\n      next = pos;\n      do {\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (code <= 32);\n      token = {\n        type: \"function\",\n        sourceIndex: pos - name.length,\n        value: name,\n        before: value.slice(pos + 1, next)\n      };\n      pos = next;\n\n      if (name === \"url\" && code !== singleQuote && code !== doubleQuote) {\n        next -= 1;\n        do {\n          escape = false;\n          next = value.indexOf(\")\", next + 1);\n          if (~next) {\n            escapePos = next;\n            while (value.charCodeAt(escapePos - 1) === backslash) {\n              escapePos -= 1;\n              escape = !escape;\n            }\n          } else {\n            value += \")\";\n            next = value.length - 1;\n            token.unclosed = true;\n          }\n        } while (escape);\n        // Whitespaces before closed\n        whitespacePos = next;\n        do {\n          whitespacePos -= 1;\n          code = value.charCodeAt(whitespacePos);\n        } while (code <= 32);\n        if (pos !== whitespacePos + 1) {\n          token.nodes = [\n            {\n              type: \"word\",\n              sourceIndex: pos,\n              value: value.slice(pos, whitespacePos + 1)\n            }\n          ];\n        } else {\n          token.nodes = [];\n        }\n        if (token.unclosed && whitespacePos + 1 !== next) {\n          token.after = \"\";\n          token.nodes.push({\n            type: \"space\",\n            sourceIndex: whitespacePos + 1,\n            value: value.slice(whitespacePos + 1, next)\n          });\n        } else {\n          token.after = value.slice(whitespacePos + 1, next);\n        }\n        pos = next + 1;\n        code = value.charCodeAt(pos);\n        tokens.push(token);\n      } else {\n        balanced += 1;\n        token.after = \"\";\n        tokens.push(token);\n        stack.push(token);\n        tokens = token.nodes = [];\n        parent = token;\n      }\n      name = \"\";\n\n      // Close parentheses\n    } else if (closeParentheses === code && balanced) {\n      pos += 1;\n      code = value.charCodeAt(pos);\n\n      parent.after = after;\n      after = \"\";\n      balanced -= 1;\n      stack.pop();\n      parent = stack[balanced];\n      tokens = parent.nodes;\n\n      // Words\n    } else {\n      next = pos;\n      do {\n        if (code === backslash) {\n          next += 1;\n        }\n        next += 1;\n        code = value.charCodeAt(next);\n      } while (\n        next < max &&\n        !(\n          code <= 32 ||\n          code === singleQuote ||\n          code === doubleQuote ||\n          code === comma ||\n          code === colon ||\n          code === slash ||\n          code === openParentheses ||\n          (code === closeParentheses && balanced)\n        )\n      );\n      token = value.slice(pos, next);\n\n      if (openParentheses === code) {\n        name = token;\n      } else {\n        tokens.push({\n          type: \"word\",\n          sourceIndex: pos,\n          value: token\n        });\n      }\n\n      pos = next;\n    }\n  }\n\n  for (pos = stack.length - 1; pos; pos -= 1) {\n    stack[pos].unclosed = true;\n  }\n\n  return stack[0].nodes;\n};\n"], "mappings": "AAAA,IAAIA,eAAe,GAAG,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;AACvC,IAAIC,gBAAgB,GAAG,GAAG,CAACD,UAAU,CAAC,CAAC,CAAC;AACxC,IAAIE,WAAW,GAAG,GAAG,CAACF,UAAU,CAAC,CAAC,CAAC;AACnC,IAAIG,WAAW,GAAG,GAAG,CAACH,UAAU,CAAC,CAAC,CAAC;AACnC,IAAII,SAAS,GAAG,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC;AAClC,IAAIK,KAAK,GAAG,GAAG,CAACL,UAAU,CAAC,CAAC,CAAC;AAC7B,IAAIM,KAAK,GAAG,GAAG,CAACN,UAAU,CAAC,CAAC,CAAC;AAC7B,IAAIO,KAAK,GAAG,GAAG,CAACP,UAAU,CAAC,CAAC,CAAC;AAC7B,IAAIQ,IAAI,GAAG,GAAG,CAACR,UAAU,CAAC,CAAC,CAAC;AAE5BS,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAE;EAC/B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAGF,KAAK;EAEjB,IAAIG,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,aAAa;EAC9D,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;EAChC,IAAIE,GAAG,GAAGV,KAAK,CAACW,MAAM;EACtB,IAAIC,KAAK,GAAG,CAAC;IAAEC,KAAK,EAAEd;EAAO,CAAC,CAAC;EAC/B,IAAIe,QAAQ,GAAG,CAAC;EAChB,IAAIC,MAAM;EAEV,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAG,EAAE;EAEd,OAAOV,GAAG,GAAGE,GAAG,EAAE;IAChB;IACA,IAAID,IAAI,IAAI,EAAE,EAAE;MACdR,IAAI,GAAGO,GAAG;MACV,GAAG;QACDP,IAAI,IAAI,CAAC;QACTQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACc,IAAI,CAAC;MAC/B,CAAC,QAAQQ,IAAI,IAAI,EAAE;MACnBL,KAAK,GAAGJ,KAAK,CAACmB,KAAK,CAACX,GAAG,EAAEP,IAAI,CAAC;MAE9BE,IAAI,GAAGJ,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC;MAChC,IAAIF,IAAI,KAAKrB,gBAAgB,IAAI0B,QAAQ,EAAE;QACzCI,KAAK,GAAGd,KAAK;MACf,CAAC,MAAM,IAAID,IAAI,IAAIA,IAAI,CAACiB,IAAI,KAAK,KAAK,EAAE;QACtCjB,IAAI,CAACe,KAAK,GAAGd,KAAK;MACpB,CAAC,MAAM,IACLK,IAAI,KAAKhB,KAAK,IACdgB,IAAI,KAAKf,KAAK,IACbe,IAAI,KAAKjB,KAAK,IAAIQ,KAAK,CAACb,UAAU,CAACc,IAAI,GAAG,CAAC,CAAC,KAAKN,IAAK,EACvD;QACAsB,MAAM,GAAGb,KAAK;MAChB,CAAC,MAAM;QACLL,MAAM,CAACsB,IAAI,CAAC;UACVD,IAAI,EAAE,OAAO;UACbE,WAAW,EAAEd,GAAG;UAChBR,KAAK,EAAEI;QACT,CAAC,CAAC;MACJ;MAEAI,GAAG,GAAGP,IAAI;;MAEV;IACF,CAAC,MAAM,IAAIQ,IAAI,KAAKpB,WAAW,IAAIoB,IAAI,KAAKnB,WAAW,EAAE;MACvDW,IAAI,GAAGO,GAAG;MACVN,KAAK,GAAGO,IAAI,KAAKpB,WAAW,GAAG,GAAG,GAAG,GAAG;MACxCe,KAAK,GAAG;QACNgB,IAAI,EAAE,QAAQ;QACdE,WAAW,EAAEd,GAAG;QAChBN,KAAK,EAAEA;MACT,CAAC;MACD,GAAG;QACDG,MAAM,GAAG,KAAK;QACdJ,IAAI,GAAGD,KAAK,CAACuB,OAAO,CAACrB,KAAK,EAAED,IAAI,GAAG,CAAC,CAAC;QACrC,IAAI,CAACA,IAAI,EAAE;UACTK,SAAS,GAAGL,IAAI;UAChB,OAAOD,KAAK,CAACb,UAAU,CAACmB,SAAS,GAAG,CAAC,CAAC,KAAKf,SAAS,EAAE;YACpDe,SAAS,IAAI,CAAC;YACdD,MAAM,GAAG,CAACA,MAAM;UAClB;QACF,CAAC,MAAM;UACLL,KAAK,IAAIE,KAAK;UACdD,IAAI,GAAGD,KAAK,CAACW,MAAM,GAAG,CAAC;UACvBP,KAAK,CAACoB,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,QAAQnB,MAAM;MACfD,KAAK,CAACJ,KAAK,GAAGA,KAAK,CAACmB,KAAK,CAACX,GAAG,GAAG,CAAC,EAAEP,IAAI,CAAC;MAExCF,MAAM,CAACsB,IAAI,CAACjB,KAAK,CAAC;MAClBI,GAAG,GAAGP,IAAI,GAAG,CAAC;MACdQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;;MAE5B;IACF,CAAC,MAAM,IAAIC,IAAI,KAAKjB,KAAK,IAAIQ,KAAK,CAACb,UAAU,CAACqB,GAAG,GAAG,CAAC,CAAC,KAAKb,IAAI,EAAE;MAC/DS,KAAK,GAAG;QACNgB,IAAI,EAAE,SAAS;QACfE,WAAW,EAAEd;MACf,CAAC;MAEDP,IAAI,GAAGD,KAAK,CAACuB,OAAO,CAAC,IAAI,EAAEf,GAAG,CAAC;MAC/B,IAAIP,IAAI,KAAK,CAAC,CAAC,EAAE;QACfG,KAAK,CAACoB,QAAQ,GAAG,IAAI;QACrBvB,IAAI,GAAGD,KAAK,CAACW,MAAM;MACrB;MAEAP,KAAK,CAACJ,KAAK,GAAGA,KAAK,CAACmB,KAAK,CAACX,GAAG,GAAG,CAAC,EAAEP,IAAI,CAAC;MACxCF,MAAM,CAACsB,IAAI,CAACjB,KAAK,CAAC;MAElBI,GAAG,GAAGP,IAAI,GAAG,CAAC;MACdQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;;MAE5B;IACF,CAAC,MAAM,IAAIC,IAAI,KAAKjB,KAAK,IAAIiB,IAAI,KAAKhB,KAAK,IAAIgB,IAAI,KAAKf,KAAK,EAAE;MAC7DU,KAAK,GAAGJ,KAAK,CAACQ,GAAG,CAAC;MAElBT,MAAM,CAACsB,IAAI,CAAC;QACVD,IAAI,EAAE,KAAK;QACXE,WAAW,EAAEd,GAAG,GAAGS,MAAM,CAACN,MAAM;QAChCX,KAAK,EAAEI,KAAK;QACZa,MAAM,EAAEA,MAAM;QACdC,KAAK,EAAE;MACT,CAAC,CAAC;MACFD,MAAM,GAAG,EAAE;MAEXT,GAAG,IAAI,CAAC;MACRC,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;;MAE5B;IACF,CAAC,MAAM,IAAItB,eAAe,KAAKuB,IAAI,EAAE;MACnC;MACAR,IAAI,GAAGO,GAAG;MACV,GAAG;QACDP,IAAI,IAAI,CAAC;QACTQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACc,IAAI,CAAC;MAC/B,CAAC,QAAQQ,IAAI,IAAI,EAAE;MACnBL,KAAK,GAAG;QACNgB,IAAI,EAAE,UAAU;QAChBE,WAAW,EAAEd,GAAG,GAAGQ,IAAI,CAACL,MAAM;QAC9BX,KAAK,EAAEgB,IAAI;QACXC,MAAM,EAAEjB,KAAK,CAACmB,KAAK,CAACX,GAAG,GAAG,CAAC,EAAEP,IAAI;MACnC,CAAC;MACDO,GAAG,GAAGP,IAAI;MAEV,IAAIe,IAAI,KAAK,KAAK,IAAIP,IAAI,KAAKpB,WAAW,IAAIoB,IAAI,KAAKnB,WAAW,EAAE;QAClEW,IAAI,IAAI,CAAC;QACT,GAAG;UACDI,MAAM,GAAG,KAAK;UACdJ,IAAI,GAAGD,KAAK,CAACuB,OAAO,CAAC,GAAG,EAAEtB,IAAI,GAAG,CAAC,CAAC;UACnC,IAAI,CAACA,IAAI,EAAE;YACTK,SAAS,GAAGL,IAAI;YAChB,OAAOD,KAAK,CAACb,UAAU,CAACmB,SAAS,GAAG,CAAC,CAAC,KAAKf,SAAS,EAAE;cACpDe,SAAS,IAAI,CAAC;cACdD,MAAM,GAAG,CAACA,MAAM;YAClB;UACF,CAAC,MAAM;YACLL,KAAK,IAAI,GAAG;YACZC,IAAI,GAAGD,KAAK,CAACW,MAAM,GAAG,CAAC;YACvBP,KAAK,CAACoB,QAAQ,GAAG,IAAI;UACvB;QACF,CAAC,QAAQnB,MAAM;QACf;QACAE,aAAa,GAAGN,IAAI;QACpB,GAAG;UACDM,aAAa,IAAI,CAAC;UAClBE,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACoB,aAAa,CAAC;QACxC,CAAC,QAAQE,IAAI,IAAI,EAAE;QACnB,IAAID,GAAG,KAAKD,aAAa,GAAG,CAAC,EAAE;UAC7BH,KAAK,CAACS,KAAK,GAAG,CACZ;YACEO,IAAI,EAAE,MAAM;YACZE,WAAW,EAAEd,GAAG;YAChBR,KAAK,EAAEA,KAAK,CAACmB,KAAK,CAACX,GAAG,EAAED,aAAa,GAAG,CAAC;UAC3C,CAAC,CACF;QACH,CAAC,MAAM;UACLH,KAAK,CAACS,KAAK,GAAG,EAAE;QAClB;QACA,IAAIT,KAAK,CAACoB,QAAQ,IAAIjB,aAAa,GAAG,CAAC,KAAKN,IAAI,EAAE;UAChDG,KAAK,CAACc,KAAK,GAAG,EAAE;UAChBd,KAAK,CAACS,KAAK,CAACQ,IAAI,CAAC;YACfD,IAAI,EAAE,OAAO;YACbE,WAAW,EAAEf,aAAa,GAAG,CAAC;YAC9BP,KAAK,EAAEA,KAAK,CAACmB,KAAK,CAACZ,aAAa,GAAG,CAAC,EAAEN,IAAI;UAC5C,CAAC,CAAC;QACJ,CAAC,MAAM;UACLG,KAAK,CAACc,KAAK,GAAGlB,KAAK,CAACmB,KAAK,CAACZ,aAAa,GAAG,CAAC,EAAEN,IAAI,CAAC;QACpD;QACAO,GAAG,GAAGP,IAAI,GAAG,CAAC;QACdQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;QAC5BT,MAAM,CAACsB,IAAI,CAACjB,KAAK,CAAC;MACpB,CAAC,MAAM;QACLU,QAAQ,IAAI,CAAC;QACbV,KAAK,CAACc,KAAK,GAAG,EAAE;QAChBnB,MAAM,CAACsB,IAAI,CAACjB,KAAK,CAAC;QAClBQ,KAAK,CAACS,IAAI,CAACjB,KAAK,CAAC;QACjBL,MAAM,GAAGK,KAAK,CAACS,KAAK,GAAG,EAAE;QACzBE,MAAM,GAAGX,KAAK;MAChB;MACAY,IAAI,GAAG,EAAE;;MAET;IACF,CAAC,MAAM,IAAI5B,gBAAgB,KAAKqB,IAAI,IAAIK,QAAQ,EAAE;MAChDN,GAAG,IAAI,CAAC;MACRC,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACqB,GAAG,CAAC;MAE5BO,MAAM,CAACG,KAAK,GAAGA,KAAK;MACpBA,KAAK,GAAG,EAAE;MACVJ,QAAQ,IAAI,CAAC;MACbF,KAAK,CAACa,GAAG,CAAC,CAAC;MACXV,MAAM,GAAGH,KAAK,CAACE,QAAQ,CAAC;MACxBf,MAAM,GAAGgB,MAAM,CAACF,KAAK;;MAErB;IACF,CAAC,MAAM;MACLZ,IAAI,GAAGO,GAAG;MACV,GAAG;QACD,IAAIC,IAAI,KAAKlB,SAAS,EAAE;UACtBU,IAAI,IAAI,CAAC;QACX;QACAA,IAAI,IAAI,CAAC;QACTQ,IAAI,GAAGT,KAAK,CAACb,UAAU,CAACc,IAAI,CAAC;MAC/B,CAAC,QACCA,IAAI,GAAGS,GAAG,IACV,EACED,IAAI,IAAI,EAAE,IACVA,IAAI,KAAKpB,WAAW,IACpBoB,IAAI,KAAKnB,WAAW,IACpBmB,IAAI,KAAKhB,KAAK,IACdgB,IAAI,KAAKf,KAAK,IACde,IAAI,KAAKjB,KAAK,IACdiB,IAAI,KAAKvB,eAAe,IACvBuB,IAAI,KAAKrB,gBAAgB,IAAI0B,QAAS,CACxC;MAEHV,KAAK,GAAGJ,KAAK,CAACmB,KAAK,CAACX,GAAG,EAAEP,IAAI,CAAC;MAE9B,IAAIf,eAAe,KAAKuB,IAAI,EAAE;QAC5BO,IAAI,GAAGZ,KAAK;MACd,CAAC,MAAM;QACLL,MAAM,CAACsB,IAAI,CAAC;UACVD,IAAI,EAAE,MAAM;UACZE,WAAW,EAAEd,GAAG;UAChBR,KAAK,EAAEI;QACT,CAAC,CAAC;MACJ;MAEAI,GAAG,GAAGP,IAAI;IACZ;EACF;EAEA,KAAKO,GAAG,GAAGI,KAAK,CAACD,MAAM,GAAG,CAAC,EAAEH,GAAG,EAAEA,GAAG,IAAI,CAAC,EAAE;IAC1CI,KAAK,CAACJ,GAAG,CAAC,CAACgB,QAAQ,GAAG,IAAI;EAC5B;EAEA,OAAOZ,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}