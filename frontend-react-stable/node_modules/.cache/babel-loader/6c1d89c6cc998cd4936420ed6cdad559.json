{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { YEAR_DECADE_COUNT } from '.';\nimport useCellClassName from '../../hooks/useCellClassName';\nimport { formatValue, isSameYear } from '../../utils/dateUtil';\nimport RangeContext from '../../RangeContext';\nimport PanelBody from '../PanelBody';\nexport var YEAR_COL_COUNT = 3;\nvar YEAR_ROW_COUNT = 4;\nfunction YearBody(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    viewDate = props.viewDate,\n    locale = props.locale,\n    generateConfig = props.generateConfig;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var yearPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  // =============================== Year ===============================\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));\n  var isInView = function isInView(date) {\n    var currentYearNumber = generateConfig.getYear(date);\n    return startYear <= currentYearNumber && currentYearNumber <= endYear;\n  };\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: yearPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameYear(generateConfig, current, target);\n    },\n    isInView: isInView,\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addYear(date, offset);\n    }\n  });\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: YEAR_ROW_COUNT,\n    colNum: YEAR_COL_COUNT,\n    baseDate: baseYear,\n    getCellText: generateConfig.getYear,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addYear,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default YearBody;", "map": {"version": 3, "names": ["_extends", "React", "YEAR_DECADE_COUNT", "useCellClassName", "formatValue", "isSameYear", "RangeContext", "PanelBody", "YEAR_COL_COUNT", "YEAR_ROW_COUNT", "YearBody", "props", "prefixCls", "value", "viewDate", "locale", "generateConfig", "_React$useContext", "useContext", "rangedValue", "hoverRangedValue", "yearPrefixCls", "concat", "yearNumber", "getYear", "startYear", "Math", "floor", "endYear", "baseYear", "setYear", "ceil", "isInView", "date", "currentYearNumber", "getCellClassName", "cellPrefixCls", "isSameCell", "current", "target", "offsetCell", "offset", "addYear", "createElement", "row<PERSON>um", "colNum", "baseDate", "getCellText", "getCellDate", "title<PERSON>ell", "format"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-picker/es/panels/YearPanel/YearBody.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { YEAR_DECADE_COUNT } from '.';\nimport useCellClassName from '../../hooks/useCellClassName';\nimport { formatValue, isSameYear } from '../../utils/dateUtil';\nimport RangeContext from '../../RangeContext';\nimport PanelBody from '../PanelBody';\nexport var YEAR_COL_COUNT = 3;\nvar YEAR_ROW_COUNT = 4;\nfunction YearBody(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    viewDate = props.viewDate,\n    locale = props.locale,\n    generateConfig = props.generateConfig;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var yearPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  // =============================== Year ===============================\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));\n  var isInView = function isInView(date) {\n    var currentYearNumber = generateConfig.getYear(date);\n    return startYear <= currentYearNumber && currentYearNumber <= endYear;\n  };\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: yearPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameYear(generateConfig, current, target);\n    },\n    isInView: isInView,\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addYear(date, offset);\n    }\n  });\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: YEAR_ROW_COUNT,\n    colNum: YEAR_COL_COUNT,\n    baseDate: baseYear,\n    getCellText: generateConfig.getYear,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addYear,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default YearBody;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,GAAG;AACrC,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAC9D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAO,IAAIC,cAAc,GAAG,CAAC;AAC7B,IAAIC,cAAc,GAAG,CAAC;AACtB,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,cAAc,GAAGL,KAAK,CAACK,cAAc;EACvC,IAAIC,iBAAiB,GAAGhB,KAAK,CAACiB,UAAU,CAACZ,YAAY,CAAC;IACpDa,WAAW,GAAGF,iBAAiB,CAACE,WAAW;IAC3CC,gBAAgB,GAAGH,iBAAiB,CAACG,gBAAgB;EACvD,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACV,SAAS,EAAE,OAAO,CAAC;EACjD;EACA,IAAIW,UAAU,GAAGP,cAAc,CAACQ,OAAO,CAACV,QAAQ,CAAC;EACjD,IAAIW,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,UAAU,GAAGrB,iBAAiB,CAAC,GAAGA,iBAAiB;EAC9E,IAAI0B,OAAO,GAAGH,SAAS,GAAGvB,iBAAiB,GAAG,CAAC;EAC/C,IAAI2B,QAAQ,GAAGb,cAAc,CAACc,OAAO,CAAChB,QAAQ,EAAEW,SAAS,GAAGC,IAAI,CAACK,IAAI,CAAC,CAACvB,cAAc,GAAGC,cAAc,GAAGP,iBAAiB,IAAI,CAAC,CAAC,CAAC;EACjI,IAAI8B,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIC,iBAAiB,GAAGlB,cAAc,CAACQ,OAAO,CAACS,IAAI,CAAC;IACpD,OAAOR,SAAS,IAAIS,iBAAiB,IAAIA,iBAAiB,IAAIN,OAAO;EACvE,CAAC;EACD,IAAIO,gBAAgB,GAAGhC,gBAAgB,CAAC;IACtCiC,aAAa,EAAEf,aAAa;IAC5BR,KAAK,EAAEA,KAAK;IACZG,cAAc,EAAEA,cAAc;IAC9BG,WAAW,EAAEA,WAAW;IACxBC,gBAAgB,EAAEA,gBAAgB;IAClCiB,UAAU,EAAE,SAASA,UAAUA,CAACC,OAAO,EAAEC,MAAM,EAAE;MAC/C,OAAOlC,UAAU,CAACW,cAAc,EAAEsB,OAAO,EAAEC,MAAM,CAAC;IACpD,CAAC;IACDP,QAAQ,EAAEA,QAAQ;IAClBQ,UAAU,EAAE,SAASA,UAAUA,CAACP,IAAI,EAAEQ,MAAM,EAAE;MAC5C,OAAOzB,cAAc,CAAC0B,OAAO,CAACT,IAAI,EAAEQ,MAAM,CAAC;IAC7C;EACF,CAAC,CAAC;EACF,OAAO,aAAaxC,KAAK,CAAC0C,aAAa,CAACpC,SAAS,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACrEiC,MAAM,EAAEnC,cAAc;IACtBoC,MAAM,EAAErC,cAAc;IACtBsC,QAAQ,EAAEjB,QAAQ;IAClBkB,WAAW,EAAE/B,cAAc,CAACQ,OAAO;IACnCW,gBAAgB,EAAEA,gBAAgB;IAClCa,WAAW,EAAEhC,cAAc,CAAC0B,OAAO;IACnCO,SAAS,EAAE,SAASA,SAASA,CAAChB,IAAI,EAAE;MAClC,OAAO7B,WAAW,CAAC6B,IAAI,EAAE;QACvBlB,MAAM,EAAEA,MAAM;QACdmC,MAAM,EAAE,MAAM;QACdlC,cAAc,EAAEA;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;AACL;AACA,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}