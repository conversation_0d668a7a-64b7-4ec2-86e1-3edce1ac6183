{"ast": null, "code": "import Table from './Table';\nimport { FooterComponents as Summary } from './Footer';\nimport Column from './sugar/Column';\nimport ColumnGroup from './sugar/ColumnGroup';\nimport { INTERNAL_COL_DEFINE } from './utils/legacyUtil';\nexport { Summary, Column, ColumnGroup, INTERNAL_COL_DEFINE };\nexport default Table;", "map": {"version": 3, "names": ["Table", "FooterComponents", "Summary", "Column", "ColumnGroup", "INTERNAL_COL_DEFINE"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-table/es/index.js"], "sourcesContent": ["import Table from './Table';\nimport { FooterComponents as Summary } from './Footer';\nimport Column from './sugar/Column';\nimport ColumnGroup from './sugar/ColumnGroup';\nimport { INTERNAL_COL_DEFINE } from './utils/legacyUtil';\nexport { Summary, Column, ColumnGroup, INTERNAL_COL_DEFINE };\nexport default Table;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,gBAAgB,IAAIC,OAAO,QAAQ,UAAU;AACtD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASH,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEC,mBAAmB;AAC1D,eAAeL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}