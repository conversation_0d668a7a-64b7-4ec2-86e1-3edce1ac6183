{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcTicks = exports.utcTickInterval = exports.timeTicks = exports.timeTickInterval = void 0;\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\nvar _duration = require(\"./duration.js\");\nvar _millisecond = _interopRequireDefault(require(\"./millisecond.js\"));\nvar _second = _interopRequireDefault(require(\"./second.js\"));\nvar _minute = _interopRequireDefault(require(\"./minute.js\"));\nvar _hour = _interopRequireDefault(require(\"./hour.js\"));\nvar _day = _interopRequireDefault(require(\"./day.js\"));\nvar _week = require(\"./week.js\");\nvar _month = _interopRequireDefault(require(\"./month.js\"));\nvar _year = _interopRequireDefault(require(\"./year.js\"));\nvar _utcMinute = _interopRequireDefault(require(\"./utcMinute.js\"));\nvar _utcHour = _interopRequireDefault(require(\"./utcHour.js\"));\nvar _utcDay = _interopRequireDefault(require(\"./utcDay.js\"));\nvar _utcWeek = require(\"./utcWeek.js\");\nvar _utcMonth = _interopRequireDefault(require(\"./utcMonth.js\"));\nvar _utcYear = _interopRequireDefault(require(\"./utcYear.js\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction ticker(year, month, week, day, hour, minute) {\n  const tickIntervals = [[_second.default, 1, _duration.durationSecond], [_second.default, 5, 5 * _duration.durationSecond], [_second.default, 15, 15 * _duration.durationSecond], [_second.default, 30, 30 * _duration.durationSecond], [minute, 1, _duration.durationMinute], [minute, 5, 5 * _duration.durationMinute], [minute, 15, 15 * _duration.durationMinute], [minute, 30, 30 * _duration.durationMinute], [hour, 1, _duration.durationHour], [hour, 3, 3 * _duration.durationHour], [hour, 6, 6 * _duration.durationHour], [hour, 12, 12 * _duration.durationHour], [day, 1, _duration.durationDay], [day, 2, 2 * _duration.durationDay], [week, 1, _duration.durationWeek], [month, 1, _duration.durationMonth], [month, 3, 3 * _duration.durationMonth], [year, 1, _duration.durationYear]];\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n\n    return reverse ? ticks.reverse() : ticks;\n  }\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = (0, _index.bisector)(_ref => {\n      let [,, step] = _ref;\n      return step;\n    }).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every((0, _index.tickStep)(start / _duration.durationYear, stop / _duration.durationYear, count));\n    if (i === 0) return _millisecond.default.every(Math.max((0, _index.tickStep)(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n  return [ticks, tickInterval];\n}\nconst [utcTicks, utcTickInterval] = ticker(_utcYear.default, _utcMonth.default, _utcWeek.utcSunday, _utcDay.default, _utcHour.default, _utcMinute.default);\nexports.utcTickInterval = utcTickInterval;\nexports.utcTicks = utcTicks;\nconst [timeTicks, timeTickInterval] = ticker(_year.default, _month.default, _week.sunday, _day.default, _hour.default, _minute.default);\nexports.timeTickInterval = timeTickInterval;\nexports.timeTicks = timeTicks;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "utcTicks", "utcTickInterval", "timeTicks", "timeTickInterval", "_index", "require", "_duration", "_millisecond", "_interopRequireDefault", "_second", "_minute", "_hour", "_day", "_week", "_month", "_year", "_utcMinute", "_utcHour", "_utcDay", "_utcWeek", "_utcMonth", "_utcYear", "obj", "__esModule", "default", "ticker", "year", "month", "week", "day", "hour", "minute", "tickIntervals", "durationSecond", "durationMinute", "durationHour", "durationDay", "durationWeek", "durationMonth", "durationYear", "ticks", "start", "stop", "count", "reverse", "interval", "range", "tickInterval", "target", "Math", "abs", "i", "bisector", "_ref", "step", "right", "length", "every", "tickStep", "max", "t", "utcSunday", "sunday"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-time/src/ticks.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.utcTicks = exports.utcTickInterval = exports.timeTicks = exports.timeTickInterval = void 0;\n\nvar _index = require(\"../../../lib-vendor/d3-array/src/index.js\");\n\nvar _duration = require(\"./duration.js\");\n\nvar _millisecond = _interopRequireDefault(require(\"./millisecond.js\"));\n\nvar _second = _interopRequireDefault(require(\"./second.js\"));\n\nvar _minute = _interopRequireDefault(require(\"./minute.js\"));\n\nvar _hour = _interopRequireDefault(require(\"./hour.js\"));\n\nvar _day = _interopRequireDefault(require(\"./day.js\"));\n\nvar _week = require(\"./week.js\");\n\nvar _month = _interopRequireDefault(require(\"./month.js\"));\n\nvar _year = _interopRequireDefault(require(\"./year.js\"));\n\nvar _utcMinute = _interopRequireDefault(require(\"./utcMinute.js\"));\n\nvar _utcHour = _interopRequireDefault(require(\"./utcHour.js\"));\n\nvar _utcDay = _interopRequireDefault(require(\"./utcDay.js\"));\n\nvar _utcWeek = require(\"./utcWeek.js\");\n\nvar _utcMonth = _interopRequireDefault(require(\"./utcMonth.js\"));\n\nvar _utcYear = _interopRequireDefault(require(\"./utcYear.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction ticker(year, month, week, day, hour, minute) {\n  const tickIntervals = [[_second.default, 1, _duration.durationSecond], [_second.default, 5, 5 * _duration.durationSecond], [_second.default, 15, 15 * _duration.durationSecond], [_second.default, 30, 30 * _duration.durationSecond], [minute, 1, _duration.durationMinute], [minute, 5, 5 * _duration.durationMinute], [minute, 15, 15 * _duration.durationMinute], [minute, 30, 30 * _duration.durationMinute], [hour, 1, _duration.durationHour], [hour, 3, 3 * _duration.durationHour], [hour, 6, 6 * _duration.durationHour], [hour, 12, 12 * _duration.durationHour], [day, 1, _duration.durationDay], [day, 2, 2 * _duration.durationDay], [week, 1, _duration.durationWeek], [month, 1, _duration.durationMonth], [month, 3, 3 * _duration.durationMonth], [year, 1, _duration.durationYear]];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = (0, _index.bisector)(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every((0, _index.tickStep)(start / _duration.durationYear, stop / _duration.durationYear, count));\n    if (i === 0) return _millisecond.default.every(Math.max((0, _index.tickStep)(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(_utcYear.default, _utcMonth.default, _utcWeek.utcSunday, _utcDay.default, _utcHour.default, _utcMinute.default);\nexports.utcTickInterval = utcTickInterval;\nexports.utcTicks = utcTicks;\nconst [timeTicks, timeTickInterval] = ticker(_year.default, _month.default, _week.sunday, _day.default, _hour.default, _minute.default);\nexports.timeTickInterval = timeTickInterval;\nexports.timeTicks = timeTicks;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGF,OAAO,CAACG,eAAe,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,gBAAgB,GAAG,KAAK,CAAC;AAElG,IAAIC,MAAM,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAEjE,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,IAAIE,YAAY,GAAGC,sBAAsB,CAACH,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAEtE,IAAII,OAAO,GAAGD,sBAAsB,CAACH,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIK,OAAO,GAAGF,sBAAsB,CAACH,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIM,KAAK,GAAGH,sBAAsB,CAACH,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,IAAIO,IAAI,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,UAAU,CAAC,CAAC;AAEtD,IAAIQ,KAAK,GAAGR,OAAO,CAAC,WAAW,CAAC;AAEhC,IAAIS,MAAM,GAAGN,sBAAsB,CAACH,OAAO,CAAC,YAAY,CAAC,CAAC;AAE1D,IAAIU,KAAK,GAAGP,sBAAsB,CAACH,OAAO,CAAC,WAAW,CAAC,CAAC;AAExD,IAAIW,UAAU,GAAGR,sBAAsB,CAACH,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAElE,IAAIY,QAAQ,GAAGT,sBAAsB,CAACH,OAAO,CAAC,cAAc,CAAC,CAAC;AAE9D,IAAIa,OAAO,GAAGV,sBAAsB,CAACH,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIc,QAAQ,GAAGd,OAAO,CAAC,cAAc,CAAC;AAEtC,IAAIe,SAAS,GAAGZ,sBAAsB,CAACH,OAAO,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAIgB,QAAQ,GAAGb,sBAAsB,CAACH,OAAO,CAAC,cAAc,CAAC,CAAC;AAE9D,SAASG,sBAAsBA,CAACc,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACpD,MAAMC,aAAa,GAAG,CAAC,CAACvB,OAAO,CAACe,OAAO,EAAE,CAAC,EAAElB,SAAS,CAAC2B,cAAc,CAAC,EAAE,CAACxB,OAAO,CAACe,OAAO,EAAE,CAAC,EAAE,CAAC,GAAGlB,SAAS,CAAC2B,cAAc,CAAC,EAAE,CAACxB,OAAO,CAACe,OAAO,EAAE,EAAE,EAAE,EAAE,GAAGlB,SAAS,CAAC2B,cAAc,CAAC,EAAE,CAACxB,OAAO,CAACe,OAAO,EAAE,EAAE,EAAE,EAAE,GAAGlB,SAAS,CAAC2B,cAAc,CAAC,EAAE,CAACF,MAAM,EAAE,CAAC,EAAEzB,SAAS,CAAC4B,cAAc,CAAC,EAAE,CAACH,MAAM,EAAE,CAAC,EAAE,CAAC,GAAGzB,SAAS,CAAC4B,cAAc,CAAC,EAAE,CAACH,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGzB,SAAS,CAAC4B,cAAc,CAAC,EAAE,CAACH,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGzB,SAAS,CAAC4B,cAAc,CAAC,EAAE,CAACJ,IAAI,EAAE,CAAC,EAAExB,SAAS,CAAC6B,YAAY,CAAC,EAAE,CAACL,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGxB,SAAS,CAAC6B,YAAY,CAAC,EAAE,CAACL,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGxB,SAAS,CAAC6B,YAAY,CAAC,EAAE,CAACL,IAAI,EAAE,EAAE,EAAE,EAAE,GAAGxB,SAAS,CAAC6B,YAAY,CAAC,EAAE,CAACN,GAAG,EAAE,CAAC,EAAEvB,SAAS,CAAC8B,WAAW,CAAC,EAAE,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,GAAGvB,SAAS,CAAC8B,WAAW,CAAC,EAAE,CAACR,IAAI,EAAE,CAAC,EAAEtB,SAAS,CAAC+B,YAAY,CAAC,EAAE,CAACV,KAAK,EAAE,CAAC,EAAErB,SAAS,CAACgC,aAAa,CAAC,EAAE,CAACX,KAAK,EAAE,CAAC,EAAE,CAAC,GAAGrB,SAAS,CAACgC,aAAa,CAAC,EAAE,CAACZ,IAAI,EAAE,CAAC,EAAEpB,SAAS,CAACiC,YAAY,CAAC,CAAC;EAEtwB,SAASC,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACjC,MAAMC,OAAO,GAAGF,IAAI,GAAGD,KAAK;IAC5B,IAAIG,OAAO,EAAE,CAACH,KAAK,EAAEC,IAAI,CAAC,GAAG,CAACA,IAAI,EAAED,KAAK,CAAC;IAC1C,MAAMI,QAAQ,GAAGF,KAAK,IAAI,OAAOA,KAAK,CAACG,KAAK,KAAK,UAAU,GAAGH,KAAK,GAAGI,YAAY,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IACtG,MAAMH,KAAK,GAAGK,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAACL,KAAK,EAAE,CAACC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;;IAEhE,OAAOE,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;EAC1C;EAEA,SAASO,YAAYA,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACxC,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACR,IAAI,GAAGD,KAAK,CAAC,GAAGE,KAAK;IAC7C,MAAMQ,CAAC,GAAG,CAAC,CAAC,EAAE/C,MAAM,CAACgD,QAAQ,EAAEC,IAAA;MAAA,IAAC,IAAIC,IAAI,CAAC,GAAAD,IAAA;MAAA,OAAKC,IAAI;IAAA,EAAC,CAACC,KAAK,CAACvB,aAAa,EAAEgB,MAAM,CAAC;IAChF,IAAIG,CAAC,KAAKnB,aAAa,CAACwB,MAAM,EAAE,OAAO9B,IAAI,CAAC+B,KAAK,CAAC,CAAC,CAAC,EAAErD,MAAM,CAACsD,QAAQ,EAAEjB,KAAK,GAAGnC,SAAS,CAACiC,YAAY,EAAEG,IAAI,GAAGpC,SAAS,CAACiC,YAAY,EAAEI,KAAK,CAAC,CAAC;IAC7I,IAAIQ,CAAC,KAAK,CAAC,EAAE,OAAO5C,YAAY,CAACiB,OAAO,CAACiC,KAAK,CAACR,IAAI,CAACU,GAAG,CAAC,CAAC,CAAC,EAAEvD,MAAM,CAACsD,QAAQ,EAAEjB,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACrG,MAAM,CAACiB,CAAC,EAAEN,IAAI,CAAC,GAAGtB,aAAa,CAACgB,MAAM,GAAGhB,aAAa,CAACmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGnB,aAAa,CAACmB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,GAAGG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;IAC5G,OAAOS,CAAC,CAACH,KAAK,CAACH,IAAI,CAAC;EACtB;EAEA,OAAO,CAACd,KAAK,EAAEO,YAAY,CAAC;AAC9B;AAEA,MAAM,CAAC/C,QAAQ,EAAEC,eAAe,CAAC,GAAGwB,MAAM,CAACJ,QAAQ,CAACG,OAAO,EAAEJ,SAAS,CAACI,OAAO,EAAEL,QAAQ,CAAC0C,SAAS,EAAE3C,OAAO,CAACM,OAAO,EAAEP,QAAQ,CAACO,OAAO,EAAER,UAAU,CAACQ,OAAO,CAAC;AAC1J1B,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzCH,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3B,MAAM,CAACE,SAAS,EAAEC,gBAAgB,CAAC,GAAGsB,MAAM,CAACV,KAAK,CAACS,OAAO,EAAEV,MAAM,CAACU,OAAO,EAAEX,KAAK,CAACiD,MAAM,EAAElD,IAAI,CAACY,OAAO,EAAEb,KAAK,CAACa,OAAO,EAAEd,OAAO,CAACc,OAAO,CAAC;AACvI1B,OAAO,CAACK,gBAAgB,GAAGA,gBAAgB;AAC3CL,OAAO,CAACI,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script"}