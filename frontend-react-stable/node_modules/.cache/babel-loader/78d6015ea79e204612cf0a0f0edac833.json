{"ast": null, "code": "/**\n * React Router DOM v6.8.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_enhanceManualRouteObjects, Router, createPath, useHref, useResolvedPath, useLocation, UNSAFE_DataRouterStateContext, UNSAFE_NavigationContext, useNavigate, UNSAFE_RouteContext, useMatches, useNavigation, unstable_useBlocker, UNSAFE_DataRouterContext } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, RouterProvider, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_enhanceManualRouteObjects, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, renderMatches, resolvePath, unstable_useBlocker, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { createRouter, createBrowserHistory, createHashHistory, ErrorResponse, invariant, joinPaths } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\n\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    for (let key of defaultSearchParams.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n  }\n  return searchParams;\n}\nfunction getFormSubmissionInfo(target, defaultAction, options) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  if (isFormElement(target)) {\n    let submissionTrigger = options.submissionTrigger;\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(target);\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    } // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    method = options.method || target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"formaction\") || form.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"formenctype\") || form.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(form); // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || defaultAction;\n    encType = options.encType || defaultEncType;\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n  let {\n    protocol,\n    host\n  } = window.location;\n  let url = new URL(action, protocol + \"//\" + host);\n  return {\n    url,\n    method: method.toLowerCase(),\n    encType,\n    formData\n  };\n}\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"],\n  _excluded3 = [\"reloadDocument\", \"replace\", \"method\", \"action\", \"onSubmit\", \"fetcherKey\", \"routeId\", \"relative\", \"preventScrollReset\"];\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\nfunction parseHydrationData() {\n  var _window;\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      let error = new Error(val.message); // Wipe away the client-side stack trace.  Nothing to fill it in with\n      // because we don't serialize SSR stack traces for security reasons\n\n      error.stack = \"\";\n      serialized[key] = error;\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\n\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\n\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref2;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\n\nfunction HistoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    history\n  } = _ref3;\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\n/**\n * The public API for rendering a history-aware <a>.\n */\n\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref4, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset\n    } = _ref4,\n    rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n\n  // `location` is the unaltered href we will render in the <a> tag for absolute URLs\n  let location = typeof to === \"string\" ? to : createPath(to);\n  let isAbsolute = /^[a-z+]+:\\/\\//i.test(location) || location.startsWith(\"//\"); // Location to use in the click handler\n\n  let navigationLocation = location;\n  let isExternal = false;\n  if (isBrowser && isAbsolute) {\n    let currentUrl = new URL(window.location.href);\n    let targetUrl = location.startsWith(\"//\") ? new URL(currentUrl.protocol + location) : new URL(location);\n    if (targetUrl.origin === currentUrl.origin) {\n      // Strip the protocol/origin for same-origin absolute URLs\n      navigationLocation = targetUrl.pathname + targetUrl.search + targetUrl.hash;\n    } else {\n      isExternal = true;\n    }\n  } // `href` is what we render in the <a> tag for relative URLs\n\n  let href = useHref(navigationLocation, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(navigationLocation, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return (/*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: isAbsolute ? location : href,\n      onClick: isExternal || reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\n\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref5, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children\n    } = _ref5,\n    rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator\n  } = React.useContext(UNSAFE_NavigationContext);\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive,\n      isPending\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive,\n    isPending\n  }) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive,\n    isPending\n  }) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\n\nconst Form = /*#__PURE__*/React.forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\nconst FormImpl = /*#__PURE__*/React.forwardRef((_ref6, forwardedRef) => {\n  let {\n      reloadDocument,\n      replace,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      fetcherKey,\n      routeId,\n      relative,\n      preventScrollReset\n    } = _ref6,\n    props = _objectWithoutPropertiesLoose(_ref6, _excluded3);\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      method: submitMethod,\n      replace,\n      relative,\n      preventScrollReset\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  FormImpl.displayName = \"FormImpl\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\n\nfunction ScrollRestoration(_ref7) {\n  let {\n    getKey,\n    storageKey\n  } = _ref7;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmitImpl\"] = \"useSubmitImpl\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return state;\n}\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\n\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n  let location = useLocation();\n  let searchParams = React.useMemo(() =>\n  // Only merge in the defaults if we haven't yet called setSearchParams.\n  // Once we call that we want those to take precedence, otherwise you can't\n  // remove a param with setSearchParams({}) if it has an initial value\n  getSearchParamsForLocation(location.search, hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    hasSetSearchParamsRef.current = true;\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\n\nfunction useSubmit() {\n  return useSubmitImpl();\n}\nfunction useSubmitImpl(fetcherKey, routeId) {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let defaultAction = useFormAction();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (typeof document === \"undefined\") {\n      throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n    }\n    let {\n      method,\n      encType,\n      formData,\n      url\n    } = getFormSubmissionInfo(target, defaultAction, options);\n    let href = url.pathname + url.search;\n    let opts = {\n      replace: options.replace,\n      preventScrollReset: options.preventScrollReset,\n      formData,\n      formMethod: method,\n      formEncType: encType\n    };\n    if (fetcherKey) {\n      !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for useFetcher()\") : invariant(false) : void 0;\n      router.fetch(fetcherKey, routeId, href, opts);\n    } else {\n      router.navigate(href, opts);\n    }\n  }, [defaultAction, router, fetcherKey, routeId]);\n}\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFormAction must be used inside a RouteContext\") : invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1); // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  })); // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash; // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? \"?\" + params.toString() : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  } // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\nfunction createFetcherForm(fetcherKey, routeId) {\n  let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n    return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n      ref: ref,\n      fetcherKey: fetcherKey,\n      routeId: routeId\n    }));\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\nlet fetcherId = 0;\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\n\nfunction useFetcher() {\n  var _route$matches;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let route = React.useContext(UNSAFE_RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher must be used inside a RouteContext\") : invariant(false) : void 0;\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : invariant(false) : void 0;\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.Form()\") : invariant(false) : void 0;\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => href => {\n    !router ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No router available for fetcher.load()\") : invariant(false) : void 0;\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.load()\") : invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let fetcher = router.getFetcher(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form,\n    submit,\n    load\n  }, fetcher), [fetcher, Form, submit, load]);\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(\"No fetcher available to clean up from useFetcher()\");\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\n\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\n\nfunction useScrollRestoration(_temp3) {\n  let {\n    getKey,\n    storageKey\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation(); // Trigger manual scroll restoration while we're active\n\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []); // Save positions on pagehide\n\n  usePageHide(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches])); // Read in any saved scroll locations\n\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {// no-op, use default empty object\n      }\n    }, [storageKey]); // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n\n    React.useLayoutEffect(() => {\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKey);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, getKey]); // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      } // been here before, scroll to it\n\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      } // try to scroll to the hash\n\n      if (location.hash) {\n        let el = document.getElementById(location.hash.slice(1));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      } // Don't reset if this navigation opted out\n\n      if (preventScrollReset === true) {\n        return;\n      } // otherwise go to the top on new locations\n\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\n\nfunction useBeforeUnload(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\n\nfunction usePageHide(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\n\nfunction usePrompt(_ref8) {\n  let {\n    when,\n    message\n  } = _ref8;\n  let blocker = unstable_useBlocker(when);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n}\n////////////////////////////////////////////////////////////////////////////////\n//#region Utils\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} //#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, ScrollRestoration, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, usePrompt as unstable_usePrompt, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit };", "map": {"version": 3, "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "has", "getAll", "for<PERSON>ach", "append", "getFormSubmissionInfo", "defaultAction", "options", "method", "action", "encType", "formData", "submissionTrigger", "getAttribute", "FormData", "name", "type", "form", "Error", "protocol", "host", "window", "location", "url", "URL", "createBrowserRouter", "routes", "opts", "createRouter", "basename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "UNSAFE_enhanceManualRouteObjects", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "_extends", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponse", "status", "statusText", "data", "internal", "error", "message", "stack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "historyRef", "React", "useRef", "current", "v5Compat", "setState", "useState", "useLayoutEffect", "listen", "createElement", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "HistoryRouter", "_ref3", "process", "env", "NODE_ENV", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "document", "Link", "forwardRef", "LinkWithRef", "_ref4", "ref", "onClick", "relative", "reloadDocument", "replace", "to", "preventScrollReset", "rest", "_objectWithoutPropertiesLoose", "_excluded", "createPath", "isAbsolute", "test", "startsWith", "navigationLocation", "isExternal", "currentUrl", "href", "targetUrl", "origin", "pathname", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref5", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "path", "useResolvedPath", "useLocation", "routerState", "useContext", "UNSAFE_DataRouterStateContext", "UNSAFE_NavigationContext", "toPathname", "encodeLocation", "locationPathname", "nextLocationPathname", "navigation", "isActive", "char<PERSON>t", "length", "isPending", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "Form", "props", "FormImpl", "_ref6", "forwardedRef", "onSubmit", "fetcher<PERSON>ey", "routeId", "_excluded3", "submit", "useSubmitImpl", "formMethod", "formAction", "useFormAction", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref7", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "UNSAFE_DataRouterContext", "invariant", "useDataRouterState", "_temp", "replaceProp", "navigate", "useNavigate", "useCallback", "useSearchParams", "defaultInit", "warning", "defaultSearchParamsRef", "hasSetSearchParamsRef", "useMemo", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "useSubmit", "router", "UseSubmitImpl", "formEncType", "fetch", "_temp2", "routeContext", "UNSAFE_RouteContext", "match", "matches", "slice", "route", "index", "params", "delete", "toString", "joinPaths", "createFetcherForm", "FetcherForm", "fetcherId", "useFetcher", "_route$matches", "UseFetcher", "id", "String", "load", "fetcher", "getFetcher", "fetcherWithComponents", "useEffect", "console", "warn", "deleteFetcher", "useFetchers", "UseFetchers", "fetchers", "values", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp3", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "e", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref8", "when", "blocker", "unstable_useBlocker", "reset", "proceed", "confirm", "setTimeout", "cond"], "sources": ["../dom.ts", "../index.tsx"], "sourcesContent": ["import type { FormEncType, FormMethod } from \"@remix-run/router\";\nimport type { RelativeRoutingType } from \"react-router\";\n\nexport const defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    for (let key of defaultSearchParams.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n  }\n\n  return searchParams;\n}\n\nexport interface SubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: FormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   *\n   * Note: It is assumed the path is already resolved. If you need to resolve a\n   * relative path, use `useFormAction`.\n   */\n  action?: string;\n\n  /**\n   * The action URL used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n}\n\nexport function getFormSubmissionInfo(\n  target:\n    | HTMLFormElement\n    | HTMLButtonElement\n    | HTMLInputElement\n    | FormData\n    | URLSearchParams\n    | { [name: string]: string }\n    | null,\n  defaultAction: string,\n  options: SubmitOptions\n): {\n  url: URL;\n  method: string;\n  encType: string;\n  formData: FormData;\n} {\n  let method: string;\n  let action: string;\n  let encType: string;\n  let formData: FormData;\n\n  if (isFormElement(target)) {\n    let submissionTrigger: HTMLButtonElement | HTMLInputElement = (\n      options as any\n    ).submissionTrigger;\n\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"action\") || defaultAction;\n    encType =\n      options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n\n    formData = new FormData(target);\n\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    method =\n      options.method ||\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    action =\n      options.action ||\n      target.getAttribute(\"formaction\") ||\n      form.getAttribute(\"action\") ||\n      defaultAction;\n    encType =\n      options.encType ||\n      target.getAttribute(\"formenctype\") ||\n      form.getAttribute(\"enctype\") ||\n      defaultEncType;\n\n    formData = new FormData(form);\n\n    // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || defaultAction;\n    encType = options.encType || defaultEncType;\n\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n\n  let { protocol, host } = window.location;\n  let url = new URL(action, `${protocol}//${host}`);\n\n  return { url, method: method.toLowerCase(), encType, formData };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type {\n  NavigateOptions,\n  RelativeRoutingType,\n  RouteObject,\n  To,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  unstable_useBlocker as useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_enhanceManualRouteObjects as enhanceManualRouteObjects,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HydrationState,\n  Router as RemixRouter,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  invariant,\n  joinPaths,\n  ErrorResponse,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n};\nexport { createSearchParams };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  unstable_Blocker,\n  unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_enhanceManualRouteObjects,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    window?: Window;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    window?: Window;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      let error = new Error(val.message);\n      // Wipe away the client-side stack trace.  Nothing to fill it in with\n      // because we don't serialize SSR stack traces for security reasons\n      error.stack = \"\";\n      serialized[key] = error;\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      ...rest\n    },\n    ref\n  ) {\n    // `location` is the unaltered href we will render in the <a> tag for absolute URLs\n    let location = typeof to === \"string\" ? to : createPath(to);\n    let isAbsolute =\n      /^[a-z+]+:\\/\\//i.test(location) || location.startsWith(\"//\");\n\n    // Location to use in the click handler\n    let navigationLocation = location;\n    let isExternal = false;\n    if (isBrowser && isAbsolute) {\n      let currentUrl = new URL(window.location.href);\n      let targetUrl = location.startsWith(\"//\")\n        ? new URL(currentUrl.protocol + location)\n        : new URL(location);\n      if (targetUrl.origin === currentUrl.origin) {\n        // Strip the protocol/origin for same-origin absolute URLs\n        navigationLocation =\n          targetUrl.pathname + targetUrl.search + targetUrl.hash;\n      } else {\n        isExternal = true;\n      }\n    }\n\n    // `href` is what we render in the <a> tag for relative URLs\n    let href = useHref(navigationLocation, { relative });\n\n    let internalOnClick = useLinkClickHandler(navigationLocation, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={isAbsolute ? location : href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean; isPending: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?:\n    | string\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => React.CSSProperties | undefined);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator } = React.useContext(NavigationContext);\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive, isPending });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\"\n        ? styleProp({ isActive, isPending })\n        : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\"\n          ? children({ isActive, isPending })\n          : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\nexport interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: FormMethod;\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props, ref) => {\n    return <FormImpl {...props} ref={ref} />;\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\ninterface FormImplProps extends FormProps {\n  fetcherKey?: string;\n  routeId?: string;\n}\n\nconst FormImpl = React.forwardRef<HTMLFormElement, FormImplProps>(\n  (\n    {\n      reloadDocument,\n      replace,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      fetcherKey,\n      routeId,\n      relative,\n      preventScrollReset,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmitImpl(fetcherKey, routeId);\n    let formMethod: FormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let formAction = useFormAction(action, { relative });\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as FormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        method: submitMethod,\n        replace,\n        relative,\n        preventScrollReset,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  FormImpl.displayName = \"FormImpl\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmitImpl = \"useSubmitImpl\",\n  UseFetcher = \"useFetcher\",\n}\n\nenum DataRouterStateHook {\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, { replace, state, preventScrollReset, relative });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\ntype SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\ntype SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | { [name: string]: string }\n  | null;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  return useSubmitImpl();\n}\n\nfunction useSubmitImpl(fetcherKey?: string, routeId?: string): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let defaultAction = useFormAction();\n\n  return React.useCallback(\n    (target, options = {}) => {\n      if (typeof document === \"undefined\") {\n        throw new Error(\n          \"You are calling submit during the server render. \" +\n            \"Try calling submit within a `useEffect` or callback instead.\"\n        );\n      }\n\n      let { method, encType, formData, url } = getFormSubmissionInfo(\n        target,\n        defaultAction,\n        options\n      );\n\n      let href = url.pathname + url.search;\n      let opts = {\n        replace: options.replace,\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        formMethod: method as FormMethod,\n        formEncType: encType as FormEncType,\n      };\n      if (fetcherKey) {\n        invariant(routeId != null, \"No routeId available for useFetcher()\");\n        router.fetch(fetcherKey, routeId, href, opts);\n      } else {\n        router.navigate(href, opts);\n      }\n    },\n    [defaultAction, router, fetcherKey, routeId]\n  );\n}\n\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash;\n\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey: string, routeId: string) {\n  let FetcherForm = React.forwardRef<HTMLFormElement, FormProps>(\n    (props, ref) => {\n      return (\n        <FormImpl\n          {...props}\n          ref={ref}\n          fetcherKey={fetcherKey}\n          routeId={routeId}\n        />\n      );\n    }\n  );\n  if (__DEV__) {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: ReturnType<typeof createFetcherForm>;\n  submit: (\n    target: SubmitTarget,\n    // Fetchers cannot replace/preventScrollReset because they are not\n    // navigation events\n    options?: Omit<SubmitOptions, \"replace\" | \"preventScrollReset\">\n  ) => void;\n  load: (href: string) => void;\n};\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>(): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    invariant(routeId, `No routeId available for fetcher.Form()`);\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => (href: string) => {\n    invariant(router, \"No router available for fetcher.load()\");\n    invariant(routeId, \"No routeId available for fetcher.load()\");\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n\n  let fetcher = router.getFetcher<TData>(fetcherKey);\n\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form,\n      submit,\n      load,\n      ...fetcher,\n    }),\n    [fetcher, Form, submit, load]\n  );\n\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(`No fetcher available to clean up from useFetcher()`);\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): Fetcher[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      sessionStorage.setItem(\n        storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n        JSON.stringify(savedScrollPositions)\n      );\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKey\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(location.hash.slice(1));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({ when, message }: { when: boolean; message: string }) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Utils\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond: boolean, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n//#endregion\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAMA,aAAa,GAAG,KAAtB;AACP,MAAMC,cAAc,GAAG,mCAAvB;AAEM,SAAUC,aAAVA,CAAwBC,MAAxB,EAAmC;EACvC,OAAOA,MAAM,IAAI,IAAV,IAAkB,OAAOA,MAAM,CAACC,OAAd,KAA0B,QAAnD;AACD;AAEK,SAAUC,eAAVA,CAA0BF,MAA1B,EAAqC;EACzC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,QAAjE;AACD;AAEK,SAAUC,aAAVA,CAAwBJ,MAAxB,EAAmC;EACvC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,MAAjE;AACD;AAEK,SAAUE,cAAVA,CAAyBL,MAAzB,EAAoC;EACxC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,OAAjE;AACD;AAOD,SAASG,eAATA,CAAyBC,KAAzB,EAAiD;EAC/C,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAN,IAAiBD,KAAK,CAACE,MAAvB,IAAiCF,KAAK,CAACG,OAAvC,IAAkDH,KAAK,CAACI,QAA1D,CAAR;AACD;AAEe,SAAAC,uBACdL,KADc,EAEdM,MAFc,EAEC;EAEf,OACEN,KAAK,CAACO,MAAN,KAAiB,CAAjB;EAAA;EACC,CAACD,MAAD,IAAWA,MAAM,KAAK,OADvB,CACmC;EAAA;EACnC,CAACP,eAAe,CAACC,KAAD,CAHlB;EAAA;AAKD;AAUD;;;;;;;;;;;;;;;;;;;;AAoBG;;AACa,SAAAQ,mBACdC,IADc,EACgB;EAAA,IAA9BA,IAA8B;IAA9BA,IAA8B,GAAF,EAAE;EAAA;EAE9B,OAAO,IAAIC,eAAJ,CACL,OAAOD,IAAP,KAAgB,QAAhB,IACAE,KAAK,CAACC,OAAN,CAAcH,IAAd,CADA,IAEAA,IAAI,YAAYC,eAFhB,GAGID,IAHJ,GAIII,MAAM,CAACC,IAAP,CAAYL,IAAZ,EAAkBM,MAAlB,CAAyB,CAACC,IAAD,EAAOC,GAAP,KAAc;IACrC,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAD,CAAhB;IACA,OAAOD,IAAI,CAACG,MAAL,CACLR,KAAK,CAACC,OAAN,CAAcM,KAAd,IAAuBA,KAAK,CAACE,GAAN,CAAWC,CAAD,IAAO,CAACJ,GAAD,EAAMI,CAAN,CAAjB,CAAvB,GAAoD,CAAC,CAACJ,GAAD,EAAMC,KAAN,CAAD,CAD/C,CAAP;GAFF,EAKG,EALH,CALC,CAAP;AAYD;AAEe,SAAAI,2BACdC,cADc,EAEdC,mBAFc,EAE6B;EAE3C,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAD,CAArC;EAEA,IAAIC,mBAAJ,EAAyB;IACvB,KAAK,IAAIP,GAAT,IAAgBO,mBAAmB,CAACV,IAApB,EAAhB,EAA4C;MAC1C,IAAI,CAACW,YAAY,CAACC,GAAb,CAAiBT,GAAjB,CAAL,EAA4B;QAC1BO,mBAAmB,CAACG,MAApB,CAA2BV,GAA3B,EAAgCW,OAAhC,CAAyCV,KAAD,IAAU;UAChDO,YAAY,CAACI,MAAb,CAAoBZ,GAApB,EAAyBC,KAAzB;SADF;MAGD;IACF;EACF;EAED,OAAOO,YAAP;AACD;SA6CeK,sBACdxB,MAAA,EAQAyB,aAAA,EACAC,OAAA,EAAsB;EAOtB,IAAIC,MAAJ;EACA,IAAIC,MAAJ;EACA,IAAIC,OAAJ;EACA,IAAIC,QAAJ;EAEA,IAAIvC,aAAa,CAACS,MAAD,CAAjB,EAA2B;IACzB,IAAI+B,iBAAiB,GACnBL,OACD,CAACK,iBAFF;IAIAJ,MAAM,GAAGD,OAAO,CAACC,MAAR,IAAkB3B,MAAM,CAACgC,YAAP,CAAoB,QAApB,CAAlB,IAAmDhD,aAA5D;IACA4C,MAAM,GAAGF,OAAO,CAACE,MAAR,IAAkB5B,MAAM,CAACgC,YAAP,CAAoB,QAApB,CAAlB,IAAmDP,aAA5D;IACAI,OAAO,GACLH,OAAO,CAACG,OAAR,IAAmB7B,MAAM,CAACgC,YAAP,CAAoB,SAApB,CAAnB,IAAqD/C,cADvD;IAGA6C,QAAQ,GAAG,IAAIG,QAAJ,CAAajC,MAAb,CAAX;IAEA,IAAI+B,iBAAiB,IAAIA,iBAAiB,CAACG,IAA3C,EAAiD;MAC/CJ,QAAQ,CAACP,MAAT,CAAgBQ,iBAAiB,CAACG,IAAlC,EAAwCH,iBAAiB,CAACnB,KAA1D;IACD;GAdH,MAeO,IACLvB,eAAe,CAACW,MAAD,CAAf,IACCR,cAAc,CAACQ,MAAD,CAAd,KACEA,MAAM,CAACmC,IAAP,KAAgB,QAAhB,IAA4BnC,MAAM,CAACmC,IAAP,KAAgB,OAD9C,CAFI,EAIL;IACA,IAAIC,IAAI,GAAGpC,MAAM,CAACoC,IAAlB;IAEA,IAAIA,IAAI,IAAI,IAAZ,EAAkB;MAChB,MAAM,IAAIC,KAAJ,CAAN;IAGD,CAPD;;IAWAV,MAAM,GACJD,OAAO,CAACC,MAAR,IACA3B,MAAM,CAACgC,YAAP,CAAoB,YAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,QAAlB,CAFA,IAGAhD,aAJF;IAKA4C,MAAM,GACJF,OAAO,CAACE,MAAR,IACA5B,MAAM,CAACgC,YAAP,CAAoB,YAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,QAAlB,CAFA,IAGAP,aAJF;IAKAI,OAAO,GACLH,OAAO,CAACG,OAAR,IACA7B,MAAM,CAACgC,YAAP,CAAoB,aAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,SAAlB,CAFA,IAGA/C,cAJF;IAMA6C,QAAQ,GAAG,IAAIG,QAAJ,CAAaG,IAAb,CAAX,CA3BA;IA8BA;;IACA,IAAIpC,MAAM,CAACkC,IAAX,EAAiB;MACfJ,QAAQ,CAACP,MAAT,CAAgBvB,MAAM,CAACkC,IAAvB,EAA6BlC,MAAM,CAACY,KAApC;IACD;EACF,CAtCM,MAsCA,IAAI1B,aAAa,CAACc,MAAD,CAAjB,EAA2B;IAChC,MAAM,IAAIqC,KAAJ,CACJ,2FADI,CAAN;EAID,CALM,MAKA;IACLV,MAAM,GAAGD,OAAO,CAACC,MAAR,IAAkB3C,aAA3B;IACA4C,MAAM,GAAGF,OAAO,CAACE,MAAR,IAAkBH,aAA3B;IACAI,OAAO,GAAGH,OAAO,CAACG,OAAR,IAAmB5C,cAA7B;IAEA,IAAIe,MAAM,YAAYiC,QAAtB,EAAgC;MAC9BH,QAAQ,GAAG9B,MAAX;IACD,CAFD,MAEO;MACL8B,QAAQ,GAAG,IAAIG,QAAJ,EAAX;MAEA,IAAIjC,MAAM,YAAYI,eAAtB,EAAuC;QACrC,KAAK,IAAI,CAAC8B,IAAD,EAAOtB,KAAP,CAAT,IAA0BZ,MAA1B,EAAkC;UAChC8B,QAAQ,CAACP,MAAT,CAAgBW,IAAhB,EAAsBtB,KAAtB;QACD;MACF,CAJD,MAIO,IAAIZ,MAAM,IAAI,IAAd,EAAoB;QACzB,KAAK,IAAIkC,IAAT,IAAiB3B,MAAM,CAACC,IAAP,CAAYR,MAAZ,CAAjB,EAAsC;UACpC8B,QAAQ,CAACP,MAAT,CAAgBW,IAAhB,EAAsBlC,MAAM,CAACkC,IAAD,CAA5B;QACD;MACF;IACF;EACF;EAED,IAAI;IAAEI,QAAF;IAAYC;GAAS,GAAAC,MAAM,CAACC,QAAhC;EACA,IAAIC,GAAG,GAAG,IAAIC,GAAJ,CAAQf,MAAR,EAAmBU,QAAnB,GAAgC,OAAAC,IAAhC,CAAV;EAEA,OAAO;IAAEG,GAAF;IAAOf,MAAM,EAAEA,MAAM,CAACrC,WAAP,EAAf;IAAqCuC,OAArC;IAA8CC;GAArD;AACD;;;;AC5DD;AACA;;AAEgB,SAAAc,oBACdC,MADc,EAEdC,IAFc,EAMb;EAED,OAAOC,YAAY,CAAC;IAClBC,QAAQ,EAAEF,IAAF,IAAE,gBAAAA,IAAI,CAAEE,QADE;IAElBC,OAAO,EAAEC,oBAAoB,CAAC;MAAEV,MAAM,EAAEM,IAAF,IAAE,gBAAAA,IAAI,CAAEN;IAAhB,CAAD,CAFX;IAGlBW,aAAa,EAAE,CAAAL,IAAI,IAAJ,gBAAAA,IAAI,CAAEK,aAAN,KAAuBC,kBAAkB,EAHtC;IAIlBP,MAAM,EAAEQ,gCAAyB,CAACR,MAAD;GAJhB,CAAZ,CAKJS,UALI,EAAP;AAMD;AAEe,SAAAC,iBACdV,MADc,EAEdC,IAFc,EAMb;EAED,OAAOC,YAAY,CAAC;IAClBC,QAAQ,EAAEF,IAAF,IAAE,gBAAAA,IAAI,CAAEE,QADE;IAElBC,OAAO,EAAEO,iBAAiB,CAAC;MAAEhB,MAAM,EAAEM,IAAF,IAAE,gBAAAA,IAAI,CAAEN;IAAhB,CAAD,CAFR;IAGlBW,aAAa,EAAE,CAAAL,IAAI,IAAJ,gBAAAA,IAAI,CAAEK,aAAN,KAAuBC,kBAAkB,EAHtC;IAIlBP,MAAM,EAAEQ,gCAAyB,CAACR,MAAD;GAJhB,CAAZ,CAKJS,UALI,EAAP;AAMD;AAED,SAASF,kBAATA,CAAA,EAA2B;EAAA,IAAAK,OAAA;EACzB,IAAIC,KAAK,IAAAD,OAAA,GAAGjB,MAAH,qBAAGiB,OAAA,CAAQE,2BAApB;EACA,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAnB,EAA2B;IACzBF,KAAK,GAAAG,QAAA,KACAH,KADA;MAEHE,MAAM,EAAEE,iBAAiB,CAACJ,KAAK,CAACE,MAAP;KAF3B;EAID;EACD,OAAOF,KAAP;AACD;AAED,SAASI,iBAATA,CACEF,MADF,EACwC;EAEtC,IAAI,CAACA,MAAL,EAAa,OAAO,IAAP;EACb,IAAIG,OAAO,GAAGxD,MAAM,CAACwD,OAAP,CAAeH,MAAf,CAAd;EACA,IAAII,UAAU,GAAmC,EAAjD;EACA,KAAK,IAAI,CAACrD,GAAD,EAAMsD,GAAN,CAAT,IAAuBF,OAAvB,EAAgC;IAC9B;IACA;IACA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAJ,KAAe,oBAA1B,EAAgD;MAC9CF,UAAU,CAACrD,GAAD,CAAV,GAAkB,IAAIwD,aAAJ,CAChBF,GAAG,CAACG,MADY,EAEhBH,GAAG,CAACI,UAFY,EAGhBJ,GAAG,CAACK,IAHY,EAIhBL,GAAG,CAACM,QAAJ,KAAiB,IAJD,CAAlB;KADF,MAOO,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAJ,KAAe,OAA1B,EAAmC;MACxC,IAAIM,KAAK,GAAG,IAAInC,KAAJ,CAAU4B,GAAG,CAACQ,OAAd,CAAZ,CADwC;MAGxC;;MACAD,KAAK,CAACE,KAAN,GAAc,EAAd;MACAV,UAAU,CAACrD,GAAD,CAAV,GAAkB6D,KAAlB;IACD,CANM,MAMA;MACLR,UAAU,CAACrD,GAAD,CAAV,GAAkBsD,GAAlB;IACD;EACF;EACD,OAAOD,UAAP;AACD;AAcD;;AAEG;;AACG,SAAUW,aAAVA,CAIeC,IAAA;EAAA,IAJS;IAC5B5B,QAD4B;IAE5B6B,QAF4B;IAG5BrC;GACmB,GAAAoC,IAAA;EACnB,IAAIE,UAAU,GAAGC,KAAK,CAACC,MAAN,EAAjB;EACA,IAAIF,UAAU,CAACG,OAAX,IAAsB,IAA1B,EAAgC;IAC9BH,UAAU,CAACG,OAAX,GAAqB/B,oBAAoB,CAAC;MAAEV,MAAF;MAAU0C,QAAQ,EAAE;IAApB,CAAD,CAAzC;EACD;EAED,IAAIjC,OAAO,GAAG6B,UAAU,CAACG,OAAzB;EACA,IAAI,CAACvB,KAAD,EAAQyB,QAAR,IAAoBJ,KAAK,CAACK,QAAN,CAAe;IACrCxD,MAAM,EAAEqB,OAAO,CAACrB,MADqB;IAErCa,QAAQ,EAAEQ,OAAO,CAACR;EAFmB,CAAf,CAAxB;EAKAsC,KAAK,CAACM,eAAN,CAAsB,MAAMpC,OAAO,CAACqC,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAClC,OAAD,CAAtD;EAEA,oBACE8B,KAAA,CAAAQ,aAAA,CAACC,MAAD,EAAO;IACLxC,QAAQ,EAAEA,QADL;IAEL6B,QAAQ,EAAEA,QAFL;IAGLpC,QAAQ,EAAEiB,KAAK,CAACjB,QAHX;IAILgD,cAAc,EAAE/B,KAAK,CAAC9B,MAJjB;IAKL8D,SAAS,EAAEzC;EALN,CAAP,CADF;AASD;AAQD;;;AAGG;;AACG,SAAU0C,UAAVA,CAAoEC,KAAA;EAAA,IAA/C;IAAE5C,QAAF;IAAY6B,QAAZ;IAAsBrC;GAAyB,GAAAoD,KAAA;EACxE,IAAId,UAAU,GAAGC,KAAK,CAACC,MAAN,EAAjB;EACA,IAAIF,UAAU,CAACG,OAAX,IAAsB,IAA1B,EAAgC;IAC9BH,UAAU,CAACG,OAAX,GAAqBzB,iBAAiB,CAAC;MAAEhB,MAAF;MAAU0C,QAAQ,EAAE;IAApB,CAAD,CAAtC;EACD;EAED,IAAIjC,OAAO,GAAG6B,UAAU,CAACG,OAAzB;EACA,IAAI,CAACvB,KAAD,EAAQyB,QAAR,IAAoBJ,KAAK,CAACK,QAAN,CAAe;IACrCxD,MAAM,EAAEqB,OAAO,CAACrB,MADqB;IAErCa,QAAQ,EAAEQ,OAAO,CAACR;EAFmB,CAAf,CAAxB;EAKAsC,KAAK,CAACM,eAAN,CAAsB,MAAMpC,OAAO,CAACqC,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAClC,OAAD,CAAtD;EAEA,oBACE8B,KAAA,CAAAQ,aAAA,CAACC,MAAD,EAAO;IACLxC,QAAQ,EAAEA,QADL;IAEL6B,QAAQ,EAAEA,QAFL;IAGLpC,QAAQ,EAAEiB,KAAK,CAACjB,QAHX;IAILgD,cAAc,EAAE/B,KAAK,CAAC9B,MAJjB;IAKL8D,SAAS,EAAEzC;EALN,CAAP,CADF;AASD;AAQD;;;;;AAKG;;AACH,SAAS4C,aAATA,CAA0EC,KAAA;EAAA,IAAnD;IAAE9C,QAAF;IAAY6B,QAAZ;IAAsB5B;GAA6B,GAAA6C,KAAA;EACxE,MAAM,CAACpC,KAAD,EAAQyB,QAAR,IAAoBJ,KAAK,CAACK,QAAN,CAAe;IACvCxD,MAAM,EAAEqB,OAAO,CAACrB,MADuB;IAEvCa,QAAQ,EAAEQ,OAAO,CAACR;EAFqB,CAAf,CAA1B;EAKAsC,KAAK,CAACM,eAAN,CAAsB,MAAMpC,OAAO,CAACqC,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAClC,OAAD,CAAtD;EAEA,oBACE8B,KAAA,CAAAQ,aAAA,CAACC,MAAD,EAAO;IACLxC,QAAQ,EAAEA,QADL;IAEL6B,QAAQ,EAAEA,QAFL;IAGLpC,QAAQ,EAAEiB,KAAK,CAACjB,QAHX;IAILgD,cAAc,EAAE/B,KAAK,CAAC9B,MAJjB;IAKL8D,SAAS,EAAEzC;EALN,CAAP,CADF;AASD;AAED,IAAa8C,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXJ,aAAa,CAACK,WAAd,GAA4B,wBAA5B;AACD;AAcD,MAAMC,SAAS,GACb,OAAO3D,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAAC4D,QAAd,KAA2B,WAD3B,IAEA,OAAO5D,MAAM,CAAC4D,QAAP,CAAgBb,aAAvB,KAAyC,WAH3C;AAKA;;AAEG;;AACI,MAAMc,IAAI,gBAAGtB,KAAK,CAACuB,UAAN,CAClB,SAASC,WAATA,CAYEC,KAAA,EAAAC,GAZF,EAYK;EAAA,IAXH;MACEC,OADF;MAEEC,QAFF;MAGEC,cAHF;MAIEC,OAJF;MAKEnD,KALF;MAME1D,MANF;MAOE8G,EAPF;MAQEC;KAGC,GAAAP,KAAA;IAFEQ,IAEF,GAAAC,6BAAA,CAAAT,KAAA,EAAAU,SAAA;;EAEH;EACA,IAAIzE,QAAQ,GAAG,OAAOqE,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BK,UAAU,CAACL,EAAD,CAAvD;EACA,IAAIM,UAAU,GACZ,gBAAiB,CAAAC,IAAjB,CAAsB5E,QAAtB,KAAmCA,QAAQ,CAAC6E,UAAT,CAAoB,IAApB,CADrC,CAJG;;EAQH,IAAIC,kBAAkB,GAAG9E,QAAzB;EACA,IAAI+E,UAAU,GAAG,KAAjB;EACA,IAAIrB,SAAS,IAAIiB,UAAjB,EAA6B;IAC3B,IAAIK,UAAU,GAAG,IAAI9E,GAAJ,CAAQH,MAAM,CAACC,QAAP,CAAgBiF,IAAxB,CAAjB;IACA,IAAIC,SAAS,GAAGlF,QAAQ,CAAC6E,UAAT,CAAoB,IAApB,CACZ,OAAI3E,GAAJ,CAAQ8E,UAAU,CAACnF,QAAX,GAAsBG,QAA9B,CADY,GAEZ,IAAIE,GAAJ,CAAQF,QAAR,CAFJ;IAGA,IAAIkF,SAAS,CAACC,MAAV,KAAqBH,UAAU,CAACG,MAApC,EAA4C;MAC1C;MACAL,kBAAkB,GAChBI,SAAS,CAACE,QAAV,GAAqBF,SAAS,CAACG,MAA/B,GAAwCH,SAAS,CAACI,IADpD;IAED,CAJD,MAIO;MACLP,UAAU,GAAG,IAAb;IACD;EACF,CAtBE;;EAyBH,IAAIE,IAAI,GAAGM,OAAO,CAACT,kBAAD,EAAqB;IAAEZ;EAAF,CAArB,CAAlB;EAEA,IAAIsB,eAAe,GAAGC,mBAAmB,CAACX,kBAAD,EAAqB;IAC5DV,OAD4D;IAE5DnD,KAF4D;IAG5D1D,MAH4D;IAI5D+G,kBAJ4D;IAK5DJ;EAL4D,CAArB,CAAzC;EAOA,SAASwB,WAATA,CACEzI,KADF,EACwD;IAEtD,IAAIgH,OAAJ,EAAaA,OAAO,CAAChH,KAAD,CAAP;IACb,IAAI,CAACA,KAAK,CAAC0I,gBAAX,EAA6B;MAC3BH,eAAe,CAACvI,KAAD,CAAf;IACD;EACF;EAED;IACE;IACAqF,KAAA,CAAAQ,aAAA,MAAA1B,QAAA,KACMmD,IADN;MAEEU,IAAI,EAAEN,UAAU,GAAG3E,QAAH,GAAciF,IAFhC;MAGEhB,OAAO,EAAEc,UAAU,IAAIZ,cAAd,GAA+BF,OAA/B,GAAyCyB,WAHpD;MAIE1B,GAAG,EAAEA,GAJP;MAKEzG,MAAM,EAAEA;IALV;EAAA;AAQH,CAlEiB;AAqEpB,IAAa+F,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXI,IAAI,CAACH,WAAL,GAAmB,MAAnB;AACD;AAuBD;;AAEG;;AACI,MAAMmC,OAAO,gBAAGtD,KAAK,CAACuB,UAAN,CACrB,SAASgC,cAATA,CAWEC,KAAA,EAAA9B,GAXF,EAWK;EAAA,IAVH;MACE,cAAgB,EAAA+B,eAAe,GAAG,MADpC;MAEEC,aAAa,GAAG,KAFlB;MAGEC,SAAS,EAAEC,aAAa,GAAG,EAH7B;MAIEC,GAAG,GAAG,KAJR;MAKEC,KAAK,EAAEC,SALT;MAMEhC,EANF;MAOEjC;KAGC,GAAA0D,KAAA;IAFEvB,IAEF,GAAAC,6BAAA,CAAAsB,KAAA,EAAAQ,UAAA;EAEH,IAAIC,IAAI,GAAGC,eAAe,CAACnC,EAAD,EAAK;IAAEH,QAAQ,EAAEK,IAAI,CAACL;EAAjB,CAAL,CAA1B;EACA,IAAIlE,QAAQ,GAAGyG,WAAW,EAA1B;EACA,IAAIC,WAAW,GAAGpE,KAAK,CAACqE,UAAN,CAAiBC,6BAAjB,CAAlB;EACA,IAAI;IAAE3D;EAAF,IAAgBX,KAAK,CAACqE,UAAN,CAAiBE,wBAAjB,CAApB;EAEA,IAAIC,UAAU,GAAG7D,SAAS,CAAC8D,cAAV,GACb9D,SAAS,CAAC8D,cAAV,CAAyBR,IAAzB,CAA+B,CAAAnB,QADlB,GAEbmB,IAAI,CAACnB,QAFT;EAGA,IAAI4B,gBAAgB,GAAGhH,QAAQ,CAACoF,QAAhC;EACA,IAAI6B,oBAAoB,GACtBP,WAAW,IAAIA,WAAW,CAACQ,UAA3B,IAAyCR,WAAW,CAACQ,UAAZ,CAAuBlH,QAAhE,GACI0G,WAAW,CAACQ,UAAZ,CAAuBlH,QAAvB,CAAgCoF,QADpC,GAEI,IAHN;EAKA,IAAI,CAACY,aAAL,EAAoB;IAClBgB,gBAAgB,GAAGA,gBAAgB,CAACnK,WAAjB,EAAnB;IACAoK,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACpK,WAArB,EADuC,GAEvC,IAFJ;IAGAiK,UAAU,GAAGA,UAAU,CAACjK,WAAX,EAAb;EACD;EAED,IAAIsK,QAAQ,GACVH,gBAAgB,KAAKF,UAArB,IACC,CAACX,GAAD,IACCa,gBAAgB,CAACnC,UAAjB,CAA4BiC,UAA5B,CADD,IAECE,gBAAgB,CAACI,MAAjB,CAAwBN,UAAU,CAACO,MAAnC,MAA+C,GAJnD;EAMA,IAAIC,SAAS,GACXL,oBAAoB,IAAI,IAAxB,KACCA,oBAAoB,KAAKH,UAAzB,IACE,CAACX,GAAD,IACCc,oBAAoB,CAACpC,UAArB,CAAgCiC,UAAhC,CADD,IAECG,oBAAoB,CAACG,MAArB,CAA4BN,UAAU,CAACO,MAAvC,MAAmD,GAJvD,CADF;EAOA,IAAIE,WAAW,GAAGJ,QAAQ,GAAGpB,eAAH,GAAqByB,SAA/C;EAEA,IAAIvB,SAAJ;EACA,IAAI,OAAOC,aAAP,KAAyB,UAA7B,EAAyC;IACvCD,SAAS,GAAGC,aAAa,CAAC;MAAEiB,QAAF;MAAYG;IAAZ,CAAD,CAAzB;EACD,CAFD,MAEO;IACL;IACA;IACA;IACA;IACA;IACArB,SAAS,GAAG,CACVC,aADU,EAEViB,QAAQ,GAAG,QAAH,GAAc,IAFZ,EAGVG,SAAS,GAAG,SAAH,GAAe,IAHd,EAKTG,MALS,CAKFC,OALE,CAMT,CAAAC,IANS,CAMJ,GANI,CAAZ;EAOD;EAED,IAAIvB,KAAK,GACP,OAAOC,SAAP,KAAqB,UAArB,GACIA,SAAS,CAAC;IAAEc,QAAF;IAAYG;GAAb,CADb,GAEIjB,SAHN;EAKA,oBACE/D,KAAC,CAAAQ,aAAD,CAACc,IAAD,EAAAxC,QAAA,KACMmD,IADN;IAEgB,gBAAAgD,WAFhB;IAGEtB,SAAS,EAAEA,SAHb;IAIEjC,GAAG,EAAEA,GAJP;IAKEoC,KAAK,EAAEA,KALT;IAME/B,EAAE,EAAEA;EANN,IAQG,OAAOjC,QAAP,KAAoB,UAApB,GACGA,QAAQ,CAAC;IAAE+E,QAAF;IAAYG;GAAb,CADX,GAEGlF,QAVN,CADF;AAcD,CAxFoB;AA2FvB,IAAakB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXoC,OAAO,CAACnC,WAAR,GAAsB,SAAtB;AACD;AA8CD;;;;;AAKG;;AACI,MAAMmE,IAAI,gBAAGtF,KAAK,CAACuB,UAAN,CAClB,CAACgE,KAAD,EAAQ7D,GAAR,KAAe;EACb,oBAAO1B,KAAA,CAAAQ,aAAA,CAACgF,QAAD,EAAA1G,QAAA,KAAcyG,KAAd;IAAqB7D,GAAG,EAAEA;GAAjC;AACD,CAHiB;AAMpB,IAAaV,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXoE,IAAI,CAACnE,WAAL,GAAmB,MAAnB;AACD;AAeD,MAAMqE,QAAQ,gBAAGxF,KAAK,CAACuB,UAAN,CACf,CAAAkE,KAAA,EAaEC,YAbF,KAcI;EAAA,IAbF;MACE7D,cADF;MAEEC,OAFF;MAGElF,MAAM,GAAG3C,aAHX;MAIE4C,MAJF;MAKE8I,QALF;MAMEC,UANF;MAOEC,OAPF;MAQEjE,QARF;MASEI;KAIA,GAAAyD,KAAA;IAHGF,KAGH,GAAArD,6BAAA,CAAAuD,KAAA,EAAAK,UAAA;EACF,IAAIC,MAAM,GAAGC,aAAa,CAACJ,UAAD,EAAaC,OAAb,CAA1B;EACA,IAAII,UAAU,GACZrJ,MAAM,CAACrC,WAAP,OAAyB,KAAzB,GAAiC,KAAjC,GAAyC,MAD3C;EAEA,IAAI2L,UAAU,GAAGC,aAAa,CAACtJ,MAAD,EAAS;IAAE+E;EAAF,CAAT,CAA9B;EACA,IAAIwE,aAAa,GAA6CzL,KAAD,IAAU;IACrEgL,QAAQ,IAAIA,QAAQ,CAAChL,KAAD,CAApB;IACA,IAAIA,KAAK,CAAC0I,gBAAV,EAA4B;IAC5B1I,KAAK,CAAC0L,cAAN;IAEA,IAAIC,SAAS,GAAI3L,KAAoC,CAAC4L,WAArC,CACdD,SADH;IAGA,IAAIE,YAAY,GACb,CAAAF,SAAS,IAAT,gBAAAA,SAAS,CAAErJ,YAAX,CAAwB,YAAxB,MACDL,MAFF;IAIAmJ,MAAM,CAACO,SAAS,IAAI3L,KAAK,CAAC8L,aAApB,EAAmC;MACvC7J,MAAM,EAAE4J,YAD+B;MAEvC1E,OAFuC;MAGvCF,QAHuC;MAIvCI;IAJuC,CAAnC,CAAN;GAZF;EAoBA,oBACEhC,KAAA,CAAAQ,aAAA,SAAA1B,QAAA;IACE4C,GAAG,EAAEgE,YADP;IAEE9I,MAAM,EAAEqJ,UAFV;IAGEpJ,MAAM,EAAEqJ,UAHV;IAIEP,QAAQ,EAAE9D,cAAc,GAAG8D,QAAH,GAAcS;EAJxC,GAKMb,KALN,CADF;AASD,CAjDc,CAAjB;AAoDA,IAAavE,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXsE,QAAQ,CAACrE,WAAT,GAAuB,UAAvB;AACD;AAOD;;;AAGG;;SACauF,kBAGSC,KAAA;EAAA,IAHS;IAChCC,MADgC;IAEhCC;GACuB,GAAAF,KAAA;EACvBG,oBAAoB,CAAC;IAAEF,MAAF;IAAUC;EAAV,CAAD,CAApB;EACA,OAAO,IAAP;AACD;AAED,IAAa7F,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXwF,iBAAiB,CAACvF,WAAlB,GAAgC,mBAAhC;AACD;AAGD;AACA;AACA;;AAEA,IAAK4F,cAAL;AAAA,WAAKA,cAAL,EAAmB;EACjBA,cAAA;EACAA,cAAA;EACAA,cAAA;AACD,CAJD,EAAKA,cAAc,KAAdA,cAAc,GAIlB,EAJkB,CAAnB;AAMA,IAAKC,mBAAL;AAAA,WAAKA,mBAAL,EAAwB;EACtBA,mBAAA;EACAA,mBAAA;AACD,CAHD,EAAKA,mBAAmB,KAAnBA,mBAAmB,GAGvB,EAHuB,CAAxB;AAKA,SAASC,yBAATA,CACEC,QADF,EACgD;EAE9C,OAAUA,QAAV;AACD;AAED,SAASC,oBAATA,CAA8BD,QAA9B,EAAsD;EACpD,IAAIE,GAAG,GAAGpH,KAAK,CAACqE,UAAN,CAAiBgD,wBAAjB,CAAV;EACA,CAAUD,GAAV,GAAApG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,CAAM,OAAAL,yBAAyB,CAACC,QAAD,CAA/B,CAAT,GAAAI,SAAS,CAAT;EACA,OAAOF,GAAP;AACD;AAED,SAASG,kBAATA,CAA4BL,QAA5B,EAAyD;EACvD,IAAIvI,KAAK,GAAGqB,KAAK,CAACqE,UAAN,CAAiBC,6BAAjB,CAAZ;EACA,CAAU3F,KAAV,GAAAqC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,CAAQ,OAAAL,yBAAyB,CAACC,QAAD,CAAjC,CAAT,GAAAI,SAAS,CAAT;EACA,OAAO3I,KAAP;AACD;AAED;;;;AAIG;;SACawE,oBACdpB,EAAA,EAaMyF,KAAA;EAAA,IAZN;IACEvM,MADF;IAEE6G,OAAO,EAAE2F,WAFX;IAGE9I,KAHF;IAIEqD,kBAJF;IAKEJ;EALF,CAYM,GAAA4F,KAAA,cAAF,EAAE,GAAAA,KAAA;EAEN,IAAIE,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIjK,QAAQ,GAAGyG,WAAW,EAA1B;EACA,IAAIF,IAAI,GAAGC,eAAe,CAACnC,EAAD,EAAK;IAAEH;EAAF,CAAL,CAA1B;EAEA,OAAO5B,KAAK,CAAC4H,WAAN,CACJjN,KAAD,IAA2C;IACzC,IAAIK,sBAAsB,CAACL,KAAD,EAAQM,MAAR,CAA1B,EAA2C;MACzCN,KAAK,CAAC0L,cAAN,GADyC;MAIzC;;MACA,IAAIvE,OAAO,GACT2F,WAAW,KAAKvC,SAAhB,GACIuC,WADJ,GAEIrF,UAAU,CAAC1E,QAAD,CAAV,KAAyB0E,UAAU,CAAC6B,IAAD,CAHzC;MAKAyD,QAAQ,CAAC3F,EAAD,EAAK;QAAED,OAAF;QAAWnD,KAAX;QAAkBqD,kBAAlB;QAAsCJ;MAAtC,CAAL,CAAR;IACD;GAbE,EAeL,CACElE,QADF,EAEEgK,QAFF,EAGEzD,IAHF,EAIEwD,WAJF,EAKE9I,KALF,EAME1D,MANF,EAOE8G,EAPF,EAQEC,kBARF,EASEJ,QATF,CAfK,CAAP;AA2BD;AAED;;;AAGG;;AACG,SAAUiG,eAAVA,CACJC,WADI,EAC6B;EAEjC9G,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAA6G,OAAO,CACL,OAAO1M,eAAP,KAA2B,WADtB,EAEL,meAFK,CAAP;EAYA,IAAI2M,sBAAsB,GAAGhI,KAAK,CAACC,MAAN,CAAa9E,kBAAkB,CAAC2M,WAAD,CAA/B,CAA7B;EACA,IAAIG,qBAAqB,GAAGjI,KAAK,CAACC,MAAN,CAAa,KAAb,CAA5B;EAEA,IAAIvC,QAAQ,GAAGyG,WAAW,EAA1B;EACA,IAAI/H,YAAY,GAAG4D,KAAK,CAACkI,OAAN,CACjB;EAAA;EAEE;EACA;EACAjM,0BAA0B,CACxByB,QAAQ,CAACqF,MADe,EAExBkF,qBAAqB,CAAC/H,OAAtB,GAAgC,IAAhC,GAAuC8H,sBAAsB,CAAC9H,OAFtC,CALX,EASjB,CAACxC,QAAQ,CAACqF,MAAV,CATiB,CAAnB;EAYA,IAAI2E,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIQ,eAAe,GAAGnI,KAAK,CAAC4H,WAAN,CACpB,CAACQ,QAAD,EAAWC,eAAX,KAA8B;IAC5B,MAAMC,eAAe,GAAGnN,kBAAkB,CACxC,OAAOiN,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAAChM,YAAD,CAAzC,GAA0DgM,QADlB,CAA1C;IAGAH,qBAAqB,CAAC/H,OAAtB,GAAgC,IAAhC;IACAwH,QAAQ,CAAC,MAAMY,eAAP,EAAwBD,eAAxB,CAAR;EACD,CAPmB,EAQpB,CAACX,QAAD,EAAWtL,YAAX,CARoB,CAAtB;EAWA,OAAO,CAACA,YAAD,EAAe+L,eAAf,CAAP;AACD;AAyCD;;;AAGG;;SACaI,UAAA,EAAS;EACvB,OAAOvC,aAAa,EAApB;AACD;AAED,SAASA,aAATA,CAAuBJ,UAAvB,EAA4CC,OAA5C,EAA4D;EAC1D,IAAI;IAAE2C;EAAF,IAAarB,oBAAoB,CAACJ,cAAc,CAAC0B,aAAhB,CAArC;EACA,IAAI/L,aAAa,GAAGyJ,aAAa,EAAjC;EAEA,OAAOnG,KAAK,CAAC4H,WAAN,CACL,UAAC3M,MAAD,EAAS0B,OAAT,EAAyB;IAAA,IAAhBA,OAAgB;MAAhBA,OAAgB,GAAN,EAAM;IAAA;IACvB,IAAI,OAAO0E,QAAP,KAAoB,WAAxB,EAAqC;MACnC,MAAM,IAAI/D,KAAJ,CACJ,sDACE,8DAFE,CAAN;IAID;IAED,IAAI;MAAEV,MAAF;MAAUE,OAAV;MAAmBC,QAAnB;MAA6BY;IAA7B,IAAqClB,qBAAqB,CAC5DxB,MAD4D,EAE5DyB,aAF4D,EAG5DC,OAH4D,CAA9D;IAMA,IAAIgG,IAAI,GAAGhF,GAAG,CAACmF,QAAJ,GAAenF,GAAG,CAACoF,MAA9B;IACA,IAAIhF,IAAI,GAAG;MACT+D,OAAO,EAAEnF,OAAO,CAACmF,OADR;MAETE,kBAAkB,EAAErF,OAAO,CAACqF,kBAFnB;MAGTjF,QAHS;MAITkJ,UAAU,EAAErJ,MAJH;MAKT8L,WAAW,EAAE5L;KALf;IAOA,IAAI8I,UAAJ,EAAgB;MACd,EAAUC,OAAO,IAAI,IAArB,IAAA7E,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,QAAkB,uCAAlB,CAAT,GAAAA,SAAS,CAAT;MACAkB,MAAM,CAACG,KAAP,CAAa/C,UAAb,EAAyBC,OAAzB,EAAkClD,IAAlC,EAAwC5E,IAAxC;IACD,CAHD,MAGO;MACLyK,MAAM,CAACd,QAAP,CAAgB/E,IAAhB,EAAsB5E,IAAtB;IACD;GA5BE,EA8BL,CAACrB,aAAD,EAAgB8L,MAAhB,EAAwB5C,UAAxB,EAAoCC,OAApC,CA9BK,CAAP;AAgCD;AAEK,SAAUM,aAAVA,CACJtJ,MADI,EAEiD+L,MAAA;EAAA,IAArD;IAAEhH;EAAF,CAAqD,GAAAgH,MAAA,cAAF,EAAE,GAAAA,MAAA;EAErD,IAAI;IAAE3K;EAAF,IAAe+B,KAAK,CAACqE,UAAN,CAAiBE,wBAAjB,CAAnB;EACA,IAAIsE,YAAY,GAAG7I,KAAK,CAACqE,UAAN,CAAiByE,mBAAjB,CAAnB;EACA,CAAUD,YAAV,GAAA7H,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,QAAe,kDAAf,CAAT,GAAAA,SAAS,CAAT;EAEA,IAAI,CAACyB,KAAD,CAAU,GAAAF,YAAY,CAACG,OAAb,CAAqBC,KAArB,CAA2B,CAAC,CAA5B,CAAd,CANqD;EAQrD;;EACA,IAAIhF,IAAI,GAAAnF,QAAA,KAAQoF,eAAe,CAACrH,MAAM,GAAGA,MAAH,GAAY,GAAnB,EAAwB;IAAE+E;GAA1B,CAAvB,CAAR,CATqD;EAYrD;EACA;EACA;EACA;;EACA,IAAIlE,QAAQ,GAAGyG,WAAW,EAA1B;EACA,IAAItH,MAAM,IAAI,IAAd,EAAoB;IAClB;IACA;IACA;IACAoH,IAAI,CAAClB,MAAL,GAAcrF,QAAQ,CAACqF,MAAvB;IACAkB,IAAI,CAACjB,IAAL,GAAYtF,QAAQ,CAACsF,IAArB,CALkB;IAQlB;IACA;;IACA,IAAI+F,KAAK,CAACG,KAAN,CAAYC,KAAhB,EAAuB;MACrB,IAAIC,MAAM,GAAG,IAAI/N,eAAJ,CAAoB4I,IAAI,CAAClB,MAAzB,CAAb;MACAqG,MAAM,CAACC,MAAP,CAAc,OAAd;MACApF,IAAI,CAAClB,MAAL,GAAcqG,MAAM,CAACE,QAAP,EAAwB,SAAAF,MAAM,CAACE,QAAP,EAAxB,GAA8C,EAA5D;IACD;EACF;EAED,IAAI,CAAC,CAACzM,MAAD,IAAWA,MAAM,KAAK,GAAvB,KAA+BkM,KAAK,CAACG,KAAN,CAAYC,KAA/C,EAAsD;IACpDlF,IAAI,CAAClB,MAAL,GAAckB,IAAI,CAAClB,MAAL,GACVkB,IAAI,CAAClB,MAAL,CAAYjB,OAAZ,CAAoB,KAApB,EAA2B,SAA3B,CADU,GAEV,QAFJ;EAGD,CAtCoD;EAyCrD;EACA;EACA;;EACA,IAAI7D,QAAQ,KAAK,GAAjB,EAAsB;IACpBgG,IAAI,CAACnB,QAAL,GACEmB,IAAI,CAACnB,QAAL,KAAkB,GAAlB,GAAwB7E,QAAxB,GAAmCsL,SAAS,CAAC,CAACtL,QAAD,EAAWgG,IAAI,CAACnB,QAAhB,CAAD,CAD9C;EAED;EAED,OAAOV,UAAU,CAAC6B,IAAD,CAAjB;AACD;AAED,SAASuF,iBAATA,CAA2B5D,UAA3B,EAA+CC,OAA/C,EAA8D;EAC5D,IAAI4D,WAAW,gBAAGzJ,KAAK,CAACuB,UAAN,CAChB,CAACgE,KAAD,EAAQ7D,GAAR,KAAe;IACb,oBACE1B,KAAC,CAAAQ,aAAD,CAACgF,QAAD,EAAA1G,QAAA,KACMyG,KADN;MAEE7D,GAAG,EAAEA,GAFP;MAGEkE,UAAU,EAAEA,UAHd;MAIEC,OAAO,EAAEA;KALb;EAQD,CAVe,CAAlB;EAYA,IAAa7E,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACXuI,WAAW,CAACtI,WAAZ,GAA0B,cAA1B;EACD;EACD,OAAOsI,WAAP;AACD;AAED,IAAIC,SAAS,GAAG,CAAhB;AAaA;;;AAGG;;SACaC,WAAA,EAAU;EAAA,IAAAC,cAAA;EACxB,IAAI;IAAEpB;EAAF,IAAarB,oBAAoB,CAACJ,cAAc,CAAC8C,UAAhB,CAArC;EAEA,IAAIX,KAAK,GAAGlJ,KAAK,CAACqE,UAAN,CAAiByE,mBAAjB,CAAZ;EACA,CAAUI,KAAV,GAAAlI,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,CAAT,0DAAAA,SAAS,CAAT;EAEA,IAAIzB,OAAO,GAAG,CAAA+D,cAAA,GAAAV,KAAK,CAACF,OAAN,CAAcE,KAAK,CAACF,OAAN,CAAcjE,MAAd,GAAuB,CAArC,CAAH,qBAAG6E,cAAyC,CAAAV,KAAzC,CAA+CY,EAA7D;EACA,EACEjE,OAAO,IAAI,IADb,IAAA7E,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,CAAT,+EAAAA,SAAS,CAAT;EAKA,IAAI,CAAC1B,UAAD,CAAe,GAAA5F,KAAK,CAACK,QAAN,CAAe,MAAM0J,MAAM,CAAC,EAAEL,SAAH,CAA3B,CAAnB;EACA,IAAI,CAACpE,IAAD,IAAStF,KAAK,CAACK,QAAN,CAAe,MAAK;IAC/B,CAAUwF,OAAV,GAAA7E,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,CAAT,oDAAAA,SAAS,CAAT;IACA,OAAOkC,iBAAiB,CAAC5D,UAAD,EAAaC,OAAb,CAAxB;EACD,CAHY,CAAb;EAIA,IAAI,CAACmE,IAAD,CAAS,GAAAhK,KAAK,CAACK,QAAN,CAAe,MAAOsC,IAAD,IAAiB;IACjD,CAAU6F,MAAV,GAAAxH,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,QAAS,wCAAT,CAAT,GAAAA,SAAS,CAAT;IACA,CAAUzB,OAAV,GAAA7E,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoG,SAAS,QAAU,yCAAV,CAAT,GAAAA,SAAS,CAAT;IACAkB,MAAM,CAACG,KAAP,CAAa/C,UAAb,EAAyBC,OAAzB,EAAkClD,IAAlC;EACD,CAJY,CAAb;EAKA,IAAIoD,MAAM,GAAGC,aAAa,CAACJ,UAAD,EAAaC,OAAb,CAA1B;EAEA,IAAIoE,OAAO,GAAGzB,MAAM,CAAC0B,UAAP,CAAyBtE,UAAzB,CAAd;EAEA,IAAIuE,qBAAqB,GAAGnK,KAAK,CAACkI,OAAN,CAC1B,MAAApJ,QAAA;IACEwG,IADF;IAEES,MAFF;IAGEiE;EAHF,GAIKC,OAJL,CAD0B,EAO1B,CAACA,OAAD,EAAU3E,IAAV,EAAgBS,MAAhB,EAAwBiE,IAAxB,CAP0B,CAA5B;EAUAhK,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB;IACA;IACA;IACA,OAAO,MAAK;MACV,IAAI,CAAC5B,MAAL,EAAa;QACX6B,OAAO,CAACC,IAAR;QACA;MACD;MACD9B,MAAM,CAAC+B,aAAP,CAAqB3E,UAArB;KALF;EAOD,CAXD,EAWG,CAAC4C,MAAD,EAAS5C,UAAT,CAXH;EAaA,OAAOuE,qBAAP;AACD;AAED;;;AAGG;;SACaK,YAAA,EAAW;EACzB,IAAI7L,KAAK,GAAG4I,kBAAkB,CAACP,mBAAmB,CAACyD,WAArB,CAA9B;EACA,OAAO,CAAC,GAAG9L,KAAK,CAAC+L,QAAN,CAAeC,MAAf,EAAJ,CAAP;AACD;AAED,MAAMC,8BAA8B,GAAG,+BAAvC;AACA,IAAIC,oBAAoB,GAA2B,EAAnD;AAEA;;AAEG;;AACH,SAAS/D,oBAATA,CAMMgE,MAAA;EAAA,IANwB;IAC5BlE,MAD4B;IAE5BC;EAF4B,CAMxB,GAAAiE,MAAA,cAAF,EAAE,GAAAA,MAAA;EACJ,IAAI;IAAEtC;EAAF,IAAarB,oBAAoB,CAACJ,cAAc,CAACgE,oBAAhB,CAArC;EACA,IAAI;IAAEC,qBAAF;IAAyBhJ;EAAzB,IAAgDuF,kBAAkB,CACpEP,mBAAmB,CAAC+D,oBADgD,CAAtE;EAGA,IAAIrN,QAAQ,GAAGyG,WAAW,EAA1B;EACA,IAAI6E,OAAO,GAAGiC,UAAU,EAAxB;EACA,IAAIrG,UAAU,GAAGsG,aAAa,EAA9B,CAPI;;EAUJlL,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB3M,MAAM,CAACS,OAAP,CAAeiN,iBAAf,GAAmC,QAAnC;IACA,OAAO,MAAK;MACV1N,MAAM,CAACS,OAAP,CAAeiN,iBAAf,GAAmC,MAAnC;KADF;GAFF,EAKG,EALH,EAVI;;EAkBJC,WAAW,CACTpL,KAAK,CAAC4H,WAAN,CAAkB,MAAK;IACrB,IAAIhD,UAAU,CAACjG,KAAX,KAAqB,MAAzB,EAAiC;MAC/B,IAAI/C,GAAG,GAAG,CAACgL,MAAM,GAAGA,MAAM,CAAClJ,QAAD,EAAWsL,OAAX,CAAT,GAA+B,IAAtC,KAA+CtL,QAAQ,CAAC9B,GAAlE;MACAiP,oBAAoB,CAACjP,GAAD,CAApB,GAA4B6B,MAAM,CAAC4N,OAAnC;IACD;IACDC,cAAc,CAACC,OAAf,CACE1E,UAAU,IAAI+D,8BADhB,EAEEY,IAAI,CAACC,SAAL,CAAeZ,oBAAf,CAFF;IAIApN,MAAM,CAACS,OAAP,CAAeiN,iBAAf,GAAmC,MAAnC;EACD,CAVD,EAUG,CAACtE,UAAD,EAAaD,MAAb,EAAqBhC,UAAU,CAACjG,KAAhC,EAAuCjB,QAAvC,EAAiDsL,OAAjD,CAVH,CADS,CAAX,CAlBI;;EAiCJ,IAAI,OAAO3H,QAAP,KAAoB,WAAxB,EAAqC;IACnC;IACArB,KAAK,CAACM,eAAN,CAAsB,MAAK;MACzB,IAAI;QACF,IAAIoL,gBAAgB,GAAGJ,cAAc,CAACK,OAAf,CACrB9E,UAAU,IAAI+D,8BADO,CAAvB;QAGA,IAAIc,gBAAJ,EAAsB;UACpBb,oBAAoB,GAAGW,IAAI,CAACI,KAAL,CAAWF,gBAAX,CAAvB;QACD;MACF,CAPD,CAOE,OAAOG,CAAP,EAAU;MAAA;IAGb,CAXD,EAWG,CAAChF,UAAD,CAXH,EAFmC;IAgBnC;;IACA7G,KAAK,CAACM,eAAN,CAAsB,MAAK;MACzB,IAAIwL,wBAAwB,GAAGtD,MAAH,IAAG,gBAAAA,MAAM,CAAEuD,uBAAR,CAC7BlB,oBAD6B,EAE7B,MAAMpN,MAAM,CAAC4N,OAFgB,EAG7BzE,MAH6B,CAA/B;MAKA,OAAO,MAAMkF,wBAAwB,IAAIA,wBAAwB,EAAjE;IACD,CAPD,EAOG,CAACtD,MAAD,EAAS5B,MAAT,CAPH,EAjBmC;IA2BnC;;IACA5G,KAAK,CAACM,eAAN,CAAsB,MAAK;MACzB;MACA,IAAI0K,qBAAqB,KAAK,KAA9B,EAAqC;QACnC;MACD,CAJwB;;MAOzB,IAAI,OAAOA,qBAAP,KAAiC,QAArC,EAA+C;QAC7CvN,MAAM,CAACuO,QAAP,CAAgB,CAAhB,EAAmBhB,qBAAnB;QACA;MACD,CAVwB;;MAazB,IAAItN,QAAQ,CAACsF,IAAb,EAAmB;QACjB,IAAIiJ,EAAE,GAAG5K,QAAQ,CAAC6K,cAAT,CAAwBxO,QAAQ,CAACsF,IAAT,CAAciG,KAAd,CAAoB,CAApB,CAAxB,CAAT;QACA,IAAIgD,EAAJ,EAAQ;UACNA,EAAE,CAACE,cAAH;UACA;QACD;MACF,CAnBwB;;MAsBzB,IAAInK,kBAAkB,KAAK,IAA3B,EAAiC;QAC/B;MACD,CAxBwB;;MA2BzBvE,MAAM,CAACuO,QAAP,CAAgB,CAAhB,EAAmB,CAAnB;IACD,CA5BD,EA4BG,CAACtO,QAAD,EAAWsN,qBAAX,EAAkChJ,kBAAlC,CA5BH;EA6BD;AACF;AAED;;;;;;;AAOG;;AACa,SAAAoK,gBACdC,QADc,EAEd1P,OAFc,EAEiB;EAE/B,IAAI;IAAE2P;GAAY,GAAA3P,OAAO,IAAI,EAA7B;EACAqD,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB,IAAIrM,IAAI,GAAGuO,OAAO,IAAI,IAAX,GAAkB;MAAEA;IAAF,CAAlB,GAAgCpH,SAA3C;IACAzH,MAAM,CAAC8O,gBAAP,CAAwB,cAAxB,EAAwCF,QAAxC,EAAkDtO,IAAlD;IACA,OAAO,MAAK;MACVN,MAAM,CAAC+O,mBAAP,CAA2B,cAA3B,EAA2CH,QAA3C,EAAqDtO,IAArD;KADF;EAGD,CAND,EAMG,CAACsO,QAAD,EAAWC,OAAX,CANH;AAOD;AAED;;;;;;;AAOG;;AACH,SAASlB,WAATA,CACEiB,QADF,EAEE1P,OAFF,EAEiC;EAE/B,IAAI;IAAE2P;GAAY,GAAA3P,OAAO,IAAI,EAA7B;EACAqD,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB,IAAIrM,IAAI,GAAGuO,OAAO,IAAI,IAAX,GAAkB;MAAEA;IAAF,CAAlB,GAAgCpH,SAA3C;IACAzH,MAAM,CAAC8O,gBAAP,CAAwB,UAAxB,EAAoCF,QAApC,EAA8CtO,IAA9C;IACA,OAAO,MAAK;MACVN,MAAM,CAAC+O,mBAAP,CAA2B,UAA3B,EAAuCH,QAAvC,EAAiDtO,IAAjD;KADF;EAGD,CAND,EAMG,CAACsO,QAAD,EAAWC,OAAX,CANH;AAOD;AAED;;;;;;;AAOG;;AACH,SAASG,SAATA,CAAwEC,KAAA;EAAA,IAArD;IAAEC,IAAF;IAAQjN;GAA6C,GAAAgN,KAAA;EACtE,IAAIE,OAAO,GAAGC,mBAAU,CAACF,IAAD,CAAxB;EAEA3M,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB,IAAIwC,OAAO,CAACjO,KAAR,KAAkB,SAAlB,IAA+B,CAACgO,IAApC,EAA0C;MACxCC,OAAO,CAACE,KAAR;IACD;EACF,CAJD,EAIG,CAACF,OAAD,EAAUD,IAAV,CAJH;EAMA3M,KAAK,CAACoK,SAAN,CAAgB,MAAK;IACnB,IAAIwC,OAAO,CAACjO,KAAR,KAAkB,SAAtB,EAAiC;MAC/B,IAAIoO,OAAO,GAAGtP,MAAM,CAACuP,OAAP,CAAetN,OAAf,CAAd;MACA,IAAIqN,OAAJ,EAAa;QACXE,UAAU,CAACL,OAAO,CAACG,OAAT,EAAkB,CAAlB,CAAV;MACD,CAFD,MAEO;QACLH,OAAO,CAACE,KAAR;MACD;IACF;EACF,CATD,EASG,CAACF,OAAD,EAAUlN,OAAV,CATH;AAUD;AAMD;AACA;AACA;;AAEA,SAASqI,OAATA,CAAiBmF,IAAjB,EAAgCxN,OAAhC,EAA+C;EAC7C,IAAI,CAACwN,IAAL,EAAW;IACT;IACA,IAAI,OAAO7C,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAa5K,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIpC,KAAJ,CAAUoC,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOmM,CAAP,EAAU;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}