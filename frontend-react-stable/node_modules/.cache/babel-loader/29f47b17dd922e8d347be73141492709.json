{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Radio,Upload,Input,Select,Button,Typography,Space,Divider,message,Spin,Table,Alert,Statistic,Row,Col,Progress}from'antd';import{InboxOutlined,PlayCircleOutlined}from'@ant-design/icons';import{LineChart,Line,XAxis,YAxis,CartesianGrid,Tooltip,Legend,ResponsiveContainer}from'recharts';import{modelPredictionAPI}from'../services/api';import useTaskManager from'../hooks/useTaskManager';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Dragger}=Upload;const{Option}=Select;// 预测结果展示组件\nconst PredictionResultDisplay=_ref=>{let{result}=_ref;// 表格列定义 - 与Streamlit版本保持一致\nconst predictionColumns=[{title:'时间戳',dataIndex:'timestamp',key:'timestamp',width:180},{title:'真实流量 (pps)',dataIndex:'packets_per_sec',key:'packets_per_sec',render:value=>value===null||value===void 0?void 0:value.toFixed(2)},{title:'平滑后真实流量',dataIndex:'packets_per_sec_smooth',key:'packets_per_sec_smooth',render:value=>value===null||value===void 0?void 0:value.toFixed(2)},{title:'模型预测趋势',dataIndex:'pred_smooth',key:'pred_smooth',render:value=>value===null||value===void 0?void 0:value.toFixed(2)},{title:'动态阈值',dataIndex:'threshold',key:'threshold',render:value=>value===null||value===void 0?void 0:value.toFixed(2)},{title:'异常状态',dataIndex:'is_anomaly',key:'is_anomaly',render:isAnomaly=>/*#__PURE__*/_jsx(\"span\",{style:{color:isAnomaly?'#ff4d4f':'#52c41a'},children:isAnomaly?'🔴 异常':'🟢 正常'})}];return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Row,{gutter:16,style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u5EFA\\u8BAE\\u7684\\u6D41\\u91CF\\u6E05\\u6D17\\u9608\\u503C (pps)\",value:result.suggested_threshold,precision:2,valueStyle:{color:'#1890ff'}}),result.suggested_threshold&&/*#__PURE__*/_jsx(Alert,{message:\"\\u2705 \\u6B64\\u9608\\u503C\\u5DF2\\u81EA\\u52A8\\u4FDD\\u5B58\\u5230\\u4EE5\\u8F93\\u5165CSV\\u6587\\u4EF6\\u547D\\u540D\\u7684\\u7ED3\\u679C\\u6587\\u4EF6\\u4E2D\\uFF0C\\u53EF\\u7528\\u4E8E\\u751F\\u6210\\u6E05\\u6D17\\u6A21\\u677F\\u3002\",type:\"success\",showIcon:true,style:{marginTop:8}})]}),/*#__PURE__*/_jsxs(Col,{span:12,children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u68C0\\u6D4B\\u5230\\u7684\\u5F02\\u5E38\\u70B9\\u6570\\u91CF\",value:result.anomaly_count,valueStyle:{color:result.anomaly_count>0?'#ff4d4f':'#52c41a'}}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"\\u8FD9\\u662F\\u57FA\\u4E8E\\u56FE\\u4E2D\\u7684\\u52A8\\u6001\\u9608\\u503C\\uFF08\\u7EA2\\u8272\\u865A\\u7EBF\\uFF09\\u68C0\\u6D4B\\u51FA\\u7684\\u3001\\u6D41\\u91CF\\u8D85\\u8FC7\\u9608\\u503C\\u7684\\u5177\\u4F53\\u65F6\\u95F4\\u70B9\\u6570\\u91CF\\u3002\"})]})]}),result.predictions&&result.predictions.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Alert,{message:\"\\u4E0B\\u56FE\\u5C55\\u793A\\u4E86\\u771F\\u5B9E\\u6D41\\u91CF\\u3001\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\\u548C\\u7528\\u4E8E\\u5B9E\\u65F6\\u68C0\\u6D4B\\u7684\\u52A8\\u6001\\u9608\\u503C\\u3002\",type:\"info\",showIcon:true,style:{marginBottom:16}}),/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u7ED3\\u679C\\u56FE\\u8868\"}),/*#__PURE__*/_jsx(\"div\",{style:{height:400,marginTop:8},children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:\"100%\",children:/*#__PURE__*/_jsxs(LineChart,{data:result.predictions// 显示所有数据点\n,margin:{top:5,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"timestamp\",tick:{fontSize:10},interval:\"preserveStartEnd\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{labelFormatter:value=>`时间: ${value}`,formatter:(value,name)=>[Number(value===null||value===void 0?void 0:value.toFixed(2)),name]}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"packets_per_sec_smooth\",stroke:\"#007bff\",strokeWidth:2,dot:false,name:\"\\u771F\\u5B9E\\u6D41\\u91CF (\\u5E73\\u6ED1\\u540E)\"}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"pred_smooth\",stroke:\"#ffa500\",strokeWidth:2,strokeDasharray:\"5 5\",dot:false,name:\"\\u6A21\\u578B\\u9884\\u6D4B\\u8D8B\\u52BF\"}),/*#__PURE__*/_jsx(Line,{type:\"monotone\",dataKey:\"threshold\",stroke:\"#ff0000\",strokeWidth:2,strokeDasharray:\"3 3\",dot:false,name:\"\\u52A8\\u6001\\u6E05\\u6D17\\u9608\\u503C\"})]})})})]}),(result.duration_seconds!==undefined||result.cpu_percent!==undefined||result.memory_mb!==undefined||result.gpu_memory_mb!==undefined||result.gpu_utilization_percent!==undefined)&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Title,{level:5,children:\"\\u8D44\\u6E90\\u4F7F\\u7528\\u60C5\\u51B5\"}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[result.duration_seconds!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u9884\\u6D4B\\u8017\\u65F6\",value:result.duration_seconds,precision:2,suffix:\"\\u79D2\",valueStyle:{color:'#1890ff'}})}),result.cpu_percent!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"CPU\\u4F7F\\u7528\\u7387\",value:result.cpu_percent,precision:1,suffix:\"%\",valueStyle:{color:'#52c41a'}})}),result.memory_mb!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5185\\u5B58\\u4F7F\\u7528\",value:result.memory_mb,precision:1,suffix:\"MB\",valueStyle:{color:'#fa8c16'}})}),result.gpu_memory_mb!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u5185\\u5B58\",value:result.gpu_memory_mb,precision:1,suffix:\"MB\",valueStyle:{color:'#722ed1'}})}),result.gpu_utilization_percent!==undefined&&/*#__PURE__*/_jsx(Col,{flex:\"auto\",children:/*#__PURE__*/_jsx(Statistic,{title:\"GPU\\u5229\\u7528\\u7387\",value:result.gpu_utilization_percent,precision:1,suffix:\"%\",valueStyle:{color:'#eb2f96'}})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u8BE6\\u60C5\\uFF08\\u524D100\\u6761\\uFF09\\uFF1A\"}),/*#__PURE__*/_jsx(Table,{columns:predictionColumns,dataSource:result.predictions.slice(0,100)// 只显示前100条\n,pagination:{pageSize:10,showSizeChanger:true,showQuickJumper:true,showTotal:total=>`共 ${total} 条记录`},size:\"small\",style:{marginTop:8},rowKey:(_,index)=>`${index}`})]})]});};// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions=['TCP','UDP','ICMP'];const datatypeOptions={TCP:['spt_sip_dip','dpt_sip_dip','len_dpt_syn','seq_ack_dip'],UDP:['spt_sip_dip','dpt_sip_dip'],ICMP:['dip']};const ModelPredictionPage=()=>{const[dataSource,setDataSource]=useState('upload');const[uploadedFile,setUploadedFile]=useState(null);const[csvDir,setCsvDir]=useState('');const[availableCsvFiles,setAvailableCsvFiles]=useState([]);const[selectedCsvFile,setSelectedCsvFile]=useState('');const[csvFilesLoading,setCsvFilesLoading]=useState(false);// 模型相关状态\nconst[modelDir,setModelDir]=useState('');const[availablePthFiles,setAvailablePthFiles]=useState([]);const[selectedModels,setSelectedModels]=useState([]);const[modelsLoading,setModelsLoading]=useState(false);const[predictionMode,setPredictionMode]=useState('single');// 单模型选择状态\nconst[selectedModelFile,setSelectedModelFile]=useState('');const[selectedParamsFile,setSelectedParamsFile]=useState('');const[selectedScalerFile,setSelectedScalerFile]=useState('');const[selectedProt,setSelectedProt]=useState('');const[selectedDatatype,setSelectedDatatype]=useState('');const[showManualSelection,setShowManualSelection]=useState(false);// 预测状态\nconst[predicting,setPredicting]=useState(false);const[progress,setProgress]=useState(0);const[results,setResults]=useState([]);const[selectedResultIndex,setSelectedResultIndex]=useState(0);const[matchingFilesLoading,setMatchingFilesLoading]=useState(false);// 任务管理\nconst{submitPredictionTask}=useTaskManager();const[useAsyncPrediction,setUseAsyncPrediction]=useState(true);// 默认使用异步预测\n// 获取CSV文件列表\nconst fetchCsvFiles=async()=>{if(!csvDir)return;setCsvFilesLoading(true);try{const response=await modelPredictionAPI.listCsvFiles(csvDir);setAvailableCsvFiles(response.data.files||[]);}catch(error){var _error$response,_error$response$data;message.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'获取CSV文件列表失败');setAvailableCsvFiles([]);}finally{setCsvFilesLoading(false);}};// 获取模型文件列表\nconst fetchModelFiles=async()=>{if(!modelDir)return;setModelsLoading(true);try{const response=await modelPredictionAPI.listModelFiles(modelDir);setAvailablePthFiles(response.data.pth_files||[]);}catch(error){var _error$response2,_error$response2$data;message.error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||'获取模型文件列表失败');setAvailablePthFiles([]);}finally{setModelsLoading(false);}};// 自动匹配参数和标准化器文件（与Streamlit版本一致）\nconst autoMatchFiles=async modelFile=>{if(!modelFile||!modelDir)return;setMatchingFilesLoading(true);try{// 调用后端API获取匹配的文件和协议信息\nconst response=await modelPredictionAPI.getMatchingFiles(modelFile,modelDir);if(response.data){const matchingFiles=response.data;// 设置自动匹配的文件\nsetSelectedParamsFile(matchingFiles.params_filename||'');setSelectedScalerFile(matchingFiles.scaler_filename||'');setSelectedProt(matchingFiles.protocol||'');setSelectedDatatype(matchingFiles.datatype||'');// 显示匹配结果\nif(matchingFiles.params_filename&&matchingFiles.scaler_filename){message.success('✅ 已自动匹配相关文件');}if(matchingFiles.protocol&&matchingFiles.datatype){message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);setShowManualSelection(false);}else{message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');setShowManualSelection(true);}}}catch(error){var _error$response3,_error$response3$data;message.error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||'获取匹配文件失败');// 如果API调用失败，回退到简单的文件名解析\nconst baseNameWithoutExt=modelFile.replace('_model_best.pth','');setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);setShowManualSelection(true);}finally{setMatchingFilesLoading(false);}};// 处理模型文件选择\nconst handleModelFileChange=modelFile=>{setSelectedModelFile(modelFile);// 重置之前的选择\nsetSelectedParamsFile('');setSelectedScalerFile('');setSelectedProt('');setSelectedDatatype('');setShowManualSelection(false);// 异步调用自动匹配\nautoMatchFiles(modelFile);};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(dataSource==='local'&&csvDir&&csvDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchCsvFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[dataSource,csvDir]);useEffect(()=>{if(modelDir&&modelDir.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchModelFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[modelDir]);// 文件上传配置\nconst uploadProps={name:'file',multiple:false,accept:'.csv',beforeUpload:()=>false,onChange:info=>{if(info.fileList.length>0){setUploadedFile(info.fileList[0]);}else{setUploadedFile(null);}}};// 开始预测\nconst handleStartPrediction=async()=>{// 验证输入\nif(dataSource==='upload'&&!uploadedFile){message.error('请上传CSV文件');return;}if(dataSource==='local'&&(!csvDir||!selectedCsvFile)){message.error('请选择CSV文件');return;}// 验证模型选择\nlet modelsToPredict=[];if(predictionMode==='single'){if(!selectedModelFile||!selectedProt||!selectedDatatype){message.error('请选择模型文件并确保协议和数据类型已设置');return;}modelsToPredict=[{model_file:selectedModelFile,params_file:selectedParamsFile,scaler_file:selectedScalerFile,protocol:selectedProt,datatype:selectedDatatype}];}else{if(selectedModels.length===0){message.error('请至少选择一个模型');return;}// 为每个选中的模型获取匹配信息（与Streamlit版本一致）\nconst validModels=[];for(const modelFile of selectedModels){try{const response=await modelPredictionAPI.getMatchingFiles(modelFile,modelDir);if(response.data){const matchingFiles=response.data;const params_file=matchingFiles.params_filename;const scaler_file=matchingFiles.scaler_filename;const protocol=matchingFiles.protocol;const datatype=matchingFiles.datatype;// 只有当所有必要信息都可用时，才添加到选中模型列表\nif(params_file&&scaler_file&&protocol&&datatype){validModels.push({model_file:modelFile,params_file,scaler_file,protocol,datatype});message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);}else{message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);}}else{message.error(`获取模型 ${modelFile} 的匹配文件失败`);}}catch(error){var _error$response4,_error$response4$data;message.error(`处理模型 ${modelFile} 时出错: ${((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||error.message}`);}}if(validModels.length===0){message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');return;}modelsToPredict=validModels;}setPredicting(true);setProgress(0);setResults([]);try{if(useAsyncPrediction&&predictionMode==='single'){// 只有单模型预测支持异步模式\nconst formData=new FormData();if(dataSource==='upload'&&uploadedFile){formData.append('file',uploadedFile.originFileObj);}else{formData.append('csv_dir',csvDir);formData.append('selected_file',selectedCsvFile);}formData.append('model_filename',selectedModelFile);formData.append('params_filename',selectedParamsFile);formData.append('scaler_filename',selectedScalerFile);formData.append('selected_prot',selectedProt);formData.append('selected_datatype',selectedDatatype);formData.append('model_dir',modelDir);formData.append('output_folder',modelDir);// 提交异步任务\nconst taskId=await submitPredictionTask(formData);if(taskId){message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');// 重置状态\nsetPredicting(false);setProgress(0);}return;// 异步模式下直接返回\n}// 同步预测模式（保留原有逻辑）\nconst allResults=[];for(let i=0;i<modelsToPredict.length;i++){const model=modelsToPredict[i];// 更新进度\nsetProgress(Math.round(i/modelsToPredict.length*90));if(modelsToPredict.length>1){message.info(`正在使用模型 ${i+1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);}const formData=new FormData();if(dataSource==='upload'&&uploadedFile){// 重新读取文件内容，因为文件内容只能读取一次\nformData.append('file',uploadedFile.originFileObj);}else{formData.append('csv_dir',csvDir);formData.append('selected_file',selectedCsvFile);}formData.append('model_filename',model.model_file);formData.append('params_filename',model.params_file);formData.append('scaler_filename',model.scaler_file);formData.append('selected_prot',model.protocol);formData.append('selected_datatype',model.datatype);formData.append('output_folder',modelDir);const response=await modelPredictionAPI.predict(formData);if(response.data){allResults.push({model_name:response.data.model_name||`${model.protocol}_${model.datatype}`,anomaly_count:response.data.anomaly_count||0,suggested_threshold:response.data.suggested_threshold||0,predictions:response.data.predictions||[],// 添加资源监控信息\nduration_seconds:response.data.duration_seconds,cpu_percent:response.data.cpu_percent,memory_mb:response.data.memory_mb,gpu_memory_mb:response.data.gpu_memory_mb,gpu_utilization_percent:response.data.gpu_utilization_percent});if(modelsToPredict.length>1){message.success(`✅ 模型 ${model.model_file} 预测成功`);}}}setProgress(100);setResults(allResults);message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);}catch(error){var _error$response5,_error$response5$data;message.error(((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||'预测失败');}finally{setPredicting(false);}};const isFormValid=()=>{const hasData=dataSource==='upload'?uploadedFile:csvDir&&selectedCsvFile;if(predictionMode==='single'){return hasData&&selectedModelFile&&selectedProt&&selectedDatatype;}else{return hasData&&selectedModels.length>0;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6A21\\u578B\\u5B9E\\u65F6\\u9884\\u6D4B\\u4E0E\\u5F02\\u5E38\\u68C0\\u6D4B\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u52A0\\u8F7D\\u5DF2\\u8BAD\\u7EC3\\u7684\\u6D41\\u91CF\\u6A21\\u578B\\uFF0C\\u5BF9\\u65B0\\u6570\\u636E\\u8FDB\\u884C\\u9884\\u6D4B\\uFF0C\\u5E76\\u6839\\u636E\\u52A8\\u6001\\u9608\\u503C\\u68C0\\u6D4B\\u5F02\\u5E38\\u6D41\\u91CF\\u3002\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6D41\\u91CF\\u6570\\u636E\\u6E90\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u6570\\u636E\\u6E90\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:dataSource,onChange:e=>setDataSource(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"upload\",children:\"\\u4E0A\\u4F20CSV\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Radio,{value:\"local\",children:\"\\u9009\\u62E9\\u672C\\u5730CSV\\u6587\\u4EF6\"})]})]}),dataSource==='upload'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4E0A\\u4F20\\u5F85\\u9884\\u6D4B\\u7684CSV\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsxs(Dragger,{...uploadProps,style:{marginTop:8},children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(InboxOutlined,{})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FDCSV\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301CSV\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"})]})]}),dataSource==='local'&&/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u6587\\u4EF6\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:csvDir,onChange:e=>setCsvDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchCsvFiles,loading:csvFilesLoading,disabled:!csvDir,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9CSV\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:csvFilesLoading,children:/*#__PURE__*/_jsx(Select,{value:selectedCsvFile,onChange:setSelectedCsvFile,placeholder:\"\\u8BF7\\u9009\\u62E9CSV\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:csvFilesLoading,children:availableCsvFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]})]})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\u6A21\\u578B\\u9009\\u62E9\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u6A21\\u578B\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:modelDir,onChange:e=>setModelDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchModelFiles,loading:modelsLoading,disabled:!modelDir,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:predictionMode,onChange:e=>setPredictionMode(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"single\",children:\"\\u5355\\u4E2A\\u6A21\\u578B\\u9884\\u6D4B\"}),/*#__PURE__*/_jsx(Radio,{value:\"multiple\",children:\"\\u591A\\u4E2A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\"})]})]}),predictionMode==='single'?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:modelsLoading,children:/*#__PURE__*/_jsx(Select,{value:selectedModelFile,onChange:handleModelFileChange,placeholder:\"\\u9009\\u62E9\\u4E00\\u4E2A\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:modelsLoading,children:availablePthFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})}),selectedModelFile&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u81EA\\u52A8\\u5339\\u914D\\u7684\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:matchingFilesLoading,children:/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8,padding:12,backgroundColor:'#f5f5f5',borderRadius:4},children:matchingFilesLoading?/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u81EA\\u52A8\\u5339\\u914D\\u76F8\\u5173\\u6587\\u4EF6...\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u53C2\\u6570\\u6587\\u4EF6:\"}),\" \",selectedParamsFile||'未匹配']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6:\"}),\" \",selectedScalerFile||'未匹配']}),!showManualSelection&&selectedProt&&selectedDatatype&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u534F\\u8BAE:\"}),\" \",selectedProt]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6570\\u636E\\u7C7B\\u578B:\"}),\" \",selectedDatatype]})]})]})})})]}),showManualSelection&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u534F\\u8BAE\\u548C\\u6570\\u636E\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:8},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Select,{value:selectedProt,onChange:setSelectedProt,placeholder:\"\\u9009\\u62E9\\u4E0E\\u6A21\\u578B\\u5BF9\\u5E94\\u7684\\u534F\\u8BAE\",style:{width:'100%'},children:protocolOptions.map(prot=>/*#__PURE__*/_jsx(Option,{value:prot,children:prot},prot))}),selectedProt&&/*#__PURE__*/_jsx(Select,{value:selectedDatatype,onChange:setSelectedDatatype,placeholder:`选择与模型对应的 ${selectedProt} 数据类型`,style:{width:'100%'},children:(datatypeOptions[selectedProt]||[]).map(datatype=>/*#__PURE__*/_jsx(Option,{value:datatype,children:datatype},datatype))})]})})]})]})})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6A21\\u578B\\u6587\\u4EF6\\uFF08\\u591A\\u9009\\uFF09\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:modelsLoading,children:/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedModels,onChange:setSelectedModels,placeholder:\"\\u8BF7\\u9009\\u62E9\\u591A\\u4E2A\\u6A21\\u578B\\u6587\\u4EF6\\u8FDB\\u884C\\u6279\\u91CF\\u9884\\u6D4B\",style:{width:'100%',marginTop:8},loading:modelsLoading,children:availablePthFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]}),availablePthFiles.length===0&&!modelsLoading&&/*#__PURE__*/_jsx(Alert,{message:\"\\u672A\\u627E\\u5230\\u6A21\\u578B\\u6587\\u4EF6\",description:\"\\u8BF7\\u786E\\u4FDD\\u6A21\\u578B\\u76EE\\u5F55\\u4E2D\\u5305\\u542B\\u8BAD\\u7EC3\\u597D\\u7684\\u6A21\\u578B\\u6587\\u4EF6\\uFF08.pth\\uFF09\\u53CA\\u5176\\u5BF9\\u5E94\\u7684\\u53C2\\u6570\\u6587\\u4EF6\\u548C\\u6807\\u51C6\\u5316\\u5668\\u6587\\u4EF6\\u3002\",type:\"warning\",showIcon:true})]})}),/*#__PURE__*/_jsx(Card,{className:\"function-card\",title:\"\\u9884\\u6D4B\\u6A21\\u5F0F\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"middle\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u9884\\u6D4B\\u6A21\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:useAsyncPrediction,onChange:e=>setUseAsyncPrediction(e.target.value),style:{marginTop:8},disabled:predictionMode==='multiple'// 多模型预测暂不支持异步\n,children:[/*#__PURE__*/_jsx(Radio,{value:true,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u5F02\\u6B65\\u9884\\u6D4B\\uFF08\\u63A8\\u8350\\uFF09\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u4E0D\\u963B\\u585E\\u5176\\u4ED6\\u64CD\\u4F5C\"})]})}),/*#__PURE__*/_jsx(Radio,{value:false,children:/*#__PURE__*/_jsxs(Space,{children:[\"\\u540C\\u6B65\\u9884\\u6D4B\",/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:\"- \\u7B49\\u5F85\\u9884\\u6D4B\\u5B8C\\u6210\\uFF0C\\u671F\\u95F4\\u65E0\\u6CD5\\u4F7F\\u7528\\u5176\\u4ED6\\u529F\\u80FD\"})]})})]})]}),useAsyncPrediction&&predictionMode==='single'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5F02\\u6B65\\u9884\\u6D4B\\u6A21\\u5F0F\",description:\"\\u9884\\u6D4B\\u4EFB\\u52A1\\u5C06\\u5728\\u540E\\u53F0\\u8FD0\\u884C\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7EE7\\u7EED\\u4F7F\\u7528\\u7CFB\\u7EDF\\u7684\\u5176\\u4ED6\\u529F\\u80FD\\u3002\\u4EFB\\u52A1\\u5B8C\\u6210\\u540E\\u4F1A\\u6536\\u5230\\u901A\\u77E5\\uFF0C\\u53EF\\u5728\\u4EFB\\u52A1\\u7BA1\\u7406\\u4E2D\\u67E5\\u770B\\u8FDB\\u5EA6\\u548C\\u7ED3\\u679C\\u3002\",type:\"info\",showIcon:true}),predictionMode==='multiple'&&/*#__PURE__*/_jsx(Alert,{message:\"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\",description:\"\\u591A\\u6A21\\u578B\\u6279\\u91CF\\u9884\\u6D4B\\u76EE\\u524D\\u4EC5\\u652F\\u6301\\u540C\\u6B65\\u6A21\\u5F0F\\uFF0C\\u9884\\u6D4B\\u8FC7\\u7A0B\\u4E2D\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\",type:\"warning\",showIcon:true})]})}),/*#__PURE__*/_jsxs(Card,{className:\"function-card\",children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleStartPrediction,loading:predicting,disabled:!isFormValid(),className:\"action-button\",children:predicting?'正在预测...':'开始预测与检测'}),predicting&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsx(Text,{children:\"\\u9884\\u6D4B\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:progress,status:\"active\"})]})]}),results.length>0&&/*#__PURE__*/_jsx(Card,{title:\"\\u9884\\u6D4B\\u7ED3\\u679C\",className:\"function-card\",children:results.length>1?/*#__PURE__*/// 多模型结果展示 - 与Streamlit版本一致\n_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Title,{level:4,children:\"\\u591A\\u6A21\\u578B\\u9884\\u6D4B\\u7ED3\\u679C\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6A21\\u578B\\u7ED3\\u679C\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:{width:'100%',marginTop:8},placeholder:\"\\u9009\\u62E9\\u6A21\\u578B\\u7ED3\\u679C\",value:selectedResultIndex,onChange:value=>setSelectedResultIndex(value),children:results.map((result,index)=>/*#__PURE__*/_jsx(Option,{value:index,children:result.model_name},index))})]}),results[selectedResultIndex]&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Title,{level:5,children:[\"\\u6A21\\u578B: \",results[selectedResultIndex].model_name]}),/*#__PURE__*/_jsx(PredictionResultDisplay,{result:results[selectedResultIndex]})]})]}):/*#__PURE__*/// 单模型结果展示\n_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Title,{level:4,children:[\"\\u9884\\u6D4B\\u7ED3\\u679C - \",results[0].model_name]}),/*#__PURE__*/_jsx(PredictionResultDisplay,{result:results[0]})]})})]});};export default ModelPredictionPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "Typography", "Space", "Divider", "message", "Spin", "Table", "<PERSON><PERSON>", "Statistic", "Row", "Col", "Progress", "InboxOutlined", "PlayCircleOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "modelPredictionAPI", "useTaskManager", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Title", "Text", "<PERSON><PERSON>", "Option", "PredictionResultDisplay", "_ref", "result", "predictionColumns", "title", "dataIndex", "key", "width", "render", "value", "toFixed", "isAnomaly", "style", "color", "children", "gutter", "marginBottom", "span", "suggested_threshold", "precision", "valueStyle", "type", "showIcon", "marginTop", "anomaly_count", "fontSize", "predictions", "length", "strong", "height", "data", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick", "interval", "labelFormatter", "formatter", "name", "Number", "stroke", "strokeWidth", "dot", "duration_seconds", "undefined", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_utilization_percent", "level", "flex", "suffix", "columns", "dataSource", "slice", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "size", "<PERSON><PERSON><PERSON>", "_", "index", "protocolOptions", "datatypeOptions", "TCP", "UDP", "ICMP", "ModelPredictionPage", "setDataSource", "uploadedFile", "setUploadedFile", "csvDir", "setCsvDir", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "modelDir", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rot", "selectedDatatype", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "progress", "setProgress", "results", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "submitPredictionTask", "useAsyncPrediction", "setUseAsyncPrediction", "fetchCsvFiles", "response", "listCsvFiles", "files", "error", "_error$response", "_error$response$data", "detail", "fetchModelFiles", "listModelFiles", "pth_files", "_error$response2", "_error$response2$data", "autoMatchFiles", "modelFile", "getMatchingFiles", "matchingFiles", "params_filename", "scaler_filename", "protocol", "datatype", "success", "warning", "_error$response3", "_error$response3$data", "baseNameWithoutExt", "replace", "handleModelFileChange", "timer", "setTimeout", "clearTimeout", "uploadProps", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "handleStartPrediction", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "push", "_error$response4", "_error$response4$data", "formData", "FormData", "append", "originFileObj", "taskId", "allResults", "i", "model", "Math", "round", "predict", "model_name", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "fontWeight", "className", "direction", "Group", "e", "target", "compact", "display", "placeholder", "onClick", "loading", "disabled", "marginLeft", "spinning", "map", "file", "padding", "backgroundColor", "borderRadius", "prot", "mode", "description", "icon", "percent", "status"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/ModelPredictionPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Table,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n  // 表格列定义 - 与Streamlit版本保持一致\n  const predictionColumns = [\n    {\n      title: '时间戳',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      width: 180,\n    },\n    {\n      title: '真实流量 (pps)',\n      dataIndex: 'packets_per_sec',\n      key: 'packets_per_sec',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '平滑后真实流量',\n      dataIndex: 'packets_per_sec_smooth',\n      key: 'packets_per_sec_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '模型预测趋势',\n      dataIndex: 'pred_smooth',\n      key: 'pred_smooth',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '动态阈值',\n      dataIndex: 'threshold',\n      key: 'threshold',\n      render: (value: number) => value?.toFixed(2),\n    },\n    {\n      title: '异常状态',\n      dataIndex: 'is_anomaly',\n      key: 'is_anomaly',\n      render: (isAnomaly: boolean) => (\n        <span style={{ color: isAnomaly ? '#ff4d4f' : '#52c41a' }}>\n          {isAnomaly ? '🔴 异常' : '🟢 正常'}\n        </span>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={12}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n        <Col span={12}>\n          <Statistic\n            title=\"检测到的异常点数量\"\n            value={result.anomaly_count}\n            valueStyle={{ color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a' }}\n          />\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            这是基于图中的动态阈值（红色虚线）检测出的、流量超过阈值的具体时间点数量。\n          </Text>\n        </Col>\n      </Row>\n\n      {/* 预测图表 - 与Streamlit版本保持一致 */}\n      {result.predictions && result.predictions.length > 0 && (\n        <div style={{ marginBottom: 24 }}>\n          <Alert\n            message=\"下图展示了真实流量、模型预测趋势和用于实时检测的动态阈值。\"\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Text strong>预测结果图表</Text>\n          <div style={{ height: 400, marginTop: 8 }}>\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <LineChart\n                data={result.predictions} // 显示所有数据点\n                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n              >\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis\n                  dataKey=\"timestamp\"\n                  tick={{ fontSize: 10 }}\n                  interval=\"preserveStartEnd\"\n                />\n                <YAxis />\n                <Tooltip\n                  labelFormatter={(value) => `时间: ${value}`}\n                  formatter={(value: number, name: string) => [Number(value?.toFixed(2)), name]}\n                />\n                <Legend />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"packets_per_sec_smooth\"\n                  stroke=\"#007bff\"\n                  strokeWidth={2}\n                  dot={false}\n                  name=\"真实流量 (平滑后)\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"pred_smooth\"\n                  stroke=\"#ffa500\"\n                  strokeWidth={2}\n                  strokeDasharray=\"5 5\"\n                  dot={false}\n                  name=\"模型预测趋势\"\n                />\n                <Line\n                  type=\"monotone\"\n                  dataKey=\"threshold\"\n                  stroke=\"#ff0000\"\n                  strokeWidth={2}\n                  strokeDasharray=\"3 3\"\n                  dot={false}\n                  name=\"动态清洗阈值\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n      <div>\n        <Text strong>预测详情（前100条）：</Text>\n        <Table\n          columns={predictionColumns}\n          dataSource={result.predictions.slice(0, 100)} // 只显示前100条\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n          size=\"small\"\n          style={{ marginTop: 8 }}\n          rowKey={(_, index) => `${index}`}\n        />\n      </div>\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  anomaly_count: number;\n  suggested_threshold: number;\n  predictions: Array<{\n    timestamp: string;\n    packets_per_sec: number;\n    packets_per_sec_smooth: number;\n    pred_smooth: number;\n    threshold: number;\n    is_anomaly: boolean;\n  }>;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const { submitPredictionTask } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            anomaly_count: response.data.anomaly_count || 0,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            predictions: response.data.predictions || [],\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传CSV文件</Radio>\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description=\"预测任务将在后台运行，您可以继续使用系统的其他功能。任务完成后会收到通知，可在任务管理中查看进度和结果。\"\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,SAAS,CACTC,GAAG,CACHC,GAAG,CACHC,QAAQ,KACH,MAAM,CACb,OAASC,aAAa,CAAEC,kBAAkB,KAAQ,mBAAmB,CACrE,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,kBAAkB,KAAQ,iBAAiB,CACpD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG9B,UAAU,CAClC,KAAM,CAAE+B,OAAQ,CAAC,CAAGnC,MAAM,CAC1B,KAAM,CAAEoC,MAAO,CAAC,CAAGlC,MAAM,CAEzB;AACA,KAAM,CAAAmC,uBAA+D,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CACjF;AACA,KAAM,CAAAE,iBAAiB,CAAG,CACxB,CACEC,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBC,KAAK,CAAE,GACT,CAAC,CACD,CACEH,KAAK,CAAE,YAAY,CACnBC,SAAS,CAAE,iBAAiB,CAC5BC,GAAG,CAAE,iBAAiB,CACtBE,MAAM,CAAGC,KAAa,EAAKA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAC7C,CAAC,CACD,CACEN,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,wBAAwB,CACnCC,GAAG,CAAE,wBAAwB,CAC7BE,MAAM,CAAGC,KAAa,EAAKA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAC7C,CAAC,CACD,CACEN,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,aAAa,CACxBC,GAAG,CAAE,aAAa,CAClBE,MAAM,CAAGC,KAAa,EAAKA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAC7C,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WAAW,CAChBE,MAAM,CAAGC,KAAa,EAAKA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAC7C,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,YAAY,CACjBE,MAAM,CAAGG,SAAkB,eACzBpB,IAAA,SAAMqB,KAAK,CAAE,CAAEC,KAAK,CAAEF,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAG,QAAA,CACvDH,SAAS,CAAG,OAAO,CAAG,OAAO,CAC1B,CAEV,CAAC,CACF,CAED,mBACElB,KAAA,QAAAqB,QAAA,eACErB,KAAA,CAAClB,GAAG,EAACwC,MAAM,CAAE,EAAG,CAACH,KAAK,CAAE,CAAEI,YAAY,CAAE,EAAG,CAAE,CAAAF,QAAA,eAC3CrB,KAAA,CAACjB,GAAG,EAACyC,IAAI,CAAE,EAAG,CAAAH,QAAA,eACZvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,8DAAiB,CACvBK,KAAK,CAAEP,MAAM,CAACgB,mBAAoB,CAClCC,SAAS,CAAE,CAAE,CACbC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACDX,MAAM,CAACgB,mBAAmB,eACzB3B,IAAA,CAAClB,KAAK,EACJH,OAAO,CAAC,kNAAwC,CAChDmD,IAAI,CAAC,SAAS,CACdC,QAAQ,MACRV,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CACzB,CACF,EACE,CAAC,cACN9B,KAAA,CAACjB,GAAG,EAACyC,IAAI,CAAE,EAAG,CAAAH,QAAA,eACZvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,wDAAW,CACjBK,KAAK,CAAEP,MAAM,CAACsB,aAAc,CAC5BJ,UAAU,CAAE,CAAEP,KAAK,CAAEX,MAAM,CAACsB,aAAa,CAAG,CAAC,CAAG,SAAS,CAAG,SAAU,CAAE,CACzE,CAAC,cACFjC,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAACT,KAAK,CAAE,CAAEa,QAAQ,CAAE,EAAG,CAAE,CAAAX,QAAA,CAAC,gOAEhD,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,CAGLZ,MAAM,CAACwB,WAAW,EAAIxB,MAAM,CAACwB,WAAW,CAACC,MAAM,CAAG,CAAC,eAClDlC,KAAA,QAAKmB,KAAK,CAAE,CAAEI,YAAY,CAAE,EAAG,CAAE,CAAAF,QAAA,eAC/BvB,IAAA,CAAClB,KAAK,EACJH,OAAO,CAAC,gLAA+B,CACvCmD,IAAI,CAAC,MAAM,CACXC,QAAQ,MACRV,KAAK,CAAE,CAAEI,YAAY,CAAE,EAAG,CAAE,CAC7B,CAAC,cACFzB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC1BvB,IAAA,QAAKqB,KAAK,CAAE,CAAEiB,MAAM,CAAE,GAAG,CAAEN,SAAS,CAAE,CAAE,CAAE,CAAAT,QAAA,cACxCvB,IAAA,CAACJ,mBAAmB,EAACoB,KAAK,CAAC,MAAM,CAACsB,MAAM,CAAC,MAAM,CAAAf,QAAA,cAC7CrB,KAAA,CAACb,SAAS,EACRkD,IAAI,CAAE5B,MAAM,CAACwB,WAAa;AAAA,CAC1BK,MAAM,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAArB,QAAA,eAEnDvB,IAAA,CAACP,aAAa,EAACoD,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC7C,IAAA,CAACT,KAAK,EACJuD,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAE,CAAEb,QAAQ,CAAE,EAAG,CAAE,CACvBc,QAAQ,CAAC,kBAAkB,CAC5B,CAAC,cACFhD,IAAA,CAACR,KAAK,GAAE,CAAC,cACTQ,IAAA,CAACN,OAAO,EACNuD,cAAc,CAAG/B,KAAK,EAAK,OAAOA,KAAK,EAAG,CAC1CgC,SAAS,CAAEA,CAAChC,KAAa,CAAEiC,IAAY,GAAK,CAACC,MAAM,CAAClC,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAEgC,IAAI,CAAE,CAC/E,CAAC,cACFnD,IAAA,CAACL,MAAM,GAAE,CAAC,cACVK,IAAA,CAACV,IAAI,EACHwC,IAAI,CAAC,UAAU,CACfgB,OAAO,CAAC,wBAAwB,CAChCO,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfC,GAAG,CAAE,KAAM,CACXJ,IAAI,CAAC,+CAAY,CAClB,CAAC,cACFnD,IAAA,CAACV,IAAI,EACHwC,IAAI,CAAC,UAAU,CACfgB,OAAO,CAAC,aAAa,CACrBO,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfT,eAAe,CAAC,KAAK,CACrBU,GAAG,CAAE,KAAM,CACXJ,IAAI,CAAC,sCAAQ,CACd,CAAC,cACFnD,IAAA,CAACV,IAAI,EACHwC,IAAI,CAAC,UAAU,CACfgB,OAAO,CAAC,WAAW,CACnBO,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAE,CAAE,CACfT,eAAe,CAAC,KAAK,CACrBU,GAAG,CAAE,KAAM,CACXJ,IAAI,CAAC,sCAAQ,CACd,CAAC,EACO,CAAC,CACO,CAAC,CACnB,CAAC,EACH,CACN,CAGA,CAACxC,MAAM,CAAC6C,gBAAgB,GAAKC,SAAS,EAAI9C,MAAM,CAAC+C,WAAW,GAAKD,SAAS,EAAI9C,MAAM,CAACgD,SAAS,GAAKF,SAAS,EAAI9C,MAAM,CAACiD,aAAa,GAAKH,SAAS,EAAI9C,MAAM,CAACkD,uBAAuB,GAAKJ,SAAS,gBACjMvD,KAAA,QAAKmB,KAAK,CAAE,CAAEI,YAAY,CAAE,EAAG,CAAE,CAAAF,QAAA,eAC/BvB,IAAA,CAACK,KAAK,EAACyD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAAC,sCAAM,CAAO,CAAC,cAC/BrB,KAAA,CAAClB,GAAG,EAACwC,MAAM,CAAE,EAAG,CAAAD,QAAA,EACbZ,MAAM,CAAC6C,gBAAgB,GAAKC,SAAS,eACpCzD,IAAA,CAACf,GAAG,EAAC8E,IAAI,CAAC,MAAM,CAAAxC,QAAA,cACdvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,0BAAM,CACZK,KAAK,CAAEP,MAAM,CAAC6C,gBAAiB,CAC/B5B,SAAS,CAAE,CAAE,CACboC,MAAM,CAAC,QAAG,CACVnC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAAC+C,WAAW,GAAKD,SAAS,eAC/BzD,IAAA,CAACf,GAAG,EAAC8E,IAAI,CAAC,MAAM,CAAAxC,QAAA,cACdvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,uBAAQ,CACdK,KAAK,CAAEP,MAAM,CAAC+C,WAAY,CAC1B9B,SAAS,CAAE,CAAE,CACboC,MAAM,CAAC,GAAG,CACVnC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACgD,SAAS,GAAKF,SAAS,eAC7BzD,IAAA,CAACf,GAAG,EAAC8E,IAAI,CAAC,MAAM,CAAAxC,QAAA,cACdvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,0BAAM,CACZK,KAAK,CAAEP,MAAM,CAACgD,SAAU,CACxB/B,SAAS,CAAE,CAAE,CACboC,MAAM,CAAC,IAAI,CACXnC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACiD,aAAa,GAAKH,SAAS,eACjCzD,IAAA,CAACf,GAAG,EAAC8E,IAAI,CAAC,MAAM,CAAAxC,QAAA,cACdvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,iBAAO,CACbK,KAAK,CAAEP,MAAM,CAACiD,aAAc,CAC5BhC,SAAS,CAAE,CAAE,CACboC,MAAM,CAAC,IAAI,CACXnC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,CACAX,MAAM,CAACkD,uBAAuB,GAAKJ,SAAS,eAC3CzD,IAAA,CAACf,GAAG,EAAC8E,IAAI,CAAC,MAAM,CAAAxC,QAAA,cACdvB,IAAA,CAACjB,SAAS,EACR8B,KAAK,CAAC,uBAAQ,CACdK,KAAK,CAAEP,MAAM,CAACkD,uBAAwB,CACtCjC,SAAS,CAAE,CAAE,CACboC,MAAM,CAAC,GAAG,CACVnC,UAAU,CAAE,CAAEP,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CACN,EACE,CAAC,EACH,CACN,cAEDpB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,2DAAY,CAAM,CAAC,cAChCvB,IAAA,CAACnB,KAAK,EACJoF,OAAO,CAAErD,iBAAkB,CAC3BsD,UAAU,CAAEvD,MAAM,CAACwB,WAAW,CAACgC,KAAK,CAAC,CAAC,CAAE,GAAG,CAAG;AAAA,CAC9CC,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGC,KAAK,EAAK,KAAKA,KAAK,MAClC,CAAE,CACFC,IAAI,CAAC,OAAO,CACZrD,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CACxB2C,MAAM,CAAEA,CAACC,CAAC,CAAEC,KAAK,GAAK,GAAGA,KAAK,EAAG,CAClC,CAAC,EACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CAC9C,KAAM,CAAAC,eAAe,CAAG,CACtBC,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAE,aAAa,CAAE,aAAa,CAAC,CACjEC,GAAG,CAAE,CAAC,aAAa,CAAE,aAAa,CAAC,CACnCC,IAAI,CAAE,CAAC,KAAK,CACd,CAAC,CAsBD,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAACjB,UAAU,CAAEkB,aAAa,CAAC,CAAGpH,QAAQ,CAAqB,QAAQ,CAAC,CAC1E,KAAM,CAACqH,YAAY,CAAEC,eAAe,CAAC,CAAGtH,QAAQ,CAAM,IAAI,CAAC,CAC3D,KAAM,CAACuH,MAAM,CAAEC,SAAS,CAAC,CAAGxH,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACyH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1H,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAAC2H,eAAe,CAAEC,kBAAkB,CAAC,CAAG5H,QAAQ,CAAS,EAAE,CAAC,CAClE,KAAM,CAAC6H,eAAe,CAAEC,kBAAkB,CAAC,CAAG9H,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC+H,QAAQ,CAAEC,WAAW,CAAC,CAAGhI,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlI,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAACmI,cAAc,CAAEC,iBAAiB,CAAC,CAAGpI,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAACqI,aAAa,CAAEC,gBAAgB,CAAC,CAAGtI,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuI,cAAc,CAAEC,iBAAiB,CAAC,CAAGxI,QAAQ,CAAwB,QAAQ,CAAC,CAErF;AACA,KAAM,CAACyI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1I,QAAQ,CAAS,EAAE,CAAC,CACtE,KAAM,CAAC2I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5I,QAAQ,CAAS,EAAE,CAAC,CACxE,KAAM,CAAC6I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9I,QAAQ,CAAS,EAAE,CAAC,CACxE,KAAM,CAAC+I,YAAY,CAAEC,eAAe,CAAC,CAAGhJ,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACiJ,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlJ,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAACmJ,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpJ,QAAQ,CAAC,KAAK,CAAC,CAErE;AACA,KAAM,CAACqJ,UAAU,CAAEC,aAAa,CAAC,CAAGtJ,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACuJ,QAAQ,CAAEC,WAAW,CAAC,CAAGxJ,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACyJ,OAAO,CAAEC,UAAU,CAAC,CAAG1J,QAAQ,CAAqB,EAAE,CAAC,CAC9D,KAAM,CAAC2J,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG5J,QAAQ,CAAS,CAAC,CAAC,CACzE,KAAM,CAAC6J,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG9J,QAAQ,CAAC,KAAK,CAAC,CAEvE;AACA,KAAM,CAAE+J,oBAAqB,CAAC,CAAGjI,cAAc,CAAC,CAAC,CACjD,KAAM,CAACkI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjK,QAAQ,CAAC,IAAI,CAAC,CAAE;AAEpE;AACA,KAAM,CAAAkK,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAAC3C,MAAM,CAAE,OAEbO,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAAqC,QAAQ,CAAG,KAAM,CAAAtI,kBAAkB,CAACuI,YAAY,CAAC7C,MAAM,CAAC,CAC9DG,oBAAoB,CAACyC,QAAQ,CAAC5F,IAAI,CAAC8F,KAAK,EAAI,EAAE,CAAC,CACjD,CAAE,MAAOC,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnB7J,OAAO,CAAC2J,KAAK,CAAC,EAAAC,eAAA,CAAAD,KAAK,CAACH,QAAQ,UAAAI,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBhG,IAAI,UAAAiG,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,aAAa,CAAC,CAC5D/C,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,OAAS,CACRI,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAA4C,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAC3C,QAAQ,CAAE,OAEfO,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAI,CACF,KAAM,CAAA6B,QAAQ,CAAG,KAAM,CAAAtI,kBAAkB,CAAC8I,cAAc,CAAC5C,QAAQ,CAAC,CAClEG,oBAAoB,CAACiC,QAAQ,CAAC5F,IAAI,CAACqG,SAAS,EAAI,EAAE,CAAC,CACrD,CAAE,MAAON,KAAU,CAAE,KAAAO,gBAAA,CAAAC,qBAAA,CACnBnK,OAAO,CAAC2J,KAAK,CAAC,EAAAO,gBAAA,CAAAP,KAAK,CAACH,QAAQ,UAAAU,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBtG,IAAI,UAAAuG,qBAAA,iBAApBA,qBAAA,CAAsBL,MAAM,GAAI,YAAY,CAAC,CAC3DvC,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,OAAS,CACRI,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,cAAc,CAAG,KAAO,CAAAC,SAAiB,EAAK,CAClD,GAAI,CAACA,SAAS,EAAI,CAACjD,QAAQ,CAAE,OAE7B+B,uBAAuB,CAAC,IAAI,CAAC,CAC7B,GAAI,CACF;AACA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAtI,kBAAkB,CAACoJ,gBAAgB,CAACD,SAAS,CAAEjD,QAAQ,CAAC,CAE/E,GAAIoC,QAAQ,CAAC5F,IAAI,CAAE,CACjB,KAAM,CAAA2G,aAAa,CAAGf,QAAQ,CAAC5F,IAAI,CAEnC;AACAqE,qBAAqB,CAACsC,aAAa,CAACC,eAAe,EAAI,EAAE,CAAC,CAC1DrC,qBAAqB,CAACoC,aAAa,CAACE,eAAe,EAAI,EAAE,CAAC,CAC1DpC,eAAe,CAACkC,aAAa,CAACG,QAAQ,EAAI,EAAE,CAAC,CAC7CnC,mBAAmB,CAACgC,aAAa,CAACI,QAAQ,EAAI,EAAE,CAAC,CAEjD;AACA,GAAIJ,aAAa,CAACC,eAAe,EAAID,aAAa,CAACE,eAAe,CAAE,CAClEzK,OAAO,CAAC4K,OAAO,CAAC,aAAa,CAAC,CAChC,CAEA,GAAIL,aAAa,CAACG,QAAQ,EAAIH,aAAa,CAACI,QAAQ,CAAE,CACpD3K,OAAO,CAAC4K,OAAO,CAAC,uBAAuBL,aAAa,CAACG,QAAQ,MAAMH,aAAa,CAACI,QAAQ,EAAE,CAAC,CAC5FlC,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CAAC,IAAM,CACLzI,OAAO,CAAC6K,OAAO,CAAC,4BAA4B,CAAC,CAC7CpC,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CACF,CACF,CAAE,MAAOkB,KAAU,CAAE,KAAAmB,gBAAA,CAAAC,qBAAA,CACnB/K,OAAO,CAAC2J,KAAK,CAAC,EAAAmB,gBAAA,CAAAnB,KAAK,CAACH,QAAQ,UAAAsB,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBlH,IAAI,UAAAmH,qBAAA,iBAApBA,qBAAA,CAAsBjB,MAAM,GAAI,UAAU,CAAC,CACzD;AACA,KAAM,CAAAkB,kBAAkB,CAAGX,SAAS,CAACY,OAAO,CAAC,iBAAiB,CAAE,EAAE,CAAC,CACnEhD,qBAAqB,CAAC,GAAG+C,kBAAkB,cAAc,CAAC,CAC1D7C,qBAAqB,CAAC,GAAG6C,kBAAkB,eAAe,CAAC,CAC3DvC,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,OAAS,CACRU,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,qBAAqB,CAAIb,SAAiB,EAAK,CACnDtC,oBAAoB,CAACsC,SAAS,CAAC,CAC/B;AACApC,qBAAqB,CAAC,EAAE,CAAC,CACzBE,qBAAqB,CAAC,EAAE,CAAC,CACzBE,eAAe,CAAC,EAAE,CAAC,CACnBE,mBAAmB,CAAC,EAAE,CAAC,CACvBE,sBAAsB,CAAC,KAAK,CAAC,CAE7B;AACA2B,cAAc,CAACC,SAAS,CAAC,CAC3B,CAAC,CAED;AACA/K,SAAS,CAAC,IAAM,CACd,GAAIiG,UAAU,GAAK,OAAO,EAAIqB,MAAM,EAAIA,MAAM,CAACnD,MAAM,CAAG,CAAC,CAAE,CAAE;AAC3D,KAAM,CAAA0H,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B7B,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAM8B,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAAC5F,UAAU,CAAEqB,MAAM,CAAC,CAAC,CAExBtH,SAAS,CAAC,IAAM,CACd,GAAI8H,QAAQ,EAAIA,QAAQ,CAAC3D,MAAM,CAAG,CAAC,CAAE,CAAE;AACrC,KAAM,CAAA0H,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BrB,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMsB,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAAC/D,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAkE,WAAW,CAAG,CAClB9G,IAAI,CAAE,MAAM,CACZ+G,QAAQ,CAAE,KAAK,CACfC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAEA,CAAA,GAAM,KAAK,CACzBC,QAAQ,CAAGC,IAAS,EAAK,CACvB,GAAIA,IAAI,CAACC,QAAQ,CAACnI,MAAM,CAAG,CAAC,CAAE,CAC5BkD,eAAe,CAACgF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC,IAAM,CACLjF,eAAe,CAAC,IAAI,CAAC,CACvB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAkF,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC;AACA,GAAItG,UAAU,GAAK,QAAQ,EAAI,CAACmB,YAAY,CAAE,CAC5C1G,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA,GAAIpE,UAAU,GAAK,OAAO,GAAK,CAACqB,MAAM,EAAI,CAACI,eAAe,CAAC,CAAE,CAC3DhH,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC,CACzB,OACF,CAEA;AACA,GAAI,CAAAmC,eAMF,CAAG,EAAE,CAEP,GAAIlE,cAAc,GAAK,QAAQ,CAAE,CAC/B,GAAI,CAACE,iBAAiB,EAAI,CAACM,YAAY,EAAI,CAACE,gBAAgB,CAAE,CAC5DtI,OAAO,CAAC2J,KAAK,CAAC,sBAAsB,CAAC,CACrC,OACF,CACAmC,eAAe,CAAG,CAAC,CACjBC,UAAU,CAAEjE,iBAAiB,CAC7BkE,WAAW,CAAEhE,kBAAkB,CAC/BiE,WAAW,CAAE/D,kBAAkB,CAC/BwC,QAAQ,CAAEtC,YAAY,CACtBuC,QAAQ,CAAErC,gBACZ,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAId,cAAc,CAAC/D,MAAM,GAAK,CAAC,CAAE,CAC/BzD,OAAO,CAAC2J,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA;AACA,KAAM,CAAAuC,WAMJ,CAAG,EAAE,CAEP,IAAK,KAAM,CAAA7B,SAAS,GAAI,CAAA7C,cAAc,CAAE,CACtC,GAAI,CACF,KAAM,CAAAgC,QAAQ,CAAG,KAAM,CAAAtI,kBAAkB,CAACoJ,gBAAgB,CAACD,SAAS,CAAEjD,QAAQ,CAAC,CAC/E,GAAIoC,QAAQ,CAAC5F,IAAI,CAAE,CACjB,KAAM,CAAA2G,aAAa,CAAGf,QAAQ,CAAC5F,IAAI,CACnC,KAAM,CAAAoI,WAAW,CAAGzB,aAAa,CAACC,eAAe,CACjD,KAAM,CAAAyB,WAAW,CAAG1B,aAAa,CAACE,eAAe,CACjD,KAAM,CAAAC,QAAQ,CAAGH,aAAa,CAACG,QAAQ,CACvC,KAAM,CAAAC,QAAQ,CAAGJ,aAAa,CAACI,QAAQ,CAEvC;AACA,GAAIqB,WAAW,EAAIC,WAAW,EAAIvB,QAAQ,EAAIC,QAAQ,CAAE,CACtDuB,WAAW,CAACC,IAAI,CAAC,CACfJ,UAAU,CAAE1B,SAAS,CACrB2B,WAAW,CACXC,WAAW,CACXvB,QAAQ,CACRC,QACF,CAAC,CAAC,CACF3K,OAAO,CAAC4K,OAAO,CAAC,QAAQP,SAAS,SAASK,QAAQ,MAAMC,QAAQ,EAAE,CAAC,CACrE,CAAC,IAAM,CACL3K,OAAO,CAAC6K,OAAO,CAAC,SAASR,SAAS,mBAAmB,CAAC,CACxD,CACF,CAAC,IAAM,CACLrK,OAAO,CAAC2J,KAAK,CAAC,QAAQU,SAAS,UAAU,CAAC,CAC5C,CACF,CAAE,MAAOV,KAAU,CAAE,KAAAyC,gBAAA,CAAAC,qBAAA,CACnBrM,OAAO,CAAC2J,KAAK,CAAC,QAAQU,SAAS,SAAS,EAAA+B,gBAAA,CAAAzC,KAAK,CAACH,QAAQ,UAAA4C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxI,IAAI,UAAAyI,qBAAA,iBAApBA,qBAAA,CAAsBvC,MAAM,GAAIH,KAAK,CAAC3J,OAAO,EAAE,CAAC,CAC1F,CACF,CAEA,GAAIkM,WAAW,CAACzI,MAAM,GAAK,CAAC,CAAE,CAC5BzD,OAAO,CAAC2J,KAAK,CAAC,wCAAwC,CAAC,CACvD,OACF,CAEAmC,eAAe,CAAGI,WAAW,CAC/B,CAEAvD,aAAa,CAAC,IAAI,CAAC,CACnBE,WAAW,CAAC,CAAC,CAAC,CACdE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,GAAIM,kBAAkB,EAAIzB,cAAc,GAAK,QAAQ,CAAE,CACrD;AACA,KAAM,CAAA0E,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B,GAAIhH,UAAU,GAAK,QAAQ,EAAImB,YAAY,CAAE,CAC3C4F,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE9F,YAAY,CAAC+F,aAAa,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAE5F,MAAM,CAAC,CAClC0F,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAExF,eAAe,CAAC,CACnD,CAEAsF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAE1E,iBAAiB,CAAC,CACpDwE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAExE,kBAAkB,CAAC,CACtDsE,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEtE,kBAAkB,CAAC,CACtDoE,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEpE,YAAY,CAAC,CAC9CkE,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAElE,gBAAgB,CAAC,CACtDgE,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAEpF,QAAQ,CAAC,CACtCkF,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEpF,QAAQ,CAAC,CAE1C;AACA,KAAM,CAAAsF,MAAM,CAAG,KAAM,CAAAtD,oBAAoB,CAACkD,QAAQ,CAAC,CAEnD,GAAII,MAAM,CAAE,CACV1M,OAAO,CAAC4K,OAAO,CAAC,gCAAgC,CAAC,CACjD;AACAjC,aAAa,CAAC,KAAK,CAAC,CACpBE,WAAW,CAAC,CAAC,CAAC,CAChB,CAEA,OAAQ;AACV,CAEA;AACA,KAAM,CAAA8D,UAA8B,CAAG,EAAE,CAEzC,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGd,eAAe,CAACrI,MAAM,CAAEmJ,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,KAAK,CAAGf,eAAe,CAACc,CAAC,CAAC,CAEhC;AACA/D,WAAW,CAACiE,IAAI,CAACC,KAAK,CAAEH,CAAC,CAAGd,eAAe,CAACrI,MAAM,CAAI,EAAE,CAAC,CAAC,CAE1D,GAAIqI,eAAe,CAACrI,MAAM,CAAG,CAAC,CAAE,CAC9BzD,OAAO,CAAC2L,IAAI,CAAC,UAAUiB,CAAC,CAAG,CAAC,IAAId,eAAe,CAACrI,MAAM,KAAKoJ,KAAK,CAACd,UAAU,UAAU,CAAC,CACxF,CAEA,KAAM,CAAAO,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAE/B,GAAIhH,UAAU,GAAK,QAAQ,EAAImB,YAAY,CAAE,CAC3C;AACA4F,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE9F,YAAY,CAAC+F,aAAa,CAAC,CACrD,CAAC,IAAM,CACLH,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAE5F,MAAM,CAAC,CAClC0F,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAExF,eAAe,CAAC,CACnD,CAEAsF,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEK,KAAK,CAACd,UAAU,CAAC,CACnDO,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEK,KAAK,CAACb,WAAW,CAAC,CACrDM,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEK,KAAK,CAACZ,WAAW,CAAC,CACrDK,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEK,KAAK,CAACnC,QAAQ,CAAC,CAChD4B,QAAQ,CAACE,MAAM,CAAC,mBAAmB,CAAEK,KAAK,CAAClC,QAAQ,CAAC,CACpD2B,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAEpF,QAAQ,CAAC,CAE1C,KAAM,CAAAoC,QAAQ,CAAG,KAAM,CAAAtI,kBAAkB,CAAC8L,OAAO,CAACV,QAAQ,CAAC,CAE3D,GAAI9C,QAAQ,CAAC5F,IAAI,CAAE,CACjB+I,UAAU,CAACR,IAAI,CAAC,CACdc,UAAU,CAAEzD,QAAQ,CAAC5F,IAAI,CAACqJ,UAAU,EAAI,GAAGJ,KAAK,CAACnC,QAAQ,IAAImC,KAAK,CAAClC,QAAQ,EAAE,CAC7ErH,aAAa,CAAEkG,QAAQ,CAAC5F,IAAI,CAACN,aAAa,EAAI,CAAC,CAC/CN,mBAAmB,CAAEwG,QAAQ,CAAC5F,IAAI,CAACZ,mBAAmB,EAAI,CAAC,CAC3DQ,WAAW,CAAEgG,QAAQ,CAAC5F,IAAI,CAACJ,WAAW,EAAI,EAAE,CAC5C;AACAqB,gBAAgB,CAAE2E,QAAQ,CAAC5F,IAAI,CAACiB,gBAAgB,CAChDE,WAAW,CAAEyE,QAAQ,CAAC5F,IAAI,CAACmB,WAAW,CACtCC,SAAS,CAAEwE,QAAQ,CAAC5F,IAAI,CAACoB,SAAS,CAClCC,aAAa,CAAEuE,QAAQ,CAAC5F,IAAI,CAACqB,aAAa,CAC1CC,uBAAuB,CAAEsE,QAAQ,CAAC5F,IAAI,CAACsB,uBACzC,CAAC,CAAC,CAEF,GAAI4G,eAAe,CAACrI,MAAM,CAAG,CAAC,CAAE,CAC9BzD,OAAO,CAAC4K,OAAO,CAAC,QAAQiC,KAAK,CAACd,UAAU,OAAO,CAAC,CAClD,CACF,CACF,CAEAlD,WAAW,CAAC,GAAG,CAAC,CAChBE,UAAU,CAAC4D,UAAU,CAAC,CACtB3M,OAAO,CAAC4K,OAAO,CAAC,SAAS+B,UAAU,CAAClJ,MAAM,SAAS,CAAC,CAEtD,CAAE,MAAOkG,KAAU,CAAE,KAAAuD,gBAAA,CAAAC,qBAAA,CACnBnN,OAAO,CAAC2J,KAAK,CAAC,EAAAuD,gBAAA,CAAAvD,KAAK,CAACH,QAAQ,UAAA0D,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBtJ,IAAI,UAAAuJ,qBAAA,iBAApBA,qBAAA,CAAsBrD,MAAM,GAAI,MAAM,CAAC,CACvD,CAAC,OAAS,CACRnB,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAyE,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,OAAO,CAAG9H,UAAU,GAAK,QAAQ,CAAGmB,YAAY,CAAIE,MAAM,EAAII,eAAgB,CAEpF,GAAIY,cAAc,GAAK,QAAQ,CAAE,CAC/B,MAAO,CAAAyF,OAAO,EAAIvF,iBAAiB,EAAIM,YAAY,EAAIE,gBAAgB,CACzE,CAAC,IAAM,CACL,MAAO,CAAA+E,OAAO,EAAI7F,cAAc,CAAC/D,MAAM,CAAG,CAAC,CAC7C,CACF,CAAC,CAID,mBACElC,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACK,KAAK,EAACyD,KAAK,CAAE,CAAE,CAACzC,KAAK,CAAE,CAAEa,QAAQ,CAAE,MAAM,CAAE+J,UAAU,CAAE,GAAG,CAAExK,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,CAAC,oEAAW,CAAO,CAAC,cACvGvB,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAAAP,QAAA,CAAC,8MAEvB,CAAM,CAAC,cAEPvB,IAAA,CAACtB,OAAO,GAAE,CAAC,cAGXsB,IAAA,CAAC9B,IAAI,EAAC2C,KAAK,CAAC,gCAAO,CAACqL,SAAS,CAAC,eAAe,CAAA3K,QAAA,cAC3CrB,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAACzH,IAAI,CAAC,OAAO,CAACrD,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eAChErB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,sCAAM,CAAM,CAAC,cAC1BrB,KAAA,CAAC/B,KAAK,CAACiO,KAAK,EACVlL,KAAK,CAAEgD,UAAW,CAClBmG,QAAQ,CAAGgC,CAAC,EAAKjH,aAAa,CAACiH,CAAC,CAACC,MAAM,CAACpL,KAAK,CAAE,CAC/CG,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAT,QAAA,eAExBvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAC,QAAQ,CAAAK,QAAA,CAAC,6BAAO,CAAO,CAAC,cACrCvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAC,OAAO,CAAAK,QAAA,CAAC,yCAAS,CAAO,CAAC,EAC3B,CAAC,EACX,CAAC,CAGL2C,UAAU,GAAK,QAAQ,eACtBhE,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,2DAAY,CAAM,CAAC,cAChCrB,KAAA,CAACK,OAAO,KAAK0J,WAAW,CAAE5I,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAT,QAAA,eAChDvB,IAAA,MAAGkM,SAAS,CAAC,sBAAsB,CAAA3K,QAAA,cACjCvB,IAAA,CAACb,aAAa,GAAE,CAAC,CAChB,CAAC,cACJa,IAAA,MAAGkM,SAAS,CAAC,iBAAiB,CAAA3K,QAAA,CAAC,mFAAgB,CAAG,CAAC,cACnDvB,IAAA,MAAGkM,SAAS,CAAC,iBAAiB,CAAA3K,QAAA,CAAC,uEAE/B,CAAG,CAAC,EACG,CAAC,EACP,CACN,CAGA2C,UAAU,GAAK,OAAO,eACrBhE,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAAC9K,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eACnDrB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5BrB,KAAA,CAAC7B,KAAK,CAAC+N,KAAK,EAACG,OAAO,MAAClL,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAEwK,OAAO,CAAE,MAAO,CAAE,CAAAjL,QAAA,eAC5DvB,IAAA,CAAC3B,KAAK,EACJ6C,KAAK,CAAEqE,MAAO,CACd8E,QAAQ,CAAGgC,CAAC,EAAK7G,SAAS,CAAC6G,CAAC,CAACC,MAAM,CAACpL,KAAK,CAAE,CAC3CuL,WAAW,CAAC,4BAAkB,CAC9BpL,KAAK,CAAE,CAAE0C,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACF/D,IAAA,CAACzB,MAAM,EACLuD,IAAI,CAAC,SAAS,CACd4K,OAAO,CAAExE,aAAc,CACvByE,OAAO,CAAE9G,eAAgB,CACzB+G,QAAQ,CAAE,CAACrH,MAAO,CAClBlE,KAAK,CAAE,CAAEwL,UAAU,CAAE,CAAE,CAAE,CAAAtL,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENrB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5BvB,IAAA,CAACpB,IAAI,EAACkO,QAAQ,CAAEjH,eAAgB,CAAAtE,QAAA,cAC9BvB,IAAA,CAAC1B,MAAM,EACL4C,KAAK,CAAEyE,eAAgB,CACvB0E,QAAQ,CAAEzE,kBAAmB,CAC7B6G,WAAW,CAAC,mCAAU,CACtBpL,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAM,CAAEgB,SAAS,CAAE,CAAE,CAAE,CACvC2K,OAAO,CAAE9G,eAAgB,CAAAtE,QAAA,CAExBkE,iBAAiB,CAACsH,GAAG,CAAEC,IAAI,eAC1BhN,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAE8L,IAAK,CAAAzL,QAAA,CAC5ByL,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CAAC,EACD,CACR,EACI,CAAC,CACJ,CAAC,cAGPhN,IAAA,CAAC9B,IAAI,EAAC2C,KAAK,CAAC,0BAAM,CAACqL,SAAS,CAAC,eAAe,CAAA3K,QAAA,cAC1CrB,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAACzH,IAAI,CAAC,OAAO,CAACrD,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eAChErB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBrB,KAAA,CAAC7B,KAAK,CAAC+N,KAAK,EAACG,OAAO,MAAClL,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAEwK,OAAO,CAAE,MAAO,CAAE,CAAAjL,QAAA,eAC5DvB,IAAA,CAAC3B,KAAK,EACJ6C,KAAK,CAAE6E,QAAS,CAChBsE,QAAQ,CAAGgC,CAAC,EAAKrG,WAAW,CAACqG,CAAC,CAACC,MAAM,CAACpL,KAAK,CAAE,CAC7CuL,WAAW,CAAC,4BAAkB,CAC9BpL,KAAK,CAAE,CAAE0C,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACF/D,IAAA,CAACzB,MAAM,EACLuD,IAAI,CAAC,SAAS,CACd4K,OAAO,CAAEhE,eAAgB,CACzBiE,OAAO,CAAEtG,aAAc,CACvBuG,QAAQ,CAAE,CAAC7G,QAAS,CACpB1E,KAAK,CAAE,CAAEwL,UAAU,CAAE,CAAE,CAAE,CAAAtL,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENrB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBrB,KAAA,CAAC/B,KAAK,CAACiO,KAAK,EACVlL,KAAK,CAAEqF,cAAe,CACtB8D,QAAQ,CAAGgC,CAAC,EAAK7F,iBAAiB,CAAC6F,CAAC,CAACC,MAAM,CAACpL,KAAK,CAAE,CACnDG,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAT,QAAA,eAExBvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAC,QAAQ,CAAAK,QAAA,CAAC,sCAAM,CAAO,CAAC,cACpCvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAC,UAAU,CAAAK,QAAA,CAAC,kDAAQ,CAAO,CAAC,EAC7B,CAAC,EACX,CAAC,CAELgF,cAAc,GAAK,QAAQ,cAC1BrG,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BvB,IAAA,CAACpB,IAAI,EAACkO,QAAQ,CAAEzG,aAAc,CAAA9E,QAAA,cAC5BvB,IAAA,CAAC1B,MAAM,EACL4C,KAAK,CAAEuF,iBAAkB,CACzB4D,QAAQ,CAAER,qBAAsB,CAChC4C,WAAW,CAAC,kMAAkC,CAC9CpL,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAM,CAAEgB,SAAS,CAAE,CAAE,CAAE,CACvC2K,OAAO,CAAEtG,aAAc,CAAA9E,QAAA,CAEtB0E,iBAAiB,CAAC8G,GAAG,CAAEC,IAAI,eAC1BhN,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAE8L,IAAK,CAAAzL,QAAA,CAC5ByL,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,CAENvG,iBAAiB,eAChBzG,IAAA,QAAKqB,KAAK,CAAE,CAAEW,SAAS,CAAE,EAAG,CAAE,CAAAT,QAAA,cAC5BrB,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAAC9K,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eACnDrB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAAAP,QAAA,CAAC,kDAAQ,CAAM,CAAC,cACtCvB,IAAA,CAACpB,IAAI,EAACkO,QAAQ,CAAEjF,oBAAqB,CAAAtG,QAAA,cACnCvB,IAAA,QAAKqB,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAC,CAAEiL,OAAO,CAAE,EAAE,CAAEC,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAA5L,QAAA,CACpFsG,oBAAoB,cACnB7H,IAAA,MAAAuB,QAAA,CAAG,iEAAa,CAAG,CAAC,cAEpBrB,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACErB,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACoF,kBAAkB,EAAI,KAAK,EAAI,CAAC,cAC3DzG,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACsF,kBAAkB,EAAI,KAAK,EAAI,CAAC,CAC5D,CAACM,mBAAmB,EAAIJ,YAAY,EAAIE,gBAAgB,eACvD/G,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eACErB,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACwF,YAAY,EAAI,CAAC,cAC1C7G,KAAA,MAAAqB,QAAA,eAAGvB,IAAA,WAAAuB,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAAC0F,gBAAgB,EAAI,CAAC,EAChD,CACH,EACD,CACH,CACE,CAAC,CACF,CAAC,EACJ,CAAC,CAELE,mBAAmB,eAClBjH,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,gFAAa,CAAM,CAAC,cACjCvB,IAAA,QAAKqB,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CAAAT,QAAA,cAC3BrB,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAAC9K,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eACnDvB,IAAA,CAAC1B,MAAM,EACL4C,KAAK,CAAE6F,YAAa,CACpBsD,QAAQ,CAAErD,eAAgB,CAC1ByF,WAAW,CAAC,8DAAY,CACxBpL,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,CAExBuD,eAAe,CAACiI,GAAG,CAAEK,IAAI,eACxBpN,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAEkM,IAAK,CAAA7L,QAAA,CAC5B6L,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CAERrG,YAAY,eACX/G,IAAA,CAAC1B,MAAM,EACL4C,KAAK,CAAE+F,gBAAiB,CACxBoD,QAAQ,CAAEnD,mBAAoB,CAC9BuF,WAAW,CAAE,YAAY1F,YAAY,OAAQ,CAC7C1F,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,CAExB,CAACwD,eAAe,CAACgC,YAAY,CAAiC,EAAI,EAAE,EAAEgG,GAAG,CAAEzD,QAAQ,eAClFtJ,IAAA,CAACQ,MAAM,EAAgBU,KAAK,CAAEoI,QAAS,CAAA/H,QAAA,CACpC+H,QAAQ,EADEA,QAEL,CACT,CAAC,CACI,CACT,EACI,CAAC,CACL,CAAC,EACH,CACN,EACI,CAAC,CACL,CACN,EACE,CAAC,cAENpJ,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,oEAAW,CAAM,CAAC,cAC/BvB,IAAA,CAACpB,IAAI,EAACkO,QAAQ,CAAEzG,aAAc,CAAA9E,QAAA,cAC5BvB,IAAA,CAAC1B,MAAM,EACL+O,IAAI,CAAC,UAAU,CACfnM,KAAK,CAAEiF,cAAe,CACtBkE,QAAQ,CAAEjE,iBAAkB,CAC5BqG,WAAW,CAAC,4FAAiB,CAC7BpL,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAM,CAAEgB,SAAS,CAAE,CAAE,CAAE,CACvC2K,OAAO,CAAEtG,aAAc,CAAA9E,QAAA,CAEtB0E,iBAAiB,CAAC8G,GAAG,CAAEC,IAAI,eAC1BhN,IAAA,CAACQ,MAAM,EAAYU,KAAK,CAAE8L,IAAK,CAAAzL,QAAA,CAC5ByL,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CACN,CAEA/G,iBAAiB,CAAC7D,MAAM,GAAK,CAAC,EAAI,CAACiE,aAAa,eAC/CrG,IAAA,CAAClB,KAAK,EACJH,OAAO,CAAC,4CAAS,CACjB2O,WAAW,CAAC,oOAA2C,CACvDxL,IAAI,CAAC,SAAS,CACdC,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,cAGP/B,IAAA,CAAC9B,IAAI,EAACgO,SAAS,CAAC,eAAe,CAACrL,KAAK,CAAC,0BAAM,CAAAU,QAAA,cAC1CrB,KAAA,CAACzB,KAAK,EAAC0N,SAAS,CAAC,UAAU,CAACzH,IAAI,CAAC,QAAQ,CAACrD,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAO,QAAA,eACjErB,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BrB,KAAA,CAAC/B,KAAK,CAACiO,KAAK,EACVlL,KAAK,CAAE8G,kBAAmB,CAC1BqC,QAAQ,CAAGgC,CAAC,EAAKpE,qBAAqB,CAACoE,CAAC,CAACC,MAAM,CAACpL,KAAK,CAAE,CACvDG,KAAK,CAAE,CAAEW,SAAS,CAAE,CAAE,CAAE,CACxB4K,QAAQ,CAAErG,cAAc,GAAK,UAAY;AAAA,CAAAhF,QAAA,eAEzCvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAE,IAAK,CAAAK,QAAA,cACjBrB,KAAA,CAACzB,KAAK,EAAA8C,QAAA,EAAC,kDAEL,cAAAvB,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAACT,KAAK,CAAE,CAAEa,QAAQ,CAAE,EAAG,CAAE,CAAAX,QAAA,CAAC,4EAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,cACRvB,IAAA,CAAC7B,KAAK,EAAC+C,KAAK,CAAE,KAAM,CAAAK,QAAA,cAClBrB,KAAA,CAACzB,KAAK,EAAA8C,QAAA,EAAC,0BAEL,cAAAvB,IAAA,CAACM,IAAI,EAACwB,IAAI,CAAC,WAAW,CAACT,KAAK,CAAE,CAAEa,QAAQ,CAAE,EAAG,CAAE,CAAAX,QAAA,CAAC,0GAEhD,CAAM,CAAC,EACF,CAAC,CACH,CAAC,EACG,CAAC,EACX,CAAC,CAELyG,kBAAkB,EAAIzB,cAAc,GAAK,QAAQ,eAChDvG,IAAA,CAAClB,KAAK,EACJH,OAAO,CAAC,sCAAQ,CAChB2O,WAAW,CAAC,0TAAsD,CAClExL,IAAI,CAAC,MAAM,CACXC,QAAQ,MACT,CACF,CAEAwE,cAAc,GAAK,UAAU,eAC5BvG,IAAA,CAAClB,KAAK,EACJH,OAAO,CAAC,4CAAS,CACjB2O,WAAW,CAAC,0KAA8B,CAC1CxL,IAAI,CAAC,SAAS,CACdC,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,cAGP7B,KAAA,CAAChC,IAAI,EAACgO,SAAS,CAAC,eAAe,CAAA3K,QAAA,eAC7BvB,IAAA,CAACzB,MAAM,EACLuD,IAAI,CAAC,SAAS,CACd4C,IAAI,CAAC,OAAO,CACZ6I,IAAI,cAAEvN,IAAA,CAACZ,kBAAkB,GAAE,CAAE,CAC7BsN,OAAO,CAAElC,qBAAsB,CAC/BmC,OAAO,CAAEtF,UAAW,CACpBuF,QAAQ,CAAE,CAACb,WAAW,CAAC,CAAE,CACzBG,SAAS,CAAC,eAAe,CAAA3K,QAAA,CAExB8F,UAAU,CAAG,SAAS,CAAG,SAAS,CAC7B,CAAC,CAGRA,UAAU,eACTnH,KAAA,QAAKgM,SAAS,CAAC,kBAAkB,CAAA3K,QAAA,eAC/BvB,IAAA,CAACM,IAAI,EAAAiB,QAAA,CAAC,gCAAK,CAAM,CAAC,cAClBvB,IAAA,CAACd,QAAQ,EAACsO,OAAO,CAAEjG,QAAS,CAACkG,MAAM,CAAC,QAAQ,CAAE,CAAC,EAC5C,CACN,EACG,CAAC,CAGNhG,OAAO,CAACrF,MAAM,CAAG,CAAC,eACjBpC,IAAA,CAAC9B,IAAI,EAAC2C,KAAK,CAAC,0BAAM,CAACqL,SAAS,CAAC,eAAe,CAAA3K,QAAA,CACzCkG,OAAO,CAACrF,MAAM,CAAG,CAAC,cACjB;AACAlC,KAAA,QAAAqB,QAAA,eACEvB,IAAA,CAACtB,OAAO,GAAE,CAAC,cACXsB,IAAA,CAACK,KAAK,EAACyD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAAC,4CAAO,CAAO,CAAC,cAChCrB,KAAA,QAAKmB,KAAK,CAAE,CAAEI,YAAY,CAAE,EAAG,CAAE,CAAAF,QAAA,eAC/BvB,IAAA,CAACM,IAAI,EAAC+B,MAAM,MAAAd,QAAA,CAAC,oEAAW,CAAM,CAAC,cAC/BvB,IAAA,CAAC1B,MAAM,EACL+C,KAAK,CAAE,CAAEL,KAAK,CAAE,MAAM,CAAEgB,SAAS,CAAE,CAAE,CAAE,CACvCyK,WAAW,CAAC,sCAAQ,CACpBvL,KAAK,CAAEyG,mBAAoB,CAC3B0C,QAAQ,CAAGnJ,KAAK,EAAK0G,sBAAsB,CAAC1G,KAAK,CAAE,CAAAK,QAAA,CAElDkG,OAAO,CAACsF,GAAG,CAAC,CAACpM,MAAM,CAAEkE,KAAK,gBACzB7E,IAAA,CAACQ,MAAM,EAAaU,KAAK,CAAE2D,KAAM,CAAAtD,QAAA,CAC9BZ,MAAM,CAACiL,UAAU,EADP/G,KAEL,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAGL4C,OAAO,CAACE,mBAAmB,CAAC,eAC3BzH,KAAA,QAAAqB,QAAA,eACErB,KAAA,CAACG,KAAK,EAACyD,KAAK,CAAE,CAAE,CAAAvC,QAAA,EAAC,gBAAI,CAACkG,OAAO,CAACE,mBAAmB,CAAC,CAACiE,UAAU,EAAQ,CAAC,cACtE5L,IAAA,CAACS,uBAAuB,EAACE,MAAM,CAAE8G,OAAO,CAACE,mBAAmB,CAAE,CAAE,CAAC,EAC9D,CACN,EACE,CAAC,cAEN;AACAzH,KAAA,QAAAqB,QAAA,eACErB,KAAA,CAACG,KAAK,EAACyD,KAAK,CAAE,CAAE,CAAAvC,QAAA,EAAC,6BAAO,CAACkG,OAAO,CAAC,CAAC,CAAC,CAACmE,UAAU,EAAQ,CAAC,cACvD5L,IAAA,CAACS,uBAAuB,EAACE,MAAM,CAAE8G,OAAO,CAAC,CAAC,CAAE,CAAE,CAAC,EAC5C,CACN,CACG,CACP,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}