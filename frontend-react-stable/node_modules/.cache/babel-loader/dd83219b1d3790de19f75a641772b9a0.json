{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = cumsum;\nfunction cumsum(values, valueof) {\n  var sum = 0,\n    index = 0;\n  return Float64Array.from(values, valueof === undefined ? v => sum += +v || 0 : v => sum += +valueof(v, index++, values) || 0);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "cumsum", "values", "valueof", "sum", "index", "Float64Array", "from", "undefined", "v"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/victory-vendor/lib-vendor/d3-array/src/cumsum.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = cumsum;\n\nfunction cumsum(values, valueof) {\n  var sum = 0,\n      index = 0;\n  return Float64Array.from(values, valueof === undefined ? v => sum += +v || 0 : v => sum += +valueof(v, index++, values) || 0);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,MAAM;AAExB,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC/B,IAAIC,GAAG,GAAG,CAAC;IACPC,KAAK,GAAG,CAAC;EACb,OAAOC,YAAY,CAACC,IAAI,CAACL,MAAM,EAAEC,OAAO,KAAKK,SAAS,GAAGC,CAAC,IAAIL,GAAG,IAAI,CAACK,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAIL,GAAG,IAAI,CAACD,OAAO,CAACM,CAAC,EAAEJ,KAAK,EAAE,EAAEH,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/H", "ignoreList": []}, "metadata": {}, "sourceType": "script"}