{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\n/* eslint react/prop-types: 0 */\nimport React from 'react';\nimport classNames from 'classnames';\nvar Pager = function Pager(props) {\n  var _classNames;\n  var prefixCls = \"\".concat(props.rootPrefixCls, \"-item\");\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(props.page), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), props.active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), !props.page), _defineProperty(_classNames, props.className, !!props.className), _classNames));\n  var handleClick = function handleClick() {\n    props.onClick(props.page);\n  };\n  var handleKeyPress = function handleKeyPress(e) {\n    props.onKeyPress(e, props.onClick, props.page);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", {\n    title: props.showTitle ? props.page : null,\n    className: cls,\n    onClick: handleClick,\n    onKeyPress: handleKeyPress,\n    tabIndex: \"0\"\n  }, props.itemRender(props.page, 'page', /*#__PURE__*/React.createElement(\"a\", {\n    rel: \"nofollow\"\n  }, props.page)));\n};\nexport default Pager;", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "Pager", "props", "_classNames", "prefixCls", "concat", "rootPrefixCls", "cls", "page", "active", "className", "handleClick", "onClick", "handleKeyPress", "e", "onKeyPress", "createElement", "title", "showTitle", "tabIndex", "itemRender", "rel"], "sources": ["/home/<USER>/frontend-react-stable/node_modules/rc-pagination/es/Pager.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n\n/* eslint react/prop-types: 0 */\nimport React from 'react';\nimport classNames from 'classnames';\n\nvar Pager = function Pager(props) {\n  var _classNames;\n\n  var prefixCls = \"\".concat(props.rootPrefixCls, \"-item\");\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(props.page), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), props.active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), !props.page), _defineProperty(_classNames, props.className, !!props.className), _classNames));\n\n  var handleClick = function handleClick() {\n    props.onClick(props.page);\n  };\n\n  var handleKeyPress = function handleKeyPress(e) {\n    props.onKeyPress(e, props.onClick, props.page);\n  };\n\n  return /*#__PURE__*/React.createElement(\"li\", {\n    title: props.showTitle ? props.page : null,\n    className: cls,\n    onClick: handleClick,\n    onKeyPress: handleKeyPress,\n    tabIndex: \"0\"\n  }, props.itemRender(props.page, 'page', /*#__PURE__*/React.createElement(\"a\", {\n    rel: \"nofollow\"\n  }, props.page)));\n};\n\nexport default Pager;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;;AAEvE;AACA,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AAEnC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAE;EAChC,IAAIC,WAAW;EAEf,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACH,KAAK,CAACI,aAAa,EAAE,OAAO,CAAC;EACvD,IAAIC,GAAG,GAAGP,UAAU,CAACI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACD,SAAS,EAAE,GAAG,CAAC,CAACC,MAAM,CAACH,KAAK,CAACM,IAAI,CAAC,GAAGL,WAAW,GAAG,CAAC,CAAC,EAAEL,eAAe,CAACK,WAAW,EAAE,EAAE,CAACE,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAEF,KAAK,CAACO,MAAM,CAAC,EAAEX,eAAe,CAACK,WAAW,EAAE,EAAE,CAACE,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAE,CAACF,KAAK,CAACM,IAAI,CAAC,EAAEV,eAAe,CAACK,WAAW,EAAED,KAAK,CAACQ,SAAS,EAAE,CAAC,CAACR,KAAK,CAACQ,SAAS,CAAC,EAAEP,WAAW,CAAC,CAAC;EAE3U,IAAIQ,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvCT,KAAK,CAACU,OAAO,CAACV,KAAK,CAACM,IAAI,CAAC;EAC3B,CAAC;EAED,IAAIK,cAAc,GAAG,SAASA,cAAcA,CAACC,CAAC,EAAE;IAC9CZ,KAAK,CAACa,UAAU,CAACD,CAAC,EAAEZ,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACM,IAAI,CAAC;EAChD,CAAC;EAED,OAAO,aAAaT,KAAK,CAACiB,aAAa,CAAC,IAAI,EAAE;IAC5CC,KAAK,EAAEf,KAAK,CAACgB,SAAS,GAAGhB,KAAK,CAACM,IAAI,GAAG,IAAI;IAC1CE,SAAS,EAAEH,GAAG;IACdK,OAAO,EAAED,WAAW;IACpBI,UAAU,EAAEF,cAAc;IAC1BM,QAAQ,EAAE;EACZ,CAAC,EAAEjB,KAAK,CAACkB,UAAU,CAAClB,KAAK,CAACM,IAAI,EAAE,MAAM,EAAE,aAAaT,KAAK,CAACiB,aAAa,CAAC,GAAG,EAAE;IAC5EK,GAAG,EAAE;EACP,CAAC,EAAEnB,KAAK,CAACM,IAAI,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,eAAeP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module"}