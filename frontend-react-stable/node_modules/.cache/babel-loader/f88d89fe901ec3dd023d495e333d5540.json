{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Radio,Upload,Input,Select,Button,Alert,Progress,Typography,Space,Divider,message,Spin}from'antd';import{InboxOutlined,PlayCircleOutlined}from'@ant-design/icons';import{dataCleaningAPI}from'../services/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const{Dragger}=Upload;const{Option}=Select;const DataCleaningPage=()=>{const[dataSource,setDataSource]=useState('upload');const[uploadedFiles,setUploadedFiles]=useState([]);const[folderPath,setFolderPath]=useState('');const[availableFiles,setAvailableFiles]=useState([]);const[selectedFiles,setSelectedFiles]=useState([]);const[outputDir,setOutputDir]=useState('');const[loading,setLoading]=useState(false);const[filesLoading,setFilesLoading]=useState(false);const[progress,setProgress]=useState(0);const[result,setResult]=useState(null);// 获取本地文件列表\nconst fetchLocalFiles=async()=>{if(!folderPath)return;setFilesLoading(true);try{const response=await dataCleaningAPI.listFiles(folderPath);setAvailableFiles(response.data.files||[]);}catch(error){var _error$response,_error$response$data;message.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'获取文件列表失败');setAvailableFiles([]);}finally{setFilesLoading(false);}};// 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\nuseEffect(()=>{if(dataSource==='local'&&folderPath&&folderPath.length>3){// 至少输入4个字符才开始请求\nconst timer=setTimeout(()=>{fetchLocalFiles();},1500);// 1.5秒延迟，给用户足够时间输入完整路径\nreturn()=>clearTimeout(timer);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[dataSource,folderPath]);// 文件上传配置\nconst uploadProps={name:'files',multiple:true,accept:'.txt',beforeUpload:()=>false,// 阻止自动上传\nonChange:info=>{setUploadedFiles(info.fileList);},onDrop:e=>{console.log('Dropped files',e.dataTransfer.files);}};// 执行数据清洗\nconst handleCleanData=async()=>{// 验证输入\nif(dataSource==='upload'&&uploadedFiles.length===0){message.error('请上传至少一个文件');return;}if(dataSource==='local'&&(!folderPath||selectedFiles.length===0)){message.error('请提供文件夹路径并选择至少一个文件');return;}setLoading(true);setProgress(0);setResult(null);try{let response;if(dataSource==='upload'){const formData=new FormData();uploadedFiles.forEach(file=>{formData.append('files',file.originFileObj);});formData.append('output_dir',outputDir);// 模拟进度更新\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+10;});},500);response=await dataCleaningAPI.cleanData(formData);clearInterval(progressInterval);}else{// 本地文件处理\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=90){clearInterval(progressInterval);return prev;}return prev+10;});},500);response=await dataCleaningAPI.cleanDataLocal({folder_path:folderPath,selected_files:selectedFiles,output_dir:outputDir});clearInterval(progressInterval);}setProgress(100);setResult(response.data);message.success('数据清洗完成！');}catch(error){var _error$response2,_error$response2$data;message.error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.detail)||'数据清洗失败');}finally{setLoading(false);}};const isFormValid=()=>{if(dataSource==='upload'){return uploadedFiles.length>0;}else{return folderPath&&selectedFiles.length>0;}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:3,style:{fontSize:'20px',fontWeight:600,marginBottom:'8px'},children:\"\\u6D41\\u91CF\\u6570\\u636E\\u5206\\u6790\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\u6216\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u4E2D\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\\uFF0C\\u5206\\u6790\\u540E\\u751F\\u6210CSV\\u6587\\u4EF6\\u5E76\\u4FDD\\u5B58\\u5230\\u6307\\u5B9A\\u76EE\\u5F55\\u3002\"}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(Card,{title:\"\\u6570\\u636E\\u5206\\u6790\",className:\"function-card\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",size:\"large\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6D41\\u91CF\\u6570\\u636E\\u6E90\\uFF1A\"}),/*#__PURE__*/_jsxs(Radio.Group,{value:dataSource,onChange:e=>setDataSource(e.target.value),style:{marginTop:8},children:[/*#__PURE__*/_jsx(Radio,{value:\"upload\",children:\"\\u4E0A\\u4F20\\u6D41\\u91CF\\u6570\\u636ETXT\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Radio,{value:\"local\",children:\"\\u9009\\u62E9\\u672C\\u5730\\u76EE\\u5F55\\u6587\\u4EF6\"})]})]}),dataSource==='upload'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u4E0A\\u4F20\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsxs(Dragger,{...uploadProps,style:{marginTop:8},children:[/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-drag-icon\",children:/*#__PURE__*/_jsx(InboxOutlined,{})}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-text\",children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u6587\\u4EF6\\u5230\\u6B64\\u533A\\u57DF\\u4E0A\\u4F20\"}),/*#__PURE__*/_jsx(\"p\",{className:\"ant-upload-hint\",children:\"\\u652F\\u6301\\u5355\\u4E2A\\u6216\\u6279\\u91CF\\u4E0A\\u4F20TXT\\u683C\\u5F0F\\u7684\\u6D41\\u91CF\\u6570\\u636E\\u6587\\u4EF6\"})]})]}),dataSource==='local'&&/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u672C\\u5730\\u76EE\\u5F55\\u8DEF\\u5F84\\uFF1A\"}),/*#__PURE__*/_jsxs(Input.Group,{compact:true,style:{marginTop:8,display:'flex'},children:[/*#__PURE__*/_jsx(Input,{value:folderPath,onChange:e=>setFolderPath(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/aizhinengqingxicepingdaliu\",style:{flex:1}}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchLocalFiles,loading:filesLoading,disabled:!folderPath,style:{marginLeft:8},children:\"\\u5237\\u65B0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u9009\\u62E9\\u6587\\u4EF6\\uFF1A\"}),/*#__PURE__*/_jsx(Spin,{spinning:filesLoading,children:/*#__PURE__*/_jsx(Select,{mode:\"multiple\",value:selectedFiles,onChange:setSelectedFiles,placeholder:\"\\u8BF7\\u9009\\u62E9TXT\\u6587\\u4EF6\",style:{width:'100%',marginTop:8},loading:filesLoading,children:availableFiles.map(file=>/*#__PURE__*/_jsx(Option,{value:file,children:file},file))})})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"CSV\\u8F93\\u51FA\\u76EE\\u5F55\\uFF1A\"}),/*#__PURE__*/_jsx(Input,{value:outputDir,onChange:e=>setOutputDir(e.target.value),placeholder:\"\\u4F8B\\u5982: /data/output\",style:{marginTop:8}})]}),/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"large\",icon:/*#__PURE__*/_jsx(PlayCircleOutlined,{}),onClick:handleCleanData,loading:loading,disabled:!isFormValid(),className:\"action-button\",children:loading?'正在处理...':'执行流量分析'}),loading&&/*#__PURE__*/_jsxs(\"div\",{className:\"progress-section\",children:[/*#__PURE__*/_jsx(Text,{children:\"\\u5904\\u7406\\u8FDB\\u5EA6\\uFF1A\"}),/*#__PURE__*/_jsx(Progress,{percent:progress,status:\"active\"})]}),result&&/*#__PURE__*/_jsx(Alert,{message:\"\\u5904\\u7406\\u5B8C\\u6210\",description:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5904\\u7406\\u7ED3\\u679C\\uFF1A\",result.message]}),result.output_file&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u8F93\\u51FA\\u6587\\u4EF6\\uFF1A\",result.output_file]}),result.processed_files&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u5904\\u7406\\u7684\\u6587\\u4EF6\\u6570\\uFF1A\",result.processed_files]}),result.total_rows&&/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u603B\\u884C\\u6570\\uFF1A\",result.total_rows]})]}),type:\"success\",showIcon:true})]})})]});};export default DataCleaningPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Radio", "Upload", "Input", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "Progress", "Typography", "Space", "Divider", "message", "Spin", "InboxOutlined", "PlayCircleOutlined", "dataCleaningAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "<PERSON><PERSON>", "Option", "DataCleaningPage", "dataSource", "setDataSource", "uploadedFiles", "setUploadedFiles", "folderPath", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "loading", "setLoading", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "fetchLocalFiles", "response", "listFiles", "data", "files", "error", "_error$response", "_error$response$data", "detail", "length", "timer", "setTimeout", "clearTimeout", "uploadProps", "name", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "console", "log", "dataTransfer", "handleCleanData", "formData", "FormData", "for<PERSON>ach", "file", "append", "originFileObj", "progressInterval", "setInterval", "prev", "clearInterval", "cleanData", "cleanDataLocal", "folder_path", "selected_files", "output_dir", "success", "_error$response2", "_error$response2$data", "isFormValid", "children", "level", "style", "fontSize", "fontWeight", "marginBottom", "type", "title", "className", "direction", "size", "width", "strong", "Group", "value", "target", "marginTop", "compact", "display", "placeholder", "flex", "onClick", "disabled", "marginLeft", "spinning", "mode", "map", "icon", "percent", "status", "description", "output_file", "processed_files", "total_rows", "showIcon"], "sources": ["/home/<USER>/frontend-react-stable/src/pages/DataCleaningPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('upload');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n    \n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n              <Radio value=\"local\">选择本地目录文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n              \n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n          {/* 结果展示 */}\n          {result && (\n            <Alert\n              message=\"处理完成\"\n              description={\n                <div>\n                  <p>处理结果：{result.message}</p>\n                  {result.output_file && <p>输出文件：{result.output_file}</p>}\n                  {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                  {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,QAAQ,CACRC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,KACC,MAAM,CACb,OAASC,aAAa,CAAEC,kBAAkB,KAAQ,mBAAmB,CACrE,OAASC,eAAe,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGb,UAAU,CAClC,KAAM,CAAEc,OAAQ,CAAC,CAAGpB,MAAM,CAC1B,KAAM,CAAEqB,MAAO,CAAC,CAAGnB,MAAM,CAEzB,KAAM,CAAAoB,gBAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAqB,QAAQ,CAAC,CAC1E,KAAM,CAAC6B,aAAa,CAAEC,gBAAgB,CAAC,CAAG9B,QAAQ,CAAQ,EAAE,CAAC,CAC7D,KAAM,CAAC+B,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiC,cAAc,CAAEC,iBAAiB,CAAC,CAAGlC,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAACmC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpC,QAAQ,CAAW,EAAE,CAAC,CAChE,KAAM,CAACqC,SAAS,CAAEC,YAAY,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACuC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyC,YAAY,CAAEC,eAAe,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC2C,QAAQ,CAAEC,WAAW,CAAC,CAAG5C,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAC6C,MAAM,CAAEC,SAAS,CAAC,CAAG9C,QAAQ,CAAM,IAAI,CAAC,CAE/C;AACA,KAAM,CAAA+C,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAChB,UAAU,CAAE,OAEjBW,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAA/B,eAAe,CAACgC,SAAS,CAAClB,UAAU,CAAC,CAC5DG,iBAAiB,CAACc,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAI,EAAE,CAAC,CAC9C,CAAE,MAAOC,KAAU,CAAE,KAAAC,eAAA,CAAAC,oBAAA,CACnBzC,OAAO,CAACuC,KAAK,CAAC,EAAAC,eAAA,CAAAD,KAAK,CAACJ,QAAQ,UAAAK,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBH,IAAI,UAAAI,oBAAA,iBAApBA,oBAAA,CAAsBC,MAAM,GAAI,UAAU,CAAC,CACzDrB,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,OAAS,CACRQ,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACAzC,SAAS,CAAC,IAAM,CACd,GAAI0B,UAAU,GAAK,OAAO,EAAII,UAAU,EAAIA,UAAU,CAACyB,MAAM,CAAG,CAAC,CAAE,CAAE;AACnE,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMY,YAAY,CAACF,KAAK,CAAC,CAClC,CACA;AACF,CAAC,CAAE,CAAC9B,UAAU,CAAEI,UAAU,CAAC,CAAC,CAE5B;AACA,KAAM,CAAA6B,WAAW,CAAG,CAClBC,IAAI,CAAE,OAAO,CACbC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAEA,CAAA,GAAM,KAAK,CAAE;AAC3BC,QAAQ,CAAGC,IAAS,EAAK,CACvBpC,gBAAgB,CAACoC,IAAI,CAACC,QAAQ,CAAC,CACjC,CAAC,CACDC,MAAM,CAAGC,CAAM,EAAK,CAClBC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEF,CAAC,CAACG,YAAY,CAACrB,KAAK,CAAC,CACpD,CACF,CAAC,CAED;AACA,KAAM,CAAAsB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC;AACA,GAAI9C,UAAU,GAAK,QAAQ,EAAIE,aAAa,CAAC2B,MAAM,GAAK,CAAC,CAAE,CACzD3C,OAAO,CAACuC,KAAK,CAAC,WAAW,CAAC,CAC1B,OACF,CAEA,GAAIzB,UAAU,GAAK,OAAO,GAAK,CAACI,UAAU,EAAII,aAAa,CAACqB,MAAM,GAAK,CAAC,CAAC,CAAE,CACzE3C,OAAO,CAACuC,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEAZ,UAAU,CAAC,IAAI,CAAC,CAChBI,WAAW,CAAC,CAAC,CAAC,CACdE,SAAS,CAAC,IAAI,CAAC,CAEf,GAAI,CACF,GAAI,CAAAE,QAAQ,CAEZ,GAAIrB,UAAU,GAAK,QAAQ,CAAE,CAC3B,KAAM,CAAA+C,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/B9C,aAAa,CAAC+C,OAAO,CAAEC,IAAI,EAAK,CAC9BH,QAAQ,CAACI,MAAM,CAAC,OAAO,CAAED,IAAI,CAACE,aAAa,CAAC,CAC9C,CAAC,CAAC,CACFL,QAAQ,CAACI,MAAM,CAAC,YAAY,CAAEzC,SAAS,CAAC,CAExC;AACA,KAAM,CAAA2C,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCrC,WAAW,CAAEsC,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACdC,aAAa,CAACH,gBAAgB,CAAC,CAC/B,MAAO,CAAAE,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEPlC,QAAQ,CAAG,KAAM,CAAA/B,eAAe,CAACmE,SAAS,CAACV,QAAQ,CAAC,CACpDS,aAAa,CAACH,gBAAgB,CAAC,CACjC,CAAC,IAAM,CACL;AACA,KAAM,CAAAA,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCrC,WAAW,CAAEsC,IAAI,EAAK,CACpB,GAAIA,IAAI,EAAI,EAAE,CAAE,CACdC,aAAa,CAACH,gBAAgB,CAAC,CAC/B,MAAO,CAAAE,IAAI,CACb,CACA,MAAO,CAAAA,IAAI,CAAG,EAAE,CAClB,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAC,CAEPlC,QAAQ,CAAG,KAAM,CAAA/B,eAAe,CAACoE,cAAc,CAAC,CAC9CC,WAAW,CAAEvD,UAAU,CACvBwD,cAAc,CAAEpD,aAAa,CAC7BqD,UAAU,CAAEnD,SACd,CAAC,CAAC,CACF8C,aAAa,CAACH,gBAAgB,CAAC,CACjC,CAEApC,WAAW,CAAC,GAAG,CAAC,CAChBE,SAAS,CAACE,QAAQ,CAACE,IAAI,CAAC,CACxBrC,OAAO,CAAC4E,OAAO,CAAC,SAAS,CAAC,CAE5B,CAAE,MAAOrC,KAAU,CAAE,KAAAsC,gBAAA,CAAAC,qBAAA,CACnB9E,OAAO,CAACuC,KAAK,CAAC,EAAAsC,gBAAA,CAAAtC,KAAK,CAACJ,QAAQ,UAAA0C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBxC,IAAI,UAAAyC,qBAAA,iBAApBA,qBAAA,CAAsBpC,MAAM,GAAI,QAAQ,CAAC,CACzD,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoD,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIjE,UAAU,GAAK,QAAQ,CAAE,CAC3B,MAAO,CAAAE,aAAa,CAAC2B,MAAM,CAAG,CAAC,CACjC,CAAC,IAAM,CACL,MAAO,CAAAzB,UAAU,EAAII,aAAa,CAACqB,MAAM,CAAG,CAAC,CAC/C,CACF,CAAC,CAED,mBACEnC,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACG,KAAK,EAACwE,KAAK,CAAE,CAAE,CAACC,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAG,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,CAAC,sCAAM,CAAO,CAAC,cAClG1E,IAAA,CAACI,IAAI,EAAC4E,IAAI,CAAC,WAAW,CAAAN,QAAA,CAAC,qPAEvB,CAAM,CAAC,cAEP1E,IAAA,CAACP,OAAO,GAAE,CAAC,cAEXO,IAAA,CAACjB,IAAI,EAACkG,KAAK,CAAC,0BAAM,CAACC,SAAS,CAAC,eAAe,CAAAR,QAAA,cAC1CxE,KAAA,CAACV,KAAK,EAAC2F,SAAS,CAAC,UAAU,CAACC,IAAI,CAAC,OAAO,CAACR,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eAEhExE,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAAAZ,QAAA,CAAC,kDAAQ,CAAM,CAAC,cAC5BxE,KAAA,CAAClB,KAAK,CAACuG,KAAK,EACVC,KAAK,CAAEhF,UAAW,CAClBsC,QAAQ,CAAGI,CAAC,EAAKzC,aAAa,CAACyC,CAAC,CAACuC,MAAM,CAACD,KAAK,CAAE,CAC/CZ,KAAK,CAAE,CAAEc,SAAS,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAExB1E,IAAA,CAAChB,KAAK,EAACwG,KAAK,CAAC,QAAQ,CAAAd,QAAA,CAAC,qDAAW,CAAO,CAAC,cACzC1E,IAAA,CAAChB,KAAK,EAACwG,KAAK,CAAC,OAAO,CAAAd,QAAA,CAAC,kDAAQ,CAAO,CAAC,EAC1B,CAAC,EACX,CAAC,CAGLlE,UAAU,GAAK,QAAQ,eACtBN,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzBxE,KAAA,CAACG,OAAO,KAAKoC,WAAW,CAAEmC,KAAK,CAAE,CAAEc,SAAS,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAChD1E,IAAA,MAAGkF,SAAS,CAAC,sBAAsB,CAAAR,QAAA,cACjC1E,IAAA,CAACJ,aAAa,GAAE,CAAC,CAChB,CAAC,cACJI,IAAA,MAAGkF,SAAS,CAAC,iBAAiB,CAAAR,QAAA,CAAC,gFAAa,CAAG,CAAC,cAChD1E,IAAA,MAAGkF,SAAS,CAAC,iBAAiB,CAAAR,QAAA,CAAC,iHAE/B,CAAG,CAAC,EACG,CAAC,EACP,CACN,CAGAlE,UAAU,GAAK,OAAO,eACrBN,KAAA,CAACV,KAAK,EAAC2F,SAAS,CAAC,UAAU,CAACP,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eACnDxE,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAAAZ,QAAA,CAAC,4CAAO,CAAM,CAAC,cAC3BxE,KAAA,CAAChB,KAAK,CAACqG,KAAK,EAACI,OAAO,MAACf,KAAK,CAAE,CAAEc,SAAS,CAAE,CAAC,CAAEE,OAAO,CAAE,MAAO,CAAE,CAAAlB,QAAA,eAC5D1E,IAAA,CAACd,KAAK,EACJsG,KAAK,CAAE5E,UAAW,CAClBkC,QAAQ,CAAGI,CAAC,EAAKrC,aAAa,CAACqC,CAAC,CAACuC,MAAM,CAACD,KAAK,CAAE,CAC/CK,WAAW,CAAC,gDAAsC,CAClDjB,KAAK,CAAE,CAAEkB,IAAI,CAAE,CAAE,CAAE,CACpB,CAAC,cACF9F,IAAA,CAACZ,MAAM,EACL4F,IAAI,CAAC,SAAS,CACde,OAAO,CAAEnE,eAAgB,CACzBR,OAAO,CAAEE,YAAa,CACtB0E,QAAQ,CAAE,CAACpF,UAAW,CACtBgE,KAAK,CAAE,CAAEqB,UAAU,CAAE,CAAE,CAAE,CAAAvB,QAAA,CAC1B,cAED,CAAQ,CAAC,EACE,CAAC,EACX,CAAC,cAENxE,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAAAZ,QAAA,CAAC,gCAAK,CAAM,CAAC,cACzB1E,IAAA,CAACL,IAAI,EAACuG,QAAQ,CAAE5E,YAAa,CAAAoD,QAAA,cAC3B1E,IAAA,CAACb,MAAM,EACLgH,IAAI,CAAC,UAAU,CACfX,KAAK,CAAExE,aAAc,CACrB8B,QAAQ,CAAE7B,gBAAiB,CAC3B4E,WAAW,CAAC,mCAAU,CACtBjB,KAAK,CAAE,CAAES,KAAK,CAAE,MAAM,CAAEK,SAAS,CAAE,CAAE,CAAE,CACvCtE,OAAO,CAAEE,YAAa,CAAAoD,QAAA,CAErB5D,cAAc,CAACsF,GAAG,CAAE1C,IAAI,eACvB1D,IAAA,CAACM,MAAM,EAAYkF,KAAK,CAAE9B,IAAK,CAAAgB,QAAA,CAC5BhB,IAAI,EADMA,IAEL,CACT,CAAC,CACI,CAAC,CACL,CAAC,EACJ,CAAC,EACD,CACR,cAGDxD,KAAA,QAAAwE,QAAA,eACE1E,IAAA,CAACI,IAAI,EAACkF,MAAM,MAAAZ,QAAA,CAAC,mCAAQ,CAAM,CAAC,cAC5B1E,IAAA,CAACd,KAAK,EACJsG,KAAK,CAAEtE,SAAU,CACjB4B,QAAQ,CAAGI,CAAC,EAAK/B,YAAY,CAAC+B,CAAC,CAACuC,MAAM,CAACD,KAAK,CAAE,CAC9CK,WAAW,CAAC,4BAAkB,CAC9BjB,KAAK,CAAE,CAAEc,SAAS,CAAE,CAAE,CAAE,CACzB,CAAC,EACC,CAAC,cAGN1F,IAAA,CAACZ,MAAM,EACL4F,IAAI,CAAC,SAAS,CACdI,IAAI,CAAC,OAAO,CACZiB,IAAI,cAAErG,IAAA,CAACH,kBAAkB,GAAE,CAAE,CAC7BkG,OAAO,CAAEzC,eAAgB,CACzBlC,OAAO,CAAEA,OAAQ,CACjB4E,QAAQ,CAAE,CAACvB,WAAW,CAAC,CAAE,CACzBS,SAAS,CAAC,eAAe,CAAAR,QAAA,CAExBtD,OAAO,CAAG,SAAS,CAAG,QAAQ,CACzB,CAAC,CAGRA,OAAO,eACNlB,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAR,QAAA,eAC/B1E,IAAA,CAACI,IAAI,EAAAsE,QAAA,CAAC,gCAAK,CAAM,CAAC,cAClB1E,IAAA,CAACV,QAAQ,EAACgH,OAAO,CAAE9E,QAAS,CAAC+E,MAAM,CAAC,QAAQ,CAAE,CAAC,EAC5C,CACN,CAGA7E,MAAM,eACL1B,IAAA,CAACX,KAAK,EACJK,OAAO,CAAC,0BAAM,CACd8G,WAAW,cACTtG,KAAA,QAAAwE,QAAA,eACExE,KAAA,MAAAwE,QAAA,EAAG,gCAAK,CAAChD,MAAM,CAAChC,OAAO,EAAI,CAAC,CAC3BgC,MAAM,CAAC+E,WAAW,eAAIvG,KAAA,MAAAwE,QAAA,EAAG,gCAAK,CAAChD,MAAM,CAAC+E,WAAW,EAAI,CAAC,CACtD/E,MAAM,CAACgF,eAAe,eAAIxG,KAAA,MAAAwE,QAAA,EAAG,4CAAO,CAAChD,MAAM,CAACgF,eAAe,EAAI,CAAC,CAChEhF,MAAM,CAACiF,UAAU,eAAIzG,KAAA,MAAAwE,QAAA,EAAG,0BAAI,CAAChD,MAAM,CAACiF,UAAU,EAAI,CAAC,EACjD,CACN,CACD3B,IAAI,CAAC,SAAS,CACd4B,QAAQ,MACT,CACF,EACI,CAAC,CACJ,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}