function cssBlankPseudo(e,t){var r=Object(t).className,n=Object(t).attr||"blank",o=Object(t).force;try{if(e.querySelector(":blank"),!o)return}catch(e){}var a=(e.ownerDocument||e).defaultView;i(a.HTMLInputElement),i(a.HTMLSelectElement),i(a.HTMLTextAreaElement);var c="input,select,textarea";function s(){this.value?(n&&this.removeAttribute(n),r&&this.classList.remove(r),this.removeAttribute("blank")):(n&&this.setAttribute("blank",n),r&&this.classList.add(r))}function i(e){var t=Object.getOwnPropertyDescriptor(e.prototype,"value"),r=t.set;t.set=function(e){r.apply(this,arguments),s.apply(this)},Object.defineProperty(e.prototype,"value",t)}Array.prototype.forEach.call(e.querySelectorAll(c),function(e){e.addEventListener("input",s),s.call(e)}),new MutationObserver(function(e){e.forEach(function(e){e.addedNodes&&e.addedNodes.forEach(function(e){1===e.nodeType&&e.matches(c)&&(e.addEventListener("input",s),s.call(e))}),e.removedNodes&&e.removedNodes.forEach(function(e){1===e.nodeType&&e.matches(c)&&e.removeEventListener("input",s)})})}).observe(e,{childList:!0,subtree:!0})}
