{"version": 3, "file": "index.mjs", "sources": ["src/browser.js"], "sourcesContent": ["export default function cssBlankPseudo(document, opts) {\n\t// configuration\n\tconst className = Object(opts).className;\n\tconst attr = Object(opts).attr || 'blank';\n\tconst force = Object(opts).force;\n\n\ttry {\n\t\tdocument.querySelector(':blank');\n\n\t\tif (!force) {\n\t\t\treturn;\n\t\t}\n\t} catch (ignoredError) { /* do nothing and continue */ }\n\n\t// observe value changes on <input>, <select>, and <textarea>\n\tconst window = (document.ownerDocument || document).defaultView;\n\n\tobserveValueOfHTMLElement(window.HTMLInputElement);\n\tobserveValueOfHTMLElement(window.HTMLSelectElement);\n\tobserveValueOfHTMLElement(window.HTMLTextAreaElement);\n\n\t// form control elements selector\n\tconst selector = 'input,select,textarea';\n\n\t// conditionally update all form control elements\n\tArray.prototype.forEach.call(\n\t\tdocument.querySelectorAll(selector),\n\t\tnode => {\n\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t}\n\t);\n\n\t// conditionally observe added or unobserve removed form control elements\n\tnew MutationObserver(mutationsList => {\n\t\tmutationsList.forEach(mutation => {\n\t\t\tif (mutation.addedNodes) {\n\t\t\t\tmutation.addedNodes.forEach(\n\t\t\t\t\tnode => {\n\t\t\t\t\t\tif (node.nodeType === 1 && node.matches(selector)) {\n\t\t\t\t\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (mutation.removedNodes) {\n\t\t\t\tmutation.removedNodes.forEach(\n\t\t\t\t\tnode => {\n\t\t\t\t\t\tif (node.nodeType === 1 && node.matches(selector)) {\n\t\t\t\t\t\t\tnode.removeEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t}\n\t\t});\n\t}).observe(document, { childList: true, subtree: true });\n\n\t// update a form control element’s css-blank attribute\n\tfunction configureCssBlankAttribute() {\n\t\tif (this.value) {\n\t\t\tif (attr) {\n\t\t\t\tthis.removeAttribute(attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.remove(className);\n\t\t\t}\n\t\t\tthis.removeAttribute('blank');\n\t\t} else {\n\t\t\tif (attr) {\n\t\t\t\tthis.setAttribute('blank', attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.add(className);\n\t\t\t}\n\t\t}\n\t}\n\n\t// observe changes to the \"value\" property on an HTML Element\n\tfunction observeValueOfHTMLElement(HTMLElement) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'value');\n\t\tconst nativeSet = descriptor.set;\n\n\t\tdescriptor.set = function set(value) { // eslint-disable-line no-unused-vars\n\t\t\tnativeSet.apply(this, arguments);\n\n\t\t\tconfigureCssBlankAttribute.apply(this);\n\t\t}\n\n\t\tObject.defineProperty(HTMLElement.prototype, 'value', descriptor);\n\t}\n}\n"], "names": ["cssBlankPseudo", "document", "opts", "className", "Object", "attr", "force", "querySelector", "ignoredError", "window", "ownerDocument", "defaultView", "observeValueOfHTMLElement", "HTMLInputElement", "HTMLSelectElement", "HTMLTextAreaElement", "selector", "Array", "prototype", "for<PERSON>ach", "call", "querySelectorAll", "node", "addEventListener", "configureCssBlankAttribute", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "matches", "removedNodes", "removeEventListener", "observe", "childList", "subtree", "value", "removeAttribute", "classList", "remove", "setAttribute", "add", "HTMLElement", "descriptor", "getOwnPropertyDescriptor", "nativeSet", "set", "apply", "arguments", "defineProperty"], "mappings": "AAAe,SAASA,cAAT,CAAwBC,QAAxB,EAAkCC,IAAlC,EAAwC;;QAEhDC,SAAS,GAAGC,MAAM,CAACF,IAAD,CAAN,CAAaC,SAA/B;QACME,IAAI,GAAGD,MAAM,CAACF,IAAD,CAAN,CAAaG,IAAb,IAAqB,OAAlC;QACMC,KAAK,GAAGF,MAAM,CAACF,IAAD,CAAN,CAAaI,KAA3B;;MAEI;IACHL,QAAQ,CAACM,aAAT,CAAuB,QAAvB;;QAEI,CAACD,KAAL,EAAY;;;GAHb,CAME,OAAOE,YAAP,EAAqB;;;;;QAGjBC,MAAM,GAAG,CAACR,QAAQ,CAACS,aAAT,IAA0BT,QAA3B,EAAqCU,WAApD;EAEAC,yBAAyB,CAACH,MAAM,CAACI,gBAAR,CAAzB;EACAD,yBAAyB,CAACH,MAAM,CAACK,iBAAR,CAAzB;EACAF,yBAAyB,CAACH,MAAM,CAACM,mBAAR,CAAzB,CAnBsD;;QAsBhDC,QAAQ,GAAG,uBAAjB,CAtBsD;;EAyBtDC,KAAK,CAACC,SAAN,CAAgBC,OAAhB,CAAwBC,IAAxB,CACCnB,QAAQ,CAACoB,gBAAT,CAA0BL,QAA1B,CADD,EAECM,IAAI,IAAI;IACPA,IAAI,CAACC,gBAAL,CAAsB,OAAtB,EAA+BC,0BAA/B;IACAA,0BAA0B,CAACJ,IAA3B,CAAgCE,IAAhC;GAJF,EAzBsD;;MAkClDG,gBAAJ,CAAqBC,aAAa,IAAI;IACrCA,aAAa,CAACP,OAAd,CAAsBQ,QAAQ,IAAI;UAC7BA,QAAQ,CAACC,UAAb,EAAyB;QACxBD,QAAQ,CAACC,UAAT,CAAoBT,OAApB,CACCG,IAAI,IAAI;cACHA,IAAI,CAACO,QAAL,KAAkB,CAAlB,IAAuBP,IAAI,CAACQ,OAAL,CAAad,QAAb,CAA3B,EAAmD;YAClDM,IAAI,CAACC,gBAAL,CAAsB,OAAtB,EAA+BC,0BAA/B;YACAA,0BAA0B,CAACJ,IAA3B,CAAgCE,IAAhC;;SAJH;;;UAUGK,QAAQ,CAACI,YAAb,EAA2B;QAC1BJ,QAAQ,CAACI,YAAT,CAAsBZ,OAAtB,CACCG,IAAI,IAAI;cACHA,IAAI,CAACO,QAAL,KAAkB,CAAlB,IAAuBP,IAAI,CAACQ,OAAL,CAAad,QAAb,CAA3B,EAAmD;YAClDM,IAAI,CAACU,mBAAL,CAAyB,OAAzB,EAAkCR,0BAAlC;;SAHH;;KAbF;GADD,EAuBGS,OAvBH,CAuBWhC,QAvBX,EAuBqB;IAAEiC,SAAS,EAAE,IAAb;IAAmBC,OAAO,EAAE;GAvBjD,EAlCsD;;WA4D7CX,0BAAT,GAAsC;QACjC,KAAKY,KAAT,EAAgB;UACX/B,IAAJ,EAAU;aACJgC,eAAL,CAAqBhC,IAArB;;;UAGGF,SAAJ,EAAe;aACTmC,SAAL,CAAeC,MAAf,CAAsBpC,SAAtB;;;WAEIkC,eAAL,CAAqB,OAArB;KARD,MASO;UACFhC,IAAJ,EAAU;aACJmC,YAAL,CAAkB,OAAlB,EAA2BnC,IAA3B;;;UAGGF,SAAJ,EAAe;aACTmC,SAAL,CAAeG,GAAf,CAAmBtC,SAAnB;;;GA5EmD;;;WAkF7CS,yBAAT,CAAmC8B,WAAnC,EAAgD;UACzCC,UAAU,GAAGvC,MAAM,CAACwC,wBAAP,CAAgCF,WAAW,CAACxB,SAA5C,EAAuD,OAAvD,CAAnB;UACM2B,SAAS,GAAGF,UAAU,CAACG,GAA7B;;IAEAH,UAAU,CAACG,GAAX,GAAiB,SAASA,GAAT,CAAaV,KAAb,EAAoB;;MACpCS,SAAS,CAACE,KAAV,CAAgB,IAAhB,EAAsBC,SAAtB;MAEAxB,0BAA0B,CAACuB,KAA3B,CAAiC,IAAjC;KAHD;;IAMA3C,MAAM,CAAC6C,cAAP,CAAsBP,WAAW,CAACxB,SAAlC,EAA6C,OAA7C,EAAsDyB,UAAtD;;;;;;"}