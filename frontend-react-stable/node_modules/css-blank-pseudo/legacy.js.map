{"version": 3, "file": "legacy.js", "sources": ["src/browser-legacy.js"], "sourcesContent": ["export default function cssBlankPseudo(document, opts) {\n\t// configuration\n\tconst className = Object(opts).className;\n\tconst attr = Object(opts).attr || 'blank';\n\tconst force = Object(opts).force;\n\n\ttry {\n\t\tdocument.querySelector(':blank');\n\n\t\tif (!force) {\n\t\t\treturn;\n\t\t}\n\t} catch (ignoredError) { /* do nothing and continue */ }\n\n\t// observe value changes on <input>, <select>, and <textarea>\n\tconst window = (document.ownerDocument || document).defaultView;\n\n\tobserveValueOfHTMLElement(window.HTMLInputElement);\n\tobserveValueOfHTMLElement(window.HTMLSelectElement);\n\tobserveValueOfHTMLElement(window.HTMLTextAreaElement);\n\tobserveSelectedOfHTMLElement(window.HTMLOptionElement);\n\n\t// form control elements selector\n\tconst selector = 'INPUT,SELECT,TEXTAREA';\n\tconst selectorRegExp = /^(INPUT|SELECT|TEXTAREA)$/;\n\n\t// conditionally update all form control elements\n\tArray.prototype.forEach.call(\n\t\tdocument.querySelectorAll(selector),\n\t\tnode => {\n\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\tnode.addEventListener('change', configureCssBlankAttribute);\n\t\t\t} else {\n\t\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\t}\n\n\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t}\n\t);\n\n\t// conditionally observe added or unobserve removed form control elements\n\tnew MutationObserver(mutationsList => {\n\t\tmutationsList.forEach(mutation => {\n\t\t\tArray.prototype.forEach.call(\n\t\t\t\tmutation.addedNodes || [],\n\t\t\t\tnode => {\n\t\t\t\t\tif (node.nodeType === 1 && selectorRegExp.test(node.nodeName)) {\n\t\t\t\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\t\t\t\tnode.addEventListener('change', configureCssBlankAttribute);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\n\t\t\tArray.prototype.forEach.call(\n\t\t\t\tmutation.removedNodes || [],\n\t\t\t\tnode => {\n\t\t\t\t\tif (node.nodeType === 1 && selectorRegExp.test(node.nodeName)) {\n\t\t\t\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\t\t\t\tnode.removeEventListener('change', configureCssBlankAttribute);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tnode.removeEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\t\t});\n\t}).observe(document, { childList: true, subtree: true });\n\n\t// update a form control element’s css-blank attribute\n\tfunction configureCssBlankAttribute() {\n\t\tif (this.value || this.nodeName === 'SELECT' && this.options[this.selectedIndex].value) {\n\t\t\tif (attr) {\n\t\t\t\tthis.removeAttribute(attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.remove(className);\n\t\t\t}\n\t\t\tthis.removeAttribute('blank');\n\t\t} else {\n\t\t\tif (attr) {\n\t\t\t\tthis.setAttribute('blank', attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.add(className);\n\t\t\t}\n\t\t}\n\t}\n\n\t// observe changes to the \"value\" property on an HTML Element\n\tfunction observeValueOfHTMLElement(HTMLElement) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'value');\n\t\tconst nativeSet = descriptor.set;\n\n\t\tdescriptor.set = function set(value) { // eslint-disable-line no-unused-vars\n\t\t\tnativeSet.apply(this, arguments);\n\n\t\t\tconfigureCssBlankAttribute.apply(this);\n\t\t}\n\n\t\tObject.defineProperty(HTMLElement.prototype, 'value', descriptor);\n\t}\n\n\t// observe changes to the \"selected\" property on an HTML Element\n\tfunction observeSelectedOfHTMLElement(HTMLElement) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'selected');\n\t\tconst nativeSet = descriptor.set;\n\n\t\tdescriptor.set = function set(value) { // eslint-disable-line no-unused-vars\n\t\t\tnativeSet.apply(this, arguments);\n\n\t\t\tconst event = document.createEvent('Event');\n\t\t\tevent.initEvent('change', true, true);\n\t\t\tthis.dispatchEvent(event);\n\t\t}\n\n\t\tObject.defineProperty(HTMLElement.prototype, 'selected', descriptor);\n\t}\n}\n"], "names": ["cssBlankPseudo", "document", "opts", "className", "Object", "attr", "force", "querySelector", "ignoredError", "window", "ownerDocument", "defaultView", "observeValueOfHTMLElement", "HTMLInputElement", "HTMLSelectElement", "HTMLTextAreaElement", "observeSelectedOfHTMLElement", "HTMLOptionElement", "selector", "selectorRegExp", "Array", "prototype", "for<PERSON>ach", "call", "querySelectorAll", "node", "nodeName", "addEventListener", "configureCssBlankAttribute", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "test", "removedNodes", "removeEventListener", "observe", "childList", "subtree", "value", "options", "selectedIndex", "removeAttribute", "classList", "remove", "setAttribute", "add", "HTMLElement", "descriptor", "getOwnPropertyDescriptor", "nativeSet", "set", "apply", "arguments", "defineProperty", "event", "createEvent", "initEvent", "dispatchEvent"], "mappings": ";;AAAe,SAASA,cAAT,CAAwBC,QAAxB,EAAkCC,IAAlC,EAAwC;;QAEhDC,SAAS,GAAGC,MAAM,CAACF,IAAD,CAAN,CAAaC,SAA/B;QACME,IAAI,GAAGD,MAAM,CAACF,IAAD,CAAN,CAAaG,IAAb,IAAqB,OAAlC;QACMC,KAAK,GAAGF,MAAM,CAACF,IAAD,CAAN,CAAaI,KAA3B;;MAEI;IACHL,QAAQ,CAACM,aAAT,CAAuB,QAAvB;;QAEI,CAACD,KAAL,EAAY;;;GAHb,CAME,OAAOE,YAAP,EAAqB;;;;;QAGjBC,MAAM,GAAG,CAACR,QAAQ,CAACS,aAAT,IAA0BT,QAA3B,EAAqCU,WAApD;EAEAC,yBAAyB,CAACH,MAAM,CAACI,gBAAR,CAAzB;EACAD,yBAAyB,CAACH,MAAM,CAACK,iBAAR,CAAzB;EACAF,yBAAyB,CAACH,MAAM,CAACM,mBAAR,CAAzB;EACAC,4BAA4B,CAACP,MAAM,CAACQ,iBAAR,CAA5B,CApBsD;;QAuBhDC,QAAQ,GAAG,uBAAjB;QACMC,cAAc,GAAG,2BAAvB,CAxBsD;;EA2BtDC,KAAK,CAACC,SAAN,CAAgBC,OAAhB,CAAwBC,IAAxB,CACCtB,QAAQ,CAACuB,gBAAT,CAA0BN,QAA1B,CADD,EAECO,IAAI,IAAI;QACHA,IAAI,CAACC,QAAL,KAAkB,QAAtB,EAAgC;MAC/BD,IAAI,CAACE,gBAAL,CAAsB,QAAtB,EAAgCC,0BAAhC;KADD,MAEO;MACNH,IAAI,CAACE,gBAAL,CAAsB,OAAtB,EAA+BC,0BAA/B;;;IAGDA,0BAA0B,CAACL,IAA3B,CAAgCE,IAAhC;GATF,EA3BsD;;MAyClDI,gBAAJ,CAAqBC,aAAa,IAAI;IACrCA,aAAa,CAACR,OAAd,CAAsBS,QAAQ,IAAI;MACjCX,KAAK,CAACC,SAAN,CAAgBC,OAAhB,CAAwBC,IAAxB,CACCQ,QAAQ,CAACC,UAAT,IAAuB,EADxB,EAECP,IAAI,IAAI;YACHA,IAAI,CAACQ,QAAL,KAAkB,CAAlB,IAAuBd,cAAc,CAACe,IAAf,CAAoBT,IAAI,CAACC,QAAzB,CAA3B,EAA+D;cAC1DD,IAAI,CAACC,QAAL,KAAkB,QAAtB,EAAgC;YAC/BD,IAAI,CAACE,gBAAL,CAAsB,QAAtB,EAAgCC,0BAAhC;WADD,MAEO;YACNH,IAAI,CAACE,gBAAL,CAAsB,OAAtB,EAA+BC,0BAA/B;;;UAGDA,0BAA0B,CAACL,IAA3B,CAAgCE,IAAhC;;OAVH;MAeAL,KAAK,CAACC,SAAN,CAAgBC,OAAhB,CAAwBC,IAAxB,CACCQ,QAAQ,CAACI,YAAT,IAAyB,EAD1B,EAECV,IAAI,IAAI;YACHA,IAAI,CAACQ,QAAL,KAAkB,CAAlB,IAAuBd,cAAc,CAACe,IAAf,CAAoBT,IAAI,CAACC,QAAzB,CAA3B,EAA+D;cAC1DD,IAAI,CAACC,QAAL,KAAkB,QAAtB,EAAgC;YAC/BD,IAAI,CAACW,mBAAL,CAAyB,QAAzB,EAAmCR,0BAAnC;WADD,MAEO;YACNH,IAAI,CAACW,mBAAL,CAAyB,OAAzB,EAAkCR,0BAAlC;;;OAPJ;KAhBD;GADD,EA8BGS,OA9BH,CA8BWpC,QA9BX,EA8BqB;IAAEqC,SAAS,EAAE,IAAb;IAAmBC,OAAO,EAAE;GA9BjD,EAzCsD;;WA0E7CX,0BAAT,GAAsC;QACjC,KAAKY,KAAL,IAAc,KAAKd,QAAL,KAAkB,QAAlB,IAA8B,KAAKe,OAAL,CAAa,KAAKC,aAAlB,EAAiCF,KAAjF,EAAwF;UACnFnC,IAAJ,EAAU;aACJsC,eAAL,CAAqBtC,IAArB;;;UAGGF,SAAJ,EAAe;aACTyC,SAAL,CAAeC,MAAf,CAAsB1C,SAAtB;;;WAEIwC,eAAL,CAAqB,OAArB;KARD,MASO;UACFtC,IAAJ,EAAU;aACJyC,YAAL,CAAkB,OAAlB,EAA2BzC,IAA3B;;;UAGGF,SAAJ,EAAe;aACTyC,SAAL,CAAeG,GAAf,CAAmB5C,SAAnB;;;GA1FmD;;;WAgG7CS,yBAAT,CAAmCoC,WAAnC,EAAgD;UACzCC,UAAU,GAAG7C,MAAM,CAAC8C,wBAAP,CAAgCF,WAAW,CAAC3B,SAA5C,EAAuD,OAAvD,CAAnB;UACM8B,SAAS,GAAGF,UAAU,CAACG,GAA7B;;IAEAH,UAAU,CAACG,GAAX,GAAiB,SAASA,GAAT,CAAaZ,KAAb,EAAoB;;MACpCW,SAAS,CAACE,KAAV,CAAgB,IAAhB,EAAsBC,SAAtB;MAEA1B,0BAA0B,CAACyB,KAA3B,CAAiC,IAAjC;KAHD;;IAMAjD,MAAM,CAACmD,cAAP,CAAsBP,WAAW,CAAC3B,SAAlC,EAA6C,OAA7C,EAAsD4B,UAAtD;GA1GqD;;;WA8G7CjC,4BAAT,CAAsCgC,WAAtC,EAAmD;UAC5CC,UAAU,GAAG7C,MAAM,CAAC8C,wBAAP,CAAgCF,WAAW,CAAC3B,SAA5C,EAAuD,UAAvD,CAAnB;UACM8B,SAAS,GAAGF,UAAU,CAACG,GAA7B;;IAEAH,UAAU,CAACG,GAAX,GAAiB,SAASA,GAAT,CAAaZ,KAAb,EAAoB;;MACpCW,SAAS,CAACE,KAAV,CAAgB,IAAhB,EAAsBC,SAAtB;YAEME,KAAK,GAAGvD,QAAQ,CAACwD,WAAT,CAAqB,OAArB,CAAd;MACAD,KAAK,CAACE,SAAN,CAAgB,QAAhB,EAA0B,IAA1B,EAAgC,IAAhC;WACKC,aAAL,CAAmBH,KAAnB;KALD;;IAQApD,MAAM,CAACmD,cAAP,CAAsBP,WAAW,CAAC3B,SAAlC,EAA6C,UAA7C,EAAyD4B,UAAzD;;;;;;"}