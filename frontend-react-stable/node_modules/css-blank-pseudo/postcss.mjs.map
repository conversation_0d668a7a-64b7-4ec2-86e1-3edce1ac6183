{"version": 3, "file": "postcss.mjs", "sources": ["src/postcss.js"], "sourcesContent": ["import postcss from 'postcss';\n\nconst selectorRegExp = /:blank([^\\w-]|$)/gi;\n\nexport default postcss.plugin('css-blank-pseudo', opts => {\n\tconst replaceWith = String(Object(opts).replaceWith || '[blank]');\n\tconst preserve = Boolean('preserve' in Object(opts) ? opts.preserve : true);\n\n\treturn root => {\n\t\troot.walkRules(selectorRegExp, rule => {\n\t\t\tconst selector = rule.selector.replace(selectorRegExp, ($0, $1) => {\n\t\t\t\treturn `${replaceWith}${$1}`;\n\t\t\t});\n\n\t\t\tconst clone = rule.clone({ selector });\n\n\t\t\tif (preserve) {\n\t\t\t\trule.before(clone);\n\t\t\t} else {\n\t\t\t\trule.replaceWith(clone);\n\t\t\t}\n\t\t});\n\t};\n});\n"], "names": ["selectorRegExp", "postcss", "plugin", "opts", "replaceWith", "String", "Object", "preserve", "Boolean", "root", "walkRules", "rule", "selector", "replace", "$0", "$1", "clone", "before"], "mappings": ";;AAEA,MAAMA,cAAc,GAAG,oBAAvB;AAEA,gBAAeC,OAAO,CAACC,MAAR,CAAe,kBAAf,EAAmCC,IAAI,IAAI;QACnDC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACH,IAAD,CAAN,CAAaC,WAAb,IAA4B,SAA7B,CAA1B;QACMG,QAAQ,GAAGC,OAAO,CAAC,cAAcF,MAAM,CAACH,IAAD,CAApB,GAA6BA,IAAI,CAACI,QAAlC,GAA6C,IAA9C,CAAxB;SAEOE,IAAI,IAAI;IACdA,IAAI,CAACC,SAAL,CAAeV,cAAf,EAA+BW,IAAI,IAAI;YAChCC,QAAQ,GAAGD,IAAI,CAACC,QAAL,CAAcC,OAAd,CAAsBb,cAAtB,EAAsC,CAACc,EAAD,EAAKC,EAAL,KAAY;eAC1D,GAAEX,WAAY,GAAEW,EAAG,EAA3B;OADgB,CAAjB;YAIMC,KAAK,GAAGL,IAAI,CAACK,KAAL,CAAW;QAAEJ;OAAb,CAAd;;UAEIL,QAAJ,EAAc;QACbI,IAAI,CAACM,MAAL,CAAYD,KAAZ;OADD,MAEO;QACNL,IAAI,CAACP,WAAL,CAAiBY,KAAjB;;KAVF;GADD;CAJc,CAAf;;;;"}