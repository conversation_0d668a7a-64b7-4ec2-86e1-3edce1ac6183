{"name": "icss-utils", "version": "4.1.1", "description": "ICSS utils for postcss ast", "main": "lib/index.js", "engines": {"node": ">= 6"}, "files": ["lib"], "scripts": {"lint": "eslint . --ignore-path .gitignore", "build": "babel --out-dir lib src", "pretest": "npm run lint", "test": "npm run test:only", "test:only": "jest", "prepublish": "yarn test && yarn run build"}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": 6}}]]}, "eslintConfig": {"parser": "babel-es<PERSON>", "parserOptions": {"sourceType": "module"}, "env": {"es6": true, "jest": true}, "extends": "eslint:recommended"}, "lint-staged": {"*.js": ["prettier --write", "eslint", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/css-modules/icss-utils.git"}, "keywords": ["css", "modules", "icss", "postcss"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/css-modules/icss-utils/issues"}, "homepage": "https://github.com/css-modules/icss-utils#readme", "dependencies": {"postcss": "^7.0.14"}, "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-eslint": "^10.0.1", "babel-jest": "^24.1.0", "eslint": "^5.14.1", "husky": "^1.3.1", "jest": "^24.1.0", "lint-staged": "^8.1.4", "prettier": "^1.16.4"}}