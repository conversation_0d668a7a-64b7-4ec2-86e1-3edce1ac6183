{"version": 3, "sources": ["services/api.ts", "store/slices/authSlice.ts", "store/slices/uiSlice.ts", "store/store.ts", "pages/LoginPage.tsx", "components/Layout/MainLayout.tsx", "pages/DataCleaningPage.tsx", "services/taskApi.ts", "hooks/useTaskManager.ts", "pages/ModelTrainingPage.tsx", "pages/ModelPredictionPage.tsx", "pages/ModelRegistryPage.tsx", "pages/CleanTemplatePage.tsx", "pages/DataQueryPage.tsx", "pages/UserManagementPage.tsx", "pages/TaskManagerPage.tsx", "App.tsx", "index.tsx"], "names": ["api", "axios", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "credentials", "formData", "URLSearchParams", "append", "username", "password", "post", "get", "changePassword", "data", "addUser", "dataCleaningAPI", "folderPath", "encodeURIComponent", "FormData", "folder_path", "output_dir", "selected_files", "for<PERSON>ach", "file", "batchId", "modelTrainingAPI", "csvDir", "csv_dir", "selected_file", "JSON", "stringify", "selected_prots", "selected_datatypes", "learning_rate", "toString", "batch_size", "epochs", "sequence_length", "hidden_size", "num_layers", "dropout", "output_folder", "modelPredictionAPI", "modelDir", "getMatchingFiles", "modelFilename", "modelRegistryAPI", "listModels", "modelId", "delete", "getStatistics", "cleanTemplateAPI", "templateDir", "templatePath", "resultDir", "responseType", "dataQueryAPI", "downloadCsv", "csvFile", "getResultContent", "resultFile", "initialState", "isAuthenticated", "user", "loading", "showChangePassword", "forceChangePassword", "loginAsync", "createAsyncThunk", "async", "_ref", "rejectWithValue", "access_token", "userData", "isDefault", "is_default", "_error$response$data", "detail", "changePasswordAsync", "_ref2", "getState", "state", "auth", "_error$response2", "_error$response2$data", "authSlice", "createSlice", "name", "reducers", "logout", "clearError", "setShowChangePassword", "action", "payload", "restoreAuth", "extraReducers", "builder", "addCase", "pending", "fulfilled", "setItem", "rejected", "actions", "uiSlice", "sidebarCollapsed", "theme", "notifications", "toggleSidebar", "setSidebarCollapsed", "setTheme", "setLoading", "addNotification", "notification", "id", "Date", "now", "timestamp", "unshift", "length", "slice", "removeNotification", "filter", "clearNotifications", "store", "configureStore", "reducer", "authReducer", "ui", "uiReducer", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions", "Title", "Text", "Typography", "LoginPage", "form", "Form", "useForm", "dispatch", "useDispatch", "navigate", "useNavigate", "useSelector", "useEffect", "_jsx", "style", "minHeight", "background", "display", "alignItems", "justifyContent", "padding", "children", "Card", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "borderRadius", "_jsxs", "Space", "direction", "size", "textAlign", "level", "color", "marginBottom", "type", "<PERSON><PERSON>", "message", "description", "showIcon", "closable", "onClose", "onFinish", "values", "unwrap", "autoComplete", "<PERSON><PERSON>", "rules", "required", "min", "Input", "prefix", "UserOutlined", "placeholder", "Password", "LockOutlined", "<PERSON><PERSON>", "htmlType", "fontSize", "Header", "<PERSON><PERSON>", "Content", "Layout", "MainLayout", "useLocation", "menuItems", "key", "icon", "BarChartOutlined", "label", "ExperimentOutlined", "AimOutlined", "DatabaseOutlined", "FileTextOutlined", "SearchOutlined", "ClockCircleOutlined", "userMenuItems", "SettingOutlined", "LogoutOutlined", "onClick", "handleLogout", "trigger", "collapsible", "collapsed", "position", "left", "top", "bottom", "zIndex", "overflow", "flexDirection", "<PERSON><PERSON>", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "handleMenuClick", "border", "flex", "className", "borderTop", "MenuUnfoldOutlined", "MenuFoldOutlined", "height", "title", "right", "margin", "fontWeight", "letterSpacing", "textShadow", "Dropdown", "menu", "placement", "arrow", "cursor", "Avatar", "transition", "<PERSON><PERSON>", "Upload", "Option", "Select", "DataCleaningPage", "dataSource", "setDataSource", "useState", "processingMode", "setProcessingMode", "uploadedFiles", "setUploadedFiles", "setFolderPath", "availableFiles", "setAvailableFiles", "selectedFiles", "setSelectedFiles", "outputDir", "setOutputDir", "filesLoading", "setFilesLoading", "progress", "setProgress", "result", "setResult", "batchTasks", "setBatchTasks", "batchLoading", "setBatchLoading", "batchProgress", "setBatchProgress", "updateBatchTask", "field", "value", "map", "task", "validateBatchTask", "inputDir", "txtFiles", "files", "toLowerCase", "endsWith", "monitorBatchProgress", "attempt", "checkProgress", "console", "log", "success", "current_step", "prev", "errorMsg", "setTimeout", "warning", "warn", "fetchLocalFiles", "timer", "clearTimeout", "uploadProps", "multiple", "accept", "beforeUpload", "onChange", "info", "fileList", "onDrop", "e", "dataTransfer", "Divider", "strong", "Radio", "Group", "target", "marginTop", "_Fragment", "compact", "disabled", "marginLeft", "Spin", "spinning", "InboxOutlined", "PlayCircleOutlined", "originFileObj", "progressInterval", "setInterval", "clearInterval", "_error$response3", "_error$response3$data", "Progress", "percent", "output_file", "processed_files", "total_rows", "index", "extra", "danger", "DeleteOutlined", "removeBatchTask", "Row", "gutter", "Col", "span", "undefined", "fileCount", "PlusOutlined", "addBatchTask", "newTask", "validTasks", "i", "taskName", "push", "tasks", "customer", "input_dir", "batch_id", "Object", "keys", "entries", "taskId", "format", "TASK_STATUS", "PENDING", "RUNNING", "COMPLETED", "FAILED", "CANCELLED", "TASK_TYPE", "TRAINING", "PREDICTION", "getTaskStatus", "taskApi", "getAllTasks", "cancelTask", "getRunningTasks", "getCompletedTasks", "deleteSingleTask", "clearCompletedTasks", "startTrainingAsync", "startPredictionAsync", "pollTaskStatus", "onProgress", "interval", "arguments", "resolve", "poll", "Error", "formatTaskStatus", "formatTaskType", "getTaskStatusColor", "calculateTaskDuration", "startTime", "endTime", "start", "end", "duration", "Math", "floor", "getTime", "useTaskManager", "setTasks", "runningTasks", "setRunningTasks", "completedTasks", "setCompletedTasks", "initialized", "setInitialized", "pollingIntervals", "useRef", "Map", "fetchAllTasks", "useCallback", "showError", "fetchRunningTasks", "fetchCompletedTasks", "task_id", "substring", "errorMessage", "cleared_count", "startPolling", "current", "has", "prevTasks", "findIndex", "t", "newTasks", "includes", "task_type", "hash", "set", "stopPolling", "initializeTaskManager", "submitTrainingTask", "submitPredictionTask", "_error$response4", "_error$response4$data", "getTaskDetail", "intervals", "clear", "getTaskResult", "find", "getCompletedTasksByType", "taskType", "TrainingResultDisplay", "_result$train_losses", "_result$val_losses", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "selectedDatatype", "split", "Statistic", "train_shape", "test_shape", "r2_score", "toFixed", "r2", "precision", "valueStyle", "static_anomaly_threshold", "cpu_percent", "memory_mb", "gpu_memory_mb", "gpu_memory", "gpu_utilization_percent", "gpu_utilization", "train_losses", "val_losses", "ResponsiveContainer", "Line<PERSON>hart", "trainLoss", "epoch", "训练损失", "验证损失", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Legend", "Line", "stroke", "strokeWidth", "dot", "y_test_actual", "y_pred", "actual", "实际值", "预测值", "model_save_path", "scaler_y_save_path", "params_save_path", "test_save_path", "finished_time", "duration_seconds", "ModelTrainingPage", "uploadedFile", "setUploadedFile", "setCsvDir", "selectedFile", "setSelectedFile", "selected<PERSON><PERSON>", "setSelectedProts", "selectedDatatypes", "setSelectedDatatypes", "TCP", "learningRate", "setLearningRate", "batchSize", "setBatchSize", "setEpochs", "sequenceLength", "setSequenceLength", "hiddenSize", "setHiddenSize", "numLayers", "setNumLayers", "setDropout", "outputFolder", "setOutputFolder", "training", "setTraining", "trainingResults", "setTrainingResults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedResultKey", "useAsyncTraining", "setUseAsyncTraining", "asyncTrainingResults", "setAsyncTrainingResults", "selectedAsyncResult<PERSON>ey", "setSelectedAsyncResultKey", "selectedAsyncTaskId", "setSelectedAsyncTaskId", "completedTrainingTasks", "handleAsyncTaskSelect", "selectedTask", "results", "latestTask", "datatypeOptions", "UDP", "ICMP", "fetchCsvFiles", "prots", "newDatatypes", "prot", "Checkbox", "datatypes", "handleDatatypeChange", "protocol", "datatype", "align", "InputNumber", "max", "step", "code", "Slide<PERSON>", "marks", "some", "localTrainingData", "result_path", "_error$response$data2", "_error$response$data3", "statusText", "updated_at", "created_at", "toLocaleString", "PredictionResultDisplay", "suggested_threshold", "suffix", "protocolOptions", "ModelPredictionPage", "availableCsvFiles", "setAvailableCsvFiles", "selectedCsvFile", "setSelectedCsvFile", "csvFilesLoading", "setCsvFilesLoading", "setModelDir", "availablePthFiles", "setAvailablePthFiles", "selectedModels", "setSelectedModels", "modelsLoading", "setModelsLoading", "predictionMode", "setPredictionMode", "selectedModelFile", "setSelectedModelFile", "selectedParamsFile", "setSelectedParamsFile", "selectedScalerFile", "setSelectedScalerFile", "setSelected<PERSON>rot", "setSelectedDatatype", "showManualSelection", "setShowManualSelection", "predicting", "setPredicting", "setResults", "selectedResultIndex", "setSelectedResultIndex", "matchingFilesLoading", "setMatchingFilesLoading", "useAsyncPrediction", "setUseAsyncPrediction", "asyncPredictionResults", "setAsyncPredictionResults", "completedPredictionTasks", "asyncResult", "model_name", "fetchModelFiles", "pth_files", "modelFile", "matchingFiles", "params_filename", "scaler_filename", "baseNameWithoutExt", "replace", "autoMatchFiles", "backgroundColor", "modelsToPredict", "model_file", "params_file", "scaler_file", "validModels", "allResults", "model", "round", "_error$response5", "_error$response5$data", "isFormValid", "hasData", "TabPane", "Tabs", "ModelRegistryPage", "_statistics$best_mode", "_selectedModel$file_p", "_selectedModel$file_p2", "_selectedModel$file_p3", "_selectedModel$file_p4", "_selectedModel$traini", "_selectedModel$traini2", "_selectedModel$traini3", "_selectedModel$cleani", "_selectedModel$model_", "_selectedModel$model_2", "_selectedModel$model_3", "_selectedModel$model_4", "_selectedModel$model_5", "models", "setModels", "statistics", "setStatistics", "selected<PERSON><PERSON>l", "setSelectedModel", "detailModalVisible", "setDetailModalVisible", "searchText", "setSearchText", "activeTab", "setActiveTab", "fetchModels", "total_count", "fetchStatistics", "columns", "dataIndex", "filteredValue", "onFilter", "record", "model_id", "render", "text", "Tag", "sorter", "a", "b", "score", "created_time", "time", "_", "EyeOutlined", "fetchModelDetail", "Popconfirm", "onConfirm", "deleteModel", "okText", "cancelText", "active<PERSON><PERSON>", "tab", "ReloadOutlined", "allowClear", "Table", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "total_models", "avg_r2_score", "best_model", "TrophyOutlined", "protocols", "count", "Descriptions", "column", "Modal", "open", "onCancel", "footer", "bordered", "training_time", "file_paths", "model_path", "params_path", "scaler_path", "test_data_path", "training_params", "cleaning_threshold", "model_architecture", "source_data", "join", "training_duration", "cpu_usage", "memory_usage", "CleanTemplatePage", "generateForm", "sendForm", "updateForm", "resultFiles", "setResultFiles", "setResultDir", "lastGeneratedTemplate", "setLastGeneratedTemplate", "templates", "setTemplates", "setTemplateDir", "selectedTemplate", "setSelectedTemplate", "templateContent", "setTemplateContent", "editModalVisible", "setEditModalVisible", "viewModalVisible", "setViewModalVisible", "sendTemplateDir", "setSendTemplateDir", "sendTemplates", "setSendTemplates", "sendTemplatesLoading", "setSendTemplatesLoading", "fetchResultFiles", "showMessage", "trim", "fetchTemplates", "templatesData", "processedTemplates", "Array", "isArray", "item", "template_name", "filename", "template_path", "file_size", "modified_time", "_item$filename", "fetchSendTemplates", "_item$filename2", "fetchTemplateContent", "content", "useDebounce", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "handler", "debouncedResultDir", "debouncedTemplateDir", "debouncedSendTemplateDir", "templateColumns", "viewTemplate", "EditOutlined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "template_content", "editTemplate", "DownloadOutlined", "templateName", "url", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "_error$response8", "_error$response8$data", "downloadTemplate", "defaultActiveKey", "layout", "selected_result_file", "updated_thresholds", "path", "pop", "resetFields", "FolderOpenOutlined", "addonAfter", "showSearch", "notFoundContent", "filterOption", "input", "option", "_option$children", "borderColor", "copyable", "SendOutlined", "target_host", "target_username", "target_password", "target_port", "target_path", "currentMode", "getFieldValue", "template_selection_mode", "_error$response7", "_error$response7$data", "_option$children2", "template", "initialValue", "CloudUploadOutlined", "TextArea", "rows", "readOnly", "fontFamily", "submit", "_error$response6", "_error$response6$data", "DataQueryPage", "csvLoading", "setCsvLoading", "resultLoading", "setResultLoading", "csvFiles", "setCsvFiles", "selectedCsv", "setSelectedCsv", "selected<PERSON><PERSON><PERSON>", "setSelectedResult", "resultContent", "set<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "contentVisible", "setContentVisible", "csv_files", "result_files", "fileName", "downloadResult", "blob", "Empty", "image", "PRESENTED_IMAGE_SIMPLE", "List", "maxHeight", "renderItem", "Meta", "avatar", "Panel", "Collapse", "UserManagementPage", "users", "setUsers", "changePasswordForm", "addUserForm", "currentUser", "isAdmin", "fetchUsers", "userList", "is_admin", "last_login", "validatePassword", "test", "validateConfirmPassword", "passwordField", "userColumns", "TeamOutlined", "old_password", "new_password", "confirm_password", "iconRender", "visible", "EyeTwoTone", "EyeInvisibleOutlined", "validator", "SafetyOutlined", "ghost", "header", "UserAddOutlined", "new_username", "new_user_password", "confirm_user_password", "pattern", "ExclamationCircleOutlined", "TaskManagerPage", "_selectedTask$result$", "setSelectedTask", "taskDetailVisible", "setTaskDetailVisible", "refreshing", "setRefreshing", "handleViewTaskDetail", "getTaskStatusIcon", "SyncOutlined", "spin", "CheckCircleOutlined", "CloseCircleOutlined", "StopOutlined", "getTaskStatusTag", "formatTime", "timeString", "all", "locale", "emptyText", "handleCancelTask", "handleClearCompleted", "completedCount", "confirm", "okType", "onOk", "MinusCircleOutlined", "handleDeleteSingleTask", "_selectedTask$result", "started_at", "completed_at", "params", "wrap", "anomaly_count", "predictions", "ProtectedRoute", "Navigate", "to", "App", "Routes", "Route", "element", "ReactDOM", "React", "StrictMode", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "getElementById"], "mappings": "wVAGA,MAAMA,E,OAAqBC,EAAMC,OAAO,CACtCC,QAAiD,OACjDC,QAAS,IACTC,QAAS,CACP,eAAgB,sBAKpBL,EAAIM,aAAaC,QAAQC,IACtBC,IACC,MAAMC,EAAQC,aAAaC,QAAQ,SAInC,OAHIF,GAASD,EAAOJ,UAClBI,EAAOJ,QAAQQ,cAAgB,UAAUH,KAEpCD,GAERK,GACQC,QAAQC,OAAOF,IAK1Bd,EAAIM,aAAaW,SAAST,IACvBS,GACQA,EAERH,IAAW,IAADI,EAMT,OAL+B,OAAb,QAAdA,EAAAJ,EAAMG,gBAAQ,IAAAC,OAAA,EAAdA,EAAgBC,UAElBR,aAAaS,WAAW,SACxBC,OAAOC,SAASC,KAAO,UAElBR,QAAQC,OAAOF,KAKnB,MAAMU,EACHC,IAEN,MAAMC,EAAW,IAAIC,gBAIrB,OAHAD,EAASE,OAAO,WAAYH,EAAYI,UACxCH,EAASE,OAAO,WAAYH,EAAYK,UAEjC9B,EAAI+B,KAAK,cAAeL,EAAU,CACvCrB,QAAS,CAAE,eAAgB,wCARpBmB,EAYAd,GACTV,EAAIgC,IAAI,cAAe,CACrB3B,QAAS,CAAEQ,cAAe,UAAUH,OAd7Bc,EAiBKS,CACdC,EACAxB,IAEAV,EAAI+B,KAAK,wBAAyBG,EAAM,CACtC7B,QAAS,CAAEQ,cAAe,UAAUH,OAtB7Bc,EAyBFW,CACPD,EACAxB,IAEAV,EAAI+B,KAAK,iBAAkBG,EAAM,CAC/B7B,QAAS,CAAEQ,cAAe,UAAUH,OAK7B0B,EACCC,GACVrC,EAAIgC,IAAI,yCAAyCM,mBAAmBD,MAF3DD,EAICV,GACV1B,EAAI+B,KAAK,4BAA6BL,EAAU,CAC9CrB,QAAS,CAAE,eAAgB,yBANpB+B,EASMF,IAEf,MAAMR,EAAW,IAAIa,SASrB,OARAb,EAASE,OAAO,cAAeM,EAAKM,aACpCd,EAASE,OAAO,aAAcM,EAAKO,YAGnCP,EAAKQ,eAAeC,QAAQC,IAC1BlB,EAASE,OAAO,iBAAkBgB,KAG7B5C,EAAI+B,KAAK,4BAA6BL,EAAU,CACrDrB,QAAS,CAAE,eAAgB,0BArBpB+B,EA0BIF,GAOblC,EAAI+B,KAAK,+BAAgCG,GAjChCE,EAoCMS,GACf7C,EAAIgC,IAAI,+BAA+Ba,KAI9BC,EACIC,GACb/C,EAAIgC,IAAI,0CAA0CM,mBAAmBS,MAF5DD,EAIEpB,GACX1B,EAAI+B,KAAK,wBAAyBL,EAAU,CAC1CrB,QAAS,CAAE,eAAgB,uBAC3BD,QAAS,IAPF0C,EAWOZ,IAchB,MAAMR,EAAW,IAAIa,SAcrB,OAbAb,EAASE,OAAO,UAAWM,EAAKc,SAChCtB,EAASE,OAAO,gBAAiBM,EAAKe,eACtCvB,EAASE,OAAO,iBAAkBsB,KAAKC,UAAUjB,EAAKkB,iBACtD1B,EAASE,OAAO,qBAAsBsB,KAAKC,UAAUjB,EAAKmB,qBAC1D3B,EAASE,OAAO,gBAAiBM,EAAKoB,cAAcC,YACpD7B,EAASE,OAAO,aAAcM,EAAKsB,WAAWD,YAC9C7B,EAASE,OAAO,SAAUM,EAAKuB,OAAOF,YACtC7B,EAASE,OAAO,kBAAmBM,EAAKwB,gBAAgBH,YACxD7B,EAASE,OAAO,cAAeM,EAAKyB,YAAYJ,YAChD7B,EAASE,OAAO,aAAcM,EAAK0B,WAAWL,YAC9C7B,EAASE,OAAO,UAAWM,EAAK2B,QAAQN,YACxC7B,EAASE,OAAO,gBAAiBM,EAAK4B,eAE/B9D,EAAI+B,KAAK,wBAAyBL,EAAU,CACjDrB,QAAS,CAAE,eAAgB,uBAC3BD,QAAS,KAMF2D,EACIhB,GACb/C,EAAIgC,IAAI,4CAA4CM,mBAAmBS,MAF9DgB,EAIMC,GACfhE,EAAIgC,IAAI,gDAAgDM,mBAAmB0B,MALlED,EAOOE,CAACC,EAAuBF,IACxChE,EAAIgC,IAAI,uDAAuDM,mBAAmB4B,gBAA4B5B,mBAAmB0B,MARxHD,EAUDrC,GACR1B,EAAI+B,KAAK,4BAA6BL,EAAU,CAC9CrB,QAAS,CAAE,eAAgB,yBAKpB8D,EACCC,IAAMpE,EAAIgC,IAAI,wBADfmC,EAGME,GACfrE,EAAIgC,IAAI,0BAA0BqC,KAJzBF,EAMGE,GACZrE,EAAIsE,OAAO,0BAA0BD,KAP5BF,EASII,IAAMvE,EAAIgC,IAAI,8BAIlBwC,EACQ9C,GACjB1B,EAAI+B,KAAK,oCAAqCL,EAAU,CACtDrB,QAAS,CAAE,eAAgB,yBAHpBmE,EAMKC,GACdzE,EAAIgC,IAAI,8CAA8CM,mBAAmBmC,MAPhED,EASUE,GACnB1E,EAAIgC,IAAI,sDAAsDM,mBAAmBoC,MAVxEF,EAYM9C,GACf1B,EAAI+B,KAAK,kCAAmCL,EAAU,CACpDrB,QAAS,CAAE,eAAgB,yBAdpBmE,EAiBI9C,GACb1B,EAAI+B,KAAK,gCAAiCL,EAAU,CAClDrB,QAAS,CAAE,eAAgB,yBAnBpBmE,EAsBOG,GAChB3E,EAAIgC,IAAI,iDAAiDM,mBAAmBqC,MAvBnEH,EAyBQE,GACjB1E,EAAIgC,IAAI,mDAAmDM,mBAAmBoC,KAAiB,CAC7FE,aAAc,SAKPC,EACI9B,GACb/C,EAAIgC,IAAI,sCAAsCM,mBAAmBS,MAFxD8B,EAIEC,CAAC/B,EAAgBgC,IAC5B/E,EAAIgC,IAAI,oCAAoCM,mBAAmBS,eAAoBT,mBAAmByC,KAAY,CAChHH,aAAc,SANPC,EASOF,GAChB3E,EAAIgC,IAAI,4CAA4CM,mBAAmBqC,MAV9DE,EAYOG,CAACL,EAAmBM,IACpCjF,EAAIgC,IAAI,6CAA6CM,mBAAmBqC,kBAA0BrC,mBAAmB2C,MAK1GjF,QClOf,MAAMkF,EAA0B,CAC9BC,iBAAiB,EACjBC,KAAM,KACN1E,MAAOC,aAAaC,QAAQ,SAC5ByE,SAAS,EACTvE,MAAO,KACPwE,oBAAoB,EACpBC,qBAAqB,GAIVC,EAAaC,YACxB,aACAC,MAAOjE,EAAmDkE,KAA2B,IAAzB,gBAAEC,GAAiBD,EAC7E,IACE,MAAM1E,QAAiBO,EAAcC,IAC/B,aAAEoE,GAAiB5E,EAASiB,KAI5B4D,SADqBtE,EAAiBqE,IACd3D,KAAKT,EAAYI,UAE/C,MAAO,CACLnB,MAAOmF,EACPT,KAAM,CACJvD,SAAUJ,EAAYI,SACtBkE,WAAmB,OAARD,QAAQ,IAARA,OAAQ,EAARA,EAAUE,cAAc,GAGzC,CAAE,MAAOlF,GAAa,IAADI,EAAA+E,EACnB,OAAOL,GAA8B,QAAd1E,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAU,2BACzD,IAKSC,EAAsBV,YACjC,sBACAC,MACExD,EAAgGkE,KAE5F,IADJ,SAAEC,EAAQ,gBAAET,GAAiBQ,EAE7B,IACE,MAAME,EAAQD,IAEd,aADuB7E,EAAuBU,EAAMoE,EAAMC,KAAK7F,QAC/CwB,IAClB,CAAE,MAAOpB,GAAa,IAAD0F,EAAAC,EACnB,OAAOb,GAA8B,QAAdY,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAU,uCACzD,IAIEQ,EAAYC,YAAY,CAC5BC,KAAM,OACN1B,eACA2B,SAAU,CACRC,OAASR,IACPA,EAAMnB,iBAAkB,EACxBmB,EAAMlB,KAAO,KACbkB,EAAM5F,MAAQ,KACd4F,EAAMhB,oBAAqB,EAC3BgB,EAAMf,qBAAsB,EAC5B5E,aAAaS,WAAW,SACxBT,aAAaS,WAAW,aAE1B2F,WAAaT,IACXA,EAAMxF,MAAQ,MAEhBkG,sBAAuBA,CAACV,EAAOW,KAC7BX,EAAMhB,mBAAqB2B,EAAOC,SAGpCC,YAAcb,IACZ,MAAM5F,EAAQC,aAAaC,QAAQ,SAC7BiB,EAAWlB,aAAaC,QAAQ,YAClCF,GAASmB,IACXyE,EAAM5F,MAAQA,EACd4F,EAAMnB,iBAAkB,EACxBmB,EAAMlB,KAAO,CACXvD,SAAUA,EACVkE,WAAW,MAMnBqB,cAAgBC,IACdA,EAEGC,QAAQ9B,EAAW+B,QAAUjB,IAC5BA,EAAMjB,SAAU,EAChBiB,EAAMxF,MAAQ,OAEfwG,QAAQ9B,EAAWgC,UAAW,CAAClB,EAAOW,KACrCX,EAAMjB,SAAU,EAChBiB,EAAMnB,iBAAkB,EACxBmB,EAAMlB,KAAO6B,EAAOC,QAAQ9B,KAC5BkB,EAAM5F,MAAQuG,EAAOC,QAAQxG,MAC7B4F,EAAMhB,mBAAqB2B,EAAOC,QAAQ9B,KAAKW,UAC/CO,EAAMf,oBAAsB0B,EAAOC,QAAQ9B,KAAKW,UAChDpF,aAAa8G,QAAQ,QAASR,EAAOC,QAAQxG,OAC7CC,aAAa8G,QAAQ,WAAYR,EAAOC,QAAQ9B,KAAKvD,YAEtDyF,QAAQ9B,EAAWkC,SAAU,CAACpB,EAAOW,KACpCX,EAAMjB,SAAU,EAChBiB,EAAMxF,MAAQmG,EAAOC,UAGtBI,QAAQnB,EAAoBoB,QAAUjB,IACrCA,EAAMjB,SAAU,EAChBiB,EAAMxF,MAAQ,OAEfwG,QAAQnB,EAAoBqB,UAAYlB,IACvCA,EAAMjB,SAAU,EAChBiB,EAAMhB,oBAAqB,EAC3BgB,EAAMf,qBAAsB,EACxBe,EAAMlB,OACRkB,EAAMlB,KAAKW,WAAY,KAG1BuB,QAAQnB,EAAoBuB,SAAU,CAACpB,EAAOW,KAC7CX,EAAMjB,SAAU,EAChBiB,EAAMxF,MAAQmG,EAAOC,cAKhB,OAAEJ,EAAM,WAAEC,EAAU,sBAAEC,EAAqB,YAAEG,IAAgBT,EAAUiB,QACrEjB,SAAiB,QCjIhC,MAOMkB,GAAUjB,YAAY,CAC1BC,KAAM,KACN1B,aAT4B,CAC5B2C,kBAAkB,EAClBC,MAAO,QACPzC,SAAS,EACT0C,cAAe,IAMflB,SAAU,CACRmB,cAAgB1B,IACdA,EAAMuB,kBAAoBvB,EAAMuB,kBAElCI,oBAAqBA,CAAC3B,EAAOW,KAC3BX,EAAMuB,iBAAmBZ,EAAOC,SAElCgB,SAAUA,CAAC5B,EAAOW,KAChBX,EAAMwB,MAAQb,EAAOC,SAEvBiB,WAAYA,CAAC7B,EAAOW,KAClBX,EAAMjB,QAAU4B,EAAOC,SAEzBkB,gBAAiBA,CAAC9B,EAAOW,KACvB,MAAMoB,EAA6B,IAC9BpB,EAAOC,QACVoB,GAAIC,KAAKC,MAAMjF,WACfkF,UAAWF,KAAKC,OAElBlC,EAAMyB,cAAcW,QAAQL,GAExB/B,EAAMyB,cAAcY,OAAS,KAC/BrC,EAAMyB,cAAgBzB,EAAMyB,cAAca,MAAM,EAAG,MAGvDC,mBAAoBA,CAACvC,EAAOW,KAC1BX,EAAMyB,cAAgBzB,EAAMyB,cAAce,OACvCT,GAAiBA,EAAaC,KAAOrB,EAAOC,UAGjD6B,mBAAqBzC,IACnBA,EAAMyB,cAAgB,QAKf,cACXC,GAAa,oBACbC,GAAmB,SACnBC,GACAC,WAAU,mBACVC,GAAe,mBACfS,GAAkB,mBAClBE,IACEnB,GAAQD,QAEGC,UAAe,QCrEvB,MAAMoB,GAAQC,YAAe,CAClCC,QAAS,CACP3C,KAAM4C,GACNC,GAAIC,IAENC,WAAaC,GACXA,EAAqB,CACnBC,kBAAmB,CACjBC,eAAgB,CAAC,wB,wHCJzB,MAAM,MAAEC,GAAK,KAAEC,IAASC,KAoITC,OA7HaA,KAC1B,MAAOC,GAAQC,KAAKC,UACdC,EAAWC,cACXC,EAAWC,gBAEX,QAAE/E,EAAO,MAAEvE,EAAK,gBAAEqE,GAAoBkF,YAAa/D,GAAqBA,EAAMC,MAEpF+D,oBAAU,KACJnF,GACFgF,EAAS,MAEV,CAAChF,EAAiBgF,IAErBG,oBAAU,IAED,KACLL,EAASlD,MAEV,CAACkD,IAWJ,OACEM,eAAA,OAAKC,MAAO,CACVC,UAAW,QACXC,WAAY,+CACZC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBC,QAAS,QACTC,SACAR,eAACS,KAAI,CACHR,MAAO,CACLS,MAAO,OACPC,SAAU,IACVC,UAAW,gCACXC,aAAc,OACdL,SAEFM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,OAAQQ,UAAW,UAAWV,SAAA,CACrFM,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAEmB,MAAO,UAAWC,aAAc,GAAIb,SAAC,uDAG/DR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,gIAKxBjK,GACCyJ,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YAAalL,EACb+K,KAAK,QACLI,UAAQ,EACRC,UAAQ,EACRC,QAASA,IAAMlC,EAASlD,OAI5BsE,gBAACtB,KAAI,CACHD,KAAMA,EACNlD,KAAK,QACLwF,SAlDW1G,UACnB,UACQuE,EAASzE,EAAW6G,IAASC,QAErC,CAAE,MAAOxL,GACP,GA8CMyL,aAAa,MACbf,KAAK,QAAOT,SAAA,CAEZR,eAACR,KAAKyC,KAAI,CACR5F,KAAK,WACL6F,MAAO,CACL,CAAEC,UAAU,EAAMX,QAAS,yCAC3B,CAAEY,IAAK,EAAGZ,QAAS,uDACnBhB,SAEFR,eAACqC,KAAK,CACJC,OAAQtC,eAACuC,KAAY,IACrBC,YAAY,uCACZR,aAAa,eAIjBhC,eAACR,KAAKyC,KAAI,CACR5F,KAAK,WACL6F,MAAO,CACL,CAAEC,UAAU,EAAMX,QAAS,mCAC3B,CAAEY,IAAK,EAAGZ,QAAS,iDACnBhB,SAEFR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0C,KAAY,IACrBF,YAAY,iCACZR,aAAa,uBAIjBhC,eAACR,KAAKyC,KAAI,CAAAzB,SACRR,eAAC2C,KAAM,CACLrB,KAAK,UACLsB,SAAS,SACT9H,QAASA,EACTmF,MAAO,CAAES,MAAO,QAASF,SAExB1F,EAAU,wBAAW,sBAK5BkF,eAAA,OAAKC,MAAO,CAAEiB,UAAW,UAAWV,SAClCR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,QAASrC,SAAC,iG,4JCzGhE,MAAM,OAAEsC,GAAM,MAAEC,GAAK,QAAEC,IAAYC,MAC3B9D,MAAM,IAAIE,KA0OH6D,OApO+B9H,IAAmB,IAAlB,SAAEoF,GAAUpF,EACzD,MAAMwE,EAAWC,eACX9I,EAAWoM,eACXzD,EAAWC,eAEX,KAAE9E,GAASiF,YAAa/D,GAAqBA,EAAMC,OACnD,iBAAEsB,GAAqBwC,YAAa/D,GAAqBA,EAAM8C,IAIrEkB,oBAAU,KAERL,EAAS9C,OACR,CAAC8C,IAIJ,MAAM0D,EAAY,CAChB,CACEC,IAAK,iBACLC,KAAMtD,eAACuD,KAAgB,IACvBC,MAAO,4BAET,CACEH,IAAK,kBACLC,KAAMtD,eAACyD,KAAkB,IACzBD,MAAO,4BAET,CACEH,IAAK,oBACLC,KAAMtD,eAAC0D,KAAW,IAClBF,MAAO,4BAET,CACEH,IAAK,kBACLC,KAAMtD,eAAC2D,KAAgB,IACvBH,MAAO,4BAET,CACEH,IAAK,kBACLC,KAAMtD,eAAC4D,KAAgB,IACvBJ,MAAO,4BAET,CACEH,IAAK,cACLC,KAAMtD,eAAC6D,KAAc,IACrBL,MAAO,4BAET,CACEH,IAAK,gBACLC,KAAMtD,eAAC8D,KAAmB,IAC1BN,MAAO,4BAET,CACEH,IAAK,mBACLC,KAAMtD,eAACuC,KAAY,IACnBiB,MAAO,6BAaLO,EAAgB,CACpB,CACEV,IAAK,UACLC,KAAMtD,eAACuC,KAAY,IACnBiB,MAAO,4BAET,CACEH,IAAK,WACLC,KAAMtD,eAACgE,KAAe,IACtBR,MAAO,gBAET,CACElC,KAAM,WAER,CACE+B,IAAK,SACLC,KAAMtD,eAACiE,KAAc,IACrBT,MAAO,2BACPU,QAvBiBC,KACnBzE,EAASnD,KACTqD,EAAS,aAyBX,OACEkB,gBAACmC,KAAM,CAAChD,MAAO,CAAEC,UAAW,SAAUM,SAAA,CACpCM,gBAACiC,GAAK,CACJqB,QAAS,KACTC,aAAW,EACXC,UAAWhH,EACXoD,MAAO,IACPT,MAAO,CACLE,WAAY,OACZS,UAAW,4BACX2D,SAAU,QACVC,KAAM,EACNC,IAAK,GACLC,OAAQ,EACRC,OAAQ,IACRC,SAAU,SACVxE,QAAS,OACTyE,cAAe,UACfrE,SAAA,CAIFR,eAAC8E,KAAI,CACHC,KAAK,SACLC,aAAc,CAACjO,EAASkO,UACxBC,MAAO9B,EACPc,QAASrI,IAAA,IAAC,IAAEwH,GAAKxH,EAAA,MAzDAwH,KACvBzD,EAASyD,IAwDmB8B,CAAgB9B,IACtCpD,MAAO,CACLmF,OAAQ,OACRC,KAAM,GAERC,UAAWhI,EAAmB,iBAAmB,kBAInD0C,eAAA,OAAKC,MAAO,CACVM,QAAS,OACTgF,UAAW,oBACXrE,UAAW,UACXV,SACAR,eAAC2C,KAAM,CACLrB,KAAK,OACLgC,KAAMhG,EAAmB0C,eAACwF,KAAkB,IAAMxF,eAACyF,KAAgB,IACnEvB,QAASA,IAAMxE,EAASjC,MACxB6H,UAAU,qBACVrF,MAAO,CACL4C,SAAU,OACVnC,MAAO,OACPgF,OAAQ,GACRtF,QAAS,OACTC,WAAY,SACZC,eAAgB,UAElBqF,MAAOrI,EAAmB,2BAAS,+BAKvC0C,eAAA,SAAAQ,SAAQ,ksBAuBVM,gBAACmC,KAAM,CAAAzC,SAAA,CACLM,gBAACgC,GAAM,CAAC7C,MAAO,CACbM,QAAS,gBACTJ,WAAY,OACZC,QAAS,OACTC,WAAY,SACZC,eAAgB,gBAChBM,UAAW,4BACX2D,SAAU,QACVE,IAAK,EACLmB,MAAO,EACPpB,KAAM,EACNG,OAAQ,KACRe,OAAQ,QACRlF,SAAA,CACAR,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CACtB4F,OAAQ,EACRzE,MAAO,UACPyB,SAAU,OACViD,WAAY,IACZC,cAAe,QACfC,WAAY,gCACZxF,SAAC,uDAIHR,eAACe,KAAK,CAAAP,SACJR,eAACiG,KAAQ,CACPC,KAAM,CAAEhB,MAAOnB,GACfoC,UAAU,cACVC,OAAK,EAAA5F,SAELM,gBAACC,KAAK,CAACd,MAAO,CAAEoG,OAAQ,WAAY7F,SAAA,CAClCR,eAACsG,KAAM,CAAChD,KAAMtD,eAACuC,KAAY,MAC3BvC,eAAA,QAAAQ,SAAW,OAAJ3F,QAAI,IAAJA,OAAI,EAAJA,EAAMvD,qBAMrB0I,eAACgD,GAAO,CAAC/C,MAAO,CACd4F,OAAQ,kBAAkD,IAA/BvI,EAAmB,GAAK,SACnDiD,QAAS,GACTJ,WAAY,OACZU,aAAc,MACdX,UAAW,sBACX0E,SAAU,SACV2B,WAAY,oBACZ/F,SACCA,W,kICxOX,MAAQrB,MAAK,GAAEC,KAAK,IAAIC,MAClB,QAAEmH,IAAYC,MACd,OAAEC,IAAWC,KAgkBJC,OA9jBoBA,KACjC,MAAOC,EAAYC,GAAiBC,mBAA6B,UAC1DC,EAAgBC,GAAqBF,mBAA6B,WAClEG,EAAeC,GAAoBJ,mBAAgB,KACnDjP,EAAYsP,GAAiBL,mBAAS,KACtCM,EAAgBC,GAAqBP,mBAAmB,KACxDQ,EAAeC,GAAoBT,mBAAmB,KACtDU,EAAWC,GAAgBX,mBAAS,KACpCjM,EAAS8C,GAAcmJ,oBAAS,IAChCY,EAAcC,GAAmBb,oBAAS,IAC1Cc,EAAUC,GAAef,mBAAS,IAClCgB,EAAQC,GAAajB,mBAAc,OAGnCkB,EAAYC,GAAiBnB,mBAKhC,KACGoB,EAAcC,GAAmBrB,oBAAS,IAC1CsB,EAAeC,GAAoBvB,mBAAkC,CAAC,GAavEwB,EAAkBA,CAACxK,EAAYyK,EAAeC,KAClDP,EAAcD,EAAWS,IAAIC,GAC3BA,EAAK5K,KAAOA,EAAK,IAAK4K,EAAM,CAACH,GAAQC,GAAUE,KAQ7CC,EAAoBzN,UACxB,IACE,MAAMzE,QAAiBmB,EAA0B8Q,EAAKE,UAEhDC,GADQpS,EAASiB,KAAKoR,OAAS,IACdxK,OAAQlG,GAC7BA,EAAK2Q,cAAcC,SAAS,SAI9B,OADAV,EAAgBI,EAAK5K,GAAI,YAAa+K,EAAS1K,OAAOpF,YAC/C8P,EAAS1K,OAAS,CAC3B,CAAE,MAAO7H,GAEP,OADAiL,KAAQjL,MAAM,4BAAQoS,EAAKE,0BACpB,CACT,GAsDIK,EAAuB/N,UAE3B,IAAIgO,EAAU,EAEd,MAAMC,EAAgBjO,UACpB,IACE,MAAMzE,QAAiBmB,EAA+BS,GAItD,GAFA+Q,QAAQC,IAAI,oDAAa5S,EAASiB,MAE9BjB,EAASiB,MAAQjB,EAASiB,KAAK4R,QAAS,CAC1C,MAAM,OAAE3S,EAAM,SAAEiR,EAAQ,aAAE2B,EAAY,MAAEjT,GAAUG,EAASiB,KAU3D,GARA0R,QAAQC,IAAI,yCAAW1S,oBAAeiR,MAGtCS,EAAiBmB,IAAI,IAChBA,EACH,CAACnR,GAAUuP,GAAY,KAGV,cAAXjR,EAIF,OAHA4K,KAAQ+H,QAAQ,8CAChBnB,GAAgB,QAChBiB,QAAQC,IAAI,oDAEP,GAAe,WAAX1S,EAAqB,CAC9B,MAAM8S,EAAWnT,GAAS,2BAI1B,OAHAiL,KAAQjL,MAAM,yCAAWmT,KACzBtB,GAAgB,QAChBiB,QAAQ9S,MAAM,wCAAWmT,EAE3B,CAGIP,EAlCU,KAkC2B,YAAXvS,GAAmC,YAAXA,IAAyBA,IAC7EuS,IACAQ,WAAWP,EAAe,MACjBD,GArCG,KAuCZ3H,KAAQoI,QAAQ,kLAChBxB,GAAgB,GAChBiB,QAAQQ,KAAK,oDAEjB,MAEER,QAAQQ,KAAK,+EAAoBnT,EAASiB,MACtCwR,EA9CU,IA+CZA,IACAQ,WAAWP,EAAe,OAE1B5H,KAAQoI,QAAQ,4HAChBxB,GAAgB,GAGtB,CAAE,MAAO7R,GACP8S,QAAQ9S,MAAM,gEAAeA,GAGzB4S,EAAU,IACZA,IACAE,QAAQC,IAAI,oDAAYH,QACxBQ,WAAWP,EAAe,OAG1B5H,KAAQoI,QAAQ,8LAChBxB,GAAgB,GAChBiB,QAAQ9S,MAAM,gEAElB,GAIF6S,KAIIU,EAAkB3O,UACtB,GAAKrD,EAAL,CAEA8P,GAAgB,GAChB,IACE,MACMmB,SADiBlR,EAA0BC,IAC1BH,KAAKoR,OAAS,GACrCzB,EAAkByB,GAGlB,MAAMD,EAAWC,EAAMxK,OAAQlG,GAC7BA,EAAK2Q,cAAcC,SAAS,SAG1BH,EAAS1K,OAAS,GACpBoJ,EAAiBsB,GACjBtH,KAAQ+H,QAAQ,kCAAST,EAAS1K,kCAElCoJ,EAAiB,IACbuB,EAAM3K,OAAS,GACjBoD,KAAQoI,QAAQ,6DAGtB,CAAE,MAAOrT,GAAa,IAAD0F,EAAAC,EACnBsF,KAAQjL,OAAoB,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAU,oDAC9C2L,EAAkB,IAClBE,EAAiB,GACnB,CAAC,QACCI,GAAgB,EAClB,CA5BuB,GAgCzB7H,oBAAU,KACR,GAAmB,UAAf8G,GAA0B/O,GAAcA,EAAWsG,OAAS,EAAG,CACjE,MAAM2L,EAAQJ,WAAW,KACvBG,KACC,MAEH,MAAO,IAAME,aAAaD,EAC5B,GAEC,CAAClD,EAAY/O,IAGhB,MAAMmS,EAAc,CAClB5N,KAAM,QACN6N,UAAU,EACVC,OAAQ,OACRC,aAAcA,KAAM,EACpBC,SAAWC,IACTnD,EAAiBmD,EAAKC,WAExBC,OAASC,IACPpB,QAAQC,IAAI,gBAAiBmB,EAAEC,aAAa3B,SAmFhD,OACEjI,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,yCACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,wPAIvBR,eAAC2K,KAAO,IAER3K,eAACS,KAAI,CAACkF,MAAM,2BAAOL,UAAU,gBAAe9E,SAC1CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAEhEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAOzB,EACPqD,SAAWI,GAAMxD,EAAkBwD,EAAEM,OAAOtC,OAC5CxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,MAAM,SAAQjI,SAAC,yCACtBR,eAAC6K,KAAK,CAACpC,MAAM,QAAOjI,SAAC,+CAKL,WAAnBwG,GACClG,gBAAAmK,YAAA,CAAAzK,SAAA,CAEFM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,qDACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAO5B,EACPwD,SAAWI,GAAM3D,EAAc2D,EAAEM,OAAOtC,OACxCxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,MAAM,QAAOjI,SAAC,qDACrBR,eAAC6K,KAAK,CAACpC,MAAM,SAAQjI,SAAC,8DAKV,UAAfqG,GACC/F,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbM,gBAACuB,KAAMyI,MAAK,CAACI,SAAO,EAACjL,MAAO,CAAE+K,UAAW,EAAG5K,QAAS,QAASI,SAAA,CAC5DR,eAACqC,KAAK,CACJoG,MAAO3Q,EACPuS,SAAWI,GAAMrD,EAAcqD,EAAEM,OAAOtC,OACxCjG,YAAY,iDACZvC,MAAO,CAAEoF,KAAM,KAEjBrF,eAAC2C,KAAM,CACLrB,KAAK,UACL4C,QAAS4F,EACThP,QAAS6M,EACTwD,UAAWrT,EACXmI,MAAO,CAAEmL,WAAY,GAAI5K,SAC1B,uBAMLM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACqL,KAAI,CAACC,SAAU3D,EAAanH,SAC3BR,eAAC2G,KAAM,CACL5B,KAAK,WACL0D,MAAOlB,EACP8C,SAAU7C,EACVhF,YAAY,oCACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnClQ,QAAS6M,EAAanH,SAErB6G,EAAeqB,IAAKrQ,GACnB2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,cAWT,WAAfwO,GACC/F,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbM,gBAAC0F,GAAO,IAAKyD,EAAahK,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAChDR,eAAA,KAAGsF,UAAU,uBAAsB9E,SACjCR,eAACuL,KAAa,MAEhBvL,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,mFAC/BR,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,0HAQrCM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbR,eAACqC,KAAK,CACJoG,MAAOhB,EACP4C,SAAWI,GAAM/C,EAAa+C,EAAEM,OAAOtC,OACvCjG,YAAY,6BACZvC,MAAO,CAAE+K,UAAW,QAKxBhL,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLqC,KAAMtD,eAACwL,KAAkB,IACzBtH,QApMc/I,UAEtB,GAAmB,WAAf0L,GAAoD,IAAzBK,EAAc9I,OAK7C,GAAmB,UAAfyI,GAA4B/O,GAAuC,IAAzByP,EAAcnJ,OAA5D,CAKAR,GAAW,GACXkK,EAAY,GACZE,EAAU,MAEV,IACE,IAAItR,EAEJ,GAAmB,WAAfmQ,EAAyB,CAC3B,MAAM1P,EAAW,IAAIa,SACrBkP,EAAc9O,QAASC,IACrBlB,EAASE,OAAO,QAASgB,EAAKoT,iBAEhCtU,EAASE,OAAO,aAAcoQ,GAG9B,MAAMiE,EAAmBC,YAAY,KACnC7D,EAAa2B,GACPA,GAAQ,IACVmC,cAAcF,GACPjC,GAEFA,EAAO,KAEf,KAEH/S,QAAiBmB,EAA0BV,GAC3CyU,cAAcF,EAChB,KAAO,CAEL,MAAMA,EAAmBC,YAAY,KACnC7D,EAAa2B,GACPA,GAAQ,IACVmC,cAAcF,GACPjC,GAEFA,EAAO,KAEf,KAEH/S,QAAiBmB,EAA+B,CAC9CI,YAAaH,EACbK,eAAgBoP,EAChBrP,WAAYuP,IAEdmE,cAAcF,EAChB,CAEA5D,EAAY,KACZE,EAAUtR,EAASiB,MACnB6J,KAAQ+H,QAAQ,6CAElB,CAAE,MAAOhT,GAAa,IAADsV,EAAAC,EACnBtK,KAAQjL,OAAoB,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAU,uCAChD,CAAC,QACCiC,GAAW,EACb,CAzDA,MAFE4D,KAAQjL,MAAM,+GALdiL,KAAQjL,MAAM,2DAkMRuE,QAASA,EACTqQ,WA/HW,WAAftE,EACKK,EAAc9I,OAAS,EAEvBtG,GAAcyP,EAAcnJ,OAAS,GA6HtCkH,UAAU,gBAAe9E,SAExB1F,EAAU,8BAAY,yCAIxBA,GACCgG,gBAAA,OAAKwE,UAAU,mBAAkB9E,SAAA,CAC/BR,eAACZ,GAAI,CAAAoB,SAAC,mCACNR,eAAC+L,KAAQ,CAACC,QAASnE,EAAUjR,OAAO,cAKnCmR,GACC/H,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YACEX,gBAAA,OAAAN,SAAA,CACEM,gBAAA,KAAAN,SAAA,CAAG,iCAAMuH,EAAOvG,WACfuG,EAAOkE,aAAenL,gBAAA,KAAAN,SAAA,CAAG,iCAAMuH,EAAOkE,eACtClE,EAAOmE,iBAAmBpL,gBAAA,KAAAN,SAAA,CAAG,6CAAQuH,EAAOmE,mBAC5CnE,EAAOoE,YAAcrL,gBAAA,KAAAN,SAAA,CAAG,2BAAKuH,EAAOoE,iBAGzC7K,KAAK,UACLI,UAAQ,OAOI,UAAnBsF,GACChH,eAAAiL,YAAA,CAAAzK,SACEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbM,gBAAA,OAAKb,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAC3ByH,EAAWS,IAAI,CAACC,EAAMyD,IACrBtL,gBAACL,KAAI,CAEHQ,KAAK,QACLhB,MAAO,CAAEoB,aAAc,IACvBsE,MAAO,gBAAMyG,EAAQ,IACrBC,MACErM,eAAC2C,KAAM,CACLrB,KAAK,OACLgL,QAAM,EACNhJ,KAAMtD,eAACuM,KAAc,IACrBrI,QAASA,KAAMsI,OAvcdzO,EAuc8B4K,EAAK5K,QAtc1DmK,EAAcD,EAAW1J,OAAOoK,GAAQA,EAAK5K,KAAOA,IAD7BA,SAycJyC,SAAA,CAEDM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACqC,KAAK,CACJoG,MAAOE,EAAKE,SACZwB,SAAWI,GAAMlC,EAAgBI,EAAK5K,GAAI,WAAY0M,EAAEM,OAAOtC,OAC/DjG,YAAY,gCACZvC,MAAO,CAAE+K,UAAW,QAGxBlK,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACqC,KAAK,CACJoG,MAAOE,EAAKlB,UACZ4C,SAAWI,GAAMlC,EAAgBI,EAAK5K,GAAI,YAAa0M,EAAEM,OAAOtC,OAChEjG,YAAY,iCACZvC,MAAO,CAAE+K,UAAW,gBAIN6B,IAAnBlE,EAAKmE,WACJ9M,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BM,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,sBAChBmI,EAAKmE,UAAU,gCApCrBnE,EAAK5K,KA2CdiC,eAAC2C,KAAM,CACLrB,KAAK,SACLgC,KAAMtD,eAAC+M,KAAY,IACnB7I,QA5fG8I,KACnB,MAAMC,EAAU,CACdlP,GAAIC,KAAKC,MAAMjF,WACf6P,SAAU,GACVpB,UAAW,GACXqF,UAAW,GAEb5E,EAAc,IAAID,EAAYgF,KAsfdhN,MAAO,CAAES,MAAO,OAAQW,aAAc,IAAKb,SAC5C,6BAIDR,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLnG,QAASqN,EACTgD,SAAgC,IAAtBlD,EAAW7J,OACrB8F,QAneS/I,UAEzB,MAAM+R,EAAa,GACnB,IAAK,IAAIC,EAAI,EAAGA,EAAIlF,EAAW7J,OAAQ+O,IAAK,CAC1C,MAAMxE,EAAOV,EAAWkF,GAClBC,EAAW,eAAKD,EAAI,IAE1B,IAAKxE,EAAKE,WAAaF,EAAKlB,UAE1B,YADAjG,KAAQjL,MAAM,uBAAQ6W,+BAIFxE,EAAkBD,IAEtCuE,EAAWG,KAAK,IAAI1E,EAAMyE,YAE9B,CAEA,GAA0B,IAAtBF,EAAW9O,OAAf,CAKAgK,GAAgB,GAEhB,IAEE,MAAM1R,QAAiBmB,EAA6B,CAClDyV,MAAOJ,EAAWxE,IAAI,CAACC,EAAMyD,KAAK,CAChCmB,SAAU,eAAKnB,EAAQ,IACvBoB,UAAW7E,EAAKE,SAChB3Q,WAAYyQ,EAAKlB,eAIrB,GAAI/Q,EAASiB,KAAK4R,QAAS,CACzB,MAAMjR,EAAU5B,EAASiB,KAAK8V,SAC9BjM,KAAQ+H,QAAQ,mEAAiBjR,KAGjC4Q,EAAqB5Q,EACvB,MACEkJ,KAAQjL,MAAM,mDAElB,CAAE,MAAOA,GAAa,IAADI,EAAA+E,EACnB8F,KAAQjL,OAAoB,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAU,uCAChD,CAAC,QACCyM,GAAgB,EAClB,CA3BA,MAFE5G,KAAQjL,MAAM,2DAidA0J,MAAO,CAAES,MAAO,QAASF,SAExB2H,EAAe,oCAAa,yCAI9BA,GAAgBuF,OAAOC,KAAKtF,GAAejK,OAAS,GACnD0C,gBAAA,OAAKb,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAC5BR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACZkN,OAAOE,QAAQvF,GAAeK,IAAItN,IAAA,IAAEyS,EAAQhG,GAASzM,EAAA,OACpD4E,eAAA,OAAkBC,MAAO,CAAE+K,UAAW,GAAIxK,SACxCR,eAAC+L,KAAQ,CACPC,QAASnE,EACTjR,OAAqB,MAAbiR,EAAmB,UAAY,SACvCiG,OAAS9B,GAAY,GAAGA,QAJlB6B,yB,kIC3jB3B,MAAME,GAAc,CACzBC,QAAS,UACTC,QAAS,UACTC,UAAW,YACXC,OAAQ,SACRC,UAAW,aAIAC,GAAY,CACvBC,SAAU,WACVC,WAAY,cAmDDC,GAAgBrT,UAC3B,IAEE,aADuB1F,EAAIgC,IAAI,eAAeoW,MAC9BlW,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,4BAAQsX,8BAAgBtX,GAChCA,CACR,GA0NIkY,GAAU,CACdC,YAhPyBvT,UACzB,IAEE,aADuB1F,EAAIgC,IAAI,iBACfE,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,oDAAaA,GACrBA,CACR,GA0OAiY,iBACAG,WAtNwBxT,UACxB,IAEE,aADuB1F,EAAIsE,OAAO,eAAe8T,MACjClW,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,4BAAQsX,kBAActX,GAC9BA,CACR,GAgNAqY,gBA1M6BzT,UAC7B,IAEE,aADuB1F,EAAIgC,IAAI,yBACfE,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,0DAAcA,GACtBA,CACR,GAoMAsY,kBA1G+B1T,UAC/B,IAEE,aADuB1F,EAAIgC,IAAI,qBACfE,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,0DAAcA,GACtBA,CACR,GAoGAuY,iBA9F8B3T,UAC9B,IAEE,aADuB1F,EAAIsE,OAAO,eAAe8T,MACjClW,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,wCAAWA,GACnBA,CACR,GAwFAwY,oBAlFiC5T,UACjC,IAEE,aADuB1F,EAAIsE,OAAO,qBAClBpC,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,0DAAcA,GACtBA,CACR,GA4EAyY,mBAhMgC7T,UAChC,IAME,aALuB1F,EAAI+B,KAAK,8BAA+BL,EAAU,CACvErB,QAAS,CACP,eAAgB,0BAGJ6B,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,oDAAaA,GACrBA,CACR,GAsLA0Y,qBA/KkC9T,UAClC,IAME,aALuB1F,EAAI+B,KAAK,kCAAmCL,EAAU,CAC3ErB,QAAS,CACP,eAAgB,0BAGJ6B,IAClB,CAAE,MAAOpB,GAEP,MADA8S,QAAQ9S,MAAM,oDAAaA,GACrBA,CACR,GAqKA2Y,eA3J4B/T,eAC5B0S,EACAsB,GAEmB,IADnBC,EAAgBC,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,GAAAA,UAAA,GAAG,IAEnB,OAAO,IAAI7Y,QAAQ,CAAC8Y,EAAS7Y,KAC3B,MAAM8Y,EAAOpU,UACX,IACE,MACMwN,SADiB6F,GAAcX,IACflF,KAEtB,IAAKA,EAEH,YADAlS,EAAO,IAAI+Y,MAAM,mCAKfL,GACFA,EAAWxG,GAITA,EAAK/R,SAAWmX,GAAYG,UAC9BoB,EAAQ3G,GACCA,EAAK/R,SAAWmX,GAAYI,OACrC1X,EAAO,IAAI+Y,MAAM7G,EAAKpS,OAAS,yCACtBoS,EAAK/R,SAAWmX,GAAYK,UACrC3X,EAAO,IAAI+Y,MAAM,yCAGjB7F,WAAW4F,EAAMH,EAErB,CAAE,MAAO7Y,GACPE,EAAOF,EACT,GAGFgZ,KAEJ,EAqHEE,iBAxE+B7Y,IACb,CAChB,CAACmX,GAAYC,SAAU,qBACvB,CAACD,GAAYE,SAAU,qBACvB,CAACF,GAAYG,WAAY,qBACzB,CAACH,GAAYI,QAAS,eACtB,CAACJ,GAAYK,WAAY,sBAEVxX,IAAWA,GAiE5B8Y,eA1D6BpO,IACb,CACd,CAAC+M,GAAUC,UAAW,2BACtB,CAACD,GAAUE,YAAa,4BAEXjN,IAASA,GAsDxBqO,mBA/CiC/Y,IAChB,CACf,CAACmX,GAAYC,SAAU,UACvB,CAACD,GAAYE,SAAU,aACvB,CAACF,GAAYG,WAAY,UACzB,CAACH,GAAYI,QAAS,QACtB,CAACJ,GAAYK,WAAY,WAEXxX,IAAW,WAwC3BgZ,sBAhCmCA,CAACC,EAAoBC,KACxD,IAAKD,EAAW,MAAO,eAEvB,MAAME,EAAQ,IAAI/R,KAAK6R,GACjBG,EAAMF,EAAU,IAAI9R,KAAK8R,GAAW,IAAI9R,KACxCiS,EAAWC,KAAKC,OAAOH,EAAII,UAAYL,EAAMK,WAAa,KAEhE,GAAIH,EAAW,GACb,MAAO,GAAGA,UACL,GAAIA,EAAW,KACpB,MAAO,GAAGC,KAAKC,MAAMF,EAAW,YAAOA,EAAW,WAIlD,MAAO,GAFOC,KAAKC,MAAMF,EAAW,oBACpBC,KAAKC,MAAOF,EAAW,KAAQ,aAoBjDlC,YAAW,GACXM,cAGaI,UCwEA4B,OArXeA,KAC5B,MAAO/C,EAAOgD,GAAYvJ,mBAAiB,KACpCjM,EAAS8C,GAAcmJ,oBAAS,IAChCwJ,EAAcC,GAAmBzJ,mBAAiB,KAClD0J,EAAgBC,GAAqB3J,mBAAiB,KACtD4J,EAAaC,GAAkB7J,oBAAS,GACzC8J,EAAmBC,iBAAO,IAAIC,KAK9BC,EAAgBC,sBAAY9V,iBAA6B,IAAtB+V,IAAS7B,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,KAAAA,UAAA,GAChD,IACEzR,GAAW,GACX,MAAMlH,QAAiB+X,GAAQC,cAC3BhY,EAAS6S,SACX+G,EAAS5Z,EAAS4W,OAAS,GAE/B,CAAE,MAAO/W,GACP8S,QAAQ9S,MAAM,oDAAaA,GACvB2a,GACF1P,KAAQjL,MAAM,mDAElB,CAAC,QACCqH,GAAW,EACb,CACF,EAAG,IAKGuT,EAAoBF,sBAAY9V,iBAA8B,IAAvB+V,EAAS7B,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,IAAAA,UAAA,GACpD,IACE,MAAM3Y,QAAiB+X,GAAQG,kBAC3BlY,EAAS6S,SACXiH,EAAgB9Z,EAAS4W,OAAS,GAEtC,CAAE,MAAO/W,GACP8S,QAAQ9S,MAAM,0DAAcA,GACxB2a,GACF1P,KAAQjL,MAAM,yDAElB,CACF,EAAG,IAKG6a,EAAsBH,sBAAY9V,iBAA8B,IAAvB+V,EAAS7B,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,IAAAA,UAAA,GACtD,IACE,MAAM3Y,QAAiB+X,GAAQI,oBAC3BnY,EAAS6S,SACXmH,EAAkBha,EAAS4W,OAAS,GAExC,CAAE,MAAO/W,GACP8S,QAAQ9S,MAAM,0DAAcA,GACxB2a,GACF1P,KAAQjL,MAAM,yDAElB,CACF,EAAG,IAKGuY,EAAmBmC,sBAAY9V,UACnC,IAEE,eADuBsT,GAAQK,iBAAiBjB,IACnCtE,UAEXmH,EAAkBjH,GAAQA,EAAKlL,OAAOoK,GAAQA,EAAK0I,UAAYxD,IAC/DrM,KAAQ+H,QAAQ,mCAAUsE,EAAOyD,UAAU,EAAG,UACvC,EAGX,CAAE,MAAO/a,GAAa,IAADI,EAAA+E,EACnB2N,QAAQ9S,MAAM,wCAAWA,GACzB,MAAMgb,GAA6B,QAAd5a,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAU,uCAErD,OADA6F,KAAQjL,MAAMgb,IACP,CACT,GACC,IAKGxC,EAAsBkC,sBAAY9V,UACtC,IACE,MAAMzE,QAAiB+X,GAAQM,sBAC3BrY,EAAS6S,UACXmH,EAAkB,IAClBlP,KAAQ+H,QAAQ,sBAAO7S,EAAS8a,sDAEpC,CAAE,MAAOjb,GACP8S,QAAQ9S,MAAM,0DAAcA,GAC5BiL,KAAQjL,MAAM,yDAChB,GACC,IAKGkb,EAAeR,sBAAY,CAACpD,EAAgBsB,KAE5C0B,EAAiBa,QAAQC,IAAI9D,IAC/BjC,cAAciF,EAAiBa,QAAQja,IAAIoW,IAG7C,MAAMuB,EAAWzD,YAAYxQ,UAC3B,IACE,MACMwN,SADiB8F,GAAQD,cAAcX,IACvBlF,KAEtB,IAAKA,EAAM,OAGX2H,EAASsB,IACP,MAAMxF,EAAQwF,EAAUC,UAAUC,GAAKA,EAAET,UAAYxD,GACrD,GAAIzB,GAAS,EAAG,CACd,MAAM2F,EAAW,IAAIH,GAErB,OADAG,EAAS3F,GAASzD,EACXoJ,CACT,CACE,MAAO,IAAIH,EAAWjJ,KAKtBwG,GACFA,EAAWxG,GAIT,CAACoF,GAAYG,UAAWH,GAAYI,OAAQJ,GAAYK,WAAW4D,SAASrJ,EAAK/R,UACnFgV,cAAciF,EAAiBa,QAAQja,IAAIoW,IAC3CgD,EAAiBa,QAAQ3X,OAAO8T,GAG5BlF,EAAK/R,SAAWmX,GAAYG,UAC9BpQ,KAAayL,QAAQ,CACnB/H,QAAS,wCACTC,YAAa,GAAGgN,GAAQiB,eAAe/G,EAAKsJ,iNAC5ChC,SAAU,GACV/L,QAASA,KAEPpN,OAAOC,SAASmb,KAAO,kBAEzBjS,MAAO,CAAEoG,OAAQ,aAEVsC,EAAK/R,SAAWmX,GAAYI,OACrCrQ,KAAavH,MAAM,CACjBiL,QAAS,kCACTC,YAAa,GAAGgN,GAAQiB,eAAe/G,EAAKsJ,mDAAqBtJ,EAAKpS,OAAS,mMAC/E0Z,SAAU,GACV/L,QAASA,KAEPpN,OAAOC,SAASmb,KAAO,kBAEzBjS,MAAO,CAAEoG,OAAQ,aAEVsC,EAAK/R,SAAWmX,GAAYK,WACrCtQ,KAAa8L,QAAQ,CACnBpI,QAAS,8CACTC,YAAa,GAAGgN,GAAQiB,eAAe/G,EAAKsJ,iDAC5ChC,SAAU,IAKdkB,IACAC,IAEJ,CAAE,MAAO7a,GACP8S,QAAQ9S,MAAM,4BAAQsX,8BAAgBtX,GACtCqV,cAAciF,EAAiBa,QAAQja,IAAIoW,IAC3CgD,EAAiBa,QAAQ3X,OAAO8T,EAClC,GACC,KAEHgD,EAAiBa,QAAQS,IAAItE,EAAQuB,IACpC,CAAC+B,EAAmBC,IAKjBgB,EAAcnB,sBAAapD,IAC3BgD,EAAiBa,QAAQC,IAAI9D,KAC/BjC,cAAciF,EAAiBa,QAAQja,IAAIoW,IAC3CgD,EAAiBa,QAAQ3X,OAAO8T,KAEjC,IAKGwE,EAAwBpB,sBAAY9V,UACxC,IAAIwV,EAEJ,UAEQQ,GAAkB,GAGxB,MAAMza,QAAiB+X,GAAQG,kBAC3BlY,EAAS6S,SAAW7S,EAAS4W,OAC/B5W,EAAS4W,MAAMlV,QAAQuQ,IACrB8I,EAAa9I,EAAK0I,WAItBT,GAAe,EACjB,CAAE,MAAOra,GACP8S,QAAQQ,KAAK,0DAActT,EAE7B,GACC,CAACoa,EAAaQ,EAAmBM,IAK9Ba,EAAqBrB,sBAAY9V,UACrC,UAEQkX,IAEN,MAAM3b,QAAiB+X,GAAQO,mBAAmB7X,GAClD,GAAIT,EAAS6S,QAAS,CACpB,MAAMsE,EAASnX,EAAS2a,QACxB,GAAIxD,EASF,OARArM,KAAQ+H,QAAQ,4LAAuC,GAGvDkI,EAAa5D,GAGbsD,GAAkB,GAEXtD,CAEX,CACF,CAAE,MAAOtX,GAAa,IAAD0F,EAAAC,EAGnB,MAFAmN,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,uDAA8B,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAUpF,EAAMiL,UAC9DjL,CACR,GACC,CAAC8b,EAAuBZ,EAAcN,IAKnCoB,EAAuBtB,sBAAY9V,UACvC,UAEQkX,IAEN,MAAM3b,QAAiB+X,GAAQQ,qBAAqB9X,GACpD,GAAIT,EAAS6S,QAAS,CACpB,MAAMsE,EAASnX,EAAS2a,QACxB,GAAIxD,EASF,OARArM,KAAQ+H,QAAQ,4LAAuC,GAGvDkI,EAAa5D,GAGbsD,GAAkB,GAEXtD,CAEX,CACF,CAAE,MAAOtX,GAAa,IAADsV,EAAAC,EAGnB,MAFAzC,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,uDAA8B,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAUpF,EAAMiL,UAC9DjL,CACR,GACC,CAAC8b,EAAuBZ,EAAcN,IAKnCxC,EAAasC,sBAAY9V,UAC7B,WACyBsT,GAAQE,WAAWd,IAC7BtE,UACX/H,KAAQ+H,QAAQ,kCAGhB6I,EAAYvE,GAGZmD,IACAG,IAEJ,CAAE,MAAO5a,GAAa,IAADic,EAAAC,EACnBpJ,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,2CAA4B,QAAdic,EAAAjc,EAAMG,gBAAQ,IAAA8b,GAAM,QAANC,EAAdD,EAAgB7a,YAAI,IAAA8a,OAAN,EAAdA,EAAsB9W,SAAUpF,EAAMiL,SACpE,GACC,CAAC4Q,EAAapB,EAAeG,IAK1BuB,EAAgBzB,sBAAY9V,UAChC,IAEE,aADuBsT,GAAQD,cAAcX,IAC7BlF,MAAQ,IAC1B,CAAE,MAAOpS,GAGP,OAFA8S,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,oDACP,IACT,GACC,IAgBH,OAbAwJ,oBAAU,KAERqR,IAGO,KAEL,MAAMuB,EAAY9B,EAAiBa,QACnCiB,EAAUva,QAAQgX,GAAYxD,cAAcwD,IAC5CuD,EAAUC,UAEX,CAACxB,IAEG,CAEL9D,QACAiD,eACAE,iBACA3V,UAGAkW,gBACAG,oBACAC,sBACAtC,mBACAC,sBACAuD,qBACAC,uBACA5D,aACA+D,gBACAjB,eACAW,cAGAS,cAAe5B,sBAAapD,IAC1B,MAAMlF,EAAO8H,EAAeqC,KAAKhB,GAAKA,EAAET,UAAYxD,GACpD,OAAW,OAAJlF,QAAI,IAAJA,OAAI,EAAJA,EAAMZ,SAAU,MACtB,CAAC0I,IAEJsC,wBAAyB9B,sBAAa+B,GAC7BvC,EAAelS,OAAOoK,GAC3BA,EAAKsJ,YAAce,GACnBrK,EAAK/R,SAAWmX,GAAYG,WAC5BvF,EAAKZ,QAEN,CAAC0I,IAGJhB,iBAAkBhB,GAAQgB,iBAC1BC,eAAgBjB,GAAQiB,eACxBC,mBAAoBlB,GAAQkB,mBAC5BC,sBAAuBnB,GAAQmB,sBAG/B7B,eACAM,eCnWJ,MAAQlP,MAAK,GAAEC,KAAK,IAAIC,MAChBmH,QAAQ,IAAIC,MACZC,OAAO,IAAIC,KAGbsM,GAAsE7X,IAA4B,IAAD8X,EAAAC,EAAA,IAA1B,UAAEC,EAAS,OAAErL,GAAQ3M,EAChG,MAAOiY,EAAcC,GAAoBF,EAAUG,MAAM,IAAK,GAE9D,OACEvT,eAAA,OAAAQ,SACEM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,SAAShB,MAAO,CAAES,MAAO,QAASF,SAAA,CACjEM,gBAAA,OAAAN,SAAA,CACEM,gBAAC1B,GAAI,CAAAoB,SAAA,CAACR,eAAA,UAAAQ,SAAQ,kBAAY,IAAE6S,KAC5BrT,eAAA,SACAc,gBAAC1B,GAAI,CAAAoB,SAAA,CAACR,eAAA,UAAAQ,SAAQ,8BAAc,IAAE8S,QAGhCxS,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,6CACN8C,MAAOV,EAAO0L,YAAc,GAAG1L,EAAO0L,YAAY,WAAQ1L,EAAO0L,YAAY,KAAO,UAGxFzT,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,6CACN8C,MAAOV,EAAO2L,WAAa,GAAG3L,EAAO2L,WAAW,WAAQ3L,EAAO2L,WAAW,KAAO,UAGrF1T,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,qBACN8C,MAAOV,EAAO4L,SAAW5L,EAAO4L,SAASC,QAAQ,GAAM7L,EAAO8L,GAAK9L,EAAO8L,GAAGD,QAAQ,GAAK,MAC1FE,UAAW,EACXC,WAAY,CAAE3S,MAAO2G,EAAO4L,SAAW,IAAO5L,EAAO8L,GAAK,GAAM,UAAY,eAGhF7T,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,uCACN8C,MAAOV,EAAOiM,yBAA2BjM,EAAOiM,yBAAyBJ,QAAQ,GAAK,MACtFE,UAAW,SAKjBhT,gBAAC2L,KAAG,CAACC,OAAQ,GAAIzM,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACxCR,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,wBACN8C,MAAOV,EAAOkM,YAAc,GAAGlM,EAAOkM,YAAYL,QAAQ,MAAQ,MAClEG,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAOV,EAAOmM,UAAY,GAAGnM,EAAOmM,UAAUN,QAAQ,QAAU,MAChEG,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,kBACN8C,MAAOV,EAAOoM,cAAgB,GAAGpM,EAAOoM,cAAcP,QAAQ,QAAW7L,EAAOqM,WAAa,GAAGrM,EAAOqM,WAAWR,QAAQ,QAAU,MACpIG,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,wBACN8C,MAAOV,EAAOsM,wBAA0B,GAAGtM,EAAOsM,wBAAwBT,QAAQ,MAAS7L,EAAOuM,gBAAkB,GAAGvM,EAAOuM,gBAAgBV,QAAQ,MAAQ,MAC9JG,WAAY,CAAE3S,MAAO,kBAK1B2G,EAAOwM,cAAgBxM,EAAOyM,YAC7B1T,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yCACbR,eAAA,OAAKC,MAAO,CAAEyF,OAAQ,IAAKsF,UAAW,GAAIxK,SACxCR,eAACyU,KAAmB,CAAC/T,MAAM,OAAOgF,OAAO,OAAMlF,SAC7CM,gBAAC4T,KAAS,CACR/c,KAAMoQ,EAAOwM,aAAa7L,IAAI,CAACiM,EAAmBvI,KAAa,CAC7DwI,MAAOxI,EAAQ,EACfyI,2BAAMF,EACNG,2BAAM/M,EAAOyM,WAAWpI,IAAU,QAEpCvG,OAAQ,CAAEpB,IAAK,EAAGmB,MAAO,GAAIpB,KAAM,GAAIE,OAAQ,GAAIlE,SAAA,CAEnDR,eAAC+U,KAAa,CAACC,gBAAgB,QAC/BhV,eAACiV,KAAK,CAACC,QAAQ,UACflV,eAACmV,KAAK,IACNnV,eAACoV,KAAO,IACRpV,eAACqV,KAAM,IACPrV,eAACsV,KAAI,CACHhU,KAAK,WACL4T,QAAQ,2BACRK,OAAO,UACPC,YAAa,EACbC,KAAK,IAEPzV,eAACsV,KAAI,CACHhU,KAAK,WACL4T,QAAQ,2BACRK,OAAO,UACPC,YAAa,EACbC,KAAK,WAKbzV,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BM,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,6BACduH,EAAOwM,aAAanW,OAAO,mDAC0B,QAApD8U,EAACnL,EAAOwM,aAAaxM,EAAOwM,aAAanW,OAAS,UAAE,IAAA8U,OAAA,EAAnDA,EAAqDU,QAAQ,GAAG,4CACjB,QAAhDT,EAACpL,EAAOyM,WAAWzM,EAAOyM,WAAWpW,OAAS,UAAE,IAAA+U,OAAA,EAA/CA,EAAiDS,QAAQ,WAMzE7L,EAAO2N,eAAiB3N,EAAO4N,QAAU5N,EAAO2N,cAActX,OAAS,GACtE0C,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+DACbR,eAAA,OAAKC,MAAO,CAAEyF,OAAQ,IAAKsF,UAAW,GAAIxK,SACxCR,eAACyU,KAAmB,CAAC/T,MAAM,OAAOgF,OAAO,OAAMlF,SAC7CM,gBAAC4T,KAAS,CACR/c,KAAMoQ,EAAO2N,cAAchN,IAAI,CAACkN,EAAgBxJ,KAAa,CAC3DA,MAAOA,EAAQ,EACfyJ,qBAAKD,EACLE,qBAAK/N,EAAO4N,OAAOvJ,MAErBvG,OAAQ,CAAEpB,IAAK,EAAGmB,MAAO,GAAIpB,KAAM,GAAIE,OAAQ,GAAIlE,SAAA,CAEnDR,eAAC+U,KAAa,CAACC,gBAAgB,QAC/BhV,eAACiV,KAAK,CAACC,QAAQ,UACflV,eAACmV,KAAK,IACNnV,eAACoV,KAAO,IACRpV,eAACqV,KAAM,IACPrV,eAACsV,KAAI,CACHhU,KAAK,WACL4T,QAAQ,qBACRK,OAAO,UACPC,YAAa,EACbC,KAAK,IAEPzV,eAACsV,KAAI,CACHhU,KAAK,WACL4T,QAAQ,qBACRK,OAAO,UACPC,YAAa,EACbC,KAAK,WAKbzV,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BM,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,4BACfuH,EAAO2N,cAActX,OAAO,uFAC1B2J,EAAO4L,SAAW5L,EAAO4L,SAASC,QAAQ,GAAM7L,EAAO8L,GAAK9L,EAAO8L,GAAGD,QAAQ,GAAK,MAAO,gCAC3F7L,EAAOiM,yBAA2BjM,EAAOiM,yBAAyBJ,QAAQ,GAAK,cAQ7F7L,EAAOgO,iBACN/V,eAACuB,KAAK,CACJC,QAAQ,uCACRC,YACEX,gBAAA,OAAAN,SAAA,CACEM,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAEuH,EAAOgO,mBACnChO,EAAOiO,oBACNlV,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,sDAAkB,IAAEuH,EAAOiO,sBAEvCjO,EAAOkO,kBACNnV,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAEuH,EAAOkO,oBAErClO,EAAOmO,gBACNpV,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,sDAAkB,IAAEuH,EAAOmO,kBAEvCnO,EAAOiM,0BACNlT,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAEuH,EAAOiM,yBAAyBJ,QAAQ,MAEtE7L,EAAOoO,eACNrV,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAEuH,EAAOoO,iBAErCpO,EAAOqO,kBACNtV,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,8BAAc,IAAEuH,EAAOqO,iBAAiBxC,QAAQ,GAAG,gBAIpEtS,KAAK,OACLI,UAAQ,UA+zBL2U,OAvzBqBA,KAClC,MAAOxP,EAAYC,GAAiBC,mBAA6B,UAC1DuP,EAAcC,GAAmBxP,mBAAc,OAC/CvO,EAAQge,GAAazP,mBAAS,KAC9BM,EAAgBC,GAAqBP,mBAAmB,KACxD0P,EAAcC,GAAmB3P,mBAAiB,KAClDY,EAAcC,GAAmBb,oBAAS,IAG1C4P,EAAeC,GAAoB7P,mBAAmB,CAAC,SACvD8P,EAAmBC,GAAwB/P,mBAAoC,CACpFgQ,IAAK,CAAC,kBAIDC,EAAcC,GAAmBlQ,mBAAS,OAC1CmQ,EAAWC,GAAgBpQ,mBAAS,KACpC7N,EAAQke,GAAarQ,mBAAS,MAC9BsQ,EAAgBC,GAAqBvQ,mBAAS,KAC9CwQ,EAAYC,GAAiBzQ,mBAAS,KACtC0Q,EAAWC,GAAgB3Q,mBAAS,IACpCzN,EAASqe,GAAc5Q,mBAAS,KAChC6Q,EAAcC,GAAmB9Q,mBAAS,KAG1C+Q,EAAUC,GAAehR,oBAAS,IAClCc,EAAUC,GAAef,mBAAS,IAClCiR,EAAiBC,GAAsBlR,mBAAc,OACrDmR,EAAmBC,GAAwBpR,mBAAiB,KAG7D,mBAAEuL,EAAkB,wBAAES,EAAuB,oBAAE3B,GAAwBf,MACtE+H,EAAkBC,GAAuBtR,oBAAS,IAGlDuR,EAAsBC,GAA2BxR,mBAAc,OAC/DyR,EAAwBC,GAA6B1R,mBAAiB,KACtE2R,EAAqBC,IAA0B5R,mBAAiB,IAGjE6R,GAAyB7F,EAAwB,YAGjD8F,GAAyBhL,IAC7B8K,GAAuB9K,GACvB,MAAMiL,EAAeF,GAAuB9F,KAAKnK,GAAQA,EAAK0I,UAAYxD,GACtEiL,GAAgBA,EAAa/Q,QAAU+Q,EAAa/Q,OAAOgR,UAC7DR,EAAwBO,EAAa/Q,QACrC0Q,EAA0B/K,OAAOC,KAAKmL,EAAa/Q,OAAOgR,SAAS,MAKvEhZ,oBAAU,KACRqR,KACC,CAACA,IAGJrR,oBAAU,KACR,GAAI6Y,GAAuBxa,OAAS,IAAMsa,EAAqB,CAC7D,MAAMM,EAAaJ,GAAuBA,GAAuBxa,OAAS,GAC1Eya,GAAsBG,EAAW3H,QACnC,GACC,CAACuH,GAAwBF,IAG5B,MACMO,GAAkB,CACtBlC,IAAK,CAAC,cAAe,cAAe,cAAe,eACnDmC,IAAK,CAAC,cAAe,eACrBC,KAAM,CAAC,QAIHC,GAAgBje,UACpB,GAAK3C,EAAL,CAEAoP,GAAgB,GAChB,IACE,MAAMlR,QAAiB6B,EAA8BC,GACrD8O,EAAkB5Q,EAASiB,KAAKoR,OAAS,GAC3C,CAAE,MAAOxS,GAAa,IAADI,EAAA+E,EACnB8F,KAAQjL,OAAoB,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAU,oDAC9C2L,EAAkB,GACpB,CAAC,QACCM,GAAgB,EAClB,CAXmB,GAerB7H,oBAAU,KACR,GAAmB,UAAf8G,GAA0BrO,GAAUA,EAAO4F,OAAS,EAAG,CACzD,MAAM2L,EAAQJ,WAAW,KACvByP,MACC,MAEH,MAAO,IAAMpP,aAAaD,EAC5B,GAEC,CAAClD,EAAYrO,IAGhB,MAAMyR,GAAc,CAClB5N,KAAM,OACN6N,UAAU,EACVC,OAAQ,OACRC,aAAcA,KAAM,EACpBC,SAAWC,IACLA,EAAKC,SAASnM,OAAS,EACzBmY,EAAgBjM,EAAKC,SAAS,IAE9BgM,EAAgB,QAqNtB,OACEzV,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,2DACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,4OAIvBR,eAAC2K,KAAO,IAGR3K,eAACS,KAAI,CAACkF,MAAM,qBAAML,UAAU,gBAAe9E,SACzCM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAChEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yCACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAO5B,EACPwD,SAAWI,GAAM3D,EAAc2D,EAAEM,OAAOtC,OACxCxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,MAAM,QAAOjI,SAAC,4CACrBR,eAAC6K,KAAK,CAACpC,MAAM,SAAQjI,SAAC,sCAKV,UAAfqG,GACC/F,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbM,gBAACuB,KAAMyI,MAAK,CAACI,SAAO,EAACjL,MAAO,CAAE+K,UAAW,EAAG5K,QAAS,QAASI,SAAA,CAC5DR,eAACqC,KAAK,CACJoG,MAAOjQ,EACP6R,SAAWI,GAAM+L,EAAU/L,EAAEM,OAAOtC,OACpCjG,YAAY,6BACZvC,MAAO,CAAEoF,KAAM,KAEjBrF,eAAC2C,KAAM,CACLrB,KAAK,UACL4C,QAASkV,GACTte,QAAS6M,EACTwD,UAAW3S,EACXyH,MAAO,CAAEmL,WAAY,GAAI5K,SAC1B,uBAMLM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACqL,KAAI,CAACC,SAAU3D,EAAanH,SAC3BR,eAAC2G,KAAM,CACL8B,MAAOgO,EACPpM,SAAUqM,EACVlU,YAAY,oCACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnClQ,QAAS6M,EAAanH,SAErB6G,EAAeqB,IAAKrQ,GACnB2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,cAWT,WAAfwO,GACC/F,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbM,gBAAC0F,GAAO,IAAKyD,GAAahK,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAChDR,eAAA,KAAGsF,UAAU,uBAAsB9E,SACjCR,eAACuL,KAAa,MAEhBvL,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,sFAC/BR,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,qFAUzCR,eAACS,KAAI,CAACkF,MAAM,yDAAYL,UAAU,gBAAe9E,SAC/CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAChEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAAC2G,KAAM,CACL5B,KAAK,WACL0D,MAAOkO,EACPtM,SA9SkBgP,IAC5BzC,EAAiByC,GAEjB,MAAMC,EAAe,IAAKzC,GAC1BwC,EAAMjhB,QAAQmhB,KACPD,EAAaC,IAASN,GAAgBM,KACzCD,EAAaC,GAAQ,CAACN,GAAgBM,GAAsC,OAIhF7L,OAAOC,KAAK2L,GAAclhB,QAAQmhB,IAC3BF,EAAMrH,SAASuH,WACXD,EAAaC,KAGxBzC,EAAqBwC,IAgSX9W,YAAY,iCACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GAAIxK,SAnW3B,CAAC,MAAO,MAAO,QAqWVkI,IAAK6Q,GACpBvZ,eAAC0G,GAAM,CAAY+B,MAAO8Q,EAAK/Y,SAC5B+Y,GADUA,SAOlB5C,EAAcjO,IAAK6Q,GAClBzY,gBAAA,OAAAN,SAAA,CACEM,gBAAC1B,GAAI,CAACwL,QAAM,EAAApK,SAAA,CAAE+Y,EAAK,qCACnBvZ,eAACwZ,KAAS1O,MAAK,CACbrC,MAAOoO,EAAkB0C,IAAS,GAClClP,SAAWoP,GA5SIC,EAACC,EAAkBF,KAC9C3C,EAAqBrN,IAAI,IACpBA,EACH,CAACkQ,GAAWF,MAySuBC,CAAqBH,EAAME,GACpDxZ,MAAO,CAAE+K,UAAW,GAAIxK,UAEtByY,GAAgBM,IAAyC,IAAI7Q,IAAKkR,GAClE5Z,eAACwZ,KAAQ,CAAgB/Q,MAAOmR,EAASpZ,SACtCoZ,GADYA,QARXL,SAmBhBzY,gBAACL,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACgE,KAAe,IAChBhE,eAAA,QAAAQ,SAAM,4CAGV8E,UAAU,gBAAe9E,SAAA,CAEzBM,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAIlM,SAAA,CAEpBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHQ,KAAK,QACL0E,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACyD,KAAkB,IACnBzD,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,gCAEhBA,SAEDM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,SAAShB,MAAO,CAAES,MAAO,QAASF,SAAA,CACjEM,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+BAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAOuO,EACP3M,SAAW5B,GAAUwO,EAAgBxO,GAAS,MAC9CrG,IAAK,KACL2X,IAAK,EACLC,KAAM,KACN/Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,gBAIlB1B,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,qCAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAOyO,EACP7M,SAAW5B,GAAU0O,EAAa1O,GAAS,IAC3CrG,IAAK,EACL2X,IAAK,IACL9Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,YAIlB1B,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,qCAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAOvP,EACPmR,SAAW5B,GAAU2O,EAAU3O,GAAS,KACxCrG,IAAK,EACL2X,IAAK,IACL9Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,oBASxBxC,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHQ,KAAK,QACL0E,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACgE,KAAe,IAChBhE,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,gCAEhBA,SAEDM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,SAAShB,MAAO,CAAES,MAAO,QAASF,SAAA,CACjEM,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,qCAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAO4O,EACPhN,SAAW5B,GAAU6O,EAAkB7O,GAAS,IAChDrG,IAAK,EACL2X,IAAK,IACL9Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,YAIlB1B,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,2CAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAO8O,EACPlN,SAAW5B,GAAU+O,EAAc/O,GAAS,IAC5CrG,IAAK,GACL2X,IAAK,IACLC,KAAM,GACN/Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,YAIlB1B,gBAAC2L,KAAG,CAACoN,MAAM,SAAQrZ,SAAA,CACjBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yBAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAAC8Z,KAAW,CACVrR,MAAOgP,EACPpN,SAAW5B,GAAUiP,EAAajP,GAAS,GAC3CrG,IAAK,EACL2X,IAAK,GACL9Z,MAAO,CAAES,MAAO,QAChB8B,YAAY,WAIlB1B,gBAAA,OAAKb,MAAO,CAAES,MAAO,QAASF,SAAA,CAC5BM,gBAAC2L,KAAG,CAACoN,MAAM,SAAS5Z,MAAO,CAAEoB,aAAc,GAAIb,SAAA,CAC7CR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,iCAEfR,eAAC2M,KAAG,CAACC,KAAM,GAAI3M,MAAO,CAAEiB,UAAW,SAAUV,SAC3CR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,SAAElH,SAGhB0G,eAACka,KAAM,CACLzR,MAAOnP,EACP+Q,SAAUsN,EACVvV,IAAK,EACL2X,IAAK,GACLC,KAAM,IACNG,MAAO,CACL,EAAG,IACH,GAAK,MACL,GAAK,MACL,GAAK,sBAUnBna,eAACyM,KAAG,CAACxM,MAAO,CAAE+K,UAAW,IAAKxK,SAC5BR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHQ,KAAK,QACL0E,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACuL,KAAa,IACdvL,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,4CAEhBA,SAEDR,eAACqC,KAAK,CACJoG,MAAOmP,EACPvN,SAAWI,GAAMoN,EAAgBpN,EAAEM,OAAOtC,OAC1CjG,YAAY,6BACZvB,KAAK,QACLqB,OAAQtC,eAACuL,KAAa,eAQhCvL,eAACS,KAAI,CAAC6E,UAAU,gBAAgBK,MAAM,2BAAMnF,SAC1CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,SAAShB,MAAO,CAAES,MAAO,QAASF,SAAA,CACjEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAO2P,EACP/N,SAAWI,GAAM4N,EAAoB5N,EAAEM,OAAOtC,OAC9CxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,OAAO,EAAKjI,SACjBM,gBAACC,KAAK,CAAAP,SAAA,CAAC,mDAELR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,IAAKrC,SAAC,oFAKpDR,eAAC6K,KAAK,CAACpC,OAAO,EAAMjI,SAClBM,gBAACC,KAAK,CAAAP,SAAA,CAAC,2BAELR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,IAAKrC,SAAC,wHAQvD4X,GACCpY,eAACuB,KAAK,CACJC,QAAQ,uCACRC,YACEX,gBAAA,OAAAN,SAAA,CAAK,+JAEHR,eAAA,SAAM,kFACQA,eAAA,UAAAQ,SAAQ,mEAAqB,6DAG/Cc,KAAK,OACLI,UAAQ,SAOhBZ,gBAACL,KAAI,CAAC6E,UAAU,gBAAe9E,SAAA,CAC7BR,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLqC,KAAMtD,eAACwL,KAAkB,IACzBtH,QA7hBoB/I,UAE1B,GAAmB,WAAf0L,IAA4ByP,EAE9B,YADA9U,KAAQjL,MAAM,qCAIhB,GAAmB,UAAfsQ,KAA4BrO,IAAWie,GAEzC,YADAjV,KAAQjL,MAAM,qCAIhB,GAA6B,IAAzBogB,EAAcvY,OAEhB,YADAoD,KAAQjL,MAAM,0DAQhB,GAJ0BogB,EAAcyD,KAAKb,GAC3C1C,EAAkB0C,IAAS1C,EAAkB0C,GAAMnb,OAAS,GAG9D,CAKA2Z,GAAY,GACZjQ,EAAY,GACZmQ,EAAmB,MACnBE,EAAqB,IAErB,IACE,GAAIC,EAAkB,CAEpB,MAAMjhB,EAAW,IAAIa,SAEF,WAAf6O,EACF1P,EAASE,OAAO,OAAQif,EAAa7K,gBAErCtU,EAASE,OAAO,UAAWmB,GAC3BrB,EAASE,OAAO,gBAAiBof,IAGnCtf,EAASE,OAAO,iBAAkBsB,KAAKC,UAAU+d,IACjDxf,EAASE,OAAO,qBAAsBsB,KAAKC,UAAUie,IACrD1f,EAASE,OAAO,gBAAiB2f,EAAahe,YAC9C7B,EAASE,OAAO,aAAc6f,EAAUle,YACxC7B,EAASE,OAAO,SAAU6B,EAAOF,YACjC7B,EAASE,OAAO,kBAAmBggB,EAAere,YAClD7B,EAASE,OAAO,cAAekgB,EAAWve,YAC1C7B,EAASE,OAAO,aAAcogB,EAAUze,YACxC7B,EAASE,OAAO,UAAWiC,EAAQN,YACnC7B,EAASE,OAAO,gBAAiBugB,GAYjC,kBATqBtF,EAAmBnb,KAGtCqK,KAAQ+H,QAAQ,wLAEhBwO,GAAY,GACZjQ,EAAY,IAIhB,CAGA,IAAIpR,EAEJ,GAAmB,WAAfmQ,EAAyB,CAC3B,MAAM1P,EAAW,IAAIa,SACrBb,EAASE,OAAO,OAAQif,EAAa7K,eACrCtU,EAASE,OAAO,iBAAkBsB,KAAKC,UAAU+d,IACjDxf,EAASE,OAAO,qBAAsBsB,KAAKC,UAAUie,IACrD1f,EAASE,OAAO,gBAAiB2f,EAAahe,YAC9C7B,EAASE,OAAO,aAAc6f,EAAUle,YACxC7B,EAASE,OAAO,SAAU6B,EAAOF,YACjC7B,EAASE,OAAO,kBAAmBggB,EAAere,YAClD7B,EAASE,OAAO,cAAekgB,EAAWve,YAC1C7B,EAASE,OAAO,aAAcogB,EAAUze,YACxC7B,EAASE,OAAO,UAAWiC,EAAQN,YACnC7B,EAASE,OAAO,gBAAiBugB,GAGjC,MAAMlM,EAAmBC,YAAY,KACnC7D,EAAa2B,GACPA,GAAQ,IACVmC,cAAcF,GACPjC,GAEFA,EAAO,IAEf,KAEH/S,QAAiB6B,EAA4BpB,GAC7CyU,cAAcF,EAChB,KAAO,CAEL,MAAM2O,EAAoB,CACxB5hB,QAASD,EACTE,cAAe+d,EACf5d,eAAgB8d,EAChB7d,mBAAoB+d,EACpB9d,cAAeie,EACf/d,WAAYie,EACZhe,OAAQA,EACRC,gBAAiBke,EACjBje,YAAame,EACble,WAAYoe,EACZne,QAASA,EACTC,cAAeqe,GAIXlM,EAAmBC,YAAY,KACnC7D,EAAa2B,GACPA,GAAQ,IACVmC,cAAcF,GACPjC,GAEFA,EAAO,IAEf,KAEH/S,QAAiB6B,EAAiC8hB,GAClDzO,cAAcF,EAChB,CAEA5D,EAAY,KAGZuB,QAAQC,IAAI,wCAAW5S,EAASiB,MAEhCsgB,EAAmBvhB,EAASiB,MAGxBjB,EAASiB,KAAKohB,SAAWrL,OAAOC,KAAKjX,EAASiB,KAAKohB,SAAS3a,OAAS,GACvE+Z,EAAqBzK,OAAOC,KAAKjX,EAASiB,KAAKohB,SAAS,IAG1DvX,KAAQ+H,QAAQ,8CAGZ7S,EAASiB,KAAK2iB,aAChB9Y,KAAQ8I,KAAK,yCAAW5T,EAASiB,KAAK2iB,cAG1C,CAAE,MAAO/jB,GACP8S,QAAQ9S,MAAM,wCAAWA,GACzB8S,QAAQ9S,MAAM,4BAASA,EAAMG,UAG7B,IAAI6a,EAAe,uCACE,IAADgJ,EAAAC,EAApB,GAAIjkB,EAAMG,SACe,QAAvB6jB,EAAIhkB,EAAMG,SAASiB,YAAI,IAAA4iB,GAAnBA,EAAqB5e,OACvB4V,EAAehb,EAAMG,SAASiB,KAAKgE,OACP,QAAvB6e,EAAIjkB,EAAMG,SAASiB,YAAI,IAAA6iB,GAAnBA,EAAqBhZ,QAC9B+P,EAAehb,EAAMG,SAASiB,KAAK6J,QAC1BjL,EAAMG,SAAS+jB,aACxBlJ,EAAe,6BAAShb,EAAMG,SAASE,UAAUL,EAAMG,SAAS+jB,mBAEzDlkB,EAAMiL,UACf+P,EAAehb,EAAMiL,SAGvBA,KAAQjL,MAAMgb,EAChB,CAAC,QACCwG,GAAY,EACd,CAjJA,MAFEvW,KAAQjL,MAAM,qGAwgBVuE,QAASgd,EACT3M,WAlXa,WAAftE,EACKyP,GAAgBK,EAAcvY,OAAS,EAEvC5F,GAAUie,GAAgBE,EAAcvY,OAAS,GAgXpDkH,UAAU,gBAAe9E,SAExBsX,EAAW,8BAAY,yCAIzBA,GACChX,gBAAA,OAAKwE,UAAU,mBAAkB9E,SAAA,CAC/BR,eAACZ,GAAI,CAAAoB,SAAC,mCACNR,eAAC+L,KAAQ,CAACC,QAASnE,EAAUjR,OAAO,cAKvCohB,GAAmBA,EAAgBe,SAClC/Y,eAAA,OAAKC,MAAO,CAAE+K,UAAW,IAAKxK,SAC5BR,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YACEX,gBAAA,OAAAN,SAAA,CACER,eAAA,KAAAQ,SAAG,2DACFwX,EAAgBsC,aACfxZ,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAEwX,EAAgBsC,eAE9C5M,OAAOE,QAAQoK,EAAgBe,SAASrQ,IAAI7M,IAAA,IAAEwH,EAAK0E,GAAsBlM,EAAA,OACxEiF,gBAAA,OAAeb,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CACrCM,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,gDAAiB,IAAE6C,KAC9BvC,gBAAA,KAAAN,SAAA,CAAG,yCAASuH,EAAOgO,mBACnBjV,gBAAA,KAAAN,SAAA,CAAG,qDAAWuH,EAAOiO,wBAHb3S,QAQhB/B,KAAK,UACLI,UAAQ,EACRzB,MAAO,CAAE+K,UAAW,QAM3BgN,GAAmBA,EAAgBe,SAAWrL,OAAOC,KAAKqK,EAAgBe,SAAS3a,OAAS,GAC3F4B,eAACS,KAAI,CAACkF,MAAM,iFAAgBL,UAAU,gBAAe9E,SACnDM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAChEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yFACbR,eAAC2G,KAAM,CACL8B,MAAOyP,EACP7N,SAAU8N,EACVlY,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,+DAAYhC,SAEvBkN,OAAOC,KAAKqK,EAAgBe,SAASrQ,IAAKrF,GACzCrD,eAAC0G,GAAM,CAAW+B,MAAOpF,EAAI7C,SAC1B6C,GADUA,SAOlB6U,GAAqBF,EAAgBe,QAAQb,IAC5ClY,eAACiT,GAAqB,CACpBG,UAAW8E,EACXnQ,OAAQiQ,EAAgBe,QAAQb,UAQzCU,GAAuBxa,OAAS,GAC/B0C,gBAACL,KAAI,CAACkF,MAAM,uCAASL,UAAU,gBAAgBrF,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACtER,eAACuB,KAAK,CACJC,QAAQ,6CACRC,YAAY,yMACZH,KAAK,UACLI,UAAQ,EACRzB,MAAO,CAAEoB,aAAc,MAEzBP,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAEhEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbR,eAAC2G,KAAM,CACL8B,MAAOiQ,EACPrO,SAAUwO,GACV5Y,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,qEAAahC,SAExBoY,GAAuBlQ,IAAKC,GAC3B3I,eAAC0G,GAAM,CAAoB+B,MAAOE,EAAK0I,QAAQ7Q,SAC5CmI,EAAK0I,QAAQW,SAAS,KACrB,GAAGrJ,EAAK0I,QAAQkC,MAAM,KAAK,OAAO,IAAIvV,KAAK2K,EAAK+R,YAAc/R,EAAKgS,YAAYC,oBAC/E,gBAAMjS,EAAK0I,QAAQC,UAAU,EAAG,UAAU,IAAItT,KAAK2K,EAAK+R,YAAc/R,EAAKgS,YAAYC,qBAH9EjS,EAAK0I,eAWvBiH,GAAwBA,EAAqBS,SAC5CjY,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yFACbR,eAAC2G,KAAM,CACL8B,MAAO+P,EACPnO,SAAUoO,EACVxY,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,+DAAYhC,SAEvBkN,OAAOC,KAAK2K,EAAqBS,SAASrQ,IAAKrF,GAC9CrD,eAAC0G,GAAM,CAAW+B,MAAOpF,EAAI7C,SAC1B6C,GADUA,SASpBmV,GAA0BF,GAAwBA,EAAqBS,QAAQP,IAC9ExY,eAACiT,GAAqB,CACpBG,UAAWoF,EACXzQ,OAAQuQ,EAAqBS,QAAQP,kBC5/BrD,MAAQrZ,MAAK,GAAEC,KAAK,IAAIC,MAChBmH,QAAQ,IAAIC,MACZC,OAAO,IAAIC,KAGbkU,GAAkEzf,IAAiB,IAAhB,OAAE2M,GAAQ3M,EAGjF,OACE0F,gBAAA,OAAAN,SAAA,CACER,eAACyM,KAAG,CAACC,OAAQ,GAAIzM,MAAO,CAAEoB,aAAc,IAAKb,SAC3CM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACwT,KAAS,CACR7N,MAAM,+DACN8C,MAAOV,EAAO+S,oBACdhH,UAAW,EACXC,WAAY,CAAE3S,MAAO,aAEtB2G,EAAO+S,qBACN9a,eAACuB,KAAK,CACJC,QAAQ,mNACRF,KAAK,UACLI,UAAQ,EACRzB,MAAO,CAAE+K,UAAW,gBAUE6B,IAA5B9E,EAAOqO,uBAAyDvJ,IAAvB9E,EAAOkM,kBAAkDpH,IAArB9E,EAAOmM,gBAAoDrH,IAAzB9E,EAAOoM,oBAAkEtH,IAAnC9E,EAAOsM,0BAC5JvT,gBAAA,OAAKb,MAAO,CAAEoB,aAAc,IAAKb,SAAA,CAC/BR,eAACb,GAAK,CAACgC,MAAO,EAAEX,SAAC,yCACjBM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,MACeqM,IAA5B9E,EAAOqO,kBACNpW,eAAC2M,KAAG,CAACtH,KAAK,OAAM7E,SACdR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAOV,EAAOqO,iBACdtC,UAAW,EACXiH,OAAO,SACPhH,WAAY,CAAE3S,MAAO,oBAIHyL,IAAvB9E,EAAOkM,aACNjU,eAAC2M,KAAG,CAACtH,KAAK,OAAM7E,SACdR,eAACwT,KAAS,CACR7N,MAAM,wBACN8C,MAAOV,EAAOkM,YACdH,UAAW,EACXiH,OAAO,IACPhH,WAAY,CAAE3S,MAAO,oBAILyL,IAArB9E,EAAOmM,WACNlU,eAAC2M,KAAG,CAACtH,KAAK,OAAM7E,SACdR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAOV,EAAOmM,UACdJ,UAAW,EACXiH,OAAO,KACPhH,WAAY,CAAE3S,MAAO,oBAIDyL,IAAzB9E,EAAOoM,eACNnU,eAAC2M,KAAG,CAACtH,KAAK,OAAM7E,SACdR,eAACwT,KAAS,CACR7N,MAAM,kBACN8C,MAAOV,EAAOoM,cACdL,UAAW,EACXiH,OAAO,KACPhH,WAAY,CAAE3S,MAAO,oBAISyL,IAAnC9E,EAAOsM,yBACNrU,eAAC2M,KAAG,CAACtH,KAAK,OAAM7E,SACdR,eAACwT,KAAS,CACR7N,MAAM,wBACN8C,MAAOV,EAAOsM,wBACdP,UAAW,EACXiH,OAAO,IACPhH,WAAY,CAAE3S,MAAO,yBAcjC4Z,GAAkB,CAAC,MAAO,MAAO,QACjC/B,GAAkB,CACtBlC,IAAK,CAAC,cAAe,cAAe,cAAe,eACnDmC,IAAK,CAAC,cAAe,eACrBC,KAAM,CAAC,QAuzBM8B,OAxyBuBA,KACpC,MAAOpU,EAAYC,GAAiBC,mBAA6B,UAC1DuP,EAAcC,GAAmBxP,mBAAc,OAC/CvO,EAAQge,GAAazP,mBAAS,KAC9BmU,EAAmBC,GAAwBpU,mBAAmB,KAC9DqU,EAAiBC,GAAsBtU,mBAAiB,KACxDuU,EAAiBC,GAAsBxU,oBAAS,IAGhDtN,EAAU+hB,GAAezU,mBAAS,KAClC0U,EAAmBC,GAAwB3U,mBAAmB,KAC9D4U,EAAgBC,GAAqB7U,mBAAmB,KACxD8U,EAAeC,GAAoB/U,oBAAS,IAC5CgV,EAAgBC,GAAqBjV,mBAAgC,WAGrEkV,EAAmBC,GAAwBnV,mBAAiB,KAC5DoV,EAAoBC,GAAyBrV,mBAAiB,KAC9DsV,EAAoBC,GAAyBvV,mBAAiB,KAC9DsM,EAAckJ,GAAmBxV,mBAAiB,KAClDuM,EAAkBkJ,GAAuBzV,mBAAiB,KAC1D0V,EAAqBC,GAA0B3V,oBAAS,IAGxD4V,EAAYC,GAAiB7V,oBAAS,IACtCc,EAAUC,GAAef,mBAAS,IAClCgS,EAAS8D,GAAc9V,mBAA6B,KACpD+V,EAAqBC,GAA0BhW,mBAAiB,IAChEiW,EAAsBC,GAA2BlW,oBAAS,IAG3D,qBAAEwL,EAAoB,wBAAEQ,EAAuB,oBAAE3B,GAAwBf,MACxE6M,EAAoBC,GAAyBpW,oBAAS,IAGtDqW,GAAwBC,IAA6BtW,mBAA6B,KAClF2R,GAAqBC,IAA0B5R,mBAAiB,IAGjEuW,GAA2BvK,EAAwB,cAGnD8F,GAAyBhL,IAC7B8K,GAAuB9K,GACvB,MAAMiL,EAAewE,GAAyBxK,KAAKnK,GAAQA,EAAK0I,UAAYxD,GAE5E,GAAIiL,GAAgBA,EAAa/Q,OAAQ,CAEvC,MAAMwV,EAAgC,CACpCzC,oBAAqBhC,EAAa/Q,OAAO+S,qBAAuB,EAChE0C,WAAY1E,EAAa/Q,OAAOyV,YAAc,2BAC9Chc,QAASsX,EAAa/Q,OAAOvG,SAAW,2BACxC4U,iBAAkB0C,EAAa/Q,OAAOqO,iBACtCnC,YAAa6E,EAAa/Q,OAAOkM,YACjCC,UAAW4E,EAAa/Q,OAAOmM,UAC/BC,cAAe2E,EAAa/Q,OAAOoM,cACnCE,wBAAyByE,EAAa/Q,OAAOsM,yBAG/CgJ,GAA0B,CAACE,GAC7B,GAIFxd,oBAAU,KACRqR,KACC,CAACA,IAGJrR,oBAAU,KACR,GAAIud,GAAyBlf,OAAS,IAAMsa,GAAqB,CAC/D,MAAMM,EAAasE,GAAyBA,GAAyBlf,OAAS,GAC9Eya,GAAsBG,EAAW3H,QACnC,GACC,CAACiM,GAA0B5E,KAG9B,MAAMU,GAAgBje,UACpB,GAAK3C,EAAL,CAEA+iB,GAAmB,GACnB,IACE,MAAM7kB,QAAiB8C,EAAgChB,GACvD2iB,EAAqBzkB,EAASiB,KAAKoR,OAAS,GAC9C,CAAE,MAAOxS,GAAa,IAADI,EAAA+E,EACnB8F,KAAQjL,OAAoB,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAU,uDAC9Cwf,EAAqB,GACvB,CAAC,QACCI,GAAmB,EACrB,CAXmB,GAefkC,GAAkBtiB,UACtB,GAAK1B,EAAL,CAEAqiB,GAAiB,GACjB,IACE,MAAMplB,QAAiB8C,EAAkCC,GACzDiiB,EAAqBhlB,EAASiB,KAAK+lB,WAAa,GAClD,CAAE,MAAOnnB,GAAa,IAAD0F,EAAAC,EACnBsF,KAAQjL,OAAoB,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAU,gEAC9C+f,EAAqB,GACvB,CAAC,QACCI,GAAiB,EACnB,CAXqB,GAwEvB/b,oBAAU,KACR,GAAmB,UAAf8G,GAA0BrO,GAAUA,EAAO4F,OAAS,EAAG,CACzD,MAAM2L,EAAQJ,WAAW,KACvByP,MACC,MAEH,MAAO,IAAMpP,aAAaD,EAC5B,GAEC,CAAClD,EAAYrO,IAEhBuH,oBAAU,KACR,GAAItG,GAAYA,EAAS2E,OAAS,EAAG,CACnC,MAAM2L,EAAQJ,WAAW,KACvB8T,MACC,MAEH,MAAO,IAAMzT,aAAaD,EAC5B,GAEC,CAACtQ,IAGJ,MAAMwQ,GAAc,CAClB5N,KAAM,OACN6N,UAAU,EACVC,OAAQ,OACRC,aAAcA,KAAM,EACpBC,SAAWC,IACLA,EAAKC,SAASnM,OAAS,EACzBmY,EAAgBjM,EAAKC,SAAS,IAE9BgM,EAAgB,QA4MtB,OACEzV,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,uEACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,iNAIvBR,eAAC2K,KAAO,IAGR3K,eAACS,KAAI,CAACkF,MAAM,iCAAQL,UAAU,gBAAe9E,SAC3CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAChEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,yCACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAO5B,EACPwD,SAAWI,GAAM3D,EAAc2D,EAAEM,OAAOtC,OACxCxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,MAAM,QAAOjI,SAAC,4CACrBR,eAAC6K,KAAK,CAACpC,MAAM,SAAQjI,SAAC,sCAKV,UAAfqG,GACC/F,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbM,gBAACuB,KAAMyI,MAAK,CAACI,SAAO,EAACjL,MAAO,CAAE+K,UAAW,EAAG5K,QAAS,QAASI,SAAA,CAC5DR,eAACqC,KAAK,CACJoG,MAAOjQ,EACP6R,SAAWI,GAAM+L,EAAU/L,EAAEM,OAAOtC,OACpCjG,YAAY,6BACZvC,MAAO,CAAEoF,KAAM,KAEjBrF,eAAC2C,KAAM,CACLrB,KAAK,UACL4C,QAASkV,GACTte,QAASwgB,EACTnQ,UAAW3S,EACXyH,MAAO,CAAEmL,WAAY,GAAI5K,SAC1B,uBAMLM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbR,eAACqL,KAAI,CAACC,SAAUgQ,EAAgB9a,SAC9BR,eAAC2G,KAAM,CACL8B,MAAO2S,EACP/Q,SAAUgR,EACV7Y,YAAY,oCACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnClQ,QAASwgB,EAAgB9a,SAExB0a,EAAkBxS,IAAKrQ,GACtB2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,cAWT,WAAfwO,GACC/F,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8DACbM,gBAAC0F,GAAO,IAAKyD,GAAahK,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAChDR,eAAA,KAAGsF,UAAU,uBAAsB9E,SACjCR,eAACuL,KAAa,MAEhBvL,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,sFAC/BR,eAAA,KAAGsF,UAAU,kBAAiB9E,SAAC,qFAUzCR,eAACS,KAAI,CAACkF,MAAM,2BAAOL,UAAU,gBAAe9E,SAC1CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAChEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbM,gBAACuB,KAAMyI,MAAK,CAACI,SAAO,EAACjL,MAAO,CAAE+K,UAAW,EAAG5K,QAAS,QAASI,SAAA,CAC5DR,eAACqC,KAAK,CACJoG,MAAOhP,EACP4Q,SAAWI,GAAM+Q,EAAY/Q,EAAEM,OAAOtC,OACtCjG,YAAY,6BACZvC,MAAO,CAAEoF,KAAM,KAEjBrF,eAAC2C,KAAM,CACLrB,KAAK,UACL4C,QAASuZ,GACT3iB,QAAS+gB,EACT1Q,UAAW1R,EACXwG,MAAO,CAAEmL,WAAY,GAAI5K,SAC1B,uBAMLM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAOsT,EACP1R,SAAWI,GAAMuR,EAAkBvR,EAAEM,OAAOtC,OAC5CxI,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAExBR,eAAC6K,KAAK,CAACpC,MAAM,SAAQjI,SAAC,yCACtBR,eAAC6K,KAAK,CAACpC,MAAM,WAAUjI,SAAC,2DAIR,WAAnBub,EACCjb,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbR,eAACqL,KAAI,CAACC,SAAUuQ,EAAcrb,SAC5BR,eAAC2G,KAAM,CACL8B,MAAOwT,EACP5R,SA3XesT,IAC7BzB,EAAqByB,GAErBvB,EAAsB,IACtBE,EAAsB,IACtBC,EAAgB,IAChBC,EAAoB,IACpBE,GAAuB,GAlDFvhB,WACrB,GAAKwiB,GAAclkB,EAAnB,CAEAwjB,GAAwB,GACxB,IAEE,MAAMvmB,QAAiB8C,EAAoCmkB,EAAWlkB,GAEtE,GAAI/C,EAASiB,KAAM,CACjB,MAAMimB,EAAgBlnB,EAASiB,KAG/BykB,EAAsBwB,EAAcC,iBAAmB,IACvDvB,EAAsBsB,EAAcE,iBAAmB,IACvDvB,EAAgBqB,EAAcjE,UAAY,IAC1C6C,EAAoBoB,EAAchE,UAAY,IAG1CgE,EAAcC,iBAAmBD,EAAcE,iBACjDtc,KAAQ+H,QAAQ,iEAGdqU,EAAcjE,UAAYiE,EAAchE,UAC1CpY,KAAQ+H,QAAQ,sHAAuBqU,EAAcjE,cAAciE,EAAchE,YACjF8C,GAAuB,KAEvBlb,KAAQoI,QAAQ,2JAChB8S,GAAuB,GAE3B,CACF,CAAE,MAAOnmB,GAAa,IAADsV,EAAAC,EACnBtK,KAAQjL,OAAoB,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAU,oDAE9C,MAAMoiB,EAAqBJ,EAAUK,QAAQ,kBAAmB,IAChE5B,EAAsB,GAAG2B,iBACzBzB,EAAsB,GAAGyB,kBACzBrB,GAAuB,EACzB,CAAC,QACCO,GAAwB,EAC1B,CAtCmC,GAoDnCgB,CAAeN,IAkXDnb,YAAY,mMACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnClQ,QAAS+gB,EAAcrb,SAEtBib,EAAkB/S,IAAKrQ,GACtB2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,QAOlB4jB,GACCjc,eAAA,OAAKC,MAAO,CAAE+K,UAAW,IAAKxK,SAC5BM,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,qDACvBR,eAACqL,KAAI,CAACC,SAAU0R,EAAqBxc,SACnCR,eAAA,OAAKC,MAAO,CAAE+K,UAAW,EAAGzK,QAAS,GAAI2d,gBAAiB,UAAWrd,aAAc,GAAIL,SACpFwc,EACChd,eAAA,KAAAQ,SAAG,oEAEHM,gBAAAmK,YAAA,CAAAzK,SAAA,CACEM,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,8BAAc,IAAE2b,GAAsB,wBACjDrb,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,0CAAgB,IAAE6b,GAAsB,yBACjDI,GAAuBpJ,GAAgBC,GACvCxS,gBAAAmK,YAAA,CAAAzK,SAAA,CACEM,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,kBAAY,IAAE6S,KACzBvS,gBAAA,KAAAN,SAAA,CAAGR,eAAA,UAAAQ,SAAQ,8BAAc,IAAE8S,kBASxCmJ,GACC3b,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mFACbR,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BM,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDR,eAAC2G,KAAM,CACL8B,MAAO4K,EACPhJ,SAAUkS,EACV/Z,YAAY,+DACZvC,MAAO,CAAES,MAAO,QAASF,SAExBwa,GAAgBtS,IAAK6Q,GACpBvZ,eAAC0G,GAAM,CAAY+B,MAAO8Q,EAAK/Y,SAC5B+Y,GADUA,MAMhBlG,GACCrT,eAAC2G,KAAM,CACL8B,MAAO6K,EACPjJ,SAAUmS,EACVha,YAAa,oDAAY6Q,6BACzBpT,MAAO,CAAES,MAAO,QAASF,UAEvByY,GAAgB5F,IAAiD,IAAI3K,IAAKkR,GAC1E5Z,eAAC0G,GAAM,CAAgB+B,MAAOmR,EAASpZ,SACpCoZ,GADUA,sBAenC9Y,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,uEACbR,eAACqL,KAAI,CAACC,SAAUuQ,EAAcrb,SAC5BR,eAAC2G,KAAM,CACL5B,KAAK,WACL0D,MAAOkT,EACPtR,SAAUuR,EACVpZ,YAAY,6FACZvC,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnClQ,QAAS+gB,EAAcrb,SAEtBib,EAAkB/S,IAAKrQ,GACtB2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,WASO,IAA7BojB,EAAkBrd,SAAiByd,GAClC7b,eAACuB,KAAK,CACJC,QAAQ,6CACRC,YAAY,qOACZH,KAAK,UACLI,UAAQ,SAOhB1B,eAACS,KAAI,CAAC6E,UAAU,gBAAgBK,MAAM,2BAAMnF,SAC1CM,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,SAAShB,MAAO,CAAES,MAAO,QAASF,SAAA,CACjEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbM,gBAAC+J,KAAMC,MAAK,CACVrC,MAAOyU,EACP7S,SAAWI,GAAM0S,EAAsB1S,EAAEM,OAAOtC,OAChDxI,MAAO,CAAE+K,UAAW,GACpBG,SAA6B,aAAnB4Q,EAA+Bvb,SAAA,CAEzCR,eAAC6K,KAAK,CAACpC,OAAO,EAAKjI,SACjBM,gBAACC,KAAK,CAAAP,SAAA,CAAC,mDAELR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,IAAKrC,SAAC,oFAKpDR,eAAC6K,KAAK,CAACpC,OAAO,EAAMjI,SAClBM,gBAACC,KAAK,CAAAP,SAAA,CAAC,2BAELR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,IAAKrC,SAAC,wHAQvD0c,GAAyC,WAAnBnB,GACrB/b,eAACuB,KAAK,CACJC,QAAQ,uCACRC,YACEX,gBAAA,OAAAN,SAAA,CAAK,+JAEHR,eAAA,SAAM,kFACQA,eAAA,UAAAQ,SAAQ,mEAAqB,6DAG/Cc,KAAK,OACLI,UAAQ,IAIQ,aAAnBqa,GACC/b,eAACuB,KAAK,CACJC,QAAQ,6CACRC,YAAY,2KACZH,KAAK,UACLI,UAAQ,SAOhBZ,gBAACL,KAAI,CAAC6E,UAAU,gBAAe9E,SAAA,CAC7BR,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLqC,KAAMtD,eAACwL,KAAkB,IACzBtH,QApfsB/I,UAE5B,GAAmB,WAAf0L,IAA4ByP,EAE9B,YADA9U,KAAQjL,MAAM,qCAIhB,GAAmB,UAAfsQ,KAA4BrO,IAAW4iB,GAEzC,YADA5Z,KAAQjL,MAAM,qCAKhB,IAAI4nB,EAMC,GAEL,GAAuB,WAAnBpC,EAA6B,CAC/B,IAAKE,IAAsB5I,IAAiBC,EAE1C,YADA9R,KAAQjL,MAAM,4HAGhB4nB,EAAkB,CAAC,CACjBC,WAAYnC,EACZoC,YAAalC,EACbmC,YAAajC,EACb1C,SAAUtG,EACVuG,SAAUtG,GAEd,KAAO,CACL,GAA8B,IAA1BqI,EAAevd,OAEjB,YADAoD,KAAQjL,MAAM,0DAKhB,MAAMgoB,EAMD,GAEL,IAAK,MAAMZ,KAAahC,EACtB,IACE,MAAMjlB,QAAiB8C,EAAoCmkB,EAAWlkB,GACtE,GAAI/C,EAASiB,KAAM,CACjB,MAAMimB,EAAgBlnB,EAASiB,KACzB0mB,EAAcT,EAAcC,gBAC5BS,EAAcV,EAAcE,gBAC5BnE,EAAWiE,EAAcjE,SACzBC,EAAWgE,EAAchE,SAG3ByE,GAAeC,GAAe3E,GAAYC,GAC5C2E,EAAYlR,KAAK,CACf+Q,WAAYT,EACZU,cACAC,cACA3E,WACAC,aAEFpY,KAAQ+H,QAAQ,uBAAQoU,yBAAkBhE,OAAcC,MAExDpY,KAAQoI,QAAQ,6BAAS+T,qGAE7B,MACEnc,KAAQjL,MAAM,4BAAQonB,+CAE1B,CAAE,MAAOpnB,GAAa,IAADic,EAAAC,EACnBjR,KAAQjL,MAAM,4BAAQonB,0BAAgC,QAAdnL,EAAAjc,EAAMG,gBAAQ,IAAA8b,GAAM,QAANC,EAAdD,EAAgB7a,YAAI,IAAA8a,OAAN,EAAdA,EAAsB9W,SAAUpF,EAAMiL,UAChF,CAGF,GAA2B,IAAvB+c,EAAYngB,OAEd,YADAoD,KAAQjL,MAAM,wOAIhB4nB,EAAkBI,CACpB,CAEA3B,GAAc,GACd9U,EAAY,GACZ+U,EAAW,IAEX,IACE,GAAIK,GAAyC,WAAnBnB,EAA6B,CAErD,MAAM5kB,EAAW,IAAIa,SAEF,WAAf6O,GAA2ByP,EAC7Bnf,EAASE,OAAO,OAAQif,EAAa7K,gBAErCtU,EAASE,OAAO,UAAWmB,GAC3BrB,EAASE,OAAO,gBAAiB+jB,IAGnCjkB,EAASE,OAAO,iBAAkB4kB,GAClC9kB,EAASE,OAAO,kBAAmB8kB,GACnChlB,EAASE,OAAO,kBAAmBglB,GACnCllB,EAASE,OAAO,gBAAiBgc,GACjClc,EAASE,OAAO,oBAAqBic,GACrCnc,EAASE,OAAO,YAAaoC,GAC7BtC,EAASE,OAAO,gBAAiBoC,GAYjC,kBATqB8Y,EAAqBpb,KAGxCqK,KAAQ+H,QAAQ,wLAEhBqT,GAAc,GACd9U,EAAY,IAIhB,CAGA,MAAM0W,EAAiC,GAEvC,IAAK,IAAIrR,EAAI,EAAGA,EAAIgR,EAAgB/f,OAAQ+O,IAAK,CAC/C,MAAMsR,EAAQN,EAAgBhR,GAG9BrF,EAAYoI,KAAKwO,MAAOvR,EAAIgR,EAAgB/f,OAAU,KAElD+f,EAAgB/f,OAAS,GAC3BoD,KAAQ8I,KAAK,wCAAU6C,EAAI,KAAKgR,EAAgB/f,WAAWqgB,EAAML,0CAGnE,MAAMjnB,EAAW,IAAIa,SAEF,WAAf6O,GAA2ByP,EAE7Bnf,EAASE,OAAO,OAAQif,EAAa7K,gBAErCtU,EAASE,OAAO,UAAWmB,GAC3BrB,EAASE,OAAO,gBAAiB+jB,IAGnCjkB,EAASE,OAAO,iBAAkBonB,EAAML,YACxCjnB,EAASE,OAAO,kBAAmBonB,EAAMJ,aACzClnB,EAASE,OAAO,kBAAmBonB,EAAMH,aACzCnnB,EAASE,OAAO,gBAAiBonB,EAAM9E,UACvCxiB,EAASE,OAAO,oBAAqBonB,EAAM7E,UAC3CziB,EAASE,OAAO,gBAAiBoC,GAEjC,MAAM/C,QAAiB8C,EAA2BrC,GAE9CT,EAASiB,OACX6mB,EAAWnR,KAAK,CACdmQ,WAAY9mB,EAASiB,KAAK6lB,YAAc,GAAGiB,EAAM9E,YAAY8E,EAAM7E,WACnEkB,oBAAqBpkB,EAASiB,KAAKmjB,qBAAuB,EAC1DtZ,QAAS9K,EAASiB,KAAK6J,SAAW,2BAElC4U,iBAAkB1f,EAASiB,KAAKye,iBAChCnC,YAAavd,EAASiB,KAAKsc,YAC3BC,UAAWxd,EAASiB,KAAKuc,UACzBC,cAAezd,EAASiB,KAAKwc,cAC7BE,wBAAyB3d,EAASiB,KAAK0c,0BAGrC8J,EAAgB/f,OAAS,GAC3BoD,KAAQ+H,QAAQ,uBAAQkV,EAAML,uCAGpC,CAEAtW,EAAY,KACZ+U,EAAW2B,GACXhd,KAAQ+H,QAAQ,6BAASiV,EAAWpgB,8CAEtC,CAAE,MAAO7H,GAAa,IAADooB,EAAAC,EACnBpd,KAAQjL,OAAoB,QAAdooB,EAAApoB,EAAMG,gBAAQ,IAAAioB,GAAM,QAANC,EAAdD,EAAgBhnB,YAAI,IAAAinB,OAAN,EAAdA,EAAsBjjB,SAAU,2BAChD,CAAC,QACCihB,GAAc,EAChB,GA8TM9hB,QAAS6hB,EACTxR,UA5TY0T,MAClB,MAAMC,EAAyB,WAAfjY,EAA0ByP,EAAgB9d,GAAU4iB,EAEpE,MAAuB,WAAnBW,EACK+C,GAAW7C,GAAqB5I,GAAgBC,EAEhDwL,GAAWnD,EAAevd,OAAS,GAsT3BygB,GACXvZ,UAAU,gBAAe9E,SAExBmc,EAAa,8BAAY,+CAI3BA,GACC7b,gBAAA,OAAKwE,UAAU,mBAAkB9E,SAAA,CAC/BR,eAACZ,GAAI,CAAAoB,SAAC,mCACNR,eAAC+L,KAAQ,CAACC,QAASnE,EAAUjR,OAAO,iBAMzCmiB,EAAQ3a,OAAS,GAChB4B,eAACS,KAAI,CAACkF,MAAM,2BAAOL,UAAU,gBAAe9E,SACzCuY,EAAQ3a,OAAS,EAEhB0C,gBAAA,OAAAN,SAAA,CACER,eAAC2K,KAAO,IACR3K,eAACb,GAAK,CAACgC,MAAO,EAAEX,SAAC,+CACjBM,gBAAA,OAAKb,MAAO,CAAEoB,aAAc,IAAKb,SAAA,CAC/BR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,uEACbR,eAAC2G,KAAM,CACL1G,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,uCACZiG,MAAOqU,EACPzS,SAAW5B,GAAUsU,EAAuBtU,GAAOjI,SAElDuY,EAAQrQ,IAAI,CAACX,EAAQqE,IACpBpM,eAAC0G,GAAM,CAAa+B,MAAO2D,EAAM5L,SAC9BuH,EAAOyV,YADGpR,SAQlB2M,EAAQ+D,IACPhc,gBAAA,OAAAN,SAAA,CACEM,gBAAC3B,GAAK,CAACgC,MAAO,EAAEX,SAAA,CAAC,iBAAKuY,EAAQ+D,GAAqBU,cACnDxd,eAAC6a,GAAuB,CAAC9S,OAAQgR,EAAQ+D,WAM/Chc,gBAAA,OAAAN,SAAA,CACEM,gBAAC3B,GAAK,CAACgC,MAAO,EAAEX,SAAA,CAAC,8BAAQuY,EAAQ,GAAGyE,cACpCxd,eAAC6a,GAAuB,CAAC9S,OAAQgR,EAAQ,UAOhDuE,GAAyBlf,OAAS,GACjC0C,gBAACL,KAAI,CAACkF,MAAM,uCAASL,UAAU,gBAAgBrF,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACtER,eAACuB,KAAK,CACJC,QAAQ,6CACRC,YAAY,uLACZH,KAAK,UACLI,UAAQ,EACRzB,MAAO,CAAEoB,aAAc,MAEzBP,gBAACC,KAAK,CAACC,UAAU,WAAWC,KAAK,QAAQhB,MAAO,CAAES,MAAO,QAASF,SAAA,CAEhEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbR,eAAC2G,KAAM,CACL8B,MAAOiQ,GACPrO,SAAUwO,GACV5Y,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,qEAAahC,SAExB8c,GAAyB5U,IAAKC,GAC7B3I,eAAC0G,GAAM,CAAoB+B,MAAOE,EAAK0I,QAAQ7Q,SAC5CmI,EAAK0I,QAAQW,SAAS,KACrB,GAAGrJ,EAAK0I,QAAQkC,MAAM,KAAK,OAAO,IAAIvV,KAAK2K,EAAK+R,YAAc/R,EAAKgS,YAAYC,oBAC/E,gBAAMjS,EAAK0I,QAAQC,UAAU,EAAG,UAAU,IAAItT,KAAK2K,EAAK+R,YAAc/R,EAAKgS,YAAYC,qBAH9EjS,EAAK0I,eAWvB+L,GAAuBhf,OAAS,GAC/B0C,gBAAA,OAAAN,SAAA,CACEM,gBAAC3B,GAAK,CAACgC,MAAO,EAAEX,SAAA,CAAC,8BAAQ4c,GAAuB,GAAGI,cACnDxd,eAAC6a,GAAuB,CAAC9S,OAAQqV,GAAuB,kB,0FCh5BxE,MAAQje,MAAK,GAAEC,KAAK,IAAIC,MAClB,QAAE0f,IAAYC,KAogBLC,OAjdqBA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACxC,MAAOjlB,EAAS8C,GAAcmJ,oBAAS,IAChCiZ,EAAQC,GAAalZ,mBAAsB,KAC3CmZ,EAAYC,GAAiBpZ,mBAA4B,OACzDqZ,EAAeC,GAAoBtZ,mBAA2B,OAC9DuZ,EAAoBC,GAAyBxZ,oBAAS,IACtDyZ,EAAYC,GAAiB1Z,mBAAS,KACtC2Z,EAAWC,GAAgB5Z,mBAAS,KAGrC6Z,EAAczlB,UAClByC,GAAW,GACX,IACE,MAAMlH,QAAiBkD,IACvByP,QAAQC,IAAI,wCAAW5S,EAASiB,MAC5BjB,EAASiB,KAAK4R,SAChB0W,EAAUvpB,EAASiB,KAAKqoB,QAAU,IAC9BtpB,EAASiB,KAAKkpB,YAAc,GAC9Brf,KAAQ+H,QAAQ,mCAAU7S,EAASiB,KAAKkpB,oCAG1CxX,QAAQ9S,MAAM,uDAAgBG,EAASiB,MACvC6J,KAAQjL,MAAM,oDAElB,CAAE,MAAOA,GAAa,IAADI,EAAA+E,EACnB2N,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAUpF,EAAMiL,UACrE,CAAC,QACC5D,GAAW,EACb,GAgDFmC,oBAAU,KACR6gB,IA7CsBzlB,WACtB,IACE,MAAMzE,QAAiBkD,IACvByP,QAAQC,IAAI,wCAAW5S,EAASiB,MAC5BjB,EAASiB,KAAK4R,QAChB4W,EAAczpB,EAASiB,KAAKuoB,aAE5B7W,QAAQ9S,MAAM,uDAAgBG,EAASiB,MACvC6J,KAAQjL,MAAM,oDAElB,CAAE,MAAOA,GAAa,IAAD0F,EAAAC,EACnBmN,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAUpF,EAAMiL,UACrE,GAiCAsf,IACC,IAGH,MAAMC,EAAU,CACd,CACEpb,MAAO,iBACPqb,UAAW,WACX3d,IAAK,WACL3C,MAAO,IACPugB,cAAeT,EAAa,CAACA,GAAc,KAC3CU,SAAUA,CAACzY,EAAY0Y,IACrBA,EAAOC,SAASpY,cAAcgJ,SAASvJ,EAAMO,gBAC7CmY,EAAOxH,SAAS3Q,cAAcgJ,SAASvJ,EAAMO,gBAC7CmY,EAAOvH,SAAS5Q,cAAcgJ,SAASvJ,EAAMO,eAC/CqY,OAASC,GACPthB,eAACZ,GAAI,CAAC6a,MAAI,EAACha,MAAO,CAAE4C,SAAU,QAASrC,SAAE8gB,KAG7C,CACE3b,MAAO,2BACPqb,UAAW,WACX3d,IAAK,WACL3C,MAAO,IACP2gB,OAAS1H,GACP3Z,eAACuhB,KAAG,CAACngB,MAAoB,QAAbuY,EAAqB,OAAsB,QAAbA,EAAqB,QAAU,SAASnZ,SAC/EmZ,KAIP,CACEhU,MAAO,2BACPqb,UAAW,WACX3d,IAAK,WACL3C,MAAO,IACP2gB,OAASzH,GACP5Z,eAACuhB,KAAG,CAACngB,MAAM,SAAQZ,SAAEoZ,KAGzB,CACEjU,MAAO,qBACPqb,UAAW,WACX3d,IAAK,WACL3C,MAAO,IACP8gB,OAAQA,CAACC,EAAcC,IAAiBD,EAAE9N,SAAW+N,EAAE/N,SACvD0N,OAASM,GACP3hB,eAACZ,GAAI,CAACwL,QAAM,EAAC3K,MAAO,CAAEmB,MAAOugB,EAAQ,GAAM,UAAYA,EAAQ,GAAM,UAAY,WAAYnhB,SAC1FmhB,EAAM/N,QAAQ,MAIrB,CACEjO,MAAO,2BACPqb,UAAW,oBACX3d,IAAK,oBACL3C,MAAO,IACP2gB,OAASpR,GAAqB,GAAGA,EAAWA,EAAS2D,QAAQ,GAAK,UAEpE,CACEjO,MAAO,2BACPqb,UAAW,eACX3d,IAAK,eACL3C,MAAO,IACP8gB,OAAQA,CAACC,EAAcC,IAAiB,IAAI1jB,KAAKyjB,EAAEG,cAAcxR,UAAY,IAAIpS,KAAK0jB,EAAEE,cAAcxR,UACtGiR,OAASQ,GAAiB,IAAI7jB,KAAK6jB,GAAMjH,kBAE3C,CACEjV,MAAO,eACPtC,IAAK,UACL3C,MAAO,IACP2gB,OAAQA,CAACS,EAAGX,IACVrgB,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLqC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IAzGM/I,WACvB,IACE,MAAMzE,QAAiBkD,EAAgCE,GACnDpD,EAASiB,KAAK4R,UAChB8W,EAAiB3pB,EAASiB,KAAK8mB,OAC/B8B,GAAsB,GAE1B,CAAE,MAAOhqB,GAAa,IAADsV,EAAAC,EACnBzC,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAUpF,EAAMiL,UACrE,GA+FuBwgB,CAAiBb,EAAOC,UAAU5gB,SAClD,iBAGDR,eAACiiB,KAAU,CACTtc,MAAM,2BACNlE,YAAY,qHACZygB,UAAWA,IAlGD/mB,WAClB,WACyBvB,EAA6BE,IACvCnC,KAAK4R,UAChB/H,KAAQ+H,QAAQ,+CAChBqX,IAEJ,CAAE,MAAOrqB,GAAa,IAADic,EAAAC,EACnBpJ,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAdic,EAAAjc,EAAMG,gBAAQ,IAAA8b,GAAM,QAANC,EAAdD,EAAgB7a,YAAI,IAAA8a,OAAN,EAAdA,EAAsB9W,SAAUpF,EAAMiL,UACnE,GAwFyB2gB,CAAYhB,EAAOC,UACpCgB,OAAO,eACPC,WAAW,eAAI7hB,SAEfR,eAAC2C,KAAM,CACLrB,KAAK,UACLgL,QAAM,EACNrL,KAAK,QACLqC,KAAMtD,eAACuM,KAAc,IAAI/L,SAC1B,wBASX,OACEM,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,yCACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,mIAIvBM,gBAACke,KAAI,CAACsD,UAAW5B,EAAWrW,SAAUsW,EAAc1gB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAC3ER,eAAC+e,GAAO,CAACwD,IAAKzhB,gBAAA,QAAAN,SAAA,CAAMR,eAAC2D,KAAgB,IAAG,8BAAYnD,SAClDM,gBAACL,KAAI,CAAAD,SAAA,CACHR,eAAA,OAAKC,MAAO,CAAEoB,aAAc,GAAIjB,QAAS,OAAQE,eAAgB,gBAAiBD,WAAY,UAAWG,SACvGM,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC2C,KAAM,CACLrB,KAAK,UACLgC,KAAMtD,eAACwiB,KAAc,IACrBte,QAAS0c,EACT9lB,QAASA,EAAQ0F,SAClB,6BAGDR,eAACqC,KAAK,CACJG,YAAY,6EACZF,OAAQtC,eAAC6D,KAAc,IACvB4E,MAAO+X,EACPnW,SAAWI,GAAMgW,EAAchW,EAAEM,OAAOtC,OACxCxI,MAAO,CAAES,MAAO,KAChB+hB,YAAU,SAKG,IAAlBzC,EAAO5hB,QAAiBtD,EAQvBkF,eAAC0iB,KAAK,CACJ3B,QAASA,EACTla,WAAYmZ,EACZ2C,OAAO,WACP7nB,QAASA,EACT8nB,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAYC,GAAU,UAAKA,wBAE7BC,OAAQ,CAAEC,EAAG,QAlBfnjB,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YAAY,4HACZH,KAAK,OACLI,UAAQ,QA5ByC,KAgDzD1B,eAAC+e,GAAO,CAACwD,IAAKzhB,gBAAA,QAAAN,SAAA,CAAMR,eAACuD,KAAgB,IAAG,8BAAY/C,SACjD0f,EACCpf,gBAAA,OAAAN,SAAA,CACEM,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAKzM,MAAO,CAAEoB,aAAc,IAAKb,SAAA,CACjDR,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACS,KAAI,CAAAD,SACHR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAOyX,EAAWkD,aAClB9gB,OAAQtC,eAAC2D,KAAgB,IACzBoQ,WAAY,CAAE3S,MAAO,iBAI3BpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACS,KAAI,CAAAD,SACHR,eAACwT,KAAS,CACR7N,MAAM,kCACN8C,MAAOyX,EAAWmD,aAClBvP,UAAW,EACXxR,OAAQtC,eAACuD,KAAgB,IACzBwQ,WAAY,CAAE3S,MAAO,iBAI3BpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACS,KAAI,CAAAD,SACHR,eAACwT,KAAS,CACR7N,MAAM,iCACN8C,OAA4B,QAArByW,EAAAgB,EAAWoD,kBAAU,IAAApE,OAAA,EAArBA,EAAuBvL,WAAY,EAC1CG,UAAW,EACXxR,OAAQtC,eAACujB,KAAc,IACvBxP,WAAY,CAAE3S,MAAO,oBAM7BN,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAIlM,SAAA,CACpBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZM,gBAACL,KAAI,CAACkF,MAAM,2BAAO1E,KAAK,QAAOT,SAAA,CAC5B0f,EAAWsD,WAAa9V,OAAOE,QAAQsS,EAAWsD,WAAW9a,IAAItN,IAAA,IAAEue,EAAU8J,GAAMroB,EAAA,OAClF0F,gBAAA,OAAoBb,MAAO,CAAEoB,aAAc,GAAIb,SAAA,CAC7CR,eAACuhB,KAAG,CAACngB,MAAoB,QAAbuY,EAAqB,OAAsB,QAAbA,EAAqB,QAAU,SAASnZ,SAC/EmZ,IAEH7Y,gBAAA,QAAMb,MAAO,CAAEmL,WAAY,GAAI5K,SAAA,CAAEijB,EAAM,2BAJ/B9J,OAOTuG,EAAWsD,WAA0D,IAA7C9V,OAAOC,KAAKuS,EAAWsD,WAAWplB,SAC3D4B,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,kCAI7BR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZM,gBAACL,KAAI,CAACkF,MAAM,uCAAS1E,KAAK,QAAOT,SAAA,CAC9B0f,EAAWzG,WAAa/L,OAAOE,QAAQsS,EAAWzG,WAAW/Q,IAAI7M,IAAA,IAAE+d,EAAU6J,GAAM5nB,EAAA,OAClFiF,gBAAA,OAAoBb,MAAO,CAAEoB,aAAc,GAAIb,SAAA,CAC7CR,eAACuhB,KAAG,CAACngB,MAAM,SAAQZ,SAAEoZ,IACrB9Y,gBAAA,QAAMb,MAAO,CAAEmL,WAAY,GAAI5K,SAAA,CAAEijB,EAAM,2BAF/B7J,OAKTsG,EAAWzG,WAA0D,IAA7C/L,OAAOC,KAAKuS,EAAWzG,WAAWrb,SAC3D4B,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,qCAM9B0f,EAAWoD,YACVtjB,eAACS,KAAI,CAACkF,MAAM,uCAAS1F,MAAO,CAAE+K,UAAW,IAAM/J,KAAK,QAAOT,SACzDM,gBAAC4iB,KAAY,CAACC,OAAQ,EAAG1iB,KAAK,QAAOT,SAAA,CACnCR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,iBAAMhD,SAC7BR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,SAAE0f,EAAWoD,WAAWlC,aAEpCphB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,qBAAOhD,SAC9BR,eAACZ,GAAI,CAACwL,QAAM,EAAC3K,MAAO,CAAEmB,MAAO,WAAYZ,SACtC0f,EAAWoD,WAAW3P,SAASC,QAAQ,OAG5C5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACuhB,KAAG,CAACngB,MAA0C,QAAnC8e,EAAWoD,WAAW3J,SAAqB,OACR,QAAnCuG,EAAWoD,WAAW3J,SAAqB,QAAU,SAASnZ,SACvE0f,EAAWoD,WAAW3J,aAG3B3Z,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACuhB,KAAG,CAACngB,MAAM,SAAQZ,SAAE0f,EAAWoD,WAAW1J,qBAOrD9Y,gBAACL,KAAI,CAAAD,SAAA,CACHR,eAACqL,KAAI,CAACpK,KAAK,UACXjB,eAAA,OAAKC,MAAO,CAAEiB,UAAW,SAAU8J,UAAW,IAAKxK,SACjDR,eAACZ,GAAI,CAAAoB,SAAC,8DAjG2C,QAyG3DR,eAAC4jB,KAAK,CACJje,MAAM,uCACNke,KAAMvD,EACNwD,SAAUA,IAAMvD,GAAsB,GACtCwD,OAAQ,CACN/jB,eAAC2C,KAAM,CAAauB,QAASA,IAAMqc,GAAsB,GAAO/f,SAAC,gBAArD,UAIdE,MAAO,IAAIF,SAEV4f,GACCtf,gBAAA,OAAAN,SAAA,CACEM,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAOT,SAAA,CACzDR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,iBAAOoJ,KAAM,EAAEpM,SACtCR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,SAAE4f,EAAcgB,aAE5BphB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACuhB,KAAG,CAACngB,MAAkC,QAA3Bgf,EAAczG,SAAqB,OACR,QAA3ByG,EAAczG,SAAqB,QAAU,SAASnZ,SAC/D4f,EAAczG,aAGnB3Z,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACuhB,KAAG,CAACngB,MAAM,SAAQZ,SAAE4f,EAAcxG,aAErC5Z,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,qBAAOhD,SAC9BR,eAACZ,GAAI,CAACwL,QAAM,EAAC3K,MAAO,CAAEmB,MAAOgf,EAAczM,SAAW,GAAM,UACjCyM,EAAczM,SAAW,GAAM,UAAY,WAAYnT,SAC/E4f,EAAczM,SAASC,QAAQ,OAGpC5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,uCAAQhD,SAC9B4f,EAAc6D,cAAgB,IAAIjmB,KAAKoiB,EAAc6D,eAAerJ,iBAAmB,QAE1F5a,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAOoJ,KAAM,EAAEpM,SACrC,IAAIxC,KAAKoiB,EAAcwB,cAAchH,sBAI1C9Z,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAQhB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACnFR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,UAA0B,QAAxB2e,EAAAiB,EAAc8D,kBAAU,IAAA/E,OAAA,EAAxBA,EAA0BgF,aAAc,UAEtDnkB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC7BR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,UAA0B,QAAxB4e,EAAAgB,EAAc8D,kBAAU,IAAA9E,OAAA,EAAxBA,EAA0BgF,cAAe,UAEvDpkB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,uCAAQhD,SAC/BR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,UAA0B,QAAxB6e,EAAAe,EAAc8D,kBAAU,IAAA7E,OAAA,EAAxBA,EAA0BgF,cAAe,UAEvDrkB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,uCAAQhD,SAC/BR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,UAA0B,QAAxB8e,EAAAc,EAAc8D,kBAAU,IAAA5E,OAAA,EAAxBA,EAA0BgF,iBAAkB,aAI5DxjB,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAQhB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACnFR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,qBAAKhD,UACE,QAA7B+e,EAAAa,EAAcmE,uBAAe,IAAAhF,OAAA,EAA7BA,EAA+BxmB,gBAAiB,QAEnDiH,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACC,QAA7Bgf,EAAAY,EAAcmE,uBAAe,IAAA/E,OAAA,EAA7BA,EAA+BvmB,aAAc,QAEhD+G,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACC,QAA7Bif,EAAAW,EAAcmE,uBAAe,IAAA9E,OAAA,EAA7BA,EAA+BvmB,SAAU,QAE5C8G,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACI,QAAhCkf,EAAAU,EAAcoE,0BAAkB,IAAA9E,OAAA,EAAhCA,EAAkC9L,QAAQ,KAAM,WAIrD9S,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAQhB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACnFR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACI,QAAhCmf,EAAAS,EAAcqE,0BAAkB,IAAA9E,OAAA,EAAhCA,EAAkCre,OAAQ,QAE7CtB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,iCAAOhD,UACG,QAAhCof,EAAAQ,EAAcqE,0BAAkB,IAAA7E,OAAA,EAAhCA,EAAkCxmB,cAAe,QAEpD4G,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACI,QAAhCqf,EAAAO,EAAcqE,0BAAkB,IAAA5E,OAAA,EAAhCA,EAAkCxmB,aAAc,QAEnD2G,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,UACI,QAAhCsf,EAAAM,EAAcqE,0BAAkB,IAAA3E,OAAA,EAAhCA,EAAkC3mB,kBAAmB,QAExD6G,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,gBAAWoJ,KAAM,EAAEpM,UACT,QAAhCuf,EAAAK,EAAcqE,0BAAkB,IAAA1E,OAAA,EAAhCA,EAAkCzmB,UAAW,WAIlDwH,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAQhB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACnFR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAOoJ,KAAM,EAAEpM,SACtCR,eAACZ,GAAI,CAAC6a,MAAI,EAAAzZ,SAAE4f,EAAcsE,aAAe,UAE3C1kB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,uCAAQhD,SAC9B4f,EAAc3M,YAAc,IAAI2M,EAAc3M,YAAYkR,KAAK,SAAW,QAE7E3kB,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,uCAAQhD,SAC9B4f,EAAc1M,WAAa,IAAI0M,EAAc1M,WAAWiR,KAAK,SAAW,WAI7E7jB,gBAAC4iB,KAAY,CAAC/d,MAAM,2BAAOqe,UAAQ,EAACL,OAAQ,EAAG1iB,KAAK,QAAQhB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CACnFR,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC5B4f,EAAcwE,kBAAoB,GAAGxE,EAAcwE,kBAAkBhR,QAAQ,MAAQ,QAExF5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,wBAAQhD,SAC9B4f,EAAcyE,UAAY,GAAGzE,EAAcyE,UAAUjR,QAAQ,MAAQ,QAExE5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,2BAAMhD,SAC5B4f,EAAc0E,aAAe,GAAG1E,EAAc0E,aAAalR,QAAQ,OAAS,QAE/E5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,kBAAOhD,SAC7B4f,EAAchM,WAAa,GAAGgM,EAAchM,WAAWR,QAAQ,OAAS,QAE3E5T,eAAC0jB,KAAazhB,KAAI,CAACuB,MAAM,wBAASoJ,KAAM,EAAEpM,SACvC4f,EAAc9L,gBAAkB,GAAG8L,EAAc9L,gBAAgBV,QAAQ,MAAQ,oB,4DC1flG,MAAQzU,MAAK,GAAEC,KAAK,IAAIC,MAChB0f,QAAQ,IAAIC,MACZtY,OAAO,IAAIC,KAi2BJoe,OAt1BqBA,KAClC,MAAOjqB,EAAS8C,GAAcmJ,oBAAS,IAChCie,GAAgBxlB,KAAKC,WACrBwlB,GAAYzlB,KAAKC,WACjBylB,GAAc1lB,KAAKC,WAGnB0lB,EAAaC,GAAkBre,mBAAmB,KAClD3M,EAAWirB,GAAgBte,mBAAS,KACpCue,EAAuBC,GAA4Bxe,mBAIhD,OAGHye,EAAWC,GAAgB1e,mBAAyB,KACpD7M,EAAawrB,GAAkB3e,mBAAS,KACxC4e,EAAkBC,GAAuB7e,mBAAiB,KAC1D8e,EAAiBC,GAAsB/e,mBAAS,KAChDgf,EAAkBC,GAAuBjf,oBAAS,IAClDkf,EAAkBC,GAAuBnf,oBAAS,IAGlDof,EAAiBC,GAAsBrf,mBAAiB,KACxDsf,EAAeC,GAAoBvf,mBAAyB,KAC5Dwf,EAAsBC,GAA2Bzf,oBAAS,GAG3D0f,EAAmBxV,sBAAY9V,iBAA+B,IAAxBurB,IAAWrX,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,KAAAA,UAAA,GACrD,GAAKjV,EAAUusB,OAKf,IACE,MAAMjwB,QAAiBuD,EAAiCG,GACpD1D,EAASiB,KAAKoR,QAChBqc,EAAe1uB,EAASiB,KAAKoR,OAAS,IAClC2d,GAAehwB,EAASiB,KAAKoR,MAAM3K,OAAS,EAC9CoD,KAAQ+H,QAAQ,6BAAS7S,EAASiB,KAAKoR,MAAM3K,yCACpCsoB,GAA8C,IAA/BhwB,EAASiB,KAAKoR,MAAM3K,QAC5CoD,KAAQ8I,KAAK,6EAGnB,CAAE,MAAO/T,GAEW,IAADI,EAAA+E,EAAjB,GADA2N,QAAQ9S,MAAM,oDAAaA,GACvBmwB,EACFllB,KAAQjL,MAAM,6DAA6B,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAUpF,EAAMiL,WAErE4jB,EAAe,GACjB,MApBEA,EAAe,GAqBnB,EAAG,CAAChrB,IAGEwsB,EAAiB3V,sBAAY9V,iBAA+B,IAAxBurB,IAAWrX,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,KAAAA,UAAA,GACnD,GAAKnV,EAAYysB,OAAjB,CAKA/oB,GAAW,GACX,IACE,MAAMlH,QAAiBuD,EAA+BC,GAGtD,GAFAmP,QAAQC,IAAI,2CAAc5S,EAASiB,MAE/BjB,EAASiB,MAAQjB,EAASiB,KAAK6tB,UAAW,CAC5C,MAAMqB,EAAgBnwB,EAASiB,KAAK6tB,UACpCnc,QAAQC,IAAI,4BAASud,GAGrB,IAAIC,EAAqB,GACrBC,MAAMC,QAAQH,KAChBC,EAAqBD,EAAcne,IAAI,CAACue,EAAM7a,KAC5C,MAAoB,kBAAT6a,EAEF,CACLC,cAAeD,EAAKjJ,QAAQ,sBAAuB,IACnDmJ,SAAUF,EACVG,cAAe,GAAGltB,KAAe+sB,IACjCI,UAAW,EACXzF,aAAc,EACd0F,cAAe,GAEQ,kBAATL,GAA8B,OAATA,EAE9B,CACLC,cAAeD,EAAKC,gBAA8B,QAAjBK,EAAIN,EAAKE,gBAAQ,IAAAI,OAAA,EAAbA,EAAevJ,QAAQ,sBAAuB,MAAO,eAAK5R,EAAQ,IACvG+a,SAAUF,EAAKE,UAAYF,EAAKC,eAAiB,WAAW9a,EAAQ,SACpEgb,cAAeH,EAAKG,eAAiB,GAAGltB,KAAe+sB,EAAKE,WAC5DE,UAAWJ,EAAKI,WAAa,EAC7BzF,aAAcqF,EAAKrF,cAAgB,EACnC0F,cAAeL,EAAKK,eAAiB,GAGlCL,EAXgD,IAADM,KAe1Dle,QAAQC,IAAI,oDAAawd,GACzBrB,EAAaqB,GAETJ,GAAeI,EAAmB1oB,OAAS,EAC7CoD,KAAQ+H,QAAQ,6BAASud,EAAmB1oB,yCACnCsoB,GAA6C,IAA9BI,EAAmB1oB,QAC3CoD,KAAQ8I,KAAK,4EAEjB,CACF,CAAE,MAAO/T,GAEW,IAAD0F,EAAAC,EAAjB,GADAmN,QAAQ9S,MAAM,oDAAaA,GACvBmwB,EACFllB,KAAQjL,MAAM,6DAA6B,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAUpF,EAAMiL,WAErEikB,EAAa,GACf,CAAC,QACC7nB,GAAW,EACb,CAzDA,MAFE6nB,EAAa,GA4DjB,EAAG,CAACvrB,IAGEstB,EAAqBvW,sBAAY9V,iBAA+B,IAAxBurB,IAAWrX,UAAAjR,OAAA,QAAAyO,IAAAwC,UAAA,KAAAA,UAAA,GACvD,GAAK8W,EAAgBQ,OAArB,CAKAH,GAAwB,GACxB,IACE,MAAM9vB,QAAiBuD,EAA+BksB,GAGtD,GAFA9c,QAAQC,IAAI,uDAAgB5S,EAASiB,MAEjCjB,EAASiB,MAAQjB,EAASiB,KAAK6tB,UAAW,CAC5C,MAAMqB,EAAgBnwB,EAASiB,KAAK6tB,UACpCnc,QAAQC,IAAI,wCAAWud,GAGvB,IAAIC,EAAqB,GACrBC,MAAMC,QAAQH,KAChBC,EAAqBD,EAAcne,IAAI,CAACue,EAAM7a,KAC5C,MAAoB,kBAAT6a,EAEF,CACLC,cAAeD,EAAKjJ,QAAQ,sBAAuB,IACnDmJ,SAAUF,EACVG,cAAe,GAAGjB,KAAmBc,IACrCI,UAAW,EACXzF,aAAc,EACd0F,cAAe,GAEQ,kBAATL,GAA8B,OAATA,EAE9B,CACLC,cAAeD,EAAKC,gBAA8B,QAAjBO,EAAIR,EAAKE,gBAAQ,IAAAM,OAAA,EAAbA,EAAezJ,QAAQ,sBAAuB,MAAO,eAAK5R,EAAQ,IACvG+a,SAAUF,EAAKE,UAAYF,EAAKC,eAAiB,WAAW9a,EAAQ,SACpEgb,cAAeH,EAAKG,eAAiB,GAAGjB,KAAmBc,EAAKE,WAChEE,UAAWJ,EAAKI,WAAa,EAC7BzF,aAAcqF,EAAKrF,cAAgB,EACnC0F,cAAeL,EAAKK,eAAiB,GAGlCL,EAXgD,IAADQ,KAe1Dpe,QAAQC,IAAI,gEAAewd,GAC3BR,EAAiBQ,GAEbJ,GAAeI,EAAmB1oB,OAAS,EAC7CoD,KAAQ+H,QAAQ,6BAASud,EAAmB1oB,iEACnCsoB,GAA6C,IAA9BI,EAAmB1oB,QAC3CoD,KAAQ8I,KAAK,4EAEjB,CACF,CAAE,MAAO/T,GAEW,IAADsV,EAAAC,EAAjB,GADAzC,QAAQ9S,MAAM,gEAAeA,GACzBmwB,EACFllB,KAAQjL,MAAM,yEAA+B,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAUpF,EAAMiL,WAEvE8kB,EAAiB,GACnB,CAAC,QACCE,GAAwB,EAC1B,CAzDA,MAFEF,EAAiB,GA4DrB,EAAG,CAACH,IAGEuB,EAAuBvsB,UAC3B,IACE,MAAMzE,QAAiBuD,EAAoCE,GAC3D,GAAIzD,EAASiB,MAAQjB,EAASiB,KAAKgwB,QAEjC,OADA7B,EAAmBntB,KAAKC,UAAUlC,EAASiB,KAAKgwB,QAAS,KAAM,IACxDjxB,EAASiB,KAAKgwB,OAEzB,CAAE,MAAOpxB,GAAa,IAADic,EAAAC,EACnBpJ,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAdic,EAAAjc,EAAMG,gBAAQ,IAAA8b,GAAM,QAANC,EAAdD,EAAgB7a,YAAI,IAAA8a,OAAN,EAAdA,EAAsB9W,SAAUpF,EAAMiL,UACrE,CACA,OAAO,MAmLHomB,EAAcA,CAACnf,EAAeof,KAClC,MAAOC,EAAgBC,GAAqBhhB,mBAAS0B,GAYrD,OAVA1I,oBAAU,KACR,MAAMioB,EAAUre,WAAW,KACzBoe,EAAkBtf,IACjBof,GAEH,MAAO,KACL7d,aAAage,KAEd,CAACvf,EAAOof,IAEJC,GAIHG,EAAqBL,EAAYxtB,EAAW,MAC5C8tB,EAAuBN,EAAY1tB,EAAa,MAChDiuB,EAA2BP,EAAYzB,EAAiB,MAS9DpmB,oBAAU,KACJkoB,GAAoD,KAA9BA,EAAmBtB,OAC3CF,GAAiB,GAEjBrB,EAAe,KAEhB,CAAC6C,EAAoBxB,IAExB1mB,oBAAU,KACJmoB,GAAwD,KAAhCA,EAAqBvB,OAC/CC,GAAe,GAEfnB,EAAa,KAEd,CAACyC,EAAsBtB,IAE1B7mB,oBAAU,KACJooB,GAAgE,KAApCA,EAAyBxB,OACvDa,GAAmB,GAEnBlB,EAAiB,KAElB,CAAC6B,EAA0BX,IAG9B,MAAMY,EAAkB,CACtB,CACEziB,MAAO,2BACPqb,UAAW,gBACX3d,IAAK,gBACLge,OAAQA,CAAChlB,EAAc8kB,IACrBrgB,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAEnE,IACd2D,eAAA,SACAA,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,QAASrC,SAChD2gB,EAAOgG,eAKhB,CACExhB,MAAO,eACPtC,IAAK,UACLge,OAAQA,CAACS,EAAQX,KACf9X,QAAQC,IAAI,kCAAU6X,GAEpBrgB,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC2C,KAAM,CACLrB,KAAK,UACLL,KAAK,QACLqC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IA3GA/I,WAEnB,GADAkO,QAAQC,IAAI,8CAAYnP,IACnBA,GAAiC,cAAjBA,EAEnB,YADAqH,KAAQjL,MAAM,8CAGMmxB,EAAqBvtB,IAEzC+rB,GAAoB,IAmGGmC,CAAalH,EAAOiG,eAAe5mB,SACnD,iBAGDR,eAAC2C,KAAM,CACL1B,KAAK,QACLqC,KAAMtD,eAACsoB,KAAY,IACnBpkB,QAASA,IArGA/I,WAEnB,GADAkO,QAAQC,IAAI,8CAAYnP,IACnBA,GAAiC,cAAjBA,EAEnB,YADAqH,KAAQjL,MAAM,8CAGMmxB,EAAqBvtB,KAEzCyrB,EAAoBzrB,GACpB+qB,EAAWqD,eAAe,CAAEC,iBAAkB3C,IAC9CG,GAAoB,KA2FGyC,CAAatH,EAAOiG,eAAe5mB,SACnD,iBAGDR,eAAC2C,KAAM,CACL1B,KAAK,QACLqC,KAAMtD,eAAC0oB,KAAgB,IACvBxkB,QAASA,IApJI/I,OAAOhB,EAAsBwuB,KAEpD,GADAtf,QAAQC,IAAI,8CAAYnP,EAAc,gBAAOwuB,GACxCxuB,GAAiC,cAAjBA,EAIrB,IACE,MAAMzD,QAAiBuD,EAAkCE,GAGnDyuB,EAAM9xB,OAAO+xB,IAAIC,gBAAgB,IAAIC,KAAK,CAACryB,EAASiB,QACpDqxB,EAAOC,SAASC,cAAc,KACpCF,EAAKhyB,KAAO4xB,EACZI,EAAKG,aAAa,WAAYR,GAAgB,iBAC9CM,SAASG,KAAKC,YAAYL,GAC1BA,EAAKM,QACLN,EAAKO,SACLzyB,OAAO+xB,IAAIW,gBAAgBZ,GAE3BpnB,KAAQ+H,QAAQ,8CAClB,CAAE,MAAOhT,GAAa,IAADkzB,EAAAC,EACnBrgB,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAdkzB,EAAAlzB,EAAMG,gBAAQ,IAAA+yB,GAAM,QAANC,EAAdD,EAAgB9xB,YAAI,IAAA+xB,OAAN,EAAdA,EAAsB/tB,SAAUpF,EAAMiL,UACnE,MApBEA,KAAQjL,MAAM,yCAiJSozB,CAAiBxI,EAAOiG,cAAejG,EAAOgG,UAAU3mB,SACxE,uBASX,OACEM,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,yCACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,yLAIvBM,gBAACke,KAAI,CAAC4K,iBAAiB,IAAI3pB,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAClDR,eAAC+e,GAAO,CAACwD,IAAKzhB,gBAAA,QAAAN,SAAA,CAAMR,eAAC4D,KAAgB,IAAG,8BAAYpD,SAClDM,gBAACL,KAAI,CAACkF,MAAM,uCAAS1E,KAAK,QAAOT,SAAA,CAC/BM,gBAACtB,KAAI,CACHD,KAAMylB,EACN6E,OAAO,WACPhoB,SAnSa1G,UACvByC,GAAW,GACX,IACE,MAAMzG,EAAW,IAAIa,SACrBb,EAASE,OAAO,eAAgB,GAAG+C,KAAa0H,EAAOgoB,wBACvD3yB,EAASE,OAAO,gBAAiByK,EAAO5J,YACpC4J,EAAOolB,eACT/vB,EAASE,OAAO,gBAAiByK,EAAOolB,eAG1C,MAAMxwB,QAAiBuD,EAAkC9C,GACzD,GAAIT,EAASiB,KAAM,CACjB,MAAM,cAAEyvB,EAAa,mBAAE2C,GAAuBrzB,EAASiB,KAGvD6J,KAAQ+H,QAAQ,CACdoe,QACE7mB,gBAAA,OAAAN,SAAA,CACER,eAAA,OAAKC,MAAO,CAAE6F,WAAY,OAAQzE,aAAc,GAAIb,SAAC,wEAGrDM,gBAAA,OAAKb,MAAO,CAAE4C,SAAU,OAAQzB,MAAO,QAASZ,SAAA,CAAC,0CACrC4mB,EAAcpnB,eAAA,SAAK,0CACnB+pB,EAAmB,gBAInC9Z,SAAU,IAIR/V,GACF0sB,GAAe,GAIjBrB,EAAyB,CACvByE,KAAM5C,EACN/qB,KAAM+qB,EAAc7T,MAAM,KAAK0W,OAAS,2BACxCpI,MAAM,IAAI7jB,MAAO4c,mBAInBoK,EAAakF,cACb9E,EAAe,IACfC,EAAa,GACf,CACF,CAAE,MAAO9uB,GAAa,IAADooB,EAAAC,EACnBvV,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAdooB,EAAApoB,EAAMG,gBAAQ,IAAAioB,GAAM,QAANC,EAAdD,EAAgBhnB,YAAI,IAAAinB,OAAN,EAAdA,EAAsBjjB,SAAUpF,EAAMiL,UACnE,CAAC,QACC5D,GAAW,EACb,GA+OqC4C,SAAA,CAE3BM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,uCACNnH,KAAK,aACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,2DAAehB,SAElDR,eAACqC,KAAK,CACJC,OAAQtC,eAACmqB,KAAkB,IAC3B3nB,YAAY,6BACZiG,MAAOrO,EACPiQ,SAAWI,GAAM4a,EAAa5a,EAAEM,OAAOtC,OACvC2hB,WACEpqB,eAAC2C,KAAM,CACL1B,KAAK,QACLqC,KAAMtD,eAACwiB,KAAc,IACrBte,QAASA,IAAMuiB,GAAiB,GAAMjmB,SACvC,uBAOTR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,uCACNnH,KAAK,uBACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,+CAAahB,SAEhDR,eAAC2G,KAAM,CACLnE,YAAoC,IAAvB2iB,EAAY/mB,OAAe,iFAAkB,2EAC1DisB,YAAU,EACVC,gBAAwC,IAAvBnF,EAAY/mB,OAAe,qHAAwB,mDACpEmsB,aAAcA,CAACC,EAAOC,KAAM,IAAAC,EAAA,OACnB,OAAND,QAAM,IAANA,GAAgB,QAAVC,EAAND,EAAQjqB,gBAAQ,IAAAkqB,OAAV,EAAPA,EAAyC1hB,cAAcgJ,SAASwY,EAAMxhB,gBACvExI,SAEA2kB,EAAYzc,IAAIrQ,GACf2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC5BnI,GADUA,aASvByI,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,uCACNnH,KAAK,aACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,+CAAahB,SAEhDR,eAACqC,KAAK,CAACG,YAAY,mCAGvBxC,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,mDACNnH,KAAK,gBAAemE,SAEpBR,eAACqC,KAAK,CAACG,YAAY,uCAKzBxC,eAACR,KAAKyC,KAAI,CAAAzB,SACRR,eAAC2C,KAAM,CACLrB,KAAK,UACLsB,SAAS,SACT9H,QAASA,EACTwI,KAAMtD,eAAC4D,KAAgB,IAAIpD,SAC5B,8CAOJ8kB,GACCxkB,gBAACL,KAAI,CACHQ,KAAK,QACLhB,MAAO,CAAE+K,UAAW,GAAI2f,YAAa,WACrChlB,MACE7E,gBAAA,QAAMb,MAAO,CAAEmB,MAAO,WAAYZ,SAAA,CAChCR,eAAC4D,KAAgB,IAAG,iDAGxByI,MACErM,eAAC2C,KAAM,CACL1B,KAAK,QACLK,KAAK,OACL4C,QAASA,IAAMqhB,EAAyB,MAAM/kB,SAC/C,SAGFA,SAAA,CAEDM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACZ,GAAI,CAACwrB,UAAQ,EAAApqB,SAAE8kB,EAAsBjpB,UAExCyE,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACZ,GAAI,CAAAoB,SAAE8kB,EAAsBzD,aAGjC7hB,eAACyM,KAAG,CAACxM,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAACZ,GAAI,CAACwrB,UAAQ,EAAC3qB,MAAO,CAAE4C,SAAU,OAAQzB,MAAO,QAASZ,SACvD8kB,EAAsB0E,mBAzHoB,KAkIzDhqB,eAAC+e,GAAO,CAACwD,IAAKzhB,gBAAA,QAAAN,SAAA,CAAMR,eAACgE,KAAe,IAAG,8BAAYxD,SACjDR,eAACS,KAAI,CACHkF,MAAM,2BACN1E,KAAK,QACLoL,MACEvL,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACqC,KAAK,CACJG,YAAY,uCACZiG,MAAOvO,EACPmQ,SAAWI,GAAMib,EAAejb,EAAEM,OAAOtC,OACzCxI,MAAO,CAAES,MAAO,OAElBV,eAAC2C,KAAM,CACLW,KAAMtD,eAACwiB,KAAc,IACrBte,QAASA,IAAM0iB,GAAe,GAC9B9rB,QAASA,EAAQ0F,SAClB,oBAIJA,SAEqB,IAArBglB,EAAUpnB,QAAiBtD,EAQ1BkF,eAAC0iB,KAAK,CACJ3B,QAASqH,EACTvhB,WAAY2e,EACZ7C,OAAO,gBACP7nB,QAASA,EACT8nB,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBE,UAAYC,GAAU,UAAKA,wBAE7BhiB,KAAK,UAjBPjB,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YAAY,0PACZH,KAAK,OACLI,UAAQ,OA3BwC,KA8CxD1B,eAAC+e,GAAO,CAACwD,IAAKzhB,gBAAA,QAAAN,SAAA,CAAMR,eAAC6qB,KAAY,IAAG,8BAAYrqB,SAC9CR,eAACS,KAAI,CAACkF,MAAM,+DAAa1E,KAAK,QAAOT,SACnCM,gBAACtB,KAAI,CACHD,KAAM0lB,EACN4E,OAAO,WACPhoB,SAxYS1G,UACnBkO,QAAQC,IAAI,wCAAWxH,GAEvBlE,GAAW,GACX,IACE,MAAMzG,EAAW,IAAIa,SACrBb,EAASE,OAAO,gBAAiByK,EAAOslB,eACxCjwB,EAASE,OAAO,cAAeyK,EAAOgpB,aACtC3zB,EAASE,OAAO,kBAAmByK,EAAOipB,iBAC1C5zB,EAASE,OAAO,kBAAmByK,EAAOkpB,iBAC1C7zB,EAASE,OAAO,cAAeyK,EAAOmpB,YAAYjyB,YAClD7B,EAASE,OAAO,cAAeyK,EAAOopB,aAEtC,MAAMx0B,QAAiBuD,EAA8B9C,GACrD,GAAIT,EAASiB,MAAQjB,EAASiB,KAAK6J,QAAS,CAC1CA,KAAQ+H,QAAQ,CACdoe,QACE7mB,gBAAA,OAAAN,SAAA,CACER,eAAA,OAAKC,MAAO,CAAE6F,WAAY,OAAQzE,aAAc,GAAIb,SAAC,4DAGrDM,gBAAA,OAAKb,MAAO,CAAE4C,SAAU,OAAQzB,MAAO,QAASZ,SAAA,CAAC,8BACvCsB,EAAOslB,cAAc7T,MAAM,KAAK0W,MAAMjqB,eAAA,SAAK,oCAC1C8B,EAAOgpB,YAAY,IAAEhpB,EAAOmpB,YAAYjrB,eAAA,SAAK,8BAC9C8B,EAAOopB,kBAIrBjb,SAAU,IAIZ,MAAMkb,EAAclG,EAASmG,cAAc,2BAC3CnG,EAASiF,cACTjF,EAASsD,eAAe,CAAE8C,wBAAyBF,GACrD,CACF,CAAE,MAAO50B,GAAa,IAAD+0B,EAAAC,EACnBliB,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAd+0B,EAAA/0B,EAAMG,gBAAQ,IAAA40B,GAAM,QAANC,EAAdD,EAAgB3zB,YAAI,IAAA4zB,OAAN,EAAdA,EAAsB5vB,SAAUpF,EAAMiL,UACnE,CAAC,QACC5D,GAAW,EACb,GA+ViC4C,SAAA,CAEvBR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,0BACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,2DAAehB,SAElDR,eAACqC,KAAK,CACJG,YAAY,yFACZiG,MAAO0d,EACP9b,SAAWI,GAAM2b,EAAmB3b,EAAEM,OAAOtC,OAC7CsS,OACE/a,eAAC2C,KAAM,CACLrB,KAAK,OACLL,KAAK,QACLqC,KAAMtD,eAACwiB,KAAc,IACrB1nB,QAASyrB,EACTriB,QAASA,IAAMsjB,GAAmB,SAM1CxnB,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,gBACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,2DAAehB,SAElDR,eAAC2G,KAAM,CACLnE,YAAY,iIACZ6nB,YAAU,EACVvvB,QAASyrB,EACTgE,aAAcA,CAACC,EAAOC,KAAM,IAAAe,EAAA,OACnB,OAANf,QAAM,IAANA,GAAgB,QAAVe,EAANf,EAAQjqB,gBAAQ,IAAAgrB,OAAV,EAAPA,EAAyCxiB,cAAcgJ,SAASwY,EAAMxhB,gBAExEshB,gBACE/D,EACEvmB,eAAA,OAAKC,MAAO,CAAEiB,UAAW,SAAUX,QAAS,QAASC,SACnDR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,uEAEE,IAAzB6lB,EAAcjoB,OAChB4B,eAAA,OAAKC,MAAO,CAAEiB,UAAW,SAAUX,QAAS,QAASC,SACnDM,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,oDACZR,eAAA,SAAK,4FAIhB,KACLQ,SAEA6lB,EAAc3d,IAAI+iB,GACjBzrB,eAAC0G,GAAM,CAA8B+B,MAAOgjB,EAASrE,cAAc5mB,SACjEM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAEirB,EAASvE,gBACvBlnB,eAAA,SACAA,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,QAASrC,SAChDirB,EAAStE,eALHsE,EAASrE,oBAa5BpnB,eAAC2K,KAAO,CAAAnK,SAAC,+CAETM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,cACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,2DAAehB,SAElDR,eAACqC,KAAK,CAACG,YAAY,oCAGvBxC,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,eACNnH,KAAK,cACLqvB,aAAc,GACdxpB,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,yCAAYhB,SAE/CR,eAACqC,KAAK,CAACf,KAAK,SAASkB,YAAY,cAKvC1B,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,qBACNnH,KAAK,kBACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,yCAAYhB,SAE/CR,eAACqC,KAAK,CAACG,YAAY,2BAGvBxC,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,eACNnH,KAAK,kBACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,mCAAWhB,SAE9CR,eAACqC,KAAMI,SAAQ,CAACD,YAAY,0CAKlCxC,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,cACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,+CAAahB,SAEhDR,eAACqC,KAAK,CAACG,YAAY,wCAGrBxC,eAACR,KAAKyC,KAAI,CAAAzB,SACRR,eAAC2C,KAAM,CACLrB,KAAK,UACLsB,SAAS,SACT9H,QAASA,EACTwI,KAAMtD,eAAC2rB,KAAmB,IAAInrB,SAC/B,qCAhI4C,QA0IvDR,eAAC4jB,KAAK,CACJje,MAAM,2BACNke,KAAMoC,EACNnC,SAAUA,IAAMoC,GAAoB,GACpCnC,OAAQ,CACN/jB,eAAC2C,KAAM,CAAauB,QAASA,IAAMgiB,GAAoB,GAAO1lB,SAAC,gBAAnD,UAIdE,MAAO,IAAIF,SAEXR,eAAC4rB,KAAQ,CACPnjB,MAAOod,EACPgG,KAAM,GACNC,UAAQ,EACR7rB,MAAO,CAAE8rB,WAAY,iBAKzB/rB,eAAC4jB,KAAK,CACJje,MAAM,uCACNke,KAAMkC,EACNjC,SAAUA,IAAMkC,GAAoB,GACpCjC,OAAQ,CACN/jB,eAAC2C,KAAM,CAAcuB,QAASA,IAAM8hB,GAAoB,GAAOxlB,SAAC,gBAApD,UAGZR,eAAC2C,KAAM,CAAYrB,KAAK,UAAU4C,QAASA,IAAMghB,EAAW8G,SAASxrB,SAAC,gBAA1D,SAIdE,MAAO,IAAIF,SAEXR,eAACR,KAAI,CACHD,KAAM2lB,EACNrjB,SApkBe1G,UACrB,IACE,MAAMhE,EAAW,IAAIa,SACrBb,EAASE,OAAO,gBAAiBsuB,GACjCxuB,EAASE,OAAO,mBAAoByK,EAAO0mB,kBAE3C,MAAM9xB,QAAiBuD,EAAgC9C,GACnDT,EAASiB,MAAQjB,EAASiB,KAAK6J,UACjCA,KAAQ+H,QAAQ,2DAChByc,GAAoB,GACpBY,GAAe,GAEnB,CAAE,MAAOrwB,GAAa,IAAD01B,EAAAC,EACnB7iB,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAd01B,EAAA11B,EAAMG,gBAAQ,IAAAu1B,GAAM,QAANC,EAAdD,EAAgBt0B,YAAI,IAAAu0B,OAAN,EAAdA,EAAsBvwB,SAAUpF,EAAMiL,UACnE,GAqjB+BhB,SAEzBR,eAACR,KAAKyC,KAAI,CACR5F,KAAK,mBACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,qDAAchB,SAEjDR,eAAC4rB,KAAQ,CACPC,KAAM,GACN5rB,MAAO,CAAE8rB,WAAY,aACrBvpB,YAAY,+E,oBC71B1B,MAAQrD,MAAK,GAAEC,KAAK,IAAIC,MAChBqH,OAAO,IAAIC,KA0dJwlB,OAtdiBA,KAC9B,MAAOrxB,EAAS8C,GAAcmJ,oBAAS,IAChCqlB,EAAYC,GAAiBtlB,oBAAS,IACtCulB,EAAeC,GAAoBxlB,oBAAS,IAG5CvO,EAAQge,GAAazP,mBAAS,KAC9BylB,EAAUC,GAAe1lB,mBAAmB,KAC5C2lB,EAAaC,GAAkB5lB,mBAAiB,KAGhD3M,EAAWirB,GAAgBte,mBAAS,KACpCoe,EAAaC,GAAkBre,mBAAmB,KAClD6lB,EAAgBC,GAAqB9lB,mBAAiB,KACtD+lB,EAAeC,GAAoBhmB,mBAAiB,KACpDimB,EAAgBC,GAAqBlmB,oBAAS,GAG/CqS,EAAgBnI,sBAAY9V,UAChCkxB,GAAc,GACd,IACE,MAAM31B,QAAiB4D,EAA0B9B,GAC7C9B,EAASiB,KAAKu1B,YAChBT,EAAY/1B,EAASiB,KAAKu1B,WAC1BP,EAAe,IACwB,IAAnCj2B,EAASiB,KAAKu1B,UAAU9uB,OAC1BoD,KAAQ8I,KAAK,oEAEb9I,KAAQ+H,QAAQ,6BAAS7S,EAASiB,KAAKu1B,UAAU9uB,gCAGvD,CAAE,MAAO7H,GAAa,IAADI,EAAA+E,EACnB2N,QAAQ9S,MAAM,uDAAgBA,GAC9BiL,KAAQjL,MAAM,gEAAgC,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAUpF,EAAMiL,WACtEirB,EAAY,GACd,CAAC,QACCJ,GAAc,EAChB,GACC,CAAC7zB,IAGEiuB,EAAmBxV,sBAAY9V,UACnCoxB,GAAiB,GACjB,IACE,MAAM71B,QAAiB4D,EAA6BF,GAChD1D,EAASiB,KAAKw1B,eAChB/H,EAAe1uB,EAASiB,KAAKw1B,cAC7BN,EAAkB,IAClBE,EAAiB,IACjBE,GAAkB,GACwB,IAAtCv2B,EAASiB,KAAKw1B,aAAa/uB,OAC7BoD,KAAQ8I,KAAK,6EAEb9I,KAAQ+H,QAAQ,6BAAS7S,EAASiB,KAAKw1B,aAAa/uB,yCAG1D,CAAE,MAAO7H,GAAa,IAAD0F,EAAAC,EACnBmN,QAAQ9S,MAAM,gEAAeA,GAC7BiL,KAAQjL,MAAM,yEAA+B,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAUpF,EAAMiL,WACrE4jB,EAAe,GACjB,CAAC,QACCmH,GAAiB,EACnB,GACC,CAACnyB,IAGEG,EAAcY,UAClB,IACEyC,GAAW,GACX,MAAMlH,QAAiB4D,EAAyB9B,EAAQ40B,GAGlDxE,EAAM9xB,OAAO+xB,IAAIC,gBAAgB,IAAIC,KAAK,CAACryB,EAASiB,QACpDqxB,EAAOC,SAASC,cAAc,KACpCF,EAAKhyB,KAAO4xB,EACZI,EAAKG,aAAa,WAAYiE,GAC9BnE,SAASG,KAAKC,YAAYL,GAC1BA,EAAKM,QACLN,EAAKO,SACLzyB,OAAO+xB,IAAIW,gBAAgBZ,GAE3BpnB,KAAQ+H,QAAQ,UAAK6jB,6BACvB,CAAE,MAAO72B,GAAa,IAADsV,EAAAC,EACnBzC,QAAQ9S,MAAM,2CAAcA,GAC5BiL,KAAQjL,MAAM,qCAAyB,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAUpF,EAAMiL,UACjE,CAAC,QACC5D,GAAW,EACb,GAIInD,EAAmBU,UACvB,IACEyC,GAAW,GACX,MAAMlH,QAAiB4D,EAA8BF,EAAWgzB,GAC5D12B,EAASiB,KAAKgwB,UAChBoF,EAAiBr2B,EAASiB,KAAKgwB,SAC/BsF,GAAkB,GAClBzrB,KAAQ+H,QAAQ,2DAEpB,CAAE,MAAOhT,GAAa,IAADic,EAAAC,EACnBpJ,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAdic,EAAAjc,EAAMG,gBAAQ,IAAA8b,GAAM,QAANC,EAAdD,EAAgB7a,YAAI,IAAA8a,OAAN,EAAdA,EAAsB9W,SAAUpF,EAAMiL,UACrE,CAAC,QACC5D,GAAW,EACb,GAIIyvB,EAAiBlyB,UACrB,IACEyC,GAAW,GACX,MAAMlH,QAAiB4D,EAA8BF,EAAWgzB,GAG1DE,EAAO,IAAIvE,KAAK,CAACryB,EAASiB,KAAKgwB,SAAU,CAAErmB,KAAM,eACjDsnB,EAAM9xB,OAAO+xB,IAAIC,gBAAgBwE,GACjCtE,EAAOC,SAASC,cAAc,KACpCF,EAAKhyB,KAAO4xB,EACZI,EAAKG,aAAa,WAAYiE,GAC9BnE,SAASG,KAAKC,YAAYL,GAC1BA,EAAKM,QACLN,EAAKO,SACLzyB,OAAO+xB,IAAIW,gBAAgBZ,GAE3BpnB,KAAQ+H,QAAQ,UAAK6jB,6BACvB,CAAE,MAAO72B,GAAa,IAADooB,EAAAC,EACnBvV,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,qCAAyB,QAAdooB,EAAApoB,EAAMG,gBAAQ,IAAAioB,GAAM,QAANC,EAAdD,EAAgBhnB,YAAI,IAAAinB,OAAN,EAAdA,EAAsBjjB,SAAUpF,EAAMiL,UACjE,CAAC,QACC5D,GAAW,EACb,GA4BF,OAtBAmC,oBAAU,KACR,GAAIvH,GAAUA,EAAO4F,OAAS,EAAG,CAC/B,MAAM2L,EAAQJ,WAAW,KACvByP,KACC,MAEH,MAAO,IAAMpP,aAAaD,EAC5B,GAEC,CAACvR,IAEJuH,oBAAU,KACR,GAAI3F,GAAaA,EAAUgE,OAAS,EAAG,CACrC,MAAM2L,EAAQJ,WAAW,KACvB8c,KACC,MAEH,MAAO,IAAMzc,aAAaD,EAC5B,GAEC,CAAC3P,IAGF0G,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SAAC,6BACpFR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,0KAIvBM,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAKzM,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAE9CR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC4D,KAAgB,IACjB5D,eAAA,QAAAQ,SAAM,6DAGVS,KAAK,QAAOT,SAEZM,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbR,eAACqC,KAAK,CACJoG,MAAOjQ,EACP6R,SAAWI,GAAM+L,EAAU/L,EAAEM,OAAOtC,OACpCjG,YAAY,6BACZF,OAAQtC,eAACmqB,KAAkB,IAC3BlqB,MAAO,CAAE+K,UAAW,GACpBof,WACEpqB,eAAC2C,KAAM,CACL1B,KAAK,QACLqC,KAAMtD,eAACwiB,KAAc,IACrBte,QAASkV,EACTte,QAASsxB,EAAW5rB,SACrB,sBAOPR,eAAC2K,KAAO,CAAC1K,MAAO,CAAE4F,OAAQ,YAEzBumB,EACCtrB,gBAAA,OAAKb,MAAO,CAAEiB,UAAW,SAAUX,QAAS,QAASC,SAAA,CACnDR,eAACqL,KAAI,CAACpK,KAAK,UACXjB,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,gEAGL,IAApBgsB,EAASpuB,OACX4B,eAACutB,KAAK,CACJ9rB,YAAY,gJACZ+rB,MAAOD,KAAME,yBAGf3sB,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,sCACbR,eAAC2G,KAAM,CACL1G,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,sDACZiG,MAAOikB,EACPriB,SAAUsiB,EACVtC,YAAU,EACVE,aAAcA,CAACC,EAAOC,KAAM,IAAAC,EAAA,OACnB,OAAND,QAAM,IAANA,GAAgB,QAAVC,EAAND,EAAQjqB,gBAAQ,IAAAkqB,OAAV,EAAPA,EAAyC1hB,cAAcgJ,SAASwY,EAAMxhB,gBACvExI,SAEAgsB,EAAS9jB,IAAIrQ,GACZ2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC7BM,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC4D,KAAgB,IAChBvL,MAHQA,MAShBq0B,GACC1sB,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAI9J,UAAW,UAAWV,SACjDM,gBAAC6B,KAAM,CACLrB,KAAK,UACLgC,KAAMtD,eAAC0oB,KAAgB,IACvBxkB,QAASA,IAAM3J,EAAYmyB,GAC3B5xB,QAASA,EACTmG,KAAK,QAAOT,SAAA,CACb,gBACKksB,OAKV1sB,eAAC2K,KAAO,CAAC1K,MAAO,CAAE4F,OAAQ,YAE1B/E,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAAC0tB,KAAI,CACHzsB,KAAK,QACLhB,MAAO,CAAE+K,UAAW,EAAG2iB,UAAW,IAAK/oB,SAAU,QACjDiC,WAAY2lB,EACZoB,WAAav1B,GACX2H,eAAC0tB,KAAKzrB,KAAI,CACR7E,QAAS,CACP4C,eAAC2C,KAAM,CAELrB,KAAK,OACLL,KAAK,QACLqC,KAAMtD,eAAC0oB,KAAgB,IACvBxkB,QAASA,IAAM3J,EAAYlC,GAC3ByC,QAASA,EAAQ0F,SAClB,gBANK,aASNA,SAEFR,eAAC0tB,KAAKzrB,KAAK4rB,KAAI,CACbC,OAAQ9tB,eAAC4D,KAAgB,CAAC3D,MAAO,CAAEmB,MAAO,aAC1CuE,MAAOtN,EACPoJ,YACEzB,eAACuhB,KAAG,CAACngB,MAAM,OAAOH,KAAK,QAAOT,SAAC,uCAcrDR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC6D,KAAc,IACf7D,eAAA,QAAAQ,SAAM,kDAGVS,KAAK,QAAOT,SAEZM,gBAACC,KAAK,CAACC,UAAU,WAAWf,MAAO,CAAES,MAAO,QAASF,SAAA,CACnDM,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbR,eAACqC,KAAK,CACJoG,MAAOrO,EACPiQ,SAAWI,GAAM4a,EAAa5a,EAAEM,OAAOtC,OACvCjG,YAAY,6BACZF,OAAQtC,eAACmqB,KAAkB,IAC3BlqB,MAAO,CAAE+K,UAAW,GACpBof,WACEpqB,eAAC2C,KAAM,CACL1B,KAAK,QACLqC,KAAMtD,eAACwiB,KAAc,IACrBte,QAASuiB,EACT3rB,QAASwxB,EAAc9rB,SACxB,sBAOPR,eAAC2K,KAAO,CAAC1K,MAAO,CAAE4F,OAAQ,YAEzBymB,EACCxrB,gBAAA,OAAKb,MAAO,CAAEiB,UAAW,SAAUX,QAAS,QAASC,SAAA,CACnDR,eAACqL,KAAI,CAACpK,KAAK,UACXjB,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,yEAGF,IAAvB2kB,EAAY/mB,OACd4B,eAACutB,KAAK,CACJ9rB,YAAY,yJACZ+rB,MAAOD,KAAME,yBAGf3sB,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,+CACbR,eAAC2G,KAAM,CACL1G,MAAO,CAAES,MAAO,OAAQsK,UAAW,GACnCxI,YAAY,+DACZiG,MAAOmkB,EACPviB,SAAUwiB,EACVxC,YAAU,EACVE,aAAcA,CAACC,EAAOC,KAAM,IAAAe,EAAA,OACnB,OAANf,QAAM,IAANA,GAAgB,QAAVe,EAANf,EAAQjqB,gBAAQ,IAAAgrB,OAAV,EAAPA,EAAyCxiB,cAAcgJ,SAASwY,EAAMxhB,gBACvExI,SAEA2kB,EAAYzc,IAAIrQ,GACf2H,eAAC0G,GAAM,CAAY+B,MAAOpQ,EAAKmI,SAC7BM,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC4D,KAAgB,IAChBvL,MAHQA,MAShBu0B,GACC5sB,eAAA,OAAKC,MAAO,CAAE+K,UAAW,IAAKxK,SAC5BM,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC2C,KAAM,CACLrB,KAAK,UACLgC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IAAMzJ,EAAiBmyB,GAChC9xB,QAASA,EAAQ0F,SAClB,6BAGDR,eAAC2C,KAAM,CACLW,KAAMtD,eAAC0oB,KAAgB,IACvBxkB,QAASA,IAAMmpB,EAAeT,GAC9B9xB,QAASA,EAAQ0F,SAClB,kCAOPR,eAAC2K,KAAO,CAAC1K,MAAO,CAAE4F,OAAQ,YAE1B/E,gBAAA,OAAAN,SAAA,CACER,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,mCACbR,eAAC0tB,KAAI,CACHzsB,KAAK,QACLhB,MAAO,CAAE+K,UAAW,EAAG2iB,UAAW,IAAK/oB,SAAU,QACjDiC,WAAYse,EACZyI,WAAav1B,GACX2H,eAAC0tB,KAAKzrB,KAAI,CACR7E,QAAS,CACP4C,eAAC2C,KAAM,CAELrB,KAAK,OACLL,KAAK,QACLqC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IAAMzJ,EAAiBpC,GAChCyC,QAASA,EAAQ0F,SAClB,gBANK,QASNR,eAAC2C,KAAM,CAELrB,KAAK,OACLL,KAAK,QACLqC,KAAMtD,eAAC0oB,KAAgB,IACvBxkB,QAASA,IAAMmpB,EAAeh1B,GAC9ByC,QAASA,EAAQ0F,SAClB,gBANK,aASNA,SAEFR,eAAC0tB,KAAKzrB,KAAK4rB,KAAI,CACbC,OAAQ9tB,eAAC4D,KAAgB,CAAC3D,MAAO,CAAEmB,MAAO,aAC1CuE,MAAOtN,EACPoJ,YACEzB,eAACuhB,KAAG,CAACngB,MAAM,QAAQH,KAAK,QAAOT,SAAC,mDAevDwsB,GAAkBF,GACjB9sB,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC+hB,KAAW,IACZjhB,gBAAA,QAAAN,SAAA,CAAOosB,EAAe,sBAG1B3sB,MAAO,CAAE+K,UAAW,IACpB/J,KAAK,QACLoL,MACErM,eAAC2C,KAAM,CACL1B,KAAK,QACLiD,QAASA,IAAM+oB,GAAkB,GAAOzsB,SACzC,iBAGFA,SAEDR,eAAC4rB,KAAQ,CACPnjB,MAAOqkB,EACPjB,KAAM,GACNC,UAAQ,EACR7rB,MAAO,CACL8rB,WAAY,YACZlpB,SAAU,OACVqb,gBAAiB,mB,sEC/c/B,MAAQ/e,MAAK,GAAEC,KAAK,IAAIC,MAClB,MAAE0uB,IAAUC,KAoZHC,OA1YsBA,KACnC,MAAOnzB,EAAS8C,GAAcmJ,oBAAS,IAChCmnB,EAAOC,GAAYpnB,mBAAiB,KACpCqnB,GAAsB5uB,KAAKC,WAC3B4uB,GAAe7uB,KAAKC,WAGrB,KAAE5E,EAAI,MAAE1E,GAAU2J,YAAa/D,GAAqBA,EAAMC,MAC1DsyB,GAAkB,OAAJzzB,QAAI,IAAJA,OAAI,EAAJA,EAAMvD,WAAY,GAChCi3B,EAA0B,UAAhBD,EAGVE,EAAavd,sBAAY9V,UAC7B,GAAKhF,EAEL,IACE,MAAMO,QAAiBO,EAAiBd,GACxC,GAAIO,EAASiB,KAAM,CAEjB,MAAM82B,EAAW/gB,OAAOE,QAAQlX,EAASiB,MAAM+Q,IAAItN,IAAA,IAAE9D,EAAUiE,GAAwBH,EAAA,MAAM,CAC3F9D,WACAo3B,SAAuB,UAAbp3B,EACVsqB,aAAcrmB,EAASqmB,cAAgB,GACvC+M,WAAYpzB,EAASozB,YAAc,MAErCR,EAASM,EACX,CACF,CAAE,MAAOl4B,GAAa,IAADI,EAAA+E,EACnB2N,QAAQ9S,MAAM,oDAAaA,GAC3BiL,KAAQjL,MAAM,6DAA6B,QAAdI,EAAAJ,EAAMG,gBAAQ,IAAAC,GAAM,QAAN+E,EAAd/E,EAAgBgB,YAAI,IAAA+D,OAAN,EAAdA,EAAsBC,SAAUpF,EAAMiL,UACrE,GACC,CAACrL,IAGJ4J,oBAAU,KACJwuB,GAAWp4B,GACbq4B,KAED,CAACD,EAASp4B,EAAOq4B,IAGpB,MAmDMI,EAAmBA,CAAC9M,EAAQrZ,IAC3BA,EAGDA,EAAMrK,OAAS,EACV5H,QAAQC,OAAO,IAAI+Y,MAAM,gDAE7B,yBAAyBqf,KAAKpmB,GAG5BjS,QAAQ8Y,UAFN9Y,QAAQC,OAAO,IAAI+Y,MAAM,uEANzBhZ,QAAQC,OAAO,IAAI+Y,MAAM,mCAY9Bsf,EAA0BA,CAAChN,EAAQrZ,KACvC,MAAMlJ,EAAO6uB,GAAsBC,EAC7BU,EAAgBxvB,IAAS6uB,EAAqB,eAAiB,oBAC/D72B,EAAWgI,EAAK6rB,cAAc2D,GAEpC,OAAKtmB,EAGDA,IAAUlR,EACLf,QAAQC,OAAO,IAAI+Y,MAAM,iEAE3BhZ,QAAQ8Y,UALN9Y,QAAQC,OAAO,IAAI+Y,MAAM,oCAS9Bwf,EAAc,CAClB,CACErpB,MAAO,qBACPqb,UAAW,WACX3d,IAAK,WACLge,OAAS/pB,GACPwJ,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACuC,KAAY,IACbvC,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAElJ,QAIpB,CACEqO,MAAO,eACPqb,UAAW,WACX3d,IAAK,WACLge,OAASkN,GACPvuB,eAACuhB,KAAG,CAACngB,MAAOmtB,EAAU,MAAQ,OAAO/tB,SAClC+tB,EAAU,qBAAQ,8BAIzB,CACE5oB,MAAO,2BACPqb,UAAW,eACX3d,IAAK,eACLge,OAASQ,GAAiBA,EAAO,IAAI7jB,KAAK6jB,GAAMjH,iBAAmB,OAErE,CACEjV,MAAO,2BACPqb,UAAW,aACX3d,IAAK,aACLge,OAASQ,GAAiBA,EAAO,IAAI7jB,KAAK6jB,GAAMjH,iBAAmB,6BAIvE,OACE9Z,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAGlB,MAAO,CAAE4C,SAAU,OAAQiD,WAAY,IAAKzE,aAAc,OAAQb,SACjFM,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACivB,KAAY,IAAG,gCAIpBjvB,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,iHAIvBM,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAKzM,MAAO,CAAE+K,UAAW,IAAKxK,SAAA,CAE9CR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZM,gBAACL,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC0C,KAAY,IACb1C,eAAA,QAAAQ,SAAM,gCAGVS,KAAK,QAAOT,SAAA,CAEZR,eAACuB,KAAK,CACJC,QAAQ,uCACRC,YAAY,gNACZH,KAAK,OACLI,UAAQ,EACRzB,MAAO,CAAEoB,aAAc,MAGzBP,gBAACtB,KAAI,CACHD,KAAM6uB,EACNvE,OAAO,WACPhoB,SAvJiB1G,UAC3B,GAAKhF,EAAL,CAEAyH,GAAW,GACX,WACyB3G,EAAuB,CAC5CK,SAAUg3B,EACVY,aAAcptB,EAAOotB,aACrBC,aAAcrtB,EAAOqtB,aACrBC,iBAAkBttB,EAAOstB,kBACxBj5B,IAEUwB,KAAK6J,UAChBA,KAAQ+H,QAAQ,+CAChB6kB,EAAmBlE,cAEvB,CAAE,MAAO3zB,GAAa,IAAD0F,EAAAC,EACnBmN,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAd0F,EAAA1F,EAAMG,gBAAQ,IAAAuF,GAAM,QAANC,EAAdD,EAAgBtE,YAAI,IAAAuE,OAAN,EAAdA,EAAsBP,SAAUpF,EAAMiL,UACnE,CAAC,QACC5D,GAAW,EACb,CApBkB,GAuJRqC,MAAO,CAAEU,SAAU,KAAMH,SAAA,CAEzBR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,eACLqvB,aAAc4C,EAAY9tB,SAE1BR,eAACqC,KAAK,CACJC,OAAQtC,eAACuC,KAAY,IACrB4I,UAAQ,EACR1C,MAAO6lB,MAIXtuB,eAACR,KAAKyC,KAAI,CACRuB,MAAM,qBACNnH,KAAK,eACL6F,MAAO,CAAC,CAAEC,UAAU,EAAMX,QAAS,yCAAYhB,SAE/CR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0C,KAAY,IACrBF,YAAY,uCACZ6sB,WAAaC,GAAaA,EAAUtvB,eAACuvB,KAAU,IAAMvvB,eAACwvB,KAAoB,QAI9ExvB,eAACR,KAAKyC,KAAI,CACRuB,MAAM,qBACNnH,KAAK,eACL6F,MAAO,CAAC,CAAEutB,UAAWb,IAAoBpuB,SAEzCR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0vB,KAAc,IACvBltB,YAAY,sHACZ6sB,WAAaC,GAAaA,EAAUtvB,eAACuvB,KAAU,IAAMvvB,eAACwvB,KAAoB,QAI9ExvB,eAACR,KAAKyC,KAAI,CACRuB,MAAM,iCACNnH,KAAK,mBACL6F,MAAO,CAAC,CAAEutB,UAAWX,IAA2BtuB,SAEhDR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0vB,KAAc,IACvBltB,YAAY,mDACZ6sB,WAAaC,GAAaA,EAAUtvB,eAACuvB,KAAU,IAAMvvB,eAACwvB,KAAoB,QAI9ExvB,eAACR,KAAKyC,KAAI,CAAAzB,SACRR,eAAC2C,KAAM,CACLrB,KAAK,UACLsB,SAAS,SACT9H,QAASA,EACTwI,KAAMtD,eAAC0C,KAAY,IACnBzB,KAAK,QAAOT,SACb,uCASR+tB,GACCvuB,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACivB,KAAY,IACbjvB,eAAA,QAAAQ,SAAM,6BACNR,eAACuhB,KAAG,CAACngB,MAAM,MAAKZ,SAAC,sCAGrBS,KAAK,QAAOT,SAEZM,gBAACktB,KAAQ,CAACpE,iBAAkB,CAAC,KAAM+F,OAAK,EAAAnvB,SAAA,CACtCR,eAAC+tB,GAAK,CACJ6B,OACE9uB,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC6vB,KAAe,IAChB7vB,eAAA,QAAAQ,SAAM,sCAETA,SAGDM,gBAACtB,KAAI,CACHD,KAAM8uB,EACNxE,OAAO,WACPhoB,SA1NI1G,UACpB,GAAKhF,EAAL,CAEAyH,GAAW,GACX,WACyB3G,EAAgB,CACrCK,SAAUg3B,EACVwB,aAAchuB,EAAOguB,aACrBC,kBAAmBjuB,EAAOiuB,kBAC1BC,sBAAuBluB,EAAOkuB,uBAC7B75B,IAEUwB,KAAK6J,UAChBA,KAAQ+H,QAAQ,+CAChB8kB,EAAYnE,cACZsE,IAEJ,CAAE,MAAOj4B,GAAa,IAADsV,EAAAC,EACnBzC,QAAQ9S,MAAM,wCAAWA,GACzBiL,KAAQjL,MAAM,iDAA2B,QAAdsV,EAAAtV,EAAMG,gBAAQ,IAAAmV,GAAM,QAANC,EAAdD,EAAgBlU,YAAI,IAAAmU,OAAN,EAAdA,EAAsBnQ,SAAUpF,EAAMiL,UACnE,CAAC,QACC5D,GAAW,EACb,CArBkB,GA0NFqC,MAAO,CAAEU,SAAU,KAAMH,SAAA,CAEzBR,eAACR,KAAKyC,KAAI,CACRuB,MAAM,2BACNnH,KAAK,eACL6F,MAAO,CACL,CAAEC,UAAU,EAAMX,QAAS,wCAC3B,CAAEY,IAAK,EAAGZ,QAAS,yCACnB,CAAEyuB,QAAS,kBAAmBzuB,QAAS,qGACvChB,SAEFR,eAACqC,KAAK,CACJC,OAAQtC,eAACuC,KAAY,IACrBC,YAAY,iDAIhBxC,eAACR,KAAKyC,KAAI,CACRuB,MAAM,iCACNnH,KAAK,oBACL6F,MAAO,CAAC,CAAEutB,UAAWb,IAAoBpuB,SAEzCR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0C,KAAY,IACrBF,YAAY,kIACZ6sB,WAAaC,GAAaA,EAAUtvB,eAACuvB,KAAU,IAAMvvB,eAACwvB,KAAoB,QAI9ExvB,eAACR,KAAKyC,KAAI,CACRuB,MAAM,6CACNnH,KAAK,wBACL6F,MAAO,CAAC,CAAEutB,UAAWX,IAA2BtuB,SAEhDR,eAACqC,KAAMI,SAAQ,CACbH,OAAQtC,eAAC0vB,KAAc,IACvBltB,YAAY,+DACZ6sB,WAAaC,GAAaA,EAAUtvB,eAACuvB,KAAU,IAAMvvB,eAACwvB,KAAoB,QAI9ExvB,eAACR,KAAKyC,KAAI,CAAAzB,SACRR,eAAC2C,KAAM,CACLrB,KAAK,UACLsB,SAAS,SACT9H,QAASA,EACTwI,KAAMtD,eAAC6vB,KAAe,IACtB5uB,KAAK,QAAOT,SACb,mCAtDD,KA6DNM,gBAACitB,GAAK,CACJ6B,OACE9uB,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACivB,KAAY,IACbjvB,eAAA,QAAAQ,SAAM,6BACNM,gBAACygB,KAAG,CAACngB,MAAM,OAAMZ,SAAA,CAAE0tB,EAAM9vB,OAAO,4BAEnCoC,SAAA,CAGDR,eAAA,OAAKC,MAAO,CAAEoB,aAAc,IAAKb,SAC/BR,eAAC2C,KAAM,CACLW,KAAMtD,eAACivB,KAAY,IACnB/qB,QAASsqB,EACT1zB,QAASA,EAAQ0F,SAClB,2CAKHR,eAAC0iB,KAAK,CACJ3B,QAASiO,EACTnoB,WAAYqnB,EACZvL,OAAO,WACPC,WAAY,CACVC,SAAU,GACVC,iBAAiB,EACjBE,UAAYC,GAAU,UAAKA,wBAE7BhiB,KAAK,YArBH,aA8BZstB,GACAvuB,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACuB,KAAK,CACJC,QAAQ,2BACRC,YAAY,yPACZH,KAAK,UACLI,UAAQ,EACR4B,KAAMtD,eAACkwB,KAAyB,e,2DC1Y9C,MAAQ/wB,MAAK,GAAEC,KAAK,IAAIC,KAukBT8wB,OArkBmBA,KAAO,IAADC,EACtC,MAAM,aACJ7f,EAAY,eACZE,EAAc,QACd3V,EAAO,kBACPqW,EAAiB,oBACjBC,EAAmB,WACnBzC,EAAU,iBACVG,EAAgB,oBAChBC,EAAmB,eACnBW,EAAc,YACd3B,GACEsC,MAEGyI,EAAcuX,GAAmBtpB,mBAAsB,OACvDupB,EAAmBC,GAAwBxpB,oBAAS,IACpDypB,EAAYC,GAAiB1pB,oBAAS,GAG7ChH,oBAAU,KACRoR,IACAC,KACC,CAACD,EAAmBC,IAGvBrR,oBAAU,KACR,MAAMqP,EAAWzD,YAAY,KAC3BwF,GAAkB,IACjB,KAEH,MAAO,IAAMvF,cAAcwD,IAC1B,CAAC+B,IAGJ,MAsCMuf,EAAwB/nB,IAC5B0nB,EAAgB1nB,GAChB4nB,GAAqB,IA4CjBI,EAAqB/5B,IACzB,OAAQA,GACN,KAAKmX,EAAYE,QACf,OAAOjO,eAAC4wB,KAAY,CAACC,MAAI,EAAC5wB,MAAO,CAAEmB,MAAO,aAC5C,KAAK2M,EAAYG,UACf,OAAOlO,eAAC8wB,KAAmB,CAAC7wB,MAAO,CAAEmB,MAAO,aAC9C,KAAK2M,EAAYI,OACf,OAAOnO,eAAC+wB,KAAmB,CAAC9wB,MAAO,CAAEmB,MAAO,aAC9C,KAAK2M,EAAYK,UACf,OAAOpO,eAACgxB,KAAY,CAAC/wB,MAAO,CAAEmB,MAAO,aACvC,QACE,OAAOpB,eAAC8D,KAAmB,CAAC7D,MAAO,CAAEmB,MAAO,eAK5C6vB,EAAoBr6B,IACxB,OAAQA,GACN,KAAKmX,EAAYE,QACf,OAAOjO,eAACuhB,KAAG,CAACngB,MAAM,aAAakC,KAAMtD,eAAC4wB,KAAY,CAACC,MAAI,IAAIrwB,SAAC,uBAC9D,KAAKuN,EAAYG,UACf,OAAOlO,eAACuhB,KAAG,CAACngB,MAAM,UAAUkC,KAAMtD,eAAC8wB,KAAmB,IAAItwB,SAAC,uBAC7D,KAAKuN,EAAYI,OACf,OAAOnO,eAACuhB,KAAG,CAACngB,MAAM,QAAQkC,KAAMtD,eAAC+wB,KAAmB,IAAIvwB,SAAC,iBAC3D,KAAKuN,EAAYK,UACf,OAAOpO,eAACuhB,KAAG,CAACngB,MAAM,UAAUkC,KAAMtD,eAACgxB,KAAY,IAAIxwB,SAAC,uBACtD,QACE,OAAOR,eAACuhB,KAAG,CAACngB,MAAM,UAAUkC,KAAMtD,eAAC8D,KAAmB,IAAItD,SAAC,yBAK3D0wB,EAAcC,GACbA,EACE,IAAInzB,KAAKmzB,GAAYvW,eAAe,SADnB,eAI1B,OACE9Z,gBAAA,OAAKb,MAAO,CAAEM,QAAS,QAASC,SAAA,CAC9BM,gBAAA,OAAKb,MAAO,CAAEoB,aAAc,OAAQjB,QAAS,OAAQE,eAAgB,gBAAiBD,WAAY,UAAWG,SAAA,CAC3GM,gBAAA,OAAAN,SAAA,CACER,eAACb,GAAK,CAACgC,MAAO,EAAEX,SAAC,6BACjBR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,gIAIzBR,eAAC2C,KAAM,CACLrB,KAAK,UACLgC,KAAMtD,eAACwiB,KAAc,IACrBte,QArIc/I,UACpBs1B,GAAc,GACd,UACQj6B,QAAQ46B,IAAI,CAChBjgB,IACAC,MAEF5P,KAAQ+H,QAAQ,2BAClB,CAAE,MAAOhT,GACPiL,KAAQjL,MAAM,2BAChB,CAAC,QACCk6B,GAAc,EAChB,GA0HM31B,QAAS01B,EAAWhwB,SACrB,oBAMHR,eAACS,KAAI,CAACR,MAAO,CAAEoB,aAAc,QAASb,SACpCM,gBAAC2L,KAAG,CAACC,OAAQ,GAAGlM,SAAA,CACdR,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,iCACN8C,MAAO8H,EAAanS,OACpBkE,OAAQtC,eAAC4wB,KAAY,CAACC,MAAI,IAC1B9c,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,iCACN8C,MAAOgI,EAAelS,OAAOuT,GAAKA,EAAElb,SAAWmX,EAAYG,WAAW9P,OACtEkE,OAAQtC,eAAC8wB,KAAmB,IAC5B/c,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAOgI,EAAelS,OAAOuT,GAAKA,EAAElb,SAAWmX,EAAYI,QAAQ/P,OACnEkE,OAAQtC,eAAC+wB,KAAmB,IAC5Bhd,WAAY,CAAE3S,MAAO,eAGzBpB,eAAC2M,KAAG,CAACC,KAAM,EAAEpM,SACXR,eAACwT,KAAS,CACR7N,MAAM,2BACN8C,MAAO8H,EAAanS,OAASqS,EAAerS,OAC5CkE,OAAQtC,eAACwL,KAAkB,aAMnC1K,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAIlM,SAAA,CAEpBR,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC4wB,KAAY,CAACC,MAAI,IAAG,iCAErB7wB,eAACuhB,KAAG,CAACngB,MAAM,aAAYZ,SAAE+P,EAAanS,YAG1CiO,MACErM,eAACoV,KAAO,CAACzP,MAAM,iCAAOnF,SACpBR,eAAC4wB,KAAY,CAACC,MAAI,EAAC5wB,MAAO,CAAEmB,MAAO,eAEtCZ,SAEDR,eAACqL,KAAI,CAACC,SAAUxQ,EAAQ0F,SACtBR,eAAC0tB,KAAI,CACH7mB,WAAY0J,EACZ8gB,OAAQ,CAAEC,UAAWtxB,eAACutB,KAAK,CAAC9rB,YAAY,sDACxCmsB,WAAajlB,GACX3I,eAAC0tB,KAAKzrB,KAAI,CACR7E,QAAS,CACP4C,eAAC2C,KAAM,CACLrB,KAAK,OACLgC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IAAMwsB,EAAqB/nB,GAAMnI,SAC3C,iBAGDR,eAACiiB,KAAU,CACTtc,MAAM,qEACNuc,UAAWA,IAtKR/mB,WACvB,UACQwT,EAAWd,SACXsD,GACR,CAAE,MAAO5a,GACP8S,QAAQ9S,MAAM,wCAAWA,EAC3B,GAgKqCg7B,CAAiB5oB,EAAK0I,SACvC+Q,OAAO,eACPC,WAAW,eAAI7hB,SAEfR,eAAC2C,KAAM,CACLrB,KAAK,OACLgL,QAAM,EACNhJ,KAAMtD,eAACgxB,KAAY,IAAIxwB,SACxB,oBAIHA,SAEFR,eAAC0tB,KAAKzrB,KAAK4rB,KAAI,CACbC,OAAQ6C,EAAkBhoB,EAAK/R,QAC/B+O,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAEkP,EAAe/G,EAAKsJ,aACjCgf,EAAiBtoB,EAAK/R,WAG3B6K,YACEX,gBAAA,OAAAN,SAAA,CACEM,gBAAC1B,GAAI,CAACkC,KAAK,YAAYspB,SAAU,CAAEtJ,KAAM3Y,EAAK0I,SAAU7Q,SAAA,CAAC,OAClDmI,EAAK0I,QAAQW,SAAS,KACzB,GAAGrJ,EAAK0I,QAAQkC,MAAM,KAAK,QAAQ5K,EAAK0I,QAAQkC,MAAM,KAAKlV,OAAO,GAAG,GAAGiT,UAAU,EAAG,KACrF,GAAG3I,EAAK0I,QAAQC,UAAU,EAAG,WAGjCtR,eAAA,SACAc,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,6BAAO0wB,EAAWvoB,EAAKgS,eAC7ChS,EAAKnH,SACJV,gBAAAmK,YAAA,CAAAzK,SAAA,CACER,eAAA,SACAc,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,iBAAKmI,EAAKnH,mBAGlBqL,IAAlBlE,EAAKd,UACJ7H,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BR,eAAC+L,KAAQ,CAACC,QAASrD,EAAKd,SAAU5G,KAAK,2BAc7DjB,eAAC2M,KAAG,CAACC,KAAM,GAAGpM,SACZR,eAACS,KAAI,CACHkF,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAAC8wB,KAAmB,IAAG,iCAEvB9wB,eAACuhB,KAAG,CAACngB,MAAM,UAASZ,SAAEiQ,EAAerS,YAGzCiO,MACErM,eAAC2C,KAAM,CACLrB,KAAK,UACLgL,QAAM,EACNrL,KAAK,QACLqC,KAAMtD,eAACuM,KAAc,IACrBpB,SAAoC,IAA1BsF,EAAerS,OACzB8F,QAlOestB,KAC3B,MAAMC,EAAiBhhB,EAAerS,OACf,IAAnBqzB,EAKJ7N,KAAM8N,QAAQ,CACZ/rB,MAAO,qEACPgiB,QAAS,8CAAW8J,2GACpBnuB,KAAMtD,eAACuM,KAAc,CAACtM,MAAO,CAAEmB,MAAO,aACtCghB,OAAQ,eACRuP,OAAQ,SACRtP,WAAY,eACZuP,KAAMz2B,UACJ,UACQ4T,UAEAvY,QAAQ46B,IAAI,CAChBjgB,IACAC,KAEJ,CAAE,MAAO7a,GACP8S,QAAQ9S,MAAM,wCAAWA,EAC3B,KArBFiL,KAAQ8I,KAAK,6EA+N2B9J,SAC/B,6BAGFA,SAEDR,eAAC0tB,KAAI,CACH7mB,WAAY4J,EACZ4gB,OAAQ,CACNC,UACEtxB,eAACutB,KAAK,CACJ9rB,YACEX,gBAAA,OAAAN,SAAA,CACER,eAAA,OAAAQ,SAAK,qDACLR,eAACZ,GAAI,CAACkC,KAAK,YAAYrB,MAAO,CAAE4C,SAAU,QAASrC,SAAC,6HAQ9DotB,WAAajlB,GACX3I,eAAC0tB,KAAKzrB,KAAI,CACR7E,QAAS,CACP4C,eAAC2C,KAAM,CACLrB,KAAK,OACLgC,KAAMtD,eAAC+hB,KAAW,IAClB7d,QAASA,IAAMwsB,EAAqB/nB,GAAMnI,SAC3C,iBAGDR,eAAC2C,KAAM,CACLrB,KAAK,OACLgL,QAAM,EACNhJ,KAAMtD,eAAC6xB,KAAmB,IAC1B3tB,QAASA,KAAM4tB,OA5SHjkB,EA4S0BlF,EAAK0I,aA3S7DuS,KAAM8N,QAAQ,CACZ/rB,MAAO,2BACPgiB,QAAS,8CAAW9Z,EAAOyD,UAAU,EAAG,qBACxChO,KAAMtD,eAAC6xB,KAAmB,CAAC5xB,MAAO,CAAEmB,MAAO,aAC3CghB,OAAQ,eACRuP,OAAQ,SACRtP,WAAY,eACZuP,KAAMz2B,gBACkB2T,EAAiBjB,UAG/BrX,QAAQ46B,IAAI,CAChBjgB,IACAC,SAdsBvD,OA4SwCrN,SACrD,kBAGDA,SAEFR,eAAC0tB,KAAKzrB,KAAK4rB,KAAI,CACbC,OAAQ6C,EAAkBhoB,EAAK/R,QAC/B+O,MACE7E,gBAACC,KAAK,CAAAP,SAAA,CACJR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAEkP,EAAe/G,EAAKsJ,aACjCgf,EAAiBtoB,EAAK/R,WAG3B6K,YACEX,gBAAA,OAAAN,SAAA,CACEM,gBAAC1B,GAAI,CAACkC,KAAK,YAAYspB,SAAU,CAAEtJ,KAAM3Y,EAAK0I,SAAU7Q,SAAA,CAAC,OAClDmI,EAAK0I,QAAQW,SAAS,KACzB,GAAGrJ,EAAK0I,QAAQkC,MAAM,KAAK,QAAQ5K,EAAK0I,QAAQkC,MAAM,KAAKlV,OAAO,GAAG,GAAGiT,UAAU,EAAG,KACrF,GAAG3I,EAAK0I,QAAQC,UAAU,EAAG,WAGjCtR,eAAA,SACAc,gBAAC1B,GAAI,CAACkC,KAAK,YAAWd,SAAA,CAAC,6BAAO0wB,EAAWvoB,EAAK+R,eAC7C/R,EAAK/R,SAAWmX,EAAYI,QAAUxF,EAAKpS,OAC1CuK,gBAAAmK,YAAA,CAAAzK,SAAA,CACER,eAAA,SACAc,gBAAC1B,GAAI,CAACkC,KAAK,SAAQd,SAAA,CAAC,iBAAKmI,EAAKpS,4BAcpDyJ,eAAC4jB,KAAK,CACJje,MAAM,2BACNke,KAAMyM,EACNxM,SAAUA,IAAMyM,GAAqB,GACrCxM,OAAQ,CACN/jB,eAAC2C,KAAM,CAAauB,QAASA,IAAMqsB,GAAqB,GAAO/vB,SAAC,gBAApD,UAIdE,MAAO,IAAIF,SAEVsY,GACC9Y,eAAA,OAAAQ,SACEM,gBAAC2L,KAAG,CAACC,OAAQ,CAAC,GAAI,IAAIlM,SAAA,CACpBM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,oBACbR,eAAA,SACAA,eAACZ,GAAI,CAACwrB,UAAQ,EAAApqB,SAAEsY,EAAazH,aAE/BvQ,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SAAEkP,EAAeoJ,EAAa7G,gBAErCnR,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,kBACbR,eAAA,SACCixB,EAAiBnY,EAAaliB,WAEjCkK,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,kBACbR,eAAA,SACC8Y,EAAaliB,SAAWmX,EAAYG,UACnClO,eAAC+L,KAAQ,CAACC,QAAS,IAAK/K,KAAK,QAAQrK,OAAO,YAC1CkiB,EAAaliB,SAAWmX,EAAYI,QAEpC2K,EAAaliB,SAAWmX,EAAYK,UADtCpO,eAAC+L,KAAQ,CAACC,QAAS8M,EAAajR,UAAY,EAAG5G,KAAK,QAAQrK,OAAO,mBAGvCiW,IAA1BiM,EAAajR,SACf7H,eAAC+L,KAAQ,CAACC,QAAS8M,EAAajR,SAAU5G,KAAK,UAE/CjB,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,sCAG3BM,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SAAE0wB,EAAWpY,EAAa6B,iBAEjC7Z,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SAAE0wB,EAAWpY,EAAa4B,iBAEjC5Z,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SACF,CAACuxB,IAEA,GAAIjZ,EAAakZ,YAAclZ,EAAamZ,aAAc,CAExD,MAAO,GADU/hB,KAAKwO,OAAO,IAAI1gB,KAAK8a,EAAamZ,cAAc7hB,UAAY,IAAIpS,KAAK8a,EAAakZ,YAAY5hB,WAAa,YAE9H,CAEK,GAAI0I,EAAakZ,YAAclZ,EAAaliB,SAAWmX,EAAYE,QAAS,CAE/E,MAAO,GADUiC,KAAKwO,QAAO,IAAI1gB,MAAOoS,UAAY,IAAIpS,KAAK8a,EAAakZ,YAAY5hB,WAAa,iCAErG,CAEK,OAAI0I,EAAaliB,SAAWmX,EAAYG,WAAgC,QAAvB6jB,EAAIjZ,EAAa/Q,cAAM,IAAAgqB,GAAnBA,EAAqB3b,iBACtE,GAAGlG,KAAKwO,MAAM5F,EAAa/Q,OAAOqO,0BAGlC0C,EAAaliB,SAAWmX,EAAYG,UACpC,qBAGA4K,EAAaliB,SAAWmX,EAAYI,OACpC,2BAEA2K,EAAaliB,SAAWmX,EAAYK,UACpC,qBAIA,0BAEV,EA9BA,QAiCJ0K,EAAatP,cACZ1I,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SAAEsY,EAAatP,kBAGvBsP,EAAatX,SACZV,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,kBACbR,eAAA,SACAA,eAACZ,GAAI,CAAAoB,SAAEsY,EAAatX,aAGvBsX,EAAaviB,OACZuK,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAACZ,GAAI,CAACkC,KAAK,SAAQd,SAAEsY,EAAaviB,WAGrCuiB,EAAaoZ,QACZpxB,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAA,eAAA,OAAKC,MAAO,CACVE,WAAY,UACZI,QAAS,GACTM,aAAc,EACdgC,SAAU,GACV8qB,UAAW,IACX/oB,SAAU,OACVoG,UAAW,GACXxK,SACAR,eAAA,OAAAQ,SAAM7H,KAAKC,UAAUkgB,EAAaoZ,OAAQ,KAAM,UAIrDpZ,EAAa/Q,QAAqC,aAA3B+Q,EAAa7G,WACnCnR,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAc,gBAACC,KAAK,CAACoxB,MAAI,EAAClyB,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAClCR,eAACuhB,KAAG,CAACngB,MAAM,QAAQkC,KAAMtD,eAAC8wB,KAAmB,IAAItwB,SAAC,mCAGjDsY,EAAa/Q,OAAOqO,kBACnBtV,gBAACygB,KAAG,CAACngB,MAAM,OAAMZ,SAAA,CAAC,iBACX0P,KAAKwO,MAAM5F,EAAa/Q,OAAOqO,kBAAkB,YAGzD0C,EAAa/Q,OAAOgR,SACnBjY,gBAACygB,KAAG,CAACngB,MAAM,SAAQZ,SAAA,CAAC,6BACXkN,OAAOC,KAAKmL,EAAa/Q,OAAOgR,SAAS3a,aAItD4B,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,+HAM5BsY,EAAa/Q,QAAqC,eAA3B+Q,EAAa7G,WACnCnR,gBAAC6L,KAAG,CAACC,KAAM,GAAGpM,SAAA,CACZR,eAACZ,GAAI,CAACwL,QAAM,EAAApK,SAAC,8BACbR,eAAA,SACAc,gBAACC,KAAK,CAACoxB,MAAI,EAAClyB,MAAO,CAAE+K,UAAW,GAAIxK,SAAA,CAClCR,eAACuhB,KAAG,CAACngB,MAAM,QAAQkC,KAAMtD,eAAC8wB,KAAmB,IAAItwB,SAAC,wCAGXqM,IAAtCiM,EAAa/Q,OAAOqqB,eACnBtxB,gBAACygB,KAAG,CAACngB,MAAM,MAAKZ,SAAA,CAAC,6BAAOsY,EAAa/Q,OAAOqqB,kBAEd,QAA/BhC,EAAAtX,EAAa/Q,OAAOsqB,mBAAW,IAAAjC,OAAA,EAA/BA,EAAiChyB,SAChC0C,gBAACygB,KAAG,CAACngB,MAAM,OAAMZ,SAAA,CAAC,6BAAOsY,EAAa/Q,OAAOsqB,YAAYj0B,UAE1D0a,EAAa/Q,OAAOqO,kBACnBtV,gBAACygB,KAAG,CAACngB,MAAM,SAAQZ,SAAA,CAAC,iBACb0P,KAAKwO,MAAM5F,EAAa/Q,OAAOqO,kBAAkB,eAI5DpW,eAAA,OAAKC,MAAO,CAAE+K,UAAW,GAAIxK,SAC3BR,eAACZ,GAAI,CAACkC,KAAK,YAAWd,SAAC,0I,OC1kB3C,MAAQwC,QAAQ,IAAIC,KAGdqvB,GAA0Dl3B,IAAmB,IAAlB,SAAEoF,GAAUpF,EAG3E,OAFwB0E,YAAa/D,GAAqBA,EAAMC,KAAKpB,iBAM9DoF,eAAAiL,YAAA,CAAAzK,SAAGA,IAHDR,eAACuyB,KAAQ,CAACC,GAAG,SAASxU,SAAO,KA6CzByU,OAvCOA,KACpB,MAAM73B,EAAkBkF,YAAa/D,GAAqBA,EAAMC,KAAKpB,iBAErE,OACEoF,eAAA,OAAKsF,UAAU,MAAK9E,SAClBM,gBAAC4xB,KAAM,CAAAlyB,SAAA,CACLR,eAAC2yB,KAAK,CACJ3I,KAAK,SACL4I,QACEh4B,EAAkBoF,eAACuyB,KAAQ,CAACC,GAAG,IAAIxU,SAAO,IAAMhe,eAACV,GAAS,MAG9DU,eAAC2yB,KAAK,CACJ3I,KAAK,KACL4I,QACE5yB,eAACsyB,GAAc,CAAA9xB,SACbR,eAACkD,GAAU,CAAA1C,SACTR,eAACgD,GAAO,CAAAxC,SACNM,gBAAC4xB,KAAM,CAAAlyB,SAAA,CACLR,eAAC2yB,KAAK,CAAC3I,KAAK,IAAI4I,QAAS5yB,eAACuyB,KAAQ,CAACC,GAAG,iBAAiBxU,SAAO,MAC9Dhe,eAAC2yB,KAAK,CAAC3I,KAAK,iBAAiB4I,QAAS5yB,eAAC4G,GAAgB,MACvD5G,eAAC2yB,KAAK,CAAC3I,KAAK,kBAAkB4I,QAAS5yB,eAACqW,GAAiB,MACzDrW,eAAC2yB,KAAK,CAAC3I,KAAK,oBAAoB4I,QAAS5yB,eAACib,GAAmB,MAC7Djb,eAAC2yB,KAAK,CAAC3I,KAAK,kBAAkB4I,QAAS5yB,eAACif,GAAiB,MACzDjf,eAAC2yB,KAAK,CAAC3I,KAAK,kBAAkB4I,QAAS5yB,eAAC+kB,GAAiB,MACzD/kB,eAAC2yB,KAAK,CAAC3I,KAAK,cAAc4I,QAAS5yB,eAACmsB,GAAa,MACjDnsB,eAAC2yB,KAAK,CAAC3I,KAAK,mBAAmB4I,QAAS5yB,eAACiuB,GAAkB,MAC3DjuB,eAAC2yB,KAAK,CAAC3I,KAAK,gBAAgB4I,QAAS5yB,eAACmwB,GAAe,uB,OC9CzE0C,IAASxR,OACPrhB,eAAC8yB,IAAMC,WAAU,CAAAvyB,SACfR,eAACgzB,IAAQ,CAACv0B,MAAOA,GAAM+B,SACrBR,eAACizB,IAAa,CAAAzyB,SACZR,eAACkzB,IAAc,CAAC7B,OAAQ8B,IAAK3yB,SAC3BR,eAACyyB,GAAG,YAKZxJ,SAASmK,eAAe,Q", "file": "static/js/main.f8da89ef.chunk.js", "sourcesContent": ["import axios, { AxiosInstance } from 'axios';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token && config.headers) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期，清除本地存储并跳转到登录页\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 认证相关API\nexport const authAPI = {\n  login: (credentials: { username: string; password: string }) => {\n    // 将JSON数据转换为URLSearchParams格式\n    const formData = new URLSearchParams();\n    formData.append('username', credentials.username);\n    formData.append('password', credentials.password);\n\n    return api.post('/auth/login', formData, {\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n    });\n  },\n  \n  getUsers: (token: string) =>\n    api.get('/auth/users', {\n      headers: { Authorization: `Bearer ${token}` },\n    }),\n  \n  changePassword: (\n    data: { username: string; old_password: string; new_password: string; confirm_password: string },\n    token: string\n  ) =>\n    api.post('/auth/change_password', data, {\n      headers: { Authorization: `Bearer ${token}` },\n    }),\n  \n  addUser: (\n    data: { username: string; new_username: string; new_user_password: string; confirm_user_password: string },\n    token: string\n  ) =>\n    api.post('/auth/add_user', data, {\n      headers: { Authorization: `Bearer ${token}` },\n    }),\n};\n\n// 数据清洗相关API\nexport const dataCleaningAPI = {\n  listFiles: (folderPath: string) =>\n    api.get(`/data_cleaning/list_files?folder_path=${encodeURIComponent(folderPath)}`),\n  \n  cleanData: (formData: FormData) =>\n    api.post('/data_cleaning/clean_data', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    }),\n  \n  cleanDataLocal: (data: { folder_path: string; selected_files: string[]; output_dir: string }) => {\n    // 将数据转换为FormData格式，与后端期望的格式匹配\n    const formData = new FormData();\n    formData.append('folder_path', data.folder_path);\n    formData.append('output_dir', data.output_dir);\n\n    // 添加每个选中的文件\n    data.selected_files.forEach(file => {\n      formData.append('selected_files', file);\n    });\n\n    return api.post('/data_cleaning/clean_data', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    });\n  },\n\n  // 批量分析API\n  batchAnalyze: (data: {\n    tasks: Array<{\n      customer: string;\n      input_dir: string;\n      output_dir: string;\n    }>;\n  }) =>\n    api.post('/data_cleaning/batch_analyze', data),\n\n  // 批量任务状态查询\n  getBatchStatus: (batchId: string) =>\n    api.get(`/data_cleaning/batch_status/${batchId}`),\n};\n\n// 模型训练相关API\nexport const modelTrainingAPI = {\n  listCsvFiles: (csvDir: string) =>\n    api.get(`/model_training/list_csv_files?csv_dir=${encodeURIComponent(csvDir)}`),\n\n  trainModel: (formData: FormData) =>\n    api.post('/model_training/train', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n      timeout: 0, // 不限时，因为模型训练可能需要很长时间\n    }),\n\n  // 本地文件训练\n  trainModelLocal: (data: {\n    csv_dir: string;\n    selected_file: string;\n    selected_prots: string[];\n    selected_datatypes: {[key: string]: string[]};\n    learning_rate: number;\n    batch_size: number;\n    epochs: number;\n    sequence_length: number;\n    hidden_size: number;\n    num_layers: number;\n    dropout: number;\n    output_folder: string;\n  }) => {\n    const formData = new FormData();\n    formData.append('csv_dir', data.csv_dir);\n    formData.append('selected_file', data.selected_file);\n    formData.append('selected_prots', JSON.stringify(data.selected_prots));\n    formData.append('selected_datatypes', JSON.stringify(data.selected_datatypes));\n    formData.append('learning_rate', data.learning_rate.toString());\n    formData.append('batch_size', data.batch_size.toString());\n    formData.append('epochs', data.epochs.toString());\n    formData.append('sequence_length', data.sequence_length.toString());\n    formData.append('hidden_size', data.hidden_size.toString());\n    formData.append('num_layers', data.num_layers.toString());\n    formData.append('dropout', data.dropout.toString());\n    formData.append('output_folder', data.output_folder);\n\n    return api.post('/model_training/train', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n      timeout: 0, // 不限时，因为模型训练可能需要很长时间\n    });\n  },\n};\n\n// 模型预测相关API\nexport const modelPredictionAPI = {\n  listCsvFiles: (csvDir: string) =>\n    api.get(`/model_prediction/list_csv_files?csv_dir=${encodeURIComponent(csvDir)}`),\n\n  listModelFiles: (modelDir: string) =>\n    api.get(`/model_prediction/list_model_files?model_dir=${encodeURIComponent(modelDir)}`),\n\n  getMatchingFiles: (modelFilename: string, modelDir: string) =>\n    api.get(`/model_prediction/get_matching_files?model_filename=${encodeURIComponent(modelFilename)}&model_dir=${encodeURIComponent(modelDir)}`),\n\n  predict: (formData: FormData) =>\n    api.post('/model_prediction/predict', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    }),\n};\n\n// 模型仓库相关API\nexport const modelRegistryAPI = {\n  listModels: () => api.get('/model_registry/list'),\n\n  getModelDetail: (modelId: string) =>\n    api.get(`/model_registry/detail/${modelId}`),\n\n  deleteModel: (modelId: string) =>\n    api.delete(`/model_registry/delete/${modelId}`),\n\n  getStatistics: () => api.get('/model_registry/statistics'),\n};\n\n// 清洗模板相关API\nexport const cleanTemplateAPI = {\n  generateTemplate: (formData: FormData) =>\n    api.post('/clean_template/generate_template', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    }),\n\n  listTemplates: (templateDir: string) =>\n    api.get(`/clean_template/list_templates?folder_path=${encodeURIComponent(templateDir)}`),\n\n  getTemplateContent: (templatePath: string) =>\n    api.get(`/clean_template/get_template_content?template_path=${encodeURIComponent(templatePath)}`),\n\n  updateTemplate: (formData: FormData) =>\n    api.post('/clean_template/update_template', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    }),\n\n  sendTemplate: (formData: FormData) =>\n    api.post('/clean_template/send_template', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' },\n    }),\n\n  listResultFiles: (resultDir: string) =>\n    api.get(`/clean_template/list_result_files?folder_path=${encodeURIComponent(resultDir)}`),\n\n  downloadTemplate: (templatePath: string) =>\n    api.get(`/clean_template/download_template?template_path=${encodeURIComponent(templatePath)}`, {\n      responseType: 'blob',\n    }),\n};\n\n// 数据查询相关API\nexport const dataQueryAPI = {\n  listCsvFiles: (csvDir: string) =>\n    api.get(`/data_query/list_csv_files?csv_dir=${encodeURIComponent(csvDir)}`),\n  \n  downloadCsv: (csvDir: string, csvFile: string) =>\n    api.get(`/data_query/download_csv?csv_dir=${encodeURIComponent(csvDir)}&csv_file=${encodeURIComponent(csvFile)}`, {\n      responseType: 'blob',\n    }),\n  \n  listResultFiles: (resultDir: string) =>\n    api.get(`/data_query/list_result_files?result_dir=${encodeURIComponent(resultDir)}`),\n  \n  getResultContent: (resultDir: string, resultFile: string) =>\n    api.get(`/data_query/get_result_content?result_dir=${encodeURIComponent(resultDir)}&result_file=${encodeURIComponent(resultFile)}`),\n};\n\n\n\nexport default api;\n", "import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\nexport interface User {\n  username: string;\n  isDefault: boolean;\n}\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  user: User | null;\n  token: string | null;\n  loading: boolean;\n  error: string | null;\n  showChangePassword: boolean;\n  forceChangePassword: boolean;\n}\n\nconst initialState: AuthState = {\n  isAuthenticated: false,\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: false,\n  error: null,\n  showChangePassword: false,\n  forceChangePassword: false,\n};\n\n// 异步登录操作\nexport const loginAsync = createAsyncThunk(\n  'auth/login',\n  async (credentials: { username: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      const { access_token } = response.data;\n      \n      // 获取用户信息\n      const userResponse = await authAPI.getUsers(access_token);\n      const userData = userResponse.data[credentials.username];\n      \n      return {\n        token: access_token,\n        user: {\n          username: credentials.username,\n          isDefault: userData?.is_default || false,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '登录失败');\n    }\n  }\n);\n\n// 异步修改密码操作\nexport const changePasswordAsync = createAsyncThunk(\n  'auth/changePassword',\n  async (\n    data: { username: string; old_password: string; new_password: string; confirm_password: string },\n    { getState, rejectWithValue }\n  ) => {\n    try {\n      const state = getState() as { auth: AuthState };\n      const response = await authAPI.changePassword(data, state.auth.token!);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.detail || '修改密码失败');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    logout: (state) => {\n      state.isAuthenticated = false;\n      state.user = null;\n      state.token = null;\n      state.showChangePassword = false;\n      state.forceChangePassword = false;\n      localStorage.removeItem('token');\n      localStorage.removeItem('username');\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setShowChangePassword: (state, action: PayloadAction<boolean>) => {\n      state.showChangePassword = action.payload;\n    },\n    // 从localStorage恢复登录状态\n    restoreAuth: (state) => {\n      const token = localStorage.getItem('token');\n      const username = localStorage.getItem('username');\n      if (token && username) {\n        state.token = token;\n        state.isAuthenticated = true;\n        state.user = {\n          username: username,\n          isDefault: false, // 恢复时假设已经修改过密码\n        };\n        // 这里可以添加验证token有效性的逻辑\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // 登录\n      .addCase(loginAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(loginAsync.fulfilled, (state, action) => {\n        state.loading = false;\n        state.isAuthenticated = true;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.showChangePassword = action.payload.user.isDefault;\n        state.forceChangePassword = action.payload.user.isDefault;\n        localStorage.setItem('token', action.payload.token);\n        localStorage.setItem('username', action.payload.user.username);\n      })\n      .addCase(loginAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      })\n      // 修改密码\n      .addCase(changePasswordAsync.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(changePasswordAsync.fulfilled, (state) => {\n        state.loading = false;\n        state.showChangePassword = false;\n        state.forceChangePassword = false;\n        if (state.user) {\n          state.user.isDefault = false;\n        }\n      })\n      .addCase(changePasswordAsync.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.payload as string;\n      });\n  },\n});\n\nexport const { logout, clearError, setShowChangePassword, restoreAuth } = authSlice.actions;\nexport default authSlice.reducer;\n", "import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface UIState {\n  sidebarCollapsed: boolean;\n  theme: 'light' | 'dark';\n  loading: boolean;\n  notifications: Notification[];\n}\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: number;\n}\n\nconst initialState: UIState = {\n  sidebarCollapsed: false,\n  theme: 'light',\n  loading: false,\n  notifications: [],\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {\n      state.sidebarCollapsed = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading = action.payload;\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: Date.now(),\n      };\n      state.notifications.unshift(notification);\n      // 保持最多10个通知\n      if (state.notifications.length > 10) {\n        state.notifications = state.notifications.slice(0, 10);\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(\n        (notification) => notification.id !== action.payload\n      );\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarCollapsed,\n  setTheme,\n  setLoading,\n  addNotification,\n  removeNotification,\n  clearNotifications,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n", "import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport uiReducer from './slices/uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    ui: uiReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n", "import React, { useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Alert, Space } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { RootState } from '../store/store';\nimport { loginAsync, clearError } from '../store/slices/authSlice';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst LoginPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  \n  const { loading, error, isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/');\n    }\n  }, [isAuthenticated, navigate]);\n\n  useEffect(() => {\n    // 清除之前的错误信息\n    return () => {\n      dispatch(clearError());\n    };\n  }, [dispatch]);\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    try {\n      await dispatch(loginAsync(values)).unwrap();\n      // 登录成功后会通过useEffect自动跳转\n    } catch (error) {\n      // 错误已经在store中处理\n    }\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(to bottom, #e6f3ff, #ffffff)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: 400,\n          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',\n          borderRadius: '8px',\n        }}\n      >\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%', textAlign: 'center' }}>\n          <div>\n            <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>\n              AI智能清洗策略系统\n            </Title>\n            <Text type=\"secondary\">\n              基于深度学习的网络流量异常检测和清洗系统\n            </Text>\n          </div>\n\n          {error && (\n            <Alert\n              message=\"登录失败\"\n              description={error}\n              type=\"error\"\n              showIcon\n              closable\n              onClose={() => dispatch(clearError())}\n            />\n          )}\n\n          <Form\n            form={form}\n            name=\"login\"\n            onFinish={handleSubmit}\n            autoComplete=\"off\"\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[\n                { required: true, message: '请输入用户名!' },\n                { min: 2, message: '用户名至少2个字符!' },\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined />}\n                placeholder=\"请输入用户名\"\n                autoComplete=\"username\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[\n                { required: true, message: '请输入密码!' },\n                { min: 6, message: '密码至少6个字符!' },\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined />}\n                placeholder=\"请输入密码\"\n                autoComplete=\"current-password\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                {loading ? '登录中...' : '登录'}\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <div style={{ textAlign: 'center' }}>\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              © 2024 AI智能清洗策略系统. All rights reserved.\n            </Text>\n          </div>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default LoginPage;\n", "import React, { useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';\nimport './MainLayout.css';\nimport './MenuFix.css';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  ExperimentOutlined,\n  AimOutlined,\n  DatabaseOutlined,\n  FileTextOutlined,\n  SearchOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  SettingOutlined,\n  ClockCircleOutlined,\n} from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout, restoreAuth } from '../../store/slices/authSlice';\nimport { toggleSidebar } from '../../store/slices/uiSlice';\n// import TaskStatusIndicator from '../TaskStatusIndicator';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const { user } = useSelector((state: RootState) => state.auth);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n\n\n\n  useEffect(() => {\n    // 恢复登录状态\n    dispatch(restoreAuth());\n  }, [dispatch]);\n\n\n\n  const menuItems = [\n    {\n      key: '/data-cleaning',\n      icon: <BarChartOutlined />,\n      label: '流量分析',\n    },\n    {\n      key: '/model-training',\n      icon: <ExperimentOutlined />,\n      label: '模型训练',\n    },\n    {\n      key: '/model-prediction',\n      icon: <AimOutlined />,\n      label: '模型预测',\n    },\n    {\n      key: '/model-registry',\n      icon: <DatabaseOutlined />,\n      label: '模型仓库',\n    },\n    {\n      key: '/clean-template',\n      icon: <FileTextOutlined />,\n      label: '清洗模板',\n    },\n    {\n      key: '/data-query',\n      icon: <SearchOutlined />,\n      label: '数据查询',\n    },\n    {\n      key: '/task-manager',\n      icon: <ClockCircleOutlined />,\n      label: '任务管理',\n    },\n    {\n      key: '/user-management',\n      icon: <UserOutlined />,\n      label: '用户管理',\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    navigate(key);\n  };\n\n  const handleLogout = () => {\n    dispatch(logout());\n    navigate('/login');\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人信息',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={200}\n        style={{\n          background: '#fff',\n          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          left: 0,\n          top: 64,\n          bottom: 0,\n          zIndex: 999,\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n        }}\n      >\n\n\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          style={{\n            border: 'none',\n            flex: 1,\n          }}\n          className={sidebarCollapsed ? 'menu-collapsed' : 'menu-expanded'}\n        />\n\n        {/* 回缩按钮移到侧边栏底部 */}\n        <div style={{\n          padding: '16px',\n          borderTop: '1px solid #f0f0f0',\n          textAlign: 'center',\n        }}>\n          <Button\n            type=\"text\"\n            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n            onClick={() => dispatch(toggleSidebar())}\n            className=\"sidebar-toggle-btn\"\n            style={{\n              fontSize: '16px',\n              width: '100%',\n              height: 40,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n            title={sidebarCollapsed ? '展开菜单' : '收起菜单'}\n          />\n        </div>\n\n        {/* 强制应用选中项样式 */}\n        <style>{`\n          .ant-layout-sider .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            background-color: #1890ff !important;\n            color: #fff !important;\n          }\n          .menu-expanded .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding-left: 24px !important;\n          }\n          .menu-collapsed .ant-menu-item-selected {\n            margin: 4px 8px !important;\n            border-radius: 6px !important;\n            padding: 0 !important;\n            height: 40px;\n            width: calc(100% - 16px);\n          }\n        `}</style>\n\n      </Sider>\n\n      <Layout>\n        <Header style={{\n          padding: '0 24px 0 24px',\n          background: '#fff',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          position: 'fixed',\n          top: 0,\n          right: 0,\n          left: 0,\n          zIndex: 1001,\n          height: '64px',\n        }}>\n          <Title level={3} style={{\n            margin: 0,\n            color: '#000000',\n            fontSize: '20px',\n            fontWeight: 600,\n            letterSpacing: '0.5px',\n            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n          }}>\n            AI智能清洗策略系统\n          </Title>\n\n          <Space>\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>{user?.username}</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </Header>\n\n        <Content style={{\n          margin: `88px 16px 24px ${(sidebarCollapsed ? 80 : 200) + 16}px`, // 顶部留出Header的空间，左侧留出Sider的空间\n          padding: 24,\n          background: '#fff',\n          borderRadius: '8px',\n          minHeight: 'calc(100vh - 112px)',\n          overflow: 'hidden', // 确保子元素不会超出圆角边界\n          transition: 'margin-left 0.2s',\n        }}>\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Alert,\n  Progress,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Row,\n  Col,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { dataCleaningAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\nconst DataCleaningPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [processingMode, setProcessingMode] = useState<'single' | 'batch'>('single');\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [folderPath, setFolderPath] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);\n  const [outputDir, setOutputDir] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [filesLoading, setFilesLoading] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [result, setResult] = useState<any>(null);\n\n  // 批量任务相关状态\n  const [batchTasks, setBatchTasks] = useState<Array<{\n    id: string;\n    inputDir: string;\n    outputDir: string;\n    fileCount?: number;\n  }>>([]);\n  const [batchLoading, setBatchLoading] = useState(false);\n  const [batchProgress, setBatchProgress] = useState<{[key: string]: number}>({});\n\n  // 批量任务管理函数\n  const addBatchTask = () => {\n    const newTask = {\n      id: Date.now().toString(),\n      inputDir: '',\n      outputDir: '',\n      fileCount: 0\n    };\n    setBatchTasks([...batchTasks, newTask]);\n  };\n\n  const updateBatchTask = (id: string, field: string, value: string) => {\n    setBatchTasks(batchTasks.map(task =>\n      task.id === id ? { ...task, [field]: value } : task\n    ));\n  };\n\n  const removeBatchTask = (id: string) => {\n    setBatchTasks(batchTasks.filter(task => task.id !== id));\n  };\n\n  const validateBatchTask = async (task: any) => {\n    try {\n      const response = await dataCleaningAPI.listFiles(task.inputDir);\n      const files = response.data.files || [];\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      updateBatchTask(task.id, 'fileCount', txtFiles.length.toString());\n      return txtFiles.length > 0;\n    } catch (error) {\n      message.error(`验证目录 ${task.inputDir} 失败`);\n      return false;\n    }\n  };\n\n  const startBatchAnalysis = async () => {\n    // 验证所有任务\n    const validTasks = [];\n    for (let i = 0; i < batchTasks.length; i++) {\n      const task = batchTasks[i];\n      const taskName = `任务${i + 1}`;\n\n      if (!task.inputDir || !task.outputDir) {\n        message.error(`请完善 \"${taskName}\" 的配置`);\n        return;\n      }\n\n      const isValid = await validateBatchTask(task);\n      if (isValid) {\n        validTasks.push({...task, taskName});\n      }\n    }\n\n    if (validTasks.length === 0) {\n      message.error('没有有效的批量任务');\n      return;\n    }\n\n    setBatchLoading(true);\n\n    try {\n      // 调用批量分析API\n      const response = await dataCleaningAPI.batchAnalyze({\n        tasks: validTasks.map((task, index) => ({\n          customer: `任务${index + 1}`,\n          input_dir: task.inputDir,\n          output_dir: task.outputDir\n        }))\n      });\n\n      if (response.data.success) {\n        const batchId = response.data.batch_id;\n        message.success(`批量任务已启动，任务ID: ${batchId}`);\n\n        // 开始监控批量任务进度\n        monitorBatchProgress(batchId);\n      } else {\n        message.error('批量任务启动失败');\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '批量分析失败');\n    } finally {\n      setBatchLoading(false);\n    }\n  };\n\n  const monitorBatchProgress = async (batchId: string) => {\n    const maxAttempts = 60; // 10分钟监控\n    let attempt = 0;\n\n    const checkProgress = async () => {\n      try {\n        const response = await dataCleaningAPI.getBatchStatus(batchId);\n\n        console.log('批量任务状态响应:', response.data); // 添加调试日志\n\n        if (response.data && response.data.success) {\n          const { status, progress, current_step, error } = response.data;\n\n          console.log(`批量任务状态: ${status}, 进度: ${progress}%`); // 添加调试日志\n\n          // 更新进度状态\n          setBatchProgress(prev => ({\n            ...prev,\n            [batchId]: progress || 0\n          }));\n\n          if (status === 'completed') {\n            message.success('批量分析完成！');\n            setBatchLoading(false);\n            console.log('批量任务成功完成');\n            return;\n          } else if (status === 'failed') {\n            const errorMsg = error || '未知错误';\n            message.error(`批量分析失败: ${errorMsg}`);\n            setBatchLoading(false);\n            console.error('批量任务失败:', errorMsg);\n            return;\n          }\n\n          // 如果任务还在进行中，继续监控\n          if (attempt < maxAttempts && (status === 'running' || status === 'pending' || !status)) {\n            attempt++;\n            setTimeout(checkProgress, 5000); // 5秒后再次检查\n          } else if (attempt >= maxAttempts) {\n            // 超时时不显示失败，而是提示用户手动检查\n            message.warning('批量任务监控超时，任务可能仍在后台运行，请稍后手动检查结果');\n            setBatchLoading(false);\n            console.warn('批量任务监控超时');\n          }\n        } else {\n          // API响应格式问题，但不立即判定为失败\n          console.warn('批量任务状态API响应格式异常:', response.data);\n          if (attempt < maxAttempts) {\n            attempt++;\n            setTimeout(checkProgress, 5000);\n          } else {\n            message.warning('无法获取批量任务状态，请手动检查任务结果');\n            setBatchLoading(false);\n          }\n        }\n      } catch (error: any) {\n        console.error('监控批量任务进度失败:', error);\n\n        // 网络错误或API错误，增加重试次数\n        if (attempt < 10) { // 增加重试次数到10次\n          attempt++;\n          console.log(`批量任务监控重试 ${attempt}/10`);\n          setTimeout(checkProgress, 5000);\n        } else {\n          // 多次重试后仍失败，提示用户但不判定任务失败\n          message.warning('无法监控批量任务进度，任务可能仍在后台运行，请稍后手动检查结果');\n          setBatchLoading(false);\n          console.error('批量任务监控最终失败');\n        }\n      }\n    };\n\n    // 开始监控\n    checkProgress();\n  };\n\n  // 获取本地文件列表\n  const fetchLocalFiles = async () => {\n    if (!folderPath) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await dataCleaningAPI.listFiles(folderPath);\n      const files = response.data.files || [];\n      setAvailableFiles(files);\n\n      // 自动选择所有txt文件\n      const txtFiles = files.filter((file: string) =>\n        file.toLowerCase().endsWith('.txt')\n      );\n\n      if (txtFiles.length > 0) {\n        setSelectedFiles(txtFiles);\n        message.success(`已自动选择 ${txtFiles.length} 个TXT文件`);\n      } else {\n        setSelectedFiles([]);\n        if (files.length > 0) {\n          message.warning('目录中没有找到TXT文件');\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n      setSelectedFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && folderPath && folderPath.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchLocalFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, folderPath]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'files',\n    multiple: true,\n    accept: '.txt',\n    beforeUpload: () => false, // 阻止自动上传\n    onChange: (info: any) => {\n      setUploadedFiles(info.fileList);\n    },\n    onDrop: (e: any) => {\n      console.log('Dropped files', e.dataTransfer.files);\n    },\n  };\n\n  // 执行数据清洗\n  const handleCleanData = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && uploadedFiles.length === 0) {\n      message.error('请上传至少一个文件');\n      return;\n    }\n    \n    if (dataSource === 'local' && (!folderPath || selectedFiles.length === 0)) {\n      message.error('请提供文件夹路径并选择至少一个文件');\n      return;\n    }\n\n    setLoading(true);\n    setProgress(0);\n    setResult(null);\n\n    try {\n      let response;\n      \n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        uploadedFiles.forEach((file) => {\n          formData.append('files', file.originFileObj);\n        });\n        formData.append('output_dir', outputDir);\n        \n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanData(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件处理\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 10;\n          });\n        }, 500);\n\n        response = await dataCleaningAPI.cleanDataLocal({\n          folder_path: folderPath,\n          selected_files: selectedFiles,\n          output_dir: outputDir,\n        });\n        clearInterval(progressInterval);\n      }\n      \n      setProgress(100);\n      setResult(response.data);\n      message.success('数据清洗完成！');\n      \n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '数据清洗失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFiles.length > 0;\n    } else {\n      return folderPath && selectedFiles.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>流量数据分析</Title>\n      <Text type=\"secondary\">\n        上传流量数据文件或选择本地目录中的流量数据文件，分析后生成CSV文件并保存到指定目录。\n      </Text>\n\n      <Divider />\n\n      <Card title=\"数据分析\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 处理模式选择 */}\n          <div>\n            <Text strong>处理模式：</Text>\n            <Radio.Group\n              value={processingMode}\n              onChange={(e) => setProcessingMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个目录分析</Radio>\n              <Radio value=\"batch\">批量目录分析</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 单个目录模式 */}\n          {processingMode === 'single' && (\n            <>\n              {/* 数据源选择 */}\n          <div>\n            <Text strong>选择流量数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地目录文件</Radio>\n              <Radio value=\"upload\">上传流量数据TXT文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>本地目录路径：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={folderPath}\n                    onChange={(e) => setFolderPath(e.target.value)}\n                    placeholder=\"例如: /data/aizhinengqingxicepingdaliu\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchLocalFiles}\n                    loading={filesLoading}\n                    disabled={!folderPath}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    mode=\"multiple\"\n                    value={selectedFiles}\n                    onChange={setSelectedFiles}\n                    placeholder=\"请选择TXT文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持单个或批量上传TXT格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n\n          {/* 输出目录 */}\n          <div>\n            <Text strong>CSV输出目录：</Text>\n            <Input\n              value={outputDir}\n              onChange={(e) => setOutputDir(e.target.value)}\n              placeholder=\"例如: /data/output\"\n              style={{ marginTop: 8 }}\n            />\n          </div>\n\n          {/* 执行按钮 */}\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={<PlayCircleOutlined />}\n            onClick={handleCleanData}\n            loading={loading}\n            disabled={!isFormValid()}\n            className=\"action-button\"\n          >\n            {loading ? '正在处理...' : '执行流量分析'}\n          </Button>\n\n          {/* 进度条 */}\n          {loading && (\n            <div className=\"progress-section\">\n              <Text>处理进度：</Text>\n              <Progress percent={progress} status=\"active\" />\n            </div>\n          )}\n\n              {/* 结果展示 */}\n              {result && (\n                <Alert\n                  message=\"处理完成\"\n                  description={\n                    <div>\n                      <p>处理结果：{result.message}</p>\n                      {result.output_file && <p>输出文件：{result.output_file}</p>}\n                      {result.processed_files && <p>处理的文件数：{result.processed_files}</p>}\n                      {result.total_rows && <p>总行数：{result.total_rows}</p>}\n                    </div>\n                  }\n                  type=\"success\"\n                  showIcon\n                />\n              )}\n            </>\n          )}\n\n          {/* 批量目录模式 */}\n          {processingMode === 'batch' && (\n            <>\n              <div>\n                <Text strong>批量任务配置：</Text>\n                <div style={{ marginTop: 16 }}>\n                  {batchTasks.map((task, index) => (\n                    <Card\n                      key={task.id}\n                      size=\"small\"\n                      style={{ marginBottom: 16 }}\n                      title={`任务 ${index + 1}`}\n                      extra={\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          onClick={() => removeBatchTask(task.id)}\n                        />\n                      }\n                    >\n                      <Row gutter={16}>\n                        <Col span={12}>\n                          <Text strong>输入目录：</Text>\n                          <Input\n                            value={task.inputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'inputDir', e.target.value)}\n                            placeholder=\"例如：/data/input\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                        <Col span={12}>\n                          <Text strong>输出目录：</Text>\n                          <Input\n                            value={task.outputDir}\n                            onChange={(e) => updateBatchTask(task.id, 'outputDir', e.target.value)}\n                            placeholder=\"例如：/data/output\"\n                            style={{ marginTop: 4 }}\n                          />\n                        </Col>\n                      </Row>\n                      {task.fileCount !== undefined && (\n                        <div style={{ marginTop: 8 }}>\n                          <Text type=\"secondary\">\n                            检测到 {task.fileCount} 个TXT文件\n                          </Text>\n                        </div>\n                      )}\n                    </Card>\n                  ))}\n\n                  <Button\n                    type=\"dashed\"\n                    icon={<PlusOutlined />}\n                    onClick={addBatchTask}\n                    style={{ width: '100%', marginBottom: 16 }}\n                  >\n                    添加任务\n                  </Button>\n\n                  <Button\n                    type=\"primary\"\n                    size=\"large\"\n                    loading={batchLoading}\n                    disabled={batchTasks.length === 0}\n                    onClick={startBatchAnalysis}\n                    style={{ width: '100%' }}\n                  >\n                    {batchLoading ? '批量分析中...' : '开始批量分析'}\n                  </Button>\n\n                  {/* 批量任务进度显示 */}\n                  {batchLoading && Object.keys(batchProgress).length > 0 && (\n                    <div style={{ marginTop: 16 }}>\n                      <Text strong>批量任务进度：</Text>\n                      {Object.entries(batchProgress).map(([taskId, progress]) => (\n                        <div key={taskId} style={{ marginTop: 8 }}>\n                          <Progress\n                            percent={progress}\n                            status={progress === 100 ? 'success' : 'active'}\n                            format={(percent) => `${percent}%`}\n                          />\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </>\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DataCleaningPage;\n", "/**\n * 任务管理API服务\n * 提供异步任务的创建、查询、取消等功能\n */\n\nimport api from './api';\n\n// 任务状态常量\nexport const TASK_STATUS = {\n  PENDING: 'pending',\n  RUNNING: 'running',\n  COMPLETED: 'completed',\n  FAILED: 'failed',\n  CANCELLED: 'cancelled'\n} as const;\n\n// 任务类型常量\nexport const TASK_TYPE = {\n  TRAINING: 'training',\n  PREDICTION: 'prediction'\n} as const;\n\nexport type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];\nexport type TaskType = typeof TASK_TYPE[keyof typeof TASK_TYPE];\n\n// 任务接口定义\nexport interface Task {\n  task_id: string;\n  task_type: TaskType;\n  status: TaskStatus;\n  progress?: number;\n  created_at: string;\n  updated_at?: string;\n  started_at?: string;\n  completed_at?: string;\n  current_step?: string;\n  total_steps?: number;\n  message?: string;\n  error?: string;\n  params?: any;\n  result?: any;\n}\n\nexport interface TaskResponse {\n  success: boolean;\n  task_id?: string;\n  task?: Task;\n  tasks?: Task[];\n  total?: number;\n  count?: number;\n  message?: string;\n}\n\n/**\n * 获取所有任务列表\n */\nexport const getAllTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks');\n    return response.data;\n  } catch (error) {\n    console.error('获取任务列表失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 获取特定任务的状态\n * @param taskId - 任务ID\n */\nexport const getTaskStatus = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.get(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`获取任务 ${taskId} 状态失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 取消任务\n * @param taskId - 任务ID\n */\nexport const cancelTask = async (taskId: string): Promise<TaskResponse> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error(`取消任务 ${taskId} 失败:`, error);\n    throw error;\n  }\n};\n\n/**\n * 获取正在运行的任务\n */\nexport const getRunningTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/tasks/running');\n    return response.data;\n  } catch (error) {\n    console.error('获取运行中任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型训练\n * @param formData - 训练参数\n */\nexport const startTrainingAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_training/train_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步训练失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 异步启动模型预测\n * @param formData - 预测参数\n */\nexport const startPredictionAsync = async (formData: FormData): Promise<TaskResponse> => {\n  try {\n    const response = await api.post('/model_prediction/predict_async', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    console.error('启动异步预测失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 轮询任务状态直到完成\n * @param taskId - 任务ID\n * @param onProgress - 进度回调函数\n * @param interval - 轮询间隔（毫秒）\n * @returns 任务完成时的结果\n */\nexport const pollTaskStatus = async (\n  taskId: string, \n  onProgress?: (task: Task) => void, \n  interval: number = 2000\n): Promise<Task> => {\n  return new Promise((resolve, reject) => {\n    const poll = async () => {\n      try {\n        const response = await getTaskStatus(taskId);\n        const task = response.task;\n        \n        if (!task) {\n          reject(new Error('任务不存在'));\n          return;\n        }\n        \n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n        \n        // 检查任务状态\n        if (task.status === TASK_STATUS.COMPLETED) {\n          resolve(task);\n        } else if (task.status === TASK_STATUS.FAILED) {\n          reject(new Error(task.error || '任务执行失败'));\n        } else if (task.status === TASK_STATUS.CANCELLED) {\n          reject(new Error('任务已被取消'));\n        } else {\n          // 继续轮询\n          setTimeout(poll, interval);\n        }\n      } catch (error) {\n        reject(error);\n      }\n    };\n    \n    poll();\n  });\n};\n\n/**\n * 获取已完成的任务\n */\nexport const getCompletedTasks = async (): Promise<TaskResponse> => {\n  try {\n    const response = await api.get('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('获取已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 删除单个已完成的任务\n */\nexport const deleteSingleTask = async (taskId: string): Promise<{ success: boolean; message: string; deleted_task_id: string }> => {\n  try {\n    const response = await api.delete(`/tasks/task/${taskId}`);\n    return response.data;\n  } catch (error) {\n    console.error('删除任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 清空所有已完成的任务\n */\nexport const clearCompletedTasks = async (): Promise<{ success: boolean; message: string; cleared_count: number; cleared_task_ids: string[] }> => {\n  try {\n    const response = await api.delete('/tasks/completed');\n    return response.data;\n  } catch (error) {\n    console.error('清空已完成任务失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 格式化任务状态显示文本\n * @param status - 任务状态\n */\nexport const formatTaskStatus = (status: TaskStatus): string => {\n  const statusMap = {\n    [TASK_STATUS.PENDING]: '等待中',\n    [TASK_STATUS.RUNNING]: '运行中',\n    [TASK_STATUS.COMPLETED]: '已完成',\n    [TASK_STATUS.FAILED]: '失败',\n    [TASK_STATUS.CANCELLED]: '已取消'\n  };\n  return statusMap[status] || status;\n};\n\n/**\n * 格式化任务类型显示文本\n * @param type - 任务类型\n */\nexport const formatTaskType = (type: TaskType): string => {\n  const typeMap = {\n    [TASK_TYPE.TRAINING]: '模型训练',\n    [TASK_TYPE.PREDICTION]: '模型预测'\n  };\n  return typeMap[type] || type;\n};\n\n/**\n * 获取任务状态对应的颜色\n * @param status - 任务状态\n */\nexport const getTaskStatusColor = (status: TaskStatus): string => {\n  const colorMap = {\n    [TASK_STATUS.PENDING]: 'default',\n    [TASK_STATUS.RUNNING]: 'processing',\n    [TASK_STATUS.COMPLETED]: 'success',\n    [TASK_STATUS.FAILED]: 'error',\n    [TASK_STATUS.CANCELLED]: 'warning'\n  };\n  return colorMap[status] || 'default';\n};\n\n/**\n * 计算任务运行时间\n * @param startTime - 开始时间\n * @param endTime - 结束时间（可选）\n */\nexport const calculateTaskDuration = (startTime?: string, endTime?: string): string => {\n  if (!startTime) return '未知';\n  \n  const start = new Date(startTime);\n  const end = endTime ? new Date(endTime) : new Date();\n  const duration = Math.floor((end.getTime() - start.getTime()) / 1000); // 秒\n  \n  if (duration < 60) {\n    return `${duration}秒`;\n  } else if (duration < 3600) {\n    return `${Math.floor(duration / 60)}分${duration % 60}秒`;\n  } else {\n    const hours = Math.floor(duration / 3600);\n    const minutes = Math.floor((duration % 3600) / 60);\n    return `${hours}小时${minutes}分`;\n  }\n};\n\nconst taskApi = {\n  getAllTasks,\n  getTaskStatus,\n  cancelTask,\n  getRunningTasks,\n  getCompletedTasks,\n  deleteSingleTask,\n  clearCompletedTasks,\n  startTrainingAsync,\n  startPredictionAsync,\n  pollTaskStatus,\n  formatTaskStatus,\n  formatTaskType,\n  getTaskStatusColor,\n  calculateTaskDuration,\n  TASK_STATUS,\n  TASK_TYPE\n};\n\nexport default taskApi;\n", "/**\n * 任务管理Hook\n * 提供任务状态管理、轮询、通知等功能\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { message, notification } from 'antd';\nimport taskApi, {\n  TASK_STATUS,\n  TASK_TYPE,\n  Task\n} from '../services/taskApi';\n\nexport const useTaskManager = () => {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [runningTasks, setRunningTasks] = useState<Task[]>([]);\n  const [completedTasks, setCompletedTasks] = useState<Task[]>([]);\n  const [initialized, setInitialized] = useState(false);\n  const pollingIntervals = useRef(new Map<string, NodeJS.Timeout>()); // 存储轮询定时器\n\n  /**\n   * 获取所有任务\n   */\n  const fetchAllTasks = useCallback(async (showError = true) => {\n    try {\n      setLoading(true);\n      const response = await taskApi.getAllTasks();\n      if (response.success) {\n        setTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取任务列表失败:', error);\n      if (showError) {\n        message.error('获取任务列表失败');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  /**\n   * 获取正在运行的任务\n   */\n  const fetchRunningTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getRunningTasks();\n      if (response.success) {\n        setRunningTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取运行中任务失败:', error);\n      if (showError) {\n        message.error('获取运行中任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 获取已完成的任务\n   */\n  const fetchCompletedTasks = useCallback(async (showError = false) => {\n    try {\n      const response = await taskApi.getCompletedTasks();\n      if (response.success) {\n        setCompletedTasks(response.tasks || []);\n      }\n    } catch (error) {\n      console.error('获取已完成任务失败:', error);\n      if (showError) {\n        message.error('获取已完成任务失败');\n      }\n    }\n  }, []);\n\n  /**\n   * 删除单个已完成的任务\n   */\n  const deleteSingleTask = useCallback(async (taskId: string): Promise<boolean> => {\n    try {\n      const response = await taskApi.deleteSingleTask(taskId);\n      if (response.success) {\n        // 从已完成任务列表中移除\n        setCompletedTasks(prev => prev.filter(task => task.task_id !== taskId));\n        message.success(`已删除任务: ${taskId.substring(0, 8)}...`);\n        return true;\n      }\n      return false;\n    } catch (error: any) {\n      console.error('删除任务失败:', error);\n      const errorMessage = error.response?.data?.detail || '删除任务失败';\n      message.error(errorMessage);\n      return false;\n    }\n  }, []);\n\n  /**\n   * 清空所有已完成的任务\n   */\n  const clearCompletedTasks = useCallback(async () => {\n    try {\n      const response = await taskApi.clearCompletedTasks();\n      if (response.success) {\n        setCompletedTasks([]);\n        message.success(`已清空 ${response.cleared_count} 个完成的任务`);\n      }\n    } catch (error) {\n      console.error('清空已完成任务失败:', error);\n      message.error('清空已完成任务失败');\n    }\n  }, []);\n\n  /**\n   * 启动任务轮询\n   */\n  const startPolling = useCallback((taskId: string, onProgress?: (task: Task) => void) => {\n    // 如果已经在轮询，先清除\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const response = await taskApi.getTaskStatus(taskId);\n        const task = response.task;\n\n        if (!task) return;\n\n        // 更新任务列表中的对应任务\n        setTasks(prevTasks => {\n          const index = prevTasks.findIndex(t => t.task_id === taskId);\n          if (index >= 0) {\n            const newTasks = [...prevTasks];\n            newTasks[index] = task;\n            return newTasks;\n          } else {\n            return [...prevTasks, task];\n          }\n        });\n\n        // 调用进度回调\n        if (onProgress) {\n          onProgress(task);\n        }\n\n        // 如果任务完成，停止轮询并发送通知\n        if ([TASK_STATUS.COMPLETED, TASK_STATUS.FAILED, TASK_STATUS.CANCELLED].includes(task.status as any)) {\n          clearInterval(pollingIntervals.current.get(taskId)!);\n          pollingIntervals.current.delete(taskId);\n\n          // 发送通知\n          if (task.status === TASK_STATUS.COMPLETED) {\n            notification.success({\n              message: '🎉 任务完成',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已成功完成！点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细结果`,\n              duration: 10,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.FAILED) {\n            notification.error({\n              message: '❌ 任务失败',\n              description: `${taskApi.formatTaskType(task.task_type)}任务执行失败: ${task.error || '未知错误'}。点击此通知或前往\"侧边栏菜单 → 任务管理\"查看详细错误信息`,\n              duration: 15,\n              onClick: () => {\n                // 跳转到任务管理页面\n                window.location.hash = '#/task-manager';\n              },\n              style: { cursor: 'pointer' }\n            });\n          } else if (task.status === TASK_STATUS.CANCELLED) {\n            notification.warning({\n              message: '⚠️ 任务已取消',\n              description: `${taskApi.formatTaskType(task.task_type)}任务已被取消`,\n              duration: 5,\n            });\n          }\n\n          // 更新运行中任务列表和已完成任务列表\n          fetchRunningTasks();\n          fetchCompletedTasks();\n        }\n      } catch (error) {\n        console.error(`轮询任务 ${taskId} 状态失败:`, error);\n        clearInterval(pollingIntervals.current.get(taskId)!);\n        pollingIntervals.current.delete(taskId);\n      }\n    }, 2000);\n\n    pollingIntervals.current.set(taskId, interval);\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  /**\n   * 停止任务轮询\n   */\n  const stopPolling = useCallback((taskId: string) => {\n    if (pollingIntervals.current.has(taskId)) {\n      clearInterval(pollingIntervals.current.get(taskId)!);\n      pollingIntervals.current.delete(taskId);\n    }\n  }, []);\n\n  /**\n   * 初始化任务管理器（延迟初始化）\n   */\n  const initializeTaskManager = useCallback(async () => {\n    if (initialized) return;\n\n    try {\n      // 尝试获取运行中任务\n      await fetchRunningTasks(false);\n\n      // 为已存在的运行中任务启动轮询\n      const response = await taskApi.getRunningTasks();\n      if (response.success && response.tasks) {\n        response.tasks.forEach(task => {\n          startPolling(task.task_id);\n        });\n      }\n\n      setInitialized(true);\n    } catch (error) {\n      console.warn('初始化任务管理失败:', error);\n      // 静默失败，不影响用户体验\n    }\n  }, [initialized, fetchRunningTasks, startPolling]);\n\n  /**\n   * 提交训练任务\n   */\n  const submitTrainingTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startTrainingAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 训练任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动训练任务失败:', error);\n      message.error('启动训练任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 提交预测任务\n   */\n  const submitPredictionTask = useCallback(async (formData: FormData): Promise<string | undefined> => {\n    try {\n      // 确保任务管理器已初始化\n      await initializeTaskManager();\n\n      const response = await taskApi.startPredictionAsync(formData);\n      if (response.success) {\n        const taskId = response.task_id;\n        if (taskId) {\n          message.success('🚀 预测任务已启动！请前往\"侧边栏菜单 → 任务管理\"查看进度和结果', 6);\n\n          // 开始轮询任务状态\n          startPolling(taskId);\n\n          // 更新运行中任务列表\n          fetchRunningTasks(false);\n\n          return taskId;\n        }\n      }\n    } catch (error: any) {\n      console.error('启动预测任务失败:', error);\n      message.error('启动预测任务失败: ' + (error.response?.data?.detail || error.message));\n      throw error;\n    }\n  }, [initializeTaskManager, startPolling, fetchRunningTasks]);\n\n  /**\n   * 取消任务\n   */\n  const cancelTask = useCallback(async (taskId: string) => {\n    try {\n      const response = await taskApi.cancelTask(taskId);\n      if (response.success) {\n        message.success('任务已取消');\n        \n        // 停止轮询\n        stopPolling(taskId);\n        \n        // 刷新任务列表\n        fetchAllTasks();\n        fetchRunningTasks();\n      }\n    } catch (error: any) {\n      console.error('取消任务失败:', error);\n      message.error('取消任务失败: ' + (error.response?.data?.detail || error.message));\n    }\n  }, [stopPolling, fetchAllTasks, fetchRunningTasks]);\n\n  /**\n   * 获取任务详情\n   */\n  const getTaskDetail = useCallback(async (taskId: string): Promise<Task | null> => {\n    try {\n      const response = await taskApi.getTaskStatus(taskId);\n      return response.task || null;\n    } catch (error) {\n      console.error('获取任务详情失败:', error);\n      message.error('获取任务详情失败');\n      return null;\n    }\n  }, []);\n\n  // 组件挂载时初始化，获取已完成任务\n  useEffect(() => {\n    // 初始化时获取已完成任务\n    fetchCompletedTasks();\n\n    // 清理函数：清除所有轮询\n    return () => {\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const intervals = pollingIntervals.current;\n      intervals.forEach(interval => clearInterval(interval));\n      intervals.clear();\n    };\n  }, [fetchCompletedTasks]);\n\n  return {\n    // 状态\n    tasks,\n    runningTasks,\n    completedTasks,\n    loading,\n\n    // 方法\n    fetchAllTasks,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    deleteSingleTask,\n    clearCompletedTasks,\n    submitTrainingTask,\n    submitPredictionTask,\n    cancelTask,\n    getTaskDetail,\n    startPolling,\n    stopPolling,\n\n    // 结果获取方法\n    getTaskResult: useCallback((taskId: string) => {\n      const task = completedTasks.find(t => t.task_id === taskId);\n      return task?.result || null;\n    }, [completedTasks]),\n\n    getCompletedTasksByType: useCallback((taskType: 'training' | 'prediction') => {\n      return completedTasks.filter(task =>\n        task.task_type === taskType &&\n        task.status === TASK_STATUS.COMPLETED &&\n        task.result\n      );\n    }, [completedTasks]),\n\n    // 工具方法\n    formatTaskStatus: taskApi.formatTaskStatus,\n    formatTaskType: taskApi.formatTaskType,\n    getTaskStatusColor: taskApi.getTaskStatusColor,\n    calculateTaskDuration: taskApi.calculateTaskDuration,\n\n    // 常量\n    TASK_STATUS,\n    TASK_TYPE\n  };\n};\n\nexport default useTaskManager;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  InputNumber,\n  Slider,\n  Checkbox,\n  Progress,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined, SettingOutlined, ExperimentOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { modelTrainingAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 训练结果展示组件\nconst TrainingResultDisplay: React.FC<{ resultKey: string; result: any }> = ({ resultKey, result }) => {\n  const [selectedProt, selectedDatatype] = resultKey.split('_', 2);\n\n  return (\n    <div>\n      <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n        <div>\n          <Text><strong>协议:</strong> {selectedProt}</Text>\n          <br />\n          <Text><strong>数据类型:</strong> {selectedDatatype}</Text>\n        </div>\n\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"训练集数据形状\"\n              value={result.train_shape ? `${result.train_shape[0]} × ${result.train_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"测试集数据形状\"\n              value={result.test_shape ? `${result.test_shape[0]} × ${result.test_shape[1]}` : 'N/A'}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"R² 分数\"\n              value={result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')}\n              precision={4}\n              valueStyle={{ color: result.r2_score > 0.8 || result.r2 > 0.8 ? '#3f8600' : '#cf1322' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"建议清洗阈值\"\n              value={result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              precision={2}\n            />\n          </Col>\n        </Row>\n\n        <Row gutter={16} style={{ marginTop: 16 }}>\n          <Col span={6}>\n            <Statistic\n              title=\"CPU使用率\"\n              value={result.cpu_percent ? `${result.cpu_percent.toFixed(2)}%` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"内存使用\"\n              value={result.memory_mb ? `${result.memory_mb.toFixed(2)} MB` : 'N/A'}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU内存\"\n              value={result.gpu_memory_mb ? `${result.gpu_memory_mb.toFixed(2)} MB` : (result.gpu_memory ? `${result.gpu_memory.toFixed(2)} MB` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"GPU使用率\"\n              value={result.gpu_utilization_percent ? `${result.gpu_utilization_percent.toFixed(2)}%` : (result.gpu_utilization ? `${result.gpu_utilization.toFixed(2)}%` : 'N/A')}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n        </Row>\n\n        {result.train_losses && result.val_losses && (\n          <div>\n            <Text strong>训练损失曲线</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.train_losses.map((trainLoss: number, index: number) => ({\n                    epoch: index + 1,\n                    训练损失: trainLoss,\n                    验证损失: result.val_losses[index] || null,\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"epoch\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"训练损失\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"验证损失\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                训练轮数: {result.train_losses.length} epochs |\n                最终训练损失: {result.train_losses[result.train_losses.length - 1]?.toFixed(6)} |\n                最终验证损失: {result.val_losses[result.val_losses.length - 1]?.toFixed(6)}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {result.y_test_actual && result.y_pred && result.y_test_actual.length > 0 && (\n          <div>\n            <Text strong>实际值 vs 预测值对比图</Text>\n            <div style={{ height: 300, marginTop: 8 }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={result.y_test_actual.map((actual: number, index: number) => ({\n                    index: index + 1,\n                    实际值: actual,\n                    预测值: result.y_pred[index],\n                  }))}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"index\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"实际值\"\n                    stroke=\"#8884d8\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"预测值\"\n                    stroke=\"#82ca9d\"\n                    strokeWidth={2}\n                    dot={false}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                显示所有 {result.y_test_actual.length} 个测试样本的预测对比 |\n                R² 分数: {result.r2_score ? result.r2_score.toFixed(4) : (result.r2 ? result.r2.toFixed(4) : 'N/A')} |\n                建议阈值: {result.static_anomaly_threshold ? result.static_anomaly_threshold.toFixed(2) : 'N/A'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n\n\n        {result.model_save_path && (\n          <Alert\n            message=\"模型文件信息\"\n            description={\n              <div>\n                <p><strong>模型保存路径:</strong> {result.model_save_path}</p>\n                {result.scaler_y_save_path && (\n                  <p><strong>标准化器保存路径:</strong> {result.scaler_y_save_path}</p>\n                )}\n                {result.params_save_path && (\n                  <p><strong>参数保存路径:</strong> {result.params_save_path}</p>\n                )}\n                {result.test_save_path && (\n                  <p><strong>测试数据保存路径:</strong> {result.test_save_path}</p>\n                )}\n                {result.static_anomaly_threshold && (\n                  <p><strong>建议清洗阈值:</strong> {result.static_anomaly_threshold.toFixed(2)}</p>\n                )}\n                {result.finished_time && (\n                  <p><strong>训练完成时间:</strong> {result.finished_time}</p>\n                )}\n                {result.duration_seconds && (\n                  <p><strong>训练耗时:</strong> {result.duration_seconds.toFixed(2)} 秒</p>\n                )}\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        )}\n      </Space>\n    </div>\n  );\n};\n\nconst ModelTrainingPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableFiles, setAvailableFiles] = useState<string[]>([]);\n  const [selectedFile, setSelectedFile] = useState<string>('');\n  const [filesLoading, setFilesLoading] = useState(false);\n\n  // 协议和数据类型选择（与Streamlit版本一致）\n  const [selectedProts, setSelectedProts] = useState<string[]>(['TCP']);\n  const [selectedDatatypes, setSelectedDatatypes] = useState<{[key: string]: string[]}>({\n    TCP: ['spt_sip_dip']\n  });\n\n  // 训练参数\n  const [learningRate, setLearningRate] = useState(0.0001);\n  const [batchSize, setBatchSize] = useState(64);\n  const [epochs, setEpochs] = useState(100);\n  const [sequenceLength, setSequenceLength] = useState(10);\n  const [hiddenSize, setHiddenSize] = useState(50);\n  const [numLayers, setNumLayers] = useState(2);\n  const [dropout, setDropout] = useState(0.2);\n  const [outputFolder, setOutputFolder] = useState('');\n\n  // 训练状态\n  const [training, setTraining] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [trainingResults, setTrainingResults] = useState<any>(null);\n  const [selectedResultKey, setSelectedResultKey] = useState<string>('');\n\n  // 任务管理\n  const { submitTrainingTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncTraining, setUseAsyncTraining] = useState(true); // 默认使用异步训练\n\n  // 异步任务结果状态\n  const [asyncTrainingResults, setAsyncTrainingResults] = useState<any>(null);\n  const [selectedAsyncResultKey, setSelectedAsyncResultKey] = useState<string>('');\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的训练任务\n  const completedTrainingTasks = getCompletedTasksByType('training');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedTrainingTasks.find(task => task.task_id === taskId);\n    if (selectedTask && selectedTask.result && selectedTask.result.results) {\n      setAsyncTrainingResults(selectedTask.result);\n      setSelectedAsyncResultKey(Object.keys(selectedTask.result.results)[0]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的训练任务\n  useEffect(() => {\n    if (completedTrainingTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedTrainingTasks[completedTrainingTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedTrainingTasks, selectedAsyncTaskId]);\n\n  // 协议和数据类型配置（与Streamlit版本完全一致）\n  const protocolOptions = ['TCP', 'UDP', 'ICMP'];\n  const datatypeOptions = {\n    TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n    UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n    ICMP: ['dip']\n  };\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setFilesLoading(true);\n    try {\n      const response = await modelTrainingAPI.listCsvFiles(csvDir);\n      setAvailableFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取文件列表失败');\n      setAvailableFiles([]);\n    } finally {\n      setFilesLoading(false);\n    }\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 协议选择变化处理（与Streamlit版本一致）\n  const handleProtocolChange = (prots: string[]) => {\n    setSelectedProts(prots);\n    // 为新选择的协议添加默认数据类型\n    const newDatatypes = { ...selectedDatatypes };\n    prots.forEach(prot => {\n      if (!newDatatypes[prot] && datatypeOptions[prot as keyof typeof datatypeOptions]) {\n        newDatatypes[prot] = [datatypeOptions[prot as keyof typeof datatypeOptions][0]];\n      }\n    });\n    // 移除未选择协议的数据类型\n    Object.keys(newDatatypes).forEach(prot => {\n      if (!prots.includes(prot)) {\n        delete newDatatypes[prot];\n      }\n    });\n    setSelectedDatatypes(newDatatypes);\n  };\n\n  // 数据类型选择变化处理\n  const handleDatatypeChange = (protocol: string, datatypes: string[]) => {\n    setSelectedDatatypes(prev => ({\n      ...prev,\n      [protocol]: datatypes\n    }));\n  };\n\n  // 开始训练\n  const handleStartTraining = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    if (selectedProts.length === 0) {\n      message.error('请至少选择一种协议');\n      return;\n    }\n\n    const hasValidDatatypes = selectedProts.some(prot =>\n      selectedDatatypes[prot] && selectedDatatypes[prot].length > 0\n    );\n\n    if (!hasValidDatatypes) {\n      message.error('请为每个协议至少选择一种数据类型');\n      return;\n    }\n\n    setTraining(true);\n    setProgress(0);\n    setTrainingResults(null);\n    setSelectedResultKey('');\n\n    try {\n      if (useAsyncTraining) {\n        // 异步训练模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload') {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedFile);\n        }\n\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 提交异步任务\n        const taskId = await submitTrainingTask(formData);\n\n        if (taskId) {\n          message.success('训练任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setTraining(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步训练模式（保留原有逻辑）\n      let response;\n\n      if (dataSource === 'upload') {\n        const formData = new FormData();\n        formData.append('file', uploadedFile.originFileObj);\n        formData.append('selected_prots', JSON.stringify(selectedProts));\n        formData.append('selected_datatypes', JSON.stringify(selectedDatatypes));\n        formData.append('learning_rate', learningRate.toString());\n        formData.append('batch_size', batchSize.toString());\n        formData.append('epochs', epochs.toString());\n        formData.append('sequence_length', sequenceLength.toString());\n        formData.append('hidden_size', hiddenSize.toString());\n        formData.append('num_layers', numLayers.toString());\n        formData.append('dropout', dropout.toString());\n        formData.append('output_folder', outputFolder);\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModel(formData);\n        clearInterval(progressInterval);\n      } else {\n        // 本地文件训练逻辑\n        const localTrainingData = {\n          csv_dir: csvDir,\n          selected_file: selectedFile,\n          selected_prots: selectedProts,\n          selected_datatypes: selectedDatatypes,\n          learning_rate: learningRate,\n          batch_size: batchSize,\n          epochs: epochs,\n          sequence_length: sequenceLength,\n          hidden_size: hiddenSize,\n          num_layers: numLayers,\n          dropout: dropout,\n          output_folder: outputFolder\n        };\n\n        // 模拟进度更新\n        const progressInterval = setInterval(() => {\n          setProgress((prev) => {\n            if (prev >= 90) {\n              clearInterval(progressInterval);\n              return prev;\n            }\n            return prev + 5;\n          });\n        }, 1000);\n\n        response = await modelTrainingAPI.trainModelLocal(localTrainingData);\n        clearInterval(progressInterval);\n      }\n\n      setProgress(100);\n\n      // 添加调试信息\n      console.log('训练响应数据:', response.data);\n\n      setTrainingResults(response.data);\n\n      // 设置默认选择第一个结果\n      if (response.data.results && Object.keys(response.data.results).length > 0) {\n        setSelectedResultKey(Object.keys(response.data.results)[0]);\n      }\n\n      message.success('模型训练完成！');\n\n      // 显示训练结果路径信息\n      if (response.data.result_path) {\n        message.info(`结果已保存至: ${response.data.result_path}`);\n      }\n\n    } catch (error: any) {\n      console.error('训练错误详情:', error);\n      console.error('错误响应:', error.response);\n\n      // 更详细的错误信息\n      let errorMessage = '模型训练失败';\n      if (error.response) {\n        if (error.response.data?.detail) {\n          errorMessage = error.response.data.detail;\n        } else if (error.response.data?.message) {\n          errorMessage = error.response.data.message;\n        } else if (error.response.statusText) {\n          errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`;\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setTraining(false);\n    }\n  };\n\n  const isFormValid = () => {\n    if (dataSource === 'upload') {\n      return uploadedFile && selectedProts.length > 0;\n    } else {\n      return csvDir && selectedFile && selectedProts.length > 0;\n    }\n  };\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型训练与特征预测</Title>\n      <Text type=\"secondary\">\n        上传或选择CSV文件，配置训练参数，根据多维特征训练流量检测模型，并进行特征预测。\n      </Text>\n\n      <Divider />\n\n      {/* 数据源选择 */}\n      <Card title=\"数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>训练数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /home/<USER>\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={filesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择文件：</Text>\n                <Spin spinning={filesLoading}>\n                  <Select\n                    value={selectedFile}\n                    onChange={setSelectedFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={filesLoading}\n                  >\n                    {availableFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 协议和数据类型选择 */}\n      <Card title=\"协议和数据类型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择协议：</Text>\n            <Select\n              mode=\"multiple\"\n              value={selectedProts}\n              onChange={handleProtocolChange}\n              placeholder=\"请选择协议\"\n              style={{ width: '100%', marginTop: 8 }}\n            >\n              {protocolOptions.map((prot) => (\n                <Option key={prot} value={prot}>\n                  {prot}\n                </Option>\n              ))}\n            </Select>\n          </div>\n\n          {selectedProts.map((prot) => (\n            <div key={prot}>\n              <Text strong>{prot} 数据类型：</Text>\n              <Checkbox.Group\n                value={selectedDatatypes[prot] || []}\n                onChange={(datatypes) => handleDatatypeChange(prot, datatypes as string[])}\n                style={{ marginTop: 8 }}\n              >\n                {(datatypeOptions[prot as keyof typeof datatypeOptions] || []).map((datatype) => (\n                  <Checkbox key={datatype} value={datatype}>\n                    {datatype}\n                  </Checkbox>\n                ))}\n              </Checkbox.Group>\n            </div>\n          ))}\n        </Space>\n      </Card>\n\n      {/* 训练参数配置 */}\n      <Card\n        title={\n          <Space>\n            <SettingOutlined />\n            <span>训练参数配置</span>\n          </Space>\n        }\n        className=\"function-card\"\n      >\n        <Row gutter={[24, 24]}>\n          {/* 基础参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <ExperimentOutlined />\n                  <Text strong>基础参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>学习率：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={learningRate}\n                      onChange={(value) => setLearningRate(value || 0.0001)}\n                      min={0.0001}\n                      max={1}\n                      step={0.0001}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.0001\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>批量大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={batchSize}\n                      onChange={(value) => setBatchSize(value || 64)}\n                      min={1}\n                      max={512}\n                      style={{ width: '100%' }}\n                      placeholder=\"64\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>训练轮数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={epochs}\n                      onChange={(value) => setEpochs(value || 100)}\n                      min={1}\n                      max={1000}\n                      style={{ width: '100%' }}\n                      placeholder=\"100\"\n                    />\n                  </Col>\n                </Row>\n              </Space>\n            </Card>\n          </Col>\n\n          {/* 模型参数 */}\n          <Col span={12}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <SettingOutlined />\n                  <Text strong>模型参数</Text>\n                </Space>\n              }\n            >\n              <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>序列长度：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={sequenceLength}\n                      onChange={(value) => setSequenceLength(value || 10)}\n                      min={1}\n                      max={100}\n                      style={{ width: '100%' }}\n                      placeholder=\"10\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>隐藏层大小：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={hiddenSize}\n                      onChange={(value) => setHiddenSize(value || 50)}\n                      min={10}\n                      max={512}\n                      step={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"50\"\n                    />\n                  </Col>\n                </Row>\n                <Row align=\"middle\">\n                  <Col span={10}>\n                    <Text strong>层数：</Text>\n                  </Col>\n                  <Col span={14}>\n                    <InputNumber\n                      value={numLayers}\n                      onChange={(value) => setNumLayers(value || 2)}\n                      min={1}\n                      max={10}\n                      style={{ width: '100%' }}\n                      placeholder=\"2\"\n                    />\n                  </Col>\n                </Row>\n                <div style={{ width: '100%' }}>\n                  <Row align=\"middle\" style={{ marginBottom: 8 }}>\n                    <Col span={12}>\n                      <Text strong>Dropout 概率：</Text>\n                    </Col>\n                    <Col span={12} style={{ textAlign: 'right' }}>\n                      <Text code>{dropout}</Text>\n                    </Col>\n                  </Row>\n                  <Slider\n                    value={dropout}\n                    onChange={setDropout}\n                    min={0}\n                    max={0.9}\n                    step={0.05}\n                    marks={{\n                      0: '0',\n                      0.2: '0.2',\n                      0.5: '0.5',\n                      0.9: '0.9'\n                    }}\n                  />\n                </div>\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n\n        {/* 模型保存路径 */}\n        <Row style={{ marginTop: 24 }}>\n          <Col span={24}>\n            <Card\n              size=\"small\"\n              title={\n                <Space>\n                  <InboxOutlined />\n                  <Text strong>模型保存路径</Text>\n                </Space>\n              }\n            >\n              <Input\n                value={outputFolder}\n                onChange={(e) => setOutputFolder(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                size=\"large\"\n                prefix={<InboxOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 训练模式选择 */}\n      <Card className=\"function-card\" title=\"训练模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择训练模式：</Text>\n            <Radio.Group\n              value={useAsyncTraining}\n              onChange={(e) => setUseAsyncTraining(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value={true}>\n                <Space>\n                  异步训练（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步训练\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待训练完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncTraining && (\n            <Alert\n              message=\"异步训练模式\"\n              description={\n                <div>\n                  训练任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始训练按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartTraining}\n          loading={training}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {training ? '正在训练...' : '开始训练预测'}\n        </Button>\n\n        {/* 训练进度 */}\n        {training && (\n          <div className=\"progress-section\">\n            <Text>训练进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n\n        {/* 训练结果展示 */}\n        {trainingResults && trainingResults.results && (\n          <div style={{ marginTop: 24 }}>\n            <Alert\n              message=\"训练完成\"\n              description={\n                <div>\n                  <p>所有模型训练完成！</p>\n                  {trainingResults.result_path && (\n                    <p><strong>结果已更新至:</strong> {trainingResults.result_path}</p>\n                  )}\n                  {Object.entries(trainingResults.results).map(([key, result]: [string, any]) => (\n                    <div key={key} style={{ marginTop: 8 }}>\n                      <p><strong>协议与数据类型:</strong> {key}</p>\n                      <p>模型已保存至: {result.model_save_path}</p>\n                      <p>标准化器已保存至: {result.scaler_y_save_path}</p>\n                    </div>\n                  ))}\n                </div>\n              }\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 16 }}\n            />\n          </div>\n        )}\n\n      {/* 查看模型训练及特征预测结果 */}\n      {trainingResults && trainingResults.results && Object.keys(trainingResults.results).length > 0 && (\n        <Card title=\"查看模型训练及特征预测结果\" className=\"function-card\">\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>选择要查看的协议和数据类型：</Text>\n              <Select\n                value={selectedResultKey}\n                onChange={setSelectedResultKey}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择协议和数据类型\"\n              >\n                {Object.keys(trainingResults.results).map((key) => (\n                  <Option key={key} value={key}>\n                    {key}\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {selectedResultKey && trainingResults.results[selectedResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedResultKey}\n                result={trainingResults.results[selectedResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 异步训练结果展示 */}\n      {completedTrainingTasks.length > 0 && (\n        <Card title=\"异步训练结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步训练已完成\"\n            description=\"以下是后台训练任务的结果，您可以查看不同协议和数据类型的训练效果。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择训练任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的训练任务\"\n              >\n                {completedTrainingTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 协议和数据类型选择器 */}\n            {asyncTrainingResults && asyncTrainingResults.results && (\n              <div>\n                <Text strong>选择要查看的协议和数据类型：</Text>\n                <Select\n                  value={selectedAsyncResultKey}\n                  onChange={setSelectedAsyncResultKey}\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"请选择协议和数据类型\"\n                >\n                  {Object.keys(asyncTrainingResults.results).map((key) => (\n                    <Option key={key} value={key}>\n                      {key}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n            )}\n\n            {/* 结果展示 */}\n            {selectedAsyncResultKey && asyncTrainingResults && asyncTrainingResults.results[selectedAsyncResultKey] && (\n              <TrainingResultDisplay\n                resultKey={selectedAsyncResultKey}\n                result={asyncTrainingResults.results[selectedAsyncResultKey]}\n              />\n            )}\n          </Space>\n        </Card>\n      )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ModelTrainingPage;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Radio,\n  Upload,\n  Input,\n  Select,\n  Button,\n  Typography,\n  Space,\n  Divider,\n  message,\n  Spin,\n  Alert,\n  Statistic,\n  Row,\n  Col,\n  Progress,\n} from 'antd';\nimport { InboxOutlined, PlayCircleOutlined } from '@ant-design/icons';\n\nimport { modelPredictionAPI } from '../services/api';\nimport useTaskManager from '../hooks/useTaskManager';\n\nconst { Title, Text } = Typography;\nconst { Dragger } = Upload;\nconst { Option } = Select;\n\n// 预测结果展示组件\nconst PredictionResultDisplay: React.FC<{ result: PredictionResult }> = ({ result }) => {\n\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Statistic\n            title=\"建议的流量清洗阈值 (pps)\"\n            value={result.suggested_threshold}\n            precision={2}\n            valueStyle={{ color: '#1890ff' }}\n          />\n          {result.suggested_threshold && (\n            <Alert\n              message=\"✅ 此阈值已自动保存到以输入CSV文件命名的结果文件中，可用于生成清洗模板。\"\n              type=\"success\"\n              showIcon\n              style={{ marginTop: 8 }}\n            />\n          )}\n        </Col>\n\n      </Row>\n\n\n\n      {/* 资源监控信息 - 一行内展示 */}\n      {(result.duration_seconds !== undefined || result.cpu_percent !== undefined || result.memory_mb !== undefined || result.gpu_memory_mb !== undefined || result.gpu_utilization_percent !== undefined) && (\n        <div style={{ marginBottom: 24 }}>\n          <Title level={5}>资源使用情况</Title>\n          <Row gutter={16}>\n            {result.duration_seconds !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"预测耗时\"\n                  value={result.duration_seconds}\n                  precision={2}\n                  suffix=\"秒\"\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n            )}\n            {result.cpu_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"CPU使用率\"\n                  value={result.cpu_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n            )}\n            {result.memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"内存使用\"\n                  value={result.memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Col>\n            )}\n            {result.gpu_memory_mb !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU内存\"\n                  value={result.gpu_memory_mb}\n                  precision={1}\n                  suffix=\"MB\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            )}\n            {result.gpu_utilization_percent !== undefined && (\n              <Col flex=\"auto\">\n                <Statistic\n                  title=\"GPU利用率\"\n                  value={result.gpu_utilization_percent}\n                  precision={1}\n                  suffix=\"%\"\n                  valueStyle={{ color: '#eb2f96' }}\n                />\n              </Col>\n            )}\n          </Row>\n        </div>\n      )}\n\n\n    </div>\n  );\n};\n\n// 协议和数据类型配置（与Streamlit版本完全一致）\nconst protocolOptions = ['TCP', 'UDP', 'ICMP'];\nconst datatypeOptions = {\n  TCP: ['spt_sip_dip', 'dpt_sip_dip', 'len_dpt_syn', 'seq_ack_dip'],\n  UDP: ['spt_sip_dip', 'dpt_sip_dip'],\n  ICMP: ['dip']\n};\n\ninterface PredictionResult {\n  model_name: string;\n  suggested_threshold: number;\n  message?: string;\n  // 资源监控信息（与Streamlit版本一致）\n  duration_seconds?: number;\n  cpu_percent?: number;\n  memory_mb?: number;\n  gpu_memory_mb?: number;\n  gpu_utilization_percent?: number;\n}\n\nconst ModelPredictionPage: React.FC = () => {\n  const [dataSource, setDataSource] = useState<'upload' | 'local'>('local');\n  const [uploadedFile, setUploadedFile] = useState<any>(null);\n  const [csvDir, setCsvDir] = useState('');\n  const [availableCsvFiles, setAvailableCsvFiles] = useState<string[]>([]);\n  const [selectedCsvFile, setSelectedCsvFile] = useState<string>('');\n  const [csvFilesLoading, setCsvFilesLoading] = useState(false);\n\n  // 模型相关状态\n  const [modelDir, setModelDir] = useState('');\n  const [availablePthFiles, setAvailablePthFiles] = useState<string[]>([]);\n  const [selectedModels, setSelectedModels] = useState<string[]>([]);\n  const [modelsLoading, setModelsLoading] = useState(false);\n  const [predictionMode, setPredictionMode] = useState<'single' | 'multiple'>('single');\n\n  // 单模型选择状态\n  const [selectedModelFile, setSelectedModelFile] = useState<string>('');\n  const [selectedParamsFile, setSelectedParamsFile] = useState<string>('');\n  const [selectedScalerFile, setSelectedScalerFile] = useState<string>('');\n  const [selectedProt, setSelectedProt] = useState<string>('');\n  const [selectedDatatype, setSelectedDatatype] = useState<string>('');\n  const [showManualSelection, setShowManualSelection] = useState(false);\n\n  // 预测状态\n  const [predicting, setPredicting] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<PredictionResult[]>([]);\n  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(0);\n  const [matchingFilesLoading, setMatchingFilesLoading] = useState(false);\n\n  // 任务管理\n  const { submitPredictionTask, getCompletedTasksByType, fetchCompletedTasks } = useTaskManager();\n  const [useAsyncPrediction, setUseAsyncPrediction] = useState(true); // 默认使用异步预测\n\n  // 异步任务结果状态\n  const [asyncPredictionResults, setAsyncPredictionResults] = useState<PredictionResult[]>([]);\n  const [selectedAsyncTaskId, setSelectedAsyncTaskId] = useState<string>('');\n\n  // 获取已完成的预测任务\n  const completedPredictionTasks = getCompletedTasksByType('prediction');\n\n  // 处理异步任务选择\n  const handleAsyncTaskSelect = (taskId: string) => {\n    setSelectedAsyncTaskId(taskId);\n    const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);\n\n    if (selectedTask && selectedTask.result) {\n      // 转换异步预测结果为简化格式\n      const asyncResult: PredictionResult = {\n        suggested_threshold: selectedTask.result.suggested_threshold || 0,\n        model_name: selectedTask.result.model_name || '未知模型',\n        message: selectedTask.result.message || '预测完成',\n        duration_seconds: selectedTask.result.duration_seconds,\n        cpu_percent: selectedTask.result.cpu_percent,\n        memory_mb: selectedTask.result.memory_mb,\n        gpu_memory_mb: selectedTask.result.gpu_memory_mb,\n        gpu_utilization_percent: selectedTask.result.gpu_utilization_percent\n      };\n\n      setAsyncPredictionResults([asyncResult]);\n    }\n  };\n\n  // 页面加载时获取已完成任务\n  useEffect(() => {\n    fetchCompletedTasks();\n  }, [fetchCompletedTasks]);\n\n  // 自动选择最新的预测任务\n  useEffect(() => {\n    if (completedPredictionTasks.length > 0 && !selectedAsyncTaskId) {\n      const latestTask = completedPredictionTasks[completedPredictionTasks.length - 1];\n      handleAsyncTaskSelect(latestTask.task_id);\n    }\n  }, [completedPredictionTasks, selectedAsyncTaskId]);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = async () => {\n    if (!csvDir) return;\n\n    setCsvFilesLoading(true);\n    try {\n      const response = await modelPredictionAPI.listCsvFiles(csvDir);\n      setAvailableCsvFiles(response.data.files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取CSV文件列表失败');\n      setAvailableCsvFiles([]);\n    } finally {\n      setCsvFilesLoading(false);\n    }\n  };\n\n  // 获取模型文件列表\n  const fetchModelFiles = async () => {\n    if (!modelDir) return;\n\n    setModelsLoading(true);\n    try {\n      const response = await modelPredictionAPI.listModelFiles(modelDir);\n      setAvailablePthFiles(response.data.pth_files || []);\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取模型文件列表失败');\n      setAvailablePthFiles([]);\n    } finally {\n      setModelsLoading(false);\n    }\n  };\n\n  // 自动匹配参数和标准化器文件（与Streamlit版本一致）\n  const autoMatchFiles = async (modelFile: string) => {\n    if (!modelFile || !modelDir) return;\n\n    setMatchingFilesLoading(true);\n    try {\n      // 调用后端API获取匹配的文件和协议信息\n      const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n\n      if (response.data) {\n        const matchingFiles = response.data;\n\n        // 设置自动匹配的文件\n        setSelectedParamsFile(matchingFiles.params_filename || '');\n        setSelectedScalerFile(matchingFiles.scaler_filename || '');\n        setSelectedProt(matchingFiles.protocol || '');\n        setSelectedDatatype(matchingFiles.datatype || '');\n\n        // 显示匹配结果\n        if (matchingFiles.params_filename && matchingFiles.scaler_filename) {\n          message.success('✅ 已自动匹配相关文件');\n        }\n\n        if (matchingFiles.protocol && matchingFiles.datatype) {\n          message.success(`✅ 已自动识别模型对应的协议和数据类型：${matchingFiles.protocol} - ${matchingFiles.datatype}`);\n          setShowManualSelection(false);\n        } else {\n          message.warning('⚠️ 无法从文件名自动识别协议和数据类型，请手动选择');\n          setShowManualSelection(true);\n        }\n      }\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '获取匹配文件失败');\n      // 如果API调用失败，回退到简单的文件名解析\n      const baseNameWithoutExt = modelFile.replace('_model_best.pth', '');\n      setSelectedParamsFile(`${baseNameWithoutExt}_params.json`);\n      setSelectedScalerFile(`${baseNameWithoutExt}_scaler_y.pkl`);\n      setShowManualSelection(true);\n    } finally {\n      setMatchingFilesLoading(false);\n    }\n  };\n\n  // 处理模型文件选择\n  const handleModelFileChange = (modelFile: string) => {\n    setSelectedModelFile(modelFile);\n    // 重置之前的选择\n    setSelectedParamsFile('');\n    setSelectedScalerFile('');\n    setSelectedProt('');\n    setSelectedDatatype('');\n    setShowManualSelection(false);\n\n    // 异步调用自动匹配\n    autoMatchFiles(modelFile);\n  };\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (dataSource === 'local' && csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dataSource, csvDir]);\n\n  useEffect(() => {\n    if (modelDir && modelDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchModelFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modelDir]);\n\n  // 文件上传配置\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: '.csv',\n    beforeUpload: () => false,\n    onChange: (info: any) => {\n      if (info.fileList.length > 0) {\n        setUploadedFile(info.fileList[0]);\n      } else {\n        setUploadedFile(null);\n      }\n    },\n  };\n\n  // 开始预测\n  const handleStartPrediction = async () => {\n    // 验证输入\n    if (dataSource === 'upload' && !uploadedFile) {\n      message.error('请上传CSV文件');\n      return;\n    }\n\n    if (dataSource === 'local' && (!csvDir || !selectedCsvFile)) {\n      message.error('请选择CSV文件');\n      return;\n    }\n\n    // 验证模型选择\n    let modelsToPredict: Array<{\n      model_file: string;\n      params_file: string;\n      scaler_file: string;\n      protocol: string;\n      datatype: string;\n    }> = [];\n\n    if (predictionMode === 'single') {\n      if (!selectedModelFile || !selectedProt || !selectedDatatype) {\n        message.error('请选择模型文件并确保协议和数据类型已设置');\n        return;\n      }\n      modelsToPredict = [{\n        model_file: selectedModelFile,\n        params_file: selectedParamsFile,\n        scaler_file: selectedScalerFile,\n        protocol: selectedProt,\n        datatype: selectedDatatype\n      }];\n    } else {\n      if (selectedModels.length === 0) {\n        message.error('请至少选择一个模型');\n        return;\n      }\n\n      // 为每个选中的模型获取匹配信息（与Streamlit版本一致）\n      const validModels: Array<{\n        model_file: string;\n        params_file: string;\n        scaler_file: string;\n        protocol: string;\n        datatype: string;\n      }> = [];\n\n      for (const modelFile of selectedModels) {\n        try {\n          const response = await modelPredictionAPI.getMatchingFiles(modelFile, modelDir);\n          if (response.data) {\n            const matchingFiles = response.data;\n            const params_file = matchingFiles.params_filename;\n            const scaler_file = matchingFiles.scaler_filename;\n            const protocol = matchingFiles.protocol;\n            const datatype = matchingFiles.datatype;\n\n            // 只有当所有必要信息都可用时，才添加到选中模型列表\n            if (params_file && scaler_file && protocol && datatype) {\n              validModels.push({\n                model_file: modelFile,\n                params_file,\n                scaler_file,\n                protocol,\n                datatype\n              });\n              message.success(`✅ 模型 ${modelFile} 已匹配: ${protocol} - ${datatype}`);\n            } else {\n              message.warning(`⚠️ 模型 ${modelFile} 无法自动识别所有必要参数，已跳过`);\n            }\n          } else {\n            message.error(`获取模型 ${modelFile} 的匹配文件失败`);\n          }\n        } catch (error: any) {\n          message.error(`处理模型 ${modelFile} 时出错: ${error.response?.data?.detail || error.message}`);\n        }\n      }\n\n      if (validModels.length === 0) {\n        message.error('没有可用的模型可以进行预测，请检查模型文件名是否包含必要的协议和数据类型信息');\n        return;\n      }\n\n      modelsToPredict = validModels;\n    }\n\n    setPredicting(true);\n    setProgress(0);\n    setResults([]);\n\n    try {\n      if (useAsyncPrediction && predictionMode === 'single') {\n        // 只有单模型预测支持异步模式\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', selectedModelFile);\n        formData.append('params_filename', selectedParamsFile);\n        formData.append('scaler_filename', selectedScalerFile);\n        formData.append('selected_prot', selectedProt);\n        formData.append('selected_datatype', selectedDatatype);\n        formData.append('model_dir', modelDir);\n        formData.append('output_folder', modelDir);\n\n        // 提交异步任务\n        const taskId = await submitPredictionTask(formData);\n\n        if (taskId) {\n          message.success('预测任务已启动，您可以继续使用其他功能，任务完成后会收到通知');\n          // 重置状态\n          setPredicting(false);\n          setProgress(0);\n        }\n\n        return; // 异步模式下直接返回\n      }\n\n      // 同步预测模式（保留原有逻辑）\n      const allResults: PredictionResult[] = [];\n\n      for (let i = 0; i < modelsToPredict.length; i++) {\n        const model = modelsToPredict[i];\n\n        // 更新进度\n        setProgress(Math.round((i / modelsToPredict.length) * 90));\n\n        if (modelsToPredict.length > 1) {\n          message.info(`正在使用模型 ${i + 1}/${modelsToPredict.length}: ${model.model_file} 进行预测...`);\n        }\n\n        const formData = new FormData();\n\n        if (dataSource === 'upload' && uploadedFile) {\n          // 重新读取文件内容，因为文件内容只能读取一次\n          formData.append('file', uploadedFile.originFileObj);\n        } else {\n          formData.append('csv_dir', csvDir);\n          formData.append('selected_file', selectedCsvFile);\n        }\n\n        formData.append('model_filename', model.model_file);\n        formData.append('params_filename', model.params_file);\n        formData.append('scaler_filename', model.scaler_file);\n        formData.append('selected_prot', model.protocol);\n        formData.append('selected_datatype', model.datatype);\n        formData.append('output_folder', modelDir);\n\n        const response = await modelPredictionAPI.predict(formData);\n\n        if (response.data) {\n          allResults.push({\n            model_name: response.data.model_name || `${model.protocol}_${model.datatype}`,\n            suggested_threshold: response.data.suggested_threshold || 0,\n            message: response.data.message || '预测完成',\n            // 添加资源监控信息\n            duration_seconds: response.data.duration_seconds,\n            cpu_percent: response.data.cpu_percent,\n            memory_mb: response.data.memory_mb,\n            gpu_memory_mb: response.data.gpu_memory_mb,\n            gpu_utilization_percent: response.data.gpu_utilization_percent,\n          });\n\n          if (modelsToPredict.length > 1) {\n            message.success(`✅ 模型 ${model.model_file} 预测成功`);\n          }\n        }\n      }\n\n      setProgress(100);\n      setResults(allResults);\n      message.success(`✅ 共完成 ${allResults.length} 个模型的预测`);\n\n    } catch (error: any) {\n      message.error(error.response?.data?.detail || '预测失败');\n    } finally {\n      setPredicting(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const hasData = dataSource === 'upload' ? uploadedFile : (csvDir && selectedCsvFile);\n\n    if (predictionMode === 'single') {\n      return hasData && selectedModelFile && selectedProt && selectedDatatype;\n    } else {\n      return hasData && selectedModels.length > 0;\n    }\n  };\n\n\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型实时预测与异常检测</Title>\n      <Text type=\"secondary\">\n        加载已训练的流量模型，对新数据进行预测，并根据动态阈值检测异常流量。\n      </Text>\n\n      <Divider />\n\n      {/* 流量数据源 */}\n      <Card title=\"流量数据源\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>预测数据源：</Text>\n            <Radio.Group\n              value={dataSource}\n              onChange={(e) => setDataSource(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"local\">选择本地CSV文件</Radio>\n              <Radio value=\"upload\">上传CSV文件</Radio>\n            </Radio.Group>\n          </div>\n\n          {/* 本地文件选择 */}\n          {dataSource === 'local' && (\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n                  <Input\n                    value={csvDir}\n                    onChange={(e) => setCsvDir(e.target.value)}\n                    placeholder=\"例如: /data/output\"\n                    style={{ flex: 1 }}\n                  />\n                  <Button\n                    type=\"primary\"\n                    onClick={fetchCsvFiles}\n                    loading={csvFilesLoading}\n                    disabled={!csvDir}\n                    style={{ marginLeft: 8 }}\n                  >\n                    刷新\n                  </Button>\n                </Input.Group>\n              </div>\n\n              <div>\n                <Text strong>选择CSV文件：</Text>\n                <Spin spinning={csvFilesLoading}>\n                  <Select\n                    value={selectedCsvFile}\n                    onChange={setSelectedCsvFile}\n                    placeholder=\"请选择CSV文件\"\n                    style={{ width: '100%', marginTop: 8 }}\n                    loading={csvFilesLoading}\n                  >\n                    {availableCsvFiles.map((file) => (\n                      <Option key={file} value={file}>\n                        {file}\n                      </Option>\n                    ))}\n                  </Select>\n                </Spin>\n              </div>\n            </Space>\n          )}\n\n          {/* 文件上传 */}\n          {dataSource === 'upload' && (\n            <div>\n              <Text strong>上传待预测的CSV文件：</Text>\n              <Dragger {...uploadProps} style={{ marginTop: 8 }}>\n                <p className=\"ant-upload-drag-icon\">\n                  <InboxOutlined />\n                </p>\n                <p className=\"ant-upload-text\">点击或拖拽CSV文件到此区域上传</p>\n                <p className=\"ant-upload-hint\">\n                  支持CSV格式的流量数据文件\n                </p>\n              </Dragger>\n            </div>\n          )}\n        </Space>\n      </Card>\n\n      {/* 模型选择 */}\n      <Card title=\"模型选择\" className=\"function-card\">\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>模型目录：</Text>\n            <Input.Group compact style={{ marginTop: 8, display: 'flex' }}>\n              <Input\n                value={modelDir}\n                onChange={(e) => setModelDir(e.target.value)}\n                placeholder=\"例如: /data/output\"\n                style={{ flex: 1 }}\n              />\n              <Button\n                type=\"primary\"\n                onClick={fetchModelFiles}\n                loading={modelsLoading}\n                disabled={!modelDir}\n                style={{ marginLeft: 8 }}\n              >\n                刷新\n              </Button>\n            </Input.Group>\n          </div>\n\n          <div>\n            <Text strong>预测模式：</Text>\n            <Radio.Group\n              value={predictionMode}\n              onChange={(e) => setPredictionMode(e.target.value)}\n              style={{ marginTop: 8 }}\n            >\n              <Radio value=\"single\">单个模型预测</Radio>\n              <Radio value=\"multiple\">多个模型批量预测</Radio>\n            </Radio.Group>\n          </div>\n\n          {predictionMode === 'single' ? (\n            <div>\n              <Text strong>选择模型文件：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  value={selectedModelFile}\n                  onChange={handleModelFileChange}\n                  placeholder=\"选择一个训练好的模型文件，系统将自动匹配对应的参数和标准化器文件\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n\n              {selectedModelFile && (\n                <div style={{ marginTop: 16 }}>\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div>\n                      <Text type=\"secondary\">自动匹配的文件：</Text>\n                      <Spin spinning={matchingFilesLoading}>\n                        <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>\n                          {matchingFilesLoading ? (\n                            <p>正在自动匹配相关文件...</p>\n                          ) : (\n                            <>\n                              <p><strong>参数文件:</strong> {selectedParamsFile || '未匹配'}</p>\n                              <p><strong>标准化器文件:</strong> {selectedScalerFile || '未匹配'}</p>\n                              {!showManualSelection && selectedProt && selectedDatatype && (\n                                <>\n                                  <p><strong>协议:</strong> {selectedProt}</p>\n                                  <p><strong>数据类型:</strong> {selectedDatatype}</p>\n                                </>\n                              )}\n                            </>\n                          )}\n                        </div>\n                      </Spin>\n                    </div>\n\n                    {showManualSelection && (\n                      <div>\n                        <Text strong>请手动选择协议和数据类型：</Text>\n                        <div style={{ marginTop: 8 }}>\n                          <Space direction=\"vertical\" style={{ width: '100%' }}>\n                            <Select\n                              value={selectedProt}\n                              onChange={setSelectedProt}\n                              placeholder=\"选择与模型对应的协议\"\n                              style={{ width: '100%' }}\n                            >\n                              {protocolOptions.map((prot) => (\n                                <Option key={prot} value={prot}>\n                                  {prot}\n                                </Option>\n                              ))}\n                            </Select>\n\n                            {selectedProt && (\n                              <Select\n                                value={selectedDatatype}\n                                onChange={setSelectedDatatype}\n                                placeholder={`选择与模型对应的 ${selectedProt} 数据类型`}\n                                style={{ width: '100%' }}\n                              >\n                                {(datatypeOptions[selectedProt as keyof typeof datatypeOptions] || []).map((datatype) => (\n                                  <Option key={datatype} value={datatype}>\n                                    {datatype}\n                                  </Option>\n                                ))}\n                              </Select>\n                            )}\n                          </Space>\n                        </div>\n                      </div>\n                    )}\n                  </Space>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div>\n              <Text strong>选择模型文件（多选）：</Text>\n              <Spin spinning={modelsLoading}>\n                <Select\n                  mode=\"multiple\"\n                  value={selectedModels}\n                  onChange={setSelectedModels}\n                  placeholder=\"请选择多个模型文件进行批量预测\"\n                  style={{ width: '100%', marginTop: 8 }}\n                  loading={modelsLoading}\n                >\n                  {availablePthFiles.map((file) => (\n                    <Option key={file} value={file}>\n                      {file}\n                    </Option>\n                  ))}\n                </Select>\n              </Spin>\n            </div>\n          )}\n\n          {availablePthFiles.length === 0 && !modelsLoading && (\n            <Alert\n              message=\"未找到模型文件\"\n              description=\"请确保模型目录中包含训练好的模型文件（.pth）及其对应的参数文件和标准化器文件。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 预测模式选择 */}\n      <Card className=\"function-card\" title=\"预测模式\">\n        <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>选择预测模式：</Text>\n            <Radio.Group\n              value={useAsyncPrediction}\n              onChange={(e) => setUseAsyncPrediction(e.target.value)}\n              style={{ marginTop: 8 }}\n              disabled={predictionMode === 'multiple'} // 多模型预测暂不支持异步\n            >\n              <Radio value={true}>\n                <Space>\n                  异步预测（推荐）\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 后台运行，不阻塞其他操作\n                  </Text>\n                </Space>\n              </Radio>\n              <Radio value={false}>\n                <Space>\n                  同步预测\n                  <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                    - 等待预测完成，期间无法使用其他功能\n                  </Text>\n                </Space>\n              </Radio>\n            </Radio.Group>\n          </div>\n\n          {useAsyncPrediction && predictionMode === 'single' && (\n            <Alert\n              message=\"异步预测模式\"\n              description={\n                <div>\n                  预测任务将在后台运行，您可以继续使用系统的其他功能。\n                  <br />\n                  任务完成后会收到通知，可在 <strong>侧边栏菜单 → 任务管理</strong> 中查看进度和结果。\n                </div>\n              }\n              type=\"info\"\n              showIcon\n            />\n          )}\n\n          {predictionMode === 'multiple' && (\n            <Alert\n              message=\"多模型批量预测\"\n              description=\"多模型批量预测目前仅支持同步模式，预测过程中请耐心等待。\"\n              type=\"warning\"\n              showIcon\n            />\n          )}\n        </Space>\n      </Card>\n\n      {/* 开始预测按钮 */}\n      <Card className=\"function-card\">\n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<PlayCircleOutlined />}\n          onClick={handleStartPrediction}\n          loading={predicting}\n          disabled={!isFormValid()}\n          className=\"action-button\"\n        >\n          {predicting ? '正在预测...' : '开始预测与检测'}\n        </Button>\n\n        {/* 预测进度 */}\n        {predicting && (\n          <div className=\"progress-section\">\n            <Text>预测进度：</Text>\n            <Progress percent={progress} status=\"active\" />\n          </div>\n        )}\n      </Card>\n\n      {/* 预测结果 */}\n      {results.length > 0 && (\n        <Card title=\"预测结果\" className=\"function-card\">\n          {results.length > 1 ? (\n            // 多模型结果展示 - 与Streamlit版本一致\n            <div>\n              <Divider />\n              <Title level={4}>多模型预测结果</Title>\n              <div style={{ marginBottom: 24 }}>\n                <Text strong>选择要查看的模型结果：</Text>\n                <Select\n                  style={{ width: '100%', marginTop: 8 }}\n                  placeholder=\"选择模型结果\"\n                  value={selectedResultIndex}\n                  onChange={(value) => setSelectedResultIndex(value)}\n                >\n                  {results.map((result, index) => (\n                    <Option key={index} value={index}>\n                      {result.model_name}\n                    </Option>\n                  ))}\n                </Select>\n              </div>\n\n              {/* 显示选中的模型结果 */}\n              {results[selectedResultIndex] && (\n                <div>\n                  <Title level={5}>模型: {results[selectedResultIndex].model_name}</Title>\n                  <PredictionResultDisplay result={results[selectedResultIndex]} />\n                </div>\n              )}\n            </div>\n          ) : (\n            // 单模型结果展示\n            <div>\n              <Title level={4}>预测结果 - {results[0].model_name}</Title>\n              <PredictionResultDisplay result={results[0]} />\n            </div>\n          )}\n        </Card>\n      )}\n\n      {/* 异步预测结果展示 */}\n      {completedPredictionTasks.length > 0 && (\n        <Card title=\"异步预测结果\" className=\"function-card\" style={{ marginTop: 24 }}>\n          <Alert\n            message=\"异步预测已完成\"\n            description=\"以下是后台预测任务的结果，您可以查看预测数据和异常检测报告。\"\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n          <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n            {/* 任务选择器 */}\n            <div>\n              <Text strong>选择预测任务：</Text>\n              <Select\n                value={selectedAsyncTaskId}\n                onChange={handleAsyncTaskSelect}\n                style={{ width: '100%', marginTop: 8 }}\n                placeholder=\"请选择要查看的预测任务\"\n              >\n                {completedPredictionTasks.map((task) => (\n                  <Option key={task.task_id} value={task.task_id}>\n                    {task.task_id.includes('_') ?\n                      `${task.task_id.split('_')[0]} (${new Date(task.updated_at || task.created_at).toLocaleString()})` :\n                      `任务 ${task.task_id.substring(0, 8)}... (${new Date(task.updated_at || task.created_at).toLocaleString()})`\n                    }\n                  </Option>\n                ))}\n              </Select>\n            </div>\n\n            {/* 结果展示 */}\n            {asyncPredictionResults.length > 0 && (\n              <div>\n                <Title level={4}>预测结果 - {asyncPredictionResults[0].model_name}</Title>\n                <PredictionResultDisplay result={asyncPredictionResults[0]} />\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default ModelPredictionPage;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>ton,\n  Table,\n  Space,\n  message,\n  Modal,\n  Descriptions,\n  Statistic,\n  Row,\n  Col,\n  Input,\n  Tag,\n  Popconfirm,\n  Spin\n} from 'antd';\nimport {\n  ReloadOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  DatabaseOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport { modelRegistryAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\n\ninterface ModelInfo {\n  model_id: string;\n  protocol: string;\n  datatype: string;\n  r2_score: number;\n  training_time: string;\n  created_time: string;\n  source_data: string;\n  cleaning_threshold: number;\n  training_duration: number;\n  cpu_usage: number;\n  memory_usage: number;\n  gpu_memory: number;\n  gpu_utilization: number;\n  file_paths: {\n    model_path: string;\n    params_path: string;\n    scaler_path: string;\n    test_data_path: string;\n  };\n  training_params: {\n    learning_rate: number;\n    batch_size: number;\n    epochs: number;\n  };\n  model_architecture: {\n    type: string;\n    hidden_size: number;\n    num_layers: number;\n    sequence_length: number;\n    dropout: number;\n  };\n  train_shape: number[];\n  test_shape: number[];\n}\n\ninterface Statistics {\n  total_models: number;\n  avg_r2_score: number;\n  best_model: {\n    model_id: string;\n    r2_score: number;\n    protocol: string;\n    datatype: string;\n  } | null;\n  protocols: { [key: string]: number };\n  datatypes: { [key: string]: number };\n}\n\nconst ModelRegistryPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState<ModelInfo[]>([]);\n  const [statistics, setStatistics] = useState<Statistics | null>(null);\n  const [selectedModel, setSelectedModel] = useState<ModelInfo | null>(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [activeTab, setActiveTab] = useState('1');\n\n  // 获取模型列表\n  const fetchModels = async () => {\n    setLoading(true);\n    try {\n      const response = await modelRegistryAPI.listModels();\n      console.log('模型列表响应:', response.data); // 添加调试日志\n      if (response.data.success) {\n        setModels(response.data.models || []);\n        if (response.data.total_count > 0) {\n          message.success(`📊 共找到 ${response.data.total_count} 个模型`);\n        }\n      } else {\n        console.error('模型列表API返回失败:', response.data);\n        message.error('获取模型列表失败');\n      }\n    } catch (error: any) {\n      console.error('获取模型列表失败:', error);\n      message.error(`❌ 获取模型列表失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      const response = await modelRegistryAPI.getStatistics();\n      console.log('统计信息响应:', response.data); // 添加调试日志\n      if (response.data.success) {\n        setStatistics(response.data.statistics);\n      } else {\n        console.error('统计信息API返回失败:', response.data);\n        message.error('获取统计信息失败');\n      }\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n      message.error(`❌ 获取统计信息失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 获取模型详情\n  const fetchModelDetail = async (modelId: string) => {\n    try {\n      const response = await modelRegistryAPI.getModelDetail(modelId);\n      if (response.data.success) {\n        setSelectedModel(response.data.model);\n        setDetailModalVisible(true);\n      }\n    } catch (error: any) {\n      console.error('获取模型详情失败:', error);\n      message.error(`❌ 获取模型详情失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 删除模型\n  const deleteModel = async (modelId: string) => {\n    try {\n      const response = await modelRegistryAPI.deleteModel(modelId);\n      if (response.data.success) {\n        message.success('✅ 模型删除成功');\n        fetchModels(); // 重新获取列表\n      }\n    } catch (error: any) {\n      console.error('删除模型失败:', error);\n      message.error(`❌ 删除模型失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  useEffect(() => {\n    fetchModels();\n    fetchStatistics();\n  }, []);\n\n  // 模型列表表格列定义\n  const columns = [\n    {\n      title: '模型ID',\n      dataIndex: 'model_id',\n      key: 'model_id',\n      width: 200,\n      filteredValue: searchText ? [searchText] : null,\n      onFilter: (value: any, record: ModelInfo) =>\n        record.model_id.toLowerCase().includes(value.toLowerCase()) ||\n        record.protocol.toLowerCase().includes(value.toLowerCase()) ||\n        record.datatype.toLowerCase().includes(value.toLowerCase()),\n      render: (text: string) => (\n        <Text code style={{ fontSize: '12px' }}>{text}</Text>\n      ),\n    },\n    {\n      title: '协议类型',\n      dataIndex: 'protocol',\n      key: 'protocol',\n      width: 100,\n      render: (protocol: string) => (\n        <Tag color={protocol === 'TCP' ? 'blue' : protocol === 'UDP' ? 'green' : 'orange'}>\n          {protocol}\n        </Tag>\n      ),\n    },\n    {\n      title: '数据类型',\n      dataIndex: 'datatype',\n      key: 'datatype',\n      width: 120,\n      render: (datatype: string) => (\n        <Tag color=\"purple\">{datatype}</Tag>\n      ),\n    },\n    {\n      title: 'R² 分数',\n      dataIndex: 'r2_score',\n      key: 'r2_score',\n      width: 120,\n      sorter: (a: ModelInfo, b: ModelInfo) => a.r2_score - b.r2_score,\n      render: (score: number) => (\n        <Text strong style={{ color: score > 0.8 ? '#52c41a' : score > 0.6 ? '#faad14' : '#ff4d4f' }}>\n          {score.toFixed(4)}\n        </Text>\n      ),\n    },\n    {\n      title: '训练时长',\n      dataIndex: 'training_duration',\n      key: 'training_duration',\n      width: 120,\n      render: (duration: number) => `${duration ? duration.toFixed(2) : 'N/A'}s`,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      width: 180,\n      sorter: (a: ModelInfo, b: ModelInfo) => new Date(a.created_time).getTime() - new Date(b.created_time).getTime(),\n      render: (time: string) => new Date(time).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      width: 150,\n      render: (_, record: ModelInfo) => (\n        <Space>\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => fetchModelDetail(record.model_id)}\n          >\n            详情\n          </Button>\n          <Popconfirm\n            title=\"确认删除\"\n            description=\"确定要删除这个模型吗？此操作不可恢复。\"\n            onConfirm={() => deleteModel(record.model_id)}\n            okText=\"确认\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"primary\"\n              danger\n              size=\"small\"\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>模型仓库管理</Title>\n      <Text type=\"secondary\">\n        集中展示、管理和查看所有已训练的模型信息。\n      </Text>\n\n      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: 24 }}>\n        <TabPane tab={<span><DatabaseOutlined />模型列表</span>} key=\"1\">\n          <Card>\n            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <Space>\n                <Button\n                  type=\"primary\"\n                  icon={<ReloadOutlined />}\n                  onClick={fetchModels}\n                  loading={loading}\n                >\n                  刷新列表\n                </Button>\n                <Input\n                  placeholder=\"搜索模型ID、协议或数据类型\"\n                  prefix={<SearchOutlined />}\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  style={{ width: 300 }}\n                  allowClear\n                />\n              </Space>\n            </div>\n\n            {models.length === 0 && !loading ? (\n              <Alert\n                message=\"暂无模型\"\n                description=\"🔍 暂无已训练的模型，请先进行模型训练。\"\n                type=\"info\"\n                showIcon\n              />\n            ) : (\n              <Table\n                columns={columns}\n                dataSource={models}\n                rowKey=\"model_id\"\n                loading={loading}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 个模型`,\n                }}\n                scroll={{ x: 1200 }}\n              />\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><BarChartOutlined />统计信息</span>} key=\"2\">\n          {statistics ? (\n            <div>\n              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"总模型数\"\n                      value={statistics.total_models}\n                      prefix={<DatabaseOutlined />}\n                      valueStyle={{ color: '#1890ff' }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"平均 R² 分数\"\n                      value={statistics.avg_r2_score}\n                      precision={4}\n                      prefix={<BarChartOutlined />}\n                      valueStyle={{ color: '#52c41a' }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={8}>\n                  <Card>\n                    <Statistic\n                      title=\"最佳模型 R²\"\n                      value={statistics.best_model?.r2_score || 0}\n                      precision={4}\n                      prefix={<TrophyOutlined />}\n                      valueStyle={{ color: '#faad14' }}\n                    />\n                  </Card>\n                </Col>\n              </Row>\n\n              <Row gutter={[16, 16]}>\n                <Col span={12}>\n                  <Card title=\"协议分布\" size=\"small\">\n                    {statistics.protocols && Object.entries(statistics.protocols).map(([protocol, count]) => (\n                      <div key={protocol} style={{ marginBottom: 8 }}>\n                        <Tag color={protocol === 'TCP' ? 'blue' : protocol === 'UDP' ? 'green' : 'orange'}>\n                          {protocol}\n                        </Tag>\n                        <span style={{ marginLeft: 8 }}>{count} 个模型</span>\n                      </div>\n                    ))}\n                    {(!statistics.protocols || Object.keys(statistics.protocols).length === 0) && (\n                      <Text type=\"secondary\">暂无数据</Text>\n                    )}\n                  </Card>\n                </Col>\n                <Col span={12}>\n                  <Card title=\"数据类型分布\" size=\"small\">\n                    {statistics.datatypes && Object.entries(statistics.datatypes).map(([datatype, count]) => (\n                      <div key={datatype} style={{ marginBottom: 8 }}>\n                        <Tag color=\"purple\">{datatype}</Tag>\n                        <span style={{ marginLeft: 8 }}>{count} 个模型</span>\n                      </div>\n                    ))}\n                    {(!statistics.datatypes || Object.keys(statistics.datatypes).length === 0) && (\n                      <Text type=\"secondary\">暂无数据</Text>\n                    )}\n                  </Card>\n                </Col>\n              </Row>\n\n              {statistics.best_model && (\n                <Card title=\"最佳模型信息\" style={{ marginTop: 16 }} size=\"small\">\n                  <Descriptions column={2} size=\"small\">\n                    <Descriptions.Item label=\"模型ID\">\n                      <Text code>{statistics.best_model.model_id}</Text>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"R² 分数\">\n                      <Text strong style={{ color: '#faad14' }}>\n                        {statistics.best_model.r2_score.toFixed(4)}\n                      </Text>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"协议类型\">\n                      <Tag color={statistics.best_model.protocol === 'TCP' ? 'blue' :\n                                  statistics.best_model.protocol === 'UDP' ? 'green' : 'orange'}>\n                        {statistics.best_model.protocol}\n                      </Tag>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"数据类型\">\n                      <Tag color=\"purple\">{statistics.best_model.datatype}</Tag>\n                    </Descriptions.Item>\n                  </Descriptions>\n                </Card>\n              )}\n            </div>\n          ) : (\n            <Card>\n              <Spin size=\"large\" />\n              <div style={{ textAlign: 'center', marginTop: 16 }}>\n                <Text>正在加载统计信息...</Text>\n              </div>\n            </Card>\n          )}\n        </TabPane>\n      </Tabs>\n\n      {/* 模型详情弹窗 */}\n      <Modal\n        title=\"模型详细信息\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedModel && (\n          <div>\n            <Descriptions title=\"基本信息\" bordered column={2} size=\"small\">\n              <Descriptions.Item label=\"模型ID\" span={2}>\n                <Text code>{selectedModel.model_id}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"协议类型\">\n                <Tag color={selectedModel.protocol === 'TCP' ? 'blue' :\n                            selectedModel.protocol === 'UDP' ? 'green' : 'orange'}>\n                  {selectedModel.protocol}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"数据类型\">\n                <Tag color=\"purple\">{selectedModel.datatype}</Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"R² 分数\">\n                <Text strong style={{ color: selectedModel.r2_score > 0.8 ? '#52c41a' :\n                                           selectedModel.r2_score > 0.6 ? '#faad14' : '#ff4d4f' }}>\n                  {selectedModel.r2_score.toFixed(4)}\n                </Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练开始时间\">\n                {selectedModel.training_time ? new Date(selectedModel.training_time).toLocaleString() : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\" span={2}>\n                {new Date(selectedModel.created_time).toLocaleString()}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"模型文件\" bordered column={1} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"模型文件\">\n                <Text code>{selectedModel.file_paths?.model_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"参数文件\">\n                <Text code>{selectedModel.file_paths?.params_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"标准化器文件\">\n                <Text code>{selectedModel.file_paths?.scaler_path || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"测试数据文件\">\n                <Text code>{selectedModel.file_paths?.test_data_path || 'N/A'}</Text>\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"训练参数\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"学习率\">\n                {selectedModel.training_params?.learning_rate || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"批次大小\">\n                {selectedModel.training_params?.batch_size || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练轮数\">\n                {selectedModel.training_params?.epochs || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"清洗阈值\">\n                {selectedModel.cleaning_threshold?.toFixed(4) || 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"模型架构\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"模型类型\">\n                {selectedModel.model_architecture?.type || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"隐藏层大小\">\n                {selectedModel.model_architecture?.hidden_size || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"网络层数\">\n                {selectedModel.model_architecture?.num_layers || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"序列长度\">\n                {selectedModel.model_architecture?.sequence_length || 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"Dropout率\" span={2}>\n                {selectedModel.model_architecture?.dropout || 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"数据信息\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"数据文件\" span={2}>\n                <Text code>{selectedModel.source_data || 'N/A'}</Text>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"训练数据形状\">\n                {selectedModel.train_shape ? `[${selectedModel.train_shape.join(', ')}]` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"测试数据形状\">\n                {selectedModel.test_shape ? `[${selectedModel.test_shape.join(', ')}]` : 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Descriptions title=\"资源使用\" bordered column={2} size=\"small\" style={{ marginTop: 16 }}>\n              <Descriptions.Item label=\"训练时长\">\n                {selectedModel.training_duration ? `${selectedModel.training_duration.toFixed(2)}s` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"CPU使用率\">\n                {selectedModel.cpu_usage ? `${selectedModel.cpu_usage.toFixed(2)}%` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"内存使用\">\n                {selectedModel.memory_usage ? `${selectedModel.memory_usage.toFixed(2)}MB` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"GPU内存\">\n                {selectedModel.gpu_memory ? `${selectedModel.gpu_memory.toFixed(2)}MB` : 'N/A'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"GPU利用率\" span={2}>\n                {selectedModel.gpu_utilization ? `${selectedModel.gpu_utilization.toFixed(2)}%` : 'N/A'}\n              </Descriptions.Item>\n            </Descriptions>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default ModelRegistryPage;\n", "import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typo<PERSON>,\n  <PERSON>,\n  Alert,\n  Tabs,\n  Button,\n  Select,\n  Input,\n  Form,\n  message,\n  Table,\n  Space,\n  Modal,\n  Row,\n  Col,\n  Divider\n} from 'antd';\nimport {\n  FileTextOutlined,\n  SettingOutlined,\n  SendOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n  DownloadOutlined,\n  EditOutlined,\n  FolderOpenOutlined,\n  CloudUploadOutlined\n} from '@ant-design/icons';\nimport { cleanTemplateAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { Option } = Select;\n\ninterface TemplateInfo {\n  template_name: string;\n  filename: string;\n  template_path: string;\n  file_size: number;\n  created_time: number;\n  modified_time: number;\n}\n\nconst CleanTemplatePage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [generateForm] = Form.useForm();\n  const [sendForm] = Form.useForm();\n  const [updateForm] = Form.useForm();\n\n  // 模板生成相关状态\n  const [resultFiles, setResultFiles] = useState<string[]>([]);\n  const [resultDir, setResultDir] = useState('');\n  const [lastGeneratedTemplate, setLastGeneratedTemplate] = useState<{\n    path: string;\n    name: string;\n    time: string;\n  } | null>(null);\n\n  // 模板管理相关状态\n  const [templates, setTemplates] = useState<TemplateInfo[]>([]);\n  const [templateDir, setTemplateDir] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('');\n  const [templateContent, setTemplateContent] = useState('');\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n\n  // 模板发送功能的状态\n  const [sendTemplateDir, setSendTemplateDir] = useState<string>('');\n  const [sendTemplates, setSendTemplates] = useState<TemplateInfo[]>([]);\n  const [sendTemplatesLoading, setSendTemplatesLoading] = useState(false);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async (showMessage = true) => {\n    if (!resultDir.trim()) {\n      setResultFiles([]);\n      return;\n    }\n\n    try {\n      const response = await cleanTemplateAPI.listResultFiles(resultDir);\n      if (response.data.files) {\n        setResultFiles(response.data.files || []);\n        if (showMessage && response.data.files.length > 0) {\n          message.success(`📁 找到 ${response.data.files.length} 个结果文件`);\n        } else if (showMessage && response.data.files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取结果文件失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取结果文件失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setResultFiles([]);\n    }\n  }, [resultDir]);\n\n  // 获取模板列表\n  const fetchTemplates = useCallback(async (showMessage = true) => {\n    if (!templateDir.trim()) {\n      setTemplates([]);\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(templateDir);\n      console.log('模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${templateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，直接使用\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${templateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的模板数据:', processedTemplates); // 调试信息\n        setTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [templateDir]);\n\n  // 获取发送模板列表\n  const fetchSendTemplates = useCallback(async (showMessage = true) => {\n    if (!sendTemplateDir.trim()) {\n      setSendTemplates([]);\n      return;\n    }\n\n    setSendTemplatesLoading(true);\n    try {\n      const response = await cleanTemplateAPI.listTemplates(sendTemplateDir);\n      console.log('发送模板列表API响应:', response.data); // 调试信息\n\n      if (response.data && response.data.templates) {\n        const templatesData = response.data.templates;\n        console.log('发送模板数据:', templatesData); // 调试信息\n\n        // 检查数据格式并转换\n        let processedTemplates = [];\n        if (Array.isArray(templatesData)) {\n          processedTemplates = templatesData.map((item, index) => {\n            if (typeof item === 'string') {\n              // 如果是字符串数组（旧格式），转换为对象\n              return {\n                template_name: item.replace('_cleantemplate.json', ''),\n                filename: item,\n                template_path: `${sendTemplateDir}/${item}`,\n                file_size: 0,\n                created_time: 0,\n                modified_time: 0\n              };\n            } else if (typeof item === 'object' && item !== null) {\n              // 如果已经是对象格式，确保所有必需字段存在\n              return {\n                template_name: item.template_name || item.filename?.replace('_cleantemplate.json', '') || `模板${index + 1}`,\n                filename: item.filename || item.template_name || `template${index + 1}.json`,\n                template_path: item.template_path || `${sendTemplateDir}/${item.filename}`,\n                file_size: item.file_size || 0,\n                created_time: item.created_time || 0,\n                modified_time: item.modified_time || 0\n              };\n            }\n            return item;\n          });\n        }\n\n        console.log('处理后的发送模板数据:', processedTemplates); // 调试信息\n        setSendTemplates(processedTemplates);\n\n        if (showMessage && processedTemplates.length > 0) {\n          message.success(`📁 找到 ${processedTemplates.length} 个可发送的模板文件`);\n        } else if (showMessage && processedTemplates.length === 0) {\n          message.info('📁 该目录下暂无模板文件');\n        }\n      }\n    } catch (error: any) {\n      console.error('获取发送模板列表失败:', error);\n      if (showMessage) {\n        message.error(`❌ 获取发送模板列表失败: ${error.response?.data?.detail || error.message}`);\n      }\n      setSendTemplates([]);\n    } finally {\n      setSendTemplatesLoading(false);\n    }\n  }, [sendTemplateDir]);\n\n  // 获取模板内容\n  const fetchTemplateContent = async (templatePath: string) => {\n    try {\n      const response = await cleanTemplateAPI.getTemplateContent(templatePath);\n      if (response.data && response.data.content) {\n        setTemplateContent(JSON.stringify(response.data.content, null, 2));\n        return response.data.content;\n      }\n    } catch (error: any) {\n      console.error('获取模板内容失败:', error);\n      message.error(`❌ 获取模板内容失败: ${error.response?.data?.detail || error.message}`);\n    }\n    return null;\n  };\n\n  // 生成清洗模板\n  const generateTemplate = async (values: any) => {\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('results_file', `${resultDir}/${values.selected_result_file}`);\n      formData.append('output_folder', values.output_dir);\n      if (values.template_name) {\n        formData.append('template_name', values.template_name);\n      }\n\n      const response = await cleanTemplateAPI.generateTemplate(formData);\n      if (response.data) {\n        const { template_path, updated_thresholds } = response.data;\n\n        // 显示详细的成功信息\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 清洗模板生成成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 文件路径: {template_path}<br/>\n                🔧 更新阈值: {updated_thresholds} 个\n              </div>\n            </div>\n          ),\n          duration: 6, // 显示6秒\n        });\n\n        // 刷新模板列表\n        if (templateDir) {\n          fetchTemplates(true); // 显示刷新消息\n        }\n\n        // 设置最近生成的模板信息\n        setLastGeneratedTemplate({\n          path: template_path,\n          name: template_path.split('/').pop() || '未知模板',\n          time: new Date().toLocaleString()\n        });\n\n        // 重置表单\n        generateForm.resetFields();\n        setResultFiles([]); // 清空结果文件列表\n        setResultDir(''); // 清空目录输入\n      }\n    } catch (error: any) {\n      console.error('生成模板失败:', error);\n      message.error(`❌ 生成模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新模板内容\n  const updateTemplate = async (values: any) => {\n    try {\n      const formData = new FormData();\n      formData.append('template_path', selectedTemplate);\n      formData.append('template_content', values.template_content);\n\n      const response = await cleanTemplateAPI.updateTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success('✅ 模板内容更新成功');\n        setEditModalVisible(false);\n        fetchTemplates(true); // 刷新模板列表\n      }\n    } catch (error: any) {\n      console.error('更新模板失败:', error);\n      message.error(`❌ 更新模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 发送模板\n  const sendTemplate = async (values: any) => {\n    console.log('发送模板参数:', values); // 调试信息\n\n    setLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('template_path', values.template_path);\n      formData.append('target_host', values.target_host);\n      formData.append('target_username', values.target_username);\n      formData.append('target_password', values.target_password);\n      formData.append('target_port', values.target_port.toString());\n      formData.append('target_path', values.target_path);\n\n      const response = await cleanTemplateAPI.sendTemplate(formData);\n      if (response.data && response.data.message) {\n        message.success({\n          content: (\n            <div>\n              <div style={{ fontWeight: 'bold', marginBottom: 4 }}>\n                🎉 模板发送成功！\n              </div>\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                📁 模板: {values.template_path.split('/').pop()}<br/>\n                🖥️ 目标: {values.target_host}:{values.target_port}<br/>\n                📂 路径: {values.target_path}\n              </div>\n            </div>\n          ),\n          duration: 6,\n        });\n\n        // 重置表单，但保留选择模式\n        const currentMode = sendForm.getFieldValue('template_selection_mode');\n        sendForm.resetFields();\n        sendForm.setFieldsValue({ template_selection_mode: currentMode });\n      }\n    } catch (error: any) {\n      console.error('发送模板失败:', error);\n      message.error(`❌ 发送模板失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载模板\n  const downloadTemplate = async (templatePath: string, templateName: string) => {\n    console.log('下载模板，路径:', templatePath, '名称:', templateName); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    try {\n      const response = await cleanTemplateAPI.downloadTemplate(templatePath);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', templateName || 'template.json');\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success('✅ 模板下载成功');\n    } catch (error: any) {\n      console.error('下载模板失败:', error);\n      message.error(`❌ 下载模板失败: ${error.response?.data?.detail || error.message}`);\n    }\n  };\n\n  // 查看模板详情\n  const viewTemplate = async (templatePath: string) => {\n    console.log('查看模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setViewModalVisible(true);\n    }\n  };\n\n  // 编辑模板\n  const editTemplate = async (templatePath: string) => {\n    console.log('编辑模板，路径:', templatePath); // 调试信息\n    if (!templatePath || templatePath === 'undefined') {\n      message.error('模板路径无效');\n      return;\n    }\n    const content = await fetchTemplateContent(templatePath);\n    if (content) {\n      setSelectedTemplate(templatePath);\n      updateForm.setFieldsValue({ template_content: templateContent });\n      setEditModalVisible(true);\n    }\n  };\n\n  // 防抖hook\n  const useDebounce = (value: string, delay: number) => {\n    const [debouncedValue, setDebouncedValue] = useState(value);\n\n    useEffect(() => {\n      const handler = setTimeout(() => {\n        setDebouncedValue(value);\n      }, delay);\n\n      return () => {\n        clearTimeout(handler);\n      };\n    }, [value, delay]);\n\n    return debouncedValue;\n  };\n\n  // 防抖处理路径变化 - 增加延迟时间减少频繁请求\n  const debouncedResultDir = useDebounce(resultDir, 1500);\n  const debouncedTemplateDir = useDebounce(templateDir, 1500);\n  const debouncedSendTemplateDir = useDebounce(sendTemplateDir, 1500);\n\n  // 页面初始化时不自动获取数据，等待用户手动刷新\n  // useEffect(() => {\n  //   fetchResultFiles();\n  //   fetchTemplates();\n  // }, []);\n\n  // 防抖后的路径变化时静默获取数据（不显示消息）\n  useEffect(() => {\n    if (debouncedResultDir && debouncedResultDir.trim() !== '') {\n      fetchResultFiles(false); // 静默获取，不显示消息\n    } else {\n      setResultFiles([]); // 清空列表\n    }\n  }, [debouncedResultDir, fetchResultFiles]);\n\n  useEffect(() => {\n    if (debouncedTemplateDir && debouncedTemplateDir.trim() !== '') {\n      fetchTemplates(false); // 静默获取，不显示消息\n    } else {\n      setTemplates([]); // 清空列表\n    }\n  }, [debouncedTemplateDir, fetchTemplates]);\n\n  useEffect(() => {\n    if (debouncedSendTemplateDir && debouncedSendTemplateDir.trim() !== '') {\n      fetchSendTemplates(false); // 静默获取，不显示消息\n    } else {\n      setSendTemplates([]); // 清空列表\n    }\n  }, [debouncedSendTemplateDir, fetchSendTemplates]);\n\n  // 模板列表表格列定义\n  const templateColumns = [\n    {\n      title: '模板名称',\n      dataIndex: 'template_name',\n      key: 'template_name',\n      render: (name: string, record: TemplateInfo) => (\n        <div>\n          <Text strong>{name}</Text>\n          <br />\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            {record.filename}\n          </Text>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: TemplateInfo) => {\n        console.log('表格行数据:', record); // 调试信息\n        return (\n          <Space>\n            <Button\n              type=\"primary\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => viewTemplate(record.template_path)}\n            >\n              查看\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => editTemplate(record.template_path)}\n            >\n              编辑\n            </Button>\n            <Button\n              size=\"small\"\n              icon={<DownloadOutlined />}\n              onClick={() => downloadTemplate(record.template_path, record.filename)}\n            >\n              下载\n            </Button>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>清洗模板生成</Title>\n      <Text type=\"secondary\">\n        基于模型训练或预测结果，生成特定客户的流量清洗模板配置文件。\n      </Text>\n\n      <Tabs defaultActiveKey=\"1\" style={{ marginTop: 24 }}>\n        <TabPane tab={<span><FileTextOutlined />模板生成</span>} key=\"1\">\n          <Card title=\"生成清洗模板\" size=\"small\">\n            <Form\n              form={generateForm}\n              layout=\"vertical\"\n              onFinish={generateTemplate}\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"结果文件目录\"\n                    name=\"result_dir\"\n                    rules={[{ required: true, message: '请输入结果文件目录' }]}\n                  >\n                    <Input\n                      prefix={<FolderOpenOutlined />}\n                      placeholder=\"例如: /data/output\"\n                      value={resultDir}\n                      onChange={(e) => setResultDir(e.target.value)}\n                      addonAfter={\n                        <Button\n                          size=\"small\"\n                          icon={<ReloadOutlined />}\n                          onClick={() => fetchResultFiles(true)}\n                        >\n                          刷新\n                        </Button>\n                      }\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"选择结果文件\"\n                    name=\"selected_result_file\"\n                    rules={[{ required: true, message: '请选择结果文件' }]}\n                  >\n                    <Select\n                      placeholder={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新\" : \"选择要生成模板的结果文件\"}\n                      showSearch\n                      notFoundContent={resultFiles.length === 0 ? \"请先输入目录路径并点击刷新获取文件列表\" : \"未找到匹配的文件\"}\n                      filterOption={(input, option) =>\n                        (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                      }\n                    >\n                      {resultFiles.map(file => (\n                        <Option key={file} value={file}>\n                          {file}\n                        </Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板输出目录\"\n                    name=\"output_dir\"\n                    rules={[{ required: true, message: '请输入输出目录' }]}\n                  >\n                    <Input placeholder=\"例如: /data/output\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"模板名称（可选）\"\n                    name=\"template_name\"\n                  >\n                    <Input placeholder=\"例如: customer_name\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<FileTextOutlined />}\n                >\n                  生成清洗模板\n                </Button>\n              </Form.Item>\n            </Form>\n\n            {/* 显示最近生成的模板信息 */}\n            {lastGeneratedTemplate && (\n              <Card\n                size=\"small\"\n                style={{ marginTop: 16, borderColor: '#52c41a' }}\n                title={\n                  <span style={{ color: '#52c41a' }}>\n                    <FileTextOutlined /> 最近生成的模板\n                  </span>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    type=\"text\"\n                    onClick={() => setLastGeneratedTemplate(null)}\n                  >\n                    ×\n                  </Button>\n                }\n              >\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Text strong>模板名称：</Text>\n                    <Text copyable>{lastGeneratedTemplate.name}</Text>\n                  </Col>\n                  <Col span={12}>\n                    <Text strong>生成时间：</Text>\n                    <Text>{lastGeneratedTemplate.time}</Text>\n                  </Col>\n                </Row>\n                <Row style={{ marginTop: 8 }}>\n                  <Col span={24}>\n                    <Text strong>文件路径：</Text>\n                    <Text copyable style={{ fontSize: '12px', color: '#666' }}>\n                      {lastGeneratedTemplate.path}\n                    </Text>\n                  </Col>\n                </Row>\n              </Card>\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SettingOutlined />模板管理</span>} key=\"2\">\n          <Card\n            title=\"模板列表\"\n            size=\"small\"\n            extra={\n              <Space>\n                <Input\n                  placeholder=\"模板目录路径\"\n                  value={templateDir}\n                  onChange={(e) => setTemplateDir(e.target.value)}\n                  style={{ width: 200 }}\n                />\n                <Button\n                  icon={<ReloadOutlined />}\n                  onClick={() => fetchTemplates(true)}\n                  loading={loading}\n                >\n                  刷新\n                </Button>\n              </Space>\n            }\n          >\n            {templates.length === 0 && !loading ? (\n              <Alert\n                message=\"暂无模板\"\n                description=\"📁 请先输入模板目录路径并点击刷新按钮获取模板列表，或者在模板生成页面创建新模板。\"\n                type=\"info\"\n                showIcon\n              />\n            ) : (\n              <Table\n                columns={templateColumns}\n                dataSource={templates}\n                rowKey=\"template_path\"\n                loading={loading}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: (total) => `共 ${total} 个模板`,\n                }}\n                size=\"small\"\n              />\n            )}\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><SendOutlined />模板发送</span>} key=\"3\">\n          <Card title=\"发送模板到目标服务器\" size=\"small\">\n            <Form\n              form={sendForm}\n              layout=\"vertical\"\n              onFinish={sendTemplate}\n            >\n              <Form.Item\n                label=\"模板目录\"\n                name=\"send_template_directory\"\n                rules={[{ required: true, message: '请输入模板目录路径' }]}\n              >\n                <Input\n                  placeholder=\"请输入模板目录路径，例如: /data/output\"\n                  value={sendTemplateDir}\n                  onChange={(e) => setSendTemplateDir(e.target.value)}\n                  suffix={\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      loading={sendTemplatesLoading}\n                      onClick={() => fetchSendTemplates(true)}\n                    />\n                  }\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"选择模板\"\n                name=\"template_path\"\n                rules={[{ required: true, message: '请选择要发送的模板' }]}\n              >\n                <Select\n                  placeholder=\"请先输入模板目录，然后选择要发送的模板文件\"\n                  showSearch\n                  loading={sendTemplatesLoading}\n                  filterOption={(input, option) =>\n                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                  }\n                  notFoundContent={\n                    sendTemplatesLoading ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">🔄 正在加载模板列表...</Text>\n                      </div>\n                    ) : sendTemplates.length === 0 ? (\n                      <div style={{ textAlign: 'center', padding: '20px' }}>\n                        <Text type=\"secondary\">\n                          📁 暂无可用模板<br/>\n                          请检查目录路径或点击刷新按钮\n                        </Text>\n                      </div>\n                    ) : null\n                  }\n                >\n                  {sendTemplates.map(template => (\n                    <Option key={template.template_path} value={template.template_path}>\n                      <div>\n                        <Text strong>{template.template_name}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {template.filename}\n                        </Text>\n                      </div>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Divider>目标服务器配置</Divider>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"目标主机\"\n                    name=\"target_host\"\n                    rules={[{ required: true, message: '请输入目标主机地址' }]}\n                  >\n                    <Input placeholder=\"例如: *************\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"端口\"\n                    name=\"target_port\"\n                    initialValue={22}\n                    rules={[{ required: true, message: '请输入端口号' }]}\n                  >\n                    <Input type=\"number\" placeholder=\"22\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"用户名\"\n                    name=\"target_username\"\n                    rules={[{ required: true, message: '请输入用户名' }]}\n                  >\n                    <Input placeholder=\"例如: root\" />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    label=\"密码\"\n                    name=\"target_password\"\n                    rules={[{ required: true, message: '请输入密码' }]}\n                  >\n                    <Input.Password placeholder=\"请输入密码\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item\n                label=\"目标路径\"\n                name=\"target_path\"\n                rules={[{ required: true, message: '请输入目标路径' }]}\n              >\n                <Input placeholder=\"例如: /etc/cleantemplate/\" />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<CloudUploadOutlined />}\n                >\n                  发送模板\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </TabPane>\n      </Tabs>\n\n      {/* 查看模板内容弹窗 */}\n      <Modal\n        title=\"模板内容\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setViewModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        <TextArea\n          value={templateContent}\n          rows={20}\n          readOnly\n          style={{ fontFamily: 'monospace' }}\n        />\n      </Modal>\n\n      {/* 编辑模板内容弹窗 */}\n      <Modal\n        title=\"编辑模板内容\"\n        open={editModalVisible}\n        onCancel={() => setEditModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setEditModalVisible(false)}>\n            取消\n          </Button>,\n          <Button key=\"save\" type=\"primary\" onClick={() => updateForm.submit()}>\n            保存\n          </Button>\n        ]}\n        width={800}\n      >\n        <Form\n          form={updateForm}\n          onFinish={updateTemplate}\n        >\n          <Form.Item\n            name=\"template_content\"\n            rules={[{ required: true, message: '模板内容不能为空' }]}\n          >\n            <TextArea\n              rows={20}\n              style={{ fontFamily: 'monospace' }}\n              placeholder=\"请输入JSON格式的模板内容\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CleanTemplatePage;\n", "import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n\n  Input,\n  Select,\n  Button,\n  message,\n  Row,\n  Col,\n  Spin,\n  Empty,\n  Space,\n  Divider,\n  List,\n  Tag\n} from 'antd';\nimport {\n  FolderOpenOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  FileTextOutlined,\n  EyeOutlined,\n  SearchOutlined\n} from '@ant-design/icons';\nimport { dataQueryAPI } from '../services/api';\nimport TextArea from 'antd/es/input/TextArea';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\n\n\nconst DataQueryPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [csvLoading, setCsvLoading] = useState(false);\n  const [resultLoading, setResultLoading] = useState(false);\n\n  // CSV文件查询相关状态\n  const [csvDir, setCsvDir] = useState('');\n  const [csvFiles, setCsvFiles] = useState<string[]>([]);\n  const [selectedCsv, setSelectedCsv] = useState<string>('');\n\n  // 结果文件查询相关状态\n  const [resultDir, setResultDir] = useState('');\n  const [resultFiles, setResultFiles] = useState<string[]>([]);\n  const [selectedResult, setSelectedResult] = useState<string>('');\n  const [resultContent, setResultContent] = useState<string>('');\n  const [contentVisible, setContentVisible] = useState(false);\n\n  // 获取CSV文件列表\n  const fetchCsvFiles = useCallback(async () => {\n    setCsvLoading(true);\n    try {\n      const response = await dataQueryAPI.listCsvFiles(csvDir);\n      if (response.data.csv_files) {\n        setCsvFiles(response.data.csv_files);\n        setSelectedCsv(''); // 重置选择\n        if (response.data.csv_files.length === 0) {\n          message.info('📁 该目录下暂无CSV文件');\n        } else {\n          message.success(`📊 找到 ${response.data.csv_files.length} 个CSV文件`);\n        }\n      }\n    } catch (error: any) {\n      console.error('获取CSV文件列表失败:', error);\n      message.error(`❌ 获取CSV文件列表失败: ${error.response?.data?.detail || error.message}`);\n      setCsvFiles([]);\n    } finally {\n      setCsvLoading(false);\n    }\n  }, [csvDir]);\n\n  // 获取结果文件列表\n  const fetchResultFiles = useCallback(async () => {\n    setResultLoading(true);\n    try {\n      const response = await dataQueryAPI.listResultFiles(resultDir);\n      if (response.data.result_files) {\n        setResultFiles(response.data.result_files);\n        setSelectedResult(''); // 重置选择\n        setResultContent(''); // 清空内容\n        setContentVisible(false);\n        if (response.data.result_files.length === 0) {\n          message.info('📁 该目录下暂无结果文件');\n        } else {\n          message.success(`📊 找到 ${response.data.result_files.length} 个结果文件`);\n        }\n      }\n    } catch (error: any) {\n      console.error('获取结果文件列表失败:', error);\n      message.error(`❌ 获取结果文件列表失败: ${error.response?.data?.detail || error.message}`);\n      setResultFiles([]);\n    } finally {\n      setResultLoading(false);\n    }\n  }, [resultDir]);\n\n  // 下载CSV文件\n  const downloadCsv = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.downloadCsv(csvDir, fileName);\n\n      // 创建下载链接\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success(`✅ ${fileName} 下载成功`);\n    } catch (error: any) {\n      console.error('下载CSV文件失败:', error);\n      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取结果文件内容\n  const getResultContent = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.getResultContent(resultDir, fileName);\n      if (response.data.content) {\n        setResultContent(response.data.content);\n        setContentVisible(true);\n        message.success('✅ 文件内容加载成功');\n      }\n    } catch (error: any) {\n      console.error('获取文件内容失败:', error);\n      message.error(`❌ 获取文件内容失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载结果文件\n  const downloadResult = async (fileName: string) => {\n    try {\n      setLoading(true);\n      const response = await dataQueryAPI.getResultContent(resultDir, fileName);\n\n      // 创建下载链接\n      const blob = new Blob([response.data.content], { type: 'text/plain' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      message.success(`✅ ${fileName} 下载成功`);\n    } catch (error: any) {\n      console.error('下载结果文件失败:', error);\n      message.error(`❌ 下载失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  // 使用防抖来避免频繁请求 - 只在用户停止输入1.5秒后才发起请求\n  useEffect(() => {\n    if (csvDir && csvDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchCsvFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [csvDir]);\n\n  useEffect(() => {\n    if (resultDir && resultDir.length > 3) { // 至少输入4个字符才开始请求\n      const timer = setTimeout(() => {\n        fetchResultFiles();\n      }, 1500); // 1.5秒延迟，给用户足够时间输入完整路径\n\n      return () => clearTimeout(timer);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [resultDir]);\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>数据查询</Title>\n      <Text type=\"secondary\">\n        查询流量分析模块生成的CSV文件和流量检测模型预测的特征值。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* CSV文件查询 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <FileTextOutlined />\n                <span>清洗出的 CSV 文件查询</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>CSV文件目录：</Text>\n                <Input\n                  value={csvDir}\n                  onChange={(e) => setCsvDir(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  prefix={<FolderOpenOutlined />}\n                  style={{ marginTop: 8 }}\n                  addonAfter={\n                    <Button\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      onClick={fetchCsvFiles}\n                      loading={csvLoading}\n                    >\n                      刷新\n                    </Button>\n                  }\n                />\n              </div>\n\n              <Divider style={{ margin: '16px 0' }} />\n\n              {csvLoading ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <Spin size=\"large\" />\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">正在加载CSV文件列表...</Text>\n                  </div>\n                </div>\n              ) : csvFiles.length === 0 ? (\n                <Empty\n                  description=\"请先输入CSV文件目录路径并点击刷新按钮获取文件列表\"\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                />\n              ) : (\n                <div>\n                  <Text strong>选择CSV文件：</Text>\n                  <Select\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder=\"选择要下载的CSV文件\"\n                    value={selectedCsv}\n                    onChange={setSelectedCsv}\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {csvFiles.map(file => (\n                      <Option key={file} value={file}>\n                        <Space>\n                          <FileTextOutlined />\n                          {file}\n                        </Space>\n                      </Option>\n                    ))}\n                  </Select>\n\n                  {selectedCsv && (\n                    <div style={{ marginTop: 16, textAlign: 'center' }}>\n                      <Button\n                        type=\"primary\"\n                        icon={<DownloadOutlined />}\n                        onClick={() => downloadCsv(selectedCsv)}\n                        loading={loading}\n                        size=\"large\"\n                      >\n                        下载 {selectedCsv}\n                      </Button>\n                    </div>\n                  )}\n\n                  <Divider style={{ margin: '16px 0' }} />\n\n                  <div>\n                    <Text strong>文件列表：</Text>\n                    <List\n                      size=\"small\"\n                      style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}\n                      dataSource={csvFiles}\n                      renderItem={(file) => (\n                        <List.Item\n                          actions={[\n                            <Button\n                              key=\"download\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<DownloadOutlined />}\n                              onClick={() => downloadCsv(file)}\n                              loading={loading}\n                            >\n                              下载\n                            </Button>\n                          ]}\n                        >\n                          <List.Item.Meta\n                            avatar={<FileTextOutlined style={{ color: '#1890ff' }} />}\n                            title={file}\n                            description={\n                              <Tag color=\"blue\" size=\"small\">CSV文件</Tag>\n                            }\n                          />\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                </div>\n              )}\n            </Space>\n          </Card>\n        </Col>\n\n        {/* 结果文件查询 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SearchOutlined />\n                <span>特征预测值查询</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <div>\n                <Text strong>结果文件目录：</Text>\n                <Input\n                  value={resultDir}\n                  onChange={(e) => setResultDir(e.target.value)}\n                  placeholder=\"例如: /data/output\"\n                  prefix={<FolderOpenOutlined />}\n                  style={{ marginTop: 8 }}\n                  addonAfter={\n                    <Button\n                      size=\"small\"\n                      icon={<ReloadOutlined />}\n                      onClick={fetchResultFiles}\n                      loading={resultLoading}\n                    >\n                      刷新\n                    </Button>\n                  }\n                />\n              </div>\n\n              <Divider style={{ margin: '16px 0' }} />\n\n              {resultLoading ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <Spin size=\"large\" />\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">正在加载结果文件列表...</Text>\n                  </div>\n                </div>\n              ) : resultFiles.length === 0 ? (\n                <Empty\n                  description=\"请先输入结果文件目录路径并点击刷新按钮获取文件列表\"\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                />\n              ) : (\n                <div>\n                  <Text strong>选择结果文件：</Text>\n                  <Select\n                    style={{ width: '100%', marginTop: 8 }}\n                    placeholder=\"选择要查看的结果文件\"\n                    value={selectedResult}\n                    onChange={setSelectedResult}\n                    showSearch\n                    filterOption={(input, option) =>\n                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())\n                    }\n                  >\n                    {resultFiles.map(file => (\n                      <Option key={file} value={file}>\n                        <Space>\n                          <FileTextOutlined />\n                          {file}\n                        </Space>\n                      </Option>\n                    ))}\n                  </Select>\n\n                  {selectedResult && (\n                    <div style={{ marginTop: 16 }}>\n                      <Space>\n                        <Button\n                          type=\"primary\"\n                          icon={<EyeOutlined />}\n                          onClick={() => getResultContent(selectedResult)}\n                          loading={loading}\n                        >\n                          查看内容\n                        </Button>\n                        <Button\n                          icon={<DownloadOutlined />}\n                          onClick={() => downloadResult(selectedResult)}\n                          loading={loading}\n                        >\n                          下载文件\n                        </Button>\n                      </Space>\n                    </div>\n                  )}\n\n                  <Divider style={{ margin: '16px 0' }} />\n\n                  <div>\n                    <Text strong>文件列表：</Text>\n                    <List\n                      size=\"small\"\n                      style={{ marginTop: 8, maxHeight: 200, overflow: 'auto' }}\n                      dataSource={resultFiles}\n                      renderItem={(file) => (\n                        <List.Item\n                          actions={[\n                            <Button\n                              key=\"view\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<EyeOutlined />}\n                              onClick={() => getResultContent(file)}\n                              loading={loading}\n                            >\n                              查看\n                            </Button>,\n                            <Button\n                              key=\"download\"\n                              type=\"link\"\n                              size=\"small\"\n                              icon={<DownloadOutlined />}\n                              onClick={() => downloadResult(file)}\n                              loading={loading}\n                            >\n                              下载\n                            </Button>\n                          ]}\n                        >\n                          <List.Item.Meta\n                            avatar={<FileTextOutlined style={{ color: '#52c41a' }} />}\n                            title={file}\n                            description={\n                              <Tag color=\"green\" size=\"small\">结果文件</Tag>\n                            }\n                          />\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                </div>\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 文件内容查看 */}\n      {contentVisible && resultContent && (\n        <Card\n          title={\n            <Space>\n              <EyeOutlined />\n              <span>{selectedResult} 内容</span>\n            </Space>\n          }\n          style={{ marginTop: 24 }}\n          size=\"small\"\n          extra={\n            <Button\n              size=\"small\"\n              onClick={() => setContentVisible(false)}\n            >\n              关闭\n            </Button>\n          }\n        >\n          <TextArea\n            value={resultContent}\n            rows={15}\n            readOnly\n            style={{\n              fontFamily: 'monospace',\n              fontSize: '12px',\n              backgroundColor: '#f5f5f5'\n            }}\n          />\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default DataQueryPage;\n", "import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Typography,\n  Card,\n  Alert,\n  Form,\n  Input,\n  Button,\n  message,\n  Space,\n\n  Collapse,\n  Table,\n  Tag,\n\n  Row,\n  Col\n} from 'antd';\nimport {\n  LockOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  TeamOutlined,\n  SafetyOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { authAPI } from '../services/api';\n\nconst { Title, Text } = Typography;\nconst { Panel } = Collapse;\n// const { confirm } = Modal;\n\ninterface User {\n  username: string;\n  is_admin: boolean;\n  created_time?: string;\n  last_login?: string;\n}\n\nconst UserManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [users, setUsers] = useState<User[]>([]);\n  const [changePasswordForm] = Form.useForm();\n  const [addUserForm] = Form.useForm();\n\n  // 从Redux store获取当前用户信息\n  const { user, token } = useSelector((state: RootState) => state.auth);\n  const currentUser = user?.username || '';\n  const isAdmin = currentUser === 'admin';\n\n  // 获取用户列表（仅管理员）\n  const fetchUsers = useCallback(async () => {\n    if (!token) return;\n\n    try {\n      const response = await authAPI.getUsers(token);\n      if (response.data) {\n        // 将用户对象转换为数组格式\n        const userList = Object.entries(response.data).map(([username, userData]: [string, any]) => ({\n          username,\n          is_admin: username === 'admin',\n          created_time: userData.created_time || '',\n          last_login: userData.last_login || '',\n        }));\n        setUsers(userList);\n      }\n    } catch (error: any) {\n      console.error('获取用户列表失败:', error);\n      message.error(`❌ 获取用户列表失败: ${error.response?.data?.detail || error.message}`);\n    }\n  }, [token]);\n\n  // 获取当前用户信息\n  useEffect(() => {\n    if (isAdmin && token) {\n      fetchUsers();\n    }\n  }, [isAdmin, token, fetchUsers]);\n\n  // 修改密码\n  const handleChangePassword = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.changePassword({\n        username: currentUser,\n        old_password: values.old_password,\n        new_password: values.new_password,\n        confirm_password: values.confirm_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 密码修改成功');\n        changePasswordForm.resetFields();\n      }\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      message.error(`❌ 修改密码失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户（仅管理员）\n  const handleAddUser = async (values: any) => {\n    if (!token) return;\n\n    setLoading(true);\n    try {\n      const response = await authAPI.addUser({\n        username: currentUser,\n        new_username: values.new_username,\n        new_user_password: values.new_user_password,\n        confirm_user_password: values.confirm_user_password\n      }, token);\n\n      if (response.data.message) {\n        message.success('✅ 用户添加成功');\n        addUserForm.resetFields();\n        fetchUsers(); // 刷新用户列表\n      }\n    } catch (error: any) {\n      console.error('添加用户失败:', error);\n      message.error(`❌ 添加用户失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 密码强度验证\n  const validatePassword = (_: any, value: string) => {\n    if (!value) {\n      return Promise.reject(new Error('请输入密码'));\n    }\n    if (value.length < 6) {\n      return Promise.reject(new Error('密码长度至少6位'));\n    }\n    if (!/(?=.*[a-zA-Z])(?=.*\\d)/.test(value)) {\n      return Promise.reject(new Error('密码必须包含字母和数字'));\n    }\n    return Promise.resolve();\n  };\n\n  // 确认密码验证\n  const validateConfirmPassword = (_: any, value: string) => {\n    const form = changePasswordForm || addUserForm;\n    const passwordField = form === changePasswordForm ? 'new_password' : 'new_user_password';\n    const password = form.getFieldValue(passwordField);\n\n    if (!value) {\n      return Promise.reject(new Error('请确认密码'));\n    }\n    if (value !== password) {\n      return Promise.reject(new Error('两次输入的密码不一致'));\n    }\n    return Promise.resolve();\n  };\n\n  // 用户列表表格列定义\n  const userColumns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (username: string) => (\n        <Space>\n          <UserOutlined />\n          <Text strong>{username}</Text>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'is_admin',\n      key: 'is_admin',\n      render: (isAdmin: boolean) => (\n        <Tag color={isAdmin ? 'red' : 'blue'}>\n          {isAdmin ? '管理员' : '普通用户'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',\n    },\n    {\n      title: '最后登录',\n      dataIndex: 'last_login',\n      key: 'last_login',\n      render: (time: string) => time ? new Date(time).toLocaleString() : '从未登录',\n    },\n  ];\n\n  return (\n    <div>\n      <Title level={3} style={{ fontSize: '20px', fontWeight: 600, marginBottom: '8px' }}>\n        <Space>\n          <TeamOutlined />\n          用户管理\n        </Space>\n      </Title>\n      <Text type=\"secondary\">\n        修改密码、添加新用户等用户管理功能。\n      </Text>\n\n      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>\n        {/* 修改密码 */}\n        <Col span={24}>\n          <Card\n            title={\n              <Space>\n                <LockOutlined />\n                <span>修改密码</span>\n              </Space>\n            }\n            size=\"small\"\n          >\n            <Alert\n              message=\"密码安全提示\"\n              description=\"为了账户安全，建议定期修改密码。新密码应包含字母和数字，长度至少6位。\"\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n\n            <Form\n              form={changePasswordForm}\n              layout=\"vertical\"\n              onFinish={handleChangePassword}\n              style={{ maxWidth: 600 }}\n            >\n              <Form.Item\n                label=\"当前用户\"\n                name=\"current_user\"\n                initialValue={currentUser}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  disabled\n                  value={currentUser}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"原密码\"\n                name=\"old_password\"\n                rules={[{ required: true, message: '请输入原密码' }]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"请输入原密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"新密码\"\n                name=\"new_password\"\n                rules={[{ validator: validatePassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请输入新密码（至少6位，包含字母和数字）\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item\n                label=\"确认新密码\"\n                name=\"confirm_password\"\n                rules={[{ validator: validateConfirmPassword }]}\n              >\n                <Input.Password\n                  prefix={<SafetyOutlined />}\n                  placeholder=\"请再次输入新密码\"\n                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  icon={<LockOutlined />}\n                  size=\"large\"\n                >\n                  修改密码\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 用户管理（仅管理员） */}\n        {isAdmin && (\n          <Col span={24}>\n            <Card\n              title={\n                <Space>\n                  <TeamOutlined />\n                  <span>用户管理</span>\n                  <Tag color=\"red\">管理员专用</Tag>\n                </Space>\n              }\n              size=\"small\"\n            >\n              <Collapse defaultActiveKey={['1']} ghost>\n                <Panel\n                  header={\n                    <Space>\n                      <UserAddOutlined />\n                      <span>添加新用户</span>\n                    </Space>\n                  }\n                  key=\"1\"\n                >\n                  <Form\n                    form={addUserForm}\n                    layout=\"vertical\"\n                    onFinish={handleAddUser}\n                    style={{ maxWidth: 600 }}\n                  >\n                    <Form.Item\n                      label=\"新用户名\"\n                      name=\"new_username\"\n                      rules={[\n                        { required: true, message: '请输入用户名' },\n                        { min: 3, message: '用户名至少3位' },\n                        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }\n                      ]}\n                    >\n                      <Input\n                        prefix={<UserOutlined />}\n                        placeholder=\"请输入新用户名\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"新用户密码\"\n                      name=\"new_user_password\"\n                      rules={[{ validator: validatePassword }]}\n                    >\n                      <Input.Password\n                        prefix={<LockOutlined />}\n                        placeholder=\"请输入新用户密码（至少6位，包含字母和数字）\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"确认新用户密码\"\n                      name=\"confirm_user_password\"\n                      rules={[{ validator: validateConfirmPassword }]}\n                    >\n                      <Input.Password\n                        prefix={<SafetyOutlined />}\n                        placeholder=\"请再次输入新用户密码\"\n                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                      />\n                    </Form.Item>\n\n                    <Form.Item>\n                      <Button\n                        type=\"primary\"\n                        htmlType=\"submit\"\n                        loading={loading}\n                        icon={<UserAddOutlined />}\n                        size=\"large\"\n                      >\n                        添加用户\n                      </Button>\n                    </Form.Item>\n                  </Form>\n                </Panel>\n\n                <Panel\n                  header={\n                    <Space>\n                      <TeamOutlined />\n                      <span>用户列表</span>\n                      <Tag color=\"blue\">{users.length} 个用户</Tag>\n                    </Space>\n                  }\n                  key=\"2\"\n                >\n                  <div style={{ marginBottom: 16 }}>\n                    <Button\n                      icon={<TeamOutlined />}\n                      onClick={fetchUsers}\n                      loading={loading}\n                    >\n                      刷新用户列表\n                    </Button>\n                  </div>\n\n                  <Table\n                    columns={userColumns}\n                    dataSource={users}\n                    rowKey=\"username\"\n                    pagination={{\n                      pageSize: 10,\n                      showSizeChanger: true,\n                      showTotal: (total) => `共 ${total} 个用户`,\n                    }}\n                    size=\"small\"\n                  />\n                </Panel>\n              </Collapse>\n            </Card>\n          </Col>\n        )}\n\n        {/* 非管理员提示 */}\n        {!isAdmin && (\n          <Col span={24}>\n            <Alert\n              message=\"权限提示\"\n              description=\"您当前是普通用户，只能修改自己的密码。如需添加新用户或查看用户列表，请联系管理员。\"\n              type=\"warning\"\n              showIcon\n              icon={<ExclamationCircleOutlined />}\n            />\n          </Col>\n        )}\n      </Row>\n    </div>\n  );\n};\n\nexport default UserManagementPage;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  List,\n  Button,\n  Space,\n  Typography,\n  Tag,\n  Progress,\n  Modal,\n  Empty,\n  message,\n  Spin,\n  Tooltip,\n  Popconfirm\n} from 'antd';\nimport {\n  SyncOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  EyeOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  PlayCircleOutlined,\n  MinusCircleOutlined\n} from '@ant-design/icons';\nimport useTaskManager from '../hooks/useTaskManager';\nimport { Task } from '../services/taskApi';\n\nconst { Title, Text } = Typography;\n\nconst TaskManagerPage: React.FC = () => {\n  const {\n    runningTasks,\n    completedTasks,\n    loading,\n    fetchRunningTasks,\n    fetchCompletedTasks,\n    cancelTask,\n    deleteSingleTask,\n    clearCompletedTasks,\n    formatTaskType,\n    TASK_STATUS\n  } = useTaskManager();\n\n  const [selectedTask, setSelectedTask] = useState<Task | null>(null);\n  const [taskDetailVisible, setTaskDetailVisible] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // 页面加载时获取任务数据\n  useEffect(() => {\n    fetchRunningTasks();\n    fetchCompletedTasks();\n  }, [fetchRunningTasks, fetchCompletedTasks]);\n\n  // 自动刷新运行中的任务\n  useEffect(() => {\n    const interval = setInterval(() => {\n      fetchRunningTasks(false); // 静默刷新\n    }, 5000); // 每5秒刷新一次\n\n    return () => clearInterval(interval);\n  }, [fetchRunningTasks]);\n\n  // 手动刷新\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    try {\n      await Promise.all([\n        fetchRunningTasks(),\n        fetchCompletedTasks()\n      ]);\n      message.success('刷新成功');\n    } catch (error) {\n      message.error('刷新失败');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  // 删除单个任务\n  const handleDeleteSingleTask = (taskId: string) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除任务 ${taskId.substring(0, 8)}... 吗？`,\n      icon: <MinusCircleOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '删除',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        const success = await deleteSingleTask(taskId);\n        if (success) {\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        }\n      }\n    });\n  };\n\n  // 查看任务详情\n  const handleViewTaskDetail = (task: Task) => {\n    setSelectedTask(task);\n    setTaskDetailVisible(true);\n  };\n\n  // 取消任务\n  const handleCancelTask = async (taskId: string) => {\n    try {\n      await cancelTask(taskId);\n      await fetchRunningTasks(); // 刷新运行中任务列表\n    } catch (error) {\n      console.error('取消任务失败:', error);\n    }\n  };\n\n  // 清空所有已完成任务\n  const handleClearCompleted = () => {\n    const completedCount = completedTasks.length;\n    if (completedCount === 0) {\n      message.info('没有已完成的任务需要清空');\n      return;\n    }\n\n    Modal.confirm({\n      title: '确认清空所有已完成任务',\n      content: `确定要清空所有 ${completedCount} 个已完成的任务吗？此操作不可撤销。`,\n      icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\n      okText: '清空',\n      okType: 'danger',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          await clearCompletedTasks();\n          // 刷新任务列表\n          await Promise.all([\n            fetchRunningTasks(),\n            fetchCompletedTasks()\n          ]);\n        } catch (error) {\n          console.error('清空任务失败:', error);\n        }\n      }\n    });\n  };\n\n  // 获取任务状态图标\n  const getTaskStatusIcon = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <SyncOutlined spin style={{ color: '#1890ff' }} />;\n      case TASK_STATUS.COMPLETED:\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case TASK_STATUS.FAILED:\n        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;\n      case TASK_STATUS.CANCELLED:\n        return <StopOutlined style={{ color: '#faad14' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取任务状态标签\n  const getTaskStatusTag = (status: string) => {\n    switch (status) {\n      case TASK_STATUS.RUNNING:\n        return <Tag color=\"processing\" icon={<SyncOutlined spin />}>运行中</Tag>;\n      case TASK_STATUS.COMPLETED:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已完成</Tag>;\n      case TASK_STATUS.FAILED:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>失败</Tag>;\n      case TASK_STATUS.CANCELLED:\n        return <Tag color=\"warning\" icon={<StopOutlined />}>已取消</Tag>;\n      default:\n        return <Tag color=\"default\" icon={<ClockCircleOutlined />}>等待中</Tag>;\n    }\n  };\n\n  // 格式化时间\n  const formatTime = (timeString?: string) => {\n    if (!timeString) return '未知';\n    return new Date(timeString).toLocaleString('zh-CN');\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <div>\n          <Title level={2}>任务管理</Title>\n          <Text type=\"secondary\">\n            查看和管理异步训练、预测任务的状态和结果\n          </Text>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<ReloadOutlined />}\n          onClick={handleRefresh}\n          loading={refreshing}\n        >\n          刷新\n        </Button>\n      </div>\n\n      {/* 任务统计 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={16}>\n          <Col span={6}>\n            <Statistic\n              title=\"运行中任务\"\n              value={runningTasks.length}\n              prefix={<SyncOutlined spin />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"已完成任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.COMPLETED).length}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"失败任务\"\n              value={completedTasks.filter(t => t.status === TASK_STATUS.FAILED).length}\n              prefix={<CloseCircleOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Col>\n          <Col span={6}>\n            <Statistic\n              title=\"总任务数\"\n              value={runningTasks.length + completedTasks.length}\n              prefix={<PlayCircleOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={[24, 24]}>\n        {/* 运行中任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <SyncOutlined spin />\n                运行中任务\n                <Tag color=\"processing\">{runningTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Tooltip title=\"自动刷新中\">\n                <SyncOutlined spin style={{ color: '#1890ff' }} />\n              </Tooltip>\n            }\n          >\n            <Spin spinning={loading}>\n              <List\n                dataSource={runningTasks}\n                locale={{ emptyText: <Empty description=\"暂无运行中的任务\" /> }}\n                renderItem={(task) => (\n                  <List.Item\n                    actions={[\n                      <Button\n                        type=\"link\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleViewTaskDetail(task)}\n                      >\n                        详情\n                      </Button>,\n                      <Popconfirm\n                        title=\"确定要取消这个任务吗？\"\n                        onConfirm={() => handleCancelTask(task.task_id)}\n                        okText=\"确定\"\n                        cancelText=\"取消\"\n                      >\n                        <Button\n                          type=\"link\"\n                          danger\n                          icon={<StopOutlined />}\n                        >\n                          取消\n                        </Button>\n                      </Popconfirm>\n                    ]}\n                  >\n                    <List.Item.Meta\n                      avatar={getTaskStatusIcon(task.status)}\n                      title={\n                        <Space>\n                          <Text strong>{formatTaskType(task.task_type)}</Text>\n                          {getTaskStatusTag(task.status)}\n                        </Space>\n                      }\n                      description={\n                        <div>\n                          <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                            ID: {task.task_id.includes('_') ?\n                              `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                              `${task.task_id.substring(0, 8)}...`\n                            }\n                          </Text>\n                          <br />\n                          <Text type=\"secondary\">开始时间: {formatTime(task.created_at)}</Text>\n                          {task.message && (\n                            <>\n                              <br />\n                              <Text type=\"secondary\">状态: {task.message}</Text>\n                            </>\n                          )}\n                          {task.progress !== undefined && (\n                            <div style={{ marginTop: 8 }}>\n                              <Progress percent={task.progress} size=\"small\" />\n                            </div>\n                          )}\n                        </div>\n                      }\n                    />\n                  </List.Item>\n                )}\n              />\n            </Spin>\n          </Card>\n        </Col>\n\n        {/* 已完成任务 */}\n        <Col span={12}>\n          <Card\n            title={\n              <Space>\n                <CheckCircleOutlined />\n                已完成任务\n                <Tag color=\"success\">{completedTasks.length}</Tag>\n              </Space>\n            }\n            extra={\n              <Button\n                type=\"primary\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n                disabled={completedTasks.length === 0}\n                onClick={handleClearCompleted}\n              >\n                清空全部\n              </Button>\n            }\n          >\n            <List\n              dataSource={completedTasks}\n              locale={{\n                emptyText: (\n                  <Empty\n                    description={\n                      <div>\n                        <div>暂无已完成的任务</div>\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          异步训练和预测完成后，结果会显示在这里\n                        </Text>\n                      </div>\n                    }\n                  />\n                )\n              }}\n              renderItem={(task) => (\n                <List.Item\n                  actions={[\n                    <Button\n                      type=\"link\"\n                      icon={<EyeOutlined />}\n                      onClick={() => handleViewTaskDetail(task)}\n                    >\n                      详情\n                    </Button>,\n                    <Button\n                      type=\"link\"\n                      danger\n                      icon={<MinusCircleOutlined />}\n                      onClick={() => handleDeleteSingleTask(task.task_id)}\n                    >\n                      删除\n                    </Button>\n                  ]}\n                >\n                  <List.Item.Meta\n                    avatar={getTaskStatusIcon(task.status)}\n                    title={\n                      <Space>\n                        <Text strong>{formatTaskType(task.task_type)}</Text>\n                        {getTaskStatusTag(task.status)}\n                      </Space>\n                    }\n                    description={\n                      <div>\n                        <Text type=\"secondary\" copyable={{ text: task.task_id }}>\n                          ID: {task.task_id.includes('_') ?\n                            `${task.task_id.split('_')[0]}...${task.task_id.split('_').slice(-1)[0].substring(0, 8)}` :\n                            `${task.task_id.substring(0, 8)}...`\n                          }\n                        </Text>\n                        <br />\n                        <Text type=\"secondary\">完成时间: {formatTime(task.updated_at)}</Text>\n                        {task.status === TASK_STATUS.FAILED && task.error && (\n                          <>\n                            <br />\n                            <Text type=\"danger\">错误: {task.error}</Text>\n                          </>\n                        )}\n                      </div>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 任务详情模态框 */}\n      <Modal\n        title=\"任务详情\"\n        open={taskDetailVisible}\n        onCancel={() => setTaskDetailVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setTaskDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTask && (\n          <div>\n            <Row gutter={[16, 16]}>\n              <Col span={12}>\n                <Text strong>任务ID:</Text>\n                <br />\n                <Text copyable>{selectedTask.task_id}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>任务类型:</Text>\n                <br />\n                <Text>{formatTaskType(selectedTask.task_type)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>状态:</Text>\n                <br />\n                {getTaskStatusTag(selectedTask.status)}\n              </Col>\n              <Col span={12}>\n                <Text strong>进度:</Text>\n                <br />\n                {selectedTask.status === TASK_STATUS.COMPLETED ? (\n                  <Progress percent={100} size=\"small\" status=\"success\" />\n                ) : selectedTask.status === TASK_STATUS.FAILED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.status === TASK_STATUS.CANCELLED ? (\n                  <Progress percent={selectedTask.progress || 0} size=\"small\" status=\"exception\" />\n                ) : selectedTask.progress !== undefined ? (\n                  <Progress percent={selectedTask.progress} size=\"small\" />\n                ) : (\n                  <Text type=\"secondary\">无进度信息</Text>\n                )}\n              </Col>\n              <Col span={12}>\n                <Text strong>创建时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.created_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>更新时间:</Text>\n                <br />\n                <Text>{formatTime(selectedTask.updated_at)}</Text>\n              </Col>\n              <Col span={12}>\n                <Text strong>执行时长:</Text>\n                <br />\n                <Text>\n                  {(() => {\n                    // 如果任务已完成、失败或取消，且有开始时间和完成时间\n                    if (selectedTask.started_at && selectedTask.completed_at) {\n                      const duration = Math.round((new Date(selectedTask.completed_at).getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒`;\n                    }\n                    // 如果任务正在运行，且有开始时间\n                    else if (selectedTask.started_at && selectedTask.status === TASK_STATUS.RUNNING) {\n                      const duration = Math.round((new Date().getTime() - new Date(selectedTask.started_at).getTime()) / 1000);\n                      return `${duration}秒 (进行中)`;\n                    }\n                    // 如果任务已完成但没有开始时间，尝试从结果中获取时长\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED && selectedTask.result?.duration_seconds) {\n                      return `${Math.round(selectedTask.result.duration_seconds)}秒`;\n                    }\n                    // 如果任务已完成但没有时间信息，显示已完成\n                    else if (selectedTask.status === TASK_STATUS.COMPLETED) {\n                      return '已完成';\n                    }\n                    // 如果任务失败或取消，显示相应状态\n                    else if (selectedTask.status === TASK_STATUS.FAILED) {\n                      return '执行失败';\n                    }\n                    else if (selectedTask.status === TASK_STATUS.CANCELLED) {\n                      return '已取消';\n                    }\n                    // 其他情况显示等待开始\n                    else {\n                      return '等待开始';\n                    }\n                  })()}\n                </Text>\n              </Col>\n              {selectedTask.current_step && (\n                <Col span={24}>\n                  <Text strong>当前步骤:</Text>\n                  <br />\n                  <Text>{selectedTask.current_step}</Text>\n                </Col>\n              )}\n              {selectedTask.message && (\n                <Col span={24}>\n                  <Text strong>消息:</Text>\n                  <br />\n                  <Text>{selectedTask.message}</Text>\n                </Col>\n              )}\n              {selectedTask.error && (\n                <Col span={24}>\n                  <Text strong>错误信息:</Text>\n                  <br />\n                  <Text type=\"danger\">{selectedTask.error}</Text>\n                </Col>\n              )}\n              {selectedTask.params && (\n                <Col span={24}>\n                  <Text strong>任务参数:</Text>\n                  <br />\n                  <div style={{\n                    background: '#f5f5f5',\n                    padding: 12,\n                    borderRadius: 4,\n                    fontSize: 12,\n                    maxHeight: 200,\n                    overflow: 'auto',\n                    marginTop: 8\n                  }}>\n                    <pre>{JSON.stringify(selectedTask.params, null, 2)}</pre>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'training' && (\n                <Col span={24}>\n                  <Text strong>训练完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      训练已完成\n                    </Tag>\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"blue\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                    {selectedTask.result.results && (\n                      <Tag color=\"purple\">\n                        模型数量: {Object.keys(selectedTask.result.results).length}\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 训练结果详情请前往\"模型训练\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n              {selectedTask.result && selectedTask.task_type === 'prediction' && (\n                <Col span={24}>\n                  <Text strong>预测完成:</Text>\n                  <br />\n                  <Space wrap style={{ marginTop: 8 }}>\n                    <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n                      预测已完成\n                    </Tag>\n                    {selectedTask.result.anomaly_count !== undefined && (\n                      <Tag color=\"red\">异常数量: {selectedTask.result.anomaly_count}</Tag>\n                    )}\n                    {selectedTask.result.predictions?.length && (\n                      <Tag color=\"blue\">预测点数: {selectedTask.result.predictions.length}</Tag>\n                    )}\n                    {selectedTask.result.duration_seconds && (\n                      <Tag color=\"orange\">\n                        耗时: {Math.round(selectedTask.result.duration_seconds)}秒\n                      </Tag>\n                    )}\n                  </Space>\n                  <div style={{ marginTop: 8 }}>\n                    <Text type=\"secondary\">\n                      💡 预测结果详情请前往\"异常检测\"页面查看\n                    </Text>\n                  </div>\n                </Col>\n              )}\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TaskManagerPage;\n", "import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store/store';\nimport LoginPage from './pages/LoginPage';\nimport MainLayout from './components/Layout/MainLayout';\nimport DataCleaningPage from './pages/DataCleaningPage';\nimport ModelTrainingPage from './pages/ModelTrainingPage';\nimport ModelPredictionPage from './pages/ModelPredictionPage';\nimport ModelRegistryPage from './pages/ModelRegistryPage';\nimport CleanTemplatePage from './pages/CleanTemplatePage';\nimport DataQueryPage from './pages/DataQueryPage';\nimport UserManagementPage from './pages/UserManagementPage';\nimport TaskManagerPage from './pages/TaskManagerPage';\nimport './App.css';\n\nconst { Content } = Layout;\n\n// 路由守卫组件\nconst ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n  \n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  \n  return <>{children}</>;\n};\n\nconst App: React.FC = () => {\n  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        <Route \n          path=\"/login\" \n          element={\n            isAuthenticated ? <Navigate to=\"/\" replace /> : <LoginPage />\n          } \n        />\n        <Route\n          path=\"/*\"\n          element={\n            <ProtectedRoute>\n              <MainLayout>\n                <Content>\n                  <Routes>\n                    <Route path=\"/\" element={<Navigate to=\"/data-cleaning\" replace />} />\n                    <Route path=\"/data-cleaning\" element={<DataCleaningPage />} />\n                    <Route path=\"/model-training\" element={<ModelTrainingPage />} />\n                    <Route path=\"/model-prediction\" element={<ModelPredictionPage />} />\n                    <Route path=\"/model-registry\" element={<ModelRegistryPage />} />\n                    <Route path=\"/clean-template\" element={<CleanTemplatePage />} />\n                    <Route path=\"/data-query\" element={<DataQueryPage />} />\n                    <Route path=\"/user-management\" element={<UserManagementPage />} />\n                    <Route path=\"/task-manager\" element={<TaskManagerPage />} />\n                  </Routes>\n                </Content>\n              </MainLayout>\n            </ProtectedRoute>\n          }\n        />\n      </Routes>\n    </div>\n  );\n};\n\nexport default App;\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport 'antd/dist/antd.css'; // 导入Ant Design样式\nimport { store } from './store/store';\nimport App from './App';\nimport './index.css';\n\nReactDOM.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <BrowserRouter>\n        <ConfigProvider locale={zhCN}>\n          <App />\n        </ConfigProvider>\n      </BrowserRouter>\n    </Provider>\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n"], "sourceRoot": ""}