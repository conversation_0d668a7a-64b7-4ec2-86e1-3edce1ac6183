#!/usr/bin/env python3
"""
修复JSON文件损坏的脚本
"""

import json
import os
import re
from datetime import datetime

def backup_files():
    """备份损坏的文件"""
    print("🔄 备份损坏的文件...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_files = []
    
    for filename in ["task_storage.json", "task_results.json"]:
        if os.path.exists(filename):
            backup_name = f"{filename}.corrupted_{timestamp}"
            os.rename(filename, backup_name)
            backup_files.append((filename, backup_name))
            print(f"   📁 {filename} → {backup_name}")
    
    return backup_files

def extract_valid_json_part(filename):
    """从损坏的JSON文件中提取有效部分"""
    print(f"🔍 分析损坏的JSON文件: {filename}")
    
    if not os.path.exists(filename):
        print(f"   ❌ 文件不存在")
        return {}
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   📊 文件大小: {len(content)} 字符")
        
        # 尝试找到最后一个完整的JSON对象
        # 从文件末尾开始，逐步减少内容，直到找到有效的JSON
        for i in range(len(content), 0, -100):  # 每次减少100字符
            try:
                # 尝试在当前位置添加闭合括号
                test_content = content[:i].rstrip()
                
                # 移除可能的不完整行
                lines = test_content.split('\n')
                while lines and not lines[-1].strip().endswith((',', '}', ']')):
                    lines.pop()
                
                if not lines:
                    continue
                
                # 重新组合内容
                test_content = '\n'.join(lines)
                
                # 确保JSON结构完整
                open_braces = test_content.count('{')
                close_braces = test_content.count('}')
                
                # 添加缺失的闭合括号
                missing_braces = open_braces - close_braces
                if missing_braces > 0:
                    # 移除最后的逗号（如果有）
                    test_content = test_content.rstrip().rstrip(',')
                    # 添加缺失的闭合括号
                    test_content += '\n' + '  }' * (missing_braces - 1) + '\n}'
                
                # 尝试解析JSON
                data = json.loads(test_content)
                print(f"   ✅ 找到有效JSON，包含 {len(data)} 个条目")
                return data
                
            except json.JSONDecodeError:
                continue
        
        print(f"   ❌ 无法恢复有效的JSON数据")
        return {}
        
    except Exception as e:
        print(f"   ❌ 读取文件失败: {e}")
        return {}

def create_clean_files():
    """创建干净的JSON文件"""
    print("🧹 创建干净的JSON文件...")
    
    # 尝试从备份文件中恢复数据
    recovered_tasks = {}
    recovered_results = {}
    
    # 检查是否有备份文件
    backup_files = [f for f in os.listdir('.') if f.startswith('task_') and 'corrupted_' in f]
    
    if backup_files:
        print("   📁 发现备份文件，尝试恢复数据...")
        
        for backup_file in backup_files:
            if 'task_storage' in backup_file:
                data = extract_valid_json_part(backup_file)
                if data:
                    recovered_tasks.update(data)
                    print(f"   ✅ 从 {backup_file} 恢复了 {len(data)} 个任务")
            
            elif 'task_results' in backup_file:
                data = extract_valid_json_part(backup_file)
                if data:
                    recovered_results.update(data)
                    print(f"   ✅ 从 {backup_file} 恢复了 {len(data)} 个结果")
    
    # 如果没有恢复到数据，创建空文件
    if not recovered_tasks:
        print("   📝 创建空的任务文件")
        recovered_tasks = {}
    
    if not recovered_results:
        print("   📝 创建空的结果文件")
        recovered_results = {}
    
    # 保存干净的文件
    try:
        with open("task_storage.json", 'w', encoding='utf-8') as f:
            json.dump(recovered_tasks, f, ensure_ascii=False, indent=2)
        
        with open("task_results.json", 'w', encoding='utf-8') as f:
            json.dump(recovered_results, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 创建干净的JSON文件成功")
        print(f"      task_storage.json: {len(recovered_tasks)} 个任务")
        print(f"      task_results.json: {len(recovered_results)} 个结果")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 创建文件失败: {e}")
        return False

def add_json_validation_to_backend():
    """为后端添加JSON验证"""
    print("🔧 为后端添加JSON验证...")
    
    validation_code = '''
def _validate_and_fix_json_file(self, filepath):
    """验证并修复JSON文件"""
    if not os.path.exists(filepath):
        return True
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            json.load(f)
        return True
    except json.JSONDecodeError as e:
        logger.error(f"JSON文件损坏: {filepath}, 错误: {e}")
        
        # 创建备份
        backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            os.rename(filepath, backup_path)
            logger.info(f"已备份损坏文件: {backup_path}")
        except Exception:
            pass
        
        # 创建空的有效JSON文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info(f"已创建新的空JSON文件: {filepath}")
            return True
        except Exception as e:
            logger.error(f"创建新JSON文件失败: {e}")
            return False
'''
    
    print("   💡 建议在TaskStorage类中添加以上验证方法")
    print("   💡 在_load_data和_save_*方法中调用此验证")

def test_json_files():
    """测试JSON文件的有效性"""
    print("🧪 测试JSON文件有效性...")
    
    files_to_test = ["task_storage.json", "task_results.json"]
    
    for filename in files_to_test:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   ✅ {filename}: 有效 ({len(data)} 个条目)")
            except json.JSONDecodeError as e:
                print(f"   ❌ {filename}: 无效 - {e}")
            except Exception as e:
                print(f"   ❌ {filename}: 错误 - {e}")
        else:
            print(f"   ⚠️  {filename}: 文件不存在")

def provide_prevention_tips():
    """提供预防建议"""
    print("💡 预防JSON损坏的建议:")
    print("=" * 60)
    
    print("🔧 代码层面:")
    print("   1. 使用原子写入（写入临时文件后重命名）")
    print("   2. 添加文件锁防止并发写入")
    print("   3. 写入前验证JSON格式")
    print("   4. 定期备份重要数据")
    
    print("\n🛠️  系统层面:")
    print("   1. 确保磁盘空间充足")
    print("   2. 避免在写入过程中强制终止程序")
    print("   3. 使用稳定的存储设备")
    print("   4. 定期检查文件系统完整性")
    
    print("\n📋 监控建议:")
    print("   1. 添加JSON文件完整性检查")
    print("   2. 记录文件写入操作日志")
    print("   3. 设置文件大小异常告警")
    print("   4. 定期验证数据完整性")

if __name__ == "__main__":
    print("🔧 JSON文件损坏修复工具")
    print("=" * 60)
    
    # 备份损坏的文件
    backup_info = backup_files()
    
    # 创建干净的文件
    if create_clean_files():
        # 测试新文件
        test_json_files()
        
        # 添加验证建议
        add_json_validation_to_backend()
        
        # 提供预防建议
        provide_prevention_tips()
        
        print(f"\n" + "=" * 60)
        print("✅ 修复完成")
        print(f"\n🎯 结果:")
        print("   ✅ 损坏文件已备份")
        print("   ✅ 创建了干净的JSON文件")
        print("   ✅ 系统可以正常运行")
        
        print(f"\n💡 下一步:")
        print("   1. 重启后端服务")
        print("   2. 测试训练和预测功能")
        print("   3. 考虑添加JSON验证机制")
        
    else:
        print(f"\n❌ 修复失败")
        print("请手动检查文件权限和磁盘空间")
