#!/usr/bin/env python3
"""
测试文件覆盖问题的脚本
"""

import os
import json
from datetime import datetime

def simulate_file_path_generation():
    """模拟文件路径生成逻辑"""
    
    def generate_file_paths(csv_filename, protocol, datatype, output_folder="/data/output"):
        """模拟文件路径生成"""
        base_filename = f"{csv_filename}_{protocol}_{datatype}"
        
        paths = {
            "model_path": os.path.join(output_folder, f"{base_filename}_model_best.pth"),
            "params_path": os.path.join(output_folder, f"{base_filename}_params.json"),
            "scaler_path": os.path.join(output_folder, f"{base_filename}_scaler_y_best.pkl"),
            "test_data_path": os.path.join(output_folder, f"{base_filename}_test.csv"),
            "predictions_path": os.path.join(output_folder, f"{base_filename}_predictions.csv")
        }
        
        return base_filename, paths
    
    print("🔍 测试文件路径生成和覆盖问题")
    print("=" * 60)
    
    # 模拟相同配置的两次训练
    csv_filename = "traffic_data"
    protocol = "TCP"
    datatype = "spt_sip_dip"
    
    print(f"📋 训练配置:")
    print(f"   CSV文件: {csv_filename}.csv")
    print(f"   协议: {protocol}")
    print(f"   数据类型: {datatype}")
    
    print(f"\n🚀 第一次训练:")
    base_filename_1, paths_1 = generate_file_paths(csv_filename, protocol, datatype)
    model_id_1 = f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"   模型ID: {model_id_1}")
    print(f"   基础文件名: {base_filename_1}")
    print(f"   模型文件: {paths_1['model_path']}")
    print(f"   参数文件: {paths_1['params_path']}")
    print(f"   测试数据: {paths_1['test_data_path']}")
    
    # 模拟时间间隔
    import time
    time.sleep(2)
    
    print(f"\n🚀 第二次训练:")
    base_filename_2, paths_2 = generate_file_paths(csv_filename, protocol, datatype)
    model_id_2 = f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"   模型ID: {model_id_2}")
    print(f"   基础文件名: {base_filename_2}")
    print(f"   模型文件: {paths_2['model_path']}")
    print(f"   参数文件: {paths_2['params_path']}")
    print(f"   测试数据: {paths_2['test_data_path']}")
    
    print(f"\n⚠️  文件覆盖分析:")
    print(f"   基础文件名相同: {'是' if base_filename_1 == base_filename_2 else '否'}")
    print(f"   模型文件路径相同: {'是' if paths_1['model_path'] == paths_2['model_path'] else '否'}")
    print(f"   参数文件路径相同: {'是' if paths_1['params_path'] == paths_2['params_path'] else '否'}")
    print(f"   测试数据路径相同: {'是' if paths_1['test_data_path'] == paths_2['test_data_path'] else '否'}")
    
    return model_id_1, model_id_2, paths_1, paths_2

def simulate_deletion_scenario():
    """模拟删除场景"""
    print(f"\n🔍 模拟删除场景...")
    print("=" * 60)
    
    # 模拟模型仓库记录
    model_1 = {
        "model_id": "model_20250724_100000",
        "created_time": "2025-07-24 10:00:00",
        "source_data": "traffic_data.csv",
        "protocol": "TCP",
        "datatype": "spt_sip_dip",
        "r2_score": 0.85,
        "file_paths": {
            "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_model_best.pth",
            "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_params.json",
            "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_scaler_y_best.pkl",
            "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_test.csv"
        }
    }
    
    model_2 = {
        "model_id": "model_20250724_100200",
        "created_time": "2025-07-24 10:02:00",
        "source_data": "traffic_data.csv",
        "protocol": "TCP",
        "datatype": "spt_sip_dip",
        "r2_score": 0.87,
        "file_paths": {
            "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_model_best.pth",  # 相同路径！
            "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_params.json",    # 相同路径！
            "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_scaler_y_best.pkl", # 相同路径！
            "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_test.csv"     # 相同路径！
        }
    }
    
    print(f"📊 模型仓库状态:")
    print(f"   模型1: {model_1['model_id']} (R²: {model_1['r2_score']}, 时间: {model_1['created_time']})")
    print(f"   模型2: {model_2['model_id']} (R²: {model_2['r2_score']}, 时间: {model_2['created_time']})")
    
    print(f"\n📁 文件系统状态:")
    print(f"   实际文件: {model_2['file_paths']['model_path']}")
    print(f"   文件内容: 第二次训练的模型（因为覆盖了第一次的）")
    
    print(f"\n❌ 删除第一个模型 ({model_1['model_id']}):")
    print(f"   1. 从模型仓库记录中删除模型1的条目")
    print(f"   2. 尝试删除模型1记录中的文件路径")
    print(f"   3. 实际删除的是: {model_1['file_paths']['model_path']}")
    print(f"   4. 但这个文件实际包含的是第二次训练的模型！")
    
    print(f"\n💥 问题结果:")
    print(f"   ✅ 模型1的记录被删除")
    print(f"   ❌ 模型2的实际文件被误删")
    print(f"   ❌ 模型2变成了'僵尸记录'（记录存在但文件不存在）")
    print(f"   ❌ 用户尝试使用模型2时会失败")

def analyze_real_registry():
    """分析真实模型仓库中的潜在问题"""
    print(f"\n🔍 分析真实模型仓库...")
    print("=" * 60)
    
    registry_file = "model_registry.json"
    
    if not os.path.exists(registry_file):
        print("❌ 模型仓库文件不存在")
        return
    
    try:
        with open(registry_file, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        models = registry.get("models", [])
        print(f"📊 模型总数: {len(models)}")
        
        # 分析文件路径冲突
        path_conflicts = {}
        for model in models:
            file_paths = model.get("file_paths", {})
            model_path = file_paths.get("model_path", "")
            
            if model_path:
                if model_path not in path_conflicts:
                    path_conflicts[model_path] = []
                path_conflicts[model_path].append({
                    "model_id": model.get("model_id", ""),
                    "created_time": model.get("created_time", ""),
                    "r2_score": model.get("r2_score", 0)
                })
        
        print(f"\n📋 文件路径冲突分析:")
        conflict_count = 0
        for path, model_list in path_conflicts.items():
            if len(model_list) > 1:
                conflict_count += 1
                print(f"\n⚠️  冲突路径: {path}")
                print(f"   关联模型数: {len(model_list)}")
                for i, model_info in enumerate(model_list, 1):
                    print(f"   模型 {i}: {model_info['model_id']} (R²: {model_info['r2_score']:.4f}, 时间: {model_info['created_time']})")
                
                # 分析哪个模型的文件可能被保留
                latest_model = max(model_list, key=lambda x: x['created_time'])
                print(f"   🎯 最新模型: {latest_model['model_id']} (文件可能包含此模型的数据)")
                print(f"   ⚠️  风险: 删除其他模型可能误删此文件")
        
        print(f"\n📊 冲突统计:")
        print(f"   总文件路径数: {len(path_conflicts)}")
        print(f"   冲突路径数: {conflict_count}")
        print(f"   冲突率: {conflict_count / len(path_conflicts) * 100:.1f}%" if len(path_conflicts) > 0 else "   冲突率: 0%")
        
        if conflict_count > 0:
            print(f"\n❌ 发现 {conflict_count} 个文件路径冲突！")
            print(f"   删除任何冲突模型都可能导致其他模型的文件被误删")
        else:
            print(f"\n✅ 未发现文件路径冲突")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 解决方案建议...")
    print("=" * 60)
    
    print(f"🔧 方案1: 文件名添加唯一标识符")
    print(f"   修改: base_filename = f\"{csv_filename}_{protocol}_{datatype}_{model_id}\"")
    print(f"   优点: 每个模型都有唯一的文件路径")
    print(f"   缺点: 需要修改现有代码")
    
    print(f"\n🔧 方案2: 文件名添加时间戳")
    print(f"   修改: base_filename = f\"{csv_filename}_{protocol}_{datatype}_{timestamp}\"")
    print(f"   优点: 简单实现，自然排序")
    print(f"   缺点: 文件名较长")
    
    print(f"\n🔧 方案3: 删除前检查文件引用")
    print(f"   修改: 删除前检查是否有其他模型引用相同文件")
    print(f"   优点: 保护现有数据")
    print(f"   缺点: 逻辑复杂")
    
    print(f"\n🔧 方案4: 使用模型ID作为目录")
    print(f"   修改: 每个模型使用独立目录 /data/output/{model_id}/")
    print(f"   优点: 完全隔离，易于管理")
    print(f"   缺点: 目录结构变化较大")
    
    print(f"\n🎯 推荐方案: 方案1 + 方案3")
    print(f"   1. 立即修复文件名生成，添加模型ID")
    print(f"   2. 增强删除逻辑，检查文件引用")
    print(f"   3. 为现有冲突模型添加警告标识")

if __name__ == "__main__":
    print("🔍 文件覆盖问题测试")
    print("=" * 60)
    
    simulate_file_path_generation()
    simulate_deletion_scenario()
    analyze_real_registry()
    suggest_solutions()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("\n🚨 严重问题确认:")
    print("1. ❌ 相同数据的多次训练会覆盖文件")
    print("2. ❌ 删除早期模型会误删后期模型的文件")
    print("3. ❌ 可能导致模型仓库中的'僵尸记录'")
    print("4. ❌ 用户可能无法使用看似正常的模型")
    print("\n🎯 需要立即修复此问题！")
