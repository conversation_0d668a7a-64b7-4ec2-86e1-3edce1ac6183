# 如何查看异步训练和预测结果

## 概述

当您使用异步模式进行模型训练或预测时，任务会在后台运行，您可以通过多种方式查看任务状态和结果。

## 查看方式

### 任务管理页面

访问路径: **侧边栏菜单 → 任务管理**

**功能特点**:
- **实时监控**: 自动每5秒刷新运行中任务的状态
- **任务统计**: 显示运行中、已完成、失败任务的数量统计
- **详细信息**: 查看每个任务的详细信息和结果
- **任务控制**: 取消运行中的任务
- **历史管理**: 查看和清理已完成的任务

## 任务状态说明

### 状态类型
- 🔵 **运行中**: 任务正在后台执行
- ✅ **已完成**: 任务成功完成
- ❌ **失败**: 任务执行过程中出现错误
- ⚠️ **已取消**: 任务被用户手动取消
- ⏳ **等待中**: 任务已提交但尚未开始执行

### 进度显示
- 运行中的任务会显示实时进度条
- 进度百分比和当前执行步骤
- 预计剩余时间（如果可用）

## 查看训练结果

### 训练完成后的结果包含:
1. **模型文件**: 保存在指定的输出目录中
   - `*_model_best.pth`: 训练好的模型文件
   - `*_params.json`: 模型参数配置
   - `*_scaler_y_best.pkl`: 数据标准化器

2. **训练指标**:
   - 训练损失曲线
   - 验证损失曲线
   - R²分数
   - 训练和测试数据集大小

3. **结果文件**:
   - `*_predictions.csv`: 预测结果文件
   - `*_test.csv`: 测试数据文件

### 查看方式:
1. 在任务管理页面点击已完成的训练任务
2. 点击"详情"按钮
3. 在任务详情中查看完整的结果信息

## 查看预测结果

### 预测完成后的结果包含:
1. **预测数据**: 
   - 原始流量数据
   - 预测的流量值
   - 异常检测阈值
   - 异常标记

2. **统计信息**:
   - 异常数据点数量
   - 建议的异常检测阈值
   - 预测准确性指标

3. **可视化数据**: 
   - 时间序列预测图表
   - 异常点标记
   - 阈值线显示

### 查看方式:
1. 在任务管理页面找到已完成的预测任务
2. 点击"详情"按钮查看预测结果
3. 结果以JSON格式显示，包含所有预测数据和统计信息

## 实用技巧

### 1. 任务监控
- 任务管理页面会自动刷新，无需手动刷新
- 可以同时运行多个任务，系统会自动管理资源
- 长时间运行的任务建议定期检查状态

### 2. 结果下载
- 训练和预测的结果文件会保存在服务器的指定目录中
- 可以通过文件管理功能下载结果文件
- 建议及时下载重要的结果文件

### 3. 错误处理
- 如果任务失败，在任务详情中会显示详细的错误信息
- 常见错误包括数据格式问题、参数配置错误、资源不足等
- 可以根据错误信息调整参数后重新提交任务

### 4. 性能优化
- 避免同时运行过多的大型任务
- 合理设置训练参数以平衡速度和效果
- 定期清理已完成的任务以释放存储空间

## 通知功能

### 任务完成通知
- 任务完成时会在页面右上角显示通知消息
- 通知包含任务类型、状态和简要结果
- 点击通知可以快速跳转到任务详情

### 浏览器通知
- 如果浏览器支持，可以接收桌面通知
- 即使切换到其他标签页也能收到任务完成提醒

## 常见问题

### Q: 为什么看不到任务结果？
A: 请检查：
1. 任务是否已经完成（状态为"已完成"）
2. 是否有权限查看该任务
3. 刷新页面或重新登录

### Q: 任务失败了怎么办？
A: 
1. 查看任务详情中的错误信息
2. 检查输入数据和参数配置
3. 根据错误提示调整后重新提交

### Q: 可以取消正在运行的任务吗？
A: 可以，在任务管理页面点击"取消"按钮即可停止任务

### Q: 任务结果保存多长时间？
A: 已完成的任务会保存在系统中，建议定期清理不需要的任务记录

## 总结

通过以上方式，您可以方便地监控和查看异步训练和预测任务的状态与结果。建议：

1. **及时查看**: 任务完成后及时查看结果
2. **定期清理**: 清理不需要的任务记录
3. **备份重要结果**: 下载并备份重要的模型和预测结果
4. **合理使用**: 避免同时运行过多任务影响系统性能

如有其他问题，请查看系统帮助文档或联系技术支持。
