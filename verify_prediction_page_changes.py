#!/usr/bin/env python3
"""
验证模型预测页面修改效果的脚本
"""

import os
import re

def check_removed_components():
    """检查已移除的组件"""
    print("🔍 检查已移除的组件")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查已移除的内容
        removed_items = [
            ("Table导入", r'import.*Table.*from'),
            ("predictionColumns定义", r'const predictionColumns\s*='),
            ("预测详情表格", r'预测详情.*前100条'),
            ("Table组件使用", r'<Table[^>]*dataSource.*predictions'),
            ("表格分页配置", r'pagination=\{[^}]*pageSize.*\}')
        ]
        
        print(f"📊 移除检查结果:")
        all_removed = True
        
        for item_name, pattern in removed_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ❌ {item_name}: 仍然存在")
                all_removed = False
            else:
                print(f"   ✅ {item_name}: 已移除")
        
        return all_removed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_remaining_components():
    """检查保留的组件"""
    print(f"\n🔍 检查保留的组件")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查应该保留的内容
        retained_items = [
            ("PredictionResultDisplay组件", r'const PredictionResultDisplay'),
            ("统计信息显示", r'<Statistic[^>]*title.*建议的流量清洗阈值'),
            ("图表展示", r'<LineChart'),
            ("异常数量统计", r'异常数量.*{result\.anomaly_count}'),
            ("模型名称显示", r'模型名称.*{result\.model_name}'),
            ("同步预测功能", r'同步预测'),
            ("异步预测结果", r'异步预测结果'),
            ("文件上传功能", r'<Upload')
        ]
        
        print(f"📊 保留检查结果:")
        all_retained = True
        
        for item_name, pattern in retained_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ✅ {item_name}: 已保留")
            else:
                print(f"   ❌ {item_name}: 可能被误删")
                all_retained = False
        
        return all_retained
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_file_size_reduction():
    """分析文件大小减少"""
    print(f"\n📊 分析文件大小变化")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计文件信息
        total_lines = len(content.split('\n'))
        total_chars = len(content)
        total_size = os.path.getsize(file_path)
        
        print(f"📋 当前文件状态:")
        print(f"   总行数: {total_lines}")
        print(f"   总字符数: {total_chars:,}")
        print(f"   文件大小: {total_size:,} 字节 ({total_size/1024:.1f} KB)")
        
        # 估算移除的内容
        estimated_removed_lines = 50  # 大约移除了50行（表格定义+表格组件）
        estimated_removed_chars = 2000  # 大约移除了2000字符
        
        print(f"\n📉 估算移除内容:")
        print(f"   移除行数: ~{estimated_removed_lines}")
        print(f"   移除字符数: ~{estimated_removed_chars:,}")
        print(f"   减少比例: ~{(estimated_removed_chars/total_chars)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_component_structure():
    """检查组件结构"""
    print(f"\n🏗️  检查组件结构")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析组件结构
        components = [
            ("PredictionResultDisplay", r'const PredictionResultDisplay[^{]*\{'),
            ("ModelPredictionPage", r'const ModelPredictionPage[^{]*\{'),
            ("统计信息区域", r'<Row gutter.*<Statistic'),
            ("图表区域", r'<ResponsiveContainer.*<LineChart'),
            ("标签页结构", r'<Tabs.*items=')
        ]
        
        print(f"🔧 组件结构分析:")
        
        for comp_name, pattern in components:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ✅ {comp_name}: 结构完整")
            else:
                print(f"   ⚠️  {comp_name}: 可能有问题")
        
        # 检查是否有语法错误的迹象
        syntax_issues = [
            ("未闭合的标签", r'<[^/>]*[^>]$'),
            ("多余的逗号", r',\s*[}\]]'),
            ("缺少分号", r'[^;{}\s]\s*\n\s*[a-zA-Z]')
        ]
        
        print(f"\n🔍 语法检查:")
        syntax_ok = True
        
        for issue_name, pattern in syntax_issues:
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                print(f"   ⚠️  可能的{issue_name}: {len(matches)}处")
                syntax_ok = False
            else:
                print(f"   ✅ {issue_name}: 无问题")
        
        return syntax_ok
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def provide_testing_instructions():
    """提供测试说明"""
    print(f"\n🧪 测试说明")
    print("=" * 60)
    
    print(f"📋 前端测试步骤:")
    print(f"1. 启动前端开发服务器")
    print(f"   cd frontend-react-stable")
    print(f"   npm start 或 yarn start")
    print(f"")
    print(f"2. 打开模型预测页面")
    print(f"   访问: http://localhost:3000/prediction")
    print(f"")
    print(f"3. 验证修改效果")
    print(f"   ✅ 应该看到: 统计信息、图表展示")
    print(f"   ❌ 不应该看到: 预测详情表格")
    print(f"")
    print(f"4. 测试功能完整性")
    print(f"   • 同步预测功能正常")
    print(f"   • 异步预测结果显示正常")
    print(f"   • 图表渲染正确")
    print(f"   • 统计信息准确")
    
    print(f"\n🎯 预期效果:")
    print(f"   ✅ 页面更简洁，加载更快")
    print(f"   ✅ 保留核心功能（图表、统计）")
    print(f"   ✅ 移除冗余信息（详细表格）")
    print(f"   ✅ 用户体验更好")

def create_rollback_instructions():
    """创建回滚说明"""
    print(f"\n🔄 回滚说明")
    print("=" * 60)
    
    print(f"如果需要恢复预测详情表格，请:")
    print(f"")
    print(f"1. 恢复Table导入")
    print(f"   在antd导入中添加: Table")
    print(f"")
    print(f"2. 恢复predictionColumns定义")
    print(f"   在PredictionResultDisplay组件中添加表格列定义")
    print(f"")
    print(f"3. 恢复表格组件")
    print(f"   在图表下方添加:")
    print(f"   <Table")
    print(f"     columns={{predictionColumns}}")
    print(f"     dataSource={{result.predictions.slice(0, 100)}}")
    print(f"     pagination={{...}}")
    print(f"   />")

if __name__ == "__main__":
    print("🔍 模型预测页面修改验证")
    print("=" * 60)
    
    # 检查移除的组件
    removed_ok = check_removed_components()
    
    # 检查保留的组件
    retained_ok = check_remaining_components()
    
    # 分析文件大小变化
    size_analyzed = analyze_file_size_reduction()
    
    # 检查组件结构
    structure_ok = check_component_structure()
    
    # 提供测试说明
    provide_testing_instructions()
    
    # 创建回滚说明
    create_rollback_instructions()
    
    print(f"\n" + "=" * 60)
    print("✅ 验证完成")
    
    print(f"\n🎯 修改总结:")
    print(f"   移除检查: {'✅ 成功' if removed_ok else '❌ 有问题'}")
    print(f"   保留检查: {'✅ 成功' if retained_ok else '❌ 有问题'}")
    print(f"   结构检查: {'✅ 正常' if structure_ok else '⚠️ 需要检查'}")
    
    if removed_ok and retained_ok and structure_ok:
        print(f"\n🎉 修改成功!")
        print("   ✅ 预测详情表格已移除")
        print("   ✅ 核心功能保持完整")
        print("   ✅ 页面结构正常")
        print("   ✅ 可以进行前端测试")
    else:
        print(f"\n⚠️  需要进一步检查:")
        if not removed_ok:
            print("   - 某些组件可能未完全移除")
        if not retained_ok:
            print("   - 某些重要组件可能被误删")
        if not structure_ok:
            print("   - 组件结构可能有问题")
    
    print(f"\n💡 下一步:")
    print("   1. 启动前端开发服务器")
    print("   2. 测试模型预测页面")
    print("   3. 验证功能完整性")
    print("   4. 确认用户体验改善")
