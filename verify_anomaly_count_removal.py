#!/usr/bin/env python3
"""
验证异常点数量移除效果的脚本
"""

import os
import re

def check_removed_anomaly_display():
    """检查已移除的异常点数量显示"""
    print("🔍 检查已移除的异常点数量显示")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查已移除的异常点数量显示相关内容
        removed_items = [
            ("异常点数量标题", r'检测到的异常点数量'),
            ("异常点数量Statistic组件", r'<Statistic[^>]*title.*异常点数量'),
            ("异常点数量值显示", r'value=\{result\.anomaly_count\}'),
            ("异常点数量颜色样式", r'result\.anomaly_count\s*>\s*0.*#ff4d4f.*#52c41a'),
            ("异常点数量说明文本", r'基于动态阈值检测出的.*时间点数量'),
            ("异常点数量描述", r'流量超过阈值的具体时间点数量')
        ]
        
        print(f"📊 异常点数量显示移除检查:")
        all_removed = True
        
        for item_name, pattern in removed_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ❌ {item_name}: 仍然存在")
                all_removed = False
            else:
                print(f"   ✅ {item_name}: 已移除")
        
        return all_removed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_layout_adjustments():
    """检查布局调整"""
    print(f"\n🔍 检查布局调整")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局调整
        layout_checks = [
            ("建议阈值列宽度", r'<Col span=\{24\}', True),  # 应该是span=24
            ("异常点数量列", r'<Col span=\{12\}.*异常点', False),  # 不应该存在
            ("Row结构保持", r'<Row gutter=\{16\}', True),  # 应该保持
        ]
        
        print(f"📐 布局调整检查:")
        layout_ok = True
        
        for check_name, pattern, should_exist in layout_checks:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            found = len(matches) > 0
            
            if should_exist and found:
                print(f"   ✅ {check_name}: 正确调整")
            elif not should_exist and not found:
                print(f"   ✅ {check_name}: 正确移除")
            else:
                print(f"   ❌ {check_name}: {'未找到' if should_exist else '仍然存在'}")
                layout_ok = False
        
        return layout_ok
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_remaining_components():
    """检查保留的组件"""
    print(f"\n🔍 检查保留的组件")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查应该保留的内容
        retained_items = [
            ("PredictionResultDisplay组件", r'const PredictionResultDisplay'),
            ("建议阈值统计", r'建议的流量清洗阈值'),
            ("阈值数值显示", r'value=\{result\.suggested_threshold\}'),
            ("阈值保存提示", r'此阈值已自动保存'),
            ("资源监控信息", r'资源使用情况'),
            ("预测耗时统计", r'预测耗时'),
            ("CPU使用率统计", r'CPU使用率'),
            ("内存使用统计", r'内存使用'),
            ("同步预测功能", r'同步预测'),
            ("异步预测结果", r'异步预测结果'),
            ("文件上传功能", r'<Upload'),
            ("模型选择功能", r'选择模型')
        ]
        
        print(f"📊 保留组件检查:")
        all_retained = True
        
        for item_name, pattern in retained_items:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                print(f"   ✅ {item_name}: 已保留")
            else:
                print(f"   ❌ {item_name}: 可能被误删")
                all_retained = False
        
        return all_retained
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_interface_compatibility():
    """检查接口兼容性"""
    print(f"\n🔍 检查接口兼容性")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查接口兼容性
        interface_checks = [
            ("PredictionResult接口", r'interface PredictionResult'),
            ("anomaly_count字段保留", r'anomaly_count:\s*number'),
            ("异步结果转换", r'anomaly_count:\s*selectedTask\.result\.anomaly_count'),
            ("同步结果转换", r'anomaly_count:\s*response\.data\.anomaly_count'),
        ]
        
        print(f"🔌 接口兼容性检查:")
        interface_ok = True
        
        for check_name, pattern in interface_checks:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"   ✅ {check_name}: 保持兼容")
            else:
                print(f"   ❌ {check_name}: 可能有问题")
                interface_ok = False
        
        return interface_ok
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_ui_improvements():
    """分析UI改进效果"""
    print(f"\n📊 分析UI改进效果")
    print("=" * 60)
    
    print(f"🎯 UI改进效果:")
    print(f"   ✅ 界面更简洁 - 移除了异常点数量显示")
    print(f"   ✅ 信息更聚焦 - 突出建议阈值这一关键信息")
    print(f"   ✅ 布局更平衡 - 建议阈值占满整行，视觉更突出")
    print(f"   ✅ 减少认知负担 - 用户无需关注异常点数量")
    
    print(f"\n📱 用户体验提升:")
    print(f"   ✅ 关键信息突出 - 建议阈值是最重要的输出")
    print(f"   ✅ 界面清爽 - 减少不必要的数据展示")
    print(f"   ✅ 操作更直观 - 用户专注于阈值设置")
    print(f"   ✅ 视觉层次清晰 - 主要信息更加醒目")

def provide_testing_instructions():
    """提供测试说明"""
    print(f"\n🧪 测试说明")
    print("=" * 60)
    
    print(f"📋 前端测试步骤:")
    print(f"1. 启动前端开发服务器")
    print(f"   cd frontend-react-stable")
    print(f"   npm start 或 yarn start")
    print(f"")
    print(f"2. 打开模型预测页面")
    print(f"   访问: http://localhost:3000/prediction")
    print(f"")
    print(f"3. 验证修改效果")
    print(f"   ✅ 应该看到: 建议阈值（占满整行）")
    print(f"   ❌ 不应该看到: 异常点数量统计")
    print(f"")
    print(f"4. 测试功能完整性")
    print(f"   • 同步预测功能正常")
    print(f"   • 异步预测结果显示正常")
    print(f"   • 建议阈值正确显示")
    print(f"   • 阈值保存提示正常")
    print(f"   • 资源监控信息正常")
    
    print(f"\n🎯 预期界面效果:")
    print(f"   📊 建议阈值区域:")
    print(f"      - 占据整行宽度")
    print(f"      - 数值清晰显示")
    print(f"      - 成功提示正常")
    print(f"   📈 资源监控区域:")
    print(f"      - 预测耗时、CPU、内存等信息")
    print(f"      - 布局保持不变")

def create_rollback_instructions():
    """创建回滚说明"""
    print(f"\n🔄 回滚说明")
    print("=" * 60)
    
    print(f"如果需要恢复异常点数量显示，请:")
    print(f"")
    print(f"1. 恢复Col布局")
    print(f"   将建议阈值的 span 从 24 改回 12")
    print(f"")
    print(f"2. 添加异常点数量Col")
    print(f"   <Col span={{12}}>")
    print(f"     <Statistic")
    print(f"       title=\"检测到的异常点数量\"")
    print(f"       value={{result.anomaly_count}}")
    print(f"       valueStyle={{{{ color: result.anomaly_count > 0 ? '#ff4d4f' : '#52c41a' }}}}")
    print(f"     />")
    print(f"     <Text type=\"secondary\">说明文本</Text>")
    print(f"   </Col>")

if __name__ == "__main__":
    print("🔍 异常点数量移除效果验证")
    print("=" * 60)
    
    # 检查移除的异常点数量显示
    removed_ok = check_removed_anomaly_display()
    
    # 检查布局调整
    layout_ok = check_layout_adjustments()
    
    # 检查保留的组件
    retained_ok = check_remaining_components()
    
    # 检查接口兼容性
    interface_ok = check_interface_compatibility()
    
    # 分析UI改进效果
    analyze_ui_improvements()
    
    # 提供测试说明
    provide_testing_instructions()
    
    # 创建回滚说明
    create_rollback_instructions()
    
    print(f"\n" + "=" * 60)
    print("✅ 验证完成")
    
    print(f"\n🎯 修改总结:")
    print(f"   异常点移除: {'✅ 成功' if removed_ok else '❌ 有问题'}")
    print(f"   布局调整: {'✅ 成功' if layout_ok else '❌ 有问题'}")
    print(f"   组件保留: {'✅ 成功' if retained_ok else '❌ 有问题'}")
    print(f"   接口兼容: {'✅ 正常' if interface_ok else '⚠️ 需要检查'}")
    
    if removed_ok and layout_ok and retained_ok and interface_ok:
        print(f"\n🎉 修改成功!")
        print("   ✅ 异常点数量显示已完全移除")
        print("   ✅ 建议阈值布局已优化（占满整行）")
        print("   ✅ 核心功能保持完整")
        print("   ✅ 接口保持兼容")
        print("   ✅ 界面更加简洁聚焦")
        print("   ✅ 可以进行前端测试")
    else:
        print(f"\n⚠️  需要进一步检查:")
        if not removed_ok:
            print("   - 异常点数量显示可能未完全移除")
        if not layout_ok:
            print("   - 布局调整可能有问题")
        if not retained_ok:
            print("   - 某些重要组件可能被误删")
        if not interface_ok:
            print("   - 接口兼容性可能有问题")
    
    print(f"\n💡 下一步:")
    print("   1. 启动前端开发服务器")
    print("   2. 测试模型预测页面")
    print("   3. 验证建议阈值显示")
    print("   4. 确认界面简洁效果")
