#!/usr/bin/env python3
"""
简化预测结果JSON字段的脚本
"""

import json
import os
from datetime import datetime

def analyze_current_fields():
    """分析当前的字段结构"""
    print("🔍 分析当前预测结果字段结构")
    print("=" * 60)
    
    try:
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 找到预测结果
        prediction_results = {}
        for task_id, result in results.items():
            if 'predictions' in result:
                prediction_results[task_id] = result
        
        print(f"📊 找到 {len(prediction_results)} 个预测结果")
        
        if prediction_results:
            # 分析第一个预测结果的字段
            sample_task_id = list(prediction_results.keys())[0]
            sample_result = prediction_results[sample_task_id]
            
            print(f"\n📋 当前字段结构 (示例: {sample_task_id[:30]}...):")
            
            # 顶级字段
            top_level_fields = list(sample_result.keys())
            print(f"   顶级字段: {top_level_fields}")
            
            # predictions数组中的字段
            if sample_result.get('predictions'):
                prediction_fields = list(sample_result['predictions'][0].keys())
                print(f"   预测数据字段: {prediction_fields}")
                print(f"   预测数据点数量: {len(sample_result['predictions'])}")
            
            # 分析字段使用情况
            print(f"\n📊 字段使用分析:")
            field_usage = {}
            for result in prediction_results.values():
                for field in result.keys():
                    field_usage[field] = field_usage.get(field, 0) + 1
            
            for field, count in sorted(field_usage.items()):
                usage_rate = (count / len(prediction_results)) * 100
                print(f"   {field}: {count}/{len(prediction_results)} ({usage_rate:.1f}%)")
        
        return prediction_results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return {}

def identify_required_fields():
    """识别前端实际需要的字段"""
    print(f"\n🎯 识别前端实际需要的字段")
    print("=" * 60)
    
    # 基于简化后的前端界面，确定需要的字段
    required_fields = {
        "顶级字段": {
            "suggested_threshold": "建议阈值 - 核心输出",
            "model_name": "模型名称 - 用于显示",
            "message": "预测消息 - 状态信息",
            "duration_seconds": "预测耗时 - 性能监控",
            "cpu_percent": "CPU使用率 - 性能监控",
            "memory_mb": "内存使用 - 性能监控"
        },
        "可选字段": {
            "gpu_memory_mb": "GPU内存 - 如果使用GPU",
            "gpu_utilization_percent": "GPU利用率 - 如果使用GPU"
        },
        "不再需要的字段": {
            "predictions": "预测数据数组 - 前端不再显示图表和详情",
            "anomaly_count": "异常数量 - 前端不再显示"
        }
    }
    
    print(f"✅ 必需字段:")
    for field, desc in required_fields["顶级字段"].items():
        print(f"   • {field}: {desc}")
    
    print(f"\n🔧 可选字段:")
    for field, desc in required_fields["可选字段"].items():
        print(f"   • {field}: {desc}")
    
    print(f"\n❌ 可以移除的字段:")
    for field, desc in required_fields["不再需要的字段"].items():
        print(f"   • {field}: {desc}")
    
    return required_fields

def create_simplified_structure():
    """创建简化的数据结构"""
    print(f"\n🔧 创建简化的数据结构")
    print("=" * 60)
    
    # 简化后的结构示例
    simplified_structure = {
        "suggested_threshold": 1800.0,
        "model_name": "TCP_spt_sip_dip",
        "message": "预测成功",
        "duration_seconds": 120.5,
        "cpu_percent": 15.2,
        "memory_mb": 256.8,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    print(f"📋 简化后的结构:")
    print(json.dumps(simplified_structure, indent=2, ensure_ascii=False))
    
    # 计算大小减少
    original_example = {
        "predictions": [
            {
                "timestamp": "2025-07-24 14:00:00",
                "packets_per_sec": 1200,
                "packets_per_sec_smooth": 1180,
                "pred_smooth": 1220,
                "threshold": 1800,
                "is_anomaly": False
            }
        ] * 100,  # 假设100个数据点
        "anomaly_count": 5,
        "suggested_threshold": 1800.0,
        "model_name": "TCP_spt_sip_dip",
        "message": "预测成功",
        "duration_seconds": 120.5,
        "cpu_percent": 15.2,
        "memory_mb": 256.8,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    original_size = len(json.dumps(original_example))
    simplified_size = len(json.dumps(simplified_structure))
    reduction_percent = ((original_size - simplified_size) / original_size) * 100
    
    print(f"\n📊 大小对比 (100个预测数据点):")
    print(f"   原始大小: ~{original_size:,} 字符")
    print(f"   简化大小: ~{simplified_size:,} 字符")
    print(f"   减少: ~{reduction_percent:.1f}%")
    
    return simplified_structure

def create_migration_script():
    """创建迁移脚本"""
    print(f"\n🔄 创建数据迁移方案")
    print("=" * 60)
    
    migration_steps = [
        {
            "步骤": "1. 备份现有数据",
            "操作": "创建task_results.json的备份文件"
        },
        {
            "步骤": "2. 简化现有结果",
            "操作": "移除predictions和anomaly_count字段"
        },
        {
            "步骤": "3. 更新后端代码",
            "操作": "修改异步预测结果保存逻辑"
        },
        {
            "步骤": "4. 验证前端兼容性",
            "操作": "确保前端能正确处理简化后的数据"
        }
    ]
    
    print(f"📋 迁移步骤:")
    for step in migration_steps:
        print(f"   {step['步骤']}: {step['操作']}")
    
    return migration_steps

def simplify_existing_results():
    """简化现有的预测结果"""
    print(f"\n🔧 简化现有预测结果")
    print("=" * 60)
    
    try:
        # 备份原文件
        backup_name = f"task_results_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        if os.path.exists("task_results.json"):
            os.rename("task_results.json", backup_name)
            print(f"📁 已备份原文件: {backup_name}")
        
        # 读取备份文件
        with open(backup_name, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 简化预测结果
        simplified_results = {}
        prediction_count = 0
        
        for task_id, result in results.items():
            if 'predictions' in result:
                # 这是预测结果，进行简化
                simplified_result = {
                    "suggested_threshold": result.get("suggested_threshold", 0),
                    "model_name": result.get("model_name", "未知模型"),
                    "message": result.get("message", "预测完成"),
                    "duration_seconds": result.get("duration_seconds"),
                    "cpu_percent": result.get("cpu_percent"),
                    "memory_mb": result.get("memory_mb"),
                    "gpu_memory_mb": result.get("gpu_memory_mb", 0),
                    "gpu_utilization_percent": result.get("gpu_utilization_percent", 0)
                }
                
                # 移除None值
                simplified_result = {k: v for k, v in simplified_result.items() if v is not None}
                
                simplified_results[task_id] = simplified_result
                prediction_count += 1
                
                print(f"   ✅ 简化预测任务: {task_id[:30]}...")
            else:
                # 保持其他类型的结果不变
                simplified_results[task_id] = result
        
        # 保存简化后的结果
        with open("task_results.json", 'w', encoding='utf-8') as f:
            json.dump(simplified_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 简化完成:")
        print(f"   简化的预测任务: {prediction_count}")
        print(f"   保持不变的任务: {len(results) - prediction_count}")
        print(f"   备份文件: {backup_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化失败: {e}")
        return False

def update_backend_code_suggestions():
    """提供后端代码更新建议"""
    print(f"\n💻 后端代码更新建议")
    print("=" * 60)
    
    suggestions = [
        {
            "文件": "backend/model_prediction.py",
            "修改": "_predict_single_model函数的返回结果",
            "说明": "移除predictions数组和anomaly_count字段"
        },
        {
            "文件": "frontend-react-stable/src/pages/ModelPredictionPage.tsx",
            "修改": "PredictionResult接口定义",
            "说明": "移除不再使用的字段定义"
        }
    ]
    
    print(f"📋 建议的代码修改:")
    for suggestion in suggestions:
        print(f"   📄 {suggestion['文件']}")
        print(f"      修改: {suggestion['修改']}")
        print(f"      说明: {suggestion['说明']}")
        print()
    
    # 提供具体的代码示例
    print(f"🔧 后端返回结果简化示例:")
    print(f"   原始返回:")
    print(f"   {{")
    print(f"     'predictions': [...],  # 移除")
    print(f"     'anomaly_count': 5,    # 移除")
    print(f"     'suggested_threshold': 1800,")
    print(f"     'model_name': 'TCP_spt_sip_dip',")
    print(f"     'message': '预测成功',")
    print(f"     'duration_seconds': 120.5")
    print(f"   }}")
    print(f"")
    print(f"   简化后返回:")
    print(f"   {{")
    print(f"     'suggested_threshold': 1800,")
    print(f"     'model_name': 'TCP_spt_sip_dip',")
    print(f"     'message': '预测成功',")
    print(f"     'duration_seconds': 120.5,")
    print(f"     'cpu_percent': 15.2,")
    print(f"     'memory_mb': 256.8")
    print(f"   }}")

def provide_benefits_analysis():
    """提供优化效益分析"""
    print(f"\n📈 优化效益分析")
    print("=" * 60)
    
    benefits = [
        {
            "类别": "存储优化",
            "效益": [
                "JSON文件大小减少80-90%",
                "磁盘空间使用减少",
                "备份文件更小"
            ]
        },
        {
            "类别": "网络传输",
            "效益": [
                "API响应大小显著减少",
                "网络传输时间缩短",
                "移动端体验改善"
            ]
        },
        {
            "类别": "前端性能",
            "效益": [
                "JSON解析速度提升",
                "内存使用减少",
                "页面加载更快"
            ]
        },
        {
            "类别": "维护性",
            "效益": [
                "数据结构更简洁",
                "调试更容易",
                "代码更易维护"
            ]
        }
    ]
    
    for benefit in benefits:
        print(f"🎯 {benefit['类别']}:")
        for item in benefit['效益']:
            print(f"   ✅ {item}")
        print()

if __name__ == "__main__":
    print("🔧 预测结果JSON字段简化工具")
    print("=" * 60)
    
    # 分析当前字段结构
    current_results = analyze_current_fields()
    
    # 识别需要的字段
    required_fields = identify_required_fields()
    
    # 创建简化结构
    simplified_structure = create_simplified_structure()
    
    # 创建迁移方案
    migration_steps = create_migration_script()
    
    # 执行简化
    if current_results:
        print(f"\n🚀 执行简化操作...")
        if simplify_existing_results():
            print(f"✅ 现有数据简化成功")
        else:
            print(f"❌ 数据简化失败")
    
    # 提供代码更新建议
    update_backend_code_suggestions()
    
    # 效益分析
    provide_benefits_analysis()
    
    print(f"\n" + "=" * 60)
    print("✅ 字段简化分析完成")
    
    print(f"\n🎯 总结:")
    print("   ✅ 移除了predictions数组（大幅减少数据量）")
    print("   ✅ 移除了anomaly_count字段（前端不再显示）")
    print("   ✅ 保留了核心字段（阈值、模型名、性能信息）")
    print("   ✅ 现有数据已简化")
    
    print(f"\n💡 下一步:")
    print("   1. 重启后端服务")
    print("   2. 测试异步预测功能")
    print("   3. 验证前端显示正常")
    print("   4. 考虑更新后端代码以返回简化结果")
