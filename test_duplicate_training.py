#!/usr/bin/env python3
"""
测试重复训练的模型注册行为
"""

import json
import os
from datetime import datetime

def simulate_model_registration():
    """模拟模型注册过程"""
    
    def generate_model_id():
        """模拟模型ID生成"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"model_{timestamp}"
    
    def register_model(model_info, registry):
        """模拟模型注册"""
        model_id = generate_model_id()
        
        model_entry = {
            "model_id": model_id,
            "created_by": "test_user",
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "source_data": model_info.get("data_file", ""),
            "protocol": model_info.get("protocol", ""),
            "datatype": model_info.get("data_type", ""),
            "r2_score": model_info.get("r2_score", 0.0),
            "training_duration": model_info.get("duration_seconds", 0),
            "model_architecture": {
                "type": "GRU",
                "hidden_size": model_info.get("hidden_size", 50),
                "num_layers": model_info.get("num_layers", 2)
            },
            "training_params": {
                "learning_rate": model_info.get("learning_rate", 0.001),
                "batch_size": model_info.get("batch_size", 32),
                "epochs": model_info.get("epochs", 100)
            }
        }
        
        registry["models"].append(model_entry)
        return model_id
    
    # 初始化模拟仓库
    registry = {"models": []}
    
    # 模拟相同数据的训练信息
    same_data_model_info = {
        "data_file": "traffic_data.csv",
        "protocol": "TCP",
        "data_type": "spt_sip_dip",
        "r2_score": 0.85,
        "duration_seconds": 120,
        "hidden_size": 50,
        "num_layers": 2,
        "learning_rate": 0.001,
        "batch_size": 32,
        "epochs": 100
    }
    
    print("🔍 测试重复训练的模型注册行为")
    print("=" * 60)
    
    print("📋 训练配置:")
    print(f"   数据文件: {same_data_model_info['data_file']}")
    print(f"   协议: {same_data_model_info['protocol']}")
    print(f"   数据类型: {same_data_model_info['data_type']}")
    print(f"   学习率: {same_data_model_info['learning_rate']}")
    print(f"   批次大小: {same_data_model_info['batch_size']}")
    print(f"   训练轮数: {same_data_model_info['epochs']}")
    
    print(f"\n🚀 开始模拟训练...")
    
    # 模拟多次训练
    model_ids = []
    for i in range(3):
        print(f"\n📊 第 {i+1} 次训练:")
        
        # 模拟训练过程（稍微不同的结果）
        model_info = same_data_model_info.copy()
        model_info["r2_score"] = 0.85 + (i * 0.01)  # 稍微不同的R²分数
        model_info["duration_seconds"] = 120 + (i * 5)  # 稍微不同的训练时长
        
        # 注册模型
        model_id = register_model(model_info, registry)
        model_ids.append(model_id)
        
        print(f"   生成模型ID: {model_id}")
        print(f"   R²分数: {model_info['r2_score']}")
        print(f"   训练时长: {model_info['duration_seconds']}秒")
        
        # 模拟时间间隔
        import time
        time.sleep(1)
    
    print(f"\n📊 注册结果统计:")
    print(f"   训练次数: 3")
    print(f"   注册模型数: {len(registry['models'])}")
    print(f"   模型ID列表: {model_ids}")
    
    print(f"\n🔍 模型仓库内容:")
    for i, model in enumerate(registry["models"], 1):
        print(f"   模型 {i}:")
        print(f"     ID: {model['model_id']}")
        print(f"     数据文件: {model['source_data']}")
        print(f"     协议: {model['protocol']}")
        print(f"     数据类型: {model['datatype']}")
        print(f"     R²分数: {model['r2_score']}")
        print(f"     创建时间: {model['created_time']}")
    
    return registry

def analyze_duplicate_behavior():
    """分析重复训练的行为"""
    print("\n🔍 分析重复训练行为...")
    print("=" * 60)
    
    print("📋 当前模型注册逻辑:")
    print("1. ✅ 每次训练都生成新的模型ID")
    print("2. ✅ 模型ID基于时间戳 (model_YYYYMMDD_HHMMSS)")
    print("3. ❌ 没有重复检查逻辑")
    print("4. ❌ 没有相同配置检测")
    print("5. ❌ 没有模型去重功能")
    
    print(f"\n📊 重复训练的结果:")
    print("• 相同数据 + 相同参数 → 产生多个模型记录")
    print("• 每个模型都有独立的ID和创建时间")
    print("• 模型仓库中会有多个几乎相同的模型")
    print("• 用户需要手动识别和管理重复模型")
    
    print(f"\n⚠️  潜在问题:")
    print("1. 模型仓库可能被重复模型填满")
    print("2. 用户难以区分不同训练的模型")
    print("3. 存储空间浪费")
    print("4. 模型管理复杂化")
    
    print(f"\n💡 可能的改进方案:")
    print("1. 添加重复检查逻辑")
    print("2. 基于配置生成唯一标识")
    print("3. 提供模型版本管理")
    print("4. 添加模型去重功能")

def check_real_model_registry():
    """检查真实的模型仓库"""
    print("\n🔍 检查真实模型仓库...")
    print("=" * 60)
    
    registry_file = "backend/model_registry.json"
    
    if not os.path.exists(registry_file):
        print("❌ 模型仓库文件不存在")
        return
    
    try:
        with open(registry_file, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        models = registry.get("models", [])
        print(f"📊 当前模型总数: {len(models)}")
        
        if len(models) == 0:
            print("ℹ️  暂无模型数据")
            return
        
        # 分析重复模型
        duplicates = {}
        for model in models:
            key = (
                model.get("source_data", ""),
                model.get("protocol", ""),
                model.get("datatype", "")
            )
            if key not in duplicates:
                duplicates[key] = []
            duplicates[key].append(model)
        
        print(f"\n📋 重复模型分析:")
        duplicate_count = 0
        for key, model_list in duplicates.items():
            if len(model_list) > 1:
                duplicate_count += len(model_list) - 1
                data_file, protocol, datatype = key
                print(f"\n🔸 重复组合: {data_file} + {protocol} + {datatype}")
                print(f"   模型数量: {len(model_list)}")
                for i, model in enumerate(model_list, 1):
                    print(f"   模型 {i}: {model.get('model_id', 'N/A')} (R²: {model.get('r2_score', 'N/A')})")
        
        print(f"\n📊 统计结果:")
        print(f"   总模型数: {len(models)}")
        print(f"   唯一组合数: {len(duplicates)}")
        print(f"   重复模型数: {duplicate_count}")
        print(f"   重复率: {duplicate_count / len(models) * 100:.1f}%" if len(models) > 0 else "   重复率: 0%")
        
    except Exception as e:
        print(f"❌ 读取模型仓库失败: {e}")

if __name__ == "__main__":
    print("🔍 重复训练模型注册测试")
    print("=" * 60)
    
    # 模拟测试
    simulate_model_registration()
    
    # 分析行为
    analyze_duplicate_behavior()
    
    # 检查真实仓库
    check_real_model_registry()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("\n🎯 结论:")
    print("用同样的数据训练模型两次，会在模型仓库中产生 **2次结果**")
    print("每次训练都会生成独立的模型记录，没有重复检查机制。")
