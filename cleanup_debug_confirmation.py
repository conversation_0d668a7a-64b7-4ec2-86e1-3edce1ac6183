#!/usr/bin/env python3
"""
确认调试代码清理的脚本
"""

import os
import re

def check_debug_code_removal():
    """检查调试代码是否已删除"""
    print("🔍 检查前端调试代码清理情况")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有console.log
        console_logs = re.findall(r'console\.log\([^)]*\)', content)
        
        # 检查是否还有调试相关的注释或代码
        debug_patterns = [
            r'🔍.*获取已完成任务',
            r'📋.*已完成预测任务',
            r'🎯.*选择异步预测任务',
            r'📊.*任务结果',
            r'✅.*转换后的异步预测结果',
            r'🎨.*异步预测结果状态',
            r'❌.*任务结果为空',
            r'🔄.*检查是否需要自动选择'
        ]
        
        debug_matches = []
        for pattern in debug_patterns:
            matches = re.findall(pattern, content)
            debug_matches.extend(matches)
        
        print(f"📊 检查结果:")
        print(f"   文件大小: {len(content)} 字符")
        print(f"   console.log 数量: {len(console_logs)}")
        print(f"   调试信息数量: {len(debug_matches)}")
        
        if console_logs:
            print(f"\n⚠️  发现 console.log:")
            for i, log in enumerate(console_logs, 1):
                print(f"   {i}. {log}")
        
        if debug_matches:
            print(f"\n⚠️  发现调试信息:")
            for i, match in enumerate(debug_matches, 1):
                print(f"   {i}. {match}")
        
        if not console_logs and not debug_matches:
            print(f"\n✅ 调试代码已完全清理")
            return True
        else:
            print(f"\n❌ 仍有调试代码残留")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def verify_functionality_intact():
    """验证功能完整性"""
    print(f"\n🔍 验证功能完整性...")
    print("=" * 60)
    
    file_path = "frontend-react-stable/src/pages/ModelPredictionPage.tsx"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能是否保留
        key_functions = [
            'fetchCompletedTasks',
            'handleAsyncTaskSelect',
            'setSelectedAsyncTaskId',
            'setAsyncPredictionResults',
            'PredictionResult',
            'completedPredictionTasks',
            'asyncPredictionResults'
        ]
        
        missing_functions = []
        for func in key_functions:
            if func not in content:
                missing_functions.append(func)
        
        print(f"📋 功能检查:")
        print(f"   检查项目数: {len(key_functions)}")
        print(f"   缺失项目数: {len(missing_functions)}")
        
        if missing_functions:
            print(f"\n❌ 缺失的功能:")
            for func in missing_functions:
                print(f"   - {func}")
            return False
        else:
            print(f"\n✅ 所有关键功能都保留")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def show_cleaned_code_structure():
    """显示清理后的代码结构"""
    print(f"\n📋 清理后的代码结构:")
    print("=" * 60)
    
    print(f"✅ 保留的核心功能:")
    print(f"   1. useEffect(() => fetchCompletedTasks(), [fetchCompletedTasks])")
    print(f"      - 页面加载时获取已完成任务")
    print(f"      - 无调试输出")
    
    print(f"\n   2. handleAsyncTaskSelect(taskId)")
    print(f"      - 处理异步任务选择")
    print(f"      - 转换结果格式")
    print(f"      - 设置异步预测结果")
    print(f"      - 无调试输出")
    
    print(f"\n   3. useEffect(() => {...}, [completedPredictionTasks, selectedAsyncTaskId])")
    print(f"      - 自动选择最新预测任务")
    print(f"      - 无调试输出")
    
    print(f"\n❌ 已删除的调试代码:")
    print(f"   - 所有 console.log 语句")
    print(f"   - 调试信息输出")
    print(f"   - 详细的状态监控")
    print(f"   - 数据结构打印")

def provide_usage_instructions():
    """提供使用说明"""
    print(f"\n💡 使用说明:")
    print("=" * 60)
    
    print(f"🎯 异步预测结果展示功能:")
    print(f"   1. 运行异步预测任务")
    print(f"   2. 等待任务完成")
    print(f"   3. 打开模型预测页面")
    print(f"   4. 切换到'异步预测结果'标签")
    print(f"   5. 查看预测结果展示")
    
    print(f"\n🔧 如需调试:")
    print(f"   - 打开浏览器开发者工具")
    print(f"   - 检查网络请求")
    print(f"   - 查看组件状态")
    print(f"   - 临时添加 console.log（记得删除）")
    
    print(f"\n📊 预期效果:")
    print(f"   - 自动获取已完成的预测任务")
    print(f"   - 自动选择最新的预测任务")
    print(f"   - 显示与同步预测相同格式的结果")
    print(f"   - 无调试信息输出到控制台")

if __name__ == "__main__":
    print("🧹 前端调试代码清理确认")
    print("=" * 60)
    
    # 检查调试代码清理
    debug_cleaned = check_debug_code_removal()
    
    # 验证功能完整性
    functionality_intact = verify_functionality_intact()
    
    # 显示代码结构
    show_cleaned_code_structure()
    
    # 提供使用说明
    provide_usage_instructions()
    
    print(f"\n" + "=" * 60)
    print("✅ 清理确认完成")
    
    if debug_cleaned and functionality_intact:
        print(f"\n🎉 清理成功:")
        print("   ✅ 调试代码已完全删除")
        print("   ✅ 核心功能完整保留")
        print("   ✅ 异步预测结果展示功能正常")
    else:
        print(f"\n⚠️  清理结果:")
        print(f"   调试代码清理: {'✅' if debug_cleaned else '❌'}")
        print(f"   功能完整性: {'✅' if functionality_intact else '❌'}")
    
    print(f"\n💡 下一步:")
    print("   1. 重启前端开发服务器")
    print("   2. 测试异步预测结果展示功能")
    print("   3. 确认无调试信息输出到控制台")
