#!/usr/bin/env python3
"""
测试异步预测结果展示的脚本
"""

import json
import os
from datetime import datetime, timedelta

def create_test_async_prediction_data():
    """创建测试异步预测数据"""
    print("🔍 创建异步预测测试数据")
    print("=" * 60)
    
    base_time = datetime.now()
    
    # 创建测试任务
    test_task = {
        "predict_12345678-1234-5678-9abc-123456789abc": {
            "task_id": "predict_12345678-1234-5678-9abc-123456789abc",
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "test_traffic_data.csv",
                "model_names": ["TCP_spt_sip_dip"]
            },
            "created_at": (base_time - timedelta(minutes=5)).isoformat(),
            "started_at": (base_time - timedelta(minutes=4)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=1)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=1)).isoformat(),
            "current_step": "预测完成",
            "error": None
        }
    }
    
    # 创建测试结果 - 格式与同步预测完全一致
    test_result = {
        "predict_12345678-1234-5678-9abc-123456789abc": {
            "predictions": [
                {
                    "timestamp": "2025-07-24 10:00:00",
                    "packets_per_sec": 1500,
                    "packets_per_sec_smooth": 1480,
                    "pred_smooth": 1520,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:01:00",
                    "packets_per_sec": 2800,
                    "packets_per_sec_smooth": 2750,
                    "pred_smooth": 1530,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:02:00",
                    "packets_per_sec": 1200,
                    "packets_per_sec_smooth": 1250,
                    "pred_smooth": 1540,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:03:00",
                    "packets_per_sec": 3200,
                    "packets_per_sec_smooth": 3150,
                    "pred_smooth": 1550,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:04:00",
                    "packets_per_sec": 1800,
                    "packets_per_sec_smooth": 1820,
                    "pred_smooth": 1560,
                    "threshold": 2000,
                    "is_anomaly": False
                }
            ],
            "anomaly_count": 2,
            "suggested_threshold": 2000,
            "model_name": "TCP_spt_sip_dip",
            "message": "预测成功",
            "duration_seconds": 180.5,
            "cpu_percent": 15.6,
            "memory_mb": 256.4,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        }
    }
    
    return test_task, test_result

def analyze_data_flow():
    """分析数据流程"""
    print(f"\n📊 异步预测结果展示数据流程:")
    print("=" * 60)
    
    print(f"🔄 数据流程:")
    print(f"1. 异步预测完成 → task_storage.set_task_result()")
    print(f"2. 结果保存到 task_results.json")
    print(f"3. 前端页面加载 → fetchCompletedTasks()")
    print(f"4. 获取已完成任务 → getCompletedTasksByType('prediction')")
    print(f"5. 用户选择任务 → handleAsyncTaskSelect()")
    print(f"6. 转换结果格式 → setAsyncPredictionResults()")
    print(f"7. 展示结果 → PredictionResultDisplay")
    
    print(f"\n📋 数据格式对比:")
    print(f"后端异步预测结果格式:")
    print(f"  predictions: [{{timestamp, packets_per_sec, packets_per_sec_smooth, pred_smooth, threshold, is_anomaly}}]")
    print(f"  anomaly_count: number")
    print(f"  suggested_threshold: number")
    print(f"  model_name: string")
    print(f"  duration_seconds: number")
    
    print(f"\n前端PredictionResult接口:")
    print(f"  predictions: [{{timestamp, packets_per_sec, packets_per_sec_smooth, pred_smooth, threshold, is_anomaly}}]")
    print(f"  anomaly_count: number")
    print(f"  suggested_threshold: number")
    print(f"  model_name: string")
    print(f"  duration_seconds?: number")
    
    print(f"\n✅ 格式完全匹配！")

def check_potential_issues():
    """检查潜在问题"""
    print(f"\n🔍 潜在问题检查:")
    print("=" * 60)
    
    issues = [
        {
            "issue": "任务状态不是 'completed'",
            "check": "确认异步预测任务状态为 'completed'",
            "solution": "检查后端任务状态更新逻辑"
        },
        {
            "issue": "任务结果为空或格式错误",
            "check": "确认 task_results.json 中有正确的预测结果",
            "solution": "检查后端 set_task_result 调用"
        },
        {
            "issue": "前端没有获取到已完成任务",
            "check": "确认 getCompletedTasksByType('prediction') 返回数据",
            "solution": "检查 fetchCompletedTasks 调用"
        },
        {
            "issue": "任务选择器没有自动选择",
            "check": "确认 useEffect 自动选择最新任务",
            "solution": "检查 handleAsyncTaskSelect 调用"
        },
        {
            "issue": "结果转换失败",
            "check": "确认 selectedTask.result 存在且格式正确",
            "solution": "检查数据转换逻辑"
        },
        {
            "issue": "PredictionResultDisplay 组件问题",
            "check": "确认组件能正确渲染预测结果",
            "solution": "检查组件实现和数据绑定"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"{i}. {issue['issue']}")
        print(f"   检查: {issue['check']}")
        print(f"   解决: {issue['solution']}")
        print()

def debug_steps():
    """调试步骤"""
    print(f"\n🛠️  调试步骤:")
    print("=" * 60)
    
    steps = [
        "1. 检查 task_storage.json 中是否有 prediction 类型的 completed 任务",
        "2. 检查 task_results.json 中是否有对应的预测结果",
        "3. 打开浏览器开发者工具，查看网络请求",
        "4. 检查 /tasks/completed API 返回的数据",
        "5. 在前端添加 console.log 查看 completedPredictionTasks",
        "6. 检查 handleAsyncTaskSelect 是否被调用",
        "7. 查看 asyncPredictionResults 状态是否更新",
        "8. 确认 PredictionResultDisplay 组件是否渲染"
    ]
    
    for step in steps:
        print(f"   {step}")

def create_frontend_debug_code():
    """创建前端调试代码"""
    print(f"\n💻 前端调试代码:")
    print("=" * 60)
    
    debug_code = '''
// 在 ModelPredictionPage.tsx 中添加调试代码

// 1. 在 useEffect 中添加日志
useEffect(() => {
  console.log('🔍 获取已完成任务...');
  fetchCompletedTasks();
}, [fetchCompletedTasks]);

// 2. 监控 completedPredictionTasks 变化
useEffect(() => {
  console.log('📋 已完成预测任务:', completedPredictionTasks);
  console.log('📊 任务数量:', completedPredictionTasks.length);
}, [completedPredictionTasks]);

// 3. 在 handleAsyncTaskSelect 中添加日志
const handleAsyncTaskSelect = (taskId: string) => {
  console.log('🎯 选择异步任务:', taskId);
  setSelectedAsyncTaskId(taskId);
  const selectedTask = completedPredictionTasks.find(task => task.task_id === taskId);
  console.log('📄 选中的任务:', selectedTask);
  
  if (selectedTask && selectedTask.result) {
    console.log('📊 任务结果:', selectedTask.result);
    // ... 转换逻辑
    console.log('✅ 转换后的结果:', asyncResult);
    setAsyncPredictionResults([asyncResult]);
  } else {
    console.log('❌ 任务结果为空或不存在');
  }
};

// 4. 监控 asyncPredictionResults 变化
useEffect(() => {
  console.log('🎨 异步预测结果状态:', asyncPredictionResults);
}, [asyncPredictionResults]);
'''
    
    print(debug_code)

def test_api_endpoints():
    """测试API端点"""
    print(f"\n🌐 API端点测试:")
    print("=" * 60)
    
    print(f"📡 测试命令:")
    print(f"# 1. 获取所有已完成任务")
    print(f"curl -X GET http://localhost:8000/tasks/completed")
    print()
    print(f"# 2. 检查特定任务状态")
    print(f"curl -X GET http://localhost:8000/tasks/status/predict_12345678-1234-5678-9abc-123456789abc")
    print()
    print(f"# 3. 检查任务结果文件")
    print(f"cat task_results.json | jq '.\"predict_12345678-1234-5678-9abc-123456789abc\"'")

if __name__ == "__main__":
    print("🔍 异步预测结果展示测试")
    print("=" * 60)
    
    # 创建测试数据
    test_task, test_result = create_test_async_prediction_data()
    print(f"✅ 创建了测试任务和结果数据")
    
    # 分析数据流程
    analyze_data_flow()
    
    # 检查潜在问题
    check_potential_issues()
    
    # 调试步骤
    debug_steps()
    
    # 前端调试代码
    create_frontend_debug_code()
    
    # API测试
    test_api_endpoints()
    
    print(f"\n" + "=" * 60)
    print("✅ 分析完成")
    print(f"\n🎯 结论:")
    print("异步预测结果展示逻辑已经存在且格式正确！")
    print("如果看不到结果，可能的原因:")
    print("1. 异步预测任务没有正确完成")
    print("2. 任务结果没有正确保存")
    print("3. 前端没有正确获取或显示数据")
    print(f"\n💡 建议:")
    print("1. 先运行一个异步预测任务")
    print("2. 检查任务是否成功完成")
    print("3. 使用上述调试代码排查问题")
    print("4. 检查浏览器控制台的错误信息")
