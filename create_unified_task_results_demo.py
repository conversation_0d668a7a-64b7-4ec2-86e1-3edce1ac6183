#!/usr/bin/env python3
"""
创建统一任务结果存储的演示
"""

import json
import os
from datetime import datetime, timedelta

def create_unified_demo():
    """创建统一存储演示"""
    print("🔍 创建异步训练和预测结果统一存储演示")
    print("=" * 60)
    
    base_time = datetime.now()
    
    # 创建包含训练和预测任务的统一存储结构
    unified_tasks = {
        # 训练任务1
        "training_12345678-1234-5678-9abc-123456789abc": {
            "task_id": "training_12345678-1234-5678-9abc-123456789abc",
            "task_type": "training",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "traffic_data.csv",
                "selected_prots": ["TCP", "UDP"],
                "selected_datatypes": {
                    "TCP": ["spt_sip_dip", "dpt_sip_dip"],
                    "UDP": ["spt_sip_dip"]
                }
            },
            "created_at": (base_time - timedelta(minutes=30)).isoformat(),
            "started_at": (base_time - timedelta(minutes=29)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=22)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=22)).isoformat(),
            "current_step": "训练完成",
            "error": None
        },
        
        # 预测任务1
        "prediction_87654321-4321-8765-cba9-987654321cba": {
            "task_id": "prediction_87654321-4321-8765-cba9-987654321cba",
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "test_data.csv",
                "model_names": ["TCP_spt_sip_dip"]
            },
            "created_at": (base_time - timedelta(minutes=15)).isoformat(),
            "started_at": (base_time - timedelta(minutes=14)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=11)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=11)).isoformat(),
            "current_step": "预测完成",
            "error": None
        },
        
        # 训练任务2
        "training_11111111-2222-3333-4444-555555555555": {
            "task_id": "training_11111111-2222-3333-4444-555555555555",
            "task_type": "training",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "network_logs.csv",
                "selected_prots": ["ICMP"],
                "selected_datatypes": {
                    "ICMP": ["dip", "sip"]
                }
            },
            "created_at": (base_time - timedelta(minutes=10)).isoformat(),
            "started_at": (base_time - timedelta(minutes=9)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=3)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=3)).isoformat(),
            "current_step": "训练完成",
            "error": None
        },
        
        # 预测任务2
        "prediction_22222222-3333-4444-5555-666666666666": {
            "task_id": "prediction_22222222-3333-4444-5555-666666666666",
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "anomaly_test.csv",
                "model_names": ["ICMP_dip"]
            },
            "created_at": (base_time - timedelta(minutes=5)).isoformat(),
            "started_at": (base_time - timedelta(minutes=4)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=1)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=1)).isoformat(),
            "current_step": "预测完成",
            "error": None
        }
    }
    
    # 创建对应的统一结果存储
    unified_results = {
        # 训练任务1的结果
        "training_12345678-1234-5678-9abc-123456789abc": {
            "results": {
                "TCP_spt_sip_dip": {
                    "r2_score": 0.8567,
                    "model_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_model_best.pth",
                    "params_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_params.json",
                    "scaler_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/traffic_data_TCP_spt_sip_dip_20250724_100000_test.csv",
                    "predictions": [1.23, 2.45, 3.67, 4.89, 5.12],
                    "train_shape": [800, 5],
                    "test_shape": [200, 5]
                },
                "TCP_dpt_sip_dip": {
                    "r2_score": 0.8234,
                    "model_path": "/data/output/traffic_data_TCP_dpt_sip_dip_20250724_100000_model_best.pth",
                    "predictions": [2.34, 3.45, 4.56, 5.67, 6.78],
                    "train_shape": [800, 5],
                    "test_shape": [200, 5]
                },
                "UDP_spt_sip_dip": {
                    "r2_score": 0.7891,
                    "model_path": "/data/output/traffic_data_UDP_spt_sip_dip_20250724_100000_model_best.pth",
                    "predictions": [3.45, 4.56, 5.67, 6.78, 7.89],
                    "train_shape": [750, 5],
                    "test_shape": [180, 5]
                }
            },
            "duration_seconds": 420.5,
            "cpu_percent": 28.7,
            "memory_mb": 1024.3,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        },
        
        # 预测任务1的结果
        "prediction_87654321-4321-8765-cba9-987654321cba": {
            "predictions": [
                {
                    "timestamp": "2025-07-24 10:00:00",
                    "packets_per_sec": 1500,
                    "packets_per_sec_smooth": 1480,
                    "pred_smooth": 1520,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:01:00",
                    "packets_per_sec": 2800,
                    "packets_per_sec_smooth": 2750,
                    "pred_smooth": 1530,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:02:00",
                    "packets_per_sec": 1200,
                    "packets_per_sec_smooth": 1250,
                    "pred_smooth": 1540,
                    "threshold": 2000,
                    "is_anomaly": False
                }
            ],
            "anomaly_count": 1,
            "suggested_threshold": 2000,
            "model_name": "TCP_spt_sip_dip",
            "message": "预测成功",
            "duration_seconds": 180.5,
            "cpu_percent": 15.6,
            "memory_mb": 256.4,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        },
        
        # 训练任务2的结果
        "training_11111111-2222-3333-4444-555555555555": {
            "results": {
                "ICMP_dip": {
                    "r2_score": 0.7234,
                    "model_path": "/data/output/network_logs_ICMP_dip_20250724_110000_model_best.pth",
                    "params_path": "/data/output/network_logs_ICMP_dip_20250724_110000_params.json",
                    "scaler_path": "/data/output/network_logs_ICMP_dip_20250724_110000_scaler_y_best.pkl",
                    "test_data_path": "/data/output/network_logs_ICMP_dip_20250724_110000_test.csv",
                    "predictions": [0.12, 0.34, 0.56, 0.78, 0.91],
                    "train_shape": [600, 4],
                    "test_shape": [150, 4]
                },
                "ICMP_sip": {
                    "r2_score": 0.6987,
                    "model_path": "/data/output/network_logs_ICMP_sip_20250724_110000_model_best.pth",
                    "predictions": [1.45, 2.67, 3.89, 4.12, 5.34],
                    "train_shape": [600, 4],
                    "test_shape": [150, 4]
                }
            },
            "duration_seconds": 360.8,
            "cpu_percent": 32.1,
            "memory_mb": 768.9,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        },
        
        # 预测任务2的结果
        "prediction_22222222-3333-4444-5555-666666666666": {
            "predictions": [
                {
                    "timestamp": "2025-07-24 11:00:00",
                    "packets_per_sec": 800,
                    "packets_per_sec_smooth": 820,
                    "pred_smooth": 850,
                    "threshold": 1200,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 11:01:00",
                    "packets_per_sec": 1500,
                    "packets_per_sec_smooth": 1480,
                    "pred_smooth": 860,
                    "threshold": 1200,
                    "is_anomaly": True
                }
            ],
            "anomaly_count": 1,
            "suggested_threshold": 1200,
            "model_name": "ICMP_dip",
            "message": "预测成功",
            "duration_seconds": 120.3,
            "cpu_percent": 12.4,
            "memory_mb": 180.7,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        }
    }
    
    return unified_tasks, unified_results

def save_unified_demo(tasks, results):
    """保存统一演示数据"""
    print(f"\n💾 保存统一演示数据...")
    print("=" * 60)
    
    # 备份现有文件
    backup_files = []
    for filename in ["task_storage.json", "task_results.json"]:
        if os.path.exists(filename):
            backup_name = f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(filename, backup_name)
            backup_files.append(backup_name)
            print(f"📁 备份: {filename} → {backup_name}")
    
    try:
        # 保存新的演示数据
        with open("task_storage.json", 'w', encoding='utf-8') as f:
            json.dump(tasks, f, ensure_ascii=False, indent=2)
        
        with open("task_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 演示数据保存成功:")
        print(f"   task_storage.json: {len(tasks)} 个任务")
        print(f"   task_results.json: {len(results)} 个结果")
        
        # 分析保存的数据
        training_tasks = [t for t in tasks.values() if t['task_type'] == 'training']
        prediction_tasks = [t for t in tasks.values() if t['task_type'] == 'prediction']
        training_results = [r for r in results.values() if 'results' in r]
        prediction_results = [r for r in results.values() if 'predictions' in r]
        
        print(f"\n📊 数据统计:")
        print(f"   训练任务: {len(training_tasks)} 个")
        print(f"   预测任务: {len(prediction_tasks)} 个")
        print(f"   训练结果: {len(training_results)} 个")
        print(f"   预测结果: {len(prediction_results)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        
        # 恢复备份文件
        for backup_file in backup_files:
            original_file = backup_file.replace(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}", "")
            if os.path.exists(backup_file):
                os.rename(backup_file, original_file)
                print(f"🔄 恢复备份: {backup_file} → {original_file}")
        
        return False

def demonstrate_frontend_usage():
    """演示前端使用方式"""
    print(f"\n🖥️  前端使用演示:")
    print("=" * 60)
    
    print(f"📋 模型训练页面:")
    print(f"   1. 获取所有已完成任务")
    print(f"   2. 过滤: task.task_type === 'training' 或 task.result?.results")
    print(f"   3. 显示训练任务列表")
    print(f"   4. 用户选择 → 显示训练结果")
    
    print(f"\n📋 模型预测页面:")
    print(f"   1. 获取所有已完成任务")
    print(f"   2. 过滤: task.task_type === 'prediction' 或 task.result?.predictions")
    print(f"   3. 显示预测任务列表")
    print(f"   4. 用户选择 → 显示预测结果")
    
    print(f"\n🔧 前端过滤代码示例:")
    print(f"""
    // 获取训练任务
    const trainingTasks = completedTasks.filter(task => 
      task.task_type === 'training' || task.result?.results
    );
    
    // 获取预测任务
    const predictionTasks = completedTasks.filter(task => 
      task.task_type === 'prediction' || task.result?.predictions
    );
    """)

def verify_unified_storage():
    """验证统一存储"""
    print(f"\n🔍 验证统一存储...")
    print("=" * 60)
    
    try:
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"✅ 验证结果:")
        print(f"   任务文件格式: 正确")
        print(f"   结果文件格式: 正确")
        print(f"   任务-结果对应: {len([t for t in tasks.keys() if t in results])} / {len(tasks)}")
        
        # 验证数据结构
        for task_id, result in results.items():
            task_type = tasks.get(task_id, {}).get('task_type', 'unknown')
            
            if task_type == 'training':
                has_results = 'results' in result
                print(f"   训练任务 {task_id[:20]}...: {'✅' if has_results else '❌'}")
            elif task_type == 'prediction':
                has_predictions = 'predictions' in result
                print(f"   预测任务 {task_id[:20]}...: {'✅' if has_predictions else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 异步训练和预测结果统一存储演示")
    print("=" * 60)
    
    # 创建演示数据
    tasks, results = create_unified_demo()
    
    # 保存演示数据
    if save_unified_demo(tasks, results):
        # 验证存储
        if verify_unified_storage():
            # 演示前端使用
            demonstrate_frontend_usage()
    
    print(f"\n" + "=" * 60)
    print("✅ 演示完成")
    print(f"\n🎯 结论:")
    print("异步训练和预测结果 **可以且已经** 存储在同一个JSON文件中！")
    print(f"\n📋 优势:")
    print("  ✅ 统一管理: 一个文件管理所有异步任务结果")
    print("  ✅ 结构清晰: 通过字段区分不同类型的结果")
    print("  ✅ 前端友好: 简单的过滤逻辑即可分离不同类型")
    print("  ✅ 扩展性好: 可以轻松添加新的任务类型")
    print(f"\n💡 当前状态:")
    print("您的系统已经实现了统一存储，无需任何修改！")
