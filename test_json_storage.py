#!/usr/bin/env python3
"""
测试JSON文件存储功能的脚本
"""

import json
import os
import time
from datetime import datetime

def test_json_file_creation():
    """测试JSON文件创建和存储"""
    print("🔍 测试JSON文件存储功能")
    print("=" * 60)
    
    # 模拟任务数据
    test_task = {
        "task_id": "test_12345678-1234-5678-9abc-123456789abc",
        "task_type": "training",
        "status": "completed",
        "progress": 100,
        "created_at": datetime.now().isoformat(),
        "started_at": datetime.now().isoformat(),
        "completed_at": datetime.now().isoformat(),
        "params": {
            "csv_filename": "test_data.csv",
            "protocols": ["TCP"],
            "datatypes": ["spt_sip_dip"]
        },
        "error": None,
        "current_step": "训练完成",
        "total_steps": 100
    }
    
    # 模拟结果数据
    test_result = {
        "results": {
            "TCP_spt_sip_dip": {
                "r2_score": 0.85,
                "model_path": "/data/output/test_TCP_spt_sip_dip_20250724_123456_model_best.pth",
                "params_path": "/data/output/test_TCP_spt_sip_dip_20250724_123456_params.json",
                "scaler_path": "/data/output/test_TCP_spt_sip_dip_20250724_123456_scaler_y_best.pkl",
                "test_data_path": "/data/output/test_TCP_spt_sip_dip_20250724_123456_test.csv",
                "predictions": [1.2, 3.4, 5.6, 7.8, 9.0],
                "train_shape": [800, 5],
                "test_shape": [200, 5]
            }
        },
        "duration_seconds": 120.5,
        "cpu_percent": 25.3,
        "memory_mb": 512.7,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    print("📋 测试数据:")
    print(f"   任务ID: {test_task['task_id']}")
    print(f"   任务类型: {test_task['task_type']}")
    print(f"   任务状态: {test_task['status']}")
    print(f"   结果大小: ~{len(json.dumps(test_result))} 字符")
    
    # 测试文件写入
    tasks_file = "test_task_storage.json"
    results_file = "test_task_results.json"
    
    try:
        # 写入任务数据
        tasks_data = {test_task["task_id"]: test_task}
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks_data, f, ensure_ascii=False, indent=2)
        
        # 写入结果数据
        results_data = {test_task["task_id"]: test_result}
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 文件写入成功:")
        print(f"   任务文件: {tasks_file} ({os.path.getsize(tasks_file)} 字节)")
        print(f"   结果文件: {results_file} ({os.path.getsize(results_file)} 字节)")
        
        # 测试文件读取
        with open(tasks_file, 'r', encoding='utf-8') as f:
            loaded_tasks = json.load(f)
        
        with open(results_file, 'r', encoding='utf-8') as f:
            loaded_results = json.load(f)
        
        print(f"\n✅ 文件读取成功:")
        print(f"   加载任务数: {len(loaded_tasks)}")
        print(f"   加载结果数: {len(loaded_results)}")
        
        # 验证数据完整性
        loaded_task = loaded_tasks[test_task["task_id"]]
        loaded_result = loaded_results[test_task["task_id"]]
        
        print(f"\n🔍 数据完整性验证:")
        print(f"   任务ID匹配: {'✅' if loaded_task['task_id'] == test_task['task_id'] else '❌'}")
        print(f"   任务状态匹配: {'✅' if loaded_task['status'] == test_task['status'] else '❌'}")
        print(f"   结果R²分数: {loaded_result['results']['TCP_spt_sip_dip']['r2_score']}")
        print(f"   预测数据长度: {len(loaded_result['results']['TCP_spt_sip_dip']['predictions'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        for file in [tasks_file, results_file]:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️  清理测试文件: {file}")

def test_concurrent_access():
    """测试并发访问"""
    print(f"\n🔍 测试并发访问...")
    print("=" * 60)
    
    import threading
    
    tasks_file = "test_concurrent_tasks.json"
    results_file = "test_concurrent_results.json"
    
    # 初始化文件
    with open(tasks_file, 'w', encoding='utf-8') as f:
        json.dump({}, f)
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({}, f)
    
    def worker(worker_id):
        """工作线程"""
        try:
            for i in range(5):
                task_id = f"worker_{worker_id}_task_{i}"
                
                # 读取现有数据
                with open(tasks_file, 'r', encoding='utf-8') as f:
                    tasks = json.load(f)
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                # 添加新数据
                tasks[task_id] = {
                    "task_id": task_id,
                    "worker_id": worker_id,
                    "iteration": i,
                    "timestamp": datetime.now().isoformat()
                }
                
                results[task_id] = {
                    "result": f"Result from worker {worker_id}, iteration {i}",
                    "value": worker_id * 10 + i
                }
                
                # 写入文件
                with open(tasks_file, 'w', encoding='utf-8') as f:
                    json.dump(tasks, f, ensure_ascii=False, indent=2)
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                
                time.sleep(0.1)  # 模拟处理时间
                
        except Exception as e:
            print(f"❌ Worker {worker_id} 失败: {e}")
    
    # 启动多个工作线程
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 检查结果
    try:
        with open(tasks_file, 'r', encoding='utf-8') as f:
            final_tasks = json.load(f)
        with open(results_file, 'r', encoding='utf-8') as f:
            final_results = json.load(f)
        
        print(f"📊 并发测试结果:")
        print(f"   最终任务数: {len(final_tasks)}")
        print(f"   最终结果数: {len(final_results)}")
        print(f"   预期数量: 15 (3个工作线程 × 5次迭代)")
        
        if len(final_tasks) == 15 and len(final_results) == 15:
            print(f"   ✅ 并发访问测试通过")
        else:
            print(f"   ⚠️  可能存在并发问题")
            
    except Exception as e:
        print(f"❌ 并发测试检查失败: {e}")
    
    finally:
        # 清理测试文件
        for file in [tasks_file, results_file]:
            if os.path.exists(file):
                os.remove(file)

def check_existing_files():
    """检查现有的存储文件"""
    print(f"\n🔍 检查现有存储文件...")
    print("=" * 60)
    
    files_to_check = [
        "task_storage.json",
        "task_results.json"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                file_size = os.path.getsize(filename)
                print(f"📁 {filename}:")
                print(f"   文件大小: {file_size} 字节")
                print(f"   数据条目: {len(data)}")
                
                if len(data) > 0:
                    # 显示前几个条目的键
                    keys = list(data.keys())[:3]
                    print(f"   示例键: {keys}")
                
            except Exception as e:
                print(f"❌ 读取 {filename} 失败: {e}")
        else:
            print(f"📁 {filename}: 文件不存在")

def analyze_storage_benefits():
    """分析JSON存储的优势"""
    print(f"\n💡 JSON文件存储优势分析...")
    print("=" * 60)
    
    print(f"🔧 技术优势:")
    print(f"   ✅ 持久化存储: 服务器重启后数据不丢失")
    print(f"   ✅ 人类可读: JSON格式便于调试和检查")
    print(f"   ✅ 跨平台: 标准JSON格式，兼容性好")
    print(f"   ✅ 备份简单: 直接复制文件即可备份")
    print(f"   ✅ 版本控制: 可以纳入Git等版本控制系统")
    
    print(f"\n📊 性能特点:")
    print(f"   ✅ 读取快速: 小到中等数据量读取性能良好")
    print(f"   ⚠️  写入开销: 每次写入需要重写整个文件")
    print(f"   ⚠️  并发限制: 需要文件锁来处理并发写入")
    print(f"   ⚠️  内存使用: 大文件需要完整加载到内存")
    
    print(f"\n🎯 适用场景:")
    print(f"   ✅ 中小型应用: 任务数量 < 10,000")
    print(f"   ✅ 开发测试: 便于调试和数据检查")
    print(f"   ✅ 单机部署: 无需复杂的数据库配置")
    print(f"   ⚠️  大规模应用: 建议考虑数据库方案")

if __name__ == "__main__":
    print("🔍 JSON文件存储功能测试")
    print("=" * 60)
    
    # 运行测试
    success = test_json_file_creation()
    
    if success:
        test_concurrent_access()
        check_existing_files()
        analyze_storage_benefits()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("\n🎯 总结:")
    print("异步任务结果现在存储在JSON文件中:")
    print("• task_storage.json - 任务基本信息")
    print("• task_results.json - 任务结果数据")
    print("• 数据持久化，服务器重启后不丢失")
    print("• 便于备份、调试和维护")
