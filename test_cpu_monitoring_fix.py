#!/usr/bin/env python3
"""
测试CPU监控修复的脚本
"""

import psutil
import time
import threading

def test_cpu_monitoring_methods():
    """测试不同的CPU监控方法"""
    print("🔍 测试CPU监控方法...")
    print("=" * 50)
    
    process = psutil.Process()
    cpu_cores = psutil.cpu_count()
    
    print(f"CPU核心数: {cpu_cores}")
    print(f"当前进程PID: {process.pid}")
    
    # 方法1: 单次测量（可能为0）
    print("\n📊 方法1: 单次测量")
    cpu1 = process.cpu_percent()
    print(f"   第一次调用: {cpu1}% (通常为0，因为没有基线)")
    
    time.sleep(0.1)
    cpu2 = process.cpu_percent()
    print(f"   第二次调用: {cpu2}% (基于上次调用的间隔)")
    
    # 方法2: 带间隔的测量
    print("\n📊 方法2: 带间隔测量")
    cpu3 = process.cpu_percent(interval=0.1)
    print(f"   interval=0.1: {cpu3}%")
    
    cpu4 = process.cpu_percent(interval=1.0)
    print(f"   interval=1.0: {cpu4}%")
    
    # 方法3: 采样平均法（推荐）
    print("\n📊 方法3: 采样平均法（推荐）")
    process.cpu_percent()  # 初始化
    samples = []
    
    def cpu_sampler():
        for i in range(10):
            time.sleep(0.5)
            cpu = process.cpu_percent()
            if cpu > 0:
                samples.append(cpu)
                print(f"   采样 {i+1}: {cpu:.2f}%")
    
    def cpu_load_simulator():
        """模拟CPU负载"""
        end_time = time.time() + 5
        while time.time() < end_time:
            # 简单的计算负载
            sum(i * i for i in range(1000))
    
    # 启动CPU负载模拟
    load_thread = threading.Thread(target=cpu_load_simulator)
    load_thread.start()
    
    # 启动CPU采样
    sample_thread = threading.Thread(target=cpu_sampler)
    sample_thread.start()
    
    # 等待完成
    load_thread.join()
    sample_thread.join()
    
    if samples:
        avg_cpu = sum(samples) / len(samples)
        print(f"\n   📈 采样结果:")
        print(f"   采样数量: {len(samples)}")
        print(f"   平均CPU使用率: {avg_cpu:.2f}%")
        print(f"   标准化CPU使用率: {avg_cpu / cpu_cores:.2f}%")
    else:
        print(f"\n   ⚠️  没有获取到有效的CPU采样数据")

def test_training_simulation():
    """模拟训练过程的CPU监控"""
    print("\n🔍 模拟训练过程CPU监控...")
    print("=" * 50)
    
    process = psutil.Process()
    cpu_cores = psutil.cpu_count()
    
    # 初始化CPU监控
    process.cpu_percent()
    cpu_samples = []
    
    print("开始模拟训练...")
    epochs = 50
    
    for epoch in range(epochs):
        # 模拟训练计算
        dummy_calc = sum(i * i for i in range(10000))
        
        # 每10个epoch采样一次CPU使用率
        if epoch % 10 == 0:
            current_cpu = process.cpu_percent()
            if current_cpu > 0:
                cpu_samples.append(current_cpu)
                print(f"   Epoch {epoch}: CPU使用率 {current_cpu:.2f}%")
        
        # 短暂休息
        time.sleep(0.01)
    
    print(f"\n📊 训练完成，CPU监控结果:")
    if cpu_samples:
        avg_cpu = sum(cpu_samples) / len(cpu_samples)
        print(f"   采样数量: {len(cpu_samples)}")
        print(f"   平均CPU使用率: {avg_cpu:.2f}%")
        print(f"   标准化CPU使用率: {avg_cpu / cpu_cores:.2f}%")
        print(f"   ✅ 成功获取CPU使用率数据")
    else:
        print(f"   ❌ 没有获取到CPU使用率数据")
        # 备用方案
        fallback_cpu = process.cpu_percent(interval=1.0)
        print(f"   备用方案CPU使用率: {fallback_cpu:.2f}%")

def test_psutil_behavior():
    """测试psutil的行为特点"""
    print("\n🔍 测试psutil行为特点...")
    print("=" * 50)
    
    process = psutil.Process()
    
    print("📋 psutil.Process.cpu_percent() 行为:")
    print("1. 第一次调用通常返回0.0（没有基线）")
    print("2. 后续调用返回自上次调用以来的CPU使用率")
    print("3. interval参数会阻塞指定时间来测量")
    print("4. 在多核系统上，返回值可能超过100%")
    
    print("\n🧪 实际测试:")
    
    # 测试连续调用
    for i in range(5):
        cpu = process.cpu_percent()
        print(f"   调用 {i+1}: {cpu:.2f}%")
        time.sleep(0.2)
    
    print("\n💡 最佳实践:")
    print("1. ✅ 在训练开始时调用一次 process.cpu_percent() 建立基线")
    print("2. ✅ 在训练过程中定期采样（每N个epoch）")
    print("3. ✅ 计算采样的平均值作为最终结果")
    print("4. ✅ 如果没有采样数据，使用 interval=1.0 作为备用")

def check_system_resources():
    """检查系统资源"""
    print("\n🔍 检查系统资源...")
    print("=" * 50)
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_count_logical = psutil.cpu_count(logical=True)
    cpu_freq = psutil.cpu_freq()
    
    print(f"📊 CPU信息:")
    print(f"   物理核心数: {cpu_count}")
    print(f"   逻辑核心数: {cpu_count_logical}")
    if cpu_freq:
        print(f"   当前频率: {cpu_freq.current:.2f} MHz")
        print(f"   最大频率: {cpu_freq.max:.2f} MHz")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"\n📊 内存信息:")
    print(f"   总内存: {memory.total / (1024**3):.2f} GB")
    print(f"   可用内存: {memory.available / (1024**3):.2f} GB")
    print(f"   使用率: {memory.percent:.1f}%")
    
    # 当前进程信息
    process = psutil.Process()
    process_memory = process.memory_info()
    print(f"\n📊 当前进程信息:")
    print(f"   PID: {process.pid}")
    print(f"   内存使用: {process_memory.rss / (1024**2):.2f} MB")
    print(f"   CPU使用率: {process.cpu_percent():.2f}%")

if __name__ == "__main__":
    print("🔍 CPU监控修复验证")
    print("=" * 60)
    
    check_system_resources()
    test_psutil_behavior()
    test_cpu_monitoring_methods()
    test_training_simulation()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("\n💡 修复总结:")
    print("1. ✅ 在训练开始时初始化CPU监控基线")
    print("2. ✅ 在训练过程中定期采样CPU使用率")
    print("3. ✅ 计算采样平均值作为最终CPU使用率")
    print("4. ✅ 添加备用方案处理无采样数据的情况")
    print("5. ✅ 添加详细日志记录CPU监控过程")
    print("\n🎯 预期效果:")
    print("• 异步训练完成后CPU使用率不再为空")
    print("• 显示训练过程中的平均CPU使用率")
    print("• 提供更准确的资源使用统计")
