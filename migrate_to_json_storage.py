#!/usr/bin/env python3
"""
数据迁移脚本：将现有内存数据迁移到JSON文件存储
"""

import json
import os
import sys
from datetime import datetime

def migrate_existing_data():
    """迁移现有数据到JSON文件"""
    print("🔄 数据迁移脚本")
    print("=" * 60)
    
    # 检查是否已有JSON文件
    tasks_file = "task_storage.json"
    results_file = "task_results.json"
    
    if os.path.exists(tasks_file) or os.path.exists(results_file):
        print("⚠️  检测到现有JSON文件:")
        if os.path.exists(tasks_file):
            print(f"   - {tasks_file}")
        if os.path.exists(results_file):
            print(f"   - {results_file}")
        
        response = input("\n是否要覆盖现有文件? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 迁移已取消")
            return False
    
    # 创建空的JSON文件
    try:
        # 初始化任务存储文件
        with open(tasks_file, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=2)
        
        # 初始化结果存储文件
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功创建JSON存储文件:")
        print(f"   - {tasks_file}")
        print(f"   - {results_file}")
        
        # 添加说明注释
        readme_content = f"""# 异步任务存储文件说明

## 文件结构

### task_storage.json
存储任务的基本信息，包括：
- task_id: 任务唯一标识符
- task_type: 任务类型 (training/prediction)
- status: 任务状态 (pending/running/completed/failed/cancelled)
- params: 任务参数
- created_at: 创建时间
- updated_at: 更新时间
- started_at: 开始时间
- completed_at: 完成时间
- progress: 进度百分比
- current_step: 当前步骤
- total_steps: 总步骤数
- error: 错误信息

### task_results.json
存储任务的结果数据，包括：
- 训练任务结果: 模型路径、R²分数、预测数据等
- 预测任务结果: 预测结果、异常数量、阈值等
- 性能指标: CPU使用率、内存使用、GPU使用等

## 注意事项

1. 这些文件由系统自动管理，请勿手动编辑
2. 定期备份这些文件以防数据丢失
3. 文件采用UTF-8编码，支持中文内容
4. JSON格式便于调试和数据检查

## 备份建议

```bash
# 创建备份
cp task_storage.json task_storage_backup_$(date +%Y%m%d_%H%M%S).json
cp task_results.json task_results_backup_$(date +%Y%m%d_%H%M%S).json

# 恢复备份
cp task_storage_backup_YYYYMMDD_HHMMSS.json task_storage.json
cp task_results_backup_YYYYMMDD_HHMMSS.json task_results.json
```

创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open("task_storage_README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"   - task_storage_README.md (说明文档)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建JSON文件失败: {e}")
        return False

def create_backup_script():
    """创建备份脚本"""
    backup_script = """#!/bin/bash
# 异步任务数据备份脚本

BACKUP_DIR="task_backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份文件
if [ -f "task_storage.json" ]; then
    cp task_storage.json "$BACKUP_DIR/task_storage_$TIMESTAMP.json"
    echo "✅ 已备份 task_storage.json"
fi

if [ -f "task_results.json" ]; then
    cp task_results.json "$BACKUP_DIR/task_results_$TIMESTAMP.json"
    echo "✅ 已备份 task_results.json"
fi

# 清理旧备份（保留最近10个）
cd $BACKUP_DIR
ls -t task_storage_*.json | tail -n +11 | xargs -r rm
ls -t task_results_*.json | tail -n +11 | xargs -r rm

echo "🎯 备份完成: $BACKUP_DIR/"
"""
    
    try:
        with open("backup_tasks.sh", 'w', encoding='utf-8') as f:
            f.write(backup_script)
        
        # 设置执行权限
        os.chmod("backup_tasks.sh", 0o755)
        
        print(f"✅ 创建备份脚本: backup_tasks.sh")
        print(f"   使用方法: ./backup_tasks.sh")
        
    except Exception as e:
        print(f"❌ 创建备份脚本失败: {e}")

def verify_migration():
    """验证迁移结果"""
    print(f"\n🔍 验证迁移结果...")
    print("-" * 40)
    
    files_to_check = [
        "task_storage.json",
        "task_results.json",
        "task_storage_README.md",
        "backup_tasks.sh"
    ]
    
    all_good = True
    
    for filename in files_to_check:
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"✅ {filename} ({file_size} 字节)")
        else:
            print(f"❌ {filename} 不存在")
            all_good = False
    
    if all_good:
        print(f"\n🎉 迁移验证通过！")
        print(f"📋 下一步:")
        print(f"   1. 重启应用服务器")
        print(f"   2. 提交新的异步任务进行测试")
        print(f"   3. 检查JSON文件是否正确更新")
        print(f"   4. 设置定期备份计划")
    else:
        print(f"\n⚠️  迁移验证失败，请检查错误信息")

if __name__ == "__main__":
    print("🔄 异步任务存储迁移工具")
    print("=" * 60)
    print("此脚本将设置JSON文件存储系统")
    print("=" * 60)
    
    # 执行迁移
    if migrate_existing_data():
        create_backup_script()
        verify_migration()
        
        print(f"\n" + "=" * 60)
        print("✅ 迁移完成")
        print(f"\n🎯 重要提醒:")
        print("1. 重启应用服务器以使用新的存储系统")
        print("2. 现有内存中的任务数据将在重启后丢失")
        print("3. 新的任务将自动保存到JSON文件中")
        print("4. 定期运行 ./backup_tasks.sh 进行数据备份")
    else:
        print(f"\n❌ 迁移失败")
        sys.exit(1)
