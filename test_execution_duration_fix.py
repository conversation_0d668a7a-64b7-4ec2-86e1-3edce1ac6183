#!/usr/bin/env python3
"""
测试执行时长修复的脚本
"""

import json
import os
from datetime import datetime, timedelta

def create_test_scenarios():
    """创建不同场景的测试数据"""
    print("🔍 创建执行时长测试场景")
    print("=" * 60)
    
    base_time = datetime.now()
    
    test_scenarios = {
        # 场景1: 正常完成的任务（有完整时间信息）
        "normal_completed_task": {
            "task_id": "normal_completed_task",
            "task_type": "training",
            "status": "completed",
            "progress": 100,
            "created_at": (base_time - timedelta(minutes=10)).isoformat(),
            "started_at": (base_time - timedelta(minutes=9)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=2)).isoformat(),
            "params": {"csv_filename": "test.csv"},
            "current_step": "训练完成"
        },
        
        # 场景2: 完成的任务（没有started_at，但有duration_seconds）
        "completed_no_start_time": {
            "task_id": "completed_no_start_time",
            "task_type": "training", 
            "status": "completed",
            "progress": 100,
            "created_at": (base_time - timedelta(minutes=15)).isoformat(),
            "started_at": None,
            "completed_at": (base_time - timedelta(minutes=5)).isoformat(),
            "params": {"csv_filename": "test2.csv"},
            "current_step": "训练完成"
        },
        
        # 场景3: 完成的任务（没有任何时间信息）
        "completed_no_time_info": {
            "task_id": "completed_no_time_info",
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "created_at": (base_time - timedelta(minutes=20)).isoformat(),
            "started_at": None,
            "completed_at": None,
            "params": {"csv_filename": "test3.csv"},
            "current_step": "预测完成"
        },
        
        # 场景4: 正在运行的任务（有started_at）
        "running_with_start": {
            "task_id": "running_with_start",
            "task_type": "training",
            "status": "running",
            "progress": 65,
            "created_at": (base_time - timedelta(minutes=8)).isoformat(),
            "started_at": (base_time - timedelta(minutes=7)).isoformat(),
            "completed_at": None,
            "params": {"csv_filename": "test4.csv"},
            "current_step": "训练中 - Epoch 65/100"
        },
        
        # 场景5: 正在运行的任务（没有started_at）
        "running_no_start": {
            "task_id": "running_no_start",
            "task_type": "training",
            "status": "running",
            "progress": 30,
            "created_at": (base_time - timedelta(minutes=5)).isoformat(),
            "started_at": None,
            "completed_at": None,
            "params": {"csv_filename": "test5.csv"},
            "current_step": "数据预处理"
        },
        
        # 场景6: 失败的任务（有时间信息）
        "failed_with_time": {
            "task_id": "failed_with_time",
            "task_type": "training",
            "status": "failed",
            "progress": 25,
            "created_at": (base_time - timedelta(minutes=12)).isoformat(),
            "started_at": (base_time - timedelta(minutes=11)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=8)).isoformat(),
            "params": {"csv_filename": "test6.csv"},
            "current_step": "数据加载失败",
            "error": "FileNotFoundError: 文件不存在"
        },
        
        # 场景7: 取消的任务
        "cancelled_task": {
            "task_id": "cancelled_task",
            "task_type": "training",
            "status": "cancelled",
            "progress": 40,
            "created_at": (base_time - timedelta(minutes=18)).isoformat(),
            "started_at": (base_time - timedelta(minutes=17)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=12)).isoformat(),
            "params": {"csv_filename": "test7.csv"},
            "current_step": "任务已取消"
        },
        
        # 场景8: 等待中的任务
        "pending_task": {
            "task_id": "pending_task",
            "task_type": "training",
            "status": "pending",
            "progress": 0,
            "created_at": base_time.isoformat(),
            "started_at": None,
            "completed_at": None,
            "params": {"csv_filename": "test8.csv"},
            "current_step": None
        }
    }
    
    # 创建对应的结果数据
    test_results = {
        # 正常完成任务的结果（包含duration_seconds）
        "normal_completed_task": {
            "results": {"TCP_spt_sip_dip": {"r2_score": 0.85}},
            "duration_seconds": 420.5,
            "cpu_percent": 25.3,
            "memory_mb": 512.7
        },
        
        # 没有started_at但有duration_seconds的任务结果
        "completed_no_start_time": {
            "results": {"UDP_spt_sip_dip": {"r2_score": 0.78}},
            "duration_seconds": 300.2,
            "cpu_percent": 22.1,
            "memory_mb": 448.3
        },
        
        # 没有时间信息的任务结果
        "completed_no_time_info": {
            "predictions": [1.0, 2.0, 3.0],
            "anomaly_count": 2,
            "model_name": "TCP_spt_sip_dip"
        }
    }
    
    return test_scenarios, test_results

def analyze_duration_logic():
    """分析执行时长逻辑"""
    print(f"\n📊 执行时长显示逻辑分析:")
    print("=" * 60)
    
    scenarios = [
        {
            "condition": "有 started_at 和 completed_at",
            "display": "计算实际执行时间（秒）",
            "example": "420秒",
            "status": "✅ 最准确"
        },
        {
            "condition": "有 started_at，状态为 running",
            "display": "计算从开始到现在的时间",
            "example": "180秒 (进行中)",
            "status": "✅ 实时更新"
        },
        {
            "condition": "已完成，有 result.duration_seconds",
            "display": "使用结果中的执行时长",
            "example": "300秒",
            "status": "✅ 备用方案"
        },
        {
            "condition": "已完成，无时间信息",
            "display": "显示已完成状态",
            "example": "已完成",
            "status": "✅ 状态指示"
        },
        {
            "condition": "失败状态",
            "display": "显示执行失败",
            "example": "执行失败",
            "status": "✅ 错误指示"
        },
        {
            "condition": "取消状态",
            "display": "显示已取消",
            "example": "已取消",
            "status": "✅ 取消指示"
        },
        {
            "condition": "等待状态",
            "display": "显示等待开始",
            "example": "等待开始",
            "status": "✅ 等待指示"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['condition']}")
        print(f"   显示: {scenario['display']}")
        print(f"   示例: {scenario['example']}")
        print(f"   状态: {scenario['status']}")
        print()

def demonstrate_backend_fix():
    """演示后端修复"""
    print(f"\n🔧 后端修复说明:")
    print("=" * 60)
    
    print(f"🐛 原始问题:")
    print(f"   条件: if status == TaskStatus.RUNNING and \"started_at\" not in self.tasks[task_id]")
    print(f"   问题: 'started_at' 字段在创建时就存在（值为None），所以条件永远为False")
    print(f"   结果: started_at 永远不会被设置")
    
    print(f"\n✅ 修复方案:")
    print(f"   条件: if status == TaskStatus.RUNNING and self.tasks[task_id][\"started_at\"] is None")
    print(f"   逻辑: 检查 started_at 的值是否为 None，而不是检查字段是否存在")
    print(f"   结果: 当任务状态变为 RUNNING 时，正确设置 started_at")
    
    print(f"\n🔄 执行流程:")
    print(f"   1. 创建任务: started_at = None")
    print(f"   2. 开始执行: status = RUNNING, started_at = 当前时间")
    print(f"   3. 任务完成: status = COMPLETED, completed_at = 当前时间")
    print(f"   4. 前端显示: 计算 completed_at - started_at")

def demonstrate_frontend_improvements():
    """演示前端改进"""
    print(f"\n🖥️  前端改进说明:")
    print("=" * 60)
    
    print(f"🎯 改进要点:")
    print(f"   ✅ 多层次判断逻辑")
    print(f"   ✅ 备用时间来源（result.duration_seconds）")
    print(f"   ✅ 状态相关的显示文本")
    print(f"   ✅ 实时计算运行中任务的时长")
    
    print(f"\n📋 判断优先级:")
    print(f"   1. 优先: started_at + completed_at（最准确）")
    print(f"   2. 次选: started_at + 当前时间（运行中任务）")
    print(f"   3. 备用: result.duration_seconds（历史数据）")
    print(f"   4. 兜底: 状态相关的文本显示")
    
    print(f"\n🎨 显示效果:")
    print(f"   正常完成: \"420秒\"")
    print(f"   运行中: \"180秒 (进行中)\"")
    print(f"   已完成: \"已完成\"")
    print(f"   执行失败: \"执行失败\"")
    print(f"   已取消: \"已取消\"")
    print(f"   等待中: \"等待开始\"")

def test_scenarios_summary():
    """测试场景总结"""
    print(f"\n🧪 测试场景总结:")
    print("=" * 60)
    
    test_cases = [
        ("正常完成任务", "有完整时间信息", "显示精确执行时长"),
        ("历史完成任务", "无started_at，有duration_seconds", "使用结果中的时长"),
        ("老旧完成任务", "无任何时间信息", "显示'已完成'状态"),
        ("运行中任务", "有started_at", "实时计算当前时长"),
        ("新建运行任务", "无started_at", "显示'等待开始'"),
        ("失败任务", "任何时间状态", "显示'执行失败'"),
        ("取消任务", "任何时间状态", "显示'已取消'"),
        ("等待任务", "无时间信息", "显示'等待开始'")
    ]
    
    for i, (scenario, condition, expected) in enumerate(test_cases, 1):
        print(f"{i}. {scenario}")
        print(f"   条件: {condition}")
        print(f"   预期: {expected}")
        print()

if __name__ == "__main__":
    print("🔍 执行时长修复测试")
    print("=" * 60)
    
    # 创建测试场景
    test_scenarios, test_results = create_test_scenarios()
    print(f"✅ 创建了 {len(test_scenarios)} 个测试场景")
    print(f"✅ 创建了 {len(test_results)} 个测试结果")
    
    # 分析逻辑
    analyze_duration_logic()
    
    # 演示修复
    demonstrate_backend_fix()
    demonstrate_frontend_improvements()
    
    # 测试场景
    test_scenarios_summary()
    
    print(f"\n" + "=" * 60)
    print("✅ 修复完成")
    print(f"\n🎯 修复总结:")
    print("1. ✅ 修复后端 started_at 设置逻辑")
    print("2. ✅ 增强前端执行时长计算逻辑")
    print("3. ✅ 添加多种备用显示方案")
    print("4. ✅ 提供状态相关的友好提示")
    print("5. ✅ 支持实时计算运行中任务时长")
    print(f"\n💡 用户体验:")
    print("• 不再显示'未开始'的错误信息")
    print("• 根据任务状态显示合适的时长信息")
    print("• 运行中任务实时更新执行时长")
