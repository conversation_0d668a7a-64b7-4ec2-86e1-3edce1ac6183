#!/usr/bin/env python3
"""
验证新的前缀提取逻辑
"""

def extract_prefix(filename):
    """提取CSV文件名第一个下划线前的内容"""
    if not filename:
        return None
    
    # 移除文件扩展名
    filename_without_ext = filename.replace('.csv', '').replace('.CSV', '')
    
    # 提取第一个下划线前的内容
    if '_' in filename_without_ext:
        clean_prefix = filename_without_ext.split('_')[0]
    else:
        clean_prefix = filename_without_ext
    
    # 只保留字母、数字、下划线和连字符
    clean_prefix = ''.join(c for c in clean_prefix if c.isalnum() or c in '_-')
    
    # 限制长度并确保不为空
    clean_prefix = clean_prefix[:15]
    return clean_prefix if clean_prefix else None

# 测试用例
test_cases = [
    "traffic_data_2024.csv",
    "network_analysis_tcp.csv", 
    "test_file_123.csv",
    "singlename.csv",
    "data_processing_final.csv",
    "log_2024_01_15.csv",
    "no_underscore.csv",
    "special@#$_chars.csv",
    "123_numbers.csv",
    "very_long_filename_test.csv"
]

print("🔍 CSV文件名前缀提取测试")
print("=" * 60)
print("规则: 提取第一个下划线前的内容")
print("=" * 60)

for filename in test_cases:
    prefix = extract_prefix(filename)
    print(f"文件名: {filename:<30} → 前缀: '{prefix}'")

print("\n" + "=" * 60)
print("✅ 提取逻辑验证完成")
