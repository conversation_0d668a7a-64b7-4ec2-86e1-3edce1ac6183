# AI智能清洗策略系统 API 文档

## 基础信息

**基础URL**: `http://localhost:8000`

**认证方式**: Bear<PERSON> (JWT)

**Content-Type**: `application/json` (除非特别说明)

---

## 1. 认证模块 (/auth)

### 1.1 用户登录

**端点**: `POST /auth/login`

**描述**: 用户登录获取访问令牌

**请求方法**: POST

**请求头**:
```
Content-Type: application/x-www-form-urlencoded
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| username | string | 用户名 | 是 |
| password | string | 密码 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### 1.2 修改密码

**端点**: `POST /auth/change_password`

**描述**: 当前用户修改自己的密码，需验证原密码并确保新密码符合复杂度要求

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: application/json
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| username | string | 当前用户名 | 是 |
| old_password | string | 原密码 | 是 |
| new_password | string | 新密码 | 是 |
| confirm_password | string | 确认新密码 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/auth/change_password" \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "old_password": "old_pass", "new_password": "NewPass123!", "confirm_password": "NewPass123!"}'
```

**响应示例**:
```json
{
  "message": "密码修改成功，请重新登录！"
}
```

### 1.3 添加用户

**端点**: `POST /auth/add_user`

**描述**: 管理员添加新用户（仅admin用户可操作）

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: application/json
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| username | string | 当前用户名（必须是admin） | 是 |
| new_username | string | 新用户名 | 是 |
| new_user_password | string | 新用户密码 | 是 |
| confirm_user_password | string | 确认新用户密码 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/auth/add_user" \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "new_username": "newuser", "new_user_password": "NewUser123!", "confirm_user_password": "NewUser123!"}'
```

**响应示例**:
```json
{
  "message": "用户 newuser 添加成功！"
}
```

### 1.4 获取用户列表

**端点**: `GET /auth/users`

**描述**: 获取所有用户信息

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/auth/users" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "admin": {
    "password": "hashed_password",
    "is_default": false
  },
  "user1": {
    "password": "hashed_password",
    "is_default": false
  }
}
```

---

## 2. 数据清洗模块 (/data_cleaning)

### 2.1 清洗数据

**端点**: `POST /data_cleaning/clean_data`

**描述**: 清洗流量数据文件，支持上传文件或选择本地文件

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| files | file[] | 上传的文件列表 | 否 |
| folder_path | string | 本地文件夹路径 | 否 |
| selected_files | string[] | 选择的本地文件列表 | 否 |
| output_dir | string | 输出目录路径 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/data_cleaning/clean_data" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "files=@traffic_data.txt" \
  -F "output_dir=/data/output"
```

**响应示例**:
```json
{
  "message": "CSV文件已保存到: /data/output/traffic_data_20240115.csv"
}
```

### 2.2 列出文件

**端点**: `GET /data_cleaning/list_files`

**描述**: 列出指定文件夹中的TXT文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| folder_path | string | 文件夹路径 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_cleaning/list_files?folder_path=/data/input" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "files": ["traffic_data1.txt", "traffic_data2.txt"]
}
```

### 2.3 批量分析

**端点**: `POST /data_cleaning/batch_analyze`

**描述**: 启动批量流量数据分析任务

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: application/json
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| tasks | object[] | 批量任务列表 | 是 |
| tasks[].customer | string | 客户名称 | 是 |
| tasks[].input_dir | string | 输入目录路径 | 是 |
| tasks[].output_dir | string | 输出目录路径 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/data_cleaning/batch_analyze" \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "tasks": [
      {
        "customer": "customer1",
        "input_dir": "/data/input/customer1",
        "output_dir": "/data/output/customer1"
      }
    ]
  }'
```

**响应示例**:
```json
{
  "success": true,
  "batch_id": "customer1_batch_uuid",
  "message": "批量分析任务已启动"
}
```

### 2.4 获取批量任务状态

**端点**: `GET /data_cleaning/batch_status/{batch_id}`

**描述**: 获取批量分析任务的执行状态

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| batch_id | string | 批量任务ID | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_cleaning/batch_status/customer1_batch_uuid" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "batch_id": "customer1_batch_uuid",
  "status": "completed",
  "progress": 100,
  "current_step": "任务完成",
  "created_at": "2024-01-15T10:30:00",
  "completed_at": "2024-01-15T10:35:00",
  "error": null
}
```

---

## 3. 模型训练模块 (/model_training)

### 3.1 训练模型（同步）

**端点**: `POST /model_training/train`

**描述**: 同步训练机器学习模型

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| file | file | 上传的CSV文件 | 否 |
| csv_dir | string | 本地CSV文件目录 | 否 |
| selected_file | string | 选择的本地文件名 | 否 |
| selected_prots | string | 选择的协议（JSON字符串） | 是 |
| selected_datatypes | string | 选择的数据类型（JSON字符串） | 是 |
| learning_rate | float | 学习率 | 是 |
| batch_size | int | 批量大小 | 是 |
| epochs | int | 训练轮数 | 是 |
| sequence_length | int | 序列长度 | 否 |
| hidden_size | int | 隐藏层大小 | 否 |
| num_layers | int | 层数 | 否 |
| dropout | float | Dropout率 | 否 |
| output_folder | string | 输出文件夹 | 否 |
| auto_generate_template | bool | 是否自动生成清洗模板 | 否 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_training/train" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "file=@traffic_data.csv" \
  -F "selected_prots=[\"tcp syn\"]" \
  -F "selected_datatypes={\"tcp syn\":[\"packets_per_sec\"]}" \
  -F "learning_rate=0.001" \
  -F "batch_size=32" \
  -F "epochs=100"
```

**响应示例**:
```json
{
  "message": "模型训练完成",
  "results": [
    {
      "protocol": "tcp syn",
      "data_type": "packets_per_sec",
      "r2_score": 0.95,
      "model_path": "/data/output/model.pth",
      "model_id": "traffic_model_20240115_143022"
    }
  ]
}
```

### 3.2 训练模型（异步）

**端点**: `POST /model_training/train_async`

**描述**: 异步训练机器学习模型

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**: 与同步训练相同

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_training/train_async" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "file=@traffic_data.csv" \
  -F "selected_prots=[\"tcp syn\"]" \
  -F "selected_datatypes={\"tcp syn\":[\"packets_per_sec\"]}" \
  -F "learning_rate=0.001" \
  -F "batch_size=32" \
  -F "epochs=100"
```

**响应示例**:
```json
{
  "success": true,
  "task_id": "traffic_uuid",
  "message": "训练任务已启动，请通过任务ID查询进度"
}
```

### 3.3 列出CSV文件

**端点**: `GET /model_training/list_csv_files`

**描述**: 列出可用于训练的CSV文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| csv_dir | string | CSV文件目录 | 否 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_training/list_csv_files?csv_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "files": ["traffic_data.csv", "network_analysis.csv"]
}
```

---

## 4. 模型预测模块 (/model_prediction)

### 4.1 模型预测（同步）

**端点**: `POST /model_prediction/predict`

**描述**: 使用训练好的模型进行预测

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| file | file | 上传的CSV文件 | 否 |
| csv_dir | string | 本地CSV文件目录 | 否 |
| selected_file | string | 选择的本地文件名 | 否 |
| model_dir | string | 模型文件目录 | 是 |
| model_filename | string | 模型文件名 | 是 |
| output_folder | string | 输出文件夹 | 否 |
| auto_generate_template | bool | 是否自动生成清洗模板 | 否 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_prediction/predict" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "file=@test_data.csv" \
  -F "model_dir=/data/output" \
  -F "model_filename=model.pth"
```

**响应示例**:
```json
{
  "suggested_threshold": 1500,
  "model_name": "tcp_syn_packets_per_sec",
  "message": "预测完成",
  "anomaly_count": 25,
  "duration_seconds": 12.5
}
```

### 4.2 模型预测（异步）

**端点**: `POST /model_prediction/predict_async`

**描述**: 异步模型预测

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**: 与同步预测相同

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_prediction/predict_async" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "file=@test_data.csv" \
  -F "model_dir=/data/output" \
  -F "model_filename=model.pth"
```

**响应示例**:
```json
{
  "success": true,
  "task_id": "test_uuid",
  "message": "预测任务已启动"
}
```

### 4.3 多模型预测（异步）

**端点**: `POST /model_prediction/predict_multi_async`

**描述**: 使用多个模型进行异步预测

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| file | file | 上传的CSV文件 | 否 |
| csv_dir | string | 本地CSV文件目录 | 否 |
| selected_file | string | 选择的本地文件名 | 否 |
| models_config | string | 模型配置（JSON字符串） | 是 |
| output_folder | string | 输出文件夹 | 否 |
| auto_generate_template | bool | 是否自动生成清洗模板 | 否 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_prediction/predict_multi_async" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "file=@test_data.csv" \
  -F "models_config=[{\"model_dir\":\"/data/output\",\"model_filename\":\"model1.pth\"},{\"model_dir\":\"/data/output\",\"model_filename\":\"model2.pth\"}]"
```

**响应示例**:
```json
{
  "success": true,
  "task_id": "test_multi_uuid",
  "message": "多模型预测任务已启动"
}
```

### 4.4 列出CSV文件

**端点**: `GET /model_prediction/list_csv_files`

**描述**: 列出可用于预测的CSV文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| csv_dir | string | CSV文件目录 | 否 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_prediction/list_csv_files?csv_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "files": ["traffic_data.csv", "test_data.csv"]
}
```

### 4.5 列出模型文件

**端点**: `GET /model_prediction/list_model_files`

**描述**: 列出可用的模型文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| model_dir | string | 模型文件目录 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_prediction/list_model_files?model_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "model_files": ["model1.pth", "model2.pth"]
}
```

### 4.6 获取匹配文件

**端点**: `GET /model_prediction/get_matching_files`

**描述**: 获取与指定模型文件匹配的相关文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| model_filename | string | 模型文件名 | 是 |
| model_dir | string | 模型文件目录 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_prediction/get_matching_files?model_filename=model.pth&model_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "model_file": "model.pth",
  "params_file": "model_params.json",
  "scaler_file": "model_scaler.pkl"
}
```

---

## 5. 模型仓库模块 (/model_registry)

### 5.1 注册模型

**端点**: `POST /model_registry/register`

**描述**: 注册新模型到仓库

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: application/json
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| model_name | string | 模型名称 | 是 |
| model_type | string | 模型类型 | 是 |
| data_file | string | 训练数据文件 | 是 |
| protocol | string | 协议类型 | 是 |
| data_type | string | 数据类型 | 是 |
| r2_score | float | R2评分 | 是 |
| model_path | string | 模型文件路径 | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/model_registry/register" \
  -H "Authorization: Bearer {TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "tcp_syn_model",
    "model_type": "GRU",
    "data_file": "traffic_data",
    "protocol": "tcp syn",
    "data_type": "packets_per_sec",
    "r2_score": 0.95,
    "model_path": "/data/output/model.pth"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "模型注册成功",
  "model_id": "traffic_model_20240115_143022"
}
```

### 5.2 获取模型列表

**端点**: `GET /model_registry/list`

**描述**: 获取所有注册的模型列表

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_registry/list" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "models": [
    {
      "model_id": "traffic_model_20240115_143022",
      "model_name": "tcp_syn_model",
      "protocol": "tcp syn",
      "data_type": "packets_per_sec",
      "r2_score": 0.95,
      "created_time": "2024-01-15 14:30:22"
    }
  ],
  "total_count": 1
}
```

### 5.3 获取模型详情

**端点**: `GET /model_registry/detail/{model_id}`

**描述**: 获取指定模型的详细信息

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| model_id | string | 模型ID | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_registry/detail/traffic_model_20240115_143022" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "model": {
    "model_id": "traffic_model_20240115_143022",
    "model_name": "tcp_syn_model",
    "protocol": "tcp syn",
    "data_type": "packets_per_sec",
    "r2_score": 0.95,
    "training_duration": 120,
    "model_path": "/data/output/model.pth",
    "created_time": "2024-01-15 14:30:22"
  }
}
```

### 5.4 删除模型

**端点**: `DELETE /model_registry/delete/{model_id}`

**描述**: 删除指定的模型

**请求方法**: DELETE

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| model_id | string | 模型ID | 是 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/model_registry/delete/traffic_model_20240115_143022" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "message": "模型已成功删除"
}
```

### 5.5 获取统计信息

**端点**: `GET /model_registry/statistics`

**描述**: 获取模型仓库的统计信息

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/model_registry/statistics" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "statistics": {
    "total_models": 10,
    "avg_r2_score": 0.92,
    "protocol_distribution": {
      "tcp syn": 5,
      "udp flood": 3,
      "icmp": 2
    }
  }
}
```

---

## 6. 清洗模板模块 (/clean_template)

### 6.1 生成清洗模板

**端点**: `POST /clean_template/generate_template`

**描述**: 根据训练或预测结果生成清洗模板

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| results_file | string | 结果文件路径 | 是 |
| output_folder | string | 输出文件夹 | 否 |
| template_name | string | 模板名称 | 否 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/clean_template/generate_template" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "results_file=/data/output/results.txt" \
  -F "output_folder=/data/output"
```

**响应示例**:
```json
{
  "success": true,
  "message": "清洗模板生成成功",
  "template_path": "/data/output/traffic_20240115_cleantemplate.json"
}
```

### 6.2 列出清洗模板

**端点**: `GET /clean_template/list_templates`

**描述**: 列出指定文件夹中的清洗模板文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| folder_path | string | 文件夹路径 | 否 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/clean_template/list_templates?folder_path=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "templates": [
    {
      "filename": "traffic_20240115_cleantemplate.json",
      "path": "/data/output/traffic_20240115_cleantemplate.json",
      "size": 1024,
      "modified_time": "2024-01-15 14:30:22"
    }
  ]
}
```

### 6.3 获取模板内容

**端点**: `GET /clean_template/get_template_content`

**描述**: 获取指定清洗模板文件的内容

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| template_path | string | 模板文件路径 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/clean_template/get_template_content?template_path=/data/output/template.json" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "content": {
    "tcp syn": 1500,
    "udp flood": 2000
  }
}
```

### 6.4 更新模板内容

**端点**: `POST /clean_template/update_template`

**描述**: 更新指定清洗模板文件的内容

**请求方法**: POST

**请求头**:
```
Authorization: Bearer {TOKEN}
Content-Type: multipart/form-data
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| template_path | string | 模板文件路径 | 是 |
| template_content | string | 模板内容（JSON字符串） | 是 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/clean_template/update_template" \
  -H "Authorization: Bearer {TOKEN}" \
  -F "template_path=/data/output/template.json" \
  -F "template_content={\"tcp syn\":1600,\"udp flood\":2100}"
```

**响应示例**:
```json
{
  "message": "模板内容已成功更新"
}
```

### 6.5 下载模板

**端点**: `GET /clean_template/download_template`

**描述**: 下载指定的清洗模板文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| template_path | string | 模板文件路径 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/clean_template/download_template?template_path=/data/output/template.json" \
  -H "Authorization: Bearer {TOKEN}" \
  -O
```

**响应**: 文件下载

### 6.6 删除模板

**端点**: `DELETE /clean_template/delete_template`

**描述**: 删除指定的清洗模板文件

**请求方法**: DELETE

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| template_path | string | 模板文件路径 | 是 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/clean_template/delete_template?template_path=/data/output/template.json" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "message": "模板文件 template.json 已成功删除"
}
```

---

## 7. 任务管理模块 (/tasks)

### 7.1 获取所有任务

**端点**: `GET /tasks/tasks`

**描述**: 获取所有任务列表

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/tasks/tasks" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "traffic_uuid",
      "task_type": "training",
      "status": "completed",
      "progress": 100,
      "created_at": "2024-01-15T10:30:00",
      "completed_at": "2024-01-15T10:35:00"
    }
  ],
  "total": 1
}
```

### 7.2 获取任务状态

**端点**: `GET /tasks/task/{task_id}`

**描述**: 获取特定任务的状态

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| task_id | string | 任务ID | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/tasks/task/traffic_uuid" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "task": {
    "task_id": "traffic_uuid",
    "task_type": "training",
    "status": "completed",
    "progress": 100,
    "current_step": "训练完成",
    "created_at": "2024-01-15T10:30:00",
    "completed_at": "2024-01-15T10:35:00",
    "result": {
      "model_id": "traffic_model_20240115_143022",
      "r2_score": 0.95
    }
  }
}
```

### 7.3 取消任务

**端点**: `DELETE /tasks/task/{task_id}`

**描述**: 取消正在运行的任务

**请求方法**: DELETE

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| task_id | string | 任务ID | 是 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/tasks/task/traffic_uuid" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "message": "任务已取消"
}
```

### 7.4 删除已完成任务

**端点**: `DELETE /tasks/task/{task_id}/delete`

**描述**: 删除已完成的任务

**请求方法**: DELETE

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**路径参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| task_id | string | 任务ID | 是 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/tasks/task/traffic_uuid/delete" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "message": "已删除任务 traffic_uuid",
  "deleted_task_id": "traffic_uuid"
}
```

### 7.5 获取运行中任务

**端点**: `GET /tasks/tasks/running`

**描述**: 获取正在运行的任务

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/tasks/tasks/running" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "training_uuid",
      "task_type": "training",
      "status": "running",
      "progress": 45,
      "current_step": "训练中..."
    }
  ],
  "count": 1
}
```

### 7.6 获取已完成任务

**端点**: `GET /tasks/completed`

**描述**: 获取已完成的任务

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X GET "http://localhost:8000/tasks/completed" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "traffic_uuid",
      "task_type": "training",
      "status": "completed",
      "progress": 100,
      "completed_at": "2024-01-15T10:35:00"
    }
  ],
  "count": 1
}
```

### 7.7 清空已完成任务

**端点**: `DELETE /tasks/completed`

**描述**: 清空所有已完成的任务

**请求方法**: DELETE

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/tasks/completed" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "success": true,
  "message": "已清空 5 个已完成的任务",
  "cleared_count": 5,
  "cleared_task_ids": ["task1", "task2", "task3", "task4", "task5"]
}
```

---

## 8. 数据查询模块 (/data_query)

### 8.1 列出CSV文件

**端点**: `GET /data_query/list_csv_files`

**描述**: 列出指定目录中的CSV文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| csv_dir | string | CSV文件目录 | 否 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_query/list_csv_files?csv_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "csv_files": ["traffic_data.csv", "network_analysis.csv"]
}
```

### 8.2 下载CSV文件

**端点**: `GET /data_query/download_csv`

**描述**: 下载指定的CSV文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| csv_dir | string | CSV文件目录 | 是 |
| csv_file | string | CSV文件名 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_query/download_csv?csv_dir=/data/output&csv_file=traffic_data.csv" \
  -H "Authorization: Bearer {TOKEN}" \
  -O
```

**响应**: 文件下载

### 8.3 列出结果文件

**端点**: `GET /data_query/list_result_files`

**描述**: 列出指定目录中的结果文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| result_dir | string | 结果文件目录 | 否 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_query/list_result_files?result_dir=/data/output" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "result_files": ["traffic_20240115_results.txt", "network_20240115_results.txt"]
}
```

### 8.4 获取结果文件内容

**端点**: `GET /data_query/get_result_content`

**描述**: 获取指定结果文件的内容

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| result_dir | string | 结果文件目录 | 是 |
| result_file | string | 结果文件名 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_query/get_result_content?result_dir=/data/output&result_file=results.txt" \
  -H "Authorization: Bearer {TOKEN}"
```

**响应示例**:
```json
{
  "content": "tcp syn 1500\nudp flood 2000\n"
}
```

### 8.5 下载结果文件

**端点**: `GET /data_query/download_result`

**描述**: 下载指定的结果文件

**请求方法**: GET

**请求头**:
```
Authorization: Bearer {TOKEN}
```

**请求参数**:
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| result_dir | string | 结果文件目录 | 是 |
| result_file | string | 结果文件名 | 是 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/data_query/download_result?result_dir=/data/output&result_file=results.txt" \
  -H "Authorization: Bearer {TOKEN}" \
  -O
```

**响应**: 文件下载

---

## 9. 系统信息

### 9.1 根路径

**端点**: `GET /`

**描述**: 获取系统基本信息

**请求方法**: GET

**请求示例**:
```bash
curl -X GET "http://localhost:8000/"
```

**响应示例**:
```json
{
  "message": "AI智能清洗策略系统 API"
}
```

---

## 错误响应格式

所有API在发生错误时都会返回统一的错误格式：

```json
{
  "detail": "错误描述信息"
}
```

**常见HTTP状态码**:
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（Token无效或过期）
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 密码复杂度要求

系统密码必须满足以下复杂度要求：
- 至少12个字符
- 至少包含一个大写字母
- 至少包含一个小写字母
- 至少包含一个数字
- 至少包含一个特殊字符（!@#$%^&*(),.?":{}|<>）

---

## 文件上传说明

- 支持的文件格式：CSV、TXT
- 文件大小限制：根据服务器配置
- 编码格式：UTF-8
- 上传方式：multipart/form-data

---

## 任务状态说明

- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 执行完成
- `failed`: 执行失败
- `cancelled`: 已取消

---

## 模型ID命名规则

模型ID采用以下命名格式：
- **有CSV文件名时**: `{csv_prefix}_model_{timestamp}`
- **没有CSV文件名时**: `model_{timestamp}`

**示例**:
- CSV文件: `traffic_data_20240115.csv` → 模型ID: `traffic_model_20240115_143022`
- CSV文件: `network_analysis.csv` → 模型ID: `network_model_20240115_143022`

---

## 版本信息

**API版本**: v1.0
**最后更新**: 2024-01-15
**文档版本**: 1.0.0
