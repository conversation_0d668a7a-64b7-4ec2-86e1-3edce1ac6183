#!/usr/bin/env python3
import os
import glob

def count_lines_in_file(file_path):
    """统计单个文件的行数"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            return len(f.readlines())
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return 0

def count_project_lines():
    """统计整个项目的代码行数"""
    
    # 定义要统计的文件类型和目录
    file_patterns = {
        'Python后端': ['backend/*.py'],
        'TypeScript/React前端': [
            'frontend-react-stable/src/**/*.tsx',
            'frontend-react-stable/src/**/*.ts',
            'frontend-react-stable/src/**/*.css'
        ],
        '配置文件': [
            'frontend-react-stable/package.json',
            'frontend-react-stable/tsconfig.json'
        ],
        '文档': ['*.md'],
        '其他Python脚本': [
            '*.py',
            '!backend/*.py',  # 排除backend目录下的文件
            '!count_lines.py'  # 排除当前统计脚本
        ]
    }
    
    total_lines = 0
    category_stats = {}
    
    print("=" * 60)
    print("AI智能清洗策略系统 - 代码行数统计")
    print("=" * 60)
    
    for category, patterns in file_patterns.items():
        category_lines = 0
        category_files = []
        
        for pattern in patterns:
            if pattern.startswith('!'):
                # 跳过排除的模式
                continue
                
            if '**' in pattern:
                # 递归搜索
                files = glob.glob(pattern, recursive=True)
            else:
                files = glob.glob(pattern)
            
            for file_path in files:
                if os.path.isfile(file_path):
                    lines = count_lines_in_file(file_path)
                    category_lines += lines
                    category_files.append((file_path, lines))
        
        if category_files:
            category_stats[category] = {
                'lines': category_lines,
                'files': category_files
            }
            total_lines += category_lines
    
    # 输出统计结果
    for category, stats in category_stats.items():
        print(f"\n📁 {category}:")
        print(f"   总行数: {stats['lines']:,} 行")
        print(f"   文件数: {len(stats['files'])} 个")
        
        # 显示每个文件的行数
        for file_path, lines in sorted(stats['files'], key=lambda x: x[1], reverse=True):
            print(f"   - {file_path}: {lines:,} 行")
    
    print("\n" + "=" * 60)
    print(f"🎯 项目总计: {total_lines:,} 行代码")
    print("=" * 60)
    
    # 生成详细报告
    print(f"\n📊 详细统计:")
    for category, stats in sorted(category_stats.items(), key=lambda x: x[1]['lines'], reverse=True):
        percentage = (stats['lines'] / total_lines) * 100
        print(f"   {category}: {stats['lines']:,} 行 ({percentage:.1f}%)")
    
    return total_lines, category_stats

if __name__ == "__main__":
    count_project_lines()
