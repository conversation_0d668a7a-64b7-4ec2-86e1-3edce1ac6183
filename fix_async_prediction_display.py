#!/usr/bin/env python3
"""
修复异步预测结果显示问题的脚本
"""

import json
import os
import requests
from datetime import datetime, timed<PERSON>ta

def diagnose_problem():
    """诊断问题"""
    print("🔍 诊断异步预测结果显示问题")
    print("=" * 60)
    
    issues = []
    
    # 1. 检查JSON文件
    try:
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        prediction_tasks = {k: v for k, v in tasks.items() if v.get('task_type') == 'prediction' and v.get('status') == 'completed'}
        prediction_results = {k: v for k, v in results.items() if 'predictions' in v}
        
        print(f"📊 JSON文件状态:")
        print(f"   已完成预测任务: {len(prediction_tasks)}")
        print(f"   预测结果数据: {len(prediction_results)}")
        
        # 检查匹配情况
        matched = 0
        unmatched_tasks = []
        for task_id in prediction_tasks:
            if task_id in prediction_results:
                matched += 1
            else:
                unmatched_tasks.append(task_id)
        
        print(f"   匹配的任务: {matched}")
        if unmatched_tasks:
            print(f"   ❌ 无结果的任务: {len(unmatched_tasks)}")
            issues.append(f"有{len(unmatched_tasks)}个预测任务没有结果数据")
        
    except Exception as e:
        print(f"❌ JSON文件检查失败: {e}")
        issues.append("JSON文件读取失败")
    
    # 2. 检查API响应
    try:
        response = requests.get("http://localhost:8000/tasks/completed", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                api_tasks = data.get('tasks', [])
                api_prediction_tasks = [t for t in api_tasks if t.get('task_type') == 'prediction']
                api_tasks_with_results = [t for t in api_prediction_tasks if t.get('result')]
                
                print(f"\n📡 API响应状态:")
                print(f"   API预测任务数: {len(api_prediction_tasks)}")
                print(f"   有结果的任务数: {len(api_tasks_with_results)}")
                
                if len(api_tasks_with_results) == 0:
                    issues.append("API返回的预测任务都没有result字段")
                elif len(api_tasks_with_results) < len(prediction_results):
                    issues.append("API返回的结果数量少于JSON文件中的结果数量")
            else:
                issues.append("API返回失败")
        else:
            issues.append(f"API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API检查失败: {e}")
        issues.append("API请求失败")
    
    return issues

def create_working_prediction_task():
    """创建一个确保能工作的预测任务"""
    print(f"\n🔧 创建确保能工作的预测任务...")
    
    # 使用简单的task_id，避免特殊字符
    task_id = f"prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 创建任务数据
    task_data = {
        "task_id": task_id,
        "task_type": "prediction",
        "status": "completed",  # 确保使用字符串，匹配TaskStatus.COMPLETED
        "progress": 100,
        "params": {
            "csv_filename": "working_test.csv",
            "model_names": ["TCP_spt_sip_dip"]
        },
        "created_at": datetime.now().isoformat(),
        "started_at": (datetime.now() - timedelta(minutes=2)).isoformat(),
        "completed_at": (datetime.now() - timedelta(minutes=1)).isoformat(),
        "updated_at": datetime.now().isoformat(),
        "current_step": "预测完成",
        "total_steps": None,
        "error": None
    }
    
    # 创建结果数据
    result_data = {
        "predictions": [
            {
                "timestamp": "2025-07-24 14:00:00",
                "packets_per_sec": 1200,
                "packets_per_sec_smooth": 1180,
                "pred_smooth": 1220,
                "threshold": 1800,
                "is_anomaly": False
            },
            {
                "timestamp": "2025-07-24 14:01:00",
                "packets_per_sec": 2200,
                "packets_per_sec_smooth": 2180,
                "pred_smooth": 1230,
                "threshold": 1800,
                "is_anomaly": True
            },
            {
                "timestamp": "2025-07-24 14:02:00",
                "packets_per_sec": 1100,
                "packets_per_sec_smooth": 1120,
                "pred_smooth": 1240,
                "threshold": 1800,
                "is_anomaly": False
            }
        ],
        "anomaly_count": 1,
        "suggested_threshold": 1800,
        "model_name": "TCP_spt_sip_dip",
        "message": "修复测试预测成功",
        "duration_seconds": 120.0,
        "cpu_percent": 15.0,
        "memory_mb": 200.0,
        "gpu_memory_mb": 0,
        "gpu_utilization_percent": 0
    }
    
    try:
        # 读取现有数据
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 添加新数据
        tasks[task_id] = task_data
        results[task_id] = result_data
        
        # 保存数据
        with open("task_storage.json", 'w', encoding='utf-8') as f:
            json.dump(tasks, f, ensure_ascii=False, indent=2)
        
        with open("task_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 创建成功: {task_id}")
        return task_id
        
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")
        return None

def test_backend_reload():
    """测试后端是否重新加载了数据"""
    print(f"\n🔄 测试后端数据重新加载...")
    
    try:
        # 等待一下让后端有时间重新加载
        import time
        time.sleep(2)
        
        response = requests.get("http://localhost:8000/tasks/completed", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tasks = data.get('tasks', [])
                prediction_tasks = [t for t in tasks if t.get('task_type') == 'prediction']
                tasks_with_results = [t for t in prediction_tasks if t.get('result')]
                
                print(f"   📊 API当前状态:")
                print(f"      预测任务数: {len(prediction_tasks)}")
                print(f"      有结果的任务: {len(tasks_with_results)}")
                
                if tasks_with_results:
                    latest_task = tasks_with_results[-1]
                    print(f"      最新任务ID: {latest_task.get('task_id', 'N/A')[:30]}...")
                    print(f"      预测数据点: {len(latest_task.get('result', {}).get('predictions', []))}")
                    return True
                else:
                    print(f"   ❌ 仍然没有带结果的预测任务")
                    return False
            else:
                print(f"   ❌ API返回失败")
                return False
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案")
    print("=" * 60)
    
    solutions = [
        {
            "问题": "后端没有重新加载JSON数据",
            "解决方案": [
                "重启后端服务",
                "检查后端是否有文件监控机制",
                "手动触发数据重新加载"
            ]
        },
        {
            "问题": "预测任务没有结果数据",
            "解决方案": [
                "检查异步预测任务是否正确保存结果",
                "验证task_id在两个JSON文件中一致",
                "确认结果数据格式正确"
            ]
        },
        {
            "问题": "前端过滤逻辑问题",
            "解决方案": [
                "检查getCompletedTasksByType函数",
                "确认过滤条件正确",
                "验证task.result字段存在"
            ]
        },
        {
            "问题": "API路径或格式问题",
            "解决方案": [
                "验证API端点路径正确",
                "检查API返回数据格式",
                "确认前端API调用正确"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"{i}. {solution['问题']}")
        for sol in solution['解决方案']:
            print(f"   • {sol}")
        print()

def create_restart_instructions():
    """创建重启说明"""
    print(f"🔄 重启说明")
    print("=" * 60)
    
    print(f"为了确保修复生效，请按以下步骤操作:")
    print(f"")
    print(f"1. 重启后端服务")
    print(f"   - 停止当前后端进程 (Ctrl+C)")
    print(f"   - 重新启动: python main.py 或 uvicorn main:app --reload")
    print(f"")
    print(f"2. 清除前端缓存")
    print(f"   - 硬刷新页面: Ctrl+F5 或 Ctrl+Shift+R")
    print(f"   - 或清除浏览器缓存")
    print(f"")
    print(f"3. 验证修复效果")
    print(f"   - 打开模型预测页面")
    print(f"   - 切换到'异步预测结果'标签")
    print(f"   - 查看任务选择下拉框")
    print(f"   - 选择最新的预测任务")
    print(f"   - 确认显示预测结果图表")

if __name__ == "__main__":
    print("🔧 异步预测结果显示修复工具")
    print("=" * 60)
    
    # 诊断问题
    issues = diagnose_problem()
    
    if issues:
        print(f"\n❌ 发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    # 创建工作的预测任务
    task_id = create_working_prediction_task()
    
    if task_id:
        # 测试后端重新加载
        if test_backend_reload():
            print(f"\n✅ 修复成功!")
            print(f"   新任务已创建并被后端识别")
        else:
            print(f"\n⚠️  需要重启后端服务")
            create_restart_instructions()
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n" + "=" * 60)
    print("✅ 修复工具运行完成")
    print(f"\n🎯 总结:")
    print("已创建新的测试预测任务，如果问题仍然存在:")
    print("1. 重启后端服务以重新加载JSON数据")
    print("2. 硬刷新前端页面清除缓存")
    print("3. 检查浏览器控制台是否有错误")
    print("4. 验证API返回数据是否包含result字段")
