#!/usr/bin/env python3
"""
测试异步预测结果显示的脚本
"""

import json
import os
from datetime import datetime, timedelta

def create_complete_async_prediction_task():
    """创建完整的异步预测任务和结果"""
    print("🔍 创建完整的异步预测任务和结果")
    print("=" * 60)
    
    base_time = datetime.now()
    task_id = f"async_prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 创建异步预测任务
    prediction_task = {
        task_id: {
            "task_id": task_id,
            "task_type": "prediction",
            "status": "completed",
            "progress": 100,
            "params": {
                "csv_filename": "test_async_prediction.csv",
                "model_names": ["TCP_spt_sip_dip"]
            },
            "created_at": (base_time - timedelta(minutes=5)).isoformat(),
            "started_at": (base_time - timedelta(minutes=4)).isoformat(),
            "completed_at": (base_time - timedelta(minutes=1)).isoformat(),
            "updated_at": (base_time - timedelta(minutes=1)).isoformat(),
            "current_step": "预测完成",
            "total_steps": None,
            "error": None
        }
    }
    
    # 创建异步预测结果 - 格式与同步预测完全一致
    prediction_result = {
        task_id: {
            "predictions": [
                {
                    "timestamp": "2025-07-24 10:00:00",
                    "packets_per_sec": 1500,
                    "packets_per_sec_smooth": 1480,
                    "pred_smooth": 1520,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:01:00",
                    "packets_per_sec": 2800,
                    "packets_per_sec_smooth": 2750,
                    "pred_smooth": 1530,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:02:00",
                    "packets_per_sec": 1200,
                    "packets_per_sec_smooth": 1250,
                    "pred_smooth": 1540,
                    "threshold": 2000,
                    "is_anomaly": False
                },
                {
                    "timestamp": "2025-07-24 10:03:00",
                    "packets_per_sec": 3200,
                    "packets_per_sec_smooth": 3150,
                    "pred_smooth": 1550,
                    "threshold": 2000,
                    "is_anomaly": True
                },
                {
                    "timestamp": "2025-07-24 10:04:00",
                    "packets_per_sec": 1800,
                    "packets_per_sec_smooth": 1820,
                    "pred_smooth": 1560,
                    "threshold": 2000,
                    "is_anomaly": False
                }
            ],
            "anomaly_count": 2,
            "suggested_threshold": 2000,
            "model_name": "TCP_spt_sip_dip",
            "message": "异步预测成功完成",
            "duration_seconds": 240.5,
            "cpu_percent": 18.3,
            "memory_mb": 312.7,
            "gpu_memory_mb": 0,
            "gpu_utilization_percent": 0
        }
    }
    
    return prediction_task, prediction_result, task_id

def save_async_prediction_data(task_data, result_data):
    """保存异步预测数据到JSON文件"""
    print(f"\n💾 保存异步预测数据...")
    
    try:
        # 读取现有数据
        tasks = {}
        results = {}
        
        if os.path.exists("task_storage.json"):
            with open("task_storage.json", 'r', encoding='utf-8') as f:
                tasks = json.load(f)
        
        if os.path.exists("task_results.json"):
            with open("task_results.json", 'r', encoding='utf-8') as f:
                results = json.load(f)
        
        # 合并新数据
        tasks.update(task_data)
        results.update(result_data)
        
        # 保存更新后的数据
        with open("task_storage.json", 'w', encoding='utf-8') as f:
            json.dump(tasks, f, ensure_ascii=False, indent=2)
        
        with open("task_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 数据保存成功")
        print(f"   📊 总任务数: {len(tasks)}")
        print(f"   📊 总结果数: {len(results)}")
        
        # 验证预测任务
        prediction_tasks = [t for t in tasks.values() if t.get('task_type') == 'prediction']
        prediction_results = [r for r in results.values() if 'predictions' in r]
        
        print(f"   📋 预测任务数: {len(prediction_tasks)}")
        print(f"   📋 预测结果数: {len(prediction_results)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")
        return False

def verify_data_structure():
    """验证数据结构"""
    print(f"\n🔍 验证数据结构...")
    
    try:
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 找到最新的预测任务
        prediction_tasks = [(k, v) for k, v in tasks.items() if v.get('task_type') == 'prediction']
        if not prediction_tasks:
            print(f"   ❌ 没有找到预测任务")
            return False
        
        # 按创建时间排序，获取最新的
        prediction_tasks.sort(key=lambda x: x[1].get('created_at', ''), reverse=True)
        latest_task_id, latest_task = prediction_tasks[0]
        
        print(f"   📋 最新预测任务:")
        print(f"      ID: {latest_task_id}")
        print(f"      状态: {latest_task.get('status')}")
        print(f"      类型: {latest_task.get('task_type')}")
        print(f"      创建时间: {latest_task.get('created_at')}")
        
        # 检查对应的结果
        if latest_task_id in results:
            result = results[latest_task_id]
            print(f"   📊 对应的结果:")
            print(f"      有predictions字段: {'predictions' in result}")
            print(f"      预测数据点数: {len(result.get('predictions', []))}")
            print(f"      异常数量: {result.get('anomaly_count', 0)}")
            print(f"      模型名称: {result.get('model_name', 'N/A')}")
            print(f"      建议阈值: {result.get('suggested_threshold', 0)}")
            
            # 检查预测数据格式
            if result.get('predictions'):
                first_prediction = result['predictions'][0]
                print(f"      预测数据字段: {list(first_prediction.keys())}")
                
                # 验证必需字段
                required_fields = ['timestamp', 'packets_per_sec', 'packets_per_sec_smooth', 'pred_smooth', 'threshold', 'is_anomaly']
                missing_fields = [field for field in required_fields if field not in first_prediction]
                
                if missing_fields:
                    print(f"      ❌ 缺失字段: {missing_fields}")
                    return False
                else:
                    print(f"      ✅ 数据格式正确")
            
            return True
        else:
            print(f"   ❌ 没有找到对应的结果数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def simulate_frontend_processing():
    """模拟前端处理逻辑"""
    print(f"\n🖥️  模拟前端处理逻辑...")
    
    try:
        with open("task_storage.json", 'r', encoding='utf-8') as f:
            tasks = json.load(f)
        
        with open("task_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 模拟 get_all_tasks 的逻辑
        all_tasks = []
        for task_id, task_info in tasks.items():
            task_copy = task_info.copy()
            if task_info["status"] == "completed" and task_id in results:
                task_copy["result"] = results[task_id]
            all_tasks.append(task_copy)
        
        # 模拟 getCompletedTasksByType('prediction') 的逻辑
        completed_prediction_tasks = [
            task for task in all_tasks 
            if task.get('task_type') == 'prediction' and task.get('status') == 'completed'
        ]
        
        print(f"   📊 模拟结果:")
        print(f"      总任务数: {len(all_tasks)}")
        print(f"      已完成预测任务数: {len(completed_prediction_tasks)}")
        
        if completed_prediction_tasks:
            latest_task = completed_prediction_tasks[-1]  # 最新的任务
            print(f"   📋 最新预测任务:")
            print(f"      ID: {latest_task.get('task_id')}")
            print(f"      有结果: {'result' in latest_task}")
            
            if 'result' in latest_task:
                result = latest_task['result']
                print(f"      结果字段: {list(result.keys())}")
                
                # 模拟前端的数据转换逻辑
                if 'predictions' in result:
                    async_result = {
                        "predictions": result.get('predictions', []),
                        "anomaly_count": result.get('anomaly_count', 0),
                        "suggested_threshold": result.get('suggested_threshold', 0),
                        "model_name": result.get('model_name', '未知模型'),
                        "duration_seconds": result.get('duration_seconds'),
                        "cpu_percent": result.get('cpu_percent'),
                        "memory_mb": result.get('memory_mb'),
                        "gpu_memory_mb": result.get('gpu_memory_mb'),
                        "gpu_utilization_percent": result.get('gpu_utilization_percent')
                    }
                    
                    print(f"   ✅ 转换后的异步预测结果:")
                    print(f"      预测数据点: {len(async_result['predictions'])}")
                    print(f"      异常数量: {async_result['anomaly_count']}")
                    print(f"      模型名称: {async_result['model_name']}")
                    print(f"      执行时长: {async_result['duration_seconds']}秒")
                    
                    return True
                else:
                    print(f"   ❌ 结果中没有predictions字段")
                    return False
            else:
                print(f"   ❌ 任务没有结果数据")
                return False
        else:
            print(f"   ❌ 没有已完成的预测任务")
            return False
            
    except Exception as e:
        print(f"   ❌ 模拟失败: {e}")
        return False

def provide_troubleshooting_steps():
    """提供故障排除步骤"""
    print(f"\n🔧 故障排除步骤:")
    print("=" * 60)
    
    print(f"1. 检查后端服务状态")
    print(f"   - 确认后端服务正在运行")
    print(f"   - 检查服务日志是否有错误")
    print(f"   - 验证API端点是否可访问")
    
    print(f"\n2. 检查数据完整性")
    print(f"   - 验证JSON文件格式正确")
    print(f"   - 确认预测任务状态为completed")
    print(f"   - 检查结果数据是否存在")
    
    print(f"\n3. 检查前端逻辑")
    print(f"   - 打开浏览器开发者工具")
    print(f"   - 查看网络请求是否成功")
    print(f"   - 检查控制台是否有错误")
    print(f"   - 验证数据是否正确获取")
    
    print(f"\n4. 手动验证步骤")
    print(f"   - 访问模型预测页面")
    print(f"   - 切换到'异步预测结果'标签")
    print(f"   - 查看任务选择下拉框")
    print(f"   - 选择最新的预测任务")
    print(f"   - 检查是否显示预测结果")

if __name__ == "__main__":
    print("🔍 异步预测结果显示测试")
    print("=" * 60)
    
    # 创建完整的异步预测任务和结果
    task_data, result_data, task_id = create_complete_async_prediction_task()
    
    # 保存数据
    if save_async_prediction_data(task_data, result_data):
        # 验证数据结构
        if verify_data_structure():
            # 模拟前端处理
            if simulate_frontend_processing():
                print(f"\n✅ 测试成功!")
                print(f"   创建的任务ID: {task_id}")
                print(f"   数据格式: 正确")
                print(f"   前端处理: 正常")
            else:
                print(f"\n❌ 前端处理模拟失败")
        else:
            print(f"\n❌ 数据结构验证失败")
    else:
        print(f"\n❌ 数据保存失败")
    
    # 提供故障排除步骤
    provide_troubleshooting_steps()
    
    print(f"\n" + "=" * 60)
    print("✅ 测试完成")
    print(f"\n🎯 下一步:")
    print("1. 重启后端服务")
    print("2. 打开模型预测页面")
    print("3. 切换到异步预测结果标签")
    print("4. 查看是否显示新创建的预测任务")
    print("5. 选择任务查看预测结果")
