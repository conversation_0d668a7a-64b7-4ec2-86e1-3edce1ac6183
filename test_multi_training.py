#!/usr/bin/env python3
"""
多CSV文件训练功能测试脚本
"""

import requests
import json
import time
import os

# 配置
BASE_URL = "http://localhost:8000"
TEST_DATA_DIR = "/tmp/test_data"
OUTPUT_DIR = "/tmp/test_output"

def create_test_data():
    """创建测试用的CSV数据"""
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    
    # 确保测试目录存在
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 生成测试数据
    start_time = datetime(2025, 3, 1)
    
    for i in range(3):
        # 生成时间序列
        timestamps = []
        packets = []
        
        for j in range(1000):  # 1000个数据点
            timestamp = start_time + timedelta(seconds=j*120)  # 每2分钟一个点
            timestamps.append(int(timestamp.timestamp()))
            # 生成随机包数据，加入一些模式
            base_packets = 100 + 50 * np.sin(j * 0.1) + np.random.normal(0, 10)
            packets.append(max(0, int(base_packets)))
        
        # 创建DataFrame
        data = {
            'timestamp': timestamps,
            'srcaddress': ['************'] * 1000,
            'dstaddress': [f'10.0.0.{i+1}'] * 1000,
            'srcport': [8080] * 1000,
            'dstport': [80] * 1000,
            'protocol': ['TCP'] * 1000,
            'packetssam': packets,
            'tcpflags': [2] * 1000  # SYN flags
        }
        
        df = pd.DataFrame(data)
        csv_path = os.path.join(TEST_DATA_DIR, f'test_data_{i+1}.csv')
        df.to_csv(csv_path, index=False)
        print(f"创建测试文件: {csv_path}")

def test_multi_training_sync():
    """测试同步多文件训练"""
    print("\n=== 测试同步多文件训练 ===")
    
    # 准备数据源配置
    data_sources = [
        {
            "id": "1",
            "type": "local",
            "csvDir": TEST_DATA_DIR,
            "selectedFile": "test_data_1.csv",
            "outputFolder": os.path.join(OUTPUT_DIR, "model1")
        },
        {
            "id": "2", 
            "type": "local",
            "csvDir": TEST_DATA_DIR,
            "selectedFile": "test_data_2.csv",
            "outputFolder": os.path.join(OUTPUT_DIR, "model2")
        }
    ]
    
    # 准备请求数据
    form_data = {
        'data_sources': json.dumps(data_sources),
        'selected_prots': json.dumps(['TCP']),
        'selected_datatypes': json.dumps({'TCP': ['len_dpt_syn']}),
        'learning_rate': '0.001',
        'batch_size': '32',
        'epochs': '10',  # 减少epochs用于测试
        'sequence_length': '10',
        'hidden_size': '50',
        'num_layers': '2',
        'dropout': '0.2',
        'auto_generate_template': 'false'
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{BASE_URL}/model_training/train_multi",
            data=form_data,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 同步多文件训练成功!")
            print(f"处理结果: {result.get('message', 'N/A')}")
            
            # 检查结果
            if 'results' in result:
                for source_id, source_result in result['results'].items():
                    print(f"\n数据源 {source_id}:")
                    if 'error' in source_result:
                        print(f"  ❌ 错误: {source_result['error']}")
                    else:
                        print(f"  ✅ 训练成功")
                        if 'results' in source_result:
                            for protocol_key in source_result['results'].keys():
                                print(f"    - {protocol_key}: 训练完成")
            
            return True
        else:
            print(f"❌ 同步训练失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 同步训练异常: {e}")
        return False

def test_multi_training_async():
    """测试异步多文件训练"""
    print("\n=== 测试异步多文件训练 ===")
    
    # 准备数据源配置
    data_sources = [
        {
            "id": "3",
            "type": "local", 
            "csvDir": TEST_DATA_DIR,
            "selectedFile": "test_data_3.csv",
            "outputFolder": os.path.join(OUTPUT_DIR, "model3")
        }
    ]
    
    # 准备请求数据
    form_data = {
        'data_sources': json.dumps(data_sources),
        'selected_prots': json.dumps(['TCP']),
        'selected_datatypes': json.dumps({'TCP': ['len_dpt_syn']}),
        'learning_rate': '0.001',
        'batch_size': '32',
        'epochs': '10',
        'sequence_length': '10',
        'hidden_size': '50',
        'num_layers': '2',
        'dropout': '0.2',
        'auto_generate_template': 'false'
    }
    
    try:
        # 发送异步请求
        response = requests.post(
            f"{BASE_URL}/model_training/train_multi_async",
            data=form_data
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 异步多文件训练任务已启动: {task_id}")
            
            # 监控任务状态
            if task_id:
                print("监控任务进度...")
                for i in range(30):  # 最多等待5分钟
                    time.sleep(10)
                    
                    # 查询任务状态
                    status_response = requests.get(f"{BASE_URL}/tasks/{task_id}")
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        status = status_data.get('status')
                        progress = status_data.get('progress', 0)
                        current_step = status_data.get('current_step', '')
                        
                        print(f"  进度: {progress:.1f}% - {status} - {current_step}")
                        
                        if status in ['COMPLETED', 'FAILED']:
                            if status == 'COMPLETED':
                                print("✅ 异步训练完成!")
                                result_data = status_data.get('result', {})
                                if 'results' in result_data:
                                    for source_id, source_result in result_data['results'].items():
                                        print(f"数据源 {source_id}: {'成功' if 'error' not in source_result else '失败'}")
                            else:
                                print(f"❌ 异步训练失败: {status_data.get('error_message', 'Unknown error')}")
                            break
                    else:
                        print(f"无法查询任务状态: {status_response.status_code}")
                        break
                        
            return True
        else:
            print(f"❌ 异步训练启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异步训练异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始多CSV文件训练功能测试...")
    
    # 创建测试数据
    print("创建测试数据...")
    create_test_data()
    
    # 测试同步训练
    sync_success = test_multi_training_sync()
    
    # 测试异步训练
    async_success = test_multi_training_async()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"同步多文件训练: {'✅ 成功' if sync_success else '❌ 失败'}")
    print(f"异步多文件训练: {'✅ 成功' if async_success else '❌ 失败'}")
    
    if sync_success and async_success:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
